FROM reg.docker.alibaba-inc.com/apsaradb/apsaradb-alios7u2-base-with-java:1.1
# DO NOT override ENTRY_POINT if you need conf file rendered by jinja2 and other preprocess in dockerinit.d.
# Just write a CMD for your service entry-point.

ENV JAVA_APP_JAR=rdsapi-ext-mysql.jar \
    JAVA_APP_DIR=/usr/local/rds/rdsapi-ext-mysql \
    JAVA_OPTIONS=-XX:-OmitStackTraceInFastThrow

# By default use /deployments/run_java.sh to start java app will auto adapt to container limit,
# just set JAVA_APP_DIR and JAVA_APP_JAR as env as java entrypoint.
#CMD /deployments/run_java.sh


ARG REVERSION=""
ARG GIT_REPO=""
ARG GIT_BRANCH=""
ARG GIT_COMMIT_HASH=""
LABEL GIT_COMMIT_HASH=${GIT_COMMIT_HASH} REVERSION=${REVERSION} GIT_BRANCH=${GIT_BRANCH} GIT_REPO=${GIT_REPO}


COPY rootfs /
COPY rootfs/meta/usr/local/rds/rdsapi-ext-mysql/application-prod.yml.j2 /usr/local/rds/rdsapi-ext-mysql/application-prod.yml

#COPY src/main/filters/AES.jar /usr/local/rds/rdsapi/package/
#COPY src/main/filters/jetty /usr/local/rds/rdsapi/package/config/resources/jetty/

#COPY target/aliyun-rds-openapi.war /usr/local/rds/rdsapi/package/config/resources/jetty/webapps/openapi.war
# commented-out code shows a desmonstration of yum install if you need for this component.
#RUN          \
#    touch /var/lib/rpm/* && \
#    yum install -y mysql-devel postgresql-devel libcurl-devel gcc  && \
#    yum install -b test -y t-rds-zeromq t-rds-freetds && \
#    mpip install --no-cache-dir ${REQUIREMENTS} && \
#    cd /wheels && \
#    pip install --no-cache-dir *.whl && \
#    yum erase -y *devel gcc cloog-ppl cpp && \
#    yum clean --enablerepo=* all && \
#    touch /var/lib/rpm/* && \
#    rm -rf ~/.cache/pip

