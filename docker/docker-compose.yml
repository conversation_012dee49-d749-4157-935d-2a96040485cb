version: '0.1'

services:
  rds_api:
    image: reg.docker.alibaba-inc.com/apsaradb/apsaradb-alios7u2-rds_api:*******
    #image: reg.docker.alibaba-inc.com/apsaradb/rds-resmanager-apc@sha256:32ec3c76bca9b1735a98e4928d4d1da4427c98e5dad915823b080e3109c4a945
    net: "host"
    volumes:
    - /home/<USER>/rds_api/log/:/usr/local/rds/rdsapi/log/
    #entrypoint: /sbin/init
    #deploy:
    #  resources:
    #    limits:
    #      cpus: '0.001'
    #      memory: 50M
    #    reservations:
    #      cpus: '0.0001'
    #      memory: 20M
    environment:
    - "RELATION_CONFIG_ALIYUN_MIDDLEWARE_DUBBO_REG="
    - "RELATION_CONFIG_ALIYUN_MIDDLEWARE_ENV_ID="
    - "RELATION_CONFIG_META_DB_CONNECTION_STRING=127.0.0.1"
    - "RELATION_CONFIG_META_DB_DB_PORT=3306"
    - "RELATION_CONFIG_META_DB_DB_NAME=dbaas"
    - "RELATION_CONFIG_META_DB_DB_USER=root"
    - "RELATION_CONFIG_META_DB_DB_PASSWD=root"
    - "RELATION_CONFIG_BAK_DB_CONNECTION_STRING=127.0.0.1"
    - "RELATION_CONFIG_BAK_DB_DB_PORT=3306"
    - "RELATION_CONFIG_BAK_DB_DB_NAME=dbaas"
    - "RELATION_CONFIG_BAK_DB_DB_USER=root"
    - "RELATION_CONFIG_BAK_DB_DB_PASSWD=root"
    - "RELATION_CONFIG_DRDS_META_DB_CONNECTION_STRING=127.0.0.1"
    - "RELATION_CONFIG_DRDS_META_DB_DB_PORT=3306"
    - "RELATION_CONFIG_DRDS_META_DB_DB_NAME=dbaas"
    - "RELATION_CONFIG_DRDS_META_DB_DB_USER=root"
    - "RELATION_CONFIG_DRDS_META_DB_DB_PASSWD=root"
    - "RELATION_CONFIG_PERF_DB_CONNECTION_STRING=127.0.0.1"
    - "RELATION_CONFIG_PERF_DB_DB_PORT=3306"
    - "RELATION_CONFIG_PERF_DB_DB_NAME=dbaas"
    - "RELATION_CONFIG_PERF_DB_DB_USER=root"
    - "RELATION_CONFIG_PERF_DB_DB_PASSWD=root"
    - "RELATION_CONFIG_DRDS_PERF_DB_CONNECTION_STRING="
    - "RELATION_CONFIG_DRDS_PERF_DB_DB_PORT="
    - "RELATION_CONFIG_DRDS_PERF_DB_DB_NAME="
    - "RELATION_CONFIG_DRDS_PERF_DB_DB_USER="
    - "RELATION_CONFIG_DRDS_PERF_DB_DB_PASSWD="
    - "RELATION_CONFIG_META_DB_METADB_SLAVE_CONNECTION_STRING=127.0.0.1"
    - "GLOBAL_DBNODE_USER="
    - "RDSAPI_AURORA_PASSWD="
    - "RELATION_HOST_DBCENTER="
    - "RELATION_CONFIG_NS_CONFIG_NS_ADDR="
