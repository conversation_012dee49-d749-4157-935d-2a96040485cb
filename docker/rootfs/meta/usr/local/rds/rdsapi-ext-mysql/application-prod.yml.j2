---
spring:
  autoconfigure.exclude: com.aliyun.boot.dubbo.DubboAutoConfiguration
  application:
    name: rdsapi-ext-mysql
  datasource:
    url: jdbc:mysql://${META_DB_CONNECTION_STRING}:${META_DB_DB_PORT}/${META_DB_DB_NAME}?useUnicode=true&characterEncoding=UTF-8&socketTimeout=30000&connectTimeout=10000&rewriteBatchedStatements=true
    username: ${META_DB_DB_USER}
    password: ${META_DB_DB_PASSWD}
    driver-class-name: com.mysql.jdbc.Driver
    druid:
        name: dbaas_master
        initial-size: 10
        min-idle: 10
        max-active: 100
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        max-wait: 60000
        time-between-eviction-runs-millis: 30000
        stat-view-servlet.enabled: false
        socket-timeout: 30000
        connect-timeout: 10000
rds:
  mybatis_plugins: true
  name-service:
    base-url: ${NS_CONFIG_NS_ADDR}
  server_env: ${RDSAPI_EXT_MYSQL_SERVER_ENV}
opentracing:
  jaeger:
    service-name: RDSAPI_EXT_MYSQL
    enable-slf4j-mdc: true

  output:
    ansi:
      enabled: DETECT

server:
  port: 9010

dubbo:
  metrics:
    enable-collector-sync: true
    use-global-registry: true
  application:
    qos-port: ${RDSAPI_EXT_MYSQL_DUBBO_QOS_PORT:22225}
    qos-accept-foreign-ip: false
  registry:
    simplified: true
management:
  endpoints:
    web:
      exposure:
        # 将 Actuator 的 /actuator/prometheus 端点暴露出来
        include: prometheus
      # 把 actuator 根 path 改为 /
      base-path: /
      # 将 prometheus 指标地址映射到 metrics
      path-mapping:
        prometheus: metrics
        health: api/v1/actuator/health
  metrics:
    tags:
      application: RDSAPI_EXT_MYSQL
