#!/usr/bin/env bash
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd -P)"
PROJECT_ROOT=$(dirname ${SCRIPT_DIR})
cd ${PROJECT_ROOT}

# PACKAGE_NAME 只允许数字字母下划线
PACKAGE_NAME="$2"
PACKAGE_VERSION="$3"
GIT_REPO="$4"
GIT_BRANCH="$5"
GIT_COMMIT="$6"


if [ "$PACKAGE_NAME" = "" ]; then
    PACKAGE_NAME=rdsapi_ext_mysql
fi
if [ "$PACKAGE_VERSION" = "" ]; then
    PACKAGE_VERSION=*******
fi

DOCKER_FILE_NAME="Dockerfile-${PACKAGE_NAME}"
if [ "$1" = "build" ]; then
    # Compile
    DOCKER_NAME=compile-${PACKAGE_NAME}-$(uuidgen)
    FROM_IMAGE="reg.docker.alibaba-inc.com/apsaradb/centos6u6-java8:1.0"
    BUILD_ROOT=${PROJECT_ROOT}/${DOCKER_NAME}
    mkdir -p ${BUILD_ROOT}
    mkdir -p ~/.m2/
    rsync -rv --exclude=.git --exclude=compile*  --exclude=target  --exclude=doc ./ ${BUILD_ROOT}
    # Image build
    docker run --rm \
        --name ${DOCKER_NAME} --network=host \
        -v ~/.m2/:/root/.m2/ \
        -v "${BUILD_ROOT}":/build \
        "${FROM_IMAGE}" \
        bash -c "cd /build/ && mvn -DskipTests=true -s settings.xml clean package -U"
    if [ ! -e ${PROJECT_ROOT}/target ]; then mkdir ${PROJECT_ROOT}/target/; fi && cp -f ${BUILD_ROOT}/target/*.jar ${PROJECT_ROOT}/target
    rm -rf ${BUILD_ROOT}
fi

# additional copy

WAR_FILE=${PROJECT_ROOT}/target/rdsapi-ext-mysql.jar
if [ ! -e ${WAR_FILE} ]; then
    echo package file not exists: ${WAR_FILE}, may check ${PROJECT_ROOT}/target/
    exit 1
fi

DEST_WAR=docker/rootfs/usr/local/rds/rdsapi-ext-mysql/rdsapi-ext-mysql.jar
mkdir -p $(dirname ${DEST_WAR})
cp -f ${WAR_FILE} ${DEST_WAR}

cd ${PROJECT_ROOT}/docker
docker build --network=host \
    --pull \
    -f ${DOCKER_FILE_NAME} \
    --build-arg REVERSION="${PACKAGE_VERSION}" \
    --build-arg GIT_REPO="${GIT_REPO}" \
    --build-arg GIT_BRANCH="${GIT_BRANCH}" \
    --build-arg GIT_COMMIT_HASH="${GIT_COMMIT}" \
    -t reg.docker.alibaba-inc.com/apsaradb/apsaradb-alios7u2-${PACKAGE_NAME}:${PACKAGE_VERSION} \
    .
docker tag reg.docker.alibaba-inc.com/apsaradb/apsaradb-alios7u2-${PACKAGE_NAME}:${PACKAGE_VERSION} \
    reg.docker.alibaba-inc.com/apsaradb/apsaradb-alios7u2-${PACKAGE_NAME}

