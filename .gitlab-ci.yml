# NOTES: following jobs all uses Docker container to run, to run gitlab-ci job locally you can install
# git-ci-multi-runner (https://docs.gitlab.com/runner/install/) to your system, add add following alias
# to your ~/.profile, and you can run 'gitlabrundocker <job name>' to execute job locally:
# ```
#alias gitlabrundocker='gitlab-ci-multi-runner exec docker --docker-pull-policy "if-not-present" --docker-privileged --docker-volumes "/var/run/docker.sock:/var/run/docker.sock" --docker-volumes "$HOME/.docker:/root/.docker" --docker-volumes "$HOME/.m2:/root/.m2" --docker-volumes "$HOME/.gitlab-runner/cache:/cache"'
# ```

# due to unstable gitlab-ci runner causing variable missing during CI jobs, move variables to a source file ./.gitlab-ci-variables
variables:


before_script:
  - source ./.gitlab-ci-variables
#  - uname -a
#  - hostname
#  - whoami

## after_script: required gitlab 8.7, however, we are using 8.3
#after_script:

stages:
  - build_and_test
  - docker_build
  - docker_test
  - deploy_preparation
  - deloy

job_package:
  tags:
    - docker
  image: "reg.docker.alibaba-inc.com/apsaradb/centos6u6-java8:1.0"
  stage: build_and_test
  script:
    - if [ -z "${BUILD_VERSION}" ];then exit 1; fi
    - javac -version
    - mvn -version
    - echo "mvn versions:set -DnewVersion=${BUILD_VERSION}"
    - mvn versions:set -DnewVersion=${BUILD_VERSION}
    - mvn -T 4C -U -Dmaven.test.skip=${MAVEN_TEST_SKIP} -Djava.net.preferIPv4Stack=true -Dspring.profiles.active=unit-test -Ddebug=false clean package
    - cp target/*.jar /cache
    - if [ -n "${OSS_ID}" ] && [ -n "${OSS_KEY}" ]; then osscmd --host=oss-cn-hangzhou.aliyuncs.com --id=${OSS_ID} --key=${OSS_KEY} put target/*.jar oss://braininstallables/bls/ ; fi
##  artifacts:
##    paths:
##      - target/*.jar
##      - target/*.war
#  cache:
##  per-job caching
#    key: "$CI_BUILD_NAME"
###    per-branch caching
##    key: "$CI_BUILD_REF_NAME"
###    per-job and per-branch caching
##    key: "$CI_BUILD_NAME/$CI_BUILD_REF_NAME"
###    per-branch and per-stage caching:
##    key: "$CI_BUILD_STAGE/$CI_BUILD_REF_NAME"
#    paths:
#      - target/*.jar
  except:
    - docker-release

job_package_ext:
  tags:
    - docker
  image: "reg.docker.alibaba-inc.com/apsaradb/centos6u6-java8:1.0"
  stage: build_and_test
  script:
    - if [ -z "${BUILD_VERSION}" ];then exit 1; fi
    - javac -version
    - mvn -version
    - echo "mvn versions:set -DnewVersion=${BUILD_VERSION}"
    - mvn versions:set -DnewVersion=${BUILD_VERSION}
    - mvn -T 4C -U -Dmaven.test.skip=${MAVEN_TEST_SKIP} -Djava.net.preferIPv4Stack=true -Ddebug=false clean package
    - cp target/*.jar /cache
    - if [ -n "${OSS_ID}" ] && [ -n "${OSS_KEY}" ]; then osscmd --host=oss-cn-hangzhou.aliyuncs.com --id=${OSS_ID} --key=${OSS_KEY} put target/*.jar oss://braininstallables/bls/ ; fi
##  artifacts:
##    paths:
##      - target/*.jar
##      - target/*.war
#  cache:
##  per-job caching
#    key: "$CI_BUILD_NAME"
###    per-branch caching
##    key: "$CI_BUILD_REF_NAME"
###    per-job and per-branch caching
##    key: "$CI_BUILD_NAME/$CI_BUILD_REF_NAME"
###    per-branch and per-stage caching:
##    key: "$CI_BUILD_STAGE/$CI_BUILD_REF_NAME"
#    paths:
#      - target/*.jar
  except:
    - docker-release


job_docker_build:
  tags:
    - docker
  image: "docker:18.03"
  stage: docker_build
  script:
    - if [ -z "${BUILD_VERSION}" ];then exit 1; fi
    - cd rdsapi-ext-physical
    - echo "/cache/${APP_NAME}-${BUILD_VERSION}.jar"
    - ls /cache/
    - ls /cache/${APP_NAME}-${BUILD_VERSION}.jar
    - mkdir -p ./cache && cp /cache/${APP_NAME}-${BUILD_VERSION}.jar ./cache/
    - echo "docker build --pull --build-arg build_target=cache/${APP_NAME}-${BUILD_VERSION}.jar --build-arg build_version=${BUILD_VERSION} --build-arg app_name=${APP_NAME} -t ${BUILD_IMAGE}:${BUILD_VERSION} ./"
    - docker build --pull --build-arg build_target=/cache/${APP_NAME}-${BUILD_VERSION}.jar --build-arg build_version=${BUILD_VERSION} --build-arg app_name=${APP_NAME} -t ${BUILD_IMAGE}:${BUILD_VERSION} ./
    - echo "docker tag ${BUILD_IMAGE}:${BUILD_VERSION} ${BUILD_IMAGE}"
    - docker tag ${BUILD_IMAGE}:${BUILD_VERSION} ${BUILD_IMAGE}
    - echo "docker history ${BUILD_IMAGE}:${BUILD_VERSION}"
    - docker history ${BUILD_IMAGE}:${BUILD_VERSION}
    - echo "docker push ${BUILD_IMAGE}:${BUILD_VERSION}"
    - docker push ${BUILD_IMAGE}:${BUILD_VERSION}
    - echo "docker push ${BUILD_IMAGE}:latest"
    - docker push ${BUILD_IMAGE}:latest
  only:
    - docker-release
    - develop
    - feature/dubbo-integration2


job_deploy_preparation:
  tags:
    - docker
  image: "docker:18.03"
  stage: deploy_preparation
  script:
    - export DOCKER_HOST=$DEPLOY_DOCKER_HOST
    - export DOCKER_TLS_VERIFY=$DEPLOY_DOCKER_TLS_VERIFY
    - export DOCKER_CERT_PATH=$DEPLOY_DOCKER_CERT_PATH
    - docker pull ${BUILD_IMAGE}:${BUILD_VERSION}
  only:
    - develop-deploy