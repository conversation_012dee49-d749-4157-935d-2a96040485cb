package com.aliyun.dba;

import com.aliyun.dba.support.doctool.doc.anno.EnableDocScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;



@EnableCaching
@SpringBootApplication
@ComponentScan(basePackages = {"com.aliyun.dba"})
@ImportResource(locations = {"classpath:dubbo.xml"})
@EnableDocScan(basePackages = {"com.aliyun.dba"})
public class RdsapiExtMysqlApplication {

    public static void main(String[] args) {
        SpringApplication.run(RdsapiExtMysqlApplication.class, args);
    }
}
