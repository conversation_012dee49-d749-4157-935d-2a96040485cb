package com.aliyun.dba.dataapi.service;

import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.*;

public interface DataApiKmsService {

    Client getClient(String regionId, String account) throws Exception;

    CreateKeyResponse createKey(String regionId, String account, String keySpec, String keyUsage) throws Exception;

    DescribeKeyResponse describeKey(String regionId, String account, String keyId) throws Exception;

    ListKeysResponse listKeys(String regionId, String account) throws Exception;

    EncryptResponse encrypt(String regionId, String account, String keyId, String plainText) throws Exception;

    DecryptResponse decrypt(String regionId, String account, String ciphertextBlob) throws Exception;

    AsymmetricEncryptResponse asymmetricEncrypt(String regionId, String account, String keyId, String keyVersion, String algorithm, String plainText) throws Exception;

    AsymmetricDecryptResponse asymmetricDecrypt(String regionId, String account, String keyId, String keyVersion, String algorithm, String cipherTextBlob) throws Exception;
}