/*
Created by yangkefan.ykf on 2022/5/16 10:37 AM
 */

package com.aliyun.dba.dataapi.service;

import java.util.Map;

public interface RDSDataAPIService {

    Map<String, Object> createSecret(String regionId, Long uid, String bid, String username, String password, String dbInstanceName, String resourceGroupId, String description, String secretName, String[] dbNames);

    Map<String, Object> retrieveSecretValueByARN(String regionId, Long uid, String bid, String secretARN);

    Map<String, Object> retrieveSecretValueByName(String regionId, String dbInstanceId, Long uid, String bid, String secretName);

    Map<String, Object> modifySecret(Map<String, Object> params);

    Map<String, Object> describeSecrets(String regionId, Long uid, String bid,String dbInstanceId, Long PageSize, Long PageNumber);

    Map<String, Object> deleteSecretByARN(String regionId, Long uid, String bid, String secretARN);

    Map<String, Object> deleteSecretByName(String regionId, String dbInstanceId, Long uid, String bid, String secretName);
}
