package com.aliyun.dba.dataapi.service;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.aliyun.dba.dataapi.dataobject.RDSDataAPISecretsDO;
import com.aliyun.dba.dataapi.idao.RDSDataAPISecretsIDao;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.kms20160120.models.CreateKeyResponse;
import com.aliyun.kms20160120.models.DecryptResponse;
import com.aliyun.kms20160120.models.EncryptResponse;
import com.aliyun.kms20160120.models.ListKeysResponseBody;
import com.aliyun.opensearch.sdk.dependencies.com.google.gson.Gson;
import org.apache.log4j.Logger;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
/**
 * author: yangkefan.ykf
 */
@Service
public class RDSDataAPIServiceImpl implements RDSDataAPIService {

    private static final Logger logger = Logger.getLogger(RDSDataAPIService.class);
    private static final String ACCOUNT = "dataapi-secrets";

    @Resource
    private RDSDataAPISecretsIDao rdsDataAPISecretsIDao;

    @Autowired
    private DataApiKmsService kmsService;

    @Resource
    private ResourceService resourceService;

    public boolean isAllowDataAPI(String uid) {
        final String ALLOW_DATA_API_SERVICE = "ALLOW_DATA_API_SERVICE";
        try {
            ResourceDO resourceDO = resourceService.getResourceByResKey(ALLOW_DATA_API_SERVICE);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                if ("*".equalsIgnoreCase(resourceDO.getRealValue())) {
                    return true;
                }
                Set<String> whiteUids = JSON.parseObject(resourceDO.getRealValue(), Set.class);
                return whiteUids.contains(uid);
            }
        } catch (Exception e) {
            logger.warn("get ALLOW_DATA_API_SERVICE failed, default false", e);
        }
        // 默认不允许
        return false;
    }

    @Override
    public Map<String, Object> createSecret(String regionId, Long uid, String bid, String username, String password, String dbInstanceId, String resourceGroupId, String description, String secretName, String[] dbNames) {
        logger.info("Creating rds data api secret");

        // 是否允许创建
        if (!isAllowDataAPI(String.valueOf(uid))) {
            logger.info("User " + uid + " is not allowed to use data api");
            return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
        }

        Map<String, Object> ret = new HashMap<>();
        try {
            if (secretName == null) {
                secretName = username;
            }
            String secretValue = "";
            try {
                secretValue = encryptSecret(regionId, ACCOUNT, username, password, dbInstanceId, resourceGroupId, dbNames);
            } catch (Exception e) {
                logger.error("Error encrypting secret using kms: " + e);
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            }
            if (Strings.isBlank(secretValue)) {
                logger.error("Secret value after encryption is blank");
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            }
            String secretARN = generateSecretARN(regionId, uid, secretName);
            RDSDataAPISecretsDO rdsDataAPISecretsDO = new RDSDataAPISecretsDO();
            rdsDataAPISecretsDO.setSecretArn(secretARN);
            rdsDataAPISecretsDO.setUid(uid);
            rdsDataAPISecretsDO.setBid(bid);
            rdsDataAPISecretsDO.setRegionId(regionId);
            rdsDataAPISecretsDO.setSecretValue(secretValue);
            rdsDataAPISecretsDO.setSecretName(secretName);
            rdsDataAPISecretsDO.setDescription(description);
            rdsDataAPISecretsDO.setUsername(username);
            rdsDataAPISecretsDO.setInsName(dbInstanceId);
            logger.info("Inserting secret to database " + rdsDataAPISecretsDO);
            rdsDataAPISecretsIDao.insertSecret(rdsDataAPISecretsDO);
            ret.put("Success", true);
            ret.put("SecretARN", secretARN);
            ret.put("SecretName", secretName);
            return ret;
        } catch (DuplicateKeyException dex) {
                logger.error("Create secret duplicate key exception: ", dex);
                return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
        } catch (Exception e) {
            logger.error("Creating rds data api secret: " + e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    @Override
    public Map<String, Object> retrieveSecretValueByARN(String regionId, Long uid, String bid, String secretArn) {
        RDSDataAPISecretsDO rdsDataAPISecretsDO =  rdsDataAPISecretsIDao.findSecretBySecretArn(secretArn, regionId, uid, bid);
        if (rdsDataAPISecretsDO == null) {
            logger.error("No secret found according to secretArn");
            return createErrorResponse(ErrorCode.SECRET_NOTFOUND);
        }
        return retrieveSecretValue(rdsDataAPISecretsDO);
    }

    @Override
    public Map<String, Object> retrieveSecretValueByName(String regionId, String dbInstanceId, Long uid, String bid, String secretName) {
        RDSDataAPISecretsDO rdsDataAPISecretsDO = rdsDataAPISecretsIDao.findSecretByRegionIdAndInsNameAndUidAndBidAndSecretName(regionId, dbInstanceId, uid, bid, secretName);
        if (rdsDataAPISecretsDO == null) {
            logger.error("No secret found according to secretName");
            return createErrorResponse(ErrorCode.SECRET_NOTFOUND);
        }
        return retrieveSecretValue(rdsDataAPISecretsDO);
    }

    @Override
    public Map<String, Object> modifySecret(Map<String, Object> params) {
        logger.info("Modifying rds api secrets");
        return null;
    }

    @Override
    public Map<String, Object> describeSecrets(String regionId, Long uid, String bid, String dbInstanceId, Long pageSize, Long pageNumber) {
        logger.info("Describing rds api secrets");
        Map<String, Object> ret = new HashMap<>();
        try {
            RDSDataAPISecretsDO rdsDataAPISecretsDO = new RDSDataAPISecretsDO();
            rdsDataAPISecretsDO.setRegionId(regionId);
            rdsDataAPISecretsDO.setUid(uid);
            rdsDataAPISecretsDO.setBid(bid);
            rdsDataAPISecretsDO.setInsName(dbInstanceId);
            List<RDSDataAPISecretsDO> secrets = rdsDataAPISecretsIDao.findSecretsByParameters(rdsDataAPISecretsDO);
            secrets.forEach(secret -> {
                secret.setInsName(null);
                secret.setSecretValue(null);
            });
            secrets = getPageData(secrets, pageNumber, pageSize);
            // if (pageSize < size) {
            //     // page starts from 1
            //     secrets = secrets.subList((pageNumber.intValue() - 1) * pageSize.intValue(), Math.min(pageNumber.intValue() * pageSize.intValue(), size));
            // }
            ret.put("Success", true);
            ret.put("PageSize", pageSize);
            ret.put("PageNumber", pageNumber);
            ret.put("TotalCount", secrets.size());
            ret.put("Secrets", secrets);
            return ret;
        } catch (Exception e) {
            logger.error("Describing rds api secrets" + e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 手动挡分页
     * @param dataList      原始数据
     * @param pageNumber    分页
     * @param pageSize      每页数量
     * @return              分页数据
     * @param <T>           数据类型
     */
    <T> List<T> getPageData(List<T> dataList, long pageNumber, long pageSize) {

        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyList();
        }

        if (pageNumber < 1) {
            pageNumber = 1;
        }

        if (pageSize > 500) {
            pageSize = 500;
        }

        return dataList.stream()
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize).collect(Collectors.toList());

    }

    @Override
    public Map<String, Object> deleteSecretByARN(String regionId, Long uid, String bid, String secretArn) {
        RDSDataAPISecretsDO foundSecret = rdsDataAPISecretsIDao.findSecretBySecretArn(secretArn, regionId, uid, bid);
        if (null == foundSecret) {
            logger.error("Secret not found by secretArn");
            return createErrorResponse(ErrorCode.SECRET_NOTFOUND);
        }
        return deleteSecretValue(foundSecret);
    }

    @Override
    public Map<String, Object> deleteSecretByName(String regionId, String dbInstanceId, Long uid, String bid, String secretName) {
        RDSDataAPISecretsDO foundSecret = rdsDataAPISecretsIDao.findSecretByRegionIdAndInsNameAndUidAndBidAndSecretName(regionId, dbInstanceId, uid, bid, secretName);
        if (null == foundSecret) {
            logger.error("Secret not found by secretName");
            return createErrorResponse(ErrorCode.SECRET_NOTFOUND);
        }
        return deleteSecretValue(foundSecret);
    }


    /*
    Private methods
     */

    private String generateSecretARN(String regionId, Long uid, String secretName) {
        StringBuilder sb = new StringBuilder("acs:rds:");
        sb.append(regionId);
        sb.append(":");
        sb.append(uid);
        sb.append(":rds-db-credentials/");
        sb.append(secretName);
        sb.append("-");
        sb.append(generateRandomStringWithGivenLength(6));
        return sb.toString();
    }

    private String generateRandomStringWithGivenLength(int length) {
        int leftLimit = 48; // numeral '0'
        int rightLimit = 122; // letter 'z'
        int targetStringLength = length;
        Random random = new Random();

        String generatedString = random.ints(leftLimit, rightLimit + 1)
                .filter(i -> (i <= 57 || i >= 65) && (i <= 90 || i >= 97))
                .limit(targetStringLength)
                .collect(StringBuilder::new, StringBuilder::appendCodePoint, StringBuilder::append)
                .toString();

        return generatedString;
    }

    private String encryptSecret(String regionId, String account, String username, String password, String dbInstanceId, String resourceGroupId, String[] dbNames) throws Exception {
        Map<String, Object> secretValue = new HashMap<>();
        secretValue.put("username", username);
        secretValue.put("password", password);
        secretValue.put("dbInstanceId", dbInstanceId);
        secretValue.put("resourceGroupId", resourceGroupId);
        if (dbNames != null && dbNames.length > 0) {
            secretValue.put("dbNames", dbNames);
        }
        String keyId = checkOrCreateKey(regionId, account);
        String plainText = new Gson().toJson(secretValue);
        String secretString = new String(Base64.getEncoder().encode(plainText.getBytes(StandardCharsets.UTF_8)));
        EncryptResponse encryptResponse = kmsService.encrypt(regionId, account, keyId, secretString);
        return encryptResponse.getBody().getCiphertextBlob();
    }

    private String checkOrCreateKey(String regionId, String account) throws Exception {
        List<ListKeysResponseBody.ListKeysResponseBodyKeysKey> keys = kmsService.listKeys(regionId, account).getBody().getKeys().getKey();
        if (keys.isEmpty()) {
            // create key
            CreateKeyResponse response = kmsService.createKey(regionId, account, "Aliyun_AES_256", "ENCRYPT/DECRYPT");
            return response.getBody().getKeyMetadata().getKeyId();
        } else {
            return keys.get(0).getKeyId();
        }
    }

    private String decryptSecret(String regionId, String account, String secretValue) throws Exception {
        DecryptResponse response = kmsService.decrypt(regionId, account, secretValue);
        return response.getBody().getPlaintext();
    }

    private Map<String, Object> retrieveSecretValue(RDSDataAPISecretsDO rdsDataAPISecretsDO) {
        logger.info("Retrieving rds api secret value");
        String secretValue = rdsDataAPISecretsDO.getSecretValue();
        Map<String, Object> ret = new HashMap<>();
        try {
            String secretString = decryptSecret(rdsDataAPISecretsDO.getRegionId(), ACCOUNT, secretValue);
            ret.put("Success", true);
            ret.put("SecretARN", rdsDataAPISecretsDO.getSecretArn());
            ret.put("SecretName", rdsDataAPISecretsDO.getSecretName());
            ret.put("SecretString", secretString);
            ret.put("Description", rdsDataAPISecretsDO.getDescription());
            return ret;
        } catch (Exception e) {
            logger.error("Retrieving rds data api secret value: " + e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, Object> deleteSecretValue(RDSDataAPISecretsDO rdsDataAPISecretsDO) {
        logger.info("Deleting rds api secret value");
        Map<String, Object> ret = new HashMap<>();
        try {
            RDSDataAPISecretsDO foundSecretByArn = rdsDataAPISecretsIDao.findSecretBySecretArnDeleted(rdsDataAPISecretsDO.getSecretArn(), rdsDataAPISecretsDO.getRegionId(), rdsDataAPISecretsDO.getUid(), rdsDataAPISecretsDO.getBid());
            if (foundSecretByArn != null) {
                // hard delete
                rdsDataAPISecretsIDao.deleteSecretByIdHard(foundSecretByArn.getId());
            }
            RDSDataAPISecretsDO foundSecretBySecretName = rdsDataAPISecretsIDao.findSecretByRegionIdAndInsNameAndUidAndBidAndSecretNameDeleted(rdsDataAPISecretsDO.getRegionId(), rdsDataAPISecretsDO.getInsName(), rdsDataAPISecretsDO.getUid(), rdsDataAPISecretsDO.getBid(), rdsDataAPISecretsDO.getSecretName());
            if (foundSecretBySecretName != null) {
                rdsDataAPISecretsIDao.deleteSecretByIdHard(foundSecretBySecretName.getId());
            }
            rdsDataAPISecretsIDao.deleteSecretById(rdsDataAPISecretsDO.getId());
            ret.put("Success", true);
            ret.put("SecretARN", rdsDataAPISecretsDO.getSecretArn());
            ret.put("SecretName", rdsDataAPISecretsDO.getSecretName());
            return ret;
        } catch (Exception e) {
            logger.error("Deleting rds api secret value: " + e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

//    public static void main(String[] args) {
//        RDSDataAPIServiceImpl rdsDataAPIService = new RDSDataAPIServiceImpl();
//        String[] dbNames = {"sbtest"};
//        try {
//            Map<String, Object> map = rdsDataAPIService.createSecret("cn-hangzhou", "1234567", "kefan", "123456", "rm-1234567", "rg-1234567", "This is secret", null, dbNames);
////            String secretString = (String) map.get("SecretValue");
////            System.out.println(secretString);
////            String plainText = rdsDataAPIService.decryptSecret("cn-hangzhou", secretString);
////            System.out.println(plainText);
////            System.out.println(new String(Base64.getDecoder().decode(plainText)));
//            System.out.println(map);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
}
