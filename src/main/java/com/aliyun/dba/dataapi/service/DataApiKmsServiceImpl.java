package com.aliyun.dba.dataapi.service;

import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.ecs.idao.EcsUserInfoIDao;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.utils.AES;
import com.aliyun.kms20160120.models.*;
import com.aliyun.teaopenapi.models.*;
import com.aliyun.kms20160120.Client;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DataApiKmsServiceImpl implements DataApiKmsService {

    @Autowired
    private EcsUserInfoIDao ecsUserInfoIDao;

    @Override
    public Client getClient(String regionId, String account) throws Exception {
        EcsUserInfoDO ecsUserInfo = ecsUserInfoIDao.getEcsUserInfo(account);
        String accessKey = AES.decryptPassword(ecsUserInfo.getAccessKeyId(), RdsConstants.PASSWORD_KEY);
        String accessSecret = AES.decryptPassword(ecsUserInfo.getAccessKeySecretEncrypted(), RdsConstants.PASSWORD_KEY);
        Config config = new Config()
                .setAccessKeyId(accessKey)
                .setAccessKeySecret(accessSecret)
                .setEndpoint("kms." + regionId + ".aliyuncs.com");
        return new Client(config);
    }

    @Override
    public CreateKeyResponse createKey(String regionId, String account, String keySpec, String keyUsage) throws Exception {
        Client kmsClient = getClient(regionId, account);
        CreateKeyRequest request = new CreateKeyRequest()
                .setKeySpec(keySpec)
                .setKeyUsage(keyUsage);
        CreateKeyResponse response = kmsClient.createKey(request);
        return response;
    }

    @Override
    public DescribeKeyResponse describeKey(String regionId, String account, String keyId) throws Exception {
        Client kmsClient = getClient(regionId, account);
        DescribeKeyRequest request = new DescribeKeyRequest()
                .setKeyId(keyId);
        DescribeKeyResponse response = kmsClient.describeKey(request);
        return response;
    }

    @Override
    public ListKeysResponse listKeys(String regionId, String account) throws Exception {
        Client kmsClient = getClient(regionId, account);
        ListKeysRequest request = new ListKeysRequest().setFilters("[{\"Key\":\"KeySpec\",\"Values\":[\"Aliyun_AES_256\"]}]");
        ListKeysResponse response = kmsClient.listKeys(request);
        return response;
    }

    @Override
    public EncryptResponse encrypt(String regionId, String account, String keyId, String plainText) throws Exception {
        Client kmsClient = getClient(regionId, account);
        EncryptRequest request = new EncryptRequest()
                .setKeyId(keyId)
                .setPlaintext(plainText);
        return kmsClient.encrypt(request);
    }

    @Override
    public DecryptResponse decrypt(String regionId, String account, String ciphertextBlob) throws Exception {
        Client kmsClient = getClient(regionId, account);
        DecryptRequest request = new DecryptRequest()
                .setCiphertextBlob(ciphertextBlob);
        return kmsClient.decrypt(request);
    }

    @Override
    public AsymmetricEncryptResponse asymmetricEncrypt(String regionId, String account, String keyId, String keyVersion, String algorithm, String plainText) throws Exception {
        Client kmsClient = getClient(regionId, account);
        AsymmetricEncryptRequest request = new AsymmetricEncryptRequest()
                .setKeyId(keyId)
                .setKeyVersionId(keyVersion)
                .setAlgorithm(algorithm)
                .setPlaintext(plainText);
        return kmsClient.asymmetricEncrypt(request);
    }

    @Override
    public AsymmetricDecryptResponse asymmetricDecrypt(String regionId, String account, String keyId, String keyVersion, String algorithm, String cipherTextBlob) throws Exception {
        Client kmsClient = getClient(regionId, account);
        AsymmetricDecryptRequest request = new AsymmetricDecryptRequest()
                .setKeyId(keyId)
                .setKeyVersionId(keyVersion)
                .setAlgorithm(algorithm)
                .setCiphertextBlob(cipherTextBlob);
        return kmsClient.asymmetricDecrypt(request);
    }
}