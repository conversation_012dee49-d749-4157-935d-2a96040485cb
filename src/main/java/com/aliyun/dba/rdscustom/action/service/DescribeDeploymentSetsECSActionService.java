package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.ecs.idao.EcsDBServiceIDaoImpl;
import com.aliyun.dba.host.dataobject.*;
import com.aliyun.dba.host.idao.HostIDaoImpl;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.ecs.model.v20140526.DescribeDeploymentSetsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Slf4j
@Service
public class DescribeDeploymentSetsECSActionService extends GeneralECSActionService {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDeploymentSetsECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_DESCRIBE_DEPLOYMENT_SETS_ACTION_KEY);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;

    @Resource
    private HostIDaoImpl hostIDao;
    @Resource
    private EcsDBServiceIDaoImpl ecsDBServiceIDao;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }


    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        List<String> ecsInsList = new ArrayList<>();
        try {
            DescribeDeploymentSetsResponse describeDeploymentSetsResponse = JSONObject.parseObject((String) result.get(PARAM_ECS_RESPONSE), DescribeDeploymentSetsResponse.class);
            List<DescribeDeploymentSetsResponse.DeploymentSet> deploymentSets = describeDeploymentSetsResponse.getDeploymentSets();
            for (DescribeDeploymentSetsResponse.DeploymentSet deployment : deploymentSets){
                ecsInsList.addAll(deployment.getInstanceIds());
            }
            logger.info("DescribeDeploymentSetsECSActionService get ecsInsList: " + ecsInsList);
        }catch (Exception e){
            logger.error("DescribeDeploymentSetsECSActionService get ecsInsList failed.", e.getMessage());
        }
        String newEcsResponse = (String) result.get(PARAM_ECS_RESPONSE);
        if(!ecsInsList.isEmpty()) {
            for (String ecsInsId : ecsInsList) {
                EcsHostDetailDO ecsHostDetailDO = ecsDBServiceIDao.getEcsHostInfoByEcsInsId(ecsInsId);
                if(Objects.nonNull(ecsHostDetailDO) && Objects.nonNull(ecsHostDetailDO.getHostId())){
                    if(StringUtils.isNotBlank(ecsHostDetailDO.getHostName())){
                        logger.info(String.format("try replace ecsInsId: %s to rcInsId: %s.", ecsInsId, ecsHostDetailDO.getHostName()));
                        newEcsResponse = newEcsResponse.replace(ecsInsId, ecsHostDetailDO.getHostName());
                        continue;
                    }
                    ExtendHostQuery hostQuery = new ExtendHostQuery();
                    hostQuery.setHostId(ecsHostDetailDO.getHostId());
                    List<ExtendHostInfo> hostInfoList = hostIDao.getHostInfoListWithVpc(hostQuery);
                    if(Objects.nonNull(hostInfoList)){
                        String rcInsId = hostInfoList.get(0).getDedicatedHostName();
                        logger.info(String.format("try replace ecsInsId: %s to rcInsId: %s.", ecsInsId, rcInsId));
                        newEcsResponse = newEcsResponse.replace(ecsInsId, rcInsId);
                    }
                }else{
                    logger.warn("DescribeDeploymentSetsECSActionService get ecsHostDetailDO failed.");
                }
            }
        }
        result.put(PARAM_ECS_RESPONSE, newEcsResponse);
        return null;
    }

}