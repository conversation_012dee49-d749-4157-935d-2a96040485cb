package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.ECSApiHelper;
import com.aliyun.dba.rdscustom.action.support.EcsInsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.AcsRequest;
import com.aliyuncs.AcsResponse;
import com.aliyuncs.ecs.model.v20140526.RunInstancesRequest;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import com.aliyuncs.ecs.model.v20140526.ImportKeyPairRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeKeyPairsRequest;


@Slf4j
@Service
public class ImportKeyPairECSActionService extends GeneralECSActionService {

    private static final LogAgent logger = LogFactory.getLogAgent(ImportKeyPairECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_IMPORT_KEY_PAIR);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;

    @Resource
    private ECSApiHelper ecsApiHelper;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }

    @Override
    public Map<String, Object> doPreAction(Map<String, String> params, Map<String, String> mapping) throws RdsException {
        super.doPreAction(params, mapping);
        return null;
    }

    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        return null;
    }

}
