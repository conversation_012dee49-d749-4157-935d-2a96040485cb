package com.aliyun.dba.rdscustom.action.service;

import com.aliyun.dba.custins.dataobject.ClusterParamDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.ClustersMetaHelper;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Slf4j
@Service
public class DeleteDeploymentSetECSActionService extends GeneralECSActionService {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteDeploymentSetECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_DELETE_DEPLOYMENT_SET_ACTION_KEY);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;

    @Resource
    private ClustersMetaHelper clustersMetaHelper;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }


    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        return null;
    }

}
