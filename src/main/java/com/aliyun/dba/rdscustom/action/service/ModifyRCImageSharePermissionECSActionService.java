package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.HostInfoMetaHelper;
import com.aliyun.dba.rdscustom.action.support.RoleArnService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.AcsRequest;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecs.model.v20140526.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.UID;

@Slf4j
@Service
public class ModifyRCImageSharePermissionECSActionService extends GeneralECSActionService {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyRCImageSharePermissionECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_MODIFY_IMAGE_SHARE_PERMISSION_ACTION_KEY);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;
    @Resource
    private RoleArnService roleArnService;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }

    @Override
    public Map<String, Object> doAction(Map<String, String> params) throws RdsException {

        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        String bizType = mysqlParamSupport.getAcountBizType(params);
        String uid = mysqlParamSupport.getParameterValue(params, UID);

        if (StringUtils.isBlank(bizType)) {
            String errMsg = "InvalidEcsAction, bizType is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        EcsUserInfoDO ecsUserInfoDO = hostInfoMetaHelper.getEcsAccountByBizTypeAndDbType(bizType, "");
        String resourceUid = ecsUserInfoDO.getAliUid();
        // init ecsRequest param
        String ecsRequestStr = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }

        ModifyImageSharePermissionRequest modifyImageSharePermissionRequest = JSONObject.parseObject(ecsRequestStr, ModifyImageSharePermissionRequest.class);
        // 把镜像分享给resourceUid
        modifyImageSharePermissionRequest.setAddAccounts(Collections.singletonList(resourceUid));
        logger.info("RDS-RequestId : {}, before ecsInsApi.call_action, ecsRequest : {}", requestId, JSONObject.toJSONString(modifyImageSharePermissionRequest));
        try {
            IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);

            logger.info("modifyImageSharePermission request: {}", JSON.toJSONString(modifyImageSharePermissionRequest));
            ModifyImageSharePermissionResponse modifyImageSharePermissionResponse = ramClient.getAcsResponse(modifyImageSharePermissionRequest);
            logger.info("modifyImageSharePermission response: {}", JSON.toJSONString(modifyImageSharePermissionResponse));
            modifyImageSharePermissionResponse.setRequestId(requestId);
            Map<String, Object> data = new HashMap<>();
            data.put(PARAM_REQUEST_ID, requestId);
            data.put(PARAM_ECS_RESPONSE, JSONObject.toJSONString(modifyImageSharePermissionResponse));
            return data;
        } catch (Exception e) {
            logger.error("modifyImageSharePermission failed. for : "+e.getMessage());
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, e.getMessage());
        }
    }

    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        return null;
    }
}