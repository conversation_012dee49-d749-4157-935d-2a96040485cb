package com.aliyun.dba.rdscustom.action.support;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.EcsHost;
import com.aliyun.apsaradb.dbaasmetaapi.model.Eni;
import com.aliyun.apsaradb.dbaasmetaapi.model.EniListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.ClusterParamDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.ecs.idao.EcsUserInfoIDao;
import com.aliyun.dba.host.dataobject.ExtendHostInfoDO;
import com.aliyun.dba.host.dataobject.HostInfo;
import com.aliyun.dba.host.dataobject.HostLevelDO;
import com.aliyun.dba.host.idao.HostInfoIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.host.support.HostSupport;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.ecs.model.v20140526.CreateNetworkInterfaceResponse;
import com.aliyuncs.ecs.model.v20140526.RunInstancesRequest;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Slf4j
@Service
public class HostInfoMetaHelper {
    private static final LogAgent logger = LogFactory.getLogAgent(HostInfoMetaHelper.class);

    @Resource
    protected MysqlParameterHelper mysqlParaHelper;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private HostService hostService;
    @Resource
    private ClusterService clusterService;
    @Resource
    private EcsUserInfoIDao ecsUserInfoIDao;
    @Resource
    private HostInfoIDao hostInfoIDao;
    // cache
    private static final Cache<String, String> resourceCache = CacheBuilder.newBuilder()
            .maximumSize(2048)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    public EcsUserInfoDO getEcsAccountByBizTypeAndDbType(String bizType, String dbType) throws RdsException {
        if (StringUtils.isBlank(bizType)) {
            String errMsg = String.format("bizType: %s , bizType is blank", bizType);
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        List<EcsUserInfoDO> ecsUserInfoDOList = ecsUserInfoIDao.getEcsUserInfoListByDBType(bizType, dbType);
        if (CollectionUtils.isEmpty(ecsUserInfoDOList)) {
            String errMsg = String.format("bizType: %s, dbType: %s, ecsUserInfoDOList is empty", bizType, dbType);
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        Optional<EcsUserInfoDO> ecsUserInfoDO = ecsUserInfoDOList.stream().findFirst();
        if (!ecsUserInfoDO.isPresent()) {
            String errMsg = String.format("bizType: %s, dbType: %s, ecsUserInfoDO is not present", bizType, dbType);
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        logger.info("getEcsAccountByBizTypeAndDbType is {}", ecsUserInfoDO.get());
        return ecsUserInfoDO.get();
    }

    public boolean modifyHostInfoStatus(String rcInstanceId, Integer status) throws RdsException {
        if (StringUtils.isEmpty(rcInstanceId)) {
            logger.error("Invalid Host Names, skip delete hostinfo");
            return false;
        }
        Set<Integer> hostIds = mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(rcInstanceId);
        if (CollectionUtils.isEmpty(hostIds)) {
            logger.warn("hostIds is empty, skip delete hostinfo");
            return false;
        }
        Optional<Integer> hostId = hostIds.stream().findFirst();
        if (!hostId.isPresent()) {
            logger.warn("hostId.isPresent is false, skip delete hostinfo");
            return false;
        }
        logger.info("hostId is [{}], set is_deleted = 1", hostId.get());
        HostInfo hostInfo = hostService.getHostInfoByHostId(hostId.get(), null, null);
        hostInfo.setStatus(status);
        hostService.updateHostInfo(hostInfo);
        return true;
    }

    public boolean modifyHostLevel(String rcInstanceId, HostLevelDO hostLevelDO) throws RdsException {
        if (StringUtils.isEmpty(rcInstanceId)) {
            logger.error("Invalid Host Names, skip");
            return false;
        }
        Set<Integer> hostIds = mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(rcInstanceId);
        if (CollectionUtils.isEmpty(hostIds)) {
            logger.warn("hostIds is empty, skip");
            return false;
        }
        Optional<Integer> hostId = hostIds.stream().findFirst();
        if (!hostId.isPresent()) {
            logger.warn("hostId.isPresent is false, skip");
            return false;
        }
        if (hostLevelDO == null) {
            log.error("hostLevelDO is null, skip");
            return false;
        }

        log.info("update hostId {} levelId to {}", hostId.get(), hostLevelDO.getId());
        HostInfo hostInfo = hostService.getHostInfoByHostId(hostId.get(), null, null);
        hostInfo.setLevelId(hostLevelDO.getId());
        hostService.updateHostInfo(hostInfo);
        return true;
    }


    /**
     * {
     * "cn-beijing-h" : {
     * "VSwitchId": "vsw-2zeo1n991p1ifhq77yosw",
     * "SecurityGroupId": "sg-2ze3sigzve21am07dcah"
     * },
     * "zoneId" : {
     * "VSwitchId": "xxx",
     * "SecurityGroupId": "xxx"
     * }
     * }
     *
     * @param clusterName
     * @return
     * @throws RdsException
     */
    public Map<String, String> getNetInfoByCluster(String clusterName) throws RdsException {
        if (StringUtils.isBlank(clusterName)) {
            logger.error("Invalid clusterName, skip getNetInfoByCluster");
            return new HashMap<>();
        }

        ClusterParamDO clusterParam = clusterService.getClusterParam(null, null, clusterName, CLUSTER_PARAM_NET_INFO);
        if (Objects.isNull(clusterParam) || StringUtils.isBlank(clusterParam.getValue())) {
            logger.error("clusterParam is null, skip getNetInfoByCluster");
            return new HashMap<>();
        }
        Map<String, String> result = JSONObject.parseObject(clusterParam.getValue(), new TypeToken<HashMap<String, String>>() {
        }.getType());
        result = Objects.isNull(result) ? new HashMap<>() : result;
        return result;
    }

    public Integer insertHostInfo(ExtendHostInfoDO extendHostInfoDO) {
        Integer hostId = hostInfoIDao.createEcsHostExtend(extendHostInfoDO);
        if (Objects.isNull(hostId)) {
            logger.error("insertHostInfo failed.");
            return -1;
        }
        return hostId;
    }

    public boolean checkHostInfoExist(String dedicatedHostName) {
        Set<Integer> hostIds = null;
        try {
            hostIds = mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(dedicatedHostName);
        } catch (RdsException e) {
            String errMsg = String.format("checkHostInfoExist get error, errsMag is %s", e.getMessage());
            logger.warn(errMsg);
            return false;
        }
        return CollectionUtils.isNotEmpty(hostIds);
    }

    public boolean checkAsync(String requestId, String uid) {
        String jsonStrValue = null;
        try {
            jsonStrValue = resourceCache.get(RDS_CUSTOM_ASYNC, () -> {
                String value = "";
                ConfigListResult configListResult = dBaasMetaService.getDefaultClient().listConfigs(requestId, RDS_CUSTOM_ASYNC);
                if (configListResult != null && CollectionUtils.isNotEmpty(configListResult.getItems())) {
                    value = configListResult.getItems().get(0).getValue();
                }
                return value;
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        if ("*".equalsIgnoreCase(jsonStrValue)) {
            return true;
        }
        if (StringUtils.isNotBlank(jsonStrValue)) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStrValue);
            return Boolean.parseBoolean(jsonObject.getString(uid));
        }
        return false;
    }

    public Eni getEniIdByEcsId(String requestId, String ecsId) {
        EniListResult eniListResult = null;
        try {
            eniListResult = dBaasMetaService.getDefaultClient().listEcsEnis(requestId, ecsId);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
        ;
        logger.info("getEniIdByEcsId eniListResult info is {}", JSONObject.toJSONString(eniListResult));
        if (Objects.nonNull(eniListResult) && CollectionUtils.isNotEmpty(eniListResult.getItems())) {
            List<Eni> enis = eniListResult.getItems();
            Optional<Eni> eniOptional = enis.stream().findFirst();
            if (eniOptional.isPresent()) {
                return eniOptional.get();
            }
        }
        return null;
    }


    public void saveRCMetaV2(CreateNetworkInterfaceResponse eniResponse, HostLevelDO hostLevelDO, RunInstancesRequest runInstancesRequest, String zoneId, String clusterName, String rcInstanceId, String ecsInstanceId, String region, String bizType, String requestId) throws RdsException, ApiException {
        // check hostInfo
        if (checkHostInfoExist(rcInstanceId)) {
            String errMsg = String.format("rcInstanceId [%s] exists", rcInstanceId);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        //save meta
        ExtendHostInfoDO extendHostInfoDO = new ExtendHostInfoDO();
        extendHostInfoDO.setIp(eniResponse.getPrivateIpAddress());
        extendHostInfoDO.setHostName(rcInstanceId);
        extendHostInfoDO.setSiteName(KEY_RDS_CUSTOM);
        extendHostInfoDO.setGroupName(String.format("%s_%s", KEY_RDS_CUSTOM, ecsInstanceId));
        extendHostInfoDO.setIsAvail(HOSTINFO_NOT_AVALIABLE);
        extendHostInfoDO.setLevelId(hostLevelDO.getId());
        extendHostInfoDO.setClusterName(clusterName);
        extendHostInfoDO.setDbType(KEY_RDS_CUSTOM);
        extendHostInfoDO.setDbVersion("1.0");
        extendHostInfoDO.setVpcId(eniResponse.getVpcId());
        extendHostInfoDO.setRegion(region);
        extendHostInfoDO.setRegionCategory(KEY_RDS_CUSTOM);
        extendHostInfoDO.setStatus(HostSupport.HOST_STATUS_CREATING);
        extendHostInfoDO.setDedicatedHostName(rcInstanceId);
        extendHostInfoDO.setBizType(bizType);
        // default value
        extendHostInfoDO.setCheckDisk(0);
        extendHostInfoDO.setCheckStat(0);
        extendHostInfoDO.setDbarchId(0);
        extendHostInfoDO.setIsBigBuffer(0);
        extendHostInfoDO.setOwnerId(999999);
        extendHostInfoDO.setCreator(999999);
        extendHostInfoDO.setModifier(999999);
        extendHostInfoDO.setLogicGroup("global");
        extendHostInfoDO.setComment(rcInstanceId);
        insertHostInfo(extendHostInfoDO);

        EcsUserInfoDO ecsUserInfoDO = getEcsAccountByBizTypeAndDbType(bizType, "");

        Eni eni = new Eni();
        eni.setEniId(eniResponse.getNetworkInterfaceId());
        eni.setIp(eniResponse.getPrivateIpAddress());
        eni.setStatus(Eni.StatusEnum.ACTIVE);
        eni.setVpcId(eniResponse.getVpcId());
        eni.setMac(eniResponse.getMacAddress());
        eni.setVswitchId(eniResponse.getVSwitchId());
        eni.setType("usr_eni");

        EcsHost ecsHost = new EcsHost();
        ecsHost.setEcsInsId(ecsInstanceId);
        ecsHost.setImageId(runInstancesRequest.getImageId());
        ecsHost.setEcsInsName(rcInstanceId);
        ecsHost.setResourceGroupName(clusterName);
        ecsHost.setClassCode(runInstancesRequest.getInstanceType());
        ecsHost.setIp(eniResponse.getPrivateIpAddress());
        ecsHost.setUserName(ecsUserInfoDO.getUserName());
        ecsHost.setRegion(region);
        ecsHost.setZoneId(zoneId);
        ecsHost.setStatus(EcsHost.StatusEnum.ACTIVE);
        ecsHost.setEnis(Collections.singletonList(eni));
        ecsHost.setBizType(EcsHost.BizTypeEnum.DBNODE);
        dBaasMetaService.getDefaultClient().createEcsHosts(requestId, Collections.singletonList(ecsHost));
    }


    public String getArchByHostType(Integer hostType) {
        if (Objects.isNull(hostType) || !hostType2Arch.containsKey(hostType)) {
            return ARCH_X86;
        }
        return hostType2Arch.get(hostType);
    }


}
