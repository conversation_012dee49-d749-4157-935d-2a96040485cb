package com.aliyun.dba.rdscustom.action.support;

import java.util.HashMap;
import java.util.Map;

public class ECSActionConstant {
    // parameter
    public static final String PARAM_ECS_ACTION = "EcsAction";
    public static final String PARAM_REQUEST_ID = "RequestId";
    public static final String PARAM_ECS_REQUEST = "EcsRequest";
    public static final String PARAM_EXTRA_PARAM = "ExtraParam";
    public static final String PARAM_RC_INSTANCE_IDS = "RCInstanceIds";
    public static final String PARAM_RC_INSTANCE_ID = "RCInstanceId";
    public static final String PARAM_ECS_RESPONSE = "EcsResponse";
    public static final String PARAM_CLUSTER_NAME = "ClusterName";
    public static final String PARAM_USER_VPC_ID = "VpcId";
    public static final String PARAM_USER_VSWITCH_ID = "VSwitchId";
    public static final String PARAM_USER_SECURITY_GROUP_ID = "SecurityGroupId";
    public static final String PARAM_RESOURCE_SECURITY_GROUP_ID = "ResourceSecurityGroupId";
    public static final String PARAM_ZONE_ID = "ZoneId";
    public static final String PARAM_HOST_LEVEL_INFO = "HostLevelInfo";
    public static final String PARAM_TASK_ID = "TaskId";
    public static final String PARAM_ENI_ID = "EniId";
    public static final String PARAM_ECS_ID = "EcsInsId";
    public static final String PARAM_USER_ID = "user_id";
    public static final String PARAM_REGION_ID = "regionId";
    public static final String PARAM_TOTAL_COUNT = "TotalCount";
    public static final String PARAM_RC_INSTANCES = "RCInstances";
    public static final String PARAM_PERIOD = "Period";
    public static final String PARAM_DATAPOINTS = "Datapoints";
    public static final String PARAM_CODE = "Code";
    public static final String PARAM_SUCCESS = "Success";
    public static final String PARAM_MESSAGE = "Message";
    public static final String PARAM_NEXT_TOKEN = "NextToken";
    public static final String PARAM_EXPRESS = "Express";
    public static final String PARAM_METRIC_NAME = "MetricName";
    public static final String PARAM_START_TIME = "StartTime";
    public static final String PARAM_END_TIME = "EndTime";
    public static final String PARAM_LENGTH = "Length";
    public static final String PARAM_DEPLOYMENT_SETS = "deploymentSets";
    public static final String PARAM_INSTANCE_IDS = "instanceIds";
    public static final String PARAM_GMT_CREATED = "gmtCreated";
    public static final String PARAM_HOST_ID = "hostId";
    public static final String PARAM_HOST_INFO_PARAM_NAME = "paramName";
    public static final String PARAM_HOST_INFO_PARAM_VALUE = "paramValue";

    public static final String PARAM_RESPONSE_EXTRA_INFO = "extraInfo";
    public static final String PARAM_ECS_SSH_KEYPAIR_NAME = "KeyPairName";
    public static final String ECS_DISK_TYPE_DATA = "data";
    public static final String PARAM_ECS_SSH_KEYPAIR_PUBLIC_KEY = "PublicKey";

    // workflow
    public static final String WORKFLOW_CREATE_RC_TASK_KEY = "create_rc_ecs";
    public static final String WORKFLOW_DELETE_RC_TASK_KEY = "delete_rc_ecs";

    // rds custom
    public static final String ECS_PARAM_INSTANCE_ID = "instanceId";
    public static final String ECS_PARAM_REQUEST_ID = "requestId";
    public static final String ECS_PARAM_INSTANCE_TYPE = "instanceType";
    public static final String ECS_PARAM_ECS_INSTANCE_TYPE = "ecsInstanceType";
    public static final String ECS_PARAM_DEPLOYMENT_SET_ID = "deploymentSetId";
    public static final String ECS_PARAM_RESPONSE_DATA_DISKS = "dataDisks";
    public static final String ECS_PARAM_RESPONSE_DESCRIPTION = "description";
    public static final String ECS_PARAM_RESPONSE_HOST_TYPE = "hostType";
    public static final String ECS_PARAM_RESPONSE_DISK_TYPE = "diskType";

    public static final String RDS_CUSTOM_TAG_KEY = "RdsCustomTag";
    public static final String RDS_CUSTOM_TAG_VALUE_1 = "1"; // zuoyebang
    public static final String RDS_CUSTOM_TAG_VALUE_2 = "2"; // reserve value
    public static final String IS_RDS_CUSTOM = "isRdsCustom";
    public static final String RDS_CUSTOM_ENV = "rdscustom";
    public static final String KEY_RDS_CUSTOM = "rds_custom";
    public static final String RDS_CUSTOM_ASYNC = "RDS_CUSTOM_ASYNC";
    public static final String RDS_CUSTOM_CTL = "RDS_CUSTOM_CTL";
    public static final String RDS_CUSTOM_DEFAULT_IMAGES = "RDS_CUSTOM_DEFAULT_IMAGES";
    public static final String RDS_CUSTOM_BOOTCMD = "RDS_CUSTOM_BOOTCMD";
    public static final String RDS_CUSTOM_CTL_IMAGE_FILTER_RULE = "image_filter_rule";


    // ecs param
    public static final String ECS_PARAM_VPC_ATTRIBUTES = "vpcAttributes";
    public static final String ECS_PARAM_PRIVATE_IP_ADDRESS = "privateIpAddress";
    public static final String ECS_RUNNING_STATUS = "Running";
    public static final String ECS_CHARGE_TYPE_POST_PAID = "PostPaid";
    public static final String ECS_ACTIVE_SECURITY_ENHANCED = "Active";
    public static final String ECS_MODEL_PREFIX = "com.aliyuncs.ecs.model.v20140526.";
    public static final String ECS_DRY_RUN_PASSED_CODE = "DryRunOperation";
    public static final String ECS_REQUEST = "Request";
    public static final String ECS_RESPONSE = "Response";
    public static final String ECS_DEFAULT_ACTION_KEY = "Default";
    public static final String ECS_RUN_INSTANCES_ACTION_KEY = "RunInstances";
    public static final String ECS_DELETE_INSTANCE_ACTION_KEY = "DeleteInstance";
    public static final String ECS_STOP_INSTANCE_ACTION_KEY = "StopInstance";
    public static final String ECS_START_INSTANCE_ACTION_KEY = "StartInstance";
    public static final String ECS_MODIFY_INSTANCE_SPEC_ACTION_KEY = "ModifyInstanceSpec";
    public static final String ECS_CREATE_DEPLOYMENT_SET_ACTION_KEY = "CreateDeploymentSet";
    public static final String ECS_DELETE_DEPLOYMENT_SET_ACTION_KEY = "DeleteDeploymentSet";
    public static final String ECS_DESCRIBE_DEPLOYMENT_SETS_ACTION_KEY = "DescribeDeploymentSets";
    public static final String ECS_DESCRIBE_INSTANCE_ATTRIBUTE_ACTION_KEY = "DescribeInstanceAttribute";
    public static final String ECS_DESCRIBE_KEY_PAIRS = "DescribeKeyPairs";
    public static final String ECS_IMPORT_KEY_PAIR = "ImportKeyPair";
    public static final String ECS_DELETE_KEY_PAIRS = "DeleteKeyPairs";
    public static final String ECS_DESCRIBE_IMAGES_ACTION_KEY = "DescribeImages";
    public static final String ECS_MODIFY_IMAGE_SHARE_PERMISSION_ACTION_KEY = "ModifyImageSharePermission";

    public static final String ECS_DETACH_KEY_PAIR = "DetachKeyPair";
    public static final String ECS_ATTACH_KEY_PAIR = "AttachKeyPair";

    public static final String ECS_ACTION_DESCRIBE_INSTANCES = "DescribeInstances";


    // host info
    public static final Integer HOSTINFO_IS_DELETED = 1;
    public static final Integer HOSTINFO_NOT_AVALIABLE = 0;
    public static final Integer HOSTINFO_IS_AVALIABLE = 1;

    // host level
    public static final String ARCH_X86 = "x86";
    public static final String ARCH_AMD = "x86";
    public static final String ARCH_ARM = "arm";
    public static final Map<Integer, String> hostType2Arch = new HashMap<Integer, String>() {{
        put(6, ARCH_X86);
        put(7, ARCH_AMD);
        put(8, ARCH_ARM);
    }};


    // eni
    public static final String ENI_AVAILABLE_STATUS = "Available";
    public static final String ENI_IN_USE_STATUS = "InUse";


    // cluster param
    public static final String CLUSTER_PARAM_NET_INFO = "net_info";
    public static final String CLUSTER_PARAM_ECS_IMAGE_ID = "ecs_image_id";

    // sts
    public static final String COMMON_PROVIDER_STS_ENDPOINT = "COMMON_PROVIDER_STS_ENDPOINT";


    // for inventory, deprecated
    public static final String RESOURCE_ACCOUNT = "<EMAIL>";
    public static final String ENI_TYPE_FOR_NODE = "node-eni";
    public static final String SERVICE_ACCOUNT = "mysql_service";
    public static final String SLR_ROLE_NAME = "AliyunServiceRoleForRds";
    public static final String SERVICE_BIZ_TYPE = "mysql_service";

    public static final String VPC_NAME_PREFIX = "rds_custom_vpc_";
    public static final String SECURITY_GROUP_NAME_PREFIX = "rds_custom_sg_";
    public static final String INIT_SECURITY_GROUP_NAME_PREFIX = "rds_custom_init_sg_";
    public static final String VSWITCH_NAME_PREFIX = "rds_custom_vsw_";
}
