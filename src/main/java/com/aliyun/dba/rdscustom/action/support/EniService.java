package com.aliyun.dba.rdscustom.action.support;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecs.model.v20140526.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.*;

@Slf4j
@Service
public class EniService {
    private static final LogAgent logger = LogFactory.getLogAgent(EniService.class);

    @Resource
    private RoleArnService roleArnService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;

    //网卡挂载超时时间，默认30s
    private final static Integer timeOutDefault = 60;


    public CreateNetworkInterfaceResponse createEniInServiceAccount(Map<String, String> extraParams) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(extraParams, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(extraParams);
        String uid = mysqlParamSupport.getParameterValue(extraParams, UID);
        String userVSwitchId = mysqlParamSupport.getParameterValue(extraParams, PARAM_USER_VSWITCH_ID);
        String userSecurityGroupId = mysqlParamSupport.getParameterValue(extraParams, PARAM_USER_SECURITY_GROUP_ID);
        CreateNetworkInterfaceResponse response = null;
        try {
            response = createEni(requestId, uid, region, userVSwitchId, userSecurityGroupId);
        } catch (Exception e) {
            String errMsg = String.format("eniService.createEni failed, requestId: %s errMsg is %s", requestId, StringUtils.replace(e.getMessage(), "\"", ""));
            logger.error(errMsg, e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, errMsg);
        }
        if (Objects.isNull(response) || Objects.isNull(response.getNetworkInterfaceId())) {
            String errMsg = "eniService.createEni response is invalid";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, errMsg);
        }
        return response;
    }

    public void attachEniToECS(String ecsInstanceId, String eniId, Map<String, String> extraParams) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(extraParams, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(extraParams);
        // init bizType, get resource uid
        String bizType = mysqlParamSupport.getAcountBizType(extraParams);
        EcsUserInfoDO ecsUserInfoDO = hostInfoMetaHelper.getEcsAccountByBizTypeAndDbType(bizType, "");
        String resourceUid = ecsUserInfoDO.getAliUid();
        // init user uid
        String uid = mysqlParamSupport.getParameterValue(extraParams, UID);
        try {
            attachEni(requestId, resourceUid, region, eniId, ecsInstanceId);
            waitEniStatus(requestId, uid, region, eniId, ENI_IN_USE_STATUS);
        } catch (Exception e) {
            String errMsg = String.format("eniService.attachEni failed, requestId: %s errMsg is %s", requestId, e.getMessage());
            logger.error(errMsg, e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, errMsg);
        }
    }

    public void waitEniStatus(String requestId, String uid, String region, String eniId, String status) throws Exception {
        String currentStatus = "";
        for (int i = 0; i < timeOutDefault; i++) {
            try {
                DescribeNetworkInterfacesResponse.NetworkInterfaceSet networkInterfaceSet = getEniStatus(requestId, uid, region, eniId);
                currentStatus = networkInterfaceSet.getStatus();
                if (status.equalsIgnoreCase(currentStatus)) {
                    return;
                }
            } catch (Exception e) {
                log.error("Exception, ErrMsg: {}", e.getMessage());
            }
            if (i == 0) {
                Thread.sleep(1500);
            } else {
                Thread.sleep(500);
            }
        }
        String errMsg = String.format("wait interface %s status to %s timeout, current status %s failed", eniId, status, currentStatus);
        throw new Exception(errMsg);
    }


    public AttachNetworkInterfaceResponse attachEni(String requestId, String uid, String region, String eniInterfaceId, String ecsInsId) throws Exception {
        IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);
        AttachNetworkInterfaceRequest request = new AttachNetworkInterfaceRequest();
        request.setRegionId(region);
        request.setNetworkInterfaceId(eniInterfaceId);
        request.setInstanceId(ecsInsId);
        log.info("attach eni request: {}", JSON.toJSONString(request));
        AttachNetworkInterfaceResponse acsResponse = ramClient.getAcsResponse(request);
        log.info("attach eni response: {}", JSON.toJSONString(acsResponse));
        return acsResponse;
    }


    public CreateNetworkInterfaceResponse createEni(String requestId, String uid, String region, String userVSwitchId, String userSecurityGroupId) throws Exception {
        IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);
        CreateNetworkInterfaceRequest createNetworkInterfaceRequest = new CreateNetworkInterfaceRequest();
        createNetworkInterfaceRequest.setRegionId(region);
        createNetworkInterfaceRequest.setVSwitchId(userVSwitchId);
        createNetworkInterfaceRequest.setSecurityGroupId(userSecurityGroupId);
        log.info("create eni request: {}", JSON.toJSONString(createNetworkInterfaceRequest));
        CreateNetworkInterfaceResponse acsResponse = ramClient.getAcsResponse(createNetworkInterfaceRequest);
        log.info("create eni response: {}", JSON.toJSONString(acsResponse));
        return acsResponse;
    }

    public DeleteNetworkInterfaceResponse deleteEni(String requestId, String uid, String region, String eniId) {
        try {
            IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);
            DeleteNetworkInterfaceRequest deleteNetworkInterfaceRequest = new DeleteNetworkInterfaceRequest();
            deleteNetworkInterfaceRequest.setNetworkInterfaceId(eniId);
            log.info("delete eni request: {}", JSON.toJSONString(deleteNetworkInterfaceRequest));
            DeleteNetworkInterfaceResponse acsResponse = ramClient.getAcsResponse(deleteNetworkInterfaceRequest);
            log.info("delete eni response: {}", JSON.toJSONString(acsResponse));
            return acsResponse;
        } catch (Exception e) {
            log.error("delete eni failed, not throw exception");
        }
        return null;
    }


    public DescribeNetworkInterfacesResponse.NetworkInterfaceSet getEniStatus(String requestId, String uid, String region, String eniInterfaceId) throws Exception {
        IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);
        DescribeNetworkInterfacesRequest request = new DescribeNetworkInterfacesRequest();
        request.setNetworkInterfaceIds(Collections.singletonList(eniInterfaceId));
        DescribeNetworkInterfacesResponse describeNetworkInterfacesResponse = ramClient.getAcsResponse(request);
        log.info("describeNetworkInterfacesResponse: {}", JSON.toJSONString(describeNetworkInterfacesResponse));
        if (Objects.nonNull(describeNetworkInterfacesResponse) && CollectionUtils.isNotEmpty(describeNetworkInterfacesResponse.getNetworkInterfaceSets())) {
            return describeNetworkInterfacesResponse.getNetworkInterfaceSets().get(0);
        }
        return null;
    }


}
