package com.aliyun.dba.rdscustom.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSBaseActionService;
import com.aliyun.dba.rdscustom.action.support.HostInfoMetaHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AES;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyun.sdk.service.cms20190101.AsyncClient;
import com.aliyun.sdk.service.cms20190101.models.DescribeMetricListRequest;
import com.aliyun.sdk.service.cms20190101.models.DescribeMetricListResponse;
import com.aliyun.sdk.service.cms20190101.models.DescribeMetricListResponseBody;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import darabonba.core.client.ClientOverrideConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("rdscustomDescribeRCMetricListImpl")
public class DescribeRCMetricListImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeRCMetricListImpl.class);
    private final static String NAMESPACE = "acs_ecs_dashboard";

    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;
    @Autowired
    @Qualifier("ECSBaseActionService")
    private ECSBaseActionService ecsBaseActionService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String bizType = mysqlParamSupport.getAcountBizType(params);

        EcsUserInfoDO ecsUserInfoDO = hostInfoMetaHelper.getEcsAccountByBizTypeAndDbType(bizType, "");
        String accessKey =
                AES.decryptPassword(ecsUserInfoDO.getAccessKeyId(),
                        RdsConstants.PASSWORD_KEY);
        String accessSecret =
                AES.decryptPassword(ecsUserInfoDO.getAccessKeySecretEncrypted(),
                        RdsConstants.PASSWORD_KEY);
        DescribeMetricListResponseBody describeMetricListResponseBody = null;
        try {
            describeMetricListResponseBody = getMetricsList(params, accessKey, accessSecret);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if(Objects.isNull(describeMetricListResponseBody)){
            String errMsg = "asyncClient.describeMetricList failed, metricListResponse is null";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.SERVICE_UNAVAILABLE, errMsg);
        }
        logger.info(String.format("after client.describeMetricList, get responseBody: %s", JSONObject.toJSONString(describeMetricListResponseBody)));
        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_PERIOD, describeMetricListResponseBody.getPeriod());
        String ecsInsId = ecsBaseActionService.getECSIdByDedicatedHostName(mysqlParamSupport.getParameterValue(params, ECS_PARAM_INSTANCE_ID));
        String datapoints = describeMetricListResponseBody.getDatapoints().replace(ecsInsId,mysqlParamSupport.getParameterValue(params, ECS_PARAM_INSTANCE_ID));
        data.put(PARAM_DATAPOINTS, datapoints);
        data.put(PARAM_CODE, describeMetricListResponseBody.getCode());
        data.put(PARAM_SUCCESS, describeMetricListResponseBody.getSuccess());
        data.put(PARAM_MESSAGE, describeMetricListResponseBody.getMessage());
        data.put(PARAM_NEXT_TOKEN, describeMetricListResponseBody.getNextToken());
        return data;
    }
    private DescribeMetricListResponseBody getMetricsList(Map<String, String> params, String accessKey, String accessSecret) throws RdsException, JsonProcessingException {
        // init regionId
        String regionId = mysqlParamSupport.getParameterValue(params, PARAM_REGION_ID);
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKey)
                .accessKeySecret(accessSecret)
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region(regionId) // Region ID
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(String.format("metrics.%s.aliyuncs.com", regionId))
                )
                .build();

        String ecsInsId = ecsBaseActionService.getECSIdByDedicatedHostName(mysqlParamSupport.getParameterValue(params, ECS_PARAM_INSTANCE_ID));
        Map<String, String> instanceMap = Collections.singletonMap(ECS_PARAM_INSTANCE_ID, ecsInsId);
        List<Object> dimensions = new ArrayList<>();
        dimensions.add(instanceMap);
        String dimensionJsonStr = new ObjectMapper().writeValueAsString(dimensions);
        logger.info(String.format("getMetricsList by ak: %s, sk: %s, region: %s, instanceId: %s, dimensions: %s",accessKey, accessSecret, regionId, ecsInsId, dimensionJsonStr));
        DescribeMetricListRequest describeMetricListRequest = DescribeMetricListRequest.builder()
                .namespace(NAMESPACE)
                .metricName(mysqlParamSupport.getParameterValue(params, PARAM_METRIC_NAME))
                .period(mysqlParamSupport.getParameterValue(params, PARAM_PERIOD))
                .startTime(mysqlParamSupport.getParameterValue(params, PARAM_START_TIME))
                .endTime(mysqlParamSupport.getParameterValue(params, PARAM_END_TIME))
                .dimensions(dimensionJsonStr)
                .nextToken(mysqlParamSupport.getParameterValue(params, PARAM_NEXT_TOKEN))
                .express(mysqlParamSupport.getParameterValue(params, PARAM_EXPRESS))
                .length(mysqlParamSupport.getParameterValue(params, PARAM_LENGTH))
                .build();

        logger.info(String.format("create describeMetricListRequest by params: %s",params));

        CompletableFuture<DescribeMetricListResponse> response = client.describeMetricList(describeMetricListRequest);
        if(Objects.isNull(response)){
            logger.error("getRCMetricList from client failed, get null response");
            client.close();
        }else{
            try{
                DescribeMetricListResponse resp = response.get();
                return resp.getBody();
            }catch (Exception e) {
                logger.error( "getRCMetricList", "get response, but get body fail", e.getMessage());
            }finally{
                client.close();
            }
        }
        return null;
    }
}
