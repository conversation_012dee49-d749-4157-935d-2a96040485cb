package com.aliyun.dba.rdscustom.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.host.dataobject.ExtendHostInfo;
import com.aliyun.dba.host.dataobject.ExtendHostQuery;
import com.aliyun.dba.host.idao.HostIDaoImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("rdscustomDescribeRCInstancesImpl")
public class DescribeRCInstancesImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeRCInstancesImpl.class);
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    private HostIDaoImpl hostIDao;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        // init cluster name
        String clusterName = mysqlParamSupport.getParameterValue(params, PARAM_CLUSTER_NAME);
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        // check cluster name
        if (StringUtils.isBlank(clusterName)) {
            String errMsg = String.format("requestId: %s, cluster name is empty!", requestId);
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init vpcId
        String vpcId = mysqlParamSupport.getParameterValue(params, PARAM_USER_VPC_ID, null);
        int pageNum = Integer.parseInt(mysqlParamSupport.getParameterValue(params, ParamConstants.PAGE_NUMBERS, "1"));
        int pageSize = Integer.parseInt(mysqlParamSupport.getParameterValue(params, ParamConstants.MAX_RECORDS_PER_PAGE, "500"));

        String instanceId = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS, null);
        String uid = mysqlParamSupport.getParameterValue(params,UID);
        List<Map<String, Object>> rcInstanceList = getRCInstanceList(uid, clusterName, vpcId, region, instanceId, pageNum, pageSize);
        List<Map<String, Object>> rcInstanceListAll = getRCInstanceList(uid, clusterName, vpcId, region, instanceId, 1, 1000);

        Map<String, Object> result = new HashMap<>();
        int totalCount = rcInstanceListAll.size();
        result.put(PARAM_TOTAL_COUNT, totalCount);
        result.put(ParamConstants.PAGE_NUMBERS, pageNum);
        result.put(ParamConstants.MAX_RECORDS_PER_PAGE, pageSize);
        result.put(PARAM_RC_INSTANCES, rcInstanceList);
        return result;

    }

    public List<Map<String, Object>> getRCInstanceList(String uid, String clusterName, String vpcId, String region, String instanceId, int pageNum, int pageSize) {
        // check param
        if (pageNum < 1 || pageSize < 1) {
            throw new IllegalArgumentException("pageNum and pageSize must be positive integers.");
        }
        // init gson
        Gson gson = new Gson();
        Type type = new TypeToken<Map<String, Object>>() {
        }.getType();
        // query
        ExtendHostQuery hostQuery = new ExtendHostQuery();
        hostQuery.setClusterName(clusterName);
        hostQuery.setPageFirst((pageNum - 1) * pageSize);
        hostQuery.setPageSize(pageSize);
        if (StringUtils.isNotBlank(vpcId)) {
            hostQuery.setVpcId(vpcId);
        }if (StringUtils.isNotBlank(instanceId)) {
            hostQuery.setHostName(instanceId);
        }if (StringUtils.isNotBlank(region)) {
            hostQuery.setRegion(region);
        }
        if (StringUtils.isNotBlank(uid)) {
            hostQuery.setUid(uid);
        }

        List<ExtendHostInfo> hostInfoList = hostIDao.getHostInfoAndParamListWithClusterNameAndUID(hostQuery);
        logger.info("getRCInstanceList-hostInfoList : {}", gson.toJson(hostInfoList));
        // parse result
        LinkedHashMap<String, Map<String, Object>> rcInstanceMap = new LinkedHashMap<>(); // hostId->hostInfo
        for (ExtendHostInfo hostInfo : hostInfoList) {
            Map<String, Object> tmpRcInstance = gson.fromJson(gson.toJson(hostInfo), type);
            String hostIdKey = String.valueOf(tmpRcInstance.get(PARAM_HOST_ID));
            if(rcInstanceMap.containsKey(hostIdKey)){
                // rcInstanceList已经有对应的host信息了，获取name和value进行更新
                Map<String, Object> rcInstance = rcInstanceMap.get(hostIdKey);
                rcInstance.put((String) tmpRcInstance.get(PARAM_HOST_INFO_PARAM_NAME), tmpRcInstance.get(PARAM_HOST_INFO_PARAM_VALUE));
            }else{
                tmpRcInstance.put((String) tmpRcInstance.get(PARAM_HOST_INFO_PARAM_NAME), tmpRcInstance.get(PARAM_HOST_INFO_PARAM_VALUE));
                rcInstanceMap.put(hostIdKey, tmpRcInstance);
            }
        }

        List<Map<String, Object>> rcInstanceList = new ArrayList<>(rcInstanceMap.values());
        logger.info("getRCInstanceList-rcInstanceList: {}", rcInstanceList);
        return rcInstanceList;
    }
}