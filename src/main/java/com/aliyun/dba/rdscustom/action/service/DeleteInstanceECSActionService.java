package com.aliyun.dba.rdscustom.action.service;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.aliyun.dba.host.support.HostSupport;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.apsaradb.dbaasmetaapi.model.Eni;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.host.dataobject.HostInfo;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.ECSApiHelper;
import com.aliyun.dba.rdscustom.action.support.EcsInsApi;
import com.aliyun.dba.rdscustom.action.support.EniService;
import com.aliyun.dba.rdscustom.action.support.HostInfoMetaHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.ecs.model.v20140526.DeleteInstanceRequest;
import com.aliyuncs.ecs.model.v20140526.DeleteInstanceResponse;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DeleteInstanceECSActionService extends GeneralECSActionService {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteInstanceECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_DELETE_INSTANCE_ACTION_KEY);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;
    @Resource
    private ECSApiHelper ecsApiHelper;
    @Resource
    private HostService hostService;
    @Resource
    protected HostIDao hostIDao;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;
    @Resource
    private EniService eniService;
    @Resource
    private WorkFlowService workFlowService;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }

    @Override
    public Map<String, Object> doAction(Map<String, String> params) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        // init bizType
        String bizType = mysqlParamSupport.getAcountBizType(params);
        if (StringUtils.isBlank(bizType)) {
            String errMsg = "InvalidEcsAction, bizType is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init uid
        String uid = mysqlParamSupport.getParameterValue(params, UID);
        // init ecsRequest param
        String ecsRequestStr = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // convert ecsRequest, from jsonString to AcsRequest
        DeleteInstanceRequest ecsRequest = JSONObject.parseObject(ecsRequestStr, DeleteInstanceRequest.class);
        // check dryRun
        if (Objects.nonNull(ecsRequest.getDryRun()) && ecsRequest.getDryRun()) {
            return deleteInstanceDryRun(ecsRequest, requestId, region, bizType);
        }

        // init extra param
        String extraParamStr = mysqlParamSupport.getParameterValue(params, PARAM_EXTRA_PARAM);
        Map<String, String> extraParams = JSONObject.parseObject(extraParamStr, new TypeReference<Map<String, String>>() {
        }.getType());
        extraParams = Objects.isNull(extraParams) ? new HashMap<>() : extraParams.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().toLowerCase(),
                        Map.Entry::getValue
                ));
        initExtraParams(params, extraParams, ecsRequest);

        // check async or sync
        if (hostInfoMetaHelper.checkAsync(requestId, uid)) {
            return deleteInstanceAsync(extraParams);
        }
        return deleteInstanceSync(extraParams, ecsRequest);
    }


    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        if (result.containsKey(PARAM_ECS_RESPONSE)) {
            String rcInstanceIds = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS);
            if (StringUtils.isEmpty(rcInstanceIds)) {
                logger.error("Invalid Host Names, skip delete hostinfo");
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM);
            }
            Set<Integer> hostIds = mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(rcInstanceIds);
            if (CollectionUtils.isEmpty(hostIds)) {
                logger.warn("hostIds is empty, skip delete hostinfo");
                return null;
            }
            Optional<Integer> hostId = hostIds.stream().findFirst();
            if (hostId.isPresent()) {
                logger.info("hostId is [{}], set is_deleted = 1", hostId.get());
                HostInfo hostInfo = hostService.getHostInfoByHostId(hostId.get(), null, null);
                hostInfo.setIsAvail(HOSTINFO_NOT_AVALIABLE);
                hostInfo.setIsDeleted(HOSTINFO_IS_DELETED);
                hostInfo.setStatus(HostSupport.HOST_STATUS_OFFLINE);
                long timestamp = System.currentTimeMillis();
                hostInfo.setHostIp(hostInfo.getHostIp() + "_" + timestamp);
                hostService.updateHostInfo(hostInfo);
            }
        }
        return null;
    }


    private void initExtraParams(Map<String, String> params, Map<String, String> extraParams, DeleteInstanceRequest ecsRequest) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        // init bizType
        String bizType = mysqlParamSupport.getAcountBizType(params);
        if (StringUtils.isBlank(bizType)) {
            String errMsg = "InvalidEcsAction, bizType is empty";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init uid
        String uid = mysqlParamSupport.getParameterValue(params, UID);
        // init ecs action
        String ecsAction = mysqlParamSupport.getParameterValue(params, PARAM_ECS_ACTION);
        if (StringUtils.isBlank(ecsAction)) {
            String errMsg = "InvalidEcsAction, ecsAction is empty";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init ecsRequest param
        String ecsRequestStr = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }

        String ecsId = ecsRequest.getInstanceId();

        // init dedicatedHostName
        String rcInstanceIds = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS);
        // only process one instance
        if (StringUtils.isBlank(rcInstanceIds)) {
            String errMsg = "rcInstanceIds is empty";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        Optional<String> rcInstanceIdOpt = Arrays.stream(rcInstanceIds.split(",")).findFirst();
        if (!rcInstanceIdOpt.isPresent()) {
            String errMsg = "rcInstanceIdOpt.isPresent is false";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        String rcInstanceId = rcInstanceIdOpt.get();

        // get eniId
        Eni eni = hostInfoMetaHelper.getEniIdByEcsId(requestId, ecsId);
        String eniId = null;
        if (Objects.nonNull(eni)) {
            eniId = eni.getEniId();
        }
        mysqlParamSupport.setParameter(extraParams, PARAM_REQUEST_ID, requestId);
        mysqlParamSupport.setParameter(extraParams, REGION_ID, region);
        mysqlParamSupport.setParameter(extraParams, ACCOUNT_BIZ_TYPE, bizType);
        mysqlParamSupport.setParameter(extraParams, PARAM_ECS_ID, ecsId);
        mysqlParamSupport.setParameter(extraParams, PARAM_ENI_ID, eniId);
        mysqlParamSupport.setParameter(extraParams, PARAM_ECS_REQUEST, ecsRequestStr);
        mysqlParamSupport.setParameter(extraParams, UID, uid);
        mysqlParamSupport.setParameter(extraParams, PARAM_RC_INSTANCE_ID, rcInstanceId);
    }

    private Map<String, Object> deleteInstanceDryRun(DeleteInstanceRequest ecsRequest, String requestId, String region, String bizType) throws RdsException {
        // init ecs api
        EcsInsApi ecsInsApi = ecsApiHelper.initECSApiByBizType(region, bizType, "");
        logger.info("RDS-RequestId : {}, before ecsInsApi.call_action, ecsRequest : {}", requestId, JSONObject.toJSONString(ecsRequest));
        // call ecs api
        DeleteInstanceResponse ecsResponse = null;
        try {
            ecsResponse = (DeleteInstanceResponse) ecsInsApi.call_action(ecsRequest);
        } catch (ClientException e) {
            String errMsg = String.format("ecs call action failed, requestId: %s, [errCode: %s], [errMsg: %s]", requestId, e.getErrCode(), e.getErrMsg());
            logger.error(errMsg);
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, errMsg);
        }
        if (Objects.isNull(ecsResponse)) {
            String errMsg = "ecsInsApi.call_action failed, acsResponse is null";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        logger.info("RDS-RequestId : {}, after ecsInsApi.call_action , ecsResponse : {}", requestId, JSONObject.toJSONString(ecsResponse));
        // modify ecsResponse.requestId
        ecsResponse.setRequestId(requestId);
        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_REQUEST_ID, requestId);
        data.put(PARAM_ECS_RESPONSE, JSONObject.toJSONString(ecsResponse));
        return data;
    }

    private Map<String, Object> deleteInstanceSync(Map<String, String> extraParams, DeleteInstanceRequest ecsRequest) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(extraParams, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(extraParams);
        // init bizType
        String bizType = mysqlParamSupport.getAcountBizType(extraParams);
        if (StringUtils.isBlank(bizType)) {
            String errMsg = "InvalidEcsAction, bizType is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init uid
        String uid = mysqlParamSupport.getParameterValue(extraParams, UID);
        // init ecsId
        String ecsId = ecsRequest.getInstanceId();
        // init ecs api
        EcsInsApi ecsInsApi = ecsApiHelper.initECSApiByBizType(region, bizType, "");

        logger.info("RDS-RequestId : {}, before ecsInsApi.call_action, ecsRequest : {}", requestId, JSONObject.toJSONString(ecsRequest));
        // call ecs api
        DeleteInstanceResponse ecsResponse = null;
        try {
            ecsResponse = (DeleteInstanceResponse) ecsInsApi.call_action(ecsRequest);
        } catch (ClientException e) {
            String errMsg = String.format("ecs call action failed, requestId: %s, [errCode: %s], [errMsg: %s]", requestId, e.getErrCode(), e.getErrMsg());
            logger.error(errMsg);
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, errMsg);
        }
        if (Objects.isNull(ecsResponse)) {
            String errMsg = "ecsInsApi.call_action failed, acsResponse is null";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        logger.info("RDS-RequestId : {}, after ecsInsApi.call_action , ecsResponse : {}", requestId, JSONObject.toJSONString(ecsResponse));


        // get eniId
        Eni eni = hostInfoMetaHelper.getEniIdByEcsId(requestId, ecsId);
        String eniId = null;
        if (Objects.nonNull(eni)) {
            eniId = eni.getEniId();
        }
        // delete eni
        if (StringUtils.isNotBlank(eniId)) {
            eniService.deleteEni(requestId, uid, region, eniId);
        }

        // modify ecsResponse.requestId
        ecsResponse.setRequestId(requestId);
        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_REQUEST_ID, requestId);
        data.put(PARAM_ECS_RESPONSE, JSONObject.toJSONString(ecsResponse));
        return data;
    }


    private Map<String, Object> deleteInstanceAsync(Map<String, String> extraParams) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(extraParams, PARAM_REQUEST_ID);
        // init bizType
        String bizType = mysqlParamSupport.getAcountBizType(extraParams);
        if (StringUtils.isBlank(bizType)) {
            String errMsg = "InvalidEcsAction, bizType is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init rc instanceId
        String rcInstanceId = mysqlParamSupport.getParameterValue(extraParams, PARAM_RC_INSTANCE_ID);


        Object taskId = null;
        try {
            taskId = workFlowService.dispatchTaskByPost("custins", rcInstanceId, "mysql", WORKFLOW_DELETE_RC_TASK_KEY, JSONObject.toJSONString(extraParams), 0, requestId);
        } catch (Exception e) {
            logger.error("deleteInstanceAsync dispatchTaskByPost failed ");
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, e.getMessage());
        }

        // modify ecsResponse.requestId
        DeleteInstanceResponse ecsResponse = new DeleteInstanceResponse();
        ecsResponse.setRequestId(requestId);
        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_REQUEST_ID, requestId);
        data.put(PARAM_ECS_RESPONSE, JSONObject.toJSONString(ecsResponse));
        data.put(PARAM_TASK_ID, taskId);
        return data;

    }

}
