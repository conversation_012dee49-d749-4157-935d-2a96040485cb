package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.ClusterParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.support.ClusterParamSupport;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.ecs.idao.EcsUserInfoIDao;
import com.aliyun.dba.host.dataobject.HostLevelDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.RoleArnService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AES;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.AcsRequest;
import com.aliyuncs.AcsResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecs.model.v20140526.CreateNetworkInterfaceRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeKeyPairsRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeDeploymentSetsResponse;
import com.aliyuncs.ecs.model.v20140526.DescribeKeyPairsResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.UID;

@Slf4j
@Service
public class DescribeKeyPairsECSActionService extends GeneralECSActionService {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeKeyPairsECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_DESCRIBE_KEY_PAIRS);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;

    @Autowired
    private EcsUserInfoIDao ecsUserInfoIDao;

    @Resource
    private RoleArnService roleArnService;


    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }

    @Override
    public Map<String, Object> doPreAction(Map<String, String> params, Map<String, String> mapping) throws RdsException{
        super.doPreAction(params, mapping);
        return null;
    }

    @Override
    public Map<String, Object> doAction(Map<String, String> params) throws RdsException {
        String extraParam = mysqlParamSupport.getParameterValue(params, PARAM_EXTRA_PARAM);
        if (StringUtils.isNotBlank(extraParam) && "rds".equals(extraParam)) {
            return super.doAction(params);
        }
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        // init uid param
        String uid = mysqlParamSupport.getUID(params);
        //init ecs request
        String ecsRequestStr = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // convert ecsRequest, from jsonString to AcsRequest
        DescribeKeyPairsRequest ecsRequest = JSONObject.parseObject(ecsRequestStr, DescribeKeyPairsRequest.class);
        AcsResponse ecsResponse = null;
        try {
            ecsResponse = describeKeyPairs(requestId, uid, region, ecsRequest);
        } catch (Exception e) {
            logger.error("role play failed");
        }
        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_REQUEST_ID, requestId);
        data.put(PARAM_ECS_RESPONSE, JSONObject.toJSONString(ecsResponse));
        return data;
    }

    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        if (result.containsKey(PARAM_ECS_RESPONSE)) {
            String paramsJson = JSONObject.toJSONString(params);
            logger.info(paramsJson);
        }
        String ecsResponseStr = (String) result.get(PARAM_ECS_RESPONSE);
        if (StringUtils.isBlank(ecsResponseStr)) {
            String errMsg = "doPostAction ecsResponseStr is blank";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        try {
            DescribeKeyPairsResponse describeKeyPairsResponse = JSONObject.parseObject((String) result.get(PARAM_ECS_RESPONSE), DescribeKeyPairsResponse.class);
            List<DescribeKeyPairsResponse.KeyPair> ecsKeyPairs = describeKeyPairsResponse.getKeyPairs();
            logger.info("DescribeDeploymentSetsECSActionService get ecsInsList: " + JSONObject.toJSONString(ecsKeyPairs));
        }catch (Exception e){
            logger.error("DescribeDeploymentSetsECSActionService get ecsInsList failed.", e.getMessage());
        }
        String newEcsResponse = (String) result.get(PARAM_ECS_RESPONSE);
        result.put(PARAM_ECS_RESPONSE, newEcsResponse);

        //sync key pair
        return null;
    }

    public DescribeKeyPairsResponse describeKeyPairs(String requestId, String uid, String region, DescribeKeyPairsRequest ecsRequest) throws Exception {
        IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);
        log.info("describe Key Pairs request: {}", JSON.toJSONString(ecsRequest));
        DescribeKeyPairsResponse acsResponse = ramClient.getAcsResponse(ecsRequest);
        log.info("describe Key Pairs response: {}", JSON.toJSONString(acsResponse));
        return acsResponse;
    }
}
