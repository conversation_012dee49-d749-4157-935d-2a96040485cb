package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.host.dataobject.HostLevelDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.HostInfoMetaHelper;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Slf4j
@Service
public class ModifyInstanceSpecECSActionService extends GeneralECSActionService {
    private static final List<String> supportECSActions = Collections.singletonList(ECS_MODIFY_INSTANCE_SPEC_ACTION_KEY);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }

    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        if (result.containsKey(PARAM_ECS_RESPONSE)) {
            String rcInstanceIds = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS);
            String hostLevelInfoStr = mysqlParamSupport.getParameterValue(params, PARAM_HOST_LEVEL_INFO);
            HostLevelDO hostLevelDO = JSONObject.parseObject(hostLevelInfoStr, HostLevelDO.class);
            hostInfoMetaHelper.modifyHostLevel(rcInstanceIds, hostLevelDO);
        }
        return null;
    }
}
