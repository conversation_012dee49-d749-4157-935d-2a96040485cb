package com.aliyun.dba.rdscustom.action.service.base;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.host.dataobject.HostLevelDO;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Slf4j
@Service
public class ECSBaseActionService implements ECSActionService {
    private static final LogAgent logger = LogFactory.getLogAgent(ECSBaseActionService.class);
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected MysqlParameterHelper mysqlParaHelper;
    @Resource
    protected EcsDBService ecsDBService;
    @Resource
    protected HostService hostService;

    @Override
    public Map<String, String> initRcId2EcsIdMappping(Map<String, String> params) throws RdsException {
        String rcInstanceIds = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS);
        if (StringUtils.isEmpty(rcInstanceIds)) {
            logger.warn("rcInstanceIds is empty, mapping is null");
            return null;
        }
        Map<String, String> rc2ecsMapping = new HashMap<>();
        for (String rcInstanceId : rcInstanceIds.split(",")) {
            if (rc2ecsMapping.containsKey(rcInstanceId)) {
                logger.warn("rcInstanceId [{}] is duplicate, skip", rcInstanceId);
                continue;
            }
            String ecsInsId = getECSIdByDedicatedHostName(rcInstanceId);
            if (rc2ecsMapping.containsValue(ecsInsId)) {
                logger.warn("ecsInsId [{}] is duplicate, skip", ecsInsId);
                continue;
            }
            rc2ecsMapping.put(rcInstanceId, ecsInsId);
        }
        logger.info("rc2ecsMapping is {}", rc2ecsMapping);
        return rc2ecsMapping;
    }

    @Override
    public Map<String, Object> doPreAction(Map<String, String> params, Map<String, String> mapping) throws RdsException {
        // check parameters;
        String ecsAction = mysqlParamSupport.getParameterValue(params, PARAM_ECS_ACTION);
        if (StringUtils.isEmpty(ecsAction)) {
            String errMsg = "InvalidEcsAction, ecsAction is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        String ecsRequest = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        if (StringUtils.isEmpty(ecsRequest)) {
            String errMsg = "InvalidEcsRequest, ecsRequest is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }

        log.info("ecsRequestStr {}", ecsRequest);
        // do rds_custom level to ecs mapping mapping
        JSONObject ecsRequestJson = JSONObject.parseObject(ecsRequest);
        if (ecsRequestJson.containsKey(ECS_PARAM_INSTANCE_TYPE)) {
            String rcLevel = (String) ecsRequestJson.get(ECS_PARAM_INSTANCE_TYPE);
            HostLevelDO hostLevelDO = hostService.getHostLevelWithName(rcLevel);
            if (hostLevelDO != null && StringUtils.isNotBlank(hostLevelDO.getEcsClassCode())) {
                ecsRequestJson.put(ECS_PARAM_INSTANCE_TYPE, hostLevelDO.getEcsClassCode());
                log.info("replace parameter {} from {} to {}", ECS_PARAM_INSTANCE_TYPE, rcLevel, hostLevelDO.getEcsClassCode());
                ecsRequest = JSONObject.toJSONString(ecsRequestJson);
            }
            mysqlParamSupport.setParameter(params, PARAM_HOST_LEVEL_INFO, JSONObject.toJSONString(hostLevelDO));
        }
        log.info("ecsRequestStr after rds_custom level mapping {}", ecsRequest);

        // mapping
        String newEcsRequest = ecsRequest;
        if (MapUtils.isNotEmpty(mapping)) {
            for (String rcId : mapping.keySet()) {
                newEcsRequest = newEcsRequest.replace(rcId, mapping.get(rcId));
            }
        }
        mysqlParamSupport.setParameter(params, PARAM_ECS_REQUEST, newEcsRequest);
        logger.info("doPreAction: after replace, param: {}", params);
        return null;
    }

    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        if (MapUtils.isEmpty(result)) {
            logger.error("postAction failed, result is empty");
            return null;
        }
        String ecsResponseStr = (String) result.get(PARAM_ECS_RESPONSE);
        if (StringUtils.isEmpty(ecsResponseStr)) {
            logger.error("postAction failed, ecsResponseStr is empty");
            return null;
        }
        // mapping
        String newEcsResponseStr = ecsResponseStr;
        if (MapUtils.isNotEmpty(mapping)) {
            for (String rcId : mapping.keySet()) {
                newEcsResponseStr = newEcsResponseStr.replace(mapping.get(rcId), rcId);
            }
        }
        result.put(PARAM_ECS_RESPONSE, newEcsResponseStr);
        logger.info("doPostAction: after replace, result: {}", result);
        return null;
    }

    public String getECSIdByDedicatedHostName(String dedicatedHostName) throws RdsException {
        Set<Integer> hostIds = mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(dedicatedHostName);
        Optional<Integer> hostId = hostIds.stream().findFirst();
        if (!hostId.isPresent()) {
            String errMsg = "get meta data failed.";
            logger.error(errMsg + " getAndCheckHostIdSetOnlyByDedicatedHostNames failed");
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        EcsHostDetailDO ecsHostDetailDO = ecsDBService.getEcsHostDetailDOByHostId(hostId.get());
        if (Objects.isNull(ecsHostDetailDO)) {
            String errMsg = "get meta data failed.";
            logger.error(errMsg + " getEcsHostDetailDOByHostId failed");
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        logger.info("getECSIdByDedicatedHostName: dedicatedHostName: {}, EcsInsId: {}", dedicatedHostName, ecsHostDetailDO.getEcsInsId());
        return ecsHostDetailDO.getEcsInsId();
    }
}
