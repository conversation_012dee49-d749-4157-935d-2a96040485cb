package com.aliyun.dba.rdscustom.action.support;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.UID;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecs.model.v20140526.DescribeImagesRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeImagesResponse;
import com.aliyuncs.ecs.model.v20140526.ModifyImageSharePermissionRequest;
import com.aliyuncs.ecs.model.v20140526.ModifyImageSharePermissionResponse;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ImageService {
    // 对镜像的检查，共享，都在这里实现，包括后续的alisql打包的镜像
    private static final LogAgent logger = LogFactory.getLogAgent(ImageService.class);
    private static final List<String> DEFAULT_ALLOWED_IMAGE_OS_NAME_LIST = Arrays.asList("AlibabaCloudLinux3", "AlibabaCloudLinux2", "CentOSStream8","CentOS7","CentOS8");
    @Resource
    private RoleArnService roleArnService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;
    @Resource
    private ClusterService clusterService;
    @Resource
    protected DBaasMetaService dBaasMetaService;

    private final Cache<String, String> resourceCache = CacheBuilder.newBuilder()
            .maximumSize(64)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();


    /**
     * {
     * "cn-beijing" : "imageId1,imageId2",
     * "regionId" : ""
     * }
     *
     * @param clusterName
     * @param imageId
     * @param region
     * @return
     */
    public boolean checkImageId(Map<String, String> params, String clusterName, String imageId, String region, String arch){
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        String uid = mysqlParamSupport.getParameterValue(params, UID);

        String bizType = mysqlParamSupport.getAcountBizType(params);
        if (StringUtils.isBlank(imageId)) {
            logger.error("Invalid imageId, skip checkImageId");
            return false;
        }
        // 1.模拟用户，去请求获得对应imageId的信息
        try{
            List<DescribeImagesResponse.Image> imageInfo = getImageInfoByImageIdAndUid(requestId, region, uid, imageId);
            if(imageInfo.isEmpty()){
                logger.error("Invalid imageId, get empty imageInfo, skip checkImageId");
                return false;
            }
            // 2.如果查到了imageId，然后对imageId的OS进行检查,判断imageId是否合法
            List<DescribeImagesResponse.Image> allowedImageInfo = getAllowedImages(requestId, imageInfo, arch, false);
            if(allowedImageInfo.isEmpty()){
                logger.error("Invalid imageId, specific image is not allowed, imageIdInfo: {}, arch: {}", JSON.toJSONString(imageInfo), arch);
                return false;
            }
            // 3.判断资源账号是否已经有image
            EcsUserInfoDO ecsUserInfoDO = hostInfoMetaHelper.getEcsAccountByBizTypeAndDbType(bizType, "");
            String resourceUid = ecsUserInfoDO.getAliUid();
            List<DescribeImagesResponse.Image> resourceImageInfo = getImageInfoByImageIdAndUid(requestId, region, resourceUid, imageId);
            if(resourceImageInfo.isEmpty()){
                // 如果没查到imageId，调用方法进行镜像分享。
                shareImageToResourceAccount(requestId, region, resourceUid, uid, imageId);
            }
            return true;
        }catch (Exception e){
            logger.error("checkImageId failed, e.message: {}", e.getMessage());
            return false;
        }
    }

    public List<DescribeImagesResponse.Image> getImageInfoByImageIdAndUid(String requestId, String region, String uid, String imageId) throws RdsException {
        DescribeImagesRequest describeImagesRequest = new DescribeImagesRequest();
        describeImagesRequest.setRegionId(region);
        describeImagesRequest.setImageId(imageId);
        logger.info("RDS-RequestId : {}, getImageInfoBy ImageId: {} And Uid: {}, ecsRequest : {}", requestId, imageId, uid, JSONObject.toJSONString(describeImagesRequest));
        try {
            IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);

            logger.info("getImageInfoByImageIdAndUid request: {}", JSON.toJSONString(describeImagesRequest));
            DescribeImagesResponse describeImagesResponse = ramClient.getAcsResponse(describeImagesRequest);
            logger.info("getImageInfoByImageIdAndUid response: {}", JSON.toJSONString(describeImagesResponse));
            return describeImagesResponse.getImages();
        } catch (Exception e) {
            logger.error("modifyImageSharePermission failed. for : "+e.getMessage());
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, e.getMessage());
        }
    }

    public void shareImageToResourceAccount(String requestId, String region, String resourceUid, String uid, String imageId) throws RdsException {
        ModifyImageSharePermissionRequest modifyImageSharePermissionRequest = new ModifyImageSharePermissionRequest();
        // 把镜像分享给resourceUid
        modifyImageSharePermissionRequest.setImageId(imageId);
        modifyImageSharePermissionRequest.setAddAccounts(Collections.singletonList(resourceUid));
        modifyImageSharePermissionRequest.setRegionId(region);
        logger.info("RDS-RequestId : {}, before ecsInsApi.call_action, ecsRequest : {}", requestId, JSONObject.toJSONString(modifyImageSharePermissionRequest));
        try {
            IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);

            logger.info("modifyImageSharePermission request: {}", JSON.toJSONString(modifyImageSharePermissionRequest));
            ModifyImageSharePermissionResponse modifyImageSharePermissionResponse = ramClient.getAcsResponse(modifyImageSharePermissionRequest);
            logger.info("modifyImageSharePermission response: {}", JSON.toJSONString(modifyImageSharePermissionResponse));
        } catch (Exception e) {
            logger.error("modifyImageSharePermission failed. for : "+e.getMessage());
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, e.getMessage());
        }
    }

    public List<DescribeImagesResponse.Image> getAllowedImages(String requestId, List<DescribeImagesResponse.Image> originImages, String cpuArch, boolean skipArchCheck) throws RdsException {
        List<DescribeImagesResponse.Image> legalImages = new ArrayList<>();
        List<String> allowedImageOSNameList = getAllowedImageOsName(requestId);
        if(Objects.isNull(allowedImageOSNameList)){
            String errMessage = "getAllowedImageOsName failed. Please check resource data.";
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, errMessage);
        }
        for(DescribeImagesResponse.Image image: originImages){
            if(!skipArchCheck && !StringUtils.equals(getArchFromImageArchitecture(image.getArchitecture()), cpuArch)){
                continue;
            }
            if(checkImageOsName(image.getOSNameEn(), allowedImageOSNameList)){
                legalImages.add(image);
            }
        }
        logger.info("getAllowedImages, originImages: {}, allowedImages: {}", JSON.toJSONString(originImages), JSON.toJSONString(legalImages));
        return legalImages;
    }

    public boolean checkImageOsName(String oSName, List<String> allowedImageOSNameList){
        if(allowedImageOSNameList.isEmpty()){
            logger.info("checkImageOsName success, for get empty allowedImageOSNameList.");
            return true;
        }
        for(String oSNamePrefix: allowedImageOSNameList){
            if(StringUtils.replace(oSName, " ","").startsWith(oSNamePrefix)){
                logger.info("checkImageOsName success, for oSName: {} startWith: {}.", oSName, oSNamePrefix);
                return true;
            }
        }
        logger.info("checkImageOsName failed, for oSName: {} allowedImageOSNameList: {}.", oSName, allowedImageOSNameList);
        return false;
    }

    public List<String> getAllowedImageOsName(String requestId){
        try {
            ConfigListResult configListResult = dBaasMetaService.getDefaultClient().listConfigs(requestId, RDS_CUSTOM_CTL);
            if (configListResult != null && CollectionUtils.isNotEmpty(configListResult.getItems())) {
                JSONObject rdsCustomCtlJsonObject= JSONObject.parseObject(configListResult.getItems().get(0).getValue());
                if(rdsCustomCtlJsonObject.containsKey(RDS_CUSTOM_CTL_IMAGE_FILTER_RULE)){
                    logger.info("getAllowedImageOsName, rdsCustomCtlJsonObject: {}", rdsCustomCtlJsonObject);
                    return (List<String>) rdsCustomCtlJsonObject.getOrDefault(RDS_CUSTOM_CTL_IMAGE_FILTER_RULE, DEFAULT_ALLOWED_IMAGE_OS_NAME_LIST);
                }
            }
        }catch (ApiException e) {
            logger.error("get RDS_CUSTOM_CTL failed for e: ", e.getMessage());
        }
        // 解析RDS_CUSTOM_CTL失败时，会返回DEFAULT_ALLOWED_IMAGE_OS_NAME_LIST
        return DEFAULT_ALLOWED_IMAGE_OS_NAME_LIST;
    }

    public String getArchFromImageArchitecture(String imageArchitecture){
        if(StringUtils.equals(imageArchitecture, "arm64")){
            return ARCH_ARM;
        }else if(StringUtils.equals(imageArchitecture, "x86_64")){
            return ARCH_X86;
        }
        return imageArchitecture;
    }

    public String getDefaultImageId(String requestId, String regionId, String arch, String uid) throws RdsException {
        String jsonStrValue = null;
        try {
            jsonStrValue = resourceCache.get(RDS_CUSTOM_DEFAULT_IMAGES, () -> {
                String value = "";
                ConfigListResult configListResult = dBaasMetaService.getDefaultClient().listConfigs(requestId, RDS_CUSTOM_DEFAULT_IMAGES);
                if (configListResult != null && CollectionUtils.isNotEmpty(configListResult.getItems())) {
                    value = configListResult.getItems().get(0).getValue();
                }
                return value;
            });
        } catch (ExecutionException e) {
            log.error("get resource {} error {}", RDS_CUSTOM_DEFAULT_IMAGES, JSONObject.toJSONString(e));
        }
        log.info("get image config {} from resource", jsonStrValue);

        String imageId = "";
        JSONObject imageConfig = JSONObject.parseObject(jsonStrValue);
        String uidKey = String.format("%s.%s.%s", uid, regionId, arch);
        String regionKey = String.format("%s.%s", regionId, arch);
        if (imageConfig.containsKey(uidKey)) {
            log.info("get image id by uid {}, region: {}, arch: {}", uid, regionId, arch);
            imageId = imageConfig.getString(uidKey);
        } else if (imageConfig.containsKey(regionKey)) {
            log.info("get image id by region: {}, arch: {}", regionId, arch);
            imageId = imageConfig.getString(regionKey);
        }
        if (StringUtils.isBlank(imageId)) {
            log.error("can not get default image id for region:{}, arch: {}, uid: {}", regionId, arch, uid);
            throw new RdsException(ErrorCode.INVALID_PARAM, "can not get default image id");
        }

        return imageId;
    }

    public Integer getImageSize(String requestId, String region, String uid, String imageId) throws RdsException {
        List<DescribeImagesResponse.Image> images = getImageInfoByImageIdAndUid(requestId, region, uid, imageId);
        if(Objects.isNull(images) || images.isEmpty()){
            return null;
        }
        return  images.get(0).getSize();
    }
}
