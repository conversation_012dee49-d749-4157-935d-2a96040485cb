package com.aliyun.dba.rdscustom.action.service.base;

import com.aliyun.dba.support.property.RdsException;

import java.util.Map;

public interface ECSActionService {

    default Map<String, String> initRcId2EcsIdMappping(Map<String, String> params) throws RdsException {
        return null;
    }

    default Map<String, Object> doAction(Map<String, String> params) throws RdsException {
        return null;
    }

    default Map<String, Object> doPreAction(Map<String, String> params, Map<String, String> mapping) throws RdsException {
        return null;
    }

    default Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        return null;
    }


}
