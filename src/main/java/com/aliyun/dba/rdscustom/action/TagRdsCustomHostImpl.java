package com.aliyun.dba.rdscustom.action;

import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.host.dataobject.HostInfoParamDO;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("rdscustomTagRdsCustomHostImpl")
public class TagRdsCustomHostImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(TagRdsCustomHostImpl.class);


    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private MysqlParameterHelper mysqlParaHelper;

    @Resource
    private HostService hostService;

    @Resource
    protected HostIDao hostIDao;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        String dedicatedHostNames = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS);
        if (StringUtils.isEmpty(dedicatedHostNames)) {
            logger.error("Invalid Host Names, skip tag TagRdsCustomHost");
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM);
        }
        Set<Integer> hostIds = mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(dedicatedHostNames);
        List<HostInfoParamDO> hostInfoParamDOList = new ArrayList<>();
        for (Integer hostId : hostIds) {
            HostInfoParamDO hostInfoParamDO = new HostInfoParamDO();
            hostInfoParamDO.setHostId(hostId);
            hostInfoParamDO.setName(RDS_CUSTOM_TAG_KEY);
            hostInfoParamDO.setValue(RDS_CUSTOM_TAG_VALUE_1);
            hostInfoParamDOList.add(hostInfoParamDO);
        }
        if (CollectionUtils.isNotEmpty(hostInfoParamDOList)) {
            logger.info("update hostinfo_param meta for hostInfoParamDOList.size(): {}", hostInfoParamDOList.size());
            hostService.batchAddHostInfoParam(hostInfoParamDOList);
        }

        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_REQUEST_ID, requestId);
        return data;
    }
}
