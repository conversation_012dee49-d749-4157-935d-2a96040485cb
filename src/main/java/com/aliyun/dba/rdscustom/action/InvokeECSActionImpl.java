package com.aliyun.dba.rdscustom.action;


import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionService;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;


import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("rdscustomInvokeECSActionImpl")
public class InvokeECSActionImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(InvokeECSActionImpl.class);

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;




    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> param) throws RdsException {
        String ecsAction = mysqlParamSupport.getParameterValue(param, PARAM_ECS_ACTION);
        if (StringUtils.isEmpty(ecsAction)) {
            logger.error("InvalidEcsAction, ecsAction is empty");
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM);
        }
        ECSActionService ecsActionService = ecsActionServiceFactory.getECSActionService(ecsAction);
        Map<String, String> rcId2EcsIdMapping = ecsActionService.initRcId2EcsIdMappping(param);
        ecsActionService.doPreAction(param, rcId2EcsIdMapping); // 对于部署集，rcId2EcsIdMapping都是null。这一步已经把ecsrequest里的kv都直接copy到param了
        Map<String, Object> data = ecsActionService.doAction(param);
        ecsActionService.doPostAction(param, data, rcId2EcsIdMapping); // param是传入的参数，data是ecsResponse的data
        return data;
    }

}
