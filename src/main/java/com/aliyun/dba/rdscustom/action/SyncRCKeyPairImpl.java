package com.aliyun.dba.rdscustom.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.host.dataobject.ExtendHostInfo;
import com.aliyun.dba.host.dataobject.ExtendHostQuery;
import com.aliyun.dba.host.idao.HostIDaoImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.ecs.model.v20140526.DescribeInstancesRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeInstancesResponse;
import com.aliyuncs.ecs.model.v20140526.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.aliyun.dba.rdscustom.action.service.base.*;
import com.aliyun.dba.rdscustom.action.service.*;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.PARAM_RC_INSTANCE_IDS;
import static com.aliyun.dba.support.property.ParamConstants.UID;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("rdscustomSyncRCKeyPairImpl")
public class SyncRCKeyPairImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyRCInstanceKeyPairImpl.class);
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected MysqlParameterHelper mysqlParaHelper;
    @Resource
    private GeneralECSActionService generalECSActionService;
    @Resource
    private AttachKeyPairECSActionService attachKeyPairECSActionService;
    @Resource
    private DetachKeyPairECSActionService detachKeyPairECSActionService;
    @Resource
    private ImportKeyPairECSActionService importKeyPairECSActionService;
    @Resource
    private DeleteKeyPairECSActionService deleteKeyPairECSActionService;
    @Resource
    private HostIDaoImpl hostIDao;
    private static final String DEDICATED_HOST_NAME= "dedicatedHostName";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        Map<String, Object> data = new HashMap<>();
        //查找这个用户下的所有rc实例
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        String clusterName = mysqlParamSupport.getParameterValue(params, PARAM_CLUSTER_NAME);
        // check cluster name
        if (org.apache.commons.lang3.StringUtils.isBlank(clusterName)) {
            String errMsg = String.format("requestId: %s, cluster name is empty!", requestId);
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        String vpcId = mysqlParamSupport.getParameterValue(params, PARAM_USER_VPC_ID, null);
        int pageNum = Integer.parseInt(mysqlParamSupport.getParameterValue(params, ParamConstants.PAGE_NUMBERS, "1"));
        int pageSize = Integer.parseInt(mysqlParamSupport.getParameterValue(params, ParamConstants.MAX_RECORDS_PER_PAGE, "500"));
        String instanceId = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS, null);
        String uid = mysqlParamSupport.getParameterValue(params,UID);
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        List<Map<String, Object>> rcInstancesList = getRCInstanceList(uid, clusterName, vpcId, region, instanceId, pageNum, pageSize);
        String RCInstanceIds = "";
        for (Map<String, Object> rcInstance : rcInstancesList) {
            String rcInstanceName = (String) rcInstance.get(DEDICATED_HOST_NAME);
            if ("".equals(RCInstanceIds)) {
                RCInstanceIds = RCInstanceIds + rcInstanceName;
            } else {
                RCInstanceIds = RCInstanceIds + "," + rcInstanceName;
            }
        }
        logger.info("RCInstanceIds list:{}", RCInstanceIds);
        params.put(PARAM_RC_INSTANCE_IDS.toLowerCase(), RCInstanceIds);

        List<String> rcInstancesNameList = new ArrayList<>();
        Map<String, String> rcId2EcsIdMapping = generalECSActionService.initRcId2EcsIdMappping(params);
        for (Map<String, Object> rcInstance : rcInstancesList) {
            String rcInstanceName = (String) rcInstance.get(DEDICATED_HOST_NAME);
            rcInstancesNameList.add(rcId2EcsIdMapping.get(rcInstanceName));
        }
        logger.info("rcInstancesNameList:{}", JSONObject.toJSONString(rcInstancesNameList));
        List<String> rcInstancesChangeKeyPairList = new ArrayList<>();
        //通过ecs接口获取这些实例使用的密钥对，并筛选出使用当前密钥对的实例
        try {
            //实例数较多时，按照一批10个实例获取，避免一次请求过多
            int lens = rcInstancesNameList.size();
            int batch = 9;
            for (int i = 0; i < lens; ) {
                int start = i;
                int end = i + batch;
                if (end > lens) {
                    end = lens;
                }
                List<String> subNameList = rcInstancesNameList.subList(start, end);
                Map<String, Object> EcsResult = getECSbyKeyPairName(params, subNameList, region);
                Object value = EcsResult.get(ECS_PARAM_INSTANCE_ID.toLowerCase());
                if (value instanceof List) {
                    List<String> batchResult = (List<String>) value;
                    rcInstancesChangeKeyPairList.addAll(batchResult);
                } else {
                    throw new RdsException(ErrorCode.INVALID_PARAM, "rcInstancesChangeKeyPairList is not List");
                }
                i = i + batch + 1;
            }
        } catch (Exception e) {
            logger.error("get ecs extra info key pair failed");
        }
        if (rcInstancesChangeKeyPairList.isEmpty()) {
            data.put("rcInstancesChangeKeyPairList", rcInstancesChangeKeyPairList);
            return data;
        }
        RCInstanceIds = "";
        for (String rcInstancesChangeKeyPair:rcInstancesChangeKeyPairList) {
            if ("".equals(RCInstanceIds)) {
                RCInstanceIds = RCInstanceIds + rcInstancesChangeKeyPair;
            } else {
                RCInstanceIds = RCInstanceIds + "," + rcInstancesChangeKeyPair;
            }
        }
        //选出使用该密钥对的实例进行同步
        //需要先进行解绑，然后删除，再重新导入密钥，并绑定。
        try {
            params.put(PARAM_RC_INSTANCE_IDS.toLowerCase(), RCInstanceIds);
            rcId2EcsIdMapping = generalECSActionService.initRcId2EcsIdMappping(params);
            String keyPairName = mysqlParamSupport.getParameterValue(params, PARAM_ECS_SSH_KEYPAIR_NAME.toLowerCase());
            DetachKeyPairRequest detachKeyPairRequest = new DetachKeyPairRequest();
            detachKeyPairRequest.setKeyPairName(keyPairName);
            String ecsRequestStr = JSONObject.toJSONString(detachKeyPairRequest);
            params.put(PARAM_ECS_ACTION.toLowerCase(), ECS_DETACH_KEY_PAIR);
            params.put(PARAM_ECS_REQUEST.toLowerCase(), ecsRequestStr);
            params.put(PARAM_EXTRA_PARAM.toLowerCase(), JSONObject.toJSONString(rcInstancesChangeKeyPairList));
            detachKeyPairECSActionService.doPreAction(params, rcId2EcsIdMapping);
            detachKeyPairECSActionService.doAction(params);
            logger.info("rcInstances Change KeyPair first step, detach all ecs use this keypair");

            DeleteKeyPairsRequest deleteKeyPairRequest = new DeleteKeyPairsRequest();
            List<Object> deleteKeyPairList = new ArrayList<>();
            deleteKeyPairList.add(keyPairName);
            deleteKeyPairRequest.setKeyPairNames(JSONObject.toJSONString(deleteKeyPairList));
            ecsRequestStr = JSONObject.toJSONString(deleteKeyPairRequest);
            params.put(PARAM_ECS_ACTION.toLowerCase(), ECS_DELETE_KEY_PAIRS);
            params.put(PARAM_ECS_REQUEST.toLowerCase(), ecsRequestStr);
            int retry = 0;
            Boolean isDelete = false;
            while (retry < 30 && !isDelete) {
                try {
                    Map<String, Object> deleteResult = deleteKeyPairECSActionService.doAction(params);
                    String ecsResponse = (String) deleteResult.get(PARAM_ECS_RESPONSE);
                    if (StringUtils.isNotBlank(ecsResponse) && !ecsResponse.contains("KeyPair.AlreadyExist")) {
                        isDelete = true;
                    }
                } catch (Exception e) {
                    isDelete =false;
                }
                retry++;
            }
            logger.info("rcInstances Change KeyPair second step, delete old keypair,retry times:{}",retry);

            ImportKeyPairRequest importKeyPairRequest = new ImportKeyPairRequest();
            importKeyPairRequest.setKeyPairName(keyPairName);
            importKeyPairRequest.setPublicKeyBody(mysqlParamSupport.getParameterValue(params, PARAM_ECS_SSH_KEYPAIR_PUBLIC_KEY));
            ecsRequestStr = JSONObject.toJSONString(importKeyPairRequest);
            params.put(PARAM_ECS_ACTION.toLowerCase(), ECS_IMPORT_KEY_PAIR);
            params.put(PARAM_ECS_REQUEST.toLowerCase(), ecsRequestStr);
            importKeyPairECSActionService.doAction(params);
            logger.info("rcInstances Change KeyPair third step, import new keypair");

            AttachKeyPairRequest attachKeyPairRequest = new AttachKeyPairRequest();
            attachKeyPairRequest.setKeyPairName(keyPairName);
            ecsRequestStr = JSONObject.toJSONString(attachKeyPairRequest);
            params.put(PARAM_ECS_ACTION.toLowerCase(), ECS_ATTACH_KEY_PAIR);
            params.put(PARAM_ECS_REQUEST.toLowerCase(), ecsRequestStr);
            params.put(PARAM_EXTRA_PARAM.toLowerCase(), JSONObject.toJSONString(rcInstancesChangeKeyPairList));
            attachKeyPairECSActionService.doPreAction(params, rcId2EcsIdMapping);
            attachKeyPairECSActionService.doAction(params);
            logger.info("rcInstances Change KeyPair forth step, attach all ecs use this keypair");
        } catch (Exception e) {
            logger.error("failed to change public key for key pair:{}",params.get(PARAM_ECS_SSH_KEYPAIR_NAME));
        }
        data.put("rcInstancesChangeKeyPairList", rcInstancesChangeKeyPairList);
        return data;
    }

    //获取列表中的实例对应的密钥对
    public Map<String, Object> getECSbyKeyPairName(Map<String, String> params, List<String> ecsId, String region) throws RdsException {
        Map<String, Object> getECSResult = new HashMap<>();
        String keyPairName = mysqlParamSupport.getParameterValue(params, PARAM_ECS_SSH_KEYPAIR_NAME);
        List<String> ecsListWithKeyPair = new ArrayList<>();
        // 调用DescribeInstances接口获取keypairname
        DescribeInstancesRequest describeInstancesRequest = new DescribeInstancesRequest();
        describeInstancesRequest.setInstanceIds(JSONObject.toJSONString(ecsId));
        describeInstancesRequest.setRegionId(region);
        Map<String, String> paramsDescribeInstances = new HashMap<>(params);
        paramsDescribeInstances.put(PARAM_ECS_ACTION.toLowerCase(), ECS_ACTION_DESCRIBE_INSTANCES);
        paramsDescribeInstances.put(PARAM_ECS_REQUEST.toLowerCase(), JSONObject.toJSONString(describeInstancesRequest));
        Map<String, Object> resp = generalECSActionService.doAction(paramsDescribeInstances);
        // 解析PARAM_ECS_RESPONSE
        if (resp.containsKey(PARAM_ECS_RESPONSE)) {
            String ecsResponseStr = (String) resp.get(PARAM_ECS_RESPONSE);
            if (StringUtils.isBlank(ecsResponseStr)) {
                String errMsg = "get keypair info failed, params:" + params + ", resp: " + resp;
                logger.error(errMsg);
            }
            DescribeInstancesResponse describeInstancesResponse = JSONObject.parseObject(ecsResponseStr, DescribeInstancesResponse.class);
            if (describeInstancesResponse != null && !describeInstancesResponse.getInstances().isEmpty()) {
                for (DescribeInstancesResponse.Instance rcInstance : describeInstancesResponse.getInstances()) {
                    if (rcInstance.getKeyPairName() != null && rcInstance.getKeyPairName().equals(keyPairName)) {
                        logger.info("specific key pair name,ecs instance name:{}", rcInstance.getHostName());
                        ecsListWithKeyPair.add(rcInstance.getHostName());
                    }
                }
            }
            getECSResult.put(ECS_PARAM_INSTANCE_ID.toLowerCase(), ecsListWithKeyPair);
        }
        return getECSResult;
    }

    public List<Map<String, Object>> getRCInstanceList(String uid, String clusterName, String vpcId, String region, String instanceId, int pageNum, int pageSize) {
        // check param
        if (pageNum < 1 || pageSize < 1) {
            throw new IllegalArgumentException("pageNum and pageSize must be positive integers.");
        }
        // init gson
        Gson gson = new Gson();
        Type type = new TypeToken<Map<String, Object>>() {
        }.getType();
        // query
        ExtendHostQuery hostQuery = new ExtendHostQuery();
        hostQuery.setClusterName(clusterName);
        hostQuery.setPageFirst((pageNum - 1) * pageSize);
        hostQuery.setPageSize(pageSize);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(vpcId)) {
            hostQuery.setVpcId(vpcId);
        }if (org.apache.commons.lang3.StringUtils.isNotBlank(instanceId)) {
            hostQuery.setHostName(instanceId);
        }if (org.apache.commons.lang3.StringUtils.isNotBlank(region)) {
            hostQuery.setRegion(region);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(uid)) {
            hostQuery.setUid(uid);
        }

        List<ExtendHostInfo> hostInfoList = hostIDao.getHostInfoAndParamListWithClusterNameAndUID(hostQuery);
        logger.info("getRCInstanceList-hostInfoList : {}", gson.toJson(hostInfoList));
        // parse result
        LinkedHashMap<String, Map<String, Object>> rcInstanceMap = new LinkedHashMap<>(); // hostId->hostInfo
        for (ExtendHostInfo hostInfo : hostInfoList) {
            Map<String, Object> tmpRcInstance = gson.fromJson(gson.toJson(hostInfo), type);
            String hostIdKey = String.valueOf(tmpRcInstance.get(PARAM_HOST_ID));
            if(rcInstanceMap.containsKey(hostIdKey)){
                // rcInstanceList已经有对应的host信息了，获取name和value进行更新
                Map<String, Object> rcInstance = rcInstanceMap.get(hostIdKey);
                rcInstance.put((String) tmpRcInstance.get(PARAM_HOST_INFO_PARAM_NAME), tmpRcInstance.get(PARAM_HOST_INFO_PARAM_VALUE));
            }else{
                tmpRcInstance.put((String) tmpRcInstance.get(PARAM_HOST_INFO_PARAM_NAME), tmpRcInstance.get(PARAM_HOST_INFO_PARAM_VALUE));
                rcInstanceMap.put(hostIdKey, tmpRcInstance);
            }
        }

        List<Map<String, Object>> rcInstanceList = new ArrayList<>(rcInstanceMap.values());
        logger.info("getRCInstanceList-rcInstanceList: {}", rcInstanceList);
        return rcInstanceList;
    }

}
