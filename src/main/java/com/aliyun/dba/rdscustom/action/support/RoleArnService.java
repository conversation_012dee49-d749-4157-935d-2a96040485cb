package com.aliyun.dba.rdscustom.action.support;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccessKeyListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.poddefault.action.support.GAD.Aes;
import com.aliyun.dba.rdscustom.action.entity.ServiceAccountInfo;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.COMMON_PROVIDER_STS_ENDPOINT;

@Slf4j
@Service
public class RoleArnService {
    @Autowired
    private DBaasMetaService dBaasMetaService;
    // cache
    private static final Cache<String, String> resourceCache = CacheBuilder.newBuilder()
            .maximumSize(2048)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    public IAcsClient initRamClient(String requestId, String regionId, String aliUid) throws Exception {
        return initRamClient(requestId, regionId, "mysql", aliUid, "aliyun");
    }
    public IAcsClient initRamClient(String requestId, String regionId, String dbType, String aliUid, String bizType) throws Exception {
        try {
            String userName = dbType + "_service";
            //从ecs_user_info 表里面查询服务账号的ak/sk
            AccessKeyListResult accessKeyListResult = dBaasMetaService.getDefaultClient().listAccessKeys(requestId, userName, null);

            //角色扮演
            DefaultProfile profile = DefaultProfile.getProfile(regionId,
                    Aes.decryptAccountPasswd(accessKeyListResult.getItems().get(0).getAccessKeyIdEncrypted(), Aes.PWD_CRYPTKEY),
                    Aes.decryptAccountPasswd(accessKeyListResult.getItems().get(0).getAccessKeySecretEncrypted(), Aes.PWD_CRYPTKEY));
            IAcsClient client = new DefaultAcsClient(profile);

            String ROLE_ARN_PATTERN = "";
            String ASSUME_ROLE_SESSEION_NAME = "";
            ServiceAccountInfo serviceAccountInfo = getServiceAccountInfo(dbType, bizType);
            // 配置service account的情况下，优先使用service account
            ROLE_ARN_PATTERN = String.format("acs:ram::%s:role/%s", aliUid, serviceAccountInfo.getServiceAccountSlr());
            ASSUME_ROLE_SESSEION_NAME = serviceAccountInfo.getServiceAccountSlr();

            final AssumeRoleWithServiceIdentityRequest request = new AssumeRoleWithServiceIdentityRequest();
            request.setMethod(MethodType.POST);
            request.setProtocol(ProtocolType.HTTPS);
            request.setAcceptFormat(FormatType.JSON);
            request.setConnectTimeout(5000);
            request.setReadTimeout(5000);
            request.setRoleArn(ROLE_ARN_PATTERN);
            request.setRoleSessionName(ASSUME_ROLE_SESSEION_NAME);
            request.setDurationSeconds(900L);
            request.setAssumeRoleFor(aliUid);
            String stsEndpoint = getStsEndpoint(requestId, regionId);
            if (StringUtils.isNotEmpty(stsEndpoint)) {
                log.info("set sts sys endpoint: {}", stsEndpoint);
                request.setEndpoint(stsEndpoint);
            }
            final AssumeRoleWithServiceIdentityResponse response = client.getAcsResponse(request);
            final AssumeRoleWithServiceIdentityResponse.Credentials stsCredentials = response.getCredentials();

            final String accessKeyId = stsCredentials.getAccessKeyId();
            final String accessKeySecret = stsCredentials.getAccessKeySecret();
            final String securityToken = stsCredentials.getSecurityToken();

            profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret, securityToken);
            DefaultAcsClient defaultAcsClient = new DefaultAcsClient(profile);
            log.info("success init defaultAcsClient for region: {}, uid: {}", regionId, aliUid);
            return defaultAcsClient;
        } catch (ApiException e) {
            log.error("call meta db api failed, code: {}, message: {}", e.getCode(), e.getResponseBody());
            throw e;
        }
    }

    private ServiceAccountInfo getServiceAccountInfo(String dbType, String bizType) throws Exception {
        String resKey = "service_account_info_" + dbType + "_" + bizType;
        try {
            ConfigListResult result = dBaasMetaService.getDefaultClient().listConfigs(UUID.randomUUID().toString(), resKey);
            if (result.getItems() == null || result.getItems().isEmpty()) {
                return null;
            }
            String value = result.getItems().get(0).getValue();
            ServiceAccountInfo serviceAccountInfo = new Gson().fromJson(value, ServiceAccountInfo.class);
            log.info("{} {} service account: {}", dbType, bizType, serviceAccountInfo);
            return serviceAccountInfo;

        } catch (ApiException e) {
            String errMsg = String.format("MetaDB api called failed, code: %s, responseBody: %s", e.getCode(), e.getResponseBody());
            log.error(errMsg, e);
            throw e;
        }
    }

    public String getStsEndpoint(String requestId, String regionId) throws Exception {
        String stsEndpointJsonStr = resourceCache.get(COMMON_PROVIDER_STS_ENDPOINT, () -> {
            String value = "";
            ConfigListResult configListResult = dBaasMetaService.getDefaultClient().listConfigs(requestId, COMMON_PROVIDER_STS_ENDPOINT);
            if (configListResult != null && CollectionUtils.isNotEmpty(configListResult.getItems())) {
                value = configListResult.getItems().get(0).getValue();
            }
            return value;
        });
        if (StringUtils.isNotBlank(stsEndpointJsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(stsEndpointJsonStr);
            return jsonObject.getString(regionId);
        }
        return null;
    }
//
//
//    public IAcsClient initServiceRamClient(String regionId, String aliUid) throws Exception {
//
//        //角色扮演
//        DefaultProfile profile = DefaultProfile.getProfile(regionId,
//                "xxx",
//                "YzHSFJZgI6Vl812pArxtgA3vX5LDHb");
//        IAcsClient client = new DefaultAcsClient(profile);
//
//        String ROLE_ARN_PATTERN = "";
//        String ASSUME_ROLE_SESSEION_NAME = "";
//        // 配置service account的情况下，优先使用service account
//        ROLE_ARN_PATTERN = String.format("acs:ram::%s:role/%s", aliUid, "AliyunServiceRoleForRds");
//        ASSUME_ROLE_SESSEION_NAME = "AliyunServiceRoleForRds";
//
//        final AssumeRoleWithServiceIdentityRequest request = new AssumeRoleWithServiceIdentityRequest();
//        request.setMethod(MethodType.POST);
//        request.setProtocol(ProtocolType.HTTPS);
//        request.setAcceptFormat(FormatType.JSON);
//        request.setConnectTimeout(5000);
//        request.setReadTimeout(5000);
//        request.setRoleArn(ROLE_ARN_PATTERN);
//        request.setRoleSessionName(ASSUME_ROLE_SESSEION_NAME);
//        request.setDurationSeconds(900L);
//        request.setAssumeRoleFor(aliUid);
//        String stsEndpoint = "sts-inner.cn-beijing.aliyuncs.com";
//        if (StringUtils.isNotEmpty(stsEndpoint)) {
//            log.info("set sts sys endpoint: {}", stsEndpoint);
//            request.setEndpoint(stsEndpoint);
//        }
//        final AssumeRoleWithServiceIdentityResponse response = client.getAcsResponse(request);
//        final AssumeRoleWithServiceIdentityResponse.Credentials stsCredentials = response.getCredentials();
//
//        final String accessKeyId = stsCredentials.getAccessKeyId();
//        final String accessKeySecret = stsCredentials.getAccessKeySecret();
//        final String securityToken = stsCredentials.getSecurityToken();
//
//        profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret, securityToken);
//        DefaultAcsClient defaultAcsClient = new DefaultAcsClient(profile);
//        return defaultAcsClient;
//
//    }
}
