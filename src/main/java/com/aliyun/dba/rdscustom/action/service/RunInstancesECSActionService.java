package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.host.dataobject.HostInfoParamDO;
import com.aliyun.dba.host.dataobject.HostLevelDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.ECSApiHelper;
import com.aliyun.dba.rdscustom.action.support.EniService;
import com.aliyun.dba.rdscustom.action.support.HostInfoMetaHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.ecs.model.v20140526.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.*;

@Slf4j
@Service
public class RunInstancesECSActionService extends GeneralECSActionService {
    private static final LogAgent logger = LogFactory.getLogAgent(RunInstancesECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_RUN_INSTANCES_ACTION_KEY);
    private static final List<String> syncHostInfoParams = Arrays.asList(ECS_PARAM_DEPLOYMENT_SET_ID, ECS_PARAM_RESPONSE_DESCRIPTION);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;
    @Resource
    private EniService eniService;
    @Resource
    private ECSApiHelper ecsApiHelper;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;
    @Resource
    private WorkFlowService workFlowService;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }

    @Override
    public Map<String, String> initRcId2EcsIdMappping(Map<String, String> params) throws RdsException {
        String rcInstanceIds = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS);
        if (StringUtils.isEmpty(rcInstanceIds)) {
            String errMsg = "rcInstanceIds is empty, mapping is null";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        // only process one instance
        if (StringUtils.isBlank(rcInstanceIds)) {
            String errMsg = "rcInstanceIds is empty";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        Optional<String> rcInstanceIdOpt = Arrays.stream(rcInstanceIds.split(",")).findFirst();
        if (!rcInstanceIdOpt.isPresent()) {
            String errMsg = "rcInstanceIdOpt.isPresent is false";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        String rcInstanceId = rcInstanceIdOpt.get();
        // check exist
        if (hostInfoMetaHelper.checkHostInfoExist(rcInstanceId)) {
            String errMsg = "hostInfoMetaHelper.checkHostInfoExist";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        Map<String, String> rc2ecsMapping = new HashMap<>();
        logger.info("RunInstancesECSActionService, skip initRcId2EcsIdMappping, rc2ecsMapping is {}", rc2ecsMapping);
        return rc2ecsMapping;
    }


    @Override
    public Map<String, Object> doAction(Map<String, String> params) throws RdsException {
        logger.info("RunInstancesECSActionService doAction with params: "+params.toString());
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        // init uid
        String uid = mysqlParamSupport.getParameterValue(params, UID);
        // init extra param
        String extraParamStr = mysqlParamSupport.getParameterValue(params, PARAM_EXTRA_PARAM);
        Map<String, String> extraParams = JSONObject.parseObject(extraParamStr, new TypeReference<Map<String, String>>() {
        }.getType());
        extraParams = Objects.isNull(extraParams) ? new HashMap<>() : extraParams.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().toLowerCase(),
                        Map.Entry::getValue
                ));
        initExtraParams(params, extraParams);
        logger.info("requestId: {}, extraParams : {}", requestId, extraParams);
        if (hostInfoMetaHelper.checkAsync(requestId, uid)) {
            logger.info("requestId: {}, uid: {}, async create rds custom", requestId, uid);
            return createEcsAsync(extraParams);
        }
        return createEcsSync(extraParams);
    }

    private void initExtraParams(Map<String, String> params, Map<String, String> extraParams) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        // init bizType
        String bizType = mysqlParamSupport.getAcountBizType(params);
        // init userId
        String bid = mysqlParamSupport.getParameterValue(params, USER_ID);
        String uid = mysqlParamSupport.getParameterValue(params, UID);
        if (StringUtils.isBlank(bizType)) {
            String errMsg = "InvalidEcsAction, bizType is empty";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init dedicatedHostName
        String rcInstanceIds = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS);
        // only process one instance
        if (StringUtils.isBlank(rcInstanceIds)) {
            String errMsg = "rcInstanceIds is empty";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        Optional<String> rcInstanceIdOpt = Arrays.stream(rcInstanceIds.split(",")).findFirst();
        if (!rcInstanceIdOpt.isPresent()) {
            String errMsg = "rcInstanceIdOpt.isPresent is false";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        String rcInstanceId = rcInstanceIdOpt.get();

        // init host level info
        String hostLevelInfoStr = mysqlParamSupport.getParameterValue(params, PARAM_HOST_LEVEL_INFO);
        if (StringUtils.isBlank(hostLevelInfoStr)) {
            String errMsg = "hostLevelInfoStr is blank";
            logger.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init ecsRequest param
        String ecsRequestStr = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }

        RunInstancesRequest runInstancesRequest = JSONObject.parseObject(ecsRequestStr, RunInstancesRequest.class);
        // init user VSwitch
        String userVSwitchId = runInstancesRequest.getVSwitchId();
        String userSecurityGroupId = runInstancesRequest.getSecurityGroupId();

        mysqlParamSupport.setParameter(extraParams, PARAM_REQUEST_ID, requestId);
        mysqlParamSupport.setParameter(extraParams, REGION_ID, region);
        mysqlParamSupport.setParameter(extraParams, ACCOUNT_BIZ_TYPE, bizType);
        mysqlParamSupport.setParameter(extraParams, USER_ID, bid);
        mysqlParamSupport.setParameter(extraParams, UID, uid);
        mysqlParamSupport.setParameter(extraParams, PARAM_HOST_LEVEL_INFO, hostLevelInfoStr);
        mysqlParamSupport.setParameter(extraParams, PARAM_RC_INSTANCE_ID, rcInstanceId);
        mysqlParamSupport.setParameter(extraParams, PARAM_ECS_REQUEST, ecsRequestStr);
        mysqlParamSupport.setParameter(extraParams, PARAM_USER_VSWITCH_ID, userVSwitchId);
        mysqlParamSupport.setParameter(extraParams, PARAM_USER_SECURITY_GROUP_ID, userSecurityGroupId);
    }


    private Map<String, Object> createEcsSync(Map<String, String> extraParams) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(extraParams, PARAM_REQUEST_ID);
        // init ecsRequest
        String ecsRequestStr = mysqlParamSupport.getParameterValue(extraParams, PARAM_ECS_REQUEST);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(extraParams);
        // init bizType
        String bizType = mysqlParamSupport.getAcountBizType(extraParams);
        // init userId
        String uid = mysqlParamSupport.getParameterValue(extraParams, UID);
        // init rc instanceId
        String rcInstanceId = mysqlParamSupport.getParameterValue(extraParams, PARAM_RC_INSTANCE_ID);
        // init cluster
        String clusterName = mysqlParamSupport.getParameterValue(extraParams, PARAM_CLUSTER_NAME);
        // init zoneId
        String zoneId = mysqlParamSupport.getParameterValue(extraParams, PARAM_ZONE_ID);
        // init host level info
        String hostLevelInfoStr = mysqlParamSupport.getParameterValue(extraParams, PARAM_HOST_LEVEL_INFO);
        if (StringUtils.isBlank(hostLevelInfoStr)) {
            String errMsg = "hostLevelInfoStr is blank";
            logger.warn(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        HostLevelDO hostLevelDO = JSONObject.parseObject(hostLevelInfoStr, HostLevelDO.class);
        // init ecsRequest
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }

        RunInstancesRequest runInstancesRequest = JSONObject.parseObject(ecsRequestStr, RunInstancesRequest.class);
        String ecsInstanceId = null;
        String eniId = null;
        boolean success = false;
        try {
            // create ecs
            ecsInstanceId = ecsApiHelper.createSingleECSInResourceAccountWithCheck(runInstancesRequest, extraParams, true);
            // create eni
            CreateNetworkInterfaceResponse createNetworkInterfaceResponse = eniService.createEniInServiceAccount(extraParams);
            eniId = createNetworkInterfaceResponse.getNetworkInterfaceId();
            // attach eni
            eniService.attachEniToECS(ecsInstanceId, eniId, extraParams);
            // save meta
            hostInfoMetaHelper.saveRCMetaV2(createNetworkInterfaceResponse, hostLevelDO, runInstancesRequest, zoneId, clusterName, rcInstanceId, ecsInstanceId, region, bizType, requestId);
            success = true;
        } catch (Exception e) {
            String errMsg = String.format("runInstances failed, requestId: %s errMsg is %s", requestId, e.getMessage());
            logger.error(errMsg, e);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE, errMsg);
        } finally {
            if (!success) {
                log.info("start rollback create resource, eniId {}, ecsId {}", eniId, ecsInstanceId);
                if (StringUtils.isNotBlank(eniId)) {
                    eniService.deleteEni(requestId, uid, region, eniId);
                }
                if (StringUtils.isNotBlank(ecsInstanceId)) {
                    ecsApiHelper.deleteECSInResourceAccount(region, requestId, ecsInstanceId, bizType);
                }
            }
        }

        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_REQUEST_ID, requestId);
        data.put(PARAM_ECS_RESPONSE, rcInstanceId);
        data.put(PARAM_ENI_ID, eniId);
        return data;
    }

    private Map<String, Object> createEcsAsync(Map<String, String> extraParams) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(extraParams, PARAM_REQUEST_ID);
        // init ecsRequest
        String ecsRequestStr = mysqlParamSupport.getParameterValue(extraParams, PARAM_ECS_REQUEST);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(extraParams);
        // init bizType
        String bizType = mysqlParamSupport.getAcountBizType(extraParams);
        // init userId
        String uid = mysqlParamSupport.getParameterValue(extraParams, UID);
        // init rc instanceId
        String rcInstanceId = mysqlParamSupport.getParameterValue(extraParams, PARAM_RC_INSTANCE_ID);
        // init cluster
        String clusterName = mysqlParamSupport.getParameterValue(extraParams, PARAM_CLUSTER_NAME);
        // init zoneId
        String zoneId = mysqlParamSupport.getParameterValue(extraParams, PARAM_ZONE_ID);
        // init host level info
        String hostLevelInfoStr = mysqlParamSupport.getParameterValue(extraParams, PARAM_HOST_LEVEL_INFO);
        if (StringUtils.isBlank(hostLevelInfoStr)) {
            String errMsg = "hostLevelInfoStr is blank";
            logger.warn(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        HostLevelDO hostLevelDO = JSONObject.parseObject(hostLevelInfoStr, HostLevelDO.class);
        // init ecsRequest
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        logger.info("parse ecsRequestStr into runInstancesRequest:{}", ecsRequestStr);
        RunInstancesRequest runInstancesRequest = JSONObject.parseObject(ecsRequestStr, RunInstancesRequest.class);


        String ecsInstanceId = null;
        String eniId = null;
        Object taskId = null;
        boolean success = false;
        try {
            // create ecs
            ecsInstanceId = ecsApiHelper.createSingleECSInResourceAccountWithCheck(runInstancesRequest, extraParams, false);
            // create eni
            CreateNetworkInterfaceResponse createNetworkInterfaceResponse = eniService.createEniInServiceAccount(extraParams);
            eniId = createNetworkInterfaceResponse.getNetworkInterfaceId();
            // save meta
            hostInfoMetaHelper.saveRCMetaV2(createNetworkInterfaceResponse, hostLevelDO, runInstancesRequest, zoneId, clusterName, rcInstanceId, ecsInstanceId, region, bizType, requestId);
            // update extra param
            mysqlParamSupport.setParameter(extraParams, PARAM_ECS_ID, ecsInstanceId);
            mysqlParamSupport.setParameter(extraParams, PARAM_ENI_ID, eniId);
            taskId = workFlowService.dispatchTaskByPost("custins", rcInstanceId, "mysql", WORKFLOW_CREATE_RC_TASK_KEY, JSONObject.toJSONString(extraParams), 0, requestId);
            success = true;
        }catch (RdsException e){
            return ResponseSupport.createErrorResponse(e.getErrorCode());
        } catch (Exception e) {
            String errMsg = String.format("runInstances failed, requestId: %s errMsg is %s", requestId, e.getMessage());
            logger.error(errMsg, e);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE, errMsg);
        } finally {
            if (!success) {
                if (StringUtils.isNotBlank(eniId) || StringUtils.isNotBlank(ecsInstanceId)) {
                    log.info("start rollback create resource, eniId {}, ecsId {}", eniId, ecsInstanceId);
                    // update extra param
                    mysqlParamSupport.setParameter(extraParams, PARAM_ECS_ID, ecsInstanceId);
                    mysqlParamSupport.setParameter(extraParams, PARAM_ENI_ID, eniId);
                    deleteECSInResourceAccountAsync(requestId, rcInstanceId, extraParams);
                }
            }
        }
        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_REQUEST_ID, requestId);
        data.put(PARAM_ECS_RESPONSE, rcInstanceId);
        data.put(PARAM_TASK_ID, taskId);
        data.put(PARAM_ENI_ID, eniId);
        return data;
    }


    private void deleteECSInResourceAccountAsync(String requestId, String rcInstanceId, Map<String, String> extraParams) {
        Object taskId = null;
        try {
            taskId = workFlowService.dispatchTaskByPost("custins", rcInstanceId, "mysql", WORKFLOW_DELETE_RC_TASK_KEY, JSONObject.toJSONString(extraParams), 0, requestId);
        } catch (Exception e) {
            logger.error("deleteInstanceAsync dispatchTaskByPost failed ");
            return;
        }
        logger.info("requestId: {}, deleteECSInResourceAccountAsync taskId: {}", requestId, taskId);
    }

    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        if (StringUtils.isEmpty((String)result.get(PARAM_ECS_RESPONSE))) {
            return null;
        }

        String ecsRequest = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        JSONObject ecsRequestJSONObject = JSONObject.parseObject(ecsRequest);
        logger.info("now in doPostAction for runinstances, ecsRequest: "+ecsRequestJSONObject.toJSONString());
        Optional<Integer> hostId = (mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames((String) result.get(PARAM_ECS_RESPONSE))).stream().findFirst();
        if(!hostId.isPresent()){
            logger.error("hostId is empty");
            return null;
        }
        List<HostInfoParamDO> hostInfoParamDOList = new ArrayList<>();

        for(String syncParam : syncHostInfoParams){
            if (ecsRequestJSONObject.containsKey(syncParam)) {
                HostInfoParamDO hostInfoParamDO = new HostInfoParamDO();
                hostInfoParamDO.setHostId(hostId.get());
                hostInfoParamDO.setName(syncParam);
                hostInfoParamDO.setValue((String) ecsRequestJSONObject.get(syncParam));
                hostInfoParamDOList.add(hostInfoParamDO);
            }
        }
        String uid = mysqlParamSupport.getParameterValue(params,UID);
        if (StringUtils.isNotBlank(uid)) {
            HostInfoParamDO hostInfoParamDO = new HostInfoParamDO();
            hostInfoParamDO.setHostId(hostId.get());
            hostInfoParamDO.setName(UID.toLowerCase());
            hostInfoParamDO.setValue(uid);
            hostInfoParamDOList.add(hostInfoParamDO);
        }
        if (CollectionUtils.isNotEmpty(hostInfoParamDOList)) {
            logger.info("update hostinfo_param meta for hostInfoParamDOList.size(): {}, value: {}", hostInfoParamDOList.size(), JSONObject.toJSONString(hostInfoParamDOList));
            hostService.batchAddHostInfoParam(hostInfoParamDOList);
        }
        return null;
    }
}
