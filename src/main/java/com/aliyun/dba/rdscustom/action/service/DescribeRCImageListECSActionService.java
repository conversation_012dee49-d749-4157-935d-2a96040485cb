package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.HostInfoMetaHelper;
import com.aliyun.dba.rdscustom.action.support.ImageService;
import com.aliyun.dba.rdscustom.action.support.RoleArnService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecs.model.v20140526.DescribeImagesRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeImagesResponse;
import com.aliyuncs.ecs.model.v20140526.ModifyImageSharePermissionRequest;
import com.aliyuncs.ecs.model.v20140526.ModifyImageSharePermissionResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.UID;

@Slf4j
@Service
public class DescribeRCImageListECSActionService extends GeneralECSActionService {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeRCImageListECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_DESCRIBE_IMAGES_ACTION_KEY);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;
    @Resource
    private ImageService imageService;
    @Resource
    private RoleArnService roleArnService;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }

    @Override
    public Map<String, Object> doAction(Map<String, String> params) throws RdsException {
        // 这里需要扮演用户的角色去发送请求，拿到客户的imageList
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        String bizType = mysqlParamSupport.getAcountBizType(params);
        String uid = mysqlParamSupport.getParameterValue(params, UID);

        if (StringUtils.isBlank(bizType)) {
            String errMsg = "InvalidEcsAction, bizType is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init ecsRequest param
        String ecsRequestStr = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }

        DescribeImagesRequest describeImagesRequest = JSONObject.parseObject(ecsRequestStr, DescribeImagesRequest.class);
        logger.info("RDS-RequestId : {}, before ecsInsApi.call_action, ecsRequest : {}", requestId, JSONObject.toJSONString(describeImagesRequest));
        try {
            IAcsClient ramClient = roleArnService.initRamClient(requestId, region, uid);

            logger.info("describeImages request: {}", JSON.toJSONString(describeImagesRequest));
            DescribeImagesResponse describeImagesResponse = ramClient.getAcsResponse(describeImagesRequest);
            logger.info("describeImages response: {}", JSON.toJSONString(describeImagesResponse));
            describeImagesResponse.setRequestId(requestId);
            if(describeImagesResponse.getTotalCount()>0){
                String cpuArch = imageService.getArchFromImageArchitecture(describeImagesRequest.getArchitecture());
                if(StringUtils.isBlank(cpuArch)){
                    describeImagesResponse.setImages(imageService.getAllowedImages(requestId, describeImagesResponse.getImages(), cpuArch, true));
                }else{
                    describeImagesResponse.setImages(imageService.getAllowedImages(requestId, describeImagesResponse.getImages(), cpuArch, false));
                }

            }
            describeImagesResponse.setTotalCount(describeImagesResponse.getImages().size());
            Map<String, Object> data = new HashMap<>();
            data.put(PARAM_REQUEST_ID, requestId);
            data.put(PARAM_ECS_RESPONSE, JSONObject.toJSONString(describeImagesResponse));
            return data;
        } catch (Exception e) {
            logger.error("describeImagesResponse failed. for : "+e.getMessage());
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, e.getMessage());
        }
    }
}