package com.aliyun.dba.rdscustom.action;


import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionService;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("rdscustomModifyRCInstanceKeyPairImpl")
public class ModifyRCInstanceKeyPairImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyRCInstanceKeyPairImpl.class);
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected MysqlParameterHelper mysqlParaHelper;
    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ECSActionService ecsActionService = ecsActionServiceFactory.getECSActionService(ECS_DETACH_KEY_PAIR);
        Map<String, String> rcId2EcsIdMapping = ecsActionService.initRcId2EcsIdMappping(params);
        ecsActionService.doPreAction(params, rcId2EcsIdMapping);
        Map<String, Object> data = ecsActionService.doAction(params);
        ecsActionService.doPostAction(params, data, rcId2EcsIdMapping);

        ecsActionService = ecsActionServiceFactory.getECSActionService(ECS_ATTACH_KEY_PAIR);
        rcId2EcsIdMapping = ecsActionService.initRcId2EcsIdMappping(params);
        ecsActionService.doPreAction(params, rcId2EcsIdMapping);
        data = ecsActionService.doAction(params);
        ecsActionService.doPostAction(params, data, rcId2EcsIdMapping);
        return null;
    }
}
