package com.aliyun.dba.rdscustom.action.support;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AES;
import com.aliyuncs.ecs.model.v20140526.*;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Slf4j
@Service
public class ResourceAccountService {
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;


    private EcsInsApi getEcsClient(String region, String bizType, String dbType) throws RdsException {
        EcsUserInfoDO ecsUserInfoDO = hostInfoMetaHelper.getEcsAccountByBizTypeAndDbType(bizType, dbType);
        String accessKey = AES.decryptPassword(ecsUserInfoDO.getAccessKeyId(), RdsConstants.PASSWORD_KEY);
        String accessSecret = AES.decryptPassword(ecsUserInfoDO.getAccessKeySecretEncrypted(), RdsConstants.PASSWORD_KEY);
        return new EcsInsApi(region, accessKey, accessSecret);
    }

    public String getDefaultVpcId(String regionId, String bizType) throws RdsException {
        EcsInsApi ecsInsApi = getEcsClient(regionId, bizType, "");
        DescribeVpcsRequest request = new DescribeVpcsRequest();
        request.setRegionId(regionId);
        DescribeVpcsResponse response = null;
        try {
            response = (DescribeVpcsResponse) ecsInsApi.call_action(request);
        } catch (ClientException e) {
            log.error("call DescribeVpcs err, {}", JSONObject.toJSONString(e));
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, "can not get vpcId");
        }
        log.info("DescribeVpcs request {}, response {}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));
        if (response == null|| CollectionUtils.isEmpty(response.getVpcs())) {
            log.error("can not get vpcId by regionId: {}, bizType:{}", regionId, bizType);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, "can not get vpcId");
        }

        String defaultName = VPC_NAME_PREFIX + StringUtils.replace(regionId, "-", "_");
        for (DescribeVpcsResponse.Vpc vpc : response.getVpcs()) {
            if (StringUtils.equalsIgnoreCase(vpc.getVpcName(), defaultName)) {
                return vpc.getVpcId();
            }
        }
        log.error("no vpc match name {}", defaultName);
        throw new RdsException(ErrorCode.INTERNAL_FAILURE, "can not get vpcId");
    }

    public String getDefaultVswitchId(String regionId, String bizType, String zoneId, String vpcId) throws RdsException {
        EcsInsApi ecsInsApi = getEcsClient(regionId, bizType, "");
        DescribeVSwitchesRequest request = new DescribeVSwitchesRequest();
        request.setRegionId(regionId);
        request.setVpcId(vpcId);
        request.setZoneId(zoneId);
        DescribeVSwitchesResponse response = null;
        try {
            response = (DescribeVSwitchesResponse) ecsInsApi.call_action(request);
        } catch (ClientException e) {
            log.error("call DescribeVSwitches err, {}", JSONObject.toJSONString(e));
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, "can not get vswitch");
        }
        log.info("DescribeVSwitches request {}, response {}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));
        if (response == null|| CollectionUtils.isEmpty(response.getVSwitches())) {
            log.error("can not get vswitchId by regionId:{}, bizType:{}, zoneId:{}, vpcId:{}", regionId, bizType, zoneId, vpcId);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, "can not get vswitchId");
        }

        String defaultName = VSWITCH_NAME_PREFIX + StringUtils.replace(zoneId, "-", "_");
        for (DescribeVSwitchesResponse.VSwitch vSwitch : response.getVSwitches()) {
            if (StringUtils.equalsIgnoreCase(vSwitch.getVSwitchName(), defaultName)) {
                return vSwitch.getVSwitchId();
            }
        }
        log.error("no vswitch match name {}", defaultName);
        throw new RdsException(ErrorCode.INTERNAL_FAILURE, "can not get vswitchId");
    }

    public String getInitSecurityGroupId(String regionId, String bizType, String vpcId) throws RdsException {
        String initName = INIT_SECURITY_GROUP_NAME_PREFIX + StringUtils.replace(regionId, "-", "_");
        return getSecurityGroupId(regionId, bizType, vpcId, initName);
    }

    public String getDefaultSecurityGroupId(String regionId, String bizType, String vpcId) throws RdsException {
        String defaultName = SECURITY_GROUP_NAME_PREFIX + StringUtils.replace(regionId, "-", "_");
        return getSecurityGroupId(regionId, bizType, vpcId, defaultName);
    }
    private String getSecurityGroupId(String regionId, String bizType, String vpcId, String securityGroupName) throws RdsException {
        EcsInsApi ecsInsApi = getEcsClient(regionId, bizType, "");
        DescribeSecurityGroupsRequest request = new DescribeSecurityGroupsRequest();
        request.setRegionId(regionId);
        request.setVpcId(vpcId);
        request.setSecurityGroupName(securityGroupName);
        DescribeSecurityGroupsResponse response = null;
        try {
            response = (DescribeSecurityGroupsResponse) ecsInsApi.call_action(request);
        } catch (ClientException e) {
            log.error("call DescribeSecurityGroups err, {}", JSONObject.toJSONString(e));
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, "can not get SecurityGroup");
        }
        log.info("DescribeSecurityGroups request {}, respone {}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));
        if (response == null|| CollectionUtils.isEmpty(response.getSecurityGroups())) {
            log.error("can not get securityGroup by regionId:{}, bizType:{}, vpcId:{}", regionId, bizType, vpcId);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, "can not get securityGroup");
        }

        return response.getSecurityGroups().get(0).getSecurityGroupId();
    }
}
