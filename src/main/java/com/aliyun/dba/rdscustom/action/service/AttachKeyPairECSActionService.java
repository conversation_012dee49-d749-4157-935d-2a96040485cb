package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.ECSApiHelper;
import com.aliyun.dba.rdscustom.action.support.EcsInsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.AcsResponse;
import com.aliyuncs.ecs.model.v20140526.AttachKeyPairRequest;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.PARAM_ECS_RESPONSE;

@Slf4j
@Service
public class AttachKeyPairECSActionService extends GeneralECSActionService {

    private static final LogAgent logger = LogFactory.getLogAgent(AttachKeyPairECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_ATTACH_KEY_PAIR);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;

    @Resource
    private ECSApiHelper ecsApiHelper;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }

    @Override
    public Map<String, Object> doPreAction(Map<String, String> params, Map<String, String> mapping) throws RdsException {
        super.doPreAction(params,mapping);
        String extraParamStr = mysqlParamSupport.getParameterValue(params, PARAM_EXTRA_PARAM);
        if (StringUtils.isBlank(extraParamStr)) {
            return null;
        }
        String newExtraParamStr = extraParamStr;
        if (MapUtils.isNotEmpty(mapping)) {
            for (String rcId : mapping.keySet()) {
                newExtraParamStr = newExtraParamStr.replace(rcId, mapping.get(rcId));
            }
        }
        mysqlParamSupport.setParameter(params, PARAM_EXTRA_PARAM, newExtraParamStr);
        logger.info("doPreAction: after replace ExtraParam, param: {}", params);
        return null;
    }

    @Override
    public Map<String, Object> doAction(Map<String, String> params) throws RdsException {
        // init requestId
        String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(params);
        // init bizType
        String bizType = mysqlParamSupport.getAcountBizType(params);
        if (StringUtils.isBlank(bizType)) {
            String errMsg = "InvalidEcsAction, bizType is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init ecs action
        String ecsAction = mysqlParamSupport.getParameterValue(params, PARAM_ECS_ACTION);
        if (StringUtils.isBlank(ecsAction)) {
            String errMsg = "InvalidEcsAction, ecsAction is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        Class<?> requestClass = null;
        Class<?> responseClass = null;
        String requestClassName = String.format("%s%s%s", ECS_MODEL_PREFIX, ecsAction, ECS_REQUEST);
        String responseClassName = String.format("%s%s%s", ECS_MODEL_PREFIX, ecsAction, ECS_RESPONSE);
        try {
            requestClass = Class.forName(requestClassName);
            responseClass = Class.forName(responseClassName);
        } catch (ClassNotFoundException e) {
            String errMsg = String.format("requestClass [%s] or responseClass [%s] not found", requestClassName, responseClassName);
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init ecsRequest param
        String ecsRequestStr = mysqlParamSupport.getParameterValue(params, PARAM_ECS_REQUEST);
        if (StringUtils.isBlank(ecsRequestStr)) {
            String errMsg = "InvokeECSAction failed, ecsRequestStr is empty";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        // convert ecsRequest, from jsonString to AcsRequest
        AttachKeyPairRequest ecsRequest = JSONObject.parseObject(ecsRequestStr, AttachKeyPairRequest.class);
        String extraParamStr = mysqlParamSupport.getParameterValue(params, PARAM_EXTRA_PARAM);
        if (StringUtils.isNotBlank(extraParamStr)) {
            ecsRequest.setInstanceIds(extraParamStr);
        }
        logger.info("RDS-RequestId : {}, before ecsInsApi.call_action, ecsRequest : {}", requestId, JSONObject.toJSONString(ecsRequest));
        // init ecs api
        EcsInsApi ecsInsApi = ecsApiHelper.initECSApiByBizType(region, bizType, "");
        // call ecs api
        AcsResponse ecsResponse = null;
        try {
            ecsResponse = ecsInsApi.call_action(ecsRequest);
        } catch (ClientException e) {
            String errMsg = String.format("ecs call action failed, requestId: %s, [errCode: %s], [errMsg: %s]", requestId, e.getErrCode(), e.getErrMsg());
            logger.error(errMsg);
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, errMsg);
        }
        if (Objects.isNull(ecsResponse)) {
            String errMsg = "ecsInsApi.call_action failed, acsResponse is null";
            logger.error(errMsg);
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
        }
        logger.info("RDS-RequestId : {}, after ecsInsApi.call_action , ecsResponse : {}", requestId, JSONObject.toJSONString(ecsResponse));
        // modify ecsResponse.requestId
        try {
            Field requestIdField = responseClass.getDeclaredField(ECS_PARAM_REQUEST_ID);
            requestIdField.setAccessible(true);
            requestIdField.set(ecsResponse, requestId);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            String errMsg = String.format("ecsResponse [%s] has no requestIdField", JSONObject.toJSONString(ecsResponse));
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        Map<String, Object> data = new HashMap<>();
        data.put(PARAM_REQUEST_ID, requestId);
        data.put(PARAM_ECS_RESPONSE, JSONObject.toJSONString(ecsResponse));
        return data;
    }

}
