package com.aliyun.dba.rdscustom.action.support;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

import java.util.Base64;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.host.dataobject.HostLevelDO;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AES;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.ecs.model.v20140526.DeleteInstanceRequest;
import com.aliyuncs.ecs.model.v20140526.DeleteInstanceResponse;
import com.aliyuncs.ecs.model.v20140526.DescribeInstanceAttributeRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeInstanceAttributeResponse;
import com.aliyuncs.ecs.model.v20140526.DescribeInstanceStatusRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeInstanceStatusResponse;
import com.aliyuncs.ecs.model.v20140526.RunInstancesRequest;
import com.aliyuncs.ecs.model.v20140526.RunInstancesResponse;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ECSApiHelper {
    private static final LogAgent logger = LogFactory.getLogAgent(ECSApiHelper.class);

    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;
    @Resource
    private ImageService imageService;
    @Resource
    private ResourceAccountService resourceAccountService;

    @Resource
    protected DBaasMetaService dBaasMetaService;

    // ecs 创建超时时间，默认30s
    private final static Integer timeOutDefault = 60;
    // 删除检查次数，默认5次
    private final static Integer deleteCheckCnt = 6;
    // 删除检查间隔，默认10s
    private final static Integer deleteCheckDuration = 10000;


//
//    public static EcsInsApi initResourceEcsApi(String region) {
//        String accessKey = "xxx";
//        String accessSecret = "AlqhqxaIFtCNYGYAi4PhD99ukSr7xq";
//        return new EcsInsApi(region, accessKey, accessSecret);
//    }

    public EcsInsApi initECSApiByBizType(String region, String bizType, String dbType) throws RdsException {
        return initECSApi(region, hostInfoMetaHelper.getEcsAccountByBizTypeAndDbType(bizType, dbType));
    }

    private EcsInsApi initECSApi(String region, EcsUserInfoDO ecsUserInfoDO) {
        String accessKey =
                AES.decryptPassword(ecsUserInfoDO.getAccessKeyId(),
                        RdsConstants.PASSWORD_KEY);
        String accessSecret =
                AES.decryptPassword(ecsUserInfoDO.getAccessKeySecretEncrypted(),
                        RdsConstants.PASSWORD_KEY);
        return new EcsInsApi(region, accessKey, accessSecret);
    }


    public RunInstancesResponse createECSInResourceAccount(RunInstancesRequest runInstancesRequest, Map<String, String> extraParams) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(extraParams, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(extraParams);
        // init bizType, use Resource Account
        String bizType = mysqlParamSupport.getAcountBizType(extraParams);

        String uid = mysqlParamSupport.getUID(extraParams);
        // init cluster
        String clusterName = mysqlParamSupport.getParameterValue(extraParams, PARAM_CLUSTER_NAME);
        // init zoneId
        String zoneId = mysqlParamSupport.getParameterValue(extraParams, PARAM_ZONE_ID);
        if (StringUtils.isBlank(zoneId)) {
            String errMsg = String.format("zoneId is empty, requestId: %s", requestId);
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        // init host level info
        String hostLevelInfoStr = mysqlParamSupport.getParameterValue(extraParams, PARAM_HOST_LEVEL_INFO);
        HostLevelDO hostLevelDO = JSONObject.parseObject(hostLevelInfoStr, HostLevelDO.class);
        if (StringUtils.isBlank(hostLevelInfoStr) || Objects.isNull(hostLevelDO)) {
            String errMsg = String.format("hostLevelInfoStr [%s] or hostLevelDO [%s] is empty, requestId: %s", hostLevelInfoStr, hostLevelDO, requestId);
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS, errMsg);
        }
        // init arch
        String arch = hostInfoMetaHelper.getArchByHostType(hostLevelDO.getHostType());

        // init rc Instance
        String rcInstanceId = mysqlParamSupport.getParameterValue(extraParams, PARAM_RC_INSTANCE_ID);

        // init imageId
        String imageId = runInstancesRequest.getImageId();
        // if imageId is blank, use default imageId
        // else check imageId
        if (StringUtils.isBlank(imageId)) {
            imageId = imageService.getDefaultImageId(requestId, region, arch, uid);
        } else if (!imageService.checkImageId(extraParams, clusterName, imageId, region, arch)) {
            String errMsg = String.format("imageId is invalid, requestId: %s, imageId: %s, clusterName: %s", requestId, imageId, clusterName);
            logger.error(errMsg);
            throw new RdsException(new Object[]{400, "InvalidParameters.ImageId", errMsg});
        }
        //init system disk size
        Integer systemDiskSize = imageService.getImageSize(requestId, region, uid, imageId);
        if(Objects.isNull(systemDiskSize)){
            systemDiskSize = hostLevelDO.getDiskSize();
            String infoMsg = String.format("use host leve disk size, requestId: %s ", requestId);
            logger.info(infoMsg);
        }else if(systemDiskSize > hostLevelDO.getDiskSize()){
            String errMsg = String.format("imageId size is invalid, requestId: %s, imageId: %s, clusterName: %s", requestId, imageId, clusterName);
            logger.error(errMsg);
            throw new RdsException(new Object[]{400, "InvalidParameters.ImageId", errMsg});
        }

        String userVSwitchId = mysqlParamSupport.getParameterValue(extraParams, PARAM_USER_VSWITCH_ID);
        if (StringUtils.isBlank(userVSwitchId)) {
            String errMsg = String.format("userVSwitchId is not valid");
            logger.error(errMsg);
            throw new RdsException(new Object[]{400, "InvalidParameters.VSwitchId", errMsg});
        }

        String userSecurityGroupId = mysqlParamSupport.getParameterValue(extraParams, PARAM_USER_SECURITY_GROUP_ID);
        if (StringUtils.isBlank(userSecurityGroupId)) {
            String errMsg = String.format("userSecurityGroupId is not valid");
            logger.error(errMsg);
            throw new RdsException(new Object[]{400, "InvalidParameters.SecurityGroupId", errMsg});
        }

        String resourceVpcId = resourceAccountService.getDefaultVpcId(region, bizType);
        String resourceVSwitchId = resourceAccountService.getDefaultVswitchId(region, bizType, runInstancesRequest.getZoneId(), resourceVpcId);
        String resourceInitSecurityGroupId = resourceAccountService.getInitSecurityGroupId(region, bizType, resourceVpcId);
        if (StringUtils.isBlank(resourceInitSecurityGroupId) || StringUtils.isBlank(resourceVSwitchId)) {
            String errMsg = String.format("resourceInitSecurityGroupId or resourceSecurityGroupId is blank, requestId: %s, cluster: %s,resourceVSwitchId: %s, resourceSecurityGroupId: %s", requestId, clusterName, resourceVSwitchId, resourceInitSecurityGroupId);
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }

        String resourceSecurityGroupId = resourceAccountService.getDefaultSecurityGroupId(region, bizType, resourceVpcId);
        if (StringUtils.isBlank(resourceSecurityGroupId)) {
            String errMsg = String.format("resourceSecurityGroupId is blank, requestId: %s, cluster: %s, resourceSecurityGroupId: %s", requestId, clusterName, resourceSecurityGroupId);
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        }
        extraParams.put(PARAM_RESOURCE_SECURITY_GROUP_ID, resourceSecurityGroupId);

        // copy request
        RunInstancesRequest runInstancesRequest1 = JSONObject.parseObject(JSONObject.toJSONString(runInstancesRequest), RunInstancesRequest.class);

        // prepare runInstancesRequest
        runInstancesRequest1.setAmount(1);
        runInstancesRequest1.setVSwitchId(resourceVSwitchId);
        runInstancesRequest1.setSecurityGroupId(resourceInitSecurityGroupId);
        runInstancesRequest1.setInstanceChargeType(ECS_CHARGE_TYPE_POST_PAID);
        runInstancesRequest1.setAutoPay(true);
        runInstancesRequest1.setImageId(imageId);
        // init system disk
        runInstancesRequest1.setSystemDiskCategory(hostLevelDO.getDiskType());
        runInstancesRequest1.setSystemDiskSize(String.valueOf(systemDiskSize));
        // init host name
        runInstancesRequest1.setHostName(rcInstanceId);
        // init SecurityEnhancementStrategy
        runInstancesRequest1.setSecurityEnhancementStrategy(ECS_ACTIVE_SECURITY_ENHANCED);
        logger.info("RDS-RequestId : {}, before ecsInsApi.call_action, runInstancesRequest1 : {}", requestId, JSONObject.toJSONString(runInstancesRequest1));

        // init ecs api
        EcsInsApi ecsInsApi = initECSApiByBizType(region, bizType, "");
        // call ecs api
        RunInstancesResponse ecsResponse = null;
        try {
            ecsResponse = (RunInstancesResponse) ecsInsApi.call_action(runInstancesRequest1);
        } catch (ClientException e) {
            String errMsg = String.format("ecs call action failed, requestId: %s, [errCode: %s], [errMsg: %s]", requestId, e.getErrCode(), e.getErrMsg());
            logger.error(errMsg);
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, errMsg);
        }
        if (Objects.isNull(ecsResponse)) {
            String errMsg = "ecsInsApi.call_action failed, acsResponse is null";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, errMsg);
        }

        logger.info("RDS-RequestId : {}, after ecsInsApi.call_action , ecsResponse : {}", requestId, JSONObject.toJSONString(ecsResponse));
        return ecsResponse;
    }


    public String createSingleECSInResourceAccountWithCheck(RunInstancesRequest runInstancesRequest, Map<String, String> extraParams, boolean isCheckStatus) throws RdsException {
        RunInstancesResponse runInstancesResponse = createECSInResourceAccount(runInstancesRequest, extraParams);
        if (Objects.isNull(runInstancesResponse) || CollectionUtils.isEmpty(runInstancesResponse.getInstanceIdSets())) {
            String errMsg = "CreateECSInResourceAccount failed, runInstancesResponse is null";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, errMsg);
        }
        Optional<String> ecsInstanceIdOpt = runInstancesResponse.getInstanceIdSets().stream().findFirst();
        if (!ecsInstanceIdOpt.isPresent()) {
            String errMsg = "CreateECSInResourceAccount failed, ecsInstanceIdOpt is null";
            logger.error(errMsg);
            throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, errMsg);
        }
        String ecsInstanceId = ecsInstanceIdOpt.get();
        if (!isCheckStatus) {
            return ecsInstanceId;
        }
        String requestId = mysqlParamSupport.getParameterValue(extraParams, PARAM_REQUEST_ID);
        // init region param
        String region = mysqlParamSupport.getAndCheckRegionID(extraParams);
        // init bizType, use Resource Account
        String bizType = mysqlParamSupport.getAcountBizType(extraParams);
        // wait ecs running
        try {
            waitEcsStatus(region, requestId, ecsInstanceId, bizType, ECS_RUNNING_STATUS);
        } catch (Exception e) {
            String errMsg = String.format("ECSApiHelper.createSingleECSInResourceAccountWithCheck failed, requestId: %s errMsg is %s", requestId, e.getMessage());
            logger.error(errMsg, e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, errMsg);
        }
        return ecsInstanceId;
    }


    public void deleteECSInResourceAccount(String region, String requestId, String ecsInstanceId, String bizType) {
        try {
            // init ecs api
            EcsInsApi ecsInsApi = initECSApiByBizType(region, bizType, "");
            DeleteInstanceRequest deleteInstanceRequest = new DeleteInstanceRequest();
            deleteInstanceRequest.setForce(true);
            deleteInstanceRequest.setInstanceId(ecsInstanceId);
            logger.info("{}, before rollback ecs, deleteInstanceRequest : {}", requestId, JSONObject.toJSONString(deleteInstanceRequest));
            DeleteInstanceResponse ecsResponse = (DeleteInstanceResponse) ecsInsApi.call_action(deleteInstanceRequest);
            log.info("{}, after rollback ecs, deleteInstanceResponse: {}", requestId, JSONObject.toJSONString(ecsResponse));
        } catch (Exception e) {
            log.error("rollback create ecs failed, requestId: {}, exception {}", requestId, JSONObject.toJSONString(e));
        }
    }


    public DescribeInstanceAttributeResponse describeECSAttributeInResourceAccount(String region, String requestId, String ecsInstanceId, String bizType) {
        try {
            // init ecs api
            EcsInsApi ecsInsApi = initECSApiByBizType(region, bizType, "");
            DescribeInstanceAttributeRequest describeInstanceAttributeRequest = new DescribeInstanceAttributeRequest();
            describeInstanceAttributeRequest.setInstanceId(ecsInstanceId);
            // call ecs api
            return (DescribeInstanceAttributeResponse) ecsInsApi.call_action(describeInstanceAttributeRequest);
        } catch (RdsException e) {
            String errMsg = String.format("describeECSAttributeInResourceAccount-initECSApiByBizType failed, requestId: %s errMsg is %s", requestId, e.getMessage());
            logger.error(errMsg);
            throw new RuntimeException(e);
        } catch (ClientException e) {
            String errMsg = String.format("ecs call action failed, requestId: %s, [errCode: %s], [errMsg: %s]", requestId, e.getErrCode(), e.getErrMsg());
            logger.error(errMsg);
            throw new RuntimeException(e);
        }
    }


    public DescribeInstanceStatusResponse.InstanceStatus describeECSStatusInResourceAccount(String region, String requestId, String ecsInstanceId, String bizType) {
        try {
            // check parameters
            if (StringUtils.isBlank(ecsInstanceId)) {
                String errMsg = String.format("describeECSStatusInResourceAccount-ecsInstanceId is invalid, requestId: %s ecsInstanceId is %s", requestId, ecsInstanceId);
                logger.error(errMsg);
                throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
            }
            // init ecs api
            EcsInsApi ecsInsApi = initECSApiByBizType(region, bizType, "");
            DescribeInstanceStatusRequest describeInstanceStatusRequest = new DescribeInstanceStatusRequest();
            describeInstanceStatusRequest.setInstanceIds(Collections.singletonList(ecsInstanceId));
            describeInstanceStatusRequest.setRegionId(region);
            logger.info("describeECSStatusInResourceAccount-request is {}", JSONObject.toJSONString(describeInstanceStatusRequest));
            // call ecs api
            DescribeInstanceStatusResponse response = (DescribeInstanceStatusResponse) ecsInsApi.call_action(describeInstanceStatusRequest);
            if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getInstanceStatuses())) {
                String errMsg = String.format("describeECSStatusInResourceAccount-response is empty, requestId: %s response is %s", requestId, JSONObject.toJSONString(response));
                logger.error(errMsg);
                throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
            }
            // check result
            for (DescribeInstanceStatusResponse.InstanceStatus instanceStatus : response.getInstanceStatuses()) {
                if (ecsInstanceId.equalsIgnoreCase(instanceStatus.getInstanceId())) {
                    logger.info("describeECSStatusInResourceAccount-instanceStatus is {}", JSONObject.toJSONString(instanceStatus));
                    return instanceStatus;
                }
            }
            String errMsg = String.format("describeECSStatusInResourceAccount-response is invalid, requestId: %s response is %s", requestId, JSONObject.toJSONString(response));
            logger.error(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, errMsg);
        } catch (RdsException e) {
            String errMsg = String.format("describeECSStatusInResourceAccount-initECSApiByBizType failed, requestId: %s errMsg is %s", requestId, e.getMessage());
            logger.error(errMsg);
            throw new RuntimeException(e);
        } catch (ClientException e) {
            String errMsg = String.format("ecs call action failed, requestId: %s, [errCode: %s], [errMsg: %s]", requestId, e.getErrCode(), e.getErrMsg());
            logger.error(errMsg);
            throw new RuntimeException(e);
        }
    }


    public void waitEcsStatus(String region, String requestId, String ecsInstanceId, String bizType, String status) throws Exception {
        String currentStatus = "";
        for (int i = 0; i < timeOutDefault; i++) {
            try {
                DescribeInstanceStatusResponse.InstanceStatus instanceStatus = describeECSStatusInResourceAccount(region, requestId, ecsInstanceId, bizType);
                currentStatus = instanceStatus.getStatus();
                if (status.equalsIgnoreCase(currentStatus)) {
                    return;
                }
            } catch (Exception e) {
                log.error("waitEcsStatus Exception, ErrMsg: {}", e.getMessage());
            }
            if (i == 0) {
                Thread.sleep(1500);
            } else {
                Thread.sleep(500);
            }
        }
        String errMsg = String.format("wait ecs %s status to %s timeout, current status %s failed", ecsInstanceId, status, currentStatus);
        throw new Exception(errMsg);
    }

    public void preCheckDelete(EcsInsApi ecsInsApi, String ecsInstanceId, String requestId) throws Exception {
        for (int i = 0; i < deleteCheckCnt; i++) {
            try {
                DeleteInstanceRequest deleteInstanceRequest = new DeleteInstanceRequest();
                deleteInstanceRequest.setForce(true);
                deleteInstanceRequest.setInstanceId(ecsInstanceId);
                deleteInstanceRequest.setDryRun(true);
                logger.info("RDS-RequestId : {}, preCheckDelete before ecsInsApi.call_action, deleteInstanceRequest : {}", requestId, JSONObject.toJSONString(deleteInstanceRequest));
                // call ecs api
                DeleteInstanceResponse ecsResponse = (DeleteInstanceResponse) ecsInsApi.call_action(deleteInstanceRequest);
                logger.info("RDS-RequestId : {}, preCheckDelete after ecsInsApi.call_action, deleteInstanceRequest : {}", requestId, JSONObject.toJSONString(ecsResponse));
            } catch (ClientException e) {
                if (ECS_DRY_RUN_PASSED_CODE.equalsIgnoreCase(e.getErrCode())) {
                    return;
                }
            }
            Thread.sleep(deleteCheckDuration);
        }
        String errMsg = String.format("preCheckDelete ecs %s timeout", ecsInstanceId);
        logger.warn(errMsg);
        throw new Exception(errMsg);
    }

}
