package com.aliyun.dba.rdscustom.action.support;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Service
public class ParamsCheckUtils {
    private static final LogAgent logger = LogFactory.getLogAgent(ParamsCheckUtils.class);
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;

    private ReplicaSet getAndCheckUserReplicaSet(Map<String, String> params) throws ApiException, RdsException {
        DefaultApi metaApi = dBaasMetaService.getDefaultClient();
        String bid = mysqlParamSupport.getParameterValue(params, ParamConstants.USER_ID);
        String uid = mysqlParamSupport.getParameterValue(params, ParamConstants.UID);
        String loginId = String.format("%s_%s", bid, uid);

        String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        ReplicaSet replicaSet = metaApi.getReplicaSet(requestId, dbInstanceName, true);
        if (replicaSet == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        if (!loginId.equals(replicaSet.getUserId())) {
            logger.error("replicaSet is {} loginId is {} userid is {}", JSONObject.toJSONString(replicaSet), loginId, replicaSet.getUserId());
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return replicaSet;
    }
}
