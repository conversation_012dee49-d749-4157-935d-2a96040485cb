package com.aliyun.dba.rdscustom.action.entity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class ServiceAccountInfo {
    /*
    {
        "ServiceAccountUid": "****************", # 服务账号UID
        "ServiceAccountSlr": "AliyunServiceRoleForRDSProxyOnEcs", # 服务账号SLR
        "ResourceAccountToServiceAccountRole": "roletoassumerdsproxyslr" # 服务账号给资源账号授权的角色
    }
    * */

    @SerializedName("ServiceAccountUid")
    @Expose
    private String serviceAccountUid = null;

    @SerializedName("ServiceAccountSlr")
    @Expose
    private String serviceAccountSlr = null;

    @SerializedName("ResourceAccountToServiceAccountRole")
    @Expose
    private String resourceAccountToServiceAccountRole = null;
}
