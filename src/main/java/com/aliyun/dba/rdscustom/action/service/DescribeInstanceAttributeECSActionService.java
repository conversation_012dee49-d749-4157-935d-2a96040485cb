package com.aliyun.dba.rdscustom.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.Eni;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.host.dataobject.ExtendHostInfo;
import com.aliyun.dba.host.dataobject.ExtendHostQuery;
import com.aliyun.dba.host.dataobject.HostLevelDO;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
import com.aliyun.dba.rdscustom.action.service.base.GeneralECSActionService;
import com.aliyun.dba.rdscustom.action.support.EniService;
import com.aliyun.dba.rdscustom.action.support.HostInfoMetaHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.ecs.model.v20140526.DescribeInstanceAttributeResponse;
import com.aliyuncs.ecs.model.v20140526.DescribeNetworkInterfacesResponse;
import com.aliyuncs.ecs.model.v20140526.RunInstancesRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeDisksRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeDisksResponse;
import com.aliyuncs.ecs.model.v20140526.DescribeInstancesRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeInstancesResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;
import static com.aliyun.dba.support.property.ParamConstants.UID;

@Slf4j
@Service
public class DescribeInstanceAttributeECSActionService extends GeneralECSActionService {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeInstanceAttributeECSActionService.class);
    private static final List<String> supportECSActions = Collections.singletonList(ECS_DESCRIBE_INSTANCE_ATTRIBUTE_ACTION_KEY);

    @Resource
    private ECSActionServiceFactory ecsActionServiceFactory;
    @Resource
    private HostService hostService;
    @Resource
    private EniService eniService;
    @Resource
    private HostInfoMetaHelper hostInfoMetaHelper;
    @Resource
    private HostIDao hostIDao;

    @PostConstruct
    public void init() {
        for (String ecsAction : supportECSActions) {
            ecsActionServiceFactory.register(ecsAction, this);
        }
    }


    @Override
    public Map<String, Object> doPostAction(Map<String, String> params, Map<String, Object> result, Map<String, String> mapping) throws RdsException {
        super.doPostAction(params, result, mapping);
        if (result.containsKey(PARAM_ECS_RESPONSE)) {
            String requestId = mysqlParamSupport.getParameterValue(params, PARAM_REQUEST_ID);
            String region = mysqlParamSupport.getAndCheckRegionID(params);
            String uid = mysqlParamSupport.getParameterValue(params, UID);

            HashMap<String, Object> extraInfo = new HashMap<>();

            String rcInstanceIds = mysqlParamSupport.getParameterValue(params, PARAM_RC_INSTANCE_IDS);
            if (StringUtils.isEmpty(rcInstanceIds)) {
                logger.error("Invalid Host Names, skip update result");
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM);
            }
            Set<Integer> hostIds = mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(rcInstanceIds);
            if (CollectionUtils.isEmpty(hostIds)) {
                logger.warn("hostIds is empty, skip update result");
                return null;
            }
            Optional<Integer> hostId = hostIds.stream().findFirst();
            String rdsCustomLevelName = null;
            if (hostId.isPresent()) {
                HostLevelDO hostLevelDO = hostService.getHostLevelByHostId(hostId.get());
                extraInfo.put(ECS_PARAM_RESPONSE_DISK_TYPE, hostLevelDO.getDiskType());
                extraInfo.put(ECS_PARAM_RESPONSE_HOST_TYPE, hostInfoMetaHelper.getArchByHostType(hostLevelDO.getHostType()));
                rdsCustomLevelName = hostLevelDO.getName();
            }
            if (StringUtils.isBlank(rdsCustomLevelName)) {
                logger.warn("rdsCustomLevelName is empty, skip update result");
                return null;
            }

            // modify ecs response
            String ecsResponseStr = (String) result.get(PARAM_ECS_RESPONSE);
            if (StringUtils.isBlank(ecsResponseStr)) {
                String errMsg = "doPostAction ecsResponseStr is blank";
                logger.error(errMsg);
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM, errMsg);
            }
            DescribeInstanceAttributeResponse describeAttrResp = JSONObject.parseObject(ecsResponseStr, DescribeInstanceAttributeResponse.class);
            DescribeInstanceAttributeResponse.VpcAttributes vpcAttributes = describeAttrResp.getVpcAttributes();
            for (String rcInstanceId : rcInstanceIds.split(",")) {
                Eni eni = hostInfoMetaHelper.getEniIdByEcsId(requestId, mapping.get(rcInstanceId));
                if (eni == null) {
                    continue;
                }
                vpcAttributes.setVpcId(eni.getVpcId());
                vpcAttributes.setVSwitchId(eni.getVswitchId());
                try {
                    DescribeNetworkInterfacesResponse.NetworkInterfaceSet eniStatus = eniService.getEniStatus(requestId, uid, region, eni.getEniId());
                    if (eniStatus != null) {
                        vpcAttributes.setPrivateIpAddress(Collections.singletonList(eniStatus.getPrivateIpAddress()));
                        describeAttrResp.setSecurityGroupIds(eniStatus.getSecurityGroupIds());
                        log.info("got eniInfo {}, update SecurityGroupId response", eniStatus);
                    }
                } catch (Exception e) {
                    logger.error("update response from eniInfo error", e);
                }
            }


            describeAttrResp.setVpcAttributes(vpcAttributes);
            try{
                List<RunInstancesRequest.DataDisk> dataDiskList = getDataDiskInfo(params, mapping.get(rcInstanceIds), region);
                // 如果没有查到的话，是一个空的List
                extraInfo.put(ECS_PARAM_RESPONSE_DATA_DISKS, JSONObject.toJSONString(dataDiskList));
            }catch (Exception e) {
                logger.warn("get dataDiskList failed",e.getMessage());
            }
            // 如果没有查到deploymentSetId，则返回null
            ExtendHostQuery hostQuery = new ExtendHostQuery();
            hostQuery.setHostId(hostId.get());
            hostQuery.setQueryParamNames(Collections.singletonList(ECS_PARAM_DEPLOYMENT_SET_ID));
            List<ExtendHostInfo> hostInfoList = hostIDao.getHostInfoAndParamListWithClusterName(hostQuery);
            if(!hostInfoList.isEmpty()){
                logger.info("get deploymentSetId by getHostInfoAndParamListWithClusterName:", JSONObject.toJSONString(hostInfoList));
            }
            extraInfo.put(ECS_PARAM_DEPLOYMENT_SET_ID, hostInfoList.stream().findFirst().map(ExtendHostInfo::getParamValue).orElse(""));

            extraInfo.put(ECS_PARAM_ECS_INSTANCE_TYPE, describeAttrResp.getInstanceType());

            try{
                Map<String,Object> otherEcsInfo = getOtherEcsInfo(params, mapping.get(rcInstanceIds), region);
                extraInfo.put(PARAM_ECS_SSH_KEYPAIR_NAME, otherEcsInfo.get(PARAM_ECS_SSH_KEYPAIR_NAME));
            }catch (Exception e) {
                logger.warn("get info failed",e.getMessage());
            }

            result.put(PARAM_RESPONSE_EXTRA_INFO, extraInfo);
            describeAttrResp.setInstanceType(rdsCustomLevelName);
            result.put(PARAM_ECS_RESPONSE, JSONObject.toJSONString(describeAttrResp));
        }
        return null;
    }


    public List<RunInstancesRequest.DataDisk> getDataDiskInfo(Map<String, String> params, String ecsId, String region) throws RdsException, InvocationTargetException, IllegalAccessException {
        List<RunInstancesRequest.DataDisk> dataDiskList = new ArrayList<>();
        // 调用DescribeDisks接口
        DescribeDisksRequest describeDisksRequest = new DescribeDisksRequest();
        describeDisksRequest.setInstanceId(ecsId);
        describeDisksRequest.setRegionId(region);
        Map<String, String> paramsDescribeDisks = new HashMap<>(params);
        paramsDescribeDisks.put(PARAM_ECS_ACTION.toLowerCase(), "DescribeDisks");
        paramsDescribeDisks.put(PARAM_ECS_REQUEST.toLowerCase(), JSONObject.toJSONString(describeDisksRequest));
        Map<String, Object> resp = this.doAction(paramsDescribeDisks);
        // 解析PARAM_ECS_RESPONSE
        if (resp.containsKey(PARAM_ECS_RESPONSE)) {
            String ecsResponseStr = (String) resp.get(PARAM_ECS_RESPONSE);
            if (StringUtils.isBlank(ecsResponseStr)) {
                String errMsg = "get DataDisks info failed, params:"+params+", resp: "+resp;
                logger.error(errMsg);
            }
            DescribeDisksResponse describeDisksResponse = JSONObject.parseObject(ecsResponseStr, DescribeDisksResponse.class);
            for(DescribeDisksResponse.Disk respDisk : describeDisksResponse.getDisks()){
                if(StringUtils.equals(respDisk.getType(), ECS_DISK_TYPE_DATA)){
                    RunInstancesRequest.DataDisk dataDisk = new RunInstancesRequest.DataDisk();
                    // 先拷贝一下
                    BeanUtils.copyProperties(dataDisk, respDisk);
                    dataDisk.setEncrypted(String.valueOf(respDisk.getEncrypted()));
                    dataDisk.setSnapshotId(respDisk.getSourceSnapshotId());
                    dataDiskList.add(dataDisk);
                }
            }
        }
        return dataDiskList;
    }

    public Map<String,Object> getOtherEcsInfo(Map<String, String> params, String ecsId, String region) throws RdsException, InvocationTargetException, IllegalAccessException {
        Map<String,Object> otherEcsInfo = new HashMap<>();
        // 调用DescribeInstances接口获取keypairname
        DescribeInstancesRequest describeInstancesRequest = new DescribeInstancesRequest();
        List<Object> describeInstancesList = new ArrayList<>();
        describeInstancesList.add(ecsId);
        describeInstancesRequest.setInstanceIds(JSONObject.toJSONString(describeInstancesList));
        describeInstancesRequest.setRegionId(region);
        Map<String, String> paramsDescribeInstances = new HashMap<>(params);
        paramsDescribeInstances.put(PARAM_ECS_ACTION.toLowerCase(), ECS_ACTION_DESCRIBE_INSTANCES);
        paramsDescribeInstances.put(PARAM_ECS_REQUEST.toLowerCase(), JSONObject.toJSONString(describeInstancesRequest));
        Map<String, Object> resp = this.doAction(paramsDescribeInstances);
        // 解析PARAM_ECS_RESPONSE
        if (resp.containsKey(PARAM_ECS_RESPONSE)) {
            String ecsResponseStr = (String) resp.get(PARAM_ECS_RESPONSE);
            if (StringUtils.isBlank(ecsResponseStr)) {
                String errMsg = "get keypair info failed, params:"+params+", resp: "+resp;
                logger.error(errMsg);
            }
            DescribeInstancesResponse describeInstancesResponse = JSONObject.parseObject(ecsResponseStr, DescribeInstancesResponse.class);
            String keyPairName = describeInstancesResponse.getInstances().get(0).getKeyPairName();
            otherEcsInfo.put(PARAM_ECS_SSH_KEYPAIR_NAME,keyPairName);
        }
        return otherEcsInfo;
    }
}
