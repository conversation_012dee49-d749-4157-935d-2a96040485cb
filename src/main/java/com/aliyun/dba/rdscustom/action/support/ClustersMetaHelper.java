package com.aliyun.dba.rdscustom.action.support;

import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.aliyun.dba.custins.dataobject.ClusterParamDO;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class ClustersMetaHelper {
    private static final LogAgent logger = LogFactory.getLogAgent(ClustersMetaHelper.class);

    @Resource
    private ClusterIDao clusterIDao;

    public String getClusterNameByUserIdAndRegion(Integer userId, String regionId){
        Map<String, Object> map = new HashMap<>();
        map.put("user_id", userId);
        map.put("region", regionId);
        List<ClustersDO> clustersDOs = clusterIDao.getClusterListByMap(map);
        if (CollectionUtils.isEmpty(clustersDOs)) {
            logger.warn("get Empty from clusters by userId: "+userId+" , regionId: " + regionId);
        }
        return clustersDOs.get(0).getClusterName();
    }

    public ClusterParamDO getClusterValueByClusterNameAndName(String clusterName, String paramName){
        List<String> params = new ArrayList<String>(1);
        params.add(paramName);
        List<ClusterParamDO> clusterParams = clusterIDao.getClusterParams(clusterName, params);
        if(clusterParams.isEmpty()){
            logger.warn("get Empty from clusters by clusterName: "+clusterName+" , params: " + params);
            return null;
        }
        logger.info("get ClusterParamDO from clusters by clusterName: "+clusterName+" , paramName: " + paramName + " , ClusterParamDO: "+clusterParams.get(0));
        return clusterParams.get(0);
    }

    public void createClusterParams(String clusterName, String paramName, String paramValue){
        // 创建前先去查询是否已经存在(clusterName, paramName)，如果有的话，执行更新操作
        ClusterParamDO tmpClusterParamDO =getClusterValueByClusterNameAndName(clusterName, paramName);
        if(Objects.isNull(tmpClusterParamDO)){
            ClusterParamDO clusterParamDO = new ClusterParamDO();
            clusterParamDO.setClusterName(clusterName);
            clusterParamDO.setName(paramName);
            clusterParamDO.setValue(paramValue);
            clusterIDao.createClusterParams(clusterParamDO);
        }else{
            updateClusterParams(clusterName, paramName, paramValue);
        }
    }

    public void updateClusterParams(String clusterName, String paramName, String paramValue){
        ClusterParamDO clusterParamDO = new ClusterParamDO();
        clusterParamDO.setClusterName(clusterName);
        clusterParamDO.setName(paramName);
        clusterParamDO.setValue(paramValue);
        clusterIDao.updateClusterParamsValue(clusterParamDO);
        logger.info("updateClusterParams clusterParamDO: "+clusterParamDO);
    }
}
