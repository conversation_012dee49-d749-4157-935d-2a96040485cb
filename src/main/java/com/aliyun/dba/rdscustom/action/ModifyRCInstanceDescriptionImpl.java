package com.aliyun.dba.rdscustom.action;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.host.dataobject.HostInfoParamDO;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.rdscustom.action.service.base.ECSBaseActionService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("rdscustomModifyRCInstanceDescriptionImpl")
public class ModifyRCInstanceDescriptionImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyRCInstanceDescriptionImpl.class);
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected MysqlParameterHelper mysqlParaHelper;
    @Resource
    protected HostService hostService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException{
        String description = mysqlParamSupport.getParameterValue(params,ECS_PARAM_RESPONSE_DESCRIPTION);
        String hostName = mysqlParamSupport.getParameterValue(params,PARAM_RC_INSTANCE_IDS);

        Optional<Integer> hostId = (mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(hostName)).stream().findFirst();
        if(!hostId.isPresent()){
            logger.error("hostId is empty");
            return null;
        }
        List<HostInfoParamDO> hostInfoParamDOList = new ArrayList<>();
        if (StringUtils.isNotBlank(description)) {
            HostInfoParamDO hostInfoParamDO = new HostInfoParamDO();
            hostInfoParamDO.setHostId(hostId.get());
            hostInfoParamDO.setName(ECS_PARAM_RESPONSE_DESCRIPTION);
            hostInfoParamDO.setValue(description);
            hostInfoParamDOList.add(hostInfoParamDO);
        }
        if (CollectionUtils.isNotEmpty(hostInfoParamDOList)) {
            logger.info("update hostinfo_param meta for description, value: {}", JSONObject.toJSONString(hostInfoParamDOList));
            hostService.batchAddHostInfoParam(hostInfoParamDOList);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("description",description);
        return data;
    }
}
