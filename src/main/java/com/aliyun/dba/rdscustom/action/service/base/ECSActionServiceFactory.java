package com.aliyun.dba.rdscustom.action.service.base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.ECS_DEFAULT_ACTION_KEY;

@Slf4j
@Service
public class ECSActionServiceFactory {

    public Map<String, ECSActionService> serviceMap = new HashMap<>();

    public void register(String action, ECSActionService ecsActionService) {
        serviceMap.put(action, ecsActionService);

    }

    public ECSActionService getECSActionService(String action) {
        if (serviceMap.containsKey(action)) {
            return serviceMap.get(action);
        }
        return serviceMap.get(ECS_DEFAULT_ACTION_KEY);
    }

}
