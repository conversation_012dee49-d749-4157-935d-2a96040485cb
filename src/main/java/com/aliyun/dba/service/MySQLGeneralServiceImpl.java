package com.aliyun.dba.service;

import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.response.AllocateVipRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.dataobject.AccountDbRelDO;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.DbsDO;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.idao.DbsIDao;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AesCfb;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dbs.support.DbsSupport.ACCOUNT_TYPE_READONLY;
import static com.aliyun.dba.dbs.support.DbsSupport.ACCOUNT_TYPE_READWRITE;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;


@Service("mySQLGeneralService")
public class MySQLGeneralServiceImpl implements MySQLGeneralService {
    @Autowired
    private ResourceSupport resourceSupport;

    @Autowired
    private IpWhiteListService ipWhiteListService;

    @Autowired
    private ClusterService clusterService;

    @Autowired
    private CustinsParamService custinsParamService;

    @Autowired
    private MysqlDBCustinsService mysqlDBCustinsService;

    @Autowired
    private CustinsService custinsService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private CustinsIDao custinsIDao;

    @Autowired
    private DbsIDao dbsIDao;

    @Autowired
    private AccountIDao accountIDao;

    @Autowired
    private DbsService dbsService;

    @Autowired
    private InstanceIDao instanceIDao;

    @Autowired
    private InstanceService instanceService;


    @Autowired
    private ResApi resApi;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    private static final String DEFAULT_LOGIC_INS_CLASS_CODE = "dhg.general.default.logic";

    @Override
    public CustInstanceDO getMasterIns(Integer parentId) throws RdsException{
        List<CustInstanceDO> insList = getInsListByInsType(parentId, 0);
        if (insList.size() ==0) {
            throw new RdsException(MysqlErrorCode.MASTER_INS_NOT_FOUND.toArray());
        }else if (insList.size() >1){
            throw new RdsException(MysqlErrorCode.MASTER_INS_MORE_THAN_1.toArray());
        }
        return insList.get(0);
    };

    @Override
    public List<CustInstanceDO> getFollowerInsList(Integer parentId) throws RdsException{
        return getInsListByInsType(parentId, 3);
    };

    @Override
    public List<CustInstanceDO> getInsListByInsType(Integer parentId, Integer insType) throws RdsException{
         return custinsService.getCustInstanceUnitByParentIdAndInsType(parentId,insType);
    };



    @Override
    public CustInstanceDO getLogicIns(Integer userId , String groupName) throws RdsException{
        CustInstanceDO custInstanceDO = custinsService.getCustInstanceByInsName(userId, groupName);
        if(custInstanceDO == null){
            throw new RdsException(MysqlErrorCode.LOGIC_INS_NOT_FOUND.toArray());
        }
        return custInstanceDO;
    };

    @Override
    public InstanceLevelDO getDefaultLogicInsLevel(String dbType, String dbVersion) throws RdsException{
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(DEFAULT_LOGIC_INS_CLASS_CODE,
                dbType, dbVersion, null, null);
        if (insLevel == null){
            throw new RdsException(MysqlErrorCode.LOGIC_INS_DEFAULT_LEVEL_REQUIRED.toArray());
        }
        return insLevel;
    }

    @Override
    public Boolean basicNotSupport(CustInstanceDO custInstanceDO, String requestId) {
        try {
            InstanceLevelDO instanceLevelByLevelId = instanceService.getInstanceLevelByLevelId(custInstanceDO.getLevelId());
            if (!MysqlParamSupport.isBasic(instanceLevelByLevelId.getCategory())) {
                return false;
            }

            if (CustinsSupport.KIND_CODE_ECS_VM.equals(custInstanceDO.getKindCode())
                    || CustinsSupport.KIND_CODE_DOCKER_ON_ECS.equals(custInstanceDO.getKindCode())) {
                return true;

            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }


}

