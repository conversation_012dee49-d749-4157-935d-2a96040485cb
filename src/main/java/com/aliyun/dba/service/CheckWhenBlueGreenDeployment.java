package com.aliyun.dba.service;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025/02/27
 **/

@Service
public class CheckWhenBlueGreenDeployment {

	private static final Logger logger = LoggerFactory.getLogger(CheckWhenBlueGreenDeployment.class);

	@Autowired
	protected MysqlParamSupport mysqlParamSupport;
	@Autowired
	private CustinsService custinsService;
	@Autowired
	private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;


	private static final Set<String> blockActionSet = new HashSet<String>() {{
		// 创建网络链接
		this.add("createdbinstancenettype");
		// 删除网络链接
		this.add("deletedbinstancenettype");
		// io 突发，冷存归档，io 加速
		this.add("modifydbinstanceclass");
		// 云盘加密，磁盘压缩
		this.add("modifydbinstanceconfig");
		// TDE
		this.add("modifydbinstancetde");
		// SSL
		this.add("modifydbinstancessl");
		// 升级小版本
		this.add("upgradedbversion");
		// 	新增列加密规则   for 列加密
		this.add("createmaskingrules");
		// 	角色权限设置	for 列加密
		this.add("modifyaccountmaskingprivilege");
		// 升级大版本
		this.add("upgradedbmajorversion");
	}};

	public void check(Map<String, String> params) throws RdsException {
		try {
			String action = CustinsParamSupport.getAction(params);
			if (StringUtils.isBlank(action)) {
				return;
			}

			if (!blockActionSet.contains(action.toLowerCase())) {
				return;
			}
			logger.info("start check, action: {}", action);

			String dbInstanceName = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_NAME);
			if(StringUtils.isBlank(dbInstanceName)) {
				logger.info("dbInstanceName is blank, end check");
				return;
			}

			CustInstanceDO custInsDO = custinsService.getCustInstanceByInsName(null, dbInstanceName);
			if (custInsDO == null ) {
				logger.info("custInsDO is null, end check");
				return;
			}

			Integer kindCode = custInsDO.getKindCode();
			boolean isNewArchOrNC = CustinsSupport.KIND_CODE_NEW_ARCH.equals(kindCode) || CustinsSupport.KIND_CODE_NC.equals(kindCode);
			if (!isNewArchOrNC) {
				logger.info("custInsDO kindCode is not 18 or 0, end check");
				return;
			}

			BlueGreenDeploymentRel asBlue = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, Long.valueOf(custInsDO.getId()), null);
			BlueGreenDeploymentRel asGreen = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, null, Long.valueOf(custInsDO.getId()));

			if (asBlue == null && asGreen == null) {
				logger.info("custin {} is not in blue-green deployment, end check", custInsDO.getInsName());
				return;
			}
			if ((asBlue != null && asBlue.getStatus() == 0) || (asGreen != null && asGreen.getStatus() == 0)) {
				logger.info("custin {} is in blue-green deployment creating, end check", custInsDO.getInsName());
				return;
			}
			logger.error("action {} is not supported because of blue-green deployment in custin {}, end check", action, custInsDO.getInsName());
		} catch (Exception e) {
			logger.error("check error.",e);
			return;
		}
		throw new RdsException(ErrorCode.UNSUPPORTED_BY_BLUE_GREEN_DEPLOYMENT);
	}

}
