package com.aliyun.dba.service;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.property.RdsException;

import java.util.List;
import java.util.Map;

public interface BlueGreenDeploymentService {
    Map<String, Object> createBlueGreenDeployment(String requestId, String regionId,
                                                  String aliUid,
                                                  CustInstanceDO custInstance,
                                                  Map<String, Object> newPrimaryConfig,
                                                  List<Map<String, Object>> newRoConfig,
                                                  List<Map<String, Object>> newNodeConfig,
                                                  Map<String, Object> newProxyConfig) throws RdsException;

    Map<String, Object> switchBlueGreenInstance(String regionId, String aliUid, String bid, CustInstanceDO custInstanceDO, String greenInstanceName, String deploymentName, Map<String, Object> switchInfo) throws Exception;

    Map<String, Object> deleteBlueGreenDeployment(String regionId, String aliUid, CustInstanceDO custInstance, String deploymentName, String mode) throws Exception;

    Map<String, Object> describeBlueGreenSyncInfo(CustInstanceDO custins, String requestId, String regionId, String aliUid, String dbInstanceName) throws Exception;

    Map<String, Object> switchBlueGreenInstancePreCheck(String regionId, String aliUid, String bid, CustInstanceDO blueCustInstance, String blueGreenDeploymentName, String dbInstanceName, String greenDBInstanceId, Boolean skipStatusCheck) throws Exception;

    Map<String, Object> switchHostinsPerfMeta(Integer blueInstanceId, Integer greenInstanceId);
}