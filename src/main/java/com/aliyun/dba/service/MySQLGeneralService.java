package com.aliyun.dba.service;

import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsIpWhiteListDO;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.DbsDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TransListDO;

import java.util.List;
import java.util.Map;

public interface MySQLGeneralService {

    //Block physical ins
    CustInstanceDO getMasterIns(Integer parentId) throws RdsException;
    List<CustInstanceDO> getFollowerInsList(Integer parentId) throws RdsException;
    List<CustInstanceDO> getInsListByInsType(Integer parentId, Integer insType) throws RdsException;


    //Block logic ins
    CustInstanceDO getLogicIns(Integer userId , String groupName) throws RdsException;
    InstanceLevelDO getDefaultLogicInsLevel(String dbType, String dbVersion) throws RdsException;

    /**
     * docker on ecs 和 ecs不支持basic版本SSL
     * @param custInstanceDO
     * @param requestId
     * @return
     */
    Boolean basicNotSupport(CustInstanceDO custInstanceDO, String requestId);

}
