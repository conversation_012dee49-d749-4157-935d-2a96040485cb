package com.aliyun.dba.service;

import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("mySQLAvzService")
public class MysqlAvzServiceImpl implements MySQLAvzService {

    public static final String[] ROLES_FOR_ONE = new String[]{"master"};
    public static final String[] ROLES_FOR_TWO = new String[]{"master", "slave"};
    public static final String[] ROLES_FOR_THREE = new String[]{"master", "follower", "logger"};

    @Override
    public Map<String, AvailableZoneInfoDO> getRoleToAvzInfo(AVZInfo avzInfo) throws RdsException {
        Map<String, AvailableZoneInfoDO> roleToAvzInfo = new HashMap<>();
        if (avzInfo.isMultiAVZExParamDOEmpty()) {
            return roleToAvzInfo;
        }

        List<AvailableZoneInfoDO> avzInfoList = avzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList();
        String[] roles;
        switch (avzInfoList.size()) {
            case 2:
                roles = ROLES_FOR_TWO;
                break;
            case 3:
                roles = ROLES_FOR_THREE;
                break;
            default:
                roles = ROLES_FOR_ONE;
                break;
        }
        for (int i = 0; i < roles.length; i++) {
            roleToAvzInfo.put(roles[i], avzInfoList.get(i));
        }
        return roleToAvzInfo;
    }

    /**
     * 多可用区时，通过role找到对应的可用区配置；单可用区直接构建AvailableZoneInfoDO返回
     * */
    @Override
    public AvailableZoneInfoDO getRoleZoneId(AVZInfo avzInfo, String role) throws RdsException {
        if (avzInfo.getDispenseMode() == ParamConstants.DispenseMode.MultiAVZDispenseMode) {
            Map<String, AvailableZoneInfoDO> roleToAvzInfo = getRoleToAvzInfo(avzInfo);
            if (!roleToAvzInfo.containsKey(role)) {
                throw new RdsException(ErrorCode.INVALID_AVZONE);
            }
            return roleToAvzInfo.get(role);
        } else {
            return MultiAVZExParamDO.SingleAVZParam(avzInfo.getMasterZoneId(), avzInfo.getMasterVSwitchId()).get(0);
        }

    }
}
