package com.aliyun.dba.service;

import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.response.AllocateVipRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.dataobject.AccountDbRelDO;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.DbsDO;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.idao.DbsIDao;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AesCfb;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.service.UserService;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.aliyun.dba.support.property.RdsException;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_ACCOUNT_MODE_NOT_SUPPORT_UPGRADE;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED;
import static com.aliyun.dba.custins.support.CustinsSupport.NEW_CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE;
import static com.aliyun.dba.custins.support.CustinsSupport.NEW_CUSTINS_ACCOUNT_MODE_NOT_SUPPORT;
import static com.aliyun.dba.custins.support.CustinsSupport.NEW_CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED;
import static com.aliyun.dba.dbs.support.DbsSupport.ACCOUNT_TYPE_READONLY;
import static com.aliyun.dba.dbs.support.DbsSupport.ACCOUNT_TYPE_READWRITE;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;


@Service("mySQLService")
public class MySQLServiceImpl implements MySQLService {
    @Autowired
    private ResourceSupport resourceSupport;

    @Autowired
    private IpWhiteListService ipWhiteListService;

    @Autowired
    private ClusterService clusterService;

    @Autowired
    private CustinsParamService custinsParamService;

    @Autowired
    private MysqlDBCustinsService mysqlDBCustinsService;

    @Autowired
    private CustinsService custinsService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private CustinsIDao custinsIDao;

    @Autowired
    private DbsIDao dbsIDao;

    @Autowired
    private AccountIDao accountIDao;

    @Autowired
    private DbsService dbsService;

    @Autowired
    private InstanceIDao instanceIDao;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private ResApi resApi;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Autowired
    protected UserService userService;

    @Override
    public void setCustinAccountMode(CustInstanceDO custins, String accountName, String subDomain, String accountType) {
        if (custins.isSupportSuperAccountMode()) {
            if (custins.isMysqlGt57() && !StringUtils.isBlank(accountName)) {
                custins.setAccountMode(CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED);
            } else {
                custins.setAccountMode(CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE);
            }
        } else {
            custins.setAccountMode(CUSTINS_ACCOUNT_MODE_NOT_SUPPORT_UPGRADE);
        }

        // 新增实例是否开启 accountMode 开关
        Boolean globalSwitch;
        try {
            globalSwitch = resourceSupport.getBooleanRealValue(
                    ResourceKey.CUSTINS_CREATE_WITH_NEW_ACCOUNT_MODE_GLOBAL_SWITCH);
        } catch (Exception e) {
            globalSwitch = false;
        }
        // 集群黑名单
        Boolean clusterNotInBlackList;
        try {
            // 临时改为子域作为黑名单, 因为瑶池不传 cluster
            String clusters = resourceSupport.getStringRealValue(
                    ResourceKey.CUSTINS_CREATE_WITH_NEW_ACCOUNT_MODE_BLACK_CLUSTERS);
            if (Strings.isNullOrEmpty(clusters)) {
                clusterNotInBlackList = true;
            } else {
                clusterNotInBlackList = !Arrays.asList(clusters.split(",")).contains(subDomain);
            }
        } catch (Exception e) {
            clusterNotInBlackList = true;
        }
        // 全局开关打开, 并且实例集群不再黑名单里, 表示权限放开
        if (globalSwitch && clusterNotInBlackList) {
            if (!custins.isSupportSuperAccountMode()) {
                custins.setAccountMode(NEW_CUSTINS_ACCOUNT_MODE_NOT_SUPPORT);
            } else {
                custins.setAccountMode(NEW_CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE);
            }
        }

        //专有云要求5.6创建实例直接设置为2
        if(custins.isSupportSuperAccountMode() && custinsService.isInAPCEnvironment()){
            if("1".equalsIgnoreCase(accountType) && custins.getAccountMode().intValue() != NEW_CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED){
                custins.setAccountMode(CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED);
            }
        }

        if (custins.isSuperAccountMode()) {
            custins.setMaxDbs(0);
            custins.setMaxAccounts(1);
        } else {
            custins.setMaxDbs(500);
            custins.setMaxAccounts(500);
        }
    }

    @Override
    public Integer createExchangeInstanceTask(String action, CustInstanceDO custins,
                                              CustInstanceDO peerins, Integer operatorId,
                                              TransListDO translist, String taskKey)
        throws RdsException {
        Map<String, Object> taskparam = new HashMap<>();
        return createExchangeInsTaskWithParam(action, custins, operatorId, translist, taskKey, taskparam);
    }

    @Override
    public Integer createExchangeInsTaskWithParam(String action, CustInstanceDO custins, Integer operatorId,
                                                  TransListDO translist, String taskKey, Map<String, Object> taskParam) {
        instanceIDao.createTransList(translist);
        taskParam.put(CustinsSupport.TRANS_ID, translist.getId());

        TaskQueueDO taskQueue = null;
        taskQueue = new TaskQueueDO(action, operatorId, custins.getId(), TASK_TYPE_CUSTINS, taskKey);

        taskQueue.setParameter(JSONObject.toJSONString(taskParam));
        taskService.createTaskQueue(taskQueue);
        instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
        return taskQueue.getId();
    }

    /**
     * @param custins
     * @param custinsConnAddrPrivate 私网连接
     * @param custinsConnAddrVPC     VPC连接
     * @return
     * @throws RdsException
     */
    @Override
    public List<ConnAddrChangeLogDO> createConnAddrChangeLogsForCreateVpcNetType(CustInstanceDO custins, CustinsConnAddrDO custinsConnAddrPrivate, CustinsConnAddrDO custinsConnAddrVPC, String region) throws RdsException {
        if ((custinsConnAddrPrivate != null && !CustinsSupport.NET_TYPE_PRIVATE.equals(custinsConnAddrPrivate.getNetType()))
                || !CustinsSupport.NET_TYPE_VPC.equals(custinsConnAddrVPC.getNetType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
        }

        Integer nIsSwitchNetType = 0;
        if (custinsConnAddrPrivate != null && custinsConnAddrPrivate.getConnAddrCust().equals(custinsConnAddrVPC.getConnAddrCust())) {//it is switching from classic private_net to VPC
            nIsSwitchNetType = 1;
        }
        // 申请VPC IP
        Response<AllocateVipRespModel> response = resApi.allocateVip(custins.getId(), CustinsSupport.NET_TYPE_VPC,
                custinsConnAddrVPC.getConnAddrCust(),
                nIsSwitchNetType,
                custinsConnAddrVPC.getVip(),
                custinsConnAddrVPC.getVswitchId(),
                custinsConnAddrVPC.getTunnelId(),
                custinsConnAddrVPC.getVpcId(),
                custinsConnAddrVPC.getVpcInstanceId(),
                null,
                "",
                region);
        if (!response.getCode().equals(200)) {
//            this.createResourceRecord(custins.getClusterName(), custins.getInsName(),
//                    JSON.toJSONString(response));
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }

        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(3);
        if (nIsSwitchNetType == 1) {// 旧的私网vip删除
            ConnAddrChangeLogDO delConnAddrLogForPrivate = ConnAddrSupport
                    .createConnAddrChangeLogForDeleteNetType(
                            custins.getId(),
                            CustinsSupport.NET_TYPE_PRIVATE,
                            custinsConnAddrPrivate.getConnAddrCust(),
                            custinsConnAddrPrivate.getVip(),
                            custinsConnAddrPrivate.getVport(),
                            custinsConnAddrPrivate.getUserVisible(),
                            0,
                            "",
                            null);//VswitchId为null
            connAddrChangeLogs.add(delConnAddrLogForPrivate);
        }

        // 添加新的VPC
        ConnAddrChangeLogDO addConnAddrLogForVPC = ConnAddrSupport
                .createConnAddrChangeLogForCreateNetType(
                        custins.getId(),
                        CustinsSupport.NET_TYPE_VPC,
                        custinsConnAddrVPC.getConnAddrCust(), //设置为私网的连接地址
                        custinsConnAddrVPC.getVip(),
                        custinsConnAddrVPC.getVport(),
                        CustinsConnAddrDO.USER_VISIBLE_YES,
                        custinsConnAddrVPC.getTunnelId(),
                        custinsConnAddrVPC.getVpcId(),
                        custinsConnAddrVPC.getVswitchId(),
                        CustinsSupport.RW_TYPE_NORMAL);

        // 保证顺序
        connAddrChangeLogs.add(addConnAddrLogForVPC);

        return connAddrChangeLogs;
    }

    @Override
    public List<ConnAddrChangeLogDO> createConnAddrChangeLogsForCreateVpcNetType(CustInstanceDO custins, CustinsConnAddrDO custinsConnAddrVPC, String region) throws RdsException {
        // 申请VPC IP
        Response<AllocateVipRespModel> response = resApi.allocateVip(custins.getId(), CustinsSupport.NET_TYPE_VPC,
                custinsConnAddrVPC.getConnAddrCust(),
                0,
                custinsConnAddrVPC.getVip(),
                custinsConnAddrVPC.getVswitchId(),
                custinsConnAddrVPC.getTunnelId(),
                custinsConnAddrVPC.getVpcId(),
                custinsConnAddrVPC.getVpcInstanceId(),
                null,
                "",
                region);
        if (!response.getCode().equals(200)) {
//            this.createResourceRecord(custins.getClusterName(), custins.getInsName(),
//                    JSON.toJSONString(response));
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }
        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(3);
        // 添加新的VPC
        ConnAddrChangeLogDO addConnAddrLogForVPC = ConnAddrSupport
                .createConnAddrChangeLogForCreateNetType(
                        custins.getId(),
                        CustinsSupport.NET_TYPE_VPC,
                        custinsConnAddrVPC.getConnAddrCust(), //设置为私网的连接地址
                        custinsConnAddrVPC.getVip(),
                        custinsConnAddrVPC.getVport(),
                        CustinsConnAddrDO.USER_VISIBLE_NO,
                        custinsConnAddrVPC.getTunnelId(),
                        custinsConnAddrVPC.getVpcId(),
                        custinsConnAddrVPC.getVswitchId(),
                        CustinsSupport.RW_TYPE_NORMAL);

        // 保证顺序
        connAddrChangeLogs.add(addConnAddrLogForVPC);

        return connAddrChangeLogs;
    }

    @Override
    public List<ConnAddrChangeLogDO> createConnAddrChangeLogsForCreateVpcNetTypeWithoutPrivateIP(CustInstanceDO custins, CustinsConnAddrDO custinsConnAddrVPC, Integer rwType, Integer isSwitch, String region) throws RdsException {
        if (!CustinsSupport.NET_TYPE_VPC.equals(custinsConnAddrVPC.getNetType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
        }
        // 申请VPC IP
        Response<AllocateVipRespModel> response = resApi.allocateVip(custins.getId(), CustinsSupport.NET_TYPE_VPC,
                custinsConnAddrVPC.getConnAddrCust(),
                isSwitch,
                custinsConnAddrVPC.getVip(),
                custinsConnAddrVPC.getVswitchId(),
                custinsConnAddrVPC.getTunnelId(),
                custinsConnAddrVPC.getVpcId(),
                custinsConnAddrVPC.getVpcInstanceId(),
                rwType, "", region);
        if (!response.getCode().equals(200)) {
//            this.createResourceRecord(custins.getClusterName(), custins.getInsName(),
//                    JSON.toJSONString(response));
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }
        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(1);
        // 添加新的VPC
        ConnAddrChangeLogDO addConnAddrLogForVPC = ConnAddrSupport
                .createConnAddrChangeLogForCreateNetType(
                        custins.getId(),
                        CustinsSupport.NET_TYPE_VPC,
                        custinsConnAddrVPC.getConnAddrCust(),
                        custinsConnAddrVPC.getVip(),
                        custinsConnAddrVPC.getVport(),
                        CustinsConnAddrDO.USER_VISIBLE_YES,
                        custinsConnAddrVPC.getTunnelId(),
                        custinsConnAddrVPC.getVpcId(),
                        custinsConnAddrVPC.getVswitchId(),
                        rwType);

        connAddrChangeLogs.add(addConnAddrLogForVPC);
        return connAddrChangeLogs;
    }

    @Override
    public List<ConnAddrChangeLogDO> createConnAddrChangeLogsForCreatePublicOrPrivateNetType(CustInstanceDO custins, CustinsConnAddrDO newCustinsConnAddr, Integer rwType) throws RdsException {
        String netTye = null;
        Response<AllocateVipRespModel> response = resApi.allocateVip(custins.getId(), newCustinsConnAddr.getNetType(),
                newCustinsConnAddr.getConnAddrCust(), 0, null,
                null, null, null, null, rwType, netTye, "");
        if (!response.getCode().equals(200)) {
//            this.createResourceRecord(custins.getClusterName(), custins.getInsName(),
//                    JSON.toJSONString(response));
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }

        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(1);

        boolean supportVip6 = true;
        if (response.getData().getVipV6() != null) {
            if (custins.isMongoDB()) {
                String minorVersion = null;
                CustinsParamDO custinsParam =
                        custinsParamService.getCustinsParam(custins.getId(), "minor_version");
                if (custinsParam != null) {
                    minorVersion = custinsParam.getValue();
                    if (minorVersion != null) {
                        minorVersion = minorVersion.substring(minorVersion.lastIndexOf("_") + 1);
                    }
                }
                if (custins.getDbVersion().compareTo("4.0") < 0 || minorVersion == null || minorVersion.compareTo("3.0.9") < 0) {
                    supportVip6 = false;
                    resApi.releaseVip(custins.getId(), response.getData().getVipV6());
                }
            }
            if (supportVip6) {
                ConnAddrChangeLogDO connAddrChangeLogV6 = ConnAddrSupport
                        .createConnAddrChangeLogForCreateNetType(
                                custins.getId(),
                                newCustinsConnAddr.getNetType(),
                                newCustinsConnAddr.getConnAddrCust(),
                                response.getData().getVipV6(),//新申请的vip
                                newCustinsConnAddr.getVport(),
                                newCustinsConnAddr.getUserVisible(),
                                0,
                                null,
                                null, //VswitchId为null
                                rwType);
                connAddrChangeLogs.add(connAddrChangeLogV6);
            }
        }

        ConnAddrChangeLogDO connAddrChangeLog = ConnAddrSupport
                .createConnAddrChangeLogForCreateNetType(
                        custins.getId(),
                        newCustinsConnAddr.getNetType(),
                        newCustinsConnAddr.getConnAddrCust(),
                        response.getData().getVip(),//新申请的vip
                        newCustinsConnAddr.getVport(),
                        newCustinsConnAddr.getUserVisible(),
                        0,
                        null,
                        null, //VswitchId为null
                        rwType);
        connAddrChangeLogs.add(connAddrChangeLog);
        return connAddrChangeLogs;
    }

    @Override
    public String getDockerCustinsClusterEndpointType(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        try {
            DockerInsLevelExtraInfo info = new Gson().fromJson(instanceLevelDO.getExtraInfo(), DockerInsLevelExtraInfo.class);
            if (info.getClusterEndpoints() == null || info.getClusterEndpoints().size() < 1) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            String clusterEndpointType = mysqlParamSupport.getParameterValue(params, ParamConstants.CLUSTER_ENDPOINT_TYPE, "0");
            if (org.apache.commons.lang.StringUtils.isEmpty(clusterEndpointType)) {
                throw new RdsException(ErrorCode.INVALID_CLUSTER_ENDPOINT_TYPE);
            }
            boolean isEndpointTypeFound = false;
            for (DockerInsLevelExtraInfo.ClusterEndpointsInfo endpointsInfo : info.getClusterEndpoints()) {
                if (clusterEndpointType.equalsIgnoreCase(endpointsInfo.getRwType().toString())) {
                    isEndpointTypeFound = true;
                    break;
                }
            }
            if (!isEndpointTypeFound) {
                throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_ENDPOINT_TYPE);
            }
            return clusterEndpointType;
        } catch (RdsException e) {
            throw e;
        } catch (Exception e) {
            throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL_EXTRA_INFO);
        }
    }

    @Override
    public List<ConnAddrChangeLogDO> createConnAddrChangeLogsForDeleteVpcNetType(CustInstanceDO custins, CustinsConnAddrDO delCustinsConnAddr, CustinsConnAddrDO custinsConnAddrPrivate) throws RdsException {
        //获取新私网Ip
        ConnAddrChangeLogDO addPrivateConnAddrLog = null;
        if ((custinsConnAddrPrivate != null && custinsConnAddrPrivate.isConnAddrUserVisible()) ||
                (custins.isPolarDBMySQLRW() || custins.isPolarDBMySQLRO())) {
            //do nothing
        } else {
            Response<AllocateVipRespModel> response = resApi.allocateVip(custins.getId(), CustinsSupport.NET_TYPE_PRIVATE,
                    delCustinsConnAddr.getConnAddrCust(), 1, null, null, null, null, null, delCustinsConnAddr.getrwType());
            if (!response.getCode().equals(200)) {
//                this.createResourceRecord(custins.getClusterName(), custins.getInsName(),
//                        JSON.toJSONString(response));
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            if (delCustinsConnAddr.getUserVisible() != 0){
                // 对于用户可见的 才新增私网地址
                addPrivateConnAddrLog = ConnAddrSupport.createConnAddrChangeLogForCreateNetType(
                        custins.getId(),
                        CustinsSupport.NET_TYPE_PRIVATE,
                        delCustinsConnAddr.getConnAddrCust(),
                        response.getData().getVip(),
                        delCustinsConnAddr.getVport(),
                        CustinsConnAddrDO.USER_VISIBLE_YES,
                        0,
                        "",
                        null,
                        delCustinsConnAddr.getrwType());
            }

        }

        // 获取VswithcID
        String vswitchId = custinsService.getVswitchIdByVpcIpAndVpcId(
                delCustinsConnAddr.getVip(), delCustinsConnAddr.getVpcId());

        ConnAddrChangeLogDO delConnAddrChangeLog = ConnAddrSupport
                .createConnAddrChangeLogForDeleteNetType(
                        custins.getId(),
                        delCustinsConnAddr.getNetType(),
                        delCustinsConnAddr.getConnAddrCust(),
                        delCustinsConnAddr.getVip(),
                        delCustinsConnAddr.getVport(),
                        delCustinsConnAddr.getUserVisible(),
                        delCustinsConnAddr.getTunnelId(),
                        delCustinsConnAddr.getVpcId(),
                        vswitchId);

        // 删除绑定的经典网络NatIP(若存在，仅限隐藏)
        ConnAddrChangeLogDO deleteBindNatIpConnAddrLog = null;
        if (custinsConnAddrPrivate != null && !custinsConnAddrPrivate.isConnAddrUserVisible()) {
            deleteBindNatIpConnAddrLog = ConnAddrSupport
                    .createConnAddrChangeLogForDeleteNetType(
                            custins.getId(),
                            custinsConnAddrPrivate.getNetType(),
                            custinsConnAddrPrivate.getConnAddrCust(),
                            custinsConnAddrPrivate.getVip(),
                            custinsConnAddrPrivate.getVport(),
                            custinsConnAddrPrivate.getUserVisible(),
                            custinsConnAddrPrivate.getTunnelId(),
                            custinsConnAddrPrivate.getVpcId(),
                            null
                    );
        }

        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(2);
        if (deleteBindNatIpConnAddrLog != null)//删除绑定经典网络NatIP(若存在)
            connAddrChangeLogs.add(deleteBindNatIpConnAddrLog);
        if (addPrivateConnAddrLog != null)
            connAddrChangeLogs.add(addPrivateConnAddrLog);//添加新的私网链路
        connAddrChangeLogs.add(delConnAddrChangeLog);//删除VPC链路
        return connAddrChangeLogs;
    }

    @Override
    public List<ConnAddrChangeLogDO> createConnAddrChangeLogsForDeletePublicOrPrivateNetType(CustInstanceDO custins, CustinsConnAddrDO delCustinsConnAddr) throws RdsException {
        // 不支持VPC网络类型
        if (CustinsSupport.isVpcNetType(delCustinsConnAddr.getNetType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
        }

        ConnAddrChangeLogDO delConnAddrChangeLog = ConnAddrSupport
                .createConnAddrChangeLogForDeleteNetType(
                        custins.getId(),
                        delCustinsConnAddr.getNetType(),
                        delCustinsConnAddr.getConnAddrCust(),
                        delCustinsConnAddr.getVip(),
                        delCustinsConnAddr.getVport(),
                        delCustinsConnAddr.getUserVisible(),
                        delCustinsConnAddr.getTunnelId(),
                        delCustinsConnAddr.getVpcId(),
                        null);

        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(1);
        connAddrChangeLogs.add(delConnAddrChangeLog);
        return connAddrChangeLogs;
    }

    @Override
    public Integer createCustInstanceTask(String action, CustInstanceDO custins, DbsDO dbs,
                                          AccountsDO account,
                                          Map<String, Object> taskQueueParam,
                                          CustinsIpWhiteListDO custinsIpWhiteList,
                                          Integer operatorId, String osPassWord)
            throws RdsException {
        // create default ip white list group
        custinsIpWhiteList.setCustinsId(custins.getId());
        ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);

        // sync_mode  ha_priority_level  sync_binlog_mode 的默认值取自于集群属性，仅在实例资源申请完成后才能确定
        List<ClusterParamDO> clusterParams = clusterService.getClusterParams(
                custins.getDbType(),
                custins.getDbVersion(),
                custins.getClusterName(),
                ClusterParamSupport.getClusterParamNameList(custins.getDbType()));
        if (mysqlParamSupport.isMysqlXdbByCustins(custins)){
            for (ClusterParamDO clusterParamDO : clusterParams) {
                if (CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE
                                .equals(clusterParamDO.getName())) {
                    clusterParamDO
                            .setValue(CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_NO_REVERT_SEMI_SYNC);
                    break;
                }
            }
        }

        List<CustinsParamDO> custinsParams = CustinsParamSupport.initCustinsParamsFromClusterParams(
                custins.getDbType(), clusterParams, custins.getId());

        //对于已经设置的实例param, 集群的param不应覆盖
        List<CustinsParamDO> settledCustinsParams = custins.getCustinsParams();
        Set<String> settledCustinsSet = new HashSet<String>();
        for (CustinsParamDO custParam : settledCustinsParams) {
            settledCustinsSet.add(custParam.getName());
        }
        //生成要覆盖的
        ArrayList<CustinsParamDO> toCreateCuistinParams = new ArrayList<CustinsParamDO>();
        for (CustinsParamDO custParam : custinsParams) {
            if (!settledCustinsSet.contains(custParam.getName())) {
                toCreateCuistinParams.add(custParam);
            }
        }

        custinsParamService.createCustinsParams(toCreateCuistinParams);

        if (mysqlDBCustinsService.isMysqlEnterprise(custins)) {
            //mysql金融版创建时默认开启sql审计
            custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SQL_LOG, CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SQL_LOG_YES);
            custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_RECORD_SOURCE, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_RECORD_SOURCE_SLS);
        }

        //添加遗漏的sql审计代码
        if(custinsParamService.isNewSqlLogSwitch(custins)&&(custins.isMysql() || custins.isPolarDBAny())){
            custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION,
                CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
        }

        List<CustinsParamDO> characterCustinsParams;
        CustInstanceDO dbCustins = null;
        custinsIpWhiteList.setIpWhiteList("0.0.0.0/0");
        if (custins.isMysqlLogic()){

            //
            CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
            custInstanceQuery.setParentId(custins.getId());
            custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_PRIMARY);
            List<CustInstanceDO> characterCustinsList = custinsService.getCustIns(custInstanceQuery);
            for (CustInstanceDO characterCustins : characterCustinsList){
                custinsIpWhiteList.setCustinsId(characterCustins.getId());
                ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
                characterCustinsParams = new ArrayList<CustinsParamDO>();
                if (characterCustins.getCharacterType().equals(CustinsSupport.CHARACTER_TYPE_MYSQL_DB)){
                    for (CustinsParamDO custinsParam : custinsParams){
                        custinsParam.setCustinsId(characterCustins.getId());
                        characterCustinsParams.add(custinsParam);
                    }
                    custinsParamService.createCustinsParams(characterCustinsParams);
                    dbCustins = characterCustins;
                }
            }
        }

        // account mode undefined, get default value from cluster parameter.
        if (custins.getAccountMode() < 0) {
            Integer accountMode = CustinsSupport.CUSTINS_ACCOUNT_MODE_NOT_SUPPORT_UPGRADE;
            if (custins.isSupportSuperAccountMode()) {
                accountMode = CustinsSupport.CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE;
            }
            custins.setAccountMode(accountMode);
            // config dbCustins
            if (dbCustins != null){
                dbCustins.setAccountMode(accountMode);
            }

            if (custins.isSuperAccountMode()) {
                custins.setMaxDbs(0);
                custins.setMaxAccounts(1);
                if (dbCustins != null){
                    dbCustins.setMaxDbs(0);
                    dbCustins.setMaxAccounts(1);
                }
                if (account != null) {
                    account.setPriviledgeType(
                            AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue());
                }
            } else {
                if (account != null) {
                    account.setPriviledgeType(AccountPriviledgeType.PRIVILEDGE_NORMAL.getValue());
                }
            }
            custinsIDao.updateCustinsAccountMode(custins);
            if (dbCustins != null){
                custinsIDao.updateCustinsAccountMode(dbCustins);
            }
        }

        if (custins.isTop()) {
            //创建同步DB及账号
            this.createSyncDbAndAccount(custins, dbs, account);
        } else {
            this.createDbAndAccount(custins, dbs, account);
            if (dbCustins != null){
                dbs.setCustinsId(dbCustins.getId());
                account.setCustinsId(dbCustins.getId());
                this.createDbAndAccount(dbCustins, dbs, account);
            }
        }

        /*
         * create system account for custins
         */

        dbsService.createAuroraAccount(custins, getRandomPasswdForEnhance(15));

        if (dbCustins != null){
            dbsService.createAuroraAccount(dbCustins, getRandomPasswdForEnhance(15));
        }

        if (custins.isMysql56()||custins.isMysql57Physical()){
            // 5.6 , 5.7 物理机双节点
            dbsService.createAuroraProxyAccount(custins, getRandomPasswdForEnhance(15));
        }

        TaskQueueDO taskQueue = null;
        taskQueue = new TaskQueueDO(action, operatorId, custins.getId(),
                TASK_TYPE_CUSTINS,
                TaskSupport.getTaskInstallInsKey(custins.getDbType()));

        taskQueue.setParameter(JSONObject.toJSONString(taskQueueParam));
        taskService.createTaskQueue(taskQueue);
        return taskQueue.getId();
    }



    private void createDbAndAccount(CustInstanceDO custins, DbsDO dbs, AccountsDO account) {
        if (account != null) {
            String password = account.getPassword();
            account.setCustinsId(custins.getId());
            account.setPassword(dbsService.getEncryptPassword(custins.getDbType(), password, custins.getKindCode()));
            account.setBackwardAccount(null);
            account.setBackwardPasswd(null);
            if ((custins.isProxy() && !account.isSuperPrivilege()) || custins.isKepler()) {
                account.setBackwardAccount(account.getAccount());
                account.setBackwardPasswd(AesCfb.encrypt(this.getRandomPasswdForEnhance(15)));
            }
            dbsIDao.createAccounts(account);
        }
        if (dbs != null) {
            dbs.setCustinsId(custins.getId());
            dbsIDao.createDbs(dbs);
            if (account != null) {
                dbsIDao.createAccountDbRel(
                        new AccountDbRelDO(custins.getId(), account.getId(), dbs.getId(),
                                ACCOUNT_TYPE_READWRITE));
            }
        }

    }

    private void createSyncDbAndAccount(CustInstanceDO custins, DbsDO dbs, AccountsDO account)
            throws RdsException {
        // Top实例account不为null
        account.setCustinsId(custins.getId());
        String password = account.getPassword();
        account.setPassword(accountIDao.getEncryptPassword(password));
        account.setBackwardAccount(null);
        account.setBackwardPasswd(null);
        if (custins.isProxy() && !account.isSuperPrivilege()) {
            account.setBackwardAccount(account.getAccount());
            account.setBackwardPasswd(AesCfb.encrypt(this.getRandomPasswdForEnhance(15)));
        }

        // 创建聚石塔同步账号
        String syncPassword = null;
        boolean isGeneratePasswordByRandom = resourceSupport.getBooleanRealValue(
                ResourceKey.RESOURCE_SYNC_PASSWORD_GENERATE_RULE);
        // 判断是否随机产生密码
        if (isGeneratePasswordByRandom) {
            syncPassword = this.getRandomPasswdForEnhance(15);
        } else {
            try {
                syncPassword = AesCfb.decrypt(resourceSupport.getStringRealValue(
                        ResourceKey.RESOURCE_TOP_SYNC_PSWD));
            } catch (Exception e) {
                String customizedErrorDesc = "fail to decrypt password";
                throw new RdsException(ErrorCode.INTERNAL_FAILURE, customizedErrorDesc);
            }
        }
        AccountsDO syncAccount = new AccountsDO(custins,
                "s_" + account.getAccount(),
                accountIDao.getEncryptPassword(syncPassword));
        syncAccount.setBizType(DbsSupport.BIZ_TYPE_SYNC);
        // 对于同步账户，后端账户密码必须设置，且密码与前端密码一致
        syncAccount.setBackwardAccount(syncAccount.getAccount());
        syncAccount.setBackwardPasswd(AesCfb.encrypt(syncPassword));

        // 创建同步DB
        String syncDbName = resourceSupport.getStringRealValue(
                ResourceKey.RESOURCE_TOP_SYNC_DB);
        DbsDO syncDbs = new DbsDO(custins, syncDbName, "utf8");
        syncDbs.setBizType(DbsSupport.BIZ_TYPE_SYNC);

        dbsIDao.createDbs(syncDbs);// 创建同步DB
        dbsIDao.createAccounts(syncAccount);// 创建同步账户
        // 绑定同步DB，权限R&W
        dbsIDao.createAccountDbRel(
                new AccountDbRelDO(custins.getId(), syncAccount.getId(), syncDbs.getId(),
                        ACCOUNT_TYPE_READWRITE));
        dbsIDao.createAccounts(account);// 创建用户账户
        // 绑定同步DB，权限R_ONLY
        dbsIDao.createAccountDbRel(
                new AccountDbRelDO(custins.getId(), account.getId(), syncDbs.getId(),
                        ACCOUNT_TYPE_READONLY));
        if (dbs != null) {
            dbs.setCustinsId(custins.getId());
            dbsIDao.createDbs(dbs);
            dbsIDao.createAccountDbRel(
                    new AccountDbRelDO(custins.getId(), account.getId(), dbs.getId(),
                            ACCOUNT_TYPE_READWRITE));
        }
    }

    private String getRandomPasswdForEnhance(int length) {
        String letter = "ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghjklmnopqrstuvwxyz";
        String num = "**********";
        String other = "()~!@$%^&*-+=|{}[]:;<>,.?/";
        String all = letter+num+other;
        StringBuffer sb = new StringBuffer(length);
        Random random = new Random();
        sb.append(letter.charAt(random.nextInt(letter.length())));
        sb.append(num.charAt(random.nextInt(num.length())));
        sb.append(other.charAt(random.nextInt(other.length())));
        for (int i = 0; i < (length - 3); i++) {
            sb.append(all.charAt(random.nextInt(all.length())));
        }
        return sb.toString();
    }

    public Replica getReplicaByRole(String requestId, String replicaSetName, Replica.RoleEnum role) throws ApiException {
        ReplicaListResult replicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSetName, null, null, null, null);

        List<Replica> replicas =
                replicaList.getItems().stream().filter(x -> x.getRole() == role).collect(Collectors.toList());
        return replicas.size() > 0 ? replicas.get(0) : null;
    }

    /**
     * 比较磁盘大小和备份集大小
     * @param restoreBackupResponse
     * @param diskSize
     * @throws RdsException
     */
    @Override
    public void compareBakSizeAndDiskSize(DescribeRestoreBackupSetResponse restoreBackupResponse, Integer diskSize) throws RdsException{
        Long bakSize = restoreBackupResponse.getBackupSetInfo().getBackupSize();
        if (bakSize == null || (bakSize/1024/1024/1024) > diskSize) {
            throw new RdsException(ErrorCode.INVALID_STORAGE);
        }
    }

    /**
     * 检查实例和用户的所属关系
     * @param custinid
     * @param bid
     * @param uid
     * @throws RdsException
     */
    public void checkCustinsAndUser(Integer custinid, String bid, String uid) throws RdsException{
        Integer user_id = userService.getUserId(bid,uid,null);
        if (user_id == null) {
            throw new RdsException(ErrorCode.USER_NOT_FOUND);
        }
        CustInstanceDO custInstanceDO = custinsIDao.getCustInstanceByCustinsIdIgnoreDelete(user_id, custinid,0);
        if (custInstanceDO == null) {
            throw new RdsException(ErrorCode.INVALID_INSTANCE);
        }
    }

    /**
     * 检查是否是已删除实例的备份集
     * @param backupSetId
     * @return
     * @throws RdsException
     */
    public boolean isRebuildBackupSet(String backupSetId){
        if (StringUtils.isNotBlank(backupSetId) && backupSetId.startsWith(MySQLParamConstants.RBH_PREFIX)) {
            return true;
        }
        return false;
    }

    @Override
    public Integer createExchangeReadInsToPrimaryTask(String action, CustInstanceDO custins,
                                              CustInstanceDO peerins, Integer operatorId,
                                              TransListDO translist, String taskKey)
            throws RdsException {
        Map<String, Object> taskparam = new HashMap<>();
        return createExchangeReadInsToPrimaryTaskWithParam(action, custins, operatorId, translist, taskKey, taskparam);
    }

    @Override
    public Integer createExchangeReadInsToPrimaryTaskWithParam(String action, CustInstanceDO custins, Integer operatorId,
                                                  TransListDO translist, String taskKey, Map<String, Object> taskParam) {
        instanceIDao.createTransList(translist);
        taskParam.put(CustinsSupport.TRANS_ID, translist.getId());

        TaskQueueDO taskQueue = null;
        taskQueue = new TaskQueueDO(action, operatorId, custins.getId(), TASK_TYPE_CUSTINS, taskKey);

        taskQueue.setParameter(JSONObject.toJSONString(taskParam));
        taskService.createTaskQueue(taskQueue);
        instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
        return taskQueue.getId();
    }
}
