package com.aliyun.dba.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.model.IPWhiteList;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.core.utils.IOUtils;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.common.utils.QuicklzParser;
import com.aliyun.dba.base.lib.*;
import com.aliyun.dba.base.parameter.backup.CreateOssBakRestoreParam;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.parameter.backup.OssBakRestoreStatusParam;
import com.aliyun.dba.base.parameter.backup.OssBakRestoreTaskParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.response.backup.OssBakRestoreResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.SlrCheckService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import com.google.common.primitives.Bytes;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutionException;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.base.support.MySQLParamConstants.DEFAULT_DB_TYPE;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.poddefault.action.RebuildExternalReplicationCustinsImpl.AliyunServiceRoleForRdsImport;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.SCHEDULER_CONFIG_ARCH_KEY;

/**
 * mybase cluster backup oss service
 */
@Service
public class ClusterBackUpService {

    private static final LogAgent LOG = LogFactory.getLogAgent(ClusterBackUpService.class);

    @Resource
    MySQLAvzService mySQLAvzService;

    @Resource
    PodParameterHelper podParameterHelper;

    @Resource
    MinorVersionServiceHelper minorVersionServiceHelper;

    @Resource
    MysqlParamSupport mysqlParamSupport;

    @Resource
    BackupService backupService;

    @Resource
    DBaasMetaService dBaasMetaService;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    CustinsParamService custinsParamService;

    @Resource
    CommonProviderService commonProviderService;

    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private DbsGateWayService dbsGateWayService;

    @Autowired
    protected UserService userService;

    @Autowired
    private CustinsIDao custinsIDao;

    @Resource
    private SlrCheckService slrCheckService;

    @Resource
    private PodTemplateHelper podTemplateHelper;

    private static final String FILEPATH_XTRABACKUP_INFO = "\"filepath\":\t\"xtrabackup_info\",";
    private static final String XTRABACKUP_INFO = "xtrabackup_info";
    private static final String XTRABACKUP_HEADER = "XBSTCK01";
    private static final long KB = 1024 * 8;
    private static final long MB = 1024 * 1024 * 8;
    private static final byte[] ZSTD_MAGIC_BYTES = new byte[] {
            (byte) 0x28, (byte) 0xB5, (byte) 0x2F, (byte) 0xFD
    };

    public enum CompressType {
        ZSTD,
        NONE
    }

    public enum BackupType {
        XTRABACKUP,
        NONE
    }

    /**
     * get backup cluster and zone from dukang->system->resource manager
     */
    public Map<String, String> getBackClusterConfig(String regionID, ResourceService resourceService) {
        List<Map<String, String>> availableZoneList = resourceService.getResourceMapList("BackupRecoverAvailableZone");
        String zoneId = null;
        String clusterName = null;
        String subDomain = null;
        if (availableZoneList != null && availableZoneList.size() > 0) {
            Map<String, String> availableZone = availableZoneList.get(0);
            String zoneMapper = availableZone.get("RealValue");
            if (StringUtils.isNotEmpty(zoneMapper)) {
                JSONObject mapper = (JSONObject) JSON.parse(zoneMapper);
                if (mapper != null) {
                    zoneId = mapper.getString("Zone-" + regionID);
                    subDomain = mapper.getString("SubDomain-" + regionID);
                    String rClusterName = mapper.getString("Cluster-" + regionID);
                    if (StringUtils.isNotEmpty(rClusterName)) {
                        clusterName = rClusterName;
                    }
                }

            }
        }
        Map<String, String> result = new HashMap<String, String>();
        if (zoneId != null) {
            result.put("zoneId", zoneId);
        }
        if (clusterName != null) {
            result.put("clusterName", clusterName);
        }
        if (subDomain != null) {
            result.put("subDomain", subDomain);
        }
        return result;
    }

    /**
     * 公式，Math.ceil(OSS_FILE_SIZE * 5）， 10G为单位，上取整下，这里需要对齐下
     * ossFileSize 实际大小
     */
    public Integer getDiskSize(Map<String, String> params) throws RdsException {
        double ossFileSize = Float.parseFloat(mysqlParamSupport.getParameterValue(params, "OssFileSize")) / 1024 / 1024 / 1024;
        Integer diskSize = BigDecimal.valueOf((Math.ceil((ossFileSize * 5) / 10)) * 10).intValue();
        if (diskSize <= 20) {
            diskSize = 20; //cloud_ssd：20~32768, cloud_essd：20~32768
        }
        int maxCloudDiskSize = 16000; // 云盘最大16T
        if (diskSize > maxCloudDiskSize) {
            throw new RdsException(ErrorCode.INVALID_STORAGE);
        }
        return diskSize;
    }

    public ReplicaSetResourceRequest allocateClusterResource(String bid, String uid, String dbInstanceName, String zoneId, String clusterName,
                                                             String dbType, String dbVersion, String portStr, String insTypeDesc, InstanceLevel instanceLevel,
                                                             ReplicaSet.BizTypeEnum bizType, String classCode, String diskType, Integer diskSize, AVZInfo avzInfo,
                                                             Replica.RoleEnum[] nodeRoles, String dbEngine) throws RdsException {
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
        replicaSetResourceRequest.setUserId(bid);
        replicaSetResourceRequest.setUid(uid);
        replicaSetResourceRequest.setPort(portStr);
        replicaSetResourceRequest.setInsType(insTypeDesc);
        replicaSetResourceRequest.setReplicaSetName(dbInstanceName);
        replicaSetResourceRequest.setDbType(dbType);
        replicaSetResourceRequest.setDbVersion(dbVersion);
        replicaSetResourceRequest.setConnType(CONN_TYPE_PHYSICAL);
        replicaSetResourceRequest.setBizType(bizType.toString());
        replicaSetResourceRequest.setClassCode(classCode);
        replicaSetResourceRequest.setStorageType(diskType);
        replicaSetResourceRequest.setDiskSize(diskSize);
        replicaSetResourceRequest.setSubDomain(avzInfo.getRegion());
        replicaSetResourceRequest.setRegionId(avzInfo.getRegionId());

        try {
            // 构造资源调度策略
            Pair<String, ScheduleTemplate> scheduleTemplatePair = podTemplateHelper
                    .getBizSysScheduleTemplate(
                            PodType.POD_RUNC,
                            bizType,
                            dbEngine,
                            instanceLevel,
                            false,
                            ReplicaSet.InsTypeEnum.MAIN.getValue(),
                            dbInstanceName,
                            null,
                            uid
                    );
            replicaSetResourceRequest.setScheduleTemplate(scheduleTemplatePair.getValue());
        } catch (Exception e) {
            LOG.error("getBizSysScheduleTemplate failed: {}", e.getMessage());
            throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
        }

        // 备份集上云基础版的时候指定x86调度
        if (Objects.equals(instanceLevel.getCategory(), InstanceLevel.CategoryEnum.BASIC)) {
            replicaSetResourceRequest.addSelectorsItem(podCommonSupport.genResourceSelector(SCHEDULER_CONFIG_ARCH_KEY, "in", Collections.singletonList("amd64")));
        }

        Boolean isDhg = mysqlParamSupport.isDHGCluster(clusterName);
        if (StringUtils.isNotBlank(clusterName) && isDhg) {
            replicaSetResourceRequest.setDedicatedHostGroupId(clusterName);
        }

        replicaSetResourceRequest.setComposeTag(
                minorVersionServiceHelper.getServiceSpecTag(
                        null,
                        bizType,
                        dbType,
                        dbVersion,
                        dbEngine,
                        KindCodeParser.KIND_CODE_NEW_ARCH,
                        instanceLevel,
                        diskType,
                        isDhg,
                        false,
                        null)   //备份上云的实例使用大客户的内核镜像
        );

        List<ReplicaResourceRequest> replicas = new ArrayList<>();
        Map<Replica.RoleEnum, String> roleHostNameMapping = podParameterHelper.getRoleHostNameMapping();


        for (int i = 0; i < nodeRoles.length; i++) {
            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(diskType);
            replicaResourceRequest.setClassCode(classCode);
            replicaResourceRequest.setDiskSize(diskSize);

            replicaResourceRequest.setRole(nodeRoles[i].toString());
            replicaResourceRequest.setHostName(roleHostNameMapping.get(nodeRoles[i])); // WARN: 依赖数字role，为外层定义mapping，不要乱修改
            //                replicaResourceRequest.setStorageType(diskType);  // 这里一定要注释
            replicaResourceRequest.setSingleTenant(false); // 多租户
            //replicaResourceRequest.setSingleTenant(true);//测试用单租户

            if (StringUtils.isNotEmpty(zoneId)) {
                replicaResourceRequest.setZoneId(zoneId);
            } else {
                AvailableZoneInfoDO availableZoneInfoDO = mySQLAvzService.getRoleZoneId(avzInfo, nodeRoles[i].toString());
                replicaResourceRequest.setZoneId(availableZoneInfoDO.getZoneID());
            }
            replicas.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
        replicaSetResourceRequest.ignoreCreateVpcMapping(true); //反向VPC的资源申请下沉到任务流
        return replicaSetResourceRequest;
    }

    /**
     * 备份集校验
     *
     * @category createRestoreTaskWithOss
     */
    public Map<String, Object> createRestoreTaskWithOss(
            String requestId, String dbType, String dbVersion, String dbInstanceName,
            Map<String, String> params) throws BaseServiceException, RdsException, com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        String zoneId = mysqlParamSupport.getParameterValue(params, "ZoneId");

        String comment = mysqlParamSupport.getParameterValue(params, "Comment");
        if (!StringUtils.isEmpty(comment) && comment.length() > 128) {
            return ResponseSupport.createErrorResponse(new Object[]{ResultCode.CODE_UNSUPPORTED, "CommentStringLengthExceeded", "Comment String is too long."});
        }

        String bid = mysqlParamSupport.getAndCheckBID(params);
        String uid = mysqlParamSupport.getUID(params);
        String restoreSize = mysqlParamSupport.getParameterValue(params, "RestoreSize");
        String stsToken = mysqlParamSupport.getParameterValue(params, "StsToken", "");
        String ossUrl = mysqlParamSupport.getParameterValue(params, "OssUrl");
        String ossBucket = mysqlParamSupport.getParameterValue(params, "OssBucket");
        String ossFilePath = mysqlParamSupport.getParameterValue(params, "OssFilePath");
        String ossFileName = mysqlParamSupport.getParameterValue(params, "OssFileName");
        String ossFileMetaData = mysqlParamSupport.getParameterValue(params, "OssFileMetaData");
        double ossFileSize = Float.parseFloat(mysqlParamSupport.getParameterValue(params, "OssFileSize")) / 1024;
        Integer ossFileSizeInKb = BigDecimal.valueOf(ossFileSize).intValue();
        if (ossFileSizeInKb <= 1) {
            ossFileSizeInKb = 1;
        }

        String ossInstanceName = null;
        boolean isUpdateOK = false;
        Long taskId;

        try {
            // 备份相关
            // 备份集默认保存3天
            Integer bakRetention = Integer.valueOf(mysqlParamSupport.getParameterValue(params, "Retention",3));
            String preferredBackupTime = getParameterValue(params, "PreferredBackupTime");
            if (!DateSupport.isminuteonly_utc(preferredBackupTime)) {
                throw new RdsException(ErrorCode.INVALID_PREFERREDBACKUPTIME);
            }
            String preferredBackupPeriod = CheckUtils.checkValidForBackupPeriod(getParameterValue(params, "preferredbackupperiod"));
            /** persistent backup oss info */
            CreateOssBakRestoreParam createOssBakRestoreParam = CreateOssBakRestoreParam.builder()
                    .user_id(bid)
                    .uid(uid)
                    .requestId(requestId)
                    .ossUrl(ossUrl)
                    .ossFileSize(ossFileSizeInKb.toString())
                    .ossBucket(ossBucket)
                    .ossFilePath(ossFilePath)
                    .ossFileName(ossFileName)
                    .ossFileMetaData(truncateOssFileMetaData(ossFileMetaData, requestId))
                    .engine(dbType)
                    .engineVersion(dbVersion)
                    .restoreSize(restoreSize)
                    .comment(comment)
                    .zoneId(zoneId)
                    .build();

            LOG.info("requestId={} createOssBakRestore begin {}", requestId, createOssBakRestoreParam.toString());
            OssBakRestoreResponse ossBakRestoreResponse = backupService.createOssBakRestore(createOssBakRestoreParam);
            ossInstanceName = ossBakRestoreResponse.getInstanceName();

            /** do TASK_CHECK_USER_BAKFILE task */
            LOG.info("requestId={} createOssBakRestore end {}", requestId, ossBakRestoreResponse.toString());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);
            // 备份信息
            jsonObject.put("bakRetention", bakRetention); // 备份保留时间, 比如 7 天
            jsonObject.put("preferredBackupPeriod", preferredBackupPeriod); // 每周哪几天备份, 比如 0101010
            jsonObject.put("preferredBackupTime", preferredBackupTime); // 备份时间, UTC, 比如 06:35Z
            jsonObject.put("baksetName", ossFileName);
            jsonObject.put("downloadUrl", ossUrl);
            jsonObject.put("stsToken", stsToken);
            jsonObject.put("checksum", "");
            jsonObject.put("checkType", "");
            jsonObject.put("ossInstanceName", ossInstanceName);
            String parameter = jsonObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = PodDefaultConstants.TASK_CHECK_USER_BAKFILE;
            Object taskIdObj = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);
            taskId = new BigDecimal(taskIdObj.toString()).longValue();
            LOG.info("requestId={} dispatchTask done taskId={}", requestId, taskId);
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, null);
            Long id = replicaSet.getId();

            /** persistent task info to backup meta */
            OssBakRestoreTaskParam ossBakRestoreTaskParam = OssBakRestoreTaskParam.builder()
                    .user_id(bid)
                    .uid(uid)
                    .requestId(requestId)
                    .instanceName(ossInstanceName)
                    .taskId(taskId)
                    .dbInstanceName(dbInstanceName)
                    .custinsId(id)
                    .build();
            ossBakRestoreResponse = backupService.updateOssBakRestoreTask(ossBakRestoreTaskParam);
            isUpdateOK = "Ok".equals(ossBakRestoreResponse.getSuccess());
        } catch (Exception ex) {
            LOG.error("createRestoreTaskWithOss failed, " + ex.getMessage());
            final Integer failedStatus = 2;
            OssBakRestoreStatusParam ossBakRestoreStatusParam = OssBakRestoreStatusParam.builder()
                    .user_id(bid)
                    .uid(uid)
                    .requestId(requestId)
                    .instanceName(ossInstanceName)
                    .status(failedStatus)
                    .reason("create task failed failed, " + ex.getMessage())
                    .build();
            backupService.updateOssBakRestoreStatus(ossBakRestoreStatusParam);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
        if (!isUpdateOK) {
            final Integer failedStatus = 2;
            OssBakRestoreStatusParam ossBakRestoreStatusParam = OssBakRestoreStatusParam.builder()
                    .user_id(bid)
                    .uid(uid)
                    .requestId(requestId)
                    .instanceName(ossInstanceName)
                    .status(failedStatus)
                    .reason("updateOssBakRestoreTask failed")
                    .build();
            backupService.updateOssBakRestoreStatus(ossBakRestoreStatusParam);
        }
        Map<String, Object> data = new HashMap<>();

        data.put("TaskId", taskId);
        data.put("DBInstanceName", dbInstanceName);
        data.put("InstanceName", ossInstanceName);
        data.put("Status", isUpdateOK);

        return data;
    }

    public boolean allocateDBInstanceResource(String requestId, String dbInstanceName, IPWhiteList ipWhiteList,
                                              AVZInfo avzInfo,
                                              ReplicaSetResourceRequest replicaSetResourceRequest) throws RdsException {

        boolean isAllocate = false;
        try {
            isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, dbInstanceName, replicaSetResourceRequest);
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, null);

            //写入白名单数据
            dBaasMetaService.getDefaultClient().createReplicaSetWhiteIps(requestId, dbInstanceName, ipWhiteList);
            // 更新主可用区参数
            custinsParamService.updateAVZInfo(replicaSet.getId().intValue(), avzInfo);
        } catch (Exception e) {
            isAllocate = false;
            if (e instanceof ApiException) {
                LOG.error(requestId + " Exception: {}", ((ApiException) e).getResponseBody());
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
            }
            LOG.error(requestId + " Exception: {}", e.getMessage());
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isAllocate) {
                //分配失败或者其它异常的情况下，要调用释放资源接口
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, dbInstanceName);
                } catch (ApiException e) {
                    LOG.error(requestId + " release ReplicaSet Exception: {}", e.getResponseBody());
                }
            }
        }
        return isAllocate;
    }
    /**
     * 查询已删除实例备份集的实例名和kindcode
     */
    public Map<String, Object> getDetachedInsBackupInfo(Map<String, String> params) throws BaseServiceException,RdsException{
        String uid = mysqlParamSupport.getUID(params);
        String bid = mysqlParamSupport.getBID(params);
        DescribeRestoreBackupSetParam caller = dbsGateWayService.describeRestoreBackupSetBuilder(params,bid);
        DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(caller);
        Map<String, Object> data = new HashMap<>();
        Integer srcCustinid = restoreBackupResponse.getBackupSetInfo().getCustinsId();
        data.put("dbInstanceId",srcCustinid);
        Integer user_id = userService.getUserId(bid,uid,null);
        if (user_id == null) {
            throw new RdsException(ErrorCode.USER_NOT_FOUND);
        }
        CustInstanceDO srcCustInstanceDO = custinsIDao.getCustInstanceByCustinsIdIgnoreDelete(user_id, srcCustinid,0);
        if (srcCustInstanceDO == null) {
            throw new RdsException(ErrorCode.INVALID_INSTANCE);
        }
        String srcDBInstanceName = srcCustInstanceDO.getInsName();
        data.put("srcDBInstanceName",srcDBInstanceName);
        String kindcode = restoreBackupResponse.getBackupSetInfo().getInstanceKindCode();
        data.put("kindcode",kindcode);
        return data;
    }

    /**
     * 申请stsToken并在params补全oss相关参数
     * @param params actionParameters
     */
    public void reloadOssParams(Map<String, String> params) throws Exception {
        String stsToken = mysqlParamSupport.getParameterValue(params, "StsToken");
        if (isStsTokenValid(stsToken)) {
            // no need to reload params
            return;
        }

        String uid = mysqlParamSupport.getUID(params);
        String bucketName = mysqlParamSupport.getParameterValue(params, "OssBucket");
        String object = mysqlParamSupport.getParameterValue(params, "OssFilePath");
        String location = mysqlParamSupport.getParameterValue(params, "Location");
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String dbType = mysqlParamSupport.getAndCheckDBType(params, DEFAULT_DB_TYPE);
        String engineVersion = mysqlParamSupport.getAndCheckDBVersion(params, dbType, true);

        AssumeRoleWithServiceIdentityResponse.Credentials stsCredentials;
        Map<String, Object> backupMeta;

        // 1. 获取stsToken
        try {
            stsCredentials = getStsCredentials(uid);
            LOG.info("credentials:{}", JSON.toJSONString(stsCredentials));
        } catch (Exception e) {
            LOG.error("getStsCredentials failed", e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }

        // 2. 读取备份文件元信息
        try {
            backupMeta = checkBackupFile(stsCredentials, bucketName, object, location, requestId);
            LOG.info("backupMeta:{}", JSON.toJSONString(backupMeta));
        } catch (Exception e) {
            LOG.error("checkBackupFile failed", e);
            throw new RdsException(ErrorCode.INVALID_USER_BAKSET);
        }

        // 3. 处理版本
        try {
            String dbVersion = backupMeta.get("server_version").toString().substring(0, 3);
            if (!dbVersion.equals(engineVersion)) {
                LOG.warn("dbversion parsed from meta data different from user input, override it, new version: {}", dbVersion);
                mysqlParamSupport.setParameter(params, ParamConstants.ENGINE_VERSION, dbVersion);
            }
        } catch (Exception e) {
            LOG.warn("parse version failed, continue with input version");
        }

        // 4. 补全参数
        String ossEndpoint = "oss-" + location + "-internal.aliyuncs.com";
        String ossUrl = String.format(
                "oss://%s:%s@%s/%s/%s", stsCredentials.getAccessKeyId(),
                stsCredentials.getAccessKeySecret(), ossEndpoint,
                bucketName, object);
        mysqlParamSupport.setParameter(params, "OssUrl", ossUrl);
        mysqlParamSupport.setParameter(params, "OssFileSize", String.valueOf(backupMeta.getOrDefault("fileSize", "")));
        mysqlParamSupport.setParameter(params, "OssFileMetaData", JSON.toJSONString(backupMeta));
        mysqlParamSupport.setParameter(params, "StsToken", stsCredentials.getSecurityToken());
        LOG.info("updated params: {}", JSON.toJSONString(params));
    }

    /**
     * 获取SLR stsToken，过期时间1.5天（最大值）
     */
    private AssumeRoleWithServiceIdentityResponse.Credentials getStsCredentials(String uid) throws ClientException, ExecutionException {
        return slrCheckService.getCredentials(uid, AliyunServiceRoleForRdsImport, 129600L);
    }

    /**
     * 获取OSS备份文件元信息
     */
    private Map<String, Object> checkBackupFile(AssumeRoleWithServiceIdentityResponse.Credentials stsCredential, String bucketName, String object, String location, String requestId) throws Exception {
        // 1. 初始化ossClient
        Map<String, Object> backfileMeta = new HashMap<>();
        String endpoint = "oss-" + location + "-internal.aliyuncs.com";
        OSSClient ossClient = getOSSClient(endpoint, stsCredential);

        // 2. 检查object是否存在
        if (!ossClient.doesObjectExist(bucketName, object)) {
            LOG.error("RequestId: {}, Message: Object {} does not exist in bucket {}", requestId, object, bucketName);
            throw new RdsException(ErrorCode.INVALID_USER_BAKSET);
        }

        // 3. 获取文件大小
        long objectSize = ossClient.getSimplifiedObjectMeta(bucketName, object).getSize();
        backfileMeta.put("fileSize", String.valueOf(objectSize));

        // 4. 处理压缩类型特殊逻辑
        CompressType compressType = getAndCheckCompressType(ossClient, bucketName, object, requestId);

        // 5. 根据header检查备份类型
        BackupType backupType = getAndCheckBackupType(ossClient, bucketName, object, requestId);

        // 6. 对于xtrabackup备份，寻找并检查xtrabackup_info元信息
        if (CompressType.NONE.equals(compressType) && BackupType.XTRABACKUP.equals(backupType)) {
            findAndCheckXtrabackupInfo(ossClient, bucketName, object, objectSize, backfileMeta, requestId);
        }

        return backfileMeta;
    }

    private OSSClient getOSSClient(String endpoint, AssumeRoleWithServiceIdentityResponse.Credentials stsCredential) {
        return new OSSClient(endpoint, stsCredential.getAccessKeyId(), stsCredential.getAccessKeySecret(), stsCredential.getSecurityToken());
    }

    private int readInt(byte[] bytes, int pos) {
        return (bytes[pos] & 0xff) |
                (bytes[pos + 1] & 0xff) << 8 |
                (bytes[pos + 2] & 0xff) << 16 |
                (bytes[pos + 3] & 0xff) << 24;
    }

    private long readLong(byte[] bytes, int pos) {
        return (bytes[pos] & 0xff) |
                (bytes[pos + 1] & 0xff) << 8 |
                (bytes[pos + 2] & 0xff) << 16 |
                ((long) bytes[pos + 3] & 0xff) << 24 |
                ((long) bytes[pos + 4] & 0xff) << 32 |
                ((long) bytes[pos + 5] & 0xff) << 40 |
                ((long) bytes[pos + 6] & 0xff) << 48 |
                ((long) bytes[pos + 7] & 0xff) << 56;
    }

    public byte[] decompressQPress(byte[] dataBytes) {
        String contentString = new String(dataBytes, StandardCharsets.US_ASCII);
        int start = contentString.indexOf("NEWBNEWB") + 8 + 12;
        int end = contentString.indexOf("ENDSENDS");
        return QuicklzParser.decompress(Arrays.copyOfRange(dataBytes, start, end)).getKey();
    }

    private Boolean isStsTokenValid(String stsToken) {
        return StringUtils.isNotBlank(stsToken);
    }

    /**
     * 从 rds_table_xxx_info.json 中获取 begin/end 位置以读取xtrabackup元数据
     */
    public static Pair<Long, Long> getXtrabackupInfoRange(byte[] contentWithXtrabackupInfo) {
        // 1. 从信息中读取 filePath: xtrabackup_info 字段的位置
        int i = Bytes.indexOf(contentWithXtrabackupInfo, FILEPATH_XTRABACKUP_INFO.getBytes());
        int beginIndex = i + FILEPATH_XTRABACKUP_INFO.length();

        // 2. 向后读取最多2000位的数据，查找 begin，end 字符，记录xtrabackup元数据位置
        String contentToScan = new String(Arrays.copyOfRange(contentWithXtrabackupInfo, beginIndex, Integer.max(beginIndex + 2000, contentWithXtrabackupInfo.length - 1)), StandardCharsets.US_ASCII);
        String beginKeyWord = "\"begin\":\t";
        int begin = contentToScan.indexOf(beginKeyWord) ;
        int end = contentToScan.indexOf('}', begin);

        // 3. 构造json并读取xtrabackup元数据起始、终止位置
        String fileInfoJsonString = "{" + contentToScan.substring(begin, end) + "}";
        JSONObject fileInfo = JSON.parseObject(fileInfoJsonString);
        return new ImmutablePair<>(fileInfo.getLong("begin"), fileInfo.getLong("end"));
    }


    /**
     * 寻找xtrabackup_info并填充元信息
     */
    private void findAndCheckXtrabackupInfo(OSSClient ossClient, String bucketName, String object, Long objectMetaSize, Map<String, Object> backfileMeta, String requestId) throws Exception {
        // 1. 尝试从最后1KB查找xtrabackup_info
        long start = Long.max( objectMetaSize - KB - 1, 0);
        long end = objectMetaSize - 1;
        byte[] objectMeta = getObjectMetaFromOss(ossClient, bucketName, object, start, end);
        int xtrabackupInfoPos = Bytes.indexOf(objectMeta, XTRABACKUP_INFO.getBytes(StandardCharsets.US_ASCII)) - 4 - 1;
        LOG.info("RequestId: {}, Message: xtrabackupInfoPos parsed from last 1KB: {}", requestId, xtrabackupInfoPos);

        // 2. 最后1KB校验失败，尝试拉取最后2MB
        if (!isXtrabackupInfoValid(objectMeta, xtrabackupInfoPos, backfileMeta, requestId)) {
            start = Long.max(objectMetaSize - 2 * MB - 1, 0);
            objectMeta = getObjectMetaFromOss(ossClient, bucketName, object, start, end);
            xtrabackupInfoPos = Bytes.indexOf(objectMeta, XTRABACKUP_INFO.getBytes(StandardCharsets.US_ASCII)) - 4 - 1;
            LOG.info("RequestId: {}, Message: xtrabackupInfoPos parsed from last 2MB: {}", requestId, xtrabackupInfoPos);
        }

        // 3. 最后2MB仍校验失败，尝试直接读取rds_table_xtrabackup_info.json中的指针
        if (!isXtrabackupInfoValid(objectMeta, xtrabackupInfoPos, backfileMeta, requestId)) {
            Pair<Long, Long> range = getXtrabackupInfoRange(objectMeta);
            objectMeta = getObjectMetaFromOss(ossClient, bucketName, object, range.getLeft(), range.getRight());
            xtrabackupInfoPos = Bytes.indexOf(objectMeta, XTRABACKUP_INFO.getBytes(StandardCharsets.US_ASCII)) - 4 - 1;
            LOG.info("RequestId: {}, Message: xtrabackupInfoPos parsed from rds_table_xtrabackup_info.json: {}", requestId, xtrabackupInfoPos);
        }

        // 4. 仍校验失败，抛出异常
        if (!isXtrabackupInfoValid(objectMeta, xtrabackupInfoPos, backfileMeta, requestId)) {
            LOG.error("RequestId: {}, Message: Cannot find xtrabckup_info from backup meta", requestId);
            throw new RdsException(ErrorCode.INVALID_USER_BAKSET);
        }
    }

    /**
     * 通过起始、终止位置从oss获取数据
     */
    private byte[] getObjectMetaFromOss(OSSClient ossClient, String bucketName, String objectName, Long start, Long end) throws IOException {
        GetObjectRequest request = new GetObjectRequest(bucketName, objectName);
        request.setRange(start, end);
        InputStream objectMetaRaw = ossClient.getObject(request).getObjectContent();
        return IOUtils.toByteArray(objectMetaRaw);
    }

    /**
     * 读取并填充备份元信息
     */
    private void parseAndFillBackFileMeta(Map<String, Object> backFileMeta, byte[] objectMeta, int xtrabackupInfoPos, String requestId) throws Exception {
        xtrabackupInfoPos += 1;
        int pathLength = readInt(objectMeta, xtrabackupInfoPos);
        xtrabackupInfoPos += 4;
        String fileName = new String(Arrays.copyOfRange(objectMeta, xtrabackupInfoPos, xtrabackupInfoPos + pathLength));
        xtrabackupInfoPos += pathLength;
        long dataLength = readLong(objectMeta, xtrabackupInfoPos);
        xtrabackupInfoPos += 8 + 12;
        if (dataLength <= 0) {
            LOG.error("RequestId: {}, Message: check object meta failed, dataLength: {}", requestId, dataLength);
            throw new RdsException(ErrorCode.INVALID_USER_BAKSET);
        }
        byte[] dataBytes = Arrays.copyOfRange(objectMeta, xtrabackupInfoPos, Integer.parseInt(Long.valueOf(xtrabackupInfoPos + dataLength).toString()));
        String infoContent;
        if (fileName.endsWith(".qp")) {
            infoContent = new String(decompressQPress(dataBytes));
        } else {
            infoContent = new String(dataBytes);
        }
        for (String s : infoContent.split("\n")) {
            String[] split = s.split("=");
            if (split.length <= 1) {
                continue;
            }
            backFileMeta.put(split[0].trim(), String.join("=", Arrays.copyOfRange(split, 1, split.length)).trim());
        }
    }

    /**
     * 根据position检查xtrabackup_info
     */
    public boolean isXtrabackupInfoValid(byte[] objectMeta, int xtrabackupInfoPos, Map<String, Object> backfileMeta, String requestId) {
        // position为负，不合法
        if (xtrabackupInfoPos < 0) {
            LOG.error("RequestId: {}, Message: xtrabackupInfoPos is negative, value: {}", requestId, xtrabackupInfoPos);
            return false;
        }
        // chunk类型不为Payload，不合法
        if (objectMeta[xtrabackupInfoPos] != 'P') {
            LOG.error("RequestId: {}, Message: check object meta failed, required chunk type: P, actual chunk type: {}", requestId, objectMeta[xtrabackupInfoPos]);
            return false;
        }
        // 尝试根据position读取元信息
        try {
            parseAndFillBackFileMeta(backfileMeta, objectMeta, xtrabackupInfoPos, requestId);
        } catch (Exception e) {
            LOG.error("RequestId: {}, Message: parseAndFillBackFileMeta failed", e);
            return false;
        }
        return true;
    }

    /**
     * 根据文件后缀检查备份类型
     */
    public BackupType getAndCheckBackupType(OSSClient ossClient, String bucketName, String object, String requestId) throws Exception {
        if (object.toLowerCase().endsWith(".xb")) {
            byte[] objectHeader = getObjectMetaFromOss(ossClient, bucketName, object, 0L, 7L);
            if (!new String(objectHeader).equals(XTRABACKUP_HEADER)) {
                LOG.error("RequestId: {}, Message: User backup has a .xb extension but header is not XBSTCK01, objectHeader: {}", requestId, objectHeader);
                throw new RdsException(ErrorCode.INVALID_BACKUPSET);
            }
            return BackupType.XTRABACKUP;
        } else if (object.toLowerCase().endsWith(".xb.zst") || object.toLowerCase().endsWith(".xbstream.zst")) {
            return BackupType.XTRABACKUP;
        }
        LOG.error("RequestId: {}, Message: user backup type is not supported by now", requestId);
        throw new RdsException(ErrorCode.INVALID_USER_BAKSET);
    }

    /**
     * 根据文件后缀检查备份压缩类型（支持将xbstream文件进行二次压缩）
     */
    public CompressType getAndCheckCompressType(OSSClient ossClient, String bucketName, String objectName, String requestId) throws Exception {
        if (objectName.toLowerCase().endsWith(".zst")) {
            byte[] objectHeader = getObjectMetaFromOss(ossClient, bucketName, objectName, 0L, 3L);
            if (!Arrays.equals(objectHeader, ZSTD_MAGIC_BYTES)) {
                LOG.error("RequestId: {}, Message: User backup file has a .zst extension but the header is not ZSTD, header: {}", requestId, objectHeader);
                throw new RdsException(ErrorCode.INVALID_USER_BAKSET);
            }
            return CompressType.ZSTD;
        }
        return CompressType.NONE;
    }

    // 裁剪 ossFileMetaData，保证在1000字节以内
    public String truncateOssFileMetaData(String ossFileMetaData, String requestId) {
        // 校验长度
        byte[] metaDataBytes = ossFileMetaData.getBytes(StandardCharsets.UTF_8);
        if (metaDataBytes.length <= 1000) {
            return ossFileMetaData;
        }
        // 获取tool_command，记录并剔除
        JSONObject jsonObject = JSON.parseObject(ossFileMetaData);
        if (jsonObject.containsKey("tool_command")) {
            String toolCommand = jsonObject.getString("tool_command");
            LOG.info("RequestId: {}, Message: tool command recorded in oss file meta data: {}", requestId, toolCommand);
            jsonObject.remove("tool_command");
            ossFileMetaData = jsonObject.toJSONString();
        }
        // 再次校验长度
        metaDataBytes = ossFileMetaData.getBytes(StandardCharsets.UTF_8);
        if (metaDataBytes.length > 1000) {
            LOG.info("RequestId: {}, Message: metadata still too long: {}", requestId, ossFileMetaData);
            metaDataBytes = Arrays.copyOfRange(metaDataBytes, 0, 1000);
            ossFileMetaData = new String(metaDataBytes, StandardCharsets.UTF_8);
        }
        return ossFileMetaData;
    }
}
