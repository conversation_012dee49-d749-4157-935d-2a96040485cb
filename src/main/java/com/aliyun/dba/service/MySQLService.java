package com.aliyun.dba.service;

import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsIpWhiteListDO;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.DbsDO;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TransListDO;


import java.util.List;
import java.util.Map;

public interface MySQLService {
    void setCustinAccountMode(CustInstanceDO custins, String accountName, String clusterName, String accountType);

    Integer createCustInstanceTask(String action, CustInstanceDO custins, DbsDO dbs,
                                   AccountsDO account,
                                   Map<String, Object> taskQueueParam,
                                   CustinsIpWhiteListDO custinsIpWhiteList,
                                   Integer operatorId, String osPassWord)
            throws RdsException;
    Integer createExchangeInstanceTask(String action, CustInstanceDO custins,
                                       CustInstanceDO srcins, Integer operatorId,
                                       TransListDO translist, String taskKey)
            throws RdsException;

    Integer createExchangeInsTaskWithParam(String action, CustInstanceDO custins, Integer operatorId,
                                           TransListDO translist, String taskKey, Map<String, Object> taskParam)
            throws RdsException;

    List<ConnAddrChangeLogDO> createConnAddrChangeLogsForCreateVpcNetType(CustInstanceDO custins,
                                                                          CustinsConnAddrDO custinsConnAddrPrivate,
                                                                          CustinsConnAddrDO custinsConnAddrVPC,
                                                                          String region)
        throws RdsException;

    List<ConnAddrChangeLogDO> createConnAddrChangeLogsForCreateVpcNetType(CustInstanceDO custins,
                                                                          CustinsConnAddrDO custinsConnAddrVPC,
                                                                          String region)
            throws RdsException;

    List<ConnAddrChangeLogDO> createConnAddrChangeLogsForCreateVpcNetTypeWithoutPrivateIP(CustInstanceDO custins,
                                                                                          CustinsConnAddrDO custinsConnAddrVPC,
                                                                                          Integer rwType,
                                                                                          Integer isSwitch,
                                                                                          String region)

        throws RdsException;

    List<ConnAddrChangeLogDO> createConnAddrChangeLogsForCreatePublicOrPrivateNetType(CustInstanceDO custins,
                                                                                      CustinsConnAddrDO newCustinsConnAddr,
                                                                                      Integer rwType)
        throws RdsException;

    String getDockerCustinsClusterEndpointType(CustInstanceDO custins, Map<String, String> params)
        throws RdsException;


    List<ConnAddrChangeLogDO> createConnAddrChangeLogsForDeleteVpcNetType(CustInstanceDO custins, CustinsConnAddrDO delCustinsConnAddr,
                                                       CustinsConnAddrDO custinsConnAddrPrivate)
        throws RdsException;


    List<ConnAddrChangeLogDO> createConnAddrChangeLogsForDeletePublicOrPrivateNetType(CustInstanceDO custins, CustinsConnAddrDO delCustinsConnAddr)
        throws RdsException;
    void compareBakSizeAndDiskSize(DescribeRestoreBackupSetResponse restoreBackupResponse, Integer diskSize) throws RdsException;
    void checkCustinsAndUser(Integer custinid, String bid, String uid) throws RdsException;
    boolean isRebuildBackupSet(String backupSetId);

    Integer createExchangeReadInsToPrimaryTask(String action, CustInstanceDO primaryCustins, CustInstanceDO exchangeReadInstance, Integer operatorId,
                                               TransListDO trans, String taskKey)throws RdsException;

    Integer createExchangeReadInsToPrimaryTaskWithParam(String action, CustInstanceDO custins, Integer operatorId,
                                           TransListDO translist, String taskKey, Map<String, Object> taskParam)
            throws RdsException;
}
