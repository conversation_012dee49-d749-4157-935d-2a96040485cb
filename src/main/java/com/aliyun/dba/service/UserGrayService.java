package com.aliyun.dba.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.common.utils.ShuffleUtil;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.CrmService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserGrayService {
    private static final LogAgent LOGGER = LogFactory.getLogAgent(UserGrayService.class);

    @Resource
    private CustinsService custinsService;

    @Resource
    protected ResourceService resourceService;
    @Resource
    protected PodParameterHelper podParameterHelper;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;
    @Resource
    private CrmService crmService;

    private static final String ALLOW_LIST = "allowList";
    private static final String BLOCK_LIST = "blockList";

    public boolean isSwitchGray(String resourceKey) {
        /**
         * 基于UID的通用灰度方案，
         * crc16(uid) % 100 计算UID对应比率；
         * resource配置demo：
         * {
         *     "ratio": 10, # 灰度比例
         *     "allowList": ["123", "456"], # 白名单用户
         *     "blockList": ["789", "987"] # 黑名单用户
         * }
         * */
        try {
            ResourceDO resourceDO = resourceService.getResourceByResKey(resourceKey);
            if (resourceDO == null || resourceDO.getRealValue().isEmpty()) {
                return false;
            }
            JSONObject versionConfig = JSON.parseObject(resourceDO.getRealValue());
            return isHit(versionConfig);
        } catch (Exception e) {
            LOGGER.error(e);
            return false;
        }
    }


    /**
     * 根据灰度配置，判断是否命中
     * @param grayConfig
     * @return
     */
    public boolean isHit(JSONObject grayConfig) {
        try {
            if (grayConfig.isEmpty()) {
                return false;
            }
            Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
            if (paramsMap == null) {
                LOGGER.warn("UID not found, skip to hit gray config");
                return false;
            }
            String uid = podParameterHelper.getParameterValue("UID");
            Integer ratio = grayConfig.getInteger("ratio");
            Set<String> allowList = new HashSet<>();
            Set<String> blockList = new HashSet<>();
            if (grayConfig.containsKey(ALLOW_LIST)) {
                allowList = new HashSet<>(grayConfig.getJSONArray(ALLOW_LIST).toJavaList(String.class));
            }
            if (grayConfig.containsKey(BLOCK_LIST)) {
                blockList = new HashSet<>(grayConfig.getJSONArray(BLOCK_LIST).toJavaList(String.class));
            }
            if (blockList.contains(uid)) {
                return false;
            }
            int uidRatio = ShuffleUtil.crc16(uid) % 100;
            LOGGER.info(String.format("get uid %s uidRatio %s ratio %s", uid, uidRatio, ratio));
            return allowList.contains(uid) || uidRatio < ratio;
        } catch (Exception e) {
            LOGGER.error(e);
            return false;
        }
    }


    /**
     * Pengine迁移新架构灰度控制
     */
    public boolean isMigratePengineToK8SSwitchGray(String uid, String regionId, CustInstanceDO custInstance, InstanceLevelDO instanceLevel) {
        /**
         * 配置的灰度key为MIGRATE_PENGINE_TO_K8S_GRAY
         * 基于UID的通用灰度方案
         * crc16(uid) % 100 计算UID对应比率；
         * resource配置demo：
         * [
         *     {"kindCode":"3" # kindcode类型
         *     "dbVersion": "8.0" #灰度版本
         *     "category": "basic"
         *     "ratio": 10, # 灰度比例
         *     "allowList": ["123", "456"], # 白名单用户
         *     "blockList": ["789", "987"] # 黑名单用户
         *     },
         *     {"kindCode":"3" # kindcode类型
         *     "dbVersion": "8.0" #灰度版本
         *     "category": "basic"
         *     "ratio": 10, # 灰度比例
         *     "allowList": ["123", "456"], # 白名单用户
         *     "blockList": ["789", "987"] # 黑名单用户
         *
         *     },
         * ]
         * */
        try {

            // TODO(wenfeng): 支持经典网络
            List<CustinsConnAddrDO> privateNet = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custInstance.getId(), CustinsSupport.NET_TYPE_PRIVATE, CustinsSupport.RW_TYPE_NORMAL);
            if (!privateNet.isEmpty()) {
                LOGGER.info("Private Net do not support migrate to k8s yet.");
                return false;
            }

            String resourceKey = MySQLParamConstants.MIGRATE_PENGINE_TO_K8S_GRAY;
            String kindCode = custInstance.getKindCode().toString();
            String category = instanceLevel.getCategory();
            String dbVersion = custInstance.getDbVersion();

            //拦截只读， 存在重搭等情况的实例, 带maxscale
            if (category.equalsIgnoreCase("standard")){
                //只读校验 physical
                List<CustInstanceDO> physicalCustInstances = custinsService.getCustInstanceByParentId(custInstance.getId());
                CustInstanceDO physicalCustins = physicalCustInstances.stream().filter(i -> i.getIsTmp() == 0).findFirst().orElse(custInstance);

                // 带只读实例放开限制
                int readCount = custinsService.countReadCustInstanceByPrimaryCustinsId(physicalCustins.getId());
                if (readCount > 0) {
//                    return false;
                    category = "readonly";
                }
                if (physicalCustins.getPrimaryCustinsId() > 0){
                    LOGGER.info("readonly instance not migrate");
                    return false;
                }

                // logic校验maxscal
                Integer custInstanceId = custInstance.getId();
                if (custInstance.getParentId() > 0){
                    CustInstanceDO logicCustInstance = custinsService.getCustInstanceByCustinsId(custInstance.getParentId());
                    custInstanceId = logicCustInstance.getId();
                }
                if (custinsService.checkHaveMaxscaleService(custInstanceId) != null){
                    return false;
                }
            }

            ResourceDO resourceDO = resourceService.getResourceByResKey(resourceKey);
            if (resourceDO == null || resourceDO.getRealValue().isEmpty()) {
                return false;
            }

            int uidRatio = ShuffleUtil.crc16(uid) % 100;

            JSONArray migrateConfigs = JSONArray.parseArray(resourceDO.getRealValue());
            for (Object migrateConfigObject : migrateConfigs) {
                JSONObject migrateConfig = JSONObject.parseObject(migrateConfigObject.toString());
                Integer grayRatio = migrateConfig.getInteger("ratio");
                String grayKindCode = migrateConfig.getString("kindCode");
                String grayCategory = migrateConfig.getString("category");
                String grayDBVersion = migrateConfig.getString("dbVersion");
                JSONArray grayRegionId = null;
                if (migrateConfig.containsKey("regionId")) {
                    grayRegionId = migrateConfig.getJSONArray("regionId");
                }

                Set<String> allowList = new HashSet<>();
                Set<String> blockList = new HashSet<>();
                if (migrateConfig.containsKey(ALLOW_LIST)) {
                    allowList.addAll(migrateConfig.getJSONArray(ALLOW_LIST).toJavaList(String.class));
                }
                if (migrateConfig.containsKey(BLOCK_LIST)) {
                    blockList.addAll(migrateConfig.getJSONArray(BLOCK_LIST).toJavaList(String.class));
                }

                if (kindCode.equalsIgnoreCase(grayKindCode) && category.equalsIgnoreCase(grayCategory) && dbVersion.equalsIgnoreCase(grayDBVersion)) {

                    if (blockList.contains(uid)) {
                        return false;
                    }

                    if (grayRegionId != null && !grayRegionId.contains(regionId)) {
                        return false;
                    }

                    // 按照uid计算的比例值来灰度，同时包括在指定列表里的uid都进行灰度
                    if ((allowList.contains(uid)) || uidRatio < grayRatio) {
                        LOGGER.info(String.format("get uid %s uidRatio %s ratio %s", uid, uidRatio, grayRatio));
                        return true;
                    }
                }
            }

            return false;

        } catch (Exception e) {
            LOGGER.error(e);
            return false;
        }
    }




    public boolean isHitColdData(String requestId, String uid, String regionId) {
        LOGGER.info(String.format("params: %s, %s, %s ", requestId, uid, regionId));
        String resourceKey = "COLD_DATA_GRAY_POLICY_MYSQL";
        ResourceDO resourceDO = resourceService.getResourceByResKey(resourceKey);

        if (resourceDO == null || StringUtils.isBlank(resourceDO.getRealValue())) {
            LOGGER.info(String.format("requestId %s resource %s is null return false", requestId, resourceKey));
            return false;
        }

        JSONObject coldDataGrayPolicy = JSON.parseObject(resourceDO.getRealValue());
        Object obj = coldDataGrayPolicy.get("GlobalBlackUIDs");
        List<String> blockUidList = obj != null ? Arrays.stream(String.valueOf(obj).split(",")).map(String::trim).collect(Collectors.toList()) : new ArrayList<>();
        if (blockUidList.contains(uid)) {
            LOGGER.info(String.format("requestId %s uid %s is in the coldData gray black list", requestId, uid));
            return false;
        }

        String grayGCLevel = coldDataGrayPolicy.getString("GCLevel");
        if (StringUtils.isNotBlank(grayGCLevel)) {
            String gcLevel = crmService.getGcLevel(uid);
            if (StringUtils.compare(gcLevel.toUpperCase(), grayGCLevel.toUpperCase()) > 0) {
                LOGGER.info(String.format("requestId %s uid %s gc level %s is higher than gray gc level", requestId, uid, gcLevel));
                return false;
            }
        }

        if (coldDataGrayPolicy.containsKey(regionId)) {
            JSONObject regionConfig = coldDataGrayPolicy.getJSONObject(regionId);
            int switchRatio = Integer.parseInt(regionConfig.getString("SwitchRatio"));
            Object uidObj = regionConfig.get("UID");
            List<String> uidList = uidObj != null ? Arrays.stream(String.valueOf(uidObj).split(",")).map(String::trim).collect(Collectors.toList()) : new ArrayList<>();
            boolean isUidHit = uidList.contains("*") || uidList.contains(uid);
            if (isUidHit) {
                LOGGER.info(String.format("requestId %s uid %s is hit the ColdData white list gray policy", requestId, uid));
                return true;
            }
            int ratio = ShuffleUtil.crc16(uid) % 100;
            if (ratio < switchRatio) {
                LOGGER.info(String.format("requestId %s ratio %s is hit the ColdData ratio gray policy", requestId, ratio));
                return true;
            }
        }
        return false;
    }


    public boolean isHitCompression(String requestId, String uid, String regionId) {
        LOGGER.info(String.format("params: %s, %s, %s ", requestId, uid, regionId));
        String resourceKey = "COMPRESSION_GRAY_POLICY_MYSQL_CLOUD";
        ResourceDO resourceDO = resourceService.getResourceByResKey(resourceKey);

        if (resourceDO == null || StringUtils.isBlank(resourceDO.getRealValue())) {
            LOGGER.info(String.format("requestId %s resource %s is null return false", requestId, resourceKey));
            return false;
        }

        JSONObject grayPolicy = JSON.parseObject(resourceDO.getRealValue());
        Object obj = grayPolicy.get("GlobalBlackUIDs");
        List<String> blockUidList = obj != null ? Arrays.stream(String.valueOf(obj).split(",")).map(String::trim).collect(Collectors.toList()) : new ArrayList<>();
        if (blockUidList.contains(uid)) {
            LOGGER.info(String.format("requestId %s uid %s is in the Compression gray black list", requestId, uid));
            return false;
        }

        String grayGCLevel = grayPolicy.getString("GCLevel");
        if (StringUtils.isNotBlank(grayGCLevel)) {
            String gcLevel = crmService.getGcLevel(uid);
            if (StringUtils.compare(gcLevel.toUpperCase(), grayGCLevel.toUpperCase()) > 0) {
                LOGGER.info(String.format("requestId %s uid %s gc level %s is higher than gray gc level", requestId, uid, gcLevel));
                return false;
            }
        }

        if (grayPolicy.containsKey(regionId)) {
            JSONObject regionConfig = grayPolicy.getJSONObject(regionId);
            int switchRatio = Integer.parseInt(Optional.ofNullable(regionConfig.getString("SwitchRatio")).orElse("0"));
            Object uidObj = regionConfig.get("UID");
            List<String> uidList = uidObj != null ? Arrays.stream(String.valueOf(uidObj).split(",")).map(String::trim).collect(Collectors.toList()) : new ArrayList<>();
            boolean isUidHit = uidList.contains("*") || uidList.contains(uid);
            if (isUidHit) {
                LOGGER.info(String.format("requestId %s uid %s is hit the Compression white list gray policy", requestId, uid));
                return true;
            }
            int ratio = ShuffleUtil.crc16(uid) % 100;
            if (ratio < switchRatio) {
                LOGGER.info(String.format("requestId %s ratio %s is hit the Compression ratio gray policy", requestId, ratio));
                return true;
            }
        }
        return false;
    }

}
