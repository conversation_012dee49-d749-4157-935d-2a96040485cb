package com.aliyun.dba.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.BlueGreenSwitchInfo;
import com.aliyun.apsaradb.dbaasmetaapi.model.BlueGreenSwitchInfoListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.consts.BlueGreenDeploymentConsts;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.BlueGreenDeploymentCommonService;
import com.aliyun.dba.base.service.DtsClientService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.idao.InstancePerfIDaoImpl;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dts20200101.Client;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailRequest;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponse;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponseBody;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Service
public class BlueGreenDeploymentServiceImpl implements BlueGreenDeploymentService {
    private static final LogAgent logger = LogFactory.getLogAgent(BlueGreenDeploymentServiceImpl.class);
    @Autowired
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;
    @Autowired
    private DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    private InstanceService instanceService;

    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private BlueGreenDeploymentCommonService commonService;
    @Autowired
    private DtsClientService dtsClientService;
    @Autowired
    private InstancePerfIDaoImpl instancePerfIDao;



    @Override
    public Map<String, Object> createBlueGreenDeployment(String requestId, String regionId,
                                                         String aliUid,
                                                         CustInstanceDO custInstance,
                                                         Map<String, Object> newPrimaryConfig,
                                                         List<Map<String, Object>> newRoConfig,
                                                         List<Map<String, Object>> newReplicaConfig,
                                                         Map<String, Object> newProxyConfig) throws RdsException {
        try {
            String dbInstanceName = custInstance.getInsName();
            // InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custInstance.getLevelId());
            // Boolean isCluster = InstanceLevel.CategoryEnum.CLUSTER.getValue().equals(insLevel.getCategory());
            String blueCustomParamGroupId = dBaasMetaService.getDefaultClient().getReplicaSetLabel(RequestSession.getRequestId(), custInstance.getInsName(), "param_group_id");
            // 检查原实例的状态
            commonService.preCheckWhenCreating(requestId, custInstance);
            // 获取绿色实例的配置，即任务流按照该配置创建绿色实例。[基础版、高可用]与[集群版]实例分开处理
            JSONObject jsonObject = null;
            // 当前支持高可用不带只读和代理，若支持集群版，则打开下面注释
            jsonObject = commonService.getGreenNormalInstanceConfig(custInstance, regionId, newPrimaryConfig, newRoConfig, newProxyConfig);
            //if (isCluster) {
            //    jsonObject = getGreenClusterInstanceConfig(custInstance, regionId, newPrimaryConfig, newReplicaConfig, newProxyConfig);
            //} else {
            //    jsonObject = getGreenNormalInstanceConfig(custInstance, regionId, newPrimaryConfig, newRoConfig, newProxyConfig);
            //}
            // 生成蓝绿部署名
            String deploymentName = commonService.generateBlueGreenDeploymentName();
            logger.info("deployment name : {}", deploymentName);
            // 创建蓝绿关系Relationship
            BlueGreenDeploymentRel blueGreenDeploymentRel = new BlueGreenDeploymentRel();
            blueGreenDeploymentRel.setDeploymentName(deploymentName);
            blueGreenDeploymentRel.setBlueCustinsId(custInstance.getId());
            blueGreenDeploymentRel.setBlueCustinsName(dbInstanceName);
            blueGreenDeploymentRel.setStatus((byte) 0);
            blueGreenDeploymentRel.setIsDeleted((byte) 0);
            blueGreenDeploymentRelServiceIDao.createBlueGreenDeploymentRel(blueGreenDeploymentRel);
            jsonObject.put("deploymentName", deploymentName);
            jsonObject.put("aliUid", aliUid);
            jsonObject.put("regionId", regionId);
            if (StringUtils.isNotEmpty(blueCustomParamGroupId)) {
                jsonObject.put("blueCustomParamGroupId", blueCustomParamGroupId);
            }
            // 下发任务流
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = PodDefaultConstants.TASK_CREATE_BLUE_GREEN_DEPLOYMENT;
            String parameter = jsonObject.toJSONString();
            logger.info("parameter : {}", parameter);
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);
            logger.info("taskId : " + taskId);
            // 构造返回结果
            Map<String, Object> data = new HashMap<>();
            data.put("blueGreenDeploymentName", deploymentName);
            data.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
            logger.info("data : {}", JSONObject.toJSONString(data));
            return data;
        } catch (RdsException ex) {
            logger.error("createBlueGreenDeployment failed: {}", JSONObject.toJSONString(ex));
            throw ex;
        } catch (Exception ex) {
            logger.error("Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    @Override
    public Map<String, Object> switchBlueGreenInstance(String regionId, String aliUid, String bid, CustInstanceDO custInstanceDO, String greenInstanceName, String deploymentName, Map<String, Object> switchInfo) throws Exception {

        String custInstanceName = custInstanceDO.getInsName();
        ReplicaSet blueInstance = dBaasMetaService.getDefaultClient().getReplicaSet(RequestSession.getRequestId(), custInstanceName, false);
        ReplicaSet greenInstance = dBaasMetaService.getDefaultClient().getReplicaSet(RequestSession.getRequestId(), greenInstanceName, false);
        logger.info("start switchBlueGreenInstancePreCheck");
        Map<String, Object> checkedResult = switchBlueGreenInstancePreCheck(regionId,
            aliUid,
            bid,
            custInstanceDO,
            deploymentName,
            custInstanceName,
            greenInstanceName, false);
        if (!MapUtils.getBoolean(checkedResult, "checkPass", true)) {
            List<Map<String, Object>> checkItems = (List<Map<String, Object>>) MapUtils.getObject(checkedResult, "CheckItems");
            for (Map<String, Object> checkItem : checkItems) {
                if (checkItem.get("checkPass").equals(false)) {
                    throw new RdsException((ErrorCode) checkItem.get("errorCode"));
                }
            }
        }

        Long switchInfoId = createSwitchInfo(deploymentName, custInstanceName, greenInstanceName);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("regionId", regionId);
        jsonObject.put("aliUid", aliUid);
        jsonObject.put("deploymentName", deploymentName);
        jsonObject.put("blueInstanceName", custInstanceDO.getInsName());
        jsonObject.put("greenInstanceName", greenInstanceName);
        jsonObject.put("blueInstanceId", blueInstance.getId());
        jsonObject.put("greenInstanceId", greenInstance.getId());
        jsonObject.put("kindCode", blueInstance.getKindCode());
        jsonObject.put("switchInfoId", switchInfoId);
        jsonObject.put(CustinsSupport.SWITCH_KEY, switchInfo);
        String domain = PodDefaultConstants.DOMAIN_MYSQL;
        String taskKey = PodDefaultConstants.TASK_SWITCH_BLUE_GREEN_DEPLOYMENT;
        String parameter = jsonObject.toJSONString();
        dBaasMetaService.getDefaultClient().updateReplicaSetStatus(RequestSession.getRequestId(), custInstanceName, "bluegreen_switching");
        dBaasMetaService.getDefaultClient().updateReplicaSetStatus(RequestSession.getRequestId(), greenInstanceName, "bluegreen_switching");
        logger.info("parameter : {}", parameter);
        Object taskId = workFlowService.dispatchTask("custins", custInstanceName, domain, taskKey, parameter, 0);
        logger.info("taskId : " + taskId);
        // 构造返回结果
        Map<String, Object> data = new HashMap<>();
        data.put(ParamConstants.DB_INSTANCE_NAME, custInstanceDO);
        logger.info("data : {}", JSONObject.toJSONString(data));
        return data;
    }

    public Long createSwitchInfo(String deploymentName, String blueInstanceName, String greenInstanceName) throws ApiException {

        BlueGreenDeploymentRel rel = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByDeploymentName(deploymentName);
        ReplicaSet blueCustins = dBaasMetaService.getDefaultClient().getReplicaSet(RequestSession.getRequestId(), blueInstanceName, false);
        ReplicaSet greenCustins = dBaasMetaService.getDefaultClient().getReplicaSet(RequestSession.getRequestId(), greenInstanceName, false);

        BlueGreenSwitchInfo switchInfo = new BlueGreenSwitchInfo();
        switchInfo.setDeploymentId(rel.getId());
        switchInfo.setBlueCustinsId(blueCustins.getId());
        switchInfo.setBlueCustinsName(blueCustins.getName());
        switchInfo.setGreenCustinsId(greenCustins.getId());
        switchInfo.setGreenCustinsName(greenCustins.getName());

        Map<String, Object> blueCustinsInfo = new HashMap<>();
        blueCustinsInfo.put("blueInstanceId", blueCustins.getId());
        blueCustinsInfo.put("blueInstanceName", blueCustins.getName());
        Map<String, Object> greenCustinsInfo = new HashMap<>();
        greenCustinsInfo.put("greenInstanceId", greenCustins.getId());
        greenCustinsInfo.put("greenInstanceName", greenCustins.getName());

        switchInfo.setBlueCustinsInfo(JSON.toJSONString(blueCustinsInfo));
        switchInfo.setGreenCustinsInfo(JSON.toJSONString(greenCustinsInfo));
        switchInfo.setSwitchLog("");
        switchInfo.setSwitchTime(OffsetDateTime.now());
        // switch status : 0success, 1failed, 2switching
        switchInfo.setSwitchStatus(2);

        BlueGreenSwitchInfo result = dBaasMetaService.getDefaultClient().createBlueGreenSwitchInfo(RequestSession.getRequestId(), switchInfo);
        logger.info("The create result is {}", JSON.toJSONString(result));
        return result.getId();
    }

    @Override
    public Map<String, Object> deleteBlueGreenDeployment(String regionId, String aliUid, CustInstanceDO custInstance, String deploymentName, String mode) throws Exception {
        String dbInstanceName = custInstance.getInsName();
        Long custinsId = Long.valueOf(custInstance.getId());
        BlueGreenDeploymentRel relation = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, deploymentName, custinsId, null);
        if (relation == null) {
            logger.error("Can not find BlueGreenDeploymentRel. deploymentName : {}, custinsId : {}", deploymentName, custinsId);
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }
        // 找到绿色实例
        String greenInstanceName = relation.getGreenCustinsName();
        CustInstanceDO greenCustins = custinsService.getCustInstanceByInsName(null, greenInstanceName);
        // 预检查，检查各实例状态
        commonService.preCheckBeforeDeleteDeployment(greenCustins, mode);
        // 更新蓝绿部署关系为删除中
        relation.setStatus((byte) 3);
        blueGreenDeploymentRelServiceIDao.updateBlueGreenDeploymentRel(relation);
        // 下发任务流，删除DTS和绿色实例（需要的话）
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("greenInstanceName", greenInstanceName);
        jsonObject.put("deploymentName", deploymentName);
        jsonObject.put("regionId", regionId);
        jsonObject.put("aliUid", aliUid);
        jsonObject.put("mode", mode);
        String domain = PodDefaultConstants.DOMAIN_MYSQL;
        String taskKey = PodDefaultConstants.TASK_DELETE_BLUE_GREEN_DEPLOYMENT;
        String parameter = jsonObject.toJSONString();
        logger.info("parameter : {}", parameter);
        Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);
        logger.info("taskId : " + taskId);
        // 构造返回结果
        Map<String, Object> data = new HashMap<>();
        data.put("deploymentName", deploymentName);
        data.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
        logger.info("data : {}", JSONObject.toJSONString(data));
        return data;
    }

    @Override
    public Map<String, Object> describeBlueGreenSyncInfo(CustInstanceDO custins, String requestId, String regionId, String aliUid, String dbInstanceName) throws Exception {
        try {
            BlueGreenDeploymentRel blueGreenDeploymentRel = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByBlueCustinsId(Long.valueOf(custins.getId()));
            if (ObjectUtils.isEmpty(blueGreenDeploymentRel)) {
                logger.error("There is no blueGreenDeploymentRel.");
                throw new Exception("There is no blueGreenDeploymentRel.");
            }
            boolean isCreating = BlueGreenDeploymentConsts.CREATING == blueGreenDeploymentRel.getStatus();
            boolean hasDtsInfo = StringUtils.isNotEmpty(blueGreenDeploymentRel.getDtsInfo());
            if (isCreating && !hasDtsInfo) {
                Map<String, Object> result = new HashMap<>();
                result.put("status", BlueGreenDeploymentConsts.DEPLOYMENT_STATUS_CREATING);
                return result;
            }
            if (!hasDtsInfo) {
                logger.error("There is no dts info.");
                throw new Exception("There is no dts info.");
            }
            JSONObject dtsInfo = JSONObject.parseObject(blueGreenDeploymentRel.getDtsInfo());
            String dtsJobId = String.valueOf(dtsInfo.get(BlueGreenDeploymentConsts.DTS_JOB_ID));
            String dtsInstanceId = String.valueOf(dtsInfo.get(BlueGreenDeploymentConsts.DTS_INSTANCE_ID));
            String endTime = String.valueOf(dtsInfo.get(BlueGreenDeploymentConsts.DTS_END_TIME));
            if (StringUtils.isEmpty(dtsInstanceId) || StringUtils.isEmpty(dtsJobId)) {
                logger.error("The dtsinfo is empty.");
                throw new Exception("The dtsinfo is empty.");
            }
            Client dtsClient = dtsClientService.getDtsClient(regionId, aliUid);
            DescribeDtsJobDetailRequest describeDtsJobDetailRequest = new DescribeDtsJobDetailRequest();
            describeDtsJobDetailRequest.setDtsJobId(dtsJobId);
            describeDtsJobDetailRequest.setDtsInstanceID(dtsInstanceId);
            logger.info("invoke describeDtsJobDetail start, request : {}", JSONObject.toJSONString(describeDtsJobDetailRequest));
            DescribeDtsJobDetailResponse response = dtsClient.describeDtsJobDetail(describeDtsJobDetailRequest);
            logger.info("invoke describeDtsJobDetail end. response : {}", JSONObject.toJSONString(response));
            if (ObjectUtils.isEmpty(response)) {
                throw new Exception("DescribeDtsJobDetailResponse is empty.");
            }

            // 判断当前是否已经切换过（控制台用于判断显示切换or回退按钮）
            String switchMode;
            BlueGreenSwitchInfoListResult switchInfoListResult = dBaasMetaService.getDefaultClient().getBlueGreenSwitchInfo(RequestSession.getRequestId(), blueGreenDeploymentRel.getId(), null, null);
            List<BlueGreenSwitchInfo> switchInfoList = switchInfoListResult.getItems();
            if (switchInfoList == null) {
                switchMode = "switch";
            } else {
                switchInfoList = switchInfoList.stream()
                        .filter(switchInfo -> switchInfo.getSwitchStatus() == 0)
                        .collect(Collectors.toList());
                int size = switchInfoList.size();
                logger.info("switchInfoList size : {}", size);
                if (size % 2 == 0) {
                    switchMode = "switch";
                } else {
                    switchMode = "rollback";
                }
            }

            Map<String, Object> result = new HashMap<>();
            DescribeDtsJobDetailResponseBody body = response.getBody();
            result.put("status", body.getStatus());
            result.put("checkpoint", body.getCheckpoint());
            result.put("delay", body.getDelay() / 1000);
            result.put("createTime", body.getCreateTime());
            result.put("blueInstanceId", body.getSourceEndpoint().getInstanceID());
            result.put("greenInstanceId", body.getDestinationEndpoint().getInstanceID());
            // 获取蓝实例描述
            ReplicaSet blueReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, blueGreenDeploymentRel.getBlueCustinsName(), false);
            result.put("blueInstanceDescription", org.apache.commons.lang3.ObjectUtils.firstNonNull(blueReplicaSet.getComment(), blueReplicaSet.getName()));
            // 获取绿实例描述
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, blueGreenDeploymentRel.getGreenCustinsName(), false);
            result.put("greenInstanceDescription", org.apache.commons.lang3.ObjectUtils.firstNonNull(replicaSet.getComment(), replicaSet.getName()));
            // 绿实例的免费试用到期时间
            String freeTime = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, blueGreenDeploymentRel.getGreenCustinsName(), "free_time");
            result.put("greenInstanceTrialEndTime", freeTime);
            result.put("dtsJobId", body.getDtsJobId());
            result.put("dtsInstanceName", body.getDtsInstanceID());
            // 增量数据迁移或同步的状态.
            //result.put("dataSynchronizationStatus", body.getDataSynchronizationStatus());
            result.put("blueGreenDeploymentName", blueGreenDeploymentRel.getDeploymentName());
            result.put("endTime", endTime);
            result.put("switchMode", switchMode);
            return result;
        } catch (RdsException ex) {
            logger.error("DescribeSyncInfo failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("DescribeDtsJobDetail Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    @Override
    public Map<String, Object> switchBlueGreenInstancePreCheck(String regionId, String aliUid, String bid, CustInstanceDO custInstanceDO, String blueGreenDeploymentName, String dbInstanceName, String greenDBInstanceId, Boolean skipStatusCheck) throws Exception {
        // blueGreenDeploymentName不能为空
        if (StringUtils.isEmpty(blueGreenDeploymentName)) {
            logger.info("BlueGreenDeploymentName cannot be empty.");
            throw new RdsException(ErrorCode.BLUE_GREEN_DEPLOYMENT_NAME_CANNOT_BE_EMPTY);
        }
        // DBInstanceName不能为空
        if (StringUtils.isEmpty(dbInstanceName)) {
            logger.info("DBInstanceName cannot be empty.");
            throw new RdsException(ErrorCode.DB_INSTANCE_NAME_CANNOT_BE_EMPTY);
        }
        // 从数据库检查blueGreenDeploymentName合法性
        BlueGreenDeploymentRel blueGreenDeploymentRel = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByDeploymentName(blueGreenDeploymentName);
        if (ObjectUtils.isEmpty(blueGreenDeploymentRel)) {
            logger.info("BlueGreenDeploymentRel is empty, Please check if blueGreenDeploymentName is correct.");
            throw new RdsException(ErrorCode.BLUE_GREEN_DEPLOYMENT_RELATION_CANNOT_BE_EMPTY);
        }
        // 检查dbInstanceName合法性
        if (!dbInstanceName.equals(blueGreenDeploymentRel.getBlueCustinsName())) {
            logger.info("Please check if dbInstanceName is correct.");
            throw new RdsException(ErrorCode.DB_INSTANCE_NAME_MISMATCH);
        }
        // greenDBInstanceId为空，则使用blueGreenDeploymentRel中的greenCustinsName
        if (StringUtils.isEmpty(greenDBInstanceId)) {
            greenDBInstanceId = blueGreenDeploymentRel.getGreenCustinsName();
        }

        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("dbinstancename", greenDBInstanceId);
        actionParams.put("regionID", regionId);
        actionParams.put("uid", aliUid);
        actionParams.put("user_id", bid);
        CustInstanceDO greenCustInstanceDO = paramSupport.getAndCheckCustInstance(actionParams);

        List<Map<String, Object>> allCheckList = new ArrayList<>();

        // 检查实例状态
        if (!skipStatusCheck) {
            Map<String, Object> checkStatusMap = new HashMap<>();
            commonService.checkStatus(custInstanceDO, greenCustInstanceDO, checkStatusMap);
            allCheckList.add(checkStatusMap);
            logger.info("status check result is {}", JSON.toJSONString(checkStatusMap));
        }

        // 检查蓝绿实例的DB
        Map<String, Object> checkDBMap = new HashMap<>();
        List<String> blueInstanceDBs = commonService.getDBs(custInstanceDO, null, new HashMap<>());
        List<String> greenInstanceDBs = commonService.getDBs(greenCustInstanceDO, null, new HashMap<>());
        commonService.checkBlueGreenList(dbInstanceName, blueInstanceDBs, greenInstanceDBs, "DB", checkDBMap, ErrorCode.INCONSISTENT_DATABASES);
        allCheckList.add(checkDBMap);
        logger.info("BlueGreenInstance Databases: blueInstanceDatabases={}, greenInstanceDatabases={}, checkDBMap={}", JSON.toJSONString(blueInstanceDBs), JSON.toJSONString(greenInstanceDBs), JSON.toJSONString(checkDBMap));

        // 检查蓝绿实例的Account
        Map<String, Object> checkAccountMap = new HashMap<>();
        List<String> blueInstanceAccounts = commonService.getAccounts(custInstanceDO, null, null, new HashMap<>());
        List<String> greenInstanceAccounts = commonService.getAccounts(greenCustInstanceDO, null, null, new HashMap<>());
        commonService.checkBlueGreenList(dbInstanceName, blueInstanceAccounts, greenInstanceAccounts, "Account", checkAccountMap, ErrorCode.INCONSISTENT_ACCOUNTS);
        allCheckList.add(checkAccountMap);
        logger.info("BlueGreenInstance Accounts: blueInstanceAccounts={}, greenInstanceAccounts={}, checkAccountMap={}", JSON.toJSONString(blueInstanceAccounts), JSON.toJSONString(greenInstanceAccounts), JSON.toJSONString(checkAccountMap));

        // 检查表数量
        Map<String, Object> checkTableMap = new HashMap<>();
        commonService.checkBlueGreenTableCount(custInstanceDO, greenCustInstanceDO, checkTableMap);
        allCheckList.add(checkTableMap);
        logger.info("check table result is {}", JSON.toJSONString(checkTableMap));

        // 检查 DTS
        Map<String, Object> checkDtsStatusMap = new HashMap<>();
        Map<String, Object> checkDtsDelayMap = new HashMap<>();
        commonService.checkDts(regionId, aliUid, blueGreenDeploymentRel, checkDtsStatusMap, checkDtsDelayMap);
        allCheckList.add(checkDtsStatusMap);
        allCheckList.add(checkDtsDelayMap);

        // 检查白名单
        // Map<String, Object> checkIpWhiteListMap = new HashMap<>();
        // commonService.checkIpWhiteList(custInstanceDO, greenCustInstanceDO, checkIpWhiteListMap);
        // allCheckList.add(checkIpWhiteListMap);

        // 检查安全组
        // Map<String, Object> checkSecurityGroupMap = new HashMap<>();
        // commonService.checkSecurityGroupList(custInstanceDO, greenCustInstanceDO, checkSecurityGroupMap);
        // allCheckList.add(checkSecurityGroupMap);

        // 检查只读
        Map<String, Object> checkReadOnlyMap = new HashMap<>();
        commonService.checkHasReadOnly(custInstanceDO, greenCustInstanceDO, checkReadOnlyMap);
        allCheckList.add(checkReadOnlyMap);

        // 检查maxscale代理
        Map<String, Object> checkMaxScaleMap = new HashMap<>();
        commonService.checkMaxScale(custInstanceDO, greenCustInstanceDO, checkMaxScaleMap);
        allCheckList.add(checkMaxScaleMap);

        // 检查内网链接
        Map<String, Object> checkConnectionStringMap = new HashMap<>();
        commonService.checkConnectionString(custInstanceDO, greenCustInstanceDO, checkConnectionStringMap);
        allCheckList.add(checkConnectionStringMap);

        Map<String, Object> results = processCheckResults(allCheckList);
        logger.info("switchBlueGreenInstancePreCheck end. result={} ", JSON.toJSONString(results));
        return results;
    }


    /**
     * 处理检查结果，生成最终的 result Map。
     *
     * @param allCheckList 包含所有检查项的列表
     * @return 包含检查结果的 Map
     */
    public Map<String, Object> processCheckResults(List<Map<String, Object>> allCheckList) {
        Map<String, Object> result = new HashMap<>();
        boolean checkPass = allCheckList.stream().allMatch(map -> map.get("checkPass") != null && (boolean) map.get("checkPass"));
        result.put("checkPass", checkPass);
        result.put("CheckItems", allCheckList);
        return result;
    }

    @Override
    public Map<String, Object> switchHostinsPerfMeta(Integer blueInstanceId, Integer greenInstanceId) {
        logger.info("blueInstanceId : {}, greenInstanceId : {}", blueInstanceId, greenInstanceId);
        Map<String, Object> condition = new HashMap<>();
        condition.put("custinsId", blueInstanceId);
        logger.info("condition 1 : {}", JSON.toJSONString(condition));
        List<InstancePerfDO> bluePerfList = instancePerfIDao.getInstancePerfByCondition(condition);
        condition.put("custinsId", greenInstanceId);
        logger.info("condition 2 : {}", JSON.toJSONString(condition));
        List<InstancePerfDO> greenPerfList = instancePerfIDao.getInstancePerfByCondition(condition);

        instancePerfIDao.deleteByCustinsId(blueInstanceId, bluePerfList.size());
        instancePerfIDao.deleteByCustinsId(greenInstanceId, greenPerfList.size());

        Map<String, Object> data = new HashMap<>();
        data.put("result", "success");
        return data;
    }





}