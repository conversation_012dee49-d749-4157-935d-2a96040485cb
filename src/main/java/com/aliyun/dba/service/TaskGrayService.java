package com.aliyun.dba.service;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/** XDBProvider切换到MySQLProvider灰度配置
 * */
@Service
public class TaskGrayService {

    private static final String RESOURCE_KEY = "POD_TASK_GRAY";

    private static final String TDDL_PREFIX = "tddl_";

    public static final String TASK_REBUILD_SLAVE = "rebuild_slave";
    public static final String TASK_MOD_INS = "mod_ins";

    @Autowired
    protected ResourceService resourceService;

    public boolean isSupportTaskGray(ReplicaSet replicaSet, String taskName) {
        // 因为任务流先切换Physical，再切换TDDL。所以tddl实例的key默认加tddl_前缀
        if (ReplicaSetService.isTDDL(replicaSet)) {
            taskName = TDDL_PREFIX + taskName;
        }
        ResourceDO resourceDO = resourceService.getResourceByResKey(RESOURCE_KEY);
        if (resourceDO == null) {
            return false;
        }
        String value = resourceDO.getRealValue();
        String[] tasks = value.split(",");
        return Arrays.asList(tasks).contains(taskName);
    }
}
