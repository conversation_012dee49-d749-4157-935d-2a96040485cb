package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.common.ResponseSupport.createUnknownExceptionResponse;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalModifyDBInstanceRWTypeImpl")
public class ModifyDBInstanceRWTypeImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(ModifyDBInstanceRWTypeImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceRWTypeImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected CustinsService custinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
        RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            String rwType = mysqlParamSupport.getParameterValue(actionParams, "rwType", 0);
            if (! CustinsParamSupport.CUSTINS_PARAM_VALUE_SET_RW_TYPES.contains(rwType)) {
                return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
            }

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            // 更新实例custins_param表
            CustinsParamDO rwTypeParam = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_RW_TYPE);
            if (rwTypeParam == null) {
                custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_RW_TYPE, rwType));
            } else {
                custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_RW_TYPE, rwType);
            }
            custinsService.updateCustinsRWType(custins.getId(), Integer.valueOf(rwType));
            return new HashMap<String, Object>();
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
