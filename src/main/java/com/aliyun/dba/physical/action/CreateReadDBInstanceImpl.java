package com.aliyun.dba.physical.action;

import com.alibaba.cobar.util.StringUtil;
import com.alibaba.fastjson.JSON;

import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.ConnAddrService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.*;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.physical.action.service.CreateReadDBInstanceStandardImpl;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.aliyun.dba.base.parameter.MysqlParameterHelper.XDB_RO_EXCLUDE_HOST_LEVELS;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;

import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.*;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.POLARX_USING_DBS_SERVICE_PENGINE_YES;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ_BACKUP;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCreateReadDBInstanceImpl")
public class CreateReadDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(CreateDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CreateReadDBInstanceImpl.class);

    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected AccountService accountService;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsResourceService custinsResourceService;
    @Autowired
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MySQLService mySQLService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CreateReadDBInstanceStandardImpl createReadDBInstanceStandardImpl;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    private ConnAddrService connAddrService;

    private static String physicalBakReadClusterKey = "physical_bak_read_cluster";

    /**
     * 创建只读实例
     *
     * @category CreateReadDBInstance
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);


            if (!custins.isMysql() || custins.isShare() || custins.isCustinsOnEcs()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (!custins.isPrimary()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            
            if (!custins.isLogicPrimary()) {//未发生容灾切换
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_MASTER_DBINSTANCE_STATUS);
            }
            
            if (!custins.isMysql56() && !custins.isMysql80() && !(custins.isMysql57() && !custins.isCustinsOnEcs())) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }

            if (custins.isReadAndWriteLock()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            if (CustinsState.STATE_READINS_TRANSING.getComment().equals(custins.getStatusDesc()) ||
                    CustinsState.STATE_MAINTAINING.getComment().equals(custins.getStatusDesc()) ||
                    CustinsState.STATE_TRANSING.getComment().equals(custins.getStatusDesc())) {
                // 主实例迁移中不能创建只读
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (!custins.isActive() && !custins.isReadMAorReadTR()) {
                //只读创建过滤 只读只读实例维护 与只读实例升降级状态
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            Boolean autoCreateProxy = mysqlParamSupport.getAutoCreateProxy(params);
            // 下发创建实例的后置任务
            if (autoCreateProxy) {
                logger.info("custins {} start to create proxy.", custins.getInsName());
                workFlowService.dispatchTask("custins", custins.getInsName(), PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_MODIFY_INS_AFTER_CREATE, "", WorkFlowService.TASK_PRIORITY_COMMON);
            }

            String instype = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_USED_TYPE);
            boolean createbackupreadins = false;
            if (instype != null && instype.equals(CUSTINS_INSTYPE_READ_BACKUP.toString())) {
                createbackupreadins = true;
            }

            InstanceLevelDO srcInsLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            boolean isStandard = InstanceLevel.CategoryEnum.STANDARD.getValue().equals(srcInsLevel.getCategory());

            String uid = mysqlParaHelper.getParameterValue(ParamConstants.UID);

            if (isCreateReadOnlyStandard(custins, createbackupreadins, isStandard, uid)){
                logger.info("custins {} create standard readonly instance.", custins.getInsName());
                return createReadDBInstanceStandardImpl.doActionRequest(custins, params);
            }

            if (!createbackupreadins && custinsService.countReadCustInstanceByPrimaryCustinsId(custins.getId()) >=
                    ResourceSupport.getInstance().getIntegerRealValue(
                            ResourceKey.RESOURCE_CUSTINS_MYSQL_READINS_COUNT)) {//超过只读实例个数限制
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            boolean usingReplicator = this.getIsReplicatorReadDBInstance(); // 确认是否使用 Proxy Binlog 复制器
            String region = mysqlParamSupport.getAndCheckRegion(params);
            if (usingReplicator) {
                // - 对于直连只读实例, 不要求与主实例同 Region (原注释: 2.7.20 只读实例不要求与主实例在同一个 Region)
                // - 由于目前 BLS 限制, 要求 BLS 只读实例同 Region (FIXME: 在 BLS 去除该限制后移除这个校验)
                String custinsRegion = avzSupport.getCustInstanceMainLocation(custins.getId());
                if (StringUtil.isEmpty(region)) {
                    region = custinsRegion;
                }
                if (!region.equals(custinsRegion)) {
                    ResponseSupport.createErrorResponse(ErrorCode.INVALID_REGION);
                }
            }

            Integer proportion = null;
            // mysql proportion为 只读实例和备用只读实例, 比值默认3:1
            if (custins.isMysql()) {
                try {
                    String proportionStr = ResourceSupport.getInstance().getStringRealValue(ResourceKey.MYSQL_READ_BAKREAD_PROPORTION);
                    proportion = Integer.parseInt(proportionStr);
                } catch (RdsException e) {
                    proportion = 3;
                }
            }

            Integer bakReads = custinsService.countReadCustInstanceBackupByPrimaryCustinsId(custins.getId(), region);
            Integer reads = custinsService.countReadCustInstanceByPrimaryCustinsIdAndRegion(custins.getId(), region);
            //如果当前实例没有备用只读实例，则创建一个
            if (createbackupreadins && proportion != null) {
                Integer tmpCount = bakReads * proportion;
                if (reads.compareTo(tmpCount) <= 0) {
                    ResponseSupport.createErrorResponse(ErrorCode.BACKUPREADINSTANCE_EXCEEDED);
                }
            }

            CustInstanceDO readins = custins.clone();
            String dbVersion = mysqlParamSupport.getDBVersion(params, readins.getDbType());
            if (Validator.isNull(dbVersion)) {
                dbVersion = "5.6";//只读实例默认版本为MySQL5.6
                // todo 这里是否可以修改为默认与只读实例同版本号 其他拦截在前面就已经拦截掉了 是否可以修改
                // 由于5.7 实例on 物理机和5.6混搭, 如果未传参数, 则将dbversion设置为和原实例一样
                if (readins.isMysql80Physical() || readins.isMysql57Physical()) {
                    dbVersion = readins.getDbVersion();
                }
            } else {
                //只读实例版本，传递version时，必须与主实例保持一致
                if(!dbVersion.equalsIgnoreCase(custins.getDbVersion())){
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
                }
            }

            readins.setInsName(
                    CheckUtils.checkValidForInsName(mysqlParamSupport.getParameterValue(params, "readdbinstancename")));
            if (custinsService.hasCustInstanceByInsName(readins.getInsName())) {
                ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }
            readins.setDbVersion(dbVersion);
            readins.setUsingReplicator(usingReplicator); // 设置启用复制器标记

            Integer netType = CustinsSupport.getNetType(mysqlParamSupport.getParameterValue(params, "dbinstancenettype",
                    CustinsSupport.NET_TYPE_PRIVATE.toString()));
            if (createbackupreadins) {
                netType = CustinsSupport.NET_TYPE_PRIVATE;
            }

            // 获取实例连接端口
            String portStr = CustinsSupport
                    .getConnPort(mysqlParamSupport.getParameterValue(params, "port"), custins.getDbType());
            String connPort = portStr;
            if (!CustinsSupport.isRedis(custins.getDbType()) || portStr.compareTo(CustinsSupport.DEFAULT_CONN_PORT_REDIS) != 0) {
                connPort = CheckUtils.parseInt(portStr, 1000, 65534, ErrorCode.INVALID_PORT).toString();
            }

            // 获取实例连接地址
            String connPrex = CheckUtils
                    .checkValidForConnAddrCust(mysqlParamSupport.getParameterValue(params, "connectionstring"));
            String connAddrCust = mysqlParaHelper.getConnAddrCust(connPrex, mysqlParaHelper.getRegionIdByClusterName(custins.getClusterName()), custins.getDbType());

            // 创建连接地址对象
            String vpcInstanceId = null;
            if (CustinsSupport.isVpcNetType(netType)) {
                vpcInstanceId = mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);
                if (vpcInstanceId == null) {
                    vpcInstanceId = readins.getInsName();
                }
            }
            CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                    connAddrCust,
                    connPort,
                    netType,
                    CustinsValidator.getRealNumber(mysqlParamSupport.getParameterValue(params, ParamConstants.TUNNEL_ID), -1),
                    mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_ID),
                    mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID),
                    mysqlParamSupport.getParameterValue(params, ParamConstants.IP_ADDRESS),
                    vpcInstanceId);

            // 非Proxy链路类型实例不支持创建VPC临时实例
            if (CustinsSupport.isVpcNetType(netType) && !custins.isProxy() && !mysqlParamSupport.supportVpcInLvs(custins)) {
                ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_CONNTYPE);
            }


            InstanceLevelDO insLevel = null;
            if (mysqlParamSupport.hasParameter(params, "dbinstanceclass")) {
                insLevel = instanceService
                        .getInstanceLevelByClassCode(mysqlParamSupport.getParameterValue(params, "dbinstanceclass"),
                                custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
            } else {
                insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            }
            if (insLevel == null) {
                ResponseSupport.createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
            } else {
                readins.setLevelId(insLevel.getId());
                readins.setDiskSize(custins.getDiskSize());//默认磁盘空间与主实例保持一致
            }
            String fromBackupIns = "0";
            if (mysqlParamSupport.hasParameter(params, ParamConstants.FROM_BACKUP_INS)) {
                fromBackupIns = mysqlParamSupport.getParameterValue(params, ParamConstants.FROM_BACKUP_INS);
            } else {
                if (!insLevel.isMysqlEnterprise() && custins.isNormal() && custins.isMysql()) {
                    try {
                        fromBackupIns = ResourceSupport.getInstance().getStringRealValue(
                                ResourceKey.MYSQL_SPEED_UP_INSTALL_READ_INS_FLAG);
                    } catch (RdsException ignored) {
                    }
                }
            }


            boolean isXdb = false;
            if (mysqlParamSupport.isMysqlXDBByLevel(srcInsLevel)) {
                //xdb企业版都不能支持加速
                fromBackupIns = "0";
                //写死一个逻辑, xdb老版本不支持创建只读, 后期移除掉
                String minorVersion = custinsService.getCustInstanceCurrentMinorVersionByCustinsId(custins.getId());
                if (StringUtils.equalsIgnoreCase("xcluster_20190709", minorVersion)) {
                    logger.warn("xdb current version is xcluster_20190709, cannot support read custins");
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
                isXdb = true;
            }

            //检查只读实例传递的规格与主实例是否合法
            if(!mysqlParamSupport.checkReadinsClassLegalWithPrimaryCustins(insLevel, srcInsLevel)){
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }


            String storage = mysqlParamSupport.getParameterValue(params, "storage");
            if (Validator.isNotNull(storage)) {
                Integer maxDiskSize = ResourceSupport.getInstance()
                        .getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
                Long diskSize =
                        CheckUtils.parseInt(storage, 5, maxDiskSize, ErrorCode.INVALID_STORAGE)
                                * 1024L;
                readins.setDiskSize(diskSize);
            }

            String comment = "";
            if (mysqlParamSupport.hasParameter(params, "dbinstancedescription")) {
                String desc = SupportUtils.decode(mysqlParamSupport.getParameterValue(params, "dbinstancedescription"));
                comment = CheckUtils
                        .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
            }

            // 获取集群&主机ID参数
            String clusterName = mysqlParamSupport.getParameterValue(params, "clustername");
            String hostId = null;
            if (StringUtils.isNotBlank(clusterName)) {  // 仅在输入集群名时HostId参数才有效
                hostId = mysqlParamSupport.getParameterValue(params, "HostId");
            }

            // 设置实例可维护时间
            Date maintainStartTime = mysqlParamSupport
                    .getAndCheckTimeByParam(params, ParamConstants.MAINTAIN_STARTTIME, DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
                            ErrorCode.INVALID_STARTTIME,
                            CustinsSupport.DEFAULT_MAINTAIN_START_TIME);
            Date maintainEndTime = mysqlParamSupport
                    .getAndCheckTimeByParam(params, ParamConstants.MAINTAIN_ENDTIME, DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
                            ErrorCode.INVALID_ENDTIME, CustinsSupport.DEFAULT_MAINTAIN_END_TIME);
            readins.setMaintainStarttime(
                    Time.valueOf(DateSupport.timeWithSecond2str(maintainStartTime)));
            readins.setMaintainEndtime(
                    Time.valueOf(DateSupport.timeWithSecond2str(maintainEndTime)));

            readins.setId(null);
            readins.setClusterName("");
            if (createbackupreadins) {
                readins.setInsType(CUSTINS_INSTYPE_READ_BACKUP);
            } else {
                readins.setInsType(CUSTINS_INSTYPE_READ);
            }
            readins.setStatus(CUSTINS_STATUS_CREATING);
            readins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
            readins.setPrimaryCustinsId(custins.getId());
            List<CustinsServiceDO> custinsServiceDOList = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
            if (custins.isMysql() && custinsServiceDOList.size() == 1) {
                readins.setConnType(CONN_TYPE_LVS);//mysql 为 maxscale单租户
            } else {
                readins.setConnType(custins.getConnType());//必须和主实例一致
            }


            // xisu add
            List<String> physicalBakReadClusterList = resourceService.getResourceRealValueList(physicalBakReadClusterKey);
            boolean needPhysicalConnBakRead = false;   //是否要创建physical网络的备用只读
            if (createbackupreadins && custins.getConnType().equals(CONN_TYPE_LVS) && physicalBakReadClusterList.size() > 0 && physicalBakReadClusterList.contains(custins.getClusterName()) ) {
                needPhysicalConnBakRead = true;
            }
            if (needPhysicalConnBakRead) {
                readins.setConnType(CONN_TYPE_PHYSICAL);
            }

            if (CustinsSupport.isDns(readins.getConnType())) {
                //ODBS集群的DNS链路类型需要拦截
                if (!connAddrService.isAllowCreateDnsLinkForODBS(region)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_CONNTYPE, "ODBS cluster does not support dns connType");
                }
            }

            readins.setComment(comment);

            // 获取主实例的磁盘类型（SSD/SATA）, 使只读实例的磁盘类型和主实例保持一致
//            ClusterParamDO clusterParamDO = clusterService
//                    .getClusterParam(custins.getDbType(), custins.getDbVersion(),
//                            custins.getClusterName(), ClusterParamSupport.CLUSTER_PARAM_HOST_TYPE);
//            String hostType = clusterParamDO.getValue();

            String hostType = custinsService.getCustinsHostType(custins.getId());

            //调用资源接口
            custinsService.createReadCustInstance(custins, readins);


            boolean isPolarxHatp = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP) != null;
            if(isPolarxHatp){
                custinsParamService.setCustinsParam(readins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP,  CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES);
            }

            // 是否为使用dbs服务化的polarx存储节点
            boolean isPolarxUsingDBSService = Boolean.parseBoolean(
                    getParameterValue(params, MinorVersionServiceHelper.POLARX_USING_DBS_SERVICE_EXT));
            if (isPolarxUsingDBSService) {
                custinsParamService.setCustinsParam(readins.getId(), POLARX_USING_DBS_SERVICE_PENGINE,
                        POLARX_USING_DBS_SERVICE_PENGINE_YES);
            }

            custinsParamService.setCustinsParam(readins.getId(), CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, mysqlParamSupport.getParameterValue(params, CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, ""));
            // read ins sync_mode should set to 0
            custinsParamService.setCustinsParam(readins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_ASYNC);
            //此处，只读实例，需要设置为自动小版本升级，这样就会选择最新的灰度版本
            custinsParamService.setCustinsParam(readins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto");

            Map<String, Object> translistParamMap = new HashMap<String, Object>(2);
            String ecsSecurityGroupId = mysqlParamSupport.getAndCheckEcsSecurityGroupId(params, false);
            if (ecsSecurityGroupId != null && !ecsSecurityGroupId.isEmpty()) {
                translistParamMap.put("region_id", mysqlParamSupport.getParameterValue(params, ParamConstants.REGION_ID));
                translistParamMap.put("sg_id", ecsSecurityGroupId);

                custinsParamService.setCustinsParam(readins.getId(), CustinsParamSupport.HAS_ECS_SECURITY_GROUP_REL, "1");
            }
            Integer[] ids = null;
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            if (!avzInfo.isValidForNewInstance()) {
                avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            }

            custinsParamService.updateAVZInfo(readins.getId(), avzInfo);
            // 创建container
            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, custins.getDbType());
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            resourceContainer.setRequestId(requestId);
            resourceContainer.setUserId(custins.getUserId());
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setAccessId(mysqlParamSupport.getParameterValue(params, "accessId"));
            resourceContainer.setOrderId(mysqlParamSupport.getParameterValue(params, "orderId"));
            if (CustinsSupport.ISOLATE_HOST_FIXED.equals(insLevel.getIsolateHost())) {
                resourceContainer.setV6CpuMatch(true);
            }
            /**
             * allocate resource
             */
            CustinsResModel custinsResModel = new CustinsResModel(readins.getId());

            // 只读实例的连接类型保持一致
            custinsResModel.setConnType(readins.getConnType());
            // host resource
            HostinsResModel hostinsResModel = new HostinsResModel(readins.getLevelId());

            hostinsResModel.setInsCount(1);
            hostinsResModel.setHostType(Integer.valueOf(hostType));

            try {
//                DataSourceHolder.setPerfSrvDataSource();
                InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
                hostinsResModel.setDiskSizeUsed(new BigDecimal(instancePerf.getDiskCurr()).longValue());
                hostinsResModel.setDiskSizeSold(custins.getDiskSize());
            } catch (Exception e) {
                hostinsResModel.setDiskSizeUsed(custins.getDiskSize());
                logger.error("Get instance perf failed for custins: " + custins.getId(), e);
            } finally {
//                DataSourceHolder.setDefaultDataSource();
            }

            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, insLevel.getExtraInfo());
            // 指定的host ids
            distributeRule.setSpecifyHostIdSet(mysqlParaHelper.getAndCheckHostIdSet());

            if (isXdb) {
                if (Objects.isNull(distributeRule.getExcludeHostLevelNameSet())) {
                    distributeRule.setExcludeHostLevelNameSet(new HashSet<>());
                }
                distributeRule.getExcludeHostLevelNameSet().addAll(Arrays.asList(XDB_RO_EXCLUDE_HOST_LEVELS));

                if (!CollectionUtils.isEmpty(distributeRule.getSpecifyHostLevelNameSet())) {
                    distributeRule.getSpecifyHostLevelNameSet().removeAll(distributeRule.getExcludeHostLevelNameSet());
                }
                logger.info(String.format("actual distributeRule is: %s", JSON.toJSONString(distributeRule)));
            }

            HashSet<String> siteList = new HashSet<String>();
            boolean clusterIsMultiSite = false;

            List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), true);
            custinsResModel.addExcludeCustinsId(custins.getId());
            if (readCustinsList != null && readCustinsList.size() > 0) {
                if (CustinsSupport.isDns(custins.getConnType())) {
                    List<InstanceDO> instanceDOList = instanceService.getInstanceByCustinsId(readCustinsList.get(0).getId());
                    if (instanceDOList != null && instanceDOList.size() > 0) {
                        PortDistributeRule portDistributeRule = new PortDistributeRule();
                        Set<Integer> ports = new HashSet<>(1);
                        ports.add(instanceDOList.get(0).getPort());
                        portDistributeRule.setSpecifyPortSet(ports);
                        hostinsResModel.setPortDistributeRule(portDistributeRule);
                    } else if (isXdb && instanceDOList.isEmpty()) {
                        // xdb的DNS链路需要支持指定端口号
                        String supportSpecifyPort = resourceService.getResourceRealValueList("XDB_DNS_SUPPORT_SPECIFY_PORT").size() > 0
                                ? resourceService.getResourceRealValueList("XDB_DNS_SUPPORT_SPECIFY_PORT").get(0) : "false";
                        if (Boolean.valueOf(supportSpecifyPort)) {
                            PortDistributeRule portDistributeRule = new PortDistributeRule();
                            Set<Integer> ports = new HashSet<>();
                            ports.add(Integer.valueOf(connPort));
                            portDistributeRule.setSpecifyPortSet(ports);
                            hostinsResModel.setPortDistributeRule(portDistributeRule);
                        }
                    }
                }
                for (CustInstanceDO oldReadIns : readCustinsList) {
                    List<InstanceDO> readInstanceList = instanceService.getInstanceByCustinsId(oldReadIns.getId());
                    for (InstanceDO readInstance: readInstanceList){
                        ClustersDO siteCluster = clusterService.getClusterByClusterName(oldReadIns.getClusterName());
                        if (StringUtils.equalsIgnoreCase(siteCluster.getLocation(), region)){
                            if (oldReadIns.getInsType() == 3){
                                siteList.add(readInstance.getSiteName());
                            }
                            if (siteCluster.getIsMultiSite() == 1 && StringUtils.isNotBlank(siteCluster.getSiteName())){
                                clusterIsMultiSite = true;
                            }
                        }

                    }

                    custinsResModel.addExcludeCustinsId(oldReadIns.getId());
                }

                // 多机房， 只读单机房，并且创建备用只读，则直接打散
                if (clusterIsMultiSite &&  siteList.size() == 1 && createbackupreadins){
                    distributeRule.addExcludeSiteName(new ArrayList<>(siteList).get(0));
                }

            }

            hostinsResModel.setDistributeRule(distributeRule);
            // 如果dns存在只读实例，需要只读实例端口和原来的端口一致
            custinsResModel.setHostinsResModel(hostinsResModel);
            // vip resource

            if (!needPhysicalConnBakRead){
                VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
                vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
                vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
                vipResModel.setVip(custinsConnAddr.getVip());
                vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
                vipResModel.setVpcId(custinsConnAddr.getVpcId());
                vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
                vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
                vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
                custinsResModel.addVipResModel(vipResModel);
            }

            if (isXdb) {
                // XDB v7 机型CPU对齐
                resourceContainer.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
            }

            resourceContainer.addCustinsResModel(custinsResModel);
            //调用资源API
            Response<AllocateResRespModel> response = resManagerService.allocateRes(resourceContainer);
            if (response.getCode().equals(200)) {
                AllocateResRespModel allocateResRespModel = response.getData();
                List<AllocateResRespModel.CustinsResRespModel> respList = allocateResRespModel.getCustinsResRespModelList();
                if (respList.isEmpty()) {
                    // can not happen
                    throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
                }
                // 只读实例和主实例可以跨集群
                readins.setClusterName(respList.get(0).getClusterName());
                readins.setConnType(respList.get(0).getConnType());
                custinsParamService.updateAVZInfo(readins.getId(), avzInfo);
            } else {
                custinsService.deleteCustInstance(readins);
//                super.createResourceRecord(custins.getClusterName(), custins.getInsName(),
//                        JSON.toJSONString(response));
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }

            if (CustinsSupport.ISOLATE_HOST_FIXED.equals(insLevel.getIsolateHost())) {
                custinsParamService.createCustinsParam(new CustinsParamDO(readins.getId(), MySQLParamConstants.V6_CPU_MATCH,
                        MySQLParamConstants.V6_CPU_MATCH_VALUE));
            }


            Map<String, Object> parameterMap = new HashMap<>(2);
            parameterMap.put(ParamConstants.FROM_BACKUP_INS, Integer.valueOf(fromBackupIns));

            ids = taskService.createReadCustInstanceTask(mysqlParamSupport.getAction(params), custins, readins,
                    getOperatorId(params), translistParamMap);
            taskService.updateTaskPenginePolicy(ids[1], getPenginePolicyID(params));

            Map<String, Object> data = new HashMap<String, Object>(15);
            data.put("MigrationID", ids[0]);
            data.put("TaskId", ids[1]);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("ReadDBInstanceID", readins.getId());
            data.put("ReadDBInstanceName", readins.getInsName());
            data.put("Region", region);
            data.put("ConnectionString", custinsConnAddr.getConnAddrCust());
            data.put("Port", custinsConnAddr.getVport());
            data.put("DBInstanceNetType", custinsConnAddr.getNetType());
            data.put("DBInstanceConnType", readins.getConnType());
            data.put(ParamConstants.FROM_BACKUP_INS, fromBackupIns);
            return data;
        } catch (RdsException re) {
            logger.error("createReadInstance error", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("createReadInstance error", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }

    protected boolean getIsReplicatorReadDBInstance() {
        return mysqlParaHelper.getParameterValue("UseReplicator", "0").equals("1");
    }

    /*
    是否创建只读双节点判断
     */
    private boolean isCreateReadOnlyStandard(CustInstanceDO custins, boolean createbackupreadins, boolean isStandard, String uid){
        //双节点只读调整为默认行为
        return custins.isMysql() && !createbackupreadins && isStandard && !StringUtils.equalsIgnoreCase(custins.getConnType(), CONN_TYPE_PROXY);
    }
}
