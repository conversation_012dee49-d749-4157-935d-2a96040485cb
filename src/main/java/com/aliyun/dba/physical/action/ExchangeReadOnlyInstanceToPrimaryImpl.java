package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TransListDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalExchangeReadOnlyInstanceToPrimaryImpl")
public class ExchangeReadOnlyInstanceToPrimaryImpl implements IAction {
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MySQLService mySQLService;
    @Autowired
    protected DbossApi dbossApi;
    @Autowired
    protected MysqlParameterHelper parameterHelper;


    /**
     * 1、主库单点情况下，将只读实例的备节点交换至主实例的备节点
     *
     * 2、主库不可用情况下，将只读实例的主节点，通过任务流变更为主实例的主节点；
     * actionParams:{"slaveInstanceId":"123","slaveInstanceId":"123","readInstanceId":"123","slaveReadInstanceId":"123"}
     * */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        try {
            // 校验主实例
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (!custins.isPrimary()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_MIGRATE_SLAVE);
            }
            CustInstanceDO exchangeReadInstance = mysqlParamSupport.getAndCheckReadCustInstance(actionParams);
            // 校验节点交换模式
            String primaryInsRole = mysqlParamSupport.getRequiredParameterValue(actionParams, "primaryInsRole");
            String readInsRole = mysqlParamSupport.getRequiredParameterValue(actionParams, "readInsRole");
            if (!isMasterRole(primaryInsRole) && !isSlaveRole(primaryInsRole)) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS);
            }
            if (!isMasterRole(readInsRole) && !isSlaveRole(readInsRole)) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS);
            }
            String taskKey;
            // 开始进行交换前针对性校验
            if (isMasterRole(primaryInsRole) && isMasterRole(readInsRole)){
                doMasterToMasterCheck(custins, exchangeReadInstance);
                taskKey = "exchange_read_ins_master_to_primary";
            } else if (isSlaveRole(primaryInsRole) && isSlaveRole(readInsRole)){
                doSlaveToSlaveCheck(custins, exchangeReadInstance);
                taskKey = "exchange_read_ins_slave_to_primary";
            } else {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS);
            }

            // 检查完成开始交换
            TransListDO trans = buildTransListDO(custins, exchangeReadInstance);

            Integer id = mySQLService.createExchangeReadInsToPrimaryTask(getAction(actionParams), custins, exchangeReadInstance,
                    getOperatorId(actionParams), trans, taskKey);
            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsState.STATE_MAINTAINING.getState(), CustinsState.STATE_MAINTAINING.getDesc());

            Map<String, Object> data = new HashMap<String, Object>(10);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", id);
            //返回值做任务类型区分
            data.put("TaskKey", taskKey);
            return data;
        } catch (RdsException re) {
            log.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public boolean isMasterRole(String role) {
        return Replica.RoleEnum.MASTER.getValue().equalsIgnoreCase(role);
    }

    public boolean isSlaveRole(String role) {
        return Replica.RoleEnum.SLAVE.getValue().equalsIgnoreCase(role);
    }

    @NotNull
    public TransListDO buildTransListDO(CustInstanceDO custins, CustInstanceDO exchangeReadInstance) {
        TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
        Map<String, Object> translistParamMap = new HashMap<>(8);

        // 设置主实例信息
        trans.setsCinsid(custins.getId());
        trans.setsCinsReserved(1);
        List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
        trans.setsHinsid1(insIds.get(0));
        trans.setsLevelid(custins.getLevelId());
        if (insIds.size() > 1) {
            trans.setsHinsid2(insIds.get(1));
        }

        // 设置只读实例信息
        List<Integer> exchangeReadInsIds = custinsService.getInstanceIdsByCustinsId(exchangeReadInstance.getId());
        trans.setdCinsid(exchangeReadInstance.getId());
        trans.setdLevelid(exchangeReadInstance.getLevelId());
        trans.setdHinsid1(exchangeReadInsIds.get(0));
        if (exchangeReadInsIds.size() > 1) {
            trans.setdHinsid2(exchangeReadInsIds.get(1));
        } else {
            trans.setdHinsid2(0);
        }

        trans.setParameter(JSON.toJSONString(translistParamMap));
        return trans;
    }

    public void doSlaveToSlaveCheck(CustInstanceDO custins, CustInstanceDO exchangeReadInstance) throws RdsException {
        // 如果是要交换备节点，需要判断备节点不可用，只读实例的备节点是否可用
        Map<String, Object> primarySlaveStatus = null;
        try {
            primarySlaveStatus = dbossApi.showSlaveStatus(custins.getId(), null, "slave");
            log.info("slave primarySlaveStatus: " + JSON.toJSONString(primarySlaveStatus));
        } catch (Exception e) {
            log.error("slave primary Call dboss api failed, exception: " + e);
        }
        // 如果主节点为空的话说明主节点是不可用的不能交换，预期是交换备节点但是主节点也不可用先报错
        if (MapUtils.isNotEmpty(primarySlaveStatus)) {
            Object primarySlaveIORunning = primarySlaveStatus.get("Slave_IO_Running");
            Object primarySlaveSQLRunning = primarySlaveStatus.get("Slave_SQL_Running");
            // 如果备节点是可用的则不需要交换
            if (String.valueOf(primarySlaveIORunning).equalsIgnoreCase("Yes") && String.valueOf(primarySlaveSQLRunning).equalsIgnoreCase("Yes")) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, "primary instance slave is available");
            }
        }

        Map<String, Object> readSlaveStatus;
        try {
            readSlaveStatus = dbossApi.showSlaveStatus(exchangeReadInstance.getId(), null, "slave");
            log.info("slave readSlaveStatus: " + JSON.toJSONString(readSlaveStatus));
        } catch (Exception e) {
            log.error("slave read Call dboss api failed, exception: " + e);
            throw new RdsException(ErrorCode.DBOSS_CAN_NOT_CONNECT_CUSTINS);
        }
        // 如果主节点为空的话说明主节点是不可用的不能交换，预期是交换备节点但是主节点也不可用先报错
        if (MapUtils.isEmpty(readSlaveStatus)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "read instance slave is not available");
        }
        Object readSlaveIORunning = readSlaveStatus.get("Slave_IO_Running");
        Object readSlaveSQLRunning = readSlaveStatus.get("Slave_SQL_Running");
        // 如果备节点是可用的则不需要交换
        if (!String.valueOf(readSlaveIORunning).equalsIgnoreCase("Yes") && !String.valueOf(readSlaveSQLRunning).equalsIgnoreCase("Yes")) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "read instance slave is not available");
        }
    }

    public void doMasterToMasterCheck(CustInstanceDO custins, CustInstanceDO exchangeReadInstance) throws RdsException {
        // 如果是要交换主节点，需要判断主节点不可用，只读实例的主节点是否可用
        Map<String, Object> primaryMasterStatus = null;
        try {
            primaryMasterStatus = dbossApi.showSlaveStatus(custins.getId(), null, "master");
            log.info("master primaryMasterStatus: " + JSON.toJSONString(primaryMasterStatus));
        } catch (Exception e) {
            log.error("master primary Call dboss api failed, exception: " + e);
        }
//        // 如果主节点不为空的话说明主节点是可用的不需要交换
//        if (MapUtils.isNotEmpty(primaryMasterStatus)) {
//            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "primary instance master is available");
//        }
        Map<String, Object> readMasterStatus;
        try {
            readMasterStatus = dbossApi.showSlaveStatus(exchangeReadInstance.getId(), null, "master");
            log.info("master readMasterStatus: " + JSON.toJSONString(readMasterStatus));
        } catch (Exception e) {
            log.error("master read Call dboss api failed, exception: " + e);
            throw new RdsException(ErrorCode.DBOSS_CAN_NOT_CONNECT_CUSTINS);
        }
        // 如果主节点为空的话说明主节点是不可用的不能交换
        if (MapUtils.isEmpty(readMasterStatus)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "read instance master is not available");
        }
    }
}
