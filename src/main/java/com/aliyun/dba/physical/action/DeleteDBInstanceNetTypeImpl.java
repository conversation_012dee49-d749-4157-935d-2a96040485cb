package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsResourceService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.NET_TYPE_VPC;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDeleteDBInstanceNetTypeImpl")
public class DeleteDBInstanceNetTypeImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DeleteDBInstanceNetTypeImpl.class);

    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected AccountService accountService;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsResourceService custinsResourceService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MySQLService mySQLService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected EcsService ecsService;

    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
//            if (custins.isLock()) {
//                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
//            }
//commented out by ligw 2017-03-29, don't check it since
//1. The task only processes the change logs of the current task, don't do garbage change logs
//2. A user can do nothing for such "internal error", but give a work order.
//3. check it in the bakend task implement.
//            if (custinsService.hasConnAddrChangeLogNotApplied(custins.getId())) {
//                logger.error("Custins:" + custins.getId() + " is ACTIVATE but still have "
//                        + "conn addr change log is not applied! This should not happen.");
//                super.printErrorJson(ErrorCode.INTERNAL_FAILURE);
//                return;
//            }

            // 检查是否修改次数超过上限
            custinsService.checkConnAddrChangeTimesExceed(custins.getId(), mysqlParamSupport.getAction(params), null);
            boolean isConnectionStringToSsl = mysqlParamSupport.isConnectionStringToSsl(params,custins);
            if (isConnectionStringToSsl){
                throw new RdsException(ErrorCode.INVALID_CONNECTIONSTRING, "The link address has been used by SSL, modification and deletion are prohibited");
            }
            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

            String connAddrCust = mysqlParamSupport.getParameterValue(params, "connectionstring");
            String netTypeStr = mysqlParamSupport.getParameterValue(params, "dbinstancenettype", null);
            Integer netType = null;
            if (netTypeStr != null) {
                netType = CustinsSupport.getNetType(netTypeStr);
            }
            // 至少要传一个参数
            if (netType == null && connAddrCust == null) {
                return createErrorResponse(ErrorCode.MISSING_ALL_PARAMETERS);
            }
            // 判断是否是隐藏vpc 如果是隐藏vpc 则可删除
            Boolean isNotUserVpc = false;
            for(CustinsConnAddrDO connAddrDO: custinsConnAddrList){
                if (connAddrCust!=null && connAddrCust.equals(connAddrDO.getConnAddrCust()) && !connAddrDO.isConnAddrUserVisible()){
                    isNotUserVpc = true;
                }
            }

            // vpc与私网共存的情况下
            // 判断如果有vpc和可见的私网共存，则不允许直接删除vpc。因为删除意味着vpc切经典，
            if (NET_TYPE_VPC.equals(netType)&&!isNotUserVpc) {
                // tangtai.cgt fix #13649421, 如果是用户vpc实例，不允许删除vpc链路
                Boolean isUserVpc = ecsService.isClusterUserVPCArch(custins.getDbType(),
                        custins.getDbVersion(), custins.getClusterName());
                if (isUserVpc) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
                }
            }

            //commented by ligw 2016-12-28, 为保持与3.3.8以前版本兼容，我们允许通过Delete VPC链接来做到VPC到经典网络的切换。
            if (custinsConnAddrList.size() <= 1 && !NET_TYPE_VPC.equals(netType)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_AT_LEASE_ONE_NETTYPE_EXIST);
            }


            String oldConnectionString = null;
            String oldPort = null;
            Integer oldNetType = null;
            List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>();
            List<String> newPrivatVipList = new ArrayList<String>();
            CustinsConnAddrDO custinsConnAddrPrivate = null;
            List<CustinsConnAddrDO> delCustinsConnAddrV6 = new ArrayList<CustinsConnAddrDO>();
            CustinsConnAddrDO delCustinsConnAddr = null;
            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
                // 获取私网连接信息，如果有
                if (CustinsSupport.NET_TYPE_PRIVATE.equals(custinsConnAddr.getNetType())) {
                    custinsConnAddrPrivate = custinsConnAddr;
                }
                // 过滤，获取要删除的网络连接信息
                if (netType != null && !netType.equals(custinsConnAddr.getNetType())) {
                    continue;
                }
                // 双vpc链路情况下：
                // 1.释放普通vpc不传连接串信息，需要过滤隐藏vpc 2.释放隐藏vpc链路会传连接串信息，需要过滤用户vpc
                if ((connAddrCust != null && !connAddrCust.equals(custinsConnAddr.getConnAddrCust())) ||
                        (StringUtils.isEmpty(connAddrCust) && !custinsConnAddr.isConnAddrUserVisible())){
                    continue;
                }

                delCustinsConnAddr = custinsConnAddr;
                delCustinsConnAddrV6.add(delCustinsConnAddr);
            }

            if (delCustinsConnAddr == null) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCENETTYPE);
            }

            if (CustinsSupport.isVpcNetType(delCustinsConnAddr.getNetType())) {
                connAddrChangeLogs = mySQLService.createConnAddrChangeLogsForDeleteVpcNetType(custins,
                        delCustinsConnAddr, custinsConnAddrPrivate);
                List<CustinsConnAddrDO> custinsConnAddrRWSplitList = connAddrCustinsService
                        .getCustinsConnAddrByCustinsId(custins.getId(), NET_TYPE_VPC, CustinsSupport.RW_TYPE_RW_SPLIT);
                if (CollectionUtils.isNotEmpty(custinsConnAddrRWSplitList)) {
                    connAddrChangeLogs.addAll(mySQLService.createConnAddrChangeLogsForDeleteVpcNetType(custins, custinsConnAddrRWSplitList.get(0), null));
                }
            } else {
                for (CustinsConnAddrDO custinsConnAddr : delCustinsConnAddrV6) {
                    connAddrChangeLogs.addAll(mySQLService.createConnAddrChangeLogsForDeletePublicOrPrivateNetType(custins, custinsConnAddr));
                }
            }
            oldConnectionString = delCustinsConnAddr.getConnAddrCust();
            oldPort = delCustinsConnAddr.getVport();
            oldNetType = delCustinsConnAddr.getNetType();


            Integer taskId = null;
            String taskKey = TaskSupport.getTaskChangeConnAddrKey(mysqlParamSupport.getAction(params),
                    CustinsSupport.isVpcNetType(netType));
            try {
                taskId = taskService.changeConnAddrTask(
                        mysqlParamSupport.getAction(params), custins, connAddrChangeLogs,
                        CustinsState.STATE_NET_DELETING, taskKey, mysqlParamSupport.getOperatorId(params));
            } catch (Exception ex) {
                logger.error("Custins: " + custins.getId()
                        + " DeleteDBInstanceNetType failed when create task. Details: "
                        + JSON.toJSONString(connAddrChangeLogs));
                for (String newPrivateVip : newPrivatVipList) {
                    custinsService.updateIpResourceStatus(newPrivateVip, 2);
                }
                throw new Exception(ex);
            }
            taskService.updateTaskPenginePolicy(taskId, CustinsParamSupport.getPenginePolicyID(params));

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("OldConnectionString", oldConnectionString);
            data.put("OldPort", oldPort);
            data.put("NetType", oldNetType);
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
        }
    }
}
