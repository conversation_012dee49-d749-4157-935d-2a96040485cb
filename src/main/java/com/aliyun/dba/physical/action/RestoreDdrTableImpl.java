package com.aliyun.dba.physical.action;

import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

/**
 * 使用跨地域备份，单库单表恢复到原实例
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRestoreDdrTableImpl")
public class RestoreDdrTableImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(RestoreDdrTableImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        preProcessForRestoreDdrTable(actionParams);
        try {
            RestoreTableImpl restoreTable = SpringContextUtil.getBeanByClass(RestoreTableImpl.class);
            return restoreTable.doActionRequest(custins, actionParams);
        } catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private static void preProcessForRestoreDdrTable(Map<String, String> actionParams) throws RdsException {
        if (getParameterValue(actionParams,"BackupSetType") == null) {
            actionParams.put("BackupSetType".toLowerCase(), BakSupport.BACKUP_SET_TYPE_DDR);
        }
        // set default value
        String isDisasterRestore = "1";
        actionParams.put("IsDisasterRestore".toLowerCase(), isDisasterRestore);
        String restoreType = getParameterValue(actionParams, ParamConstants.RESTORE_TYPE);
        if (restoreType == null){
            throw new RdsException(ErrorCode.RESTORE_TYPE_NOT_FOUND);
        }
        if (!BakSupport.RESTORE_TYPE_BAKID.equals(restoreType) &&
                !BakSupport.RESTORE_TYPE_USER.equals(restoreType) &&
                !BakSupport.RESTORE_TYPE_TIME.equals(restoreType)){
            throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
        }
    }
}

