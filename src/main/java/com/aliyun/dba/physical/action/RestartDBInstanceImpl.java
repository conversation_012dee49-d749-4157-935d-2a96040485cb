package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.base.support.MySQLParamConstants.LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRestartDBInstanceImpl")
public class RestartDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(RestartDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(RestartDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws
        RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            if (!Objects.equals(custins.getLockMode(), CustinsSupport.CUSTINS_LOCK_NO) &&
                    !((Objects.equals(custins.getLockMode(), CustinsSupport.CUSTINS_LOCK_DISK_FULL) ||
                            Objects.equals(custins.getLockMode(), CustinsSupport.CUSTINS_LOCK_READINS_DISK_FULL))
                            && custins.getLockReason().equals(LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE))) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (custins.isShare()) {
                //不是专享实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                //实例状态错误
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            Map<String, Object> param = null;
            Integer taskId = taskService.restartCustInstanceTask(mysqlParamSupport.getAction(params), custins,
                mysqlParamSupport.getOperatorId(params), param);
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(params));
            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
