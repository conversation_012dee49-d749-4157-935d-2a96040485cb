package com.aliyun.dba.physical.action;


import com.aliyun.apsaradb.activityprovider.model.CertConfig;
import com.aliyun.dba.base.common.consts.SSLConsts;
import com.aliyun.dba.base.service.CAServerApiExt;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.support.api.ICaServerApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeDBInstanceSSLImpl")
public class DescribeDBInstanceSSLImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(DescribeDBInstanceSSLImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceSSLImpl.class);

    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ICaServerApi caServerApi;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    private CAServerApiExt caServerApiExt;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID);
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (custins.isMysql51()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }

            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            CustinsParamDO insSSLConfig = custinsParamService.getCustinsParam(
                    custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL);
            boolean sslEnabled = false;
            if (insSSLConfig != null) {
                if (insSSLConfig.getValue().equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SSL)) {
                    sslEnabled = true;
                }
            }
            CustinsParamDO insSSLForceEncryption = custinsParamService.getCustinsParam(
                    custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_FORCE_ENCRYPTION);
            String forceEncryption = insSSLForceEncryption != null ? insSSLForceEncryption.getValue() : "0";
            String caType = SSLConsts.CA_TYPE_ALIYUN;
            CustinsParamDO caTypeConfig = custinsParamService.getCustinsParam(
                    custins.getId(), SSLConsts.CUSTINS_PARAM_NAME_CA_TYPE);
            if (caTypeConfig != null) {
                caType = caTypeConfig.getValue();
            }
            Map<String, Object> data = new HashMap<String, Object>();

            String commonName = "";
            String notAfter = "";
            if (sslEnabled) {
                if (StringUtils.equalsIgnoreCase(caType, SSLConsts.CA_TYPE_CUSTOM)) {
                    CertConfig certConfig = caServerApiExt.getInstanceCustomServerCert(requestId, custins.getInsName());
                    commonName = certConfig.getCommonName();
                    notAfter = certConfig.getNotafter();
                    data.put(ParamConstants.SERVER_CERT, certConfig.getCert());
                    data.put(ParamConstants.SERVER_KEY, certConfig.getKey());
                } else {
                    Map<String, String> result = caServerApi.queryInstanceCert(custins.getInsName(), custins.getClusterName());
                    commonName = result.get("common_name");
                    notAfter = result.get("notafter");
                }
                data.put(ParamConstants.CA_YTPE, caType);
            }
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.CERT_COMMON_NAME, commonName);
            data.put(ParamConstants.SSL_EXPIRED_TIME, notAfter);
            data.put(ParamConstants.SSL_ENABLED, sslEnabled);
            data.put(ParamConstants.SSL_UPDATE_REASON, "");
            data.put(ParamConstants.FORCE_ENCRYPTION, forceEncryption);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
