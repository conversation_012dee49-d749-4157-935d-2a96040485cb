package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.physical.action.support.StorageCompressionHelper.COMPRESSION_MODE_OFF;
import static com.aliyun.dba.physical.action.support.StorageCompressionHelper.COMPRESSION_MODE_ON;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeDBInstanceStorageCompressionImpl")
public class DescribeDBInstanceStorageCompressionImpl implements IAction {
    private static final Cache<String, String> resourceCache = CacheBuilder.newBuilder()
            .maximumSize(2048)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();
    private static final String COMPRESSION_RATIO_CACHE_KEY = "compression_ration_cache";
    private static final String COMPRESSION_RATIO_RESOURCE_KEY = "compression_ration";
    private static final String COMPRESSION_RATIO_RESOURCE_GRAY_UID_KEY = "compression_ration_gray_uid";
    private static final String COMPRESSION_RATIO_RESOURCE_UID_KEY = "compression_ration_uid";
    private static final String COMPRESSION_RATIO_RESOURCE_INS_NAME_KEY = "compression_ration_ins_name";
    private static final String COMPRESSION_RATIO_RESOURCE_DISK_MIN_KEY = "compression_ration_disk_min";
    private static final String COMPRESSION_RATIO_RESOURCE_DISK_MAX_KEY = "compression_ration_disk_max";
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected StorageCompressionHelper storageCompressionHelper;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String replicaSetName = mysqlParaHelper.getDBInstanceName();
        String uid = paramSupport.getParameterValue(params, ParamConstants.UID);
        try {

            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            boolean compressionModeOn = storageCompressionHelper.isCompressionOn(custins.getId());
            String compressionMode = compressionModeOn ? COMPRESSION_MODE_ON : COMPRESSION_MODE_OFF;
            Integer physicalSizeGB = storageCompressionHelper.getPhysicalDiskSizeGB(replicaSet);

            // 当前已开启压缩，则supportCompression 直接为true，否则检查是否满足开启条件
            boolean isSupportCompression = compressionModeOn;
            if (!compressionModeOn) {
                isSupportCompression = isSupportCompression(uid, replicaSet);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSetName);
            data.put("CompressionMode", compressionMode);
            data.put("SupportCompression", isSupportCompression);
            data.put("DbInstanceStorage", physicalSizeGB);
            data.put("CompressionRatio", storageCompressionHelper.getCustinsCompressionRatio(custins.getId()));
            return data;
        } catch (RdsException ex) {
            log.error("DescribeDBInstanceStorageCompression failed, error: {}", JSON.toJSONString(ex));
            throw ex;
        } catch (ApiException ex) {
            log.error("call meta api failed, error: {}", JSON.toJSONString(ex));
            throw new RdsException(ErrorCode.API_CALLING_FAILED);
        } catch (Exception ex) {
            log.error("interface error, ex: {}", JSON.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public boolean isSupportCompression(String uid, ReplicaSet replicaSet) throws ApiException, ExecutionException {
        boolean supportCompression = isCurrentInsSupportCompression(uid, replicaSet);
        if (!supportCompression) {
            return false;
        }
        // 判断只读实例是否都已开启压缩
        List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(replicaSet.getId().intValue(), true);
        if (CollectionUtils.isEmpty(readCustinsList)) {
            return true;
        }

        return readCustinsList.parallelStream().allMatch(readCustins -> {
            boolean readCompressionOn = storageCompressionHelper.isCompressionOn(readCustins.getId());
            if (!readCompressionOn) {
                log.info("read ins {} not on compression, so primary can not enable strage compression", readCustins.getInsName());
            }
            return readCompressionOn;
        });
    }
    private boolean isCurrentInsSupportCompression(String uid, ReplicaSet replicaSet) throws ApiException, ExecutionException {
        String requestId = RequestSession.getRequestId();
        String dbInstanceName = replicaSet.getName();

        String resourceValue = resourceCache.get(COMPRESSION_RATIO_RESOURCE_KEY, new Callable<String>() {
            @Override
            public String call() throws Exception {
                ResourceDO resource = resourceService.getResourceByResKey(COMPRESSION_RATIO_RESOURCE_KEY);
                if (resource == null) {
                    return "{}";
                }
                return resource.getRealValue();
            }
        });
        log.info("get compression_ratio config from db {}", resourceValue);
        JSONObject compressionRationResource = JSON.parseObject(resourceValue);
        // 判断uid在灰度白名单内
        String compressionRationGrayUid = compressionRationResource.getString(COMPRESSION_RATIO_RESOURCE_GRAY_UID_KEY);
        if (StringUtils.isNotEmpty(compressionRationGrayUid) && !Sets.newHashSet(StringUtils.split(compressionRationGrayUid, ",")).contains(uid)) {
            log.info("uid {} not in compression ration gray uid white list", uid);
            return false;
        }
        // 判断uid在开启压缩白名单内
        String uidWhiteList = compressionRationResource.getString(COMPRESSION_RATIO_RESOURCE_UID_KEY);
        if (StringUtils.isNotBlank(uidWhiteList)) {
            Set<String> uidSet = Sets.newHashSet(StringUtils.split(uidWhiteList, ","));
            if (uidSet.contains(uid)) {
                log.info("uid {} in uid white list, return true", uid);
                return true;
            }
        }

        // 判断实例id在开启压缩白名单内
        String insNameWhiteList = compressionRationResource.getString(COMPRESSION_RATIO_RESOURCE_INS_NAME_KEY);
        if (StringUtils.isNotBlank(insNameWhiteList)) {
            Set<String> insNameSet = Sets.newHashSet(StringUtils.split(insNameWhiteList, ","));
            if (insNameSet.contains(dbInstanceName)) {
                log.info("dbInstanceName {} in ins name white list, return true", dbInstanceName);
                return true;
            }
        }

        // 独享实例支持开启压缩
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), false);
        if (Objects.equals(instanceLevel.getIsolationType(), InstanceLevel.IsolationTypeEnum.COMMON)) {
            log.info("only dedicated instance support compression, current isolationType: {}", instanceLevel.getIsolationType());
            return false;
        }

        int diskSizeT = replicaSet.getDiskSizeMB() / 1024;
        int diskMin = compressionRationResource.containsKey(COMPRESSION_RATIO_RESOURCE_DISK_MIN_KEY) ? compressionRationResource.getInteger(COMPRESSION_RATIO_RESOURCE_DISK_MIN_KEY) : 1000;
        int diskMax = compressionRationResource.containsKey(COMPRESSION_RATIO_RESOURCE_DISK_MAX_KEY) ? compressionRationResource.getInteger(COMPRESSION_RATIO_RESOURCE_DISK_MAX_KEY) : 2400;
        boolean isSupportCompression = diskMin <= diskSizeT && diskSizeT <= diskMax;

        log.info("ins diskSizeT {}, supportCompression is {}", diskSizeT, isSupportCompression);
        return isSupportCompression;
    }
}