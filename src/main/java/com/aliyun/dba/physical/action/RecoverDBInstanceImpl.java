package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.lib.*;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.service.WhitelistTemplateService;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.physical.action.support.RecoveryHelper;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.CustinsRebuildResourceServiceImpl;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckHostIdSet;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.support.property.RdsConstants.*;

/*
   备库恢复参数：BackupSetId
   TargetMinorVersion/MinorVersion
   SiteName
   recoveryType
   InstanceID(可选)
   ChangeMaster(可选)
   示例：{
      "Action": "RecoverDBInstance",
      "DBInstanceName": "rm-2ze84y318h4p5619z",
      "UID": "1377539968280501",
      "USER_ID": "26842",
      "recoveryType": "slave",
      "SiteName": "nm125",
      "accessId": "DuKang",
      "BackupSetId": "2158983267",
      "regionId": "cn-beijing",
      "requestId": "68351dab-437e-4814-bba3-5bdf9245fae2_0156f704d3c8"
      }

     --------------------
     删除实例恢复数据
     参数：
     Engine： Mysql
     OrderId： 订单号
     DBInstanceName： 恢复的实例名，建议与原实例名称不一致
     recoveryType ： 恢复类型 deleted
     DBInstanceClass：选填 指定的规格
     Storage： 选填 存储大小
     SecurityIPList： 必填，白名单
     SourceDBInstanceId： 必填 原实例的ID
     SourceDBInstanceName： 选填， 原实例name
     示例：
     {
          "Engine": "MySQL",
          "OrderId": "237639814140000",
          "USER_ID": "26842",
          "UID": "1377539968280501",
          "Storage": "120",
          "DBInstanceName": "rm-dcrecovery013",
          "AccessId": "DuKang",
          "requestId": "68351dab-437e-4814-bba3-5bdf9245fae2_0156f704d3c8",
          "Action": "RecoverDBInstance",
          "recoveryType": "deleted",
          "DBInstanceClass": "rds.mysql.s2.large",
          "SecurityIPList": "10.0.0.0%2F16",
          "SourceDBInstanceId": 951414,
          "SourceDBInstanceName": "rm-2ze99i14j959g571k"
        }
*/

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRecoverDBInstanceImpl")
@Slf4j
public class RecoverDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(RecoverDBInstanceImpl.class);

    @Resource
    protected InstanceService instanceService;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected ClusterService clusterService;
    @Resource
    protected CustinsRebuildResourceServiceImpl custinsRebuildResourceService;
    @Resource
    protected TaskService taskService;
    @Resource
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Resource
    protected MycnfService mycnfService;
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected CommonProviderService commonProviderService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected CustinsParamService custinsParamService;
    @Resource
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private RecoveryHelper recoveryHelper;
    @Resource
    private CustinsIDao custinsIDao;
    @Resource
    private InstanceIDao instanceIDao;
    @Resource
    protected ConnAddrCustinsService connAddrCustinsService;
    @Resource
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Resource
    protected BakService bakService;
    @Resource
    protected AccountService accountService;
    @Resource
    protected DbsService dbsService;
    @Resource
    protected IpWhiteListService ipWhiteListService;
    @Resource
    protected CustinsResourceService custinsResourceService;
    @Resource
    protected MySQLService mySQLService;
    @Resource
    protected HostService hostService;
    @Resource
    protected AVZSupport avzSupport;
    @Resource
    protected DTZSupport dtzSupport;
    @Resource
    protected ResourceService resourceService;
    @Resource
    protected MinorVersionService minorVersionService;
    @Resource
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Resource
    private MysqlParamGroupHelper mysqlParamGroupHelper;
    @Resource
    private WhitelistTemplateService whitelistTemplateService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            //获取恢复的类型：
            // 默认项： slave 只恢复一个slave节点（临时节点）， 是否交换节点，需要确认
            // ins 恢复实例， 需要恢复两个节点， 需要进行链路交换，交换链路前需要确认
            // clone 克隆一个新的实例， 与原实例保持独立（实际更多的应该走标准的克隆流程）
            String recoveryType = getParameterValue(params, "recoveryType", "slave");
            if (StringUtils.equalsIgnoreCase(recoveryType, "slave")) {
                custins = mysqlParamSupport.getAndCheckCustInstance(params);
                InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
                recoveryHelper.checkSupportInstance(custins, instanceLevel);
                InstanceDO recoveryInstanceNode = getAndCheckRecoveryInstanceNode(custins, params, recoveryType);

                return recoverySlaveNode(custins, params, instanceLevel, recoveryInstanceNode);
            } else if (StringUtils.equalsIgnoreCase(recoveryType, "ins")) {
                return recoveryIns(custins, params);
            } else if (StringUtils.equalsIgnoreCase(recoveryType, "deleted")) {
                return recoveryDeleteIns(custins, params);
            } else if (StringUtils.equalsIgnoreCase(recoveryType, "clone")) {
                return cloneForRecovery(custins, params);
            } else {
                logger.error("custins {} not support recovery mode {}", custins.getInsName(), recoveryType);
                throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
            }
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public InstanceDO getAndCheckRecoveryInstanceNode(CustInstanceDO custins, Map<String, String> params,
                                                       String recoveryType) throws RdsException {

        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
        // 限制节点数量， 本地盘目前只有高可用， 并且只支持两个节点的，单节点只读/备用只读暂时不支持
        if (instanceList.size() != 2) {
            logger.error("custins {}, node size is {} != 2", instanceList.size());
            throw new RdsException(ErrorCode.INVALID_NODE_COUNT);
        }

        //在恢复模式是恢复主/备节点的时候进行检查节点信息， 其他可不检查
        if (!ImmutableList.of("master", "slave").contains(recoveryType)) {
            return null;
        }

        //获取instance node信息并作校验； 如果没有传入具体的instance id， 则选取
        String insId = getParameterValue(params, ParamConstants.INSTANCE_ID);
        if (StringUtils.isBlank(insId)) {
            for (InstanceDO instance : instanceList) {
                if (StringUtils.equalsIgnoreCase(recoveryType, "slave") && instance.getRole() == 1) {
                    return instance;
                }
            }

            logger.error("custins {} recovery {} ,but instance not given, please check", custins.getInsName(), recoveryType);
            throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
        } else {
            //检查恢复的节点信息，进行校验，避免搞错
            Integer srcInstanceId = CustinsValidator.getRealNumber(getParameterValue(params, ParamConstants.INSTANCE_ID));
            if (srcInstanceId < 0) {
                logger.error("Invalid instance id format: " + getParameterValue(params, ParamConstants.INSTANCE_ID));
                throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
            }

            for (InstanceDO instance : instanceList) {
                if (instance.getId().equals(srcInstanceId)) {
                    //todo检查对应的角色
                    if (StringUtils.equalsIgnoreCase(recoveryType, "master") && instance.getRole() == 0) {
                        return instance;
                    } else if (StringUtils.equalsIgnoreCase(recoveryType, "slave") && instance.getRole() == 1) {
                        return instance;
                    } else {
                        logger.error("custins {}, recovery {}, but node {} role {} not match",
                                custins.getInsName(), recoveryType, srcInstanceId, instance.getRole());
                        throw new RdsException(MysqlErrorCode.INVALID_REBUILD_SLAVE_INSTANCE.toArray());
                    }
                }
            }

            logger.error("custins {} recovery node {} not exists, please check", custins.getInsName(), srcInstanceId);
            throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
        }
    }

    private void checkMirrorIns(CustInstanceDO custins) throws RdsException {
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
        custInstanceQuery.setPrimaryCustinsId(custins.getId());
        //正在删除的实例忽略掉
        custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
        Long mirrorCount = custinsService.countCustIns(custInstanceQuery);

        if (mirrorCount > 0) {
            throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
        }
    }

    private String getAndCheckBackupSet(CustInstanceDO custins,
                                        Map<String, String> actionParams) throws RdsException {
        String backupSetId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.BACKUP_SET_ID);
        if (StringUtils.isNotEmpty(backupSetId)) {
            if (CustinsSupport.DB_VERSION_MYSQL_55.equalsIgnoreCase(custins.getDbVersion())) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            recoveryHelper.checkBakSetValid(custins, Long.parseLong(backupSetId));
            return backupSetId;
        }
        return null;
    }

    private String getAndCheckMinorVersion(CustInstanceDO custins,
                                           Map<String, String> actionParams) throws RdsException {
        return recoveryHelper.getTargetMinorVersion(actionParams, custins);

    }

    private boolean getChangeMaster(CustInstanceDO custins,
                                    Map<String, String> actionParams) throws RdsException {
        //是否需要搭建复制关系， 默认需要搭建
        String changeMaster = mysqlParamSupport.getParameterValue(actionParams, "ChangeMaster", "true");
        return StringUtils.equalsIgnoreCase(changeMaster, "true") || StringUtils.equalsIgnoreCase(changeMaster, "1");
    }

    private CustInstanceDO createMirrorNodeForRecoveryNode(CustInstanceDO custins) throws RdsException {
        // 创建临时实例
        Long timestamp = System.currentTimeMillis();
        CustInstanceDO tempCustins = custins.clone();
        tempCustins.setId(null);
        tempCustins.setClusterName("");
        //设置专门的标识，标识在进行实例恢复
        tempCustins.setInsName("recovery" + timestamp + "_" + custins.getInsName());
        tempCustins.setStatus(CUSTINS_STATUS_CREATING);
        tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
        tempCustins.setInsType(CUSTINS_INSTYPE_MIRROR);
        tempCustins.setLevelId(custins.getLevelId());
        tempCustins.setDbVersion(custins.getDbVersion());
        tempCustins.setDiskSize(custins.getDiskSize());
        // 必须和源实例一致
        tempCustins.setConnType(custins.getConnType());
        tempCustins.setPrimaryCustinsId(custins.getId());

        custinsIDao.createCustInstance(tempCustins);
        try {
            // 迁移时同步实例参数
            custinsParamService.syncCustinsParams(custins.getId(), tempCustins.getId(), custins.getDbType());
            //此处，临时实例，设置为自动小版本升级，这样就会选择最新的灰度版本
            custinsParamService.createCustinsParam(new CustinsParamDO(tempCustins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto"));
        } catch (Exception e) {
            // 这里失败了做一次记录的删除
            custinsService.deleteCustInstance(tempCustins);
            throw e;
        }

        return tempCustins;
    }

    public Map<String, Object> recoverySlaveNode(CustInstanceDO custins,
                                                 Map<String, String> actionParams,
                                                 InstanceLevelDO instanceLevel,
                                                 InstanceDO recoveryInstanceNode) throws RdsException {
        // not magic cluster
        Integer resourceMode = clusterService.getClusterResourceMode(custins.getClusterName());
        if (!CLUSTER_RESOURCE_MODE_SINGLE.equals(resourceMode)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }

        String backupSetId = getAndCheckBackupSet(custins, actionParams);
        String minorVersion = getAndCheckMinorVersion(custins, actionParams);
        //获取指定主机
        Set<Integer> hostIdSet = getAndCheckHostIdSet(actionParams);
        String resourceStrategy = mysqlParamSupport.getResourceStrategy(actionParams);
        //获取指定的机房
        String siteName = getParameterValue(actionParams, ParamConstants.SITE_NAME);
        //是否搭建复制关系
        boolean isChangeMaster = getChangeMaster(custins, actionParams);

        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
        Integer srcInstanceId = recoveryInstanceNode.getId();

        //检查是否已经有临时实例， 排除状态是deleteing中的
        checkMirrorIns(custins);
        // 创建临时实例
        CustInstanceDO tempCustins = createMirrorNodeForRecoveryNode(custins);

        boolean isAllocateResource = false;
        try {
            ResourceContainer resourceContainer = custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(custins,
                    tempCustins, instanceList,
                    srcInstanceId, resourceStrategy,
                    siteName, hostIdSet);

            // doc: https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89jyZdNnC4pmON4OW3kdP0wQ
            boolean isEmergentResAlloc = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(actionParams, MySQLParamConstants.EMERGENT_RES_ALLOC, "false"));
            resourceContainer.setEmergencyLevel(isEmergentResAlloc ? 1 : 0);
            resourceContainer.setUserId(custins.getUserId());
            resourceContainer.setRequestId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID));
            resourceContainer.setAccessId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID));
            resourceContainer.setOrderId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID));
            //替换成给定的
            resourceContainer.setSourceHost(recoveryInstanceNode.getHostId());

            Response response = resManagerService.allocateRes(resourceContainer);
            isAllocateResource = true;

            if (!response.getCode().equals(200)) {
                custinsService.deleteCustInstance(tempCustins);
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }

            TransListDO translist = createTransListForRebuildSlave(custins, tempCustins, srcInstanceId, instanceList, false);

            Integer taskId;
            Map<String, Object> taskExtraParams = new HashMap<>();
            taskExtraParams.put("recovery_mode", true);
            taskExtraParams.put("is_change_master", isChangeMaster);
            if (minorVersion != null) {
                taskExtraParams.put("minor_version", minorVersion);
                taskExtraParams.put("release_date", minorVersionServiceHelper.parseReleaseDateFromMinorVersion(minorVersion));
            }

            if (StringUtils.isNotEmpty(backupSetId)) {
                taskExtraParams.put("backup_set_id", Long.parseLong(backupSetId));
            }

            taskId = recoveryHelper.recoverySlaveInstanceTask(
                    getAction(actionParams), custins, tempCustins, translist, getOperatorId(actionParams), taskExtraParams);

            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(actionParams));

            Map<String, Object> data = new HashMap<>(6);
            data.put("MigrationID", translist.getId());
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (Exception e) {
            if (!isAllocateResource) {
                custinsService.deleteCustInstance(tempCustins); // 没有申请过资源，出现异常删除临时实例
            }
            throw e;
        }
    }

    public TransListDO createTransListForRebuildSlave(CustInstanceDO custins, CustInstanceDO tempCustins, Integer srcInstanceId, List<InstanceDO> srcInstanceList, boolean isForce) {
        TransListDO translist = new TransListDO(custins, tempCustins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        List<InstanceDO> tmpInstanceList = instanceIDao.getInstanceByCustinsId(tempCustins.getId());

        translist.setdHinsid1(tmpInstanceList.get(0).getId());
        translist.setdHinsid2(0);

        // 设置源主机实例ID
        translist.setsHinsid1(srcInstanceList.get(0).getId());
        if (srcInstanceList.size() > 1) {
            translist.setsHinsid2(srcInstanceList.get(1).getId());
        }
        Map<String, Object> param = new HashMap<String, Object>();
        List<Map<String, Object>> hostInsInfoList = new ArrayList<Map<String, Object>>();
        Map<String, Object> hostInsInfo = new HashMap<String, Object>();
        hostInsInfo.put("s_hid", srcInstanceId);
        hostInsInfo.put("d_hid", tmpInstanceList.get(0).getId());
        hostInsInfoList.add(hostInsInfo);
        param.put("TransferHostId", hostInsInfoList);
        // 这个参数有点奇怪要重新命名
        if (isForce && custins.isMysql()) {
            param.put("use_backup_on_master", true);
        }
        if (custins.isMysql()) {
            param.put("rebuild_slave", true);
        }
        translist.setParameter(JSON.toJSONString(param));
        return translist;
    }

    /*
    实例异常删除时实例恢复
     */
    public Map<String, Object> recoveryDeleteIns(CustInstanceDO custins, Map<String, String> params) throws Exception {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);

        //删除实例恢复， 先获取删除之前实例的信息
        if (mysqlParamSupport.getSourceDBInstanceID(params) == null) {
            logger.error("RecoveryDbInstance for recovery deleted ins, not found sourceDbInstanceId");
            return createErrorResponse(ErrorCode.INVALID_INSTANCE_ID);
        }
        CustInstanceDO srcCustIns = mysqlParamSupport.getAndCheckCustInstanceById(params, "sourcedbinstanceid");
        if (srcCustIns.getIsDeleted() == 0) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        String dbType = srcCustIns.getDbType();
        String dbVersion = srcCustIns.getDbVersion();

        //设置实例的规格信息
        custins.setLevelId(srcCustIns.getLevelId());
        //如果设置了classCode, 则使用设置的classCode
        String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
        if (StringUtils.isNotBlank(classCode)) {
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion, null, null);
            String hostType = String.valueOf(insLevel.getHostType());
            if (!hostType.equals(CUSTINS_PARAM_VALUE_HOST_TYPE_DEFAULT)) {
                logger.error("RecoveryDBInstance check classCode error, classCode not support physical local_ssd");
                return createErrorResponse(ErrorCode.UNSUPPORTED_CLASS_CODE);
            }
            custins.setLevelId(insLevel.getId());
        }
        //获取level info
        InstanceLevelDO custinsLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());

        //获取恢复用的备份集
        Long backupSetId = recoveryDeleteInsBackupSetId(srcCustIns, params);
        //获取内核版本号
        String minorVersion = recoveryDeleteInsMinorVersion(srcCustIns, params,
                backupSetId, dbType, dbVersion,
                classCode, custinsLevel);

        //继承原实例的一些属性
        custins.setCharacterType(srcCustIns.getCharacterType());
        custins.setParentId(srcCustIns.getId());
        custins.setAccountMode(srcCustIns.getAccountMode());
        custins.setStatus(CUSTINS_STATUS_CREATING);
        custins.setStatusDesc(CustinsState.STATE_CREATING.getComment());

        //设置为独享实例
        custins.setType(DB_INSTANCE_TYPE_EXCLUSIVE);
        String resourceStrategy = mysqlParamSupport.getResourceStrategy(params);

        //参数相关
        //这里设置一个默认的参数模版
        String paramGroupId = recoveryDeleteInsCheckParamGroupId(custins, params, dbType, dbVersion);
        // defaultTimeZone
        //String defaultTimeZone = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_PARAM_TIME_ZONE, "");
        Map<String, String> customMysqlParams = mysqlParamSupport.getAndCheckMysqlCustomParams(params);

        // 设置实例公共属性
        mysqlParamSupport.updateCustinsCommonProperties(custins, params);
        Integer bizType = mysqlParamSupport.getAndCheckBizType(params);
        //指定创建实例使用的磁盘类型（SSD 或 SATA或ECS_CLOUD_SSD， 默认使用SSD）
        String hostType = CUSTINS_PARAM_VALUE_HOST_TYPE_DEFAULT;

        //磁盘大小设置, 默认为实例原来的磁盘大小
        String requestDiskSize = mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE);
        if (StringUtils.isBlank(requestDiskSize)){
            custins.setDiskSize(srcCustIns.getDiskSize());
        } else {
            // 这里暂时支持最大 5G-12T
            Long diskSize = CheckUtils.parseLong(requestDiskSize, 5L, 12000L, ErrorCode.INVALID_STORAGE);
            custins.setDiskSize(diskSize * 1024);
        }

        Long usedDiskSize = null;

        // 设置SQLWall配置
        custins = recoveryDeleteInsSqlwall(custins, params);

        //新增默认备份参数
        params = recoveryDeleteInsAddDefaultBakPolicy(custins, params);
        //设置region id
        custins.setRegionId(mysqlParamSupport.getRegionIdByClusterName(srcCustIns.getClusterName()));
        //将region-zone信息补齐
        params = recoveryDeleteInsAddZoneInfo(srcCustIns, custins, params);

        //可用区
        AVZInfo avzInfo = avzSupport.getAVZInfo(params);
        String region = avzInfo.getRegion();

        String connType = srcCustIns.getConnType();
        // 本地盘目前只有两个节点的， 其他的不支持
        Integer nodeCount = 2;

        //创建实例的管控参数
        List<CustinsParamDO> custinsParams = custinsParamService.createCustinsParamsForNewCustins(custins,
                getParameterValue(params, "SystemDBCharset", CUSTINS_PARAM_VALUE_SYS_DBS_CHARSET_CHINESE_PRC_CI_AS),
                getParameterValue(params, ParamConstants.DB_INSTANCE_SYNC_MODE),
                getParameterValue(params, ParamConstants.ECS_SECURITY_GROUP_ID),
                getParameterValue(params, ParamConstants.RESOURCE_GROUP_ID, "").trim(),
                paramGroupId, customMysqlParams);
        custins.setCustinsParams(custinsParams);
        custins = custinsService.createCustInstance(custins);

        List<Integer> instanceIds;
        try {
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String accessId = getParameterValue(params, ParamConstants.ACCESSID);
            String orderId = getParameterValue(params, ParamConstants.ORDERID);
            instanceIds = createMysql(getAction(params), requestId, custins,
                    custinsLevel, avzInfo, srcCustIns.getClusterName(),
                    connType, nodeCount, bizType,
                    Integer.valueOf(hostType),
                    usedDiskSize,
                    null,
                    resourceStrategy, accessId, orderId);
        } catch (RdsException e) {
            return createErrorResponse(e.getErrorCode());
        }

        //更新实例的管控参数
        avzSupport.updateAVZInfoByInstanceIds(avzInfo, instanceIds);
        custinsParamService.updateAVZInfo(custins.getId(), avzInfo);
        if (ISOLATE_HOST_FIXED.equals(custinsLevel.getIsolateHost())) {
            custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(),
                    MySQLParamConstants.V6_CPU_MATCH,
                    MySQLParamConstants.V6_CPU_MATCH_VALUE));
        }
        // 版本升级配置， 统一都是自动升级
        String upgradeMinorVersionOption = "Auto";
        custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, upgradeMinorVersionOption));

        //设置白名单
        CustinsIpWhiteListDO custinsIpWhiteList = ipWhiteListService.getAndCheckCustinsIpWhiteList(custins,
                getParameterValue(params, ParamConstants.SECURITY_IP_LIST),
                getParameterValue(params, ParamConstants.WHITELIST_NET_TYPE, CustinsIpWhiteListDO.DEFAULT_NET_TYPE),
                getParameterValue(params, ParamConstants.SECURITY_IP_TYPE, CustinsIpWhiteListDO.DEFAULT_IP_TYPE));

        int userId = mysqlParamSupport.getAndCreateUserId(params);
        CustinsIpWhiteListDO[] templateList;
        int[] templateIdList;
        templateList = mysqlParamSupport.getAndCheckWhitelistTemplateList(params, userId);
        templateIdList = mysqlParamSupport.getAndCheckTemplateIdList(params, templateList);
        whitelistTemplateService.createWhitelistTemplateRecord(templateList, templateIdList, userId, custins);

        //构建任务的paramter参数
        Map<String, Object> taskQueueParam = new HashMap<>();
        taskQueueParam = getTaskQueueParam(params);
        // 记录参数模板ID & 参数
        taskQueueParam.put(CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
        taskQueueParam.put(CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, SysParamGroupHelper.describeSysParamGroupId(paramGroupId));
        taskQueueParam.put(CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS, customMysqlParams);

        String restoreType = BakSupport.RESTORE_TYPE_BAKID;
        taskQueueParam.put("bakhis_id", backupSetId);
        taskQueueParam.put("recover_custins_id", srcCustIns.getId());
        //指定版本创建
        if (StringUtils.isNotBlank(minorVersion)) {
            taskQueueParam.put("minor_version", minorVersion);
        }

        // trans list 信息
        TransListDO trans = recoveryDeleteInsTransList(srcCustIns, custins, restoreType, backupSetId, instanceIds);
        taskQueueParam.put("trans_list_id", trans.getId());
        taskQueueParam.put("recovery_deleted", "1");

        //下发创建任务
        Integer taskId = mySQLService.createCustInstanceTask(getAction(params),
                custins, null, null, taskQueueParam, custinsIpWhiteList,
                getOperatorId(params), null);
        taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(params));

        Map<String, Object> data = new HashMap<>(6);
        data.put("MigrationID", trans.getId());
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", taskId);
        return data;
    }

    public Map<String, String> recoveryDeleteInsAddZoneInfo(CustInstanceDO srcCustIns, CustInstanceDO custins,
                                             Map<String, String> params) throws RdsException {
        String regionId = mysqlParamSupport.getRegionIdByClusterName(srcCustIns.getClusterName());
        custins.setRegionId(regionId);
        String zoneId = CustinsParamSupport.getParameterValue(params, ParamConstants.ZONE_ID);
        if (StringUtils.isBlank(zoneId)){
            zoneId = clusterService.getZoneIdByClusterName(srcCustIns.getClusterName());
            params.put(ParamConstants.ZONE_ID, zoneId);
            String region = clusterService.getRegionByCluster(srcCustIns.getClusterName());
            params.put("region", region);
            params.put(ParamConstants.DISPENSE_MODE, "0");
        }
        return params;
    }

    public Map<String, String> recoveryDeleteInsAddDefaultBakPolicy(CustInstanceDO custins, Map<String, String> params){
        params.put("preferredbackupperiod", "0101010");
        params.put("preferredbackuptime", "16:43Z");
        params.put("backupretentionperiod", "7");
        return params;
    }

    public String recoveryDeleteInsCheckParamGroupId(CustInstanceDO custins, Map<String, String> params,
                                                        String dbType, String dbVersion) throws RdsException {
        //这里设置一个默认的参数模版
        String paramGroupId = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_PARAM_GROUP_ID);
        if (StringUtils.isNotBlank(paramGroupId)) {
            // MGR复制模式，本地盘不支持
            String syncMode = SysParamGroupHelper.getSyncMode(paramGroupId).toString();
            if (mysqlParamGroupHelper.isMgr(syncMode)) {
                logger.error("local ssd not not support MGR param group {}", paramGroupId);
                throw new RdsException(ErrorCode.INVALID_PARAM_GROUP_CODE);
            }
            // 参数模板信息检查
            // FIXME：此处暂时忽略category校验与存储引擎校验
            SysParamGroupHelper.sysParamGroupIdValidation(paramGroupId, dbType, dbVersion, "", "");
            parameterGroupTemplateGenerator.paramGroupMatchValidate(dbType, dbVersion, "", paramGroupId, false);
        }
        return paramGroupId;
    }

    public Long recoveryDeleteInsBackupSetId(CustInstanceDO srcCustIns, Map<String, String> params) throws RdsException {
        //获取恢复用的备份集
        String bakId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        Long backupSetId = 0L;
        //如果设置了bakID, 则使用设置的， 没有则获取一个
        if (bakId != null) {
            backupSetId = CheckUtils.parseLong(bakId, null, null, ErrorCode.BACKUPSET_NOT_FOUND);
        } else {
            Map<String, Object> condition = ImmutableMap.of(
                    "ignoredRetention", true,
                    "custinsIds", com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.ImmutableList.of(srcCustIns.getId()),
                    "location", BakSupport.BAKUPSET_LOCATION_OSS,
                    "status", "OK");
            List<Map<String, Object>> bakHistoryMapByCondition = bakService.getBakHistoryMapByCondition(condition);
            if (bakHistoryMapByCondition.isEmpty()) {
                logger.error("RecoveryDBInstance custins backup list is empty.");
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
            backupSetId = (Long) bakHistoryMapByCondition.get(0).get("BackupSetID");
        }

        return backupSetId;
    }

    public CustInstanceDO recoveryDeleteInsSqlwall(CustInstanceDO custins, Map<String, String> params) {
        if (custins.isTop()) {
            custins.setSqlwallSwitch("true".equalsIgnoreCase(getParameterValue(params, "SqlwallSwitch", "true")) ? 1 : 0);
            custins.setSqlwallTimeoutEvent(Integer.valueOf(getParameterValue(params, "SqlwallTimeoutEvent", "2")));
            custins.setSqlwallInjectEvent(Integer.valueOf(getParameterValue(params, "SqlwallInjectEvent", "2")));
        } else {
            custins.setSqlwallSwitch("true".equalsIgnoreCase(getParameterValue(params, "SqlwallSwitch", "true")) ? 1 : 0);
            custins.setSqlwallTimeoutEvent(Integer.valueOf(getParameterValue(params, "SqlwallTimeoutEvent", "0")));
            custins.setSqlwallInjectEvent(Integer.valueOf(getParameterValue(params, "SqlwallInjectEvent", "0")));
        }
        return custins;
    }

    /*
    获取内核版本号
    先获取请求中指定的内核版本号， 如果没有， 则从备份集中获取， 然后校验内核版本有效性
     */
    public String recoveryDeleteInsMinorVersion(CustInstanceDO srcCustIns, Map<String, String> params,
                                                Long backupSetId, String dbType,
                                                String dbVersion, String classCode,
                                                InstanceLevelDO custinsLevel) throws RdsException {
        String targetMinorVersion = mysqlParamSupport.getParameterValue(params, "TargetMinorVersion");
        // 如果没有指定的内核版本号,则从备份集中获取
        if (StringUtils.isBlank(targetMinorVersion)) {
            targetMinorVersion = recoveryHelper.getBakSetMinorVersion(srcCustIns, backupSetId);
        }

        String minorVersion = null;
        if (StringUtils.isNotBlank(targetMinorVersion)) {
            minorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
                    dbType,
                    dbVersion,
                    classCode,
                    MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL,
                    targetMinorVersion);
        } else {
            // 尝试查找最新的内核版本，需要包含灰度策略
            minorVersion = minorVersionServiceHelper.tryGetLatestMinorVersion(
                    dbType,
                    dbVersion,
                    custinsLevel.getCategory(),
                    KindCodeParser.KIND_CODE_NC,
                    MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_RPM.toLowerCase());
            if (StringUtils.isNotBlank(minorVersion)) {
                logger.info("find the latest minorVersion from metadb [{}]", minorVersion);
            } else {
                logger.warn("latest minorVersion from metadb not found");
            }
        }

        return minorVersion;
    }

    public TransListDO recoveryDeleteInsTransList(CustInstanceDO srcCustIns, CustInstanceDO custins,
                                                  String restoreType, Long backupSetId,
                                                  List<Integer> instanceIds) {
        /**
         * trans_list init
         */
        TransListDO trans;
        trans = new TransListDO(srcCustIns, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);

        List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(srcCustIns.getId());
        trans.setsHinsid1(insIds.get(0));
        if (insIds.size() > 1) {
            trans.setsHinsid2(insIds.get(1));
        }
        trans.setsCinsReserved(1);

        Map<String, Object> translistParamMap = new HashMap<>(8);
        translistParamMap.put("restoreType", restoreType);
        translistParamMap.put("recovery_deleted", "1");
        if (BakSupport.RESTORE_TYPE_BAKID.equals(restoreType)) {
            trans.setBakhisId(backupSetId);
        }

        trans.setdCinsid(custins.getId());
        trans.setdLevelid(custins.getLevelId());
        trans.setdDisksize(custins.getDiskSize());
        trans.setdHinsid1(instanceIds.get(0));
        if (instanceIds.size() > 1) {
            trans.setdHinsid2(instanceIds.get(1));
        }
        trans.setParameter(JSON.toJSONString(translistParamMap));
        instanceService.createTransList(trans);

        return trans;
    }

    public List<Integer> createMysql(String action, String requestId, CustInstanceDO custins, InstanceLevelDO insLevel,
                                     AVZInfo avzInfo, String preferClusterName, String connType,
                                     Integer nodeCount, Integer bizType, Integer hostType,
                                     Long diskSizeUsed, Set<Integer> hostIdSet, String resourceStrategy,
                                     String accessId, String orderId) throws RdsException {
        // 创建container
        ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, custins.getDbType());
        resourceContainer.setRequestId(requestId);
        resourceContainer.setUserId(custins.getUserId());
        //resourceContainer.setClusterName(specifyClusterName);
        resourceContainer.setPreferClusterName(preferClusterName);
        resourceContainer.setBizType(bizType);
        resourceContainer.setAccessId(accessId);
        resourceContainer.setOrderId(orderId);
        /**
         * allocate resource
         */
        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        custinsResModel.setConnType(connType);
        custinsResModel.setSpecifyProxyGroupId(null);

        // host resource
        HostinsResModel hostinsResModel = new HostinsResModel(custins.getLevelId());

        //isSame:true/false
        // extra_ins_res_info:{2:instance_level_id,1:logger_instance_level_id}
        hostinsResModel.setInsCount(nodeCount);
        hostinsResModel.setHostType(hostType);
        hostinsResModel.setDiskSizeUsed(diskSizeUsed);
        hostinsResModel.setDiskSizeSold(custins.getDiskSize());
        if (resourceStrategy != null) {
            hostinsResModel.setStrategy(resourceStrategy);
        }
        // init distribute rule
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
        distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
        distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
        InsLevelExtraInfo.updateDistributeRule(distributeRule, insLevel.getExtraInfo());
        // 指定的host ids
        distributeRule.setSpecifyHostIdSet(hostIdSet);

        hostinsResModel.setDistributeRule(distributeRule);
        custinsResModel.setHostinsResModel(hostinsResModel);
        resourceContainer.addCustinsResModel(custinsResModel);
        resourceContainer.setV6CpuMatch(true);
        CustinsDistributeRule custinsDistributeByUserIdRule = new CustinsDistributeRule();
        custinsDistributeByUserIdRule.setScatterType("SCATTER_TYPE_USER_ID");
        custinsDistributeByUserIdRule.setMaxInsPerHost(1);
        resourceContainer.getCustinsDistributeRuleList().add(custinsDistributeByUserIdRule);

        //调用资源API
        Response<AllocateResRespModel> response = resManagerService.allocateRes(resourceContainer);
        if (!response.getCode().equals(200)) {
            custinsService.deleteCustInstance(custins);
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND, ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }
        AllocateResRespModel.CustinsResRespModel allocateResRespModel = response.getData().getCustinsResRespModelList().get(0);
        custins.setClusterName(allocateResRespModel.getClusterName());
        custins.setConnType(allocateResRespModel.getConnType());
        return allocateResRespModel.getInstanceIdList();
    }

    public Map<String, Object> recoveryIns(CustInstanceDO custins,
                                           Map<String, String> actionParams) throws RdsException {

        return null;
    }

    public Map<String, Object> cloneForRecovery(CustInstanceDO custins,
                                                Map<String, String> actionParams) throws RdsException {

        return null;
    }

}
