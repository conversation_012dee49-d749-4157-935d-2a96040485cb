package com.aliyun.dba.physical.action;


import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalExcuteDBInstanceCommandImpl")
public class ExcuteDBInstanceCommandImpl implements IAction {

    //private static Logger logger =Logger.getLogger(CloneDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ExcuteDBInstanceCommandImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected TaskService taskService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            // 获取传入的command命令并进行base64解码，
            //增加对command check, filter dangerous and invalid command
            String commandTmpl = mysqlParamSupport.getAndCheckCommand(actionParams);
            logger.info("input data:" + commandTmpl);
            List<String> dbNames = getAllSubMatches(commandTmpl);
            if (!checkCommandOrdered(commandTmpl)){
                return createErrorResponse(ErrorCode.FORBIDDEN_COMMAND);
            }
            if (!checkDBsExists(dbNames)){
                return createErrorResponse(ErrorCode.USED_DBNAME_NOT_EXISTS);
            }
            // 与insname等信息存入task_queue parameter中
            //new 一个taskid
            Map<String, Object> taskQueueParam = new HashMap<>();
            taskQueueParam.put("command", commandTmpl);
            taskQueueParam.put("prepareDbs", dbNames);
            Integer taskId = taskService.excuteCommandTask(getAction(actionParams),
                    custins, getOperatorId(actionParams), taskQueueParam);
            Map<String, Object> data = new HashMap<String, Object>(){};
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.TASK_ID, taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private static boolean matches(Pattern regex, CharSequence input) {
        Matcher m = regex.matcher(input);
        return m.matches();
    }

    private boolean checkCommandOrdered(String command){
        for (Pattern s: RdsConstants.FORBIDDEN_COMMANDS){
            boolean isMatch = matches(s, command);
            if (isMatch){
                logger.error("Forbidden command: " + command);
                return false;
            }
        }
        return true;
    }

    private boolean checkDBsExists(List<String> dbNames){
        boolean Flag = false;
        for(String db:dbNames) {
            if (Arrays.asList(RdsConstants.SUPPORT_DBLIST).contains(db)) {
                Flag = true;
            } else {
                logger.error("used db table name doesn't exist: " + db);
                Flag = false;
                break;
            }
        }
        return Flag;
    }

    private List<String> getAllSubMatches(String command){
        List<String> list=new ArrayList<String>();
        List<String> usedDBs=new ArrayList<String>();
        String pattern = "\\{([a-zA-Z&_]+.[a-zA-Z&_]+)\\}";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(command);
        while (m.find ())
        {
            System.out.println (m.group(1));
            list.add(m.group(1));
        }
        for(String dbs:list){
            usedDBs.add(dbs.split("\\.")[0]);
        }
        return usedDBs;
    }
}

