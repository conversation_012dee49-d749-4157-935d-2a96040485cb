package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCheckCanUpgradeImpl")
public class CheckCanUpgradeImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CheckCanUpgradeImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Resource
    private InstanceService instanceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
            RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);

            String region = mysqlParamSupport.getParameterValue(actionParams, "Region");

            String targetLevel = mysqlParamSupport.getParameterValue(actionParams, "TargetDBInstanceClass");

            InstanceLevelDO targetInsLevel = instanceService.getInstanceLevelByClassCode(
                    targetLevel,
                    custins.getDbType(),
                    custins.getDbVersion(),
                    custins.getTypeChar(),
                    null);
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if (targetInsLevel != null
                    && (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(custins.getDbVersion()) || CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(custins.getDbVersion()))
                    && InstanceSupport.CATEGORY_STANDARD.equals(oldLevel.getCategory())
                    && InstanceSupport.CATEGORY_ENTERPRISE.equals(targetInsLevel.getCategory())
                    && CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(targetInsLevel.getDbType())
                    && (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(targetInsLevel.getDbVersion()) || CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(targetInsLevel.getDbVersion()))){

                if (!mysqlEngineCheckService.checkCanUpgradeToXDB(custins)){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBENGINE);
                }
            }
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("DBInstanceName", custins.getInsName());
            data.put("Region", region);
            data.put("TargetDBInstanceClass", targetLevel);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
