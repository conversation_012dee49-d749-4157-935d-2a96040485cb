package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalSwitchHostinsPerfMetaImpl")
public class SwitchHostinsPerfMetaImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.SwitchHostinsPerfMetaImpl.class);

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private BlueGreenDeploymentService blueGreenDeploymentService;

    @Autowired
    protected CustinsService custinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            logger.info("physicalSwitchHostinsPerfMetaImpl start. params : {}", JSON.toJSONString(params));
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String greenInstanceName = params.get("GreenDBInstanceName".toLowerCase());
            CustInstanceDO greenCustins = custinsService.getCustInstanceByInsName(null, greenInstanceName, 0);

            Integer blueInstanceId = custins.getId();
            Integer greenInstanceId = greenCustins.getId();
            logger.info("blueInstanceId : {}, greenInstanceId : {}", blueInstanceId, greenInstanceId);
            Map<String, Object> result = blueGreenDeploymentService.switchHostinsPerfMeta(blueInstanceId, greenInstanceId);
            logger.info("physicalSwitchHostinsPerfMetaImpl result : {}", JSON.toJSONString(result));
            return result;
        } catch (RdsException ex) {
            logger.error("physicalSwitchHostinsPerfMetaImpl failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("physicalSwitchHostinsPerfMetaImpl Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
