package com.aliyun.dba.physical.action.service;

import com.alicloud.apsaradb.resmanager.HostLevelDistributeRule;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.UpgradeResContainer;
import com.alicloud.apsaradb.resmanager.response.*;
import com.aliyun.dba.base.service.CrmService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Service
public class ResManagerService {

    private static final LogAgent logger = LogFactory.getLogAgent(ResManagerService.class);
    @Resource
    private IResApi resApi;
    @Resource
    private ResourceService resourceService;
    @Resource
    private CrmService crmService;
    @Resource
    private UserService userService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private PhysicalResourceGuaranteeModelService physicalResourceGuaranteeModelService;

    private static final String MYSQL_VIP_HOST_LEVEL = "MYSQL_VIP_HOST_LEVEL";
    private static final String MYSQL_VIP_GC_LEVEL = "MYSQL_VIP_GC_LEVEL";
    private static final String MYSQL_VIP_UID = "MYSQL_VIP_UID";

    public Response<AllocateResRespModel> allocateRes(ResourceContainer resourceContainer) {
        try {
            //适配资源保障策略
            physicalResourceGuaranteeModelService.addResourceGuaranteeModelPolicy(resourceContainer);

            // 以下为老逻辑, 若无资源保障策略, res-manager侧会使用老逻辑的策略
            // 公有云VIP客户提供机型优先级
            if (!custinsService.isInAPCEnvironment() && isVip(resourceContainer.getUserId())) {
                logger.info("vip allocateRes.");
                HostLevelDistributeRule hostLevelDistributeRule = resourceContainer.getHostLevelDistributeRule();
                if (hostLevelDistributeRule == null) {
                    hostLevelDistributeRule = new HostLevelDistributeRule();
                }

                ResourceDO resourceDO = resourceService.getResourceByResKey(MYSQL_VIP_HOST_LEVEL);
                if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                    List<String> preferHostLevelNameSet = Arrays.asList(resourceDO.getRealValue().split(","));
                    hostLevelDistributeRule.setPreferHostLevelNameSet(preferHostLevelNameSet);
                    logger.info("user_id = {}. Get MysqlPreferHostLevelNameSet {}, and set PreferHostLevelNameSet {}.", resourceContainer.getUserId(), resourceDO.getRealValue(), preferHostLevelNameSet);
                    resourceContainer.setHostLevelDistributeRule(hostLevelDistributeRule);
                }
            }
        } catch (Exception e) {
            logger.error("set hostLevelDistributeRule error.", e);
        }

        Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
        return response;
    }


    public Response<EvaluateUpgradeResRespModel> evaluateUpgradeRes(UpgradeResContainer upgradeResContainer) {
        try {
            physicalResourceGuaranteeModelService.addResourceGuaranteeModelPolicy(upgradeResContainer);
        } catch (Exception e) {
            logger.error("Add ResourceGuaranteeModelPolicy in upgradeResContainer error.", e);
        }
        return resApi.evaluateUpgradeRes(upgradeResContainer);
    }



    public Response<UpgradeResRespModel> upgradeRes(UpgradeResContainer upgradeResContainer) {
        try {
            //适配资源保障策略
            physicalResourceGuaranteeModelService.addResourceGuaranteeModelPolicy(upgradeResContainer);
        } catch (Exception e) {
            logger.error("Add ResourceGuaranteeModelPolicy in upgradeResContainer error.", e);
        }

        return resApi.upgradeRes(upgradeResContainer);
    }


    public Response<EvaluateResRespModel> evaluateRes(ResourceContainer resourceContainer) {
        try {
            physicalResourceGuaranteeModelService.addResourceGuaranteeModelPolicy(resourceContainer);
            logger.info("evaluateRes: resourceContainer = {}", resourceContainer);
        } catch (Exception e) {
            logger.error("Add ResourceGuaranteeModelPolicy for evaluateRes error.", e);
        }
        return resApi.evaluateRes(resourceContainer);
    }

    public Response<EvaluateResRespModel> evaluateResQuota(ResourceContainer resourceContainer) {
        try {
            physicalResourceGuaranteeModelService.addResourceGuaranteeModelPolicy(resourceContainer);
            logger.info("evaluateResQuota: resourceContainer = {}", resourceContainer);
        } catch (Exception e) {
            logger.error("Add ResourceGuaranteeModelPolicy for evaluateResQuota error.", e);
        }
        return resApi.evaluateResQuota(resourceContainer);
    }

    private boolean isVip(Integer userId) {
        try {
            UserDO userDO = userService.getUserDOByUserId(userId);
            String uid = StringUtils.isNotEmpty(userDO.getUid()) ? userDO.getUid() : userDO.getLoginId().split("_")[1];
            // 判断Vip客户GC级别
            ResourceDO resourceDO = resourceService.getResourceByResKey(MYSQL_VIP_GC_LEVEL);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                String gcLevel = crmService.getGcLevel(uid);
                logger.info("userId {} gc level {}.", userId, gcLevel);
                if (StringUtils.compare(gcLevel, resourceDO.getRealValue().toUpperCase()) >= 0) {
                    return true;
                }
            }

            // 判断uid是否单独配置
            resourceDO = resourceService.getResourceByResKey(MYSQL_VIP_UID);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                List<String> vips = Arrays.asList(resourceDO.getRealValue().split(","));
                logger.info("userId {} uid {}.", userId, uid);
                return vips.contains(uid);
            }
            return false;
        } catch (Exception e) {
            logger.error("isVip error.", e);
            return false;
        }
    }

}
