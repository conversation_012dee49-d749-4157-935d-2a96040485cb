package com.aliyun.dba.physical.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.poddefault.action.service.EncdbService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeDBInstanceCLSImpl")
public class DescribeDBInstanceCLSImpl implements IAction {

    @Autowired
    EncdbService encdbService;
    @Autowired
    private CustinsParamService custinsParamService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            Map<String, Object> result = encdbService.getEncdbParams(custins);
            result.put("encryptionKey", "");
            CustinsParamDO curClsKeyMode =
                    custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.CLS_KEY_MODE);
            result.put("encryptionKeyMode", curClsKeyMode != null ? curClsKeyMode.getValue() : PodDefaultConstants.CLS_MODE_NONE);
            return result;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
