package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.EvaluateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_DOCKER;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_DOCKER_ON_ECS;
import static com.aliyun.dba.support.property.ParamConstants.ENGINE_VERSION;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalTransferResourceRequestImpl")
public class TransferResourceRequestImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(EvaluateRegionResourceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(TransferResourceRequestImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {
            String region = mysqlParamSupport.getAndCheckRegion(actionParams);
            String dbType = mysqlParamSupport.getAndCheckDBType(actionParams, null);
            // Docker 实例集群的db_type类型为docker,引入临时变量做调整
            String dbTypeForCluster = dbType;
            String classCode = mysqlParamSupport.getAndCheckClassCode(actionParams);
            String dbVersion = mysqlParamSupport.getAndCheckDBVersion(actionParams, dbType, false);
            Integer bizType = mysqlParamSupport.getAndCheckBizType(actionParams);

            mysqlParamSupport.getAndSetContainerTypeAndHostTypeIfEmpty(actionParams, dbType, dbVersion, classCode);

            String containerType = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CONTAINER_TYPE,
                CustinsSupport.CONTAINER_TYPE_HOST);
            String hostType = mysqlParamSupport.getAndCheckHostType(actionParams);

            String clusterName = getParameterValue(actionParams, ParamConstants.CLUSTER_NAME);
            String insType = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_USED_TYPE);

            custins = new CustInstanceDO();
            custins.setDbType(dbType);
            custins.setDbVersion(dbVersion);

            custins = mysqlParamSupport.setInstanceLevel(custins, classCode, bizType,
                mysqlParamSupport.getParameterValue(actionParams, ParamConstants.STORAGE));

            if ((CustinsSupport.isContainerTypeDocker(containerType)
                && CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD.equals(hostType))) {
                custins.setKindCode(KIND_CODE_DOCKER_ON_ECS);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }

            if (StringUtils.equals(CustinsSupport.DB_TYPE_POLARDB_MYSQL, dbType)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE);
            }
            if (CustinsSupport.isContainerTypeDocker(containerType)
                && !CustinsSupport.KIND_CODE_DOCKER_ON_ECS.equals(custins.getKindCode())
                && !CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE.equals(custins.getKindCode())) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }
            custins.setDbTypeForCluster(dbTypeForCluster);

            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            Integer nodeCount;
            if ("3".equals(insType) || "4".equals(insType)) {
                nodeCount = 1;
            } else if (insLevel.isMysqlEnterprise() || mysqlParamSupport.isMysqlXDBByLevel(insLevel)) {
                nodeCount = 3;
            } else {
                nodeCount = 2;
            }

            //mysql 只读高可用节点， 使用node = 2
            if ("3".equals(insType) && StringUtils.equals(CustinsSupport.DB_TYPE_MYSQL, dbType) && insLevel.isStandardLevel()){
                nodeCount = 2;
            }

            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(actionParams);
            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(actionParams, insLevel.getDbType());
            resourceContainer.setUserId(custins.getUserId());
            resourceContainer.setRequestId(getParameterValue(actionParams,ParamConstants.REQUEST_ID));
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setEvaluateNum(evaluateNum);
            // 所有物理机的评估 需要增加v6标签 去进行评估
            resourceContainer.setV6CpuMatch(true);

            /**
             * allocate resource
             */
            CustinsResModel custinsResModel = new CustinsResModel(null);
            HostinsResModel hostinsResModel = new HostinsResModel(insLevel.getId());
            hostinsResModel.setInsCount(nodeCount);
            hostinsResModel.setHostType(Integer.valueOf(hostType));

            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setCabinetDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, insLevel.getExtraInfo());
            // 指定的host ids
            distributeRule.setSpecifyHostIdSet(CustinsParamSupport.getAndCheckHostIdSet(actionParams));
            hostinsResModel.setDistributeRule(distributeRule);
            custinsResModel.setHostinsResModel(hostinsResModel);
            resourceContainer.addCustinsResModel(custinsResModel);

            if (mysqlParamSupport.isMysqlXDBByLevel(insLevel)) {
                // XDB v7 机型CPU对齐
                resourceContainer.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
            }

            //调用资源API
            Map<String, Object> data = new HashMap<String, Object>();

            Map<String, String> requestsData = new HashMap<>();
            requestsData.put("type", "ResourceContainer");
            requestsData.put("value", JSON.toJSONString(resourceContainer));
            data.put("Requests", Collections.singletonList(requestsData));
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
