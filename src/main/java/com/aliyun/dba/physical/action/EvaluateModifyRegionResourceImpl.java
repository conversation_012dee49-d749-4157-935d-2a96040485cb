package com.aliyun.dba.physical.action;

import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.EvaluateUpgradeResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;


import java.util.*;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;

import javax.annotation.Resource;

import static com.aliyun.dba.base.parameter.MysqlParameterHelper.XDB_RO_EXCLUDE_HOST_LEVELS;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckHostIdSet;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.support.property.ErrorCode.UNSUPPORTED_BY_BLUE_GREEN_DEPLOYMENT;
import static com.aliyun.dba.support.property.ParamConstants.ACCESSID;
import static com.aliyun.dba.support.property.ParamConstants.ENGINE_VERSION;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalEvaluateModifyRegionResourceImpl")
public class EvaluateModifyRegionResourceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(EvaluateModifyRegionResourceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateModifyRegionResourceImpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected AVZSupport avzSupport;
    @Resource
    protected ResManagerService resManagerService;
    @Resource
    protected StorageCompressionHelper storageCompressionHelper;
    @Autowired
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            String requestId = getParameterValue(actionParams, ParamConstants.REQUEST_ID);
            String specifyTransType = CustinsParamSupport.getAndCheckTransType(actionParams);
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);

            BlueGreenDeploymentRel asBlue = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, Long.valueOf(custins.getId()), null);
            BlueGreenDeploymentRel asGreen = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, null, Long.valueOf(custins.getId()));
            if (asBlue != null || asGreen != null) {
                logger.error("There is a blue-green deployment, and modify spec is not allowed");
                throw new RdsException(UNSUPPORTED_BY_BLUE_GREEN_DEPLOYMENT);
            }

            String storage = getParameterValue(actionParams, ParamConstants.STORAGE);

            // 实例过期不允许变配； 当 ALLOW_EXPIRED_INS_MODIFY = true 时允许过期实例下发变配请求
            boolean expiredLock = custins.getLockMode() == CustinsSupport.CUSTINS_LOCK_YES.intValue() && StringUtils.equalsIgnoreCase(custins.getLockReason(), "instance_expired");
            String RES_KEY = "ALLOW_EXPIRED_INS_MODIFY";
            ResourceDO resourceDO = resourceService.getResourceByResKey(RES_KEY);
            if (expiredLock && (Objects.isNull(resourceDO) || !Boolean.parseBoolean(resourceDO.getRealValue()))) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            boolean currentCompressionOn = storageCompressionHelper.isCompressionOn(custins.getId());
            Long diskSize;
            if (currentCompressionOn) {
                diskSize = custinsService.getAndCheckDiskSizeWithCompression(custins, storage, getAndCheckBizType(actionParams), storageCompressionHelper.getCustinsCompressionRatio(custins.getId()));
            }
            else{
                diskSize = custinsService.getAndCheckDiskSize(custins, storage, getAndCheckBizType(actionParams));
            }

            if (Objects.equals(specifyTransType, CustinsSupport.CUSTINS_TRANS_TYPE_LOCAL_LIMIT)) {
                return evaluateLocalLimit(custins, requestId, diskSize);
            }

            // 开放白名单，支持TRANS_TYPE_LOCAL_REMOTE全本地或全跨机，禁止半跨机
            ModifyDBInstanceClassImpl physicalModifyDBInstanceClassImpl = SpringContextUtil.getBeanByClass(ModifyDBInstanceClassImpl.class);
            specifyTransType = physicalModifyDBInstanceClassImpl.checkAndSetTransTypeLocalRemote(custins.getInsName(), getParameterValue(actionParams, ParamConstants.UID), specifyTransType);

            CustInstanceDO primaryins = null;//当前实例primary_custins_id指向的实例
            CustInstanceDO guardins = null;
            // 如果当前实例为逻辑主实例，则获取其逻辑灾备实例，便于后面判断含有灾备实例情况进行版本升级的合法性。
            if (custins.isLogicPrimary()) {
                guardins = custinsService.getGuardInstanceByPrimaryCustinsId(custins.getId());
            }
            // 如果当前实例为逻辑灾备实例、只读实例或只读实例备节点，获取其primary_custins_id指向的实例
            if (custins.getPrimaryCustinsId() != null && custins.getPrimaryCustinsId() > 0) {
                primaryins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            }

            if (primaryins != null && !primaryins.isActive() && !primaryins.isReadMAorReadTR()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_MASTER_DBINSTANCE_STATUS);// 主实例状态错误
            }
            if (primaryins != null && primaryins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            // 获得region以及旧region
            String region = getParameterValue(actionParams, "Region");
            String oldRegion = clusterService.getRegionByCluster(custins.getClusterName());
            String specifyCluster = getParameterValue(actionParams, ParamConstants.CLUSTER_NAME);
            Set<Integer> specifyHostIdSet = getAndCheckHostIdSet(actionParams);
            String dbVersion = custinsService.getDBVersion(custins.getDbType(), custins.getDbVersion());
            String levelCode = getParameterValue(actionParams, ParamConstants.TARGET_DB_INSTANCE_CLASS);

            boolean isAcrossRegion;

            if (!StringUtils.isBlank(region) && !region.equals(oldRegion)) {
                // 设置跨 Region 标记
                isAcrossRegion = true;
                // 如果实例为灾备实例或存在灾备实例，则需确保跨Region迁移后灾备实例与主实例不在同一Region
                if (custins.isLogicPrimary() && guardins != null) {
                    String guardRegion = clusterService
                            .getRegionByCluster(guardins.getClusterName());
                    if (region.equals(guardRegion)) {
                        return createErrorResponse(ErrorCode.INVALID_REGION);
                    }
                }
                if (custins.isLogicGuard() && primaryins != null) {
                    String primaryRegion = clusterService
                            .getRegionByCluster(primaryins.getClusterName());
                    if (region.equals(primaryRegion)) {
                        return createErrorResponse(ErrorCode.INVALID_REGION);
                    }
                }

                // 如果当前实例是只读实例或只读实例备节点, 且启用了 BLS 复制器, 需要阻止跨 Region 迁移 (FIXME: BLS 支持跨 Region 移除)
                if (custins.isReadOrBackup() && custins.isMysql()) { // 目前只有 MySQL 只读实例启用 BLS 复制器
                    CustinsParamDO param = custinsParamService.getCustinsParam(custins.getId(),
                            CustinsParamSupport.CUSTINS_PARAM_NAME_READ_INS_USING_REPLICATOR);
                    if (param != null
                            && CustinsParamSupport.CUSTINS_PARAM_VALUE_READ_INS_USING_REPLICATOR_YES
                            .equals(param.getValue())) {
                        return createErrorResponse(ErrorCode.CROSS_REGION_TRANS_NOT_ALLOWED);
                    }
                }
            } else {
                region = oldRegion;
                isAcrossRegion = false;
            }

            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            boolean isMysqlEnterpriseCustins = mysqlDBCustinsService.isMysqlEnterprise(custins);
            boolean isVersionTrans = false;//是否跨版本
            if (dbVersion == null) {//默认为当前版本
                dbVersion = custins.getDbVersion();
            } else {
                Integer versionTrans = dbVersion.compareTo(custins.getDbVersion());
                if (versionTrans != 0) {
                    isVersionTrans = true;
                    if (isMysqlEnterpriseCustins) {
                        //企业版实例不支持版本升级
                        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                    }
                }
                if (versionTrans < 0) {//只能升版本
                    return createErrorResponse(ErrorCode.INVALID_ENGINEVERSION);
                }
                if (versionTrans > 0) {
                    if (custins.isShare()) {
                        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                    }
                    if (guardins != null && custins.isLogicPrimary()
                            // 对于拥用灾备实例的情况，若要升级逻辑主实例的版本，必须先升级逻辑灾备实例。
                            && dbVersion.compareTo(guardins.getDbVersion()) > 0) {
                        return createErrorResponse(
                                ErrorCode.UNSUPPORTED_GUARD_DBINSTANCE_VERSION);//
                        // 如果当前实例为逻辑主实例，且要升级的dbVersion大于逻辑灾备实例，则返回错误。
                    }
                }
            }

            InstanceLevelDO newLevel;
            if (Validator.isNull(levelCode)) {
                levelCode = oldLevel.getClassCode();
                if (isVersionTrans) {
                    newLevel = instanceService
                            .getInstanceLevelByClassCode(levelCode, custins.getDbType(), dbVersion,
                                    custins.getTypeChar(), null);
                } else {
                    newLevel = oldLevel;
                }
            } else {
                newLevel = instanceService
                        .getInstanceLevelByClassCode(levelCode, custins.getDbType(), dbVersion,
                                custins.getTypeChar(), null);
            }

            if (newLevel == null) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
            }

            if (newLevel.isClusterLevel()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
            }

//            if (newLevel.getHostType() == 2) {
//                //暂时不支持升级到ecs规格
//                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
//            }

            // 如果是本地盘迁移云盘，使用k8s资源评估接口
            boolean isFromPhysicalStandardToK8sStandard = ValidateIsFromPhysicalStandardToK8sStandard(
                    custins, actionParams, newLevel, oldLevel);
            if (isFromPhysicalStandardToK8sStandard) {
                // 这里执行拦截条件的检查，瑶池调用资源评估得时候就会把不符合条件的实例拦截住
                com.aliyun.dba.physical.action.service.TransferPhysicalToK8sService transferPhysicalToK8sService = SpringContextUtil
                        .getBeanByClass(com.aliyun.dba.physical.action.service.TransferPhysicalToK8sService.class);
                transferPhysicalToK8sService.validatePhysicalToK8s(custins, actionParams);
                //根据lb mock zoneId
                actionParams.put(ParamConstants.ZONE_ID.toLowerCase(), transferPhysicalToK8sService.getMasterZoneIdByLB(custins, getParameterValue(actionParams, ParamConstants.REGION_ID)));
                AVZInfo avzInfo = transferPhysicalToK8sService.generateAvzInfoByLBAndHostInfo(custins, getParameterValue(actionParams, ParamConstants.REGION_ID), requestId);
                actionParams.put(ParamConstants.REGION.toLowerCase(), region);
                actionParams.put(ParamConstants.MULTI_AVZ_EX_PARAM.toLowerCase(), JSON.toJSONString(avzInfo.getMultiAVZExParamDO()));
                actionParams.put(ParamConstants.DISPENSE_MODE.toLowerCase(),"1");
                // 资源评估
                com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl k8sEvaluateRegionResourceImpl = SpringContextUtil
                        .getBeanByClass(com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl.class);
                String targetClassCode = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.TARGET_DB_INSTANCE_CLASS);
                actionParams.put(ParamConstants.DB_INSTANCE_CLASS.toLowerCase(), targetClassCode);
                // fixme 如果没有传zoneID，则设置与源实例master zoneid一致
                if (StringUtils.isEmpty(getParameterValue(actionParams, ParamConstants.ZONE_ID))) {
                    AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
                    String zoneId = oldAvzInfo.getMasterZoneId();
                    actionParams.put(ParamConstants.ZONE_ID.toLowerCase(), zoneId);
                }
                final Map<String, Object> ret = k8sEvaluateRegionResourceImpl.doActionRequest(custins, actionParams);
                return ret;
            }

            boolean mysqlCategorySwitch = newLevel.isMysqlEnterprise() ^ isMysqlEnterpriseCustins;
            if (mysqlCategorySwitch) {
                if (guardins != null) {
                    //有灾备实例的目前不允许系列切换
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
            }


            boolean isLocalTrans = !isVersionTrans && !isAcrossRegion;
            boolean forceLocalTrans = mysqlParamSupport.getParameterValue(actionParams,
                    "ForceLocalTrans", "0").equals("1");

            //获取实例instance 节点信息， 评估资源依据现有实例的节点个数进行评估， 包兼容只读单节点以及m-s双节点
            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            //判断是否为 只读双节点
            boolean isReadInsStandard = custins.isRead() && instanceList.size() > 1;

            //对于只读实例的变配，统一走跨机迁移
            // todo 对于只读m-s对齐主实例， 可以进行本地变配
            if (isLocalTrans && custins.isRead() && !forceLocalTrans) {
                if (!newLevel.getId().equals(oldLevel.getId()) && !isReadInsStandard) {
                    isLocalTrans = false;
                }
            }

            Boolean isMysqlXdb = mysqlParamSupport.isMysqlXDBByLevel(newLevel);
            Boolean isXdbRead = mysqlParamSupport.isXdbReadCustins(custins);

            // 判断迁移类型

            if (mysqlCategorySwitch && !isMysqlXdb) {
                specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
            }

            //5.7,8.0高可用升三节点
            if ((CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(custins.getDbVersion())
                    ||CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(custins.getDbVersion()))
                    && InstanceSupport.CATEGORY_STANDARD.equals(oldLevel.getCategory())
                    && InstanceSupport.CATEGORY_ENTERPRISE.equals(newLevel.getCategory())
                    && CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(newLevel.getDbType())
                    && (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(newLevel.getDbVersion())
                    ||CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(newLevel.getDbVersion()))) {
                specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
            }

            if (!isLocalTrans) {
                if (CustinsSupport.CUSTINS_TRANS_TYPE_LOCAL.equals(specifyTransType)) {
                    // 校验是否支持强制本地升降级
                    return createErrorResponse(ErrorCode.UNSUPPORTED_TRANS_TYPE);
                } else {
                    specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
                }
            }

            AVZInfo avzInfo = avzSupport.getAVZInfo(actionParams);
            if(!avzInfo.isValidForModify()){
                avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            }

            //UpgradeResContainer container = new UpgradeResContainer(region);
            UpgradeResContainer container = avzSupport.getUpgradeResContainer(avzInfo);
            container.setRequestId(requestId);
            container.setClusterName(specifyCluster);
            container.setPreferClusterName(custins.getClusterName());
            // 所有物理机的评估 需要增加v6标签 去进行评估
            container.setV6CpuMatch(true);
            container.setSourceHost(mysqlParamSupport.getInstanceCurrHostId(actionParams));

            // accessId
            container.setAccessId(getParameterValue(actionParams, ACCESSID));

            // init custins res model
            UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
            custinsResModel.setCustinsId(custins.getId());

            List<CustinsServiceDO> maxscaleService = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(),
                    CustinsSupport.DB_TYPE_MAXSCALE);
            if(maxscaleService != null && maxscaleService.size() >0){
                custinsResModel.setNeedProxyGroup(false);
            }

            // init host ins res model
            HostinsResModel hostinsResModel = new HostinsResModel(newLevel.getId());
            hostinsResModel.setTransType(Integer.valueOf(specifyTransType));

            InstanceDO instance = instanceList.get(0);
            hostinsResModel.setHostType(instance.getHostType());
            hostinsResModel.setInsCount(instanceList.size());

            if (mysqlCategorySwitch) {
                //三双节点发生切换
                if (!isMysqlEnterpriseCustins) {
                    hostinsResModel.setInsCount(3);
                } else {
                    hostinsResModel.setInsCount(2);
                }
            }

            if (isMysqlXdb) {
                hostinsResModel.setInsCount(3);
            }

            boolean isFromK8sBasicToPhysicalHA = KindCodeParser.KIND_CODE_NEW_ARCH.equals(custins.getKindCode());
            if (isFromK8sBasicToPhysicalHA) {
                hostinsResModel.setInsCount(2);   // 不改是 1
                hostinsResModel.setHostType(0);   // 不改是 2
                container.setDbType(custins.getDbType());  // 不改是 global
            }

            hostinsResModel.setDiskSizeSold(diskSize);
            // get disk size used

            Long diskUsage = instanceService.getInstanceDiskUsage(custins, 0);
            hostinsResModel.setDiskSizeUsed(diskUsage);
            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setSpecifyHostIdSet(specifyHostIdSet);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, newLevel.getExtraInfo());

            if (isXdbRead) {
                if (Objects.isNull(distributeRule.getExcludeHostLevelNameSet())) {
                    distributeRule.setExcludeHostLevelNameSet(new HashSet<>());
                }
                distributeRule.getExcludeHostLevelNameSet().addAll(Arrays.asList(XDB_RO_EXCLUDE_HOST_LEVELS));

                if (!CollectionUtils.isEmpty(distributeRule.getSpecifyHostLevelNameSet())) {
                    distributeRule.getSpecifyHostLevelNameSet().removeAll(distributeRule.getExcludeHostLevelNameSet());
                }
                logger.info(String.format("actual distributeRule is: %s", JSON.toJSONString(distributeRule)));
            }

            hostinsResModel.setDistributeRule(distributeRule);

            if (custins.isReadOrBackup()) {
                List<Integer> excludeIds = new ArrayList<>();
                excludeIds.add(custins.getPrimaryCustinsId());
                List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(
                        custins.getPrimaryCustinsId(), true);
                List<Integer> readInsTmpId = custinsService.getReadCustInstanceTmpInsIdByPrimaryCustinsId(custins.getPrimaryCustinsId());
                if (readInsTmpId.size() != 0){
                    for (int i=0;i<readInsTmpId.size();i++){
                        excludeIds.add(readInsTmpId.get(i));
                    }
                }
                if (readCustinsList != null && readCustinsList.size() > 0) {
                    for (CustInstanceDO oldReadIns : readCustinsList) {
                        if (!oldReadIns.getId().equals(custins.getId())){
                            excludeIds.add(oldReadIns.getId());
                        }
                    }
                }
                custinsResModel.setExcludeCustinsIdList(excludeIds);
            } else {
                List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(
                        custins.getId(), true);
                if (readCustinsList != null && readCustinsList.size() > 0) {
                    List<Integer> excludeIds = new ArrayList<>();
                    for (CustInstanceDO oldReadIns : readCustinsList) {
                        excludeIds.add(oldReadIns.getId());
                    }
                    custinsResModel.setExcludeCustinsIdList(excludeIds);
                }
            }
            // 如果dns,需要前后端口一致
            if (custins.isDns()) {
                PortDistributeRule portDistributeRule = new PortDistributeRule();
                Set<Integer> ports = new HashSet<>(1);
                ports.add(instance.getPort());
                portDistributeRule.setSpecifyPortSet(ports);
                hostinsResModel.setPortDistributeRule(portDistributeRule);
            }

            if (isMysqlXdb) {
                // XDB v7 机型CPU对齐
                container.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
            }

            //custinsResModel.setHostinsResModel(hostinsResModel);
            //container.addUpgradeCustinsResModel(custinsResModel);

            container.setUid(getParameterValue(actionParams, ParamConstants.UID));
            logger.info("[EvaluateModifyRegionResourceImpl] custins " + custins.getInsName() + " specifyTransType: " + specifyTransType);
            Response<EvaluateUpgradeResRespModel> response = evaluateInstanceUpgradeRes(custins, isReadInsStandard,
                    specifyTransType, container, custinsResModel, hostinsResModel);

            Map<String, Object> data = new HashMap<>(4);
            data.put("DBInstanceName", custins.getInsName());
            if (response.getCode().equals(200)) {
                data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                if (response.getData().getCustinsResRespModelList().get(0).getIsLocalUpgrade() == 0) {
                    data.put(ParamConstants.DB_INSTANCE_TRANS_TYPE, CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE);
                } else {
                    data.put(ParamConstants.DB_INSTANCE_TRANS_TYPE, CustinsSupport.CUSTINS_TRANS_TYPE_LOCAL);
                }
            } else {
                data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                data.put(ParamConstants.ERROR_MESSAGE, ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            data.put(ParamConstants.DB_INSTANCE_CLASS, newLevel.getClassCode());
            data.put(ENGINE_VERSION, newLevel.getDbVersion());
            data.put("Region", region);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /*
    资源评估
    只读实例非跨机类型，先强制reset为本地，本地无资源再进行跨机评估
     */
    public Response<EvaluateUpgradeResRespModel> evaluateInstanceUpgradeRes(CustInstanceDO custins, boolean isReadInsStandard,
                                                                            String specifyTransType,
                                                                            UpgradeResContainer container,
                                                                            UpgradeCustinsResModel custinsResModel,
                                                                            HostinsResModel hostinsResModel) throws RdsException{
        Response<EvaluateUpgradeResRespModel> response = null;
        if (custins.isRead() && isReadInsStandard && !StringUtils.equalsIgnoreCase(specifyTransType, CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE)){
            specifyTransType = CUSTINS_TRANS_TYPE_LOCAL;
            hostinsResModel.setTransType(Integer.valueOf(specifyTransType));
            custinsResModel.setHostinsResModel(hostinsResModel);
            container.addUpgradeCustinsResModel(custinsResModel);
            logger.warn("readonly custins {} modify ins trans type is not CUSTINS_TRANS_TYPE_REMOTE try reset to CUSTINS_TRANS_TYPE_LOCAL", custins.getInsName());
            response = resManagerService.evaluateUpgradeRes(container);
            if (!response.getCode().equals(200)) {
                specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
                hostinsResModel.setTransType(Integer.valueOf(specifyTransType));
                custinsResModel.setHostinsResModel(hostinsResModel);
                List<UpgradeCustinsResModel> upgradeCustinsResModelList = new ArrayList<UpgradeCustinsResModel>();
                upgradeCustinsResModelList.add(custinsResModel);
                container.setUpgradeCustinsResModelList(upgradeCustinsResModelList);
                logger.warn("readonly custins {} modify ins trans type reset to CUSTINS_TRANS_TYPE_LOCAL allocate resource failed, try reset to CUSTINS_TRANS_TYPE_REMOTE", custins.getInsName());
                response = resManagerService.evaluateUpgradeRes(container);
            }

        }else {
            custinsResModel.setHostinsResModel(hostinsResModel);
            container.addUpgradeCustinsResModel(custinsResModel);
            response = resManagerService.evaluateUpgradeRes(container);
        }
        return response;
    }

    private Map<String, Object> evaluateLocalLimit(CustInstanceDO custins, String requestId, Long diskSize) {
        long upgradeDiff = diskSize - custins.getDiskSize();
        if (upgradeDiff > CustinsSupport.LOCAL_UPGRADE_DISK_LIMIT_MAX_SIZE * 1024) {
            return createErrorResponse(ErrorCode.INVALID_STORAGE, "force local extend disk storage size too large");
        }
        if (!mysqlParamSupport.hasLockTaskInLastHours(custins, 12)) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_TRANS_TYPE, "Force local disk extension must execute only if the instance has a lock task within the last 24 hours.");
        }
        Map<String, Object> data = new HashMap<>(4);
        data.put("DBInstanceName", custins.getInsName());

        try {
            long dataSpaceAvail = resApi.fetchHostDataSpaceAvail(requestId, custins.getId());
            long diskLimit = Math.min(dataSpaceAvail, CustinsSupport.LOCAL_UPGRADE_DISK_LIMIT_MAX_SIZE * 1024);
            data.put(ParamConstants.DB_INSTANCE_TRANS_TYPE, CustinsSupport.CUSTINS_TRANS_TYPE_LOCAL);
            data.put(ParamConstants.LOCAL_UPGRADE_DISK_LIMIT, diskLimit / 1024);
            data.put(ParamConstants.DB_INSTANCE_AVAILABLE, diskLimit >= upgradeDiff ? 1 : 0);
        } catch (Exception ex) {
            logger.warn(String.format("%s %s %s", requestId, ex.getMessage(), ex));
        }

        return data;
    }

    public static Boolean ValidateIsFromPhysicalStandardToK8sStandard(
            CustInstanceDO custins, Map<String, String> actionParams,
            InstanceLevelDO newLevel, InstanceLevelDO oldLevel) {
        Boolean support57 = false;
        Boolean support80 = false;
        String requestId = getParameterValue(actionParams, ParamConstants.REQUEST_ID);
        if ("5.7".equals(custins.getDbVersion())
                && InstanceLevel.CategoryEnum.STANDARD.getValue().equals(newLevel.getCategory())
                && InstanceLevel.CategoryEnum.STANDARD.getValue().equals(oldLevel.getCategory())
                && oldLevel.getHostType() == 0
                && newLevel.getHostType() == 2) {
            logger.info(requestId + "ValidateIsFromPhysicalStandardToK8sStandard: support57 is true");
            support57 = true;
        };
        if ("8.0".equals(custins.getDbVersion())
                && InstanceLevel.CategoryEnum.STANDARD.getValue().equals(newLevel.getCategory())
                && InstanceLevel.CategoryEnum.STANDARD.getValue().equals(oldLevel.getCategory())
                && oldLevel.getHostType() == 0
                && newLevel.getHostType() == 2) {
            logger.info(requestId + "ValidateIsFromPhysicalStandardToK8sStandard: support80 is true");
            support80 = true;
        };
        return support57 || support80;
    }

}