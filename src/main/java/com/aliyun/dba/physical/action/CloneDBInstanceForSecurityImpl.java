package com.aliyun.dba.physical.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

/**
 * 逻辑与CloneDBInstance相同，只有一个参数不同，isValidCount = false
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Service("physicalCloneDBInstanceForSecurityImpl")
public class CloneDBInstanceForSecurityImpl implements IAction {

    //private static Logger logger =Logger.getLogger(CloneDBInstanceForSecurityImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CloneDBInstanceForSecurityImpl.class);

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {
            actionParams.put("isValidCount".toLowerCase(), String.valueOf(false));
            CloneDBInstanceImpl cloneDBInstanceImpl = SpringContextUtil.getBeanByClass(CloneDBInstanceImpl.class);
            return cloneDBInstanceImpl.doActionRequest(custins, actionParams);
        } catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
