package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.idao.UpgradeReportDO;
import com.aliyun.dba.base.idao.UpgradeReportIDao;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeUpgradeMajorVersionResultImpl")
public class DescribeUpgradeMajorVersionResultImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeUpgradeMajorVersionResultImpl.class);

    @Autowired
    protected UpgradeReportIDao upgradeReportIDao;

    @Autowired
    protected CustinsService custinsService;

    @Resource
    private MysqlParamSupport mysqlParamSupport;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        Map<String, Object> condition = new HashMap<String, Object>(8);
        String pageSizeStr = mysqlParamSupport.getParameterValue(params,"maxrecordsperpage");
        Integer pageSize = CheckUtils.parseInt(pageSizeStr, 1, 100, ErrorCode.INVALID_MAXRECORDSPERPAGE);
        String pageNumberStr = mysqlParamSupport.getParameterValue(params,"pagenumbers");
        Integer pageNumber = CheckUtils.checkPageNumbers(pageNumberStr);
        String type = mysqlParamSupport.getParameterValue(params,"type");
        String dstVersion = mysqlParamSupport.getParameterValue(params,"dstVersion");
        String taskId = mysqlParamSupport.getParameterValue(params,"taskId");

        condition.put("pageSize", pageSize);
        condition.put("pageFirst", (pageNumber - 1) * pageSize);
        condition.put("srcInsName", custins.getInsName());
        condition.put("type", type);
        condition.put("taskId", taskId);
        condition.put("dstVersion", dstVersion);
        int totalSize = upgradeReportIDao.countUpgradeReportByCondition(condition);
        List<UpgradeReportDO> upgradeReportList = upgradeReportIDao.getUpgradeReportByCondition(condition);
        Map<String, Object> data = new HashMap<String, Object>(4);
        data.put("upgradeReportList", upgradeReportList);
        data.put("MaxRecordsPerPage", pageSize);
        data.put("PageNumbers", pageNumber);
        data.put("TotalSize", totalSize);
        return  data;
    }
}
