package com.aliyun.dba.physical.action.service;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.*;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ;
import static com.aliyun.dba.task.support.TaskSupport.TASK_INSTALL_READ_CUSTINS;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.*;

import javax.annotation.Resource;

import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.alibaba.cobar.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.DistributeMode;
import com.alicloud.apsaradb.resmanager.DistributeRule;
import com.alicloud.apsaradb.resmanager.HostinsResModel;
import com.alicloud.apsaradb.resmanager.PortDistributeRule;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.VipResModel;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.ConnAddrService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsIpWhiteListDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsResourceService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCreateReadDBInstanceStandardImpl")
public class CreateReadDBInstanceStandardImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateReadDBInstanceStandardImpl.class);

    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected AccountService accountService;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsResourceService custinsResourceService;
    @Autowired
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MySQLService mySQLService;
    @Autowired
    protected HostService hostService;
    @Autowired
    private CustinsIDao custinsIDao;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Resource
    private ConnAddrService connAddrService;

    /**
     * 创建只读实例, 主备节点部署模式
     * 老模式是只读单节点 + 备用只读单节点 进行配比, 并且不支持跨机房部署，
     * 该模式不满足容灾要求， 因此采用跟主实例对齐 主 + 备部署
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            //只支持mysql 本地盘形态， 其他一律不支持
            if (!custins.isMysql() || !custins.getKindCode().equals(KIND_CODE_NC)){
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            //只读创建过滤 只读只读实例维护 与只读实例升降级状态
            if (!custins.isActive() && !custins.isReadMAorReadTR()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //超过只读实例个数限制
            Integer maxReadIns = ResourceSupport.getInstance().getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MYSQL_READINS_COUNT);
            Integer existsReadIns = custinsService.countReadCustInstanceByPrimaryCustinsId(custins.getId());
            if (existsReadIns >=maxReadIns) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_NODE_NUMBER);
            }

            CustInstanceDO readins = custins.clone();
            String region = mysqlParamSupport.getAndCheckRegion(params);

            // 只读的版本强制与主实例的大版本保持一致
            String dbVersion = custins.getDbVersion();
            readins.setDbVersion(dbVersion);

            String readDbInstanceName = mysqlParamSupport.getParameterValue(params, "readdbinstancename");
            readins.setInsName(CheckUtils.checkValidForInsName(readDbInstanceName));
            if (custinsService.hasCustInstanceByInsName(readins.getInsName())) {
                ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }

            // 确认是否使用 Proxy Binlog 复制器
            boolean usingReplicator = this.getIsReplicatorReadDBInstance();
            if (usingReplicator) {
                String custinsRegion = avzSupport.getCustInstanceMainLocation(custins.getId());
                if (StringUtil.isEmpty(region)) {
                    region = custinsRegion;
                }
                if (!region.equals(custinsRegion)) {
                    ResponseSupport.createErrorResponse(ErrorCode.INVALID_REGION);
                }
            }
            // 设置启用复制器标记
            readins.setUsingReplicator(usingReplicator);

            //规格
            InstanceLevelDO insLevel = null;
            if (mysqlParamSupport.hasParameter(params, "dbinstanceclass")) {
                String classCode = mysqlParamSupport.getParameterValue(params, "dbinstanceclass");
                insLevel = instanceService.getInstanceLevelByClassCode(classCode, custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
            } else {
                insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            }
            if (insLevel == null) {
                ResponseSupport.createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
            } else {
                readins.setLevelId(insLevel.getId());
                readins.setDiskSize(custins.getDiskSize());
            }

            //InnerRDS limit instance level scope
            if(mysqlParaHelper.isInnerRDS(custins.getId()) && !mysqlParaHelper.isInstanceLevelSupportInnerRDS(insLevel.getExtraInfo())){
                return createErrorResponse(ErrorCode.UNSUPPORTED_READ_INSTANCE_LEVEL);
            }
            //检查只读实例传递的规格与主实例是否合法
            InstanceLevelDO srcInsLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if(!mysqlParamSupport.checkReadinsClassLegalWithPrimaryCustins(insLevel, srcInsLevel)){
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }

            String netTypeString = mysqlParamSupport.getParameterValue(params, "dbinstancenettype", CustinsSupport.NET_TYPE_PRIVATE.toString());
            Integer netType = CustinsSupport.getNetType(netTypeString);

            // 获取实例连接端口, 连接地址
            String portStr = CustinsSupport.getConnPort(mysqlParamSupport.getParameterValue(params, "port"), custins.getDbType());
            String connPort = CheckUtils.parseInt(portStr, 1000, 65534, ErrorCode.INVALID_PORT).toString();
            String connPrex = CheckUtils.checkValidForConnAddrCust(mysqlParamSupport.getParameterValue(params, "connectionstring"));
            String connAddrCust = mysqlParaHelper.getConnAddrCust(connPrex, mysqlParaHelper.getRegionIdByClusterName(custins.getClusterName()), custins.getDbType());

            // 创建连接地址对象
            String vpcInstanceId = null;
            if (CustinsSupport.isVpcNetType(netType)) {
                vpcInstanceId = mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);
                if (vpcInstanceId == null) {
                    vpcInstanceId = readins.getInsName();
                }
            }
            CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                    connAddrCust,
                    connPort,
                    netType,
                    CustinsValidator.getRealNumber(mysqlParamSupport.getParameterValue(params, ParamConstants.TUNNEL_ID), -1),
                    mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_ID),
                    mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID),
                    mysqlParamSupport.getParameterValue(params, ParamConstants.IP_ADDRESS),
                    vpcInstanceId);

            String storage = mysqlParamSupport.getParameterValue(params, "storage");
            if (Validator.isNotNull(storage)) {
                Integer maxDiskSize = ResourceSupport.getInstance().getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
                Long diskSize = CheckUtils.parseInt(storage, 5, maxDiskSize, ErrorCode.INVALID_STORAGE) * 1024L;
                readins.setDiskSize(diskSize);
            }
            String comment = "";
            if (mysqlParamSupport.hasParameter(params, "dbinstancedescription")) {
                String desc = SupportUtils.decode(mysqlParamSupport.getParameterValue(params, "dbinstancedescription"));
                comment = CheckUtils.checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
            }

            // 获取集群&主机ID参数
            String clusterName = mysqlParamSupport.getParameterValue(params, "clustername");

            // 设置实例可维护时间
            Date maintainStartTime = mysqlParamSupport.getAndCheckTimeByParam(params, ParamConstants.MAINTAIN_STARTTIME,
                    DateUTCFormat.MINUTE_ONLY_UTC_FORMAT, ErrorCode.INVALID_STARTTIME, CustinsSupport.DEFAULT_MAINTAIN_START_TIME);
            Date maintainEndTime = mysqlParamSupport.getAndCheckTimeByParam(params, ParamConstants.MAINTAIN_ENDTIME,
                    DateUTCFormat.MINUTE_ONLY_UTC_FORMAT, ErrorCode.INVALID_ENDTIME, CustinsSupport.DEFAULT_MAINTAIN_END_TIME);
            readins.setMaintainStarttime(Time.valueOf(DateSupport.timeWithSecond2str(maintainStartTime)));
            readins.setMaintainEndtime(Time.valueOf(DateSupport.timeWithSecond2str(maintainEndTime)));

            readins.setId(null);
            readins.setClusterName("");
            readins.setComment(comment);
            readins.setInsType(CUSTINS_INSTYPE_READ);

            readins.setStatus(CUSTINS_STATUS_CREATING);
            readins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
            readins.setPrimaryCustinsId(custins.getId());
            List<CustinsServiceDO> custinsServiceDOList = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
            if (custins.isMysql() && custinsServiceDOList.size() == 1) {
                readins.setConnType(CONN_TYPE_LVS);//mysql 为 maxscale单租户
            } else {
                readins.setConnType(custins.getConnType());//必须和主实例一致
            }
            if (CustinsSupport.isDns(readins.getConnType())) {
                //ODBS集群的DNS链路类型需要拦截
                if (!connAddrService.isAllowCreateDnsLinkForODBS(region)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_CONNTYPE, "ODBS cluster does not support dns connType");
                }
            }

            String hostType = custinsService.getCustinsHostType(custins.getId());

            //创建cust instance 记录，并copy 实例管控参数
            custinsService.createReadCustInstance(custins, readins);

            String resGroupId = mysqlParamSupport.getParameterValue(params, CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, "");
            custinsParamService.setCustinsParam(readins.getId(), CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, resGroupId);
            // read ins sync_mode should set to 0
            custinsParamService.setCustinsParam(readins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_ASYNC);
            //此处，只读实例，需要设置为自动小版本升级，这样就会选择最新的灰度版本
            custinsParamService.setCustinsParam(readins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto");

            Map<String, Object> transListParamMap = new HashMap<String, Object>(2);

            Integer[] ids = null;
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            if (!avzInfo.isValidForNewInstance()) {
                avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            }

            // 需要重新构建 实例申请资源的params， 支持跨机房，双节点
            custinsParamService.updateAVZInfo(readins.getId(), avzInfo);
            // 创建container
            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, custins.getDbType());
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            resourceContainer.setRequestId(requestId);
            resourceContainer.setUserId(custins.getUserId());
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setAccessId(mysqlParamSupport.getParameterValue(params, "accessId"));
            resourceContainer.setOrderId(mysqlParamSupport.getParameterValue(params, "orderId"));
            if (CustinsSupport.ISOLATE_HOST_FIXED.equals(insLevel.getIsolateHost())) {
                resourceContainer.setV6CpuMatch(true);
            }

            //如果主实例开启了压缩 需要记录压缩mode和压缩率到只读实例
            String compressionMode = getParameterValue(params, "compressionMode");
            CustinsParamDO primaryCompressionModeParam = custinsParamService.getCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_MODE_KEY);
            if (compressionMode != null && compressionMode.equals(StorageCompressionHelper.COMPRESSION_MODE_ON) && primaryCompressionModeParam != null && StorageCompressionHelper.COMPRESSION_MODE_ON.equals(primaryCompressionModeParam.getValue()) ){
                CustinsParamDO primaryCompressionRatioParam = custinsParamService.getCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_RATIO_KEY);
                custinsParamService.setCustinsParam(readins.getId(), StorageCompressionHelper.COMPRESSION_RATIO_KEY, primaryCompressionRatioParam.getValue());
                custinsParamService.setCustinsParam(readins.getId(), StorageCompressionHelper.COMPRESSION_MODE_KEY, StorageCompressionHelper.COMPRESSION_MODE_ON);

                CustInstanceDO readCustinsCompressionDo  = custinsService.getCustInstanceByInsName(null, readins.getInsName());
                Double compressionRatio = Double.valueOf(primaryCompressionRatioParam.getValue());
                readCustinsCompressionDo.setDiskSize( (long)(readins.getDiskSize() * compressionRatio) );
                custinsService.updateCustInstance(readCustinsCompressionDo);

                List<InstanceDO> instanceList = instanceService.getInstanceByCustinsIds(Collections.singletonList(readins.getId()));
                for (InstanceDO instanceDo : instanceList) {
                    instanceService.updateInstanceDiskSizeByInsId(instanceDo.getId(), (int)(readins.getDiskSize() * compressionRatio));
                }
            }

            /**
             * allocate resource
             */
            CustinsResModel custinsResModel = new CustinsResModel(readins.getId());
            // 只读实例的连接类型保持一致
            custinsResModel.setConnType(readins.getConnType());
            // host resource
            HostinsResModel hostinsResModel = new HostinsResModel(readins.getLevelId());

            hostinsResModel.setInsCount(2);
            hostinsResModel.setHostType(Integer.valueOf(hostType));

            try {
                InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
                hostinsResModel.setDiskSizeUsed(new BigDecimal(instancePerf.getDiskCurr()).longValue());
                hostinsResModel.setDiskSizeSold(custins.getDiskSize());
            } catch (Exception e) {
                hostinsResModel.setDiskSizeUsed(custins.getDiskSize());
                logger.error("Get instance perf failed for custins: " + custins.getId(), e);
            }

            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, insLevel.getExtraInfo());
            // 指定的host ids调度
            distributeRule.setSpecifyHostIdSet(mysqlParaHelper.getAndCheckHostIdSet());
            //与主实例打散
            custinsResModel.addExcludeCustinsId(custins.getId());
            // 与现有的只读进行打散
            List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), true);
            if (readCustinsList != null && readCustinsList.size() > 0) {
                // 只读双节点不进行强制打散
                List<Integer> readInsId = new ArrayList<>();
                for (CustInstanceDO oldReadIns : readCustinsList) {
                    readInsId.add(oldReadIns.getId());
                }
                custinsResModel.setInferiorCustinsIdList(readInsId);
            }

            if (CustinsSupport.isDns(custins.getConnType()) && StringUtils.isNotBlank(connPort)) {
                PortDistributeRule portDistributeRule = new PortDistributeRule();
                Set<Integer> specifyPortSet = new HashSet<>(1);
                specifyPortSet.add(Integer.valueOf(connPort));
                portDistributeRule.setSpecifyPortSet(specifyPortSet);
                hostinsResModel.setPortDistributeRule(portDistributeRule);
            }

            hostinsResModel.setDistributeRule(distributeRule);
            custinsResModel.setHostinsResModel(hostinsResModel);
            // vip resource
            VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
            vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
            vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
            vipResModel.setVip(custinsConnAddr.getVip());
            vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
            vipResModel.setVpcId(custinsConnAddr.getVpcId());
            vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
            vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
            vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
            custinsResModel.addVipResModel(vipResModel);
            resourceContainer.addCustinsResModel(custinsResModel);
            //调用资源API
            Response<AllocateResRespModel> response = resManagerService.allocateRes(resourceContainer);
            if (response.getCode().equals(200)) {
                AllocateResRespModel allocateResRespModel = response.getData();
                List<AllocateResRespModel.CustinsResRespModel> respList = allocateResRespModel.getCustinsResRespModelList();
                if (respList.isEmpty()) {
                    // can not happen
                    throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
                }
                // 只读实例和主实例可以跨集群
                readins.setClusterName(respList.get(0).getClusterName());
                readins.setConnType(respList.get(0).getConnType());
                custinsParamService.updateAVZInfo(readins.getId(), avzInfo);
            } else {
                custinsService.deleteCustInstance(readins);
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
            }

            if (CustinsSupport.ISOLATE_HOST_FIXED.equals(insLevel.getIsolateHost())) {
                custinsParamService.createCustinsParam(new CustinsParamDO(readins.getId(), MySQLParamConstants.V6_CPU_MATCH,
                        MySQLParamConstants.V6_CPU_MATCH_VALUE));
            }




//            Map<String, Object> parameterMap = new HashMap<>(2);
//            parameterMap.put(ParamConstants.FROM_BACKUP_INS, Integer.valueOf(fromBackupIns));

            ids = this.createReadCustInstanceTask(mysqlParamSupport.getAction(params), custins, readins,
                    getOperatorId(params), transListParamMap);
            taskService.updateTaskPenginePolicy(ids[1], getPenginePolicyID(params));
            Map<String, Object> data = new HashMap<String, Object>(15);
            data.put("MigrationID", ids[0]);
            data.put("TaskId", ids[1]);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("ReadDBInstanceID", readins.getId());
            data.put("ReadDBInstanceName", readins.getInsName());
            data.put("Region", region);
            data.put("ConnectionString", custinsConnAddr.getConnAddrCust());
            data.put("Port", custinsConnAddr.getVport());
            data.put("DBInstanceNetType", custinsConnAddr.getNetType());
            data.put("DBInstanceConnType", readins.getConnType());
//            data.put(ParamConstants.FROM_BACKUP_INS, fromBackupIns);
            data.put(ParamConstants.FROM_BACKUP_INS, "0");
            return data;
        } catch (RdsException re) {
            logger.error("createReadInstance error", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("createReadInstance error", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }

    protected boolean getIsReplicatorReadDBInstance() {
        return mysqlParaHelper.getParameterValue("UseReplicator", "0").equals("1");
    }

    public Integer[] createReadCustInstanceTask(String action, CustInstanceDO custins, CustInstanceDO readins, Integer operatorId, Map<String, Object> translistParamMap) throws RdsException {
        TransListDO translist = new TransListDO(custins, readins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_READ);

        List<Integer> insIds = custinsIDao.getInstanceIdsByCustinsId(custins.getId());
        translist.setsHinsid1(insIds.get(0));
        translist.setsHinsid2(insIds.get(1));

        List<Integer> readInsIds = custinsIDao.getInstanceIdsByCustinsId(readins.getId());
        translist.setdHinsid1(readInsIds.get(0));
        translist.setdHinsid2(readInsIds.get(1));
        if (!translistParamMap.isEmpty()) {
            translist.setParameter(JSON.toJSONString(translistParamMap));
        }

        // 更新节role信息
        int counter = 0;
        for (Integer instanceId : readInsIds) {
            instanceService.updateInstanceRoleByInsId(instanceId, counter++ == 0 ? INSTANCE_ROLE_MASTER : INSTANCE_ROLE_SLAVE);
        }

        dbsService.syncAllDbsAndAccounts(custins, readins);
        if (readins.isReadBackup() && readins.isMysql()){
            logger.warn("mysql bakreadins_whitelist_127, custinsId="+readins.getId());
            CustinsIpWhiteListDO custinsIpWhiteList = new CustinsIpWhiteListDO(readins.getId(), "127.0.0.1");
            ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
        }else {
            ipWhiteListService.syncCustinsIpWhiteList(custins.getId(), readins.getId());
        }

        instanceService.createTransList(translist);

        Map<String, Object> taskParams = new HashMap<>();
        taskParams.put("isStandard", "1");
        taskParams.put(TRANS_ID, translist.getId());

        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId,
                custins.getId(), TASK_TYPE_CUSTINS, TASK_INSTALL_READ_CUSTINS,
                JSON.toJSONString(taskParams));

        taskService.createTaskQueue(taskQueue);
        instanceService.updateTransListTaskId(translist.getId(), taskQueue.getId());
        return new Integer[] { translist.getId(), taskQueue.getId() };
    }

}
