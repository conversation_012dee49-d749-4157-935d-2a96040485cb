package com.aliyun.dba.physical.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.aliyun.dba.custins.support.CustinsSupport.CHARACTER_TYPE_PHYSICAL;

@Service("MysqlMajorVersionCheckService")
public class MysqlMajorVersionCheckServiceImpl implements MysqlMajorVersionCheckService{


    private static final LogAgent logger = LogFactory.getLogAgent(MysqlMajorVersionCheckServiceImpl.class);

    @Resource
    private CustinsService custinsService;

    @Resource
    private CustinsParamService custinsParamService;

    /**
     * MySQL5.7带MaxScale的内核必须>=1.13.41才支持大版本升级
     * MySQL8.0最新内核小版本是20230930（8.0.34）及以后,代理内核必须>=1.14.5或者20231123
     * 必须先升级MaxScale的内核版本
     *
     * @param custins
     * @return
     */
    @Override
    public boolean checkCanUpgradeMajorVersionWithMaxScale(CustInstanceDO custins, String targetReleaseDate) {
        if (custins == null) {
            return true; //为空不用校验
        }
        if (StringUtils.equals(custins.getBizType(), ReplicaSet.BizTypeEnum.ALIGROUP.getValue())) {
            return true;  //集团不用校验
        }
        if (!custins.isMysql57()) {
            return true; //非5.7不用校验
        }
        if (custins.isReadOrBackup()) {
            // 如果是只读转为主实例
            custins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            if (custins == null) {
                return true;
            }
        }
        if (CHARACTER_TYPE_PHYSICAL.equals(custins.getCharacterType())) {
            custins = custinsService.getCustInstanceByCustinsId(custins.getParentId());
            if (custins == null) {
                return true;
            }
        }
        List<CustinsServiceDO> custinsServiceDOS = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
        if (CollectionUtils.isEmpty(custinsServiceDOS)) {
            return true;  //不带maxscale不用校验
        }
        Integer maxScaleCustinsId = LangUtil.getInteger(custinsServiceDOS.get(0).getServiceId());
        CustInstanceDO maxscale = custinsService.getCustInstanceByCustinsId(maxScaleCustinsId);
        if (maxscale == null || maxscale.isDeleting()) {
            return true;  //maxscale正在删除不用校验
        }
        CustinsParamDO custinsMinorVersion = custinsParamService.getCustinsParam(maxScaleCustinsId, "minor_version");
        if (custinsMinorVersion != null && StringUtils.isNotBlank(custinsMinorVersion.getValue())) {
            String maxScaleMinorVersion = custinsMinorVersion.getValue();
            // 版本规则为：老架构（maxscale_service_1.13.41）、新架构（Maxscale_MySQL_2.2.12_20230921、Maxscale_MySQL_Serverless_2.2.12_20231026）
            if (StringUtils.equals(maxscale.getDbVersion(), "3.5")) {
                String maxscaleReleaseDate = StringUtils.substringAfterLast(maxScaleMinorVersion, "_");
                if (maxscaleReleaseDate.length() != 8) {
                    return true;  //不符合版本规则，不校验
                }
                if (targetReleaseDate != null && "20230930".compareTo(targetReleaseDate) <= 0 && "20231127".compareTo(maxscaleReleaseDate) > 0) {
                    //https://aone.alibaba-inc.com/v2/project/1190539/req/53314908
                    //MYSQL8.0升级到20230930（8.0.34）及以后版本，需确保maxscale新架构在20231127及其以上版本
                    logger.error("maxscale's version is {}, do not allow upgrade", maxScaleMinorVersion);
                    return false;
                }
            } else {
                String[] vers = StringUtils.split(maxScaleMinorVersion, "_");
                if (vers.length != 3) {
                    return true;   //不符合版本规则，不校验
                }
                String[] vvv = StringUtils.split(vers[2], ".");
                if (vvv.length != 3) {
                    return true; //不符合版本规则，不校验
                }
                Long checkVersion = LangUtil.getLong(StringUtils.join(vvv));
                if (checkVersion == null) {
                    return true; //不符合版本规则，不校验
                }
                if ("1.13.41".compareTo(vers[2]) > 0) {
                    //小于1.13.41的5.7版本都不允许做大版本升级
                    logger.error("maxscale's version is {}, do not allow upgrade", maxScaleMinorVersion);
                    return false;
                }
                if (targetReleaseDate != null && "20230930".compareTo(targetReleaseDate) <= 0 && "1.14.5".compareTo(vers[2]) > 0) {
                    //https://aone.alibaba-inc.com/v2/project/1190539/req/53314908
                    //MYSQL8.0升级到20230930（8.0.34）及以后版本，需确保maxscale老架构在1.14.5及其以上版本
                    logger.error("maxscale's version is {}, do not allow upgrade", maxScaleMinorVersion);
                    return false;
                }
            }
        }
        return true;
    }
}
