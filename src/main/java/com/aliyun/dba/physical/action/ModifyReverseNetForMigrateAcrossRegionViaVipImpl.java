package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalModifyReverseNetForMigrateAcrossRegionViaVipImpl")
public class ModifyReverseNetForMigrateAcrossRegionViaVipImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyReverseNetForMigrateAcrossRegionViaVipImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Resource
    private InstanceService instanceService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
            RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);

            String bifrostHostinsId = mysqlParamSupport.getParameterValue(actionParams, "BifrostHostinsId");
            String bifrostVipPortMapping = mysqlParamSupport.getParameterValue(actionParams, "BifrostVipPortMapping");
            String masterMySQLVipPortMapping = mysqlParamSupport.getParameterValue(actionParams, "MasterMySQLVipPortMapping");
            String hostinsIdNcPidsMapping = mysqlParamSupport.getParameterValue(actionParams, "HostinsIdNcPidsMapping");
            String isShutdown = mysqlParamSupport.getParameterValue(actionParams, "IsShutdown", "0");
            Map<String, Object> taskParam = new HashMap<>();
            // 以下几个参数用于资源申请下沉
            taskParam.put("bifrost_hostins_id", bifrostHostinsId);
            taskParam.put("bifrost_vip_port_mapping", bifrostVipPortMapping);
            taskParam.put("master_mysql_vip_port_mapping", masterMySQLVipPortMapping);
            taskParam.put("is_shutdown", isShutdown);
            taskParam.put("hostins_id_nc_pid_mapping", hostinsIdNcPidsMapping);

            // 下发任务
            TaskQueueDO taskQueue = new TaskQueueDO(mysqlParameterHelper.getAction(), mysqlParameterHelper.getOperatorId(), custins.getId(),
                    TASK_TYPE_CUSTINS, "modify_reverse_net", JSON.toJSONString(taskParam));
            taskService.createTaskQueue(taskQueue);

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("DBInstanceName", custins.getInsName());
            data.put("taskId", taskQueue.getId());
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
