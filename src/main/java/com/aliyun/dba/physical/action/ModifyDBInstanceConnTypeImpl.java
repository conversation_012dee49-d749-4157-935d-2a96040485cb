package com.aliyun.dba.physical.action;

import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.RdsResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.AllocateVipRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelRelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalModifyDBInstanceConnTypeImpl")
public class ModifyDBInstanceConnTypeImpl implements IAction {

    //private static Logger logger = Logger.getLogger(ModifyDBInstanceConnTypeImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceConnTypeImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsSearchService custinsSearchService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected UserService userService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // 仅主实例, 只读,备用支持此操作
            if (!(custins.isPrimary() && custins.isLogicPrimary())) {
                if (!custins.isReadOrBackup()){
                    // #22571967 , 放行 只读,备用只读
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
            }

            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

            CustinsConnAddrDO custinsConnAddr = custinsConnAddrList.get(0);

            String newConnType = mysqlParamSupport.getAndCheckConnType(actionParams, ParamConstants.NEW_DB_INSTANCE_CONN_TYPE);
            this.checkNewConnTypeValid(custins, custinsConnAddr, newConnType);

            boolean hasMaxscale = !custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale").isEmpty();
            if (hasMaxscale && newConnType.equals(CONN_TYPE_PROXY)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }

            //有opensearch的proxy实例无法切换到其他链路类型
            if (custins.isProxy() && !newConnType.equals(CustinsSupport.CONN_TYPE_PROXY)
                && custinsSearchService.checkCustinsSearch(custins)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (CustinsSupport.CONN_TYPE_LVS.equals(custins.getConnType())
                && CONN_TYPE_PROXY.equals(newConnType) && custins.isMysql80()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }


            if (CustinsSupport.CONN_TYPE_PROXY.equals(custins.getConnType())
                    && CustinsSupport.CONN_TYPE_LVS.equals(newConnType)) {
                List<CustinsConnAddrDO> custinsRWSplitConnAddrList = connAddrCustinsService
                        .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_RW_SPLIT);
                boolean isVPCInstance = ConnAddrSupport.hasVPCConnAddr(custinsConnAddrList);
                //boolean isInternetInstance = ConnAddrSupport.hasInternetConnAddr(custinsConnAddrList);
                if (isVPCInstance) {
                    if (!mysqlParamSupport.supportVpcInLvs(custins)) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
                    }
                }
                if (custinsRWSplitConnAddrList.size() > 0) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
                }
                if(!"true".equals(mysqlParamSupport.getParameterValue(actionParams,"foreproxy2lvs","true"))) {
                    List<String> params = Arrays.asList(CustinsParamSupport.CUSTINS_PARAM_NAME_PROXY_MAX_POOL_CONNS,
                            CustinsParamSupport.CUSTINS_PARAM_NAME_PROXY_AUTH_SHIELD_CHECK_INTERVAL);
                    List<CustinsParamDO> custinsParamDOList = custinsParamService.getCustinsParams(custins.getId(), params);
                    if (!custinsParamDOList.isEmpty()) {
                        for (CustinsParamDO cp : custinsParamDOList) {
                            if (CustinsParamSupport.CUSTINS_PARAM_NAME_PROXY_MAX_POOL_CONNS.equals(cp.getName())) {
                                if (Integer.valueOf(cp.getValue()) > 0) {
                                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE,
                                            ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE.getDesc() + String.format(",because param %s > 0", cp.getName()));
                                }
                            } else if (CustinsParamSupport.CUSTINS_PARAM_NAME_PROXY_AUTH_SHIELD_CHECK_INTERVAL.equals(cp.getName())) {
                                if (Integer.valueOf(cp.getValue()) > 0) {
                                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE,
                                            ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE.getDesc() + String.format(",because param %s > 0", cp.getName()));
                                }
                            }
                        }
                    }
                }
            }

            CustInstanceDO guardins = custinsService
                    .getGuardInstanceByPrimaryCustinsId(custins.getId());
            if (guardins != null) {
                return createErrorResponse(ErrorCode.GUARDINSTANCE_EXIST);
            }
            List<CustInstanceDO> readinsList = custinsService
                    .getReadCustInstanceListByPrimaryCustinsId(custins.getId(), true);
            // 如果实例有只读实例，则不允许切换到dns实例
            if (readinsList.size() > 0 && CustinsSupport.CONN_TYPE_DNS.equals(newConnType)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_READINSTANCE);
            }

            Set<String> clusterNameSet = new HashSet<String>();
            clusterNameSet.add(custins.getClusterName());

//            List<Integer> readCustinsIds = new ArrayList<Integer>();
            for (CustInstanceDO readins : readinsList) {
                if (readins.isRead()) {
//                    readCustinsIds.add(readins.getId());
                    clusterNameSet.add(readins.getClusterName());
                }
            }

            // 调用ResourceManager判断当前集群（包括只读实例所在集群）是否支持目标链路类型
            for (String clusterName : clusterNameSet) {
                if (!resApi.isSupportConnType(clusterName, newConnType)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
                }
            }

            ConnAddrChangeLogDO connAddrChangeLog = this.createConnAddrChangeLogForDnsLvsSwitch(
                    actionParams, custins, newConnType, custinsConnAddr);
            Map<String, Object> dbmxsRet = createDBMXSForRdsCustins(actionParams, custins, true, true);

            //lvs<->proxy,dns<->proxy
            if (custins.isLvs() || custins.isProxy() || custins.isDns()) {
                custinsParamService.setCustinsParam(custins.getId(), "max_pool_conns", "0");
                custinsParamService.setCustinsParam(custins.getId(), "a_s_check_interval", "0");
                custinsParamService.setCustinsParam(custins.getId(), "alive_time", "5000");
                custinsParamService.setCustinsParam(custins.getId(), "a_s_activate_cnt", "0");
                custinsParamService.setCustinsParam(custins.getId(), "a_s_forbid_time", "0");
                custinsParamService.setCustinsParam(custins.getId(), "switch_keep_alive", "1");
            }

            Integer taskId = taskService.switchConnTypeTask(
                    mysqlParamSupport.getAction(actionParams),
                    custins,  Collections.emptyList(), newConnType, connAddrChangeLog,
                    mysqlParamSupport.getOperatorId(actionParams));
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));

            Map<String, Object> data = new HashMap<String, Object>();
            data.putAll(dbmxsRet);
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.OLD_DB_INSTANCE_CONN_TYPE, custins.getConnType());
            data.put(ParamConstants.NEW_DB_INSTANCE_CONN_TYPE, newConnType);
            data.put(ParamConstants.TASK_ID, taskId);
            return data;

        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public Map<String, Object> createDBMXSForRdsCustins(Map<String, String> actionParams, CustInstanceDO custins, boolean isConnTypeSwitching, boolean isCheckExistedDBMXS) throws RdsException {

        Map<String, Object> ret = new HashMap<>();
        if (!getSupportTenantMode(custins, isConnTypeSwitching)) {
            logger.warn("custins(" + custins.getInsName() + ") does not support DBMXS link, use HAProxy.");
            ret.put("IsSingleTenant", false);
            return ret;
        }

        List<CustinsServiceDO> custinsServiceDOList = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "dbmxs");

        if (isCheckExistedDBMXS && custinsServiceDOList.size() > 0) {
            throw new RdsException(ErrorCode.DBMXS_ALREADY_EXIST);
        }

        ClustersDO clustersDO = clusterService.getClusterByClusterName(custins.getClusterName());
        if (clustersDO == null) {
            logger.error("can not get cluster info by cluster name: " + custins.getClusterName());
            throw new RdsException(ErrorCode.CLUSTER_NOT_FOUND);
        }
        String region = clustersDO.getLocation();

        // init resource container
        String subDomain = clusterService.getRegionByCluster(custins.getClusterName());
        ResourceContainer resourceContainer = new ResourceContainer(subDomain, CustinsSupport.DB_TYPE_MYSQL);
        resourceContainer.setRequestId(mysqlParamSupport.getParameterValue(actionParams,ParamConstants.REQUEST_ID));
        resourceContainer.setClusterName(custins.getClusterName());
        resourceContainer.setUserId(custins.getUserId());
        resourceContainer.setAccessId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID));
        resourceContainer.setOrderId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID));
        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        custinsResModel.setConnType(custins.getConnType());
        custinsResModel.setPreferProxyGroupId(custins.getProxyGroupId());
        resourceContainer.addCustinsResModel(custinsResModel);

        CustInstanceDO dbmxsCustins = null;
        String dbmxsInsName = "";
        for (int i = 0; i < 11; i++) {
            dbmxsInsName = SupportUtils.getRandomInsName(20);
            dbmxsCustins = custinsService.getCustInstanceByInsName(null, dbmxsInsName);
            if (dbmxsCustins == null) {
                break;
            }
            if (i == 10) {
                throw new RdsException(ErrorCode.INVALID_DBMXS_INS_NAME);
            }
        }

        Integer dbmxsNetType = CustinsSupport.NET_TYPE_PRIVATE;
        String dbmxsConnectionString = dbmxsInsName.replace('_', '-');
        Integer dbmxsPort = Integer.parseInt(CustinsSupport.getConnPort(null, CustinsSupport.DB_TYPE_DBMXS));
        resourceContainer = addDbmxsToResourceContainer(actionParams, custins, resourceContainer, dbmxsInsName, region,
            dbmxsNetType,
            null,
            null,
            null,
            null,
            null,
            null,
            dbmxsConnectionString,
            dbmxsPort,
            false);

        RdsResModel dbmxsRdsResModel = getDbmxsRdsResModel(custins, resourceContainer);
        if(dbmxsRdsResModel == null){
            throw new RdsException(ErrorCode.DBMXS_NOT_FOUND,"getDbmxsRdsModel failed!");
        }

        Response<AllocateResRespModel> response = resManagerService.allocateRes(resourceContainer);
        if (!response.getCode().equals(200)) {
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }

        ret.put("IsSingleTenant", true);
        ret.put("TargetCustinsName", custins.getInsName());
        ret.put("DbmxsInstanceClassCode", dbmxsRdsResModel.getdBInstanceClass());
        ret.put("DbmxsInstanceName", dbmxsRdsResModel.getDbInstanceName());
        ret.put("DbmxsPort", dbmxsPort);
        return ret;
    }

    public  ResourceContainer addDbmxsToResourceContainer(Map<String, String> actionParams, CustInstanceDO custins, ResourceContainer resourceContainer,
                                                          String dbmxsInsName, String region, Integer netType,
                                                          String vpcId, Integer tunnelId, String vSwitchId,
                                                          String ipAddress, String vpcInstanceId,String ipWhiteList,
                                                          String connectionString, Integer port, boolean forceAllocateDBMXS) throws RdsException{
        List<CustinsServiceDO> custinsServiceDOList = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "dbmxs");

        if (!forceAllocateDBMXS && custinsServiceDOList.size() != 0) {
            logger.error("custins(" + custins.getInsName() + ") already got one dbmxs instance: " + custinsServiceDOList.get(0).getServiceName());
            throw new RdsException(ErrorCode.DBMXS_ALREADY_EXIST);
        }

        //String dbmxsDBVersion = super.getParameterValue("DBMXSDBVersion", "4.2");
        String dbmxsDBVersion = "4.2";
        String classCode = mysqlParamSupport.getParameterValue(actionParams, "SpecificDbmxsClassCode");

        if (StringUtils.isNotBlank(classCode) && instanceService.getInstanceLevelByClassCode(classCode, DB_TYPE_DBMXS, dbmxsDBVersion, null, "logic") == null) {
            throw new RdsException(ErrorCode.INVALID_DBMXS_SPECIFIC_CLASS);
        }

        InstanceLevelDO dbmxsInstanceLevel = getDBMXSMappingLevelWithSiteNumber(custins, dbmxsDBVersion);

        if (dbmxsInsName == null) {
            throw new RdsException(ErrorCode.INVALID_DBMXS_INS_NAME);
        }

        String subDomain = region == null ? clusterService.getRegionByCluster(custins.getClusterName()) : region;
        CustinsResModel custinsResModel = null;
        Integer custinsResModelIndex;
        List<CustinsResModel> custinsResModelList = resourceContainer.getCustinsResModelList();
        for (custinsResModelIndex = 0; custinsResModelIndex < custinsResModelList.size() && custinsResModel == null; custinsResModelIndex++) {
            custinsResModel = custinsResModelList.get(custinsResModelIndex).getCustinsId().equals(custins.getId()) ? custinsResModelList.get(custinsResModelIndex) : null;
        }
        // custinsResModelIndex minus 1 because it has plus 1 before the loop ended.
        custinsResModelIndex--;

        if (custinsResModel == null) {
            logger.error("Can not find target custins(id: " + custins.getId() + ") in resourceContainer.CustinsResModelList.");
            throw new RdsException(ErrorCode.TARGET_CUSTINS_NOT_FOUND);
        }

        UserDO userDO = userService.getUserDOByUserId(custins.getUserId());
        if (userDO == null) {
            logger.error("Can not get user by user_id: " + custins.getUserId() + ", custins_id: " + custins.getId());
            throw new RdsException(ErrorCode.USER_NOT_FOUND);
        }
        String login_id = userDO.getLoginId();
        String bid = login_id.split("_", 2)[0];
        String uid = login_id.split("_", 2)[1];
        if (ipWhiteList == null) {
            List<CustinsIpWhiteListDO> ipWhiteListGroupList =
                ipWhiteListService.getCustinsIpWhiteList(custins.getId(), null, null, null);
            ipWhiteList = "";
            for (CustinsIpWhiteListDO ipWhiteListDO : ipWhiteListGroupList) {
                ipWhiteList = ipWhiteList + ipWhiteListDO.getIpWhiteList() + ",";
            }
            ipWhiteList = ipWhiteList.replace(" ", "").replaceAll(",*$", "");
        }

        if (connectionString == null) {
            throw new RdsException(ErrorCode.INVALID_DBMXS_CONNECTION_STRING);
        }

        RdsResModel rdsResModel = new RdsResModel();
        rdsResModel.setRole("dbmxs");
        rdsResModel.setUser_Id(bid);
        rdsResModel.setUid(uid);
        rdsResModel.setInsCount(1);
        rdsResModel.setdBInstanceClass(dbmxsInstanceLevel.getClassCode());
        rdsResModel.setRegion(subDomain);
        rdsResModel.setEngine(dbmxsInstanceLevel.getDbType());
        rdsResModel.setEngineVersion(dbmxsInstanceLevel.getDbVersion());
        rdsResModel.setdBInstanceNetType(netType.toString());
        rdsResModel.setDbInstanceName(dbmxsInsName);
        rdsResModel.setConnectionString(connectionString);
        rdsResModel.setPort(port);
        rdsResModel.setSecurityIPList(ipWhiteList);
        rdsResModel.setBackupRetentionPeriod(7);
        rdsResModel.setPreferredBackupTime("00:00Z");
        if (CustinsSupport.isVpcNetType(netType)) {
            rdsResModel.setVpcId(vpcId);
            rdsResModel.setTunnelId(tunnelId);
            rdsResModel.setVswitchId(vSwitchId);
            rdsResModel.setIpAddress(ipAddress);
            rdsResModel.setVpcInstanceId(vpcInstanceId);
        }

        List<RdsResModel> rdsResModelList = custinsResModel.getRdsResModelList() != null ? custinsResModel.getRdsResModelList() : new ArrayList<RdsResModel>();
        rdsResModelList.add(rdsResModel);
        custinsResModel.setRdsResModelList(rdsResModelList);
        custinsResModelList.set(custinsResModelIndex, custinsResModel);
        resourceContainer.setCustinsResModelList(custinsResModelList);

        return resourceContainer;
    }

    RdsResModel getDbmxsRdsResModel(CustInstanceDO custins, ResourceContainer resourceContainer) {
        CustinsResModel custinsResModel = null;
        List<CustinsResModel> custinsResModelList = resourceContainer.getCustinsResModelList();
        for (int i = 0; i < custinsResModelList.size() && custinsResModel == null; i++) {
            custinsResModel = custinsResModelList.get(i).getCustinsId().equals(custins.getId()) ? custinsResModelList.get(i) : null;
        }
        if (custinsResModel == null) {
            logger.error("Can not get custins " + custins.getId() + "from resource container");
            return null;
        }
        List<RdsResModel> rdsResModelList = custinsResModel.getRdsResModelList() != null ? custinsResModel.getRdsResModelList() : new ArrayList<RdsResModel>();
        for (RdsResModel rdsResModel : rdsResModelList) {
            if (rdsResModel.getEngine().equals("dbmxs")) {
                return rdsResModel;
            }
        }
        logger.error("Can not get dbmxs RdsResModel.");
        return null;
    }

    /**
     * 判断实例可以切换的 Proxy 租户模式。
     * @param custins
     * @param isConnTypeSwitching
     * @return The supported tenant mode. false: multi-tenant, true: single-tenant
     */

    boolean getSupportTenantMode(CustInstanceDO custins, boolean isConnTypeSwitching) throws RdsException {
        InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        if (instanceLevelDO == null) {
            logger.error("custins(" + custins.getInsName() + ") can not find instance_level by level_id (" + custins.getLevelId() + ")!");
            throw new RdsException(ErrorCode.INSLEVEL_NOT_FOUNT);
        }

        // enable_dbmxs:         1: enable single-tenant, other: disable single-tenant
        CustinsParamDO enableDBMXS = custinsParamService.getCustinsParam(custins.getId(), "enable_dbmxs");

        // force_tenant_mode:    null: not force, 0: force multi-tenant, 1: force single-tenant, other: illegal value
        CustinsParamDO forceTenantMode = custinsParamService.getCustinsParam(custins.getId(), "force_tenant_mode");
        if(forceTenantMode != null && !Arrays.asList("0", "1").contains(forceTenantMode.getValue())){
            throw new RdsException(ErrorCode.INVALID_FORCE_TENANT_MODE,
                "Invalid custins_param force_tenant_mode for custins " + custins.getId() + "! " +
                    "It can only be 0 or 1.");
        }

        int minCpuCores = 8;
        try {
            minCpuCores = Integer.parseInt(resourceService.getDisplayValue("MIN_CPU_CORES_FOR_SINGLE_TENANT_PROXY", "dbmxs"));
        } catch(NumberFormatException e) {
            minCpuCores = 8;
        } catch(NullPointerException e) {
            minCpuCores = 8;
        }

        return ("1".equals(enableDBMXS == null ? "0" : enableDBMXS.getValue()))

            && (isConnTypeSwitching ? custins.isSupportDbmxsForConnTypeSwitching()
            : custins.isSupportDbmxsForProxyTenantModeSwitching())

            && (forceTenantMode != null ? "1".equals(forceTenantMode.getValue())
            : (CustinsSupport.ISOLATE_HOST_FIXED.equals(instanceLevelDO.getIsolateHost())
                || CustinsSupport.ISOLATE_HOST_SINGLE.equals(instanceLevelDO.getIsolateHost()))
                && instanceLevelDO.getCpuCores() >= minCpuCores);
    }

    public InstanceLevelDO getDBMXSMappingLevelWithSiteNumber(CustInstanceDO custins, String dbVersion) throws RdsException {
        InstanceLevelDO dbmxsInstanceLevel = instanceService.getMappingInstanceLevel(custins, DB_TYPE_DBMXS, dbVersion, "logic");
        if (dbmxsInstanceLevel == null) {
            throw new RdsException(ErrorCode.MAPPING_INSTANCE_LEVEL_NOT_FOUND);
        }
        ClustersDO clustersDO = clusterService.getClusterByClusterName(custins.getClusterName());
        if (clustersDO == null) {
            logger.error("No cluster named " + custins.getClusterName());
            throw new RdsException(ErrorCode.CLUSTER_NOT_FOUND);
        }
        // DBMXS only supports single or double site mode.
        Integer multiplier = clustersDO.getIsMultiSite() == null || clustersDO.getIsMultiSite() == 0  ? 1 : 2;
        InstanceLevelDO dbmxsServerLevel = instanceService.getExpectInstanceLevelByDBTypeAndCharacterType(
            dbmxsInstanceLevel.getId(),
            instanceService.getAllInstanceLevelsByParentLevelId(dbmxsInstanceLevel.getId()),
            DB_TYPE_DBMXS_SERVER,
            CHARACTER_TYPE_PHYSICAL);
        InstanceLevelDO expectedDBMXSServerLevel = instanceService.getInstanceLevelsByCondition(
            dbmxsServerLevel.getDbType(),
            dbmxsServerLevel.getCharacterType(),
            dbmxsServerLevel.getCpuCores(),
            dbmxsServerLevel.getMemSize(),
            dbmxsServerLevel.getInsCount() * multiplier);
        if (expectedDBMXSServerLevel == null) {
            logger.error("No level with cpu: " + dbmxsServerLevel.getCpuCores() + " mem: " +
                dbmxsServerLevel.getMemSize() + " ins_count: " + dbmxsServerLevel.getInsCount() * multiplier +
                "(" + dbmxsServerLevel.getInsCount() + ") * (" + multiplier + ")");
            throw new RdsException(ErrorCode.MAPPING_INSTANCE_LEVEL_NOT_FOUND);
        }
        List<InstanceLevelRelDO> relDOs = instanceService.getInstanceLevelRelByCharacterLevelIdList(Arrays.asList(expectedDBMXSServerLevel.getId()));
        Collections.sort(relDOs, new Comparator<InstanceLevelRelDO>() {
            @Override
            public int compare(InstanceLevelRelDO relDO1, InstanceLevelRelDO relDO2) {
                return relDO1.getId().compareTo(relDO2.getId()) * -1;
            }
        });
        if (relDOs.size() == 0) {
            logger.error("No parent level id with character id " + expectedDBMXSServerLevel.getId());
            throw new RdsException(ErrorCode.MAPPING_INSTANCE_LEVEL_NOT_FOUND);
        }
        InstanceLevelDO expectedDBMXSLevel = instanceService.getInstanceLevelByLevelId(relDOs.get(0).getParentLevelId());
        if (expectedDBMXSLevel == null) {
            logger.error("Can not get instance level with level id " + relDOs.get(0).getParentLevelId());
            throw new RdsException(ErrorCode.MAPPING_INSTANCE_LEVEL_NOT_FOUND);
        }
        return expectedDBMXSLevel;
    }

    /**
     * lvs与dns之间链路切换时创建ConnAddrChangeLog
     *
     * @param custins
     * @param newConnType
     * @param custinsConnAddr
     * @return
     * @throws RdsException
     */
    private ConnAddrChangeLogDO createConnAddrChangeLogForDnsLvsSwitch(Map<String,String> actionParams,
                                                                       CustInstanceDO custins,
                                                                       String newConnType,
                                                                       CustinsConnAddrDO custinsConnAddr) throws RdsException {
        ConnAddrChangeLogDO connAddrChangeLog = null;

        // dns与lvs之间互切需要记录ConnAddrChangeLog
        if (custins.isDns() && CustinsSupport.isLvs(newConnType) ||
                custins.isLvs() && CustinsSupport.isDns(newConnType)) {

            String vip = null;
            // dns切换到vip需要申请vip
            if (custins.isDns() && CustinsSupport.isLvs(newConnType)) {
                // 申请VIP & DNS
                Response<AllocateVipRespModel> response = resApi.vip(custins.getId(), custinsConnAddr.getNetType(),
                        custinsConnAddr.getConnAddrCust(), 0);
                if (!response.getCode().equals(200)) {
                    //mysqlParamSupport.createResourceRecord(actionParams,custins.getClusterName(), custins.getInsName(), JSON.toJSONString(response));
                    throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                            ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                }
                vip = response.getData().getVip();
            }

            connAddrChangeLog = ConnAddrSupport.createConnAddrChangeLogForUpdateNetType(
                    custins.getId(),
                    custinsConnAddr.getNetType(),
                    custinsConnAddr.getConnAddrCust(),
                    custinsConnAddr.getVip(),
                    custinsConnAddr.getVport(),
                    custinsConnAddr.getUserVisible(),
                    custinsConnAddr.getTunnelId(),
                    custinsConnAddr.getVpcId(),
                    null,
                    custinsConnAddr.getConnAddrCust(),
                    vip,
                    custinsConnAddr.getVport(),
                    custinsConnAddr.getUserVisible(),
                    custinsConnAddr.getTunnelId(),
                    custinsConnAddr.getVpcId(),
                    null);
        }
        return connAddrChangeLog;
    }

    /**
     * 校验实例是否允许切换至新链路
     *
     * @param custins
     * @param newConnType
     * @throws RdsException
     */
    private void checkNewConnTypeValid(CustInstanceDO custins, CustinsConnAddrDO custinsConnAddr,
                                       String newConnType) throws RdsException {
        if (custins.isDns() || custins.isProxy()) {
            // dns链路和proxy链路仅支持切换到lvs
            if (!CustinsSupport.CONN_TYPE_LVS.equals(newConnType)) {
                throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
            }
        } else if (custins.isLvs()) {
            // 切换至DNS链路
            if (CustinsSupport.CONN_TYPE_DNS.equals(newConnType)) {
                checkPortValidForSwitchToDNS(custins, custinsConnAddr);
            } else if (CustinsSupport.CONN_TYPE_PROXY.equals(newConnType)) {
                ;// OK
            } else {
                throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
            }
        }
    }

    /**
     * 检查实例主机端口与vip端口是否一致
     *
     * @param custins
     * @param custinsConnAddr
     * @throws RdsException
     */
    private void checkPortValidForSwitchToDNS(CustInstanceDO custins,
                                              CustinsConnAddrDO custinsConnAddr)
            throws RdsException {
        // 检查物理主机端口与vip端口是否一致
        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
        if (instanceList.size() > 1 &&
                !instanceList.get(0).getPort().equals(instanceList.get(1).getPort())) {
            // 主备端口不一致则不允许进行切换
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
        if (!custinsConnAddr.getVport().equals(instanceList.get(0).getPort().toString())) {
            // 物理主机端口与vip端口不一致则不允许切换至DNS实例
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
    }
}
