package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsSearchService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.api.OpenSearchApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDeleteDBInstanceImpl")
public class DeleteDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsSearchService custinsSearchService;
    @Autowired
    protected UserService userService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    ReplicaSetService replicaSetService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws
        RdsException {

        try {
            custins = mysqlParamSupport.getCustInstance(params);
            if (custins == null) {
                if (custinsService.checkDeletedCustInstanceByInsName(mysqlParamSupport.getDBInstanceName(params))) {
                    //实例已销毁
                    return createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);
                }
                //实例不存在，或者不是实例拥有者
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //统一优化到一个方法中
            mysqlParamSupport.checkNotDeleteHaProxyCustins(custins);

            if (!custins.isNormal() && !custins.isMysqlLogic()) {
                logger.warn("Can't delete custins not normal or logic from this api.");
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            //发生容灾切换时，禁止删除主实例和灾备实例
            if ((custins.isGuard() && custins.isLogicPrimary()) || (custins.isPrimary() && custins.isLogicGuard())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            Integer deleteMode = CustinsSupport.GRACE_DELETE;
            if (mysqlParamSupport.hasParameter(params, "deletemode")) {
                deleteMode = CustinsSupport.getDeleteMode(mysqlParamSupport.getParameterValue(params, "deletemode"));
            }
            //如果需要删除的实例是主实例的话，则需要确保该主实例所关联的灾备实例和只读实例已经被删除
            if (custins.isPrimary()) {
                //查询灾备实例，如果有灾备实例的话，则禁止删除
                CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                //过滤掉正在删除的实例
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_GUARD);//灾备实例
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                //查询只读实例，如果有只读实例的话，则禁止删除
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                //过滤掉正在删除的实例
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_READ);//只读实例
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
            }

            //当前mysql实例为读实例
            if (custins.isRead()) {
                CustInstanceDO primaryIns = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                List<CustInstanceDO> readinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(primaryIns.getId(), false);
                List<CustInstanceDO> validReadInsList = new ArrayList<>();
                for (CustInstanceDO readins : readinsList) {
                    if (!CustinsSupport.CUSTINS_STATUS_DELETING.equals(readins.getStatus()) &&
                            !CustinsSupport.CUSTINS_STATUS_CREATING.equals(readins.getStatus())) {
                        validReadInsList.add(readins);
                    }
                }
                List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(primaryIns.getId(), null, CustinsSupport.RW_TYPE_RW_SPLIT);

                if (taskService.getCustinsRunningTask(primaryIns.getId(), TaskSupport.TASK_CREATE_RW_SPLIT_VIP)) {
                    return createErrorResponse(ErrorCode.UNFINISHED_CREATE_RW_TASK);
                }

                if (custinsConnAddrList.size() > 0) {
                    if (validReadInsList.size() == 1) {
                        // 如果存在读写分离vip，那么不允许删除最后一个只读实例。
                        // 除非指定参数强制删除只读实例，那么先触发删除主实例的读写分离vip任务，
                        // 再触发删除只读实例任务。
                        //aone https://aone.alibaba-inc.com/req/29915995
                        // 瑶池自动关闭读写分离，删除只读也关闭读写分离，导致任务多下了, 所以这里禁止删除只读的时候关闭读写分离
                        deleteMode = CustinsSupport.GRACE_DELETE;
                        if (CustinsSupport.GRACE_DELETE.equals(deleteMode)) {
                            return createErrorResponse(ErrorCode.RW_SPLIT_NETTYPE_EXIST);
                        } else {
                            CustinsConnAddrDO delCustinsConnAddr = custinsConnAddrList.get(0);
                            ConnAddrChangeLogDO delConnAddrChangeLog = ConnAddrSupport
                                .createConnAddrChangeLogForDeleteNetType(
                                    primaryIns.getId(),
                                    delCustinsConnAddr.getNetType(),
                                    delCustinsConnAddr.getConnAddrCust(),
                                    delCustinsConnAddr.getVip(),
                                    delCustinsConnAddr.getVport(),
                                    delCustinsConnAddr.getUserVisible(),
                                    delCustinsConnAddr.getTunnelId(),
                                    delCustinsConnAddr.getVpcId(),
                                    null,
                                    CustinsSupport.RW_TYPE_RW_SPLIT);

                            List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(1);
                            connAddrChangeLogs.add(delConnAddrChangeLog);

                            try {
                                Integer taskId = taskService.changeConnAddrTask(mysqlParamSupport.getAction(params), primaryIns, connAddrChangeLogs,
                                    CustinsState.STATUS_ACTIVATION, TaskSupport.TASK_CHANGE_CONN_ADDR_DELETE_VIP,
                                    mysqlParamSupport.getOperatorId(params));
                                taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(params));
                            } catch (Exception ex) {
                                logger.error("Custins: " + primaryIns.getId()
                                    + " DeleteDBInstanceRWSplitNetType failed when create task. Details: "
                                    + JSON.toJSONString(connAddrChangeLogs));
                                throw new Exception(ex);
                            }
                        }
                    }
                }
            }
            /**
             * OpenSearch是集团的一个全文索引服务，与rds关联，如果用户选择
             * rds实例写入之后，OpenSearch异步从实例中拖数据，建立全文索引
             * 用户查询时，通过proxy进行过滤，如果发现有类似与like的操作，则查询该实例
             * */
            if (custinsSearchService.checkCustinsSearch(custins)) {
                String apiUrlString = userService.getOpenSearchApiUrlByClusterName(custins.getClusterName());
                if (apiUrlString == null) {
                    return createErrorResponse(ErrorCode.OPENSEARCH_NOT_SUPPORT);
                }
                JSONObject apiUrl = JSON.parseObject(apiUrlString);
                OpenSearchApi api = new OpenSearchApi(apiUrl.getString("host"),
                    apiUrl.getString("accesskey"),
                    apiUrl.getString("secret"));
                CustinsSearchDO custinsSearch = custinsSearchService.getCustinsSearchByCustins(custins);
                api.deleteOpenSearchInstance(custinsSearch.getAppName());
                custinsSearchService.deleteCustinsSearch(custinsSearch);
            }

            //删除实例
            String delayDeleteTimeStr = mysqlParamSupport.getParameterValue(params, ParamConstants.DELAY_DELETE_TIME);
            Integer taskId = -1;
            if (delayDeleteTimeStr != null) {
                Integer delayDeleteTime = CheckUtils.parseInt(delayDeleteTimeStr, 1, 100, ErrorCode.INVALID_DELAY_DELETE_TIME);
                Date nowDate = new Date();
                long realDeleteTime = nowDate.getTime()/1000 + (long) (delayDeleteTime * 3600);
                custinsParamService.setCustinsParam(custins.getId(), "scheduled_delete_ins_timestamp",
                        String.valueOf(realDeleteTime));
            } else {
                taskId = taskService.deleteCustInstanceAndTask(
                        mysqlParamSupport.getAction(params), custins,
                        mysqlParamSupport.getOperatorId(params));
                taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(params));
            }

            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
