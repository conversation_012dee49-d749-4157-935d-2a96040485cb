package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.DistributeMode;
import com.alicloud.apsaradb.resmanager.DistributeRule;
import com.alicloud.apsaradb.resmanager.HostinsResModel;
import com.alicloud.apsaradb.resmanager.PortDistributeRule;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.VipResModel;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.BaklistDO;
import com.aliyun.dba.bak.entity.BaksetMetaInfo;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.dba.bak.support.BakSupport.BAKTYPE_FULL;
import static com.aliyun.dba.bak.support.BakSupport.BAKWAY_XTRABACKUP;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.base.support.MysqlErrorCode.INVALID_PARAM_TABLEMETA_DUPLICATE_DBNAME;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAction;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckHostIdSet;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getOperatorId;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.CONN_TYPE_PROXY;
import static com.aliyun.dba.instance.support.InstanceSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_SLAVE;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.CUSTINS_PARAM_NAME_IS_POLARX_HATP;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

/**
 * 单库单表恢复，恢复到原实例功能
 * 恢复到新实例复用CloneDBInstance
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRestoreTableImpl")
public class RestoreTableImpl implements IAction {

    //private static Logger logger = Logger.getLogger(RestoreTableImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(RestoreTableImpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected CloneDBInstanceImpl cloneDBInstance;
    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        boolean useCDM = Boolean.parseBoolean(CustinsParamSupport.getParameterValue(actionParams,"useCDM","false"));
        logger.info("useCDM:"+useCDM);
        boolean isSuccess = false;
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            //专享型实例
            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            //不支持5.6、8.0金融版
            if (mysqlDBCustinsService.isMysqlEnterprise(custins) && !custins.isMysql57()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //已有子实例，禁止做恢复
            if (custinsService.hasChildCustInstanceByCustinsId(custins.getId())) {
                return createErrorResponse(ErrorCode.CHILDINSTANCE_EXIST);
            }

            //如果有延迟加载相关任务running，禁止做恢复
            Map<String, Object> condition = new HashMap<>();
            condition.put("custinsId", custins.getId());
            condition.put("taskKey", "lazy_load");
            int[] status = {0, 1, 7, 8};
            condition.put("status", status);
            if(taskService.countTaskQueueByCondition(condition)>0){
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String restoreType =  mysqlParamSupport.getParameterValue(actionParams, ParamConstants.RESTORE_TYPE, RESTORE_TYPE_BAKID);
            boolean isRestoreByTime = BakSupport.isRestoreByTime(restoreType);

            Boolean isDisasterRestore = BakSupport.getAndCheckIsDisasterRestore(actionParams);
            BaksetMetaInfo baksetMetaInfo = null;
            TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
            trans.setsCinsReserved(1);
            //检查传入的tableMeta参数
            String tableMeta;
            //原实例恢复需要的空间
            long needSpace;
            if (isDisasterRestore) {
                CheckCreateDdrDBInstanceImpl checkCreateDdrDBInstance = SpringContextUtil.getBeanByClass(CheckCreateDdrDBInstanceImpl.class);
                actionParams.put("engine", custins.getDbType());
                actionParams.put("engineversion", custins.getDbVersion());
                baksetMetaInfo = checkCreateDdrDBInstance.doCheckDdrRestore(custins, actionParams, restoreType, true);
                trans.setBakhisId(baksetMetaInfo.getId());
                if (isRestoreByTime) {
                    // 根据备份时间点查询备份集大小
                    DateTime restoreTimeUTC = custinsService.validRestoreByTimeSafe(actionParams, custins);
                    //转化为Bak库时间进行比较
                    Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_BAK);

                    trans.setIsBaseTime(1);
                    trans.setRecoverTime(restoreTime);
                }
                tableMeta = mysqlParamSupport.getAndCheckTableMeta(actionParams, baksetMetaInfo, "restore", custins.getDbVersion(), false);
                //库表恢复场景空间检查，单位KB，乘以10倍解压缩率
                needSpace = mysqlParamSupport.getAndCheckStorageForRestoreDbtables(tableMeta, baksetMetaInfo) / 1024 * 10;
            } else {
                //必须开启了库表备份
                BaklistDO bakList = bakService.getBaklistByCustinsId(custins.getId());
                if (bakList.getCompress() != 4) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }

                BakhistoryDO history;
                if (isRestoreByTime) {
                    if (!mysqlEngineCheckService.checkCloneBeforeUpgrade(actionParams,custins.getId())){
                        throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
                    }

                    // 根据备份时间点查询备份集大小
                    DateTime restoreTimeUTC = custinsService.validRestoreByTimeSafe(actionParams, custins);
                    //转化为Bak库时间进行比较
                    Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_BAK);

                    trans.setIsBaseTime(1);
                    trans.setRecoverTime(restoreTime);

                    //查询符合时间要求的备份集
                    history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                    bakService.lockBinlogForRestore(custins.getId(),history.getBakEnd(),restoreTime); // 锁定用于恢复的Binlog，避免被删除
                    bakService.lockBakHisForRestore(history.getHisId());
                    ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                            JSON.toJSONString(ImmutableMap.of(
                                    "custinsId", custins.getId().toString(),
                                    "begin", history.getBakBegin().getTime(),
                                    "end", restoreTime.getTime())));

                    ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(history.getHisId()));
                } else {
                    //直接获取备份集
                    Long bakId = CheckUtils.parseLong(getParameterValue(actionParams, "BackupSetID"), null,
                            null, ErrorCode.BACKUPSET_NOT_FOUND);
                    checkService.getAndCheckBakhistory(custins, bakId);
                    history = bakService.getBakhistoryByBackupSetId(custins.getId(), bakId);
                    bakService.lockBakHisForRestore(history.getHisId());
                    ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(history.getHisId()));
                    trans.setBakhisId(bakId);
                }
                if(history == null){
                    return createErrorResponse(ErrorCode.INVALID_BAKSET);
                }
                tableMeta = mysqlParamSupport.getAndCheckTableMeta(actionParams, history, "restore", custins.getDbVersion(), false);
                //库表恢复场景空间检查，单位KB，乘以10倍解压缩率
                needSpace = mysqlParamSupport.getAndCheckStorageForRestoreDbtables(tableMeta, history) / 1024 * 10;
            }

            //通过dbossApi检查当前实例是否存在重复库表名称，仅恢复到原实例时需要检查
            Map<String,Object> checkWithDbossMap = mysqlParamSupport.checkTableMetaWithDboss(custins,tableMeta);
            boolean result = (boolean)checkWithDbossMap.get("result");
            if(!result){
                MysqlErrorCode mysqlErrorCode = MysqlErrorCode.INVALID_PARAM_TABLEMETA_DUPLICATE_DBNAME;
                mysqlErrorCode.setDesc((String)checkWithDbossMap.get("descInfo"));
                return createErrorResponse(INVALID_PARAM_TABLEMETA_DUPLICATE_DBNAME.toArray());
            }

            //实例原空间大小，单位MB
            Long custinsSpaceMB = custins.getDiskSize();
            if (custinsSpaceMB != null) {
                Long custinsSpaceKB = custinsSpaceMB * 1024;
                needSpace = needSpace > custinsSpaceKB ? custinsSpaceKB : needSpace;
            }


            List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
            trans.setsHinsid1(insIds.get(0));
            if (insIds.size() > 1) {
                trans.setsHinsid2(insIds.get(1));
            }
            CustInstanceDO tempCustins;

            tempCustins = createTempCustins(custins,needSpace,actionParams,useCDM);

            trans.setdLevelid(tempCustins.getLevelId());
            trans.setdCinsid(tempCustins.getId());
            insIds = custinsService.getInstanceIdsByCustinsId(tempCustins.getId());
            trans.setdHinsid1(insIds.get(0));
            if (insIds.size() > 1) {
                trans.setdHinsid2(insIds.get(1));
            } else {
                trans.setdHinsid2(0);
            }
            //task_key 为：recover_ins_dbstables

            Integer taskId;
            String taskKey;
            //库表恢复场景
            //TableMeta参数存入translist的parameter参数中
            Map<String, Object> translistParams = new HashMap<>();
            translistParams.put("table_meta", tableMeta);
            translistParams.put("restore_table_mode", getParameterValue(actionParams, "RestoreTableMode"));
            translistParams.put("restoreType", restoreType);
            translistParams.put("is_disaster_restore", isDisasterRestore ? 1 : 0);
            translistParams.put("useCDM",useCDM);
            if (baksetMetaInfo != null) {
                baksetMetaInfo.setBakTableMeta(null);
                translistParams.put("bakhis_info", JSONObject.toJSONString(baksetMetaInfo));
            }
            trans.setParameter(JSONObject.toJSONString(translistParams));

            //task_key 为：recover_ins_dbstables
            taskId = instanceService.restoreCustInstanceDBsTablesTask(
                getAction(actionParams), custins, trans, tempCustins, getOperatorId(actionParams));
            taskKey = TaskSupport.TASK_RECOVER_INS_DBSTABLES;

            Map<String, Object> data = new HashMap<String, Object>(10);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("MigrationID", trans.getId());
            data.put("ChildDBInstanceID", tempCustins.getId());
            data.put("ChildDBInstanceName", tempCustins.getInsName());
            data.put("TaskId", taskId);
            //说明当前任务类型
            data.put("TaskKey", taskKey);
            isSuccess = true;
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if(!isSuccess) {
                if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")) {
                    JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
                    bakService.unlockBinlogForRestore(
                            Integer.valueOf(lockBinlog.get("custinsId").toString()),
                            new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                            new Date(Long.parseLong(lockBinlog.get("end").toString()))
                    );
                }
                if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
                    String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
                    bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));

                }
            }
        }
    }


    /**
     * 非CDM场景下的临时实例，只有一个节点，任务完成直接被释放
     * @param custins
     * @param needSpace
     * @param actionParams
     * @return
     * @throws RdsException
     */
    private CustInstanceDO createTempCustins(CustInstanceDO custins, long needSpace, Map<String, String> actionParams,boolean useCDM) throws RdsException {

        //process param
        Boolean isDisasterRestore = BakSupport.getAndCheckIsDisasterRestore(actionParams);
        String restoreType =  mysqlParamSupport.getParameterValue(actionParams, ParamConstants.RESTORE_TYPE, RESTORE_TYPE_BAKID);
        boolean isRestoreByTime = BakSupport.isRestoreByTime(restoreType);

        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());

        // 库表恢复从当前实例获取主可用区信息
        AVZInfo avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);

        // 获取网络连接
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);
        CustinsConnAddrDO defaultCustinsConnAddr = ConnAddrSupport.getCustinsConnAddrDefaultWithoutVPC(custinsConnAddrList);


        CustInstanceDO tempCustins = custins.clone();

        if(!useCDM) {
            long restoreNeedSpace = needSpace * 3 + 2000 * 1024;
            if (custins.getDiskSize() * 1024 < restoreNeedSpace) {
                tempCustins.setDiskSize(restoreNeedSpace / 1024);
            }
            tempCustins.setId(null);
            tempCustins.setClusterName("");
        }
        tempCustins.setStatus(CUSTINS_STATUS_CREATING);
        tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
        tempCustins.setInsType(CUSTINS_INSTYPE_TMP);


        String timestamp = String.valueOf(System.currentTimeMillis());
        String preferCluster;
        String specifyCluster;
        List<CustinsConnAddrDO> tmpCustinsConnAddrList;

        //恢复到子实例
        tempCustins.setInsName("sub" + timestamp + "_" + custins.getInsName());
        tempCustins.setParentId(custins.getId());
        tempCustins.setGmtExpired(DateUtils.addDays(new Date(), 2));

        // TODO 2.7.22 1. 连接串的命名规则变化；2. 连接端口为DbType的默认值；
        Integer specifyNetType;
        if (CustinsParamSupport.hasParameterValue(actionParams, ParamConstants.DB_INSTANCE_NET_TYPE)) {
            specifyNetType = CustinsSupport.getNetType(actionParams);
        }
        // 兼容阶段，如果未传NetType，则取主实例的NetType，仅当主实例仅有一种网络类型时有效。否则报错
        else {
            //子实例不能有多个vip，创建子实例，解除这个限制，只要创建出临时实例即可
                /*if (custinsConnAddrList.size() > 1) {
                    return createErrorResponse(ErrorCode.INVALID_DBINSTANCENETTYPE);
                }*/
            specifyNetType = defaultCustinsConnAddr.getNetType();
        }

        // 非Proxy链路类型实例不支持创建VPC临时实例
        tmpCustinsConnAddrList = new ArrayList<CustinsConnAddrDO>();
        // 创建连接地址对象
        String connAddrCust = mysqlParamSupport.getConnAddrCust(
                "sub" + timestamp + "-" + custins.getInsName().replace('_', '-'),
                mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()),
                custins.getDbType());

        // 创建实例连接对象
        String vpcInstanceId = null;
        if (CustinsSupport.isVpcNetType(specifyNetType)) {
            vpcInstanceId = getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID);
            if (vpcInstanceId == null) {
                vpcInstanceId = tempCustins.getInsName();
            }
        }
        CustinsConnAddrDO tmpcustinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                connAddrCust,
                CustinsSupport.getConnPort(null, tempCustins.getDbType()),
                specifyNetType,
                CustinsValidator
                        .getRealNumber(getParameterValue(actionParams, ParamConstants.TUNNEL_ID),
                                -1),
                getParameterValue(actionParams, ParamConstants.VPC_ID),
                getParameterValue(actionParams, ParamConstants.VSWITCH_ID),
                getParameterValue(actionParams, ParamConstants.IP_ADDRESS),
                vpcInstanceId);

        tmpCustinsConnAddrList.add(tmpcustinsConnAddr);

        // 恢复到子实例允许用户指定Region
        //region = region == null ? clusterService.getRegionByCluster(custins.getClusterName()) : region;

        specifyCluster = null;
        preferCluster = null;

        //调用资源api
        custinsService.createCustInstanceForTrans(custins, tempCustins);

        //为临时实例设置小版本自动升级
        custinsParamService.setCustinsParam(tempCustins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto");

        Integer needInsCount;
        Integer resourceMode = clusterService.getClusterResourceMode(custins.getClusterName());
        // 对机模式下恢复到子实例
        if (CLUSTER_RESOURCE_MODE_PAIR.equals(resourceMode)) {
            needInsCount = instanceList.size();
        } else {
            needInsCount = 1;
        }
        logger.info("allocate node count:"+needInsCount);

        ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, custins.getDbType());
        resourceContainer.setUserId(custins.getUserId());
        resourceContainer.setRequestId(getParameterValue(actionParams, ParamConstants.REQUEST_ID));
        resourceContainer.setClusterName(specifyCluster);
        resourceContainer.setPreferClusterName(preferCluster);
        resourceContainer.setAccessId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID));
        resourceContainer.setOrderId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID));
        resourceContainer.setSourceHost(mysqlParamSupport.getInstanceCurrHostId(actionParams));
        if (CustinsParamSupport.hasParameterValue(actionParams, ParamConstants.REQUEST_ID)) {
            resourceContainer.setRequestId(getParameterValue(actionParams, ParamConstants.REQUEST_ID));
        }
        CustinsResModel custinsResModel = new CustinsResModel(tempCustins.getId());
        custinsResModel.setConnType(custins.getConnType());
        custinsResModel.setPreferProxyGroupId(custins.getProxyGroupId());
        // init host ins res model
        HostinsResModel hostinsResModel = new HostinsResModel(tempCustins.getLevelId());
        hostinsResModel.setHostType(instanceList.get(0).getHostType());
        hostinsResModel.setInsCount(needInsCount);
        hostinsResModel.setDiskSizeSold(tempCustins.getDiskSize());
        // get disk size used
        // 磁盘是超卖的。但是只读实例和克隆实例的场景，还有实例迁移的场景，分配出来立刻就会有大量数据写上去, 用来预占空间的
        Long cusDiskSize = this.getDiskSizeUsed(custins);
        if (!isRestoreByTime && !isDisasterRestore) {
            Long backupSetID = CheckUtils.parseLong(getParameterValue(actionParams, "BackupSetID"), null,
                    null, ErrorCode.BACKUPSET_NOT_FOUND);
            Long bakSetDiskSize = this.getBakSetDiskSizeUsed(custins, backupSetID);
            if (bakSetDiskSize != null && bakSetDiskSize != -1 && bakSetDiskSize > cusDiskSize) {
                hostinsResModel.setDiskSizeUsed(bakSetDiskSize);
            } else {
                hostinsResModel.setDiskSizeUsed(cusDiskSize);
            }
        } else {
            hostinsResModel.setDiskSizeUsed(cusDiskSize);
        }

        // init distribute rule
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
        distributeRule.setCabinetDistributeMode(DistributeMode.FORCE_SCATTER);
        distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
        distributeRule.setSpecifyHostIdSet(CustinsParamSupport.getAndCheckHostIdSet(actionParams));
        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        InsLevelExtraInfo.updateDistributeRule(distributeRule, instanceLevel.getExtraInfo());

        // excludeLocal = true 去本机
        for (InstanceDO instance : instanceList) {
            distributeRule.addExcludeHostId(instance.getHostId());
        }
        hostinsResModel.setDistributeRule(distributeRule);

        // 如果dns,需要前后端口一致
        if (custins.isDns()) {
            PortDistributeRule portDistributeRule = new PortDistributeRule();
            Set<Integer> ports = new HashSet<>(1);
            ports.add(instanceList.get(0).getPort());
            portDistributeRule.setSpecifyPortSet(ports);
            hostinsResModel.setPortDistributeRule(portDistributeRule);
        }

        custinsResModel.setHostinsResModel(hostinsResModel);
        // 库表恢复临时实例，不需要vip，避免slb 用尽
//        for (CustinsConnAddrDO custinsConnAddr : tmpCustinsConnAddrList) {
//            VipResModel vipResModel = new VipResModel(defaultCustinsConnAddr.getNetType());
//            vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
//            vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
//            vipResModel.setVip(custinsConnAddr.getVip());
//            vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
//            vipResModel.setVpcId(custinsConnAddr.getVpcId());
//            vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
//            vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
//            vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
//            custinsResModel.addVipResModel(vipResModel);
//        }

        if (mysqlParamSupport.isMysqlXDBByLevel(instanceLevel)) {
            // XDB v7 机型CPU对齐
            resourceContainer.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
        }

        resourceContainer.addCustinsResModel(custinsResModel);

        Response<AllocateResRespModel> response = resManagerService.allocateRes(resourceContainer);
        if (!response.getCode().equals(200)) {
            custinsService.deleteCustInstance(tempCustins);
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }
        AllocateResRespModel.CustinsResRespModel custinsResRespModel = response.getData().getCustinsResRespModelList()
                .get(0);

        tempCustins.setClusterName(custinsResRespModel.getClusterName());
        tempCustins.setConnType(custinsResModel.getConnType());
        return tempCustins;
    }

    protected Long getDiskSizeUsed(CustInstanceDO custins) {
        try {
            InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
            return new BigDecimal(instancePerf.getDiskCurr()).longValue();
        } catch (Exception e) {
            logger.warn("Get instance perf failed for custins: " + custins.getId());
        }
        return custins.getDiskSize();
    }

    protected Long getBakSetDiskSizeUsed(CustInstanceDO custins, Long backupSetID) throws RdsException {
        BakhistoryDO history = bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetID);
        Long diskSize = null;
        JSONObject jsonObject = JSONObject.parseObject(history.getBaksetInfo());
        diskSize = jsonObject.getLongValue("disk_size");
        return diskSize;
    }
}

