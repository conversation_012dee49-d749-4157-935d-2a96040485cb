package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.BlueGreenDeploymentCommonService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.utils.ParameterHelper;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCreateBlueGreenDeploymentImpl")
public class CreateBlueGreenDeploymentImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.CreateBlueGreenDeploymentImpl.class);
    @Resource
    private BlueGreenDeploymentService blueGreenDeploymentService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private BlueGreenDeploymentCommonService commonService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            logger.info("CreateBlueGreenDeploymentImpl start.");
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String aliUid = mysqlParamSupport.getUID(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            logger.info("custins : " + JSON.toJSONString(custins));
            // 处理小版本
            String value = mysqlParamSupport.getParameterValue(params, "minorVersion");
            if (value != null) {
                params.put("minorVersion", value.split("_")[1]);
            }
            regionId = commonService.checkAndCorrectRegionId(regionId, custins);
            // 获取用户的绿色实例修改的配置
            Map<String, Object> newPrimaryConfig = commonService.getNewPrimaryConfigFromParams(params);
            logger.info("newPrimaryConfig : {}", JSON.toJSONString(newPrimaryConfig));
            List<Map<String, Object>> newRoConfig = commonService.getNewRoConfigFromParams(params);
            logger.info("newRoConfig : {}", JSON.toJSONString(newRoConfig));
            List<Map<String, Object>> newNodeConfig = commonService.getNewReplicaConfigFromParams(params);
            logger.info("newNodeConfig : {}", JSON.toJSONString(newNodeConfig));
            Map<String, Object> newProxyConfig = commonService.getNewProxyConfigFromParams(params);
            logger.info("newProxyConfig : {}", JSON.toJSONString(newProxyConfig));
            Map<String, Object> data = blueGreenDeploymentService.createBlueGreenDeployment(requestId, regionId, aliUid, custins, newPrimaryConfig, newRoConfig, newNodeConfig, newProxyConfig);
            logger.info("CreateBlueGreenDeploymentImpl end.");
            return data;
        } catch (RdsException ex) {
            logger.error("CreateBlueGreenDeployment failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("CreateBlueGreenDeployment Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}