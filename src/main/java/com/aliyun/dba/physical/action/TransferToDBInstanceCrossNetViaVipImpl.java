package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import io.kubernetes.client.util.common.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.support.property.ParamConstants.*;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalTransferToDBInstanceCrossNetViaVipImpl")
public class TransferToDBInstanceCrossNetViaVipImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(TransferToDBInstanceCrossNetViaVipImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Resource
    private InstanceService instanceService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    ResourceService resourceService;
    @Autowired
    DbossApi dbossApi;

    @Autowired
    RdsApi rdsApi;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
            RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);

            List<String> transfer_to_binstance_support_uid = resourceService.getResourceRealValueList("TRANSFER_TO_DBINSTANCE_SUPPORT_UID");
            if (Collections.isEmptyCollection(transfer_to_binstance_support_uid)) {
                transfer_to_binstance_support_uid = ImmutableList.of("1647796581073291");
            } else {
                transfer_to_binstance_support_uid = transfer_to_binstance_support_uid.stream().map(
                        s -> Arrays.stream(s.split(",")).map(String::trim).collect(Collectors.toList())).reduce((sl1, sl2) -> {
                    sl1.addAll(sl2);
                    return sl1;
                }).orElse(transfer_to_binstance_support_uid);
            }
            String aliUid = mysqlParameterHelper.getUID();
            if (!transfer_to_binstance_support_uid.contains(aliUid)) {
                throw new RdsException(ErrorCode.USER_PERMISSION);
            }
            List<Map<String, Object>> accounts = dbossApi.queryAccounts(custins.getId(), null, null, 0, 100);
            if (!Collections.isEmptyCollection(accounts.stream().filter(a -> !a.getOrDefault("accountName", "").equals("normandy_default")).collect(Collectors.toList()))) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ACCOUNT_STATUS);
            }
            List<Map<String, Object>> dBs = dbossApi.queryDBs(custins.getId(), null, 0, 100);
            if (!Collections.isEmptyCollection(dBs)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DB_STATUS);
            }

            String sourceDBInstanceName = mysqlParamSupport.getParameterValue(actionParams, "SourceDBInstanceName");
            String sourceRegionId = mysqlParamSupport.getParameterValue(actionParams, "SourceRegionId");
            String sourceDBInstanceConnType = mysqlParamSupport.getParameterValue(actionParams, "SourceDBInstanceConnType");
            String sourceDBInstanceClusterName = mysqlParamSupport.getParameterValue(actionParams, "SourceDBInstanceClusterName");
            String callBackUrl = mysqlParamSupport.getParameterValue(actionParams, "CallBackUrl", "ApiUrl");
            String sourceDBInstanceUid = mysqlParamSupport.getParameterValue(actionParams, "sourceDBInstanceUid");
            String sourceDBInstanceBid = mysqlParamSupport.getParameterValue(actionParams, "sourceDBInstanceBid");
            Date switchTime = mysqlParameterHelper.getAndCheckSwitchTime(DataSourceMap.DATA_SOURCE_DBAAS);
            String switchMode = mysqlParameterHelper.getAndCheckSwitchTimeMode(ParamConstants.SWITCH_TIME_MODE, switchTime, true);

            // 校验原实例是否存在
            CustInstanceDO sourceCustInstance = rdsApi.getCustInstanceByRdsApi(sourceDBInstanceName, sourceDBInstanceBid, sourceDBInstanceUid, sourceRegionId); // raise if NotFound

            // 校验大版本是否相同
            if (!sourceCustInstance.getDbVersion().equals(custins.getDbVersion())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }

            // 修改原实例状态
            Map<String, Object> result = rdsApi.getDataByRdsApi(new HashMap<String, String>() {{
                put("Action", "ModifyDBInstanceStatus");
                put("SourceStatus", "1");
                put("DestStatus", "6");
                put(REGION_ID, sourceRegionId);
                put(DB_INSTANCE_NAME, sourceDBInstanceName);
                put(UID, sourceDBInstanceUid);
                put(USER_ID, sourceDBInstanceBid);
            }}, ParamConstants.PENGINE_ACCESS);
            if (!result.get("DestStatus").equals(6)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_MASTER_DBINSTANCE_STATUS);
            }

            Map<String, Object> taskParam = new HashMap<>();
            // 以下几个参数用于资源申请下沉
            taskParam.put("src_ins_name", sourceDBInstanceName);
            taskParam.put("src_region_id", sourceRegionId);
            taskParam.put("src_region", sourceRegionId);
            taskParam.put("src_custins_conn_type", sourceDBInstanceConnType);
            taskParam.put("src_cluster_name", sourceDBInstanceClusterName);
            taskParam.put("src_uid", sourceDBInstanceUid);
            taskParam.put("src_bid", sourceDBInstanceBid);
            taskParam.put("call_back_url", callBackUrl);
            taskParam.put("switch_info", custinsService.getEffectiveTimeMap(switchMode, switchTime));

            if (!Objects.equals(custins.getStatus(), CUSTINS_STATUS_ACTIVE)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_TRANS, "MIGRATING_FROM_OTHER_REGION_OVERWRITE");

            TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
            List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
            trans.setsHinsid1(insIds.get(0));
            if (insIds.size() > 1) {
                trans.setsHinsid2(insIds.get(1));
            }
            trans.setsCinsReserved(1);
            trans.setdCinsid(custins.getId());
            trans.setdLevelid(custins.getLevelId());
            trans.setdDisksize(custins.getDiskSize());
            trans.setdHinsid1(insIds.get(0));
            if (insIds.size() > 1) {
                trans.setdHinsid2(insIds.get(1));
            }
            trans.setParameter("{}");
            instanceService.createTransList(trans);
            taskParam.put("trans_list_id", trans.getId());

            // 下发任务
            TaskQueueDO taskQueue = new TaskQueueDO(mysqlParameterHelper.getAction(), mysqlParameterHelper.getOperatorId(), custins.getId(),
                    TASK_TYPE_CUSTINS, "transfer_from_other_region", JSON.toJSONString(taskParam));
            taskService.createTaskQueue(taskQueue);

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("DBInstanceName", custins.getInsName());
            data.put("taskId", taskQueue.getId());
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
