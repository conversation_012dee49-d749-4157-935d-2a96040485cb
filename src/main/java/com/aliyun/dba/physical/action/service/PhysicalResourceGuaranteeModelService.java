package com.aliyun.dba.physical.action.service;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.alicloud.apsaradb.resmanager.*;
import com.aliyun.dba.base.common.consts.ResourceScheduleConsts;
import com.aliyun.dba.base.service.ResourceScheduleService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 本地盘适配资源保障模型
 * 相关文档:https://alidocs.dingtalk.com/i/nodes/P7QG4Yx2Jpx4OolYCzA3kz7dJ9dEq3XD?utm_scene=team_space
 * <AUTHOR>
 */
@Service
public class PhysicalResourceGuaranteeModelService {

    private static final LogAgent logger = LogFactory.getLogAgent(PhysicalResourceGuaranteeModelService.class);

    @Resource
    private ResourceScheduleService resourceScheduleService;


    /**
     * 添加资源保障策略(混开策略)到资源请求(resourceContainer)中
     *
     * @param resourceContainer
     */
    public void addResourceGuaranteeModelPolicy(ResourceContainer resourceContainer) {
        //恢复类(如重搭)、新购类(如创建、克隆)场景分别采用不同的资源保障策略, 通过action进行区分, action为空默认跟新购一致
        String mapKey = resourceScheduleService.getResGuaranteeModelMapKey();

        //获取资源策略映射表
        Map<String, String> resGuaranteeLevelMap = resourceScheduleService.getResourceGuaranteeModelPolicyMap(resourceContainer.getUserId(), resourceContainer.getUid(), mapKey);
        if (CollectionUtils.isEmpty(resGuaranteeLevelMap)) {
            logger.info("UserId = {}, resGuaranteeLevelMap is null. Skip.", resourceContainer.getUserId());
            return;
        }

        // 在 ResourceContainer.HostLevelDistributeRule 中添加机型策略
        HostLevelDistributeRule hostLevelDistributeRule = resourceContainer.getHostLevelDistributeRule();
        if (hostLevelDistributeRule == null) {
            hostLevelDistributeRule = new HostLevelDistributeRule();
        }
        hostLevelDistributeRule.setResourceGuaranteeLevel(resGuaranteeLevelMap.get(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL));
        hostLevelDistributeRule.setResourceGuaranteeLevelBackup(resGuaranteeLevelMap.get(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL_BACKUP));
        logger.info("UserId = {}, set ResourceGuaranteeLevel: {}, set ResourceGuaranteeLevelBackup: {}.", resourceContainer.getUserId(),
                hostLevelDistributeRule.getResourceGuaranteeLevel(), hostLevelDistributeRule.getResourceGuaranteeLevelBackup());
        resourceContainer.setHostLevelDistributeRule(hostLevelDistributeRule);
    }

    /**
     * 添加资源保障策略(混开策略)到资源请求(upgradeResContainer)中
     *
     * @param upgradeResContainer
     */
    public void addResourceGuaranteeModelPolicy(UpgradeResContainer upgradeResContainer) {
        // upgradeResContainer为变配使用, 不判断action
        Map<String, String> resGuaranteeLevelMap = resourceScheduleService.getResourceGuaranteeModelPolicyMap(upgradeResContainer.getUserId(), upgradeResContainer.getUid(),
                ResourceScheduleConsts.ResGuaranteeModelResourceKey.MYSQL_RES_GUARANTEE_MODEL_USER_MAP.getValue());
        if (CollectionUtils.isEmpty(resGuaranteeLevelMap)) {
            logger.info("UserId = {}, resGuaranteeLevelMap is null. Skip.", upgradeResContainer.getUserId());
            return;
        }
        List<UpgradeCustinsResModel> upgradeCustinsResModelList = upgradeResContainer.getUpgradeCustinsResModelList();

        // UpgradeResContainer.UpgradeCustinsResModel[*].HostinsResModel.DistributeRule
        for (UpgradeCustinsResModel upgradeCustinsResModel : upgradeCustinsResModelList) {
            HostinsResModel hostinsResModel = upgradeCustinsResModel.getHostinsResModel();
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();

            distributeRule.setResourceGuaranteeLevel(resGuaranteeLevelMap.get(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL));
            distributeRule.setResourceGuaranteeLevelBackup(resGuaranteeLevelMap.get(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL_BACKUP));

            logger.info("UserId = {}, set ResourceGuaranteeLevel: {}, set ResourceGuaranteeLevelBackup: {}.", upgradeResContainer.getUserId(),
                    distributeRule.getResourceGuaranteeLevel(), distributeRule.getResourceGuaranteeLevelBackup());

            hostinsResModel.setDistributeRule(distributeRule);
            upgradeCustinsResModel.setHostinsResModel(hostinsResModel);
        }
    }
}
