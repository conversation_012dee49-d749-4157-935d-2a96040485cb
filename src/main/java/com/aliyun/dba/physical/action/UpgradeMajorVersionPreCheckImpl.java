package com.aliyun.dba.physical.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.upgradeprecheck.UpgradeMajorVersionPreCheckExecutor;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSON;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAction;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalUpgradeMajorVersionPreCheckImpl")
@Slf4j
public class UpgradeMajorVersionPreCheckImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeMajorVersionPreCheckImpl.class);
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected TaskService taskService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private ReplicaSetService replicaSetService;
    @Autowired
    private UpgradeMajorVersionPreCheckExecutor preCheckExecutor;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        try {
            String targetEngineVersion = mysqlParamSupport.getParameterValue(actionParams, "TargetEngineVersion");
            String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID, "");
            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(actionParams);
            preCheckExecutor.execute(custins, replicaSetMeta, targetEngineVersion, requestId);
            //修改实例状态
            String dbInstanceStatusDesc = CustinsState.STATE_MAINTAINING.getComment();
            Integer custinsId = custins.getId();
            custinsService.updateCustInstanceStatusByCustinsId(custinsId, CustinsSupport.CUSTINS_STATUS_TRANS, dbInstanceStatusDesc);
            //下发任务
            Map<String, Object> taskQueueParam = new HashMap<String, Object>();
            taskQueueParam.put("target_major_version", targetEngineVersion);
            String taskKey = "upgrade_major_version_precheck";
            TaskQueueDO taskQueue = new TaskQueueDO(getAction(actionParams), CustinsParamSupport.getOperatorId(actionParams), custins.getId(), TaskSupport.TASK_TYPE_CUSTINS,
                    taskKey, JSON.toJSONString(taskQueueParam));
            taskService.createTaskQueue(taskQueue);
            Integer taskId = taskQueue.getId();
            //返回信息
            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TargetMajorVersion", targetEngineVersion);
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

}
