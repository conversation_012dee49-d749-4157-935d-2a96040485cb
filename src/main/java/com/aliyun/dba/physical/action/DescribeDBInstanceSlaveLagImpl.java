package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.ConnectionUtils;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AesCfb;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.common.ResponseSupport.createUnknownExceptionResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeDBInstanceSlaveLagImpl")
public class DescribeDBInstanceSlaveLagImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(DescribeDBInstanceSlaveLagImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceSlaveLagImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected AccountService accountService;
    @Resource
    protected DbossApi dbossApi;

    private static final Integer CONNECT_STATUS_YES = 1;
    private static final Integer CONNECT_STATUS_NO = 0;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
        RdsException {

        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (!custins.isMysql56()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }
            if (custins.isShare() || !custins.isPrimary()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (CustinsSupport.CUSTINS_STATUS_CREATING.equals(custins.getStatus()) ||
                CustinsSupport.CUSTINS_STATUS_DELETING.equals(custins.getStatus())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            // only valid for the custins which has 3 or more instance.
            Integer instanceId = null;
            if (instanceList.size() > 2) {
                instanceId = CustinsValidator.getRealNumber(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.INSTANCE_ID));
            }
            InstanceDO instanceSlave = null;
            for (InstanceDO instance : instanceList) {
                if (instance.isSlave() && (instanceId == null || instance.getId().equals(instanceId))) {
                    instanceSlave = instance;
                    break;
                }
            }

            if (instanceSlave == null) {
                if (instanceId == null) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
                // can not find the instance specified.
                throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
            }

            List<Integer> custinsIds = new ArrayList<Integer>(1);
            custinsIds.add(custins.getId());
            Map<String, Object> queryCondition = new HashMap<String, Object>();
            queryCondition.put("custinsIds", custinsIds);
            queryCondition.put("bizType", DbsSupport.BIZ_TYPE_SYS);
            queryCondition.put("priviledgeType",
                AccountPriviledgeType.PRIVILEDGE_SYSTEM_AURORA.getValue());
            List<AccountsDO> systemAccountDOs = accountService.getSystemAccountsByCondition(queryCondition);

            Integer connectStatus = CONNECT_STATUS_YES;
            String lastIoTimeStamp = null;
            String lastSqlTimeStamp = null;
            Object[] slaveLagInfo = null;
            try {
                Map<String, Object> slaveLagInfoMap = dbossApi.showSlaveLag(instanceSlave.getCustinsId(), 5000L, 5000L);
                slaveLagInfo = new Object[]{connectStatus, slaveLagInfoMap.get("Last_IO_Timestamp"), slaveLagInfoMap.get("Last_SQL_Timestamp")};
            } catch(Exception e) {
                connectStatus = CONNECT_STATUS_NO;
                logger.error("Can't connect to specified database.", e );
                slaveLagInfo = new Object[]{connectStatus, lastIoTimeStamp, lastSqlTimeStamp};
            }

            Map<String, Object> data = new HashMap<String, Object>();
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.CONNECTION_STATUS, slaveLagInfo[0]);
            data.put(ParamConstants.INSTANCE_ID, instanceSlave.getId());

            if (slaveLagInfo[1] != null) {
                lastIoTimeStamp = (String) slaveLagInfo[1];
            }
            if (slaveLagInfo[2] != null) {
                lastSqlTimeStamp = (String) slaveLagInfo[2];
            }
            data.put(ParamConstants.LOG_SYNC_TIME, lastIoTimeStamp);
            data.put(ParamConstants.DATA_SYNC_TIME, lastSqlTimeStamp);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
