package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.alicloud.apsaradb.resmanager.response.UpgradeResRespModel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.service.TransMysqlDBTaskParam;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.physical.action.service.TransferPhysicalToK8sService;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.aliyun.dba.resource.support.ResourceKey;


import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.aliyun.dba.base.parameter.MysqlParameterHelper.XDB_RO_EXCLUDE_HOST_LEVELS;
import static com.aliyun.dba.base.support.MySQLParamConstants.PARAM_FORCE_REBUILD_IN_SITE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.entity.CustinsState.STATE_VERSION_TRANSING;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_MASTER;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_SLAVE;
import static com.aliyun.dba.physical.action.support.StorageCompressionHelper.COMPRESSION_MODE_OFF;
import static com.aliyun.dba.physical.action.support.StorageCompressionHelper.COMPRESSION_MODE_ON;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.CUSTINS_PARAM_NAME_IS_POLARX_HATP;
import static com.aliyun.dba.support.property.ParamConstants.ENGINE_VERSION;
import static com.aliyun.dba.support.property.ParamConstants.TARGET_DB_INSTANCE_CLASS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalModifyDBInstanceClassImpl")
public class ModifyDBInstanceClassImpl implements IAction {

    //private static Logger logger = Logger.getLogger(ModifyDBInstanceClassImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceClassImpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected DbmxsCustinsService dbmxsCustinsService;
    @Autowired
    protected CustinsIDao custinsIDao;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected MysqlParamGroupHelper mysqlParamGroupHelper;
    @Autowired
    protected InstanceIDao instanceIDao;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    private TransferPhysicalToK8sService transferPhysicalToK8sService;
    @Autowired
    private ResourceSupport resourceSupport;

    @Autowired
    protected UserService userService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    private DescribeDBInstanceStorageCompressionImpl describeDBInstanceStorageCompression;
    @Resource
    protected MysqlParamSupport paramSupport;

    @Resource
    protected StorageCompressionHelper storageCompressionHelper;

    private static String dnsIgnoreIntranetVipKey  = "dnsIgnoreIntranetVipKey";

    /**
     * 1.灾备实例为老的逻辑，可以不考虑
     * 2.迁移分为两种：升级，迁移
     * 3.升级分为规格升级（执行规格和当前规格不一致）和磁盘升级（规格相同，磁盘不同）两种
     * */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            //ModifyDBInstanceClass为false，MigrateDBInstance为true
            boolean isTransfer = CustinsSupport.isTransfer(getAction(params));
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            String uid = getParameterValue(params, ParamConstants.UID);

            //如果实例是开启压缩能力
            String compressionMode = getParameterValue(params, "compressionMode");
            boolean currentCompressionOn = storageCompressionHelper.isCompressionOn(custins.getId());
            if (StringUtils.isNotEmpty(compressionMode)) {
                if (currentCompressionOn && StringUtils.equalsIgnoreCase(compressionMode, COMPRESSION_MODE_OFF) ) {
                    logger.error("ins_name:{} not support disable compression", custins.getInsName());
                    return createErrorResponse(ErrorCode.INVALID_ACTION);
                }
                if (!currentCompressionOn && compressionMode.equals(COMPRESSION_MODE_ON)) {
                    boolean supportCompression = describeDBInstanceStorageCompression.isSupportCompression(uid, replicaSet);
                    if (!supportCompression) {
                        logger.error("ins_name:{} not support compression", custins.getInsName());
                        return createErrorResponse(ErrorCode.INVALID_ACTION);
                    }

                    CustInstanceDO custinsCompressionDo  = custinsService.getCustInstanceByInsName(null, custins.getInsName());

                    Double compressionRatio = getDefaultCompressionRatio(custins.getRegionId(), uid);

                    setInstanceCompression(custins, "on", uid, custins.getDiskSize());

                    logger.info("ins_name:{} disksize:{} compression_ratio:{}", custins.getInsName(), custins.getDiskSize(), compressionRatio );
                    custinsCompressionDo.setDiskSize( (long)(custins.getDiskSize() * compressionRatio) );
                    custinsService.updateCustInstance(custinsCompressionDo);

                    List<InstanceDO> instanceList = instanceService.getInstanceByCustinsIds(Collections.singletonList(custins.getId()));
                    for (InstanceDO instanceDo : instanceList) {
                        instanceService.updateInstanceDiskSizeByInsId(instanceDo.getId(), (int)(custins.getDiskSize() * compressionRatio));
                    }

                    custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_TRANS,
                            CustinsState.STATE_OPEN_COMPRESSION_TRANSING.getComment());

                    //下发开启压缩的任务任务
                    Map<String, Object> taskQueueParam = new HashMap<String, Object>();
                    String taskKey = "mysql_set_compression";
                    TaskQueueDO taskQueue = new TaskQueueDO(getAction(params), CustinsParamSupport.getOperatorId(params), custins.getId(), TaskSupport.TASK_TYPE_CUSTINS,
                            taskKey, JSON.toJSONString(taskQueueParam));
                    taskService.createTaskQueue(taskQueue);
                    Integer taskId = taskQueue.getId();
                    //返回信息
                    Map<String, Object> data = new HashMap<String, Object>(4);
                    data.put("TaskId", taskId);
                    return data;
                }
            }

            // 判断是否是本地盘变k8s高可用
            if (transferPhysicalToK8sService.isPhysicalToK8s(custins, params)) {
//                transferPhysicalToK8sService.validatePhysicalToK8s(custins, params);
                return transferPhysicalToK8sService.doActionRequest(custins, params);
            }
            //判断主实例状态是否支持变配
            if (custins.getInsType() == 0 && !custins.isActive() && custins.getStatus() == CustinsState.STATE_READINS_TRANSING.getState()) {
                logger.error("master configuration changes error, error from read-only instance status does not support, please check !");
                return createErrorResponse(ErrorCode.UNSUPPORTED_READ_OR_BAKREAD_STATE, "Primary instance status is read-only migrating,The current read-only instance status does not support configuration changes.");
            }
            // 切换只读实例任务流会下发升降级的任务..现在ha加了状态,升降级判断实例状态..要放行这个状态
            // 暂时在custins中增加判断readswitch 拆分业务第二阶段再做改造
            if (!custins.isActive() && !mysqlParamSupport.isReadSwitch(custins)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // 不支持目标规格是集群版系列
            String targetLevelCode = mysqlParamSupport.getParameterValue(params, TARGET_DB_INSTANCE_CLASS);
            if (StringUtils.isNotEmpty(targetLevelCode)) {
                InstanceLevelDO targetLevel = instanceService.getInstanceLevelByClassCode(targetLevelCode, custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
                if (targetLevel.isClusterLevel()) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
            }


            boolean isPolarxHatp = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP) != null;

            CustInstanceDO primaryins = null;
            CustInstanceDO guardins = null;
            CustInstanceDO backupReadins = null;
            String srcDbmxsName = null;
            String dstDbmxsName = null;
            String isSingleTenant = null;
            Integer resProxyGroupId = null;
            List<RdsResModel> rdsResModelList = new ArrayList<>();

            // 如果当前实例为逻辑主实例，则获取其逻辑灾备实例，便于后面判断含有灾备实例情况进行版本升级的合法性。
            if (custins.isLogicPrimary()) {
                guardins = custinsService.getGuardInstanceByPrimaryCustinsId(custins.getId());
            }
            // 如果当前实例为逻辑灾备实例、只读实例或只读实例备节点，获取其primary_custins_id指向的实例
            if (custins.getPrimaryCustinsId() != null && custins.getPrimaryCustinsId() > 0) {
                primaryins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            }

            //主实例状态在 {只读维护，只读升降级,大版本升级}允许并行迁移，其他状态报错
            if (primaryins != null && !primaryins.isActive() && !primaryins.isReadMAorReadTR() && !STATE_VERSION_TRANSING.getComment().equals(primaryins.getStatusDesc())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_MASTER_DBINSTANCE_STATUS);
            }
            //当前实例为灾备实例且主实例被锁定
            if (primaryins != null && primaryins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            // 跨可用区迁移是否保留源vip
            boolean isPreserveOldLink = mysqlParamSupport.isPreserveOldLink(params);
            // 保留vip目前只支持lvs
            if (isPreserveOldLink && (custins.isProxy() || custins.isDns())) {
                logger.error("proxy or dns not support preserve old link");
                return createErrorResponse(ErrorCode.UNSUPPORTED_CONNTYPE);
            }

            // 获得region以及旧region
            String region = avzSupport.getMainLocation(params);
            String oldRegion = avzSupport.getCustInstanceMainLocation(custins.getId());

            String specifyCluster = getParameterValue(params, ParamConstants.CLUSTER_NAME);
            String storage = getParameterValue(params, ParamConstants.STORAGE);
            Set<Integer> specifyHostIdSet = CustinsParamSupport.getAndCheckHostIdSet(params);
            String dbVersion = custinsService.getDBVersion(custins.getDbType(), getParameterValue(params, ENGINE_VERSION));
            if (dbVersion == null) {
                dbVersion = custins.getDbVersion();
            }
            String levelCode = null;
            boolean isAcrossRegion;
            boolean isMaxscale = false;

            // 获取是否使用了Maxscale
            if (custins != null) {
                List<CustinsServiceDO> maxscale = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
                isMaxscale = !CollectionUtils.isEmpty(maxscale);
            }

            //获取版本，升级情况下，不是迁移
            if (!isTransfer) {
                //获取升级规格和存在，如果新规格和原规格一致，则是升级磁盘
                levelCode = getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
                storage = getParameterValue(params, ParamConstants.STORAGE);
            }

            //跨region迁移
            if (!StringUtils.isBlank(region) && !oldRegion.equals(region)) {
                // 设置跨 Region 标记
                isAcrossRegion = true;
                // 如果实例为灾备实例或存在灾备实例，则需确保跨Region迁移后灾备实例与主实例不在同一Region
                if (custins.isLogicPrimary() && guardins != null) {
                    String guardRegion = clusterService.getRegionByCluster(guardins.getClusterName());
                    if (region.equals(guardRegion)) {
                        return createErrorResponse(ErrorCode.INVALID_REGION);
                    }
                }
                if (custins.isLogicGuard() && primaryins != null) {
                    String primaryRegion = clusterService.getRegionByCluster(primaryins.getClusterName());
                    if (region.equals(primaryRegion)) {
                        return createErrorResponse(ErrorCode.INVALID_REGION);
                    }
                }

                // 如果当前实例是只读实例或只读实例备节点, 且启用了 BLS 复制器, 需要阻止跨 Region 迁移 (FIXME: BLS 支持跨 Region 移除)
                if (custins.isReadOrBackup() && custins.isMysql()) { // 目前只有 MySQL 只读实例启用 BLS 复制器
                    CustinsParamDO param = custinsParamService.getCustinsParam(custins.getId(),
                            CustinsParamSupport.CUSTINS_PARAM_NAME_READ_INS_USING_REPLICATOR);
                    if (param != null && CustinsParamSupport.CUSTINS_PARAM_VALUE_READ_INS_USING_REPLICATOR_YES.equals(param.getValue())) {
                        return createErrorResponse(ErrorCode.CROSS_REGION_TRANS_NOT_ALLOWED);
                    }
                }
            }
            else {
                region = oldRegion;
                isAcrossRegion = false;
            }

            AVZInfo avzInfo = getTargetAvzInfo(params);
            ParamConstants.DispenseMode custinsCurMode = custinsParamService.getDispenseMode(custins.getId());

            // 仅备用区变化，进行备库重搭
            // 实例原来就是主可用区模式/单机房组合可用区则支持重搭，否则不支持，需要走临时实例迁移
            // todo 这里需支持只读m-s重搭迁移
            if (!isAcrossRegion && null == levelCode && null == storage && custins.getDbVersion().equals(dbVersion)) {
                AvailableZoneInfoDO targetSlaveZoneInfo = getTargetSlaveZoneInfo(avzInfo);
                if (null != targetSlaveZoneInfo && ParamConstants.DispenseMode.MultiAVZDispenseMode.equals(custinsCurMode)) {
                    return mazMigrateWithRebuildSlave(custins, params, targetSlaveZoneInfo, custinsCurMode);
                } else if (null != targetSlaveZoneInfo && null!=avzInfo
                        && ParamConstants.DispenseMode.ClassicDispenseMode.equals(custinsCurMode)
                        && ParamConstants.DispenseMode.MultiAVZDispenseMode.equals(avzInfo.getDispenseMode())){
                    ClustersDO instanceCluster = clusterService.getClusterByClusterName(custins.getClusterName());
                    //实例为单机房集群的实例，可用做重搭， 多机房的实例必须都进行迁移
                    if (instanceCluster.getIsMultiSite() == 0){
                        return classicToMazMigrateWithRebuildSlave(custins, params, avzInfo, targetSlaveZoneInfo);
                    }
                }
            }

            List<CustinsServiceDO> dbmxsService = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(),
                    CustinsSupport.DB_TYPE_DBMXS);
            //主可用区添加
            if (dbmxsService != null && dbmxsService.size() == 1 && isAcrossRegion){
                CustinsServiceDO custinsServiceDO = dbmxsService.get(0);
                srcDbmxsName = custinsServiceDO.getServiceName();
                Map<String, Object> ret = dbmxsCustinsService.createDBMXSForRdsCustins(custins, false, false, params);
                dstDbmxsName = ret.get("DbmxsInstanceName").toString();
                isSingleTenant = ret.get("IsSingleTenant").toString();
                rdsResModelList = (List<RdsResModel>) ret.get("RdsResModelList");
            }

            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(
                    custins.getId(),
                    null,
                    CustinsSupport.RW_TYPE_NORMAL);

            List<CustinsConnAddrDO> rwSplitConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(
                    custins.getId(),
                    null,
                    RW_TYPE_RW_SPLIT);

            List<VipResModel> vipResModelList = new ArrayList<>();
            if (isAcrossRegion) {
                // vpc实例需要传入vpc信息才能跨可用区
                CustinsConnAddrDO vpcConnAddr = ConnAddrSupport.getVPCConnAddr(custinsConnAddrList);
                if (vpcConnAddr != null) {
                    String vpcId = CheckUtils.checkValidForVPCId(getParameterValue(params, ParamConstants.VPC_ID));
                    String tunnelId = CheckUtils.checkValidForTunnelId(getParameterValue(params, ParamConstants.TUNNEL_ID));
                    String vswitchId = CheckUtils.checkValidForVswitchId(
                            getParameterValue(params, ParamConstants.VSWITCH_ID));
                    String ipaddress = CheckUtils.checkValidForIPAddress(
                            getParameterValue(params, ParamConstants.IP_ADDRESS));
                    String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(
                            getParameterValue(params, ParamConstants.VPC_INSTANCE_ID));

                    VipResModel vipResModel = new VipResModel(vpcConnAddr.getNetType());
                    vipResModel.setUserVisible(vpcConnAddr.getUserVisible());
                    vipResModel.setConnAddrCust(mysqlParamSupport.getConnAddrCust(
                            "tmp" + System.currentTimeMillis() + "-" + custins.getInsName().replace('_', '-'),
                            mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()),
                            custins.getDbType()));
                    vipResModel.setVip(ipaddress);
                    vipResModel.setVport(Integer.valueOf(vpcConnAddr.getVport()));
                    vipResModel.setVpcId(vpcId);
                    vipResModel.setTunnelId(CustinsValidator.getRealNumber(tunnelId, -1));
                    vipResModel.setVswitchId(vswitchId);
                    vipResModel.setVpcInstanceId(vpcInstanceId);
                    vipResModelList.add(vipResModel);
                }
                CustinsConnAddrDO rwSplitVpcConnAddr = ConnAddrSupport.getVPCConnAddr(rwSplitConnAddrList);
                if (rwSplitVpcConnAddr != null) {
                    String vpcId = CheckUtils.checkValidForVPCId(getParameterValue(params, ParamConstants.VPC_ID));
                    String tunnelId = CheckUtils.checkValidForTunnelId(getParameterValue(params, ParamConstants.TUNNEL_ID));
                    String vswitchId = CheckUtils.checkValidForVswitchId(
                            getParameterValue(params, ParamConstants.VSWITCH_ID));
                    String ipaddress = CheckUtils.checkValidForIPAddress(
                            getParameterValue(params, ParamConstants.RW_SPLIT_IP_ADDRESS));
                    String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(
                            getParameterValue(params, ParamConstants.RW_SPLIT_VPC_INSTANCE_ID));

                    VipResModel vipResModel = new VipResModel(rwSplitVpcConnAddr.getNetType());
                    vipResModel.setUserVisible(rwSplitVpcConnAddr.getUserVisible());
                    vipResModel.setConnAddrCust(mysqlParamSupport.getConnAddrCust(
                            "tmprw" + System.currentTimeMillis() + "-" + custins.getInsName().replace('_', '-'),
                            mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()),
                            custins.getDbType()));
                    vipResModel.setVip(ipaddress);
                    vipResModel.setRwType(RW_TYPE_RW_SPLIT);
                    vipResModel.setVport(Integer.valueOf(rwSplitVpcConnAddr.getVport()));
                    vipResModel.setVpcId(vpcId);
                    vipResModel.setTunnelId(CustinsValidator.getRealNumber(tunnelId, -1));
                    vipResModel.setVswitchId(vswitchId);
                    vipResModel.setVpcInstanceId(vpcInstanceId);
                    vipResModelList.add(vipResModel);
                }
            }

            //判断是否是xdb企业版
            boolean isXdb = mysqlParamSupport.isMysqlXdbByCustins(custins);
            boolean isXdbRead = mysqlParamSupport.isXdbReadCustins(custins);

            //规格category=enterprise
            boolean isMysqlEnterpriseCustins = mysqlDBCustinsService.isMysqlEnterprise(custins);
            boolean mysqlCategorySwitch = false;
            InstanceLevelDO targetInsLevel = null;
            if (levelCode != null) {
                //获取目标规格
                targetInsLevel = instanceService.getInstanceLevelByClassCode(
                        levelCode,
                        custins.getDbType(),
                        dbVersion,
                        custins.getTypeChar(),
                        null);
                //目标规格不存在
                if(targetInsLevel == null){
                    throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                }
                if(!isXdb){
                    //56企业版变非企业版
                    mysqlCategorySwitch = targetInsLevel.isMysqlEnterprise() ^ isMysqlEnterpriseCustins;
                }
                //不支持xdb企业版变为非企业版
                if(isXdb && !InstanceSupport.CATEGORY_ENTERPRISE.equalsIgnoreCase(targetInsLevel.getCategory())){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
                //5.7,8.0高可用升三节点
                InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
                if ((CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(custins.getDbVersion())
                        ||CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(custins.getDbVersion()))
                        && InstanceSupport.CATEGORY_STANDARD.equals(oldLevel.getCategory())
                        && InstanceSupport.CATEGORY_ENTERPRISE.equals(targetInsLevel.getCategory())
                        && CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(targetInsLevel.getDbType())
                        && (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(targetInsLevel.getDbVersion())
                        ||CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(targetInsLevel.getDbVersion()))) {
                    if (!mysqlEngineCheckService.checkCanUpgradeToXDB(custins)){
                        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                    }
                    mysqlCategorySwitch = true;
                }
            }

            if (mysqlCategorySwitch) {
                // Mysql 5.6 2升3 不支持 maxscale
                if (CustinsSupport.DB_VERSION_MYSQL_56.equalsIgnoreCase(custins.getDbVersion()) && isMaxscale) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                if (guardins != null) {
                    //有灾备实例的目前不允许系列切换
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
            }

            boolean isVersionTrans = false;//是否跨版本
            if (!dbVersion.equals(custins.getDbVersion())) {
                Integer versionTrans = dbVersion.compareTo(custins.getDbVersion());
                if (versionTrans != 0) {
                    isVersionTrans = true;
                }
                if (versionTrans < 0) {//只能升版本
                    return createErrorResponse(ErrorCode.INVALID_ENGINEVERSION);
                }
                if (versionTrans > 0) {
                    if (custins.isShare()) {
                        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                    }
                    // 对于拥用灾备实例的情况，若要升级逻辑主实例的版本，必须先升级逻辑灾备实例。
                    if (guardins != null && custins.isLogicPrimary() && dbVersion.compareTo(guardins.getDbVersion()) > 0) {
                        return createErrorResponse(ErrorCode.UNSUPPORTED_GUARD_DBINSTANCE_VERSION);
                    }
                }
            }

            //企业版都不支持大版本升级
            if (isVersionTrans && isMysqlEnterpriseCustins) {
                //企业版实例不支持版本升级
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // mysql大版本升级，只有55升56 是通过变配实现，其他56To57 和 57To80 是通过UpgradeDBMajorVersion实现
            boolean isMysql55To56 = custins.isMysql55() && "5.6".equals(dbVersion);
            if (isVersionTrans && !isMysql55To56) {
                return createErrorResponse(ErrorCode.INVALID_ENGINEVERSION);
            }

            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            InstanceLevelDO newLevel = null;
            //没有传level_code，则规格不变
            if (Validator.isNull(levelCode)) {
                levelCode = oldLevel.getClassCode();
                if (isVersionTrans) {
                    //如果是大版本升级，则查询到对应大版本的规格
                    newLevel = instanceService.getInstanceLevelByClassCode(
                            levelCode,
                            custins.getDbType(),
                            dbVersion,
                            custins.getTypeChar(),
                            null);
                } else {
                    newLevel = oldLevel;
                }
            } else {
                newLevel = instanceService.getInstanceLevelByClassCode(
                        levelCode,
                        custins.getDbType(),
                        dbVersion,
                        custins.getTypeChar(),
                        null);
            }
            if (newLevel == null) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
            }

            //InnerRDS limit instance level scope
            if(!newLevel.getClassCode().equals(oldLevel.getClassCode()) && mysqlParaHelper.isInnerRDS(custins.getId())
                    && !mysqlParaHelper.isInstanceLevelSupportInnerRDS(newLevel.getExtraInfo())){
                return createErrorResponse(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
            }
            // use remote trans type when upgrade minor version with modify db instance class
            String minorVersion = null;
            if(Objects.nonNull(getParameterValue(params, "MinorVersion"))){
                //String dbType, String dbVersion, String classCode, String dbEngine, String targetMinorVersion
                minorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(newLevel.getDbType(), newLevel.getDbVersion(), newLevel.getClassCode(),
                        MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL, getParameterValue(params, "MinorVersion"));
            }

            if (newLevel.getHostType() == 2) {
                //暂时不支持升级到ecs规格
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            Long diskSize;
            if (currentCompressionOn) {
                diskSize = getAndCheckDiskSizeWithCompression(custins, storage, getAndCheckBizType(params), storageCompressionHelper.getCustinsCompressionRatio(custins.getId()));
            }else{
                diskSize = custinsService.getAndCheckDiskSize(custins, storage, getAndCheckBizType(params));

            }

            //校验UTC时间，返回带时区的日期
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(params);
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(params, utcDate, true);

            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            //判断是否为 只读双节点
            boolean isReadInsStandard = custins.isRead() && instanceList.size() == 2;

            //小版本升级且不跨region迁移，才会本地升级
            boolean isLocalTrans = !isVersionTrans;
            // 若跨版本或跨Region则都不进行本地升降级
            isLocalTrans = isLocalTrans && !isAcrossRegion;
            //是否指定了强制本地升级
            boolean forceLocalTrans = getParameterValue(params, "ForceLocalTrans", "0").equals("1");

            //对于只读单节点实例的变配，统一走跨机迁移， 只读双节点对齐主实例
            if (isLocalTrans && custins.isReadOrBackup() && !forceLocalTrans) {
                if (custins.isRead() && !newLevel.getId().equals(oldLevel.getId()) && !isReadInsStandard) {
                    isLocalTrans = false;
                }
                String forceTransKey = getParameterValue(params, "ForceTransKey");
                //对于只读实例，由于后端运维需求，会调用此接口进行对只读实例进行强制跨机迁移。
                if (forceTransKey != null && FORCE_TRANS_KEY.equals(forceTransKey)) {
                    isLocalTrans = false;
                }
            }

            //本机升降级涉及套餐调整的需要检查slave是否宕机，空间不需要; 对与只有单个主机节点的实例不需要（如只读实例）
            // 只读双节点对齐主实例需要检查一下slave的状态
            if (isLocalTrans && !newLevel.getId().equals(oldLevel.getId()) && !custins.isReadOrBackup()) {
                if (!instanceService.checkSlaveInstanceHealth(custins.getId())) {
                    isLocalTrans = false;
                }
            } else if (isLocalTrans && !newLevel.getId().equals(oldLevel.getId()) && isReadInsStandard){
                if (!instanceService.checkSlaveInstanceHealth(custins.getId())) {
                    isLocalTrans = false;
                }
            }

            // 判断迁移类型
            String specifyTransType = CustinsParamSupport.getAndCheckTransType(params);
            boolean is_local_limit_trans_type = false;
            if (Objects.equals(specifyTransType, CUSTINS_TRANS_TYPE_LOCAL_LIMIT)) {
                // 紧急变配，仅限实例已锁定场景使用，限制最大扩容100G
                long upgradeDiff = diskSize - custins.getDiskSize();
                if (upgradeDiff > CustinsSupport.LOCAL_UPGRADE_DISK_LIMIT_MAX_SIZE * 1024) {
                    return createErrorResponse(ErrorCode.INVALID_STORAGE, "force local extend disk storage size too large");
                }
                if (!mysqlParamSupport.hasLockTaskInLastHours(custins, 12)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_TRANS_TYPE, "Force local disk extension must execute only if the instance has a lock task within the last 24 hours.");
                }
                specifyTransType = CUSTINS_TRANS_TYPE_LOCAL;
                is_local_limit_trans_type = true;
            }

            // 开放白名单，支持TRANS_TYPE_LOCAL_REMOTE全本地或全跨机，禁止半跨机
            specifyTransType = checkAndSetTransTypeLocalRemote(custins.getInsName(), uid, specifyTransType);

            // 企业版变非企业版只能跨机迁移
            if (mysqlCategorySwitch) {
                specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
            }

            //不能本地升级，且指定了本地升级，则提示不支持
            if (!isLocalTrans) {
                if (CustinsSupport.CUSTINS_TRANS_TYPE_LOCAL.equals(specifyTransType)) {
                    // 校验是否支持强制本地升降级
                    return createErrorResponse(ErrorCode.UNSUPPORTED_TRANS_TYPE);
                } else {
                    specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
                }
            }

            // 迁移可用区不进行重搭的情况， 都走跨机
            if (mysqlParamSupport.isMigrateZone(params)){
                specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
            }

            // use remote trans type when upgrade minor version with modify db instance class
            if(Objects.nonNull(minorVersion)){
                specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
            }

            //主可用区添加
            Boolean isSameAvz = false;

            AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            if (avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.ClassicDispenseMode) &&
                    oldAvzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
                //可用区迁移时，如果不传 ZoneIdSlave1 参数，瑶池下发请求DispenseMode=0，将导致迁移失败，此处进行拦截
                if (isAcrossRegion) {
                    logger.error("dispense mode error, may be error from param ZoneIdSlave1, please check !!");
                    return createErrorResponse(ErrorCode.INVALID_AVZONE);
                }
                avzInfo = oldAvzInfo;
                isSameAvz = true;
            }
            if(!avzInfo.isValidForModify()){
                avzInfo = oldAvzInfo;
                isSameAvz = true;
            }

            //UpgradeResContainer container = new UpgradeResContainer(region);
            UpgradeResContainer container = avzSupport.getUpgradeResContainer(avzInfo);
            container.setRequestId(getParameterValue(params, ParamConstants.REQUEST_ID));
            container.setClusterName(specifyCluster);
            container.setPreferClusterName(custins.getClusterName());
            container.setAccessId(CustinsParamSupport.getParameterValue(params, ParamConstants.ACCESSID));
            container.setOrderId(CustinsParamSupport.getParameterValue(params, ParamConstants.ORDERID));
            container.setSourceHost(mysqlParamSupport.getInstanceCurrHostId(params));
            container.setUserId(custins.getUserId());

            // init custins res model
            UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
            custinsResModel.setCustinsId(custins.getId());

            List<CustinsServiceDO> maxscaleService = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(),
                    CustinsSupport.DB_TYPE_MAXSCALE);
            if (maxscaleService != null && maxscaleService.size() > 0) {
                custinsResModel.setNeedProxyGroup(false);
            }
            // init host ins res model
            String resourceStrategy = mysqlParamSupport.getResourceStrategy(params);
            HostinsResModel hostinsResModel = new HostinsResModel(newLevel.getId());
            hostinsResModel.setTransType(Integer.valueOf(specifyTransType));
            if (is_local_limit_trans_type) {
                hostinsResModel.setLocalUpgradeLimit(true);
                // 设置本地变配磁盘水位放到最大
            }

            InstanceDO instance = instanceList.get(0);
            if (isXdb) {
                instance = instanceList.stream().filter((e) -> e.getRole() != null && e.getRole() <= INSTANCE_ROLE_SLAVE).findAny().get();
            }
            hostinsResModel.setHostType(instance.getHostType());
            hostinsResModel.setInsCount(instanceList.size());
            if (resourceStrategy != null) {
                hostinsResModel.setStrategy(resourceStrategy);
            }
            if (CUSTINS_TYPE_SHARE.equals(instance.getType())) {
                hostinsResModel.setType(instance.getType());
            }
            if (mysqlCategorySwitch) {
                //企业版和主从版发生切换
                Integer nodeCount = newLevel.getInsCount();
                if (!isMysqlEnterpriseCustins) {
                    //变成 多节点
                    nodeCount = nodeCount == null ? 3 : nodeCount;
                    hostinsResModel.setInsCount(nodeCount);
                } else {
                    //变成 主从版, 兼容环境规格问题
                    hostinsResModel.setInsCount(2);
                }
            }

            //xdb升级变配，指定主机节点数量为2
            if(isXdb && !custins.isReadOrBackup()){
                hostinsResModel.setInsCount(3);
            }

            if (mysqlCategorySwitch && !isMysqlEnterpriseCustins && CustinsSupport.isDns(custins.getConnType())) {
                //xdb只给Leader和Follower创建DNS
                int insCount = mysqlParamSupport.isMysqlXDBByLevel(newLevel) ? 2 : hostinsResModel.getInsCount();
                for (int i = 0; i < insCount; ++i) {
                    String connAddrNode = mysqlParamSupport.getConnAddrCust(custins.getInsName() + "-" + (i + 1),mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()), custins.getDbType());
                    CustinsConnAddrDO custinsConnAddr = ConnAddrSupport
                            .createCustinsConnAddr(connAddrNode, String.valueOf(instance.getPort()),
                                    custins.getNetType(), -1, null, null, null, null);
                    custinsConnAddr.setInsId(-1);
                    VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
                    vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
                    vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
                    vipResModel.setVip(custinsConnAddr.getVip());
                    vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
                    vipResModel.setVpcId(custinsConnAddr.getVpcId());
                    vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
                    vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
                    vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
                    vipResModel.setInstanceId(custinsConnAddr.getInsId());
                    vipResModelList.add(vipResModel);
                }
            }

            /*
               申请变配的镜像实例资源时，resManager 默认会申请和源实例一致的网络信息。
               由于线上经典网络库存不足，因此如果xdb实例拥有经典网络，申请资源时则需要告诉resManager不要申请经典网络SLB；
               但是如果是跨可用区变配，必须申请和源实例一致的网络信息，否则会导致任务流切换LB失败；
               建议经典网络实例先切换VPC实例之后再跨可用区迁移。
             */
            if ((isXdb || isXdbRead) && !isAcrossRegion) {
                Optional<CustinsConnAddrDO> privateAddr = custinsConnAddrList.stream()
                        .filter(custinsConnAddrDO -> NET_TYPE_PRIVATE.equals(custinsConnAddrDO.getNetType()))
                        .findAny();

                if (privateAddr.isPresent()) {
                    custinsResModel.setIgnoreIntranetVip(true);
                }
            }

            // 如果是备用只读 不申请经典网络 或者 如果不是跨可用区场景 不要申请经典网络
            if (custins.getInsType() == CustInsType.CUST_INS_TYPE_READ_BACKUP.getValue()){
                custinsResModel.setIgnoreIntranetVip(true);
            }

            /*
             * 变配忽略申请经典网络的条件
             * 1. 没有跨可用区
             * 2. lvs实例
             * 3. dns链路
             */
            if (!isAcrossRegion && custins.isLvs() ) {
                logger.info("lvs instance ignore IntranetVip custins name {} request_id {}",custins.getInsName(), getParameterValue(params, ParamConstants.REQUEST_ID));
                custinsResModel.setIgnoreIntranetVip(true);
            }
            if (!isAcrossRegion && custins.isDns()) {
                logger.info("dns instance ignore IntranetVip custins name {} request_id {}",custins.getInsName(), getParameterValue(params, ParamConstants.REQUEST_ID));
                custinsResModel.setIgnoreIntranetVip(true);
            }

            hostinsResModel.setDiskSizeSold(diskSize);
            // get disk size used
            Long diskUsage = instanceService.getInstanceDiskUsage(custins, 0);
            hostinsResModel.setDiskSizeUsed(diskUsage);

            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            if (hostinsResModel.getInsCount() > 3) {
                //四节点
                distributeRule.setSiteDistributeMode(DistributeMode.AVG_SCATTER);
            } else {
                distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            }
            distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);

            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setSpecifyHostIdSet(specifyHostIdSet);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, newLevel.getExtraInfo());

            if (isXdbRead) {
                if (Objects.isNull(distributeRule.getExcludeHostLevelNameSet())) {
                    distributeRule.setExcludeHostLevelNameSet(new HashSet<>());
                }
                distributeRule.getExcludeHostLevelNameSet().addAll(Arrays.asList(XDB_RO_EXCLUDE_HOST_LEVELS));

                if (!CollectionUtils.isEmpty(distributeRule.getSpecifyHostLevelNameSet())) {
                    distributeRule.getSpecifyHostLevelNameSet().removeAll(distributeRule.getExcludeHostLevelNameSet());
                }
                logger.info(String.format("actual distributeRule is: %s", JSON.toJSONString(distributeRule)));
            }

            hostinsResModel.setDistributeRule(distributeRule);

            if (custins.isReadOrBackup()) {
                List<Integer> excludeIds = new ArrayList<>();
                excludeIds.add(custins.getPrimaryCustinsId());
                //只读高可用不与只读实例强制打散
                if (!isReadInsStandard){
                    List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(
                            custins.getPrimaryCustinsId(), true);
                    List<Integer> readInsTmpId = custinsService.getReadCustInstanceTmpInsIdByPrimaryCustinsId(custins.getPrimaryCustinsId());
                    if (readInsTmpId.size() != 0){
                        excludeIds.addAll(readInsTmpId);
                    }
                    if (readCustinsList != null && readCustinsList.size() > 0) {
                        for (CustInstanceDO oldReadIns : readCustinsList) {
                            if (!oldReadIns.getId().equals(custins.getId())){
                                excludeIds.add(oldReadIns.getId());
                            }
                        }
                    }
                }
                custinsResModel.setExcludeCustinsIdList(excludeIds);
            } else {
                List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(
                        custins.getId(), true);
                if (readCustinsList != null && readCustinsList.size() > 0) {
                    List<Integer> excludeIds = new ArrayList<>();
                    for (CustInstanceDO oldReadIns : readCustinsList) {
                        excludeIds.add(oldReadIns.getId());
                    }
                    custinsResModel.setExcludeCustinsIdList(excludeIds);
                }
            }
            // 如果dns,需要前后端口一致
            String port = getParameterValue(params, ParamConstants.PORT);
            if (custins.isDns()) {
                PortDistributeRule portDistributeRule = new PortDistributeRule();
                Set<Integer> ports = new HashSet<>(1);
                ports.add(instance.getPort());
                portDistributeRule.setSpecifyPortSet(ports);
                hostinsResModel.setPortDistributeRule(portDistributeRule);
            } else if (!StringUtils.isEmpty(port)) {
                // 由proxy/lvs链路指定切换为dns链路的话，必须保证新实例的端口保证和原vip的vport一致
                PortDistributeRule portDistributeRule = new PortDistributeRule();
                Set<Integer> ports = new HashSet<>(1);
                ports.add(Integer.valueOf(port));
                portDistributeRule.setSpecifyPortSet(ports);
                hostinsResModel.setPortDistributeRule(portDistributeRule);
            }

            custinsResModel.setHostinsResModel(hostinsResModel);
            custinsResModel.setVipResModelList(vipResModelList);
            //主可用区添加
            custinsResModel.setRdsResModelList(rdsResModelList);
            //container.addUpgradeCustinsResModel(custinsResModel);
            container.setV6CpuMatch(true);
            if (isXdb || isXdbRead) {
                // XDB v7 机型CPU对齐
                container.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
            }

            // keep max conn
            hostinsResModel.setMaxConn(instance.getMaxConn());

            logger.info("[ModifyDBInstanceClassImpl] custins " + custins.getInsName() + " specifyTransType: " + specifyTransType);
            Response<UpgradeResRespModel> response = modifyInstanceAllocateRes(custins, isReadInsStandard, specifyTransType,
                    container, hostinsResModel, custinsResModel);
            logger.warn("resApi.upgradeRes,container"+JSONObject.toJSONString(container)+",response:"+JSONObject.toJSONString(response));
            if (!response.getCode().equals(200)) {
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }

            //主可用区添加
            if (custins.getProxyGroupId() == 0){
                resProxyGroupId = response.getData().getCustinsResRespModelList().get(0).getProxyGroupId();
                try{
                    custinsIDao.updateProxyGroupId(custins.getId());
                }catch (Exception e){
                    logger.warn("update proxy group id for custins error", e);
                }
            }
            UpgradeResRespModel upgradeResRespModel = response.getData();

            if (!isSameAvz) {
                // 备份可用区相关字段，如果任务流失败或中断，回滚，确保元数据正确
                if(enableUpdateCustinsParam(custins,getParameterValue(params, ParamConstants.UID))){
                    backupParams(custins,"multi_avz_ex_param");
                    backupParams(custins,"master_location");
                    backupParams(custins,"slave_location");
                    backupParams(custins,"dispense_mode");
                }
                custinsParamService.updateAVZInfo(custins.getId(), avzInfo);
            }

            if (CustinsSupport.ISOLATE_HOST_FIXED.equals(newLevel.getIsolateHost()) &&
                    custinsParamService.getCustinsParam(custins.getId(), MySQLParamConstants.V6_CPU_MATCH) == null) {
                custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(), MySQLParamConstants.V6_CPU_MATCH,
                        MySQLParamConstants.V6_CPU_MATCH_VALUE));
            }

            String inputDesc = getParameterValue(params, "DBInstanceStatusDesc");

            // 本地盘变配过程中可用区对齐
            if (InstanceSupport.CATEGORY_STANDARD.equals(oldLevel.getCategory()) && custins.isMysql() && !isAcrossRegion
                    && !mysqlParamSupport.isMigrateZone(params)) {
                updateDstInstanceRole(upgradeResRespModel, custins.getId(), custins);
            }

            // 迁移可用区直接修改状态为迁移中
            String migratingAvz = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false");
            if (Boolean.parseBoolean(migratingAvz)) {
                inputDesc = CustinsState.STATE_TRANSING.getComment();
            }

            TransMysqlDBTaskParam param = new TransMysqlDBTaskParam.Builder()
                    .withAction(getAction(params))
                    .withOperatorId(getOperatorId(params))
                    .withCustins(custins)
                    .withPrimaryins(primaryins)
                    .withSwitchTime(utcDate)
                    .withDiskSize(diskSize)
                    .withNewLevel(newLevel)
                    .withUpgradeResRespModel(upgradeResRespModel)
                    .withIsTransfer(isTransfer)
                    .withSwitchMode(switchMode)
                    .withInputDesc(inputDesc)
                    .withFromEcsToPhysical(false)
                    .withSrcDbmxsName(srcDbmxsName)
                    .withDstDbmxsName(dstDbmxsName)
                    .withIsSingleTenant(isSingleTenant)
                    .withResProxyGroupId(resProxyGroupId)
                    .withIsPreserveOldLink(isPreserveOldLink)
                    .withIsPolarxHatp(isPolarxHatp)
                    .withMinorVersion(minorVersion)
                    .build();
            //isPolarxHatp=true，临时实例需要打标
            Integer taskId = instanceService.transMysqlDBTask(param);

            // 升级联动修改参数模板
            // 大版本升级需要修改默认系统参数模板
            if (isVersionTrans) {
                mysqlParamGroupHelper.mysqlUpgradeUpdateSysParamGroupVersion(custins, dbVersion);
            }
            // category 升级
            if (mysqlCategorySwitch) {
                String category = targetInsLevel.getCategory();
                // 只读实例的实际category与父实例一致（如xdb只读实例，规格为standard，参数实际上是enterprise）
                if (custins.isReadOrBackup()) {
                    CustInstanceDO primaryIns = custinsService.getAllCustinsByCustinsId(custins.getPrimaryCustinsId());
                    InstanceLevelDO primaryInsLevel = instanceService.getInstanceLevelByLevelId(primaryIns.getLevelId());
                    category = primaryInsLevel.getCategory();
                }
                mysqlParamGroupHelper.mysqlUpgradeUpdateSysParamGroupCategory(custins, category);
            }

            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(params));

            Map<String, Object> data = new HashMap<>(7);
            data.put("MigrationID", 0);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("SourceDBInstanceClass", oldLevel.getClassCode());
            data.put("TargetDBInstanceClass", newLevel.getClassCode());
            data.put("TaskId", taskId);
            data.put("Region", region);
            data.put("SwitchMode", switchMode);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public void backupParams(CustInstanceDO custins,String param){
        CustinsParamDO custinsParam = custinsParamService.getCustinsParam(custins.getId(), param);
        String backupParam=param+"_old";
        if(custinsParam!=null) {
            custinsParamService.setCustinsParam(custinsParam.getCustinsId(), backupParam, custinsParam.getValue());
        }
    }

    public boolean  enableUpdateCustinsParam(CustInstanceDO custins,String uid){
        try{
            ResourceDO uidResourceDO=resourceService.getResourceByResKey("ENABLE_UPDATE_CUSTINS_PARAM");
            ResourceDO regionResourceDO=resourceService.getResourceByResKey("ENABLE_UPDATE_CUSTINS_PARAM_REGION");
            if(uidResourceDO==null || regionResourceDO==null || StringUtils.isBlank(uid)) {
                return false;
            }
            boolean flagUser=false;
            boolean flagRegion=false;
            String uidRealValue=uidResourceDO.getRealValue();
            String regionRealValue=regionResourceDO.getRealValue();
            String[] accessList = uidRealValue.split(",");
            String[] accessRegionList = regionRealValue.split(",");
            if("true".equals(uidRealValue) || Arrays.asList(accessList).contains(uid)){
                flagUser=true;
            }
            String regionId=mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName());
            if("all".equals(regionRealValue)|| Arrays.asList(accessRegionList).contains(regionId)){
                flagRegion=true;
            }
            return flagUser && flagRegion;
        }catch (Exception e){
            logger.error(e);
            return false;
        }

    }

    private Map<String, Object> rebuildSlaveInstance(String instanceId, String dbInstanceName, String userId, String uid, String siteName, String requestId) throws RdsException {
        Map<String, String> rebuildParams = new HashMap<>();
        rebuildParams.put(ParamConstants.ACTION.toLowerCase(), "RebuildSlaveInstance");
        rebuildParams.put(ParamConstants.SLAVE_REBUILD_TYPE.toLowerCase(), SLAVE_REBUILD_TYPE_REMOTE);
        rebuildParams.put(ParamConstants.DB_INSTANCE_NAME.toLowerCase(), dbInstanceName);
        rebuildParams.put(ParamConstants.INSTANCE_ID.toLowerCase(), instanceId);
        rebuildParams.put(ParamConstants.USER_ID.toLowerCase(), userId);
        rebuildParams.put(ParamConstants.UID.toLowerCase(), uid);
        rebuildParams.put(ParamConstants.SITE_NAME.toLowerCase(), siteName);
        rebuildParams.put(ParamConstants.REQUEST_ID.toLowerCase(), requestId);
        rebuildParams.put(PARAM_FORCE_REBUILD_IN_SITE.toLowerCase(), "true");

        logger.info("可用区迁移改写为备库重搭，request: " + JSON.toJSONString(rebuildParams));
        RebuildSlaveInstanceImpl rebuildSlaveInstanceImpl = SpringContextUtil.getBeanByClass(RebuildSlaveInstanceImpl.class);
        return rebuildSlaveInstanceImpl.doActionRequest(null, rebuildParams);
    }

    private AVZInfo getTargetAvzInfo(Map<String, String> params){
        AVZInfo avzInfo = null;
        try {
            avzInfo = avzSupport.getAVZInfo(params);
        } catch (RdsException e) {
            logger.error("parse avzInfo failed", e);
        }
        return avzInfo;
    }

    private AvailableZoneInfoDO getTargetSlaveZoneInfo(AVZInfo avzInfo) {
        MultiAVZExParamDO multiAvzDo = avzInfo.getMultiAVZExParamDO();
        AvailableZoneInfoDO targetSlaveZoneInfo = null;
        if (null != multiAvzDo) {
            for (AvailableZoneInfoDO zoneInfoDO : multiAvzDo.getAvailableZoneInfoList()) {
                if ("slave".equals(zoneInfoDO.getRole())) {
                    targetSlaveZoneInfo = zoneInfoDO;
                    break;
                }
            }
        }
        return targetSlaveZoneInfo;
    }

    private InstanceDO getInstanceDoByRole(Integer custinsId, Integer roleId) {
        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custinsId);

        for (InstanceDO instance: instanceList) {
            if (roleId.equals(instance.getRole())) {
                return instance;
            }
        }
        return null;
    }

    /*
    原逻辑
    都是主可用区模式下， 迁移可用区进行备库重搭场景
    这里将原来的逻辑抽出来，方便维护
     */
    private Map<String, Object> mazMigrateWithRebuildSlave(CustInstanceDO custins,
                                                           Map<String, String> params,
                                                           AvailableZoneInfoDO targetSlaveZoneInfo,
                                                           ParamConstants.DispenseMode custinsCurMode) throws RdsException{
        InstanceDO slaveInstanceDo = getInstanceDoByRole(custins.getId(), INSTANCE_ROLE_SLAVE);
        String targetSiteName = clusterService.getSiteNameByAvzone(targetSlaveZoneInfo.getZoneID());
        if (null == slaveInstanceDo || slaveInstanceDo.getSiteName().equals(targetSiteName)) {
            logger.warn("slave is null or slave is not modify, siteName: {}", targetSiteName);
            return createErrorResponse(ErrorCode.INVALID_AVZONE);
        }

        // 旧可用区模式无法只迁移备库
        if (ParamConstants.DispenseMode.ClassicDispenseMode.equals(custinsCurMode)) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        // 实例当前主可用区与元数据不一致，迁移备库可能导致ha不托管
        // ha后再迁移可能出现该情况，可再次ha恢复，否则需订正元数据
        AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins.getId());
        String masterSiteNameByAvzInfo = clusterService.getSiteNameByAvzone(oldAvzInfo.getMasterZoneId());
        InstanceDO masterInstanceDo = getInstanceDoByRole(custins.getId(), INSTANCE_ROLE_MASTER);
        String masterSiteNameByInstance = masterInstanceDo.getSiteName();
        if (!StringUtils.equalsIgnoreCase(masterSiteNameByAvzInfo, masterSiteNameByInstance)) {
            logger.error("master siteName check error, now is {}, from avzInfo is {}, can't migrate slave, please fix first!", masterSiteNameByInstance, masterSiteNameByAvzInfo);
            throw new RdsException(ErrorCode.INVALID_MULTIPARAM_ZONEINFO_LIST);
        }

        Map<String, Object> result = rebuildSlaveInstance(slaveInstanceDo.getId().toString(),
                custins.getInsName(),
                getParameterValue(params, ParamConstants.USER_ID),
                getParameterValue(params, ParamConstants.UID),
                targetSiteName,
                getParameterValue(params, ParamConstants.REQUEST_ID));

        // 备成功迁移时，改写实例状态，以便用户感知
        if (!result.containsKey("errorCode")) {
            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_TRANS,
                    CustinsState.STATE_SLAVE_INS_TRANSING.getComment());
            custinsParamService.updateAVZInfo(custins.getId(), avzSupport.getAVZInfo(params));
        }
        return result;
    }

    /*
    原实例是组合可用区 迁移到--> 主可用区
    条件： classic -> maz; 原实例的集群为单机房集群， 多机房集群只有走两个节点迁移
    note: 需要先update掉avz信息， 不然重搭可能无法生效到另外的机房； 这样需要做好元数据的回滚（异常 或者 出现error）
     */
    private Map<String, Object> classicToMazMigrateWithRebuildSlave(CustInstanceDO custins,
                                                                    Map<String, String> params,
                                                                    AVZInfo avzInfo,
                                                                    AvailableZoneInfoDO targetSlaveZoneInfo
    ) throws RdsException{
        AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins.getId());
        InstanceDO slaveInstanceDo = getInstanceDoByRole(custins.getId(), INSTANCE_ROLE_SLAVE);
        String targetSiteName = clusterService.getSiteNameByAvzone(targetSlaveZoneInfo.getZoneID());

        // 这里不比较原备节点机房是否一样， 因为存在被调度到多可机房集群的情况，都走重搭
        if (null == slaveInstanceDo) {
            logger.warn("instance slave or master node is null, please check.");
            return createErrorResponse(ErrorCode.INVALID_AVZONE);
        }

        // 先进行更新掉元数据，重搭的时候才能生效调度到其他机房
        custinsParamService.updateAVZInfo(custins.getId(), avzInfo);
        try{
            Map<String, Object> result = rebuildSlaveInstance(slaveInstanceDo.getId().toString(),
                    custins.getInsName(),
                    getParameterValue(params, ParamConstants.USER_ID),
                    getParameterValue(params, ParamConstants.UID),
                    targetSiteName,
                    getParameterValue(params, ParamConstants.REQUEST_ID));

            // 备成功迁移时，改写实例状态，以便用户感知
            if (!result.containsKey("errorCode")) {
                custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_TRANS,
                        CustinsState.STATE_SLAVE_INS_TRANSING.getComment());
            } else {
                // 下发重搭任务失败， 则将可用区信息回滚
                custinsParamService.updateAVZInfo(custins.getId(), oldAvzInfo);
            }

            return result;
        } catch (RdsException re){
            logger.warn(re.getMessage(), re);
            custinsParamService.updateAVZInfo(custins.getId(), oldAvzInfo);
            return createErrorResponse(re.getErrorCode());
        }
    }

    /*
    针对MySQL本地盘跨机变配过程中，主备可用区没有对齐的情况，更新实例的role
    主备均在同一可用区则不需要更新role
     */
    private void updateDstInstanceRole(UpgradeResRespModel upgradeResRespModel, Integer custinsId,
                                       CustInstanceDO custins) throws RdsException{
        try {
            UpgradeResRespModel.CustinsResRespModel custinsResRespModel = upgradeResRespModel.getCustinsResRespModelList().get(0);
            List<InstanceDO> srcInstanceList = instanceService.getInstanceByCustinsId(custinsId);
            List<Integer> dstInstanceIdList = custinsResRespModel.getDstInstanceIdList();
            Set<Integer> instanceNode = new HashSet<>(dstInstanceIdList);
            InstanceDO srcMasterInstanceDO = null;
            InstanceDO srcSlaveInstanceDO = null;
            for (InstanceDO instance : srcInstanceList) {
                instanceNode.add(instance.getId());
                if (INSTANCE_ROLE_MASTER == instance.getRole()) {
                    srcMasterInstanceDO = instance;
                } else if (INSTANCE_ROLE_SLAVE == instance.getRole()) {
                    srcSlaveInstanceDO = instance;
                } else {
                    logger.warn("instance role is not define, current instance info: {}", JSON.toJSONString(instance));
                }
            }
            // master 和 slave 在同一可用区则不需要对齐
            if (srcMasterInstanceDO != null && srcSlaveInstanceDO != null && !srcMasterInstanceDO.getSiteName().equals(srcSlaveInstanceDO.getSiteName())) {
                for (Integer insId : dstInstanceIdList) {
                    InstanceDO dstInstanceDO = instanceService.getInstanceByInsId(insId);
                    if (srcMasterInstanceDO.getSiteName().equals(dstInstanceDO.getSiteName())) {
                        instanceService.updateInstanceRoleByInsId(insId, INSTANCE_ROLE_MASTER);
                    } else if (srcSlaveInstanceDO.getSiteName().equals(dstInstanceDO.getSiteName())) {
                        instanceService.updateInstanceRoleByInsId(insId, INSTANCE_ROLE_SLAVE);
                    } else {
                        logger.warn("instance site name is not found, current instance site name:{}，custins name:{}", dstInstanceDO.getSiteName(), custins.getInsName());
                    }
                }
            }

            // 只读两个节点的情况， 不支持半跨机
            if (custins.isRead() && srcInstanceList.size() == 2 && instanceNode.size() == 3){
                logger.warn("read ins standard, resource request exists one node in srcInstanceList, modify not support transfer_hostins ");
                throw new RdsException(ErrorCode.UNSUPPORTED_TRANS_TYPE);
            }
        } catch (Exception e) {
            logger.warn("custins:{} update dstInstance role failed", custins.getInsName());
        }

    }

    /*
    申请资源
    只读双节点不是强制迁移， 则重置为本地调度， 本地调度不满足则退化为强制迁移
     */
    public Response<UpgradeResRespModel> modifyInstanceAllocateRes(CustInstanceDO custins, boolean isReadInsStandard, String specifyTransType,
                                                                   UpgradeResContainer container, HostinsResModel hostinsResModel,
                                                                   UpgradeCustinsResModel custinsResModel) throws RdsException {
        Response<UpgradeResRespModel> response;
        if (isReadInsStandard && !StringUtils.equalsIgnoreCase(specifyTransType, CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE)) {
            specifyTransType = CUSTINS_TRANS_TYPE_LOCAL;
            hostinsResModel.setTransType(Integer.valueOf(specifyTransType));
            custinsResModel.setHostinsResModel(hostinsResModel);
            container.addUpgradeCustinsResModel(custinsResModel);
            logger.warn("readonly custins {} modify ins trans type is not CUSTINS_TRANS_TYPE_REMOTE try reset to CUSTINS_TRANS_TYPE_LOCAL", custins.getInsName());
            response = resManagerService.upgradeRes(container);
            if (!response.getCode().equals(200)) {
                specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
                hostinsResModel.setTransType(Integer.valueOf(specifyTransType));
                custinsResModel.setHostinsResModel(hostinsResModel);
                List<UpgradeCustinsResModel> upgradeCustinsResModelList = new ArrayList<UpgradeCustinsResModel>();
                upgradeCustinsResModelList.add(custinsResModel);
                container.setUpgradeCustinsResModelList(upgradeCustinsResModelList);
                logger.warn("readonly custins {} modify ins trans type reset to CUSTINS_TRANS_TYPE_LOCAL allocate resource failed, try reset to CUSTINS_TRANS_TYPE_REMOTE", custins.getInsName());
                response = resManagerService.upgradeRes(container);
            }
        } else {
            container.addUpgradeCustinsResModel(custinsResModel);
            response = resManagerService.upgradeRes(container);
        }
        return response;
    }


    /*
     * 设置实例的压缩率
     * 1. 记录custins_param参数
     * 2. 修改cust_instance的disk_size
     * 3. 修改instance_stat找的size
     */
    public void setInstanceCompression(CustInstanceDO custins, String compressionMode, String uid, Long disk_size) {
        custinsParamService.setCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_MODE_KEY , compressionMode);
        Double custinsCompression = getDefaultCompressionRatio(custins.getRegionId(), uid);
        custinsParamService.setCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_RATIO_KEY, custinsCompression.toString());
        custinsParamService.setCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_BEFORE_SIZE, disk_size.toString());

    }


    public double getDefaultCompressionRatio(String regionId, String uid){
        Double custinsCompression ;
        // 如果配置了使用用户压缩率，覆盖全局压缩率
        Double compressionRatioGlobal = mysqlParamSupport.getGlobalCompressionRatio(regionId);
        Double compressionRatioUser = mysqlParamSupport.getUserCompresionRatio(uid);
        if (Objects.isNull(compressionRatioUser)){
            custinsCompression = compressionRatioGlobal;
        }else {
            custinsCompression = compressionRatioUser;
        }
        return custinsCompression;
    }


    /*
     *  根据原有getAndCheckDiskSize新增压缩率后的计算
     */
    public Long getAndCheckDiskSizeWithCompression(CustInstanceDO custins, String storage, Integer bizType, Double compressionRatio) throws RdsException {

        if (storage != null && Integer.parseInt(custins.getDiskSize().toString()) > Integer.parseInt(storage) * compressionRatio * 1024) {
            InstancePerfDO instancePerf = this.instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
            String diskCurr = null;
            if (instancePerf != null && instancePerf.getDiskCurr() != null) {
                diskCurr = instancePerf.getDiskCurr();
            } else {
                diskCurr = custins.getDiskSize().toString();
            }

            Long diskUsed = (new BigDecimal(diskCurr)).longValue();
            if ((double)(Integer.parseInt(storage) * compressionRatio * 1024) <= (double)diskUsed * CustinsSupport.DISK_SIZE_REDUCE_RULE) {
                throw new RdsException(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
            }
        }

        if (StringUtils.isEmpty(storage)) {
            return custins.getDiskSize();
        } else {
            ResourceKey resourceKey = ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE;
            if (CustinsSupport.BIZ_TYPE_PARTITION.equals(bizType)) {
                resourceKey = ResourceKey.RESOURCE_PARTITION_MAX_DISK_SIZE;
            }

            Integer maxDiskSize = this.resourceSupport.getIntegerRealValue(resourceKey);

            try {
                if (storage == null) {
                    throw new RdsException(ErrorCode.INVALID_STORAGE);
                } else {
                    Integer value = Integer.parseInt(storage);
                    Integer minDiskSize = 5;
                    if (minDiskSize != null && minDiskSize.compareTo(value) > 0) {
                        throw new RdsException(ErrorCode.INVALID_STORAGE);
                    } else if (maxDiskSize != null && maxDiskSize.compareTo(value) < 0) {
                        throw new RdsException(ErrorCode.INVALID_STORAGE);
                    } else {
                        return (long) (value * compressionRatio * 1024L);
                    }
                }
            } catch (Exception var5) {
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }
        }
    }

    // 先查实例id是否在禁止半跨机白名单，如果没有，再查uid是否在白名单
    public String checkAndSetTransTypeLocalRemote(String custInstanceName, String uid, String originTransType) {
        // 如果TransType!=0(优先本地)，不修改TransType
        if (!Objects.equals(originTransType, "0")) {
            return originTransType;
        }

        // 强制本地或者全跨机，禁止半跨机
        String TRANS_TYPE_LOCAL_REMOTE = "3";
        ResourceDO resourceDO = resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_CUST_INSTANCE_NAME");
        if (resourceDO == null || StringUtils.isEmpty(resourceDO.getRealValue())) {
            logger.error("[checkAndSetTransTypeLocalRemote]: TRANS_TYPE_LOCAL_REMOTE_CUST_INSTANCE_NAME not found in dbaas.resource table");
        } else {
            List<String> custInsNameList = Arrays.asList(resourceDO.getRealValue().split(","));
            logger.info("[checkAndSetTransTypeLocalRemote]: TRANS_TYPE_LOCAL_REMOTE_CUST_INSTANCE_NAME = {}", JSON.toJSONString(custInsNameList));
            if (custInsNameList.contains(custInstanceName)) {
                logger.info("[checkAndSetTransTypeLocalRemote]: custins {} set specifyTransType from {} to {}", custInstanceName, originTransType, TRANS_TYPE_LOCAL_REMOTE);
                return String.valueOf(TRANS_TYPE_LOCAL_REMOTE);
            }
        }
        resourceDO = resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_UID");
        if (resourceDO == null || StringUtils.isEmpty(resourceDO.getRealValue())) {
            logger.error("[checkAndSetTransTypeLocalRemote]: TRANS_TYPE_LOCAL_REMOTE_UID not found in dbaas.resource table");
            return originTransType;
        }
        List<String> uidList = Arrays.asList(resourceDO.getRealValue().split(","));
        logger.info("[checkAndSetTransTypeLocalRemote]: TRANS_TYPE_LOCAL_REMOTE_UID = {}", JSON.toJSONString(uidList));
        if (uidList.contains(uid)) {
            logger.info("[checkAndSetTransTypeLocalRemote]: uid {} custins {} set specifyTransType from {} to {}", uid, custInstanceName, originTransType, TRANS_TYPE_LOCAL_REMOTE);
            return String.valueOf(TRANS_TYPE_LOCAL_REMOTE);
        }
        return originTransType;
    }

}


