package com.aliyun.dba.physical.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.EncdbService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeAccountMaskingPrivilegeImpl")
@Slf4j
public class DescribeAccountMaskingPrivilegeImpl implements IAction {

    @Autowired
    private EncdbService encdbService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        try {
            return encdbService.describeAccountMaskingPrivilege(custInstanceDO,params);
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}