package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.DistributeMode;
import com.alicloud.apsaradb.resmanager.DistributeRule;
import com.alicloud.apsaradb.resmanager.HostinsResModel;
import com.alicloud.apsaradb.resmanager.PortDistributeRule;
import com.alicloud.apsaradb.resmanager.UpgradeCustinsResModel;
import com.alicloud.apsaradb.resmanager.UpgradeResContainer;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.alicloud.apsaradb.resmanager.response.UpgradeResRespModel;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckHostIdSet;
import static com.aliyun.dba.custins.support.CustinsParamSupport.hasParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_CREATING;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_REMOVE_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_REMOVE;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalSilentMigrateImpl")
public class SilentMigrateImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(SilentMigrateImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(SilentMigrateImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected InstanceIDao instanceIDao;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ResourceSupport resourceSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (!custins.isActive()) {
                createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            //CustinsOnEcs实例无法使用该接口
            if (custins.isShare() || custins.isReadOrBackup()) {
                createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            Date switchBeginTime = null;
            Date switchEndTime = null;
            if (hasParameterValue(actionParams, "SwitchBeginTime") && hasParameterValue(actionParams, "SwitchEndTime")) {
                switchBeginTime = mysqlParamSupport.getAndCheckTimeByParam(actionParams, "SwitchBeginTime", DateUTCFormat.SECOND_UTC_FORMAT,
                        ErrorCode.INVALID_SWITCH_TIME);
                switchEndTime = mysqlParamSupport
                        .getAndCheckTimeByParam(actionParams, "SwitchEndTime", DateUTCFormat.SECOND_UTC_FORMAT,
                                ErrorCode.INVALID_SWITCH_TIME);

                Date now = new Date();
                long timeDiff = switchEndTime.getTime() - switchBeginTime.getTime();
                logger.warn("switch begin time" + switchBeginTime + "swich end time" + switchEndTime
                        + "timediff" + timeDiff);
                Integer expire = resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_SWITCH_TIME_ALLOWED);
                if (switchEndTime.getTime() <= now.getTime()
                        || switchBeginTime.getTime() > org.apache.commons.lang3.time.DateUtils
                        .addHours(now, expire).getTime() || (timeDiff / (60 * 1000)) < 20) {
                    return createErrorResponse(ErrorCode.INVALID_SWITCH_TIME);
                }
            }
            // 设置切换方式
            String switchMode = mysqlParamSupport.getAndCheckSwitchTimeMode(actionParams, ParamConstants.SWITCH_TIME_MODE);

            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

            CustinsConnAddrDO defaultCustinsConnAddr = custinsConnAddrList.get(0);
            CustInstanceDO tempCustins = null;

            Long timestamp = System.currentTimeMillis();
            tempCustins = custins.clone();
            tempCustins.setId(null);
            tempCustins.setClusterName("");
            tempCustins.setInsName("tmp" + timestamp + "_" + custins.getInsName());
            tempCustins.setStatus(CUSTINS_STATUS_CREATING);
            tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
            tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
            tempCustins.setInsType(custins.getInsType());
            tempCustins.setLevelId(custins.getLevelId());
            tempCustins.setDbVersion(custins.getDbVersion());
            tempCustins.setDiskSize(custins.getDiskSize());
            tempCustins.setConnType(custins.getConnType());//必须和源实例一致

            // 只能在本集群进行静默迁移
            String specifyClusterName = custins.getClusterName();
            String region = clusterService.getRegionByCluster(custins.getClusterName());
            InstanceLevelDO instanceLevelDO = instanceService
                    .getInstanceLevelByLevelId(custins.getLevelId());

            UpgradeResContainer container = new UpgradeResContainer(region);
            container.setRequestId(mysqlParamSupport.getParameterValue(actionParams,ParamConstants.REQUEST_ID));
            container.setClusterName(specifyClusterName);
            container.setPreferClusterName(specifyClusterName);
            container.setAccessId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID));
            container.setOrderId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID));
            container.setSourceHost(mysqlParamSupport.getInstanceCurrHostId(actionParams));

            // init custins res model
            UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
            custinsResModel.setCustinsId(custins.getId());

            // init host ins res model
            Set<Integer> hostIds = getAndCheckHostIdSet(actionParams);
            HostinsResModel hostinsResModel = new HostinsResModel(custins.getLevelId());
            if (hostIds.isEmpty()) {
                hostinsResModel.setTransType(Integer.valueOf(CUSTINS_TRANS_TYPE_REMOTE));
            }
            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            InstanceDO instance = instanceList.get(0);
            hostinsResModel.setHostType(instance.getHostType());
            hostinsResModel.setInsCount(instanceList.size());
            hostinsResModel.setDiskSizeSold(custins.getDiskSize());
            // get disk size used
            try {
                InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
                hostinsResModel.setDiskSizeUsed(new BigDecimal(instancePerf.getDiskCurr()).longValue());
            } catch (Exception e) {
                logger.error("Get instance perf failed for custins: " + custins.getId(), e);
                hostinsResModel.setDiskSizeUsed(custins.getDiskSize());
            }

            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setSpecifyHostIdSet(hostIds);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, instanceLevelDO.getExtraInfo());
            hostinsResModel.setDistributeRule(distributeRule);

            // 如果dns,需要前后端口一致
            if (custins.isDns()) {
                PortDistributeRule portDistributeRule = new PortDistributeRule();
                Set<Integer> ports = new HashSet<>(1);
                ports.add(instance.getPort());
                portDistributeRule.setSpecifyPortSet(ports);
                hostinsResModel.setPortDistributeRule(portDistributeRule);
            }

            custinsResModel.setHostinsResModel(hostinsResModel);
            container.addUpgradeCustinsResModel(custinsResModel);

            Response<UpgradeResRespModel> response = resApi.upgradeRes(container);
            UpgradeResRespModel upgradeResRespModel = response.getData();
            if (!response.getCode().equals(200)) {
                //mysqlParamSupport.createResourceRecord(actionParams,custins.getClusterName(), custins.getInsName(), JSON.toJSONString(response));
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            UpgradeResRespModel.CustinsResRespModel custinsResRespModel =
                    upgradeResRespModel.getCustinsResRespModelList().get(0);

            tempCustins.setClusterName(custinsResRespModel.getClusterName());
            tempCustins.setConnType(custinsResRespModel.getConnType());
            tempCustins.setProxyGroupId(custinsResRespModel.getProxyGroupId());

            custinsService.createCustInstanceForTrans(custins, tempCustins);

            // update instance custins
            List<Integer> tmpInstanceList = custinsResRespModel.getDstInstanceIdList();
            instanceIDao.updateInstanceCustinsIdByInsIds(tmpInstanceList, tempCustins.getId());
            // update custins hostins rel table
            instanceIDao.updateCustinsHostinsRelByInsIds(tmpInstanceList, tempCustins.getId());

            List<Integer> custinsConnAddrIdList =
                    custinsResRespModel.getCustinsConnAddrIdList();
            // update custins conn addr table
            connAddrCustinsService.updateCustinsConnAddrCustinsIdByIds(
                    custinsConnAddrIdList, tempCustins.getId());

            //原逻辑，TransList与TransListDO对象一致，所以此处使用TransListDO
            //TransList translist = createTransListForSilentMigrate(custins, tempCustins, switchBeginTime, switchEndTime, switchMode);
            TransListDO transListDO = createTransListDOForSilentMigrate(custins, tempCustins, switchBeginTime, switchEndTime, switchMode);

            Integer taskId = instanceService.silentTransCustInstanceTask(
                    mysqlParamSupport.getAction(actionParams), mysqlParamSupport.getOperatorId(actionParams),
                    TaskSupport.TASK_SILENT_TRANSFER_MYSQL, custins, transListDO, tempCustins);
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));
            Map<String, Object> data = new HashMap<String, Object>(7);
            data.put("MigrationID", transListDO.getId());
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            data.put("Region", region);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 2018-08-21新创建，逻辑同createTransListForSilentMigrate，将TransList转化为TransListDO
     */
    private TransListDO createTransListDOForSilentMigrate(CustInstanceDO custins,
                                                          CustInstanceDO tempCustins,
                                                          Date switchBeginTime,
                                                          Date switchEndTime, String switchTimeMode) {
        TransListDO translistDO = new TransListDO(custins, tempCustins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        List<InstanceDO> tmpInstanceList = instanceService.getInstanceByCustinsId(tempCustins.getId());
        translistDO.setdHinsid1(tmpInstanceList.get(0).getId());
        if (tmpInstanceList.size() >= 2) {
            translistDO.setdHinsid2(tmpInstanceList.get(1).getId());
        } else {
            translistDO.setdHinsid2(0);
        }

        // 设置源主机实例ID
        List<InstanceDO> srcInstanceList = instanceService.getInstanceByCustinsId(custins.getId());
        translistDO.setsHinsid1(srcInstanceList.get(0).getId());
        translistDO.setsHinsid2(srcInstanceList.get(1).getId());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, Object> param = new HashMap<String, Object>();
        if (switchBeginTime != null && switchEndTime != null) {
            Map<String, Object> switch_time = new HashMap<String, Object>();
            switch_time.put("begin_time", sdf.format(switchBeginTime));
            switch_time.put("end_time", sdf.format(switchEndTime));
            param.put("switch_time_range", switch_time);
        }

        if (StringUtils.isNotBlank(switchTimeMode)) {
            param.put(CustinsSupport.SWITCH_MODE_FOR_SILENTTRANS, switchTimeMode);
        }
        param.put("wait_sync", true);
        param.put("silent_transfer", true);
        translistDO.setParameter(JSON.toJSONString(param));
        return translistDO;
    }
}
