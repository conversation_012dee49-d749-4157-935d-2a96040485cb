package com.aliyun.dba.physical.action;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.EvaluateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.common.consts.ResourceScheduleConsts;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.PhysicalResourceGuaranteeModelService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.parameter.MysqlParameterHelper.XDB_RO_EXCLUDE_HOST_LEVELS;
import static com.aliyun.dba.base.support.MySQLParamConstants.CUR_QUOTA;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_DOCKER;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_DOCKER_ON_ECS;
import static com.aliyun.dba.support.property.ParamConstants.*;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalEvaluateRegionResourceImpl")
public class EvaluateRegionResourceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(EvaluateRegionResourceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateRegionResourceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected ResourceService resourceService;
    @Resource
    private ResManagerService resManagerService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {

        try {
            // 如果是只读实例本地盘迁移云盘，使用k8s资源评估接口
            if (ValidateIsFromPhysicalToK8s(custins, actionParams)) {
                // 这里执行拦截条件的检查，瑶池调用资源评估得时候就会把不符合条件的实例拦截住
                com.aliyun.dba.physical.action.service.TransferPhysicalToK8sService transferPhysicalToK8sService = SpringContextUtil
                        .getBeanByClass(com.aliyun.dba.physical.action.service.TransferPhysicalToK8sService.class);
                transferPhysicalToK8sService.validatePhysicalToK8sForRo(custins, actionParams);
                // 资源评估
                com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl k8sEvaluateRegionResourceImpl = SpringContextUtil
                        .getBeanByClass(com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl.class);
                if (StringUtils.isEmpty(getParameterValue(actionParams, ParamConstants.ZONE_ID))) {
                    AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
                    String zoneId = oldAvzInfo.getMasterZoneId();
                    actionParams.put(ParamConstants.ZONE_ID.toLowerCase(), zoneId);
                }
                String requestId = getParameterValue(actionParams, ParamConstants.REQUEST_ID);
                logger.info(requestId + " [poddefault.action.EvaluateRegionResourceImpl] start evaluate FromPhysicalToK8s read-only instances resources" + JSON.toJSONString(actionParams));
                final Map<String, Object> ret = k8sEvaluateRegionResourceImpl.doActionRequest(custins, actionParams);
                return ret;
            }

            // 用于Quota评估使用，补充部分Quota评估需要的业务参数
            Boolean isQuotaEvaluate = mysqlParamSupport.getAndFillQuotaEvaluate(actionParams);
            String region = mysqlParamSupport.getAndCheckRegion(actionParams);
            String dbType = mysqlParamSupport.getAndCheckDBType(actionParams, null);
            // Docker 实例集群的db_type类型为docker,引入临时变量做调整
            String dbTypeForCluster = dbType;
            String classCode = mysqlParamSupport.getAndCheckClassCode(actionParams);
            String dbVersion = mysqlParamSupport.getAndCheckDBVersion(actionParams, dbType, false);
            Integer bizType = mysqlParamSupport.getAndCheckBizType(actionParams);
            // 获取uid
            String uid = getParameterValue(actionParams, ParamConstants.UID);

            mysqlParamSupport.getAndSetContainerTypeAndHostTypeIfEmpty(actionParams, dbType, dbVersion, classCode);

            String containerType = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CONTAINER_TYPE,
                CustinsSupport.CONTAINER_TYPE_HOST);
            String hostType = mysqlParamSupport.getAndCheckHostType(actionParams);

            String clusterName = getParameterValue(actionParams, ParamConstants.CLUSTER_NAME);
            String insType = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_USED_TYPE);

            custins = new CustInstanceDO();
            custins.setDbType(dbType);
            custins.setDbVersion(dbVersion);

            custins = mysqlParamSupport.setInstanceLevel(custins, classCode, bizType,
                    mysqlParamSupport.getParameterValue(actionParams, ParamConstants.STORAGE));

            if ((CustinsSupport.isContainerTypeDocker(containerType)
                && CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD.equals(hostType))) {
                custins.setKindCode(KIND_CODE_DOCKER_ON_ECS);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }

            if (StringUtils.equals(CustinsSupport.DB_TYPE_POLARDB_MYSQL, dbType)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE);
            }
            if (CustinsSupport.isContainerTypeDocker(containerType)
                && !CustinsSupport.KIND_CODE_DOCKER_ON_ECS.equals(custins.getKindCode())
                && !CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE.equals(custins.getKindCode())) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }
            custins.setDbTypeForCluster(dbTypeForCluster);

            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            Integer nodeCount;
            if ("3".equals(insType) || "4".equals(insType)) {
                nodeCount = 1;
            } else if (insLevel.isMysqlEnterprise() || mysqlParamSupport.isMysqlXDBByLevel(insLevel)) {
                nodeCount = 3;
            } else {
                nodeCount = 2;
            }

            //mysql 只读高可用节点， 使用node = 2
            if ("3".equals(insType) && StringUtils.equals(CustinsSupport.DB_TYPE_MYSQL, dbType) && insLevel.isStandardLevel()){
                ResourceDO roStandardGrayDO = resourceService.getResourceByResKey("READ_INS_STANDARD_GRAY");
                String readonlyStandardGray = "false";
                if (roStandardGrayDO != null){
                    readonlyStandardGray = roStandardGrayDO.getRealValue();
                }
                if (StringUtils.equalsIgnoreCase(readonlyStandardGray, "true")){
                    nodeCount = 2;
                }
            }

            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(actionParams);

            //实例节点依据maz添加可用区信息，经典可用区使用zoneid，不区分实例规格以及实例类型： ro/备用只读
            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(actionParams, insLevel.getDbType());
            // 原逻辑：user_id = -1
//            resourceContainer.setUserId(custins.getUserId());
            // 实例创建已适配资源保障方案，评估需要与创建行为一致，此处需要获取用户真实user_id，用于后续判断用户等级
            resourceContainer.setUserId(mysqlParamSupport.getUserId(actionParams));
            logger.info("add user_id in resourceContainer. user_id = {}.", resourceContainer.getUserId());
            if (StringUtils.isNotEmpty(uid)) {
                resourceContainer.setUid(uid);
                logger.info("add uid in resourceContainer. uid = {}.", resourceContainer.getUid());
            }

            resourceContainer.setRequestId(getParameterValue(actionParams,ParamConstants.REQUEST_ID));
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setEvaluateNum(evaluateNum);
            // 所有物理机的评估 需要增加v6标签 去进行评估
            resourceContainer.setV6CpuMatch(true);

            /**
             * allocate resource
             */
            CustinsResModel custinsResModel = new CustinsResModel(null);
            HostinsResModel hostinsResModel = new HostinsResModel(insLevel.getId());
            hostinsResModel.setInsCount(nodeCount);
            hostinsResModel.setHostType(Integer.valueOf(hostType));
            hostinsResModel.setDiskSizeSold(custins.getDiskSize());


            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, insLevel.getExtraInfo());
            boolean isXdbRead = mysqlParamSupport.isXdbReadCustins(custins);

            if (isXdbRead) {
                if (Objects.isNull(distributeRule.getExcludeHostLevelNameSet())) {
                    distributeRule.setExcludeHostLevelNameSet(new HashSet<>());
                }
                distributeRule.getExcludeHostLevelNameSet().addAll(Arrays.asList(XDB_RO_EXCLUDE_HOST_LEVELS));

                if (!CollectionUtils.isEmpty(distributeRule.getSpecifyHostLevelNameSet())) {
                    distributeRule.getSpecifyHostLevelNameSet().removeAll(distributeRule.getExcludeHostLevelNameSet());
                }
                logger.info(String.format("actual distributeRule is: %s", JSON.toJSONString(distributeRule)));
            }

            // 指定的host ids
            distributeRule.setSpecifyHostIdSet(CustinsParamSupport.getAndCheckHostIdSet(actionParams));
            hostinsResModel.setDistributeRule(distributeRule);
            custinsResModel.setHostinsResModel(hostinsResModel);
            resourceContainer.addCustinsResModel(custinsResModel);
            CustinsDistributeRule custinsDistributeByUserIdRule = new CustinsDistributeRule();
            custinsDistributeByUserIdRule.setScatterType("SCATTER_TYPE_USER_ID");
            custinsDistributeByUserIdRule.setMaxInsPerHost(1);
            resourceContainer.getCustinsDistributeRuleList().add(custinsDistributeByUserIdRule);

            // set access id
            resourceContainer.setAccessId(getParameterValue(actionParams, ACCESSID));

            if (isQuotaEvaluate) {
                return evaluateResQuota(resourceContainer, actionParams);
            }

            if (mysqlParamSupport.isMysqlXDBByLevel(insLevel)) {
                // XDB v7 机型CPU对齐
                resourceContainer.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
            }

            //调用资源API
            Map<String, Object> data = new HashMap<String, Object>();

            Response<EvaluateResRespModel> response = resManagerService.evaluateRes(resourceContainer);
            if (response.getCode().equals(200)) {
                data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                data.put(ParamConstants.SUBDOMAIN_AVAILABLE_DETAIL, response.getData().getSudomainDetails());
            }
            //主可用区添加
            else if (response.getCode().equals(500)) {
                //throw new RdsException(ErrorCode.RESOURCE_NOT_CONFIG);
                //24431937 resource manager return 500, call yuqian, here return 4xx first, and desc is RESOURCE_NOT_CONFIG
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND, ErrorCode.RESOURCE_NOT_CONFIG.getDesc());
            }
            else {
                data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                data.put(ParamConstants.ERROR_MESSAGE, ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            data.put(ENGINE_VERSION, insLevel.getDbVersion());
            data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(insLevel.getDbType()));
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 单独做Quota评估调用
     */
    private Map<String, Object> evaluateResQuota(ResourceContainer container, Map<String, String> params) throws RdsException {
        String quotaMaxCount = mysqlParamSupport.getParameterValue(params, MySQLParamConstants.QUOTA_MAX_COUNT);
        container.setQuotaMaxCount(StringUtils.isNoneBlank(quotaMaxCount) ? Integer.parseInt(quotaMaxCount) : null);
        String emptyHostListStr = mysqlParamSupport.getParameterValue(params, MySQLParamConstants.EMPTY_HOST_LIST);
        List<ResourceContainer.EmptyHostItem> emptyHostList = null;
        try {
            if (StringUtils.isNotBlank(emptyHostListStr)) {
                emptyHostList = JSON.parseArray(emptyHostListStr, ResourceContainer.EmptyHostItem.class);
            }
        } catch (Exception e) {
            logger.error("evaluateResQuota parse emptyHostList failed. ", e);
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        container.setEmptyHostList(emptyHostList);
        Response<EvaluateResRespModel> response = resManagerService.evaluateResQuota(container);
        Map<String, Object> data = new HashMap<>();
        if (response.getCode().equals(200)) {
            // 是因为评估的都是高可用规格，所以需要*2算最终Quota，按单个节点算
            data.put(Available_Quota, (response.getData().getEmptyQuotaCount() + response.getData().getCurQuotaCount()) * 2);  // 包含空机器的Quota
            data.put(CUR_QUOTA, response.getData().getCurQuotaCount() * 2);  // 不包含空机器的Quota
            data.put(REQUEST_ID, container.getRequestId());
        }
        else {
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
        }
        return data;
    }

    /**
     * 判断是否为从本地跨云盘的高可用只读实例
     */
    public Boolean ValidateIsFromPhysicalToK8s(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        // 批量评估本地升云盘的只读实例
        if (!custins.isRead()) {
            return false;
        }

        InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        InstanceLevelDO newLevel;
        String levelCode = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_CLASS);

        String dbVersion = custinsService.getDBVersion(custins.getDbType(), custins.getDbVersion());
        if (dbVersion == null) {//默认为当前版本
            dbVersion = custins.getDbVersion();
        }
        newLevel = instanceService.getInstanceLevelByClassCode(levelCode, custins.getDbType(), dbVersion, custins.getTypeChar(), null);
        if (newLevel == null || newLevel.isClusterLevel()) {
            logger.warn("[EvaluateRegionResourceImpl.ValidateIsFromPhysicalToK8s]: newLevel is null or clusterLevel");
            return false;
        }

        Boolean support57 = false;
        Boolean support80 = false;
        String requestId = getParameterValue(actionParams, ParamConstants.REQUEST_ID);
        if ("5.7".equals(custins.getDbVersion())
                && InstanceLevel.CategoryEnum.STANDARD.getValue().equals(newLevel.getCategory())
                && InstanceLevel.CategoryEnum.STANDARD.getValue().equals(oldLevel.getCategory())
                && oldLevel.getHostType() == 0
                && newLevel.getHostType() == 2) {
            logger.info(requestId + " [EvaluateRegionResourceImpl.ValidateIsFromPhysicalToK8s]: support57 is true");
            support57 = true;
        };
        if ("8.0".equals(custins.getDbVersion())
                && InstanceLevel.CategoryEnum.STANDARD.getValue().equals(newLevel.getCategory())
                && InstanceLevel.CategoryEnum.STANDARD.getValue().equals(oldLevel.getCategory())
                && oldLevel.getHostType() == 0
                && newLevel.getHostType() == 2) {
            logger.info(requestId + " [EvaluateRegionResourceImpl.ValidateIsFromPhysicalToK8s]: support80 is true");
            support80 = true;
        };
        return support57 || support80;
    }
}
