package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.dba.base.common.consts.SSLConsts;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.CAServerApiExt;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_SWITCH;
import static com.aliyun.dba.support.property.ParamConstants.SSL_VALUE_DISABLED;
import static com.aliyun.dba.support.property.ParamConstants.SSL_VALUE_ENABLED;
import static com.aliyun.dba.task.support.TaskSupport.TASK_MODIFY_SSL;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalModifyDBInstanceSSLImpl")
public class ModifyDBInstanceSSLImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(ModifyDBInstanceSSLImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceSSLImpl.class);

    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CaServerApi caServerApi;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    private CustinsIDao custinsIDao;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private CAServerApiExt caServerApiExt;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            String caType = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
            String forceEncryption = getParameterValue(actionParams, ParamConstants.FORCE_ENCRYPTION,SSLConsts.FORCE_ENCRYPTION);
            String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID);
            if (!SSLConsts.CA_TYPE.contains(caType)) {
                return createErrorResponse(ErrorCode.INVALID_CA_TYPE);
            }
            try {
                caServerApi.getCAServerConfig(custins.getClusterName());
            } catch (RdsException re) {
                //没有ca server，该集群无法开启ssl
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENABLE_SSL);
            }

            if (custins.isMysql51()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isShare() || custins.isCustinsOnEcs()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (custins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            String connectionString = CheckUtils
                    .checkNullForConnectionString(
                            getParameterValue(actionParams, ParamConstants.CONNECTION_STRING));
            if (connectionString.length() > 64) {
                return createErrorResponse(ErrorCode.CONNECTIONSTRING_LENGTH_EXCEEDED);
            }
            String minorVersionStr = custinsParamService.getCustinsParam(custins.getId(), "minor_version").getValue();
            String dbMinorVersionLongStr = "0";
            if (minorVersionStr.contains(":")) {
                int index = minorVersionStr.indexOf(":");
                dbMinorVersionLongStr = minorVersionStr.substring(index + 1, index + 9);
            } else {
                int index = minorVersionStr.lastIndexOf("_");
                dbMinorVersionLongStr = minorVersionStr.substring(index + 1, index + 9);
            }
            long minorVersion = Long.parseLong(dbMinorVersionLongStr);

            //获取请求参数
            String sslStatus = getParameterValue(actionParams, ParamConstants.SSL_ENABLED);
            boolean sslUpdate = false;
            String serverCert = null;
            String serverKey = null;

            if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
                //关闭ssl, 0
                sslStatus = SSL_VALUE_DISABLED;
            } else {
                // 开启或更新 ssl, 1
                sslStatus = SSL_VALUE_ENABLED;
                if (StringUtils.equalsIgnoreCase(caType, SSLConsts.CA_TYPE_CUSTOM)) {  //自定义证书
                    serverCert = mysqlParameterHelper.getAndCheckCustomsServerCert();
                    serverKey = mysqlParameterHelper.getAndCheckCustomsServerKey();
                }
                String sslEnabledNow = null;
                CustinsParamDO insSslConfig = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL);
                CustinsParamDO forceEncryptionConfig = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_FORCE_ENCRYPTION);
                if (insSslConfig != null) {
                    sslEnabledNow = insSslConfig.getValue();
                }
                if (StringUtils.equalsIgnoreCase(SSL_VALUE_ENABLED, sslEnabledNow)) {
                    sslUpdate = true;
                    //如果是更新，证书类型需一致
                    CustinsParamDO caTypeConfig = custinsParamService.getCustinsParam(custins.getId(), SSLConsts.CUSTINS_PARAM_NAME_CA_TYPE);
                    String caTypeFromMeta = caTypeConfig == null ? SSLConsts.CA_TYPE_ALIYUN : caTypeConfig.getValue();
                    if (!StringUtils.equalsIgnoreCase(caTypeFromMeta, caType)) {
                        return createErrorResponse(ErrorCode.INVALID_CA_TYPE, "Specify ca type is invalid.");
                    }
                    if (forceEncryptionConfig != null){
                        String oldForceEncryption = forceEncryptionConfig.getValue();
                        if (!StringUtils.equalsIgnoreCase(oldForceEncryption, forceEncryption)){
                            sslUpdate = false;
                        }
                    }else if ("1".equals(forceEncryption)){
                        sslUpdate = false;
                    }
                }
            }
            if("1".equals(forceEncryption) && StringUtils.equalsIgnoreCase(SSL_VALUE_ENABLED, sslStatus)){
                if (!custins.isMysql57() && !custins.isMysql80()) {
                    //仅对mysql,5.7,8.0实例有效
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
                String forceEncryptionSwitch = SSLConsts.FORCE_ENCRYPTION_MINOR_VERSION;
                ConfigListResult configs = dBaasMetaService.getDefaultClient().listConfigs(requestId, PodDefaultConstants.MYSQL_SSL_FORCE_ENCRYPTION_SWITCH);
                if (configs.getItems() != null && !configs.getItems().isEmpty()) {
                    Config switchConfig = configs.getItems().get(0);
                    forceEncryptionSwitch = !StringUtils.isBlank(switchConfig.getValue()) ? switchConfig.getValue() : SSLConsts.FORCE_ENCRYPTION_MINOR_VERSION;
                }
                if (minorVersion < Integer.parseInt(forceEncryptionSwitch)){
                    return createErrorResponse(ErrorCode.MINOR_VERSION_NOT_SUPPORT_SSLENABLED);
                }
                if(!mysqlEngineCheckService.checkMinorVersionWithMaxScale(custins)){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_MAXSCALE_MINOR_VERSION);
                }
            }

            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);
            Boolean connStringValid = false;
            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
                if (connectionString.equals(custinsConnAddr.getConnAddrCust()) &&
                        custinsConnAddr.isConnAddrUserVisible()) {
                    connStringValid = true;
                }
            }
            if (!connStringValid) {
                if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
                    logger.warn("close ssl, current connstr doesn't exists, continue");
                } else {
                    return createErrorResponse(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
                }
            }

            //自定义证书，通过CaServer上传
            if (StringUtils.equalsIgnoreCase(SSL_VALUE_ENABLED, sslStatus) &&
                    com.alibaba.druid.util.StringUtils.equalsIgnoreCase(caType, SSLConsts.CA_TYPE_CUSTOM)) {
                caServerApiExt.uploadCustomServerCert(requestId, custins.getInsName(), connectionString, serverCert, serverKey);
            }

            if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
                //关闭ssl, 关联链路
                custinsParamService.deleteCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_CERT_CN_NAME);
            } else {
                // 更新实例开启ssl的dns
                CustinsParamDO certCNName = custinsParamService.getCustinsParam(
                        custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME);
                if (certCNName == null) {
                    custinsParamService.createCustinsParam(
                            new CustinsParamDO(custins.getId(),
                                    CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME,
                                    connectionString)
                    );
                } else {
                    custinsParamService.setCustinsParam(
                            custins.getId(),
                            CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME,
                            connectionString
                    );
                }
                custinsParamService.setCustinsParam(custins.getId(), SSLConsts.CUSTINS_PARAM_NAME_CA_TYPE, caType);
            }

            boolean dynamic_update = mysqlParameterHelper.isSupportModifySSLDynamic(custins, sslUpdate);

            custinsIDao.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_SWITCH,
                    CustinsState.STATE_SSL_MODIFYING.getComment());
            Map<String, Object> taskParamMap = new HashMap<String, Object>();
            taskParamMap.put("ssl_status", Integer.valueOf(sslStatus));
            taskParamMap.put("ca_type", caType);
            taskParamMap.put("dynamic_update", dynamic_update ? 1 : 0);
            taskParamMap.put("force_encryption", Integer.valueOf(forceEncryption));
            TaskQueueDO taskQueue = new TaskQueueDO(getAction(actionParams), getOperatorId(actionParams), custins.getId(), TASK_TYPE_CUSTINS,
                    TASK_MODIFY_SSL, JSON.toJSONString(taskParamMap));
            taskService.createTaskQueue(taskQueue);
            Integer taskId = taskQueue.getId();

            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(actionParams));

            Map<String, Object> data = new HashMap<>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

}
