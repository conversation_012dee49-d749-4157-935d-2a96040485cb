package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.ArchivelogListDO;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.CustinsRebuildResourceServiceImpl;
import com.aliyun.dba.physical.action.service.PhysicalResourceGuaranteeModelService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.dba.base.support.MySQLParamConstants.PARAM_FORCE_REBUILD_IN_SITE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckHostIdSet;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.CUSTINS_PARAM_NAME_IS_POLARX_HATP;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRebuildSlaveInstanceImpl")
public class RebuildSlaveInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(RebuildSlaveInstanceImpl.class);

    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsRebuildResourceServiceImpl custinsRebuildResourceService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    protected MycnfService mycnfService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private BakService bakService;
    @Resource
    private MinorVersionService minorVersionService;
    @Resource
    private PhysicalResourceGuaranteeModelService physicalResourceGuaranteeModelService;

    private static final String RebuildFollowerType = "RebuildFollowerType";
    private static final String RebuildFollower = "RebuildFollower";
    private static final String RebuildLogger = "RebuildLogger";
    private static final Integer LOGGER_ROLE = 2;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            if (custins.isShare()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            List<InstanceDO> instanceList;
            if (custins.isLogic()) {
                instanceList = instanceService.getInstanceByParentCustinsId(custins.getId());
            } else {
                instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            }

            // check instance id specified exist.
            Integer srcInstanceId = CustinsValidator.getRealNumber(getParameterValue(params, ParamConstants.INSTANCE_ID));
            if (srcInstanceId < 0) {
                logger.error("Invalid instance id format: " + getParameterValue(params, ParamConstants.INSTANCE_ID));
                return createErrorResponse(ErrorCode.INVALID_INSTANCE_ID);
            }
            InstanceDO srcInstanceDo = null;
            boolean isExist = false;
            for (InstanceDO instance : instanceList) {
                if (instance.getId().equals(srcInstanceId)) {
                    isExist = true;
                    srcInstanceDo = instance;
                    break;
                }
            }
            if (!isExist) {
                return createErrorResponse(ErrorCode.INVALID_INSTANCE_ID);
            }
            //# Aone 21743830,check instanceId is not master
            if (srcInstanceDo.getRole() == 0) {
                return createErrorResponse(MysqlErrorCode.INVALID_REBUILD_SLAVE_INSTANCE.toArray());
            }

            // 获得用户指定备份 hostins 列表 并设置到custins的属性中
            List<Integer> specifyBackupHostinsList = CustinsValidator
                    .getRealNumberList(getParameterValue(params, ParamConstants.SPECIFY_BACKUP_HOSTINS_LIST));
            custins.setSpecifyBackupHostinsList(specifyBackupHostinsList);

            String rebuildType = mysqlParamSupport.getAndCheckRebuildType(params);
            // docker on ecs all local rebuild
            if (CustinsSupport.SLAVE_REBUILD_TYPE_LOCAL.equals(rebuildType) || custins.isCustinsDockerOnEcs()) {
                return rebuildSlaveInstanceLocalMode(params, custins, srcInstanceId);
            } else if (custins.isRead()) {
                logger.info("readonly rebuild remote.");
                return readonlyRebuildSlaveRemoteMode(custins, params);
            } else {
                logger.info("other rebuild instance remote");
                return primaryRebuildSlaveInstanceRemoteMode(custins, params);
            }

        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private void  checkBakSetValid(CustInstanceDO custins, Long backupSetId) throws RdsException {
        BakhistoryDO bakHistory = bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId);
        if (bakHistory == null) {
            logger.error("backup set {} not found.", backupSetId);
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        if (!"ok".equalsIgnoreCase(bakHistory.getStatus()) || bakHistory.getIsAvail() != 1) {
            logger.error("backup set {} status {} isAvail {}.", backupSetId, bakHistory.getStatus(), bakHistory.getIsAvail());
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        if (!("DATA".equals(bakHistory.getType()) && "P".equals(bakHistory.getBakWay())  && 0 == bakHistory.getBakScale())) {
            logger.error("backup set {} type {} way {} bakType {} scale {}.", backupSetId, bakHistory.getType(), bakHistory.getBakWay(), bakHistory.getBakType(), bakHistory.getBakScale());
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        if ("I".equals(bakHistory.getBakType())){
            logger.info("backup set {} is increment backup set.", backupSetId);
            JSONObject baksetInfo = JSON.parseObject(bakHistory.getBaksetInfo());
            if (baksetInfo == null) {
                logger.error("increment backup set {} invalid bakset info, baksetInfo is null", backupSetId);
                throw new RdsException(ErrorCode.INVALID_BAKSET);
            }
            Long lastHisID = baksetInfo.getLong("last_his_id");
            if (lastHisID==null) {
                logger.error("increment backup set {} invalid bakset info, lastHisID is null", backupSetId);
                throw new RdsException(ErrorCode.INVALID_BAKSET);
            }
            BakhistoryDO fullBakHistory=bakService.getBakhistoryByBackupSetId(custins.getId(), lastHisID);
            if(fullBakHistory==null){
                logger.error("get full backup set error,increment backup set info {}",bakHistory.getBaksetInfo());
                throw new RdsException(ErrorCode.INVALID_BAKSET);
            }
            logger.info("increment backup set {} lastHisID {}",backupSetId,lastHisID);
        }
        JSONObject slaveStatus = JSON.parseObject(bakHistory.getSlaveStatus(),JSONObject.class);
        if (slaveStatus == null) {
            logger.error("backupset {} invalid slave status, slave status is null", backupSetId);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        String binlogFileName = slaveStatus.getString("BINLOG_FILE");
        if (StringUtils.isEmpty(binlogFileName)) {
            logger.error("backupset {} invalid slave status, binlogFileName is null", backupSetId);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        Integer binlogFileNo = Integer.parseInt(binlogFileName.substring(binlogFileName.lastIndexOf(".") + 1));
        List<ArchivelogListDO> binlogList = bakService.getArchivelogByCustinsId(custins.getId(), binlogFileNo, binlogFileNo);
        if (binlogList == null || binlogList.isEmpty()) {
            logger.error("The log {} that needs to sync for this bak {} is not existed).", binlogFileName, backupSetId);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        Integer hostinsId = bakHistory.getHostinsId();
        boolean isBinlogExist = false;
        for (ArchivelogListDO binlog : binlogList) {
            if (hostinsId.equals(binlog.getHostinsId()) && (binlog.getRemoteStatus() == 2 || "host".equals(binlog.getLocation()) && binlog.getLocalStatus() == 0)) {
                isBinlogExist = true;
                break;
            }
        }
        if (!isBinlogExist) {
            logger.error("The log {} that needs to sync for this bak {} is not existed).", binlogFileName, backupSetId);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
    }

    /**
     * 检查指定备份集的数据库内核小版本号
     *
     * @param custins
     * @param backupSetId
     * @return 数据库内核小版本号, null 表示没有获取到该参数
     * @throws RdsException
     */
    private String getBakSetMinorVersion(CustInstanceDO custins, Long backupSetId) throws RdsException {
        BakhistoryDO bakHistory = bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId);
        if (bakHistory == null) {
            logger.error("backupset {} not found.", backupSetId);
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        } else if (!"ok".equalsIgnoreCase(bakHistory.getStatus()) || bakHistory.getIsAvail() != 1) {
            logger.error("backupset {} status {} isAvail {}.", backupSetId, bakHistory.getStatus(), bakHistory.getIsAvail());
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        JSONObject slaveStatus = JSON.parseObject(bakHistory.getSlaveStatus(), JSONObject.class);
        if (slaveStatus == null) {
            logger.error("invalid slave status, slave status is null");
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        return slaveStatus.getString("MINOR_VERSION");
    }

    /**
     * 检查并获取目标数据库内核小版本
     *
     * @param actionParams
     * @param custins
     * @return 数据库内核小版本号，null表示不指定
     * @throws RdsException
     */
    public String getTargetMinorVersion(Map<String, String> actionParams, CustInstanceDO custins) throws RdsException {
        String targetMinorVersionParam = mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion");
        if (StringUtils.isEmpty(targetMinorVersionParam)) {
            return null;
        }
        String targetMinorVersionReleaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionParam);
        if (StringUtils.isEmpty(targetMinorVersionReleaseDate)) {
            logger.error("invalid targetMinorVersion {}, can't parse release data from it.", targetMinorVersionParam);
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }
        String classCode = instanceService.getInstanceLevelByLevelId(custins.getLevelId()).getClassCode();
        String dbEngine = mysqlParamSupport.isMysqlXDB(custins.getDbType(), custins.getDbVersion(), classCode)
                ? MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_XDB
                : MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL;
        String targetMinorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
                custins.getDbType(),
                custins.getDbVersion(),
                classCode,
                dbEngine,
                targetMinorVersionParam);
        if (StringUtils.isEmpty(targetMinorVersion)) {
            logger.error("invalid targetMinorVersion {},checkAndGetTargetMinorVersion return null", targetMinorVersionParam);
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }

        // MySQL 8.0 does not support minor version downgrades.
        String mysql80AllowDowngrade = mysqlParamSupport.getParameterValue(actionParams, "mysql80AllowDowngrade",true);
        if ("true".equals(mysql80AllowDowngrade) || !CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(custins.getDbVersion())) {
            return targetMinorVersion;
        }
        String backupSetId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.BACKUP_SET_ID);
        if (StringUtils.isNotEmpty(backupSetId)) {
            String bakSetMinorVersion = getBakSetMinorVersion(custins, Long.parseLong(backupSetId));
            if (StringUtils.isEmpty(bakSetMinorVersion)){
                logger.warn("It can't be found minor version info in backup set {}.",backupSetId);
                throw new RdsException(ErrorCode.INVALID_BAKSET);
            }
            String bakSetReleaseData = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(bakSetMinorVersion);
            if (StringUtils.compare(bakSetReleaseData, targetMinorVersionReleaseDate) > 0) {
                logger.error("invalid minor version, expected:{}, bakSet:{}", targetMinorVersionReleaseDate, bakSetReleaseData);
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
            }
        } else {
            CustinsParamDO minorVersionInfo = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_KEY);
            if (minorVersionInfo == null) {
                throw new RdsException(ErrorCode.CUSTINS_MINOR_VERSION_ATTR_MISSING);
            }
            String currentReleaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(minorVersionInfo.getValue());
            if (StringUtils.isEmpty(currentReleaseDate)) {
                throw new RdsException(ErrorCode.CUSTINS_MINOR_VERSION_ATTR_MISSING);
            }
            if (StringUtils.compare(currentReleaseDate, targetMinorVersionReleaseDate) > 0) {
                logger.error("invalid minor version, expected:{}, current:{}", targetMinorVersionReleaseDate, currentReleaseDate);
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION_LOWER_THAN_CUSTINS);
            }

        }
        return targetMinorVersion;
    }

    /**
     * 支持强制备份
     * 单机和对机都支持
     *
     * @param custins
     */
    public Map<String, Object> rebuildSlaveInstanceLocalMode(Map<String, String> actionParams, CustInstanceDO custins, Integer srcInstanceId)
            throws RdsException {

        Map<String, Object> param = new HashMap<>();
        boolean isForce = mysqlParamSupport.getParameterValue(actionParams, "IsForce", "false").equalsIgnoreCase("true");
        if (isForce) {
            param.put("use_backup_on_master", true);
        }

        param.put("rebuild_instance_id", srcInstanceId);

        // 检查是否有指定版本创建
        String targetMinorVersion = getTargetMinorVersion(actionParams, custins);
        if (targetMinorVersion != null) {
            param.put("minor_version", targetMinorVersion);
        }

        Integer taskId = instanceService
                .rebuildSlaveInstanceLocalModeTask(mysqlParamSupport.getAction(actionParams), custins,
                        mysqlParamSupport.getOperatorId(actionParams), JSON.toJSONString(param));
        taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));

        Map<String, Object> data = new HashMap<>();
        data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
        data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
        data.put(ParamConstants.TASK_ID, taskId);
        return data;
    }


    /**
     * 单机（魔方）备库重搭
     * 暂不支持强制主备备份
     * 只能在单机集群中
     *
     * @param custins
     * @throws RdsException
     */
    public Map<String, Object> primaryRebuildSlaveInstanceRemoteMode(CustInstanceDO custins,
                                                                     Map<String, String> actionParams) throws RdsException {

        Integer resourceMode = clusterService.getClusterResourceMode(custins.getClusterName());
        // not magic cluster
        if (!CLUSTER_RESOURCE_MODE_SINGLE.equals(resourceMode)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }

        // check have a mirror custins
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
        custInstanceQuery.setPrimaryCustinsId(custins.getId());
        Long mirrorCount = custinsService.countCustIns(custInstanceQuery);
        Integer srcInstanceId = CustinsValidator
                .getRealNumber(getParameterValue(actionParams, ParamConstants.INSTANCE_ID));
        InstanceDO srcInstance = instanceService.getInstanceByInsId(srcInstanceId);
        // 复用xdb三节点的follower重建，logger重建
        Integer levelId = custins.getLevelId();
        // 根据实例的instanceId来判断角色，logger节点有自己的规格
        if (srcInstance.getRole() != null && srcInstance.getRole().equals(LOGGER_ROLE)) {
            levelId = srcInstance.getLevelId().intValue();
        }

        if (mirrorCount > 0) {
            throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
        }

        Map<String, Object> taskExtraParams = new HashMap<>();

        String backupSetId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.BACKUP_SET_ID);
        if (StringUtils.isNotEmpty(backupSetId)) {
            if (CustinsSupport.DB_VERSION_MYSQL_55.equalsIgnoreCase(custins.getDbVersion())) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            checkBakSetValid(custins, Long.parseLong(backupSetId));
            taskExtraParams.put("backup_set_id", Long.parseLong(backupSetId));
        }

        //检查是否有指定版本创建
        String targetMinorVersion = getTargetMinorVersion(actionParams, custins);
        if (targetMinorVersion != null) {
            taskExtraParams.put("minor_version", targetMinorVersion);
            taskExtraParams.put("release_date", minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersion));
        }

        if (StringUtils.isNotEmpty(backupSetId) || StringUtils.isNotEmpty(targetMinorVersion)) {
            taskExtraParams.put("recovery_mode", true);
        }

        //创建clone实例
        cloneCustinsForRebuild(custins, levelId);
        if (taskExtraParams.isEmpty()) {
            return rebuild(custins, actionParams);
        }
        return rebuild(custins, actionParams, taskExtraParams);
    }

    /*
    获取实例的备库重搭临时实例
     */
    private CustInstanceDO getRebuildMirrorIns(CustInstanceDO custins) throws RdsException {
        CustInstanceDO rebuildIns = null;
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        if (custins.isRead()) {
            logger.info("readonly query tmp instance");
            custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_READ);
            custInstanceQuery.setPrimaryCustinsId(custins.getPrimaryCustinsId());
            custInstanceQuery.setIsTmp(1);

            List<CustInstanceDO> mirrorCustins = custinsService.getCustIns(custInstanceQuery);
            for (CustInstanceDO mirror : mirrorCustins) {
                if (mirror.getInsName().endsWith(custins.getInsName())) {
                    rebuildIns = mirror;
                    break;
                }
            }
        } else {
            custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
            custInstanceQuery.setPrimaryCustinsId(custins.getId());
            custInstanceQuery.setIsTmp(1);
            List<CustInstanceDO> mirrorCustins = custinsService.getCustIns(custInstanceQuery);
            rebuildIns = mirrorCustins.get(0);
        }
        if (rebuildIns == null) {
            throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
        }
        return rebuildIns;
    }

    public Map<String, Object> rebuild(CustInstanceDO custins, Map<String, String> actionParams, Map<String, Object> param) throws RdsException {
        boolean isAllocateResource = false;
        CustInstanceDO tempCustins = getRebuildMirrorIns(custins);
        try {
            Map<String, Object> data = new HashMap<>(6);

            boolean isForce = mysqlParamSupport.getParameterValue(actionParams, "IsForce", "false").equalsIgnoreCase("true");

            List<InstanceDO> instanceList;
            if (custins.isLogic()) {
                instanceList = instanceService.getInstanceByParentCustinsId(custins.getId());
            } else {
                instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            }

            // check instance id specified exist.
            Integer srcInstanceId = CustinsValidator.getRealNumber(getParameterValue(actionParams, ParamConstants.INSTANCE_ID));

            // 获得用户指定备份 hostins 列表 并设置到custins的属性中
            List<Integer> specifyBackupHostinsList = CustinsValidator
                    .getRealNumberList(getParameterValue(actionParams, ParamConstants.SPECIFY_BACKUP_HOSTINS_LIST));
            custins.setSpecifyBackupHostinsList(specifyBackupHostinsList);

            String region = clusterService.getRegionByCluster(custins.getClusterName());
            ResourceContainer resourceContainer;
            Set<Integer> hostIdSet = getAndCheckHostIdSet(actionParams);
            String resourceStrategy = mysqlParamSupport.getResourceStrategy(actionParams);
            boolean forceRebuildInSite = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(actionParams, PARAM_FORCE_REBUILD_IN_SITE, "false"));

            if (custins.isRead() && instanceList.size() == 2) {
                resourceContainer = custinsRebuildResourceService.getMysqlResContainerForReadOnlyRebuild(hostIdSet,
                        custins, tempCustins, instanceList, srcInstanceId,
                        getParameterValue(actionParams, ParamConstants.SITE_NAME), resourceStrategy, forceRebuildInSite);
            } else {
                resourceContainer = custinsRebuildResourceService.getMysqlResContainerForRebuild(hostIdSet,
                        custins, tempCustins, region, instanceList, srcInstanceId,
                        getParameterValue(actionParams, ParamConstants.SITE_NAME), resourceStrategy, forceRebuildInSite);
            }

            // doc: https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89jyZdNnC4pmON4OW3kdP0wQ
            boolean isEmergentResAlloc = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(actionParams, MySQLParamConstants.EMERGENT_RES_ALLOC, "false"));
            resourceContainer.setEmergencyLevel(isEmergentResAlloc ? 1 : 0);
            resourceContainer.setUserId(custins.getUserId());
            resourceContainer.setRequestId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID));
            resourceContainer.setAccessId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID));
            resourceContainer.setOrderId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID));
            if (custinsParamService.getCustinsParam(custins.getId(), MySQLParamConstants.V6_CPU_MATCH) != null) {
                resourceContainer.setV6CpuMatch(true);
            }
            resourceContainer.setSourceHost(mysqlParamSupport.getInstanceCurrHostId(actionParams));

            InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if (mysqlParamSupport.isMysqlXDBByLevel(instanceLevel)) {
                // XDB v7 机型CPU对齐
                resourceContainer.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
            }
            Response response = resManagerService.allocateRes(resourceContainer);
            isAllocateResource = true;
            if (!response.getCode().equals(200)) {
                custinsService.deleteCustInstance(tempCustins);
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            TransListDO translist = instanceService.createTransListForRebuildSlave(custins, tempCustins,
                    srcInstanceId, instanceList, isForce);

            Integer taskId;
            if (param != null) {
                taskId = instanceService.rebuildSlaveInstanceTask(
                        getAction(actionParams), custins, tempCustins, translist, getOperatorId(actionParams), param);
            } else {
                taskId = instanceService.rebuildSlaveInstanceTask(
                        getAction(actionParams), custins, tempCustins, translist, getOperatorId(actionParams));
            }
            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(actionParams));
            data.put("MigrationID", translist.getId());
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (Exception e) {
            if (e instanceof RdsException) {
                RdsException re = (RdsException) e;
                return createErrorResponse(re.getErrorCode());
            }
            logger.error(e.getMessage(), e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isAllocateResource) {
                custinsService.deleteCustInstance(tempCustins); // 没有申请过资源，出现异常删除临时实例
            }
        }
    }

    public Map<String, Object> rebuild(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return rebuild(custins, actionParams, null);
    }

    public Map<String, Object> rebuildInstanceAllocateRes(CustInstanceDO custins, Integer srcInstanceId,
                                                          List<InstanceDO> instanceList,
                                                          CustInstanceDO tempCustins, Map<String, String> actionParams) throws RdsException {
        String region = clusterService.getRegionByCluster(custins.getClusterName());
        String resourceStrategy = mysqlParamSupport.getResourceStrategy(actionParams);

        // copy mycnf_custinstance from src to temp.
        mycnfService.syncMycnfCustinstances(custins.getId(), tempCustins.getId());

        ResourceContainer resourceContainer;
        Set<Integer> hostIdSet = getAndCheckHostIdSet(actionParams);
        String resoureStartegy = mysqlParamSupport.getResourceStrategy(actionParams);
        if (custins.isCustinsOnDocker()) {
            resourceContainer = custinsRebuildResourceService.getDockerResContainerForRebuild(
                    custins, tempCustins, region, instanceList, srcInstanceId, hostIdSet, resourceStrategy);
        } else {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
        resourceContainer.setAccessId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID));
        resourceContainer.setOrderId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID));
        resourceContainer.setSourceHost(mysqlParamSupport.getInstanceCurrHostId(actionParams));
        Response response = resApi.allocateRes(resourceContainer);
        if (!response.getCode().equals(200)) {
            custinsService.deleteCustInstance(tempCustins);
            return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }
        boolean isForce = getParameterValue(actionParams, ParamConstants.IS_FORCE, "false").equalsIgnoreCase("true");
        TransListDO translist = instanceService.createTransListForRebuildSlave(custins, tempCustins,
                srcInstanceId, instanceList, isForce);
        Integer taskId = instanceService.rebuildSlaveInstanceTask(
                getAction(actionParams), custins, tempCustins, translist, getOperatorId(actionParams));
        taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(actionParams));

        Map<String, Object> data = new HashMap<String, Object>(6);
        data.put("MigrationID", translist.getId());
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", taskId);
        return data;
    }


    public CustInstanceDO cloneCustinsForRebuild(CustInstanceDO custins, Integer levelId) throws RdsException {
        return cloneCustinsForRebuild(custins, true, levelId);
    }

    public CustInstanceDO cloneCustinsForRebuild(CustInstanceDO custins, boolean saveToDB, Integer levelId) throws RdsException {
        CustInstanceDO tmpCustins = cloneCustins(custins, CUSTINS_INSTYPE_MIRROR, "rebuild");
        tmpCustins.setLevelId(levelId);
        if (saveToDB) {
            custinsService.createCustInstanceForRebuildSlave(custins, tmpCustins);
            //此处，临时实例，需要设置为自动小版本升级，这样就会选择最新的灰度版本
            custinsParamService.createCustinsParam(new CustinsParamDO(tmpCustins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto"));
            //此处，如果是polarx_hatp实例，则子实例也是
            boolean isPolarxHatp = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP) != null;
            if (isPolarxHatp) {
                custinsParamService.setCustinsParam(tmpCustins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP, CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES);
            }
        }
        return tmpCustins;
    }

    private CustInstanceDO cloneCustins(CustInstanceDO custins, Integer insType, String namePrefix) throws RdsException {
        // 创建临时实例
        Long timestamp = System.currentTimeMillis();
        CustInstanceDO tempCustins = custins.clone();
        tempCustins.setId(null);
        tempCustins.setClusterName("");
        tempCustins.setInsName(namePrefix + timestamp + "_" + custins.getInsName());
        tempCustins.setStatus(CUSTINS_STATUS_CREATING);
        tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
        tempCustins.setInsType(insType);
        tempCustins.setLevelId(custins.getLevelId());
        tempCustins.setDbVersion(custins.getDbVersion());
        tempCustins.setDiskSize(custins.getDiskSize());
        // 必须和源实例一致
        tempCustins.setConnType(custins.getConnType());
        tempCustins.setPrimaryCustinsId(custins.getId());

        return tempCustins;
    }

    /*
    只读重搭限制临时实例个数
     */
    private void readOnlyLimitRebuildSlaveMirror(CustInstanceDO custins) throws RdsException {
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_READ);
        custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
        custInstanceQuery.setPrimaryCustinsId(custins.getPrimaryCustinsId());

        List<CustInstanceDO> tmpCustinsList = custinsService.getCustIns(custInstanceQuery);
        if (tmpCustinsList.size() > 5) {
            logger.error("primary exists too many readonly tmp instance. num is {} > 5", tmpCustinsList.size());
            throw new RdsException(ErrorCode.TOO_MANY_REL_CUSTINS);
        }

        // 当前只读的重搭实例检查
        for (CustInstanceDO tmpCustins : tmpCustinsList) {
            if (tmpCustins.getInsName().endsWith(custins.getInsName())) {
                throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
            }
        }
    }

    /*
    只读双节点备节点重搭
    备节点重搭构建，重搭节点的申请需要设置为临时实例，类型为只读， 不能设置为mirror
     */
    public Map<String, Object> readonlyRebuildSlaveRemoteMode(CustInstanceDO custins,
                                                              Map<String, String> actionParams) throws RdsException {
        // not magic cluster
        Integer resourceMode = clusterService.getClusterResourceMode(custins.getClusterName());
        if (!CLUSTER_RESOURCE_MODE_SINGLE.equals(resourceMode)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }

        this.readOnlyLimitRebuildSlaveMirror(custins);

        //依据原只读实例，创建只读临时实例
        Long timestamp = System.currentTimeMillis();
        CustInstanceDO tmpCustins = custins.clone();
        tmpCustins.setId(null);
        tmpCustins.setClusterName("");
        tmpCustins.setInsName("rebuild" + timestamp + "_" + custins.getInsName());
        tmpCustins.setStatus(CUSTINS_STATUS_CREATING);
        tmpCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        tmpCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
        tmpCustins.setInsType(CustInsType.CUST_INS_TYPE_READ.getValue());
        tmpCustins.setLevelId(custins.getLevelId());
        tmpCustins.setDbVersion(custins.getDbVersion());
        tmpCustins.setDiskSize(custins.getDiskSize());
        tmpCustins.setConnType(custins.getConnType());
        tmpCustins.setPrimaryCustinsId(custins.getPrimaryCustinsId());
        custinsService.createCustInstanceForRebuildSlave(custins, tmpCustins);
        custinsParamService.createCustinsParam(new CustinsParamDO(tmpCustins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto"));

        return rebuild(custins, actionParams);
    }
}
