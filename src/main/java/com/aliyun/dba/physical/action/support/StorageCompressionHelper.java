package com.aliyun.dba.physical.action.support;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

@Slf4j
@Service
public class StorageCompressionHelper {

    @Resource
    protected CustinsParamService custinsParamService;


    public static final String COMPRESSION_MODE_KEY  = "compression_mode";
    public static final String COMPRESSION_RATIO_KEY  = "compress_ratio_adjust";
    public static final String COMPRESSION_MODE_ON = "on";
    public static final String COMPRESSION_MODE_OFF = "off";
    public static final String COMPRESSION_BEFORE_SIZE = "disk_size_before_compression";


    public boolean isCompressionOn(Integer custinsId){
        CustinsParamDO compressionModeParam = custinsParamService.getCustinsParam(custinsId, COMPRESSION_MODE_KEY);
        if (compressionModeParam != null && StringUtils.equalsIgnoreCase(compressionModeParam.getValue(), COMPRESSION_MODE_ON)) {
            return true;
        }
        return false;
    }


    public  Double getCustinsCompressionRatio(Integer custinsId) {
        CustinsParamDO compressionRatioParam = custinsParamService.getCustinsParam(custinsId, COMPRESSION_RATIO_KEY);
        if (compressionRatioParam == null || StringUtils.isBlank(compressionRatioParam.getValue())) {
            return 1.0;
        }


        try {
            return Double.valueOf(compressionRatioParam.getValue());
        } catch (Exception e) {
            log.error("custins compression ratio {} parse failed, {}", compressionRatioParam.getValue(), JSONObject.toJSONString(e));
            throw e;
        }
    }

    public Integer getLogicalDizeSizeGB(ReplicaSet replicaSet) {
        return replicaSet.getDiskSizeMB() / 1024;
    }

    public Integer getPhysicalDiskSizeGB(ReplicaSet replicaSet) {
        Integer logicalSizeGB = getLogicalDizeSizeGB(replicaSet);
        Double compressionRatio = getCustinsCompressionRatio(replicaSet.getId().intValue());
        return calculatePhysicalSize(logicalSizeGB, compressionRatio);
    }

    public Integer calculatePhysicalSize(Integer logicalSize, Double compressionRatio) {
        if (Objects.isNull(logicalSize) || Objects.isNull(compressionRatio)) {
            return logicalSize;
        }
        BigDecimal logicalSizeBD = BigDecimal.valueOf(logicalSize);
        BigDecimal compressionRatioBD = BigDecimal.valueOf(compressionRatio);
        BigDecimal fiveBD = BigDecimal.valueOf(5);

        BigDecimal result = logicalSizeBD.divide(compressionRatioBD, 10, RoundingMode.HALF_UP)
                .divide(fiveBD, 0, RoundingMode.DOWN)
                .multiply(fiveBD);
        return result.intValue();
    }

    public static Integer calculateLogicalSize(Integer physicalSize, Double compressionRatio) {
        if (Objects.isNull(physicalSize) || Objects.isNull(compressionRatio)) {
            return physicalSize;
        }
        BigDecimal physicalSizeBD = BigDecimal.valueOf(physicalSize);
        BigDecimal compressionRatioBD = BigDecimal.valueOf(compressionRatio);
        BigDecimal fiveBD = BigDecimal.valueOf(5);

        BigDecimal result = physicalSizeBD.multiply(compressionRatioBD)
                .divide(fiveBD, 0, RoundingMode.UP)
                .multiply(fiveBD);
        return result.intValue();
    }
}
