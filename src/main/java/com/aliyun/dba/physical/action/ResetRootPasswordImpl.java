package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.task.support.TaskSupport.TASK_RESET_PASSWORD;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalResetRootPasswordImpl")
public class ResetRootPasswordImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(ResetRootPasswordImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ResetRootPasswordImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
        RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (!custins.canResetRootPassword()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String password = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.ACCOUNT_PASSWORD);
            if ("".equals(password)) {
                custinsParamService.setCustinsParam(custins.getId(),
                    CustinsParamSupport.CUSTINS_PARAM_NAME_RESET_PASSWORD,
                    CustinsParamSupport.CUSTINS_PARAM_VALUE_DISABLE_RESET_PASWORD);
            } else {
                //password is null or other password.
                custinsParamService.setCustinsParam(custins.getId(),
                    CustinsParamSupport.CUSTINS_PARAM_NAME_RESET_PASSWORD,
                    CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_RESET_PASWORD);
            }

            TaskQueueDO taskQueue = new TaskQueueDO(mysqlParamSupport.getAction(actionParams),
                mysqlParamSupport.getOperatorId(actionParams),
                custins.getId(), TASK_TYPE_CUSTINS, TASK_RESET_PASSWORD);
            if (password == null) {
                taskQueue.setParameter("");
            } else {
                Map<String, String> lParaMap = new HashMap<>();
                lParaMap.put("password", password);
                taskQueue.setParameter(JSON.toJSONString(lParaMap));
            }
            taskService.createTaskQueue(taskQueue);
            taskService.updateTaskPenginePolicy(taskQueue.getId(), mysqlParamSupport.getPenginePolicyID(actionParams));

            Map<String, Object> data = new HashMap<String, Object>();
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.TASK_ID, taskQueue.getId());
            return data;
        } catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
