package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.commonkindcode.support.helper.TimezoneHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRepairDBInstanceParameterImpl")
public class RepairDBInstanceParameterImpl implements IAction {


    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;

    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Resource
    protected DbossApi dbossApi;
    @Autowired
    protected MycnfService mycnfService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            doBasicCheck(custins);

            String parameter = mysqlParamSupport.getParameterValue(params, ParamConstants.PARAMETER_NAME);
            if (StringUtils.isBlank(parameter)) {
                log.error("ParameterName is empty");
                throw new RdsException(ErrorCode.INVALID_PARAMETERS);
            }

            if (StringUtils.equalsIgnoreCase(parameter, MySQLParamConstants.DEFAULT_TIME_ZONE)) {
                return repairDefaultTimeZone(custins);
            }

            log.error("repair parameter {} is not supported yet!", parameter);
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        } catch (RdsException re) {
            log.error("got RdsException {}, {}", re.getMessage(), JSONObject.toJSONString(re));
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error("got Exception {}, {}", ex.getMessage(), JSONObject.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    protected void doBasicCheck(CustInstanceDO custins) throws RdsException {
        // check ins status
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        if (custins.isShare()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        // check is standard ins
        if (mysqlDBCustinsService.isMysqlEnterprise(custins)) {
            log.info("ins level is {}, not supported yet!", custins.getLevelId());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
        if (instanceList.size() != 2) {
            log.info("ins instances size is {}, not supported yet!", instanceList.size());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        // check if has running task
        Map<String, Object> taskCondition = new HashMap<>();
        taskCondition.put("custinsId", custins.getId());
        // 0等待  1运行  7暂停 8中断
        int[] taskStatus = {0, 1, 7, 8};
        taskCondition.put("status", taskStatus);
        Integer taskCount = taskService.countTaskQueueByCondition(taskCondition);
        if (taskCount > 0) {
            log.info("ins has unfinished task {}, not supported yet!", taskCount);
            throw new RdsException(ErrorCode.TASK_HAS_EXIST);
        }
    }

    protected Map<String, Object> repairDefaultTimeZone(CustInstanceDO custins) throws Exception {
        // get default_time_zone from meta
        String defaultTimeZone = TimezoneHelper.DEFAULT_TIMEZONE;
        MycnfCustinstanceDO defaultTimeZoneMeta = mycnfService.getMycnfCustinstance(custins.getId(), MySQLParamConstants.DEFAULT_TIME_ZONE);
        if (defaultTimeZoneMeta != null) {
            defaultTimeZone = defaultTimeZoneMeta.getParaValue();
        }

        // get master running time_zone from dboss
        Map<String, Object> variables = dbossApi.getParameter(custins.getId(), "%time_zone");
        String systemTimeZone = String.valueOf(variables.get("system_time_zone"));
        String runningTimeZone = String.valueOf(variables.get("time_zone"));
        log.info("ins default_time_zone {}, system_time_zone {}, time_zone {}", defaultTimeZone, systemTimeZone, runningTimeZone);

        // dispatch task
        TaskQueueDO taskQueue = new TaskQueueDO(mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(),
                custins.getId(), TASK_TYPE_CUSTINS, MySQLParamConstants.TASK_REPAIR_PARAMETER_TIME_ZONE);
        taskService.createTaskQueue(taskQueue);
        taskService.updateTaskPenginePolicy(taskQueue.getId(), mysqlParaHelper.getPenginePolicyID());

        // return data
        Map<String, Object> data = new HashMap<>();
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", taskQueue.getId());
        return data;
    }
}
