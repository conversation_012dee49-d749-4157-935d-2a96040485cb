package com.aliyun.dba.physical.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.common.http.HttpClient;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.resource.dataobject.BakOwnerDO;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.api.NameServiceApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

import static com.aliyun.dba.support.property.ErrorCode.*;
import static com.aliyun.dba.support.property.ErrorCode.DBOSS_CAN_NOT_CONNECT_CUSTINS;

/**
 * mysql大版本升级调用dboss查询信息,需要指定role
 */
@Service
public class DbossApiService{

    private static final Logger logger = LoggerFactory.getLogger(DbossApi.class);


    private static Map<String, String> regionToEndpoint = null;
    private static final String ACCOUNT_EXISTS = "AccountExists";
    private static final String ACCOUNT_NOT_EXISTS = "AccountNotExists";
    private static final String DB_EXISTS = "DBExists";
    private static final String DB_NOT_EXISTS = "DBNotExists";
    private static final String OLD_PASSWORD_WRONG = "OldPasswordWrong";
    private static final String ACTION_NOT_SUPPORT = "ActionNotSupport";

    private static final String ACCOUNT_ADD_ERROR = "Account.AddError";
    private static final String ACCOUNT_QUERY_ERROR = "Account.QueryError";
    private static final String ACCOUNT_DEL_ERROR = "Account.DelError";
    private static final String ACCOUNT_UPDATE_ERROR = "Account.UpdateError";
    private static final String DATABASE_ADD_ERROR = "Database.AddError";
    private static final String DATABASE_QUERY_ERROR = "Database.QueryError";
    private static final String DATABASE_DEL_ERROR = "Database.DelError";
    private static final String DATABASE_UPDATE_ERROR = "Database.UpdateError";
    private static final String DB_CONNECT_FAILED = "Database.ConnectError";

    private static String dbossEndpoint = null;

    @Autowired
    private NameServiceApi nameServiceApi;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private ClusterService clusterService;




    public Map<String, Object> queryEngineCount(Integer custinsId, Integer engine,String role) throws IOException, RdsException {
        String url = String.format("%s/dbs/dbenginecountwithrole", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(3);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("engine", String.valueOf(engine));
        params.put("role",role);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }

    private String getDefaultServiceEndpoint() {
        // 其他实例直接使用默认的 Endpoint
        if (dbossEndpoint == null) {
            String subdomain = nameServiceApi.getGwSubDomain("DBOSS", null, null);
            if (StringUtils.isNotEmpty(subdomain)) {
                dbossEndpoint = "http://" + subdomain;
                return dbossEndpoint;
            }

            BakOwnerDO bakOwner = resourceService.getBakOwnerByType(BakOwnerDO.SERVICE_DBOSS);
            String apiUrl = bakOwner.getApiUrl();
            try {
                JSONObject apiUrlJson = JSON.parseObject(apiUrl);
                dbossEndpoint = apiUrlJson.getString("api");
            } catch (Exception e) {
                dbossEndpoint = String.format("http://%s:%s", bakOwner.getIp(), bakOwner.getPort());
            }
        }
        return dbossEndpoint;
    }



    private Object getAndCheckResponse(Response resp) throws IOException, RdsException {
        assert resp.body() != null;
        Object res = JSON.parse(resp.body().string());
        if (resp.code() == HttpStatus.OK.value()) {
            return res;
        }

        Map<String, String> resMap = (Map<String, String>) res;
        String strCode = resMap.get("code");
        String message = resMap.get("message");
        if (resp.code() == HttpStatus.CONFLICT.value()) {
            if (ACCOUNT_EXISTS.equals(strCode)) {
                throw new RdsException(ACCOUNTNAME_ALREADYEXISTS, message);
            } else if (DB_EXISTS.equals(strCode)) {
                throw new RdsException(DBNAME_ALREADYEXISTS, message);
            } else if (ACTION_NOT_SUPPORT.equals(strCode)) {
                throw new RdsException(ACCOUNT_NOT_SUPPORT, message);
            }
        } else if (resp.code() == HttpStatus.NOT_FOUND.value()) {
            if (ACCOUNT_NOT_EXISTS.equals(strCode)) {
                throw new RdsException(ACCOUNT_NOT_FOUND, message);
            } else if (DB_NOT_EXISTS.equals(strCode)) {
                throw new RdsException(DB_NOT_FOUND, message);
            }
        } else if (resp.code() == HttpStatus.BAD_REQUEST.value()) {
            if (OLD_PASSWORD_WRONG.equals(strCode)) {
                throw new RdsException(OLDPASSWORD_WRONG, message);
            } else if (ACTION_NOT_SUPPORT.equals(strCode)) {
                // 处理账号有绑定 db 的场景，优雅报错
                throw new RdsException(ErrorCode.ACCOUNT_NOT_SUPPORT, message);
            } else if (ACCOUNT_ADD_ERROR.equals(strCode)) {
                throw new RdsException(DBOSS_ADD_ACCOUNT_ERROR, message);
            } else if (ACCOUNT_QUERY_ERROR.equals(strCode)) {
                throw new RdsException(DBOSS_QUERY_ACCOUNT_ERROR, message);
            } else if (ACCOUNT_DEL_ERROR.equals(strCode)) {
                throw new RdsException(DBOSS_DEL_ACCOUNT_ERROR, message);
            } else if (ACCOUNT_UPDATE_ERROR.equals(strCode)) {
                throw new RdsException(DBOSS_UPDATE_ACCOUNT_ERROR, message);
            } else if (DATABASE_ADD_ERROR.equals(strCode)) {
                throw new RdsException(DBOSS_ADD_DATABASE_ERROR, message);
            } else if (DATABASE_QUERY_ERROR.equals(strCode)) {
                throw new RdsException(DBOSS_QUERY_DATBASE_ERROR, message);
            } else if (DATABASE_DEL_ERROR.equals(strCode)) {
                throw new RdsException(DBOSS_DEL_DATBASE_ERROR, message);
            } else if (DATABASE_UPDATE_ERROR.equals(strCode)) {
                throw new RdsException(DBOSS_UPDATE_DATBASE_ERROR, message);
            } else if (DB_CONNECT_FAILED.equals(strCode)) {
                throw new RdsException(DBOSS_CONNECT_INSTANCE_ERROR, message);
            } else {
                // 400异常记录日志， 其他未注明异常保持原逻辑不处理
                logger.error("dboss api response error message : " + message);
            }
        } else if (resp.code() == HttpStatus.INTERNAL_SERVER_ERROR.value()) {
            logger.error("dboss api response error message : " + message);
            throw new RdsException(DBOSS_CAN_NOT_CONNECT_CUSTINS);
        }
        return res;
    }



    private String getServiceEndpoint(Long custinsId) {
        // 如果是专有域实例, 看是否有单独部署的 DBOSS
        if (regionToEndpoint == null) {
            try {
                ResourceDO resource = resourceService.getResourceByResKey("PRIVATE_REGION_DBOSS_ENPOINTS");
                JSONObject apiUrlJson = JSON.parseObject(resource.getRealValue());
                regionToEndpoint = JSONObject.toJavaObject(apiUrlJson, Map.class);
            } catch (Exception e) {
                regionToEndpoint = new HashMap<>();
            }
        }
        if (!CollectionUtils.isEmpty(regionToEndpoint)) {
            ClustersDO cluster = clusterService.getClusterByCustinsId(custinsId);
            String regionId = cluster.getRegion();
            if (StringUtils.isNotEmpty(regionToEndpoint.get(regionId))) {
                return regionToEndpoint.get(regionId);
            }
        }

        // 其他实例直接使用默认的 Endpoint
        return getDefaultServiceEndpoint();
    }

    public Map<String, Object> querySignValue(Integer custinsId, String dbName, String dbTableNames, String columnValue) throws IOException, RdsException {
        String url = String.format("%s/dbs/querySignValue", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(4);
        params.put("custinsId", String.valueOf(custinsId));
        if (dbName != null) {
            params.put("dbName", dbName);
        }
        if (dbTableNames != null) {
            params.put("dbTableNames", dbTableNames);
        }
        if (columnValue != null) {
            params.put("columnValue", columnValue);
        }
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }

    public Map<String, Object> insertSign(Integer custinsId, String dbName, String dbTableNames, String signValue) throws IOException, RdsException {
        String url = String.format("%s/dbs/insertSign", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(4);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("dbName", dbName);
        params.put("dbTableNames", dbTableNames);
        params.put("signValue", signValue);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }
    public Map<String, Object> getBatchHashResult(Integer custinsId, String dbName, String dbTableNames, String batchNum) throws IOException, RdsException {
        String url = String.format("%s/dbs/getBatchHashResult", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(4);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("dbName", dbName);
        params.put("dbTableNames", dbTableNames);
        params.put("batchNum", batchNum);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }
    public Map<String, Object> listTrustTable(Integer custinsId, String dbName) throws IOException, RdsException {
        String url = String.format("%s/dbs/listTrustTable", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(2);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("dbName", dbName);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map) getAndCheckResponse(res);
        return result;
    }
    public Map<String, Object> deleteTrustTable(Integer custinsId, String dbName, String dbTableNames) throws IOException, RdsException {
        String url = String.format("%s/dbs/deleteTrustTable", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(3);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("dbName", dbName);
        params.put("dbTableNames", dbTableNames);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }
    public Map<String, Object> createTrustTable(Integer custinsId, String tableNameAndColumn, String dbName) throws IOException, RdsException {
        String url = String.format("%s/dbs/createTrustTable", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(3);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("tableNameAndColumn", tableNameAndColumn);
        params.put("dbName", dbName);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }
    public Map<String, Object> createTrustTrigger(Integer custinsId, String tableNameAndColumn, String dbName) throws IOException, RdsException {
        String url = String.format("%s/dbs/createTrustTrigger", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(3);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("tableNameAndColumn", tableNameAndColumn);
        params.put("dbName", dbName);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }

    public Map<String, Object> operateEncDBRule(Integer custinsId, String operateType, String operateString) throws IOException, RdsException {
        String url = String.format("%s/dbs/operateEncDBRule", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(3);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("operateType", operateType);
        params.put("operateString", operateString);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }

    public List<Map<String, Object>> queryEncDBInfo(Integer custinsId, String selectName, String isUser) throws IOException, RdsException {
        String url = String.format("%s/dbs/queryEncDBInfo", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(3);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("selectName", selectName);
        params.put("isUser", isUser);
        Response res = HttpClient.get(url, params, null, null);
        List<Map<String, Object>> result = (List<Map<String, Object>>) getAndCheckResponse(res);
        return result;
    }

    public List<Map<String, Object>> queryAllEncDBInfo(Integer custinsId, String isUser) throws IOException, RdsException {
        String url = String.format("%s/dbs/queryAllEncDBInfo", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(2);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("isUser", isUser);
        Response res = HttpClient.get(url, params, null, null);
        List<Map<String, Object>> result = (List<Map<String, Object>>) getAndCheckResponse(res);
        return result;
    }

    public Map<String, Object> selectAllEncDBInfo(Integer custinsId, String isUser) throws IOException, RdsException {
        String url = String.format("%s/dbs/selectAllEncDBInfo", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(2);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("isUser", isUser);
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }

    public void configEncdbParam(Integer custinsId, String param, String value) throws IOException, RdsException {
        String url = String.format("%s/dbs/configEncDBParam", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(3);
        params.put("custinsId", String.valueOf(custinsId));
        params.put("param", param);
        params.put("value", value);
        Response res = HttpClient.get(url, params, null, null);
        getAndCheckResponse(res);
    }

    public Map<String, Object> showEncdbParams(Integer custinsId) throws IOException, RdsException {
        String url = String.format("%s/dbs/showEncDBParams", getServiceEndpoint(Long.valueOf(custinsId)));
        Map<String, String> params = new HashMap<>(1);
        params.put("custinsId", String.valueOf(custinsId));
        Response res = HttpClient.get(url, params, null, null);
        Map<String, Object> result = (Map<String, Object>) getAndCheckResponse(res);
        return result;
    }

}
