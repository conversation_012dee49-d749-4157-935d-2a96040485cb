package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.idao.MysqlCustinsParamIDao;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeAnalyticdbByPrimaryDBInstanceImpl")
public class DescribeAnalyticdbByPrimaryDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(
        DescribeAnalyticdbByPrimaryDBInstanceImpl.class);

    @Autowired
    protected MysqlCustinsParamIDao mysqlCustinsParamIDao;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        List<String> insNameList = mysqlCustinsParamIDao.getInsNameByCustinsParam("SourceRdsInstanceId",
                                custInstanceDO.getId().toString(), "analyticdb");

        Map<String, Object> data = new HashMap<String, Object>(2);
        data.put("AnalyticdbCnt", insNameList.size());
        data.put("AnalyticdbIds", StringUtils.join(insNameList, ","));
        return data;
    }
}
