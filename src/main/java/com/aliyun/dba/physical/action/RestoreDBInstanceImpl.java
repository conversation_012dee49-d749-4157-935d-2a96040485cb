package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.DistributeMode;
import com.alicloud.apsaradb.resmanager.DistributeRule;
import com.alicloud.apsaradb.resmanager.HostinsResModel;
import com.alicloud.apsaradb.resmanager.PortDistributeRule;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.VipResModel;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.LogPlanDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.time.DateUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.dba.bak.support.BakSupport.BAKTYPE_FULL;
import static com.aliyun.dba.bak.support.BakSupport.BAKWAY_XTRABACKUP;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_LASTEST;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAction;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckRestoreType;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getOperatorId;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.CLUSTER_RESOURCE_MODE_PAIR;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_CREATING;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRestoreDBInstanceImpl")
public class RestoreDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(RestoreDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(RestoreDBInstanceImpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected DTZSupport dtzSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (mysqlDBCustinsService.isMysqlEnterprise(custins)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isReadAndWriteLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (!custins.isLogicPrimary()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // 0：恢复到备份集； 1：恢复到时间点 8：恢复到最新时间点
            String restoreType = getAndCheckRestoreType(actionParams);
            Map<String,String> translistParams = new HashMap<>();
            translistParams.put("restoreType", restoreType);

            BakhistoryDO history = null;
            Long realBacksetSize = null;
            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                if (!mysqlEngineCheckService.checkCloneBeforeUpgrade(actionParams,custins.getId())){
                    throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
                }
                Date restoreTime = custinsService.validRestoreByTime(actionParams, custins);
                history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                realBacksetSize = history.getBaksetSize();
            } else if (RESTORE_TYPE_LASTEST.equals(restoreType)){
                // 克隆到最新时间
                DateTime nowUTC = DateTime.now(DateTimeZone.UTC);
                Date nowTime = dtzSupport.getSpecificTimeZoneDate(nowUTC, DATA_SOURCE_BAK);
                history = bakService.getBakhistoryByRecoverTime(custins.getId(), nowTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                realBacksetSize = history.getBaksetSize();
            } else {
                Long bakId = CheckUtils.parseLong(getParameterValue(actionParams, "BackupSetID"), null,
                        null, ErrorCode.BACKUPSET_NOT_FOUND);
                checkService.getAndCheckBakhistory(custins, bakId);
                history = bakService.getBakhistoryByBackupSetId(custins.getId(), bakId);
                realBacksetSize = history.getBaksetSize();
            }

            //检查是否没有备份集id
            if(history == null){
                return createErrorResponse(ErrorCode.INVALID_BAKSET);
            }

            //0: 不保留，覆盖性恢复； 1:保留，恢复到子实例
            boolean isRetainInstance = BakSupport.isRetainInstance(
                    RESTORE_TYPE_TIME.equals(restoreType) || RESTORE_TYPE_LASTEST.equals(restoreType),
                getParameterValue(actionParams, "RetainInstance"), custins.getDbType());

            //检查是否库表恢复
            boolean isRestoreTable = isRetainInstance ? mysqlParamSupport.getAndCheckRestoreTable(actionParams) : false;

            //检查TableMeta参数
            String tableMeta = null;
            if(isRestoreTable){
                tableMeta = mysqlParamSupport.getAndCheckTableMeta(actionParams,history,"restore", custins.getDbVersion(), false);
            }

            //原实例恢复需要的空间
            long needSpace = 0L;
            //库表恢复场景空间检查
            if (isRestoreTable){
                //单位KB，乘以10倍解压缩率
                needSpace = mysqlParamSupport.getAndCheckStorageForRestoreDbtables(tableMeta, history) / 1024 * 10;
                /*InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
                //没有性能数据，不能进行库表恢复
                if(instancePerf == null || instancePerf.getDiskCurr() == null){
                    throw new RdsException(MysqlErrorCode.EVALUATE_STORAGE_FOR_RESTORE_NOT_FOUND.toArray());
                }
                //cust_instance表中单位为MB
                long availSpace = custins.getDiskSize() * 1024 - Long.valueOf(instancePerf.getDiskCurr()) * 1024;
                if(availSpace < needSpace){
                    throw new RdsException(MysqlErrorCode.STORAGE_FOR_RESTORE_NOT_ENOUGH.toArray());
                }*/
            }
            //实例恢复空间检查
            else{
                if (Long.parseLong(custins.getDiskSize().toString()) * 1024 <= realBacksetSize * CustinsSupport.DISK_SIZE_REDUCE_RULE){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
                }
            }

            /*如果实例配置binlog不上传，API直接禁止做还原时间点操作*/
            //上传方式 0：不上传 1：落地上传'
            LogPlanDO logPlan = bakService.getLogPlanByCustinsId(custins.getId());

            if ((RESTORE_TYPE_TIME.equals(restoreType) || RESTORE_TYPE_LASTEST.equals(restoreType)) &&
                    (logPlan == null || !logPlan.isEnableBackupLog())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_BACKUP_LOG_STATUS);
            }

            if (custinsService.hasChildCustInstanceByCustinsId(custins.getId())) {
                return createErrorResponse(ErrorCode.CHILDINSTANCE_EXIST);//已有子实例
            }
            //具有逻辑灾备实例，禁止覆盖性恢复
            if (!isRetainInstance && (
                    custinsService.getGuardInstanceByPrimaryCustinsId(custins.getId()) != null
                            || custinsService.countReadCustInstanceByPrimaryCustinsId(custins.getId())
                                    > 0)) {//具有只读实例，禁止覆盖性恢复
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);//容灾主实例，禁止覆盖性恢复
            }

            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());

            // custins which have 3 or more instance do not support recovery with out retain custins.
            if (custins.isMysql() && instanceList.size() > 2 && !isRetainInstance) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            //主可用区添加
            boolean retainLocation = false;
            AVZInfo avzInfo = avzSupport.getAVZInfo(actionParams);
            if(!avzInfo.isValidForModify()){
                retainLocation = true;
            }

            // 获取网络连接
            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);
            CustinsConnAddrDO defaultCustinsConnAddr = ConnAddrSupport.getCustinsConnAddrDefaultWithoutVPC(custinsConnAddrList);

            TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
            trans.setsCinsReserved(isRetainInstance ? 1 : 0);
            // 恢复到时间点
            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                Date restoreTime = checkService.getAndCheckTimeByDateStr(
                        getParameterValue(actionParams, ParamConstants.RESTORE_TIME),
                        DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME);
                checkService.checkRestoreTimeValid(custins, restoreTime, logPlan);
                trans.setIsBaseTime(1);
                trans.setRecoverTime(restoreTime);
            }
            // 恢复到最近时间点
            else if (RESTORE_TYPE_LASTEST.equals(restoreType)) {
                trans.setIsBaseTime(0);
            }
            // 恢复到备份集
            else {
                Long bakId = CheckUtils.parseLong(getParameterValue(actionParams, "BackupSetID"), null, null, ErrorCode.BACKUPSET_NOT_FOUND);
                // 检查备份集是否有效
                bakService.getBakhistory(custins, bakId);
                trans.setBakhisId(bakId);

            }
            trans.setParameter(JSONObject.toJSONString(translistParams));

            // 获取实例使用的磁盘类型（SSD/SATA），在升降级过程中不允许改变
            String hostType = custinsService.getCustinsHostType(custins.getId());

            CustInstanceDO tempCustins = null;
            List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
            trans.setsHinsid1(insIds.get(0));
            if (insIds.size() > 1) {
                trans.setsHinsid2(insIds.get(1));
            }

            tempCustins = custins.clone();
            //临时实例所需空间为库表总和的3倍，如果小于原实例磁盘，可以临时放大
            if(isRestoreTable){
                //放大3倍，加上2GB，单位KB
                long restoreNeedSpace = needSpace * 3 + 2000 * 1024;
                if(custins.getDiskSize() * 1024 < restoreNeedSpace){
                    tempCustins.setDiskSize(restoreNeedSpace / 1024);
                }
            }
            tempCustins.setId(null);
            tempCustins.setClusterName("");
            tempCustins.setStatus(CUSTINS_STATUS_CREATING);
            tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
            tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
            tempCustins.setInsType(CUSTINS_INSTYPE_TMP);
            String timestamp = String.valueOf(System.currentTimeMillis());
            String preferCluster = null;
            String specifyCluster = null;
            Boolean excludeLocal = false;
            List<CustinsConnAddrDO> tmpCustinsConnAddrList = null;
            //不保留原实例，创建新的实例，然后链路切换到新实例，原实例不再保留
            if (!isRetainInstance) {//要迁移
                tempCustins.setInsType(custins.getInsType());//ins_type设置与源实例一致
                tempCustins.setInsName("tmp" + timestamp + "_" + custins.getInsName());

                // TODO 2.7.22 1. 连接串的命名规则变化；
                int connNum = 1;
                tmpCustinsConnAddrList = new ArrayList<>();
                for (int i = 0; i < connNum; i++) {
                    // 创建连接地址对象
                    String connAddrCust = mysqlParamSupport.getConnAddrCust(
                            "tmp" + timestamp + "-" + custins.getInsName().replace('_', '-') + i,
                            mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()),
                            custins.getDbType());

                    // 创建实例连接对象
                    CustinsConnAddrDO custinsConnAddr = new CustinsConnAddrDO(connAddrCust,
                            defaultCustinsConnAddr.getVport(),
                            defaultCustinsConnAddr.getNetType());

                    tmpCustinsConnAddrList.add(custinsConnAddr);
                }

                // 覆盖性恢复必须为主实例所在region，忽略用户传入的region参数
                retainLocation = true;

                // 如果实例有多个vip，则申请资源时指定集群
                if (custinsConnAddrList.size() > 1 || custins.isProxy()) {
                    specifyCluster = custins.getClusterName();
                }
                preferCluster = custins.getClusterName();
                // 覆盖性恢复要排除本机
                excludeLocal = true;
            }
            else {//保留原实例，即要创建子实例，此时ins_type=1，然后原实例复制临时实例
                tempCustins.setInsName("sub" + timestamp + "_" + custins.getInsName());
                tempCustins.setParentId(custins.getId());
                tempCustins.setGmtExpired(DateUtils.addDays(new Date(), 2));

                // TODO 2.7.22 1. 连接串的命名规则变化；2. 连接端口为DbType的默认值；
                Integer specifyNetType = null;
                if (CustinsParamSupport.hasParameterValue(actionParams, ParamConstants.DB_INSTANCE_NET_TYPE)) {
                    specifyNetType = CustinsSupport.getNetType(actionParams);
                }
                // 兼容阶段，如果未传NetType，则取主实例的NetType，仅当主实例仅有一种网络类型时有效。否则报错
                else {
                    //子实例不能有多个vip
                    if (custinsConnAddrList.size() > 1) {
                        return createErrorResponse(ErrorCode.INVALID_DBINSTANCENETTYPE);
                    }
                    specifyNetType = defaultCustinsConnAddr.getNetType();
                }

                // 非Proxy链路类型实例不支持创建VPC临时实例
                tmpCustinsConnAddrList = new ArrayList<CustinsConnAddrDO>();
                // 创建连接地址对象
                String connAddrCust = mysqlParamSupport.getConnAddrCust(
                        "sub" + timestamp + "-" + custins.getInsName().replace('_', '-'),
                        mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()),
                        custins.getDbType());

                // 创建实例连接对象
                String vpcInstanceId = null;
                if (CustinsSupport.isVpcNetType(specifyNetType)) {
                    vpcInstanceId = getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID);
                    if (vpcInstanceId == null) {
                        vpcInstanceId = tempCustins.getInsName();
                    }
                }
                CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                        connAddrCust,
                        CustinsSupport.getConnPort(null, tempCustins.getDbType()),
                        specifyNetType,
                        CustinsValidator
                                .getRealNumber(getParameterValue(actionParams, ParamConstants.TUNNEL_ID),
                                        -1),
                        getParameterValue(actionParams, ParamConstants.VPC_ID),
                        getParameterValue(actionParams, ParamConstants.VSWITCH_ID),
                        getParameterValue(actionParams, ParamConstants.IP_ADDRESS),
                        vpcInstanceId);

                tmpCustinsConnAddrList.add(custinsConnAddr);

                // 恢复到子实例允许用户指定Region
                //region = region == null ? clusterService.getRegionByCluster(custins.getClusterName()) : region;

                specifyCluster = null;
                preferCluster = null;
            }

            //主可用区添加
            if(retainLocation) {
                avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            }

            //调用资源api
            custinsService.createCustInstanceForTrans(custins, tempCustins);
            //设置临时实例自动小版本升级
            custinsParamService.createCustinsParam(new CustinsParamDO(tempCustins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto"));

            Integer needInsCount;
            Integer resourceMode = clusterService.getClusterResourceMode(custins.getClusterName());
            // 对机模式下恢复到子实例
            if (CLUSTER_RESOURCE_MODE_PAIR.equals(resourceMode)) {
                needInsCount = instanceList.size();
            } else if (isRetainInstance) {
                // 恢复到节点
                needInsCount = 1;
            } else {
                // 恢复到子实例，包含多节点，三节点等
                needInsCount = instanceList.size();
            }

            //ResourceContainer resourceContainer = new ResourceContainer(region, custins.getDbType());

            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, custins.getDbType());
            resourceContainer.setUserId(custins.getUserId());
            resourceContainer.setRequestId(getParameterValue(actionParams, ParamConstants.REQUEST_ID));
            resourceContainer.setClusterName(specifyCluster);
            resourceContainer.setPreferClusterName(preferCluster);
            resourceContainer.setAccessId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID));
            resourceContainer.setOrderId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID));
            resourceContainer.setSourceHost(mysqlParamSupport.getInstanceCurrHostId(actionParams));
            if (CustinsParamSupport.hasParameterValue(actionParams, ParamConstants.REQUEST_ID)) {
                resourceContainer.setRequestId(getParameterValue(actionParams, ParamConstants.REQUEST_ID));
            }
            CustinsResModel custinsResModel = new CustinsResModel(tempCustins.getId());
            custinsResModel.setConnType(custins.getConnType());
            custinsResModel.setPreferProxyGroupId(custins.getProxyGroupId());
            // init host ins res model
            HostinsResModel hostinsResModel = new HostinsResModel(tempCustins.getLevelId());
            hostinsResModel.setHostType(instanceList.get(0).getHostType());
            hostinsResModel.setInsCount(needInsCount);
            hostinsResModel.setDiskSizeSold(tempCustins.getDiskSize());
            // get disk size used
            Long cusDiskSize = this.getDiskSizeUsed(custins);
            if (!(RESTORE_TYPE_TIME.equals(restoreType) || RESTORE_TYPE_LASTEST.equals(restoreType))) {
                Long backupSetID = CheckUtils.parseLong(getParameterValue(actionParams, "BackupSetID"), null,
                        null, ErrorCode.BACKUPSET_NOT_FOUND);
                Long bakSetDiskSize = this.getBakSetDiskSizeUsed(custins, backupSetID);
                if (bakSetDiskSize != null && bakSetDiskSize != -1 && bakSetDiskSize > cusDiskSize) {
                    hostinsResModel.setDiskSizeUsed(bakSetDiskSize);
                } else {
                    hostinsResModel.setDiskSizeUsed(cusDiskSize);
                }
            } else {
                hostinsResModel.setDiskSizeUsed(cusDiskSize);
            }

            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setCabinetDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setSpecifyHostIdSet(CustinsParamSupport.getAndCheckHostIdSet(actionParams));
            InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            InsLevelExtraInfo.updateDistributeRule(distributeRule, instanceLevel.getExtraInfo());

            if (excludeLocal) {
                for (InstanceDO instance : instanceList) {
                    distributeRule.addExcludeHostId(instance.getHostId());
                }
            }
            hostinsResModel.setDistributeRule(distributeRule);

            // 如果dns,需要前后端口一致
            if (custins.isDns()) {
                PortDistributeRule portDistributeRule = new PortDistributeRule();
                Set<Integer> ports = new HashSet<>(1);
                ports.add(instanceList.get(0).getPort());
                portDistributeRule.setSpecifyPortSet(ports);
                hostinsResModel.setPortDistributeRule(portDistributeRule);
            }

            custinsResModel.setHostinsResModel(hostinsResModel);
            for (CustinsConnAddrDO custinsConnAddr : tmpCustinsConnAddrList) {
                VipResModel vipResModel = new VipResModel(defaultCustinsConnAddr.getNetType());
                vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
                vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
                vipResModel.setVip(custinsConnAddr.getVip());
                vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
                vipResModel.setVpcId(custinsConnAddr.getVpcId());
                vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
                vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
                vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
                custinsResModel.addVipResModel(vipResModel);
            }
            resourceContainer.addCustinsResModel(custinsResModel);

            if (mysqlParamSupport.isMysqlXDBByLevel(instanceLevel)) {
                // XDB v7 机型CPU对齐
                resourceContainer.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
            }

            Response<AllocateResRespModel> response = resManagerService.allocateRes(resourceContainer);
            //主可用区添加
            custinsParamService.updateAVZInfo(custins.getId(),avzInfo);
            if (!response.getCode().equals(200)) {
                custinsService.deleteCustInstance(tempCustins);
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            AllocateResRespModel.CustinsResRespModel custinsResRespModel = response.getData().getCustinsResRespModelList()
                    .get(0);

            tempCustins.setClusterName(custinsResRespModel.getClusterName());
            tempCustins.setConnType(custinsResModel.getConnType());

            trans.setdCinsid(tempCustins.getId());
            insIds = custinsService.getInstanceIdsByCustinsId(tempCustins.getId());
            trans.setdHinsid1(insIds.get(0));
            if (insIds.size() > 1) {
                trans.setdHinsid2(insIds.get(1));
            } else {
                trans.setdHinsid2(0);
            }

            Integer taskId = null;
            String taskKey = null;
            //库表恢复场景
            if(isRestoreTable){
                //TableMeta参数存入translist的parameter参数
                translistParams.put("table_meta", tableMeta);
                trans.setParameter(JSONObject.toJSONString(translistParams));
                //task_key 为：recover_ins_dbstables
                taskId = instanceService.restoreCustInstanceDBsTablesTask(
                    getAction(actionParams), custins, trans, tempCustins, getOperatorId(actionParams));
                taskKey = TaskSupport.TASK_RECOVER_INS_DBSTABLES;
            }
            else{
                taskId = instanceService.restoreCustInstanceTask(
                    getAction(actionParams), custins, trans, tempCustins, getOperatorId(actionParams));
                taskKey = TaskSupport.TASK_RECOVER_INS;
            }

            Map<String, Object> data = new HashMap<String, Object>(10);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("RetainInstance", isRetainInstance ? 1 : 0);
            data.put("MigrationID", trans.getId());
            data.put("ChildDBInstanceID", tempCustins.getId());
            data.put("ChildDBInstanceName", tempCustins.getInsName());
            data.put("TaskId", taskId);
            //说明当前任务类型
            data.put("TaskKey", taskKey);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    protected Long getDiskSizeUsed(CustInstanceDO custins) {
        try {
            InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
            return new BigDecimal(instancePerf.getDiskCurr()).longValue();
        } catch (Exception e) {
            logger.error("Get instance perf failed for custins: " + custins.getId(), e);
        }
        return custins.getDiskSize();
    }

    protected Long getBakSetDiskSizeUsed(CustInstanceDO custins, Long backupSetID) throws RdsException {
        BakhistoryDO history = bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetID);
        Long diskSize = null;
        JSONObject jsonObject = JSONObject.parseObject(history.getBaksetInfo());
        diskSize = jsonObject.getLongValue("disk_size");
        return diskSize;
    }
}
