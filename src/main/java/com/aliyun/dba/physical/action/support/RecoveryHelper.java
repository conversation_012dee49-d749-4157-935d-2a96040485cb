package com.aliyun.dba.physical.action.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.aliyun.dba.bak.dataobject.ArchivelogListDO;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.HostInfo;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.CustinsRebuildResourceServiceImpl;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;


@Service
public class RecoveryHelper {
    private static final LogAgent logger = LogFactory.getLogAgent(RecoveryHelper.class);

    @Resource
    protected InstanceService instanceService;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected ClusterService clusterService;
    @Resource
    protected CustinsRebuildResourceServiceImpl custinsRebuildResourceService;
    @Resource
    protected TaskService taskService;
    @Resource
    protected IResApi resApi;
    @Resource
    protected MycnfService mycnfService;
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected CommonProviderService commonProviderService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected CustinsParamService custinsParamService;
    @Resource
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private BakService bakService;
    @Resource
    private IpWhiteListService ipWhiteListService;
    @Resource
    private DbsService dbsService;
    @Resource
    private InstanceIDao instanceIDao;
    @Resource
    private ModuleService moduleService;
    @Resource
    private CustinsIDao custinsIDao;
    @Resource
    private HostService hostService;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;
    @Resource
    private ResourceService resourceService;

    @Resource
    protected AVZSupport avzSupport;
    @Resource
    protected ClusterIDao clusterIDao;

    public void checkSupportInstance(CustInstanceDO custins, InstanceLevelDO instanceLevel) throws RdsException {
        if (custins.isShare()) {
            logger.error("custins {} type is share, not support", custins.getInsName());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        if (custins.isLogic()) {
            logger.error("custins {} character is logic, not support", custins.getInsName());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        // 主实例， 只支持高可用形态
        if (!instanceLevel.isStandardLevel()) {
            logger.error("custins {}, category {} not is standard", custins.getInsName(), instanceLevel.getCategory());
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CATEGORY);
        }

        //暂时只支持主实例 因为只读实例一般是需要先等主实例恢复再重建恢复，与主实例数据保持一致，避免恢复的时候主与只读数据不一样
        if (custins.getInsType() != 0) {
            logger.error("custins {}, insType {} not main", custins.getInsName(), custins.getInsType());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        if (custins.isProxy() || StringUtils.equalsIgnoreCase(custins.getConnType(), CONN_TYPE_PROXY)){
            logger.error("custins {} recovery, not support netType : proxy");
            throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
        }

        if (StringUtils.equalsIgnoreCase(custins.getBizType(), "aligroup")){
            logger.error("custins {} recovery, biztype is not aliyun");
            throw new RdsException(ErrorCode.INVALID_BIZ_TYPE);
        }
    }

    public void checkBakSetValid(CustInstanceDO custins, Long backupSetId) throws RdsException {
        BakhistoryDO bakHistory = bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId);
        if (bakHistory == null) {
            logger.error("backup set {} not found.", backupSetId);
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        if (!"ok".equalsIgnoreCase(bakHistory.getStatus()) || bakHistory.getIsAvail() != 1) {
            logger.error("backup set {} status {} isAvail {}.", backupSetId, bakHistory.getStatus(), bakHistory.getIsAvail());
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        if (!("DATA".equals(bakHistory.getType()) && "P".equals(bakHistory.getBakWay()) && 0 == bakHistory.getBakScale())) {
            logger.error("backup set {} type {} way {} bakType {} scale {}.", backupSetId, bakHistory.getType(), bakHistory.getBakWay(), bakHistory.getBakType(), bakHistory.getBakScale());
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        //增量备份暂时不放开
        //if ("I".equals(bakHistory.getBakType())) {
        //    logger.info("backup set {} is increment backup set.", backupSetId);
        //    JSONObject baksetInfo = JSON.parseObject(bakHistory.getBaksetInfo());
        //    if (baksetInfo == null) {
        //        logger.error("increment backup set {} invalid bakset info, baksetInfo is null", backupSetId);
        //        throw new RdsException(ErrorCode.INVALID_BAKSET);
        //    }
        //    Long lastHisID = baksetInfo.getLong("last_his_id");
        //    if (lastHisID == null) {
        //        logger.error("increment backup set {} invalid bakset info, lastHisID is null", backupSetId);
        //        throw new RdsException(ErrorCode.INVALID_BAKSET);
        //    }
        //    BakhistoryDO fullBakHistory = bakService.getBakhistoryByBackupSetId(custins.getId(), lastHisID);
        //    if (fullBakHistory == null) {
        //        logger.error("get full backup set error,increment backup set info {}", bakHistory.getBaksetInfo());
        //        throw new RdsException(ErrorCode.INVALID_BAKSET);
        //    }
        //    logger.info("increment backup set {} lastHisID {}", backupSetId, lastHisID);
        //}
        JSONObject slaveStatus = JSON.parseObject(bakHistory.getSlaveStatus(), JSONObject.class);
        if (slaveStatus == null) {
            logger.error("backupset {} invalid slave status, slave status is null", backupSetId);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        String binlogFileName = slaveStatus.getString("BINLOG_FILE");
        if (StringUtils.isEmpty(binlogFileName)) {
            logger.error("backupset {} invalid slave status, binlogFileName is null", backupSetId);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        Integer binlogFileNo = Integer.parseInt(binlogFileName.substring(binlogFileName.lastIndexOf(".") + 1));
        List<ArchivelogListDO> binlogList = bakService.getArchivelogByCustinsId(custins.getId(), binlogFileNo, binlogFileNo);
        if (binlogList == null || binlogList.isEmpty()) {
            logger.error("The log {} that needs to sync for this bak {} is not existed).", binlogFileName, backupSetId);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        Integer hostinsId = bakHistory.getHostinsId();
        boolean isBinlogExist = false;
        for (ArchivelogListDO binlog : binlogList) {
            if (hostinsId.equals(binlog.getHostinsId()) && (binlog.getRemoteStatus() == 2 || "host".equals(binlog.getLocation()) && binlog.getLocalStatus() == 0)) {
                isBinlogExist = true;
                break;
            }
        }
        if (!isBinlogExist) {
            logger.error("The log {} that needs to sync for this bak {} is not existed).", binlogFileName, backupSetId);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
    }

    /**
     * 检查指定备份集的数据库内核小版本号
     *
     * @param custins
     * @param backupSetId
     * @return 数据库内核小版本号, null 表示没有获取到该参数
     * @throws RdsException
     */
    public String getBakSetMinorVersion(CustInstanceDO custins, Long backupSetId) throws RdsException {
        BakhistoryDO bakHistory = bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId);
        if (bakHistory == null) {
            logger.error("backupset {} not found.", backupSetId);
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        } else if (!"ok".equalsIgnoreCase(bakHistory.getStatus()) || bakHistory.getIsAvail() != 1) {
            logger.error("backupset {} status {} isAvail {}.", backupSetId, bakHistory.getStatus(), bakHistory.getIsAvail());
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        JSONObject slaveStatus = JSON.parseObject(bakHistory.getSlaveStatus(), JSONObject.class);
        if (slaveStatus == null) {
            logger.error("invalid slave status, slave status is null");
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        return slaveStatus.getString("MINOR_VERSION");
    }

    /**
     * 检查并获取目标数据库内核小版本
     *
     * @param actionParams
     * @param custins
     * @return 数据库内核小版本号，null表示不指定
     * @throws RdsException
     */
    public String getTargetMinorVersion(Map<String, String> actionParams, CustInstanceDO custins) throws RdsException {
        String targetMinorVersionParam = mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion");
        if (StringUtils.isEmpty(targetMinorVersionParam)) {
            // 如果TargetMinorVersion不存在，则查看MinorVersion是否存在
            targetMinorVersionParam = mysqlParamSupport.getParameterValue(actionParams, "MinorVersion");
            if (StringUtils.isEmpty(targetMinorVersionParam)) {
                return null;
            }
        }
        String targetMinorVersionReleaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionParam);
        if (StringUtils.isEmpty(targetMinorVersionReleaseDate)) {
            logger.error("invalid targetMinorVersion {}, can't parse release data from it.", targetMinorVersionParam);
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }
        String classCode = instanceService.getInstanceLevelByLevelId(custins.getLevelId()).getClassCode();
        String dbEngine = MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL;
        String targetMinorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
                custins.getDbType(),
                custins.getDbVersion(),
                classCode,
                dbEngine,
                targetMinorVersionParam);
        if (StringUtils.isEmpty(targetMinorVersion)) {
            logger.error("invalid targetMinorVersion {},checkAndGetTargetMinorVersion return null", targetMinorVersionParam);
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }

        return targetMinorVersion;
    }

    public Integer recoverySlaveInstanceTask(
            String action, CustInstanceDO custins, CustInstanceDO tempCustins, TransListDO translist,
            Integer operatorId, Map<String, Object> extraParams) throws RdsException {
        // 同步父实例的账号和IP白名单信息
        dbsService.syncAllDbsAndAccounts(custins, tempCustins);
        ipWhiteListService.syncCustinsIpWhiteList(custins.getId(), tempCustins.getId());

        instanceIDao.createTransList(translist);

        Integer targetId = tempCustins.getId();
        String taskKey = "dr_recovery_instance_slave";

        Map<String, Object> param = new HashMap<String, Object>(extraParams);
        param.put("trans_id", translist.getId());
        param.put("trans_by_trans_id", true);
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, targetId,
                TASK_TYPE_CUSTINS,
                taskKey,
                String.valueOf(JSON.toJSONString(param)));
        taskService.createTaskQueue(taskQueue);
        instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
        return taskQueue.getId();

    }
}
