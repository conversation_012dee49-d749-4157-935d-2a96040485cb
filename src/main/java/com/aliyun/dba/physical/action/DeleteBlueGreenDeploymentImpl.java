package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDeleteBlueGreenDeploymentImpl")
public class DeleteBlueGreenDeploymentImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.DeleteBlueGreenDeploymentImpl.class);
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private BlueGreenDeploymentService blueGreenDeploymentService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            logger.info("DeleteBlueGreenDeploymentImpl start, custins : {}, params : {}", JSON.toJSONString(custins), JSON.toJSONString(params));
            String aliUid = mysqlParamSupport.getUID(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String mode = params.get("mode".toLowerCase());
            String deploymentName = params.get("blueGreenDeploymentName".toLowerCase());
            Map<String, Object> data = blueGreenDeploymentService.deleteBlueGreenDeployment(regionId, aliUid, custins, deploymentName, mode);
            logger.info("DeleteBlueGreenDeploymentImpl end, custins : {}, params : {}", JSON.toJSONString(data));
            return data;
        } catch (RdsException ex) {
            logger.error("DeleteBlueGreenDeploymentImpl failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("DeleteBlueGreenDeploymentImpl Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}