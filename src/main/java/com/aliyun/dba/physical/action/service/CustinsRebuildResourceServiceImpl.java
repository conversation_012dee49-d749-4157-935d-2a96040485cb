package com.aliyun.dba.physical.action.service;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.*;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.DockerInsLevelParseConfig;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.host.dataobject.HostInfo;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.Validator;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;


@Service
public class CustinsRebuildResourceServiceImpl implements CustinsResourceService {
    private static final Logger logger = Logger.getLogger(CustinsRebuildResourceServiceImpl.class);
    @Autowired
    private ClusterService clusterService;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private CustinsParamService custinsParamService;

    @Autowired
    private HostService hostService;

    @Autowired
    private CustinsService custinsService;

    @Autowired
    private ConnAddrCustinsService connAddrCustinsService;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    protected AVZSupport avzSupport;

    @Autowired
    protected ClusterIDao clusterIDao;

    @Override
    public void createResourceRecord(String action, String UUID, String resourceName, String insName, String content) {
        Map<String, Object> map = new HashMap<>(5);
        map.put("className", action);
        map.put("resourceName", resourceName);
        map.put("insName", insName);
        map.put("content", content);
//        SystemConfig.getInstance().createResourceRecord(UUID, map);
    }


    @Override
    public Map<String, Object> getShareResource(String action, String UUID, CustInstanceDO custins, String region,
                                                String clusterName, String account, String dbname,
                                                Integer netType) throws RdsException {
        List<String> clusters = clusterService.getClusterNamesByRegion(region, custins.getDbType());
        if (clusters.isEmpty()) {
            throw new RdsException(ErrorCode.REGION_NOT_FOUND);
        }
        if (Validator.isNotNull(clusterName)) {
            if (clusters.contains(clusterName)) {
                clusters.clear();
                clusters.add(clusterName);
            } else {
                throw new RdsException(ErrorCode.CLUSTER_NOT_FOUND);
            }
        }
        boolean flag = false;
        for (String cluster : clusters) {
            if (instanceService.haveShareInstanceByClusterName(cluster, custins.getDbType(),
                    custins.getDbVersion(), netType)) {
                clusterName = cluster;
                flag = true;
                break;
            }
        }
        if (!flag) {
            createResourceRecord(action, UUID, region, custins.getInsName(),
                    "region has no avalibale share host instance.");
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
        }
        Integer[] insIds = instanceService
                .getShareInsIdByClusterName(clusterName, custins.getDbType(),
                        custins.getDbVersion(), netType, dbname, account);
        if (insIds == null) {
            createResourceRecord(action, UUID, region, custins.getInsName(),
                    "cluster has no avalibale share host instance, #param[cluster:" + clusterName
                            + ",database:" + custins.getDbType() + ",net_type:" + netType
                            + ",dbname:" + dbname + ",account:" + account + "]");

            throw new RdsException(ErrorCode.DB_AND_ACCOUNT_NAME_ALREADYEXISTS);
        }
        Map<String, Object> res = new HashMap<String, Object>(2);
        res.put("clusterName", clusterName);
        res.put("insIds", insIds);
        return res;
    }


    @Override
    public ResourceContainer getDockerResContainerForRebuild(
            CustInstanceDO custins, CustInstanceDO tempCustins, String region,
            List<InstanceDO> instanceList, Integer srcInstanceId, Set<Integer> hostIdSet, String resourceStrategy
    ) throws RdsException {

        InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        DockerInsLevelParseConfig config = custinsService.parseDockerInsExtraInfo(insLevel.getExtraInfo());

        // DistributeMode siteMode = config.getDistributePolicy().getSite();
        DistributeMode cabinetMode = config.getDistributePolicy().getCabinet();
        DistributeMode hostMode = config.getDistributePolicy().getHost();

        ResourceContainer resourceContainer = new ResourceContainer(region,
                CustinsSupport.DB_TYPE_DOCKER);
        CustinsResModel custinsResModel = new CustinsResModel(tempCustins.getId());
        // init host res model
        HostinsResModel hostinsResModel = new HostinsResModel(custins.getLevelId());
        hostinsResModel.setInsCount(1);
        hostinsResModel.setInsPortCount(config.getPortCountPerIns());
        hostinsResModel.setDiskType(config.getDiskType());
        if (resourceStrategy != null) {
            hostinsResModel.setStrategy(resourceStrategy);
        }

        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSpecifyHostIdSet(hostIdSet);
        for (InstanceDO instance : instanceList) {
            if (instance.getId().equals(srcInstanceId)) {
                // keep site name not changed
                distributeRule.addSpecifySiteName(instance.getSiteName());
                // set current host id low priority
                distributeRule.addInferiorHostId(instance.getHostId());
                // keep host type same
                hostinsResModel.setHostType(instance.getHostType());
            } else {
                if (DistributeMode.FORCE_SCATTER.equals(cabinetMode)) {
                    distributeRule.addExcludeCabinet(instance.getCabinet());
                } else if (DistributeMode.TRY_SCATTER.equals(cabinetMode)) {
                    distributeRule.addInferiorCabinet(instance.getCabinet());
                } else if (DistributeMode.FORCE_CROWD.equals(cabinetMode)) {
                    distributeRule.addSpecifyCabinet(instance.getCabinet());
                }

                if (DistributeMode.FORCE_SCATTER.equals(hostMode)) {
                    distributeRule.addExcludeHostId(instance.getHostId());
                } else if (DistributeMode.TRY_SCATTER.equals(hostMode)) {
                    distributeRule.addInferiorHostId(instance.getHostId());
                } else if (DistributeMode.FORCE_CROWD.equals(hostMode)) {
                    distributeRule.addSpecifyHostId(instance.getHostId());
                }
            }
        }
        // append host res model
        custinsResModel.setHostinsResModel(hostinsResModel);
        // specify connType
        custinsResModel.setConnType(custins.getConnType());
        // append custins res model
        resourceContainer.addCustinsResModel(custinsResModel);
        return resourceContainer;
    }

    public String classicSlaveDistributeInstanceSite(CustInstanceDO custins, List<InstanceDO> instanceList,
                                                     Integer srcInstanceId, String siteName){
        HashSet<String> mazClusterSiteList = new HashSet<String>();
        String masterSiteName = "";
        HashSet<String> otherNodeSiteNameList = new HashSet<String>();
        for (InstanceDO instance : instanceList) {
            if (!instance.getId().equals(srcInstanceId)) {
                otherNodeSiteNameList.add(instance.getSiteName());
            }

            if (instance.getRole() != null && instance.getRole() == 0) {
                masterSiteName = instance.getSiteName();
            }
        }
        try{
            //主备节点相同的情况下做一次选取；
            //组合可用区做一次查询， 转换为hash列表，存在单可用区/多可用区
            ClustersDO siteCluster = clusterService.getClusterByClusterName(custins.getClusterName());
            String clusterSiteName = siteCluster.getSiteName();
            if (siteCluster.getIsMultiSite() == 1 && StringUtils.isNotBlank(clusterSiteName)){
                if (clusterSiteName.contains(",")){
                    mazClusterSiteList = new HashSet<String>(Arrays.asList(clusterSiteName.split(",")));
                } else {
                    mazClusterSiteList.add(clusterSiteName);
                }
            } else {
                //单机房直接返回
                return siteName;
            }

            //多机房， 先做一次未分配节点的选取
            List<String> noUsedSiteNameList = mazClusterSiteList.stream().filter(x -> !otherNodeSiteNameList.contains(x)).collect(Collectors.toList());
            if (!noUsedSiteNameList.isEmpty()){
                //if multi zone, then give random site;
                int randomIndex = new Random().nextInt(noUsedSiteNameList.size());
                logger.info(String.format("instance: %s ,mazDistributeInstanceSite schedule noUsedSiteNameList, %s", srcInstanceId, noUsedSiteNameList.get(randomIndex)));
                return noUsedSiteNameList.get(randomIndex);
            }
            //都做了分配, 例如三节点只有两个机房，使用非master site
            for (String site: mazClusterSiteList){
                if (!StringUtils.equalsIgnoreCase(site, masterSiteName)){
                    logger.info(String.format("instance: %s ,mazDistributeInstanceSite schedule random mazClusterSiteList, %s", srcInstanceId, site));
                    return site;
                }
            }
        }catch (Exception e){
            logger.warn("try mazDistributeInstanceSite schedule node failed, {} ", e);
        }
        return siteName;
    }

    public Set<String> getAllClusterSitesByInstance(CustInstanceDO custins){

        HashSet<String> clusterSiteList = new HashSet<String>();

        try{
            //Get all sites from the cluster info
            ClustersDO siteCluster = clusterService.getClusterByClusterName(custins.getClusterName());
            String clusterSiteName = siteCluster.getSiteName().toLowerCase();
            if (siteCluster.getIsMultiSite() == 1 && StringUtils.isNotBlank(clusterSiteName)){
                if (clusterSiteName.contains(",")){
                    clusterSiteList = new HashSet<String>(Arrays.asList(clusterSiteName.split(",")));
                } else {
                    clusterSiteList.add(clusterSiteName);
                }
                logger.info(String.format("instance: %s ,getAllClusterSitesByInstance schedule clusterSiteList, %s", custins.getId(), JSON.toJSONString(clusterSiteList)));
                return clusterSiteList;
            } else if (siteCluster.getIsMultiSite() != 1 && StringUtils.isNotBlank(clusterSiteName)){
                //Single site cluster，then return directly
                clusterSiteList.add(clusterSiteName);
                return clusterSiteList;
            }

        }catch (Exception e){
            logger.warn("try getAllClusterSitesByInstance schedule node failed, {} ", e);
        }
        return clusterSiteList;
    }

    @Override
    public ResourceContainer getMysqlResContainerForRebuild(Set<Integer> hostIdSet,
                                                            CustInstanceDO custins, CustInstanceDO tempCustins,
                                                            String region,
                                                            List<InstanceDO> instanceList, Integer srcInstanceId,
                                                            String siteName, String resourceStrategy) throws RdsException {
        return getMysqlResContainerForRebuild(hostIdSet, custins, tempCustins, region,
                instanceList, srcInstanceId, siteName, resourceStrategy, false);
    }

    public ResourceContainer getMysqlResContainerForRebuild(Set<Integer> hostIdSet,
                                                            CustInstanceDO custins, CustInstanceDO tempCustins,
                                                            String region,
                                                            List<InstanceDO> instanceList, Integer srcInstanceId,
                                                            String siteName, String resourceStrategy, boolean forceRebuildInSite) throws RdsException {
        if (forceRebuildInSite && StringUtils.isBlank(siteName)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "siteName can't be empty when forceRebuildInSite is true");
        }
        ResourceContainer resourceContainer = avzSupport.getRebuildSlaveResourceContainer(custins, hostIdSet, srcInstanceId);

        CustinsResModel custinsResModel = new CustinsResModel(tempCustins.getId());
        custinsResModel.setConnType(custins.getConnType());
        custinsResModel.setPreferProxyGroupId(custins.getProxyGroupId());

        // init host res model
        InstanceDO instanceDO = instanceList.get(0);
        Integer masterPort = instanceList.stream().filter(i -> CUSTINS_ROLE_MASTER.equals(i.getRole())).map(InstanceDO::getPort).findFirst().orElse(instanceDO.getPort());
        HostinsResModel hostinsResModel = new HostinsResModel(tempCustins.getLevelId());
        hostinsResModel.setInsCount(1);
        hostinsResModel.setRole(CUSTINS_ROLE_SLAVE);
        // 日志节点设置日志节点的角色
        InstanceDO srcInstanceDO = instanceService.getInstanceByInsId(srcInstanceId);
        if (srcInstanceDO.getRole() != null && srcInstanceDO.getRole().intValue() == CUSTINS_ROLE_LOGGER) {
            hostinsResModel.setRole(CUSTINS_ROLE_LOGGER);
        }

        hostinsResModel.setDiskSizeUsed(instanceService.getInstanceDiskUsage(custins, 0));
        if (resourceStrategy != null) {
            hostinsResModel.setStrategy(resourceStrategy);
        }
        if (CUSTINS_TYPE_SHARE.equals(instanceDO.getType())) {
            hostinsResModel.setType(instanceDO.getType());
        }
        // 如果dns,需要前后端口一致
        if (custins.isDns()) {
            PortDistributeRule portDistributeRule = new PortDistributeRule();
            Set<Integer> ports = new HashSet<>(1);
            ports.add(masterPort);
            portDistributeRule.setSpecifyPortSet(ports);
            hostinsResModel.setPortDistributeRule(portDistributeRule);
        }
        // append host res model
        custinsResModel.setHostinsResModel(hostinsResModel);
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSpecifyHostIdSet(hostIdSet);

        boolean isMultiAVZDispenseMode = custinsParamService.getDispenseMode(custins.getId()).equals(ParamConstants.DispenseMode.MultiAVZDispenseMode);

        InstanceDO masterInstanceDO = null;
        for (InstanceDO instance : instanceList) {
            if (instance.getId().equals(srcInstanceId)) {
                hostinsResModel.setHostType(instance.getHostType());
                distributeRule.addExcludeHostId(instance.getHostId());
                // fix 组合可用区模式， 设置了siteName则使用设置的， 若没有设置则使用instance自身的
                if (StringUtils.isNotBlank(siteName)) {
                    distributeRule.addSpecifySiteName(siteName);
                }else if (StringUtils.isBlank(siteName) && !isMultiAVZDispenseMode) {
                    if (hostIdSet.isEmpty()) {
                        //if not specified host,then give default site for slave;
                        String slaveSiteName = classicSlaveDistributeInstanceSite(custins, instanceList, srcInstanceId, instance.getSiteName());
                        distributeRule.addSpecifySiteName(slaveSiteName);

                    } else {
                        //if specified host, then Check if the host's site belong to the cust_ins's cluster-sites;
                        Set<String> requiredSites = getAllClusterSitesByInstance(custins);
                        for (Integer hostId: hostIdSet) {
                            HostInfo hostInfoDO = hostService.getHostInfo(hostId, null, null);
                            if (!requiredSites.contains(hostInfoDO.getSiteName().toLowerCase())) {
                                throw new RdsException(ErrorCode.INVALID_TARGET_TRANSFER_HOSTIDS);
                            };
                        }
                    }
                }
            } else {
                distributeRule.addExcludeHostId(instance.getHostId());
                if (!StringUtils.isEmpty(instance.getCabinet())) {
                    distributeRule.addExcludeCabinet(instance.getCabinet());
                }
                distributeRule.addInferiorSiteName(instance.getSiteName());
            }

            if (instance.getRole() != null && instance.getRole() == 0) {
                masterInstanceDO = instance;
            }
        }
        /**
         主可用区模式下有些情况需要指定机房保证备节点可用区不变
         * 1. 重搭的节点可用区在主可用区, 包括单可用区部署以及主节点切换到了备节点的情况
         * 2. 重搭的节点可用区不在主可用区, 包括指定备节点可用区以及随机备节点可用区的情况
          */
        if (isMultiAVZDispenseMode && !forceRebuildInSite) {
            // cust_instance 对应的集群就是主可用区子域对应的集群, 所在的机房为主可用区所在的机房
            String custinsSiteName = clusterService.getClusterByClusterName(custins.getClusterName()).getSiteName();
            String[] slaveLocations = custinsParamService.getSlaveLocations(custins.getId());

            String masterSiteName = masterInstanceDO.getSiteName();

            // 主节点在主可用区
            if (StringUtils.equalsIgnoreCase(masterSiteName, custinsSiteName)) {
                //设置备可用区的情况下， 保证在选择的机房内
                if (slaveLocations.length > 0) {
                    String locationWillRebuild = slaveLocations[srcInstanceDO.getRole() - 1];
                    ClustersDO clustersDO = clusterIDao.getClusterBySubDomain(locationWillRebuild);
                    if (clustersDO == null) {
                        logger.error("can not find cluster by subdomain: " +  locationWillRebuild);
                        throw new RdsException(ErrorCode.INTERNAL_FAILURE, "slave location not found");
                    }
                    String siteNameWillRebuild = clustersDO.getSiteName();
                    distributeRule.addSpecifySiteName(siteNameWillRebuild);
                    logger.info(String.format("Instance: %s will be rebuild by specified slave site name: %s", srcInstanceId, siteNameWillRebuild));
                } else {
                    //没有设置slavelocation， 设置了sitename使用设置的， 没有设置的情况：
                    if (StringUtils.isBlank(siteName)){
                        //主备节点相同，则排除主， 不同则使用原来的
                        if (StringUtils.equalsIgnoreCase(custinsSiteName, srcInstanceDO.getSiteName())){
                            logger.info(String.format("Instance: %s will be rebuild custins, not correct, src site name is %s, exclude site name: %s", srcInstanceId, srcInstanceDO.getSiteName(), custinsSiteName));
                            distributeRule.addExcludeSiteName(custinsSiteName);
                        } else {
                            logger.info(String.format("Instance: %s will be rebuild custins, correct,  use site name: %s", srcInstanceId, srcInstanceDO.getSiteName()));
                            distributeRule.addSpecifySiteName(srcInstanceDO.getSiteName());
                        }
                    }
                }
            } else {
                // 1.备在设置的主可用区， 主节点不在， 则强制设置保持不变，不考虑master是否在slavelocation
                // 2.主备节点都不在设置的主可用区内， 则备节点强制在主可用区， 不考虑设置的slave
                logger.info(String.format("Instance: %s will be rebuild custins, master site name: %s, slave site name: %s, force use site name: %s", srcInstanceId, masterSiteName, srcInstanceDO.getSiteName(), custinsSiteName));
                distributeRule.addSpecifySiteName(custinsSiteName);
            }
        }
        List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), true);
        if (readCustinsList != null && readCustinsList.size() > 0) {
            for (CustInstanceDO readIns : readCustinsList) {
                custinsResModel.addExcludeCustinsId(readIns.getId());
            }
        }

        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        InsLevelExtraInfo.updateDistributeRule(distributeRule, instanceLevel.getExtraInfo());

        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

        CustinsConnAddrDO defaultCustinsConnAddr = ConnAddrSupport
                .getCustinsConnAddrDefaultWithoutVPC(
                        custinsConnAddrList);

        // 创建连接地址对象
        String connAddrCust = resourceService.getConnAddrCust(
                tempCustins.getInsName().replace('_', '-'), custins.getDbType());


        // 创建实例连接对象
        CustinsConnAddrDO custinsConnAddr = new CustinsConnAddrDO(
                connAddrCust, defaultCustinsConnAddr.getVport(),
                defaultCustinsConnAddr.getNetType());


        if (!(tempCustins.getKindCode() == 0 && tempCustins.getDbType().equals("mysql") && tempCustins.getIsTmp() == 1 && tempCustins.getInsType() == CUSTINS_INSTYPE_MIRROR)){
            VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
            vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
            vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
            vipResModel.setVip(custinsConnAddr.getVip());
            vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
            vipResModel.setVpcId(custinsConnAddr.getVpcId());
            vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
            vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
            vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
            custinsResModel.addVipResModel(vipResModel);
        }

        custinsResModel.setHostinsResModel(hostinsResModel);
        resourceContainer.addCustinsResModel(custinsResModel);
        return resourceContainer;
    }

    public ResourceContainer getMysqlResContainerForReadOnlyRebuild(Set<Integer> hostIdSet,
                                                                    CustInstanceDO custins, CustInstanceDO tempCustins,
                                                                    List<InstanceDO> instanceList, Integer srcInstanceId,
                                                                    String siteName, String resourceStrategy, boolean forceRebuildInSite) throws RdsException {
        if (forceRebuildInSite && StringUtils.isBlank(siteName)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "siteName can't be empty when forceRebuildInSite is true");
        }
        ResourceContainer resourceContainer = avzSupport.getRebuildSlaveResourceContainer(custins, hostIdSet, srcInstanceId);
        CustinsResModel custinsResModel = new CustinsResModel(tempCustins.getId());
        custinsResModel.setConnType(custins.getConnType());

        InstanceDO instanceDO = instanceList.get(0);
        HostinsResModel hostinsResModel = new HostinsResModel(tempCustins.getLevelId());
        hostinsResModel.setInsCount(1);
        hostinsResModel.setRole(CUSTINS_ROLE_SLAVE);

        InstanceDO srcInstanceDO = instanceService.getInstanceByInsId(srcInstanceId);
        if (custins.isReadOrBackup()) {
            CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            Long primaryDiskUsed = instanceService.getInstanceDiskUsage(primaryCustins, 0);
            Long readDiskUsed = instanceService.getInstanceDiskUsage(custins, 0);
            hostinsResModel.setDiskSizeUsed(Math.max(primaryDiskUsed, readDiskUsed));
        } else {
            hostinsResModel.setDiskSizeUsed(instanceService.getInstanceDiskUsage(custins, 0));
        }

        if (resourceStrategy != null) {
            hostinsResModel.setStrategy(resourceStrategy);
        }

        // 如果dns,需要前后端口一致
        if (custins.isDns()) {
            PortDistributeRule portDistributeRule = new PortDistributeRule();
            Set<Integer> ports = new HashSet<>(1);
            ports.add(instanceDO.getPort());
            portDistributeRule.setSpecifyPortSet(ports);
            hostinsResModel.setPortDistributeRule(portDistributeRule);
        }
        // append host res model
        custinsResModel.setHostinsResModel(hostinsResModel);
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSpecifyHostIdSet(hostIdSet);

        boolean isMultiAVZDispenseMode = custinsParamService.getDispenseMode(custins.getId()).equals(ParamConstants.DispenseMode.MultiAVZDispenseMode);
        InstanceDO masterInstanceDO = null;
        for (InstanceDO instance : instanceList) {
            if (instance.getId().equals(srcInstanceId)) {
                hostinsResModel.setHostType(instance.getHostType());
                distributeRule.addExcludeHostId(instance.getHostId());
                // fix 组合可用区模式， 设置了siteName则使用设置的， 若没有设置则使用instance自身的
                if (StringUtils.isNotBlank(siteName)) {
                    distributeRule.addSpecifySiteName(siteName);
                }else if (StringUtils.isBlank(siteName) && !isMultiAVZDispenseMode) {
                    if (hostIdSet.isEmpty()) {
                        //if not specified host,then give default site for slave;
                        String slaveSiteName = classicSlaveDistributeInstanceSite(custins, instanceList, srcInstanceId, instance.getSiteName());
                        distributeRule.addSpecifySiteName(slaveSiteName);

                    } else {
                        //if specified host, then Check if the host's site belong to the cust_ins's cluster-sites;
                        Set<String> requiredSites = getAllClusterSitesByInstance(custins);
                        for (Integer hostId: hostIdSet) {
                            HostInfo hostInfoDO = hostService.getHostInfo(hostId, null, null);
                            if (!requiredSites.contains(hostInfoDO.getSiteName().toLowerCase())) {
                                throw new RdsException(ErrorCode.INVALID_TARGET_TRANSFER_HOSTIDS);
                            };
                        }
                    }
                }

            } else {
                distributeRule.addExcludeHostId(instance.getHostId());
                if (!StringUtils.isEmpty(instance.getCabinet())) {
                    distributeRule.addExcludeCabinet(instance.getCabinet());
                }
                distributeRule.addInferiorSiteName(instance.getSiteName());
            }

            if (instance.getRole() != null && instance.getRole() == 0) {
                masterInstanceDO = instance;
            }
        }
        /**
         主可用区模式下有些情况需要指定机房保证备节点可用区不变
         * 1. 重搭的节点可用区在主可用区, 包括单可用区部署以及主节点切换到了备节点的情况
         * 2. 重搭的节点可用区不在主可用区, 包括指定备节点可用区以及随机备节点可用区的情况
         */
        if (isMultiAVZDispenseMode && !forceRebuildInSite) {
            // cust_instance 对应的集群就是主可用区子域对应的集群, 所在的机房为主可用区所在的机房
            String custinsSiteName = clusterService.getClusterByClusterName(custins.getClusterName()).getSiteName();
            String[] slaveLocations = custinsParamService.getSlaveLocations(custins.getId());
            String masterSiteName = masterInstanceDO.getSiteName();

            // 主节点在主可用区
            if (StringUtils.equalsIgnoreCase(masterSiteName, custinsSiteName)) {
                //设置备可用区的情况下， 保证在选择的机房内
                if (slaveLocations.length > 0) {
                    String locationWillRebuild = slaveLocations[srcInstanceDO.getRole() - 1];
                    ClustersDO clustersDO = clusterIDao.getClusterBySubDomain(locationWillRebuild);
                    if (clustersDO == null) {
                        logger.error("can not find cluster by subdomain: " +  locationWillRebuild);
                        throw new RdsException(ErrorCode.INTERNAL_FAILURE, "slave location not found");
                    }
                    String siteNameWillRebuild = clustersDO.getSiteName();
                    distributeRule.addSpecifySiteName(siteNameWillRebuild);
                    logger.info(String.format("Instance: %s will be rebuild by specified slave site name: %s", srcInstanceId, siteNameWillRebuild));
                } else {
                    //没有设置slavelocation， 设置了sitename使用设置的， 没有设置的情况：
                    if (StringUtils.isBlank(siteName)) {
                        //主备节点相同，则排除主， 不同则使用原来的
                        if (StringUtils.equalsIgnoreCase(custinsSiteName, srcInstanceDO.getSiteName())) {
                            logger.info(String.format("Instance: %s will be rebuild custins, not correct, src site name is %s, exclude site name: %s", srcInstanceId, srcInstanceDO.getSiteName(), custinsSiteName));
                            distributeRule.addExcludeSiteName(custinsSiteName);
                        } else {
                            logger.info(String.format("Instance: %s will be rebuild custins, correct,  use site name: %s", srcInstanceId, srcInstanceDO.getSiteName()));
                            distributeRule.addSpecifySiteName(srcInstanceDO.getSiteName());
                        }
                    }
                }
            } else {
                // 1.备在设置的主可用区， 主节点不在， 则强制设置保持不变，不考虑master是否在slavelocation
                // 2.主备节点都不在设置的主可用区内， 则备节点强制在主可用区， 不考虑设置的slave
                logger.info(String.format("Instance: %s will be rebuild custins, master site name: %s, slave site name: %s, force use site name: %s", srcInstanceId, masterSiteName, srcInstanceDO.getSiteName(), custinsSiteName));
                distributeRule.addSpecifySiteName(custinsSiteName);
            }
        }

        // 只读, 需要与主实例打散
        custinsResModel.addExcludeCustinsId(custins.getPrimaryCustinsId());
        List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getPrimaryCustinsId(), true);
        if (readCustinsList != null && readCustinsList.size() > 0) {
            for (CustInstanceDO readIns : readCustinsList) {
                if (!readIns.getId().equals(custins.getId())){
                    custinsResModel.addExcludeCustinsId(readIns.getId());
                }
            }
        }

        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        InsLevelExtraInfo.updateDistributeRule(distributeRule, instanceLevel.getExtraInfo());

        custinsResModel.setHostinsResModel(hostinsResModel);
        resourceContainer.addCustinsResModel(custinsResModel);
        return resourceContainer;
    }

    /*
    Note重要： 不要随意改动，恢复后可用区部署信息会乱， 故障恢复逃逸后再治理
    实例恢复使用， 其他情况禁止调用
    1.指定了机房的，直接使用指定机房
    2.指定主机的，直接使用指定主机
    3.都没有指定，则走正常的getMysqlResContainerForRebuild

    恢复用的实例不申请链路（指定机房/主机； 原逻辑也不应该申请链路）
     */
    public ResourceContainer getMysqlResContainerForRecoveryNode(CustInstanceDO custins, CustInstanceDO tempCustins,
                                                                 List<InstanceDO> instanceList, Integer srcInstanceId,
                                                                 String resourceStrategy,
                                                                 String siteName, Set<Integer> hostIdSet) throws RdsException {
        //机房& 主机都没有指定退化原逻辑
        if (StringUtils.isEmpty(siteName) && (hostIdSet==null || hostIdSet.isEmpty())){
            logger.info(String.format("%s get recovery resource not set host or sitename, so schedule by getMysqlResContainerForRebuild", custins.getInsName()));
            return getMysqlResContainerForRebuild(hostIdSet, custins,  tempCustins, "",
                    instanceList,  srcInstanceId, siteName,  resourceStrategy,  false);
        }

        //设置主机资源属性
        HostinsResModel hostinsResModel = new HostinsResModel(tempCustins.getLevelId());
        hostinsResModel.setInsCount(1);
        hostinsResModel.setRole(CUSTINS_ROLE_SLAVE);
        hostinsResModel.setDiskSizeUsed(instanceService.getInstanceDiskUsage(custins, 0));
        if (resourceStrategy != null) {
            hostinsResModel.setStrategy(resourceStrategy);
        }

        InstanceDO instanceDO = instanceList.get(0);
        Integer masterPort = instanceList.stream().filter(i -> CUSTINS_ROLE_MASTER.equals(i.getRole())).map(InstanceDO::getPort).findFirst().orElse(instanceDO.getPort());
        if (CUSTINS_TYPE_SHARE.equals(instanceDO.getType())) {
            hostinsResModel.setType(instanceDO.getType());
        }
        // 如果dns,需要前后端口一致
        if (custins.isDns()) {
            PortDistributeRule portDistributeRule = new PortDistributeRule();
            Set<Integer> ports = new HashSet<>(1);
            ports.add(masterPort);
            portDistributeRule.setSpecifyPortSet(ports);
            hostinsResModel.setPortDistributeRule(portDistributeRule);
        }

        // 设置打散主机策略
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        //两个不同时生效， 优先 生效机房级别的
        if (StringUtils.isNotBlank(siteName)) {
            distributeRule.addSpecifySiteName(siteName);
        } else if (hostIdSet.size() > 0){
            distributeRule.setSpecifyHostIdSet(hostIdSet);
        }

        for (InstanceDO instance : instanceList) {
            distributeRule.addExcludeHostId(instance.getHostId());
            if (instance.getId().equals(srcInstanceId)) {
                hostinsResModel.setHostType(instance.getHostType());
            }
            if (!StringUtils.isEmpty(instance.getCabinet())) {
                distributeRule.addExcludeCabinet(instance.getCabinet());
            }
        }
        //如果规格有特殊的， 设置到打散策略里
        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        InsLevelExtraInfo.updateDistributeRule(distributeRule, instanceLevel.getExtraInfo());

        // 设置实例级别策略
        CustinsResModel custinsResModel = new CustinsResModel(tempCustins.getId());
        custinsResModel.setConnType(custins.getConnType());
        custinsResModel.setPreferProxyGroupId(custins.getProxyGroupId());

        // 设置打散策略， 与只读打散
        List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), true);
        if (readCustinsList != null && readCustinsList.size() > 0) {
            for (CustInstanceDO readIns : readCustinsList) {
                custinsResModel.addExcludeCustinsId(readIns.getId());
            }
        }

        custinsResModel.setHostinsResModel(hostinsResModel);

        ResourceContainer resourceContainer = avzSupport.getRebuildSlaveResourceContainer(custins, hostIdSet, srcInstanceId);
        resourceContainer.addCustinsResModel(custinsResModel);
        return resourceContainer;
    }
}
