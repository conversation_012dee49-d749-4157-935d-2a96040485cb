package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.DistributeMode;
import com.alicloud.apsaradb.resmanager.DistributeRule;
import com.alicloud.apsaradb.resmanager.HostinsResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.VipResModel;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.CheckBaksetDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.entity.CustinsState.STATE_MAINTAINING;
import static com.aliyun.dba.custins.entity.CustinsState.STATE_READINS_MAINTAINING;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAction;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckHostIdSet;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckRestoreType;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getOperatorId;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getPenginePolicyID;
import static com.aliyun.dba.custins.support.CustinsSupport.CONN_TYPE_LVS;
import static com.aliyun.dba.custins.support.CustinsSupport.CONN_TYPE_PROXY;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_CREATING;
import static com.aliyun.dba.custins.support.CustinsSupport.getAndCheckBizType;
import static com.aliyun.dba.custins.support.CustinsSupport.getNetType;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_MASTER;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_SLAVE;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;
import static com.aliyun.dba.support.property.ParamConstants.*;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_PRIMARY;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalExchangeDBInstanceImpl")
public class ExchangeDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ExchangeDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected MySQLService mySQLService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);

            //原逻辑
            String action = getParameterValue(actionParams, ParamConstants.ACTION);

            //只读实例单节点变更为双节点， 将备用只读挪移到只读
            if (isUpgradeReadIns(custins, actionParams)){
                return readInsUpgradeStandard(custins, actionParams);
            }

            TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
            Map<String, Object> translistParamMap = new HashMap<>(8);

            trans.setsCinsid(custins.getId());
            trans.setsCinsReserved(1);
            List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
            trans.setsHinsid1(insIds.get(0));
            trans.setsLevelid(custins.getLevelId());
            if (insIds.size() > 1) {
                trans.setsHinsid2(insIds.get(1));
            }

            CustInstanceDO peerins = mysqlParamSupport.getAndCheckTargetCustInstance(actionParams);
            List<Integer> peerInsIds = custinsService.getInstanceIdsByCustinsId(peerins.getId());
            trans.setdCinsid(peerins.getId());
            trans.setsLevelid(peerins.getLevelId());
            trans.setdHinsid1(peerInsIds.get(0));
            if (peerInsIds.size() > 1) {
                trans.setdHinsid2(peerInsIds.get(1));
            } else {
                trans.setdHinsid2(0);
            }

            trans.setParameter(JSON.toJSONString(translistParamMap));

            String taskKey = "exchange_ins";
            Integer id = mySQLService.createExchangeInstanceTask(getAction(actionParams), custins, peerins,
                    getOperatorId(actionParams), trans, taskKey);

            Map<String, Object> data = new HashMap<String, Object>(10);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", id);
            //返回值做任务类型区分
            data.put("TaskKey", taskKey);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public boolean isUpgradeReadIns(CustInstanceDO custins, Map<String, String> actionParams){
        String upgradeStandard = mysqlParamSupport.getParameterValue(actionParams, "UpgradeStandard");
        return StringUtils.equalsIgnoreCase(upgradeStandard, "true");
    }

    //只读实例单节点升级为双节点
    public Map<String, Object> readInsUpgradeStandard(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException{
        if (!custins.isRead() || custins.getKindCode() != 0){
            throw new RdsException(ErrorCode.UNSUPPORTED_DB_TYPE);
        }
        if (custins.isProxy() || StringUtils.equalsIgnoreCase(custins.getConnType(), CONN_TYPE_PROXY) || custins.isShare()){
            throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
        }
        if (StringUtils.equalsIgnoreCase(custins.getBizType(), "aligroup")){
            throw new RdsException(ErrorCode.INVALID_BIZ_TYPE);
        }

        CustInstanceDO primaryins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
        if (!primaryins.isActive() || !custins.isActive()){
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        if (primaryins.isProxy()){
            logger.error("primary ins conn type is proxy, not permit upgrade");
            throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
        }

        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        if (!InstanceSupport.CATEGORY_STANDARD.equals(instanceLevel.getCategory())){
            logger.error("instance is not standard");
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CATEGORY);
        }

        boolean isXdbRead = mysqlParamSupport.isXdbReadCustins(custins);
        if (isXdbRead){
            logger.error("custins is xdb, not support");
            throw new RdsException(ErrorCode.UNSUPPORTED_DB_TYPE);
        }

        List<InstanceDO> readInstanceList = instanceService.getInstanceByCustinsId(custins.getId());

        CustInstanceDO targetDBInstance = null;
        String targetDBInstanceName = mysqlParamSupport.getParameterValue(actionParams, "targetdbinstancename");
        if (StringUtils.isNotBlank(targetDBInstanceName)){
            targetDBInstance = mysqlParamSupport.getAndCheckTargetCustInstance(actionParams);
            if (!targetDBInstance.isReadBackup()){
                logger.error("targetDBInstanceName {} is not bak read ins", targetDBInstance.getInsName());
                throw new RdsException(ErrorCode.INVALID_INS_TYPE);
            }
        } else {
            // 若没有传入备用只读实例， 则选取一个只读
            // 优先选取 只读 + 备用只读在同集群的，以及在同机房的，其他情况任选
            List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getPrimaryCustinsId(), true);
            for (CustInstanceDO readIns:readCustinsList){
                if (readIns.isReadBackup() && readIns.isActive()){
                    targetDBInstance = readIns;
                    // 不同规格的先跳过， 全网不同规格约1100个
                    if (!readIns.getLevelId().equals(custins.getLevelId())){
                        continue;
                    }
                    if (StringUtils.equalsIgnoreCase(readIns.getClusterName(), custins.getClusterName())){
                        break;
                    } else {
                        List<InstanceDO> targetInstanceList = instanceService.getInstanceByCustinsId(readIns.getId());
                        if (StringUtils.equalsIgnoreCase(targetInstanceList.get(0).getSiteName(), readInstanceList.get(0).getSiteName())){
                            break;
                        }
                    }
                }
            }
            if (targetDBInstance == null){
                logger.error("{} to exchange bak readins not found", custins.getInsName());
                throw new RdsException(ErrorCode.UNSUPPORTED_READ_OR_BAKREAD_STATE);
            }
        }

        List<Integer> targetInsIds = custinsService.getInstanceIdsByCustinsId(targetDBInstance.getId());
        TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
        Map<String, Object> transListParamMap = new HashMap<>(8);

        trans.setsCinsid(custins.getId());
        trans.setsCinsReserved(1);
        // trans.setsHinsid1(insIds.get(0));
        trans.setsHinsid1(readInstanceList.get(0).getId());
        trans.setsHinsid2(0);
        trans.setsLevelid(custins.getLevelId());

        trans.setdCinsid(targetDBInstance.getId());
        trans.setdLevelid(targetDBInstance.getLevelId());
        trans.setdHinsid1(targetInsIds.get(0));
        trans.setdHinsid2(0);

        transListParamMap.put("exchangeBakReadIns", "1");
        trans.setParameter(JSON.toJSONString(transListParamMap));

        String taskKey = "exchange_ins";
        Integer id = mySQLService.createExchangeInsTaskWithParam(getAction(actionParams), custins,
                getOperatorId(actionParams), trans, taskKey, transListParamMap);

        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), STATE_READINS_MAINTAINING.getState(), STATE_READINS_MAINTAINING.getComment());
        custinsService.updateCustInstanceStatusByCustinsId(targetDBInstance.getId(), STATE_MAINTAINING.getState(), STATE_MAINTAINING.getComment());

        Map<String, Object> data = new HashMap<String, Object>(10);
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", id);
        data.put("TaskKey", taskKey);
        return data;
    }
}
