package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckRestoreType;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalEvaluateBakSetImpl")
public class EvaluateBakSetImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateBakSetImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected DTZSupport dtzSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckSourceCustInstance(actionParams);
            mysqlParamSupport.cloneValidSrcCustins(custins);

            BakhistoryDO history = null;
            String restoreType = getAndCheckRestoreType(actionParams);
            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                if (!mysqlEngineCheckService.checkCloneBeforeUpgrade(actionParams,custins.getId())){
                    throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
                }
                // 根据备份时间点查询备份集大小
                DateTime restoreTimeUTC = custinsService.validRestoreByTimeSafe(actionParams, custins);
                //转化为Bak库时间进行比较
                Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_BAK);
                history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                if (mysqlParamSupport.isMysqlXdbByCustins(custins)){
                    // xdb 按时间点clone必须要有 CONSENSUS_APPLY_INDEX和BINLOG_FILE 字段 用于克隆实例的回放
                    if (!JSONObject.parseObject(history.getSlaveStatus()).containsKey("CONSENSUS_APPLY_INDEX") ||
                            !JSONObject.parseObject(history.getSlaveStatus()).containsKey("BINLOG_FILE")) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETSTATUS);
                    }
                }
            } else if (RESTORE_TYPE_LASTEST.equals(restoreType)){
                // 克隆到最新时间
                DateTime nowUTC = DateTime.now(DateTimeZone.UTC);
                Date nowTime = dtzSupport.getSpecificTimeZoneDate(nowUTC, DATA_SOURCE_BAK);
                history = bakService.getBakhistoryByRecoverTime(custins.getId(), nowTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                if (mysqlParamSupport.isMysqlXdbByCustins(custins)){
                    // xdb 按时间点clone必须要有 CONSENSUS_APPLY_INDEX和BINLOG_FILE 字段 用于克隆实例的回放
                    if (!JSONObject.parseObject(history.getSlaveStatus()).containsKey("CONSENSUS_APPLY_INDEX") ||
                            !JSONObject.parseObject(history.getSlaveStatus()).containsKey("BINLOG_FILE")) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETSTATUS);
                    }
                }
            }
            Map<String, Object> data = new HashMap<String, Object>(10);
            data.put("HisId", history.getHisId());
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
