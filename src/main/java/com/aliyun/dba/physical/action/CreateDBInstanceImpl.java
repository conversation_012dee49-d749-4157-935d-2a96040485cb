package com.aliyun.dba.physical.action;

import NameServiceCommon.NameServiceClient;
import NameServiceModels.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.AllocateVipRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.entity.BaksetMetaInfo;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.ConnAddrService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.WhitelistTemplateService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.commonkindcode.support.ParamChecker;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.DbsDO;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.ImmutableList;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.ImmutableMap;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import io.kubernetes.client.util.common.Collections;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;


import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckHostIdSet;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.*;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;
import static com.aliyun.dba.support.property.RdsConstants.INSTANCE_TYPE;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCreateDBInstanceImpl")
public class CreateDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(CreateDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CreateDBInstanceImpl.class);

    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected AccountService accountService;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsResourceService custinsResourceService;
    @Autowired
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MySQLService mySQLService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected MinorVersionService minorVersionService;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Autowired
    private MysqlParamGroupHelper mysqlParamGroupHelper;
    @Autowired
    private CustinsParamGroupsService custinsParamGroupsService;
    @Resource
    private DbsGateWayService dbsGateWayService;


    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Resource
    private ConnAddrService connAddrService;
    @Autowired
    private WhitelistTemplateService whitelistTemplateService;
    @Autowired
    protected StorageCompressionHelper storageCompressionHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {

        try {
            //xdb 5.7 & 8.0
            if (mysqlParamSupport.isMysqlXDBByParams(params)) {
                return doCreateXDBInstance(custins, params);
            }
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            //得到实例类型，版本，资源分配策略
            String dbType = mysqlParamSupport.getAndCheckDBType(params, null);
            String dbVersion = mysqlParamSupport.getAndCheckDBVersion(params, dbType, true);
            String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
            String resourceStrategy = mysqlParamSupport.getResourceStrategy(params);
            String uid = getParameterValue(params, ParamConstants.UID);
            // 支持实例创建指定参数模板
            String paramGroupId = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_PARAM_GROUP_ID);

            String defaultTimeZone = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_PARAM_TIME_ZONE, "");
            if (StringUtils.equalsIgnoreCase(defaultTimeZone, MySQLParamConstants.SYSTEM_TIME_ZONE)) {
                logger.error("default_time_zone=SYSTEM may cause master slave time_zone inconsistent!");
                return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
            }

            Map<String, String> customMysqlParams = mysqlParamSupport.getAndCheckMysqlCustomParams(params);
            if (StringUtils.isNotBlank(paramGroupId)) {
                // MGR复制模式，本地盘不支持
                String syncMode = SysParamGroupHelper.getSyncMode(paramGroupId).toString();
                if (mysqlParamGroupHelper.isMgr(syncMode)) {
                    logger.error("local ssd not not support MGR param group {}", paramGroupId);
                    throw new RdsException(ErrorCode.INVALID_PARAM_GROUP_CODE);
                }
                // 参数模板信息检查
                // FIXME：此处暂时忽略category校验与存储引擎校验
                SysParamGroupHelper.sysParamGroupIdValidation(paramGroupId, dbType, dbVersion, "", "");
                parameterGroupTemplateGenerator.paramGroupMatchValidate(dbType, dbVersion, "", paramGroupId, false);
            }

            String upgradeMinorVersionOption;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.AUTO_UPGRADE_MINOR_VERSION)) {
                upgradeMinorVersionOption = mysqlParamSupport.getParameterValue(params, ParamConstants.AUTO_UPGRADE_MINOR_VERSION);
                if (!ParamConstants.AUTO_UPGRADE_MINOR_VERSION_OPTIONS.contains(upgradeMinorVersionOption)) {
                    throw new RdsException(ErrorCode.INVALID_ITEM_VALUE);
                }
            } else {
                upgradeMinorVersionOption = resourceService.getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).size() > 0 ? resourceService.getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).get(0) : "Auto";
            }
            mysqlParamSupport.getAndSetContainerTypeAndHostTypeIfEmpty(params, dbType, dbVersion, classCode);
            String containerType = mysqlParamSupport.getParameterValue(params, ParamConstants.CONTAINER_TYPE,
                    CustinsSupport.CONTAINER_TYPE_HOST);

            if (mysqlParamSupport.hasParameter(params, ParamConstants.DB_INSTANCE_ID)) {
                // Resource allocated already. just update it.
                Integer custinsId = Integer.parseInt(
                        mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_ID));
                custins = custinsService.getCustInstanceByCustinsId(custinsId);
                if (custins == null) {
                    return createErrorResponse(ErrorCode.INVALID_INSTANCE_ID);
                }

                Integer userId = mysqlParamSupport.getAndCreateUserId(params);
                custins.setUserId(userId);

            } else {
                custins = new CustInstanceDO(mysqlParamSupport.getAndCreateUserId(params),
                        mysqlParamSupport.getAndCheckServiceType(params),
                        dbType, mysqlParamSupport.getCustinsMaxDbs(dbType),
                        mysqlParamSupport.getCustinsMaxAccounts(dbType));
            }
            String backupSetId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
            if (mysqlParamSupport.getSourceDBInstanceID(params) != null) {
                CustInstanceDO instance = mysqlParamSupport.getAndCheckCustInstanceById(params, "sourcedbinstanceid");
                if (!instance.getDbType().equals(dbType)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DB_TYPE);
                }
                if (!instance.getDbVersion().equals(dbVersion)) {
                    return createErrorResponse(ErrorCode.INVALID_ENGINEVERSION);
                }
                custins.setRegionId(mysqlParamSupport.getRegionIdByClusterName(instance.getClusterName()));
            } else {
                custins.setRegionId(mysqlParamSupport.getAndCheckRegionID(params));
            }
            custins.setDbVersion(mysqlParamSupport.getAndCheckDBVersion(params, custins.getDbType(), true));

            // 设置实例公共属性
            mysqlParamSupport.updateCustinsCommonProperties(custins, params);

            Integer bizType = mysqlParamSupport.getAndCheckBizType(params);
            //指定创建实例使用的磁盘类型（SSD 或 SATA或ECS_CLOUD_SSD， 默认使用SSD）
            String hostType = mysqlParamSupport.getAndCheckHostType(params);

            //新增实例规格设置
            if (mysqlParamSupport.hasParameter(params, ParamConstants.DB_INSTANCE_CLASS)) {
                custins = mysqlParamSupport.setInstanceLevel(custins,
                        mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS),
                        bizType, mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE));
            }

            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion, null, null);
            //InnerRDS limit instance level scope
            if(mysqlParamSupport.isInnerRDS(params) && !mysqlParaHelper.isInstanceLevelSupportInnerRDS(insLevel.getExtraInfo())){
                return createErrorResponse(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
            }
            if ("5.7".equals(dbVersion)) {
                if (insLevel == null) {
                    if (insLevel == null) {
                        throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                    }
                }
                hostType = String.valueOf(insLevel.getHostType());
                if (CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD.equals(hostType)) {
                    custins.setKindCode(KIND_CODE_ECS_VM);
                } else {
                    custins.setKindCode(KIND_CODE_NC);
                }
            }

            //原MysqlAdapter代码
            String connType = CustinsSupport.getAndCheckConnType(null, params);
            String connPort = CustinsSupport.getConnPort(null, custins.getDbType());
            Integer netType = CustinsSupport.getNetType(params);
            String targetMinorVersion = mysqlParamSupport.getParameterValue(params, "TargetMinorVersion");
            // MySQL 8.0 不支持 proxy 链路, 专有云中支持创建dns链路mysql8.0实例(兼容minirds创建dns链路mysql80实例的场景)
            if (DB_VERSION_MYSQL_80.equals(dbVersion)
                    && !(custinsService.isInAPCEnvironment() && CustinsSupport.isDns(connType))) {
                connType = CONN_TYPE_LVS;
            }

            Integer specifyPort = null;
            // For dns ins ,support specify port
            if (CustinsSupport.isDns(connType)) {
                String customSpecifyPort = getParameterValue(params, ParamConstants.PORT);
                if(!StringUtils.isEmpty(customSpecifyPort)) {
                    String supportSpecifyPort = resourceService.getResourceRealValueList("MYSQL_DNS_SUPPORT_SPECIFY_PORT").size() > 0
                            ? resourceService.getResourceRealValueList("MYSQL_DNS_SUPPORT_SPECIFY_PORT").get(0) : "false";
                    if (Boolean.parseBoolean(supportSpecifyPort)) {
                        specifyPort = Integer.parseInt(customSpecifyPort);
                        connPort = specifyPort.toString();
                        if (specifyPort < 1000 || specifyPort > 65534) {
                            return createErrorResponse(ErrorCode.INVALID_PORT);
                        }
                    }
                }
            }


            // 非聚石塔实例且非DNS链路实例才可设置专享|共享 & 网络类型 & 实例端口号
            if (!custins.isTop() && !CustinsSupport.isDns(connType)) {
                String type = CustinsSupport.getCustinsType(params);
                if (!INSTANCE_TYPE.contains(type)) {
                    return createErrorResponse(ErrorCode.INVALID_DBINSTANCETYPE);
                }
                custins.setType(type);

                // 共享实例不支持创建VPC网络类型实例
                if (custins.isShare() && CustinsSupport.isVpcNetType(netType)) {
                    return createErrorResponse(ErrorCode.INVALID_DBINSTANCENETTYPE);
                }
                String portStr = CustinsSupport
                        .getConnPort(getParameterValue(params, ParamConstants.PORT), custins.getDbType());

                connPort = CheckUtils.parseInt(portStr, 1000, 65534, ErrorCode.INVALID_PORT)
                        .toString();
            } else if (custins.isTop() && !CustinsSupport.isDns(connType)) {
                if (CustinsSupport.NET_TYPE_PUBLIC.equals(netType)) {
                    return createErrorResponse(ErrorCode.INVALID_DBINSTANCENETTYPE);
                }
            }

            String srcCustinsId = getParameterValue(params, ParamConstants.SOURCE_DBINSTANCE_ID);

            AVZInfo avzInfo = avzSupport.getAVZInfo(params);

            // 获取实例指定的region、cluster
            String region = avzInfo.getRegion();
            String clusterName = getParameterValue(params, ParamConstants.CLUSTER_NAME);

            if (CustinsSupport.isDns(connType)) {
                if (!connAddrService.isAllowCreateDnsLinkForODBS(region)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_CONNTYPE, "ODBS cluster does not support dns connType");
                }
            }

            // 可能需要创建隐藏vip
            List<CustinsConnAddrDO> custinsConnAddrList = new ArrayList<>();
            String connectionString = CheckUtils
                    .checkValidForConnAddrCust(getParameterValue(params, ParamConstants.CONNECTION_STRING));

            String connAddrCust = mysqlParaHelper.getConnAddrCust(connectionString,custins.getRegionId(), custins.getDbType());
            if (CustinsSupport.isVpcNetType(netType)) {
                // 校验VPC网络类型实例的链路类型
                if (connType != null && !connType.equals(CONN_TYPE_PROXY) && !connType.equals(CONN_TYPE_LVS)) {
                    return createErrorResponse(ErrorCode.INVALID_CONN_TYPE);
                }

                // VPC 实例， 必须传入tunnelid， vswitchid， ipaddress
                String tunnelId = CheckUtils.checkValidForTunnelId(getParameterValue(params, ParamConstants.TUNNEL_ID));
                String vpcId = CheckUtils.checkValidForVPCId(getParameterValue(params, ParamConstants.VPC_ID));
                String vswitchId = CheckUtils.checkValidForVswitchId(getParameterValue(params, ParamConstants.VSWITCH_ID));
                String ipaddress = CheckUtils.checkValidForIPAddress(getParameterValue(params, ParamConstants.IP_ADDRESS));

                // 创建实例连接对象
                String vpcInstanceId = getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);
                if (vpcInstanceId == null) {
                    vpcInstanceId = custins.getInsName();
                }

                if (StringUtils.isNotEmpty(srcCustinsId)) {
                    String retainConnAddrString = connAddrService.getRetainSourceConnAddressIfNeeded(params);
                    if (StringUtils.isNotEmpty(retainConnAddrString)) {
                        connAddrCust = retainConnAddrString;
                    }
                }
                CustinsConnAddrDO custinsConnAddr = ConnAddrSupport
                        .createCustinsConnAddr(connAddrCust, connPort,
                                netType, CustinsValidator.getRealNumber(tunnelId, -1),
                                vpcId,
                                vswitchId,
                                ipaddress,
                                vpcInstanceId);
                custinsConnAddrList.add(custinsConnAddr);
            } else {
                // 创建实例连接对象
                CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(connAddrCust, connPort,
                        netType, -1, null, null, null, null);
                custinsConnAddrList.add(custinsConnAddr);
            }

            // 设置SQLWall配置
            if (custins.isTop()) {
                if (getParameterValue(params, ParamConstants.ACCOUNT_NAME) == null) {// TOP实例account为必选参数
                    return createErrorResponse(ErrorCode.ACCOUNT_NOT_FOUND);
                }
                custins.setSqlwallSwitch("true".equalsIgnoreCase(getParameterValue(params, "SqlwallSwitch", "true")) ? 1 : 0);
                custins.setSqlwallTimeoutEvent(Integer.valueOf(getParameterValue(params, "SqlwallTimeoutEvent", "2")));
                custins.setSqlwallInjectEvent(Integer.valueOf(getParameterValue(params, "SqlwallInjectEvent", "2")));
            } else {
                custins.setSqlwallSwitch("true".equalsIgnoreCase(getParameterValue(params, "SqlwallSwitch", "true")) ? 1 : 0);
                custins.setSqlwallTimeoutEvent(Integer.valueOf(getParameterValue(params, "SqlwallTimeoutEvent", "0")));
                custins.setSqlwallInjectEvent(Integer.valueOf(getParameterValue(params, "SqlwallInjectEvent", "0")));
            }

            Integer nodeCount = CustinsSupport.getAndCheckDBInstanceNodeCount(params, custins);
            //企业版实例一期强制node数量为3
            if (mysqlDBCustinsService.isMysqlEnterprise(custins)) {
                InstanceLevelDO enterlevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
                logger.warn("enterprise_nodecount=" + enterlevel.getInsCount());
                nodeCount = enterlevel.getInsCount() == null ? 3 : enterlevel.getInsCount();
            }

            if (nodeCount == null) {
                nodeCount = 2;
            }

            if (mysqlDBCustinsService.isMysqlEnterprise(custins) && CustinsSupport.isDns(connType)) {
                for (int i = 0; i < nodeCount; ++i) {
                    String connAddrNode = mysqlParaHelper.getConnAddrCust(connectionString + "-" + (i + 1),
                            (custins.getRegionId()),
                            custins.getDbType());
                    CustinsConnAddrDO custinsConnAddr = ConnAddrSupport
                            .createCustinsConnAddr(connAddrNode, connPort,
                                    netType, -1, null, null, null, null);
                    custinsConnAddr.setInsId(-1);
                    custinsConnAddrList.add(custinsConnAddr);
                }
            }

            //检查是否有指定版本创建
            String minorVersion = null;
            if (StringUtils.isNotBlank(targetMinorVersion)) {
                minorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
                    dbType,
                    dbVersion,
                    classCode,
                    MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL,
                    targetMinorVersion);
            } else {
                // 尝试查找最新的内核版本，需要包含灰度策略
                minorVersion = minorVersionServiceHelper.tryGetLatestMinorVersion(
                        dbType,
                        dbVersion,
                        insLevel.getCategory(),
                        KindCodeParser.KIND_CODE_NC,
                        MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_RPM.toLowerCase());
                if (StringUtils.isNotBlank(minorVersion)) {
                    logger.info("find the latest minorVersion from metadb [{}]", minorVersion);
                } else {
                    logger.warn("latest minorVersion from metadb not found");
                }
            }

            boolean createFromBackup = false;
            String bakId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
            Long bakupsetId = 0L;
            CustInstanceDO srcCustins = null;
            BaksetMetaInfo baksetMetaInfo = null;
            String restoreType = null;
            // 获取备份策略参数，下发实例创建时，这些参数将被写入parameter字段中
            Map<String, Object> taskQueueParam;
            if (KIND_CODE_NC.equals(custins.getKindCode())) {
                taskQueueParam = CustinsSupport.getTaskQueueParamForMysqlLocalSSD(params);
            } else {
                taskQueueParam = CustinsSupport.getTaskQueueParam(params);
            }

            // 记录参数模板ID
            taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
            // 记录参数模板详情
            taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, SysParamGroupHelper.describeSysParamGroupId(paramGroupId));
            // 记录指定参数
            taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS, customMysqlParams);

            //获得是否为容灾恢复场景标识
            Long realBacksetSize = null;
            Boolean isDisasterRestore = BakSupport.getAndCheckIsDisasterRestore(params);
            if (isDisasterRestore) {
                // 设置容灾恢复的标志位
                taskQueueParam.put("is_disaster_restore", 1);
                createFromBackup = true;
                // 容灾恢复因为跨region, 所以源实例不存在, BLS恢复可能会同region, 在对应分支中进行设置
                srcCustinsId = null;
                restoreType = mysqlParamSupport.getAndCheckRestoreType(params);
                CheckCreateDdrDBInstanceImpl checkCreateDdrDBInstance = SpringContextUtil.getBeanByClass(CheckCreateDdrDBInstanceImpl.class);
                baksetMetaInfo = checkCreateDdrDBInstance.doCheckDdrRestore(custins, params, restoreType);
                bakupsetId = baksetMetaInfo.getId();
                realBacksetSize = baksetMetaInfo.getBaksetSize();

                String restoreByDbsService = getParameterValue(params, MySQLParamConstants.DISASTER_RESTORE_BY_DBS_SERVICE, "false");
                if (Boolean.parseBoolean(restoreByDbsService)) {
                    //实例切换高级备份策略后，需使用备份dbsGateWay API，会下发新的任务流，依赖如下参数
                    taskQueueParam.put(MySQLParamConstants.DISASTER_RESTORE_BY_DBS_SERVICE, 1);
                    taskQueueParam.put(ParamConstants.BACKUP_SET_ID, getParameterValue(params, ParamConstants.BACKUP_SET_ID));
                    taskQueueParam.put(PodDefaultConstants.PARAM_BACKUP_SET_REGION, getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION));
                    taskQueueParam.put(ParamConstants.SOURCE_DB_INSTANCE_NAME, getParameterValue(params, ParamConstants.SOURCE_DB_INSTANCE_NAME));
                    taskQueueParam.put(PodDefaultConstants.PARAM_SOURCE_REGION, getParameterValue(params, PodDefaultConstants.PARAM_SOURCE_REGION));
                    taskQueueParam.put(ParamConstants.RESTORE_TIME, getParameterValue(params, ParamConstants.RESTORE_TIME));
                }
            }
            if (srcCustinsId != null) {
                srcCustins = custinsService.getAndCheckCustInstanceById(
                        srcCustinsId,
                        getParameterValue(params, ParamConstants.USER_ID),
                        getParameterValue(params, ParamConstants.UID),
                        getParameterValue(params, ParamConstants.INNER_USER_ID));
                if (srcCustins.getIsDeleted() == 0) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
                custins.setParentId(srcCustins.getId());
                custins.setAccountMode(srcCustins.getAccountMode());
                if (mySQLService.isRebuildBackupSet(bakId)) {
                    bakupsetId = rebuildDeletedInsConfig(params, bakId, srcCustins, taskQueueParam, dbType, dbVersion, classCode);
                } else if (bakId != null) {
                    bakupsetId = CheckUtils
                            .parseLong(bakId, null, null, ErrorCode.BACKUPSET_NOT_FOUND);
                } else {
                    Map<String, Object> condition = ImmutableMap.of(
                            "ignoredRetention", true,
                            "custinsIds", ImmutableList.of(srcCustins.getId()),
                            "location", BAKUPSET_LOCATION_OSS,
                            "status", "OK");
                    List<Map<String, Object>> bakHistoryMapByCondition = bakService.getBakHistoryMapByCondition(
                            condition);
                    if (bakHistoryMapByCondition.isEmpty()) {
                        return createErrorResponse(ErrorCode.BACKUPSET_NOT_FOUND);
                    }
                    bakupsetId = (Long) bakHistoryMapByCondition.get(0).get("BackupSetID");
                }
                restoreType = RESTORE_TYPE_BAKID;
                BakhistoryDO bakhistoryDO = bakService.getBakhistory(srcCustins, bakupsetId);
                realBacksetSize = bakhistoryDO.getBaksetSize();
                taskQueueParam.put("bakhis_id", bakupsetId);
                taskQueueParam.put("recover_custins_id", srcCustins.getId());
                createFromBackup = true;
            }

            //指定版本创建（基于备份集重建不能指定内核版本）
            if (StringUtils.isNotBlank(minorVersion) && !createFromBackup) {
                taskQueueParam.put("minor_version", minorVersion);
            }

            Integer taskId = null;
            if (custins.isExcluse()) {
                // 无源实例则按照一定规则设置accountMode
                if (srcCustinsId == null) {
                    mySQLService.setCustinAccountMode(custins, getParameterValue(params, ParamConstants.ACCOUNT_NAME),
                            region, mysqlParamSupport.getParameterValue(params, ParamConstants.ACCOUNT_TYPE));
                }

                AccountsDO account = getAccount(custins, params);

                DbsDO db = dbsService.getAndCheckDb(custins,
                        getParameterValue(params, ParamConstants.DB_NAME),
                        getParameterValue(params, ParamConstants.DB_DESCRIPTION),
                        getParameterValue(params, ParamConstants.DB_CHARACTER_SET_NAME));
                CustinsIpWhiteListDO custinsIpWhiteList = ipWhiteListService.getAndCheckCustinsIpWhiteList(custins,
                        getParameterValue(params, ParamConstants.SECURITY_IP_LIST),
                        getParameterValue(params, ParamConstants.WHITELIST_NET_TYPE, CustinsIpWhiteListDO.DEFAULT_NET_TYPE),
                        getParameterValue(params, ParamConstants.SECURITY_IP_TYPE, CustinsIpWhiteListDO.DEFAULT_IP_TYPE));
                int userId = mysqlParamSupport.getAndCreateUserId(params);
                CustinsIpWhiteListDO[] templateList;
                int[] templateIdList;
                templateList= mysqlParamSupport.getAndCheckWhitelistTemplateList(params, userId);
                templateIdList=mysqlParamSupport.getAndCheckTemplateIdList(params,templateList);
                List<CustinsParamDO> custinsParams = custinsParamService.createCustinsParamsForNewCustins(custins,
                        getParameterValue(params, "SystemDBCharset",
                                CustinsParamSupport.CUSTINS_PARAM_VALUE_SYS_DBS_CHARSET_CHINESE_PRC_CI_AS),
                        getParameterValue(params, ParamConstants.DB_INSTANCE_SYNC_MODE),
                        getParameterValue(params, ParamConstants.ECS_SECURITY_GROUP_ID),
                        getParameterValue(params, ParamConstants.RESOURCE_GROUP_ID, "").trim(),
                        paramGroupId, customMysqlParams);
                String compressionMode = getParameterValue(params, "compressionMode");
                if (StorageCompressionHelper.COMPRESSION_MODE_ON.equals(compressionMode)) {
                    updateCompressionParams(custins, params, custinsParams);
                    updateCompressionInsDiskSize(custins, params);
                }
                custins.setCustinsParams(custinsParams);

                boolean hasInstanceId = getParameterValue(params, ParamConstants.DB_INSTANCE_ID) != null;
                if (hasInstanceId) {
                    custins = custinsService.updateCustInstance(custins);
                } else {
                    if(custinsService.isInAPCEnvironment()){
                        if (mysqlParaHelper.checkDiskFullStatusForDnsConn()){
                            custins.setIsCheck(mysqlParamSupport.getDiskCheck(params, "DiskCheck"));
                        }
                    }
                    custins = custinsService.createCustInstance(custins);

                    InstanceLevelDO custinsLevel = instanceService
                            .getInstanceLevelByLevelId(custins.getLevelId());
                    String proxyGroupId = getParameterValue(params, ParamConstants.PROXY_GROUP_ID);
                    Integer perferedProxyGroupId = proxyGroupId == null ? null : Integer.valueOf(proxyGroupId);
                    List<Integer> instanceIds;
                    // --------------- 裁撤迁移场景，假定用户已经有数据 -------------------
                    List<String> transfer_to_binstance_support_uid = resourceService.getResourceRealValueList("TRANSFER_TO_DBINSTANCE_SUPPORT_UID");
                    if (Collections.isEmptyCollection(transfer_to_binstance_support_uid)) {
                        transfer_to_binstance_support_uid = com.google.common.collect.ImmutableList.of("1647796581073291");
                    } else {
                        transfer_to_binstance_support_uid = transfer_to_binstance_support_uid.stream().map(
                                s -> Arrays.stream(s.split(",")).map(String::trim).collect(Collectors.toList())).reduce((sl1, sl2) -> {
                            sl1.addAll(sl2);
                            return sl1;
                        }).orElse(transfer_to_binstance_support_uid);
                    }
                    String aliUid = mysqlParamSupport.getUID(params);
                    boolean isForTransfer = transfer_to_binstance_support_uid.contains(aliUid) && custins.getDiskSize() > 1024L * 50;

                    // --------------- 裁撤迁移场景，假定用户已经有数据 -------------------

                    Long usedDiskSize = null;
                    if ((createFromBackup && realBacksetSize != null)) { //回收站，容灾恢复场景要把备份集大小*10倍压缩比 传递给资源调度;
                        usedDiskSize = Math.min(custins.getDiskSize(), (realBacksetSize * 10) / 1024);
                    }
                    if (isForTransfer) { // 诺曼底账号认为是要迁移数据上来，预留空间
                        usedDiskSize = custins.getDiskSize();
                    }
                    try {
                        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
                        String accessId = CustinsParamSupport.getParameterValue(params, ParamConstants.ACCESSID);
                        String orderId = CustinsParamSupport.getParameterValue(params, ParamConstants.ORDERID);
                        instanceIds = createMysql(
                                getAction(params), requestId, custins, custinsLevel, avzInfo, clusterName, null,
                                null,
                                connType, perferedProxyGroupId, custinsConnAddrList,
                                nodeCount, bizType, Integer.valueOf(hostType),
                                usedDiskSize,
                                getAndCheckHostIdSet(params),
                                resourceStrategy, accessId, orderId, specifyPort, false);
                    } catch (RdsException e) {
                        return createErrorResponse(e.getErrorCode());
                    }

                    avzSupport.updateAVZInfoByInstanceIds(avzInfo, instanceIds);
                    custinsParamService.updateAVZInfo(custins.getId(), avzInfo);
                    if (CustinsSupport.ISOLATE_HOST_FIXED.equals(custinsLevel.getIsolateHost())) {
                        custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(),
                                MySQLParamConstants.V6_CPU_MATCH,
                                MySQLParamConstants.V6_CPU_MATCH_VALUE));
                    }

                    if (createFromBackup) {
                        /**
                         * trans_list init
                         */
                        TransListDO trans;
                        Map<String, Object> translistParamMap = new HashMap<>(8);
                        if (isDisasterRestore) {
                            // 容灾恢复场景无源实例信息, 将源实例塞成目标实例
                            srcCustins = custins;
                            // 将容灾恢复场景标识及备份集信息塞进param中
                            translistParamMap.put("bakhis_info", JSONObject.toJSONString(baksetMetaInfo));
                            String binlogName = getParameterValue(params, "BinlogName");
                            if (Validator.isNotNull(binlogName)) {
                                translistParamMap.put("binlog_name", binlogName);
                                translistParamMap.put("binlog_position", getParameterValue(params, "BinlogPosition", "0"));
                                if (hasParameterValue(params, "BinlogHostinsId")) {
                                    translistParamMap.put("binlog_hostins_id", getParameterValue(params, "BinlogHostinsId"));
                                } else {
                                    translistParamMap.put("binlog_role", getParameterValue(params, "BinlogRole", "master"));
                                }
                            }
                        }
                        trans = new TransListDO(srcCustins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
                        if (RESTORE_TYPE_TIME.equals(restoreType)) {
                            trans.setBakhisId(bakupsetId);
                            taskQueueParam.put("bakhis_id", bakupsetId);
                            trans.setIsBaseTime(1);
                            //写入dbaas库，使用dbaas库时间
                            DateTime restoreTimeUTC = dtzSupport.getUTCDateByDateStr(getParameterValue(params, ParamConstants.RESTORE_TIME));
                            Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_DBAAS);
                            trans.setRecoverTime(restoreTime);
                        }
                        List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(srcCustins.getId());
                        trans.setsHinsid1(insIds.get(0));
                        if (insIds.size() > 1) {
                            trans.setsHinsid2(insIds.get(1));
                        }
                        trans.setsCinsReserved(1);
                        translistParamMap.put("restoreType", restoreType);
                        if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                            taskQueueParam.put("bakhis_id", bakupsetId);
                            trans.setBakhisId(bakupsetId);
                        }
                        trans.setdCinsid(custins.getId());
                        trans.setdLevelid(custins.getLevelId());
                        trans.setdDisksize(custins.getDiskSize());
                        trans.setdHinsid1(instanceIds.get(0));
                        if (instanceIds.size() > 1) {
                            trans.setdHinsid2(instanceIds.get(1));
                        }
                        trans.setParameter(JSON.toJSONString(translistParamMap));
                        instanceService.createTransList(trans);
                        taskQueueParam.put("trans_list_id", trans.getId());
                    }
                }

                if (StringUtils.isNotEmpty(paramGroupId) && !paramGroupId.startsWith(ParamTransHelper.SYS_PARAM_GROUP_ID_PREFIX)){
                    Map tmpParamGroup = parameterGroupTemplateGenerator.paramGroupMatchValidate(dbType, dbVersion, null, paramGroupId, true);
                    Long pId = Long.valueOf(tmpParamGroup.get("ParameterGroupId").toString());
                    Map<String, Object> parameterMapTmp = ParamChecker.getParameterGroupDetail(pId, custinsParamGroupsService);
                    if (!parameterMapTmp.isEmpty() && parameterMapTmp.containsKey("sync_binlog")) {
                        logger.info("checkAddUserSyncBinlogCustinsParam custins_id {} ",custins.getId());
                        mysqlParaHelper.checkAddUserSyncBinlogCustinsParam(parameterMapTmp, custins.getId());
                    }

                }
                //写入白名单模版数据
                whitelistTemplateService.createWhitelistTemplateRecord(templateList,templateIdList,userId,custins);

                taskId = mySQLService.createCustInstanceTask(getAction(params),
                        custins, db, account, taskQueueParam, custinsIpWhiteList,
                        getOperatorId(params), null);
                taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(params));

                Boolean autoCreateProxy = Boolean.valueOf(getParameterValue(params, "AutoCreateProxy", "false"));
                if (autoCreateProxy) {
                    // 下发创建实例的后置任务
                    workFlowService.dispatchTask("custins", custins.getInsName(), PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_MODIFY_INS_AFTER_CREATE, "", WorkFlowService.TASK_PRIORITY_COMMON);
                }

            } else { // 共享实例
                String dbname = CheckUtils.checkValidForDbName(getParameterValue(params, ParamConstants.DB_NAME));
                String accountName = CheckUtils.checkValidForAccountName(
                        CheckUtils.checkNullForAccountName(getParameterValue(params, ParamConstants.ACCOUNT_NAME)),
                        custins.getDbType(), custins.isTop(),
                        AccountPriviledgeType.PRIVILEDGE_NORMAL, custins.getDbVersion());
                DbsDO dbs = new DbsDO(custins, dbname,
                        dbsService.checkCharacterSetName(custins.getDbType(),
                                custins.getDbVersion(),
                                getParameterValue(params, ParamConstants.DB_CHARACTER_SET_NAME)));
                if (CustinsParamSupport.hasParameterValue(params, ParamConstants.DB_DESCRIPTION)) {
                    String desc = URLDecoder
                            .decode(getParameterValue(params, ParamConstants.DB_DESCRIPTION, ""), "UTF-8");
                    dbs.setComment(
                                    CheckUtils.checkLength(desc, 1, 256, ErrorCode.INVALID_DBDESCRIPTION));
                }
                AccountsDO account = new AccountsDO(custins, accountName,
                        accountService.getAndCheckDecryptedAccountPassword(
                                getParameterValue(params, ParamConstants.ACCOUNT_PASSWORD),
                                getParameterValue(params, ParamConstants.ENCRYPT_ACCOUNT_PASSWORD)));
                // 共享实例最大支持创建1个DB，2个账户
                custins.setMaxDbs(1);
                custins.setMaxAccounts(2);
                // 共享实例custinsConnAddrList 只有一个元素
                CustinsConnAddrDO custinsConnAddr = custinsConnAddrList.get(0);
                Response<AllocateVipRespModel> response = resApi
                        .allocateDns(custins.getId(), custinsConnAddr.getNetType(), custinsConnAddr.getConnAddrCust(),
                                0);
                if (!response.getCode().equals(200)) {
                    return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                            ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                }
                taskId = custinsService
                        .createShareCustInstance(getAction(params), custins, dbs, account,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           custinsResourceService
                                        .getShareResource(getAction(params), getRequestUUID(params), custins, region, clusterName,
                                                accountName,
                                                dbname, custinsConnAddr.getNetType()),
                                custinsConnAddr, getOperatorId(params));
                taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(params));

            }
            custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, upgradeMinorVersionOption));
            // createCustInstanceTask 刷入参数后根据 param_group_id 重置 sync_binlog_mode 与 sync_mode
            if (custins.isMysql() && !StringUtils.isBlank(paramGroupId)) {
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_BINLOG_MODE,
                        SysParamGroupHelper.getSyncBinlogMode(paramGroupId).toString());
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE,
                        SysParamGroupHelper.getSyncMode(paramGroupId).toString());

                if (StringUtils.isNotEmpty(paramGroupId) && !paramGroupId.startsWith(ParamTransHelper.SYS_PARAM_GROUP_ID_PREFIX)){
                    Map tmpParamGroup = parameterGroupTemplateGenerator.paramGroupMatchValidate(dbType, dbVersion, null, paramGroupId, true);
                    Long pId = Long.valueOf(tmpParamGroup.get("ParameterGroupId").toString());
                    Map<String, Object> parameterMapTmp = ParamChecker.getParameterGroupDetail(pId, custinsParamGroupsService);
                    if (!parameterMapTmp.isEmpty() && parameterMapTmp.containsKey("sync_binlog")) {
                        logger.info("checkAddUserSyncBinlogCustinsParam custins_id {} ",custins.getId());
                        mysqlParaHelper.checkAddUserSyncBinlogCustinsParam(parameterMapTmp, custins.getId());
                    }

                }
            }
            // set is_inner_rds custins param to identify inner rds
            if (mysqlParamSupport.isInnerRDS(params)){
                custinsParamService.setCustinsParam(custins.getId(),CustinsParamSupport.CUSTINS_PARAM_IS_INNER_RDS,"1");
            }


            Map<String, Object> data = new HashMap<>();
            data.put("Region", region);
            data.put("ConnectionString", connAddrCust);
            data.put("Port", connPort);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("DBInstanceType", custins.getTypeChar());
            data.put("DBMaxQty", custins.getMaxDbs());
            data.put("AccountMaxQty", custins.getMaxAccounts());
            data.put("TaskId", taskId);
            data.put("DBInstanceNetType", netType);
            data.put("DBInstanceConnType", custins.getConnType());
            data.put(ParamConstants.ACCOUNT_TYPE, custins.isSuperAccountMode() ? 1 : 0);
            data.put(ParamConstants.SUPPORT_UPGRADE_ACCOUNT_TYPE,
                    custins.isAllowUpgradeSuperAccountMode() ? 1 : 0);
            if (custins.isMongoDB()) {
                data.put(ParamConstants.REPLSET_NAME, "mgset-" + custins.getId());
            }

            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

    public void updateCompressionInsDiskSize(CustInstanceDO custins, Map<String, String> params) {
        String compressionRatio = getParameterValue(params, "compressionRatio");
        if (StringUtils.isEmpty(compressionRatio)) {
            return;
        }
        Long diskSizeInOrder = custins.getDiskSize();
        Long diskSizeInMaotai = Long.valueOf(StorageCompressionHelper.calculateLogicalSize(diskSizeInOrder.intValue(), Double.valueOf(compressionRatio)));
        logger.info("ins_name:{} compressionMode on diskSizeInOrder:{}, diskSizeInMaotai:{} ", custins.getInsName(), diskSizeInOrder, diskSizeInMaotai);
        custins.setDiskSize(diskSizeInMaotai);
    }

    public void updateCompressionParams(CustInstanceDO custins, Map<String, String> params, List<CustinsParamDO> custinsParams) {
        // 创建实例记录
        String compressionMode = getParameterValue(params, "compressionMode");
        String compressionRatio = getParameterValue(params, "compressionRatio");
        if (StringUtils.isEmpty(compressionRatio)) {
            return;
        }
        CustinsParamDO custinsParamDOCompressionMode = new CustinsParamDO();
        custinsParamDOCompressionMode.setName(StorageCompressionHelper.COMPRESSION_MODE_KEY);
        custinsParamDOCompressionMode.setValue(compressionMode);
        custinsParams.add(custinsParamDOCompressionMode);

        CustinsParamDO custinsParamDOCompressionRatio = new CustinsParamDO();
        custinsParamDOCompressionRatio.setName(StorageCompressionHelper.COMPRESSION_RATIO_KEY);
        custinsParamDOCompressionRatio.setValue(compressionRatio);
        custinsParams.add(custinsParamDOCompressionRatio);

        CustinsParamDO custinsParamDOCompressionBeforeSize = new CustinsParamDO();
        custinsParamDOCompressionBeforeSize.setName(StorageCompressionHelper.COMPRESSION_BEFORE_SIZE);
        custinsParamDOCompressionBeforeSize.setValue(custins.getDiskSize().toString());
        custinsParams.add(custinsParamDOCompressionBeforeSize);
    }

    public AccountsDO getAccount(CustInstanceDO custins, Map<String, String> params) throws Exception {
        AccountsDO account = accountService.genAccountByCustins(custins,
                getParameterValue(params, ParamConstants.ACCOUNT_NAME),
                getParameterValue(params, ParamConstants.ACCOUNT_PASSWORD),
                getParameterValue(params, ParamConstants.ENCRYPT_ACCOUNT_PASSWORD));
        //feature:https://aone.alibaba-inc.com/v2/project/2004973/req/********
        //如果SuperAccountName有值，则覆盖account
        if (mysqlParaHelper.hasParameter(ParamConstants.SUPER_ACCOUNT_NAME) && mysqlParaHelper.hasParameter(ParamConstants.ENCRYPT_SUPER_ACCOUNT_PASSWORD)
                &&  !mysqlParaHelper.checkAccountIsReserved(custins.getDbVersion(), getParameterValue(params, ParamConstants.SUPER_ACCOUNT_NAME))) {
            String accountName = CheckUtils.checkValidForAccountName(
                    getParameterValue(params, ParamConstants.SUPER_ACCOUNT_NAME), custins.getDbType(), custins.isTop(), AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN, custins.getDbVersion()
            );
            account = new AccountsDO(custins, accountName, mysqlParaHelper.getAndCheckSuperAccountPassword());
            account.setPriviledgeType(AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue());
        }
        return account;
    }
    private List<Integer> createMysql(String action, String requestId,
                                      CustInstanceDO custins, InstanceLevelDO insLevel, AVZInfo avzInfo, String specifyClusterName,
                                      String preferClusterName, Integer excludeCustinsId, String connType, Integer proxyGroupId,
                                      List<CustinsConnAddrDO> custinsConnAddrList, Integer nodeCount, Integer bizType,
                                      Integer hostType, Long diskSizeUsed, Set<Integer> hostIdSet, String resourceStrategy, String accessId, String orderId,
                                      Integer specifyPort,
                                      boolean isXdb) throws RdsException {

        // 创建container
        ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, custins.getDbType());
        resourceContainer.setRequestId(requestId);
        resourceContainer.setUserId(custins.getUserId());
        resourceContainer.setClusterName(specifyClusterName);
        resourceContainer.setPreferClusterName(preferClusterName);
        resourceContainer.setBizType(bizType);
        resourceContainer.setAccessId(accessId);
        resourceContainer.setOrderId(orderId);
        /**
         * allocate resource
         */
        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        custinsResModel.setConnType(connType);
        custinsResModel.setSpecifyProxyGroupId(proxyGroupId);
        if (excludeCustinsId != null) {
            custinsResModel.addExcludeCustinsId(excludeCustinsId);
        }

        // host resource
        HostinsResModel hostinsResModel = new HostinsResModel(custins.getLevelId());

        //isSame:true/false
        // extra_ins_res_info:{2:instance_level_id,1:logger_instance_level_id}

        hostinsResModel.setInsCount(nodeCount);
        hostinsResModel.setHostType(hostType);
        hostinsResModel.setDiskSizeUsed(diskSizeUsed);
        hostinsResModel.setDiskSizeSold(custins.getDiskSize());
        if (resourceStrategy != null) {
            hostinsResModel.setStrategy(resourceStrategy);
        }
        // init distribute rule
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
        distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
        distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
        InsLevelExtraInfo.updateDistributeRule(distributeRule, insLevel.getExtraInfo());
        // 指定的host ids
        distributeRule.setSpecifyHostIdSet(hostIdSet);

        hostinsResModel.setDistributeRule(distributeRule);
        if (specifyPort != null) {
            PortDistributeRule portDistributeRule = new PortDistributeRule();
            Set<Integer> specifyPortSet = new HashSet<>();
            specifyPortSet.add(specifyPort);
            portDistributeRule.setSpecifyPortSet(specifyPortSet);
            hostinsResModel.setPortDistributeRule(portDistributeRule);
        }

        custinsResModel.setHostinsResModel(hostinsResModel);

        // vip resource
        for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
            VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
            vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
            vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
            vipResModel.setVip(custinsConnAddr.getVip());
            vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
            vipResModel.setVpcId(custinsConnAddr.getVpcId());
            vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
            vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
            vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
            vipResModel.setInstanceId(custinsConnAddr.getInsId());
            custinsResModel.addVipResModel(vipResModel);
        }
        resourceContainer.addCustinsResModel(custinsResModel);
        resourceContainer.setV6CpuMatch(true);
        CustinsDistributeRule custinsDistributeByUserIdRule = new CustinsDistributeRule();
        custinsDistributeByUserIdRule.setScatterType("SCATTER_TYPE_USER_ID");
        custinsDistributeByUserIdRule.setMaxInsPerHost(1);
        resourceContainer.getCustinsDistributeRuleList().add(custinsDistributeByUserIdRule);

        if (isXdb) {
            // XDB v7 机型CPU对齐
            resourceContainer.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
        }

        //调用资源API
        Response<AllocateResRespModel> response = resManagerService.allocateRes(resourceContainer);
        if (!response.getCode().equals(200)) {
            custinsService.deleteCustInstance(custins);
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }
        AllocateResRespModel.CustinsResRespModel allocateResRespModel = response.getData().getCustinsResRespModelList()
                .get(0);
        custins.setClusterName(allocateResRespModel.getClusterName());
        custins.setConnType(allocateResRespModel.getConnType());
        return allocateResRespModel.getInstanceIdList();
    }
    private Long rebuildDeletedInsConfig(Map<String, String> params, String bakId,
                                      CustInstanceDO srcCustins, Map<String, Object> taskQueueParam,
                                                  String dbType, String dbVersion, String classCode
    ) throws Exception {
        DescribeRestoreBackupSetParam caller = dbsGateWayService.describeRestoreBackupSetBuilder(params,mysqlParamSupport.getBID(params));
        DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(caller);
        if (restoreBackupResponse.getBackupSetInfo() == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        bakId = restoreBackupResponse.getBackupSetInfo().getBackupId();
        mySQLService.compareBakSizeAndDiskSize(restoreBackupResponse,mysqlParamSupport.getAndCheckStorage(params));
        mySQLService.checkCustinsAndUser(restoreBackupResponse.getBackupSetInfo().getCustinsId(),mysqlParamSupport.getBID(params),mysqlParamSupport.getUID(params));
        Long bakupsetId;
        if (StringUtils.isNotBlank(bakId)) {
            bakupsetId = CheckUtils.parseLong(bakId, null, null, ErrorCode.BACKUPSET_NOT_FOUND);
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        String backupMinorVersion =  restoreBackupResponse.getBackupSetInfo().getExtraInfo().getMINOR_VERSION();
        try {
            String finalMinorVersion = minorVersionServiceHelper.checkAndGetAllMinorVersion(dbType, dbVersion, classCode,KIND_CODE_NC,
                    MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL, backupMinorVersion);
            mysqlParaHelper.setParameter("TargetMinorVersion",finalMinorVersion);
        } catch (RdsException re) {
            logger.error("rebuild deleted ins, minor version is not find: {}", backupMinorVersion);
        }
        return bakupsetId;
    }

    //创建xdb实例
    private Map<String, Object> doCreateXDBInstance(CustInstanceDO custins, Map<String, String> params)
            throws RdsException, UnsupportedEncodingException {

        boolean isPolarxHatp = false;

        // 是否为使用dbs服务化的polarx存储节点
        boolean isPolarxUsingDBSService = Boolean.parseBoolean(
                getParameterValue(params, MinorVersionServiceHelper.POLARX_USING_DBS_SERVICE_EXT));

        String dbType = mysqlParamSupport.getAndCheckDBType(params, null);
        String dbVersion = mysqlParamSupport.getAndCheckDBVersion(params, dbType, true);
        String classCode = getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
        String resourceStrategy = mysqlParamSupport.getResourceStrategy(params);
        String paramGroupId = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_PARAM_GROUP_ID);
        Map<String, String> customMysqlParams = mysqlParamSupport.getAndCheckMysqlCustomParams(params);
        SysParamGroupHelper.sysParamGroupIdValidation(paramGroupId, dbType, dbVersion, "", "");
        custins = new CustInstanceDO(mysqlParamSupport.getAndCreateUserId(params),
                mysqlParamSupport.getAndCheckServiceType(params),
                dbType,
                mysqlParamSupport.getCustinsMaxDbs(dbType),
                mysqlParamSupport.getCustinsMaxAccounts(dbType));

        custins.setDbVersion(mysqlParamSupport.getAndCheckDBVersion(params, custins.getDbType(), true));
        // 设置实例公共属性
        mysqlParamSupport.updateCustinsCommonProperties(custins, params);

        Integer bizType = mysqlParamSupport.getAndCheckBizType(params);
        String hostType = mysqlParamSupport.getAndCheckHostType(params);

        //设置规格信息
        if (mysqlParamSupport.hasParameter(params, ParamConstants.DB_INSTANCE_CLASS)) {
            custins = mysqlParamSupport.setInstanceLevel(custins,
                    mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS),
                    bizType, mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE));
        }

        //InstanceLevel一定存在，判断xdb57时一定会查询规格
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion, null, null);
        hostType = String.valueOf(insLevel.getHostType());
        //目前只有物理机
        if (CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD.equals(hostType)) {
            custins.setKindCode(KIND_CODE_ECS_VM);
        } else {
            custins.setKindCode(KIND_CODE_NC);
        }

        // XDB 只能有lvs和dns链路
        String connType = CustinsSupport.getAndCheckConnType(null, params);
        if (connType == null || connType.equals(CONN_TYPE_PROXY)) {
            connType = CONN_TYPE_LVS;
        }
        String connPort = CustinsSupport.getConnPort(null, custins.getDbType());
        Integer netType = CustinsSupport.getNetType(params);

        // 非聚石塔实例才可设置专享|共享 & 网络类型 & 实例端口号
        Integer specifyPort = null;
        if (!custins.isTop()) {
            String type = CustinsSupport.getCustinsType(params);
            if (!INSTANCE_TYPE.contains(type)) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCETYPE);
            }
            custins.setType(type);
            // 共享实例不支持创建VPC网络类型实例
            if (custins.isShare() && CustinsSupport.isVpcNetType(netType)) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCENETTYPE);
            }
            String portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), custins.getDbType());
            connPort = CheckUtils.parseInt(portStr, 1000, 5999, ErrorCode.INVALID_PORT).toString();

            // DNS链路指定了端口，让资源管理器去分配指定端口
            if (CustinsSupport.isDns(connType)) {
                String supportSpecifyPort = resourceService.getResourceRealValueList("XDB_DNS_SUPPORT_SPECIFY_PORT").size() > 0
                        ? resourceService.getResourceRealValueList("XDB_DNS_SUPPORT_SPECIFY_PORT").get(0) : "false";
                if (Boolean.valueOf(supportSpecifyPort)) {
                    specifyPort = Integer.parseInt(connPort);
                }
            }
        } else if (custins.isTop() && !CustinsSupport.isDns(connType)) {
            if (CustinsSupport.NET_TYPE_PUBLIC.equals(netType)) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCENETTYPE);
            }
        }

        AVZInfo avzInfo = avzSupport.getAVZInfo(params);
        // 获取实例指定的region、cluster
        String region = avzInfo.getRegion();
        String clusterName = getParameterValue(params, ParamConstants.CLUSTER_NAME);

        // 可能需要创建隐藏vip
        List<CustinsConnAddrDO> custinsConnAddrList = new ArrayList<>();
        String connectionString = CheckUtils.checkValidForConnAddrCust(getParameterValue(params, ParamConstants.CONNECTION_STRING));
        String connAddrCust = mysqlParamSupport.getConnAddrCust(connectionString,(custins.getRegionId()), custins.getDbType());

        if (CustinsSupport.isVpcNetType(netType)) {
            // 校验VPC网络类型实例的链路类型
            if (connType != null && !connType.equals(CONN_TYPE_PROXY) && !connType.equals(CONN_TYPE_LVS)) {
                return createErrorResponse(ErrorCode.INVALID_CONN_TYPE);
            }

            // VPC 实例， 必须传入tunnelid， vswitchid， ipaddress
            String tunnelId = CheckUtils.checkValidForTunnelId(getParameterValue(params, ParamConstants.TUNNEL_ID));
            String vpcId = CheckUtils.checkValidForVPCId(getParameterValue(params, ParamConstants.VPC_ID));
            String vswitchId = CheckUtils.checkValidForVswitchId(getParameterValue(params, ParamConstants.VSWITCH_ID));
            String ipaddress = CheckUtils.checkValidForIPAddress(getParameterValue(params, ParamConstants.IP_ADDRESS));

            // 创建实例连接对象
            String vpcInstanceId = getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);
            if (vpcInstanceId == null) {
                vpcInstanceId = custins.getInsName();
            }
            CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                    connAddrCust,
                    connPort,
                    netType,
                    CustinsValidator.getRealNumber(tunnelId, -1),
                    vpcId,
                    vswitchId,
                    ipaddress,
                    vpcInstanceId);
            custinsConnAddrList.add(custinsConnAddr);
        } else {
            // 创建实例连接对象
            CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(connAddrCust, connPort,
                    netType, -1, null, null, null, null);
            custinsConnAddrList.add(custinsConnAddr);
        }

        // 设置SQLWall配置
        if (custins.isTop()) {
            if (getParameterValue(params, ParamConstants.ACCOUNT_NAME) == null) {// TOP实例account为必选参数
                return createErrorResponse(ErrorCode.ACCOUNT_NOT_FOUND);
            }
            custins.setSqlwallSwitch("true".equalsIgnoreCase(getParameterValue(params, "SqlwallSwitch", "true")) ? 1 : 0);
            custins.setSqlwallTimeoutEvent(Integer.valueOf(getParameterValue(params, "SqlwallTimeoutEvent", "2")));
            custins.setSqlwallInjectEvent(Integer.valueOf(getParameterValue(params, "SqlwallInjectEvent", "2")));
        } else {
            custins.setSqlwallSwitch("true".equalsIgnoreCase(getParameterValue(params, "SqlwallSwitch", "true")) ? 1 : 0);
            custins.setSqlwallTimeoutEvent(Integer.valueOf(getParameterValue(params, "SqlwallTimeoutEvent", "0")));
            custins.setSqlwallInjectEvent(Integer.valueOf(getParameterValue(params, "SqlwallInjectEvent", "0")));
        }


        Integer nodeCount = (insLevel.getInsCount() == null) ? 3 : insLevel.getInsCount();
        if (CustinsSupport.isDns(connType)) {
            //只给Leader和Follower创建DNS
            for (int i = 0; i < 2; ++i) {
                String connAddrNode = mysqlParamSupport.getConnAddrCust(connectionString + "-" + (i + 1),(custins.getRegionId()), custins.getDbType());
                CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                        connAddrNode,
                        connPort,
                        netType,
                        -1,
                        null,
                        null,
                        null,
                        null);
                custinsConnAddr.setInsId(-1);
                custinsConnAddrList.add(custinsConnAddr);
            }
        }


        //检查是否有指定版本创建
        String targetMinorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
            dbType,
            dbVersion,
            classCode,
            MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_XDB,
            mysqlParamSupport.getParameterValue(params, "TargetMinorVersion"));

        boolean createFromBackup = false;
        String bakId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        Long bakupsetId = 0L;
        CustInstanceDO srcCustins = null;
        BaksetMetaInfo baksetMetaInfo = null;
        String restoreType = null;
        // 获取备份策略参数，下发实例创建时，这些参数将被写入parameter字段中
        Map<String, Object> taskQueueParam = CustinsSupport.getTaskQueueParam(params);
        String srcCustinsId = getParameterValue(params, ParamConstants.SOURCE_DBINSTANCE_ID);

        // 记录参数模板
        taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
        // 记录参数模板详情
        taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, SysParamGroupHelper.describeSysParamGroupId(paramGroupId));
        // 记录mysql特定参数
        taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS, customMysqlParams);



        //指定版本创建
        if(targetMinorVersion != null){
            taskQueueParam.put("minor_version", targetMinorVersion);
            String tag = minorVersionServiceHelper.getTagByCondition(dbType, dbVersion, classCode, MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_XDB, mysqlParamSupport.getParameterValue(params, "TargetMinorVersion"));
            isPolarxHatp = MINOR_VERSION_TAG_XCLUSTER_RPM_POLARX_HATP.equalsIgnoreCase(tag);
        }


        //获得是否为容灾恢复场景标识
        Boolean isDisasterRestore = BakSupport.getAndCheckIsDisasterRestore(params);
        if (isDisasterRestore) {
            // 设置容灾恢复的标志位
            taskQueueParam.put("is_disaster_restore", 1);
            createFromBackup = true;
            // 容灾恢复因为跨region, 所以源实例不存在, BLS恢复可能会同region, 在对应分支中进行设置
            srcCustinsId = null;
            if (isPolarxUsingDBSService) {
                // PolarDB-X的DN支持的恢复类型有所区别，且无需校验备份集信息
                restoreType = getParameterValue(params, ParamConstants.RESTORE_TYPE);
                if (!RESTORE_TYPE_XDB_SEQUENCE_NUM.equals(restoreType) && !RESTORE_TYPE_BAKID.equals(restoreType)) {
                    throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
                }
                logger.info("Data node of polardb-x, no need to check cross region backup set info.");
            } else {
                restoreType = mysqlParamSupport.getAndCheckRestoreType(params);
                CheckCreateDdrDBInstanceImpl checkCreateDdrDBInstance = SpringContextUtil.getBeanByClass(CheckCreateDdrDBInstanceImpl.class);
                baksetMetaInfo = checkCreateDdrDBInstance.doCheckDdrRestore(custins, params, restoreType);
                bakupsetId = baksetMetaInfo.getId();
            }
        }
        if (srcCustinsId != null) {
            srcCustins = custinsService.getAndCheckCustInstanceById(
                    srcCustinsId,
                    getParameterValue(params, ParamConstants.USER_ID),
                    getParameterValue(params, ParamConstants.UID),
                    getParameterValue(params, ParamConstants.INNER_USER_ID));
            if (srcCustins.getIsDeleted() == 0) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            custins.setParentId(srcCustins.getId());
            custins.setAccountMode(srcCustins.getAccountMode());
            if (bakId != null) {
                bakupsetId = CheckUtils.parseLong(bakId, null, null, ErrorCode.BACKUPSET_NOT_FOUND);
            } else {
                Map<String, Object> condition = ImmutableMap.of(
                        "ignoredRetention", true,
                        "custinsIds", ImmutableList.of(srcCustins.getId()),
                        "location", BAKUPSET_LOCATION_OSS,
                        "status", "OK");
                List<Map<String, Object>> bakHistoryMapByCondition = bakService.getBakHistoryMapByCondition(
                        condition);
                if (bakHistoryMapByCondition.isEmpty()) {
                    return createErrorResponse(ErrorCode.BACKUPSET_NOT_FOUND);
                }
                bakupsetId = (Long) bakHistoryMapByCondition.get(0).get("BackupSetID");
            }
//            if (mysqlParamSupport.isMysqlXDBByLevel(insLevel)) {
//                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
//            }
            restoreType = RESTORE_TYPE_BAKID;
            bakService.getBakhistory(srcCustins, bakupsetId);
            taskQueueParam.put("bakhis_id", bakupsetId);
            taskQueueParam.put("recover_custins_id", srcCustins.getId());
            createFromBackup = true;
        }
        Integer taskId = null;
        if (custins.isExcluse()) {
            // 无源实例则按照一定规则设置accountMode
            if (srcCustinsId == null) {
                mySQLService.setCustinAccountMode(custins, getParameterValue(params, ParamConstants.ACCOUNT_NAME), region, mysqlParamSupport.getParameterValue(params, ParamConstants.ACCOUNT_TYPE));
            }

            AccountsDO account = accountService.genAccountByCustins(custins,
                    getParameterValue(params, ParamConstants.ACCOUNT_NAME),
                    getParameterValue(params, ParamConstants.ACCOUNT_PASSWORD),
                    getParameterValue(params, ParamConstants.ENCRYPT_ACCOUNT_PASSWORD));
            DbsDO db = dbsService.getAndCheckDb(custins,
                    getParameterValue(params, ParamConstants.DB_NAME),
                    getParameterValue(params, ParamConstants.DB_DESCRIPTION),
                    getParameterValue(params, ParamConstants.DB_CHARACTER_SET_NAME));
            CustinsIpWhiteListDO custinsIpWhiteList = ipWhiteListService.getAndCheckCustinsIpWhiteList(custins,
                    getParameterValue(params, ParamConstants.SECURITY_IP_LIST),
                    getParameterValue(params, ParamConstants.WHITELIST_NET_TYPE, CustinsIpWhiteListDO.DEFAULT_NET_TYPE),
                    getParameterValue(params, ParamConstants.SECURITY_IP_TYPE, CustinsIpWhiteListDO.DEFAULT_IP_TYPE));
            List<CustinsParamDO> custinsParams = custinsParamService.createCustinsParamsForNewCustins(custins,
                    getParameterValue(params, "SystemDBCharset",
                            CustinsParamSupport.CUSTINS_PARAM_VALUE_SYS_DBS_CHARSET_CHINESE_PRC_CI_AS),
                    getParameterValue(params, ParamConstants.DB_INSTANCE_SYNC_MODE),
                    getParameterValue(params, ParamConstants.ECS_SECURITY_GROUP_ID),
                    getParameterValue(params, ParamConstants.RESOURCE_GROUP_ID, "").trim(),
                    paramGroupId, customMysqlParams);

            if(isPolarxHatp){
                custinsParams.add(new CustinsParamDO(null, CUSTINS_PARAM_NAME_IS_POLARX_HATP, CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES));
            }

            if (isPolarxUsingDBSService) {
                custinsParams.add(new CustinsParamDO(null, POLARX_USING_DBS_SERVICE_PENGINE,
                        POLARX_USING_DBS_SERVICE_PENGINE_YES));
            }

            custins.setCustinsParams(custinsParams);

            boolean hasInstanceId = getParameterValue(params, ParamConstants.DB_INSTANCE_ID) != null;
            if (hasInstanceId) {
                custins = custinsService.updateCustInstance(custins);
            } else {
                custins = custinsService.createCustInstance(custins);

                String proxyGroupId = getParameterValue(params, ParamConstants.PROXY_GROUP_ID);
                Integer perferedProxyGroupId = proxyGroupId == null ? null : Integer.valueOf(proxyGroupId);
                List<Integer> instanceIds;
                try {
                    String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
                    String accessId = CustinsParamSupport.getParameterValue(params, ParamConstants.ACCESSID);
                    String orderId = CustinsParamSupport.getParameterValue(params, ParamConstants.ORDERID);
                    instanceIds = createMysql(
                            getAction(params),
                            requestId,
                            custins,
                            insLevel,
                            avzInfo,
                            clusterName,
                            null,
                            null,
                            connType,
                            perferedProxyGroupId,
                            custinsConnAddrList,
                            nodeCount,
                            bizType,
                            Integer.valueOf(hostType),
                            null,
                            getAndCheckHostIdSet(params),
                            resourceStrategy,
                            accessId,
                            orderId,
                            specifyPort,
                            true);
                } catch (RdsException e) {
                    return createErrorResponse(e.getErrorCode());
                }
                if (CustinsSupport.ISOLATE_HOST_FIXED.equals(insLevel.getIsolateHost())) {
                    custinsParamService.setCustinsParam(custins.getId(), MySQLParamConstants.V6_CPU_MATCH,
                            MySQLParamConstants.V6_CPU_MATCH_VALUE);
                }

                avzSupport.updateAVZInfoByInstanceIds(avzInfo, instanceIds);
                custinsParamService.updateAVZInfo(custins.getId(), avzInfo);

                if (createFromBackup) {
                    if (isPolarxUsingDBSService) {
                        srcCustins = custins;
                        TransListDO trans = new TransListDO(srcCustins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
                        Map<String, Object> translistParamMap = new HashMap<>(8);
                        translistParamMap.put("restoreType", restoreType);

                        // 填充共有参数
                        List<String> disasterRestoreParams = Arrays.asList(
                                "sourceRegion",
                                "sourceDBInstanceName",
                                "destCrossRegion"
                        );
                        for (String paramName : disasterRestoreParams) {
                            String param = getParameterValue(params, paramName);
                            if (param == null || param.isEmpty()) {
                                return createErrorResponse(ErrorCode.INTERNAL_FAILURE,
                                        String.format("The specified parameter %s is empty", paramName));
                            }
                            translistParamMap.put(paramName, param);
                        }

                        if (RESTORE_TYPE_XDB_SEQUENCE_NUM.equals(restoreType)) {
                            // 任意时间点一致性恢复
                            BigInteger seqNum = new BigInteger(getParameterValue(params, "SequenceNumber"));
                            BigInteger pointPeekBackward = new BigInteger(getParameterValue(params, "PointPeekBackward"));
                            BigInteger pointPeekForward = new BigInteger(getParameterValue(params, "PointPeekForward"));
                            DateTime restoreTimeUTC = dtzSupport.getUTCDateByDateStr(getParameterValue(params, ParamConstants.RESTORE_TIME));
                            Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_DBAAS);

                            trans.setIsBaseTime(1);
                            trans.setRecoverTime(restoreTime);
                            translistParamMap.put("sequenceNumber", seqNum.toString());
                            translistParamMap.put("pointPeekBackward", pointPeekBackward.toString());
                            translistParamMap.put("pointPeekForward", pointPeekForward.toString());
                        } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                            // 备份集恢复：仅适用于PolarDB-X标准版
                            translistParamMap.put("backupSetId", bakId);
                        }

                        List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(srcCustins.getId());
                        trans.setsHinsid1(insIds.get(0));
                        if (insIds.size() > 1) {
                            trans.setsHinsid2(insIds.get(1));
                        }
                        trans.setsCinsReserved(1);
                        trans.setdCinsid(custins.getId());
                        trans.setdLevelid(custins.getLevelId());
                        trans.setdDisksize(custins.getDiskSize());
                        trans.setdHinsid1(instanceIds.get(0));
                        if (instanceIds.size() > 1) {
                            trans.setdHinsid2(instanceIds.get(1));
                        }
                        trans.setParameter(JSON.toJSONString(translistParamMap));
                        instanceService.createTransList(trans);
                        taskQueueParam.put("trans_list_id", trans.getId());
                    } else {

                        TransListDO trans;
                        Map<String, Object> translistParamMap = new HashMap<>(8);
                        if (isDisasterRestore) {
                            // 容灾恢复场景无源实例信息, 将源实例塞成目标实例
                            srcCustins = custins;
                            // 将容灾恢复场景标识及备份集信息塞进param中
                            translistParamMap.put("bakhis_info", JSONObject.toJSONString(baksetMetaInfo));
                        }
                        trans = new TransListDO(srcCustins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
                        if (RESTORE_TYPE_TIME.equals(restoreType)) {
                            trans.setBakhisId(bakupsetId);
                            taskQueueParam.put("bakhis_id", bakupsetId);
                            trans.setIsBaseTime(1);
                            DateTime restoreTimeUTC = dtzSupport.getUTCDateByDateStr(getParameterValue(params, ParamConstants.RESTORE_TIME));
                            Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_DBAAS);
                            trans.setRecoverTime(restoreTime);
                        }
                        List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(srcCustins.getId());
                        trans.setsHinsid1(insIds.get(0));
                        if (insIds.size() > 1) {
                            trans.setsHinsid2(insIds.get(1));
                        }
                        trans.setsCinsReserved(1);
                        translistParamMap.put("restoreType", restoreType);
                        if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                            taskQueueParam.put("bakhis_id", bakupsetId);
                            trans.setBakhisId(bakupsetId);
                        }
                        trans.setdCinsid(custins.getId());
                        trans.setdLevelid(custins.getLevelId());
                        trans.setdDisksize(custins.getDiskSize());
                        trans.setdHinsid1(instanceIds.get(0));
                        if (instanceIds.size() > 1) {
                            trans.setdHinsid2(instanceIds.get(1));
                        }
                        trans.setParameter(JSON.toJSONString(translistParamMap));
                        instanceService.createTransList(trans);
                        taskQueueParam.put("trans_list_id", trans.getId());
                    }
                }
            }

            taskId = mySQLService.createCustInstanceTask(
                    getAction(params),
                    custins,
                    db,
                    account,
                    taskQueueParam,
                    custinsIpWhiteList,
                    getOperatorId(params),
                    null);
            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(params));
        } else { // 共享实例
            String dbname = CheckUtils.checkValidForDbName(getParameterValue(params, ParamConstants.DB_NAME));
            String accountName = CheckUtils.checkValidForAccountName(
                    CheckUtils.checkNullForAccountName(getParameterValue(params, ParamConstants.ACCOUNT_NAME)),
                    custins.getDbType(),
                    custins.isTop(),
                    AccountPriviledgeType.PRIVILEDGE_NORMAL,
                    custins.getDbVersion());

            DbsDO dbs = new DbsDO(
                    custins,
                    dbname,
                    dbsService.checkCharacterSetName(
                            custins.getDbType(),
                            custins.getDbVersion(),
                            getParameterValue(params, ParamConstants.DB_CHARACTER_SET_NAME)));

            if (CustinsParamSupport.hasParameterValue(params, ParamConstants.DB_DESCRIPTION)) {
                String desc = URLDecoder.decode(getParameterValue(params, ParamConstants.DB_DESCRIPTION, ""), "UTF-8");
                dbs.setComment(CheckUtils.checkLength(desc, 1, 256, ErrorCode.INVALID_DBDESCRIPTION));
            }

            AccountsDO account = new AccountsDO(
                    custins,
                    accountName,
                    accountService.getAndCheckDecryptedAccountPassword(
                            getParameterValue(params, ParamConstants.ACCOUNT_PASSWORD),
                            getParameterValue(params, ParamConstants.ENCRYPT_ACCOUNT_PASSWORD)));

            // 共享实例最大支持创建1个DB，2个账户
            custins.setMaxDbs(1);
            custins.setMaxAccounts(2);
            // 共享实例custinsConnAddrList 只有一个元素
            CustinsConnAddrDO custinsConnAddr = custinsConnAddrList.get(0);
            Response<AllocateVipRespModel> response = resApi.allocateDns(
                    custins.getId(),
                    custinsConnAddr.getNetType(),
                    custinsConnAddr.getConnAddrCust(),
                    0);
            if (!response.getCode().equals(200)) {
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            taskId = custinsService.createShareCustInstance(
                    getAction(params),
                    custins,
                    dbs,
                    account,
                    custinsResourceService.getShareResource(
                            getAction(params),
                            getRequestUUID(params),
                            custins,
                            region,
                            clusterName,
                            accountName,
                            dbname,
                            custinsConnAddr.getNetType()),
                    custinsConnAddr,
                    getOperatorId(params));
            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(params));
        }

        Map<String, Object> data = new HashMap<>();
        data.put("Region", region);
        data.put("ConnectionString", connAddrCust);
        data.put("Port", connPort);
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("DBInstanceType", custins.getTypeChar());
        data.put("DBMaxQty", custins.getMaxDbs());
        data.put("AccountMaxQty", custins.getMaxAccounts());
        data.put("TaskId", taskId);
        data.put("DBInstanceNetType", netType);
        data.put("DBInstanceConnType", custins.getConnType());
        data.put(ParamConstants.ACCOUNT_TYPE, custins.isSuperAccountMode() ? 1 : 0);
        data.put(ParamConstants.SUPPORT_UPGRADE_ACCOUNT_TYPE,
                custins.isAllowUpgradeSuperAccountMode() ? 1 : 0);
        if (custins.isMongoDB()) {
            data.put(ParamConstants.REPLSET_NAME, "mgset-" + custins.getId());
        }
        return data;
    }

    public static void main(String[] args) {

        NameServiceClient nsc = new NameServiceClient("http://name-service-atp-3421.apsaradb-gw.alibabacloud.test", 3, 3);
        String version = null;
        String bizType = null;
        String region = null;
        String site = null;
        String tags = null;
        Service service = nsc.findFirstAvailableService("RDSAPI_EXT_MYSQL", version, bizType, region, site, tags);
        //List<Registry> registries = service.getRegistries();
        //String addr = registries.get(0).getAddress();
        System.out.println(service.getGwSubDomain());

    }
}
