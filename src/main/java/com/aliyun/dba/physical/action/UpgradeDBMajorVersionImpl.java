package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.upgradeprecheck.UpgradeMajorVersionPreCheckExecutor;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.DbossApiService;
import com.aliyun.dba.physical.action.service.MysqlMajorVersionCheckService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.*;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_VERSION_MYSQL_57;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_VERSION_MYSQL_80;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_STANDARD;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalUpgradeDBMajorVersionImpl")
@Slf4j
public class UpgradeDBMajorVersionImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeDBVersionImpl.class);

    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected DbossApi dbossApi;

    @Autowired
    protected CustinsParamService custinsParamService;

    @Autowired
    protected MycnfService mycnfService;

    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;

    @Autowired
    protected CheckService checkService;

    @Autowired
    protected InstanceService instanceService;

    @Autowired
    protected TaskService taskService;

    @Autowired
    protected ResApi resApi;

    @Autowired
    protected AVZSupport avzSupport;

    @Autowired
    private MinorVersionService minorVersionService;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected InstanceIDao instanceIDao;

    @Autowired
    protected CustinsIDao custinsIDao;

    @Resource
    private ReplicaSetService replicaSetService;


    @Resource
    private MysqlMajorVersionCheckService mysqlMajorVersionCheckService;

    @Resource
    DbossApiService dbossApiService;

    @Autowired
    private ClusterService clusterService;

    @Autowired
    protected ResourceService resourceService;

    @Autowired
    private UpgradeMajorVersionPreCheckExecutor preCheckExecutor;

    //大版本升级前置检查开关
    private static final String UPGRADE_MAJOR_VERSION_PRECHECK_SWITCH = "UPGRADE_MAJOR_VERSION_PRECHECK_SWITCH";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        try {
            //各个地方从参数Map中获取值时，无需重复传递Map
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParameterHelper.getAndCheckCustInstance();
            String majorVersion = getParameterValue(actionParams, ParamConstants.MAJOR_VERSION);
            // 设置切换时间
            Date switchTime = checkService.getAndCheckSwitchTime(getParameterValue(actionParams, ParamConstants.SWITCH_TIME));
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(actionParams, switchTime, true);
            Map<String, Object> taskQueueParam = new HashMap<String, Object>();
            //是否走新版check
            ResourceDO upgradePreCheckSwitch = resourceService.getResourceByResKey(UPGRADE_MAJOR_VERSION_PRECHECK_SWITCH);
            if (Objects.isNull(upgradePreCheckSwitch) || !BooleanUtils.toBoolean(upgradePreCheckSwitch.getRealValue())) {
                //实例是否存在   1存在
                if (custins.isDeleting()) {
                    return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
                }
                // 要求实例状态是运行中  1代表active
                if (!custins.isActive()) {
                    //实例状态错误
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
                Integer custinsId = custins.getId();
                // 当前只支持56>>57 或者 57>>80
                String targetDbVersion = majorVersion;
                boolean is56to57 = custins.isMysql56() && DB_VERSION_MYSQL_57.equalsIgnoreCase(targetDbVersion);
                boolean is57to80 = custins.isMysql57() && DB_VERSION_MYSQL_80.equalsIgnoreCase(targetDbVersion);
                if (!(is56to57 || is57to80)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
                }

                // 校验maxscale内核小版本是否支持
                List<String> releaseDateList = minorVersionService.getReleaseDateListByTag(
                        custins.getDbType(),
                        CustinsSupport.DB_VERSION_MYSQL_80,
                        KindCodeParser.KIND_CODE_NC,
                        CATEGORY_STANDARD,
                        MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_RPM
                );
                String targetReleaseDate = releaseDateList.isEmpty() ? null : releaseDateList.get(0);
                if (!mysqlMajorVersionCheckService.checkCanUpgradeMajorVersionWithMaxScale(custins, targetReleaseDate)) {
                    return ResponseSupport.createErrorResponse(new Object[]{ResultCode.CODE_UNSUPPORTED, "MaxscaleMinorVersionNotSupport", "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first."});
                }

                // mysql80不支持proxy链路，因此57升级80 校验当前实例必须是lvs链路
                if (custins.isMysql57() && !custins.isLvs()) {
                    return createErrorResponse(ErrorCode.INVALID_CONN_TYPE);
                }

                //db check
                //mysql 5.6， 5.7 upgrade check myisam
                /*
                 * engine
                 * 0 myisam
                 * 1 innodb
                 * 2 memory
                 * 3 tokudb
                 * more...  in dboss
                 * */
                // TODO 这里注释， 因为表数量超过10万条的时候 这里很慢 会导致接口超时 这个接口逻辑放到任务流当中去做
                // 重新放开该校验逻辑, 将该校验放到任务流会导致用户感知不到升级失败原因(aone: 38643082)
                Integer engine =0;

                // 政务云不放在茅台层校验 放到再往后面的pengine任务流中去校验
                ClustersDO cluster = clusterService.getClusterByCustinsId(Long.valueOf(custinsId));
                logger.info("Look at the clusterName and cluster.location here: " + custins.getClusterName() + ", " + cluster.getLocation());
                List<String> versionUpgradeWhiteLists = resourceService.getResourceRealValueList("MYSQL_VERSION_UPGRADE_WHITE_LIST");
                Set<String> versionUpgradeWhiteListSet = versionUpgradeWhiteLists.stream().map(versionUpgradeWhiteList -> versionUpgradeWhiteList.split(",")).flatMap(Arrays::stream).collect(Collectors.toSet());
                if (!versionUpgradeWhiteListSet.contains(custins.getInsName())) {
                    if (!(custins.getClusterName().contains("GOV") || cluster.getLocation().contains("gov"))){
                        Map<String, Object> engineList = dbossApiService.queryEngineCount(custinsId,engine,"slave");
                        Integer engineCount = (Integer)(engineList.get("myisam"));
                        if (engineCount > 0) {
                            logger.info("myisam engineCount: " + engineCount);
                            return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE_MYISAM);
                        }
                    }
                } else {
                    logger.info("The instance is in the version upgrade white list, skip the db check, white list:{}, custins:{}", JSON.toJSONString(versionUpgradeWhiteListSet), JSON.toJSONString(custins));
                }

                // 校验mysql56 的 aliyun_root的账户不存在
                String accountName = "aliyun_root";
                List<Map<String, Object>> accountList = dbossApi.queryAccounts(custins.getId(), accountName, "", 0, 1);
                if (custins.isMysql56() && !CollectionUtils.isEmpty(accountList)){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ACCOUNT_ALIYUNROOT);
                }

                //level check  56的规格在57是否存在（或者57的规格在80是否存在）
                boolean levelSupport = custinsService.checkInstanceLevelSupportUpgradeTargetMajorVersion(custinsId, targetDbVersion);
                if (!levelSupport){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
                }

                // 对于56升级57时进行特殊校验
                // 从用户的参数元数据中去获取default_storge_enigne的设置值，如果为tokudb引擎，myisam引擎等需要报错 aone to #********
                if (is56to57) {
                    // 不支持default_storge_enigne为tokudb引擎，myisam引擎
                    MycnfCustinstanceDO storageEngineMycnf = mycnfService.getMycnfCustinstance(custins.getId(), "default_storage_engine");
                    if (Objects.nonNull(storageEngineMycnf) && Arrays.asList("tokudb", "myisam").contains(storageEngineMycnf.getParaValue().toLowerCase())){
                        Object[] sslErrorCode = new Object[]{ResultCode.CODE_ERROR, "InvalidDefaultStorageEngine", "This instance default_storage_engine has tokudb or myisam and does not support major version upgrades"};
                        return ResponseSupport.createErrorResponse(sslErrorCode);
                    }
                }
                if (!versionUpgradeWhiteListSet.contains(custins.getInsName())) {
                    Map<String, Object> queryFtsIndexResult = dbossApi.queryFtsIndex(custins.getId());
                    Integer ftsIndexCount = Integer.valueOf(queryFtsIndexResult.getOrDefault("count", "0").toString());
                    if (ftsIndexCount > 0) {
                        log.error("find fts index in custins. ftsIndexCount:{}, ftsIndexList:{}", ftsIndexCount, JSON.toJSONString(queryFtsIndexResult.get("result")));
                        String minorVersionStr = custinsParamService.getCustinsParam(custins.getId(), "minor_version").getValue();
                        String dbMinorVersionLongStr = "0";
                        if (minorVersionStr.contains(":")) {
                            int index = minorVersionStr.indexOf(":");
                            dbMinorVersionLongStr = minorVersionStr.substring(index + 1, index + 9);
                        } else {
                            int index = minorVersionStr.lastIndexOf("_");
                            dbMinorVersionLongStr = minorVersionStr.substring(index + 1, index + 9);
                        }
                        long minorVersion = Long.parseLong(dbMinorVersionLongStr);
                        if(minorVersion < 20221130L) {
                            throw new RdsException(ErrorCode.UNSUPPORTED_FTS_INDEX_VERSION);
                        }
                        throw new RdsException(ErrorCode.UNSUPPORTED_FTS_INDEX);
                    }
                } else {
                    logger.info("The instance is in the version upgrade white list, skip the db fts index check, white list:{}, custins:{}", JSON.toJSONString(versionUpgradeWhiteListSet), JSON.toJSONString(custins));
                }

                //需要没有开通TDE
                List<String> params = new ArrayList<String>(1);
                List<CustinsParamDO> resultList;
                params.add(CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_TDE);
                resultList = custinsParamService.getCustinsParams(custins.getId(), params);
                String tdeStatus = CustinsParamSupport.CUSTINS_PARAM_VALUE_TDE_ENABLED;
                // 对于5.7实例，TDEStatus not support
                if (!resultList.isEmpty() && resultList.get(0).getValue().equals(tdeStatus)){
                    Object[] tdeErrorCode = new Object[]{ResultCode.CODE_ERROR, "InvalidTDEstatus", "This instance has TDE enabled and does not support major version upgrades"};
                    return ResponseSupport.createErrorResponse(tdeErrorCode);
                }

                List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(
                        custins.getId(), true);
                if (!CollectionUtils.isEmpty(readCustinsList)) {
                    Set<Integer> readLevelIdSet = new HashSet<>();
                    HashSet<String> readConnTypeSet = new HashSet<>();
                    // 校验只读实例的规格在mysql57/80是否支持 (aone: 39057202)
                    for (CustInstanceDO custInstanceDO : readCustinsList) {
                        readLevelIdSet.add(custInstanceDO.getLevelId());
                        boolean readLevelSupport = custinsService.checkInstanceLevelSupportUpgradeTargetMajorVersion(
                                custInstanceDO.getId(), targetDbVersion);
                        if (!readLevelSupport){
                            return createErrorResponse(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
                        }
                        // mysql80不支持proxy链路,所以升级前mysql57只读实例的connType链路必须是lvs 不判断备用只读
                        // 因备用只读存在physical网络链路所以需要支持physical链路的升级
                        if ((custInstanceDO.isReadBackup() && (
                                        (custInstanceDO.isMysql57() && !custInstanceDO.isLvs() && !"physical".equals(custInstanceDO.getConnType())) ||
                                        (custInstanceDO.isMysql56() && !"physical".equals(custInstanceDO.getConnType()) && !custInstanceDO.isLvs() && !custInstanceDO.isProxy() && !custInstanceDO.isDns()))) ||
                                        (!custInstanceDO.isReadBackup() && custInstanceDO.isMysql57() && !custInstanceDO.isLvs() && !custInstanceDO.isDns())) {
                            return createErrorResponse(ErrorCode.INVALID_CONN_TYPE);
                        }
                        //当存在dns链路时 需要单独判断只读和备用只读网络是否一致
                        readConnTypeSet.add(custInstanceDO.getConnType());
                    }

                    //当存在dns链路时 需要单独判断只读和备用只读网络是否一致,如果不一致则不允许升级
                    if (readConnTypeSet.size() > 1 && readConnTypeSet.contains("lvs") && readConnTypeSet.contains("dns")){
                        return createErrorResponse(ErrorCode.INVALID_CONN_TYPE);
                    }

                    //检验只读是否满足升级条件
                    this.checkReadIns(readCustinsList, targetDbVersion);
                }

                //锁定状态不支持
                if (!custins.isNoLock()) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
                }

                //需要是主实例  不能是临时只是或者备用只读实例
                if (!custins.isPrimary()){
                    return createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);
                }

                //查询任务记录 判断升级任务流是否已存在 (aone: 40176395)
                Map<String, Object> condition = new HashMap<>();
                condition.put("custinsId", custinsId); //实例ID
                condition.put("action", "UpgradeDBMajorVersion"); //API请求标
                condition.put("taskKey", "upgrade_major_version");
                // 0等待  1运行  7暂停 8中断
                int[] status = {0, 1, 7, 8};
                condition.put("status", status);
                Integer count = taskService.countTaskQueueByCondition(condition);
                if (count > 0) {
                    String warningMsg = "Current upgrade_major-version tasksflow with one of wait, run, pause and " +
                            "interrupt status already exist. New upgrade_major-version tasks are not allowed to be issued.";
                    logger.warn(warningMsg);
                    Map<String, Object> skipReturn = new HashMap<String, Object>(5);
                    skipReturn.put("DBInstanceID", custins.getId());
                    skipReturn.put("DBInstanceName", custins.getInsName());
                    skipReturn.put("TargetMajorVersion",majorVersion);
                    skipReturn.put("TaskId", null);
                    skipReturn.put("desc", warningMsg);
                    return skipReturn;
                }
            }else{
                String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID, "");
                ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(actionParams);
                preCheckExecutor.execute(custins, replicaSetMeta, majorVersion, requestId);
            }
            // 创建task
            Map<String, Object> effMap = custinsService.getEffectiveTimeMap(switchMode, switchTime);
            taskQueueParam.put(CustinsSupport.SWITCH_KEY, effMap);
            taskQueueParam.put(ParamConstants.MAJOR_VERSION,majorVersion);

            // todo :String statusDesc = CustinsState.STATE_MINOR_VERSION_UPGRADING.getComment(); STATE_MINOR_VERSION_UPGRADING改为大版本升级中
            Integer taskId = instanceService
                    .upgradeMajorVersionTask(getAction(actionParams), custins, taskQueueParam, CustinsParamSupport.getOperatorId(actionParams));

            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TargetMajorVersion",majorVersion);
            data.put("TaskId", taskId);
            return data;

        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }

    }

    /*
  将只读实例校验逻辑抽出来，并支持带只读双节点的情况校验
   */
    public void checkReadIns(List<CustInstanceDO> readCustinsList, String targetDbVersion) throws RdsException{
        HashSet<String> readConnTypeSet = new HashSet<>();
        // 校验只读实例的规格在mysql57/80是否支持 (aone: 39057202)
        for (CustInstanceDO custInstanceDO : readCustinsList) {
            boolean readLevelSupport = custinsService.checkInstanceLevelSupportUpgradeTargetMajorVersion(custInstanceDO.getId(), targetDbVersion);
            if (!readLevelSupport){
                throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
            }
            // mysql80不支持proxy链路,所以升级前mysql57只读实例的connType链路必须是lvs 不判断备用只读
            // 因备用只读存在physical网络链路所以需要支持physical链路的升级
            if ((custInstanceDO.isReadBackup() && (
                    (custInstanceDO.isMysql57() && !custInstanceDO.isLvs() && !"physical".equals(custInstanceDO.getConnType())) ||
                    (custInstanceDO.isMysql56() && !"physical".equals(custInstanceDO.getConnType()) && !custInstanceDO.isLvs() && !custInstanceDO.isProxy() && !custInstanceDO.isDns()))) ||
                    (!custInstanceDO.isReadBackup() && custInstanceDO.isMysql57() && !custInstanceDO.isLvs() && !custInstanceDO.isDns())) {
                throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
            }
        }

        //将只读双节点过滤掉，不进行比较
        List<CustInstanceDO> readSingleCustinsList = new ArrayList<>();
        List<CustInstanceDO> bakReadCustinsList = new ArrayList<>();
        for (CustInstanceDO readIns : readCustinsList){
            if (readIns.isRead()){
                List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(readIns.getId());
                if (instanceList.size() != 2){
                    readSingleCustinsList.add(readIns);
                    readConnTypeSet.add(readIns.getConnType());
                }
            } else if(readIns.isReadBackup()){
                bakReadCustinsList.add(readIns);
                readConnTypeSet.add(readIns.getConnType());
            }
        }
        //除了双节点，没有单节点的只读实例则不进行后面的校验
        if (readSingleCustinsList.size() == 0){
            return;
        }

        //存在只读但是不存在备用只读的不支持升级
        if(bakReadCustinsList.size() < 1){
            throw new RdsException(ErrorCode.UNSUPPORTED_READ_OR_BAKREAD_STATE);
        }

        //当存在dns链路时 需要单独判断只读和备用只读网络是否一致,如果不一致则不允许升级
        if (readConnTypeSet.size() > 1 && readConnTypeSet.contains("lvs") && readConnTypeSet.contains("dns")){
            throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
        }

    }

}
