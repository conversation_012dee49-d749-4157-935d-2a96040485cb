package com.aliyun.dba.physical.action;


import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.AccountsDTO;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.*;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.task.service.TaskServiceImpl;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.base.Strings;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsSupport.*;

/**
 * 实例账户模式区分:
 * 之前的老账户模式都是通过 cust_instance 变量的 account_mode 字段来区分的,
 * 为了保持一致性和更好的迁移管理实例帐号模式从实例列表到实例账户模式),也继续使用 account_mode 来区分,在原有的基础上,做 +3 处理
 * +----------+-----------------+-----------------------------+------------------------------+
 * | 账户模式  | 不支持有权限账户   | 支持有权限账户但是没有高权限账户 | 有高权限账户                   |
 * +----------+-----------------+-----------------------------+------------------------------+
 * | 老账户模式 | O               | 1                           | 2                            |
 * | 新账户模式 | 3               | 4                           | 5                            |
 * +----------+-----------------+-----------------------------+------------------------------+
 * https://alidocs.dingtalk.com/i/nodes/D1YKdxGX7EqVQRo5jrBaJe4QrZk95AzP?doc_type=wiki_doc&utm_medium=dingdoc_doc_plugin_card&utm_scene=team_space&utm_source=dingdoc_doc
 **/

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCreateAccountImpl")
public class CreateAccountImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateAccountImpl.class);

    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    protected DbossApi dbossApi;
    @Resource
    protected AccountService accountService;
    @Resource
    protected AccountIDao accountIDao;
    @Resource
    protected TaskServiceImpl taskService;
    @Resource
    protected CustinsIDao custinsIDao;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);

        try {
            if (!custInstanceDO.isLogicPrimary()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            Integer custinsId = custInstanceDO.getId();

            String accountPrivilegeDesc = parameterHelper.getAccountPrivilege();
            String accountType = parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,
                    CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON);
            Integer accountPrivilegeType = CustinsSupport.getAccountPrivilegeType(accountType);
            if (accountPrivilegeType == null) {
                throw new RdsException(ErrorCode.INVALID_ACCOUNT_TYPE);
            }
            AccountPriviledgeType accountPriviledgeType = AccountPriviledgeType.getAccountPriviledgeType(accountPrivilegeType);

            String accountName
                    = CheckUtils.optimizeCheckValidForAccountName(parameterHelper.getAccountName(), custInstanceDO.getDbType(),
                    false, accountPriviledgeType, custInstanceDO.getDbVersion(),
                    custInstanceDO.getKindCode(), DbsSupport.BIZ_TYPE_USER);
            String dbInsName = parameterHelper.getDBInstanceName();
            String password = parameterHelper.getAndCheckDecryptedAccountPassword();
            String dbInfo = parameterHelper.getDbInfo();
            List<String> dbNames = parameterHelper.getDBNames();
            String comment = SupportUtils.decode(parameterHelper.getAndCheckAccountDesc());

            // 返回值参数
            Integer accountID = 0;
            Integer taskID = 0;
            Integer accountStatus = DbsSupport.STATUS_ACTIVE;
            // 普通账号，走DBoss创建
            if (CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON.equals(accountType) && dbossApi.isHandleByDBoss(custInstanceDO)) {
                // basic check
                if (custInstanceDO.isReadAndWriteLock()) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
                }
                if (!custInstanceDO.inAvailableStatus()) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }

                Map<String, Object> account = new HashMap<>(7);
                account.put("accountName", accountName);
                account.put("accountType", accountType); // “0”, not “normal”.
                account.put("custinsId", custinsId);
                account.put("password", password);
                account.put("requestId", requestId);
                if (!Strings.isNullOrEmpty(comment)) {
                    account.put("comment", comment);
                }
                List<Map<String, String>> privileges
                        = accountService.getDatabasePrivileges(dbNames, accountPrivilegeDesc, dbInfo, custInstanceDO.getDbType());
                if (!CollectionUtils.isEmpty(privileges)) {
                    account.put("privileges", privileges);
                }

                //mysql普通账号名不能与高权限名称类似
                if (accountIDao.queryAccountByAccountName(custinsId, accountName) != null) {
                    throw new RdsException(ErrorCode.ACCOUNTNAME_ALREADYEXISTS);
                }

                dbossApi.createAccount(account);
            } else {
                AccountsDTO addAccountsDTO = new AccountsDTO();
                addAccountsDTO.setBid(parameterHelper.getBID());
                addAccountsDTO.setUid(parameterHelper.getUID());
                addAccountsDTO.setDbInsName(dbInsName);
                addAccountsDTO.setAccount(accountName);
                addAccountsDTO.setPassword(password);
                addAccountsDTO.setComment(comment);
                addAccountsDTO.setDbInfo(dbInfo);
                addAccountsDTO.setDbNames(dbNames);
                addAccountsDTO.setAccountPrivilegeDesc(accountPrivilegeDesc);// 账户DB之间的权限
                addAccountsDTO.setPriviledgeType(accountPriviledgeType);//普通/超级账号权限

                // 逻辑实例单元不能直接创建账户
                String characterType = custInstanceDO.getCharacterType();
                if (characterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_DB) ||
                        characterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_SPHINX)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_CHARACTER_TYPE);
                }

                // 老账户模式通用一套逻辑即可
                if (CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER.equals(accountType)) {
                    // 查询元数据中是否有高权限账号
                    String superName = null;
                    Integer accId = null;
                    if (accountService.countAccountByCustInsId(custinsId) >= custInstanceDO.getMaxAccounts()
                            && custInstanceDO.isSuperAccountMode() && custInstanceDO.getMaxAccounts() == 1) {
                        AccountsDO superAcc = accountService.getAccount(custinsId, DbsSupport.BIZ_TYPE_USER,
                                AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue());
                        if(superAcc != null){
                            superName = superAcc.getAccount();
                            accId = superAcc.getId();
                        }
                    }

                    // mysql老架构高权限账号名不能与普通账号名称类似，同时查询用户实例上有没有高权限账号
                    Map<String, Integer> accountCount = dbossApi.getAccountCount(String.valueOf(custinsId),
                            null, RdsConstants.ROLETYPE_USER);
                    if (accountCount != null && !accountCount.isEmpty() && accountCount.get("accounts") > 0) {
                        Integer totalCount = accountCount.get("accounts");
                        List<Map<String, Object>> accountList = dbossApi.queryAccounts(
                                custinsId, null, null, 0, totalCount);
                        for (Map<String, Object> acc : accountList) {
                            String accName = (String) acc.get("accountName");
                            if (accName.equalsIgnoreCase(accountName)) {
                                throw new RdsException(ErrorCode.ACCOUNTNAME_ALREADYEXISTS);
                            }
                            else if(accName.equals(superName)){
                                // 如果用户使用高权限账号在数据链路创建大小写不同的相似普通账号，之后通过数据链路删除高权限账号，再创建一个完全不同的账号，用ignore会报错
                                throw new RdsException(ErrorCode.ACCOUNTQUOTA_EXCEEDED);
                            }
                        }
                    }

                    // 用户实例上不存在和待创建高权限账号相似的账号名
                    // 如果元数据中存在高权限账号但是用户实例中不存在，那么重刷元数据
                    if (superName != null) {
                        // 用户实例上没有对应的高权限账号，重刷元数据
                        accountIDao.deleteAccountByIdDirectly(accId, superName);
                        // 此时account_mode为2或5, 重置account_mode为allow_upgrade, 后续pengine在任务流init的时候会检查并更新
                        if (CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED.equals(custInstanceDO.getAccountMode())) {
                            custInstanceDO.setAccountMode(CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE);
                        }
                        if (NEW_CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED.equals(custInstanceDO.getAccountMode())) {
                            custInstanceDO.setAccountMode(NEW_CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE);
                        }
                        custinsIDao.updateCustinsAccountMode(custInstanceDO);
                    }
                }

                AccountsDTO accountDTO = accountService.addAccount(addAccountsDTO, parameterHelper.getAction(),
                        parameterHelper.getOperatorId(), parameterHelper.getParameterValue(ParamConstants.ACCESSID));

                taskService.updateTaskPenginePolicy(accountDTO.getTaskId(), parameterHelper.getPenginePolicyID());
                // 重新获取返回参数
                accountID = accountDTO.getId();
                taskID = accountDTO.getTaskId();
                accountStatus = accountDTO.getStatus();
                accountName = accountDTO.getAccount();
            }

            // return
            Map<String, Object> data = new HashMap<>();
            data.put(ParamConstants.ACCOUNT_NAME, accountName);
            data.put(ParamConstants.ACCOUNT_ID, accountID);
            data.put(ParamConstants.ACCOUNT_STATUS, accountStatus);
            data.put(ParamConstants.TASK_ID, taskID);
            data.put(ParamConstants.ACCOUNT_TYPE, accountType);
            return data;

        } catch (RdsException re) {
            logger.error(requestId + " CreateAccount failed: " + re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " CreateAccount failed: " + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
