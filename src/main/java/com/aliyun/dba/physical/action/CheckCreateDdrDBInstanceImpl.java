package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.bak.entity.BaksetMetaInfo;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.backup.DescribeCrossRegionMigrateNewArchStatusParam;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreArchiveLogParam;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeCrossRegionMigrateNewArchStatusResponse;
import com.aliyun.dba.base.response.backup.DescribeRestoreArchiveLogResponse;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.support.PodDateTimeUtils;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DisasterRestoreApi;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsParamSupport.hasParameterValue;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCheckCreateDdrDBInstanceImpl")
public class CheckCreateDdrDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(CheckCreateDdrDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CheckCreateDdrDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected DisasterRestoreApi disasterRestoreApi;
    @Autowired
    protected RdsApi rdsApi;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    private DbsGateWayService dbsGateWayService;
    @Autowired
    private PodDateTimeUtils podDateTimeUtils;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params)
            throws RdsException {
        try {
            BakSupport.preProcessForCreateDdr(params);
            String restoreType = mysqlParamSupport.getAndCheckRestoreType(params);
            doCheckDdrRestore(custins, params, restoreType);
            Map<String, Object> data = new HashMap<>();
            data.put("IsValid", true);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public BaksetMetaInfo doCheckDdrRestore(CustInstanceDO custins, Map<String, String> params, String restoreType)
            throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID);
        String bid = mysqlParamSupport.getAndCheckBID(params);
        String uid = mysqlParamSupport.getAndCheckUID(params);
        String sourceRegion = mysqlParamSupport.getParameterValue(params, "sourceRegion");
        String sourceDBInstanceName = mysqlParamSupport.getParameterValue(params, "sourceDBInstanceName");
        if (isBackupByDbsNewArch(requestId, accessId, bid, uid, sourceRegion, sourceDBInstanceName)) {
            logger.info("source ins {} backup is new dbs backup, so call dbs api do check", sourceDBInstanceName);
            return doCheckDdrRestoreByDbsNewArch(params);
        }

        return doCheckDdrRestore(custins, params, restoreType, false);
    }

    public BaksetMetaInfo doCheckDdrRestore(CustInstanceDO custins, Map<String, String> params, String restoreType,
                                            boolean withBakTableMeta) throws RdsException {
        BaksetMetaInfo baksetMetaInfo;
        String bakId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        String loginId = mysqlParamSupport.getAndCheckLoginId(params);
        if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            String backupSetType = getParameterValue(params, "BackupSetType", BACKUP_SET_TYPE_DDR);
            Long bakupsetId = CheckUtils.parseLong(bakId, null, null, ErrorCode.BACKUPSET_NOT_FOUND);
            if (BACKUP_SET_TYPE_NORMAL.equals(backupSetType)) {
                String srcRegion = getParameterValue(params, "BackupSetRegion");
                if (srcRegion == null) {
                    throw new RdsException(ErrorCode.NO_BACKUP_SET_REGION);
                }
                // TODO need change to rdsapi
                String data = disasterRestoreApi.getBakhitoryInfoFromOriginRegion(srcRegion, bakupsetId);
                baksetMetaInfo = BakSupport.getAndCheckBaksetMetaInfo(data, params, false, false);
                baksetMetaInfo.setSrcRegion(srcRegion);
            } else if (BACKUP_SET_TYPE_DDR.equals(backupSetType)) {
                // 校验备份集有效性, 并获取备份集信息
                HashMap<String, String> query = new HashMap<>(2);
                query.put("downloadlink", "oss");
                if(withBakTableMeta) {
                    query.put("baktablemeta", "1");
                }
                String binlogName = getParameterValue(params, "BinlogName");
                if (Validator.isNotNull(binlogName)) {
                    query.put("binlogname", binlogName);
                    query.put("binlogposition", getParameterValue(params, "BinlogPosition", "0"));
                    if (hasParameterValue(params, "BinlogHostinsId")) {
                        query.put("binloghostinsid", getParameterValue(params, "BinlogHostinsId"));
                    } else {
                        query.put("binlogrole", getParameterValue(params, "BinlogRole", "master"));
                    }
                }
                String data = disasterRestoreApi.getDdrBakhitoryInfo(loginId, bakupsetId, query);
                baksetMetaInfo = BakSupport.getAndCheckBaksetMetaInfo(data, params, false, true);
            } else {
                throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_SET_TYPE);
            }
            baksetMetaInfo.setId(bakupsetId);
        } else if (RESTORE_TYPE_USER.equals(restoreType)) {
            String backupSetType = getParameterValue(params, "BackupSetType", BACKUP_SET_TYPE_DDR);
            String backsetName = getParameterValue(params, "BakSetName");
            if (backsetName == null) {
                throw new RdsException(ErrorCode.NO_BAKSET_NAME);
            }
            if (BACKUP_SET_TYPE_NORMAL.equals(backupSetType)) {
                String srcRegion = getParameterValue(params, "BackupSetRegion");
                if (srcRegion == null) {
                    throw new RdsException(ErrorCode.NO_BACKUP_SET_REGION);
                }
                String data = rdsApi.getBakhitoryInfoFromOriginRegion(srcRegion, backsetName);
                baksetMetaInfo = BakSupport.getAndCheckBaksetMetaInfoFromRdsApi(data, params);
                baksetMetaInfo.setSrcRegion(srcRegion);
            } else if (BACKUP_SET_TYPE_DDR.equals(backupSetType)) {
                // TODO not support right now
                String data = disasterRestoreApi.getBakhitoryInfoWithoutUrl(loginId, backsetName);
                baksetMetaInfo = BakSupport.getAndCheckBaksetMetaInfo(data, params, true, true);
            } else {
                throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_SET_TYPE);
            }
        } else if (RESTORE_TYPE_TIME.equals(restoreType)) {
            String srcRegion = getParameterValue(params, "SourceRegion");
            if (srcRegion == null) {
                throw new RdsException(ErrorCode.NO_BACKUP_SET_REGION);
            }
            String srcInsName = getParameterValue(params, "SourceDBInstanceName");
            if (srcInsName == null) {
                throw new RdsException(ErrorCode.NO_SOURCE_INSTANCE_NAME);
            }
            // 校验恢复时间点
            Map<String, String> queryMap = new HashMap<>(3);
            queryMap.put("recovertime", getParameterValue(params, ParamConstants.RESTORE_TIME));
            queryMap.put("kindcode", custins.getKindCode().toString());
            if (withBakTableMeta) {
                queryMap.put("baktablemeta", "1");
            }
            String data = disasterRestoreApi.getBaksetMetaInfoByRecoverTime(loginId, srcRegion, srcInsName, queryMap);
            baksetMetaInfo = getAndCheckBaksetMetaInfo(data);
            baksetMetaInfo.setSrcRegion(srcRegion);
            baksetMetaInfo.setInsName(srcInsName);
        } else {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
        }
        mysqlParamSupport.checkBaksetMetaInfo(baksetMetaInfo, params);
        return baksetMetaInfo;
    }

    /**
     * 控制台切换高级备份策略后，后续流程需要调用DBS新API;
     * 返回True 时使用新API，否则保持原逻辑
     */
    public boolean isBackupByDbsNewArch(String requestId, String accessId, String bid, String uid, String sourceRegion, String sourceDBInstanceName) {
        if (StringUtils.isBlank(sourceRegion) || StringUtils.isBlank(sourceDBInstanceName)) {
            return false;
        }

        try {
            DescribeCrossRegionMigrateNewArchStatusParam param = DescribeCrossRegionMigrateNewArchStatusParam.builder()
                    .requestId(requestId)
                    .accessId(accessId)
                    .callerBid(bid)
                    .userId(uid)
                    .instanceName(sourceDBInstanceName)
                    .regionCode(sourceRegion)
                    .build();
            DescribeCrossRegionMigrateNewArchStatusResponse res = dbsGateWayService.describeCrossRegionMigrateNewArchStatus(param);
            return Boolean.parseBoolean(res.getMigrated());
        } catch (Exception e) {
            logger.error("call DescribeCrossRegionMigrateNewArchStatus error, {}", e);
        }
        return false;
    }

    private BaksetMetaInfo doCheckDdrRestoreByDbsNewArch(Map<String, String> params) throws RdsException {
        try {
            DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = doCheckAndUpdateDdrRestoreParamsByDbsNewArch(params);

            BaksetMetaInfo baksetMetaInfo = new BaksetMetaInfo();
            baksetMetaInfo.setBaksetSize(backupSetInfo.getBackupSize());
            return baksetMetaInfo;
        } catch (BaseServiceException e) {
            logger.error("call dbs service failed, {}", JSONObject.toJSONString(e));
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 调用DBS 新API 检查备份恢复参数是否合法
     */
    public DescribeRestoreBackupSetResponse.BackupSetInfo doCheckAndUpdateDdrRestoreParamsByDbsNewArch(Map<String, String> params)
            throws BaseServiceException, RdsException {
        // 校验支持实例系列
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        String dbType = mysqlParamSupport.getAndCheckDBType(params, "MySQL");
        String dbVersion = mysqlParamSupport.getDBVersion(params, dbType);
        String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion, null, null);
        if (insLevel == null ) {
            logger.error("dbVersion {}, classCode {} not found!", dbVersion, classCode);
            throw new RdsException(ErrorCode.UNSUPPORTED_CLASS_CODE);
        }
        if (!insLevel.isStandardLevel()) {
            logger.error("classCode {}, category is {}, only support standard!", classCode, insLevel.getCategory());
            throw new RdsException(ErrorCode.UNSUPPORTED_CLASS_CODE);
        }

        // 校验恢复参数
        String restoreType = mysqlParamSupport.getAndCheckRestoreType(params);
        String bid = mysqlParamSupport.getAndCheckBID(params);
        String uid = mysqlParamSupport.getAndCheckUID(params);
        String sourceDBInstanceName = mysqlParamSupport.getSourceDBInstanceName(params);
        String sourceRegionId = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_SOURCE_REGION);

        if (StringUtils.isEmpty(sourceDBInstanceName)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "param sourceDBInstanceName is required");
        }
        if (StringUtils.isEmpty(sourceRegionId)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "param sourceRegion is required");
        }
        String backupSetRegionId = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION);
        if (StringUtils.isEmpty(backupSetRegionId)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "param backupSetRegion is required");
        }

        DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo;
        if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            // 按备份集恢复，校验备份集有效性
            backupSetInfo = checkAndUpdateBackupSetParamsByDbsNewArch(bid, uid, sourceDBInstanceName, sourceRegionId, params);
        } else if (RESTORE_TYPE_TIME.equals(restoreType)) {
            // 按时间点恢复，先校验备份集，再校验binlog
            backupSetInfo = checkAndUpdateRestoreByTimeParamsByDbsNewArch(bid, uid, sourceDBInstanceName, sourceRegionId, params);
        } else {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
        }

        logger.info("updated restore params: {}", JSONObject.toJSONString(params));
        return backupSetInfo;
    }


    /**
     * 调用DBS 新API 检查按备份集恢复参数是否合法
     */
    public DescribeRestoreBackupSetResponse.BackupSetInfo checkAndUpdateBackupSetParamsByDbsNewArch(String bid, String uid, String sourceDBInstanceName, String sourceRegionId, Map<String, String> params)
            throws BaseServiceException, RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID);
        String backupSetId = mysqlParamSupport.getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        String backupSetRegionId = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION);

        if (StringUtils.isEmpty(backupSetId)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "param backupSetId is required");
        }

        DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(
                DescribeRestoreBackupSetParam.builder()
                        .requestId(requestId)
                        .accessId(accessId)
                        .callerBid(bid)
                        .userId(uid)
                        .instanceName(sourceDBInstanceName)
                        .instanceRegion(sourceRegionId)
                        .sceneType(PodDefaultConstants.BACK_SCENE_TYPE_REPLICATION)
                        .backupId(backupSetId)
                        .regionCode(backupSetRegionId)
                        .build()
        );
        if (!restoreBackupResponse.getRestoreTimeValid() || restoreBackupResponse.getBackupSetInfo() == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }

        DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = restoreBackupResponse.getBackupSetInfo();
        updateBackupSetInfoToParams(backupSetInfo, params);
        return backupSetInfo;
    }


    /**
     * 调用DBS 新API 检查按时间点恢复参数是否合法
     */
    public DescribeRestoreBackupSetResponse.BackupSetInfo checkAndUpdateRestoreByTimeParamsByDbsNewArch(String bid, String uid, String sourceDBInstanceName, String sourceRegionId, Map<String, String> params)
            throws BaseServiceException, RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID);
        String backupSetId = mysqlParamSupport.getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        String backupSetRegionId = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION);

        String restoreTime = mysqlParamSupport.getParameterValue(params, ParamConstants.RESTORE_TIME);
        Long restoreTimePoint;
        try {
            restoreTimePoint = podDateTimeUtils.getUTCDateByDateStr(restoreTime).getTime()/1000;
        } catch (Exception e) {
            logger.error("requestId: {}, parse restoreTime failed, value: {}, msg: {}", requestId, restoreTime, e.getMessage(), e);
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }

        // 按时间点恢复，先查找备份集
        var describeBackupSetParam = DescribeRestoreBackupSetParam.builder()
                .requestId(requestId)
                .accessId(accessId)
                .callerBid(bid)
                .userId(uid)
                .instanceName(sourceDBInstanceName)
                .instanceRegion(sourceRegionId)
                .sceneType(PodDefaultConstants.BACK_SCENE_TYPE_REPLICATION)
                .regionCode(backupSetRegionId);

        // 如果用户输入了backupSetId，这里直接指定，否则接口可能选择到最新的备份集
        if (StringUtils.isNotEmpty(backupSetId)) {
            describeBackupSetParam.backupId(backupSetId);
        } else {
            describeBackupSetParam.restoreTimePoint(restoreTimePoint);
        }

        DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(describeBackupSetParam.build());
        if (!restoreBackupResponse.getRestoreTimeValid() || restoreBackupResponse.getBackupSetInfo() == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }

        DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = restoreBackupResponse.getBackupSetInfo();
        updateBackupSetInfoToParams(backupSetInfo, params);

        // 再检查检查日志文件
        String consistentTime = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_CONSISTENT_TIME);
        DescribeRestoreArchiveLogResponse restoreLogResponse = dbsGateWayService.describeRestoreArchiveLog(
                DescribeRestoreArchiveLogParam.builder()
                        .requestId(requestId)
                        .accessId(accessId)
                        .callerBid(bid)
                        .userId(uid)
                        .instanceName(sourceDBInstanceName)
                        .instanceRegion(sourceRegionId)
                        .sceneType(PodDefaultConstants.BACK_SCENE_TYPE_REPLICATION)
                        .regionCode(backupSetRegionId)
                        .restoreTimePoint(restoreTimePoint)
                        .consistentTime(Long.parseLong(consistentTime))
                        .build());
        if (!restoreLogResponse.getRestoreTimeValid()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }
        params.put(PodDefaultConstants.PARAM_RESTORE_TIME_POINT.toLowerCase(), restoreTimePoint.toString());

        return backupSetInfo;
    }

    /**
     * 设置备份集信息到请求参数中，创建实例时使用
     */
    public void updateBackupSetInfoToParams(DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        logger.info("requestId:{}, backup set info: {}", requestId, JSONObject.toJSONString(backupSetInfo));

        try {
            params.put(MySQLParamConstants.DISASTER_RESTORE_BY_DBS_SERVICE.toLowerCase(), "true");

            String backupSetId = backupSetInfo.getBackupId();
            params.put(ParamConstants.BACKUP_SET_ID.toLowerCase(), backupSetId);

            Long consistentTime = backupSetInfo.getConsistentTime();
            params.put(PodDefaultConstants.PARAM_CONSISTENT_TIME.toLowerCase(), consistentTime.toString());

//            // 设置实例内核版本、核心参数，保证实例可拉起
//            String originMinorVersion = backupSetInfo.getExtraInfo().getSlaveStatus().getMinorVersion();
//            params.put(PodDefaultConstants.PARAM_TARGET_MINOR_VERSION.toLowerCase(), originMinorVersion);
        } catch (Exception e) {
            logger.error("requestId: {}, set backup set info to params failed, msg: {}", e.getMessage(), e);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
    }
}
