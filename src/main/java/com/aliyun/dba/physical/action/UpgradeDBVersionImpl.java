package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAction;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_ENTERPRISE;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalUpgradeDBVersionImpl")
public class UpgradeDBVersionImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeDBVersionImpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    private DbossApi dbossApi;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected CustinsIDao custinsIDao;
    @Autowired
    protected MinorVersionService minorVersionService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;


    private static final String DB_VERSION_MYSQL_XDB_57 = "xdb_5.7";
    private static final String DB_VERSION_MYSQL_XDB_80 = "xdb_8.0";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        /*
         * mysql 大版本与小版本升级
         * */
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            custins = mysqlParameterHelper.getAndCheckCustInstance();
            if (custins.isReadAndWriteLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (custins.isShare()) {
                //不是专享实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                //实例状态错误
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //FIXME：指定小版本升级，瑶池传递的参数是TargetMinorVersion，杜康传递的参数是MinorVersion，后续需要杜康对齐
            //控制台来源格式为xcluster_20200229,xcluster80_20200229,rds_20200229, 杜康来源格式为mysql56_20200229
            //MVM_VALID_VERSIONS_MYSQL_5.6的格式为mysql_20200229或者mysql56_20200229
            String yaochiMinorVersion = getParameterValue(actionParams, "TargetMinorVersion");
            String dukangMinorVersion = getParameterValue(actionParams, "MinorVersion");
            String targetMinorVersion = yaochiMinorVersion != null ? yaochiMinorVersion : dukangMinorVersion != null ? dukangMinorVersion : null;


            //大版本参数，不是小版本
            String majorVersion = getParameterValue(actionParams, ParamConstants.MAJOR_VERSION);
            String accessId = getParameterValue(actionParams, ParamConstants.ACCESSID);
            Integer custinsId = custins.getId();
            String engineVersion = custins.getDbVersion();
            // 设置切换时间

            //Date switchTime = checkService.getAndCheckSwitchTime(getParameterValue(actionParams, ParamConstants.SWITCH_TIME));
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(actionParams);
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(actionParams, utcDate, true);
            Map<String, Object> taskQueueParam = new HashMap<String, Object>(3);


            if (majorVersion != null) {
                // 大版本升级方式
                if (custins.isMysql51() || custins.isMysql55() || custins.isMysql57()) {
                    //mysql5.1  5.5 大版本采用迁移方式升级，改升级后方式主要为本地升级
                    // 5.7 not support now
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
                }

                List<String> params = new ArrayList<String>(1);
                List<CustinsParamDO> resultList = new ArrayList<CustinsParamDO>();
                params.add(CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_TDE);
                resultList = custinsParamService.getCustinsParams(custins.getId(), params);
                String tdeStatus = CustinsParamSupport.CUSTINS_PARAM_VALUE_TDE_ENABLED;
                // 对于5.7实例，TDEStatus not support
                if (!resultList.isEmpty() && resultList.get(0).getValue().equals(tdeStatus)){
                    return createErrorResponse(ErrorCode.TDESTATUS_ALREADY_CONFIGED);
                }

                if (engineVersion.equals(majorVersion)){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
                }

                if (custins.isReadOrBackup()){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }

                //db check
                Integer engine =0;
                //mysql 5.6 upgrade check myisam
                /*
                 * engine
                 * 0 myisam
                 * 1 innodb
                 * 2 memory
                 * 3 tokudb
                 * more...  in dboss
                 * */
                Map<String, Object> engineList = dbossApi.queryEngineCount(custinsId,engine);
                Integer engineCount = (Integer)(engineList.get("myisam"));

                if (engineCount > 0) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE_MYISAM);
                }

                //account check
                String accountName = "aliyun_root";
                List<Map<String, Object>> accountList = dbossApi.queryAccounts(custins.getId(), accountName, "", 0, 1);
                if (!CollectionUtils.isEmpty(accountList)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ACCOUNT_ALIYUNROOT);

                }

                //level check
                boolean levelSupport = custinsService.checkInstanceLevelSupportUpgradeMajorVersion(custinsId);
                if (!levelSupport){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
                }


                List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(
                        custins.getId(), true);
                List<CustInstanceDO> inValidReadInslist = new ArrayList<>();
                if (readCustinsList != null && readCustinsList.size() > 0) {
                    // read ins are active or deleted
                    for (CustInstanceDO readins : readCustinsList) {
                        if (!CustinsSupport.CUSTINS_STATUS_DELETING.equals(readins.getStatus()) &&
                                !CustinsSupport.CUSTINS_STATUS_ACTIVE.equals(readins.getStatus())) {
                            inValidReadInslist.add(readins);
                        }
                    }
                    if (!inValidReadInslist.isEmpty()) {
                        return createErrorResponse(ErrorCode.UNSUPPORTED_READ_OR_BAKREAD_STATE);
                    }

                    // read ins level are all supported
                    for (CustInstanceDO readins : readCustinsList){
                        boolean readLevelSupport = custinsService.checkInstanceLevelSupportUpgradeMajorVersion(readins.getId());
                        if (!readLevelSupport){
                            ErrorCode errorCode = ErrorCode.UNSUPPORTED_READ_INSTANCE_LEVEL.resetDesc();
                            errorCode.setDesc(String.format(errorCode.getDesc(), readins.getId().toString()));
                            return createErrorResponse(errorCode);
                        }
                        resultList = custinsParamService.getCustinsParams(readins.getId(), params);
                        if (!resultList.isEmpty() && resultList.get(0).getValue().equals(tdeStatus)){
                            return createErrorResponse(ErrorCode.TDESTATUS_ALREADY_CONFIGED);
                        }
                    }
                }

                Map<String, Object> effMap = custinsService.getEffectiveTimeMapTimeZoneSafe(switchMode, utcDate);
                taskQueueParam.put(CustinsSupport.SWITCH_KEY, effMap);
                taskQueueParam.put(ParamConstants.MAJOR_VERSION,majorVersion);
                Integer taskId = instanceService
                        .upgradeMajorVersionTask(getAction(actionParams), custins, taskQueueParam, CustinsParamSupport.getOperatorId(actionParams));
                taskService.updateTaskPenginePolicy(taskId, CustinsParamSupport.getPenginePolicyID(actionParams));

                Map<String, Object> data = new HashMap<String, Object>(4);
                data.put("DBInstanceID", custins.getId());
                data.put("DBInstanceName", custins.getInsName());
                data.put("TargetMajorVersion",majorVersion);
                data.put("TaskId", taskId);
                return data;

            }
            else {

                if (custins.isMysql51()) {
                    //mysql5.1 不支持小版本升级
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
                }

                InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
                if (mysqlParameterHelper.isMysqlXDBByLevel(instanceLevelDO)) {
                    if(custins.isMysql57()){
                        engineVersion = DB_VERSION_MYSQL_XDB_57;
                    }
                    else if(custins.isMysql80()){
                        engineVersion = DB_VERSION_MYSQL_XDB_80;
                    }
                }

                CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custinsId, ParamConstants.AUTO_UPGRADE_MINOR_VERSION);
                String upgradeMinorVersionSet = custinsParamDO != null ? custinsParamDO.getValue(): "Manual";

                //版本不合法则抛出异常
                String category = minorVersionServiceHelper.getMinorVersionCategory(custins);
                String minorVersionTag = minorVersionServiceHelper.getAndCheckMinorVersionTag(custins);
                CustinsParamDO minorVersionInfo = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_KEY);
                if(minorVersionInfo == null){
                    //实例缺少小版本信息
                    throw new RdsException(ErrorCode.CUSTINS_MINOR_VERSION_ATTR_MISSING);
                }
                String nowReleaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(minorVersionInfo.getValue());
                String targetReleaseDate = null;

                if(targetMinorVersion != null){
                    targetReleaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersion);
                    if(Integer.valueOf(targetReleaseDate) <= Integer.valueOf(nowReleaseDate)){
                        //指定版本小于当前实例版本
                        throw new RdsException(ErrorCode.INVALID_MINOR_VERSION_LOWER_THAN_CUSTINS);
                    }
                    //xdb私有协议与非私有协议需要区分开，需要判断实例是否是polarx_hatp，需要加入tag
                    List<MinorVersionReleaseDO> minorVersionReleaseDOS = minorVersionService.querySpecifyMinorVersionListByCondition(custins.getDbType(), custins.getDbVersion(), custins.getKindCode(), category, minorVersionTag, targetReleaseDate);
                    if(minorVersionReleaseDOS == null || minorVersionReleaseDOS.size() == 0){
                        //找不到指定版本
                        throw new RdsException(ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND);
                    }
                }
                //如果都没有小版本信息，则查询大于等于当前实例版本的最新hotfix版本做升级
                else{
                    List<MinorVersionReleaseDO> minorVersionReleaseDOS = minorVersionService.queryMinorVersionListByCondition(custins.getDbType(), custins.getDbVersion(), custins.getKindCode(), category, minorVersionTag, nowReleaseDate);
                    if (mysqlParamSupport.isMysqlXdbByCustins(custins)) {
                        // xcluster 于 20230505 不再演进rds控制台的三节点版本。后续的版本号用于 PolarDB-X 标准版。
                        minorVersionReleaseDOS = minorVersionReleaseDOS.stream()
                                .filter(minorVersion -> Long.parseLong(minorVersion.getReleaseDate()) < 20230505)
                                .collect(Collectors.toList());
                    }
                    List<MinorVersionReleaseDO> filterMinorVersionReleaseDOS = minorVersionReleaseDOS.stream()
                            .filter(v -> minorVersionServiceHelper.isAvailableMinorVersion(v))
                            .collect(Collectors.toList());
                    //当前实例已经是最新版本
                    if (CollectionUtils.isEmpty(filterMinorVersionReleaseDOS)) {
                        throw new RdsException(ErrorCode.INVALID_MINOR_VERSION_ALREADY_LATEST);
                    }
                    targetReleaseDate = filterMinorVersionReleaseDOS.get(0).getReleaseDate();
                    logger.warn("auto choose minor_version=" + targetReleaseDate);
                }

                if (!mysqlEngineCheckService.checkCanUpgradeMinorVersionWithMaxScale(custins, targetReleaseDate)) {
                    return ResponseSupport.createErrorResponse(new Object[]{ResultCode.CODE_UNSUPPORTED, "MaxscaleMinorVersionNotSupport", "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first."});
                }

                //需要从rds_20200229转为mysqlxx_20200229
                targetMinorVersion = generateMinorVersionForTask(custins.getDbVersion(), category, targetReleaseDate);

                taskQueueParam.put("minor_version", targetMinorVersion);
                Map<String, Object> effMap = custinsService.getEffectiveTimeMapTimeZoneSafe(switchMode, utcDate);


                taskQueueParam.put("pt_switch_time_mode", getParameterValue(actionParams, ParamConstants.SWITCH_TIME_MODE));
                taskQueueParam.put("pt_switch_time", getParameterValue(actionParams, ParamConstants.SWITCH_TIME));


                taskQueueParam.put(CustinsSupport.SWITCH_KEY, effMap);
                Integer taskId = instanceService
                        .upgradeMinorVersionTask(getAction(actionParams), custins, taskQueueParam, CustinsParamSupport.getOperatorId(actionParams));
                taskService.updateTaskPenginePolicy(taskId, CustinsParamSupport.getPenginePolicyID(actionParams));

                Map<String, Object> data = new HashMap<String, Object>(4);
                data.put("DBInstanceID", custins.getId());
                data.put("DBInstanceName", custins.getInsName());
                data.put("TargetMinorVersion", generateMinorVersionForShow(engineVersion, targetMinorVersion));

                //对外暴露版本格式为rds_20xxxxxx
                data.put("TaskId", taskId);
                return data;
            }
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }



    /**
     * targetMinorVersion: rds_202020229,xcluster_20200229,xcluster80_20200229
     * */
    private String generateMinorVersionForTask(String dbVersion, String category, String releaseDate){

        String xclusterPrefix = "xcluster_";
        String xcluster80Prefix = "xcluster80_";
        String mysqlPrefix = "mysql";

        if(CATEGORY_ENTERPRISE.equalsIgnoreCase(category) && dbVersion.equalsIgnoreCase(DB_VERSION_MYSQL_57)){
            return xclusterPrefix + releaseDate;
        }
        else if(CATEGORY_ENTERPRISE.equalsIgnoreCase(category) && dbVersion.equalsIgnoreCase(DB_VERSION_MYSQL_80)){
            return xcluster80Prefix + releaseDate;
        }
        else{
            return mysqlPrefix + dbVersion.replace(".", "") + "_" + releaseDate;
        }
    }

    /**
     * rds_20200229转换为mysqlxx_20200229, xcluster_20200229, xcluster80_20200229
     * */
    private String generateMinorVersionForShow(String engineVersion, String targetMinorVersion){

        String prefix = DB_VERSION_MYSQL_XDB_57.equals(engineVersion) ? "xcluster_"
            : DB_VERSION_MYSQL_XDB_80.equals(engineVersion) ? "xcluster80_" : "rds_";
        String releaseDate = targetMinorVersion.split("_")[1];
        return prefix + releaseDate;
    }

    /**
     * 待添加到二方包
     * */
    private boolean checkTargetMinorVersionWithValidVersions(Integer custinsId, String dbType,
                                                            String dbVersion,
                                                            Integer kindCode,
                                                             String category,
                                                            String targetMinorVersion)

        throws RdsException {

        String[] versions = targetMinorVersion.split("_");
        if (versions.length != 2) {
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }

        String prefix = versions[0];

        List<String> prefixList = new ArrayList<>();
        prefixList.add("rds");
        prefixList.add("xcluster");
        prefixList.add("xcluster80");
        prefixList.add("mysql");
        prefixList.add("mysql56");
        prefixList.add("mysql57");
        prefixList.add("mysql80");

        if(!prefixList.contains(prefix)){
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }
        //xcluster目前没有valid 列表可以检查，所以先抛出异常, 暂时不支持指定版本升级
        if(CATEGORY_ENTERPRISE.equalsIgnoreCase(category)){
            return false;
        }
        else{
            String resKey = "MVM_VALID_VERSIONS_MYSQL_"+dbVersion;
            List<String> resMinorVersions = resourceService.getResourceRealValueList(resKey);
            if(resMinorVersions == null || resMinorVersions.size() == 0){
                return false;
            }
            try{
                JSONObject jsonObject = JSON.parseObject(resMinorVersions.get(0));
                JSONArray kindCodeVersionList = jsonObject.getJSONArray(String.valueOf(kindCode));
                if(!kindCodeVersionList.contains(targetMinorVersion)){
                    return false;
                }
            }
            catch(Exception ex){
                logger.error("parse valid version json error", ex);
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
            }
            //检查当前实例版本，如果当前实例版本大于指定版本，也拦截
            CustinsParamDO custinsVersionDO = custinsParamService.getCustinsParam(custinsId, "minor_version");
            if(custinsVersionDO == null || custinsVersionDO.getValue() == null){
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
            }
            Integer releaseDate = Integer.valueOf(custinsVersionDO.getValue().split("_")[1]);
            Integer targetDate = Integer.valueOf(targetMinorVersion.split("_")[1]);
            //目标日期 小于 当前版本日期， hotfix支持等于
            if(targetDate < releaseDate){
                logger.warn("specify targetDate:"+targetDate+" less than releaseDate:"+releaseDate);
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
            }
        }
        return true;
    }

    private String getFinalMinorVersion(String resKey, CustInstanceDO custins) {
        List<String> resMinorVersions = resourceService.getResourceRealValueList(resKey);
        if (resMinorVersions.size() > 0) {
            JSONObject minorVersionMap = JSON.parseObject(resMinorVersions.get(0));
            return minorVersionMap.getString(custins.getKindCode().toString());
        }
        return null;
    }

    public String getCustInstancelastMinorVersionByCustinsId(Integer custinsId, String engineVersion) {

        List<Map<String, Object>> versions = custinsIDao.getCustInstanceLastMinorVersionByCustinsId(custinsId);
        List<Set<String>> vl = new ArrayList<Set<String>>();

        for (Map<String, Object> version : versions) {
            String versionList = (String) version.get("version");
            JSONArray ja = JSONArray.parseArray(versionList);
            Map<String, Set<String>> vers = split2VerList(ja);

            if(engineVersion.equals(DB_VERSION_MYSQL_56)){
                vl.add(vers.get(DB_VERSION_MYSQL_56));
            }
            else if(engineVersion.equals(DB_VERSION_MYSQL_57)){
                vl.add(vers.get(DB_VERSION_MYSQL_57));
            }
            else if(engineVersion.equals(DB_VERSION_MYSQL_80)){
                vl.add(vers.get(DB_VERSION_MYSQL_80));
            }
            else if(engineVersion.equals(DB_VERSION_MYSQL_XDB_57)){
                vl.add(vers.get(DB_VERSION_MYSQL_XDB_57));
            }
            else if(engineVersion.equals(DB_VERSION_MYSQL_XDB_80)){
                vl.add(vers.get(DB_VERSION_MYSQL_XDB_80));
            }
            else{
                vl.add(vers.get(DB_VERSION_MYSQL_55));
            }
        }
        try {
            Set<String> baseversion = vl.get(0);
            for (Set<String> ver : vl) {
                baseversion.retainAll(ver);
            }
            List<String> verlist2 = new ArrayList<String>();
            verlist2.addAll(baseversion);
            Collections.sort(verlist2, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    return Arrays.asList(o1.split("_")).get(1).toString().compareTo(Arrays.asList(o2.split("_")).get(1).toString());
                }
            });
            if (verlist2.isEmpty()) {
                return "";
            } else {
                return verlist2.get(verlist2.size() - 1).replaceAll("\"", "").replace(" ", "");
            }
        } catch (Exception e) {
            logger.warn("Can't find user now use minor version, will use null instead!");
            return "";
        }

    }


    public static Map<String, Set<String>> split2VerList(JSONArray verlist) {

        Set<String> mysql55mvl = new HashSet<>();
        Set<String> mysql56mvl = new HashSet<>();
        Set<String> mysql57vl = new HashSet<>();
        Set<String> mysql80mvl = new HashSet<>();
        Set<String> mysql57xdbmvl = new HashSet<>();
        Set<String> mysql80xdbmvl = new HashSet<>();

        for (Object ob : verlist) {
            String str = ob.toString();
            if(str.contains("mysql55")){
                mysql55mvl.add(str);
            }
            else if(str.contains("mysql56")){
                mysql56mvl.add(str);
            }
            else if(str.contains("mysql57")){
                mysql57vl.add(str);
            }
            else if(str.contains("mysql80")){
                mysql80mvl.add(str);
            }
            else if(str.contains("xcluster80")){
                mysql80xdbmvl.add(str);
            }
            else if(str.contains("xcluster")){
                mysql57xdbmvl.add(str);
            }
        }

        Map<String, Set<String>> vers = new HashMap<String, Set<String>>();

        vers.put(DB_VERSION_MYSQL_55, mysql55mvl);
        vers.put(DB_VERSION_MYSQL_56, mysql56mvl);
        vers.put(DB_VERSION_MYSQL_57, mysql57vl);
        vers.put(DB_VERSION_MYSQL_80, mysql80mvl);
        vers.put(DB_VERSION_MYSQL_XDB_57, mysql57xdbmvl);
        vers.put(DB_VERSION_MYSQL_XDB_80, mysql80xdbmvl);

        return vers;
    }

    private Boolean isSupportGrayMinorVersionAccess(String accessId) {
        return ParamConstants.YAOCHI_ACCESS.equalsIgnoreCase(accessId) || ParamConstants.DUKANG_ACCESS.equalsIgnoreCase(accessId);
    }
}
