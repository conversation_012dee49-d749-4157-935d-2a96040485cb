package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.instance.entity.MycnfChangeLog;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.EncdbService;
import com.aliyun.dba.poddefault.action.service.TdeKmsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.user.service.UserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static com.aliyun.dba.task.support.TaskSupport.TASK_FLUSH_PARAMS;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

import java.io.IOException;
import java.util.*;


@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalModifyDBInstanceCLSImpl")
public class ModifyDBInstanceCLSImpl implements IAction {

    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    private CustinsServiceImpl custinsService;
    @Autowired
    private UserServiceImpl userService;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private EncdbService encdbService;
    @Autowired
    private TdeKmsService tdeKmsService;
    @Autowired
    private KmsApi kmsApi;
    @Autowired
    private TaskService taskService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private MycnfService mycnfService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO primCustins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = RequestSession.getRequestId();

        try {
            primCustins = mysqlParamSupport.getAndCheckCustInstance(params);
            if (primCustins.getInsType() != CustInsType.CUST_INS_TYPE_PRIMARY.getValue()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            if (StringUtils.isBlank(regionId)) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            // 直接复用tde的region检查
            if (!tdeKmsService.checkTdeSupported(requestId, regionId)) {
                log.error("CLS not supported in this region {}", regionId);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            List<CustInstanceDO> allCustins = custinsService.getReadCustInstanceListByPrimaryCustinsId(primCustins.getId(), false);
            if (allCustins == null) {allCustins = Collections.emptyList();}
            allCustins.add(primCustins);
            // 检查主&只读实例是否都可用
            for (CustInstanceDO c : allCustins) {
                encdbService.checkCustinsStatusAvailable(c);
            }
            // 算法 & 黑白名单模式配置可以支持所有实例，通过dboss设置不需要区分具体产品形态。
            try {
                String clsEncAlgo = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO);
                if (!StringUtils.isBlank(clsEncAlgo)) {
                    encdbService.setEncdbGlobalAlgo(primCustins, clsEncAlgo);
                }
                String clsWhitListMode = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_CLS_WHITELIST_MODE);
                if (!StringUtils.isBlank(clsWhitListMode)) {
                    encdbService.setWhiteListMode(primCustins, clsWhitListMode);
                }
            } catch (RdsException re) {
                log.error(requestId + " RdsException: ", re);
                return ResponseSupport.createErrorResponse(re.getErrorCode());
            } catch (IOException ioe) {
                log.error(requestId + " IOException: ", ioe);
                return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            }
            // 不支持设置KMS密钥。
            String keyMode = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_CLS_KEY_MODE);
            if (PodDefaultConstants.CLS_MODE_KMS_KEY.equals(keyMode)) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAM);
            }
            String clsStatus = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_CLS_STATUS);
            Map<String, Object> data = new HashMap<>();
            List<Integer> taskIds = new ArrayList<>();
            for (CustInstanceDO c : allCustins) {
                MycnfChangeLog oldLog = instanceService.getMycnfCustinsHistoryByName(c.getId(), "loose_encdb");
                String oldValue = oldLog != null ? oldLog.getNewValue() : "OFF";
                String newValue = null;
                if ("1".equals(clsStatus)) {
                    if (StringUtils.equalsIgnoreCase(oldValue, "OFF")) {
                        newValue = "ON";
                    }
                    custinsParamService.setCustinsParam(c.getId(), PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_CLIENT_KEY);
                } else {
                    // 禁用 loose_encdb
                    if (StringUtils.equalsIgnoreCase(oldValue, "ON")) {
                        newValue = "OFF";
                    }
                    custinsParamService.setCustinsParam(c.getId(), PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_NONE);
                }
                if (newValue != null) {
                    MycnfChangeLog newLog = new MycnfChangeLog(c.getId(), "loose_encdb", oldValue, newValue, new Date());
                    newLog.setCreator(99999);
                    newLog.setModifier(99999);
                    mycnfService.createMycnfChangeLog(newLog);
                    TaskQueueDO task = new TaskQueueDO(
                            mysqlParameterHelper.getAction(), mysqlParameterHelper.getOperatorId(), c.getId(), TASK_TYPE_CUSTINS, TASK_FLUSH_PARAMS
                    );
                    taskService.createTaskQueue(task);
                    int taskId = task.getId();
                    taskService.updateTaskPenginePolicy(taskId, mysqlParameterHelper.getPenginePolicyID());
                    taskIds.add(taskId);
                }
            }
            if (!taskIds.isEmpty()) data.put("TaskIDs", taskIds);
            data.put("DBInstanceID", primCustins.getId());
            data.put("DBInstanceName", primCustins.getInsName());
            return data;
        } catch (RdsException re) {
            log.error(requestId + " RdsException: ", re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            log.error(requestId + " Exception: ", e);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
