package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.service.MysqlAccountService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.AccountsDTO;
import com.aliyun.dba.dbs.dataobject.AccountsQuery;
import com.aliyun.dba.dbs.entity.AccountDbRel;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.ecs.dataobject.RdsRegionDO;
import com.aliyun.dba.resource.dataobject.RegionAVZonDO;
import com.aliyun.dba.resource.dataobject.RegionAVZoneQuery;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.dbs.support.AccountPriviledgeType.*;
import static com.aliyun.dba.dbs.support.AccountPriviledgeType.PRIVILEDGE_NORMAL;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeAccountListImpl")
public class DescribeAccountListImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeAccountListImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected AccountService accountService;

    @Autowired
    protected DbossApi dbossApi;

    @Autowired
    protected MycnfService mycnfService;

    @Autowired
    protected MysqlAccountService mysqlAccountService;

    public Map<String,Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            CustInstanceDO custins1 = mysqlParamSupport.getAndCheckCustInstance(params);
            if (dbossApi.isHandleByDBoss(custins1)) {
                return mysqlAccountService.describeAccountListByDboss(custins1, params);
            } else {
                return mysqlAccountService.describeAccountList(params);
            }
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.warn(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
