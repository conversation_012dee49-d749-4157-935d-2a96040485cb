package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.HostinsParamService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.support.CpuShareHelper;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRefreshAllocateResourceImpl")
public class RefreshAllocateResourceImpl implements IAction {

    @Resource
    protected MysqlParamSupport mysqlParamSupport;

    @Resource
    protected InstanceService instanceService;

    @Resource
    protected CustinsParamService custinsParamService;

    @Resource
    protected HostService hostService;

    @Resource
    protected ResourceService resourceService;

    @Resource
    protected HostinsParamService hostinsParamService;

    @Resource
    protected MysqlParamSupport paramSupport;

    @Resource
    protected TaskService taskService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        Object taskId = null;
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            var instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId().intValue());
            boolean isModifyCgroup = Boolean.parseBoolean(paramSupport.getParameterValue(params, "IsModifyCgroup", false));
            Map<String, Object> checkResult = preCheckForRefresh(custins, instanceLevel, isModifyCgroup);
            if (Objects.nonNull(checkResult)) {
                return checkResult;
            }
            boolean isModifyMemory = Boolean.parseBoolean(paramSupport.getParameterValue(params, "IsModifyMemory", false));

            var instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            var cpuComputeMeta = initCpuComputeMeta(custins, instanceList);


            if (!isModifyCgroup) {  // only update ins meta
                for (var instance : instanceList) {
                    var ip = instance.getIp();
                    instanceLevel = instanceService.getInstanceLevelByLevelId(instance.getLevelId().intValue());
                    if (instanceLevel.getIsolateHost() == 2) {
                        continue;
                    }
                    hostinsParamService.setParam(instance.getId().longValue(), CpuShareHelper.ALLOCATE_CPU_CORES,
                            getCpuGroupRules(ip, instanceLevel.getCpuCores(), cpuComputeMeta).toString());

                    if (isModifyMemory && Objects.nonNull(instanceLevel.getLimitMemory())) {
                        hostinsParamService.setParam(instance.getId().longValue(), CpuShareHelper.ALLOCATE_MEM_SIZE_MB,
                                instanceLevel.getLimitMemory().toString());
                    }

                }
            } else {
                String taskKey = "refresh_ins_resource";
                Map<String, Object> taskParams = new HashMap<String, Object>(3);
                taskParams.put("custins_id", custins.getId());
                taskParams.put("custins_name", custins.getInsName());
                taskParams.put("is_modify_memory", isModifyMemory);
                var taskQueue = new TaskQueueDO(mysqlParamSupport.getAction(params), mysqlParamSupport.getOperatorId(params), custins.getId(), TaskSupport.TASK_TYPE_CUSTINS, taskKey);
                taskService.createTaskQueue(taskQueue);
                taskService.updateTaskPenginePolicy(taskQueue.getId(), mysqlParamSupport.getPenginePolicyID(params));
                taskId = taskQueue.getId();
            }
            Map<String, Object> result = new HashMap<>();
            result.put("DBInstanceName", custins.getInsName());
            result.put("TaskId", taskId);
            result.put("RequestId", requestId);
            return result;
        } catch (RdsException ex) {
            log.error("Physical RefreshAllocateResource failed, ", ex);
            return createErrorResponse(ex.getErrorCode());
        } catch (Exception ex) {
            log.error("RefreshAllocateResource Internal, ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, Object> preCheckForRefresh(CustInstanceDO custins, InstanceLevelDO instanceLevel, Boolean isModifyCgroup) {
        if (!custins.isActive()) {
            // 实例状态错误
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        if (isModifyCgroup) {
            if (custins.isShare()) {
                // 不是专享实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (custins.isReadAndWriteLock()) {
                // 实例锁状态
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
        }
        var custinsParam = custinsParamService.getCustinsParam(custins.getId(), "singleTenant");
        if (Objects.nonNull(custinsParam) && "true".equalsIgnoreCase(custinsParam.getValue()) || instanceLevel.getIsolateHost() != 0) {
            // 单租户
            return createErrorResponse(ErrorCode.ALREADY_IN_SINGLE_TENANT_MODE);
        }
        return null;
    }

    private BigDecimal getCpuGroupRules(String ip, Integer cores, CpuComputeMeta cpuComputeMeta) {
        String DEFAULT_KEY = "DEFAULT";
        Double DEFAULT_VALUE = 1.25;
        if (cpuComputeMeta.getFlexibleResourceCpu() == 1) {
            log.info("use flexible_resource_cpu, skip compute.");
            return new BigDecimal(cores);
        }
        if (cpuComputeMeta.getCpuCgroupMapping().size() <= 0 || cpuComputeMeta.getHostLevelMapping().size() <= 0) {
            return new BigDecimal(DEFAULT_VALUE.intValue() * cores);
        }

        var hostLevelMapping = cpuComputeMeta.getHostLevelMapping();
        var cpuCgroupMapping = cpuComputeMeta.getCpuCgroupMapping();

        String hostName = hostLevelMapping.getOrDefault(ip, DEFAULT_KEY);
        var config = cpuCgroupMapping.getOrDefault(hostName, cpuCgroupMapping.get(DEFAULT_KEY));
        config = Objects.isNull(config) ? new HashMap<>() : config;
        log.info("cores: {}, config: {}, hostName: {}", cores, JSON.toJSONString(config), hostName);
        BigDecimal cgroupCoefficient = new BigDecimal(config.getOrDefault(cores.toString(), config.getOrDefault(DEFAULT_KEY, DEFAULT_VALUE)).toString());

        return cgroupCoefficient.multiply(new BigDecimal(cores));
    }

    private CpuComputeMeta initCpuComputeMeta(CustInstanceDO custins, List<InstanceDO> instanceList) {
        var cpuComputeMeta = new CpuComputeMeta();

        // init flexible resource cpu
        var flexibleResourceCpu = custinsParamService.getCustinsParam(custins.getId(), MySQLParamConstants.FLEXIBLE_RESOURCE_CPU);
        if (Objects.nonNull(flexibleResourceCpu)) {
            Integer value = flexibleResourceCpu.getValue().equals("1") ? 1 : 0;
            cpuComputeMeta.setFlexibleResourceCpu(value);
        }

        // init host Level Mapping
        var hostLevelMapping = cpuComputeMeta.getHostLevelMapping();
        for (var instance : instanceList) {
            var hostLevel = hostService.getHostLevelByHostId(instance.getHostId());
            if (Objects.nonNull(hostLevel) && Objects.nonNull(hostLevel.getName())) {
                hostLevelMapping.put(instance.getIp(), hostLevel.getName());
            }
        }
        if (hostLevelMapping.isEmpty()) {
            return cpuComputeMeta;
        }

        // init cpu cgroup mapping
        var cpuCgroupMapping = cpuComputeMeta.getCpuCgroupMapping();
        var resourceList = resourceService.getResourceListByResourceKey(ResourceKey.MYSQL_NORMAL_LEVEL_CPU_ENLARGE_MAPPING);
        for (var resource : resourceList) {
            String realValue = resource.getRealValue();
            cpuCgroupMapping.put(resource.getDisplayValue(), (Map<String, Object>) JSON.parseObject(realValue, Map.class));
        }

        return cpuComputeMeta;
    }

    @Getter
    @Setter
    private static class CpuComputeMeta {
        Map<String, Map<String, Object>> cpuCgroupMapping = new HashMap<>();
        Map<String, String> hostLevelMapping = new HashMap<>();
        Integer flexibleResourceCpu = 0;
    }

}
