package com.aliyun.dba.physical.action.support;

import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.poddefault.action.support.flag.MyFlag;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_ClOUD_SSD;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_STORAGE_TYPE;

@Data
@Slf4j
public class PysicalToPodModifyInsParam {
    public static final Long F_DHG = 1L;
    public static final Long F_TDDL = 1L << 1;
    public static final Long F_READ_INS = 1L << 2;
    public static final Long F_EMERGENCY_TRANS = 1L << 3;
    public static final Long F_MOD_AVZ = 1L << 4;
    public static final Long F_DISK_TYPE_CHANGED = 1L << 5;
    public static final Long F_DISK_SIZE_CHANGED = 1L << 6;
    public static final Long F_TRANS_INS = 1L << 7;
    public static final Long F_PFS = 1L << 8;
    public static final Long F_BASIC = 1L << 9;
    public static final Long F_STANDARD = 1L << 10;
    public static final Long F_ENTERPRISE = 1L << 11;
    public static final Long F_XDB = 1L << 12;              // 是否是XDB
    public static final Long F_TRANS_NC = 1L << 13;         // 是否使用NC
    public static final Long F_LOCAL_SSD = 1L << 14;
    public static final Long F_SINGLE_NODE = 1L << 15;
    public static final Long F_DOUBLE_NODE = 1L << 16;      //双节点实例

    private MyFlag flags = new MyFlag();

    private AliyunInstanceDependency dependency;
    private Map<String, String> params;
    private Map<String, Boolean> initParams = new HashMap<>();

    private CustInstanceDO custins;
    private ReplicaSet replicaSetMeta;
    private String requestId;

    private User user;
    private String bid;
    private String uid;
    private String dbInstanceName;
    private String dbType;
    private String dbVersion;
    private String clusterName;
    private String orderId;
    private boolean isDHG = false;
    private boolean isTDDL = false;
    private boolean isReadIns = false;
    private boolean isEmergencyTransfer = false;
    private boolean isModifyAvz = false;
    private boolean isDiskSizeChange = false;
    private boolean isDiskTypeChange = false;
    private boolean isTransIns = false;
    private boolean isPfs = false;

    private ModifyReplicaSetResourceRequest.ModifyModeEnum modifyMode;
    private Integer diskSizeGB;
    private Integer targetDiskSizeGB;
    private String classCode;
    private String targetClassCode;
    private InstanceLevel srcInstanceLevel;
    private InstanceLevel targetInstanceLevel;
    private String srcDiskType;
    private String targetDiskType;
    private String srcPerformanceLevel;
    private String targetPerformanceLevel;

    //autopl 配置
    private Long provisionedIops;
    private boolean burstingEnabled;

    private String regionId;
    private AVZInfo oldAvzInfo;
    private AVZInfo avzInfo;
    private Date switchTime;
    private Map<String, Object> switchInfo;
    private Map<Replica.RoleEnum, String> roleHostNameMapping;
    private ScheduleTemplate scheduleTemplate;
    private PodScheduleTemplate podScheduleTemplate;
    private boolean isTargetSingleTenant;

    private ReplicaSet.BizTypeEnum bizType;
    private String currentMinorVersion;
    private String currentReleaseDate;

    public PysicalToPodModifyInsParam(AliyunInstanceDependency dependency, Map<String, String> params) {
        this.dependency = dependency;
        this.params = params;
        requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID, "");
        this.setParamInitDone("requestId");
    }

    public PysicalToPodModifyInsParam initCustins() throws Exception {
        custins = dependency.getMysqlParamSupport().getAndCheckCustInstance(params);
        setParamInitDone("custins");
        return this;
    }

    public PysicalToPodModifyInsParam initReplicaSetMeta() throws Exception {
        replicaSetMeta = dependency.getReplicaSetService().getAndCheckUserReplicaSet(params);
        ReplicaSet primaryReplicaSet = dependency.getPodCommonSupport().getPrimaryReplicaSet(requestId, replicaSetMeta).getRight();
        boolean isDoubleReadIns = dependency.getReplicaSetService().isAligroupDoubleNodeRead(requestId, replicaSetMeta.getName());
        flags.setFlags(F_BASIC, BASIC_LEVEL.equalsIgnoreCase(primaryReplicaSet.getCategory()));
        flags.setFlags(F_STANDARD, STANDARD_LEVEL.equalsIgnoreCase(primaryReplicaSet.getCategory()));
        flags.setFlags(F_ENTERPRISE, ENTERPRISE_LEVEL.equalsIgnoreCase(primaryReplicaSet.getCategory()));
        flags.setFlags(F_DOUBLE_NODE, isDoubleReadIns);
        setParamInitDone("replicaSetMeta");
        multiWriteEngineValidate();
        return this;
    }

    public PysicalToPodModifyInsParam initDBType() throws Exception {
        checkDepend("replicaSetMeta");
        dbType = replicaSetMeta.getService();
        setParamInitDone("dbType");
        return this;
    }

    public PysicalToPodModifyInsParam initDBVersion() throws Exception {
        checkDepend("replicaSetMeta");
        dbVersion = replicaSetMeta.getServiceVersion();
        setParamInitDone("dbVersion");
        return this;
    }

    public PysicalToPodModifyInsParam initClusterName() throws Exception {
        checkDepend("replicaSetMeta");
        clusterName = replicaSetMeta.getResourceGroupName();
        setParamInitDone("clusterName");
        return this;
    }

    public PysicalToPodModifyInsParam setClusterNameToParamsForDHG() throws Exception {
        checkDepend("clusterName").checkDepend("isDHG");
        if (isDHG) {
            params.put("ClusterName".toLowerCase(), clusterName);
        }
        setParamInitDone("setClusterNameToParamsForDHG");
        return this;
    }

    public PysicalToPodModifyInsParam setIsDHG() throws Exception {
        checkDepend("clusterName");
        isDHG = dependency.getMysqlParamSupport().isDHGCluster(clusterName);
        flags.setFlags(F_DHG, isDHG);
        setParamInitDone("isDHG");
        return this;
    }

    public PysicalToPodModifyInsParam setIsReadIns() throws Exception {
        checkDepend("replicaSetMeta");
        isReadIns = ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSetMeta.getInsType()) ||
                ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType());
        flags.setFlags(F_READ_INS, isReadIns);
        setParamInitDone("isReadIns");
        return this;
    }

    public PysicalToPodModifyInsParam initOrderId() {
        orderId = getParameterValue(params, "OrderId");
        setParamInitDone("orderId");
        return this;
    }

    public PysicalToPodModifyInsParam initDiskSizeGB() throws Exception {
        checkDepend("replicaSetMeta");
        Integer diskSizeMB = replicaSetMeta.getDiskSizeMB();
        if (diskSizeMB == null || diskSizeMB == 0) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }

        diskSizeGB = diskSizeMB / 1024;
        setParamInitDone("diskSizeGB");
        return this;
    }

    public PysicalToPodModifyInsParam initTargetDiskSizeGB() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("diskSizeGB");
        targetDiskSizeGB = diskSizeGB;
        if (dependency.getMysqlParamSupport().hasParameter(params, ParamConstants.STORAGE)) {
            targetDiskSizeGB = CheckUtils.parseInt(
                    dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.STORAGE),
                    ESSD_MIN_DISK_SIZE, 102400, ErrorCode.INVALID_STORAGE);
        }
        setParamInitDone("targetDiskSizeGB");
        return this;
    }

    public PysicalToPodModifyInsParam initClassCode() throws Exception {
        checkDepend("replicaSetMeta");
        classCode = replicaSetMeta.getClassCode();
        setParamInitDone("classCode");
        return this;
    }

    public PysicalToPodModifyInsParam initTargetClassCode() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("classCode");
        targetClassCode = classCode;
        if (dependency.getMysqlParamSupport().hasParameter(params, ParamConstants.TARGET_DB_INSTANCE_CLASS)) {
            targetClassCode = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
        }
        setParamInitDone("targetClassCode");
        return this;
    }

    public PysicalToPodModifyInsParam initUser() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("requestId");
        user = dependency.getDBaasMetaService().getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
        setParamInitDone("user");

        bid = user.getBid();
        uid = user.getAliUid();
        setParamInitDone("bid");
        setParamInitDone("uid");

        return this;
    }

    public PysicalToPodModifyInsParam initInstanceName() {
        dbInstanceName = dependency.getMysqlParamSupport().getDBInstanceName(params);
        setParamInitDone("dbInstanceName");
        return this;
    }

    public PysicalToPodModifyInsParam initSrcInstanceLevel() throws Exception {
        checkDepend("dbType").checkDepend("dbVersion").checkDepend("classCode");
        srcInstanceLevel = dependency.getDBaasMetaService().getDefaultClient()
                .getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
        setParamInitDone("srcInstanceLevel");
        return this;
    }

    public PysicalToPodModifyInsParam initTargetInstanceLevel() throws Exception {
        checkDepend("dbType").checkDepend("dbVersion").checkDepend("targetClassCode").checkDepend("isReadIns");
        targetInstanceLevel = isClassCodeChange() ?
                dependency.getDBaasMetaService().getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, targetClassCode, null) :
                srcInstanceLevel;

        val isBasicToStandard = InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.STANDARD.equals(targetInstanceLevel.getCategory());


        if (!isReadIns && !isBasicToStandard && targetInstanceLevel.getCategory() != srcInstanceLevel.getCategory()) {
            //非只读实例场景，目前不支持原规格和目标规格跨Category，相关任务流还没有
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
        }
        setParamInitDone("targetInstanceLevel");
        return this;
    }

    public PysicalToPodModifyInsParam initOldAVZInfo() throws Exception {
        checkDepend("custins");
        oldAvzInfo = dependency.getAvzSupport().getAVZInfoFromCustInstance(custins);
        setParamInitDone("oldAvzInfo");
        return this;
    }

    public PysicalToPodModifyInsParam initAVZInfo() throws Exception {
        avzInfo = dependency.getAvzSupport().getAVZInfo(params);
        setParamInitDone("avzInfo");
        return this;
    }

    public PysicalToPodModifyInsParam initRegionId() throws Exception {
        checkDepend("avzInfo");
        regionId = avzInfo.getRegionId();
        setParamInitDone("regionId");
        return this;
    }

    public PysicalToPodModifyInsParam initTargetDiskType() throws Exception {
        checkDepend("dbInstanceName").checkDepend("targetDiskSizeGB");
        targetDiskType = dependency.getPodParameterHelper().getParameterValue(DB_INSTANCE_STORAGE_TYPE);
        if (StringUtils.isNotEmpty(targetDiskType) && !PodCommonSupport.isSupportDiskType(targetDiskType)) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        if (StringUtils.isBlank(targetDiskType)) {
            targetDiskType = srcDiskType;
            targetPerformanceLevel = srcPerformanceLevel;
        } else {
            PodParameterHelper.checkCloudEssdStorageValid(targetDiskType, targetDiskSizeGB);
            targetPerformanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(targetDiskType);
            targetDiskType = PodParameterHelper.transferDiskTypeParam(targetDiskType);
        }
        setParamInitDone("targetDiskType");
        setParamInitDone("targetPerformanceLevel");
        return this;
    }

    public PysicalToPodModifyInsParam initAutoPLConfig() throws Exception {
        /*
         * autopl config parameter，provisionedIops和burstingEnabled
         */
        checkDepend("dbInstanceName");
        checkDepend("targetDiskType");
        checkDepend("targetDiskSizeGB").checkDepend("uid");
        String paramProvisionedIops = getParameterValue(params, ParamConstants.AUTOPL_PROVISIONED_IOPS);
        String paramBurstingEnabled = getParameterValue(params, ParamConstants.AUTOPL_BURSTING_ENABLED);
        provisionedIops = dependency.getReplicaSetService().getAutoConfigProvisionedIops(requestId, dbInstanceName, targetDiskType, paramProvisionedIops, targetDiskSizeGB, uid);
        burstingEnabled = dependency.getReplicaSetService().getAutoConfigBurstingEnabled(requestId, dbInstanceName, targetDiskType, paramBurstingEnabled);

        setParamInitDone("burstingEnabled");
        setParamInitDone("provisionedIops");
        return this;
    }


    /**
     * 新架构实例
     * */
    public PysicalToPodModifyInsParam initSrcDiskTypeForPod() throws Exception {
        checkDepend("dbInstanceName");
        // getReplicaSetStorageType中的listReplicasInReplicaSet无法查询到本地盘实例
        srcDiskType = dependency.getReplicaSetService().getReplicaSetStorageType(dbInstanceName, requestId);
        srcPerformanceLevel = dependency.getReplicaSetService().getVolumePerfLevel(requestId, dbInstanceName, srcDiskType);
        setParamInitDone("srcDiskType");
        setParamInitDone("srcPerformanceLevel");
        return this;
    }

    /**
     * 本地盘实例
     * */
    public PysicalToPodModifyInsParam initSrcDiskTypeForPhysical() throws Exception {
        checkDepend("dbInstanceName");
        // 本地盘实例,DiskType为local_ssd
        srcDiskType = Replica.StorageTypeEnum.LOCAL_SSD.toString();
        srcPerformanceLevel = null;
        setParamInitDone("srcDiskType");
        setParamInitDone("srcPerformanceLevel");
        return this;
    }

    public PysicalToPodModifyInsParam setIsModifyAvz() throws Exception {
        checkDepend("avzInfo").checkDepend("oldAvzInfo");
        if (!avzInfo.isValidForModify()) {
            setParamInitDone("isModifyAvz");
            avzInfo = oldAvzInfo;
            return this;
        }

        isModifyAvz = isModifyAvz(avzInfo, oldAvzInfo);
        flags.setFlags(F_MOD_AVZ, isModifyAvz);
        setParamInitDone("isModifyAvz");
        return this;
    }

    public PysicalToPodModifyInsParam initRoleHostNameMapping() throws Exception {
        checkDepend("setClusterNameToParamsForDHG");
        roleHostNameMapping = dependency.getPodParameterHelper().getRoleHostNameMapping();
        setParamInitDone("roleHostNameMapping");
        return this;
    }

    public boolean getIsEmergencyTransfer() {
        return this.isEmergencyTransfer;
    }

    public PysicalToPodModifyInsParam initSwitchInfo() throws Exception {
        switchTime = dependency.getMysqlParamSupport().parseCheckSwitchTimeTimeZoneSafe(params);
        switchInfo = dependency.getPodParameterHelper().getSwitchInfo(params);
        setParamInitDone("switchInfo");
        return this;
    }

    public PysicalToPodModifyInsParam setIsDiskSizeChange() throws Exception {
        checkDepend("diskSizeGB").checkDepend("targetDiskSizeGB");
        isDiskSizeChange = targetDiskSizeGB != null && !diskSizeGB.equals(targetDiskSizeGB);
        flags.setFlags(F_DISK_SIZE_CHANGED, isDiskSizeChange);
        setParamInitDone("isDiskSizeChange");
        return this;
    }

    public PysicalToPodModifyInsParam setIsDiskTypeChange() throws Exception {
        checkDepend("srcDiskType").checkDepend("targetDiskType");
        isDiskTypeChange = targetDiskType != null && !srcDiskType.equals(targetDiskType);
        flags.setFlags(F_DISK_TYPE_CHANGED, isDiskTypeChange);
        //只能云盘之间进行变更
        if (isDiskTypeChange && !StringUtils.contains(srcDiskType.toLowerCase(), "cloud")
                && !StringUtils.contains(targetDiskType.toLowerCase(), "cloud")) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
        }
        setParamInitDone("isDiskTypeChange");
        return this;
    }

    public boolean isEncryptionKeyAvailable() throws Exception {
        this.checkDepend("custins").checkDepend("uid");

        // 检查云盘加密kmsKeyId无效时，禁止变配，避免卸载云盘后无法挂载，导致实例不可用
        if (!dependency.getKmsService().isCustinsByokAndKeyEnableOrNoByok(custins, uid)) {
            throw new RdsException(ErrorCode.INVALID_KMS_KEY);
        }
        return true;
    }

    public boolean isOnlyDiskSizeChange() throws Exception {
        checkDepend("isDiskSizeChange").checkDepend("isDiskTypeChange");
        return isDiskSizeChange && !isClassCodeChange() && !isDiskTypeChange;
    }

    public boolean isClassCodeChange() throws Exception {
        checkDepend("classCode").checkDepend("targetClassCode");
        return !classCode.equals(targetClassCode);
    }

    public boolean isOnlyDiskTypeChange() throws Exception {
        checkDepend("isDiskSizeChange").checkDepend("isDiskTypeChange");
        return !isDiskSizeChange && !isClassCodeChange() && isDiskTypeChange;
    }

    public boolean isModifyCloudSSDToCloudEssd() throws Exception{
        String srcDataDiskCategory = getSrcDiskType();
        String targetDataDiskCategory = getTargetDiskType();
        String targetPerLevl = DockerOnEcsConstants.getEssdPerLevel(targetDataDiskCategory);
        return StringUtils.isNotEmpty(srcDataDiskCategory) &&
                StringUtils.isNotEmpty(targetDataDiskCategory) &&
                srcDataDiskCategory.equals(ECS_ClOUD_SSD) &&
                targetPerLevl != null;
    }

    public void multiWriteEngineValidate() throws Exception {
        checkDepend("replicaSetMeta");
        if (!dependency.getAligroupService().isXdbMultiWriteEngine(requestId, replicaSetMeta)) {
            return;
        }
        log.info("current replicaset is multi engine");
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
    }

    /**
     * 设置参数初始化完成标，用于其他参数依赖检查
     */
    private void setParamInitDone(String paramName) {
        initParams.put(paramName, true);
    }

    public void diskSizeValidation() throws RdsException {
        if (targetDiskSizeGB == null) {
            return;
        }
        if (targetDiskSizeGB >= diskSizeGB) {
            //如果目标的磁盘大小大于当前实例的，不需要做校验
            return;
        }
        InstancePerfDO instancePerf = dependency.getInstanceService().getInstancePerfByCustinsId(replicaSetMeta.getId().intValue(), 0);
        String diskCurr;
        if (instancePerf != null && instancePerf.getDiskCurr() != null) {
            diskCurr = instancePerf.getDiskCurr();
        } else {
            diskCurr = replicaSetMeta.getDiskSizeMB().toString();
        }
        Long diskUsed = (new BigDecimal(diskCurr)).longValue();
        double targetDiskSizeMB = targetDiskSizeGB * 1024;
        if (targetDiskSizeMB <= (double)diskUsed * CustinsSupport.DISK_SIZE_REDUCE_RULE) {
            log.error("request diskSize {} is less than current used size {} * 1.2: ", targetDiskSizeMB, diskCurr);
            throw new RdsException(ErrorCode.INVALID_STORAGE);
        }
    }

    /**
     * 依赖检查
     */
    public PysicalToPodModifyInsParam checkDepend(String paramName) throws Exception {
        if (!initParams.containsKey(paramName)) {
            throw new Exception("must init " + paramName + " first!");
        }
        return this;
    }

    public String getTmpReplicaSetName(String bizType) throws RdsException {
        int MAX_TMP_INSTANCE_NAME_LENGTH = 38;
        String temporderId = "aligroup".equals(bizType) ? null : orderId;
        String tmpReplicaSetNamePrefix = BaseModifyDBInstanceService.getTempReplicaSetNamePrefix(requestId, dbInstanceName, temporderId, MAX_TMP_INSTANCE_NAME_LENGTH);
        return String.format("%s-%s", tmpReplicaSetNamePrefix.toLowerCase(), dbInstanceName);
    }

    public boolean isXDB() throws Exception {
        checkDepend("replicaSetMeta");
        return dependency.getReplicaSetService().isReplicaSetXDB(requestId, replicaSetMeta.getName());
    }

    public boolean isCloudDisk() throws Exception {
        checkDepend("targetDiskType");
        return ReplicaSetService.isStorageTypeCloudDisk(targetDiskType);
    }

    public boolean isLocalSSD() throws Exception {
        checkDepend("targetDiskType");
        return Replica.StorageTypeEnum.LOCAL_SSD.toString().equalsIgnoreCase(targetDiskType);
    }


    public boolean isSingleNode() throws Exception {
        checkDepend("srcInstanceLevel");
        return MysqlParamSupport.isSingleNode(srcInstanceLevel);
    }

    public boolean isBaiscCategory() throws Exception {
        checkDepend("srcInstanceLevel");
        return MysqlParamSupport.checkCategory(srcInstanceLevel, InstanceLevel.CategoryEnum.BASIC);
    }

    public static void replicaNameValidate(List<Replica> currentReplicas, String tmpReplicaSetName) throws RdsException {
        for (Replica currentReplica : currentReplicas) {
            // 这里要做校验，判断replica的名字是否正在使用，
            // 否则在Common资源申请失败会对正在使用的资源做释放
            if (Objects.requireNonNull(currentReplica.getName()).contains(tmpReplicaSetName)) {
                log.error("Replica's name is {}, tmpReplicaSetName is {}, not allow to allocate resource", currentReplica.getName(), tmpReplicaSetName);
                throw new RdsException(ErrorCode.INVALID_TARGET_DBINSTANCENAME);
            }
        }
    }


    /**
     * 对比可用区配置是否发现变化。可用区模式发生变化返回true，主可用区模式对比多个role，非主可用区模式对比ZoneID
     */
    private boolean isModifyAvz(AVZInfo avzInfo, AVZInfo oldAvzInfo) throws RdsException {
        boolean modifyAvz = false;
        if (avzInfo.getDispenseMode() != oldAvzInfo.getDispenseMode()) {
            modifyAvz = true;
        } else if (avzInfo.getDispenseMode() == ParamConstants.DispenseMode.MultiAVZDispenseMode) {
            avzInfo.getMultiAVZExParamDO().sortAvailableZoneInfoAsMasterFirst();
            oldAvzInfo.getMultiAVZExParamDO().sortAvailableZoneInfoAsMasterFirst();
            List<String> newZoneList = avzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList().stream().map(AvailableZoneInfoDO::getZoneID).collect(Collectors.toList());
            List<String> oldZoneList = oldAvzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList().stream().map(AvailableZoneInfoDO::getZoneID).collect(Collectors.toList());
            if (!newZoneList.equals(oldZoneList)) {
                modifyAvz = true;
            }
        } else {
            // 变配时参数可能不带ZoneID
            modifyAvz = StringUtils.isNotBlank(avzInfo.getMasterZoneId()) &&
                    !avzInfo.getMasterZoneId().equals(oldAvzInfo.getMasterZoneId());
        }
        return modifyAvz;
    }

    public PysicalToPodModifyInsParam initRsTemplate() throws Exception {
        checkDepend("replicaSetMeta")
                .checkDepend("targetInstanceLevel")
                .checkDepend("isTargetSingleTenant");
        scheduleTemplate = dependency.getPodTemplateHelper().getReplicaSetScheduleTemp(replicaSetMeta, targetInstanceLevel, isTargetSingleTenant, null);
        podScheduleTemplate = dependency.getPodTemplateHelper().getReplicaSetPodScheduleTemplate(replicaSetMeta);
        setParamInitDone("scheduleTemplate");
        setParamInitDone("podScheduleTemplate");
        return this;
    }

    public PysicalToPodModifyInsParam initIsTargetSingleTenant() throws Exception {
        checkDepend("replicaSetMeta")
                .checkDepend("targetDiskType")
                .checkDepend("targetInstanceLevel")
                .checkDepend("isDHG");
        if (dependency.getReplicaSetService().isCloudSingleTenant(replicaSetMeta.getBizType(), targetDiskType, targetInstanceLevel, isDHG)) {
            isTargetSingleTenant = true;
        } else {
            isTargetSingleTenant = false;
        }
        setParamInitDone("isTargetSingleTenant");
        return this;
    }

    public PysicalToPodModifyInsParam setBizType() {
        bizType=dependency.getPodParameterHelper().getBizType(requestId,regionId);
//        if (StringUtils.isNotBlank(tddlClusterName)) {
//            bizType = ReplicaSet.BizTypeEnum.ALIGROUP;
//        }
        setParamInitDone("bizType");
        return this;
    }

    public PysicalToPodModifyInsParam setCurrentMinorVersion() throws RdsException {
        CustinsParamDO custinsParamDO = dependency.getCustinsParamService().getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_KEY);
        if (custinsParamDO != null) {
            currentMinorVersion = custinsParamDO.getValue();
            currentReleaseDate = dependency.getMinorVersionServiceHelper().parseReleaseDateFromMinorVersion(custinsParamDO.getValue());
        } else {
            throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
        }
        setParamInitDone("currentMinorVersion");
        setParamInitDone("currentReleaseDate");
        return this;
    }

    public PysicalToPodModifyInsParam fixReplicaSetMeta(){
        bizType=dependency.getPodParameterHelper().getBizType(requestId,regionId);
        replicaSetMeta.setBizType(bizType);
        return this;
    }


}
