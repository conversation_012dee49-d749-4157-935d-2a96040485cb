package com.aliyun.dba.physical.action;

import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCreateDdrDBInstanceImpl")
public class CreateDdrDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(CreateDdrDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CreateDdrDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        BakSupport.preProcessForCreateDdr(actionParams);
        try {
            CreateDBInstanceImpl createDBInstance = SpringContextUtil.getBeanByClass(CreateDBInstanceImpl.class);
            return createDBInstance.doActionRequest(custins, actionParams);
        } catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
