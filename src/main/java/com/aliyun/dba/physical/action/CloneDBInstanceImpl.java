package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.enums.Common;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.CheckBaksetDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.support.EcsCommonSupport;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAction;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckHostIdSet;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckRestoreType;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getOperatorId;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getPenginePolicyID;
import static com.aliyun.dba.custins.support.CustinsSupport.CONN_TYPE_LVS;
import static com.aliyun.dba.custins.support.CustinsSupport.CONN_TYPE_PROXY;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_CREATING;
import static com.aliyun.dba.custins.support.CustinsSupport.getAndCheckBizType;
import static com.aliyun.dba.custins.support.CustinsSupport.getNetType;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_MASTER;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_SLAVE;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.*;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;
import static com.aliyun.dba.support.property.ParamConstants.TARGET_UID;
import static com.aliyun.dba.support.property.ParamConstants.TARGET_USER_ID;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_PRIMARY;

/**
 * createMysql为共用部分，可以优化
 * 包括整个备份集恢复到新实例，库表恢复到新实例
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalCloneDBInstanceImpl")
public class CloneDBInstanceImpl implements IAction {

    //private static Logger logger =Logger.getLogger(CloneDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CloneDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected IResApi resApi;
    @Resource
    private ResManagerService resManagerService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    protected EcsCommonSupport ecsCommonSupport;
    @Autowired
    private CustinsIDao custinsIDao;

    //cloneDBInstanceForSecurity，isValidCount检查为false；cloneDBInstance为true（或者null）
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        boolean isSuccess = false;
        try {
        	// 快捷方式构建一些动态的参数，跟杜康的通用运维做集成.
        	mysqlParameterHelper.buildShortcutCloneParams(custins, actionParams);

            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParamSupport.getAndCheckSourceCustInstance(actionParams);
            mysqlParamSupport.cloneValidSrcCustins(custins);

            //根据原实例信息，判断是否是polarx_hatp，如果是，给新的实例，打标为polarx_hatp
            boolean isPolarxHatp = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP) != null;

            // 是否为使用dbs服务化的polarx存储节点
            boolean isPolarxUsingDBSService = Boolean.parseBoolean(
                    getParameterValue(actionParams, MinorVersionServiceHelper.POLARX_USING_DBS_SERVICE_EXT));

            String isValidCount = mysqlParamSupport.getParameterValue(actionParams, "isValidCount");
            //默认为校验，CloneDBInstanceForSecurityImpl中不校验，会传递该参数,为null则是cloneDBInstance
            if(isValidCount == null){
                //需要验证数量
                mysqlParamSupport.cloneValidCommon(custins, actionParams);
            }

            //原逻辑
            String action = getParameterValue(actionParams, ParamConstants.ACTION);
            Integer targetUserId = null;
            BakhistoryDO history = null;
            Long realBacksetSize = null;

            if ("CloneDBInstanceForSecurity".equalsIgnoreCase(action)) {
                targetUserId = checkService.getAndCheckUserId(
                        getParameterValue(actionParams, TARGET_USER_ID),
                        getParameterValue(actionParams, TARGET_UID),
                        getParameterValue(actionParams, ParamConstants.ACTION));
            }
            TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
            Map<String, Object> translistParamMap = new HashMap<>(8);

            trans.setsCinsReserved(1);
            List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
            trans.setsHinsid1(insIds.get(0));
            if (insIds.size() > 1) {
                trans.setsHinsid2(insIds.get(1));
            }

            /**
             * trans_list param_map set up
             */
            String restoreType = getAndCheckRestoreType(actionParams);
            translistParamMap.put("restoreType", restoreType);

            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                if (!mysqlEngineCheckService.checkCloneBeforeUpgrade(actionParams,custins.getId())){
                    throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
                }
            }

            if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                // 根据备份集Id查询备份集大小
                Long bakId = CheckUtils.parseLong(getParameterValue(actionParams, "BackupSetID"), null,
                        null, ErrorCode.BACKUPSET_NOT_FOUND);
                checkService.getAndCheckBakhistory(custins, bakId);
                history = bakService.getBakhistoryByBackupSetId(custins.getId(), bakId);
                if (!custins.getDbVersion().equals(history.getDbVersion())) { return ResponseSupport.createErrorResponse(ErrorCode.BACKUP_INSTANCE_NOT_MATCHED);}
                realBacksetSize = history.getBaksetSize();
                trans.setBakhisId(bakId);
                bakService.lockBakHisForRestore(history.getHisId());
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(history.getHisId()));


            } else if (RESTORE_TYPE_TIME.equals(restoreType)) {
                // 根据备份时间点查询备份集大小
                DateTime restoreTimeUTC = custinsService.validRestoreByTimeSafe(actionParams, custins);
                //转化为Bak库时间进行比较
                Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_BAK);
                history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                if (!custins.getDbVersion().equals(history.getDbVersion())) { return ResponseSupport.createErrorResponse(ErrorCode.BACKUP_INSTANCE_NOT_MATCHED);}
                bakService.lockBinlogForRestore(custins.getId(),history.getBakBegin(),restoreTime); // 锁定用于恢复的Binlog，避免被删除
                bakService.lockBakHisForRestore(history.getHisId());
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                        JSON.toJSONString(ImmutableMap.of(
                                "custinsId", custins.getId().toString(),
                                "begin", history.getBakBegin().getTime(),
                                "end", restoreTime.getTime())));
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(history.getHisId()));

                if (mysqlParamSupport.isMysqlXdbByCustins(custins)){
                    // xdb 按时间点clone必须要有 CONSENSUS_APPLY_INDEX 字段 用于克隆实例的回放，需内核版本支持
                    if (!JSONObject.parseObject(history.getSlaveStatus()).containsKey("CONSENSUS_APPLY_INDEX")) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
                    }
                }
                realBacksetSize = history.getBaksetSize();

                trans.setIsBaseTime(1);
                //写入dbaas库，使用dbaas库时间
                Date recoverTimeDbaas = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_DBAAS);
                trans.setRecoverTime(recoverTimeDbaas);
            } else if (RESTORE_TYPE_XDB_SEQUENCE_NUM.equals(restoreType)){
                if(!mysqlDBCustinsService.isMysqlEnterprise(custins)|| custins.getDbVersion().equals("5.6")){
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                BigInteger seqNum = new BigInteger(getParameterValue(actionParams, "SequenceNumber"));
                // In MicroSeconds
                BigInteger pointPeekBackward = new BigInteger(getParameterValue(actionParams, "PointPeekBackward"));
                BigInteger pointPeekForward = new BigInteger(getParameterValue(actionParams, "PointPeekForward"));


                DateTime restoreTimeUTC;
                if (isPolarxUsingDBSService) {
                    // PolarDB-X备份服务化后：1. 对恢复时间的校验在逻辑层进行；2. 无需对备份数据加锁
                    logger.info("Current instance is data node of polardb-x, using dbs service.");
                    restoreTimeUTC = dtzSupport.getUTCDateByDateStr(
                            CustinsParamSupport.getParameterValue(actionParams, "RestoreTime"));
                    Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_BAK);
                    history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                } else {
                    restoreTimeUTC = custinsService.validRestoreByTimeSafe(actionParams, custins);
                    Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_BAK);
                    history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                    bakService.lockBinlogForRestore(custins.getId(),history.getBakBegin(),restoreTime); // 锁定用于恢复的Binlog，避免被删除
                    bakService.lockBakHisForRestore(history.getHisId());
                    ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                            JSON.toJSONString(ImmutableMap.of(
                                    "custinsId", custins.getId().toString(),
                                    "begin", history.getBakBegin().getTime(),
                                    "end", restoreTime.getTime())));
                    ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(history.getHisId()));
                }

                if (mysqlParamSupport.isMysqlXdbByCustins(custins)){
                    // xdb 按时间点clone必须要有 CONSENSUS_APPLY_INDEX 字段 用于克隆实例的回放，需内核版本支持
                    if (!JSONObject.parseObject(history.getSlaveStatus()).containsKey("CONSENSUS_APPLY_INDEX")) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
                    }
                }

                realBacksetSize = history.getBaksetSize();

                trans.setIsBaseTime(1);
                //写入dbaas库，使用dbaas库时间
                Date recoverTimeDbaas = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_DBAAS);
                trans.setRecoverTime(recoverTimeDbaas);
                translistParamMap.put("sequenceNumber", seqNum.toString());
                translistParamMap.put("pointPeekBackward", pointPeekBackward.toString());
                translistParamMap.put("pointPeekForward", pointPeekForward.toString());
            } else if (RESTORE_TYPE_LASTEST.equals(restoreType)){
                // 克隆到最新时间
                DateTime nowUTC = DateTime.now(DateTimeZone.UTC);
                Date nowTime = dtzSupport.getSpecificTimeZoneDate(nowUTC, DATA_SOURCE_BAK);
                history = bakService.getBakhistoryByRecoverTime(custins.getId(), nowTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
                if (!custins.getDbVersion().equals(history.getDbVersion())) { return ResponseSupport.createErrorResponse(ErrorCode.BACKUP_INSTANCE_NOT_MATCHED);}
                bakService.lockBakHisForRestore(history.getHisId());
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(history.getHisId()));

                realBacksetSize = history.getBaksetSize();
                trans.setIsBaseTime(0);
            } else {
                CheckBaksetDO checkBakset = custinsService.validRestoreByUser(custins,
                    getParameterValue(actionParams, ParamConstants.DOWNLOAD_URL),
                    getParameterValue(actionParams, ParamConstants.BAKSET_NAME),
                    getParameterValue(actionParams, ParamConstants.CHECKSUM));
                Date restoreTime = checkBakset.getGmtCreated();
                history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, BAKWAY_XTRABACKUP,
                    BAKTYPE_FULL);
                if (!custins.getDbVersion().equals(history.getDbVersion())) { return ResponseSupport.createErrorResponse(ErrorCode.BACKUP_INSTANCE_NOT_MATCHED);}
                bakService.lockBinlogForRestore(custins.getId(),history.getBakBegin(),restoreTime); // 锁定用于恢复的Binlog，避免被删除
                bakService.lockBakHisForRestore(history.getHisId());
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                        JSON.toJSONString(ImmutableMap.of(
                                "custinsId", custins.getId().toString(),
                                "begin", history.getBakBegin().getTime(),
                                "end", restoreTime.getTime())));
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(history.getHisId()));
                realBacksetSize = history.getBaksetSize();

                translistParamMap.put("downloadUrl", checkBakset.getDownloadUrl());
                translistParamMap.put("baksetName", checkBakset.getName());
            }

            //检查是否库表恢复
            boolean isRestoreTable = mysqlParamSupport.getAndCheckRestoreTable(actionParams);
            //检查TableMeta参数
            String tableMeta = null;
            if(isRestoreTable){
                //不支持金融版
                if (mysqlDBCustinsService.isMysqlEnterprise(custins)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }

                tableMeta = mysqlParamSupport.getAndCheckTableMeta(actionParams,history,"clone", custins.getDbVersion(), false);
            }

            String storage = getParameterValue(actionParams, "storage");
            if (storage != null) {
                //库表恢复场景下，检验用户传递的storage大小是否大于需要的空间
                if (isRestoreTable){
                    //单位KB，乘以10倍解压缩率
                    realBacksetSize = mysqlParamSupport.getAndCheckStorageForRestoreDbtables(tableMeta, history) / 1024 * 10;
                    /*if(Long.parseLong(storage) * CustinsSupport.GB_TO_KB <= realBacksetSize * CustinsSupport.DISK_SIZE_REDUCE_RULE){
                        throw new RdsException(MysqlErrorCode.STORAGE_FOR_RESTORE_NOT_ENOUGH.toArray());
                    }*/
                }
                else if (Long.parseLong(storage) * CustinsSupport.GB_TO_KB <= realBacksetSize * CustinsSupport.DISK_SIZE_REDUCE_RULE){

                    CustinsParamDO srcCompressionModeParam = custinsParamService.getCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_MODE_KEY);
                    CustinsParamDO srcCompressionRatioParam = custinsParamService.getCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_RATIO_KEY);
                    if (srcCompressionModeParam.getValue().equals("on")){
                        if (Long.parseLong(storage) * CustinsSupport.GB_TO_KB * Double.valueOf(srcCompressionRatioParam.getValue())  <= realBacksetSize * CustinsSupport.DISK_SIZE_REDUCE_RULE){
                            return createErrorResponse(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
                        }
                    }else{
                        return createErrorResponse(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
                    }
                }
            }

            CustInstanceDO cloneCustins = cloneSrcCustinsWithoutParentid(custins, !custins.isDockerLogic(), actionParams);
            if (targetUserId != null) {
                cloneCustins.setUserId(targetUserId);
            }

            // 检查DBInstanceClass是否是docker on ecs规格，调用onecs克隆逻辑
            if (CustinsParamSupport.hasParameterValue(actionParams, ParamConstants.DB_INSTANCE_CLASS)) {
                String dbInstanceClass = CustinsParamSupport.getParameterValue(actionParams,ParamConstants.DB_INSTANCE_CLASS);
                InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(dbInstanceClass,
                    custins.getDbType(), custins.getDbVersion(), null,null);
                //InnerRDS limit instance level scope
                if(mysqlParameterHelper.isInnerRDS(custins.getId()) && !mysqlParameterHelper.isInstanceLevelSupportInnerRDS(insLevel.getExtraInfo())){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
                }
                if (mysqlParameterHelper.isOnEcsDocker(insLevel)) {
                    // 本地盘克隆到云盘场景，若本地盘实例已开启TDE则 拒绝克隆（aone: 38898788）
                    List<String> params = new ArrayList<String>(1);
                    params.add(CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_TDE);
                    List<CustinsParamDO> resultList = custinsParamService.getCustinsParams(custins.getId(), params);
                    String tdeStatus = CustinsParamSupport.CUSTINS_PARAM_VALUE_TDE_ENABLED;
                    if (CollectionUtils.isNotEmpty(resultList) && resultList.get(0).getValue().equals(tdeStatus)) {
                        Object[] tdeErrorCode = new Object[] {
                                ResultCode.CODE_ERROR, "InvalidTDEstatus",
                                "This instance has TDE enabled and does not support clone to onecs instances."};
                        return ResponseSupport.createErrorResponse(tdeErrorCode);
                    }

                    ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
                    return ecsCommonSupport.cloneInsOnDockerOnEcs(custins);
                }
            }

            List<CustinsConnAddrDO> custinsConnAddrList = getTmpConnAddrList(custins, actionParams);
            Integer bizType = getAndCheckBizType(actionParams);
            // 如果未传NetType，默认为私网;
            Integer specifyNetType = CustinsSupport.NET_TYPE_PRIVATE;
            if (CustinsParamSupport.hasParameterValue(actionParams, ParamConstants.DB_INSTANCE_NET_TYPE)) {
                specifyNetType = getNetType(actionParams);

            }
            Boolean isVpcNetType = CustinsSupport.isVpcNetType(specifyNetType);
            String connType = custins.getConnType();
            if (isVpcNetType && CONN_TYPE_LVS.equals(custins.getConnType())) {
                if (!custinsService.supportVpcInLvs(custins)) {
                    connType = CONN_TYPE_PROXY;
                }
            }

            // 如果没传SubDomain子域信息，那么克隆实例与源实例在同一个SubDomain
            //主可用区添加
            AVZInfo avzInfo = avzSupport.getAVZInfo(actionParams);
            AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            if(!avzInfo.isValidForModify()){
                avzInfo = oldAvzInfo;
            }
            ParamConstants.DispenseMode dispenseMode = avzInfo.getDispenseMode();
            MultiAVZExParamDO multiAVZExParamDO = avzInfo.getMultiAVZExParamDO();

            // 获取实例使用的磁盘类型（SSD/SATA），在升降级过程中不允许改变
            String hostType = custinsService.getCustinsHostType(custins.getId());

            //调用资源api
            custinsService.createCustInstanceForTrans(custins, cloneCustins);

            //如果是polarx_hatp，新实例需要打标
            if(isPolarxHatp) {
                custinsParamService.setCustinsParam(cloneCustins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP,
                    CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES);
            }

            if (isPolarxUsingDBSService) {
                custinsParamService.setCustinsParam(cloneCustins.getId(), POLARX_USING_DBS_SERVICE_PENGINE,
                        POLARX_USING_DBS_SERVICE_PENGINE_YES);
            }

            custinsParamService.setCustinsParam(cloneCustins.getId(),
                    CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID,
                    getParameterValue(actionParams, CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, ""));

            //为clone实例添加自动小版本升级
            custinsParamService.setCustinsParam(cloneCustins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto");
            // 判断原实例是否开启白名单高安全模式，有则复制改参数
            CustinsParamDO securityIPMode = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.SECURITY_IP_MODE);
            if (securityIPMode != null && ParamConstants.SECURITY_IPMODE_SAFETY.equalsIgnoreCase(securityIPMode.getValue())) {
                custinsParamService.setCustinsParam(cloneCustins.getId(), CustinsParamSupport.SECURITY_IP_MODE, ParamConstants.SECURITY_IPMODE_SAFETY);
            }
            
            /**
             * NOTE:
             * 快速克隆支持，这个是MySQL内部使用，在数据写坏的时候，任务流通过参数来识别这个场景，
             * 不要在用户场景下用这个逻辑!
             */
            String cloneMode = mysqlParameterHelper.getParameterValue(MysqlParameterHelper.CLONE_MODE);
            if (cloneMode != null && MysqlParameterHelper.QUICK_MODE.equalsIgnoreCase(cloneMode)) {
        		// NOTE: 快速克隆走独立的逻辑，这是原来逻辑的重载
            	custinsParamService.setCustinsParam(cloneCustins.getId(), MysqlParameterHelper.CLONE_MODE, cloneMode);
        	}// END MySQL 快速克隆支持

            //主可用区添加
            custinsParamService.updateAVZInfo(cloneCustins.getId(), avzInfo);

            InstanceLevelDO insLevel = instanceService
                    .getInstanceLevelByLevelId(cloneCustins.getLevelId());

            Integer nodeCount = insIds.size();

            boolean mysqlCategorySwitch = insLevel.isMysqlEnterprise() ^ mysqlDBCustinsService.isMysqlEnterprise(custins);
            // 金融版和高可用版之间发生克隆时,金融版分配3个节点,高可用分配2个节点; 对于其他情况按照源实例的节点个数

            String syncParamValue = CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_SEMI_SYNC;
            boolean isMysqlXDB = mysqlParamSupport.isMysqlXDBByLevel(insLevel);
            if (mysqlCategorySwitch) {
                if (insLevel.isMysqlEnterprise() || isMysqlXDB) {
                    nodeCount = 3;
                    syncParamValue = CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_NO_REVERT_SEMI_SYNC;
                } else {
                    nodeCount = 2;
                }
            }


            Set<Integer> hostIdSet = getAndCheckHostIdSet(actionParams);
            String requestId = mysqlParamSupport.getParameterValue(actionParams,ParamConstants.REQUEST_ID);
            String accessId = CustinsParamSupport.getParameterValue(actionParams,ParamConstants.ACCESSID);
            String orderId = CustinsParamSupport.getParameterValue(actionParams,ParamConstants.ORDERID);

            //如果src实例开启了压缩 需要记录压缩mode和压缩率
            CustinsParamDO srcCompressionModeParam = custinsParamService.getCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_MODE_KEY);
            if (srcCompressionModeParam != null && StorageCompressionHelper.COMPRESSION_MODE_ON.equals(srcCompressionModeParam.getValue()) ){
                updateCompressionParams(custins, cloneCustins);
                updateCompressionInsDiskSize(custins, cloneCustins);
            }

            //主可用区添加
            long needSpace;
            if(isRestoreTable){
                //需要空间，加200M，单位KB
                needSpace = realBacksetSize + 200 * 1024;
                //需要空间和storage比较，取最大者
                if(storage != null && needSpace > Long.parseLong(storage) * CustinsSupport.GB_TO_KB){
                    trans.setdDisksize(needSpace/1024);
                }
                else{
                    trans.setdDisksize(cloneCustins.getDiskSize());
                }
            } else{
                needSpace = realBacksetSize * 10;
                trans.setdDisksize(cloneCustins.getDiskSize());
            }



            List<Integer> cloneInsIds = createMysql(getAction(actionParams), requestId, cloneCustins, insLevel, avzInfo,
                null, custins.getClusterName(), null, connType, null, custinsConnAddrList,
                nodeCount, bizType, Integer.valueOf(hostType), Math.min(custins.getDiskSize(), needSpace / 1024), hostIdSet, accessId, orderId,
                    isMysqlXDB);
            avzSupport.updateAVZInfoByInstanceIds(avzInfo, cloneInsIds);
            custinsParamService.updateAVZInfo(cloneCustins.getId(),avzInfo);
            if(avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
                int counter = 0;
                for (Integer instanceId :cloneInsIds) {
                    instanceService.updateInstanceRoleByInsId(instanceId, counter++ == 0 ? INSTANCE_ROLE_MASTER : INSTANCE_ROLE_SLAVE);
                }
            }

            trans.setdCinsid(cloneCustins.getId());
            trans.setdLevelid(cloneCustins.getLevelId());
            custinsParamService.setCustinsParam(cloneCustins.getId(),
                    CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, syncParamValue);
            if (CustinsSupport.ISOLATE_HOST_FIXED.equals(insLevel.getIsolateHost())
                    && custinsParamService.getCustinsParam(custins.getId(), MySQLParamConstants.V6_CPU_MATCH) == null) {
                custinsParamService.createCustinsParam(new CustinsParamDO(cloneCustins.getId(),
                        MySQLParamConstants.V6_CPU_MATCH, MySQLParamConstants.V6_CPU_MATCH_VALUE));
            }
            //克隆过程中sql审计相关参数不进行同步
            updateSqlAuditParams(cloneCustins);
            trans.setdHinsid1(cloneInsIds.get(0));
            if (cloneInsIds.size() > 1) {
                trans.setdHinsid2(cloneInsIds.get(1));
            } else {
                trans.setdHinsid2(0);
            }
            //将tableMeta参数存放在translist的parameter参数中
            if(isRestoreTable){
                translistParamMap.put("table_meta",tableMeta);
            }
            trans.setParameter(JSON.toJSONString(translistParamMap));


            String taskKey = TaskSupport.TASK_CLONE_INS;
            Integer taskId = null;

            if(isRestoreTable){
                taskKey = TaskSupport.TASK_CLONE_INS_DBSTABLES;
                taskId = instanceService.cloneCustInstanceDBsTablesTask(getAction(actionParams), custins,
                    trans, cloneCustins, getOperatorId(actionParams), cloneCustins.getId(),
                    RESTORE_TYPE_TIME.equals(restoreType), taskKey);
            }
            else{
                taskId = instanceService.cloneCustInstanceTask(getAction(actionParams), custins,
                    trans, cloneCustins, getOperatorId(actionParams), cloneCustins.getId(),
                    RESTORE_TYPE_TIME.equals(restoreType));
            }

            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(actionParams));

            Map<String, Object> data = new HashMap<String, Object>(10);
            data.put("DBInstanceID", cloneCustins.getId());
            data.put("DBInstanceName", cloneCustins.getInsName());
            data.put("TaskId", taskId);
            //返回值做任务类型区分
            data.put("TaskKey", taskKey);
            isSuccess = true;
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if(!isSuccess) {
                if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")) {
                    JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
                    bakService.unlockBinlogForRestore(
                            Integer.valueOf(lockBinlog.get("custinsId").toString()),
                            new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                            new Date(Long.parseLong(lockBinlog.get("end").toString()))
                    );
                }
                if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
                    String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
                    bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));

                }
            }
        }
    }

    public void updateCompressionInsDiskSize(CustInstanceDO custins, CustInstanceDO cloneCustins) {
        CustinsParamDO srcCompressionRatioParam = custinsParamService.getCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_RATIO_KEY);
        Long diskSizeInOrder = cloneCustins.getDiskSize();
        Long diskSizeInMaotai = Long.valueOf(StorageCompressionHelper.calculateLogicalSize(diskSizeInOrder.intValue(), Double.valueOf(srcCompressionRatioParam.getValue())));
        cloneCustins.setDiskSize(diskSizeInMaotai);

        CustInstanceDO custinsCompressionDo  = custinsService.getCustInstanceByInsName(null, cloneCustins.getInsName());
        custinsCompressionDo.setDiskSize(diskSizeInMaotai);
        custinsService.updateCustInstance(custinsCompressionDo);

        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsIds(Collections.singletonList(cloneCustins.getId()));
        for (InstanceDO instanceDo : instanceList) {
            instanceService.updateInstanceDiskSizeByInsId(instanceDo.getId(), diskSizeInMaotai.intValue());
        }
    }

    public void updateCompressionParams(CustInstanceDO custins, CustInstanceDO cloneCustins) {
        CustinsParamDO srcCompressionRatioParam = custinsParamService.getCustinsParam(custins.getId(), StorageCompressionHelper.COMPRESSION_RATIO_KEY);
        custinsParamService.setCustinsParam(cloneCustins.getId(), StorageCompressionHelper.COMPRESSION_MODE_KEY, StorageCompressionHelper.COMPRESSION_MODE_ON);
        custinsParamService.setCustinsParam(cloneCustins.getId(), StorageCompressionHelper.COMPRESSION_RATIO_KEY, srcCompressionRatioParam.getValue());
        custinsParamService.setCustinsParam(cloneCustins.getId(), StorageCompressionHelper.COMPRESSION_BEFORE_SIZE, cloneCustins.getDiskSize().toString());
    }

    public CustInstanceDO cloneSrcCustinsWithoutParentid(CustInstanceDO srcCustins, boolean isSetParentId, Map<String, String> actionParams) throws RdsException {
        CustInstanceDO cloneCustins = srcCustins.clone();
        cloneCustins.setId(null);
        cloneCustins.setClusterName("");
        cloneCustins.setStatus(CUSTINS_STATUS_CREATING);
        cloneCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        cloneCustins.setIsTmp(CUSTINS_INSTYPE_PRIMARY);
        cloneCustins.setInsType(CUSTINS_INSTYPE_PRIMARY);
        cloneCustins.setInsName(getParameterValue(actionParams, ParamConstants.DB_INSTANCE_NAME));
        if (isSetParentId) {
            cloneCustins.setParentId(srcCustins.getId());
        }
        cloneCustins.setMaintainStarttime(srcCustins.getMaintainStarttime());
        cloneCustins.setMaintainEndtime(srcCustins.getMaintainEndtime());

        Integer bizType = getAndCheckBizType(actionParams);
        // 设置实例规格信息(gp不能让用户任意的选择规格,只能继承父实例的规格,包括levelID和groupCount)
        if (!cloneCustins.isGpdb()) {
            custinsService.setInstanceLevel(cloneCustins, getParameterValue(actionParams, ParamConstants.DB_INSTANCE_CLASS),
                    bizType, getParameterValue(actionParams, ParamConstants.STORAGE));
        }

        // 设置实例描述
        if (CustinsParamSupport.hasParameterValue(actionParams, ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            String desc = SupportUtils.decode(getParameterValue(actionParams, ParamConstants.DB_INSTANCE_DESCRIPTION));
            cloneCustins.setComment(CheckUtils
                    .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION));
        }

        return cloneCustins;
    }

    public List<CustinsConnAddrDO> getTmpConnAddrList(CustInstanceDO srcCustins, Map<String, String> actionParams)
            throws RdsException {
        List<CustinsConnAddrDO> tmpCustinsConnAddrList = new ArrayList<CustinsConnAddrDO>();

        // 如果未传NetType，默认为私网;
        Integer specifyNetType = CustinsSupport.NET_TYPE_PRIVATE;
        if (CustinsParamSupport.hasParameterValue(actionParams, ParamConstants.DB_INSTANCE_NET_TYPE)) {
            specifyNetType = getNetType(actionParams);
        }
        Boolean isVpcNetType = CustinsSupport.isVpcNetType(specifyNetType);

        String connPrex = CheckUtils.checkNullForConnectionString(
                getParameterValue(actionParams, ParamConstants.CONNECTION_STRING));
        String portStr = CustinsSupport.getConnPort(getParameterValue(actionParams, ParamConstants.PORT),
                srcCustins.getDbType());

        // 创建连接地址对象
        String connAddrCust = mysqlParameterHelper.getConnAddrCust(connPrex,
                mysqlParameterHelper.getRegionIdByClusterName(srcCustins.getClusterName()), srcCustins.getDbType());

        // 创建实例连接对象
        String vpcInstanceId = null;
        if (isVpcNetType) {
            vpcInstanceId = getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID);
            if (vpcInstanceId == null) {
                vpcInstanceId = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_NAME);
            }
        }
        CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                connAddrCust,
                portStr,
                specifyNetType,
                CustinsValidator
                        .getRealNumber(getParameterValue(actionParams, ParamConstants.TUNNEL_ID), -1),
                getParameterValue(actionParams, ParamConstants.VPC_ID),
                getParameterValue(actionParams, ParamConstants.VSWITCH_ID),
                getParameterValue(actionParams, ParamConstants.IP_ADDRESS),
                vpcInstanceId);

        tmpCustinsConnAddrList.add(custinsConnAddr);

        if (CustinsSupport.isDns(srcCustins.getConnType()) && mysqlParamSupport.isMysqlXdbByCustins(srcCustins)) {
            //给Leader和Follower创建DNS
            for (int i = 0; i < 2; ++i) {
                String connAddrNode = mysqlParameterHelper.getConnAddrCust(connPrex + "-" + (i + 1),mysqlParamSupport.getRegionIdByClusterName(srcCustins.getClusterName()), srcCustins.getDbType());
                CustinsConnAddrDO dnsCustinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                        connAddrNode,
                        portStr,
                        specifyNetType,
                        -1,
                        null,
                        null,
                        null,
                        null);
                dnsCustinsConnAddr.setInsId(-1);
                tmpCustinsConnAddrList.add(dnsCustinsConnAddr);
            }
        }

        return tmpCustinsConnAddrList;
    }

    public List<Integer> createMysql(String action, String requestId,
                                      CustInstanceDO custins, InstanceLevelDO insLevel, AVZInfo avzInfo, String specifyClusterName,
                                      String preferClusterName, Integer excludeCustinsId, String connType, Integer proxyGroupId,
                                      List<CustinsConnAddrDO> custinsConnAddrList, Integer nodeCount, Integer bizType,
                                      Integer hostType, Long diskSizeUsed, Set<Integer> hostIdSet, String accessId, String orderId,
                                     boolean isMysqlXDB) throws RdsException {

        // 创建container
        ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, custins.getDbType());
        resourceContainer.setUserId(custins.getUserId());
        resourceContainer.setRequestId(requestId);
        resourceContainer.setClusterName(specifyClusterName);
        resourceContainer.setPreferClusterName(preferClusterName);
        resourceContainer.setBizType(bizType);
        resourceContainer.setAccessId(accessId);
        resourceContainer.setOrderId(orderId);

        /**
         * allocate resource
         */
        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        custinsResModel.setConnType(connType);
        custinsResModel.setSpecifyProxyGroupId(proxyGroupId);
        if (excludeCustinsId != null) {
            custinsResModel.addExcludeCustinsId(excludeCustinsId);
        }

        // host resource
        HostinsResModel hostinsResModel = new HostinsResModel(custins.getLevelId());

        hostinsResModel.setInsCount(nodeCount);
        hostinsResModel.setHostType(hostType);
        hostinsResModel.setDiskSizeUsed(diskSizeUsed);
        hostinsResModel.setDiskSizeSold(custins.getDiskSize());
        // init distribute rule
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
        distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
        distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
        InsLevelExtraInfo.updateDistributeRule(distributeRule, insLevel.getExtraInfo());
        // 指定的host ids
        distributeRule.setSpecifyHostIdSet(hostIdSet);

        hostinsResModel.setDistributeRule(distributeRule);
        custinsResModel.setHostinsResModel(hostinsResModel);

        // vip resource
        for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
            VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
            vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
            vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
            vipResModel.setVip(custinsConnAddr.getVip());
            vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
            vipResModel.setVpcId(custinsConnAddr.getVpcId());
            vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
            vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
            vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
            vipResModel.setInstanceId(custinsConnAddr.getInsId());
            custinsResModel.addVipResModel(vipResModel);
        }
        resourceContainer.addCustinsResModel(custinsResModel);
        resourceContainer.setV6CpuMatch(true);

        CustinsDistributeRule custinsDistributeByUserIdRule = new CustinsDistributeRule();
        custinsDistributeByUserIdRule.setScatterType("SCATTER_TYPE_USER_ID");
        custinsDistributeByUserIdRule.setMaxInsPerHost(1);
        resourceContainer.getCustinsDistributeRuleList().add(custinsDistributeByUserIdRule);

        if (isMysqlXDB) {
            // XDB v7 机型CPU对齐
            resourceContainer.setCpuMatchRule(Common.CPU_MATCH_RULE_XDB_V7);
        }

        //调用资源API
        Response<AllocateResRespModel> response = resManagerService.allocateRes(resourceContainer);
        if (!response.getCode().equals(200)) {
            custinsService.deleteCustInstance(custins);
            //            custinsResourceService.createResourceRecord(action, UUID, custins.getClusterName(), custins.getInsName(),
            //                JSON.toJSONString(response));
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }
        AllocateResRespModel.CustinsResRespModel allocateResRespModel = response.getData().getCustinsResRespModelList()
            .get(0);
        custins.setClusterName(allocateResRespModel.getClusterName());
        custins.setConnType(allocateResRespModel.getConnType());
        return allocateResRespModel.getInstanceIdList();
    }

    public boolean isUsingDBSService() {
        ResourceDO resource = resourceService.getResourceByResKey("POLARDBX_GLOBAL_CONFIG");
        if (resource == null || resource.getRealValue() == null) {
            return false;
        }
        logger.info("Resource fetched: " + resource.getRealValue());

        boolean using = false;
        String uid = ActionParamsProvider.ACTION_PARAMS_MAP.get().get(ParamConstants.UID);
        try {
            JSONObject jsonObject = JSON.parseObject(resource.getRealValue());
            using = Boolean.parseBoolean(jsonObject.getString("polardbx.dbs.service.region"));
            if (uid == null) {
                return using;
            }

            String disableUserConfigs = jsonObject.getString("polardbx.dbs.service.disable.users");
            if (disableUserConfigs != null) {
                List<String> disabledUsers = JSONObject.parseArray(disableUserConfigs, String.class);
                if (disabledUsers.stream().anyMatch(user -> user.equals(uid))) {
                    return false;
                }
            }

            String userConfigs = jsonObject.getString("polardbx.dbs.service.enable.users");
            if (userConfigs != null) {
                List<String> enabledUsers = JSONObject.parseArray(userConfigs, String.class);
                if (enabledUsers.stream().anyMatch(user -> user.equals(uid))) {
                    return true;
                }
            }
        } catch (Exception exception) {
            logger.info("Fail to parse resource, exception: " + exception);
        }
        return using;
    }

    /**
     * 判断是否为PolarDB-X标准版
     * @param serviceName dn的实例名
     * */
    public boolean isPolarxStandard(String serviceName) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("serviceName", serviceName);
        List<CustinsServiceDO> srv = custinsIDao.getCustinsServiceList(params);
        if (srv == null || srv.isEmpty()) {
            return false;
        }

        Integer custinsId = srv.get(0).getCustinsId();
        CustinsParamDO custinsParam = custinsParamService.getCustinsParam(custinsId, "topologyType");
        return custinsParam != null
                && (custinsParam.getValue().equals("1azone_xdb") || custinsParam.getValue().equals("3azones_xdb"));
    }

    public void updateSqlAuditParams(CustInstanceDO cloneCustins) {
        custinsParamService.setCustinsParam(cloneCustins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SQL_LOG, CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SQL_LOG_NO);
        if (custinsParamService.getCustinsParam(cloneCustins.getId(), "enable_das_pro" )!= null){
            custinsParamService.deleteCustinsParam(cloneCustins.getId(), "enable_das_pro");
        }
        if (custinsParamService.getCustinsParam(cloneCustins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_VISIBLE_TIME )!= null){
            custinsParamService.deleteCustinsParam(cloneCustins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_VISIBLE_TIME);
        }
        if (custinsParamService.getCustinsParam(cloneCustins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_RETENTION )!= null){
            custinsParamService.deleteCustinsParam(cloneCustins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_RETENTION);
        }

    }

}
