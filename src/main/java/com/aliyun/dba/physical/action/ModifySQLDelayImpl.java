package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAction;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getOperatorId;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getPenginePolicyID;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalModifySQLDelayImpl")
public class ModifySQLDelayImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(ModifySQLDelayImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ModifySQLDelayImpl.class);

    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsService custinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (!custins.isRead()) { //只支持只读实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            //aone:https://work.aone.alibaba-inc.com/issue/31931952
            if(!custins.isActive()){
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            if (mysqlParamSupport.isMysqlXdbByCustins(primaryCustins)) {
                //XDB的只读不支持设置sqldelay
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            String sqlDelay = getParameterValue(actionParams, "SqlDelay");
            CheckUtils.parseInt(sqlDelay, 0, Integer.MAX_VALUE, ErrorCode.INVALID_SQL_DELAY_TIME);
            Map<String, Object> taskQueueParam = new HashMap<String, Object>(1);
            taskQueueParam.put("sql_delay", sqlDelay); //sql_delay use in task_queue
            Integer taskId = instanceService.setSqlDelay(getAction(actionParams), custins, taskQueueParam,
                    getOperatorId(actionParams));
            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(actionParams));
            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("SqlDelay", sqlDelay);  //SqlDelay use in api result
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
