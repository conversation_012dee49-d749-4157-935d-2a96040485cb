package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import static java.util.Arrays.asList;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_NC;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_MYSQL;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalIsolateDBInstanceTmpfsImpl")

/**
 * <AUTHOR>
 * @date 2021/11/02
 */
public class IsolateDBInstanceTmpfsImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(IsolateDBInstanceTmpfsImpl.class);

    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsParamService custinsParamService;

    private static final String isolateTmpfs = "need_isolate_tmpfs";
    private static final String paramsTmpfs = "IsolateTmpfs";
    private static final String taskKey = "isolate_tmpfs";

    private static final List<String> availableValues = asList("0", "1");

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            if (!custins.getKindCode().equals(KIND_CODE_NC) || !custins.getDbType().equals(DB_TYPE_MYSQL)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            String tmpfs = getParameterValue(params, paramsTmpfs);
            if (tmpfs != null) {
                if (!availableValues.contains(tmpfs)) {
                    return createErrorResponse(ErrorCode.PARAM_TYPE_ERROR);
                }
                custinsParamService.setCustinsParam(custins.getId(), isolateTmpfs, tmpfs);
            }

            TaskQueueDO taskQueue = new TaskQueueDO(mysqlParamSupport.getAction(params),
                mysqlParamSupport.getOperatorId(params), custins.getId(), TaskSupport.TASK_TYPE_CUSTINS, taskKey);
            taskService.createTaskQueue(taskQueue);

            Map<String, Object> data = new HashMap<>(3);
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.TASK_ID, taskQueue.getId());
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
