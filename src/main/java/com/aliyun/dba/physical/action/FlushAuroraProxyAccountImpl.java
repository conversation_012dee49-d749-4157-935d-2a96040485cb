package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_TRANS;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalFlushAuroraProxyAccountImpl")
public class FlushAuroraProxyAccountImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(FlushAuroraProxyAccountImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(FlushAuroraProxyAccountImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected TaskService taskService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
        RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);

            if (custins.isMysql51() || custins.isMysql55()) {
                //mysql5.1, mysql5.5 不支持小版本升级
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }
            if (custins.isReadAndWriteLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            //此处isMysql和isCustinsOnEcs可通过
            if (custins.isShare()) {
                //不是专享实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (custins.isRead()) {
                // 过滤只读实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                //实例状态错误
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (custins.isMysql56() || custins.isMysql57Physical()) {
                Integer custinsId = custins.getId();
                String engineVersion = custins.getDbVersion();
                // 最新内核
                //String minorVersionInMeta = null;
                //try {
                //    minorVersionInMeta = custinsService.getCustInstancelastMinorVersionByCustinsId(custinsId, engineVersion);
                //} catch (Exception e) {
                //    logger.error("minor_version_exp", e);
                //}

                // 设置切换时间
                Date switchTime = mysqlParamSupport.getAndCheckSwitchTime(actionParams);
                String switchMode = mysqlParamSupport.getAndCheckSwitchModeForAP(actionParams, ParamConstants.SWITCH_TIME_MODE, switchTime);

                //默认 运维时间切换
                if (StringUtils.isEmpty(switchMode)) {
                    switchMode = CustinsSupport.MAINTAIN_MODE;
                }

                Integer isForce = mysqlParamSupport.getAndCheckIsForce(actionParams);//1: 强制, 0: 不强制 默认:不强制
                Map<String, Object> taskQueueParam = new HashMap<String, Object>(3);
                //if (StringUtils.isNotBlank(minorVersionInMeta)) {
                //    taskQueueParam.put("minor_version", minorVersionInMeta);
                //}

                Map<String, Object> effMap = custinsService.getEffectiveTimeMap(switchMode, switchTime);
                taskQueueParam.put(CustinsSupport.SWITCH_KEY, effMap);
                taskQueueParam.put("is_force", isForce);
                Integer taskId = instanceService
                    .flushAuroraProxyAccount(mysqlParamSupport.getAction(actionParams),
                        custins, taskQueueParam, mysqlParamSupport.getOperatorId(actionParams));
                taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));
                custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_TRANS,
                    CustinsState.STATE_MAINTAIN_ACTIVITION.getComment());

                Map<String, Object> data = new HashMap<String, Object>(4);
                data.put("DBInstanceID", custins.getId());
                data.put("DBInstanceName", custins.getInsName());
                //data.put("TargetMinorVersion", minorVersionInMeta);
                data.put("TaskId", taskId);
                data.put("isForce", isForce);
                data.put("switchMode", switchMode);
                return data;
            }
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
