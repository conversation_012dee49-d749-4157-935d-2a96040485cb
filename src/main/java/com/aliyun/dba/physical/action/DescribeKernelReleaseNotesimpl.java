package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_MYSQL;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalDescribeKernelReleaseNotesimpl")
public class DescribeKernelReleaseNotesimpl implements IAction {

    //private static final Logger logger = Logger.getLogger(DescribeKernelReleaseNotesimpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeKernelReleaseNotesimpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);

            if (custins.isMysql51() || custins.isCustinsOnEcs()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);//mysql5.1 ecs 不维护
            }

            // TODO: need to fix indentation later
            Integer custinsId = custins.getId();
            // 获取正在使用小版本
            String minorVersionNowUse = custinsService.getCustInstanceCurrentMinorVersionByCustinsId(custinsId);
            String engineVersion = custins.getDbVersion();

            // 获取实例最高可升级小版本
            String minorVersionInMeta = custinsService.getCustInstancelastMinorVersionByCustinsId(custinsId,
                    engineVersion);

            //获取正在使用的小版本与最新的小版本之间的差异releaseNote，如果小版本是最新，则返回最新的的小版本releaseNote
            List<Map<String, String>> releaseNotesMap = custinsService.getCustInstaceDiffReleaseNote(DB_TYPE_MYSQL,
                    engineVersion, minorVersionNowUse, minorVersionInMeta);

            Map<String, String> restMap = new HashMap<String, String>();
            for (Map<String, String> note : releaseNotesMap) {
                restMap.put(note.get("release_version"), note.get("release_note"));
            }
            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("DBInstaceDiffReleaseNote", restMap);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
