package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalRestartDBSlaveInsImpl")
public class RestartDBSlaveInsImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(RestartDBSlaveInsImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected InstanceService instanceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws
        RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            if (custins.isReadAndWriteLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (custins.isShare()) {
                //不是专享实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                //实例状态错误
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //检查是否为高可用或者物理机三节点的实例
            List<InstanceDO> instanceDOList = instanceService.getInstanceByCustinsId(custins.getId());
            if (instanceDOList == null || instanceDOList.size() < 2) {
                return createErrorResponse(MysqlErrorCode.INVALID_CUSTINS_HAS_NO_SLAVE_INSTACE.toArray());
            }

            boolean hasSlaveIns = false;
            for (InstanceDO instanceDO : instanceDOList) {
                if (instanceDO.getRole() == 1) {
                    String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
                    logger.info("requestId=" + requestId + ",get slave instance=" + instanceDO.getId());
                    hasSlaveIns = true;
                    break;
                }
            }

            if (!hasSlaveIns) {
                return createErrorResponse(MysqlErrorCode.INVALID_CUSTINS_HAS_NO_SLAVE_INSTACE.toArray());
            }

            Map<String, Object> param = null;
            Integer taskId = createRestartDBSlaveInsTask(mysqlParamSupport.getAction(params), custins,
                mysqlParamSupport.getOperatorId(params), param);
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(params));
            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public Integer createRestartDBSlaveInsTask(String action, CustInstanceDO custins, Integer operatorId,
                                               Map<String, Object> param) {

        String taskKey = "restart_slave_ins";
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, custins.getId(), TaskSupport.TASK_TYPE_CUSTINS,
            taskKey, param == null ? "" : JSONObject
            .toJSONString(param), 0);
        taskQueue.setPriority(1);
        taskService.createTaskQueue(taskQueue);
        return taskQueue.getId();
    }

}
