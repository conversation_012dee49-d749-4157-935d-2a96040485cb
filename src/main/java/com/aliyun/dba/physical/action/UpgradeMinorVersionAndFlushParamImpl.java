package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceConfig;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.entity.MycnfChangeLog;
import com.aliyun.dba.instance.entity.MycnfTemplate;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.kpi.service.KpiService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_PARAM_MINOR_VERSION_KEY;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("physicalUpgradeMinorVersionAndFlushParamImpl")
@Slf4j
public class UpgradeMinorVersionAndFlushParamImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeMinorVersionAndFlushParamImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected MinorVersionService minorVersionService;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Autowired
    private MycnfService mycnfService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private KpiService kpiService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParameterHelper.getAndCheckCustInstance();
            String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
            if (custins == null) {
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);//实例不存在，或者不是实例拥有者
            }

            if (custins.isReadAndWriteLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (custins.isShare()) {
                //不是专享实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                //实例状态错误
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isMysql51()) {
                //mysql5.1 不支持小版本升级
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }
            List<CustInstanceDO> readCustInstances = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), true);
            if (!readCustInstances.isEmpty()) {
                logger.error("custins {} exists readonly instance.", custins.getInsName());
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            Map<String, Object> taskQueueParam = new HashMap<String, Object>(3);
            // 小版本处理
            Integer custinsId = custins.getId();
            String targetMinorVersionInit = getParameterValue(actionParams, "targetMinorVersion");
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(actionParams);
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(actionParams, utcDate, true);
            String category = minorVersionServiceHelper.getMinorVersionCategory(custins);
            String minorVersionTag = minorVersionServiceHelper.getAndCheckMinorVersionTag(custins);
            CustinsParamDO minorVersionInfo = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_KEY);
            if(minorVersionInfo == null){
                //实例缺少小版本信息
                throw new RdsException(ErrorCode.CUSTINS_MINOR_VERSION_ATTR_MISSING);
            }
            String targetReleaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionInit);
            String nowReleaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(minorVersionInfo.getValue());
            if(Integer.valueOf(targetReleaseDate) <= Integer.valueOf(nowReleaseDate)){
                //指定版本小于当前实例版本
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION_LOWER_THAN_CUSTINS);
            }
            List<MinorVersionReleaseDO> minorVersionReleaseDOS = minorVersionService.querySpecifyMinorVersionListByCondition(custins.getDbType(), custins.getDbVersion(), custins.getKindCode(), category, minorVersionTag, targetReleaseDate);
            if(minorVersionReleaseDOS == null || minorVersionReleaseDOS.size() == 0){
                //找不到指定版本
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND);
            }
            if (!mysqlEngineCheckService.checkCanUpgradeMinorVersionWithMaxScale(custins, targetReleaseDate)) {
                return ResponseSupport.createErrorResponse(new Object[]{ResultCode.CODE_UNSUPPORTED, "MaxscaleMinorVersionNotSupport", "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first."});
            }
            String targetMinorVersion = "mysql" + custins.getDbVersion().replace(".", "") + "_" + targetReleaseDate;
            taskQueueParam.put("minor_version", targetMinorVersion);
            Map<String, Object> effMap = custinsService.getEffectiveTimeMapTimeZoneSafe(switchMode, utcDate);
            taskQueueParam.put("pt_switch_time_mode", getParameterValue(actionParams, ParamConstants.SWITCH_TIME_MODE));
            taskQueueParam.put("pt_switch_time", getParameterValue(actionParams, ParamConstants.SWITCH_TIME));
            taskQueueParam.put(CustinsSupport.SWITCH_KEY, effMap);
            // 参数处理
            Map<String, Object> parameterMap = null;
            if (mysqlParameterHelper.hasParameter("Parameters")){
                parameterMap = mysqlParameterHelper.getAndCheckParameters();
                addChangeLog(parameterMap, custins, requestId);
            }
            String dbInstanceStatusDesc = CustinsState.STATE_MAINTAINING.getComment();
            custinsService.updateCustInstanceStatusByCustinsId(custinsId, CustinsSupport.CUSTINS_STATUS_TRANS, dbInstanceStatusDesc);
            String taskKey = "upgrade_minor_version_flush_params";
            TaskQueueDO taskQueue = new TaskQueueDO(getAction(actionParams), CustinsParamSupport.getOperatorId(actionParams), custins.getId(), TaskSupport.TASK_TYPE_CUSTINS,
                    taskKey, JSON.toJSONString(taskQueueParam));
            taskService.createTaskQueue(taskQueue);
            Integer taskId = taskQueue.getId();
            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TargetMajorVersion", targetMinorVersionInit);
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

    private void addChangeLog(Map<String, Object> parameterMap, CustInstanceDO custins, String requestId) throws ApiException, RdsException {
        if (null != parameterMap.get("max_connections")) {
            InstanceConfig maxConnConfig = dBaasMetaService.getDefaultClient().getReplicaSetConfig(requestId, custins.getInsName(), "max_connections");
            if (StringUtils.equalsIgnoreCase(maxConnConfig.getValue(), String.valueOf(parameterMap.get("max_connections")))) {
                logger.warn("max_connections param value is equal to previous");
                parameterMap.remove("max_connections");
            }
            // 检查修改参数是否为空
            if (parameterMap.isEmpty()) {
                return;
            }
        }
        InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());

        CustinsParamDO custInsParamDO = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
        String paramGroupId = custInsParamDO == null ? "" : custInsParamDO.getValue();
        MycnfCustinstanceDO collationServer = mycnfService.getMycnfCustinstance(custins.getId(), "collation_server");
        ParamContext paramContext = new ParamContext(custins, paramGroupId);
        paramContext.setIgnoreVisible(true);
        paramContext.setAliGroup(false);
        paramContext.setArm(false);
        paramContext.setPolarxHatp(false);
        paramContext.setCategory(instanceLevelDO.getCategory());
        paramContext.setCpuCoreCount(instanceLevelDO.getCpuCores());
        paramContext.setMemSize(instanceLevelDO.getMemSize().longValue());
        paramContext.setDiskSize(custins.getDiskSize());
        paramContext.setMaxConnection(instanceLevelDO.getMaxConn());
        if (collationServer != null) {
            paramContext.setExistCollationServer(true);
        }
        Map<String, MycnfTemplate> mycnfTemp = parameterGroupTemplateGenerator.getSysBaseParamTempMap(paramContext);
        Integer custinsId = custins.getId();
        Date date = new Date();
        for (String key : parameterMap.keySet()) {
            MycnfTemplate temp = mycnfTemp.get(key);
            // 刷参数据写入change_log前，用trim() 去掉空格 (aone:38203755)
            String newValue = String.valueOf(parameterMap.get(key)).trim();

            MycnfChangeLog changelog = instanceService.getMycnfCustinsHistoryByName(custinsId, key);
            String oldValue = changelog != null ? changelog.getNewValue() : temp.getDefaultValue();
            MycnfChangeLog log = new MycnfChangeLog(custinsId, key, oldValue, newValue, date);
            log.setCreator(99999);
            log.setModifier(999999);
            if (StringUtils.equals("max_connections", log.getName()) || StringUtils.equals("max_user_connections", log.getName())) {
                log.setCreator(382438);
                log.setModifier(382438);
            }
            mycnfService.createMycnfChangeLog(log);
        }
    }
}
