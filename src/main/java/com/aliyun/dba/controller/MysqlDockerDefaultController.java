package com.aliyun.dba.controller;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.adapter.MysqlDockerDefaultDBEngineExtAdapter;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 2019/9/23.
 */
@Controller()
public class MysqlDockerDefaultController {

    @Resource
    private MysqlDockerDefaultDBEngineExtAdapter mysqlDockerDefaultDBEngineExtAdapter;

    @RequestMapping(value = "/test/testdockerdefault")
    public @ResponseBody
    Object
    testDockerDefaultInstance(HttpServletRequest request) {
        Map<String, String[]> requestMap = request.getParameterMap();

        Map<String, String> params = new HashMap<>();
        if (requestMap.get("content") != null) {
            String[] jsons = requestMap.get("content");
            params.putAll(JSON.parseObject(jsons[0], Map.class));
        } else {
            String name = "";
            String value = "";
            for (Map.Entry<String, String[]> entry : requestMap.entrySet()) {
                name = entry.getKey();
                String[] values = entry.getValue();
                if (null == values) {
                    value = "";
                } else if (values.length > 1) {
                    for (int i = 0; i < values.length; i++) { //用于请求参数中有多个相同名称
                        value = values[i] + ",";
                    }
                    value = value.substring(0, value.length() - 1);
                } else {
                    value = values[0];//用于请求参数中请求参数名唯一
                }
                params.put(name.toLowerCase(), value);
            }
        }
        try {
            return mysqlDockerDefaultDBEngineExtAdapter.getActionResult(null, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

}
