package com.aliyun.dba.controller;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.adapter.MysqlCommonKindcodeDBEngineExtAdapter;
import com.aliyun.dba.adapter.MysqlDBEngineExtAdapter;
import com.aliyun.dba.adapter.MysqlDockerDefaultDBEngineExtAdapter;
import com.aliyun.dba.adapter.MysqlPodDefaultDBEgngineExtAdapter;
import com.aliyun.dba.support.property.RdsException;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 2019/9/23.
 */
@RestController
@RequestMapping(value = "/services")
public class BaseController {

    @Resource
    private MysqlDBEngineExtAdapter mysqlDBEngineExtAdapter;
    @Resource
    private MysqlDockerDefaultDBEngineExtAdapter mysqlDockerDefaultDBEngineExtAdapter;
    @Resource
    private MysqlCommonKindcodeDBEngineExtAdapter mysqlCommonKindcodeDBEngineExtAdapter;
    @Resource
    private MysqlPodDefaultDBEgngineExtAdapter mysqlPodDefaultDBEgngineExtAdapter;
    @GetMapping
    public @ResponseBody Object operateCustins(HttpServletRequest request) throws RdsException {
        Map<String, String[]> requestMap = request.getParameterMap();
        Map<String, String> params = new HashMap<>();
        if (requestMap.get("content") != null) {
            String[] jsons = requestMap.get("content");
            params.putAll(JSON.parseObject(jsons[0], Map.class));
        } else {
            String name = "";
            String value = "";
            for (Map.Entry<String, String[]> entry : requestMap.entrySet()) {
                name = entry.getKey();
                String[] values = entry.getValue();
                if (null == values) {
                    value = "";
                } else if (values.length > 1) {
                    for (String s : values) { //用于请求参数中有多个相同名称
                        value = s + ",";
                    }
                    value = value.substring(0, value.length() - 1);
                } else {
                    value = values[0];  //用于请求参数中请求参数名唯一
                }
                params.put(name.toLowerCase(), value);
            }
        }

        String opsServiceVersion = params.get("opsserviceversion");
        if (Strings.isNotEmpty(opsServiceVersion)) {
            return mysqlPodDefaultDBEgngineExtAdapter.getActionResult(null, params);
        }
        String migrateServiceVersion = params.get("migrateserviceversion");
        if (Strings.isNotEmpty(migrateServiceVersion)) {
            return mysqlDockerDefaultDBEngineExtAdapter.getActionResult(null, params);
        }
        return mysqlDBEngineExtAdapter.getActionResult(null, params);
    }


}
