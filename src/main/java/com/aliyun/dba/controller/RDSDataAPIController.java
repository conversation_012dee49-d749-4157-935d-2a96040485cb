package com.aliyun.dba.controller;

import com.aliyun.dba.commonkindcode.action.RetrieveSecretValueImpl;
import com.aliyun.dba.dataapi.service.RDSDataAPIService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@RestController
@RequestMapping(value = "dataapi")
public class RDSDataAPIController {

    private static final LogAgent logger = LogFactory.getLogAgent(RDSDataAPIController.class);

    @Autowired
    private RDSDataAPIService rdsDataAPIService;

    @RequestMapping(value = "/createSecret")
    public Map<String, Object> createSecret(@RequestParam(name="regionId") String regionId, @RequestParam(name="uid") Long uid, @RequestParam(name="bid") String bid, @RequestParam(name="username") String username, @RequestParam(name="password") String password, @RequestParam(name="dbInstanceId") String dbInstanceId,
                                            @RequestParam(name="resourceGroupId") String resourceGroupId, @RequestParam(name="dbNames", required=false) String dbNames, @RequestParam(name="description", required=false) String description, @RequestParam(name="secretName", required=false) String secretName) {
        String[] dbNameArr;
        if (dbNames != null) {
            dbNameArr = dbNames.split(",");
        } else {
            dbNameArr = null;
        }
        return rdsDataAPIService.createSecret(regionId, uid, bid, username, password, dbInstanceId, resourceGroupId, description, secretName, dbNameArr);
    }

    @RequestMapping(value = "/retrieveSecretValue")
    public Map<String, Object> retrieveSecretValue(@RequestParam(name="regionId") String regionId, @RequestParam(name="dbInstanceId", required=false) String dbInstanceId, @RequestParam(name="uid") Long uid, @RequestParam(name="bid") String bid, @RequestParam(name="secretArn", required=false) String secretArn, @RequestParam(name="secretName", required=false) String secretName, @RequestParam(name="username", required=false) String username) {
        if (secretArn != null) {
            return rdsDataAPIService.retrieveSecretValueByARN(regionId, uid, bid, secretArn);
        }
        if (secretName != null) {
            if (null == dbInstanceId) {
                logger.error("SecretName needs to be used with dbInstanceId");
                return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
            }
            return rdsDataAPIService.retrieveSecretValueByName(regionId, dbInstanceId, uid, bid, secretName);
        }
        return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
    }

    @RequestMapping(value = "/deleteSecret")
    public Map<String, Object> deleteSecret(@RequestParam(name="regionId") String regionId, @RequestParam(name="dbInstanceId", required=false) String dbInstanceId, @RequestParam(name="uid") Long uid, @RequestParam(name="bid") String bid, @RequestParam(name="secretArn", required = false) String secretArn, @RequestParam(name="secretName", required = false) String secretName) {
        if (secretArn != null) {
            return rdsDataAPIService.deleteSecretByARN(regionId, uid, bid, secretArn);
        }
        if (secretName != null) {
            if (null == dbInstanceId) {
                logger.error("SecretName needs to be used with dbInstanceId");
                return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
            }
            return rdsDataAPIService.deleteSecretByName(regionId, dbInstanceId, uid, bid, secretName);
        }
        return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
    }

    @RequestMapping(value = "/describeSecrets")
    public Map<String, Object> describeSecrets(@RequestParam(name="regionId") String regionId, @RequestParam(name="uid") Long uid, @RequestParam(name="dbInstanceId", required=false) String dbInstanceId, @RequestParam(name="bid") String bid, @RequestParam(name="pageSize", required = false) Long pageSize, @RequestParam(name="pageNumber", required = false) Long pageNumber) {
        if (pageSize == null) {
            pageSize = 10L;
        }
        if (pageNumber == null) {
            pageNumber = 1L;
        }
        return rdsDataAPIService.describeSecrets(regionId, uid, bid, dbInstanceId, pageSize, pageNumber);
    }
}
