package com.aliyun.dba.controller;

import com.aliyun.dba.support.common.registry.service.RegistryService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller()
@RequestMapping(value = "healthCheck")
public class HealthCheckController {

    @Resource
    private RegistryService registryService;

    @RequestMapping(value = "/check")
    public @ResponseBody
    Object
    check(HttpServletRequest request) {
        return registryService.componentHealthCheck();
    }
}
