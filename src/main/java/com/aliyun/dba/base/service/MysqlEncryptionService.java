package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.BakOwnerDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyuncs.kms.model.v20160120.CreateServiceKeyResponse;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aliyun.dba.base.idao.UserKmsRelDO;
import com.aliyun.dba.base.idao.UserKmsRelIDao;

import javax.annotation.Resource;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.property.ParamConstants.KMS_KEY_TYPE_ALIYN_AES_256;
import static com.aliyun.dba.support.property.ParamConstants.KMS_KEY_TYPE_ALIYN_SM4;


@Service
@Slf4j
public class MysqlEncryptionService {

    @Autowired
    protected ResourceService resourceService;
    @Autowired
    private KmsApi kmsApi;
    @Autowired
    protected UserKmsRelIDao userKmsRelIDao;
    @Resource
    private KmsService kmsService;
    @Resource
    private ClusterIDao clusterIDao;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private DBaasMetaService metaService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private ReplicaSetService replicaSetService;

    /**
     * 获取云盘加密的服务密钥
     * @param clusterName
     * @param uid
     * @param userId
     * @return
     * @throws Exception
     */
    public String getServiceKey(String clusterName, String uid, Integer userId) throws Exception {
        List<BakOwnerDO> bakOwnerDOList = resourceService.getBakOwnerListByCluster(clusterName,
                BakOwnerDO.KMS_OWNER_TYPE);
        if (bakOwnerDOList.size() != 1) {
            log.error("cluster " + clusterName + "should config one kms service.");
            throw new RdsException(ErrorCode.INVALID_CLUSTER_KMS);
        }
        String encryptionKey = "";
        Integer kmsId = bakOwnerDOList.get(0).getBakId();
        Map<String, Object> condition = new HashMap<>(2);
        condition.put("kmsId", kmsId);
        condition.put("userId", userId);
        UserKmsRelDO userKmsRel = userKmsRelIDao.getUserKmsRelByKmsIdAndUserId(condition);
        String roleArn = "acs:ram::" + uid + ":role/aliyunrdsinstanceencryptiondefaultrole";
        boolean serviceKeyExist = false;
        if (userKmsRel == null) {
            // 如果没有服务密钥，先检查用户账号下是否有，有的话补充元数据，没有则创建并插入元数据
            List<String> serviceKeys = kmsApi.listKeysByFilters(clusterName, roleArn, uid, SERVICE_KEY_FILTERS);
            if (!serviceKeys.isEmpty()) {
                for (String key : serviceKeys) {
                    DescribeKeyResponse describeKeyResponse = kmsApi.describeKey(clusterName, key, roleArn, uid);
                    String creator = describeKeyResponse.getKeyMetadata().getCreator();
                    if (creator.equalsIgnoreCase("rds")) {
                        encryptionKey = key;
                        serviceKeyExist = true;
                        break;
                    }
                }
            }
            if (!serviceKeyExist) {
                CreateServiceKeyResponse createServiceKeyResponse = kmsApi.createServiceKey(clusterName, null, uid, TDE_KEY_USAGE);
                encryptionKey = createServiceKeyResponse.getKeyMetadata().getKeyId();
            }
            UserKmsRelDO userKmsRelDO = new UserKmsRelDO();
            userKmsRelDO.setKmsId(kmsId);
            userKmsRelDO.setUserId(userId);
            userKmsRelDO.setMasterKey(encryptionKey);
            userKmsRelIDao.addUserKmsRel(userKmsRelDO);
        } else {
            encryptionKey = userKmsRel.getMasterKey();
            List<String> serviceKeys = kmsApi.listKeysByFilters(clusterName, roleArn, uid, SERVICE_KEY_FILTERS);
            if (!serviceKeys.isEmpty()) {
                for (String key : serviceKeys) {
                    DescribeKeyResponse describeKeyResponse = kmsApi.describeKey(clusterName, key, roleArn, uid);
                    String creator = describeKeyResponse.getKeyMetadata().getCreator();
                    if (creator.equalsIgnoreCase("rds")) {
                        encryptionKey = key;
                        serviceKeyExist = true;
                        if (encryptionKey.equalsIgnoreCase(key)) {
                            break;
                        }
                    }
                }
            }
            if (!serviceKeyExist) {
                CreateServiceKeyResponse createServiceKeyResponse = kmsApi.createServiceKey(clusterName, null, uid, TDE_KEY_USAGE);
                encryptionKey = createServiceKeyResponse.getKeyMetadata().getKeyId();
            }
        }
        return encryptionKey;
    }

    /**
     * 针对有主实例的情况，进行变配、创建只读、克隆操作时，检查云盘加密的密钥创建者，目标实例多租户不能使用自定义密钥
     * @param requestId
     * @param replicaSet
     * @param isSingleTenant
     * @throws Exception
     */
    public void checkEncryptionKeyByReplicaSet(String requestId, ReplicaSet replicaSet,  Boolean isSingleTenant) throws Exception {
        String dbInstanceName = replicaSet.getName();
        if (StringUtils.isNotBlank(dbInstanceName) && dbInstanceName.startsWith("dbs-")) {
            log.info("check name start with dbs, ins name:{}, skip",dbInstanceName);
            return;
        }
        String encryptionKey = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSet.getName(),PodDefaultConstants.ENCRYPTION_KEY_LABEL);
        if (StringUtils.isBlank(encryptionKey)){
            return;
        }
        String cluster = replicaSet.getResourceGroupName();
        User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);
        String uid = user.getAliUid();
        String roleArn = kmsService.getUserRoleArn(uid);
        DescribeKeyResponse describeKeyResponse = kmsApi.describeKey(cluster, encryptionKey, roleArn, uid);
        if(describeKeyResponse==null || describeKeyResponse.getKeyMetadata()==null){
            throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
        }
        //禁用状态的密钥不支持加密
        String keyState = describeKeyResponse.getKeyMetadata().getKeyState();
        if(keyState.equalsIgnoreCase("Disabled")){
            throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
        }
        String keyType = describeKeyResponse.getKeyMetadata().getKeySpec();
        //云盘加密只支持对称加密
        if(!keyType.equals(KMS_KEY_TYPE_ALIYN_AES_256) && !keyType.equals(KMS_KEY_TYPE_ALIYN_SM4)){
            throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
        }
        //通用型规格只支持默认密钥
        if(!isSingleTenant) {
            checkKMSCreator(cluster, encryptionKey, roleArn, uid);
        }
    }

    /**
     * 针对创建实例，检查云盘加密的密钥创建者，多租户不能使用自定义密钥
     * @param requestId
     * @param params
     * @param isSingleTenant
     * @throws Exception
     */
    public void checkEncryptionKeyForNewCustins(String requestId, Map<String, String> params,  Boolean isSingleTenant) throws Exception {
        String encryptionKey = mysqlParamSupport.getParameterValue(params, "EncryptionKey", null);
        String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
        if (StringUtils.isNotBlank(dbInstanceName) && dbInstanceName.startsWith("dbs-")) {
            return;
        }
        //clone,createread
        String primaryInsName = mysqlParamSupport.getParameterValue(params, "PrimaryDBInstanceName", null);
        if (StringUtils.isNotBlank(primaryInsName)) {
            encryptionKey = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, primaryInsName,PodDefaultConstants.ENCRYPTION_KEY_LABEL);
            log.info("primaryInsName:{} key:{}",primaryInsName,encryptionKey);
        }
        String sourceInsName = mysqlParamSupport.getParameterValue(params, "SourceDBInstanceName", null);
        if (StringUtils.isNotBlank(sourceInsName)) {
            ReplicaSet sourceReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, sourceInsName, true);
            if (sourceReplicaSet != null) {
                encryptionKey = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, sourceInsName, PodDefaultConstants.ENCRYPTION_KEY_LABEL);
            }
            log.info("sourceInsName:{} key:{}",sourceInsName,encryptionKey);
        }
        if (StringUtils.isBlank(encryptionKey)) {
            return;
        }
        String region = mysqlParamSupport.getParameterValue(params, "region", null);
        if (StringUtils.isBlank(region)) {
            region = mysqlParamSupport.getParameterValue(params, "SubDomain",null);;
        }
        List<String> clusterNameList = clusterIDao.getClusterNamesByRegion(region, "global", true);
        if (clusterNameList.isEmpty()) {
            throw new RdsException(ErrorCode.CLUSTER_NOT_FOUND);
        }
        String cluster = clusterNameList.get(0);
        String uid = mysqlParamSupport.getUID(params);
        String roleArn = kmsService.getUserRoleArn(uid);
        DescribeKeyResponse describeKeyResponse = kmsApi.describeKey(cluster, encryptionKey, roleArn, uid);
        if(describeKeyResponse==null || describeKeyResponse.getKeyMetadata()==null){
            throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
        }
        String keyState = describeKeyResponse.getKeyMetadata().getKeyState();
        //禁用状态的密钥不支持加密
        if(keyState.equalsIgnoreCase("Disabled")){
            throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
        }
        String keyType = describeKeyResponse.getKeyMetadata().getKeySpec();
        //云盘加密只支持对称加密
        if(!keyType.equals(KMS_KEY_TYPE_ALIYN_AES_256) && !keyType.equals(KMS_KEY_TYPE_ALIYN_SM4)){
            throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
        }
        //通用型规格只支持默认密钥
        if(!isSingleTenant) {
            checkKMSCreator(cluster, encryptionKey, roleArn, uid);
        }
    }

    /*
     * 检查kms密钥的创建者
     */
    private void checkKMSCreator(String cluster, String encryptionKey, String roleArn, String uid) throws Exception{
        try {
            DescribeKeyResponse describeKeyResponse = kmsApi.describeKey(cluster, encryptionKey, roleArn, uid);
            DescribeKeyResponse.KeyMetadata keyMetadata = describeKeyResponse.getKeyMetadata();
            String keyCreator = keyMetadata.getCreator();
            if (keyCreator.equalsIgnoreCase(uid)) {
                throw new RdsException(ErrorCode.INVALID_KMS_TYPE_FOR_INSTANCE);
            }
        } catch (RdsException e) {
            //自定义密钥（创建者为用户），不支持通用型规格
            throw new RdsException(ErrorCode.UNSUPPORTED_CLOUD_DISK_ENCRYPTION);
        } catch (Exception e) {
            log.error("describeKey failed! " +",roleArn=" + roleArn +",keyId=" + encryptionKey+",uid="+uid, e);
            throw new RdsException(ErrorCode.KMS_API_ERROR);
        }
    }

    // 确保有tag，否则有些地方解析会有问题
    public void ensureTagExistence(String clusterName, String roleArn, String keyId, String uid) throws Exception {
        Map<String, Boolean> resourceTags = kmsApi.resourceTags(clusterName, roleArn, keyId, uid);
        boolean hasTag = resourceTags.getOrDefault(CustinsSupport.ROLE_ARN_TAG, false);
        if (!hasTag) {
            kmsApi.tagResource(clusterName, roleArn, keyId, uid);
        }
    }

    private void checkKmsKeyValid(String requestId, ReplicaSet replicaSet, String encryptionKey) throws Exception {
        String cluster = replicaSet.getResourceGroupName();
        User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);
        String uid = user.getAliUid();
        String roleArn = kmsService.getUserRoleArn(uid);
        DescribeKeyResponse describeKeyResponse = kmsApi.describeKey(cluster, encryptionKey, roleArn, uid);
        if (describeKeyResponse == null || describeKeyResponse.getKeyMetadata() == null) {
            throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
        }
        // 禁用状态的密钥不支持
        String keyState = describeKeyResponse.getKeyMetadata().getKeyState();
        if (keyState.equalsIgnoreCase("Disabled")) {
            throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
        }
    }

    public void ensureTdeEncryptionKeyValid(String requestId, ReplicaSet replicaSet) throws Exception {
        String dbInstanceName = replicaSet.getName();
        String tdeEnabled = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, dbInstanceName, TDE_ENABLED);
        // 实例没开启TDE，放行
        if (!StringUtils.equalsIgnoreCase(tdeEnabled, "1")) {
            log.info("{} TDE not enabled", dbInstanceName);
            return;
        }
        String encryptionKey = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, dbInstanceName, TDE_ENCRYPTION_KEY_ID);
        if (StringUtils.isBlank(encryptionKey)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TDE_STATUS);
        }
        checkKmsKeyValid(requestId, replicaSet, encryptionKey);
    }

    public void ensureClsEncryptionKeyValid(String requestId, ReplicaSet replicaSet) throws Exception {
        String dbInstanceName = replicaSet.getName();
        String clsKeyMode = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, dbInstanceName, CLS_KEY_MODE);
        // 实例没开启CLS，放行
        if (!StringUtils.equalsIgnoreCase(CLS_MODE_KMS_KEY, clsKeyMode)) {
            log.info("{} CLS not in kms_key mode", dbInstanceName);
            return;
        }
        String encryptionKey = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, dbInstanceName, CLS_ENCRYPTION_KEY_ID);
        if (StringUtils.isBlank(encryptionKey)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DB_CLS_STATUS);
        }
        checkKmsKeyValid(requestId, replicaSet, encryptionKey);
    }
}
