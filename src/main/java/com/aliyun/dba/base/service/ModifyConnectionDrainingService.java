package com.aliyun.dba.base.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.model.ConfigVipConfBody;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.ALB_CONNECTION_DRAINING_SWITCH;
import static com.aliyun.dba.base.support.MySQLParamConstants.ALB_CONNECTION_DRAINING_TIMEOUT;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Service
public class ModifyConnectionDrainingService{
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyConnectionDrainingService.class);
    @Resource
    public MysqlParamSupport paramSupport;
    @Resource
    public CustinsService custinsService;
    @Resource
    public DBaasMetaService metaService;
    @Resource
    public ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;

    @Resource
    public LinksApi linksApi;

    public Map<String, Object> modifyConnectionDraining(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        ReplicaSet replicaSet = null;
        try {
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //get all connections,and filter lvs type connection,do not support dns/physical connection
            if (replicaSet.getConnType() == null ||
                    replicaSet.getConnType().getValue().equalsIgnoreCase("dns") ||
                    (replicaSet.getConnType().getValue().equalsIgnoreCase("physical") && !ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(replicaSet.getCategory()))
            ) {
                logger.error(String.format("Current connection type is %s", replicaSet.getConnType().getValue()));
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_CONNTYPE);
            }
            //Connection config
            Integer drainTimeout = paramSupport.getDrainingTimeout(params);
            ConfigVipConfBody.ConnectionDrainEnum drainEnable = paramSupport.getDrainingEnable(params);
            logger.info(String.format("RequestId is %s, Modify connection draining,drainEnable is %s,drainTimeout is %s", requestId, drainEnable, drainTimeout));
            //call vip manager  modify connection draining
            //Serverless do not have connection in rds-instance,then ignore it
            if (!ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(replicaSet.getCategory().toLowerCase())){
                EndpointListResult endpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null);
                logger.info(String.format("RequestId is %s, listReplicaSetEndpoints result is %s", requestId, endpointListResult));
                List<Endpoint> EndpointList = endpointListResult.getItems().stream().filter(e -> e.getUserVisible() && (e.getVip() != null)).collect(Collectors.toList());
                for (Endpoint endpoint : EndpointList) {
                    //String custinsNameOrId, String ip, ConfigVipConfBody body, String requestId
                    try {
                        ConfigVipConfBody body = new ConfigVipConfBody();
                        body.connectionDrain(drainEnable);
                        body.connectionDrainTimeout(drainTimeout);
                        linksApi.configVipConf(replicaSet.getName(), endpoint.getVip(), body, requestId);
                    } catch (Exception e) {
                        logger.error(String.format("Modify connection draining failed,error info is %s", e.getMessage()));
                        return createErrorResponse(new Object[]{ResultCode.CODE_NOTFOUND, "IncorrectConnectionDrainingConfig", e.getMessage()});
                    }
                }
            }

            //Deal with the maxscale for  this cust instance
            String maxscaleInstanceName = custinsService.checkHaveMaxscaleService(replicaSet.getId().intValue());
            logger.info(String.format("RequestId is %s, maxscale instance name is %s",requestId, maxscaleInstanceName));
            if (maxscaleInstanceName != null) {
                //Do connection draining for maxscale's vip
                EndpointListResult maxscaleEndpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, maxscaleInstanceName, null, null, null, null);
                logger.info(String.format("RequestId is %s, maxscale endpoint list is %s",requestId, maxscaleEndpointListResult.getItems()));
                List<Endpoint> maxscaleEndpointList = maxscaleEndpointListResult.getItems().stream().filter(e -> e.getUserVisible() && (e.getVip() != null)).collect(Collectors.toList());
                for (Endpoint maxscaleEndpoint : maxscaleEndpointList) {
                    //String custinsNameOrId, String ip, ConfigVipConfBody body, String requestId
                    try {
                        ConfigVipConfBody body = new ConfigVipConfBody();
                        body.connectionDrain(drainEnable);
                        body.connectionDrainTimeout(drainTimeout);
                        linksApi.configVipConf(maxscaleInstanceName, maxscaleEndpoint.getVip(), body, requestId);
                    } catch (Exception e) {
                        logger.error(String.format("Modify connection draining failed,error info is %s", e.getMessage()));
                        return createErrorResponse(new Object[]{ResultCode.CODE_NOTFOUND, "IncorrectConnectionDrainingConfig", e.getMessage()});
                    }
                }
            }

            //Update cust instance's param ,set tag for connection draining
            SetConnectionDrainingParam(requestId, replicaSet.getName(), drainTimeout, drainEnable);


            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("MaxscaleName", maxscaleInstanceName);
            return data;

        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    public void SetConnectionDrainingParam(String requestId, String replicaSetName,Integer drainingTimeout, ConfigVipConfBody.ConnectionDrainEnum drainEnable) throws ApiException {
        //For RDS connection draining config
        ReplicaSetResource replicaSetResource = metaService.getDefaultClient().getReplicaSetBundleResource(requestId,
                replicaSetName);
        if (replicaSetResource == null) {
            return;
        }
        Map<String, String> labelsMap = replicaSetResource.getReplicaSet().getLabels();
        if (labelsMap != null) {
            if(!labelsMap.containsKey(ALB_CONNECTION_DRAINING_SWITCH)){
                labelsMap.put(ALB_CONNECTION_DRAINING_SWITCH, drainEnable.toString());
            }else {
                labelsMap.replace(ALB_CONNECTION_DRAINING_SWITCH, drainEnable.toString());
            }
            if(!labelsMap.containsKey(ALB_CONNECTION_DRAINING_TIMEOUT)){
                labelsMap.put(ALB_CONNECTION_DRAINING_TIMEOUT, drainingTimeout.toString());
            }else {
                labelsMap.replace(ALB_CONNECTION_DRAINING_TIMEOUT, drainingTimeout.toString());
            }
            metaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSetName, labelsMap);
        } else {
            logger.error(String.format("Current LabelMap is null"));
        }
    }
}
