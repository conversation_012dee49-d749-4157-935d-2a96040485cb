package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.AccountsDTO;
import com.aliyun.dba.dbs.dataobject.AccountsQuery;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.dbs.support.AccountPriviledgeType.PRIVILEDGE_NORMAL;
import static com.aliyun.dba.dbs.support.AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN;

@Service("mysqlAccountService")
public class MysqlAccountServiceImpl implements MysqlAccountService {

    private final static LogAgent logger = LogFactory.getLogAgent(MysqlAccountServiceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected AccountService accountService;

    @Autowired
    protected DbossApi dbossApi;

    @Autowired
    protected MycnfService mycnfService;

    @Autowired
    protected CustinsService custinsService;

    @Override
    public Map<String, Object> describeAccountListByDboss(CustInstanceDO custins1, Map<String, String> params) throws IOException, RdsException {
        if (custins1.isReadAndWriteLock()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }

        if (custins1.isMysql() && custins1.isDiskFullLock()
                && ((custins1.isCustinsDockerOnEcs() || custins1.isCustinsOnEcs()) ||
                custins1.isReadOrBackup())) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }


        if (!custins1.inAvailableStatus()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        List<Map<String, Object>> accounts = new ArrayList<>();
        String accountName = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCOUNT_NAME, "");
        String DBName = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_NAME, "");
        int pageNum = Integer.parseInt(mysqlParamSupport.getParameterValue(params, ParamConstants.PAGE_NUMBERS, "1"));
        int pageSize = Integer.parseInt(mysqlParamSupport.getParameterValue(params, ParamConstants.MAX_RECORDS_PER_PAGE, "500"));
        String bid = mysqlParamSupport.getBID(params);

        // 聚石塔实例需要过滤掉高权限账户和前缀为 s_* 的推送账户
        Boolean isRequestFromJST = CustinsSupport.isRequestFromJST(mysqlParamSupport.getParameterValue(params,
                ParamConstants.ACCESSID), bid);
        // 从元数据中查询高权限账号以及相关元数据
        AccountsDO superAcc = accountService.getAccount(custins1.getId(), DbsSupport.BIZ_TYPE_USER,
                AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue());
        boolean isFirstPageAndWithSuperAcc = pageNum == 1 && superAcc != null && Strings.isNullOrEmpty(accountName);
        boolean isQuerySuperacc = superAcc != null && accountName.equals(superAcc.getAccount());
        // 屏蔽mysql高权限账号逻辑账号目前统一走dboss查询 如果确认只有mysql使用这段逻辑后续可以考虑删除该逻辑
        if ((isFirstPageAndWithSuperAcc || isQuerySuperacc) && !custins1.isMysql()) {
            Map<String, Object> account = new HashMap<>();
            account.put("AccountID", superAcc.getId());
            account.put("AccountName", superAcc.getAccount());
            account.put("AccountStatus", superAcc.getStatus());
            account.put("DBInstanceName", custins1.getInsName());
            account.put("PrivExceeded", 0);
            account.put("Engine", CustinsSupport.getEngine(custins1.getDbType()));
            account.put("AccountDescription", superAcc.getComment());
            account.put("AccountType", 1);
            account.put("DBName", null);
            account.put("AccountPrivilege", "ALL");
            account.put("AccountPrivilegeDetail", "");
            account.put("PrivilegeType", superAcc.getPriviledgeType());
            account.put("PrivilegeStatus", null);
            String queryAccountPassword = mysqlParamSupport.getParameterValue(params, "QueryAccountPassword", "false");
            if (StringUtils.equalsIgnoreCase(queryAccountPassword, "true")) {
                account.put("AccountPassword", superAcc.getPassword());
            }
            accounts.add(account);
        }

        // 推送库/账号配置
        MycnfCustinstanceDO innerUserList = mycnfService.getMycnfCustinstance(custins1.getId(), RdsConstants.MYCNF_CUSTINS_KEY_INNER_USER_LIST);
        Set<String> excludeUserSet = new HashSet<>();
        if (null != innerUserList) {
            String[] innerAccountList = StringUtils.split(innerUserList.getParaValue(), ",");
            excludeUserSet.addAll(Arrays.asList(innerAccountList));
        }

        Map<String, String> filterAccountsMap = new HashMap<>();
        List<AccountsDO> adminAccounts = accountService.queryCustinsAccounts(custins1.getId(), null, null);
        Map<String, Integer> nameToPrivilegeType = new HashMap<>();
        for (AccountsDO adminAcc: adminAccounts) {
            nameToPrivilegeType.put(adminAcc.getAccount(), adminAcc.getPriviledgeType());
            if (PRIVILEDGE_SUPER_ALIYUN.getValue() != adminAcc.getPriviledgeType() &&
                    PRIVILEDGE_NORMAL.getValue() != adminAcc.getPriviledgeType()) {
                excludeUserSet.add(adminAcc.getAccount());
            }
        }

        String filterAccounts = String.join(",", excludeUserSet);

        filterAccountsMap.put("ACCOUNTS_NOT_IN", filterAccounts);

        // 过滤掉推送账户 聚石塔实例
        if (isRequestFromJST) {
            filterAccountsMap.put("ACCOUNTS_NOT_LIKE", "s_%");
        }

        List<Map<String, Object>> accountList = dbossApi.queryAccounts(
                custins1.getId(), accountName, DBName, (pageNum - 1) * pageSize, pageSize, JSONObject.toJSONString(filterAccountsMap));

        Map<String,Integer> accountCount = dbossApi.getAccountCount(String.valueOf(custins1.getId()),JSONObject.toJSONString(filterAccountsMap),RdsConstants.ROLETYPE_USER);
        Integer totalCount = 0;
        if(accountCount != null && !accountCount.isEmpty()){
            totalCount = accountCount.get("accounts");
        }

        // 历史问题，原代码有问题
        // 根据accountName精确查询accountList时，TotalCount依旧返回所有数据条数
        if (StringUtils.isNotEmpty(accountName)) {
            totalCount = CollectionUtils.isEmpty(accountList) ? 0 : 1;
        }

        for (Map<String, Object> acc: accountList) {
            String currentAccName = (String) acc.get("accountName");
            boolean isSuperAccount = (superAcc != null && currentAccName.equals(superAcc.getAccount()));


            Map<String, Object> account = new HashMap<>(16);

            int privilegeType = nameToPrivilegeType.get(currentAccName) != null
                    ? nameToPrivilegeType.get(currentAccName) : PRIVILEDGE_NORMAL.getValue();

            // 用户账户列表只允许返回高权限账户和普通账户
            if (PRIVILEDGE_SUPER_ALIYUN.getValue() != privilegeType
                    && PRIVILEDGE_NORMAL.getValue() != privilegeType) {
                continue;
            }
            // MSSQL user vpc instance and current account is sa
            account.put("PrivilegeType", privilegeType);
            account.put("AccountID", 0);
            account.put("AccountName", currentAccName);
            account.put("AccountStatus", DbsSupport.STATUS_ACTIVE);
            account.put("totalCount",totalCount);
            if(custins1.isPgsql() && custins1.isCustinsDockerOnEcs()) {
                account.put("AccountStatus", acc.get("status") != null ? (int) acc.get("status") : DbsSupport.STATUS_ACTIVE);
            }
            account.put("DBInstanceName", custins1.getInsName());
            account.put("PrivExceeded", acc.get("privExceeded"));
            account.put("Engine", CustinsSupport.getEngine(custins1.getDbType()));
            account.put("AccountDescription", acc.get("comment") != null ? acc.get("comment") : "");


            account.put("AccountType", isSuperAccount ? 1 : 0);
            //mysql高权限账号特殊处理
            if (isSuperAccount && custins1.isMysql()) {
                account.put("AccountID", superAcc.getId());
                account.put("AccountStatus", superAcc.getStatus());
                account.put("AccountDescription", superAcc.getComment());
                account.put("PrivilegeType", superAcc.getPriviledgeType());
                String queryAccountPassword = mysqlParamSupport.getParameterValue(params, "QueryAccountPassword", "false");
                if (StringUtils.equalsIgnoreCase(queryAccountPassword, "true")) {
                    account.put("AccountPassword", superAcc.getPassword());
                }
            }

            List<Map<String, String>> privileges = (List<Map<String, String>>) acc.get("privileges");
            if (!CollectionUtils.isEmpty(privileges)) {
                for (Map<String, String> priv : privileges) {
                    Map<String, Object> tmpAcc = new HashMap<>(account);
                    tmpAcc.put("DBName", priv.get("dbname"));
                    tmpAcc.put("AccountPrivilege", CheckUtils.getPrivilgeDesc(priv.get("privileges"), custins1.getDbType()));
                    tmpAcc.put("AccountPrivilegeDetail", priv.get("privileges"));
                    tmpAcc.put("PrivilegeStatus", DbsSupport.STATUS_ACTIVE);
                    accounts.add(tmpAcc);
                }
            } else {
                account.put("DBName", null);
                account.put("AccountPrivilege", "");
                account.put("AccountPrivilegeDetail", "");
                account.put("PrivilegeStatus", null);
                accounts.add(account);
            }
        }
        Map<String, Object> data = new HashMap<>();
        data.put("Accounts", accounts);
        data.put("ResultTypeKeyName", "NonDataFormat");
        return data;
    }

    @Override
    public Map<String, Object> describeAccountList(Map<String, String> params) throws RdsException {
        String bid = mysqlParamSupport.getBID(params);
        String uid = mysqlParamSupport.getUID(params);
        String accountName = mysqlParamSupport.getAccountName(params);
        String dbInsNameStr = mysqlParamSupport.getDBInstanceName(params);
        String[] dbInsNames = SupportUtils.splitToArray(dbInsNameStr, ",");
        String dbType = mysqlParamSupport.getAndChangeEngine(params);
        String dbName = mysqlParamSupport.getDBName(params);
        AccountsQuery accountsQuery = new AccountsQuery();
        accountsQuery.setBid(bid);
        accountsQuery.setUid(uid);
        accountsQuery.setDbName(dbName);
        accountsQuery.setDbType(dbType);
        accountsQuery.setAccountName(accountName);

        // 公开的查询账户接口允许用户查询普通账户&超级账户
        List<AccountPriviledgeType> priviledgeTypes = AccountPriviledgeType
                .getSuperPriviledgeTypes();
        priviledgeTypes.add(PRIVILEDGE_NORMAL);
        accountsQuery.setPriviledgeTypes(priviledgeTypes);


        List<AccountsDTO> subAccountsDTOs =null;
        if (dbInsNames.length > 0) {
            List<String> dbInsNamesList = Arrays.asList(dbInsNames);
            accountsQuery.setDbInsNames(dbInsNamesList);
        }

        List<AccountsDTO> accountsDTOs = accountService.getAccounts(accountsQuery,
                mysqlParamSupport.getAction(params));
        List<Map<String, Object>> accountMaps = new ArrayList<Map<String, Object>>();
        HashSet<Object> accountSize = new HashSet<>();
        for (AccountsDTO accountsDTO : accountsDTOs) {
            Map<String, Object> accountMap = new HashMap<String, Object>();
            accountMap.put("AccountID", accountsDTO.getId());
            accountMap.put("AccountName", accountsDTO.getAccount());
            accountMap.put("AccountStatus", accountsDTO.getStatus());
            accountMap.put("AccountDescription", accountsDTO.getComment());
            accountMap.put("PrivilegeStatus", accountsDTO.getRelStatus());
            accountMap.put("DBName", accountsDTO.getDbName());
            accountMap.put("DBInstanceName", accountsDTO.getDbInsName());
            accountMap.put("AccountPrivilege", accountsDTO.getAccountPrivilegeDesc());
            accountMap.put("PrivExceeded", 0);
            accountMap.put("AccountPrivilegeDetail", "");
            accountMap.put("PrivilegeType", accountsDTO.getPriviledgeType());
            accountMap.put(ParamConstants.ACCOUNT_TYPE,
                    AccountPriviledgeType
                            .isSuperPriviledgeType(accountsDTO.getPriviledgeType()) ? 1 : 0);
            accountMap.put("Engine", accountsDTO.getEngine());
            if("MySQL".equalsIgnoreCase(accountsDTO.getEngine()) || "MySQL".equalsIgnoreCase(dbType)){
                accountSize.add(accountsDTO.getId());
            }
            CustInstanceDO tempCustins = custinsService
                    .getCustInstanceByInsName(null, accountsDTO.getDbInsName());
            if (tempCustins.isMongoDB()) {
                accountMap.put("CharacterType", tempCustins.getCharacterType());
            }

            if (accountsDTO.getSqlwallSwitch() != null) {
                accountMap.put("SqlwallSwitch",
                        accountsDTO.getSqlwallSwitch().equals(1) ? "true" : "false");
                accountMap.put("SqlwallTimeoutEvent", accountsDTO.getSqlwalTimeoutEvent());
                accountMap.put("SqlwallInjectEvent", accountsDTO.getSqlwallInjectEvent());
            }
            accountMaps.add(accountMap);
        }
        //公共云mysql有账号分页需求兼容老账号模式实例(老账号模式并非真实分页) 加入totalCount字段
        Integer totalCount = accountSize.size();
        accountMaps.forEach(map -> map.put("totalCount",totalCount));
        Map<String, Object> data = new HashMap<>();
        data.put("Accounts", accountMaps);
        data.put("ResultTypeKeyName", "NonDataFormat");
        return data;
    }
}
