package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dts20200101.Client;
import com.aliyun.dts20200101.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;


@Service
@Slf4j
public class DTSService {

    @Resource
    private DBaasMetaService dbaasMetaService;

    @Resource
    private DTSClient dtsClient;

    public DescribeDtsJobDetailResponse describeDtsJobDetail(String regionId, String dtsInstanceId, String dtsJobId) throws Exception {
        Client client = dtsClient.getDtsClient(regionId);
        DescribeDtsJobDetailRequest describeDtsJobDetailRequest = new DescribeDtsJobDetailRequest();
        describeDtsJobDetailRequest.setDtsInstanceID(dtsInstanceId);
        describeDtsJobDetailRequest.setDtsJobId(dtsJobId);
        log.info("invoke describeDtsJobDetail, request : {}", JSONObject.toJSONString(describeDtsJobDetailRequest));
        DescribeDtsJobDetailResponse response = client.describeDtsJobDetail(describeDtsJobDetailRequest);
        log.info("describeDtsJobDetail response : {}", JSONObject.toJSONString(response));
        return response;
    }

    /**
     * 查询dts预检查的情况
     */
    public DescribePreCheckStatusResponse describePreCheckStatus(String regionId, String dtsInstanceId, String dtsJobId) throws Exception {
        Client client = dtsClient.getDtsClient(regionId);
        DescribePreCheckStatusRequest describePreCheckStatusRequest = new DescribePreCheckStatusRequest();
        describePreCheckStatusRequest.setDtsJobId(dtsJobId);
        describePreCheckStatusRequest.setJobCode("01");
        describePreCheckStatusRequest.setStructType("after");
        log.info("invoke describePreCheckStatus, request : {}", JSONObject.toJSONString(describePreCheckStatusRequest));
        DescribePreCheckStatusResponse response = client.describePreCheckStatus(describePreCheckStatusRequest);
        log.info("describePreCheckStatus response : {}", JSONObject.toJSONString(response));
        return response;
    }

    /**
     * 暂停DTS任务
     */
    public SuspendDtsJobResponse suspendDtsJob(String regionId, String dtsInstanceId, String dtsJobId) throws Exception {
        Client client = dtsClient.getDtsClient(regionId);
        SuspendDtsJobRequest suspendDtsJobRequest = new SuspendDtsJobRequest();
        suspendDtsJobRequest.setDtsInstanceId(dtsInstanceId);
        suspendDtsJobRequest.setDtsJobId(dtsJobId);
        suspendDtsJobRequest.setRegionId(regionId);

        log.info("invoke suspendDtsJob, request : {}", JSONObject.toJSONString(suspendDtsJobRequest));
        SuspendDtsJobResponse response = client.suspendDtsJob(suspendDtsJobRequest);
        log.info("suspendDtsJob response : {}", JSONObject.toJSONString(response));
        return response;
    }

    /**
     * 启动DTS任务
     */
    public StartDtsJobResponse startDtsJob(String regionId, String dtsInstanceId, String dtsJobId) throws Exception {
        Client client = dtsClient.getDtsClient(regionId);
        StartDtsJobRequest startDtsJobRequest = new StartDtsJobRequest();
        startDtsJobRequest.setDtsInstanceId(dtsInstanceId);
        startDtsJobRequest.setDtsJobId(dtsJobId);
        startDtsJobRequest.setRegionId(regionId);

        log.info("invoke startDtsJob, request : {}", JSONObject.toJSONString(startDtsJobRequest));
        StartDtsJobResponse response = client.startDtsJob(startDtsJobRequest);
        log.info("startDtsJob response : {}", JSONObject.toJSONString(response));
        return response;
    }

    /**
     * 修改DTS任务
     */
    public ModifyDtsJobResponse modifyDtsJob(String regionId, String dtsInstanceId, Map<String, Object> dbList, String reserved) throws Exception {
        Client client = dtsClient.getDtsClient(regionId);
        ModifyDtsJobRequest modifyDtsJobRequest = new ModifyDtsJobRequest();

        modifyDtsJobRequest.setDbList(dbList);
        modifyDtsJobRequest.setDtsInstanceId(dtsInstanceId);
        modifyDtsJobRequest.setRegionId(regionId);
        modifyDtsJobRequest.setReserved(reserved);
        modifyDtsJobRequest.setStructureInitialization(true);
        modifyDtsJobRequest.setDataInitialization(true);
        modifyDtsJobRequest.setDataSynchronization(true);

        log.info("invoke modifyDtsJob, request : {}", JSONObject.toJSONString(modifyDtsJobRequest));
        ModifyDtsJobResponse response = client.modifyDtsJob(modifyDtsJobRequest);
        log.info("modifyDtsJob response : {}", JSONObject.toJSONString(response));
        return response;
    }
}
