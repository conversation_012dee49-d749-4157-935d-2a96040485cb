package com.aliyun.dba.base.service;

import com.aliyun.dba.poddefault.action.support.GAD.Aes;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.accountlabel.model.v20200315.QueryCustomerLabelRequest;
import com.aliyuncs.accountlabel.model.v20200315.QueryCustomerLabelResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import org.springframework.beans.factory.annotation.Value;

/**
 * 参考：https://aliyuque.antfin.com/aone619864/hpo9hg/iyv94i#51dc75af
 */
public class CrmApi {


    private final  static String token = "b6360e69a2878e879b86f9dd45ebb8426b9ae19e2716fdc8f33ff63c40971acda8ec08a0cc1d4d25ff7cfc567bdab0c9";
    private static final String region = "cn-hangzhou";
    private static final String product = "AccountLabel";
    private static final String endpoint = "account-label-share.aliyuncs.com";

    private DefaultAcsClient client;

    public CrmApi(String accessKey, String accessSecret) {
        IClientProfile profile = DefaultProfile.getProfile(region, accessKey, accessSecret);
        try {
            DefaultProfile.addEndpoint(product, region, product,endpoint);
        } catch (ClientException e) {
            throw new RuntimeException("add default endpoint failed.", e);
        }
        client = new DefaultAcsClient(profile);
    }

    /**
     * 查询用户打标信息
     * @param uid Long
     * @return QueryCustomerLabelResponse
     */
    public QueryCustomerLabelResponse queryCustomerLabel(Long uid, String labelSeries) throws Exception {
        QueryCustomerLabelRequest queryCustomerLabelRequest = new QueryCustomerLabelRequest();
        queryCustomerLabelRequest.setConnectTimeout(10 * 1000);
        queryCustomerLabelRequest.setReadTimeout(10 * 1000);
        queryCustomerLabelRequest.setPK(uid);
        queryCustomerLabelRequest.setLabelSeries(labelSeries);
        queryCustomerLabelRequest.setToken(Aes.decryptPassword(token, RdsConstants.PASSWORD_KEY));
        return client.getAcsResponse(queryCustomerLabelRequest);
    }
}