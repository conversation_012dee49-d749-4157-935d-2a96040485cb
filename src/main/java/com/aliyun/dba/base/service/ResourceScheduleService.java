package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.aliyun.dba.base.common.consts.ResourceScheduleConsts;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 资源调度相关service
 * <AUTHOR>
 */
@Service
public class ResourceScheduleService {

    private static final LogAgent logger = LogFactory.getLogAgent(ResourceScheduleService.class);

    @Resource
    private UserService userService;

    @Resource
    private ResourceService resourceService;

    @Resource
    private CrmService crmService;

    private static final ImmutableList<String> REBUILD_ACTION_LIST =
            ImmutableList.of(
                    "rebuildslaveinstance"
            );

    /**
     * 从resource表获取客户等级-资源保障策略映射map
     *
     * @param userId
     * @param uid
     * @param key
     * @return
     */
    public Map<String, String> getResourceGuaranteeModelPolicyMap(Integer userId, String uid, String key) {
        try {
            if (userId == null && StringUtils.isEmpty(uid)) {
                logger.info("UserId and uid cannot both be empty. Skip add resource guarantee model policy.");
                return null;
            }

            //获取uid
            if (StringUtils.isEmpty(uid)) {
                UserDO userDO = userService.getUserDOByUserId(userId);
                if (userDO != null) {
                    if (StringUtils.isNotEmpty(userDO.getUid())) {
                        uid = userDO.getUid();
                    } else if (StringUtils.isNotEmpty(userDO.getLoginId())) {
                        uid = userDO.getLoginId().split("_")[1];
                    }
                }
            }
            logger.info("Get ResourceGuaranteeModel Policy Map for uid {}, user_id {}.", uid, userId);
            if (StringUtils.isEmpty(uid)) {
                logger.info("Uid is empty. Skip add resource guarantee model policy.");
                return null;
            }

            //获取客户等级-策略映射表, 根据客户等级选择不一样的资源申请策略
            ResourceDO resourceDO = getResourceByResKey(key);
            if (resourceDO == null || StringUtils.isEmpty(resourceDO.getRealValue())) {
                logger.info("Uid = {}. Get ResourceGuaranteeModel Policy Map is null.", uid);
                //没有配置客户等级与机型映射策略，返回旧逻辑
                return null;
            }
            Map<String, Map<String, String>> resGuaranteeModelUserMap = JSON.parseObject(resourceDO.getRealValue(), Map.class);
            logger.info("Resource_key = {}. Get ResourceGuaranteeModel Policy Map: {}", key, resGuaranteeModelUserMap);

            //判断客户等级
            String userLevelTag = getUserLevelTag(uid);

            Map<String, String> resGuaranteeLevelMap = resGuaranteeModelUserMap.get(userLevelTag);

            if (CollectionUtils.isEmpty(resGuaranteeLevelMap)) {
                logger.info("Uid = {}, resGuaranteeLevelMap is null.", uid);
                return null;
            }

            logger.info("Uid = {}, resGuaranteeLevelMap = {}", uid, resGuaranteeLevelMap);
            return resGuaranteeLevelMap;

        } catch (Exception e) {
            logger.error("Get ResourceGuaranteeModelPolicyMap error.", e);
            return null;
        }
    }


    /**
     * 根据action获取不同的resource_key，从而获取不同场景下的资源保障策略
     *
     * @return
     */
    public String getResGuaranteeModelMapKey() {
        String action = RequestSession.getAction();
        logger.info("Get ResGuaranteeModelMapKey for action {}.", action);
        if (StringUtils.isNotEmpty(action) && REBUILD_ACTION_LIST.contains(action.toLowerCase())) {
            return ResourceScheduleConsts.ResGuaranteeModelResourceKey.MYSQL_RES_GUARANTEE_MODEL_USER_MAP_FOR_REBUILD.getValue();
        }
        return ResourceScheduleConsts.ResGuaranteeModelResourceKey.MYSQL_RES_GUARANTEE_MODEL_USER_MAP.getValue();
    }


    private String getUserLevelTag(String uid) {
        String userLevelTag;
        if (isResGuaranteeModelSuperVip(uid)) {
            //打标vvip客户
            userLevelTag = ResourceScheduleConsts.UserLevelTag.USER_LEVEL_SUPER_VIP.getValue();
        } else if (isResGuaranteeModelVip(uid)) {
            userLevelTag = ResourceScheduleConsts.UserLevelTag.USER_LEVEL_VIP.getValue();
        } else {
            userLevelTag = ResourceScheduleConsts.UserLevelTag.USER_LEVEL_GENERAL_USER.getValue();
        }
        logger.info("Uid = {}, userLevelTag = {}", uid, userLevelTag);
        return userLevelTag;
    }




    /**
     * 判断是否资源保障模型(混开方案)中的vip客户
     *
     * @param uid
     * @return
     */
    private boolean isResGuaranteeModelVip(String uid) {
        //判断是否资源保障模型(混开方案)中的vip客户
        try {
            ResourceDO resourceDO = getResourceByResKey(ResourceScheduleConsts.ResGuaranteeModelResourceKey.MYSQL_RES_GUARANTEE_MODEL_VIP_GC_LEVEL.getValue());
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                String gcLevel = crmService.getGcLevel(uid);
                logger.info("uid {} gc level {}.", uid, gcLevel);
                return StringUtils.compare(gcLevel, resourceDO.getRealValue().toUpperCase()) >= 0;
            }
            return false;
        } catch (Exception e) {
            logger.error("isResGuaranteeModelVip error.", e);
            return false;
        }
    }


    /**
     * 判断uid是否为混开方案中打标的vvip客户
     *
     * @param uid
     * @return
     */
    private boolean isResGuaranteeModelSuperVip(String uid) {
        try {
            // 判断uid是否为混开方案中打标的vvip客户
            ResourceDO resourceDO = getResourceByResKey(ResourceScheduleConsts.ResGuaranteeModelResourceKey.MYSQL_RES_GUARANTEE_MODEL_SUPER_VIP_UID.getValue());
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                List<String> superVips = Arrays.asList(resourceDO.getRealValue().split(","));
                logger.info("uid {}, super vip list: {}.",uid, superVips);
                return superVips.contains(uid);
            }
            return false;
        } catch (Exception e) {
            logger.error("isResGuaranteeModelSuperVip error.", e);
            return false;
        }
    }


    /**
     * 避免频繁查询resource表, 将查询结果存入缓存
     *
     */
    private static final Cache<String, ResourceDO> resourceCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();


    private ResourceDO getResourceByResKey(String resKey) {
        try {
            return resourceCache.get(resKey, () -> {
                ResourceDO resourceDO = resourceService.getResourceByResKey(resKey);
                if (resourceDO == null) {
                    return new ResourceDO();  //empty resource
                }
                return resourceDO;
            });
        } catch (Exception e) {
            logger.error("getResourceByResKey for add ResGuaranteeModel Error.", e);
            return null;
        }
    }
}
