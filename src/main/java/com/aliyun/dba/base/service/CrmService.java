package com.aliyun.dba.base.service;

import com.aliyun.dba.support.dataobject.AccessAuthorizationDO;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.service.SignatureService;
import com.aliyun.dba.support.utils.AES;
import com.aliyuncs.accountlabel.model.v20200315.QueryCustomerLabelResponse;
import com.aliyuncs.exceptions.ClientException;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class CrmService {
    private static final Logger logger = LoggerFactory.getLogger(CrmService.class);
    private static final Integer OWNER_TYPE_ID = 116;
    @Resource
    private SignatureService signatureService;

    // 缓存信息
    private static final Cache<String, String> gcLevelCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();

    private static final Cache<String, CrmApi> clientCache = CacheBuilder.newBuilder()
            .maximumSize(3)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();


    private CrmApi getCrmApi() throws ExecutionException {
        return clientCache.get("client", () -> {
            List<AccessAuthorizationDO> accessAuthorizationDOs = signatureService.getAccessAuthorizations(
                    null,
                    OWNER_TYPE_ID,
                    0,
                    1);
            AccessAuthorizationDO accessAuthorization = accessAuthorizationDOs.get(0);
            String accessKey = accessAuthorization.getAccessId();
            String accessSecret = accessAuthorization.getAccessKey();
            accessSecret = AES.decryptPassword(accessSecret, RdsConstants.PASSWORD_KEY);
            return new CrmApi(accessKey, accessSecret);
        });
    }

    /**
     * 查询GC等级，只有GC3 ~ 7有数据，GC1 ~ 2默认也返回GC0，查询失败返回GC0
     * */
    public String getGcLevel(String uid) {
        try {
            return gcLevelCache.get(uid, () -> {
                QueryCustomerLabelResponse response = getCrmApi().queryCustomerLabel(Long.valueOf(uid),"gc_level");
                if (response.getData().size() > 0) {
                    return response.getData().get(0).getLabel().toUpperCase();
                } else {
                    return "GC0";
                }
            });
        } catch (Exception e) {
            logger.error("getGcLevel Error.", e);
            return "GC0";
        }
    }
}
