package com.aliyun.dba.base.service;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.property.RdsException;

import java.util.Map;

public interface MysqlEngineCheckService {
    boolean checkCloneBeforeUpgrade (Map<String, String> params,Integer custinsId) throws RdsException;

    boolean checkCanUpgradeToXDB(CustInstanceDO custins) throws RdsException;

    boolean checkUserClusterCustins(CustInstanceDO custins);

    boolean checkCanUpgradeMinorVersionWithMaxScale(CustInstanceDO custins, String targetReleaseDate);

    boolean checkMinorVersionWithMaxScale(CustInstanceDO custins);

}
