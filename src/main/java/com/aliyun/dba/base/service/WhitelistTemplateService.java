package com.aliyun.dba.base.service;

import com.aliyun.apsaradb.dbaasmetaapi.model.IPWhiteList;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLinkedWhitelistTemplate;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsIpWhiteListDO;
import com.aliyun.dba.custins.idao.InstanceLinkedWhitelistTemplateIDao;
import com.aliyun.dba.custins.idao.WhitelistTemplateIDao;
import com.aliyun.dba.custins.service.IpWhiteListService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WhitelistTemplateService {
    @Autowired
    DBaasMetaService dBaasMetaService;
    @Autowired
    private InstanceLinkedWhitelistTemplateIDao instanceLinkedWhitelistTemplateIDao;
    @Autowired
    private WhitelistTemplateIDao whitelistTemplateIDao;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    private static final Logger logger = LoggerFactory.getLogger(WhitelistTemplateService.class);
    /**
     * 关联实例与白名单模版对应的白名单（云盘）
     */
    public void createWhitelistTemplateRecord(String requestId, IPWhiteList[] templateList, int[] templateIdList, int userId, String dbInstanceName) throws Exception {
        if (templateIdList != null && templateList != null && templateIdList.length == templateList.length) {
            logger.info("createWhitelistTemplateRecord dbInstanceName:{}, templateIdList:{}",dbInstanceName,templateList);
            for (int i = 0; i < templateList.length; i++) {
                //这里为null可能的原因是用户在创建实例的过程中把template给释放了
                if (dBaasMetaService.getDefaultClient().getWhitelistTemplate(requestId, String.valueOf(userId), templateIdList[i]) != null) {
                    InstanceLinkedWhitelistTemplate templateInfo = new InstanceLinkedWhitelistTemplate();
                    templateInfo.setTemplateId(templateIdList[i]);
                    dBaasMetaService.getDefaultClient().createReplicaSetWhiteListTemplates(requestId, dbInstanceName, templateInfo);
                    dBaasMetaService.getDefaultClient().createReplicaSetWhiteIps(requestId, dbInstanceName, templateList[i]);
                }
            }
        }
    }
    /**
     * 关联实例与白名单模版对应的白名单（本地盘）
     */
    public void createWhitelistTemplateRecord(CustinsIpWhiteListDO[] templateList, int[] templateIdList, int userId, CustInstanceDO custins) {
        if (templateIdList != null && templateList != null && templateIdList.length == templateList.length) {
            logger.info("createWhitelistTemplateRecord dbInstanceName:{}, templateIdList:{}",custins.getInsName(),templateList);
            for (int i = 0; i < templateList.length; i++) {
                if (whitelistTemplateIDao.getWhitelistTemplate(userId, templateIdList[i]) != null) {
                    instanceLinkedWhitelistTemplateIDao.createWhitelistTemplateRelationToInstance(templateIdList[i], custins.getInsName());
                    templateList[i].setCustinsId(custins.getId());
                    ipWhiteListService.createCustinsIpWhiteList(templateList[i]);
                }
            }
        }
    }
}
