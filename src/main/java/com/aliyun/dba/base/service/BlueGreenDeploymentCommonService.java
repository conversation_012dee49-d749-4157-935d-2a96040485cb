package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.consts.BlueGreenDeploymentConsts;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.base.idao.ZoneIDao;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodType;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.api.SecurityGroupApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dts20200101.Client;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailRequest;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponse;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponseBody;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.utils.StringUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.common.consts.BlueGreenDeploymentConsts.*;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_ACTIVE;
import static com.aliyun.dba.custins.support.CustinsSupport.STORAGE_TYPE_LOCAL_SSD;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_MASTER;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_SLAVE;

@Service
public class BlueGreenDeploymentCommonService {
    private static final LogAgent logger = LogFactory.getLogAgent(BlueGreenDeploymentCommonService.class);


    private static final Map<String, Pair<String, ErrorCode>> UNSUPPORTED_OPTIONS = new HashMap<String, Pair<String, ErrorCode>>() {{
        // 磁盘压缩
        put(StorageCompressionHelper.COMPRESSION_MODE_KEY, Pair.of("Disk Compression is not allowed", ErrorCode.DISK_COMPRESSION_NOT_ALLOWED));
        // SSL
        put(CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL, Pair.of("SSL is not allowed", ErrorCode.SSL_NOT_ALLOWED));
        // 冷存归档
        put(ParamConstants.RDS_COLD_DATA_ENABLED, Pair.of("Cold Data Archiving is not allowed", ErrorCode.COLD_DATA_ARCHIVING_NOT_ALLOWED));
        // IO 加速
        put(ParamConstants.IO_ACCELERATION_ENABLED, Pair.of("IO Acceleration is not allowed", ErrorCode.IO_ACCELERATION_NOT_ALLOWED));
        // TDE 加密
        put(CustinsParamSupport.TDE_ENABLED, Pair.of("TDE Encryption is not allowed", ErrorCode.TDE_ENCRYPTION_NOT_ALLOWED));
    }};

    private static final Set<String> TRUTH_VALUES = new HashSet<String>() {
        {
            add("1");
            add("true");
            add("yes");
            add("on");
            add("enable");
            add("enabled");
            add("y");
            add("t");
        }
    };


    private static final String LOOSE_ENCDB = "loose_encdb";

    @Autowired
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;
    @Autowired
    private ZoneIDao zoneIDao;
    @Resource
    private MysqlParamSupport paramSupport;
    @Autowired
    protected DbossApi dbossApi;
    @Resource
    protected ResourceService resourceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private DBaasMetaService dBaasMetaService;
    @Autowired
    private InstanceIDao instanceIDao;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private EcsDBService ecsDBService;
    @Autowired
    private DockerCommonService dockerCommonService;
    @Autowired
    private ClusterService clusterService;
    @Resource
    protected KmsService kmsService;
    @Autowired
    protected MycnfService mycnfService;
    @Autowired
    private DtsClientService dtsClientService;
    @Resource
    private IpWhiteListService ipWhiteListService;
    @Resource
    private SecurityGroupApi securityGroupApi;

    public String checkAndCorrectRegionId(String regionId, CustInstanceDO custins) {
        String clusterName = custins.getClusterName();
        ClustersDO cluster = clusterService.getClusterByClusterName(clusterName);
        if (StringUtils.isEmpty(regionId) || !regionId.equalsIgnoreCase(cluster.getRegion())) {
            regionId = cluster.getRegion();
        }
        logger.info("The regionId is {}.", regionId);
        return regionId;
    }

    public Map<String, Object> getNewPrimaryConfigFromParams(Map<String, String> params) {
        Map<String, Object> newPrimaryConfig = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        params.forEach((key, value) -> {
            if (PRIMARY_CONFIG_PARAM_SET.contains(key) && params.get(key) != null) {
                logger.info("getNewPrimaryConfigFromParams, key : {}, value : {}", key, value);
                newPrimaryConfig.put(key, value);
            }
        });
        return newPrimaryConfig;
    }

    public List<Map<String, Object>> getNewRoConfigFromParams(Map<String, String> params) {
        List<Map<String, Object>> configList = new ArrayList<>();
        String readOnlyInstanceListStr = params.get(READONLY_INSTANCE_LIST);
        if (StringUtils.isEmpty(readOnlyInstanceListStr)) {
            logger.info("readOnlyInstanceList is null.");
            return null;
        }
        List<Map<String, Object>> readOnlyInstanceList = JSON.parseObject(readOnlyInstanceListStr, new TypeReference<List<Map<String, Object>>>() {
        });
        for (Map<String, Object> readOnlyInstance : readOnlyInstanceList) {
            Map<String, Object> config = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            params.forEach((key, value) -> {
                if (RO_CONFIG_PARAM_SET.contains(key) && readOnlyInstance.get(key) != null) {
                    config.put(key, value);
                }
            });
            configList.add(config);
        }
        return configList;
    }

    public List<Map<String, Object>> getNewReplicaConfigFromParams(Map<String, String> params) {
        List<Map<String, Object>> configList = new ArrayList<>();
        String replicaListStr = params.get(REPLICA_LIST);
        if (StringUtils.isEmpty(replicaListStr)) {
            logger.info("replicaListStr is null.");
            return null;
        }
        List<Map<String, Object>> replicaList = JSON.parseObject(replicaListStr, new TypeReference<List<Map<String, Object>>>() {
        });
        for (Map<String, Object> replica : replicaList) {
            Map<String, Object> config = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            params.forEach((key, value) -> {
                if (REPLICA_CONFIG_PARAM_SET.contains(key) && replica.get(key) != null) {
                    config.put(key, value);
                }
            });
            configList.add(config);
        }
        return configList;
    }

    public Map<String, Object> getNewProxyConfigFromParams(Map<String, String> params) {
        Map<String, Object> newProxyConfig = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        String proxyListStr = params.get(PROXY_INSTANCE);
        if (StringUtils.isEmpty(proxyListStr)) {
            logger.info("proxyListStr is null.");
            return null;
        }
        Map<String, Object> proxyInstanceConfig = JSON.parseObject(proxyListStr);
        proxyInstanceConfig.forEach((key, value) -> {
            if (PROXY_CONFIG_PARAM_SET.contains(key) && proxyInstanceConfig.get(key) != null) {
                newProxyConfig.put(key, value);
            }
        });
        return newProxyConfig;
    }


    public void checkBlueGreenTableCount(CustInstanceDO blueCustins, CustInstanceDO greenCustins, Map<String, Object> checkTableMap) throws RdsException, IOException {
        Map<String, Integer> blueMap = dbossApi.queryTableCount(Long.valueOf(blueCustins.getId()));
        Integer blueTableCount = blueMap.get("count");
        Map<String, Integer> greenMap = dbossApi.queryTableCount(Long.valueOf(greenCustins.getId()));
        Integer greenTableCount = greenMap.get("count");
        logger.info("table count blue : {}, green : {}", blueTableCount, greenTableCount);

        initCheckResultMap(checkTableMap, "Table-Count");
        if (!Objects.equals(blueTableCount, greenTableCount)) {
            updateCheckResult(checkTableMap, "the table count is different between blue and green instance.", ErrorCode.TABLE_COUNT_NOT_EQUAL);
        }
    }

    public void checkBlueGreenList(String dbInstanceName, List<String> blueInstanceCheckList, List<String> greenInstanceCheckList, String checkItemName, Map<String, Object> resultMap, ErrorCode errorCode) {
        String checkResult = "Pass";
        //蓝实例多于绿实例
        List<String> moreInBlue = new ArrayList<>();
        //蓝实例少于绿实例
        List<String> missingInBlue = new ArrayList<>();
        for (String blueInstance : blueInstanceCheckList) {
            if (!greenInstanceCheckList.contains(blueInstance)) {
                moreInBlue.add(blueInstance);
            }
        }
        for (String greenInstance : greenInstanceCheckList) {
            if (!blueInstanceCheckList.contains(greenInstance)) {
                missingInBlue.add(greenInstance);
            }
        }
        boolean checkPass = moreInBlue.isEmpty() && missingInBlue.isEmpty();
        if (!moreInBlue.isEmpty()) {
            String moreInBlueString = String.join(", ", moreInBlue);
            checkResult = checkItemName + "s in blue instance " + dbInstanceName + " are more than green instance. Extra " + checkItemName + " are " + moreInBlueString + ".";
        } else if (!missingInBlue.isEmpty()) {
            String missingInBlueString = String.join(", ", missingInBlue);
            checkResult = checkItemName + "s in blue instance " + dbInstanceName + " are less than green instance. Extra " + checkItemName + " are " + missingInBlueString + ".";
        }
        resultMap.put("checkItemName", checkItemName);
        resultMap.put("checkPass", checkPass);
        resultMap.put("errorCode", errorCode);
        resultMap.put("checkResult", checkResult);
    }

    public List<String> getDBs(CustInstanceDO custins, String dbName, Map<String, String> params) throws Exception {
        int pageNum = Integer.parseInt(paramSupport.getParameterValue(params, ParamConstants.PAGE_NUMBER, "1"));
        int pageSize = Integer.parseInt(paramSupport.getParameterValue(params, ParamConstants.PAGE_SIZE, "500"));
        List<Map<String, Object>> dbs = dbossApi.queryDBs(custins.getId(), dbName, (pageNum - 1) * pageSize, pageSize);
        List<String> dbList = new ArrayList<>();
        for (Map<String, Object> db : dbs) {
            dbName = String.valueOf(db.remove("dbname"));
            dbList.add(dbName);
        }
        return dbList;
    }

    public List<String> getAccounts(CustInstanceDO custins, String accountName, String dbName, Map<String, String> params) throws Exception {
        int pageNum = Integer.parseInt(paramSupport.getParameterValue(params, ParamConstants.PAGE_NUMBER, "1"));
        int pageSize = Integer.parseInt(paramSupport.getParameterValue(params, ParamConstants.PAGE_SIZE, "500"));
        List<Map<String, Object>> accounts = dbossApi.queryAccounts(custins.getId(), accountName, dbName, (pageNum - 1) * pageSize, pageSize);
        List<String> accountList = new ArrayList<>();
        for (Map<String, Object> account : accounts) {
            accountName = String.valueOf(account.remove("accountName"));
            accountList.add(accountName);
        }
        return accountList;
    }


    public void checkStatus(CustInstanceDO custInstanceDO, CustInstanceDO greenCustInstanceDO, Map<String, Object> checkStatusMap) {
        initCheckResultMap(checkStatusMap, "Status-Check");
        if (!CUSTINS_STATUS_ACTIVE.equals(custInstanceDO.getStatus()) || !CUSTINS_STATUS_ACTIVE.equals(greenCustInstanceDO.getStatus())) {
            updateCheckResult(checkStatusMap, "Current DB instance status should be active.", ErrorCode.UNSUPPORTED_STATUS_EXCEPT_ACTIVE);
        }
    }

    public void checkDts(String regionId, String aliUid, BlueGreenDeploymentRel blueGreenDeploymentRel, Map<String, Object> checkDtsStatusMap, Map<String, Object> checkDtsDelayMap) throws Exception {
        JSONObject dtsInfo = JSONObject.parseObject(blueGreenDeploymentRel.getDtsInfo());
        String dtsJobId = String.valueOf(dtsInfo.get(BlueGreenDeploymentConsts.DTS_JOB_ID));
        String dtsInstanceId = String.valueOf(dtsInfo.get(BlueGreenDeploymentConsts.DTS_INSTANCE_ID));
        Client dtsClient = dtsClientService.getDtsClient(regionId, aliUid);
        DescribeDtsJobDetailRequest describeDtsJobDetailRequest = new DescribeDtsJobDetailRequest();
        describeDtsJobDetailRequest.setDtsJobId(dtsJobId);
        describeDtsJobDetailRequest.setDtsInstanceID(dtsInstanceId);
        logger.info("invoke describeDtsJobDetail start, request : {}", JSONObject.toJSONString(describeDtsJobDetailRequest));
        DescribeDtsJobDetailResponse dtsJobDetail = dtsClient.describeDtsJobDetail(describeDtsJobDetailRequest);
        logger.info("invoke describeDtsJobDetail end. response : {}", JSONObject.toJSONString(dtsJobDetail));

        if (Objects.isNull(dtsJobDetail)) {
            logger.error("dtsJobDetail is null.");
            throw new Exception("dtsJobDetail is null.");
        }

        DescribeDtsJobDetailResponseBody dtsJobDetailBody = dtsJobDetail.getBody();
        String status = dtsJobDetailBody.getStatus();
        Long delay = dtsJobDetailBody.getDelay();
        logger.info("dtsJobDetail status:{} delay: {}", status, delay);

        initCheckResultMap(checkDtsStatusMap, "DTS-Status");
        if (!"synchronizing".equalsIgnoreCase(status)) {
            updateCheckResult(checkDtsStatusMap, "DTS status is not synchronizing.", ErrorCode.DTS_SYNC_STATUS_NOT_SUPPORTED);
        }

        initCheckResultMap(checkDtsDelayMap, "DTS-Delay");
        if (delay > 5000) {
            updateCheckResult(checkDtsDelayMap, "DTS delay is greater than 5 seconds.", ErrorCode.DTS_SYNC_DELAY_TOO_LARGE);
        }
    }

    public void checkIpWhiteList(CustInstanceDO custInstanceDO, CustInstanceDO greenCustInstanceDO, Map<String, Object> checkIpWhiteListMap) {
        List<CustinsIpWhiteListDO> blueIpList = ipWhiteListService.getCustinsIpWhiteList(custInstanceDO.getId(), null, null, null);
        List<CustinsIpWhiteListDO> greenIpList = ipWhiteListService.getCustinsIpWhiteList(greenCustInstanceDO.getId(), null, null, null);
        boolean equality = verifyIpWhiteListEquality(blueIpList, greenIpList);

        initCheckResultMap(checkIpWhiteListMap, "IpWhiteList");
        if (!equality) {
            updateCheckResult(checkIpWhiteListMap, "IpWhiteList is not consistent.", ErrorCode.INCONSISTENT_IP_WHITELISTS);
        }
    }

    public void checkSecurityGroupList(CustInstanceDO custInstanceDO, CustInstanceDO greenCustInstanceDO, Map<String, Object> checkSecurityGroupMap) throws RdsException {
        List<Map<String, String>> blueSecurityGroupList = securityGroupApi.getECSSGRel(custInstanceDO);
        List<Map<String, String>> greenSecurityGroupList = securityGroupApi.getECSSGRel(greenCustInstanceDO);
        boolean equality = verifySecurityGroupListEquality(blueSecurityGroupList, greenSecurityGroupList);
        initCheckResultMap(checkSecurityGroupMap, "SecurityGroup");
        if (!equality) {
            updateCheckResult(checkSecurityGroupMap, "SecurityGroup is not consistent.", ErrorCode.INCONSISTENT_SECURITY_GROUPS);
        }
    }

    private static void initCheckResultMap(Map<String, Object> checkSecurityGroupMap, final String checkItemName) {
        checkSecurityGroupMap.put("checkItemName", checkItemName);
        checkSecurityGroupMap.put("checkPass", true);
        checkSecurityGroupMap.put("checkResult", "Pass");
    }

    public void checkHasReadOnly(CustInstanceDO blue, CustInstanceDO green, Map<String, Object> checkResultMap) {
        initCheckResultMap(checkResultMap, "ReadOnly");
        List<CustInstanceDO> blueReadList = custinsService.getReadCustInstanceListByPrimaryCustinsId(blue.getId(), false);
        List<CustInstanceDO> greenReadList = custinsService.getReadCustInstanceListByPrimaryCustinsId(green.getId(), false);

        boolean hasBlueReadOnly = !CollectionUtils.isEmpty(blueReadList);
        boolean hasGreenReadOnly = !CollectionUtils.isEmpty(greenReadList);

        if (hasBlueReadOnly && hasGreenReadOnly) {
            updateCheckResult(checkResultMap, "Both blue and green instances have read-only instances.", ErrorCode.UNSUPPORTED_INSTANCE_WITH_READ_INSTANCE);
        } else if (hasBlueReadOnly) {
            updateCheckResult(checkResultMap, "The blue instance has read-only instances.", ErrorCode.UNSUPPORTED_INSTANCE_WITH_READ_INSTANCE);
        } else if (hasGreenReadOnly) {
            updateCheckResult(checkResultMap, "The green instance has read-only instances.", ErrorCode.UNSUPPORTED_INSTANCE_WITH_READ_INSTANCE);
        }
    }

    public void checkMaxScale(CustInstanceDO blue, CustInstanceDO green, Map<String, Object> checkResultMap) {
        initCheckResultMap(checkResultMap, "MaxScale");
        List<CustinsServiceDO> blueMaxScale = custinsService.getCustinsServicesByCustinsIdAndServiceRole(blue.getId(), "maxscale");
        List<CustinsServiceDO> greenMaxScale = custinsService.getCustinsServicesByCustinsIdAndServiceRole(green.getId(), "maxscale");

        boolean hasBlueMaxScale = !CollectionUtils.isEmpty(blueMaxScale);
        boolean hasGreenMaxScale = !CollectionUtils.isEmpty(greenMaxScale);
        if (hasBlueMaxScale && hasGreenMaxScale) {
            updateCheckResult(checkResultMap, "Both blue and green instances have maxscale.", ErrorCode.UNSUPPORTED_INSTANCE_WITH_MAXSCALE);
        } else if (hasBlueMaxScale) {
            updateCheckResult(checkResultMap, "The blue instance has maxscale.", ErrorCode.UNSUPPORTED_INSTANCE_WITH_MAXSCALE);
        } else if (hasGreenMaxScale) {
            updateCheckResult(checkResultMap, "The green instance has maxscale.", ErrorCode.UNSUPPORTED_INSTANCE_WITH_MAXSCALE);
        }
    }

    public void checkConnectionString(CustInstanceDO blue, CustInstanceDO green, Map<String, Object> checkResultMap) {
        initCheckResultMap(checkResultMap, "ConnectionString");
        // 获取蓝绿实例的连接地址列表
        List<CustinsConnAddrDO> blueLinkList = connAddrCustinsService.getCustinsConnAddrByCustinsId(blue.getId(), null, CustinsSupport.RW_TYPE_NORMAL);
        List<CustinsConnAddrDO> greenLinkList = connAddrCustinsService.getCustinsConnAddrByCustinsId(green.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

        // 筛选蓝绿实例的私网和公网连接地址
        List<CustinsConnAddrDO> bluePrivate = getConnAddrByNetType(blueLinkList, CustinsSupport.NET_TYPE_PRIVATE);
        List<CustinsConnAddrDO> bluePublic = getConnAddrByNetType(blueLinkList, CustinsSupport.NET_TYPE_PUBLIC);
        List<CustinsConnAddrDO> greenPrivate = getConnAddrByNetType(greenLinkList, CustinsSupport.NET_TYPE_PRIVATE);
        List<CustinsConnAddrDO> greenPublic = getConnAddrByNetType(greenLinkList, CustinsSupport.NET_TYPE_PUBLIC);

        // 比较私网连接地址
        if (!verifyConnAddrEquality(bluePrivate, greenPrivate)) {
            String resultMessage = "Private network connection string is inconsistent between blue and green instances.";
            updateCheckResult(checkResultMap, resultMessage, ErrorCode.INCONSISTENT_PRIVATE_CONNECTIONS);
        }

        // 比较公网连接地址
        if (!verifyConnAddrEquality(bluePublic, greenPublic)) {
            String resultMessage = "Public network connection string is inconsistent between blue and green instances.";
            updateCheckResult(checkResultMap, resultMessage, ErrorCode.INCONSISTENT_PUBLIC_CONNECTIONS);
        }

    }

    /**
     * 根据 netType 筛选连接地址
     */
    private List<CustinsConnAddrDO> getConnAddrByNetType(List<CustinsConnAddrDO> connAddrList, Integer netType) {
        if (connAddrList == null || connAddrList.isEmpty()) {
            return null;
        }
        return connAddrList.stream()
            .filter(connAddr -> netType.equals(connAddr.getNetType()))
            .collect(Collectors.toList());
    }

    /**
     * 比较两个连接地址是否一致 verifyConnAddrEquality
     */
    public boolean verifyConnAddrEquality(List<CustinsConnAddrDO> addrList1, List<CustinsConnAddrDO> addrList2) {
        if (addrList1 == addrList2) {
            return true;
        }
        // 如果两个列表长度不相等，则直接返回 false
        return addrList1 != null && addrList2 != null && addrList1.size() == addrList2.size();
    }

    private void updateCheckResult(Map<String, Object> checkResultMap, String resultMessage, ErrorCode errorCode) {
        checkResultMap.put("checkPass", false);
        checkResultMap.put("errorCode", errorCode);
        checkResultMap.put("checkResult", resultMessage);
    }



    /**
     * 比较两个白名单是否相同
     *
     * @param a
     * @param b
     * @return true:相同 false:不同
     */
    public boolean verifyIpWhiteListEquality(List<CustinsIpWhiteListDO> a, List<CustinsIpWhiteListDO> b) {

        if (a == b) {
            return true;
        }
        if (a == null || b == null || a.size() != b.size()) {
            return false;
        }

        // 转换为 Map 并比较
        Map<String, Set<String>> blueMap = convertIpWhiteListToMap(a);
        Map<String, Set<String>> greenMap = convertIpWhiteListToMap(b);

        return blueMap.equals(greenMap);
    }

    public Map<String, Set<String>> convertIpWhiteListToMap(List<CustinsIpWhiteListDO> ipWhiteListDOList) {
        return ipWhiteListDOList.stream()
                .collect(Collectors.toMap(
                        CustinsIpWhiteListDO::getGroupName,
                        ipWhiteListDO -> {
                            String ipWhiteList = ipWhiteListDO.getIpWhiteList();
                            return org.apache.commons.lang3.StringUtils.isEmpty(ipWhiteList)
                                    ? Collections.emptySet()
                                    : Arrays.stream(ipWhiteList.split(","))
                                    .map(String::trim)
                                    .collect(Collectors.toSet());
                        }
                ));
    }

    /**
     * 比较两个安全组是否相同
     *
     * @param a
     * @param b
     * @return true:相同 false:不同
     */
    public boolean verifySecurityGroupListEquality(List<Map<String, String>> a, List<Map<String, String>> b) {
        return ListUtils.isEqualList(a, b);
    }

    public Integer getQuantityLimit(String reskey) throws Exception {
        ResourceDO resource = resourceService.getResourceByResKey(reskey);
        if (ObjectUtils.isEmpty(resource) || org.apache.commons.lang3.StringUtils.isEmpty(resource.getRealValue())) {
            logger.error("requestId : {}, can not get resource by res key: {}", RequestSession.getRequestId(), reskey);
            throw new Exception("can not get resource by res key.");
        }
        return Integer.valueOf(resource.getRealValue());
    }

    /**
     * 切换时检查
     * */
    public void preCheckWhenSwitching(int insId) throws RdsException {
        // 不允许 有只读实例
        List<CustInstanceDO> readinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(insId, false);
        if (CollectionUtils.isNotEmpty(readinsList)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_READ_INSTANCE);
        }
        // 不允许有代理
        List<CustinsServiceDO> custinsServiceDOS = custinsService.getCustinsServicesByCustinsIdAndServiceRole(insId, "maxscale");
        if (CollectionUtils.isNotEmpty(custinsServiceDOS)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_MAXSCALE);
        }


    }

    /**
     * 创建时检查
     * */
    public void preCheckWhenCreating(String requestId, CustInstanceDO custInstance) throws Exception {
        // 检查主实例状态
        if (!custInstance.isActive()) {
            logger.error("Custins status is not active, current status is : " + custInstance.getStatus());
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }

        if (custInstance.getInsType() != 0) {
            logger.error("Custins type is not normal, current type is : " + custInstance.getInsType());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        // 不允许 有只读实例
        List<CustInstanceDO> readinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custInstance.getId(), false);
        if (CollectionUtils.isNotEmpty(readinsList)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_READ_INSTANCE);
        }
        // 不允许有代理
        List<CustinsServiceDO> custinsServiceDOS = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custInstance.getId(), "maxscale");
        if (CollectionUtils.isNotEmpty(custinsServiceDOS)) {
           throw new RdsException(ErrorCode.MAXSCALE_NOT_SUPPORT);
        }
        // 检查主实例是否已经有蓝绿关系
        BlueGreenDeploymentRel rel = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByBlueCustinsId(Long.valueOf(custInstance.getId()));
        if (rel != null) {
            logger.error("The instance already has a blue green deployment, deployment id : " + rel.getId());
            throw new RdsException(ErrorCode.BLUE_GREEN_DEPLOYMENT_ALREADY_EXISTS);
        }

        // 不支持集群版
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custInstance.getLevelId());
        if (MysqlParamSupport.isCluster(insLevel.getCategory())){
            throw new RdsException(ErrorCode.CLUSTER_CATEGORY_NOT_SUPPORTED);
        }
        // 不支持基础版
        if (MysqlParamSupport.isBasic(insLevel.getCategory())){
            throw new RdsException(ErrorCode.BASIC_CATEGORY_NOT_SUPPORTED);
        }
        // 检查是否是组合可用区实例(ClassicDispenseMode 组合可用区，MultiAVZDispenseMode主备可用区)
        CustinsParamDO param = custinsParamService.getCustinsParam(custInstance.getId(), "dispense_mode");
        if (param != null) {
            String dispenseMode = param.getValue();
            if ("ClassicDispenseMode".equalsIgnoreCase(dispenseMode) && !MysqlParamSupport.isBasic(insLevel.getCategory())) {
                logger.error("Not support ClassicDispenseMode instance.");
                throw new RdsException(ErrorCode.INVALID_DISPENSE_MODE);
            }
        }

		//	只支持 kindcode = 0,18
		if (custInstance.getKindCode() != 0 && custInstance.getKindCode() != 18) {
			logger.error("Not support kindcode.", custInstance.getId());
			throw new RdsException(ErrorCode.UNSUPPORTED_KIND_CODE);
		}

		//  pod  不支持  rund
		if (custInstance.getKindCode() == 18) {
			Replica replica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, custInstance.getInsName(), null, null, null, null).getItems().get(0);
			ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, replica.getId(), null);
			if (PodType.POD_ECS_RUND.getRuntimeType().equals(replicaResource.getVpod().getRuntimeType())){
                logger.error("Not support pod_rund.", custInstance.getId());
                throw new RdsException(ErrorCode.UNSUPPORTED_KIND_CODE);
            }
		}

		// 	不支持经典网络
		List<CustinsConnAddrDO> privateNet = connAddrCustinsService.getCustinsConnAddrByCustinsId(
			custInstance.getId(),
			CustinsSupport.NET_TYPE_PRIVATE,
			CustinsSupport.RW_TYPE_NORMAL);
		if (!privateNet.isEmpty()) {
			logger.error("Not support classicNetType", custInstance.getId());
			throw new RdsException(ErrorCode.INVALID_CLASSIC_NET_TYPE);
		}

        // 检查实例中是否有rdsdt_bluegreen（同步账号）相关的DLO（Trigger、Scheduler、Function等）
        List<Map<String, String>> res = dbossApi.queryPdoByBlueGreenAcc(Long.valueOf(custInstance.getId()));
        logger.info("queryPdoByBlueGreenAcc : {}", JSON.toJSONString(res));
        if (res != null && !res.isEmpty()) {
            logger.error("There are pdo associate with rdsdt_bluegreen in {} : {}.", custInstance.getInsName(), JSON.toJSONString(res));
            throw new RdsException(ErrorCode.PDO_ASSOCIATED_NOT_ALLOWED);
        }


        //  各种云盘能力
        List<CustinsParamDO> custinsParamList = custinsParamService.getCustinsParams(custInstance.getId(), Lists.newArrayList(UNSUPPORTED_OPTIONS.keySet()));
        if (CollectionUtils.isNotEmpty(custinsParamList)) {
            logger.info("start checking unsupported options");
            // 遍历参数列表，检查是否有不支持的配置值
            for (CustinsParamDO p : custinsParamList) {
                logger.info("Custins option: {}:{}", p.getName(), p.getValue());
                if (p.getValue() != null && TRUTH_VALUES.contains(p.getValue())) {
                    String errorMessage = UNSUPPORTED_OPTIONS.get(p.getName()).getLeft();
                    ErrorCode errorCode = UNSUPPORTED_OPTIONS.get(p.getName()).getRight();
                    logger.error("{} 实例ID: {}", errorMessage, custInstance.getId());
                    throw new RdsException(errorCode);
                }
            }
        }

        //  云盘加密 值不是 boolean 而是 string(CloudDisk)
        CustinsParamDO encryption = custinsParamService.getCustinsParam(custInstance.getId(), PodDefaultConstants.ENCRYPTION_TYPE_LABEL);
        if (encryption != null && PodDefaultConstants.ENCRYPTION_CLOUD_DISK_TYPE.equalsIgnoreCase(encryption.getValue())) {
            logger.error("CloudDisk Encryption is not allowed", custInstance.getId());
            throw new RdsException(ErrorCode.CLOUD_DISK_ENCRYPTION_NOT_ALLOWED);
        }

        //  列加密 查询mycnf_custinstance
        MycnfCustinstanceDO encdb = mycnfService.getMycnfCustinstance(custInstance.getId(), LOOSE_ENCDB);
        if (encdb != null && "on".equalsIgnoreCase(encdb.getParaValue())) {
            logger.error("Column encryption is not allowed", custInstance.getId());
            throw new RdsException(ErrorCode.COLUMN_ENCRYPTION_NOT_ALLOWED);
        }

        // 检查DB数量，不能为0
        Map<String, Object> cntMap = dbossApi.queryDBCount(custInstance.getId());
        logger.info("cntMap : {}", JSON.toJSONString(cntMap));
        int dbCount = cntMap != null ? (int) cntMap.get("totalCount") : 0;
        if (dbCount == 0) {
            logger.error("There is no db in custins.");
            throw new RdsException(ErrorCode.DB_COUNT_ZERO_NOT_ALLOWED);
        }

        List<Map<String, Object>> accountList = dbossApi.queryAccounts(custInstance.getId(), "root", null, 0, 1);
        if (accountList != null && !accountList.isEmpty()) {
            logger.error("Exists an account named root.");
            throw new RdsException(ErrorCode.UNSUPPORTED_SOURCE_DB_ROOT_ACCOUNT);
        }

    }

    public String generateBlueGreenDeploymentName() {
        String strRandom;
        String deploymentName = null;
        for (int i = 0; i < 10; i++) {
            strRandom = generateString(17);
            deploymentName = "bgr-" + strRandom;
            BlueGreenDeploymentRel rel = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByDeploymentName(deploymentName);
            if (rel == null) {
                logger.info("Blue Green Deployment Rel name is " + deploymentName);
                break;
            }
        }
        return deploymentName;
    }

    public String generateString(Integer length) {
        char[] genChar = new char[length];
        int[] flag = {0, 0};
        int i = 0;
        while (flag[0] == 0 || flag[1] == 0 || i < length) {
            i = i % length;
            int f = (int) (Math.random() * 2 % 2);
            if (f == 1) {
                genChar[i] = (char) ('a' + Math.random() * 26);
            } else {
                genChar[i] = (char) ('0' + Math.random() * 10);
            }
            flag[f] = 1;
            i++;
        }
        return new String(genChar);
    }

    public JSONObject getGreenNormalInstanceConfig(CustInstanceDO custInstance,
                                                   String regionId,
                                                   Map<String, Object> newPrimaryConfig,
                                                   List<Map<String, Object>> newRoConfig,
                                                   Map<String, Object> newProxyConfig) throws Exception {
        // 获取蓝色实例配置参数
        Map<String, Object> bluePrimaryInstanceConfig = getBluePrimaryInstanceConfig(custInstance, regionId); // 蓝色主实例配置
        //List<Map<String, Object>> blueRoInstanceConfigList = getBlueRoInstanceConfigList(custInstance, regionId); // 蓝色只读实例配置
        //Map<String, Object> blueProxyInstanceConfig = getBlueProxyInstanceConfig(custInstance, regionId); // 蓝色代理实例配置
        // 将蓝色实例的参数与需要变更的配置合并，得到绿色实例的配置参数
        Map<String, Object> greenPrimaryInstanceConfig = getGreenPrimaryInstanceConfig(bluePrimaryInstanceConfig, newPrimaryConfig); // 绿色主实例配置
        //List<Map<String, Object>> greenRoInstanceConfigList = getGreenRoInstanceConfigList(blueRoInstanceConfigList, newRoConfig); // 绿色只读实例配置
        //Map<String, Object> greenProxyInstanceConfig = getGreenProxyInstanceConfig(blueProxyInstanceConfig, newProxyConfig); // 绿色代理实例配置
        // 特殊处理，如果是大版本升级场景，未指定绿实例的小版本，则使用当前最新小版本
        String greenVersion = String.valueOf(newPrimaryConfig.get("engineVersion"));
        String blueVersion = String.valueOf(bluePrimaryInstanceConfig.get("engineVersion"));
        if (!org.apache.commons.lang3.StringUtils.equals(blueVersion, greenVersion)) {
            greenPrimaryInstanceConfig.remove("minorVersion");
        }
        // 校验绿色实例参数
        //checkGreenInstanceConfig(greenPrimaryInstanceConfig, greenRoInstanceConfigList);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("greenPrimaryInstanceConfig", greenPrimaryInstanceConfig);
        //jsonObject.put("greenRoInstanceConfigList", greenRoInstanceConfigList);
        //jsonObject.put("greenProxyInstanceConfig", greenProxyInstanceConfig);
        return jsonObject;
    }

    public JSONObject getGreenClusterInstanceConfig(CustInstanceDO custInstance,
                                                    String regionId,
                                                    Map<String, Object> newPrimaryConfig,
                                                    List<Map<String, Object>> newReplicaConfig,
                                                    Map<String, Object> newProxyConfig) throws Exception {
        // 获取蓝色实例当前配置参数
        Map<String, Object> bluePrimaryInstanceConfig = getBluePrimaryInstanceConfig(custInstance, regionId); // 蓝色主实例配置
        List<Map<String, Object>> blueReplicaConfigList = getBlueReplicaConfigList(custInstance, regionId); // 蓝色节点配置
        Map<String, Object> blueProxyInstanceConfig = getBlueProxyInstanceConfig(custInstance, regionId); // 蓝色代理实例配置
        // 将蓝色实例的当前配置与需要变更的配置合并，得到绿色实例的配置参数
        Map<String, Object> greenPrimaryInstanceConfig = getGreenPrimaryInstanceConfig(bluePrimaryInstanceConfig, newPrimaryConfig); // 绿色主实例配置
        List<Map<String, Object>> greenReplicaConfigList = getGreenReplicaListConfig(blueReplicaConfigList, newReplicaConfig); // 绿色节点配置
        Map<String, Object> greenProxyInstanceConfig = getGreenProxyInstanceConfig(blueProxyInstanceConfig, newProxyConfig); // 绿色代理实例配置
        // 场景：集群版创建时，会先创建一个节点（同规格），其余节点在任务流中追加，因此需要特殊处理。
        // 集群版实例特殊处理：先去掉greenPrimaryInstanceConfig中的zoneIdSlave1，再从greenReplicaConfigList中找到一个和主实例规格相同的replica去配置zoneIdSlave1，并从greenReplicaConfigList中删除该replica
        clusterInstanceSpecialtreatment(greenPrimaryInstanceConfig, greenReplicaConfigList);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("greenPrimaryInstanceConfig", greenPrimaryInstanceConfig);
        jsonObject.put("greenReplicaConfigList", greenReplicaConfigList);
        jsonObject.put("greenProxyInstanceConfig", greenProxyInstanceConfig);
        return jsonObject;
    }

    public Map<String, Object> getBluePrimaryInstanceConfig(CustInstanceDO custins, String regionId) throws Exception {
        // 查询主实例的规格、可用区、磁盘类型、容量等信息
        Integer levelId = custins.getLevelId();
        InstanceLevelDO instanceLevel = instanceIDao.getInstanceLevelByLevelId(levelId);
        Long diskSizeMB = custins.getDiskSize();
        Long diskSizeGB = diskSizeMB / 1024;
        String dbInstanceStorageType;
        if (custins.getKindCode() == 18) {
            String storageType = replicaSetService.getReplicaSetStorageType(custins.getInsName(), RequestSession.getRequestId());
            if (Objects.equals(storageType, "cloud_auto")) {
                dbInstanceStorageType = CLOUD_ESSD_MAP.get(storageType);
            } else {
                dbInstanceStorageType = replicaSetService.getVolumePerfLevel(RequestSession.getRequestId(), custins.getInsName(), storageType);
                dbInstanceStorageType = CLOUD_ESSD_MAP.get(dbInstanceStorageType);
            }
        } else if (custins.getKindCode() == 0) {
            dbInstanceStorageType = STORAGE_TYPE_LOCAL_SSD;
        } else {
            logger.error("kindcode is not 0 or 18, please check.");
            throw new RdsException(ErrorCode.INVALID_INSTANCE);
        }
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(custins.getId(), CustinsSupport.NET_TYPE_VPC, CustinsSupport.RW_TYPE_NORMAL);
        if (custinsConnAddrList == null || custinsConnAddrList.size() == 0) {
            logger.error("Can not get custinsConnAddr, please check.");
            throw new RdsException(ErrorCode.INVALID_INSTANCE);
        }
        CustinsConnAddrDO custinsConnAddr = custinsConnAddrList.get(0);
        String VPCId = custinsConnAddr.getVpcId();
        String vswitchId = custinsService.getVswitchIdByVpcIpAndVpcId(custinsConnAddr.getVip(), custinsConnAddr.getVpcId());
        String zoneId = dockerCommonService.getZoneIdByCustins(custins);
        CustinsParamDO minorVersionDo = custinsParamService.getCustinsParam(custins.getId(), "minor_version");
        String minorVersion = minorVersionDo.getValue().split("_")[1];
        CustinsParamDO optimizedWritesInfo = custinsParamService.getCustinsParam(custins.getId(), "optimized_writes_info");
        JSONObject optimizedWritesJson;
        Boolean optimizedWrites = false;
        if (optimizedWritesInfo != null) {
            optimizedWritesJson = JSON.parseObject(optimizedWritesInfo.getValue());
            optimizedWrites = optimizedWritesJson.getBoolean("init_optimized_writes");
        }

        CustinsParamDO burstingEnabledParam = custinsParamService.getCustinsParam(custins.getId(), "burstingEnabled");
        boolean burstingEnabled = false;
        if (burstingEnabledParam != null) {
            burstingEnabled = Boolean.parseBoolean(burstingEnabledParam.getValue());
        }

        // 封装成Map返回
        Map<String, Object> config = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        config.put("dbInstanceName", custins.getInsName());
        config.put("regionId", regionId);
        config.put("dbInstanceClass", instanceLevel.getClassCode());
        config.put("dbInstanceStorage", diskSizeGB);
        config.put("engineVersion", custins.getDbVersion());
        config.put("minorVersion", minorVersion);
        config.put("engine", custins.getDbType());
        config.put("dbInstanceStorageType", dbInstanceStorageType);
        config.put("dbInstanceNetType", "Intranet");
        config.put("vSwitchId", vswitchId);
        config.put("vpcId", VPCId);
        config.put("zoneId", zoneId);
        config.put("instanceNetworkType", "VPC");
        if (!MysqlParamSupport.isBasic(instanceLevel.getCategory())) {
            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            InstanceDO slaveInstance = instanceList.stream().filter(instance -> instance.getRole().equals(1)).collect(Collectors.toList()).get(0);
            String zoneIdSlave1 = zoneIDao.getZoneIdBySiteAndRegion(slaveInstance.getSiteName(), regionId);
            config.put("zoneIdSlave1", zoneIdSlave1); // 备节点可用区
        }
        if (optimizedWrites) {
            config.put("optimizedWrites", "optimized");
        }
        config.put("burstingEnabled", burstingEnabled);
        return config;
    }

    public List<Map<String, Object>> getBlueRoInstanceConfigList(CustInstanceDO custInstance, String regionId) throws ApiException, RdsException {
        List<CustInstanceDO> roCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custInstance.getId(), false);
        if (roCustinsList == null || roCustinsList.size() == 0) {
            logger.info("There is not read only instance.");
            return new ArrayList<>();
        }
        roCustinsList = roCustinsList.stream().filter(roCustins -> roCustins.getInsType() == 3).collect(Collectors.toList());
        // 获取该实例下面的只读实例
        List<Map<String, Object>> configList = new ArrayList<>();
        for (CustInstanceDO roCustins : roCustinsList) {
            Integer levelId = roCustins.getLevelId();
            InstanceLevelDO instanceLevel = instanceIDao.getInstanceLevelByLevelId(levelId);
            Long diskSizeMB = roCustins.getDiskSize();
            Long diskSizeGB = diskSizeMB / 1024;
            String dbInstanceStorageType;
            if (roCustins.getKindCode() == 18) {
                String storageType = replicaSetService.getReplicaSetStorageType(roCustins.getInsName(), RequestSession.getRequestId());
                if (Objects.equals(storageType, "cloud_auto")) {
                    dbInstanceStorageType = CLOUD_ESSD_MAP.get(storageType);
                } else {
                    dbInstanceStorageType = replicaSetService.getVolumePerfLevel(RequestSession.getRequestId(), roCustins.getInsName(), storageType);
                    dbInstanceStorageType = CLOUD_ESSD_MAP.get(dbInstanceStorageType);
                }
            } else if (roCustins.getKindCode() == 0) {
                dbInstanceStorageType = STORAGE_TYPE_LOCAL_SSD;
            } else {
                logger.error("kindcode is not 0 or 18, please check.");
                throw new RdsException(ErrorCode.INVALID_INSTANCE);
            }
            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(roCustins.getId(), CustinsSupport.NET_TYPE_VPC, CustinsSupport.RW_TYPE_NORMAL);
            if (custinsConnAddrList == null || custinsConnAddrList.size() == 0) {
                logger.error("Can not get custinsConnAddr, please check.");
                throw new RdsException(ErrorCode.INVALID_INSTANCE);
            }
            CustinsConnAddrDO custinsConnAddr = custinsConnAddrList.get(0);
            String vpcId = custinsConnAddr.getVpcId();
            String vswitchId = custinsService.getVswitchIdByVpcIpAndVpcId(custinsConnAddr.getVip(), custinsConnAddr.getVpcId());
            String zoneId = getRoInstanceZoneId(roCustins);
            // 封装成Map返回
            Map<String, Object> config = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            config.put("roInstanceName", roCustins.getInsName());
            config.put("primaryInstanceName", custInstance.getInsName());
            config.put("regionId", regionId);
            config.put("dbInstanceClass", instanceLevel.getClassCode());
            config.put("dbInstanceStorage", diskSizeGB);
            config.put("engineVersion", roCustins.getDbVersion());
            config.put("engine", roCustins.getDbType());
            config.put("dbInstanceStorageType", dbInstanceStorageType);
            config.put("dbInstanceNetType", "Intranet");
            config.put("vSwitchId", vswitchId);
            config.put("vpcId", vpcId);
            config.put("zoneId", zoneId);
            config.put("instanceNetworkType", "VPC");
            configList.add(config);
        }
        return configList;
    }

    public List<Map<String, Object>> getBlueReplicaConfigList(CustInstanceDO custins, String regionId) throws ApiException {
        List<Map<String, Object>> configList = new ArrayList<>();
        List<Replica> replicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                        RequestSession.getRequestId(),
                        custins.getInsName(),
                        null,
                        null,
                        null,
                        null)
                .getItems()
                .stream()
                .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.SLAVE))
                .collect(Collectors.toList());
        for (Replica replica : replicas) {
            Map<String, Object> config = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            config.put("primaryInstanceName", custins.getInsName());
            config.put("replicaName", replica.getName());
            config.put("dbInstanceClass", replica.getClassCode());
            config.put("zoneId", replica.getZoneId());
            configList.add(config);
        }
        return configList;
    }

    public Map<String, Object> getBlueProxyInstanceConfig(CustInstanceDO custins, String regionId) throws RdsException, ApiException {
        Map<String, Object> config = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        // 获取Maxscale实例
        List<CustinsServiceDO> custinsServiceList = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
        if (CollectionUtils.isEmpty(custinsServiceList)) {
            logger.info("There is not maxscale instance.");
            return null;
        }
        Integer maxScaleCustinsId = LangUtil.getInteger(custinsServiceList.get(0).getServiceId());
        CustInstanceDO maxscale = custinsService.getCustInstanceByCustinsId(maxScaleCustinsId);
        CustinsParamDO persistentConnectionDO = custinsParamService.getCustinsParam(maxScaleCustinsId, "PersistentConnection");
        String persistentConnection = getPersistentConnectionConfig(persistentConnectionDO);
        InstanceLevelDO instanceLevel = instanceIDao.getInstanceLevelByLevelId(maxscale.getLevelId());
        String dbProxyInstanceType = instanceLevel.getIsolateHost() == 2 ? "exclusive" : "common";
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(maxscale.getId(), CustinsSupport.NET_TYPE_VPC, CustinsSupport.RW_TYPE_NORMAL);
        if (custinsConnAddrList == null || custinsConnAddrList.size() == 0) {
            logger.error("Can not get custinsConnAddr, please check.");
            throw new RdsException(ErrorCode.INVALID_INSTANCE);
        }
        CustinsConnAddrDO custinsConnAddr = custinsConnAddrList.get(0);
        String vpcId = custinsConnAddr.getVpcId();
        String vswitchId = custinsService.getVswitchIdByVpcIpAndVpcId(custinsConnAddr.getVip(), custinsConnAddr.getVpcId());
        // 本地代理实例，只要配置dbProxyInstanceNum，所有节点的cpu加起来除2
        if (custins.getKindCode() == 0) {
            CustInstanceDO physicalCustins = custinsService.getCustInstanceByParentId(maxscale.getId()).get(0);
            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(physicalCustins.getId());
            int cpuCores = 0;
            for (InstanceDO instance : instanceList) {
                cpuCores += instance.getCpuCores();
            }
            cpuCores = cpuCores / 2;
            config.put("dbProxyInstanceNum", cpuCores);
        }
        // 云盘实例，只要配置DBProxyNodes
        else if (custins.getKindCode() == 18) {
            List<Replica> replicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(RequestSession.getRequestId(),
                    maxscale.getInsName(),
                    null,
                    null,
                    null,
                    null).getItems();
            Map<String, Long> replicaCountByZoneId = replicaList.stream()
                    .collect(Collectors.groupingBy(Replica::getZoneId, Collectors.counting()));
            List<Map<String, String>> dbProxyNodes = new ArrayList<>();
            for (String zoneId : replicaCountByZoneId.keySet()) {
                Integer cpuCore = replicaList.stream()
                        .filter(replica -> zoneId.equals(replica.getZoneId()))
                        .map(Replica::getCpuCores)
                        .findFirst().get();
                Map<String, String> node = new HashMap<>();
                node.put("zoneId", zoneId);
                node.put("nodeCounts", String.valueOf(replicaCountByZoneId.get(zoneId)));
                node.put("cpuCores", String.valueOf(cpuCore));
                dbProxyNodes.add(node);
            }
            config.put("dbProxyNodes", dbProxyNodes);
        } else {
            logger.error("The instance type not support.");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
        config.put("proxyInstanceName", maxscale.getInsName());
        config.put("primaryInstanceName", custins.getInsName());
        config.put("regionId", regionId);
        config.put("instanceNetworkType", "VPC");
        config.put("persistentConnectionStatus", persistentConnection);
        config.put("vpcId", vpcId);
        config.put("vSwitchId", vswitchId);
        config.put("dbProxyInstanceType", dbProxyInstanceType);
        return config;
    }

    public Map<String, Object> getGreenPrimaryInstanceConfig(Map<String, Object> primaryInstanceConfig, Map<String, Object> newPrimaryConfig) {
        logger.info("primaryInstanceConfig : {}", JSON.toJSONString(primaryInstanceConfig));
        logger.info("newPrimaryConfig : {}", JSON.toJSONString(newPrimaryConfig));
        primaryInstanceConfig.putAll(newPrimaryConfig);
        logger.info("The green primary instance config is {}", JSON.toJSONString(primaryInstanceConfig));
        return primaryInstanceConfig;
    }

    public List<Map<String, Object>> getGreenRoInstanceConfigList(List<Map<String, Object>> roInstanceConfigList, List<Map<String, Object>> newRoConfig) {
        if (roInstanceConfigList == null) {
            logger.info("blueRoInstanceList is null, skip getGreenRoInstanceListConfig.");
            return new ArrayList<>();
        }
        if (newRoConfig == null) {
            logger.info("newRoConfig is null, return roInstanceConfigList.");
            return roInstanceConfigList;
        }
        Map<String, Map<String, Object>> newRoConfigMap = newRoConfig.stream()
                .collect(Collectors.toMap(
                        config -> ((String) config.get("roInstanceName")).toLowerCase(),
                        config -> config,
                        (existing, replacement) -> existing // 处理重复键的情况
                ));
        for (Map<String, Object> blueRoInstance : roInstanceConfigList) {
            String roInstanceName = (String) blueRoInstance.get("roInstanceName");
            if (roInstanceName != null) {
                Map<String, Object> newRoInstance = newRoConfigMap.get(roInstanceName.toLowerCase());
                if (newRoInstance != null) {
                    blueRoInstance.putAll(newRoInstance);
                }
            }
        }
        return roInstanceConfigList;
    }

    public List<Map<String, Object>> getGreenReplicaListConfig(List<Map<String, Object>> replicaConfigList, List<Map<String, Object>> newReplicaConfig) {
        if (replicaConfigList == null) {
            logger.info("blueNodeList is null, skip it.");
            return new ArrayList<>();
        }
        if (newReplicaConfig == null) {
            logger.info("newNodeConfig is null, return blueNodeList.");
            return replicaConfigList;
        }
        Map<String, Map<String, Object>> newReplicaConfigMap = newReplicaConfig.stream()
                .collect(Collectors.toMap(
                        config -> ((String) config.get("replicaName")).toLowerCase(),
                        config -> config,
                        (existing, replacement) -> existing // 处理重复键的情况
                ));
        for (Map<String, Object> replicaConfig : replicaConfigList) {
            String replicaName = (String) replicaConfig.get("replicaName");
            if (replicaName != null) {
                Map<String, Object> newReplica = newReplicaConfigMap.get(replicaName.toLowerCase());
                if (newReplica != null) {
                    replicaConfig.putAll(newReplica);
                }
            }
        }
        return replicaConfigList;
    }

    public Map<String, Object> getGreenProxyInstanceConfig(Map<String, Object> proxyInstanceConfig, Map<String, Object> newProxyConfig) {
        if (proxyInstanceConfig == null) {
            logger.info("blueProxyInstance is null, skip getGreenProxyInstanceConfig.");
            return new HashMap<>();
        }
        if (newProxyConfig == null) {
            logger.info("newProxyConfig is null, return proxyInstanceConfig.");
            return proxyInstanceConfig;
        }
        proxyInstanceConfig.putAll(newProxyConfig);
        // 下面这段逻辑：
        // - 创建新架构实例时，不需要dbProxyInstanceNum参数，需要DBProxyNodes
        // - 创建老架构实例时，不需要DBProxyNodes参数，需要dbProxyInstanceNum
        // 后期新老架构创建参数统一后，需要修改这段逻辑。
        // 如果绿实例是老架构实例，则需要删除DBProxyNodes
        if (newProxyConfig.get("dbProxyInstanceNum") != null) {
            proxyInstanceConfig.remove("DBProxyNodes");
        }
        // 如果绿实例是老架构实例，则需要删除DBProxyInstanceNum
        else if (newProxyConfig.get("DBProxyNodes") != null) {
            proxyInstanceConfig.remove("dbProxyInstanceNum");
        }
        logger.info("The green proxy instance config is {}", JSON.toJSONString(proxyInstanceConfig));
        return proxyInstanceConfig;
    }

    public String getPersistentConnectionConfig(CustinsParamDO persistentConnectionDO) {
        if (persistentConnectionDO == null) {
            return "Disabled";
        }
        if ("true".equalsIgnoreCase(persistentConnectionDO.getValue())) {
            return "Enabled";
        } else {
            return "Disabled";
        }
    }

    public void clusterInstanceSpecialtreatment(Map<String, Object> greenPrimaryInstanceConfig, List<Map<String, Object>> greenReplicaConfigList) {
        greenPrimaryInstanceConfig.remove("zoneIdSlave1");
        String primaryClassCode = String.valueOf(greenPrimaryInstanceConfig.get("dbInstanceClass"));
        int size = greenReplicaConfigList.size();
        int index = -1;
        for (int i = 0; i < size; i++) {
            Map<String, Object> replicaConfig = greenReplicaConfigList.get(i);
            String replicaClassCode = String.valueOf(replicaConfig.get("dbInstanceClass"));
            if (primaryClassCode.equalsIgnoreCase(replicaClassCode)) {
                index = i;
            }
        }
        Map<String, Object> replicaConfig = greenReplicaConfigList.remove(index);
        greenPrimaryInstanceConfig.put("zoneIdSlave1", replicaConfig.get("zoneId"));
    }

    public String getRoInstanceZoneId(CustInstanceDO roCustins) {
        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(roCustins.getId());
        int size = instanceList.size();
        // 主节点ZoneId
        InstanceDO masterInstance = instanceList.stream().filter(instance -> instance.getRole().equals(INSTANCE_ROLE_MASTER)).collect(Collectors.toList()).get(0);
        EcsHostDetailDO masterHost = ecsDBService.getEcsHostDetailDOByHostId(masterInstance.getHostId());
        String masterZoneId = masterHost.getZoneId();
        // 备节点ZoneId
        String slaveZoneId = null;
        if (size > 1) {
            InstanceDO slaveInstance = instanceList.stream().filter(instance -> instance.getRole().equals(INSTANCE_ROLE_SLAVE)).collect(Collectors.toList()).get(0);
            EcsHostDetailDO slaveHost = ecsDBService.getEcsHostDetailDOByHostId(slaveInstance.getHostId());
            slaveZoneId = slaveHost.getZoneId();
        }
        // 参考CreateReadOnlyDBInstance接口文档ZoneId参数说明
        String zoneId = null;
        if (slaveZoneId == null || masterZoneId.equalsIgnoreCase(slaveZoneId)) {
            zoneId = masterZoneId;
        } else {
            zoneId = masterZoneId + ":" + slaveZoneId;
        }
        return zoneId;
    }

    public void checkGreenInstanceConfig(Map<String, Object> greenPrimaryInstanceConfig, List<Map<String, Object>> greenRoInstanceConfigList) {
        if (greenRoInstanceConfigList.size() == 0) {
            return;
        }
        // 校验只读和主实例的版本是否一致，不一致按照主实例版本为准
        String engineVersion = String.valueOf(greenPrimaryInstanceConfig.get("engineVersion"));
        for (Map<String, Object> greenRoInstanceConfig : greenRoInstanceConfigList) {
            String roEngineVersion = String.valueOf(greenRoInstanceConfig.get("engineVersion"));
            if (!engineVersion.equalsIgnoreCase(roEngineVersion)) {
                greenRoInstanceConfig.put("engineVersion", engineVersion);
            }
        }
    }

    public void preCheckBeforeDeleteDeployment(CustInstanceDO greenCustins, String mode) throws RdsException {
        // 删除部署关系和实例，需要检查绿色实例、只读、代理的状态
        if ("deployment".equalsIgnoreCase(mode)) {
            // 检查主实例状态
            if (!greenCustins.isActive()) {
                logger.error("Custins status is not active, current status is : " + greenCustins.getStatus());
                throw new RdsException(ErrorCode.INVALID_STATUS);
            }
            //// 检查只读实例状态
            //List<CustInstanceDO> readinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(greenCustins.getId(), false);
            //if (readinsList != null && readinsList.size() > 0) {
            //    for (CustInstanceDO readins : readinsList) {
            //        if (!readins.isActive()) {
            //            logger.error("readins " + readins.getInsName() + " status is not active, current status is : " + greenCustins.getStatus());
            //            throw new RdsException(ErrorCode.INVALID_STATUS);
            //        }
            //    }
            //}
            //// 检查代理状态
            //List<CustinsServiceDO> custinsServiceDOS = custinsService.getCustinsServicesByCustinsIdAndServiceRole(greenCustins.getId(), "maxscale");
            //if (CollectionUtils.isNotEmpty(custinsServiceDOS)) {
            //    Integer maxScaleCustinsId = LangUtil.getInteger(custinsServiceDOS.get(0).getServiceId());
            //    CustInstanceDO maxscale = custinsService.getCustInstanceByCustinsId(maxScaleCustinsId);
            //    if (!maxscale.isActive()) {
            //        logger.error("maxscale " + maxscale.getInsName() + " status is not active, current status is : " + greenCustins.getStatus());
            //        throw new RdsException(ErrorCode.INVALID_STATUS);
            //    }
            //}
        }
    }
}