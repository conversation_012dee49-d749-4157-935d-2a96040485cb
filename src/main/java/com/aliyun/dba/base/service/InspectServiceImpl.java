package com.aliyun.dba.base.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.idao.PengineBlobInfo;
import com.aliyun.dba.base.idao.PengineIDao;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;

@Component
public class InspectServiceImpl implements InspectService {
	/**
	 * 设置体检任务为高优先级，否则任务会等待5-10s才被调度器选中
	 */
	private static final int VIP_INSPECT_TASK = 1;

	private static final String PENGINE_CONTEXT = "context";

	private static final String INSPECT_OUTPUT = "inspect_output";

	private static final LogAgent logger = LogFactory.getLogAgent(InspectServiceImpl.class);
	
	private static final String action = "InspectDBInstance";
	private static final String taskKey = "inspect";
	
	@Autowired
    protected TaskService taskService;
	
	@Autowired
	protected PengineIDao pengineDao;
	
	public class FailInspectCommandException extends RuntimeException {
		public FailInspectCommandException(String msg) {
			super(msg);
		}

		private static final long serialVersionUID = 1L;		
	}
	
	@Override
	public Integer triggerInspectCustinsTask(Integer operatorId, CustInstanceDO custins, Map<String, String> opts) throws Exception {
		// TODO Auto-generated method stub
		logger.info("operatorId={}, CustInstanceDO={}", operatorId, custins);
		String taskparam = this.buildTaskParams(custins, opts);
        TaskQueueDO task = new TaskQueueDO(action, operatorId, custins.getId(), TaskSupport.TASK_TYPE_CUSTINS, taskKey, taskparam);
        task.setPriority(VIP_INSPECT_TASK);
        
        this.taskService.createTaskQueue(task);

        return task.getId();
	}
	
	private String buildTaskParams(CustInstanceDO custins, Map<String, String> opts) {
		Map<String, Object> params = new HashMap<String, Object>(3);
		params.put("custins_id", custins.getId());
		params.put("custins_name", custins.getInsName());
		
		if (opts != null && params != null) {
			opts.forEach((k, v) -> params.put(k, v));
		}
		
		return JSON.toJSONString(params);
	}
	
	// 轮询pengine任务状态次数
	private static final int RETRY_TIMES = 30;
	// 每次轮询的时间间隔
	private static final long LOAD_OUTPUT_INTERVAL_MILLSECONDS = 1000;

	@Override
	public JSONObject reloadCommandOutput(Long taskId) throws Exception {
		logger.info("reload command output from pengine context with taskId={}", taskId);
		JSONObject res = null;
		for (int i = 0; i < RETRY_TIMES; i++) {
			if (res != null) {
				break;
			}
			
			// TODO: 如果下游pengine任务长时间running，比如ECS hang住了，或者别的什么原因，那么最长会导致api等到30秒
			// 一种优化思路是限制running -> done的时间，但这样的代价可能会导致pengine成功了，但最后结果没有返回，浪费了一个pengin任务的资源
			// 目前决定暂时关闭这个优化
			List<PengineBlobInfo> records = pengineDao.listPengineBlobInfoByTaskId(taskId);
			if (records != null && records.size() > 0) {
				logger.info("waiting task status={}", records.get(0).getTaskStatus());
				// 如果任务已经挂了，状态不是0:Pending, 1:Running, 2:Done, 就直接退出
				if (records.get(0).getTaskStatus() > 2) {
					logger.warn("task id={} has status={}, skip the inspect command by throwing exception", 
							taskId, records.get(0).getTaskStatus());
					throw new FailInspectCommandException(String.format("inspect command with task id=%d failed", taskId));
				}
				
				// 只有状态为2:Done的时候才可以回收执行结果
				if (records.get(0).getTaskStatus() == 2) {
					String ctxStr = records.get(0).getContext();
					JSONObject obj = JSON.parseObject(ctxStr);
					JSONObject ctxObj = obj.getJSONObject(PENGINE_CONTEXT);
					if (ctxObj.containsKey(INSPECT_OUTPUT)) {
						res = ctxObj.getJSONObject(INSPECT_OUTPUT);
						logger.info("command output={}", res);
					}
				}
			}
			
			Thread.sleep(LOAD_OUTPUT_INTERVAL_MILLSECONDS);
		}
	
		return res;
	}
}
