package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.ecs.idao.EcsUserInfoIDao;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AES;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;


@Slf4j
@Service
public class SlrCheckService {
    @Resource
    private EcsUserInfoIDao ecsUserInfoIDao;

    private static final String DEFULT_PRODUCTREGIONID = "cn-hangzhou";
    private static final String SERVICE_LINKED_ROLE_RDS_IMPORT = "AliyunServiceRoleForRdsImport";
    private static final String SLR_CONFIG_RDS_IMPORT = "rds_import";
    private static final String SLR_CONFIG_MYSQL = "mysql_service";
    public static final String SERVICE_LINKED_ROLE_MYSQL = "AliyunServiceRoleForRds";

    private static final Map<String, String> SERVICE_LINKED_ROLE_ERROR_DESC_MAP = new HashMap<String, String>() {{
        put(SERVICE_LINKED_ROLE_MYSQL, "Service linked role for RDS MySQL not exist.");
        put(SERVICE_LINKED_ROLE_RDS_IMPORT, "Service linked role for RDS Import not exist.");
    }};

    private static final Map<String, String> SERVICE_LINKED_ROLE_CONFIG_NAME_MAP = new HashMap<String, String>() {{
        put(SERVICE_LINKED_ROLE_MYSQL, SLR_CONFIG_MYSQL);
        put(SERVICE_LINKED_ROLE_RDS_IMPORT, SLR_CONFIG_RDS_IMPORT);
    }};

    private static final Map<String, DefaultAcsClient> instanceMap = new HashMap<>(2);

    public DefaultAcsClient getInstance(String roleName) {
        DefaultAcsClient instance = instanceMap.get(roleName);
        if (instance == null) {
            synchronized (SlrCheckService.class) {
                instance = instanceMap.get(roleName);
                if (instance == null) {
                    try {
                        instance = initClient(roleName);
                        instanceMap.put(roleName, instance);
                    } catch (Exception e) {
                        log.info("get Instance exception:{}", e.getMessage());
                        throw new RuntimeException("Failed to initialize AcsClient ", e);
                    }
                }
            }
        }
        return instance;
    }

    private DefaultAcsClient initClient(String roleName) throws ClientException {
        EcsUserInfoDO ecsUserInfoDO = Objects.requireNonNull(ecsUserInfoIDao.getEcsUserInfo(SERVICE_LINKED_ROLE_CONFIG_NAME_MAP.get(roleName)));
        log.info("ecsUserInfo:{}", JSON.toJSONString(ecsUserInfoDO));
        String productRegionId = DEFULT_PRODUCTREGIONID;
        String ak = AES.decryptPassword(ecsUserInfoDO.getAccessKeyId(),
                RdsConstants.PASSWORD_KEY);
        String sk = AES.decryptPassword(ecsUserInfoDO.getAccessKeySecretEncrypted(),
                RdsConstants.PASSWORD_KEY);

        DefaultProfile.addEndpoint("", productRegionId, "Sts", "sts-inner.aliyuncs.com");
        DefaultProfile.addEndpoint("", productRegionId, "ram-inner", "ram-inc-share.aliyuncs.com");
        IClientProfile profile = DefaultProfile.getProfile(productRegionId, ak, sk);
        return new DefaultAcsClient(profile);
    }

    private AssumeRoleWithServiceIdentityResponse assumeRoleWithServiceIdentity(String assumeRoleFor, String roleSessionName, String roleArn, Long duration) throws ClientException, ExecutionException {
        log.info("AssumeRoleWithServiceIdentity, assumeRoleFor: {}, roleSessionName: {}, roleArn: {}", assumeRoleFor, roleSessionName, roleArn);

        Assert.hasLength(assumeRoleFor);
        Assert.hasLength(roleSessionName);
        Assert.hasLength(roleArn);

        final AssumeRoleWithServiceIdentityRequest request = new AssumeRoleWithServiceIdentityRequest();
        request.setMethod(MethodType.POST);
        request.setProtocol(ProtocolType.HTTPS);
        request.setAcceptFormat(FormatType.JSON);
        request.setConnectTimeout(5000);
        request.setReadTimeout(5000);
        // like acs:ram::"+ uid +":role/AliyunServiceRoleForRds
        request.setRoleArn(roleArn);
        // like AliyunServiceRoleForRds
        request.setRoleSessionName(roleSessionName);
        request.setDurationSeconds(duration);
        // aka uid
        request.setAssumeRoleFor(assumeRoleFor);
        DefaultAcsClient instance = getInstance(roleSessionName);
        final AssumeRoleWithServiceIdentityResponse response = instance.getAcsResponse(request);

        if (response != null) {
            SlrCheckService.log.info("AssumeRoleWithServiceIdentity, response: {}", JSON.toJSONString(response));
        }
        return response;
    }

    /**
     * roleName like: AliyunServiceRoleForRds
     *
     * @param uid
     * @param requestId
     * @param roleName
     * @return
     */
    public Boolean checkServiceLinkedRole(String uid, String requestId, String roleName, Boolean throwException) throws RdsException {
        String roleArn = "acs:ram::" + uid + ":role/" + roleName;
        log.info("requestId {} check slr {} for uid : {}", requestId, uid, roleArn);
        boolean res = true;

        AssumeRoleWithServiceIdentityResponse response;
        try {
            response = assumeRoleWithServiceIdentity(uid, roleName, roleArn, 900L);
            AssumeRoleWithServiceIdentityResponse.Credentials stsCredentials = response.getCredentials();
            if (stsCredentials == null) {
                log.info("requestId {} AssumeRole Fail : {}", requestId, JSON.toJSONString(response));
                res = false;
            }
        } catch (ClientException e) {
            log.error("requestId {} AssumeRoleWithServiceIdentity ClientException: {}", requestId, e.getMessage());
            res = false;
        } catch (Exception e) {
            log.error("requestId {} AssumeRoleWithServiceIdentity UnknownException: {}", requestId, e.getMessage());
            res = false;
        }
        if (!res && throwException) {
            throw new RdsException(ErrorCode.SERVICE_LINKED_ROLE_NOT_EXIST,
                    SERVICE_LINKED_ROLE_ERROR_DESC_MAP.getOrDefault(roleName, "Service linked role not exist."));
        }
        log.info("requestId {} check slr success!", requestId);
        return res;
    }

    /**
     * 获取SLR凭证
     */
    public AssumeRoleWithServiceIdentityResponse.Credentials getCredentials(String uid, String roleName, Long duration) throws ClientException, ExecutionException {
        String roleArn = generateRoleArn(uid, roleName);
        return assumeRoleWithServiceIdentity(uid, roleName, roleArn, duration).getCredentials();
    }

    /**
     * 生成roleArn
     */
    private String generateRoleArn(String uid, String roleName) {
        return "acs:ram::" + uid + ":role/" + roleName;
    }

}