package com.aliyun.dba.base.service;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.ecs.idao.EcsUserInfoIDao;
import com.aliyun.dba.physical.action.service.DbossApiService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.rdscustom.action.support.RoleArnService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.service.SignatureService;
import com.aliyun.dba.support.utils.AES;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.kms.model.v20160120.AsymmetricSignRequest;
import com.aliyuncs.kms.model.v20160120.AsymmetricSignResponse;
import com.aliyuncs.kms.model.v20160120.AsymmetricVerifyRequest;
import com.aliyuncs.kms.model.v20160120.AsymmetricVerifyResponse;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.aliyuncs.utils.Base64Helper;
import com.aliyuncs.profile.DefaultProfile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.aliyun.dba.support.dataobject.AccessAuthorizationDO;
import org.springframework.stereotype.Service;
import com.aliyun.dba.support.api.DbossApi;
import javax.annotation.Resource;
import java.util.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_SEMI_SYNC;
import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.COMMON_PROVIDER_STS_ENDPOINT;

@Service
@Slf4j
public class TrustTableService {
    @Resource
    DbossApiService dbossApiService;
    @Autowired
    protected DbossApi dbossApi;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private KmsService kmsService;
    @Resource
    private CustinsParamService custinsParamService;
    @Autowired
    private EcsUserInfoIDao ecsUserInfoIDao;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    protected ResourceService resourceService;
    @Resource
    private RoleArnService roleArnService;
    private static final Logger logger = LoggerFactory.getLogger(TrustTableService.class);
    private static final String TRUST_DB_NAME = "rds_mysql_trust_database";
    private static final String TRUST_TABLE_FLAG = "trust_table_flag";
    private static final String TRUST_TABLE_KEY_ID = "trust_table_key_id";
    private static final String TRUST_TABLE_KEY_VERSION_ID = "trust_table_key_version_id";
    private static final String KMS_ALGORITHM = "kmsAlgorithm";

    public Map<String, Object> create(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        if (!custins.isMysql80()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_MAJOR_VERSION);
        }
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        try {
            Map<String, Object> data = new HashMap<String, Object>();
            //check kms
            CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), "trust_table_flag");
            if ( !custinsParamDO.getValue().equals("1") ) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_OPERATEION);
            }
            String dbName = params.get("dbname");
            String tableName = params.get("tablename");
            String columnNames = params.get("columnname");
            String tableNameAndColumn = tableName + ":" + columnNames;
            Map<String, Object> resultMap = dbossApiService.createTrustTable(custins.getId(),tableNameAndColumn,dbName);
            if (resultMap.get("error") != null) {
                logger.error("create {} trust table failed",tableName);
                throw new RdsException(ErrorCode.CREATE_TABLE_FAILED);
            }
            resultMap = dbossApiService.createTrustTrigger(custins.getId(),tableNameAndColumn,dbName);
            if (resultMap.get("error") != null) {
                logger.error("create {} trigger failed",tableName);
                throw new RdsException(ErrorCode.DBOSS_DATABASE_EXEC_SQL_ERROR);
            }
            data.put("message","successful create");
            return data;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
    public Map<String, Object> delete(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        //可以不需要列名
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        if (!custins.isMysql80()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_MAJOR_VERSION);
        }
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        Map<String, Object> data = new HashMap<String, Object>();
        try {
            CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), "trust_table_flag");
            if ( !custinsParamDO.getValue().equals("1") ) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_OPERATEION);
            }
            String dbName = params.get("dbname");
            String tableName = params.get("tablename");
            Map<String, Object> trustDBList = dbossApiService.deleteTrustTable(custins.getId(), dbName, tableName);
            if (trustDBList.get("error") != null) {
                logger.error("delete {} failed",tableName);
                throw new RdsException(ErrorCode.DBOSS_DATABASE_EXEC_SQL_ERROR);
            }
            data.put("code","200");
            data.put("custinsId",custins.getId());
            data.put("requestId",params.get("requestid"));
            return data;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
    public Map<String, Object> list(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        if (!custins.isMysql80()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_MAJOR_VERSION);
        }
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        Map<String, Object> data = new HashMap<String, Object>();
        try {
            CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), "trust_table_flag");
            if ( !custinsParamDO.getValue().equals("1") ) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_OPERATEION);
            }
            String dbName = params.get("dbname");
            Map<String, Object> trustDBList = dbossApiService.listTrustTable(custins.getId(),dbName);
            List<String> tables = new LinkedList<String>();
            for (String tableName : trustDBList.keySet()) {
                tables.add((String) trustDBList.get(tableName));
            }
            data.put("tables",tables);
            data.put("code","200");
            data.put("custinsId",custins.getId());
            data.put("requestId",params.get("requestid"));
            return data;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
    public Map<String, Object> modify(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        if (!custins.isMysql80()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_MAJOR_VERSION);
        }
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        String currentMinorVersion = custinsService.getCustInstanceCurrentMinorVersionByCustinsId(custins.getId());
        if (StringUtils.isNotBlank(currentMinorVersion)) {
            String currentDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(currentMinorVersion);
            ResourceDO resourceDO = resourceService.getResourceByResKey("TRUSTDB_SUPPORT_MINOR_VERSION_RELEASE_DATE");
            String releaseData = resourceDO.getRealValue();
            int diff = currentDate.compareTo(releaseData);
            if (diff < 0) {
                throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }
        }
        Map<String, Object> data = new HashMap<String, Object>();
        try {
            String keyId = params.get("keyid");
            String roleArn = params.get("rolearn");
            String keyVersionId = params.get("keyversionid");
            String uId = params.get("uid");
            String roleName = roleArn.split("/")[1];
            //check kms
            try{
                kmsService.checkKeyIsAvailable(custins,roleArn,keyId,uId);
            }catch (Exception e1){
                logger.error("byokCheckCreationKeyFail, custins"+custins.getId()+",keyId=" + keyId+",uid="+uId, e1);
                throw new RdsException(ErrorCode.KMS_API_ERROR);
            }
            //write management config
            custinsParamService.setCustinsParam(custins.getId(),"trust_table_rolename",roleName);
            custinsParamService.setCustinsParam(custins.getId(),"trust_table_key_id",keyId);
            custinsParamService.setCustinsParam(custins.getId(),"trust_table_key_version_id",keyVersionId);
            custinsParamService.setCustinsParam(custins.getId(),"trust_table_flag","1");
            if (params.get("kmsalgorithm") != null) {
                custinsParamService.setCustinsParam(custins.getId(), "kmsAlgorithm", params.get("kmsalgorithm"));
            } else {
                custinsParamService.setCustinsParam(custins.getId(), "kmsAlgorithm", "SM2DSA");
            }
            data.put("message","start trust table successful");
            data.put("code","200");
            data.put("custinsId",custins.getId());
            data.put("requestId",params.get("requestid"));
            return data;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public void checkParamValid(Map<String, String> params) throws RdsException {
        CustInstanceDO custins = mysqlParamSupport.getAndCheckCustInstance(params);
        if (!custins.isMysql80()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_MAJOR_VERSION);
        }

        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        //判断trust table功能是否开启（因为可以手动验证，不走stat）
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), TRUST_TABLE_FLAG);
        if ( !custinsParamDO.getValue().equals("1") ) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_OPERATEION);
        }
    }

    public String getParamValue(Integer cusId, String key) {
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(cusId, key);
        return custinsParamDO.getValue();
    }

    public Map<String, Object> sign(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        checkParamValid(params);
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            Map<String, Object> data = new HashMap<String, Object>();
            //获取参数
            String uId = params.get("uid");
            String regionId = params.get("regionid");
            String keyId = getParamValue(custins.getId(), TRUST_TABLE_KEY_ID);
            String keyVersionId = getParamValue(custins.getId(), TRUST_TABLE_KEY_VERSION_ID);
            String algorithm = getParamValue(custins.getId(), KMS_ALGORITHM);
            data.put("kmsAlgorithm",algorithm);

            //请求初始化
            AsymmetricSignRequest request = new AsymmetricSignRequest();
            request.setKeyId(keyId);
            request.setAlgorithm(algorithm);
            request.setKeyVersionId(keyVersionId);

            //没有需要加密的表，直接返回
            Map<String, Object> trustDBList = dbossApiService.listTrustTable(custins.getId(), TRUST_DB_NAME);
            if (trustDBList.isEmpty()) {
                return data;
            }

            //获取表名进行遍历签名
            IAcsClient client = getKMSClient(custins, uId, regionId, keyVersionId, requestId);
            for (String tableNum : trustDBList.keySet()) {
                String dbTable= (String) trustDBList.get(tableNum);
                String dbName = dbTable.split("\\.")[0];
                String tableName = dbTable.split("\\.")[1];
                Map<String, Object> batchHashResult = dbossApiService.getBatchHashResult(custins.getId(), dbName, tableName, "0");
                data.put("hashAlgorithm",batchHashResult.get("algorithm"));
                if (batchHashResult.get("hash_result") != null) {
                    String batchHash = (String) batchHashResult.get("hash_result");
                    //kms签名sign
                    int len = batchHash.length();
                    byte[] signByte = new byte[len / 2];
                    for (int i = 0; i < len / 2; i++) {
                        signByte[i] = (byte) Integer.parseInt(batchHash.substring(2 * i, 2 * i + 2), 16);
                    }
                    String Digest = Base64Helper.encode(signByte);
                    request.setDigest(Digest);
                    AsymmetricSignResponse response = null;
                    try {
                        //获取签名结果
                        response = client.getAcsResponse(request);
                    } catch (ServerException e) {
                        e.printStackTrace();
                    } catch (ClientException e) {
                        System.out.println("ErrCode:" + e.getErrCode());
                        System.out.println("ErrMsg:" + e.getErrMsg());
                        System.out.println("RequestId:" + e.getRequestId());
                        data.put("code", e.getErrCode());
                        data.put("message", e.getErrMsg());
                    }
                    dbossApiService.insertSign(custins.getId(), dbName, tableName, response.getValue());
                }
                //没有需要签名的值，直接跳过
            }
            return data;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
    public Map<String, Object> verify(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        checkParamValid(params);
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            Map<String, Object> data = new HashMap<String, Object>();
            //获取参数
            String uId = params.get("uid");
            String regionId = params.get("regionid");
            String dbName = params.get("dbname");
            String dbTableNames = params.get("tablename");
            String columnValue = params.get("columnvalue");

            String keyId = getParamValue(custins.getId(), TRUST_TABLE_KEY_ID);
            String keyVersionId = getParamValue(custins.getId(), TRUST_TABLE_KEY_VERSION_ID);
            String algorithm = getParamValue(custins.getId(), KMS_ALGORITHM);
            data.put("kmsAlgorithm",algorithm);

            //请求初始化
            AsymmetricVerifyRequest verifyRequest = new AsymmetricVerifyRequest();
            verifyRequest.setKeyId(keyId);
            verifyRequest.setAlgorithm(algorithm);
            verifyRequest.setKeyVersionId(keyVersionId);

            //连接dboss
            Map<String, Object> TableRow = dbossApiService.querySignValue(custins.getId(), dbName, dbTableNames, columnValue);
            if (TableRow.get("signValue") == null) {
                data.put("warn","no data to verify.");
                return data;
            }

            //函数化获取client
            IAcsClient client = getKMSClient(custins, uId, regionId, keyVersionId, requestId);
            //获取需要进行验证的表、列及其所在数据库
            data.put("hashAlgorithm",TableRow.get("algorithm"));
            if (dbName == null) {
                dbName = (String) TableRow.get("dbname");
                dbTableNames = (String) TableRow.get("dbtablename");
            }
            String signValue = (String) TableRow.get("signValue");
            String isSameHash = (String) TableRow.get("isSameHash");
            if (isSameHash.equals("0")) {
                logger.error("{} table {} hash value verify failed ", dbName, dbTableNames);
                throw new RdsException(ErrorCode.KMS_API_ERROR, "sign value verify failed");
            }
            String batchHash = (String) TableRow.get("batchHashResult");
            //验证签名
            int len = batchHash.length();
            byte[] signByte = new byte[len/2];
            for (int i = 0; i < len/2; i++) {
                signByte[i] = (byte) Integer.parseInt(batchHash.substring(2 * i ,2 * i + 2),16);
            }
            verifyRequest.setDigest(Base64Helper.encode(signByte));
            verifyRequest.setValue(signValue);
            try {
                AsymmetricVerifyResponse verifyResponse = client.getAcsResponse(verifyRequest);
                if (verifyResponse.getValue()) {
                    data.put("code","200");
                    data.put("message","value verify successful.");
                } else {
                    logger.error("{} table {} kms verify failed ", dbName, dbTableNames);
                    throw new RdsException(ErrorCode.KMS_API_ERROR, "sign value verify failed");
                }
            } catch (ServerException e) {
                e.printStackTrace();
            } catch (ClientException e) {
                System.out.println("ErrCode:" + e.getErrCode());
                System.out.println("ErrMsg:" + e.getErrMsg());
                System.out.println("RequestId:" + e.getRequestId());
                data.put("code",e.getErrCode());
                data.put("message",e.getErrMsg());
                throw new RdsException(ErrorCode.KMS_API_ERROR);
            }
            data.put("custinsId",custins.getId());
            data.put("requestId",params.get("requestid"));
            return data;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public IAcsClient getKMSClient(CustInstanceDO custins, String uId, String regionId, String keyVersionId, String requestId) throws RdsException{
        String secret = null;
        //获取临时访问的ak，sk和sts token
        EcsUserInfoDO ecsUserInfoDO = ecsUserInfoIDao.getEcsUserInfoList("rds_service").get(0);
        String accessKeyId = AES.decryptPassword(ecsUserInfoDO.getAccessKeyId(),
                RdsConstants.PASSWORD_KEY);
        String accessKeySecret =
                AES.decryptPassword(ecsUserInfoDO.getAccessKeySecretEncrypted(),
                        RdsConstants.PASSWORD_KEY);
        IClientProfile profile0 = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client0 = new DefaultAcsClient(profile0);
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), "trust_table_rolename");
        String roleName = custinsParamDO.getValue();
        String roleArn = "acs:ram::" + uId.toString() + ":role/" + roleName;
        String ststoken = null;
        try {
            AssumeRoleWithServiceIdentityRequest request = new AssumeRoleWithServiceIdentityRequest();
            request.setRoleArn(roleArn);
            request.setRoleSessionName(roleName);
            request.setAssumeRoleFor(uId.toString());
            String stsEndpoint = roleArnService.getStsEndpoint(requestId, regionId);
            if (StringUtils.isNotEmpty(stsEndpoint)) {
                log.info("set sts sys endpoint: {}", stsEndpoint);
                request.setEndpoint(stsEndpoint);
            }
            AssumeRoleWithServiceIdentityResponse response = client0.getAcsResponse(request);
            com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse.Credentials res = response.getCredentials();
            accessKeyId = res.getAccessKeyId();
            secret = res.getAccessKeySecret();
            ststoken = res.getSecurityToken();
        } catch (Exception ex){
            logger.error("create ststoken failed. e=" + ex.getMessage(), ex);
            throw new RdsException(ErrorCode.KMS_API_ERROR, ex.getMessage().replace("\r\n", " "));
        }
        //申请访问KMS的链路
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, secret, ststoken);
        IAcsClient client = new DefaultAcsClient(profile);
        return client;
    }
}
