package com.aliyun.dba.base.service;
/**
 * the class is used to deal parameters comes from rdsapi
 */

import com.alibaba.cobar.util.StringUtil;
import com.alibaba.fastjson.*;
import com.alicloud.apsaradb.resmanager.PbdResModel;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.PbdRespModel;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.adb_vip_manager_client.model.ConfigVipConfBody;
import com.aliyun.dba.bak.dataobject.BakTableMetaDO;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.CheckBaksetDO;
import com.aliyun.dba.bak.dataobject.LogPlanDO;
import com.aliyun.dba.bak.entity.BaksetMetaInfo;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.BizUseEnterpriseRegistry;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.commonkindcode.support.helper.TimezoneHelper;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.idao.WhitelistTemplateIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodType;
import com.aliyun.dba.resource.dataobject.RegionAVZonDO;
import com.aliyun.dba.resource.dataobject.RegionAVZoneQuery;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.dataobject.ProxyMetaPool;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.service.HaProxyService;
import com.aliyun.dba.support.utils.*;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.dba.user.support.UserSupport;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.sql.Time;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.zip.DataFormatException;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dbs.support.DbsSupport.BIZ_TYPE_SYS;
import static com.aliyun.dba.dbs.support.DbsSupport.BIZ_TYPE_USER;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_PRIORITY_COMMON;
import static com.aliyun.dba.resource.support.ResourceSupport.*;
import static com.aliyun.dba.support.property.ParamConstants.*;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_PRIMARY;
import static com.aliyun.dba.support.property.RdsConstants.REGEX_CONN_ADDR;

/***
 * <AUTHOR> 2018-07-26
 *
 * the class mainly used to deal with parameters come from rdsapi
 * and trans parameters into  Specified format
 * */
@Service
@Slf4j
public class MysqlParamSupport {

    /**
     * <AUTHOR>
     */
    private static final Logger logger = Logger.getLogger(MysqlParamSupport.class);

    protected static final String INTERNAL_SYSTEM = "internal_system";
    public static final String MIGRATING_AVZ = "migratingAvz";
    private static final String RDS_REGION_COMPRESSION_CONFIG = "RDS_REGION_COMPRESSION_CONFIG";
    private static final String RDS_UID_COMPRESSION_CONFIG = "RDS_UID_COMPRESSION_CONFIG";
    private static final String RDS_LOCAL_COMPRESSION_CONFIG = "local_compresion_ratio";

    public static final String DISPENSE_MODE = "dispense_mode";
    private static final Integer MAX_RESTORE_TABLE_COUNT = 100;

    //缓存以下内容
    private static List<String> dbTypeList = null;

    private static Map<String, List<String>> dbVersionMap = null;

    private static Map<String, String> connLastMap = null;

    private static Map<String, Map<String, String>> regionConnLastMap = null;
    private static Map<String, Integer> metaDBTimeZoneDiffMap = new ConcurrentHashMap<>();

    //缓存加速访问
    private static final Cache<String, String> resourceCache = CacheBuilder.newBuilder()
        .maximumSize(2048)
        .expireAfterWrite(10, TimeUnit.MINUTES)
        .build();

    /**
     * old class code don't display cpu cores.
     */
    private static Set<String> classCodeSetNotDisplayCpuCores = new HashSet<String>();
    private static long classCodeSetUpdateTimestamp = 0;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected UserService userService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    private BakService bakService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private IpWhiteListService ipWhiteListService;
    @Autowired
    private MycnfService mycnfService;
    @Autowired
    private CheckService checkService;
    @Autowired
    protected CloneEnabledCustinsService cloneEnabledCustinsService;
    @Autowired
    protected UserSupport userSupport;
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected DbossApi dbossApi;
    @Autowired
    protected HaProxyService haProxyService;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    private WhitelistTemplateIDao whitelistTemplateIDao;
    @Autowired
    protected TaskService taskService;
    @Autowired
    private ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    private CustinsParamService custinsParamService;

    public void setParameter(Map<String, String> actionParams, String paraName, String paraValue) {
        actionParams.put(paraName.toLowerCase(), paraValue);
    }

    public String getParameterValue(Map<String, String> actionParams, String param) {
        return actionParams.get(param.toLowerCase());
    }

    public String getParameterValue(Map<String, String> actionParams, String param, Object defaultValue) {
        param = param.toLowerCase();
        if (defaultValue != null && !(defaultValue instanceof String)) {
            defaultValue = defaultValue.toString();
        }
        return hasParameter(actionParams, param) ? actionParams.get(param) : (String)defaultValue;
    }

    /**
     * 获取指定的参数，如果缺少这个参数或者参数为空，抛出异常
     */
    public String getRequiredParameterValue(Map<String, String> actionParams, String param) throws RdsException {
        String value = actionParams.get(param.toLowerCase());
        if (StringUtils.isEmpty(value)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, String.format("Parameter '%s' is required.", param));
        }
        return value;
    }

    public boolean hasParameter(Map<String, String> actionParams, String param) {
        param = param.toLowerCase();
        return actionParams.containsKey(param) && StringUtils.isNotEmpty(actionParams.get(param));
    }

    public String getAction(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "action");
    }

    public Integer getPenginePolicyID(Map<String, String> actionParams) {
        String penginePolicyId = getParameterValue(actionParams, "PenginePolicy", "0");
        try {
            return Integer.parseInt(penginePolicyId);
        } catch (NumberFormatException e) {
            logger.error(e.getMessage(), e);
            return 0;
        }
    }

    public Integer getSlientHours(Map<String, String> actionParams) {
        String slientHours = getParameterValue(actionParams, PodDefaultConstants.SLIENT_HOURS, "24");
        try {
            return Integer.parseInt(slientHours);
        } catch (NumberFormatException e) {
            logger.error(e.getMessage(), e);
            return 24;
        }
    }

    public Boolean isSkipBackup (Map<String, String> actionParams) {
        String isSkipBackup = getParameterValue(actionParams, PodDefaultConstants.SKIP_BACKUP, "0");
        return "1".equals(isSkipBackup);
    }

    public boolean isMigrateZone(Map<String, String> actionParams) throws RdsException {
        String isMigrate = this.getParameterValue(actionParams, MIGRATING_AVZ, "false");
        return "true".equalsIgnoreCase(isMigrate);
    }

    public Map<String, String> getAndCheckMysqlCustomParams(Map<String, String> actionParams) throws RdsException {
        Map<String, String> mysqlCustomParams = new HashMap<>(2);
        String defaultTimeZone = getParameterValue(actionParams, ParamConstants.DB_PARAM_TIME_ZONE,"");
        String region = getParameterValue(actionParams, REGION_ID, "");
        if (StringUtils.isEmpty(defaultTimeZone)) {
            defaultTimeZone = TimezoneHelper.getDefaultTimeZone(region);
        }
        if (StringUtils.isNotBlank(defaultTimeZone)) {
            if (!TimezoneHelper.validator(defaultTimeZone)) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, ErrorCode.INVALID_PARAMETERS.getDesc() + "[Invalid defaultTimeZone:" + defaultTimeZone + "]");
            }
            mysqlCustomParams.put("default_time_zone", defaultTimeZone);
            log.info("param default_time_zone is set to {}", defaultTimeZone);
        }

        String isIgnoreCase = getParameterValue(actionParams, ParamConstants.DB_PARAM_IGNORE_CASE,"");
        if (StringUtils.isNotBlank(isIgnoreCase)){
            if (isIgnoreCase.equals("true")){
                mysqlCustomParams.put("lower_case_table_names", "1");
            } else {
                mysqlCustomParams.put("lower_case_table_names", "0");
            }
        }

        String customParameters = getParameterValue(actionParams, "CustomParameters", "");
        if (StringUtils.isNotBlank(customParameters)) {
            try {
                JSONObject jsonObject = JSON.parseObject(customParameters);
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    mysqlCustomParams.put(entry.getKey(), String.valueOf(entry.getValue()));
                }
            } catch (JSONException jsonException) {
                log.error("Failed to parse json", jsonException);
                throw new RdsException(ErrorCode.INVALID_PARAM, "invalid json CustomParameters . Failed to parse json " + customParameters);
            }
        }

        return mysqlCustomParams;
    }

    public ModifyReplicaSetResourceRequest.ModifyModeEnum getAndCheckModifyModeParam(Map<String, String> actionParams, String defaultVal) {
        String modeStr = getParameterValue(actionParams, "ModifyMode", defaultVal);
        return ModifyReplicaSetResourceRequest.ModifyModeEnum.fromValue(modeStr);
    }



    /**
     * 生成日志需要使用UUID,此处有问题
     */
    public String getUUID(Map<String, String> actionParams) {
        //return (String) request.getAttribute(RdsConstants.REQUEST_UUID);
        return this.getParameterValue(actionParams, RdsConstants.REQUEST_UUID);
    }

    public String getBID(Map<String, String> actionParams) {//万网等供应商ID，对应user表corp_name

        return getParameterValue(actionParams, "user_id");
    }

    public String getAndCheckBID(Map<String, String> actionParams) throws RdsException {
        String bid = getParameterValue(actionParams, "user_id");
        if (org.apache.commons.lang3.StringUtils.isEmpty(bid)) {
            throw new RdsException(ErrorCode.MISSING_USER_ID);
        }
        return bid;
    }

    public boolean ifDispatchTaskToGrandCanal(Map<String, String> actionParams) {
        String dispatchToGrandCanal = getParameterValue(
            actionParams, MySQLParamConstants.DISPATCH_TO_GRANDCANAL);
        return "1".equals(dispatchToGrandCanal);
    }

    public String getAndCheckStorageEngine(Map<String, String> actionParams) {
        return getParameterValue(actionParams, MySQLParamConstants.STORAGE_ENGINE);
    }


    public CustInstanceDO getAndCheckCustInstanceById(Map<String, String> actionParams, String CustinsIdKey)
        throws RdsException {
        Integer userId = getAndCheckUserId(actionParams);
        Integer custinsId = CheckUtils.parseInt(getParameterValue(actionParams, CustinsIdKey), null, null,
            ErrorCode.DBINSTANCE_NOT_FOUND);
        CustInstanceDO custins = custinsService.getCustInstanceByCustinsIdIgnoreDelete(userId, custinsId, 0);
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public Integer getAndCheckTargetUserId(Map<String, String> actionParams) throws RdsException {
        return getAndCheckUserId(actionParams, "targetuserid", "targetuid",
            ErrorCode.MISSING_TARGET_USER_ID, ErrorCode.MISSING_TARGET_UID);
    }

    public Integer getAndCheckUserId(Map<String, String> actionParams) throws RdsException {
        return getAndCheckUserId(actionParams, "user_id", "uid", ErrorCode.MISSING_USER_ID, ErrorCode.MISSING_UID);
    }

    private Integer getAndCheckUserId(Map<String, String> actionParams, String bidArgName, String uidArgName,
                                      ErrorCode missingBid,
                                      ErrorCode missingUid) throws RdsException {

        String action = this.getAction(actionParams);
        String bid = this.getParameterValue(actionParams, bidArgName);
        String uid = this.getParameterValue(actionParams, uidArgName);
        if (this.hasParameter(actionParams, ParamConstants.INNER_USER_ID)) {
            return CustinsValidator.getRealNumber(this.getParameterValue(actionParams, ParamConstants.INNER_USER_ID), -1);
        }
        return checkService.getAndCheckUserId(bid, uid, action);
    }

    public String getAndCheckLoginId(Map<String, String> actionParams) throws RdsException {
        getAndCheckUserId(actionParams);
        return this.getParameterValue(actionParams, "user_id") + "_" + this.getParameterValue(actionParams, "uid");
    }

    /**
     * 检查备份集ID是否有效
     *
     * @param custins
     * @param bakId
     * @return
     * @throws RdsException
     */
    public BakhistoryDO getAndCheckBakhistory(CustInstanceDO custins, Long bakId) throws RdsException {

        BakhistoryDO history = new BakhistoryDO();
        //GP只能取得LOG类型的backhistory记录,其他类型数据库取得DATA类型的backhistory记录
        if (custins.isGpdb()) {
            history = bakService.getGpdbBakhistoryByBackupSetId(custins.getId(), bakId);
        } else {
            history = bakService.getBakhistoryByBackupSetId(custins.getId(), bakId);
        }
        if (history == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        if (!history.isBakForInstance()) {//仅支持实例级别备份
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
        }
        if (!history.getStatus().equals("OK") || history.getIsAvail() == 0) {//加判断is avail
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETSTATUS);
        }
        if (custins.isMysql() && custins.isExcluse() && BAKWAY_MYSQLDUMP
            .equals(history.getBakWay())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
        }
        if (custins.isMysql()) {
            String bakWay;
            if (custins.isCustinsUseEcsSnapshot()) {
                bakWay = BAKWAY_SNAPSHOT;
            } else {
                bakWay = BAKWAY_XTRABACKUP;
            }
            if (!history.getBakWay().equals(bakWay)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
            }
        }
        if (BAKTYPE_INCREMENT.equals(history.getBakType())) {
            Integer countBakHis = bakService.countBakHisotryBeforeBakId(custins.getId(), bakId, BAKTYPE_FULL, 1);
            if (countBakHis <= 0) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
        }
        return history;
    }

    //真正用户的ID（各供应商生成），对应user表dept_name
    public String getUID(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "uid");
    }

    public Integer getNetType(Map<String, String> actionParams) throws RdsException {
        String netType = getParameterValue(actionParams, "DBInstanceNetType");
        if (netType == null) {
            return null;
        }

        Integer netTyeValue = (Integer)CustinsSupport.NET_TYPE_MAP.get(netType);
        if (netTyeValue == null) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCENETTYPE);
        } else {
            return netTyeValue;
        }
    }

    public String getResourceStrategy(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "ResourceStrategy");
    }

    public String getAndCheckUID(Map<String, String> actionParams) throws RdsException {
        String uid = getParameterValue(actionParams, "uid");
        if (org.apache.commons.lang3.StringUtils.isEmpty(uid)) {
            throw new RdsException(ErrorCode.MISSING_UID);
        }
        return uid;
    }

    public Boolean getAndCheckRetainClassic(Map<String, String> actionParams) throws RdsException {
        String retainClassic = getParameterValue(actionParams, ParamConstants.RETAIN_CLASSIC);
        if (org.apache.commons.lang3.StringUtils.isEmpty(retainClassic)) {
            // 默认为切换
            return false;
        } else if (retainClassic.equals("1")) {
            return true;
        } else if (retainClassic.equals("0")) {
            return false;
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
    }

    public String selectOneAssociatedDomain(String parentDomain, String resKey, String associatedDbType) {
        Gson gson = new Gson();
        List<String> associatedDomainList = resourceService.getResourceRealValueList(resKey);
        for (String associatedDomain : associatedDomainList) {
            HashMap<String, Map> associatedDomainMap =
                gson.fromJson(associatedDomain, new TypeToken<HashMap<String, Object>>() {
                }.getType());
            for (String key : associatedDomainMap.keySet()) {
                if (key.equals(parentDomain) && associatedDomainMap.get(key).containsKey(associatedDbType)) {
                    ArrayList<String> domainList = gson.fromJson(associatedDomainMap.get(key).get(associatedDbType)
                            .toString(),
                        new TypeToken<ArrayList<String>>() {
                        }.getType());
                    if (domainList.size() > 0) {
                        Collections.shuffle(domainList);
                        return domainList.get(0);
                    }
                }
            }
        }
        return null;
    }

    public Boolean getAndCheckValidateOnly(Map<String, String> actionParams) throws RdsException {
        String validateOnly = getParameterValue(actionParams, ParamConstants.VALIDATE_ONLY);
        if (org.apache.commons.lang3.StringUtils.isEmpty(validateOnly)) {
            // 默认为切换
            return false;
        } else if (validateOnly.equals("1")) {
            return true;
        } else if (validateOnly.equals("0")) {
            return false;
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
    }

    public Boolean supportVpcInLvs(CustInstanceDO custins) {
        if (custins.isSqlserver() && !custins.isCustinsOnEcs()) {
            return false;
        }
        if (!(custins.isMysql() && !custins.isCustinsOnEcs())) {
            return true;
        }

        if (custins.isMysql()) {
            return true;
        }

        try {
            // 全局开关，如果实例开关不存在，以全局开关为准
            return resourceSupport.getIntegerRealValue(ResourceKey.GLOBAL_SUPPORT_LVS_IN_VPC) == 1;
        } catch (RdsException e) {
            e.printStackTrace();
        }
        return false;
    }

    public Boolean getAndCheckSafeDelete(Map<String, String> actionParams) throws RdsException {
        String safeDelete = getParameterValue(actionParams, ParamConstants.SAFE_DELETE);
        if (org.apache.commons.lang3.StringUtils.isEmpty(safeDelete)) {
            // 默认为不保留切换
            return false;
        } else if (safeDelete.equals("1")) {
            return true;
        } else if (safeDelete.equals("0")) {
            return false;
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
    }

    public String getConnectionString(Map<String, String> actionParams) {//实例连接地址

        return getParameterValue(actionParams, "connectionstring");
    }
    public String getNewConnectionString(Map<String, String> actionParams) {//新实例连接地址
        return getParameterValue(actionParams, "newconnectionstring");
    }
    public String getPort(Map<String, String> actionParams) {//服务端口
        return getParameterValue(actionParams, "port");
    }
    public String getNewPort(Map<String, String> actionParams) {//新服务端口
        return getParameterValue(actionParams, "newport");
    }
    public String getDBInstanceName(Map<String, String> actionParams) {//实例名

        return getParameterValue(actionParams, "dbinstancename");
    }

    public String getPrimaryDBInstanceName(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "primarydbinstancename");
    }

    public String getDBInstanceID(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "dbinstanceid");
    }

    public Boolean getAutoCreateProxy(Map<String, String> actionParams) {

        return Boolean.valueOf(getParameterValue(actionParams, "AutoCreateProxy", "false"));
    }

    public Map<String, String> getHostNameMapping(Map<String, String> actionParams) throws RdsException {
        String hostNameMappingString = getParameterValue(actionParams, "HostNameMapping", null);
        if (StringUtils.isNotBlank(hostNameMappingString)) {
            try {
                return (Map<String, String>) JSON.parseObject(hostNameMappingString, Map.class);
            } catch (Exception e) {
                log.error("Parse HostNameMapping failed, value: {}", hostNameMappingString);
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
        }
        return null;
    }

    public String getDbNodes(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "dbnode");
    }

    public String getDbNodeIds(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "dbnodeid");
    }

    public String getSourceDBInstanceName(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "sourcedbinstancename");
    }

    public String getAndCheckDBInstanceName(Map<String, String> actionParams) throws RdsException {//实例名
        return CheckUtils.checkNullForInsName(getParameterValue(actionParams, "dbinstancename"));
    }

    public String getAndCheckisAllowDel(Map<String, String> actionParams) throws RdsException {
        String IsAllowDel = getParameterValue(actionParams, "IsAllowDel");
        if (Validator.isNull(IsAllowDel)) {
            throw new RdsException(ErrorCode.ISALLOWDEL_NOT_FOUND);
        }
        if (!IsAllowDel.equals("0") && !IsAllowDel.equals("1")) {
            throw new RdsException(ErrorCode.INVALID_ISALLOWDELDBDINSTANCE);
        }
        return IsAllowDel;
    }

    public String getAndCheckSourceDBInstanceName(Map<String, String> actionParams) throws RdsException {//来源实例名
        return CheckUtils.checkNullForInsName(getParameterValue(actionParams, "sourcedbinstancename"));
    }

    public String getAndCheckTargetDBInstanceName(Map<String, String> actionParams) throws RdsException {//目标实例名
        return CheckUtils.checkNullForInsName(getParameterValue(actionParams, "targetdbinstancename"));
    }

    public String getSourceDBInstanceID(Map<String, String> actionParams) throws RdsException {
        return getParameterValue(actionParams, "sourcedbinstanceid");
    }

    public String getAccountName(Map<String, String> actionParams) {//帐号
        return getParameterValue(actionParams, ParamConstants.ACCOUNT_NAME);
    }

    public String getAcountBizType(Map<String, String> actionParams) {//帐号类型

        return getParameterValue(actionParams, ParamConstants.ACCOUNT_BIZ_TYPE);
    }

    public String getAndCheckAccountName(Map<String, String> actionParams) throws RdsException {
        return CheckUtils.checkNullForAccountName(this.getAccountName(actionParams));
    }

    public String getAccountPassword(Map<String, String> actionParams) {//操作帐号的密码

        return getParameterValue(actionParams, "accountpassword");
    }

    public String getAndCheckAccountPassword(Map<String, String> actionParams) throws RdsException {
        String accountPassword = getParameterValue(actionParams, "accountpassword");
        return CheckUtils.checkValidForAccountPassword(accountPassword);
    }

    public String getTargetEngineVersion(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "TargetEngineVersion");
    }

    public String getTargetEngine(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "TargetEngine");
    }

    public String getMinorVersion(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "minorversion");
    }

    public String getMajorVersion(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "majorversion");
    }

    public String getAndCheckUpgradePolicy(Map<String, String> actionParams) throws RdsException {
        String upgradePolicy = getParameterValue(actionParams, ParamConstants.UPGRADE_POLICY);
        return CheckUtils.checkUpgradePolicy(upgradePolicy);
    }

    public String getAndCheckTargetCustinsCount(Map<String, String> actionParams) throws RdsException {
        String targetCustinsCount = getParameterValue(actionParams, ParamConstants.TARGET_CUSTINS_COUNT);
        return targetCustinsCount == null ? "1" : targetCustinsCount;
    }

    public String getInstanceId(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "InstanceID");
    }

    public Integer getInstanceCurrHostId(Map<String, String> actionParams) {
        String instanceIdStr = getInstanceId(actionParams);
        Integer instanceId = instanceIdStr == null ? 0 : Integer.parseInt(instanceIdStr);

        Integer currHostId = 0;
        if (instanceId > 0) {
            InstanceDO instance = instanceService.getInstanceByInsId(instanceId);
            if (instance != null) {
                Integer hostId = instance.getHostId();
                if (hostId != null && hostId > 0) {
                    currHostId = hostId;
                }
            }
        }

        return currHostId;
    }


    public List<Integer> getInstanceIdList(Map<String, String> actionParams) throws RdsException {
        List<Integer> instanceIdList = new ArrayList<Integer>();
        String instanceIdListSrc = getParameterValue(actionParams, "InstanceIdList");
        if (instanceIdListSrc == null || instanceIdListSrc.equals("")) {
            return instanceIdList;
        } else {
            for (String instanceIdSrc : instanceIdListSrc.split(",")) {
                try {
                    instanceIdList.add(Integer.parseInt(instanceIdSrc.trim()));
                } catch (NumberFormatException e) {
                    logger.error("Parsing getInstanceIdList(" + instanceIdListSrc + ") got error: " + e);
                    throw new RdsException(ErrorCode.INVALID_INSTANCE_ID_LIST);
                }
            }
            return instanceIdList;
        }
    }

    public String getUpgradeMode(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "upgrademode");
    }

    public String getRollbackMode(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "rollbackmode");
    }

    /**
     * 获取解密后的密码
     *
     * @return
     * @throws RdsException
     */
    public String getAndCheckDecryptedAccountPassword(Map<String, String> actionParams) throws RdsException {
        return getAndCheckDecryptedAccountPassword(actionParams, "accountpassword", "encryptaccountpassword");
    }

    public String getAndCheckAccountBizType(Map<String, String> actionParams, CustInstanceDO custIns)
        throws RdsException {
        String accountBizType = this.getAcountBizType(actionParams);
        // kepler_cstore不支持AccountBizType设置为"userbiz"
        if ((accountBizType == null
            || accountBizType.equals(BIZ_TYPE_USER))
            && !custIns.isKeplerCstore()) {
            return BIZ_TYPE_USER;
        } else if (accountBizType.equals(BIZ_TYPE_SYS) &&
            custIns.isCustinsOnDocker()) {
            return BIZ_TYPE_SYS;
        } else {
            throw new RdsException(ErrorCode.INVALID_ACCOUNT_BIZ_TYPE);
        }
    }

    public String getAndCheckDecryptedAccountPassword(Map<String, String> actionParams, String argAccountPassword,
                                                      String argEncryptAccountPassword)
        throws RdsException {
        String encryptAccountPassword = getParameterValue(actionParams, argEncryptAccountPassword);
        String decryptedAccountPassword = getParameterValue(actionParams, argAccountPassword);
        if (encryptAccountPassword != null) {
            String decryptKey = resourceSupport.getStringRealValue(ResourceKey.RESOURCE_PWD_TRANS_CRYPTKEY);
            decryptedAccountPassword = AES.decryptPassword(encryptAccountPassword, decryptKey);
        }

        return CheckUtils.checkValidForAccountPassword(decryptedAccountPassword);
    }

    /**
     * 获取解密后的密码，但不校验(可能为空)
     *
     * @param argAccountPassword
     * @param argEncryptAccountPassword
     * @return
     * @throws RdsException
     */
    public String getDecryptedAccountPasswordWithoutCheck(Map<String, String> actionParams, String argAccountPassword,
                                                          String argEncryptAccountPassword)
        throws RdsException {
        String encryptAccountPassword = getParameterValue(actionParams, argEncryptAccountPassword);
        String decryptedAccountPassword = getParameterValue(actionParams, argAccountPassword);
        if (encryptAccountPassword != null) {
            String decryptKey = resourceSupport.getStringRealValue(ResourceKey.RESOURCE_PWD_TRANS_CRYPTKEY);
            decryptedAccountPassword = AES.decryptPassword(encryptAccountPassword, decryptKey);
        }
        return decryptedAccountPassword;
    }

    /**
     * 获取 redis hmacEncryptAccountNewPassword
     *
     * @param hmacEncryptAccountNewPassword
     * @return
     * @throws RdsException
     */
    public String getHmacEncryptAccountNewPasswordForRedis(Map<String, String> actionParams,
                                                           String hmacEncryptAccountNewPassword) throws RdsException {
        String encryptAccountPassword = getParameterValue(actionParams, hmacEncryptAccountNewPassword);
        if (encryptAccountPassword != null && Pattern.compile("^[A-Fa-f0-9]{1,128}").matcher(encryptAccountPassword)
            .matches() && encryptAccountPassword.length() % 2 == 0) {
            return encryptAccountPassword;
        }
        throw new RdsException(ErrorCode.INVALID_ACCOUNTPASSWORD);
    }

    public String getDbInfo(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "dbinfo");
    }

    public String getAccountPrivilege(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "AccountPrivilege");
    }

    /**
     * 获取dbName列表，多个dbname通过逗号来分割
     *
     * @return
     */
    public List<String> getDBNames(Map<String, String> actionParams) {//数据库名称
        String dbname = this.getDBName(actionParams);
        List<String> dbNameList = Collections.emptyList();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dbname)) {
            String[] dbnames = SupportUtils.splitToArray(dbname, ",");
            dbNameList = Arrays.asList(dbnames);
        }
        return dbNameList;
    }

    public String getDBName(Map<String, String> actionParams) {

        return getParameterValue(actionParams, ParamConstants.DB_NAME);
    }

    public String getAndCheckDBName(Map<String, String> actionParams) throws RdsException {
        return CheckUtils.checkNullForDBName(this.getDBName(actionParams));
    }

    public Integer getAndCheckEvaluateNum(Map<String, String> actionParams) throws RdsException {
        String evaluateNumStr = getParameterValue(actionParams,ParamConstants.EVALUATE_NUM, "1");
        if (evaluateNumStr != null) {
            return Integer.parseInt(evaluateNumStr);
        }
        return null;
    }

    public Boolean getAndFillQuotaEvaluate(Map<String, String> params) throws RdsException {
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        String quotaEvaluateStr = getParameterValue(params, Quota_Evaluate, "0");
        //  * Fill some params for quota service request
        if (Objects.equals(quotaEvaluateStr, "1")) {
            String dbType = getAndCheckDBType(params, null);
            String dbVersion = getAndCheckDBVersion(params, dbType, true);
            String classCode = getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion, null, null);

            String region = CustinsParamSupport.getParameterValue(params, REGION);
            if (region == null) {
                region = CustinsParamSupport.getParameterValue(params, SUB_DOMAIN);
            }
            String regionId = CustinsParamSupport.getParameterValue(params, REGION_ID);
            String zoneId = CustinsParamSupport.getParameterValue(params, ParamConstants.ZONE_ID);

            // MultiAVZ
            MultiAVZExParamDO multiAVZExParam = new MultiAVZExParamDO();
            AvailableZoneInfoDO masterAvailableZoneInfo = new AvailableZoneInfoDO();
            masterAvailableZoneInfo.setZoneID(zoneId);
            masterAvailableZoneInfo.setRegion(region);
            masterAvailableZoneInfo.setIsMaster(true);
            AvailableZoneInfoDO slaveAvailableZoneInfo = new AvailableZoneInfoDO();
            slaveAvailableZoneInfo.setZoneID(zoneId);
            slaveAvailableZoneInfo.setRegion(region);
            slaveAvailableZoneInfo.setIsMaster(false);
            List<AvailableZoneInfoDO> availableZoneInfoList = new ArrayList<>();
            availableZoneInfoList.add(masterAvailableZoneInfo);
            availableZoneInfoList.add(slaveAvailableZoneInfo);
            multiAVZExParam.setAvailableZoneInfoList(availableZoneInfoList);
            params.put(ParamConstants.MULTI_AVZ_EX_PARAM, JSON.toJSONString(multiAVZExParam));

            // DispenseMode
            String dispenseMode = "1";
            if (InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(insLevel.getCategory())) {
                dispenseMode = "0";
            }
            params.put(ParamConstants.DISPENSE_MODE.toLowerCase(), dispenseMode);
        }
        return Objects.equals(quotaEvaluateStr, "1");
    }

    public Integer getAndCheckServiceType(Map<String, String> actionParams) throws RdsException {
        String serviceType = getParameterValue(actionParams, "servicetype", "0");
        return CheckUtils.checkServiceType(serviceType);
    }

    public String getAndCheckRegion(Map<String, String> actionParams) throws RdsException {//数据中心
        String region = getParameterValue(actionParams, "region");
        return CheckUtils.checkNullForRegion(region);
    }

    public Integer getAndCheckNetType(Map<String, String> actionParams) throws RdsException {
        String netType = getParameterValue(actionParams, "dbinstancenettype", "1");
        if (netType == null || !CustinsSupport.NET_TYPE_MAP.containsKey(netType)) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCENETTYPE);
        }
        return CustinsSupport.NET_TYPE_MAP.get(netType);
    }

    public Integer getAndCheckMaxscaleNetType(Map<String, String> actionParams) throws RdsException {
        String netType = getParameterValue(actionParams, "maxscalenettype", "1");
        if (netType == null || !CustinsSupport.NET_TYPE_MAP.containsKey(netType)) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALENETTYPE);
        }
        return CustinsSupport.NET_TYPE_MAP.get(netType);
    }

    public String getAndCheckMaxscaleConnectionString(Map<String, String> actionParams) throws RdsException {
        String connectionString = getParameterValue(actionParams, "maxscaleconnectionstring");
        if (connectionString == null) {
            return null;
        } else if (connectionString.length() > 40 || !Pattern.compile(REGEX_CONN_ADDR).matcher(connectionString)
            .matches()) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALE_CONNECTION_STRING);
        } else {
            return connectionString;
        }
    }

    public String getAndCheckMaxscaleInsName(Map<String, String> actionParams) throws RdsException {
        String maxscaleInsName = getParameterValue(actionParams, "maxscaleinsname", "");
        if (org.apache.commons.lang3.StringUtils.isBlank(maxscaleInsName)) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALE_INS_NAME);
        }
        return maxscaleInsName;
    }

    public Integer getAndCheckDistType(Map<String, String> actionParams) throws RdsException {
        String distType = getParameterValue(actionParams, "distributiontype");
        if (distType == null) {
            return null;
        }
        if (!CustinsSupport.RW_DIST_POLICY_MAP.containsKey(distType)) {
            throw new RdsException(ErrorCode.INVALID_DISTRIBUTIONTYPE);
        }
        return CustinsSupport.RW_DIST_POLICY_MAP.get(distType);
    }

    public Integer getAndCheckMaxDelayTime(Map<String, String> actionParams) throws RdsException {

        String maxDelayTime = getParameterValue(actionParams, "maxdelaytime");
        if (maxDelayTime == null) {
            return null;
        }
        return CheckUtils.parseInt(maxDelayTime, 0, 7200, ErrorCode.INVALID_MAX_DELAY_TIME);
    }

    public void CheckRWWeightFormat(Map<String, Object> rwWeightMap, List<String> insNames) throws RdsException {
        for (String key : rwWeightMap.keySet()) {
            if (!insNames.contains(key.toString())) {
                throw new RdsException(ErrorCode.INVALID_WEIGHT_INSNAME);
            }
        }
        Integer weightSum = 0;
        for (Object value : rwWeightMap.values()) {
            Integer wt = CheckUtils.parseInt(value.toString(), 0, 10000, ErrorCode.INVALID_WEIGHT);
            if (wt % 100 != 0) {
                throw new RdsException(ErrorCode.INVALID_WEIGHT);
            }
            weightSum += wt;
        }
        if (weightSum == 0) {
            throw new RdsException(ErrorCode.INVALID_WEIGHT);
        }
    }

    public String getAndChangeEngine(Map<String, String> actionParams) throws RdsException {//数据库类型
        String engine = getParameterValue(actionParams, "engine");
        if (Validator.isNotNull(engine)) {
            engine = CustinsSupport.changeEngine(engine);
            if (Validator.isNull(engine)) {
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }
        }
        return engine;
    }

    public String validAndChangeEngine(String engine) throws RdsException {//数据库类型
        if (Validator.isNotNull(engine)) {
            engine = CustinsSupport.changeEngine(engine);
            if (Validator.isNull(engine)) {
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }
        }
        return engine;
    }

    /**
     * 获取要创建rds实例的业务类型（partition分区实例 或 普通实例）
     *
     * @return
     */
    public Integer getAndCheckBizType(Map<String, String> actionParams) {
        String bizTypeStr = getParameterValue(actionParams, ParamConstants.BIZ_TYPE);
        if (ParamConstants.PARTITION_NAME.equalsIgnoreCase(bizTypeStr)) {
            return CustinsSupport.BIZ_TYPE_PARTITION;
        }
        return CustinsSupport.BIZ_TYPE_RDS;
    }

    public Integer getAndCheckBizType(Map<String, String> actionParams, String bizType) {
        if (bizType == null) {
            bizType = getParameterValue(actionParams, ParamConstants.BIZ_TYPE);
        }
        if (ParamConstants.PARTITION_NAME.equalsIgnoreCase(bizType)) {
            return CustinsSupport.BIZ_TYPE_PARTITION;
        }
        return CustinsSupport.BIZ_TYPE_RDS;
    }

    public String getAndCheckDBType(Map<String, String> actionParams, String defaultType) throws RdsException {
        if (dbTypeList == null) {
            dbTypeList = resourceService.getResourceRealValueList(RESOURCE_DB_TYPE);
        }
        String dbType = this.getAndChangeEngine(actionParams);
        if (dbType == null && defaultType != null) {
            dbType = defaultType;
        }
        if (dbType == null || !dbTypeList.contains(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        return dbType;
    }

    public String getAndCheckDockerDBType(String defaultType) throws RdsException {
        if (dbTypeList == null) {
            dbTypeList = resourceService.getResourceRealValueList(RESOURCE_DB_TYPE);
        }
        String dbType = this.validAndChangeEngine(defaultType);
        if (dbType == null && defaultType != null) {
            dbType = defaultType;
        }
        if (dbType == null || !dbTypeList.contains(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        return dbType;
    }

    public String getAndCheckClassCode(Map<String, String> actionParams) throws RdsException {
        String classCode = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_CLASS);
        if (org.apache.commons.lang3.StringUtils.isBlank(classCode)) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        }
        return classCode;
    }

    public String getAndCheckDescription(Map<String, String> actionParams) throws RdsException {
        String description = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_DESCRIPTION);
        return checkDescription(description);
    }

    public String getAndCheckStorageType(Map<String, String> actionParams) throws RdsException {
        return getParameterValue(actionParams, "storagetype");
    }

    public Integer getAndCheckStorage(Map<String, String> actionParams) throws RdsException {
        return CheckUtils.parseInt(getParameterValue(actionParams, ParamConstants.STORAGE),
                ESSD_MIN_DISK_SIZE, 102400, ErrorCode.INVALID_STORAGE);
    }

    public String checkDescription(String description) throws RdsException {
        String desc = SupportUtils.decode(description);
        desc = CheckUtils
            .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
        return desc;
    }

    private void initDbVersionMap(String dbType) {
        if (dbVersionMap == null) {
            dbVersionMap = new HashMap<>();
        }
        if (dbType != null && !dbVersionMap.containsKey(dbType)) {
            List<String> dbVersionList = resourceService.getResourceRealValueList(
                RESOURCE_DB_VERSION, RESOURCE_DB_TYPE, dbType);
            logger.warn("initDbVersionMap is invoke...dbVersionList.size=" + dbVersionList.size());
            if (!dbVersionList.isEmpty()) {
                dbVersionMap.put(dbType, dbVersionList);
            }
        }
    }

    public Long getExtendDiskSizeForEcsIns(String dbType, Long diskSize) {
        // For MysqlOnEcsDBEngineExtAdapter 5.7 ins on ecs, allocate extra disk. 1G ~ 20G
        if (CustinsSupport.DB_TYPE_MYSQL.equals(dbType) || CustinsSupport.DB_TYPE_DOCKER.equals(dbType)) {
            Long extendDiskSizeGB = Math.max(Math.round(diskSize.floatValue() / 1024 * 0.1), 1L);
            diskSize += (Math.min(extendDiskSizeGB * 1024, 20 * 1024L) + 4096L);
        }
        return diskSize;
    }

    public List<String> getDbTypeList() {
        if (dbTypeList == null) {
            dbTypeList = resourceService.getResourceRealValueList(RESOURCE_DB_TYPE);
        }
        return dbTypeList;
    }

    public List<String> getDbVersionList(String dbType) throws RdsException {
        if (!this.getDbTypeList().contains(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        if (dbVersionMap == null || !dbVersionMap.containsKey(dbType)) {
            initDbVersionMap(dbType);
        }
        return dbVersionMap.get(dbType);
    }

    /**
     * TODO:2.7.17 1. 创建MySQL类型主实例版本默认为5.5
     *
     * @param dbType
     * @param setDefault
     * @return
     * @throws RdsException
     */
    public String getAndCheckDBVersion(Map<String, String> actionParams, String dbType, boolean setDefault)
        throws RdsException {
        this.initDbVersionMap(dbType);
        if (!dbVersionMap.containsKey(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        String dbVersion = getParameterValue(actionParams, "engineversion");
        logger.warn("CommponentsAction.getAndCheckDBVersion version=" + dbVersion);
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbVersion)) {
            if (setDefault) {
                dbVersion = CustinsSupport.getDbTypeDefaultVersion(dbType);
                logger.warn("step1=CommponentsAction.getAndCheckDBVersion version=" + dbVersion);
            } else {
                throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
            }
        }
        if (!dbVersionMap.get(dbType).contains(dbVersion)) {
            throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
        }
        return dbVersion;
    }

    public String getAndCheckCharacterType(Map<String, String> actionParams, boolean setDefault) throws RdsException {
        String characterType = getParameterValue(actionParams, "charactertype");
        if (org.apache.commons.lang3.StringUtils.isEmpty(characterType)) {
            if (setDefault) {
                characterType = CustinsSupport.CHARACTER_TYPE_NORMAL;
            } else {
                throw new RdsException(ErrorCode.INVALID_CHARACTER_TYPE);
            }
        }
        if (!CustinsSupport.CHARACTER_TYPE_LOGIC.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_NORMAL.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_MYSQL_DB.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_MYSQL_SPHINX.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_REDIS_PROXY.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_REDIS_CS.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_PHYSICAL.equals(characterType)) {
            throw new RdsException(ErrorCode.INVALID_CHARACTER_TYPE);
        }
        if (CustinsSupport.CHARACTER_TYPE_MYSQL_DB.equals(characterType)) {
            return CustinsSupport.CHARACTER_TYPE_NORMAL;
        }
        return characterType;
    }

    public String getDBVersion(Map<String, String> actionParams, String dbType) throws RdsException {
        this.initDbVersionMap(dbType);
        String dbVersion = getParameterValue(actionParams, "engineversion");
        if (Validator.isNotNull(dbVersion)) {
            if (!dbVersionMap.containsKey(dbType)) {
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }
            if (!dbVersionMap.get(dbType).contains(dbVersion)) {
                throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
            }
        }
        return dbVersion;
    }

    public String getAndCheckBakType(Map<String, String> actionParams, String bakWay) throws RdsException {
        String baktype = getParameterValue(actionParams, "backupType");
        return CheckUtils.checkBakType(baktype, bakWay);
    }

    public String getAndCheckBakWay(Map<String, String> actionParams, String dbType, Boolean onEcs)
        throws RdsException {
        String bakway = getParameterValue(actionParams, "backupMethod");
        return CheckUtils.checkBakWay(dbType, onEcs, bakway);
    }

    public String getAndCheckCharacterSetName(Map<String, String> actionParams, String dbType, String dbVersion)
        throws RdsException {//字符集
        String character = getParameterValue(actionParams, "charactersetname", "utf8");
        return this.checkCharacterSetName(dbType, dbVersion, character);
    }

    public Set<String> getAndCheckSecurityIpList(Map<String, String> actionParams) throws RdsException {
        String ipList = getParameterValue(actionParams, ParamConstants.SECURITY_IP_LIST);
        if (ipList == null) {
            throw new RdsException(ErrorCode.INVALID_SECURITYIPLIST_FORMAT);
        }
        String ipType = getAndCheckSecurityIpType(actionParams);
        return CheckUtils.checkIpList(ipList,
            resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_IP_WHILE_LIST_NUM), ipType);
    }

    public String getAndCheckWhitelistNetType(Map<String, String> actionParams) throws RdsException {
        // 若用户不传WhitelistNetType，则默认为mix，当控制台支持后，netType为必传，只能选择classic和vpc
        String netType = getParameterValue(actionParams, ParamConstants.WHITELIST_NET_TYPE,
            CustinsIpWhiteListDO.DEFAULT_NET_TYPE);
        return CheckUtils.checkValidForIpWhiteListNetType(netType);
    }

    public String getAndCheckSecurityIpType(Map<String, String> actionParams) throws RdsException {
        // 若用户不传 SecurityIpType，则默认为 ipv4
        String ipType = getParameterValue(actionParams, ParamConstants.SECURITY_IP_TYPE,
            CustinsIpWhiteListDO.DEFAULT_IP_TYPE);
        if (ipType == null || !ParamConstants.SECURITY_IP_TYPE_SET.contains(ipType.toLowerCase())) {
            throw new RdsException(ErrorCode.INVALID_SECURITY_IP_TYPE);
        }
        return ipType.toLowerCase();
    }

    public CustinsIpWhiteListDO getAndCheckCustinsIpWhiteList(Map<String, String> actionParams, CustInstanceDO custins)
        throws RdsException {
        Set<String> ipSet = getAndCheckSecurityIpList(actionParams);
        String whitelistNetType = getAndCheckWhitelistNetType(actionParams);
        return new CustinsIpWhiteListDO(custins.getId(), SupportUtils.getIpWhiteListStr(ipSet),
            CustinsIpWhiteListDO.DEFAULT_GROUP_NAME, "", whitelistNetType);
    }

    public String getWhitelistNetTypeByNetType(Integer netType) {
        String whitelistNetType;
        if (CustinsSupport.NET_TYPE_VPC.equals(netType)) {
            whitelistNetType = ParamConstants.VPC_WHITELIST_NET_TYPE;
        } else {
            whitelistNetType = ParamConstants.CLASSIC_WHITELIST_NET_TYPE;
        }
        return whitelistNetType;
    }

    public void fillCustinsListDefaultIpWhiteList(List<Map<String, Object>> custinsList) {
        for (Map<String, Object> custins : custinsList) {
            Integer custinsId = (Integer)custins.get("DBInstanceID");
            String whitelistNetType = getWhitelistNetTypeByNetType((Integer)custins.get("DBInstanceNetType"));
            List<CustinsIpWhiteListDO> custinsIpWhiteList = ipWhiteListService.getCustinsIpWhiteList(
                custinsId, CustinsIpWhiteListDO.DEFAULT_GROUP_NAME, whitelistNetType,
                CustinsIpWhiteListDO.DEFAULT_IP_TYPE);
            if (custinsIpWhiteList.isEmpty()) {
                custinsIpWhiteList = ipWhiteListService.getCustinsIpWhiteList(
                    custinsId, CustinsIpWhiteListDO.DEFAULT_GROUP_NAME, CustinsIpWhiteListDO.DEFAULT_NET_TYPE,
                    CustinsIpWhiteListDO.DEFAULT_IP_TYPE);
            }
            if (!custinsIpWhiteList.isEmpty()) {
                custins.put(ParamConstants.SECURITY_IP_LIST, custinsIpWhiteList.get(0).getIpWhiteList());
            }
        }
    }

    public void fillCustinsListRwType(List<Map<String, Object>> custinsList) {
        for (Map<String, Object> custins : custinsList) {
            Integer custinsId = (Integer)custins.get("DBInstanceID");
            String engine = (String)custins.get("Engine");
            Integer insRWType = 0;
            if (!CustinsSupport.DB_ENGINE_MYSQL.equals(engine) && !CustinsSupport.DB_ENGINE_REDIS.equals(engine) &&
                !CustinsSupport.DB_ENGINE_MONGODB.equals(engine)) {
                return;
            } else {
                AuroraListDO aurora = custinsService.getAuroraListByCustinsId(custinsId);
                if (aurora != null) {
                    insRWType = aurora.getIsReadonly();
                }
            }
            custins.put("ReadWriteType", insRWType);
        }
    }

    public String getStartTime(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "starttime");
    }

    public String getEndTime(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "endtime");
    }

    public String getFlushSysImage(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "Image");
    }

    public String getParameterName(Map<String, String> actionParams) {

        return getParameterValue(actionParams, "parametername");
    }

    public long getAndCheckTimestampByParam(Map<String, String> actionParams, String dateParamName, ErrorCode errorCode)
        throws RdsException {
        String dateStr = getParameterValue(actionParams, dateParamName);
        try {
            Date date = DateSupport.str2second_gmt(dateStr);
            return date.getTime() / 1000;
        } catch (Exception e) {
            throw new RdsException(errorCode);
        }
    }

    public Date getAndCheckTimeByParam(Map<String, String> actionParams, String dateParamName, DateUTCFormat format,
                                       ErrorCode errorCode) throws RdsException {
        String dateStr = getParameterValue(actionParams, dateParamName);
        return getAndCheckTimeByDateStr(dateStr, format, errorCode);
    }

    public Date getAndCheckTimeByParam(Map<String, String> actionParams, String paramName, DateUTCFormat format,
                                       ErrorCode errorCode,
                                       String defaultDate) throws RdsException {
        String date = getParameterValue(actionParams, paramName);
        if (date == null && defaultDate != null) {
            date = defaultDate;
        }
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds();
        return CheckUtils.getAndCheckDateTime(date, format, errorCode, timeZoneDiffSec);
    }

    public Date getAndCheckTimeByDateStr(String dateStr, DateUTCFormat format, ErrorCode errorCode)
        throws RdsException {
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds();
        return CheckUtils.getAndCheckDateTime(dateStr, format, errorCode, timeZoneDiffSec);
    }

    public Date getAndCheckStartTime(Map<String, String> actionParams, DateUTCFormat format) throws RdsException {
        String date = getParameterValue(actionParams, "starttime");
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds();
        return CheckUtils.getAndCheckDateTime(date, format, ErrorCode.INVALID_STARTTIME, timeZoneDiffSec);
    }

    public Date getAndCheckEndTime(Map<String, String> actionParams, DateUTCFormat format) throws RdsException {
        String date = getParameterValue(actionParams, "endtime");
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds();
        return CheckUtils.getAndCheckDateTime(date, format, ErrorCode.INVALID_ENDTIME, timeZoneDiffSec);
    }

    public Integer getAndCheckPageSize(Map<String, String> actionParams) throws RdsException {
        String maxRecordsPerPage = getParameterValue(actionParams, "maxrecordsperpage");
        return CheckUtils.checkMaxRecordsPerPage(maxRecordsPerPage);
    }

    public Integer getAndCheckPageNo(Map<String, String> actionParams) throws RdsException {
        String pageNumbers = getParameterValue(actionParams, "pagenumbers");
        return CheckUtils.checkPageNumbers(pageNumbers);
    }

    public String getAndCheckSiteName(Map<String, String> actionParams) throws RdsException {
        String siteName = this.getParameterValue(actionParams, ParamConstants.SITE_NAME);
        if (org.apache.commons.lang3.StringUtils.isBlank(siteName)) {
            throw new RdsException(ErrorCode.SITENAME_NOT_FOUND);
        }
        return siteName;
    }

    public Integer getAndCheckSlaveCount(Map<String, String> actionParams) throws RdsException {
        String insCount = this.getParameterValue(actionParams, ParamConstants.INSTANCE_COUNT,
            CustinsSupport.Default_Instance_Count);

        Integer instanceCount = -1;
        try {
            instanceCount = Integer.valueOf(insCount);
        } catch (Exception e) {
            throw new RdsException(ErrorCode.REBUILD_INSCOUNT_ERROR);
        }
        if (instanceCount < 1) {
            throw new RdsException(ErrorCode.REBUILD_INSCOUNT_LESS1ERROR);
        }

        return instanceCount;
    }

    /**
     * 获得静默迁移请求中的switch time mode 参数
     *
     * @param switchTimeMode
     * @return
     */
    public String getAndCheckSwitchTimeMode(Map<String, String> actionParams, String switchTimeMode) {
        String timeMode = getParameterValue(actionParams, switchTimeMode);
        return timeMode;
    }

    /**
     * 获得重启方式参数
     *
     * @param restartMethod
     * @return
     */
    public String getAndCheckRestartMethod(Map<String, String> actionParams, String restartMethod) throws RdsException {
        String restartMethd = getParameterValue(actionParams, restartMethod);
        if (StringUtils.isNotBlank(restartMethd)) {
            if (restartMethd.equalsIgnoreCase(CustinsSupport.RESTART_NOW)){
                return CustinsSupport.RESTART_NOW;
            } else {
                if (restartMethd.equalsIgnoreCase(CustinsSupport.RESTART_HASWITCH)){
                    return CustinsSupport.RESTART_HASWITCH;
                } else {
                    throw new RdsException(ErrorCode.INVALID_RESTARTMETHOD);
                }
            }

        }
        return CustinsSupport.RESTART_NOW;
    }

    /**
     * 判断是否为优雅重启，默认直接重启
     *
     * @param restartMethod
     * @return
     */
    public Boolean getAndCheckRestartElegant(String restartMethod) throws RdsException {
        if (StringUtils.isNotBlank(restartMethod)) {
            if (restartMethod.equalsIgnoreCase(CustinsSupport.RESTART_NOW)){
                return false;
            } else {
                if (restartMethod.equalsIgnoreCase(CustinsSupport.RESTART_HASWITCH)) {
                    return true;
                } else {
                    throw new RdsException(ErrorCode.INVALID_RESTARTMETHOD);
                }
            }

        } else {
            return false;
        }
    }

    /**
     * 判断是否为physical链路
     * @param replicaSet
     * @return
     */
    public Boolean getAndCheckIsPhysical(ReplicaSet replicaSet) throws RdsException {

        if (CONN_TYPE_PHYSICAL.equalsIgnoreCase(replicaSet.getConnType().toString())){
            return true;
        } else {
            return false;
        }
    }

    public Date parseCheckSwitchTimeTimeZoneSafe(Map<String, String> actionParams) throws RdsException {
        String switchTimeStr = getParameterValue(actionParams, ParamConstants.SWITCH_TIME);
        //不是指定时间点切换模式
        if(switchTimeStr == null){
            return null;
        }
        //存在时区转换问题，时间只做校验，校验通过之后，使用原来的切换时间
        checkService.getAndCheckSwitchTime(switchTimeStr);
        //先转为UTC时间，一切时间都按照UTC时间计算
        DateTime utcSwitchTime = dtzSupport.getUTCDateByDateStr(switchTimeStr);
        return utcSwitchTime.toDate();
    }

    public Integer getAndCheckSwitchWindow(Map<String, String> actionParams){
        String switchWindow = getParameterValue(actionParams, ParamConstants.SWITCH_WINDOW);
        if(switchWindow == null){
            return null;
        }
        return Integer.valueOf(switchWindow);
    }

    //期望时间需要超过当前时间
    public Date parseCheckApplyResourceTimeZoneSafe(String expectedTimeUtcStr) throws RdsException {
        if(expectedTimeUtcStr == null){
            return null;
        }
        //先转为UTC时间，一切时间都按照UTC时间计算
        DateTime utcSwitchTime = dtzSupport.getUTCDateByDateStr(expectedTimeUtcStr);
        if(utcSwitchTime.toDate().getTime() < System.currentTimeMillis()){
            throw new RdsException(MysqlErrorCode.INVALID_APPLY_RESOURCE_EXPECTED_TIME.toArray());
        }
        return utcSwitchTime.toDate();
    }

    public Date parseCheckSwitchTimeTimeZoneSafe(String switchTimeStr) throws RdsException {
        if(switchTimeStr == null){
            return null;
        }
        //存在时区转换问题，时间只做校验，校验通过之后，使用原来的切换时间
        checkService.getAndCheckSwitchTime(switchTimeStr);
        //先转为UTC时间，一切时间都按照UTC时间计算
        DateTime utcSwitchTime = dtzSupport.getUTCDateByDateStr(switchTimeStr);
        return utcSwitchTime.toDate();
    }

    /**
     * switchPromptlyParam,切换模式 switchTime,切换时间 isTrans, 是否是迁移任务,ha任务也会调用这个接口
     */
    public String getAndCheckSwitchTimeMode(Map<String, String> actionParams, String switchPromptlyParam,
                                            Date switchTime, boolean isTrans) throws RdsException {

        String switchPromptly = this.getParameterValue(actionParams, switchPromptlyParam);

        if (org.apache.commons.lang3.StringUtils.isEmpty(switchPromptly)) {
            //没有传递switchTimeMode参数
            if (switchTime == null) {
                return CustinsSupport.NOW_MODE;
            } else {
                if (isTrans) {
                    //jian rong
                    return CustinsSupport.COMPATIBILITY_MODE;
                } else {
                    throw new RdsException(ErrorCode.INVALID_SWICTHTIMEMODE);
                }
            }
        } else {
            if (CustinsSupport.SWITCH_NOW.equals(switchPromptly)) {
                //立即切换
                return CustinsSupport.NOW_MODE;
            } else if (CustinsSupport.SWITCH_MAINTAIN.equals(switchPromptly)) {
                //可运维时间
                return CustinsSupport.MAINTAIN_MODE;
            } else if (CustinsSupport.SWITCH_POINT.equals(switchPromptly)) {
                //指定时间点
                if (switchTime == null) {
                    throw new RdsException(ErrorCode.INVALID_SWICTHTIME);
                } else {
                    return CustinsSupport.APPOINT_MODE;
                }

            } else {
                throw new RdsException(ErrorCode.INVALID_UNKNOWMODE);
            }
        }
    }

    public String getAndCheckRestoreType(Map<String, String> actionParams) throws RdsException {
        String restoreType = this.getParameterValue(actionParams, ParamConstants.RESTORE_TYPE);
        if (restoreType == null) {
            return RESTORE_TYPE_BAKID;
        }
        if (RESTORE_TYPE_TIME.equals(restoreType) || RESTORE_TYPE_BAKID.equals(restoreType) || RESTORE_TYPE_OSSBAK
            .equals(restoreType)
            || RESTORE_TYPE_USER.equals(restoreType) || RESTORE_TYPE_STANDBY.equals(restoreType) || RESTORE_TYPE_SRCCUST
            .equals(restoreType) || RESTORE_TYPE_LASTEST.equals(restoreType)) {
            return restoreType;
        }
        throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
    }

    /**
     * flush ap账号,默认 maintainTime
     */
    public String getAndCheckSwitchModeForAP(Map<String, String> actionParams, String switchPromptlyParam,
                                             Date switchTime) throws RdsException {

        String switchPromptly = getParameterValue(actionParams, switchPromptlyParam);

        if (org.apache.commons.lang3.StringUtils.isEmpty(switchPromptly)) {
            //没有传递switchTimeMode参数
            return CustinsSupport.MAINTAIN_MODE;
        } else {
            if (CustinsSupport.SWITCH_NOW.equals(switchPromptly)) {
                //立即切换
                return CustinsSupport.NOW_MODE;
            } else if (CustinsSupport.SWITCH_MAINTAIN.equals(switchPromptly)) {
                //可运维时间
                return CustinsSupport.MAINTAIN_MODE;
            } else if (CustinsSupport.SWITCH_POINT.equals(switchPromptly)) {
                //指定时间点
                if (switchTime == null) {
                    throw new RdsException(ErrorCode.INVALID_SWICTHTIME);
                } else {
                    return CustinsSupport.APPOINT_MODE;
                }

            } else {
                throw new RdsException(ErrorCode.INVALID_UNKNOWMODE);
            }
        }
    }

    public String getAndCheckEntityType(Map<String, String> actionParams) throws RdsException {
        String entityType = getParameterValue(actionParams, ParamConstants.ENTITY_TYPE);
        if (org.apache.commons.lang3.StringUtils.isBlank(entityType)) {
            throw new RdsException(ErrorCode.INVALID_ENTITY_TYPE);
        }

        return entityType;
    }

    public Integer getAndCheckUserLogEntityId(Map<String, String> actionParams) throws RdsException {
        String str = this.getParameterValue(actionParams, ParamConstants.ENTITY_ID);
        Integer entityId = CustinsValidator.getRealNumber(str);
        return entityId;
    }

    public String getAndCheckEntityId(Map<String, String> actionParams) throws RdsException {
        String entityId = getParameterValue(actionParams, ParamConstants.ENTITY_ID);
        if (org.apache.commons.lang3.StringUtils.isBlank(entityId)) {
            throw new RdsException(ErrorCode.INVALID_ENTITY_ID);
        }
        return entityId;
    }

    public String getEntityId(Map<String, String> actionParams) throws RdsException {
        String entityId = this.getParameterValue(actionParams, ParamConstants.ENTITY_ID);

        return entityId;
    }

    public String getTagType(Map<String, String> actionParams) {
        String tagType = this.getParameterValue(actionParams, ParamConstants.TAG_TYPE);
        return tagType;
    }

    public String getAndCheckTagType(Map<String, String> actionParams) throws RdsException {
        String tagType = this.getParameterValue(actionParams, ParamConstants.TAG_TYPE);
        if (org.apache.commons.lang3.StringUtils.isBlank(tagType)) {
            throw new RdsException(ErrorCode.INVALID_TAG_TYPE);
        }

        return tagType;
    }

    public String getAndCheckTagValue(Map<String, String> actionParams) throws RdsException {
        String tagValue = this.getParameterValue(actionParams, ParamConstants.TAG_VALUE);
        if (org.apache.commons.lang3.StringUtils.isBlank(tagValue)) {
            throw new RdsException(ErrorCode.INVALID_TAG_VALUE);
        }

        return tagValue;
    }

    /**
     * 获取操作者ID，杜康使用
     *
     * @return
     */
    public Integer getOperatorId(Map<String, String> actionParams) {
        if (hasParameter(actionParams, ParamConstants.OPERATOR_ID)) {
            return CustinsValidator.getRealNumber(
                getParameterValue(actionParams, ParamConstants.OPERATOR_ID));
        } else {
            return 999999;
        }
    }

    /**
     * 取值范围：utf8/gbk/latin1
     *
     * @param dbType
     * @param character
     * @return
     * @throws RdsException TODO 2.7.17 字符排除mysql5.1，配置成utf8mb4#5.1
     */
    public String checkCharacterSetName(String dbType, String dbVersion, String character)
        throws RdsException {
        if (Validator.isNull(character)) {
            throw new RdsException(ErrorCode.INVALID_CHARACTERSETNAME);
        }
        String charsetValue = resourceService.getDisplayValue(RESOURCE_DB_CHARSET, character,
            RESOURCE_DB_TYPE, dbType);
        if (charsetValue != null) {
            int idx = charsetValue.indexOf('#');
            if (idx != -1) {
                String version = charsetValue.substring(idx + 1);
                if (dbVersion.equals(version)) {
                    throw new RdsException(ErrorCode.INVALID_CHARACTERSETNAME);
                }
            }
            return character;
        }
        if (dbType
            .equals(CustinsSupport.DB_TYPE_MSSQL)) {//传入mssql字符集在mysql字符集列表里，则返回mssql的字符集Chinese_PRC_CI_AS
            List<String> list = resourceService.getResourceRealValueList(RESOURCE_DB_CHARSET,
                RESOURCE_DB_TYPE, CustinsSupport.DB_TYPE_MYSQL);
            if (list.contains(character)) {
                return "Chinese_PRC_CI_AS";
            }
        }
        throw new RdsException(ErrorCode.INVALID_CHARACTERSETNAME);
    }

    /**
     * 获取用户连接地址，连接字符串+后辍，失败返回null
     *
     * @param connectionString
     * @param dbType
     * @return
     */
    public String getConnAddrCust(String connectionString, String dbType) throws RdsException {
        return connectionString + getConnAddrCustLast(dbType);
    }

    public String getConnAddrCustLast(String dbType) throws RdsException {
        if (connLastMap == null || !connLastMap.containsKey(dbType)) {
            List<Map<String, String>> resMapList = resourceService.getResourceMapList(RESOURCE_CONNADDR_LAST);
            connLastMap = new HashMap<String, String>(resMapList.size());
            for (Map<String, String> resMap : resMapList) {
                connLastMap.put(resMap.get("RealValue"), resMap.get("DisplayValue"));
            }
        }
        if (!connLastMap.containsKey(dbType)) {
            throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
        }
        return connLastMap.get(dbType);
    }
    /**
     * 获取用户连接地址，连接字符串+后辍，失败返回null
     *
     * @param connectionString
     * @param regionId
     * @param dbType
     * @return
     */
    public String getConnAddrCust(String connectionString, String regionId, String dbType) throws RdsException {
        return connectionString + getConnAddrCustLast(regionId, dbType);
    }
    /**
     * 获取用户连接地址后辍，失败返回null
     *
     * @param regionId
     * @param dbType
     * @return
     */
    public String getConnAddrCustLast(String regionId, String dbType) throws RdsException {
        if (regionConnLastMap == null || !regionConnLastMap.containsKey(dbType)) {
            List<Map<String, String>> resMapList = resourceService.getResourceMapList(RESOURCE_REGION_CONNADDR_LAST);
            regionConnLastMap = new HashMap<>(resMapList.size());
            for (Map<String, String> resMap : resMapList) {
                try {
                    regionConnLastMap.put(resMap.get("RealValue"), JSON.parseObject(resMap.get("DisplayValue"), new TypeReference<Map<String, String>>() {
                    }));
                } catch (Throwable e) {
                    logger.warn(
                        String.format("%s config for %s cannot be parsed: %s; value: %s",
                            RESOURCE_REGION_CONNADDR_LAST, resMap.get("RealValue"), e, resMap.get("DisplayValue")), e);
                }
            }
        }
        if ((!regionConnLastMap.containsKey(dbType)) || regionConnLastMap.get(dbType) == null) {
            return getConnAddrCustLast(dbType);
        }
        String regionConnAddr = regionConnLastMap.get(dbType).getOrDefault(regionId, null);
        return (StringUtils.isEmpty(regionConnAddr) ? getConnAddrCustLast(dbType) : regionConnAddr);
    }
    /**
     * 获取用户连接地址，连接字符串+后辍，失败返回null
     *
     * @param connectionString
     * @param dbType
     * @param dockerType
     * @return
     */
    public String getConnAddrCust4Docker(String connectionString, String dockerType, String dbType)
        throws RdsException {
        try{
            return getConnAddrCust(connectionString, dbType);
        }catch (RdsException ignored){
            return getConnAddrCust(connectionString, dockerType);
        }
    }
    /**
     * 获取用户连接地址，连接字符串+后辍，失败返回null
     *
     * @param connectionString
     * @param regionId
     * @param dbType
     * @param dockerType
     * @return
     */
    public String getConnAddrCust4Docker(String connectionString, String regionId, String dockerType, String dbType)
        throws RdsException {
        try{
            return getConnAddrCust(connectionString, regionId, dbType);
        }catch (RdsException ignored){
            return getConnAddrCust(connectionString, regionId, dockerType);
        }
    }
    public String getRegionIdByClusterName(String cluster) throws RdsException{
        ClustersDO clusterByClusterName = clusterService.getClusterByClusterName(cluster);
        if(clusterByClusterName.getType() >= 1) {
            return clusterByClusterName.getRegion();
        }
        List<String> regionIds = clusterService.getRegionByClusterNames(new String[]{cluster});
        if (regionIds == null || regionIds.isEmpty()){
            throw new RdsException(ErrorCode.INVALID_PARAM, "Can't find regionId, with clusterName=" + cluster);
        }
        return regionIds.get(0);
    }
    /**
     * 判断用户的连接串是否是完成的地址，否则作为前缀处理
     *
     * @param connectionString
     * @param dbType
     * @return
     */
    public Boolean isValidFullConnectionAddr(String connectionString, String dbType) throws RdsException {
        String last = getConnAddrCustLast(dbType);
        if (connectionString.endsWith(last)) {
            String preFix = org.apache.commons.lang3.StringUtils.substringBeforeLast(connectionString,
                last);
            CheckUtils.checkValidForConnAddrCust(preFix);
            return true;
        }
        return false;
    }
    /**
     * 判断用户的连接串是否是完成的地址，否则作为前缀处理
     *
     * @param connectionString
     * @param dbType
     * @return
     */
    public Boolean isValidFullConnectionAddr(String connectionString, String regionId, String dbType) throws RdsException {
        String last = getConnAddrCustLast(regionId, dbType);
        if (connectionString.endsWith(last)) {
            String preFix = org.apache.commons.lang3.StringUtils.substringBeforeLast(connectionString,
                last);
            CheckUtils.checkValidForConnAddrCust(preFix);
            return true;
        }
        return false;
    }
    /**
     * 获取实例支持的最大DB数量
     *
     * @param dbType
     * @return
     * @throws RdsException
     */
    public Integer getCustinsMaxDbs(String dbType) throws RdsException {
        List<Map<String, String>> resMapList = resourceService
            .getResourceMapList(RESOURCE_CUSTINS_MAX_DBS);
        for (Map<String, String> resMap : resMapList) {
            if (dbType.equals(resMap.get("RealValue"))) {
                try {
                    return Integer.parseInt(resMap.get("DisplayValue"));
                } catch (NumberFormatException e) {
                    throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
                }
            }
        }
        throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
    }

    /**
     * 获取用户传入的账号信息
     *
     * @param custins
     * @return
     * @throws RdsException
     */
    public AccountsDO getAndCheckAccount(Map<String, String> actionParams, CustInstanceDO custins) throws RdsException {
        AccountsDO account = null;
        if (custins.isRedis()) {
            if (hasParameter(actionParams, ParamConstants.ACCOUNT_PASSWORD)
                || hasParameter(actionParams, ParamConstants.ENCRYPT_ACCOUNT_PASSWORD)) {
                account = new AccountsDO(custins, custins.getInsName(),
                    getAndCheckDecryptedAccountPassword(actionParams));
            } else {
                throw new RdsException(ErrorCode.ACCOUNT_NOT_FOUND);
            }
        } else if (hasParameter(actionParams, ParamConstants.ACCOUNT_NAME)) {
            String accountName = CheckUtils.checkValidForAccountName(
                getAccountName(actionParams), custins.getDbType(), custins.isTop(),
                custins.isSuperAccountMode() ?
                    AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN :
                    AccountPriviledgeType.PRIVILEDGE_NORMAL,
                custins.getDbVersion());
            account = new AccountsDO(custins, accountName,
                getAndCheckDecryptedAccountPassword(actionParams));
            if (custins.isSuperAccountMode()) {
                account.setPriviledgeType(AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue());
            } else {
                account.setPriviledgeType(AccountPriviledgeType.PRIVILEDGE_NORMAL.getValue());
            }
        }
        return account;
    }

    /**
     * 获取实例支持创建的最大Account数量
     *
     * @param dbType
     * @return
     * @throws RdsException
     */
    public Integer getCustinsMaxAccounts(String dbType) throws RdsException {
        List<Map<String, String>> resMapList = resourceService
            .getResourceMapList(RESOURCE_CUSTINS_MAX_ACCOUNTS);
        for (Map<String, String> resMap : resMapList) {
            if (dbType.equals(resMap.get("RealValue"))) {
                try {
                    return Integer.parseInt(resMap.get("DisplayValue"));
                } catch (NumberFormatException e) {
                    throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
                }
            }
        }
        throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
    }

    /**
     * 获取创建实例时指定的磁盘类型
     *
     * @return
     */
    public String getAndCheckHostType(Map<String, String> actionParams) throws RdsException {
        String hostType = getParameterValue(actionParams, ParamConstants.CUSTINS_HOST_TYPE);
        return CheckUtils.checkHostType(hostType);
    }
    /**
     * 获取创建实例时指定的磁盘类型
     *
     * @return
     */
    public Integer getDiskCheck(Map<String, String> actionParams, String param) throws RdsException {
        String diskCheck = getParameterValue(actionParams, param);
        try {
            return Integer.parseInt(diskCheck);
        } catch  (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 获取创建Docker化实例的磁盘类型
     *
     * @return
     */
    public String getAndCheckDockerHostType(Map<String, String> actionParams, String dbType, String dbVersion)
        throws RdsException {
        String hostType = getParameterValue(actionParams, ParamConstants.CUSTINS_HOST_TYPE);
        if (CustinsSupport.isDockeronEcs(dbVersion, dbType)) {
            hostType = CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD;
        }

        if (hostType == null || CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE.contains(hostType)) {
            return hostType;
        }
        throw new RdsException(ErrorCode.INVALID_HOST_TYPE);
    }

    public void checkRestoreTimeValid(CustInstanceDO custins, Date restoreTime, LogPlanDO logPlan)
        throws RdsException {
        long times = restoreTime.getTime();
        logger.warn("times is: "+times+",System.currentTimeMillis() is: "+System.currentTimeMillis());
        if (times >= System.currentTimeMillis()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }
        logger.warn("times is: "+times+",custins.getGmtCreated().getTime() is: "+custins.getGmtCreated().getTime());
        if (times <= custins.getGmtCreated().getTime()) {
            throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
        }

        // 不允许恢复到备份保留周期之前的时间点
        Integer retention = bakService.getBaklistByCustinsId(custins.getId()).getRetention();
        if (logPlan != null) {
            Integer logRetention = logPlan.getRetention();
            retention = Math.min(retention, logRetention);
        }
        Date expireTime = DateUtils.addDays(DateSupport.str2date(DateSupport.date2str(new Date())),
            -retention);
        logger.warn("times is: "+times+",expireTime.getTime() is: "+expireTime.getTime());
        if (times < expireTime.getTime()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }

        if (logPlan != null) {
            // TODO: mongo公测期间不收费,后续收费时需去除特殊处理
            if ((!custins.isMongoDB() && !logPlan.isEnableBackupLog()) || logPlan.getEnableUploadTime().after(
                restoreTime)) {
                throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
            }
            // make sure there is a valid backup set between enable upload time and restore time.
            Integer countBakHis = bakService.countBakHisotry(custins.getId(),
                logPlan.getEnableUploadTime(), restoreTime, BAKTYPE_FULL, 1);
            if (countBakHis <= 0) {
                throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
            }
        }

        this.checkBakupSetAvailForRestoreTime(custins, restoreTime);
    }

    public void checkBakupSetAvailForRestoreTime(CustInstanceDO custins, Date restoreTime)
        throws RdsException {
        String bakWay;
        if (custins.isSqlserver()) {
            bakWay = BAKWAY_XTRABACKUP;
        } else if (custins.isMongoDB()) {
            // MongoDB过渡期间同时支持逻辑备份和物理备份
            bakWay = null;
        } else if (custins.isCustinsDockerOnPolarStore()) {
            bakWay = null;
        } else {
            if (custins.isCustinsOnEcs() || custins.isCustinsDockerOnEcs()) {
                bakWay = BAKWAY_SNAPSHOT;
            } else {
                bakWay = BAKWAY_XTRABACKUP;
            }
        }
        Date fullBakTimeBefore = bakService.getLatestBakTimeBefore(custins.getId(), restoreTime, BAKTYPE_FULL, bakWay);
        if (fullBakTimeBefore == null) {
            throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
        }
        Boolean checkOK = bakService.checkBinlogNotExpired(custins.getId(), fullBakTimeBefore, restoreTime);
        // 备份集和还原时间点期间的日志不能有被清理的
        if (!checkOK) {
            throw new RdsException(ErrorCode.RECOVERTIME_BINLOG_NOT_FOUND);
        }

        if (custins.isPolarDB()) {
            checkOK = bakService.checkBinlogSameSize(custins.getId(), fullBakTimeBefore, restoreTime);
            if (!checkOK) {
                throw new RdsException(ErrorCode.RECOVERTIME_BINLOG_NOT_VALID);
            }
        }

        if (custins.isSqlserver()) {

            Integer count = bakService.countBakHisotry(custins.getId(), fullBakTimeBefore,
                restoreTime, BAKTYPE_INCREMENT, null);

            if (count > 0) {
                Integer availCount = bakService.countBakHisotry(custins.getId(), fullBakTimeBefore, restoreTime,
                    BAKTYPE_INCREMENT, 1);
                if (!count.equals(availCount)) {
                    throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
                }
            }

            Date fullBakTimeAfter = bakService.getLatestBakTimeAfter(custins.getId(), restoreTime, BAKTYPE_FULL,
                bakWay);
            if (fullBakTimeAfter == null) {
                fullBakTimeAfter = new Date();
            }
            count = instanceService.countTransListByTypeCondition(null, custins.getId(),
                fullBakTimeBefore, fullBakTimeAfter, TRANS_TYPE_RECOVER);
            if (count > 0) {
                throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
            }
        }
    }

    /**
     * 获取创建实例时指定的几点类型：single|double
     *
     * @return
     */
    public String getAndCheckNodeType(Map<String, String> actionParams) throws RdsException {
        String nodeType = getParameterValue(actionParams, ParamConstants.CUSTINS_NODE_TYPE);
        return CheckUtils.checkNodeType(nodeType);
    }

    /**
     * 获取创建实例时指定的region_id(ex:cn-hangzhou)
     *
     * @return
     */
    public String getAndCheckRegionID(Map<String, String> actionParams) throws RdsException {//地域
        String regionID = getParameterValue(actionParams, ParamConstants.REGION_ID);
        return CheckUtils.checkNullForRegion(regionID);
    }

    public Integer getAndCheckDBInstanceUsedType(Map<String, String> actionParams) throws RdsException {
        String insTypeStr = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_USED_TYPE);
        Integer insType = CUSTINS_INSTYPE_PRIMARY; //默认值
        if (insTypeStr != null) {
            insType = SupportUtils.strToint(insTypeStr);
        }
        return insType;
    }

    /**
     * 获取创建实例时指定的av_zone(ex:cn-hangzhou-a)
     *
     * @return
     */
    public String getAndCheckAvZone(Map<String, String> actionParams) throws RdsException {//可用区
        String avZone = getParameterValue(actionParams, ParamConstants.ZONE_ID);
        return CheckUtils.checkNullForAvZone(avZone);
    }

    /**
     * 获取校验参数DBInstanceNodeCount参数，仅主实例MySQL5.6有效
     *
     * @param custins
     * @return
     * @throws RdsException
     */
    public Integer getAndCheckDBInstanceNodeCount(Map<String, String> actionParams, CustInstanceDO custins)
        throws RdsException {

        String nodeCountStr = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_NODE_COUNT);
        if (nodeCountStr == null) {
            return null;
        }
        Integer nodeCount = CustinsValidator.getRealNumber(nodeCountStr, -1);
        if (custins.isPrimary() && custins.isMysql56()) {
            if (nodeCount < 2) {
                throw new RdsException(ErrorCode.INVALID_NODE_COUNT);
            }
            return nodeCount;
        } else if (custins.isRedisNormal()) {
            if (nodeCount > 3 || nodeCount < 1) {
                throw new RdsException(ErrorCode.INVALID_NODE_COUNT);
            }
            return nodeCount;
        } else if (custins.isKeplerRc()) {
            if (nodeCount != 1) {
                throw new RdsException(ErrorCode.INVALID_NODE_COUNT);
            }
            return nodeCount;
        }
        return nodeCount;
    }

    /**
     * 获取校验DBInstanceGroupCount参数，仅对GP实例有效
     *
     * @param custins
     * @return
     * @throws RdsException
     */
    public Integer getAndCheckDBInstanceGroupCount(Map<String, String> actionParams, CustInstanceDO custins)
        throws RdsException {
        if (custins.isGpdb() || custins.isHawq() || custins.isHBase()) {
            String groupCountStr = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_GROUP_COUNT);
            Integer groupCount = CustinsValidator.getRealNumber(groupCountStr, -1);
            if (groupCount < 2) {
                throw new RdsException(ErrorCode.INVALID_GROUP_COUNT);
            }
            return groupCount;
        }
        return null;
    }

    /**
     * @return
     * @throws RdsException
     */
    public Integer getTasksFilter(Map<String, String> actionParams) throws RdsException {
        //默认过滤，只查询从API下发的任务
        String tasksFilterStr = getParameterValue(actionParams, ParamConstants.TASKS_FILTER, "1");
        return Integer.valueOf(tasksFilterStr);
    }

    /**
     * 获取校验参数DBInstanceSyncMode参数，仅主实例MySQL5.6有效
     *
     * @param custins
     * @return
     * @throws RdsException
     */
    public String getAndCheckDBInstanceSyncMode(Map<String, String> actionParams, CustInstanceDO custins)
        throws RdsException {

        String syncModeStr = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_SYNC_MODE);
        if (syncModeStr == null) {
            if (mysqlDBCustinsService.isMysqlEnterprise(custins)) {
                return CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_NO_REVERT_SEMI_SYNC;
            }
            return null;
        }
        if (custins.isPrimary() && custins.isMysql56()) {
            if (!CustinsParamSupport.CUSTINS_PARAM_VALUE_SET_SYNC_MODE.contains(syncModeStr)) {
                throw new RdsException(ErrorCode.INVALID_SYNC_MODE);
            }
            return syncModeStr;
        }
        return null;
    }

    /**
     * 获取当前元数据库与 UTC 时间的差值, 单位为秒
     *
     * @return FIXME:后续需要将识别不同调用者所属的元数据库，获取对应时间
     */
    private Integer getMetaDBTimeZoneDiffSeconds() {
        /*String dataSource = DataSourceHolder.getCurrentDataSource();
        dataSource = dataSource != null? dataSource: "default_datasource";
        Integer timeZoneDiffSec = metaDBTimeZoneDiffMap.get(dataSource);
        if (timeZoneDiffSec == null) {
            timeZoneDiffSec = resourceService.getSecondsDiffBetweenLocalAndGMT();
            metaDBTimeZoneDiffMap.put(dataSource, timeZoneDiffSec);
        }
        return timeZoneDiffSec;*/
        return 28800;
    }

    private String makeLog(String action, String result) {
        return "response message, #message[" +
            "action:" +
            action +
            ", result:" +
            result +
            "]";
    }

    public String getAndCheckConnType(Map<String, String> actionParams, String paramName) throws RdsException {
        if (paramName == null) {
            paramName = ParamConstants.DB_INSTANCE_CONN_TYPE;
        }
        String connType = getParameterValue(actionParams, paramName);
        return CheckUtils.checkConnType(connType);
    }

    /**
     * 判断是否为备份验证服务产生的克隆实例
     *
     * @return
     */
    public boolean isBvsInstance(Map<String, String> actionParams) {
        String accessId = getParameterValue(actionParams, ParamConstants.ACCESSID);
        String targetUid = getParameterValue(actionParams, ParamConstants.TARGET_UID);
        if ("BVS".equals(accessId) && "apsaradb_bvs".equals(targetUid)) {
            return true;
        }
        return false;
    }

    /**
     * 获取主机实例角色
     */
    public Integer getAndCheckInstanceRole(Map<String, String> actionParams) throws RdsException {
        String instanceRoleStr = getParameterValue(actionParams, ParamConstants.INSTANCE_ROLE);
        Integer instanceRole = ParamConstants.INSTANCE_ROLE_MASTER; //默认值
        if (instanceRoleStr != null) {
            instanceRole = SupportUtils.strToint(instanceRoleStr);
        }
        return instanceRole;
    }

    public String getAndCheckDownloadUrl(Map<String, String> actionParams) throws RdsException {
        String downloadUrl = Base64Decoder.decode(getParameterValue(actionParams, ParamConstants.DOWNLOAD_URL));
        //FIXME 增加下载地址有效性的校验
        if (Validator.isNull(downloadUrl)) {
            throw new RdsException(ErrorCode.INVALID_DOWNLOAD_URL);
        }
        return downloadUrl;
    }

    private static boolean isBase64(String str) {
        String base64Pattern = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";
        return Pattern.matches(base64Pattern, str);
    }

    public String getAndCheckCommand(Map<String, String> actionParams) throws RdsException {
        String parameterValue = getParameterValue(actionParams, ParamConstants.COMMAND);
        parameterValue = parameterValue.replaceAll(" ", "+");
        logger.info("parameterValue:" + parameterValue);
        if (!isBase64(parameterValue)){
            throw new RdsException(ErrorCode.INVALID_BASE64);
        }
        String command = Base64Decoder.decode(parameterValue);
        if (Validator.isNull(command)) {
            throw new RdsException(ErrorCode.INVALID_BASE64);
        }
        return command;
    }

    public CheckBaksetDO getAndCheckCheckBakset(Map<String, String> actionParams) throws RdsException {
        String baksetName = this.getParameterValue(actionParams, ParamConstants.BAKSET_NAME);
        String checksum = this.getParameterValue(actionParams, ParamConstants.CHECKSUM);
        if (Validator.isNull(baksetName) || Validator.isNull(checksum)) {
            throw new RdsException(ErrorCode.INVALID_BAKSET_NAME);
        }

        CheckBaksetDO checkBakset = bakService.getCheckBaksetByBaksetName(baksetName);
        if (checkBakset == null) {
            throw new RdsException(ErrorCode.INVALID_BAKSET_NAME);
        }
        // 校验Checksum
        if (!checksum.equals(checkBakset.getChecksum())) {
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        return checkBakset;
    }

    /**
     * 获取InstanceGroupId参数
     *
     * @return
     * @throws RdsException
     */
    public List<String> getAndCheckInstanceGroupId(Map<String, String> actionParams) throws RdsException {
        String groupIds = getParameterValue(actionParams, ParamConstants.INSTANCE_GROUP_ID);
        if (org.apache.commons.lang3.StringUtils.isBlank(groupIds)) {
            throw new RdsException(ErrorCode.INVALID_INSTANCE_GROUP_ID);
        }
        return Arrays.asList(groupIds.split(","));
    }

    /**
     * 获取InstanceGroupId参数
     *
     * @return
     * @throws RdsException
     */
    public String getAndCheckResourceGroupId(Map<String, String> actionParams) throws RdsException {
        String groupId = getParameterValue(actionParams, ParamConstants.RESOURCE_GROUP_ID, "").trim();
        if (groupId != null && groupId.length() > 256) {
            throw new RdsException(ErrorCode.INVALID_INSTANCE_GROUP_ID);
        }
        return groupId;
    }

    /**
     * 获取InstanceGroupId参数
     *
     * @return
     * @throws RdsException
     */
    public List<String> getInstanceGroupId(Map<String, String> actionParams) throws RdsException {
        String groupIds = getParameterValue(actionParams, ParamConstants.INSTANCE_GROUP_ID);
        if (org.apache.commons.lang3.StringUtils.isBlank(groupIds)) {
            return new ArrayList<String>(0);
        }
        return Arrays.asList(groupIds.split(","));
    }

    /**
     * @return null if no specified parameter found
     * @throws RdsException if specified parameter not valid
     */
    public List<Integer> getAndCheckHostStatus(Map<String, String> actionParams) throws RdsException {
        String str = getParameterValue(actionParams, ParamConstants.HOST_STATUS);
        if (str == null) {
            return null;
        }

        List<Integer> list = new ArrayList<Integer>();
        String[] hostStatusList = SupportUtils.splitToArray(str, ",");
        for (String hostStatus : hostStatusList) {
            Integer status = CustinsValidator.getRealNumber(hostStatus);
            if (status < 0) {
                throw new RdsException(ErrorCode.INVALID_HOST_STATUS);
            } else {
                list.add(status);
            }
        }
        return list;
    }

    /**
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckHostId(Map<String, String> actionParams) throws RdsException {
        String str = getParameterValue(actionParams, ParamConstants.HOST_ID);
        Integer hostId = CustinsValidator.getRealNumber(str);
        if (hostId < 0) {
            throw new RdsException(ErrorCode.INVALID_HOST_ID);
        }
        return hostId;
    }

    public String getAndCheckHostName(Map<String, String> actionParams) {
        String hostName = getParameterValue(actionParams, ParamConstants.HOST_NAME);
        return StringUtils.isEmpty(hostName) ? null : hostName;
    }

    public Integer getAndCheckHostIsAvail(Map<String, String> actionParams) throws RdsException {
        String str = getParameterValue(actionParams, ParamConstants.ISAVAIL);
        Integer isAvail = null;
        try {
            isAvail = Integer.parseInt(str);
            if (isAvail != 0 && isAvail != 1) {
                throw new RdsException(ErrorCode.INVALID_HOST_ISAVAIL);
            }
        } catch (NumberFormatException e) {
            throw new RdsException(ErrorCode.INVALID_HOST_ISAVAIL);
        }
        return isAvail;
    }

    public Set<Integer> getAndCheckHostIdSet(Map<String, String> actionParams) throws RdsException {
        Set<Integer> hostIdSet = new HashSet<Integer>();
        String hostIdStr = getParameterValue(actionParams, ParamConstants.HOST_ID);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(hostIdStr)) {
            String[] hostIds = hostIdStr.split(",");
            for (String hostId : hostIds) {
                hostIdSet.add(CustinsValidator.getRealNumber(hostId.trim()));
            }
        }
        return hostIdSet;
    }

    public String getAndCheckTransType(Map<String, String> actionParams) throws RdsException {
        String specifyTransType = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_TRANS_TYPE,
            CustinsSupport.CUSTINS_TRANS_TYPE_DEFAULT);
        if (!CustinsSupport.CUSTINS_TRANS_TYPE_SET.contains(specifyTransType)) {
            throw new RdsException(ErrorCode.INVALID_TRANS_TYPE);
        }
        return specifyTransType;
    }

    public String getAndCheckSccAction(Map<String, String> actionParams) throws RdsException {
        String sccType = getParameterValue(actionParams, ServerlessConstant.ACTION_TYPE);
        if (!ServerlessConstant.ACTION_TYPE_LIST.contains(sccType.toLowerCase())) {
            throw new RdsException(ErrorCode.INVALID_SCAL_TYPE);
        }
        return sccType;
    }

    public String getAndCheckRebuildType(Map<String, String> actionParams) throws RdsException {
        String rebuildType = getParameterValue(actionParams, ParamConstants.SLAVE_REBUILD_TYPE,
            CustinsSupport.SLAVE_REBUILD_TYPE_REMOTE);
        if (!CustinsSupport.SLAVE_REBUILD_TYPE_SET.contains(rebuildType)) {
            throw new RdsException(ErrorCode.INVALID_SLAVE_REBUILD_TYPE);
        }

        return rebuildType;
    }

    /**
     * @param classCode
     * @return
     */
    public boolean displayCpuCores(String classCode) {
        if (System.currentTimeMillis() - classCodeSetUpdateTimestamp > 10 * 60 * 1000L) {
            classCodeSetUpdateTimestamp = System.currentTimeMillis();
            List<ResourceDO> resourceList = resourceService.getResourceListByResourceKey(
                ResourceKey.RESOURCE_CLASSCODE_NOT_DISPLAY_CPU_CORES);
            Set<String> newClassCodeSet = new HashSet<String>();
            for (ResourceDO resource : resourceList) {
                newClassCodeSet.add(resource.getRealValue());
            }
            classCodeSetNotDisplayCpuCores = newClassCodeSet;
        }
        return !classCodeSetNotDisplayCpuCores.contains(classCode);
    }

    /**
     * 校验实例切换时间
     *
     * @return
     * @throws RdsException
     */
    public Date getAndCheckSwitchTime(Map<String, String> actionParams) throws RdsException {
        String switchTimeStr = getParameterValue(actionParams, ParamConstants.SWITCH_TIME);
        if (switchTimeStr == null) {
            return null;
        }

        // validate switch time
        DateTime now = new DateTime(DateTimeZone.UTC);
        Integer expire = resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_SWITCH_TIME_ALLOWED);
        try {
            Date switchTimeUTC = DateSupport.str2second_gmt(switchTimeStr);
            if (switchTimeUTC.getTime() <= now.getMillis()
                || switchTimeUTC.getTime() > now.plusHours(expire).getMillis()) {
                throw new RdsException(ErrorCode.INVALID_SWITCH_TIME);
            }
        } catch (ParseException e) {
            throw new RdsException(ErrorCode.INVALID_SWITCH_TIME);
        }

        return getAndCheckTimeByParam(actionParams, ParamConstants.SWITCH_TIME,
            DateUTCFormat.SECOND_UTC_FORMAT,
            ErrorCode.INVALID_SWITCH_TIME);
    }

    /**
     * 获取静默指定小时的utc时间
     * @return
     */
    public String getUtcTimeForSliceHours(Integer hours) {
        DateTime curTime = new DateTime();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US);
        TimeZone utcZone = TimeZone.getTimeZone("UTC");
        sf.setTimeZone(utcZone);
        DateTime getUTC = curTime.plusHours(hours);
        String  dateTimeString = sf.format(getUTC.toDate());
        return dateTimeString;
    }

    /**
     * 判断是否强制flush 返回 1 :强制 返回 0 : 不强制
     *
     * @return
     * @throws RdsException
     */
    public Integer getAndCheckIsForce(Map<String, String> actionParams) throws RdsException {
        String isforce = "";
        if (hasParameter(actionParams, ParamConstants.IS_FORCE)) {
            isforce = getParameterValue(actionParams, ParamConstants.IS_FORCE);
        }
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("1", isforce)) {
            return 1;
        }
        return 0;
    }

    /**
     * 获取Proxy Host ID
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckProxyHostId(Map<String, String> actionParams) throws RdsException {
        String str = getParameterValue(actionParams, ParamConstants.PROXY_HOST_ID);
        Integer proxyHostId = CustinsValidator.getRealNumber(str);
        if (proxyHostId <= 0) {
            throw new RdsException(ErrorCode.INVALID_PROXY_HOST_ID);
        }
        return proxyHostId;
    }

    /**
     * 获取Proxy组名
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckProxyGroupName(Map<String, String> actionParams) throws RdsException {
        String proxyGroupName = getParameterValue(actionParams, ParamConstants.PROXY_GROUP_NAME);
        if (proxyGroupName == null || proxyGroupName == "") {
            throw new RdsException(ErrorCode.INVALID_PROXY_GROUP_NAME);
        }
        return proxyGroupName;
    }

    /**
     * 获取等待连接主动关闭的等待时长
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckWaitSessionCloseTimeout(Map<String, String> actionParams) throws RdsException {
        String str = getParameterValue(actionParams, ParamConstants.PROXY_WAIT_SESSION_CLOSE_TIMEOUT);
        Integer waitSessionCloseTimeout = CustinsValidator.getRealNumber(str);
        if (waitSessionCloseTimeout < 0 || waitSessionCloseTimeout > 365) {
            throw new RdsException(ErrorCode.INVALID_WAIT_SESSION_CLOSE_TIMEOUT);
        }
        return waitSessionCloseTimeout;
    }

    /**
     * 获取可强制关闭的连接数
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckMaxSessionNum(Map<String, String> actionParams) throws RdsException {
        String str = getParameterValue(actionParams, ParamConstants.PROXY_MAX_SESSION_NUM);
        Integer maxSessionNum = CustinsValidator.getRealNumber(str);
        if (maxSessionNum < 0 || maxSessionNum > 10000) {
            throw new RdsException(ErrorCode.INVALID_MAX_SESSION_NUM);
        }
        return maxSessionNum;
    }

    /**
     * 获取Proxy Delay Stop 的最大等待时长
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckDelayStopTimeout(Map<String, String> actionParams) throws RdsException {
        String str = getParameterValue(actionParams, ParamConstants.DELAY_STOP_TIMEOUT);
        Integer delayStopTimeout = CustinsValidator.getRealNumber(str);
        if (delayStopTimeout < 0 || delayStopTimeout > 3600) {
            throw new RdsException(ErrorCode.INVALID_DELAY_STOP_TIMEOUT);
        }
        return delayStopTimeout;
    }

    /**
     * 获取强制下线Proxy节点开关状态
     *
     * @return not null
     * @throws RdsException
     */
    public boolean getAndCheckForceOffline(Map<String, String> actionParams) throws RdsException {
        String proxyForceOffline = getParameterValue(actionParams, ParamConstants.PROXY_FORCE_OFFLINE);
        if (org.apache.commons.lang3.StringUtils.isBlank(proxyForceOffline)) {
            return false;
        }

        if (proxyForceOffline.toLowerCase().equals("false")) {
            return false;
        } else if (proxyForceOffline.toLowerCase().equals("true")) {
            return true;
        } else {
            throw new RdsException(ErrorCode.INVALID_PROXY_FORCE_OFFLINE);
        }
    }

    /**
     * 获取切换的新分组
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckNewProxyGroupName(Map<String, String> actionParams) throws RdsException {
        String newProxyGroupName = getParameterValue(actionParams, ParamConstants.NEW_PROXY_GROUP_NAME);
        if (org.apache.commons.lang3.StringUtils.isBlank(newProxyGroupName)) {
            throw new RdsException(ErrorCode.INVALID_NEW_PROXY_GROUP_NAME);
        }
        return newProxyGroupName;
    }

    /**
     * 获取Proxy集群名
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckProxyClusterName(Map<String, String> actionParams) throws RdsException {
        String proxyClusterName = getParameterValue(actionParams, ParamConstants.PROXY_CLUSTER_NAME);
        if (proxyClusterName == null || proxyClusterName == "") {
            throw new RdsException(ErrorCode.INVALID_PROXY_CLUSTER_NAME);
        }
        return proxyClusterName;
    }

    /**
     * 获取Site列表
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckSiteListString(Map<String, String> actionParams) throws RdsException {
        String siteListString = getParameterValue(actionParams, ParamConstants.SITE_LIST);
        if (siteListString == null || siteListString == "") {
            throw new RdsException(ErrorCode.INVALID_SITE_LIST);
        }
        return siteListString;
    }

    /**
     * 获取Proxy Role
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckProxyRole(Map<String, String> actionParams) throws RdsException {
        String proxyRole = getParameterValue(actionParams, ParamConstants.PROXY_ROLE);
        if (proxyRole == null || proxyRole == "") {
            throw new RdsException(ErrorCode.INVALID_PROXY_ROLE);
        }
        return proxyRole;
    }

    /**
     * 获取Proxy Node RPM Version
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckRPMVersion(Map<String, String> actionParams) throws RdsException {
        String rpmVersion = getParameterValue(actionParams, ParamConstants.RPM_VERSION);
        if (rpmVersion == null || rpmVersion == "") {
            throw new RdsException(ErrorCode.INVALID_RPM_VERSION);
        }
        return rpmVersion;
    }

    /**
     * 获取扩容节点数
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckAddNodeNumber(Map<String, String> actionParams) throws RdsException {
        String str = getParameterValue(actionParams, ParamConstants.ADD_NODE_NUMBER);
        Integer addNodeNumber = CustinsValidator.getRealNumber(str);
        if (addNodeNumber <= 0) {
            throw new RdsException(ErrorCode.INVALID_ADD_NODE_NUMBER);
        }
        return addNodeNumber;
    }

    /**
     * 获取Creator
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckCreator(Map<String, String> actionParams) throws RdsException {
        String creator = getParameterValue(actionParams, ParamConstants.CREATOR);
        if (creator == null || creator == "") {
            throw new RdsException(ErrorCode.INVALID_CREATOR);
        }
        return creator;
    }

    /**
     * 获取IP列表
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckIPListString(Map<String, String> actionParams) throws RdsException {
        String ipListString = getParameterValue(actionParams, ParamConstants.IP_LIST);
        if (ipListString == null || ipListString == "") {
            throw new RdsException(ErrorCode.INVALID_IP_LIST);
        }
        return ipListString;
    }

    /**
     * 获取&解析外部参数
     *
     * @return
     * @throws RdsException
     */
    public Map<String, List<MycnfCustinstanceDO>> getAndCheckExternalParameter(Map<String, String> actionParams)
        throws RdsException {
        String custInsDbType = this.getAndCheckDBType(actionParams, null);
        String custInsDbVersion = this.getAndCheckDBVersion(actionParams, custInsDbType, true);
        String externalParamStr = getParameterValue(actionParams, ParamConstants.EXTERNAL_PARAMETER);
        Map<String, List<MycnfCustinstanceDO>> externalParam = new HashMap<>();

        EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersion(custInsDbType,
            custInsDbVersion);
        JSONObject jsonServices = JSON.parseObject(engineCompose.getServices());

        if (org.apache.commons.lang3.StringUtils.isNotBlank(externalParamStr)) {
            try {
                JSONObject externalParamJSON = JSON.parseObject(externalParamStr);
                for (String dbType : externalParamJSON.keySet()) {
                    // 根据外部传参的dbType和engine_compose中该dbType对应的dbVersion查询MycnfTemplate表对应的参数
                    EngineService engineService = new Gson().fromJson(
                        jsonServices.getString(dbType), EngineService.class);
                    if (engineService == null) {
                        ErrorCode errorCode = ErrorCode.INVALID_EXTERNAL_PARAMETER_DBTYPE.resetDesc();
                        errorCode.setDesc(String.format(errorCode.getDesc(), dbType));
                        throw new RdsException(errorCode);
                    }

                    List<MycnfTemplateDO> mycnfTemplateDOList = mycnfService
                        .getMycnfTemplateDOListByDbTypeAndDbVersion(dbType, engineService.getVersion());
                    List<String> mycnfTemplateParameterNames = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(mycnfTemplateDOList)) {
                        for (MycnfTemplateDO mycnfTemplateDO : mycnfTemplateDOList) {
                            mycnfTemplateParameterNames.add(mycnfTemplateDO.getName());
                        }
                    }

                    JSONObject params = externalParamJSON.getJSONObject(dbType);
                    List<MycnfCustinstanceDO> engineParams = new ArrayList<>();
                    for (String paramName : params.keySet()) {
                        if (mycnfTemplateParameterNames.contains(paramName)) {
                            ErrorCode errorCode = ErrorCode.INVALID_EXTERNAL_PARAMETER_NAME.resetDesc();
                            errorCode.setDesc(String.format(errorCode.getDesc(), paramName, dbType));
                            throw new RdsException(errorCode);
                        }
                        MycnfCustinstanceDO cnf = new MycnfCustinstanceDO();
                        cnf.setName(paramName);
                        cnf.setParaValue(params.getString(paramName));
                        engineParams.add(cnf);
                    }
                    externalParam.put(dbType, engineParams);
                }
            } catch (RdsException e) {
                logger.error("Invalid external parameter json format. ", e);
                throw new RdsException(e.getErrorCode());
            } catch (Exception e) {
                logger.error("Invalid external parameter json format. ", e);
                throw new RdsException(ErrorCode.INVALID_EXTERNAL_PARAMETER);
            }
        }
        return externalParam;
    }

    public String getAndCheckProxyApiVersion(Map<String, String> actionParams, String clusterName) throws RdsException {

        String proxyApiVersion = getParameterValue(actionParams, "proxyapiversion");
        if (org.apache.commons.lang3.StringUtils.isEmpty(proxyApiVersion)) {
            proxyApiVersion = clusterService.getDefaultAppVersionOfCluster(clusterName, "proxyapi");
            if (proxyApiVersion == null) {
                logger.error("Can not get default proxyapi version of cluster " + clusterName);
                throw new RdsException(ErrorCode.INVALID_PROXY_API_VERSION);
            }
        } else {
            ClusterAppVersionsDO clusterAppVersionsDO = clusterService.getAppVersionOfCluster(clusterName, "proxyapi",
                proxyApiVersion);
            if (clusterAppVersionsDO == null) {
                throw new RdsException(ErrorCode.INVALID_PROXY_API_VERSION);
            }
        }
        return proxyApiVersion;
    }

    public String getAndCheckProxyNodeVersion(Map<String, String> actionParams, String clusterName)
        throws RdsException {

        String proxyNodeVersion = getParameterValue(actionParams, "proxynodeversion");
        if (org.apache.commons.lang3.StringUtils.isEmpty(proxyNodeVersion)) {
            proxyNodeVersion = clusterService.getDefaultAppVersionOfCluster(clusterName, "proxynode");
            if (proxyNodeVersion == null) {
                logger.error("Can not get default proxynode version of cluster " + clusterName);
                throw new RdsException(ErrorCode.INVALID_PROXY_NODE_VERSION);
            }
        } else {
            ClusterAppVersionsDO clusterAppVersionsDO = clusterService.getAppVersionOfCluster(clusterName, "proxynode",
                proxyNodeVersion);
            if (clusterAppVersionsDO == null) {
                throw new RdsException(ErrorCode.INVALID_PROXY_NODE_VERSION);
            }
        }
        return proxyNodeVersion;
    }

    public String getAndCheckClusterName(Map<String, String> actionParams) throws RdsException {//Cluster Name
        String clusterName = getParameterValue(actionParams, ParamConstants.CLUSTER_NAME);
        if (clusterName == null || clusterName.equals("")) {
            throw new RdsException(ErrorCode.INVALID_PROXY_CLUSTER_NAME);
        } else {
            return clusterName;
        }
    }

    public Boolean getAndCheckEnablePartition(Map<String, String> actionParams) throws RdsException {//Enable Partition
        String enablePartition = getParameterValue(actionParams, ParamConstants.ENABLE_PARTITION);
        if (org.apache.commons.lang3.StringUtils.isBlank(enablePartition)) {
            return false;
        }
        if (enablePartition.toLowerCase().equals("false")) {
            return false;
        } else if (enablePartition.toLowerCase().equals("true")) {
            return true;
        } else {
            throw new RdsException(ErrorCode.INVALID_ENABLE_PARTITION);
        }
    }

    public Boolean getAndCheckOnlyGenerateData(Map<String, String> actionParams) throws RdsException {
        String onlyGenerateData = getParameterValue(actionParams, "OnlyGenerateData");
        if (org.apache.commons.lang3.StringUtils.isBlank(onlyGenerateData)) {
            return false;
        }
        if (onlyGenerateData.toLowerCase().equals("false")) {
            return false;
        } else if (onlyGenerateData.toLowerCase().equals("true")) {
            return true;
        } else {
            throw new RdsException(ErrorCode.HAPROXY_CREATING_ERROR, "OnlyGenerateData only can be 'true' of 'false'.");
        }
    }

    /**
     * add by fengyi (jianming.wjm) to split oss urls split mulit-oss url to list
     */
    public List<String> getOssUrls(String ossUrls) {
        List<String> ossUrlList = Collections.emptyList();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(ossUrls)) {
            String[] urls = ossUrls.split("\\|", -1);
            ossUrlList = Arrays.asList(urls);
        }
        return ossUrlList;
    }

    public String getFileNameOnOss(String ossUrl) {
        String rt = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(ossUrl)) {
            int index = ossUrl.lastIndexOf("/");
            String temp = ossUrl.substring(index + 1);

            rt = temp.split("\\?")[0];

        }
        return rt;
    }

    public String getFileExtension(String fileName) {
        String rt = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(fileName)) {
            int index = fileName.lastIndexOf(".");
            rt = fileName.substring(index + 1);
        }
        return rt;
    }

    public boolean checkFileExtensionValidity(String[] acceptExtArray, String targetValue) {
        for (String ext : acceptExtArray) {
            if (ext.equalsIgnoreCase(targetValue)) {
                return true;
            }
        }
        return false;
    }

    /*
     * we don't acccept users' data name is in list:
     * [master, tempdb, msdb, model, distribution]
     *
     * we don't accept users' database name is sys_info for top biz(聚石塔)
     * top biz for mssql system name is sys_info by default
     *
     * if users' db name is in this list, return false
     * **/
    public boolean checkDBNameValidity(String[] exceptDBArray, String targetDb) {
        for (String ext : exceptDBArray) {
            if (ext.equalsIgnoreCase(targetDb)) {
                return false;
            }
        }
        return true;
    }

    public String getAndCheckEcsSecurityGroupId(Map<String, String> actionParams, boolean mustExist)
        throws RdsException {
        String securityGroupId = getParameterValue(actionParams, ParamConstants.ECS_SECURITY_GROUP_ID);
        if (securityGroupId == null) {
            if (mustExist) {
                throw new RdsException(ErrorCode.INVALID_ECS_SECURITY_GROUP_ID);
            }
            return null;
        }

        if (securityGroupId.length() == 0) {
            return "";
        }

        String[] securityGroupIdList = securityGroupId.split(",");
        if (securityGroupIdList.length != 1) {
            throw new RdsException(ErrorCode.INVALID_ECS_SECURITY_GROUP_ID);
        }

        for (String one : securityGroupIdList) {
            if (one.length() > 50) {
                throw new RdsException(ErrorCode.INVALID_ECS_SECURITY_GROUP_ID);
            }
        }
        return securityGroupId;
    }

    public Map<String, Object> genPbdTaskStat(List<AllocateResRespModel.CustinsResRespModel> custinsResRespModelList) {
        Map<String, Object> pbdTaskStat = new HashMap<String, Object>();
        for (AllocateResRespModel.CustinsResRespModel custModel : custinsResRespModelList) {
            if (custModel.getPbdRespModelList() != null) {
                List<Map<String, Object>> pbd_status = new ArrayList<Map<String, Object>>();
                for (PbdRespModel pbdRespModel : custModel.getPbdRespModelList()) {
                    Map<String, Object> pbd_stat = new HashMap<String, Object>(2);
                    pbd_stat.put("pbd_name", pbdRespModel.getPbdName());
                    pbd_stat.put("task_id", pbdRespModel.getTaskId());
                    pbd_status.add(pbd_stat);
                }
                pbdTaskStat.put(String.valueOf(custModel.getCustinsId()), pbd_status);
            }

        }

        return pbdTaskStat;
    }

    public List<PbdResModel> genPbdModelList(EngineService engineService, CustInstanceDO logicCustins,
                                             Long storageNum) {
        List<PbdResModel> pbdResModelList = new ArrayList<PbdResModel>();

        if (engineService.getStorage().getstorageDataPathInfo().isNeedapply()
            && engineService.getStorage().getstorageDataPathInfo().getType().equals("pbd")) {
            String pbd_name = "pbd_" + "data_dir_" + String.valueOf(logicCustins.getId()) + "_" +
                engineService.getStorage().getstorageDataPathInfo().getLabel();
            String Label = engineService.getStorage().getstorageDataPathInfo().getLabel();
            PbdResModel pbdResModel = new PbdResModel(pbd_name);
            pbdResModel.setNickName(pbd_name);
            pbdResModel.setPbdSize(Integer.valueOf(storageNum.intValue() / 1024 + 10));
            pbdResModel.setLabel(Label);
            pbdResModelList.add(pbdResModel);
        }
        if (engineService.getStorage().getstorageLogPathInfo().isNeedapply()
            && engineService.getStorage().getstorageLogPathInfo().getType().equals("pbd")) {
            String pbd_name = "pbd_" + "log_dir_" + String.valueOf(logicCustins.getId()) + "_" +
                engineService.getStorage().getstorageLogPathInfo().getLabel();
            String Label = engineService.getStorage().getstorageLogPathInfo().getLabel();
            PbdResModel pbdResModel = new PbdResModel(pbd_name);
            pbdResModel.setNickName(pbd_name);
            pbdResModel.setPbdSize(Integer.valueOf(storageNum.intValue() / 1024 + 10));
            pbdResModel.setLabel(Label);
            pbdResModelList.add(pbdResModel);
        }

        return pbdResModelList;
    }

    /**
     * 获取规格对应的container_type和host_type,并设到parameter中
     *
     * @param dbType
     * @param dbVersion
     * @param classCode
     * @throws RdsException
     */
    public void getAndSetContainerTypeAndHostTypeIfEmpty(Map<String, String> actionParams, String dbType,
                                                         String dbVersion,
                                                         String classCode) throws RdsException {
        // TODO: force use host_type from ins level?
        if (StringUtils.isNotBlank(classCode) &&
            (!hasParameter(actionParams, ParamConstants.CONTAINER_TYPE)
                || !hasParameter(actionParams, ParamConstants.CUSTINS_HOST_TYPE))) {
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                dbType, dbVersion, null, null);
            if (insLevel == null) {
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            // do not has container_type and hos                         t_type params, try get from instance_level
            if (!hasParameter(actionParams, ParamConstants.CONTAINER_TYPE)) {
                getAndSetContainerTypeFromInsLevel(actionParams, insLevel);
            }
            if (!hasParameter(actionParams, ParamConstants.CUSTINS_HOST_TYPE)) {
                getAndSetHostTypeFromInsLevel(actionParams, insLevel);
            }
        }
    }

    /**
     * 获取规格对应的contaner_type,并设到parameter中
     *
     * @param insLevel
     * @throws RdsException
     */
    public void getAndSetContainerTypeFromInsLevel(Map<String, String> actionParams, InstanceLevelDO insLevel)
        throws RdsException {
        if (StringUtils.isNotBlank(insLevel.getExtraInfo())) {
            DockerInsLevelParseConfig config = custinsService
                .parseDockerInsExtraInfo(insLevel.getExtraInfo());
            String containerType = config.getContainerType();
            if (StringUtils.isNotEmpty(containerType)) {
                setParameter(actionParams, ParamConstants.CONTAINER_TYPE, containerType);
            }
        }
    }

    /**
     * 获取规格对应的host_type,并设到parameter中
     *
     * @param insLevel
     * @throws RdsException
     */
    public void getAndSetHostTypeFromInsLevel(Map<String, String> actionParams, InstanceLevelDO insLevel)
        throws RdsException {
        Integer hostType = insLevel.getHostType();
        setParameter(actionParams, ParamConstants.CUSTINS_HOST_TYPE, hostType.toString());
    }

    /**
     * 获取实例规格信息
     *
     * @param custins
     * @param classCode
     * @return
     * @throws RdsException
     */
    public CustInstanceDO setInstanceLevel(CustInstanceDO custins, String classCode,
                                           Integer bizType,
                                           String diskSize) throws RdsException {
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
            custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
        if (insLevel == null) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        } else {
            custins.setLevelId(insLevel.getId());
            custins.setDiskSize(insLevel.getDiskSize());
        }
        if (Validator.isNotNull(diskSize) && custins.isExcluse()) {
            setDiskSize(custins, bizType, diskSize);
        }
        custins.setCharacterType(insLevel.getCharacterType());
        return custins;
    }

    public void setDiskSize(CustInstanceDO custins, Integer bizType, String diskSize) throws RdsException {
        ResourceKey resourceKey = ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE;
        if (CustinsSupport.BIZ_TYPE_PARTITION.equals(bizType)) {
            resourceKey = ResourceKey.RESOURCE_PARTITION_MAX_DISK_SIZE;
        }
        Integer maxDiskSize = resourceSupport.getIntegerRealValue(resourceKey);
        custins.setDiskSize(
            CheckUtils.parseInt(diskSize, 5, maxDiskSize, ErrorCode.INVALID_STORAGE)
                * 1024L);
    }

    /**
     * CreateDBInstance, CreateDistributeDBInstance
     *
     * @return
     * @throws RdsException
     */
    public Integer getAndCreateUserId(Map<String, String> actionParams) throws RdsException {
        if (hasParameter(actionParams, ParamConstants.INNER_USER_ID)) {
            return CustinsValidator.getRealNumber(getParameterValue(actionParams, ParamConstants.INNER_USER_ID), -1);
        }
        String bid = getParameterValue(actionParams, "user_id");
        if (StringUtils.isEmpty(bid)) {
            throw new RdsException(ErrorCode.MISSING_USER_ID);
        }
        String uid = getParameterValue(actionParams, "uid");
        if (StringUtils.isEmpty(uid)) {
            throw new RdsException(ErrorCode.MISSING_UID);
        }

        for (int i = 0; i < 3; i++) {
            try {
                Integer userId = null;

                try {
                    userId = userSupport.getUserIdByLoginId(bid + "_" + uid);
                } catch (RdsException e) {
                    logger.warn("LoginID: " + bid + "_" + uid + " doesn't exist, now create it.");
                }

                if (userId == null) {
                    if (!userService.hasBid(bid)) {
                        userService.createBid(bid);
                    }
                    userId = userService.createUser(bid, uid);
                }
                return userId;
            } catch (Exception e) {
                logger.error("Query/Insert user failed, try again." + bid + "_" + uid, e);
                try {
                    Thread.sleep(new Random().nextInt(100) + 100);
                }
                catch (InterruptedException ignore) {

                }
            }
        }
        throw new RdsException(ErrorCode.INVALID_UID);
    }

    public Integer getUserId(Map<String, String> actionParams) throws RdsException {
        if (hasParameter(actionParams, ParamConstants.INNER_USER_ID)) {
            return CustinsValidator.getRealNumber(getParameterValue(actionParams, ParamConstants.INNER_USER_ID), -1);
        }
        String bid = getParameterValue(actionParams, "user_id");
        if (StringUtils.isEmpty(bid)) {
            logger.info("The request is missing a user_id parameter.");
            return -1;
        }
        String uid = getParameterValue(actionParams, "uid");
        if (StringUtils.isEmpty(uid)) {
            logger.info("The request is missing a uid parameter.");
            return -1;
        }

        Integer userId = null;
        try {
            userId = userSupport.getUserIdByLoginId(bid + "_" + uid);
        } catch (RdsException e) {
            logger.warn("LoginID: " + bid + "_" + uid + " doesn't exist");
        }
        if (userId == null) {
            return -1;
        }
        return userId;
    }




    public CustInstanceDO getAndCheckCustInstance(Map<String, String> actionParams) throws RdsException {
        return this.getAndCheckCustInstance(actionParams,0);
    }
    public String getAndCheckGeneralCategoryGroupName(Map<String, String> actionParams) throws RdsException {
        String groupName = this.getParameterValue(actionParams,MySQLParamConstants.PARAM_NAME_GENERAL_CATEGORY_GROUP_NAME);
        if (org.apache.commons.lang3.StringUtils.isEmpty(groupName)) {
            throw new RdsException(MysqlErrorCode.MISSING_PARAM_GROUP_NAME.toArray());
        }
        return groupName;
    }


    public CustInstanceDO getAndCheckCustInstance(Map<String, String> actionParams, Integer isTmp) throws RdsException {
        Integer userId = this.getAndCheckUserId(actionParams);
        CustInstanceDO custins;
        if (INTERNAL_SYSTEM.equals(this.getBID(actionParams))) {
            custins = custinsService.getCustInstanceByInsName(null,
                this.getAndCheckDBInstanceName(actionParams), isTmp);
        } else {
            custins = custinsService.getCustInstanceByInsName(userId,
                this.getAndCheckDBInstanceName(actionParams), isTmp);
        }
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public boolean isReadSwitch(CustInstanceDO custins) {
        return (custins.isRead() || custins.isReadBackup()) && CUSTINS_STATUS_SWITCH.equals(custins.getStatus());
    }

    public boolean isPreserveOldLink(Map<String, String> actionParams) throws RdsException {
        return "1".equals(actionParams.get("preserveoldlink"));
    }

    public boolean cloneValidSrcCustins(CustInstanceDO custins) throws RdsException {


        if (custins.isShare() || custins.isReadOrBackup() || custins.isSub() || !custins.isLogicPrimary()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        // 去除判断源实例是否在clone中，允许并发clone
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        if (custins.isReadAndWriteLock()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }

        return true;
    }

    public CustInstanceDO getCustInstance(Map<String, String> actionParams) throws RdsException {
        return custinsService.getCustInstanceByInsName(this.getAndCheckUserId(actionParams),
            this.getAndCheckDBInstanceName(actionParams));
    }

    public CustInstanceDO getAndCheckSourceCustInstance(Map<String, String> actionParams) throws RdsException {
        Integer userId = this.getAndCheckUserId(actionParams);
        CustInstanceDO custins = null;
        if (INTERNAL_SYSTEM.equals(this.getBID(actionParams))) {
            custins = custinsService.getCustInstanceByInsName(null,
                this.getAndCheckSourceDBInstanceName(actionParams), 0);
        } else {
            custins = custinsService.getCustInstanceByInsName(userId,
                this.getAndCheckSourceDBInstanceName(actionParams), 0);
        }
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public CustInstanceDO getAndCheckTargetCustInstance(Map<String, String> actionParams) throws RdsException {
        Integer userId = this.getAndCheckUserId(actionParams);
        CustInstanceDO custins = null;
        if (INTERNAL_SYSTEM.equals(this.getBID(actionParams))) {
            custins = custinsService.getCustInstanceByInsName(null,
                this.getAndCheckTargetDBInstanceName(actionParams), 0);
        } else {
            custins = custinsService.getCustInstanceByInsName(userId,
                this.getAndCheckTargetDBInstanceName(actionParams), 0);
        }
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public boolean cloneValidCommon(CustInstanceDO srcCustins, Map<String,String> actionParams) throws RdsException {
        return cloneValidCommon(srcCustins, false, actionParams);
    }

    public boolean cloneValidCommon(CustInstanceDO srcCustins, Boolean checkClone, Map<String,String> actionParams) throws RdsException {
        // 判断克隆实例个数是否达到限制
        Map<String, Object> condition = new HashMap<String, Object>(2);
        condition.put("custinsId", srcCustins.getId());
        List<Integer> status = new ArrayList<Integer>();
        status.add(0);
        condition.put("status", status);
        List<Integer> cloneInsList = cloneEnabledCustinsService.getCloneCustInstanceByCondition(condition);
        if (cloneInsList.size() >= resourceSupport.getIntegerRealValue(
            ResourceKey.RESOURCE_CREATING_CLONE_CUSTINS_COUNT)) {
            throw new RdsException(ErrorCode.CREATING_CLONE_INS_EXCEEDED);
        }

        if (!checkClone || hasParameter(actionParams, ParamConstants.DB_INSTANCE_NAME)) {
            String cloneInsName = CheckUtils.checkValidForInsName(getDBInstanceName(actionParams));
            if (custinsService.hasCustInstanceByInsName(cloneInsName)) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }
        }

        if (!checkClone || hasParameter(actionParams, ParamConstants.CONNECTION_STRING)) {
            CheckUtils.checkValidForConnAddrCust(getParameterValue(actionParams, ParamConstants.CONNECTION_STRING));
        }

        return true;
    }

    /**
     * 检查是否目标也是RunD
     * 1、RunC迁移到RunD
     * 2、RunD迁移到RunD
     */
    public PodType getTargetRuntimeType(ReplicaSet replicaSet, Map<String, String> actionParams) {
        try {
            String requestId = getParameterValue(actionParams, ParamConstants.REQUEST_ID);
            Replica replica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems().get(0);
            ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, replica.getId(), null);

            String targetRuntimeType = getParameterValue(actionParams, PodDefaultConstants.Target_Runtime_Type, replicaResource.getVpod().getRuntimeType());
            if (PodType.POD_ECS_RUND.getRuntimeType().equalsIgnoreCase(targetRuntimeType)) {
                if (replicaSetService.isVbmInstance(requestId, replicaResource.getReplicaSetName())) {
                    return PodType.POD_VBM_RUND;
                } else {
                    return PodType.POD_ECS_RUND;
                }
            } else {
                return PodType.POD_RUNC;
            }
        } catch (Exception e) {
            log.error("check is migrate to rund failed, {}", e.getMessage());
            return PodType.POD_RUNC;
        }
    }

    /**
     * 设置实例公共属性
     *
     * @param custins
     * @throws RdsException
     */
    public void updateCustinsCommonProperties(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        // 设置实例名
        custins.setInsName(CheckUtils.checkValidForInsName(getDBInstanceName(actionParams)));
        if (custinsService.hasCustInstanceByInsName(custins.getInsName())) {
            throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
        }

        // 设置ProxyGroupId
        if (hasParameter(actionParams, ParamConstants.PROXY_GROUP_ID)) {
            String proxyGroupId = getParameterValue(actionParams, ParamConstants.PROXY_GROUP_ID);
            Integer perferedProxyGroupId = Integer.valueOf(proxyGroupId);
            custins.setProxyGroupId(perferedProxyGroupId);
        }

        // 设置实例描述
        if (hasParameter(actionParams, ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            String desc = SupportUtils
                .decode(getParameterValue(actionParams, ParamConstants.DB_INSTANCE_DESCRIPTION));
            custins.setComment(CheckUtils
                .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION));
        }

        // 设置实例过期时间
        if (hasParameter(actionParams, ParamConstants.EXPIRED_TIME)) {
            Date expiredTime = getAndCheckTimeByParam(actionParams, ParamConstants.EXPIRED_TIME,
                DateUTCFormat.MINUTE_UTC_FORMAT, ErrorCode.INVALID_EXPIREDTIME);
            if (expiredTime.getTime() <= System.currentTimeMillis()) {
                throw new RdsException(ErrorCode.INVALID_EXPIREDTIME);
            } else {
                custins.setGmtExpired(expiredTime);
            }
        }

        // 设置实例可维护时间
        Date maintainStartTime = getAndCheckTimeByParam(actionParams, ParamConstants.MAINTAIN_STARTTIME, DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
            ErrorCode.INVALID_STARTTIME,
            CustinsSupport.DEFAULT_MAINTAIN_START_TIME);
        Date maintainEndTime = getAndCheckTimeByParam(actionParams, ParamConstants.MAINTAIN_ENDTIME, DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
            ErrorCode.INVALID_ENDTIME, CustinsSupport.DEFAULT_MAINTAIN_END_TIME);
        custins.setMaintainStarttime(Time.valueOf(DateSupport.timeWithSecond2str(maintainStartTime)));
        custins.setMaintainEndtime(Time.valueOf(DateSupport.timeWithSecond2str(maintainEndTime)));

        // 设置是否接受优化建议服务
        String optmization = getParameterValue(actionParams, ParamConstants.OPTMIZATION_SERVICE, "0");
        custins.setIsAccept(Integer.valueOf(CheckUtils.checkBooleanInt(optmization, ErrorCode.INVALID_OPTMIZATIONSERVICE)));
    }

    public String getAndCheckDBDescription(Map<String, String> params) throws RdsException, UnsupportedEncodingException {
        if (CustinsParamSupport.hasParameterValue(params, ParamConstants.DB_DESCRIPTION)) {
            String desc = URLDecoder.decode(getParameterValue(params, ParamConstants.DB_DESCRIPTION, ""), "UTF-8");
            return CheckUtils.checkLength(desc, 1, 256, ErrorCode.INVALID_DBDESCRIPTION);
        }
        return null;
    }

    public void checkBaksetMetaInfo(BaksetMetaInfo baksetMetaInfo, Map<String, String> params) throws RdsException {
        String gtidPurgedKey = "bak_gtid_purged";
        // 判空
        if (baksetMetaInfo.getBaksetName() == null || baksetMetaInfo.getBaksetSize() == null ||
            baksetMetaInfo.getDbType() == null     || baksetMetaInfo.getDbVersion() == null  ||
            baksetMetaInfo.getChecksum() == null   || baksetMetaInfo.getRemoteUrl() == null  ||
            baksetMetaInfo.getExtraInfo() == null) {
            logger.error(baksetMetaInfo.toString());
            throw new RdsException(ErrorCode.NO_AVAILABLE_DISASTER_RESTORE_BAKSET);
        }
        if (baksetMetaInfo.getExtraInfo().get(gtidPurgedKey) == null) {
            if (isMysqlXDBByParams(params)) {
                logger.info("XDB instance, no need to check GTID_PURGED");
            } else {
                logger.error(baksetMetaInfo.toString());
                throw new RdsException(ErrorCode.NO_AVAILABLE_DISASTER_RESTORE_BAKSET);
            }
        }
        String dbType = getAndCheckDBType(params, null);
        String dbVersion = getAndCheckDBVersion(params, dbType, true);
        //仅支持实例级别的物理备份
        boolean invalidBakScale = baksetMetaInfo.getBakScale() != null && !BAK_FOR_INSTANCE.equals(baksetMetaInfo.getBakScale());
        boolean invalidBakWay = baksetMetaInfo.getBakWay() != null && !BAKWAY_XTRABACKUP.equals(baksetMetaInfo.getBakWay());
        if (invalidBakScale || invalidBakWay) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
        }
        //仅支持type为DATA的全量备份
        boolean invalidBakType = baksetMetaInfo.getBakType() != null && !BAKTYPE_FULL.equals(baksetMetaInfo.getBakType());
        boolean invalidType = baksetMetaInfo.getType() != null && !TYPE_DATA.equals(baksetMetaInfo.getType());
        if (invalidBakType || invalidType) {
            throw new RdsException(ErrorCode.INVALID_BAK_TYPE);
        }
        if (baksetMetaInfo.getDbVersion().compareTo(DB_VERSION_MYSQL_56) >= 0) {
            if (baksetMetaInfo.getDbVersion().equals(DB_VERSION_MYSQL_56)){
                if (!dbVersion.equals(DB_VERSION_MYSQL_55) && !dbVersion.equals(DB_VERSION_MYSQL_56)) {
                    throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
                }
            } else if (!baksetMetaInfo.getDbVersion().equals(dbVersion)) {
                throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
            }
        } else {
            throw new RdsException(ErrorCode.UNSUPPORTED_BAKSET_VERSION);
        }
        // tde备份只支持恢复到源region
        String tdeEnabledKey = "tde_enabled";
        String destRegion = getAndCheckRegionID(params);
        if (baksetMetaInfo.getExtraInfo().get(tdeEnabledKey) != null && (boolean)baksetMetaInfo.getExtraInfo().get(tdeEnabledKey)
            && !destRegion.equals(baksetMetaInfo.getSrcRegion())) {
            throw new RdsException(ErrorCode.CROSS_REGION_UNSUPPORT_TDE);
        }

        // 加密备份集只支持恢复到源region
        String encryptKey = "encryption";
        if (baksetMetaInfo.getExtraInfo().get(encryptKey) != null && !baksetMetaInfo.getExtraInfo().get(encryptKey).toString().equalsIgnoreCase("{}")
            && !destRegion.equals(baksetMetaInfo.getSrcRegion())) {
            throw new RdsException(ErrorCode.CROSS_REGION_UNSUPPORT_ENCRYPTED_BACKUP);
        }

        // 容灾恢复只支持恢复到源和目的region
        String backupSetType = getParameterValue(params, "BackupSetType", BACKUP_SET_TYPE_DDR);
        if (BACKUP_SET_TYPE_DDR.equals(backupSetType) && !destRegion.equals(baksetMetaInfo.getSrcRegion())
            && !destRegion.equals(baksetMetaInfo.getDdrRegion())) {
            throw new RdsException(ErrorCode.DISASTER_RESTORE_REGION_NOT_MATCHED);
        }
    }

    /**
     * 判断是否是库表恢复
     * */
    public boolean getAndCheckRestoreTable(Map<String,String> actionParams){

        return hasParameter(actionParams,"RestoreTable");
    }

    public String getAndCheckTableMeta(Map<String, String> actionParams, BaksetMetaInfo ddrBakMetaInfo, String type, String custinsDbVersion, Boolean isCloudDisk) throws RdsException {
        //旧的库表信息，新库名称与原来所有旧库名称不相同
        Map<String,Set<String>> oldDBTablesMap = getDBTablesFromBakMetaInfo(ddrBakMetaInfo, isCloudDisk);
        return getAndCheckTableMeta(actionParams, ddrBakMetaInfo.getDbVersion(), oldDBTablesMap, type, custinsDbVersion);
    }

    public String getAndCheckTableMeta(Map<String, String> actionParams, BakhistoryDO bakhistoryDO, String type, String custinsDbVersion, Boolean isCloudDisk) throws RdsException {
        //旧的库表信息，新库名称与原来所有旧库名称不相同
        Map<String,Set<String>> oldDBTablesMap = getDBTablesFromBakMetaInfo(bakhistoryDO, isCloudDisk);
        return getAndCheckTableMeta(actionParams, bakhistoryDO.getDbVersion(), oldDBTablesMap, type, custinsDbVersion);
    }

    /**
     * TableMeta参数校验
     * 1.任何场景下，都有可能有整库恢复（tables=[])，此时需要从baktablemeta中获取原表名称作为新表名称
     * 2.整库恢复场景下，无论库是否重命名，都需要保证dbname,newdbname属性存在，且新库表db.table之间不重复
     * 3.任何场景下，新库名称一定存在；新库名称之间不能重复；新库表名称db.table之间不能重复（库不重复，表就不会重复）；
     * 4.restore场景下，新库名称与旧有库名称（包括oldDBName）不能重复；新库表名称与旧有库表名称不能重复（库不重复，表就不会重复）
     * 5.mysql限制表名称长度不能大于64，去除_shadow_bak，原表长度如果超过53，则报表名称过长
     * 6.restore场景下，原库实例的版本号必须和备份集的版本号匹配
     * 7.mysql限制库名表名长度不能大于64，库名去除rds_xxxx_，原库表名不能超过55
     * */
    private String getAndCheckTableMeta(Map<String, String> actionParams, String bakSetDbVersion, Map<String,Set<String>> oldDBTablesMap, String type, String custinsDbversion) throws RdsException{

        //系统保留表
        Set<String> systemDBNames = new HashSet<>();
        systemDBNames.add("performance_schema");
        systemDBNames.add("mysql");
        systemDBNames.add("information_schema");
        if ("5.7".compareTo(bakSetDbVersion) < 0) {
            systemDBNames.add("sys");
        }

        // 库表恢复不允许备份数据的版本和原实例版本不同
        if (!custinsDbversion.equals(bakSetDbVersion)) {
            logger.error("db version of bakset is not matched!");
            throw new RdsException(MysqlErrorCode.INVALID_BAKHISTORY_DB_VERSION_MISMATCH.toArray());
        }

        String tableMeta = getParameterValue(actionParams, "TableMeta");
        if(StringUtils.isEmpty(tableMeta)){
            //抛出TableMeta参数缺失异常
            throw new RdsException(ErrorCode.INVALID_PARAM_TABLE_META);
        }

        //旧的库表信息，新库名称与原来所有旧库名称不相同
        Set<String> oldDBNamesSet = oldDBTablesMap.keySet();

        //新库表db.table名称之间不能重复
        Set<String> newDBTables = new HashSet<>();
        Integer newDBTablesNum = 0;

        //记录当前新库是整库恢复还是库表恢复
        Map<String,Boolean> newDBNamesMap = new HashMap<>();

        // 记录当前恢复传入的表数量
        Integer tablesCount = 0;

        try{
            JSONArray jsonArray = JSONArray.parseArray(tableMeta);
            //空的tablemeta 非法
            if(jsonArray.size() ==0 ){
                throw new RdsException(ErrorCode.INVALID_PARAM_TABLE_META);
            }
            for(int i=0;i<jsonArray.size();i++){
                JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                String oldDBName = dbInfo.getString("name");
                String newDBName = dbInfo.getString("newname");
                //新，旧库名称不能为null
                if(StringUtils.isBlank(newDBName) || StringUtils.isBlank(oldDBName)){
                    logger.error("old db name and new db name cannot be null");
                    throw new RdsException(MysqlErrorCode.MISSING_NEW_OR_OLD_DBNAME_IN_TABLEMETA.toArray());
                }
                if(systemDBNames.contains(newDBName)){
                    logger.error("new db name cannot be same with system db");
                    throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
                }
                //旧库名称不存在
                if(oldDBNamesSet != null && !oldDBNamesSet.contains(oldDBName)){
                    logger.error("old db name is not invild");
                    throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_OLD_DBNAME_NOT_FOUND.toArray());
                }
                //tables不存在，或tables=[]，则为整库恢复
                JSONArray tables = dbInfo.getJSONArray("tables");
                boolean isRestoreDB = (tables == null || tables.size()==0);

                // 记录传入的 tableMeta 表数量
                if (tables != null && tables.size() > 0) {
                    tablesCount += tables.size();
                }

                //restore场景下整库恢复，新库名称不能和任何原库相同（增加dbossApi检查）
                if(isRestoreDB && "restore".equals(type) && oldDBNamesSet.contains(newDBName)){
                    throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLEMETA_DUPLICATE_DBNAME.toArray());
                }

                // max=64, tmp_prefix=9(eg:rds_xxxx_)
                if (oldDBName != null && oldDBName.length() > 55) {
                    logger.error("length of old db name for restore is too long");
                    throw new RdsException(MysqlErrorCode.INVALID_DB_NAME_TOO_LONG.toArray());
                }

                if(isRestoreDB && oldDBName != null && newDBName.length() > 64) {
                    logger.error("length of new db name for restore is too long");
                    throw new RdsException(MysqlErrorCode.INVALID_DB_NAME_TOO_LONG.toArray());
                }

                //新库列表中已包含整库恢复的库名称
                if(newDBNamesMap.containsKey(newDBName)){
                    //db1.* => db_new.* db2.table => db_new.table场景，restore和clone都不允许出现
                    if(isRestoreDB || newDBNamesMap.get(newDBName) == true){
                        logger.error("newDBName for restore total db has already exists");
                        throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
                    }
                }
                else{
                    newDBNamesMap.put(newDBName,isRestoreDB);
                }

                //整库恢复，需要从baktablemeta中获取原库所有表信息作为新库表名称
                Set<String> oldDBTables = oldDBTablesMap.get(oldDBName);
                if(isRestoreDB){
                    newDBTables.addAll(oldDBTables);
                    newDBTablesNum += oldDBTables.size();
                }
                //非整库恢复
                else{
                    for(int j=0;j<tables.size();j++){
                        JSONObject tableInfo = (JSONObject)tables.get(j);
                        String oldTableName = tableInfo.getString("name");
                        // 校验 oldTableName 是否存在
                        if (!oldDBTables.contains(oldDBName + "." + oldTableName)) {
                            throw new RdsException(ErrorCode.INVALID_PARAM_TABLE_META);
                        }
                        //后端已经没有这个限制，已经没有加_shadow_bak
                        //max=64, and 11 for _shadow_bak
//                        if(oldTableName != null && oldTableName.length() > 53){
//                            throw new RdsException(MysqlErrorCode.INVALID_TABLE_NAME_TOO_LONG.toArray());
//                        }
                        String newTableName = tableInfo.getString("newname");
                        //max=64, for'_backup' or other suffix
                        if(newTableName != null && newTableName.length() > 64){
                            throw new RdsException(MysqlErrorCode.INVALID_TABLE_NAME_TOO_LONG.toArray());
                        }
                        String oldDBTableName = oldDBName+"."+oldTableName;
                        String newDBTableName = newDBName+"."+newTableName;
                        //新库表名称和旧库表名称不能冲突
                        if("restore".equals(type) && oldDBTables.contains(newDBTableName)){
                            throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
                        }
                        //clone场景下，旧库表名称中包含新库表名称，只允许和原来的db.table相同，不允许和其他相同
                        if("clone".equals(type) && oldDBTables.contains(newDBTableName)){
                            if(!oldDBTableName.equalsIgnoreCase(newDBTableName)){
                                throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
                            }
                        }
                        newDBTables.add(newDBTableName);
                        newDBTablesNum ++;
                    }
                }
            }
            //新库表名称之间都不允许重复
            if(newDBTables.size() != newDBTablesNum){
                throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
            }
            //23964326, 重新返回JSON字符串，覆盖原有tableMeta，防止fastjson兼容的单引号导致任务中解析失败
            tableMeta = jsonArray.toJSONString();
        }
        catch(RdsException e){
            logger.error("check parameter TableMeta exception : ", e);
            throw e;
        }
        catch(Exception e){
            logger.error("parse parameter TableMeta Error : ", e);
            //抛出参数格式异常
            throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META.toArray());
        }

        // 至多支持传入100个表做恢复，更多的表可以换成整个db
        if (tablesCount > MAX_RESTORE_TABLE_COUNT) {
            throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_TOO_MARY_TABLES.toArray());
        }

        return tableMeta;
    }

    public BakTableMetaDO getBakTableMetaDO(BakhistoryDO bakhistoryDO) {
        Long bakHisId = bakhistoryDO.getHisId();
        Map<String,Object> condition = new HashMap<>();
        condition.put("backupSetId", bakHisId);
        return bakService.getTableMeta(condition);
    }

    /**
     * 从备份元信息common-tables中获取<db,Set<db.table>>信息，供判断重复使用
     * */
    public Map<String,Set<String>> getDBTablesFromBakMetaInfo(BaksetMetaInfo ddrBakMetaInfo, Boolean isCloudDisk) throws RdsException{
        BakTableMetaDO bakTableMetaDO = ddrBakMetaInfo.getBakTableMeta();
        return getDBTablesFromBakMetaInfo(ddrBakMetaInfo.getDbVersion(), bakTableMetaDO, isCloudDisk);
    }

    /**
     * 从备份元信息common-tables中获取<db,Set<db.table>>信息，供判断重复使用
     * */
    public Map<String,Set<String>> getDBTablesFromBakMetaInfo(BakhistoryDO bakhistoryDO, Boolean isCloudDisk) throws RdsException{

        if(bakhistoryDO == null){
            throw new RdsException(MysqlErrorCode.INVALID_BAKHISTORYDO_WHEN_CHECK_TABLEMETA.toArray());
        }

        BakTableMetaDO bakTableMetaDO = getBakTableMetaDO(bakhistoryDO);
        return getDBTablesFromBakMetaInfo(bakhistoryDO.getDbVersion(), bakTableMetaDO, isCloudDisk);
    }

    private Map<String,Set<String>> getDBTablesFromBakMetaInfo(String bakSetDbVersion, BakTableMetaDO bakTableMetaDO, Boolean isCloudDisk) throws RdsException{
        if(bakTableMetaDO == null){
            throw new RdsException(MysqlErrorCode.INVALID_BAKTABLEMETADO_WHEN_CHECK_TABLEMETA.toArray());
        }

        Map<String,Set<String>> dbTablesMap = new HashMap<>();

        try {
            String info = BakSupport.getDecompressedBakMetaInfo(bakTableMetaDO);
            JSONArray jsonArray = JSONArray.parseArray(info);
            if (bakSetDbVersion.compareTo("5.7") <= 0 && !isCloudDisk) {
                for(int i = 0; i< jsonArray.size(); i++){
                    JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                    String dbName = dbInfo.getString("name");
                    String typeName = dbInfo.getString("type");
                    //检查common信息
                    if("common-tables".equalsIgnoreCase(dbName) && "common".equalsIgnoreCase(typeName)){
                        JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                        for(int j=0;j<bakTableInfo.size();j++){
                            JSONObject tableInfo = (JSONObject)bakTableInfo.get(j);
                            String commonDBName = tableInfo.getString("db");
                            String tableName = tableInfo.getString("name");
                            String dbTableName = commonDBName+"."+tableName;
                            if(dbTablesMap.containsKey(commonDBName)){
                                dbTablesMap.get(commonDBName).add(dbTableName);
                            }
                            else{
                                Set<String> tablesSet = new HashSet<>();
                                tablesSet.add(dbTableName);
                                dbTablesMap.put(commonDBName,tablesSet);
                            }
                        }
                    }
                }
            } else {
                for(int i = 0; i < jsonArray.size(); i++){
                    JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                    //检查common信息
                    if ("common-tables".equalsIgnoreCase(dbInfo.getString("name"))
                        && "common".equalsIgnoreCase(dbInfo.getString("type"))){
                        JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                        for(int j=0;j<bakTableInfo.size();j++){
                            JSONObject tableInfo = (JSONObject)bakTableInfo.get(j);
                            String fileType = tableInfo.getString("filetype");
                            String dbName = "";
                            String dbTableName = "";
                            if ("myisam".equalsIgnoreCase(fileType)) {
                                dbTableName = tableInfo.getString("name").replaceAll("\\.MYD", "").replaceAll("\\.MYI", "");
                                dbName = dbTableName.split("/")[0];
                            } else if (tableInfo.getString("name").endsWith(".CSV")) {
                                dbTableName = tableInfo.getString("name").replaceAll("\\.CSV", "");
                                dbName = dbTableName.split("/")[0];
                            } else if ("opt".equalsIgnoreCase(fileType)) {
                                dbName = tableInfo.getString("db");
                                dbTableName = dbName + "." + tableInfo.getString("name");
                            }
                            if (dbName.length() > 0) {
                                if (dbTablesMap.containsKey(dbName)) {
                                    dbTablesMap.get(dbName).add(dbTableName);
                                } else {
                                    Set<String> tablesSet = new HashSet<>();
                                    tablesSet.add(dbTableName);
                                    dbTablesMap.put(dbName, tablesSet);
                                }
                            }
                        }
                    } else if ("db".equalsIgnoreCase(dbInfo.getString("type"))){
                        String dbName = dbInfo.getString("name");
                        JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                        for (int j=0;j<bakTableInfo.size();j++) {
                            JSONObject tableInfo = (JSONObject)bakTableInfo.get(j);
                            String dbTableName = dbName + "." + tableInfo.getString("name");
                            if (dbTablesMap.containsKey(dbName)) {
                                dbTablesMap.get(dbName).add(dbTableName);
                            } else {
                                Set<String> tablesSet = new HashSet<>();
                                tablesSet.add(dbTableName);
                                dbTablesMap.put(dbName, tablesSet);
                            }
                        }
                    }
                }
            }
        }
        catch (DataFormatException d){
            logger.error("parse compress meta info error", d);
            //抛出参数格式异常
            throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META.toArray());
        }

        return dbTablesMap;
    }

    /**
     * 返回库表备份恢复所需空间
     * 所需空间包括两部分：
     * 1.恢复数据所需空间
     * 2.恢复common-tables中表结构所需空间
     * @return long 单位：B
     * */

    public long getAndCheckStorageForRestoreDbtables(String tableMeta, BaksetMetaInfo ddrBakMetaInfo) throws RdsException{
        BakTableMetaDO bakTableMetaDO = ddrBakMetaInfo.getBakTableMeta();
        return getAndCheckStorageForRestoreDbtables(tableMeta, bakTableMetaDO);
    }

    public long getAndCheckStorageForRestoreDbtables(String tableMeta, BakhistoryDO bakhistoryDO) throws RdsException {
        BakTableMetaDO bakTableMetaDO = getBakTableMetaDO(bakhistoryDO);
        return getAndCheckStorageForRestoreDbtables(tableMeta, bakTableMetaDO);
    }

    private long getAndCheckStorageForRestoreDbtables(String tableMeta, BakTableMetaDO bakTableMetaDO) throws RdsException{

        long commonTablesSize = 0;
        long restoreDataSize = 0;

        JSONArray tableMetaArray = JSONArray.parseArray(tableMeta);

        try{
            String info = BakSupport.getDecompressedBakMetaInfo(bakTableMetaDO);
            JSONArray jsonArray = JSONArray.parseArray(info);
            for(int i = 0; i< jsonArray.size(); i++){
                JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                String dbName = dbInfo.getString("name");
                String typeName = dbInfo.getString("type");
                //检查common信息
                if("common-tables".equalsIgnoreCase(dbName) && "common".equalsIgnoreCase(typeName)){
                    JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                    for(int j=0;j<bakTableInfo.size();j++){
                        JSONObject tableInfo = (JSONObject)bakTableInfo.get(j);
                        Long begin = tableInfo.getLong("begin");
                        Long end = tableInfo.getLong("end");
                        if(begin != null && end !=null){
                            commonTablesSize += end - begin;
                        }
                    }
                }
                //检查其他DB是否是需要恢复的DB
                else{
                    //遍历tableMeta中的数据库
                    for(int k= 0;k<tableMetaArray.size();k++){
                        JSONObject tableMetaDBInfo = (JSONObject)tableMetaArray.get(k);
                        String oldDBName = tableMetaDBInfo.getString("name");
                        //需要恢复的数据库
                        if(dbName.equals(oldDBName)){
                            //tableMeta中的tables=[]为整库恢复
                            JSONArray tables = tableMetaDBInfo.getJSONArray("tables");
                            boolean isRestoreDB = tables == null || tables.size() == 0;
                            //获取备份的数据库下所有的库表信息
                            JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                            for(int j=0;j<bakTableInfo.size();j++){
                                //备份的一个表信息
                                JSONObject bakTableInfoDetail = (JSONObject)bakTableInfo.get(j);
                                String bakTableName = bakTableInfoDetail.getString("name");
                                Long begin = bakTableInfoDetail.getLong("begin");
                                Long end = bakTableInfoDetail.getLong("end");
                                //整库恢复
                                if(isRestoreDB && begin != null && end != null){
                                    restoreDataSize += end - begin;
                                }
                                //遍历tableMeta指定库下tables，比较表名称
                                else{
                                    for(int w=0;w<tables.size();w++){
                                        JSONObject restoreTable = (JSONObject)tables.get(w);
                                        String name = restoreTable.getString("name");
                                        //当前备份中表和tableMeta中表名称相同，需要恢复
                                        if(bakTableName.equals(name)){
                                            restoreDataSize += end - begin;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        catch(DataFormatException e){
            logger.error("parse compress meta info error", e);
            //抛出参数格式异常
            throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META.toArray());
        }

        logger.info("commonTablesSize:"+commonTablesSize+" B, restoreDataSize:"+restoreDataSize+" B");
        //用户最终只需要承担原数据大小即可
        return commonTablesSize + restoreDataSize;
    }

    /**
     * 通过dbossApi检查tableMeta中新库表名称是否与原实例有重复
     * 1.整库恢复场景，新库名不能与现有实例重复
     * 2.库表恢复场景，新 库.表 名称不能与现有实例重复
     */
    public Map<String, Object> checkTableMetaWithDboss(CustInstanceDO srcCustins, String tableMeta) throws RdsException{

        //整库恢复新库名称
        List<String> restoreDBSet = new ArrayList<>();
        //库表恢复新库表名称
        List<String> restoreDBTableSet  = new ArrayList<>();

        try {
            JSONArray jsonArray = JSONArray.parseArray(tableMeta);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                String oldDBName = dbInfo.getString("name");
                String newDBName = dbInfo.getString("newname");
                //tables不存在，或tables=[]，则为整库恢复
                JSONArray tables = dbInfo.getJSONArray("tables");
                boolean isRestoreDB = (tables == null || tables.size() == 0);
                //整库恢复
                if (isRestoreDB) {
                    restoreDBSet.add(newDBName);
                }
                //非整库恢复
                else {
                    for (int j = 0; j < tables.size(); j++) {
                        JSONObject tableInfo = (JSONObject)tables.get(j);
                        String newTableName = tableInfo.getString("newname");
                        String newDBTableName = newDBName + "." + newTableName;
                        restoreDBTableSet.add(newDBTableName);
                    }
                }
            }
        } catch(Exception ex){
            logger.error("check parameter tablemeta error, detail:",ex);
            //检查多次都没有结果时，放开，防止紧急情况下造成无法恢复
            return createDbossCheckResultMap(true,null,null);
        }

        //整库恢复是否已经检查通过，防止重复检查
        boolean hasCheckTotalDB = false;
        //重试三次,单次查询5秒超时
        int retryTimes = 3;
        while(retryTimes-- >0){
            try {

                Map<String, Object> checkDBsExistsMap = null;
                Map<String, Object> checkDBTablesExistsMap = null;

                //整库恢复为空，则整库恢复不校验
                if(restoreDBSet.isEmpty()){
                    hasCheckTotalDB = true;
                }
                else{
                    if(!hasCheckTotalDB){
                        checkDBsExistsMap= dbossApi.queryDBsBasicInfo(srcCustins.getId(), restoreDBSet);
                    }
                    //没有结果则重试
                    if(checkDBsExistsMap == null || checkDBsExistsMap.get("result") == null){
                        continue;
                    }
                    List<String> existsDBsList = (List<String>)checkDBsExistsMap.get("result");
                    if(existsDBsList.size() > 0){
                        return createDbossCheckResultMap(false, existsDBsList, null);
                    }
                    hasCheckTotalDB = true;
                }

                //校验库表部分
                if(restoreDBTableSet.isEmpty()){
                    return createDbossCheckResultMap(true,null,null);
                }
                else{
                    checkDBTablesExistsMap = dbossApi.queryDBTablesBasicInfo(srcCustins.getId(), restoreDBTableSet);
                    if (checkDBTablesExistsMap == null || checkDBTablesExistsMap.get("result") == null) {
                        continue;
                    }
                    Map<String,String> existsDbTablesMap = (Map<String, String>)checkDBTablesExistsMap.get("result");
                    if(existsDbTablesMap.size() > 0){
                        return createDbossCheckResultMap(false, null, existsDbTablesMap);
                    }
                }
                //返回
                return createDbossCheckResultMap(true,null,null);
            } catch(Exception ex){
                logger.error("check parameter tablemeta error, detail:",ex);
                //某些场景下，库表回复比较紧急，需要忽略当前校验
            }
        }
        //检查多次都没有结果时，放开，防止紧急情况下造成无法恢复
        return createDbossCheckResultMap(true,null,null);
    }

    private Map<String, Object> createDbossCheckResultMap(boolean result, List<String> existsDBList, Map<String,String> existsDbTableMap){

        Map<String, Object> checkResult = new HashMap<>();
        String descInfo = null;
        checkResult.put("result", result);
        if(existsDBList != null){
            checkResult.put("existsDBs", existsDBList);
            descInfo = "following dbs already exists: " + existsDBList.toString().replaceAll("\"", "'");
        }
        else if(existsDbTableMap != null){
            checkResult.put("existsDbTables", existsDbTableMap);
            List<String> existsDbTables = new ArrayList<>(existsDbTableMap.size());
            for (Map.Entry<String,String> entry : existsDbTableMap.entrySet()) {
                existsDbTables.add(entry.getKey() + "." + entry.getValue());
            }
            descInfo = "following db-tables already exists: " + existsDbTables.toString().replaceAll("\"", "'");
        }
        checkResult.put("descInfo", descInfo);
        return checkResult;
    }

    public boolean isMysqlXDBByParams(Map<String, String> actionParams) throws RdsException{
        String dbType = getAndCheckDBType(actionParams, null);
        String dbVersion = getAndCheckDBVersion(actionParams, dbType, true);
        String classCode = getParameterValue(actionParams, ParamConstants.DB_INSTANCE_CLASS);
        return isMysqlXDB(dbType, dbVersion, classCode);
    }

    public boolean isMysqlXDB(String dbType, String dbVersion, String classCode) throws RdsException{

        if(CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(dbType) &&
            (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(dbVersion) ||
                CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(dbVersion))){
            InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion, null, null);
            if(instanceLevel == null){
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            return InstanceSupport.CATEGORY_ENTERPRISE.equalsIgnoreCase(instanceLevel.getCategory());
        }

        return false;
    }


    public void checkNotDeleteHaProxyCustins(CustInstanceDO custins) throws RdsException {
        //FIXME:HA元数据库过滤，需要从业务中剔除
        HashMap<String, Object> haproxyMetadbFilter = new HashMap<>();
        haproxyMetadbFilter.put("metadb_ins_name", custins.getInsName());
        haproxyMetadbFilter.put("source", "rds");
        List<ProxyMetaPool> hm = null;
        try {
            hm = haProxyService.queryHaproxyMetadbPool(haproxyMetadbFilter);
        }catch (Exception e){
            logger.warn(e.toString());
        }
        if(hm!=null && hm.size() > 0) {
            throw new RdsException(ErrorCode.HAPROXY_METADB_DELETE_ERROR,
                String.format("this rds instance:%s used by haproxy metadb pool, can't delete", custins.getInsName()));
        }
        if (getBlsCustInstance(custins) != null) {
            if (haProxyService.checkCustinsConnAddrCountMoreThanOne(custins.getId(), null, null)) {
                //return createErrorResponse(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
                throw new RdsException(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
            }
        }
    }

    protected EntityTagDO getBlsCustInstance(CustInstanceDO custins){
        List<EntityTagDO> entityTagList = custinsService.getEntityTagList(ParamConstants.ENTITY_TYPE_CUSTINS, String.valueOf(custins.getId()), "bls-db-replica-id");
        if (null != entityTagList && entityTagList.size() > 0 ){
            return entityTagList.get(0);
        }
        return null;
    }

    public boolean isMysqlXdbByCustins(CustInstanceDO custIns){
        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custIns.getLevelId());
        if(instanceLevel == null){
            return false;
        }
        return InstanceSupport.CATEGORY_ENTERPRISE.equals(instanceLevel.getCategory())
            && CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(custIns.getDbType())
            && (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(custIns.getDbVersion()) ||
            CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(custIns.getDbVersion()));
    }

    public boolean isXdbReadCustins(CustInstanceDO custIns) {
        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custIns.getLevelId());
        if (instanceLevel == null) {
            return false;
        }
        // xdb 只读规格的复用的俩节点，需要拿主实例的规格判断
        if (custIns.isReadOrBackup() && custIns.getPrimaryCustinsId() != null && custIns.getPrimaryCustinsId() > 0) {
            CustInstanceDO primaryCustinsDO = custinsService.getCustInstanceByCustinsId(custIns.getPrimaryCustinsId());
            instanceLevel = instanceService.getInstanceLevelByLevelId(primaryCustinsDO.getLevelId());

            return InstanceSupport.CATEGORY_ENTERPRISE.equals(instanceLevel.getCategory())
                    && CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(custIns.getDbType())
                    && (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(custIns.getDbVersion()) ||
                    CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(custIns.getDbVersion()));
        } else {
            return false;
        }
    }

    public boolean isMysqlXDBByLevel(InstanceLevelDO instanceLevel){
        return InstanceSupport.CATEGORY_ENTERPRISE.equals(instanceLevel.getCategory())
            && CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(instanceLevel.getDbType())
            && (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(instanceLevel.getDbVersion()) ||
            CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(instanceLevel.getDbVersion()));
    }

    // 根据mysql实例检查maxscale实例是否正常
    public void checkMaxscaleStatus(CustInstanceDO custins) throws RdsException{
        CustInstanceDO maxscale_ins;
        if (custins.getPrimaryCustinsId() != 0){
            CustInstanceDO primary_ins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            maxscale_ins = custinsService.getMaxscaleCustins(primary_ins);
        }else {
            maxscale_ins = custinsService.getMaxscaleCustins(custins);
        }
        if (maxscale_ins != null){
            if (!maxscale_ins.isActive()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS, "maxscale status mot active");
            }
            CustInstanceDO maxscale_ins_server = custinsService.getCustInstanceByParentId(maxscale_ins.getId()).get(0);
            if (!maxscale_ins_server.isActive()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS, "maxscale status mot active");
            }
        }
    }

    public Boolean isDHGCluster(String clusterName) {
        if(StringUtils.isEmpty(clusterName)){
            return false;
        }
        ClustersDO clustersDO = clusterService.getClusterByClusterName(clusterName);
        return clustersDO != null && clustersDO.getType() != null && clustersDO.getType() >= 1;
    }

    public static boolean isSingleNode(InstanceLevel instanceLevel) {
        if (instanceLevel == null) {
            return false;
        }

        if(instanceLevel.getCategory().getValue().toLowerCase().equalsIgnoreCase(
            InstanceLevel.CategoryEnum.SERVERLESS_BASIC.getValue()) &&
            instanceLevel.getInsCount() != null && instanceLevel.getInsCount() ==1){
            return true;
        }
        return instanceLevel.getCategory() == InstanceLevel.CategoryEnum.BASIC ||
            (instanceLevel.getCategory() == InstanceLevel.CategoryEnum.ENTERPRISE &&
                instanceLevel.getInsCount() != null && instanceLevel.getInsCount() == 1);
    }

    /**
     * 判断是否为集群版
     * */
    public static boolean isCluster(String category) {
        return InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(category);
    }

    /**
     * 判断是否为集群版
     * */
    public static boolean isCluster(InstanceLevel instanceLevel) {
        if (instanceLevel == null) {
            return false;
        }
        return isCluster(instanceLevel.getCategory().toString());
    }

    /**
     * 判断是否是基础版
     * @param category
     * @return
     */
    public static boolean isBasic(String category) {
        return InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(category);
    }

    /**
     * 判断是否是serverless基础版
     * @param category
     * @return
     */
    public static boolean isServerLessBasic(String category) {
        return InstanceLevel.CategoryEnum.SERVERLESS_BASIC.toString().equalsIgnoreCase(category);
    }

    /**
     * 判断是否为高可用版
     * */
    public static boolean isStandard(InstanceLevel instanceLevel) {
        if (instanceLevel == null) {
            return false;
        }
        return instanceLevel.getCategory() == InstanceLevel.CategoryEnum.STANDARD;
    }

    /**
     * 检查category是否为指定类型
     * */
    public static boolean checkCategory(InstanceLevel instanceLevel, InstanceLevel.CategoryEnum category) {
        if (instanceLevel == null || category == null) {
            return false;
        }
        return instanceLevel.getCategory() == category;
    }

    /**
     * 检查实例锁状态
     */
    public static void checkLockMode(ReplicaSet replicaSet) throws RdsException {
        if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }
    }

    /**
     * 检查实例状态
     */
    public static void checkInstanceActive(ReplicaSet replicaSet) throws RdsException {
        if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
    }

    /**
     * 当前只读规格限制：
     * 单节点实例不能创建只读
     * 高可用实例不能选取三节点规格
     * 三节点实例的只读规格有可能是高可用的，也可能是三节点的，不做拦截
     * */
    public boolean checkReadinsClassLegalWithPrimaryCustins(InstanceLevelDO readInsLevel, InstanceLevelDO primaryInsLevel){
        if(InstanceSupport.CATEGORY_BASIC.equalsIgnoreCase(primaryInsLevel.getCategory())){
            return false;
        }
        if(InstanceSupport.CATEGORY_STANDARD.equalsIgnoreCase(primaryInsLevel.getCategory()) && InstanceSupport.CATEGORY_ENTERPRISE.equalsIgnoreCase(readInsLevel.getCategory())){
            return false;
        }
        if (!Objects.equals(readInsLevel.getHostType(), primaryInsLevel.getHostType())) {
            return false;
        }
        return true;
    }


    public String getCheckDBVersion(Map<String, String> actionParams, String dbType, String defautDBVersion)
        throws RdsException {
        this.initDbVersionMap(dbType);
        if (!dbVersionMap.containsKey(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        String dbVersion = getParameterValue(actionParams, "engineversion", defautDBVersion);
        logger.warn("CommponentsAction.getCheckDBVersion version=" + dbVersion);
        if (!dbVersionMap.get(dbType).contains(dbVersion)) {
            throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
        }
        return dbVersion;
    }

    public String getCurConnType(Map<String, String> actionParams) {//实例名

        return getParameterValue(actionParams, "curConnType");
    }

    public String getDestConnType(Map<String, String> actionParams) {//实例名

        return getParameterValue(actionParams, "destConnType");
    }
    /**
     * 强制mock杭州金融云传参
     * */
    public void mockHangZhouFinanceRegion(Map<String, String> map) {
        try {
            String requestId = getParameterValue(map, ParamConstants.REQUEST_ID);
            String regionId = getParameterValue(map, ParamConstants.REGION_ID);
            String zoneId = getParameterValue(map, ParamConstants.ZONE_ID);
            String subDomain = getParameterValue(map, ParamConstants.REGION);
            if (RdsConstants.REGIONID_HANGZHOU_ALIYUN.equals(regionId) && isSupportHangzhouFinance()) {
                String regionIdInUse = getHangzhouFinanceRegion(regionId, zoneId, subDomain);
                if (!regionIdInUse.equalsIgnoreCase(regionId)) {
                    logger.info(String.format("%s regionId %s, zoneId %s regionIdInUse %s", requestId, regionId, zoneId, regionIdInUse));
                    map.put(ParamConstants.REGION_ID.toLowerCase(), regionIdInUse);
                }
            }
        } catch (Exception e) {
            logger.error("failed mock regionId.", e);
        }
    }

    /**
     * 是否开启杭州金融云的mock逻辑
     * */
    public boolean isSupportHangzhouFinance() {
        try {
            String grayKey = "mysql_support_hangzhou_finance";
            String grayValue = resourceCache.get(grayKey, new Callable<String>() {
                @Override
                public String call() throws Exception {
                    ResourceDO resource = resourceService.getResourceByResKey(grayKey);
                    if (resource == null) {
                        return "false";
                    }
                    return resource.getRealValue();
                }
            });
            return "true".equalsIgnoreCase(grayValue);
        } catch (Exception e) {
            logger.error("isSupportFinanceDataEncryption failed.", e);
            return false;
        }
    }

    /**
     * 杭州金融云传下来的 region 是 cn-hangzhou，要做兼容
     * */
    public String getHangzhouFinanceRegion(String regionId, String zoneId, String subDomain) throws RdsException, ExecutionException {
        String regionIdUse = regionId;
        if (RdsConstants.REGIONID_HANGZHOU_ALIYUN.equals(regionId)) {
            String regionIdTemp = null;

            // 通过ZoneID查
            if (StringUtils.isNotEmpty(zoneId)) {
                regionIdTemp = resourceCache.get(zoneId, new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        RegionAVZoneQuery query = new RegionAVZoneQuery();
                        query.setAvz(zoneId);
                        List<RegionAVZonDO> regionAVZonDOList = resourceService.getRegionAVZoneList(query);
                        if (regionAVZonDOList.isEmpty()) {
                            return "";
                        }
                        return regionAVZonDOList.get(0).getRegion();
                    }
                });
            }

            // 通过subDomain查
            if (StringUtils.isEmpty(regionIdTemp) && StringUtils.isNotEmpty(subDomain)) {
                regionIdTemp = resourceCache.get(subDomain, new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        RegionAVZonDO regionAVZonDO = resourceService.getRegionAVZoneBySubDomain(subDomain);
                        if (regionAVZonDO == null) {
                            return "";
                        }
                        return regionAVZonDO.getRegion();
                    }
                });
            }


            if(StringUtils.isNotEmpty(regionIdTemp) && RdsConstants.REGIONID_HANGZHOU_FINANCE.equals(regionIdTemp)){
                regionIdUse = regionIdTemp;
            }
        }
        return regionIdUse;
    }

    public static Boolean useEnterpriseRegistry(DBaasMetaService dBaasMetaService, String regionId, String dbType, String bizType) throws ApiException {
        if (StringUtils.isBlank(bizType)) {
            bizType = BIZ_TYPE_ALIYUN;
        }
        try {
            ConfigListResult configList = dBaasMetaService.getDefaultClient().listConfigs(RequestSession.getRequestId(), MySQLParamConstants.RESOURCE_KEY_USE_ENTERPRISE_REGISTRY);
            if (Objects.isNull(configList) || CollectionUtils.isEmpty(configList.getItems())) {
                log.info("can not find resource key: {}", MySQLParamConstants.RESOURCE_KEY_USE_ENTERPRISE_REGISTRY);
                return false;
            }
            String bizInfoListStr = configList.getItems().get(0).getValue();
            log.info("the db and biz list of using enterprise registry: {}", bizInfoListStr);
            if (StringUtils.isBlank(bizInfoListStr)) {
                return false;
            }
            List<BizUseEnterpriseRegistry.BizInfo> bizInfoList = new BizUseEnterpriseRegistry(bizInfoListStr).getBizInfoList();
            if (CollectionUtils.isEmpty(bizInfoList)) {
                return false;
            }
            for (BizUseEnterpriseRegistry.BizInfo bizInfo : bizInfoList) {
                if (Objects.nonNull(bizInfo.getDbType()) && bizInfo.getDbType().equalsIgnoreCase(dbType)) {
                    if (CollectionUtils.isEmpty(bizInfo.getBizTypeList()) || CollectionUtils.isEmpty(bizInfo.getRegionList())) {
                        continue;
                    }
                    // support *
                    if ((bizInfo.getBizTypeList().get(0).equals("*") || bizInfo.getBizTypeList().contains(bizType))
                            && (bizInfo.getRegionList().get(0).equals("*") || bizInfo.getRegionList().contains(regionId))) {
                        return true;
                    }
                }
            }
            return false;
        } catch (ApiException e) {
            if (e.getCode() == 404) {
                log.info("can not find resource key: {}", MySQLParamConstants.RESOURCE_KEY_USE_ENTERPRISE_REGISTRY);
                return false;
            }
            log.error("failed to call dbaasMetaService of listConfigs {}, err: {}", MySQLParamConstants.RESOURCE_KEY_USE_ENTERPRISE_REGISTRY, e.getMessage());
            throw e;
        }
    }

    public String getDbNode(Map<String, String> actionParams) {
        return CustinsParamSupport.getParameterValue(actionParams, "dbnode");
    }

    public List<Map<String, String>> getNodesInfo(Map<String, String> actionParams) throws RdsException {
        String addinfos = getDbNode(actionParams);
        if (!StringUtil.isEmpty(addinfos)) {
            try {
                List<Map<String, String>> infos = JSON.parseObject(addinfos, new TypeReference<List<Map<String, String>>>() {
                });;
                return infos;
            } catch (Exception var4) {
                logger.warn(String.format("format nodeinfo error, data: %s", addinfos));
                throw new RdsException(new Object[]{400, "InvalidParameters.Format", "addNodeInfo format error"});
            }
        } else {
            return null;
        }
    }

    public String getIoAccelerationEnabled(Map<String, String> actionParams) {
        return CustinsParamSupport.getParameterValue(actionParams, IO_ACCELERATION_ENABLED);
    }
    public boolean isInnerRDS(Map<String, String> actionParams) throws RdsException{
        String isInnerRDS = getParameterValue(actionParams, ParamConstants.IsInnerRDS, '0');
        String accessID = getParameterValue(actionParams, ACCESSID, "unknown");
        return "1".equals(isInnerRDS) || "DuKang".equalsIgnoreCase(accessID) || "TianLong".equalsIgnoreCase(accessID);
    }
    public CustinsIpWhiteListDO[] getAndCheckWhitelistTemplateList(Map<String, String> actionParams,int userId) throws RdsException {
        String templateIdRaw= getParameterValue(actionParams,WHITELIST_TEMPLATE_LIST,null);
        if (StringUtils.isEmpty(templateIdRaw) || templateIdRaw.equals("0")) {
            return null;
        }
        String[] templateIdStringList=templateIdRaw.split(",");
        if(templateIdStringList.length>10){
            throw new RdsException(ErrorCode.TOO_MANY_WHITELIST_TEMPLATE_IDS);
        }
        try{
            CustinsIpWhiteListDO[] templateList=new CustinsIpWhiteListDO[templateIdStringList.length];
            for(int i=0;i<templateIdStringList.length;i++){
                int templateId=Integer.parseInt(templateIdStringList[i]);
                WhitelistTemplateDO whitelistTemplate = whitelistTemplateIDao.getWhitelistTemplate(userId, templateId);
                if(templateId<=0 || whitelistTemplate==null || StringUtils.isBlank(whitelistTemplate.getIps())){
                    throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
                }
                String whitelistNetType = getAndCheckWhitelistNetType(actionParams);
                CustinsIpWhiteListDO ipWhiteList = new CustinsIpWhiteListDO();
                ipWhiteList.setIpWhiteList(whitelistTemplate.getIps());
                ipWhiteList.setGroupName(whitelistTemplate.getTemplateName()+ TEMPLATE_SUFFIX);
                ipWhiteList.setGroupTag("template");
                ipWhiteList.setNetType(whitelistNetType);
                ipWhiteList.setIpType(getAndCheckSecurityIpType(actionParams));
                templateList[i]=ipWhiteList;
            }
            return templateList;
        }catch (NumberFormatException | RdsException ne){
            throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
        }
    }
    public int[] getAndCheckTemplateIdList(Map<String, String> actionParams, CustinsIpWhiteListDO[] templateList) throws RdsException {
        String templateIdRaw = getParameterValue(actionParams, WHITELIST_TEMPLATE_LIST,null);
        if (StringUtils.isEmpty(templateIdRaw) || templateIdRaw.equals("0")) {
            return null;
        }
        String[] templateIdList;
        int[] idList;
        try {
            templateIdList = templateIdRaw.split(",");
            idList = new int[templateIdList.length];
            for (int i = 0; i < templateIdList.length; i++) {
                int templateId = Integer.parseInt(templateIdList[i]);
                idList[i] = templateId;
            }
        } catch (Exception e) {
            throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
        }
        if (templateIdList.length != templateList.length) {
            throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
        }
        return idList;
    }

    public String getEncryptionKey(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "encryptionkey");
    }
    public String getRoleArn(Map<String, String> actionParams) {
        return getParameterValue(actionParams, "rolearn");
    }

    public Integer getDrainingTimeout(Map<String, String> actionParams) throws RdsException {
        String drainingTimeout = getParameterValue(actionParams, "DrainingTimeout");
        if (drainingTimeout == null) {
            return 15;
        }
        return CheckUtils.parseInt(drainingTimeout, 1, 900, ErrorCode.INVALID_PARAM);
    }

    public ConfigVipConfBody.ConnectionDrainEnum getDrainingEnable(Map<String, String> actionParams) throws RdsException {
        String drainingEnable = getParameterValue(actionParams, "DrainingEnable");
        if (drainingEnable != null && drainingEnable.equals("0")) {
            return ConfigVipConfBody.ConnectionDrainEnum.OFF;
        }
        return ConfigVipConfBody.ConnectionDrainEnum.ON;
    }

    /**
     * 判断实例过去有没有"磁盘满"的锁定任务
     * @param custins   custins
     * @param hours
     * @return  true：没有，false：有
     */
    public boolean hasLockTaskInLastHours(CustInstanceDO custins, int hours) {

        Map<String, Object> condition = new HashMap<>();
        condition.put("custinsId", custins.getId());
        condition.put("action", "MysqlFlushInsForOpenaccPipeline");
        condition.put("taskKey", "lock_ins");
        condition.put("startTime", OffsetDateTime.now().minusHours(hours));

       return Optional.ofNullable(taskService.getTaskAndContext(condition))
            .map(task -> task.get("context"))
            .map(context -> JSON.parseObject((String) context))
            .map(context -> context.getJSONObject("cust_ins"))
            .map(custIns -> custIns.getInteger("lock_mode"))
            .map(lockMode -> CUSTINS_LOCK_DISK_FULL.equals(lockMode))
            .orElse(false);
    }

    /**
     * 返回透传的任务优先级
     */
    public Integer getPriority(Map<String, String> actionParams) {
        return Integer.valueOf(getParameterValue(actionParams, "Priority", TASK_PRIORITY_COMMON));
    }

    /**
     * 查看是否忽略维护中状态机
     */
    public Boolean isIgnoreMaintainingStatus(Map<String, String> actionParams) {
        return Boolean.valueOf(getParameterValue(actionParams, "IgnoreMaintainingStatus", "false"));

    }

    /**
     * 获取从集群版迁移可用区信息
     */
    public String getInformationForClusterMigrate(Map<String, String> actionParams) {//实例名
        return getParameterValue(actionParams, "migrateClusterAvzInfo");
    }


    /*
     *  获取全局默认压缩率
     */
    public Double getGlobalCompressionRatio(String regionId) {
        Double defaultCompressionRation = 2.0;
        ResourceDO resourceDO = resourceService.getResourceByResKey(RDS_REGION_COMPRESSION_CONFIG);
        if (Objects.isNull(resourceDO) || StringUtils.isEmpty(resourceDO.getRealValue())) {
            return defaultCompressionRation;
        }
        // 解析压缩配置
        JSONObject compressionRatioConfig;
        try {
            compressionRatioConfig = JSON.parseObject(resourceDO.getRealValue());
        } catch (Exception e) {
            return defaultCompressionRation;
        }
        // 获取region配置
        if (compressionRatioConfig.isEmpty() || !compressionRatioConfig.containsKey(regionId)) {
            return defaultCompressionRation;
        }
        JSONObject regionCompressionRatioConfig = compressionRatioConfig.getJSONObject(regionId);
        // 获取本地盘配置
        if (regionCompressionRatioConfig.isEmpty() || !regionCompressionRatioConfig.containsKey(RDS_LOCAL_COMPRESSION_CONFIG)) {
            return defaultCompressionRation;
        }
        JSONObject localCompressionRatioConfig = regionCompressionRatioConfig.getJSONObject(RDS_LOCAL_COMPRESSION_CONFIG);
        // 获取默认配置
        if (localCompressionRatioConfig.isEmpty() || !localCompressionRatioConfig.containsKey("default")) {
            return defaultCompressionRation;
        }
        return localCompressionRatioConfig.getDouble("default");
    }

    /*
     *  获取用户默认压缩率
     */
    public Double getUserCompresionRatio(String uid) {
        ResourceDO resourceDO = resourceService.getResourceByResKey(RDS_UID_COMPRESSION_CONFIG);
        if (Objects.isNull(resourceDO) || StringUtils.isEmpty(resourceDO.getRealValue())) {
            return null;
        }
        // 解析压缩配置
        JSONObject compressionRatioConfig;
        try {
            compressionRatioConfig = JSON.parseObject(resourceDO.getRealValue());
        } catch (Exception e) {
            return null;
        }
        // 获取region配置
        if (compressionRatioConfig.isEmpty() || !compressionRatioConfig.containsKey(uid)) {
            return null;
        }
        JSONObject uidCompressionRatioConfig = compressionRatioConfig.getJSONObject(uid);
        // 获取本地盘配置
        if (uidCompressionRatioConfig.isEmpty() || !uidCompressionRatioConfig.containsKey(RDS_LOCAL_COMPRESSION_CONFIG)) {
            return null;
        }
        JSONObject localCompressionRatioConfig = uidCompressionRatioConfig.getJSONObject(RDS_LOCAL_COMPRESSION_CONFIG);
        // 获取默认配置
        if (localCompressionRatioConfig.isEmpty() || !localCompressionRatioConfig.containsKey("default")) {
            return null;
        }
        return localCompressionRatioConfig.getDouble("default");
    }

    public CustInstanceDO getAndCheckReadCustInstance(Map<String, String> actionParams) throws RdsException {
        Integer userId = this.getAndCheckUserId(actionParams);
        CustInstanceDO custins = null;
        if (INTERNAL_SYSTEM.equals(this.getBID(actionParams))) {
            custins = custinsService.getCustInstanceByInsName(null,
                    this.getAndCheckReadDBInstanceName(actionParams), 0);
        } else {
            custins = custinsService.getCustInstanceByInsName(userId,
                    this.getAndCheckReadDBInstanceName(actionParams), 0);
        }
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    private String getAndCheckReadDBInstanceName(Map<String, String> actionParams) throws RdsException {
        return CheckUtils.checkNullForInsName(getParameterValue(actionParams, "readdbinstancename"));
    }

    /**
     * 判断需要删除或修改的链接地址是否与ssl地址一致
     * */
    public boolean isConnectionStringToSsl(Map<String, String> params, CustInstanceDO custins) {
        CustinsParamDO insSslConfig = custinsParamService.getCustinsParam(
                custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL);
        CustinsParamDO insCertConfig = custinsParamService.getCustinsParam(
                custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME);
        String connectionString = getParameterValue(params, "connectionstring");
        if (insSslConfig != null && insCertConfig != null) {
            if (insSslConfig.getValue().equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SSL)){
                return insCertConfig.getValue().equals(connectionString);
            }
        }
        return false;
    }

    /**
     * 获取ip地址请求参数
     */
    public String getAndCheckIpAddress(Map<String, String> actionParams) throws RdsException {
        String ipAddress = getParameterValue(actionParams, "IP");
        if (StringUtils.isBlank(ipAddress) && !Validator.isIp(ipAddress)) {
            throw new RdsException(ErrorCode.INVALID_IP); //校验IP的格式
        }
        return ipAddress;
    }

    /**
     * 是否搭建复制
     */
    public boolean isBuildReplication(Map<String, String> actionParams) throws RdsException {
        String buildReplication = getParameterValue(actionParams, "buildreplication");
        return Boolean.parseBoolean(buildReplication);
    }
}
