package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccessKeyListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.poddefault.action.support.GAD.Aes;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.COMMON_PROVIDER_STS_ENDPOINT;

@Slf4j
@Service
public class SLRService {
    @Autowired
    private DBaasMetaService dBaasMetaService;
    private static final Cache<String, String> resourceCache = CacheBuilder.newBuilder()
            .maximumSize(2048)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    public AssumeRoleWithServiceIdentityResponse getAssumeAuthInfoForSLR(String regionId, String aliUid) throws Exception {
        String userName = "rdsbluegreen_service";
        AccessKeyListResult accessKeyListResult = dBaasMetaService.getDefaultClient().listAccessKeys(RequestSession.getRequestId(), userName, null);
        DefaultProfile profile = DefaultProfile.getProfile(regionId,
                Aes.decryptAccountPasswd(accessKeyListResult.getItems().get(0).getAccessKeyIdEncrypted(), Aes.PWD_CRYPTKEY),
                Aes.decryptAccountPasswd(accessKeyListResult.getItems().get(0).getAccessKeySecretEncrypted(), Aes.PWD_CRYPTKEY));
        IAcsClient client = new DefaultAcsClient(profile);
        String roleSessionName = "AliyunServiceRoleForRDSBlueGreen";
        String roleArn = String.format("acs:ram::%s:role/%s", aliUid, roleSessionName);
        AssumeRoleWithServiceIdentityRequest request = new AssumeRoleWithServiceIdentityRequest();
        request.setRoleSessionName(roleSessionName);
        request.setAssumeRoleFor(aliUid);
        request.setRoleArn(roleArn);
        request.setDurationSeconds(900L);
        String stsEndpoint = getStsEndpoint(RequestSession.getRequestId(), regionId);
        if (StringUtils.isNotEmpty(stsEndpoint)) {
            log.info("set sts sys endpoint: {}", stsEndpoint);
            request.setEndpoint(stsEndpoint);
        }
        AssumeRoleWithServiceIdentityResponse response = client.getAcsResponse(request);
        if (response != null) {
            log.info("AssumeRoleWithServiceIdentity, response: {}", JSON.toJSONString(response));
        }
        return response;
    }

    private String getStsEndpoint(String requestId, String regionId) throws Exception {
        String stsEndpointJsonStr = resourceCache.get(COMMON_PROVIDER_STS_ENDPOINT, () -> {
            String value = "";
            ConfigListResult configListResult = dBaasMetaService.getDefaultClient().listConfigs(requestId, COMMON_PROVIDER_STS_ENDPOINT);
            if (configListResult != null && CollectionUtils.isNotEmpty(configListResult.getItems())) {
                value = configListResult.getItems().get(0).getValue();
            }
            return value;
        });
        if (StringUtils.isNotBlank(stsEndpointJsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(stsEndpointJsonStr);
            return jsonObject.getString(regionId);
        }
        return null;
    }
}