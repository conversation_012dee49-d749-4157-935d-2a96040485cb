package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.CertConfig;
import com.aliyun.apsaradb.dbaasmetaapi.model.BakOwner;
import com.aliyun.apsaradb.dbaasmetaapi.model.BakOwnerListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.poddefault.action.support.GAD.Aes;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.SignatureUtil;
import com.aliyun.dba.support.utils.UrlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SignatureException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static NameServiceCommon.NameServiceConstants.BAKONWER_TYPE_CA_SERVER;

/**
 * CaServer API 扩展（支持自定义SSL证书）
 *
 * <AUTHOR> on 2024/5/20
 */
@Service
@Slf4j
public class CAServerApiExt extends CaServerApi {

    @Resource
    private DBaasMetaService dbaasMetaClient;

    /**
     * 上传用户的自定义证书
     *
     * @param requestId
     * @param instanceName
     * @param commonName
     * @return
     * @throws Exception
     */
    public boolean uploadCustomServerCert(String requestId, String instanceName, String commonName, String cert, String privateKey) throws Exception {
        Map<String, String> paramMap = new HashMap<>();
        try {
            paramMap.put("instance_name", instanceName);
            paramMap.put("connection_urls", commonName);
            paramMap.put("cert", cert);
            paramMap.put("private_key", privateKey);
            paramMap.put("request_id", requestId);
            paramMap.put("cert_id", "1");
            paramMap.put("action", "uploadCustomDBCert");
            log.info("current {} is custom, start to upload server cert", instanceName);
            JSONObject result = invokeCaServer(requestId, instanceName, paramMap, "POST");
            if (result == null) {
                throw new Exception("result is empty");
            }
            if (StringUtils.equalsIgnoreCase(LangUtil.getString(result.get("code")), "200")) {
                log.info("upload {} server cert success", instanceName);
                return true;
            } else if (StringUtils.equalsIgnoreCase(LangUtil.getString(result.get("code")), "400")) {  //证书格式，密钥和证书内容不匹配
                throw new RdsException(ErrorCode.INVALID_CERT_OR_KEY);
            } else if (StringUtils.equalsIgnoreCase(LangUtil.getString(result.get("code")), "460")) {  //证书时间戳不正确
                throw new RdsException(ErrorCode.INVALID_CERT_OR_KEY, "Specify server certificate timestamp is invalid.");
            } else if (StringUtils.equalsIgnoreCase(LangUtil.getString(result.get("code")), "461")) {  //证书域名不匹配
                throw new RdsException(ErrorCode.INVALID_CERT_OR_KEY, "Specify server certificate subject is invalid.");
            } else {
                log.error("upload custom cert to CaServer unknown error, {}", LangUtil.getString(result.get("message")));
                throw new Exception(LangUtil.getString(result.get("message")));
            }
        } catch (Exception e) {
            if (e instanceof RdsException) {
                throw e;
            }
            log.error("upload custom cert to CaServer failed, {}", e.getMessage(), e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }


    /**
     * 获取实例的自定义证书信息
     *
     * @param requestId
     * @param instanceName
     * @return
     * @throws Exception
     */
    public CertConfig getInstanceCustomServerCert(String requestId, String instanceName) throws Exception {
        Map<String, String> paramMap;
        try {
            paramMap = new HashMap<>();
            paramMap.put("action", "getInsCert");
            paramMap.put("instance_name", instanceName);
            paramMap.put("request_id", requestId);
            paramMap.put("cert_id", "1");
            JSONObject result = invokeCaServer(requestId, instanceName, paramMap, "GET");
            if (result == null || !result.get("code").toString().equals("200") || result.get("result_data") == null) {
                throw new Exception(result != null ? result.get("message").toString() : "");
            }
            Object data = result.get("result_data");
            return JSON.parseObject(JSON.toJSONString(data), CertConfig.class);
        } catch (Exception e) {
            log.error("get cert from CaServer failed, {}", e.getMessage(), e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 发送Ca Server请求。会自动获取bakOwner配置，构建URL。
     */
    private JSONObject invokeCaServer(String requestId, String instanceName, Map<String, String> paramMap, String httpMethod) throws Exception {
        Map<String, String> config = getServiceConfig(requestId, instanceName);
        String caServiceBaseUrl = config.get("url");
        String accessId = config.get("access_id");
        String accessKey = config.get("access_key");
        if (StringUtils.equalsIgnoreCase(httpMethod, "POST")) {
            paramMap.put("access_id", accessId);
            paramMap.put("timestamp", SignatureUtil.getIntTimeStamp());
            paramMap.put("request_id", SignatureUtil.getRequestId());
            paramMap.put("sign", SignatureUtil.createSignature(paramMap, accessKey));
            String result = UrlUtil.getDataAsStreamFromPostSSLUrl(caServiceBaseUrl, JSON.toJSONString(paramMap));
            return JSON.parseObject(result);
        } else {
            SignatureUtil.getParamMapWithSignatureForCA(paramMap, accessId, accessKey);
            String url = String.format("%s?%s", caServiceBaseUrl, StringUtils.join(buildCaServerRequestParams(paramMap), "&"));
            String result = UrlUtil.getDataAsStreamFromGetSSLUrl(url);
            return JSON.parseObject(result);
        }
    }


    private List<NameValuePair> buildCaServerRequestParams(Map<String, String> params) throws Exception {
        List<NameValuePair> paramsList = new ArrayList();
        for (String key : params.keySet()) {
            paramsList.add(new BasicNameValuePair(key, params.get(key)));
        }
        return paramsList;
    }

    /**
     * 从bakOwner获取实例所在集群的ca server配置
     */
    private Map<String, String> getServiceConfig(String requestId, String instanceName) throws Exception {
        ReplicaSet replicaSet = dbaasMetaClient.getDefaultClient().getReplicaSet(requestId, instanceName, false);
        BakOwnerListResult bakOwnerListResult = dbaasMetaClient.getDefaultClient()
                .listBakOwners(requestId, Integer.valueOf(BAKONWER_TYPE_CA_SERVER), replicaSet.getResourceGroupName());
        BakOwner bakOwner = bakOwnerListResult.getItems().get(0);
        JSONObject akConfig = JSONObject.parseObject(bakOwner.getApiUrl());
        String hostName = bakOwner.getHostName();
        Integer port = bakOwner.getPort();
        String uri = akConfig.getString("uri");
        String url = String.format("https://%s:%s%s", hostName, port, uri);

        Map<String, String> config = new HashMap<>();
        config.put("access_id", akConfig.get("access_id").toString());
        config.put("access_key", Aes.decryptPassword(akConfig.getString("access_key"), Aes.PWD_CRYPTKEY));
        config.put("url", url);
        return config;
    }

}
