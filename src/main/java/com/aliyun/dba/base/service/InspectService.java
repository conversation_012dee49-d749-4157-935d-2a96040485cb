/**
 * 
 */
package com.aliyun.dba.base.service;

import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;

/**
 * <AUTHOR>
 *
 */
public interface InspectService {
	// TODO: 自定义异常
	Integer triggerInspectCustinsTask(Integer operatorId, CustInstanceDO custins, Map<String, String> opts) throws Exception;
	
	JSONObject reloadCommandOutput(Long taskId) throws Exception;
}
