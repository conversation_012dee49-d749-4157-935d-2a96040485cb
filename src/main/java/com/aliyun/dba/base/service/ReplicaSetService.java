package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.apsaradb.activityprovider.model.PerformanceLevelEnum;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.cache.LocalCacheService;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.support.NetProtocolEnum;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.ProvisionedIopsHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.bak.support.BakSupport.BAKWAY_SNAPSHOT;
import static com.aliyun.dba.bak.support.BakSupport.BAKWAY_XTRABACKUP;
import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.base.support.MySQLParamConstants.TDDL_TASK_MIGRATE;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.property.ParamConstants.*;


@Service
@Slf4j
public class ReplicaSetService {


    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private UserService userService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private ResourceService resourceService;
    @Resource
    private LocalCacheService cacheService;
    @Resource
    private DbossApi dbossApi;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private ProvisionedIopsHelper provisionedIopsHelper;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private CustinsService custinsService;

    private final static LogAgent LOG_AGENT = LogFactory.getLogAgent(ReplicaSetService.class);
    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;


    public boolean isReplicaSetMariaDB(String requestId, String insName) throws ApiException {
        DefaultApi regionMetaApi = dBaasMetaService.getDefaultClient();
        ReplicaSet replicaSet = regionMetaApi.getReplicaSet(requestId, insName, true);
        return replicaSet != null && "mariadb".equalsIgnoreCase(replicaSet.getService());
    }

    public ReplicaSet getAndCheckUserReplicaSet(Map<String, String> params) throws RdsException, ApiException {
        DefaultApi regionMetaApi = dBaasMetaService.getDefaultClient();
        return this.getAndCheckUserReplicaSet(params, regionMetaApi);
    }

    public ReplicaSetListResult getReplicaSetSubIns(String requestId, String insName, String insType) throws ApiException{
        return dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId, insName, insType);
    }

    public ServiceRelationListResult getReplicaSetServices(String requestId, String name) throws ApiException{
        return  dBaasMetaService.getDefaultClient().listReplicaSetServices(requestId, name);
    }

    public ReplicaSet getAndCheckUserReplicaSetFromCenterRegion(Map<String, String> params, String centerRegionId) throws ApiException, RdsException {
        DefaultApi regionMetaApi = dBaasMetaService.getRegionClient(centerRegionId);
        return this.getAndCheckUserReplicaSet(params, regionMetaApi);
    }

    private ReplicaSet getAndCheckUserReplicaSet(Map<String, String> params, DefaultApi metaApi) throws ApiException, RdsException {
        String loginId;
        if (mysqlParamSupport.hasParameter(params, ParamConstants.INNER_USER_ID)) {
            Integer userId = CustinsValidator.getRealNumber(mysqlParamSupport.getParameterValue(params, ParamConstants.INNER_USER_ID), -1);
            loginId = userService.getUserDOByUserId(userId).getLoginId();
        } else {
            String bid = mysqlParamSupport.getParameterValue(params, ParamConstants.USER_ID);
            String uid = mysqlParamSupport.getParameterValue(params, ParamConstants.UID);
            loginId = String.format("%s_%s", bid, uid);
        }
        String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        ReplicaSet replicaSet = metaApi.getReplicaSet(requestId, dbInstanceName, true);
        if (replicaSet == null){
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        if (!loginId.equals(replicaSet.getUserId())) {
            LOG_AGENT.error("replicaSet is {} loginId is {} userid is {}", JSONObject.toJSONString(replicaSet), loginId, replicaSet.getUserId());
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return replicaSet;

    }

    public ReplicaSet getReplicaSet(Map<String, String> params) throws ApiException, RdsException {
        DefaultApi regionMetaApi = dBaasMetaService.getDefaultClient();
        String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        ReplicaSet replicaSet = regionMetaApi.getReplicaSet(requestId, dbInstanceName, true);
        if (replicaSet == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return replicaSet;
    }

    /**
     * 通过 instanceId 或者 节点角色来获取 replica
     *
     * @param requestId
     * @param replicaSetName
     * @param instanceId
     * @param role
     * @return
     * @throws ApiException
     */
    public Replica getSpecificReplica(String requestId, String replicaSetName, String replicaName, Long instanceId, Replica.RoleEnum role) throws ApiException {
        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSetName, null, null, null, null);
        List<Replica> currentReplicas = new ArrayList<>();
        if (replicaName != null) {
            currentReplicas = listReplicasInReplicaSet.getItems().stream().filter(x -> replicaName.equals(x.getName())).collect(Collectors.toList());
        }
        else if (instanceId != null) {
            currentReplicas = listReplicasInReplicaSet.getItems().stream().filter(x -> instanceId.equals(x.getId())).collect(Collectors.toList());
        } else if (role != null) {
            currentReplicas = listReplicasInReplicaSet.getItems().stream().filter(x -> x.getRole() == role).collect(Collectors.toList());
        }
        return currentReplicas.size() > 0 ? currentReplicas.get(0) : null;
    }

    public boolean isStopped(ReplicaSet replicaSet) {
        return ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus());
    }

    public boolean isReplicasetInStopStage2(String requestId, String replicaSetName) throws ApiException {
        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSetName, null, null, null, null);

        List<Replica> stoppedReplicas = listReplicasInReplicaSet.getItems().stream().filter(x -> Replica.StatusEnum.STOPPED.equals(x.getStatus())).collect(Collectors.toList());
        return !stoppedReplicas.isEmpty();
    }

    public static boolean isReplicaSetAliGroup(ReplicaSet replicaSet) {
        ReplicaSet.BizTypeEnum bizType = replicaSet.getBizType();
        return PodParameterHelper.isAliGroup(bizType) || replicaSet.getConnType() == ReplicaSet.ConnTypeEnum.TDDL;
    }

    public static boolean isTDDL(ReplicaSet replicaSet) {
        return CONN_TYPE_TDDL.equalsIgnoreCase(replicaSet.getConnType().name());
    }

    public boolean isServerless(ReplicaSet replicaSet) {
        return ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(replicaSet.getCategory().toLowerCase());
    }

    public boolean isServerless(InstanceLevel instanceLevel) {
        return ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(instanceLevel.getCategory().getValue());
    }

    public boolean isTddlTaskMigrate(String requestId, ReplicaSet replicaSet) throws ApiException {
        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
        String tddlTaskMigrate = labels.get(TDDL_TASK_MIGRATE);
        String composeTag = labels.get("composeTag");
        return !"cloud_disk_for_arm".equalsIgnoreCase(composeTag)
                && Boolean.parseBoolean(tddlTaskMigrate)
                && CONN_TYPE_TDDL.equalsIgnoreCase(Objects.requireNonNull(replicaSet.getConnType()).toString());
    }

    public boolean isTddlDeleteIns(String requestId, ReplicaSet replicaSet) throws ApiException {
        return CONN_TYPE_TDDL.equalsIgnoreCase(Objects.requireNonNull(replicaSet.getConnType()).toString());
    }

    public boolean isTddlChangeSpecTag(String requestId, String replicaSetName, String connType) throws ApiException, RdsException {
        if (!CONN_TYPE_TDDL.equalsIgnoreCase(connType)) {
            return false;
        }
        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSetName);
        String tddlTaskMigrate = labels.get(TDDL_TASK_MIGRATE);
        String instructionSetArch = labels.get("instructionSetArch");
        boolean changeSpecTag = Boolean.valueOf(cacheService.getValue("UPGRADE_XDB_INS_ROUTE"));
        return changeSpecTag && !CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(instructionSetArch) && !Boolean.valueOf(tddlTaskMigrate);
    }

    public boolean isReplicaSetXDB(String requestId, String replicaSetName) throws ApiException {
        ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, replicaSetName);
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSetResource.getReplicaSet().getService(), replicaSetResource.getReplicaSet().getServiceVersion(), replicaSetResource.getReplicaSet().getClassCode(), true);
        if ((DB_VERSION_MYSQL_57.equalsIgnoreCase(replicaSetResource.getReplicaSet().getServiceVersion()) || DB_VERSION_MYSQL_80.equalsIgnoreCase(replicaSetResource.getReplicaSet().getServiceVersion()))
                && InstanceLevel.CategoryEnum.ENTERPRISE == instanceLevel.getCategory()) {
            return true;
        }
        return "xdb".equalsIgnoreCase(replicaSetResource.getReplicaSet().getLabels().get("dbEngine"));
    }

    public String getReplicaSetStorageType(String replicaSetName, String requestId) throws ApiException {
        ReplicaListResult replicasResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSetName, null, null, null, null);
        Replica dataReplica = replicasResult.getItems().stream()
                .filter(x -> Replica.RoleEnum.LOGGER != x.getRole())
                .collect(Collectors.toList()).get(0);
        return dataReplica.getStorageType().toString();
    }

    public boolean isVbmInstance(String requestId, String replicaSetName) throws ApiException {
        String vbmCustinsLabel = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetName, VBM_CUSTINS_LABEL_KEY);
        return StringUtils.isNotBlank(vbmCustinsLabel) && VBM_CUSTINS_LABEL_VALUE.equals(vbmCustinsLabel);
    }

    /**
     * 获取Volume的性能等级，
     * 只有CloudESSD支持
     * @param diskType
     * @return
     */
    public String getVolumePerfLevel(String requestId, String replicaSetName, String diskType) throws ApiException {
        if (!StringUtils.equalsIgnoreCase(diskType, Replica.StorageTypeEnum.CLOUD_ESSD.toString())) {
            return null;
        }
        ReplicaListResult replicasResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSetName, null, null, null, null);
        Replica dataReplica = replicasResult.getItems().stream()
                .filter(x -> Replica.RoleEnum.LOGGER != x.getRole())
                .collect(Collectors.toList()).get(0);
        ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, dataReplica.getId(), null);
        Optional<Volume> dataVolume = Objects.requireNonNull(replicaResource.getVolumes()).stream().filter(v -> StringUtils.equalsIgnoreCase(v.getCategory(), "data")).findFirst();
        return dataVolume.get().getPerformanceLevel() == null ? PerformanceLevelEnum.PL1.getValue() : dataVolume.get().getPerformanceLevel().getValue(); //默认是PL1
    }

    /**
     *  autopl parameters (burstingEnabled)
     *  1. paramBurstingEnabled
     *  2. srcBurstingEnabled
     *  3. AUTOPL_BURSTING_ENABLED_DEFAULT_VALUE: false
     */
    public Boolean getAutoConfigBurstingEnabled(String requestId, String replicaSetName, String diskType, String paramBurstingEnabled) throws ApiException
    {
        // first priority is paramBurstingEnabled
        if(StringUtils.isNotBlank(paramBurstingEnabled))
        {
            return Boolean.parseBoolean(paramBurstingEnabled);
        }
        // if replicaSetName is blank, use AUTOPL_BURSTING_ENABLED_DEFAULT_VALUE: false
        if(StringUtils.isBlank(replicaSetName))
        {
            return AUTOPL_BURSTING_ENABLED_DEFAULT_VALUE;
        }
        // if replicaSetName is not null, use srcBurstingEnabled
        String srcBurstingEnabled = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                    requestId, replicaSetName, AUTOPL_BURSTING_ENABLED);

        return StringUtils.isNotBlank(srcBurstingEnabled)?Boolean.parseBoolean(srcBurstingEnabled):AUTOPL_BURSTING_ENABLED_DEFAULT_VALUE;

    }

    /**
     *  autopl parameters (provisionedIops), not force refresh
     */
    public Long getAutoConfigProvisionedIops(String requestId, String replicaSetName, String diskType, String paramProvisionedIops, Integer diskSizeGB, String uid) throws ApiException {
        return getAutoConfigProvisionedIops(requestId, replicaSetName, diskType, paramProvisionedIops, diskSizeGB, uid, false);
    }
    /**
     *  autopl parameters (provisionedIops)
     *  1. paramProvisionedIops
     *  2. srcProvisionedIops
     *  3. defaultProvisionedIops, by provisionedIops policy
     */
    public Long getAutoConfigProvisionedIops(String requestId, String replicaSetName, String diskType, String paramProvisionedIops, Integer diskSizeGB, String uid, boolean forceRefresh) throws ApiException {
        // check paramProvisionedIops
        if (!PodParameterHelper.checkProvisionedIopsValid(replicaSetName, diskType, paramProvisionedIops, diskSizeGB)) {
            return Long.valueOf(AUTOPL_PROVISIONED_IOPS_DEFAULT_VALUE);
        }
        // get default provisionedIops
        Long defaultProvisionedIops = provisionedIopsHelper.getDefaultProvisionedIops(diskSizeGB, uid, replicaSetName);
        // first priority is paramProvisionedIops
        if (StringUtils.isNotBlank(paramProvisionedIops)) {
            return Long.parseLong(paramProvisionedIops);
        }
        // if replicaSetName is blank or force refresh, use defaultProvisionedIops
        if (StringUtils.isBlank(replicaSetName) || forceRefresh) {
            return defaultProvisionedIops;
        }
        // if replicaSetName is not null and not forceRefresh, use srcProvisionedIops
        String srcProvisionedIops = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                requestId, replicaSetName, AUTOPL_PROVISIONED_IOPS);

        return StringUtils.isNumeric(srcProvisionedIops) ? Math.max(defaultProvisionedIops, Long.parseLong(srcProvisionedIops)) : defaultProvisionedIops;

    }

    /**
     * cold data parameter: coldDataEnabled
     * 1. parameterColdDataEnabled
     * 2. srcColdDataEnabled
     * 3. default value: Boolean.FALSE
     */
    public Boolean getColdDataEnabled(String requestId, String replicaSetName, String parameterColdDataEnabled) throws ApiException {
        // first priority is parameterColdDataEnabled
        if(StringUtils.isNotBlank(parameterColdDataEnabled))
        {
            return Boolean.parseBoolean(parameterColdDataEnabled);
        }
        // if replicaSetName is blank, use default value: Boolean.FALSE
        if(StringUtils.isBlank(replicaSetName))
        {
            return Boolean.FALSE;
        }
        // if replicaSetName is not null, use srcColdDataEnabled
        String srcColdDataEnabled = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                requestId, replicaSetName, RDS_COLD_DATA_ENABLED);

        return StringUtils.isNotBlank(srcColdDataEnabled)?Boolean.parseBoolean(srcColdDataEnabled):Boolean.FALSE;
    }

    /**
     * IoAcceleration parameter: ioAccelerationEnabled
     * 1. parameterIoAccelerationEnabled
     * 2. srcIoAccelerationEnabled
     */
    public Boolean getIoAccelerationEnabled(String requestId, String replicaSetName, String parameterIoAccelerationEnabled) throws Exception {
        // first priority is parameterIoAccelerationEnabled
        if(StringUtils.isNotBlank(parameterIoAccelerationEnabled))
        {
            return podCommonSupport.transferIoAccelerationEnabledType(parameterIoAccelerationEnabled);
        }
        // if replicaSetName is blank, use default value: Boolean.FALSE
        if(StringUtils.isBlank(replicaSetName))
        {
            return Boolean.FALSE;
        }
        // if replicaSetName is not null, use srcIoAccelerationEnabled
        boolean srcIoAccelerationEnabled = podCommonSupport.transferIoAccelerationEnabledType(dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                requestId, replicaSetName, IO_ACCELERATION_ENABLED));

        return srcIoAccelerationEnabled;
    }

    /*
    获取replica级别的磁盘性能等级
     */
    public String getReplicaVolumePerfLevel(String requestId, Replica replica) throws ApiException {
        if (!StringUtils.equalsIgnoreCase(replica.getStorageType().toString(), Replica.StorageTypeEnum.CLOUD_ESSD.toString())) {
            return null;
        }

        ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, replica.getId(), null);
        Optional<Volume> dataVolume = Objects.requireNonNull(replicaResource.getVolumes()).stream().filter(v -> StringUtils.equalsIgnoreCase(v.getCategory(), "data")).findFirst();
        return dataVolume.get().getPerformanceLevel() == null ? PerformanceLevelEnum.PL1.getValue() : dataVolume.get().getPerformanceLevel().getValue(); //默认是PL1
    }

    public static Boolean isStorageTypeCloudDisk(String storageType) {
        return Replica.StorageTypeEnum.CLOUD_ESSD.toString().equals(storageType)
                || Replica.StorageTypeEnum.CLOUD_AUTO.toString().equals(storageType)
                || Replica.StorageTypeEnum.CLOUD_SSD.toString().equals(storageType);
    }


    public static String getStorageType(String storageType) throws RdsException {
        try {
            return Replica.StorageTypeEnum.valueOf(storageType.toUpperCase()).toString();
        } catch (Exception e) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        }
    }


    private final static Map<InstanceLevel.CategoryEnum, SingleTenantStrategy> SINGLE_TENANT_STRATEGY = new HashMap<InstanceLevel.CategoryEnum, SingleTenantStrategy>(){{

    }};


    /**
     * 云盘非共享规格或者命中单租户的策略
     * 即为云盘单租户
     *
     * 判断是否单租户该方法仅用于申请资源时候使用
     *
     * @param diskType
     * @param instanceLevel
     * @return
     */
    public boolean isCloudSingleTenant(ReplicaSet.BizTypeEnum bizType, String diskType, InstanceLevel instanceLevel, boolean isDhg) {
        if (!PodParameterHelper.isAliYun(bizType) || isDhg) {
            //非云上业务没有单租户
            //大客户主机组没有单租户
            return false;
        }
        if  (isStorageTypeCloudDisk(diskType) &&
                instanceLevel.getIsolationType() != InstanceLevel.IsolationTypeEnum.COMMON) {
            return true;
        }
        //不同的产品类型有自己的单租户策略
        if (!SINGLE_TENANT_STRATEGY.containsKey(instanceLevel.getCategory())) {
            return false;
        }
        return SINGLE_TENANT_STRATEGY.get(instanceLevel.getCategory()).doMatch(instanceLevel);
    }
    /**
     * 从实例的Label属性中判断是否云盘单租户
     */
    public boolean isCloudDiskSingleTenant(String requestId, ReplicaSet replicaSet) throws ApiException {
        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
        return LangUtil.getBoolean(labels.getOrDefault("single_tenant", "false"));
    }

    /**
     * 集团云盘版本需要两块云盘, 需要分别指定数据盘和本地盘的空间大小
     * 模式数据盘与日志盘空间配比为 3: 1, 最小100G，最大1T
     *
     * @param diskSize
     * @param volumeSpecName
     * @return
     */
    public List<VolumeSpec> getCloudDiskReplicaVolumeSpecList(Integer diskSize, String volumeSpecName) {
        List<VolumeSpec> volumeSpecList = new ArrayList<>();

        VolumeSpec dataVolumeSpec = new VolumeSpec();
        dataVolumeSpec.setName("data");
        dataVolumeSpec.setCategory("data");
        dataVolumeSpec.setDiskSize(diskSize);
        volumeSpecList.add(dataVolumeSpec);

        VolumeSpec logVolumeSpec = new VolumeSpec();
        logVolumeSpec.setName("log");
        logVolumeSpec.setCategory("log");
        int logDiskSize = 80;
        if (diskSize > 512 && diskSize <= 2048) {
            logDiskSize = 150;
        } else if (diskSize > 2048 && diskSize <= 4096) {
            logDiskSize = 250;
        } else if (diskSize > 4096) {
            logDiskSize = 400;
        }
        logDiskSize = getDailyLogDiskSize(logDiskSize);
        logVolumeSpec.setDiskSize(logDiskSize);
        volumeSpecList.add(logVolumeSpec);

        return volumeSpecList;
    }

    private int getDailyLogDiskSize(int defaultValue) {
        int returnValue = defaultValue;
        List<String> realValues = resourceService.getResourceRealValueList("DailyLogDiskSize");
        if (realValues != null && !realValues.isEmpty()) {
            try {
                returnValue = Integer.parseInt(realValues.get(0));
            } catch (Exception ignored) {
            }
        }
        return returnValue;
    }

    public boolean replicasetInAvailableStatus(ReplicaSet.StatusEnum status) {
        return ReplicaSet.StatusEnum.ACTIVATION == status
                || ReplicaSet.StatusEnum.TRANSING == status
                || ReplicaSet.StatusEnum.HA_SWITCHING == status
                || ReplicaSet.StatusEnum.BACKING == status;
    }

    public boolean isActive(ReplicaSet replicaSet) {
        return ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSet.getStatus()) ||
                ReplicaSet.StatusEnum.ACTIVE.equals(replicaSet.getStatus());
    }

    public boolean isXDBIns(String requestId, String replicaSetName) throws ApiException {
        ReplicaSet primaryReplicaSet;
        ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, replicaSetName);
        if (ReplicaSet.InsTypeEnum.TMP.equals(replicaSetResource.getReplicaSet().getInsType())) {
            replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, replicaSetResource.getReplicaSet().getPrimaryInsName());
        }

        /**
         * 获取中心实例是否为xdb
         */
        String centerReplicaSetName = replicaSetResource.getReplicaSet().getLabels().get("CenterReplicaSetName");
        if (StringUtils.isNotEmpty(centerReplicaSetName)) {
            String centerRegionId = replicaSetResource.getReplicaSet().getLabels().get("CenterRegionId");
            primaryReplicaSet = dBaasMetaService.getRegionClient(centerRegionId).getReplicaSetBundleResource(requestId, centerReplicaSetName).getReplicaSet();
            return "xdb".equalsIgnoreCase(primaryReplicaSet.getLabels().get("dbEngine"));

        } else {

            /**
             * 没有中心实例获取当前region的父实例
             */
            String primaryInsName = replicaSetResource.getReplicaSet().getPrimaryInsName();
            if (primaryInsName != null && !primaryInsName.isEmpty()) {
                primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, primaryInsName).getReplicaSet();
            } else {
                primaryReplicaSet = replicaSetResource.getReplicaSet();
            }
        }

        if ((DB_VERSION_MYSQL_57.equalsIgnoreCase(primaryReplicaSet.getServiceVersion()) || DB_VERSION_MYSQL_80.equalsIgnoreCase(primaryReplicaSet.getServiceVersion()))
                && primaryReplicaSet.getCategory().equalsIgnoreCase("enterprise")) {
            return true;
        }

        return "xdb".equalsIgnoreCase(primaryReplicaSet.getLabels().get("dbEngine"));

    }

    public boolean isCloudPfsDisk(String requestId, String replicaSetName) throws com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSetName);
        if (labels.containsKey("cloud_pfs")) {

            return Boolean.parseBoolean(labels.get("cloud_pfs"));
        }
        // 兼容集团实例
        return "cloud_pfs".equalsIgnoreCase(labels.get("composeTag"));
    }

    public String getReplicaSetBakWay(String requestId, String replicaSetName) throws ApiException {
        String diskType = this.getReplicaSetStorageType(replicaSetName, requestId);
        if (this.isCloudPfsDisk(requestId, replicaSetName) || !isStorageTypeCloudDisk(diskType)) {
            return BAKWAY_XTRABACKUP;
        } else {
            return BAKWAY_SNAPSHOT;
        }
    }

    public void mazPreCheckForXDB(Map<String, String> params, String dbEngine) {
        mazPreCheckForXDB(params, dbEngine, ImmutableList.of("master", "slave", "slave"));
    }

    public void mazPreCheckForReadIns(Map<String, String> params, String dbEngine, int nodeCount) {
        // 最多两个节点
        nodeCount = Integer.min(nodeCount, 2);
        mazPreCheckForXDB(params, dbEngine, ImmutableList.of("master", "slave").subList(0, nodeCount));
    }

    /**
     * XDB如果只传了一个机房，需要补充机房
     *
     * @param params
     * @param dbEngine
     */
    public void mazPreCheckForXDB(Map<String, String> params, String dbEngine, List<String> roleList) {
        String multiAVZExParamStr = CustinsParamSupport.getParameterValue(params, MULTI_AVZ_EX_PARAM, "");
        if (StringUtils.isNotBlank(multiAVZExParamStr)) {
            return;
        }

        String connType = getParameterValue(params, "DBInstanceConnType");
        String tddlClusterName = getParameterValue(params, "TddlClusterName");
        boolean isBizTypeAligroup = (StringUtils.isNotBlank(tddlClusterName) || CONN_TYPE_TDDL.equalsIgnoreCase(connType));
        if (!"XDB".equalsIgnoreCase(dbEngine) || !isBizTypeAligroup) {
            return;
        }

        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO> avzList = new ArrayList<>();
        for (String role : roleList) {
            AvailableZoneInfoDO az = new AvailableZoneInfoDO(getParameterValue(params, REGION), role);
            az.setZoneID(getParameterValue(params, ZONE_ID));
            az.setUserSpecified(true);
            az.setVSwitchID(getParameterValue(params, ParamConstants.VSWITCH_ID));
            avzList.add(az);
        }
        multiAVZExParamDO.setAvailableZoneInfoList(avzList);
        params.put(MULTI_AVZ_EX_PARAM.toLowerCase(), JSONObject.toJSONString(multiAVZExParamDO));
        params.put(DISPENSE_MODE.toLowerCase(), "1");
    }

    public boolean isAligroupDoubleNodeRead(String requestId, String replicaSetName) throws ApiException{
        return isAligroupDoubleNodeRead(requestId, null, replicaSetName);
    }

    public boolean isAligroupDoubleNodeRead(String requestId, String regionId, String replicaSetName) throws ApiException{
        Map<String, String> labels = dBaasMetaService.getRegionClient(regionId).listReplicaSetLabels(requestId, replicaSetName);
        if(labels.containsKey("nodeCount") && "2".equals(labels.get("nodeCount"))){
            return true;
        }
        return false;
    }

    /**
     * 读取实例的ReplicaSetLabel: NetProtocol = IPv4IPv6
     * 如果不存在则使用默认的NetProtocol=IPv4
     * */
    public NetProtocolEnum getNetProtocol(String requestId, String replicaSetName) throws Exception {
        NetProtocolEnum netProtocolEnum = NetProtocolEnum.IPv4;
        try {
            String netProtocolLabel = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetName, "NetProtocol");
            if (StringUtils.isNotEmpty(netProtocolLabel)) {
                netProtocolEnum = NetProtocolEnum.valueOf(netProtocolLabel);
            }
        } catch (ApiException e) {
            log.warn("queryReplicaSetLabelError", e);
        } catch (IllegalArgumentException e) {
            log.warn("ConvertLabelToNetProtocolError, replicaSet: {}", replicaSetName);
        }
        return netProtocolEnum;
    }

    public VolumeSpec buildEssdVolumeSpec(Replica replica, String diskCategory, String requestId) throws Exception {
        if (replica.getStorageType() != Replica.StorageTypeEnum.CLOUD_ESSD
                && replica.getStorageType() != Replica.StorageTypeEnum.CLOUD_AUTO) {
            return null;
        }

        ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, replica.getId(), null);
        Volume volume =
            replicaResource.getVolumes().stream().filter(i -> StringUtils.equalsIgnoreCase(i.getCategory(), diskCategory)).findFirst()
                .get();
        if (volume != null) {
            VolumeSpec volumeSpec = new VolumeSpec();
            volumeSpec.setName(diskCategory);
            volumeSpec.setCategory(diskCategory);
            if (volume.getPerformanceLevel() != null) {
                volumeSpec.setPerformanceLevel(volume.getPerformanceLevel().getValue());
            } else {
                volumeSpec.setPerformanceLevel("PL1");
            }
            volumeSpec.setDiskSize(volume.getSizeMB() / 1024);
            return volumeSpec;
        }
        return null;
    }

    /**
     * 修改replica状态，元数据修改。自行判断是否可以在该状态修改
     */
    public void updateReplicaStatus(String requestId, Long replicaId, Replica.StatusEnum status) throws ApiException {
        dBaasMetaService.getDefaultClient().updateReplicaStatus(requestId, replicaId, status.toString());
    }

    public boolean isCluster(String requestId, String replicaSetName) throws ApiException {
        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSetName, false);
        return InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(replicaSet.getCategory());
    }

    public boolean isMgr(String requestId, String replicaSetName) throws ApiException {
        String syncMode = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetName, LABEL_SYNC_MODE);
        return isMgr(Collections.singletonMap(LABEL_SYNC_MODE, syncMode));
    }

    public boolean isMgr(String performanceMode) {
        return CLUSTER_MGR.equalsIgnoreCase(performanceMode);
    }

    public boolean isMgr(Map<String, String> labels) {
        return SYNC_MODE_MGR.equalsIgnoreCase(labels.get(LABEL_SYNC_MODE));
    }

    /**
     * 实例参数变更，检查MGR相关限制
     * */
    public void isSupportMgr(String replicaSetName, boolean isChangeParamGroup) throws Exception {
        String requestId = RequestSession.getRequestId();
        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSetName, false);
        ResourceDO resource = resourceService.getResourceByResKey("mgr_minor_version");
        if (resource != null && StringUtils.isNotEmpty(resource.getRealValue())) {
            String requestDBMinorVersion = resource.getRealValue().split(",")[0];
            String requestMaxScaleMinorVersion = resource.getRealValue().split(",")[1];

            // 检查DB内核小版本
            String dbMinorVersion = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, replicaSet.getId().intValue());
            if (dbMinorVersion.compareTo(requestDBMinorVersion) < 0) {
                log.error("custins db version {}, and mgr_minor_version request >= {}.", dbMinorVersion, requestDBMinorVersion);
                throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }

            // 检查Maxscale内核小版本
            Integer maxscaleInsId = getMaxscaleInsId(replicaSet.getName());
            if (maxscaleInsId != null) {
                String maxScaleMinorVersion = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, maxscaleInsId);
                if (maxScaleMinorVersion.compareTo(requestMaxScaleMinorVersion) < 0) {
                    log.error("custins maxscale version {}, and mgr_minor_version request >= {}.", maxScaleMinorVersion, requestMaxScaleMinorVersion);
                    throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
                }
            }
        }

        if (!"8.0".equals(replicaSet.getServiceVersion())) {
            log.error("{} custins db version {}, and mgr only support 8.0 .", requestId, replicaSet.getServiceVersion());
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_ENGINE_VERSION);
        }
        InstanceLevel level = dBaasMetaService.getDefaultClient()
                .getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), false);
        if (level.getMemSizeMB() < 8 * 1024) {
            log.error("{} memory too small for mgr, need >= 8GB.", requestId);
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_MEMORY_TOO_SMALL);
        }

        /**
         * 变更参数模板时，支持xengine直接变MGR，只要实例没有xengine表
         * */
        if (!isChangeParamGroup) {
            String paramGroupId = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetName, "param_group_id");
            if (StringUtils.isNotEmpty(paramGroupId)) {
                String storageEngine = SysParamGroupHelper.getDBStorageEngine(paramGroupId);
                if ("xengine".equalsIgnoreCase(storageEngine)) {
                    log.error("param group is xengine.");
                    throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_XENGINE);
                }
            }
        }

        ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null);
        List<Replica> replicaList = replicaListResult.getItems();
        if (replicaListResult.getItems().size() < 3 || replicaList.size() % 2 != 1) {
            log.error("{} node num is {}.", requestId, replicaList.size());
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_NODE_NUM);
        }

        Set<String> classCodes = new HashSet<>();
        for (Replica replica : replicaList) {
            classCodes.add(replica.getClassCode());
        }

        if (classCodes.size() > 1) {
            log.error("{} class code set is {}.", requestId, classCodes);
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_NODE_CLASS_CODE);
        }

        Map<String, Object> engineCount = dbossApi.queryEngineCount(replicaSet.getId().intValue(), 3);
        Integer xengineCount = Integer.valueOf(engineCount.getOrDefault("xengine", "0").toString());
        if (xengineCount > 0) {
            log.error("{} find xengine in custins.", requestId);
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_XENGINE);
        }

        Map<String, Object> tableCountInfo = dbossApi.getWithoutPriKeyTableCount(replicaSet.getId().intValue());
        Integer tableCount = Integer.valueOf(tableCountInfo.getOrDefault("count", "0").toString());
        if (tableCount > 0) {
            log.error("{} find table without primary key in custins.", requestId);
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_TABLE_WITHOUT_PRIMARY_KEY);
        }

        boolean ioAccelerationEnabled = podCommonSupport.transferIoAccelerationEnabledType(dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSetName).get(IO_ACCELERATION_ENABLED));
        if (ioAccelerationEnabled) {
            log.error("{} mgr with IO acceleration not supported.", requestId);
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_Invalid_Parameters);
        }
    }

    public void isSupportMgr(Integer nodeNum, InstanceLevel instanceLevel, String minorVersionTag) throws RdsException {
        if (StringUtils.isNotEmpty(minorVersionTag)) {
            ResourceDO resource = resourceService.getResourceByResKey("mgr_minor_version");
            if (resource != null && StringUtils.isNotEmpty(resource.getRealValue())) {
                String requestDBMinorVersion = resource.getRealValue().split(",")[0];
                if (minorVersionTag.compareTo(requestDBMinorVersion) < 0) {
                    log.error("custins db version {}, and mgr_minor_version request >= {}.", minorVersionTag, resource.getRealValue());
                    throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
                }
            }
        }
        if (instanceLevel.getMemSizeMB() < 8 * 1024) {
            log.error("{} memory too small for mgr, need >= 8GB.", RequestSession.getRequestId());
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_MEMORY_TOO_SMALL);
        }
        if (nodeNum < 3 || nodeNum % 2 != 1) {
            log.error("{} node num is {}.", RequestSession.getRequestId(), nodeNum);
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_NODE_NUM);
        }
    }

    public Integer getMaxscaleInsId(String replicaSetName) throws ApiException {
        ServiceRelationListResult serviceRelationList = dBaasMetaService.getDefaultClient()
                .listReplicaSetServices(RequestSession.getRequestId(), replicaSetName);
        List<ServiceRelation.StatusEnum> statusEnumList = new ArrayList<>();
        statusEnumList.add(ServiceRelation.StatusEnum.ACTIVE);
        if (serviceRelationList.getItems() != null) {
            Optional<ServiceRelation> maxscale = serviceRelationList
                    .getItems()
                    .stream()
                    .filter(r -> "maxscale".equalsIgnoreCase(r.getServiceRole()) && statusEnumList.contains(r.getStatus())).findFirst();
            if (maxscale.isPresent()) {
                return Integer.valueOf(maxscale.get().getServiceId());
            }
        }
        return null;
    }

    /**
     * MGR实例拦截不支持修改的参数
     * */
    public void isMgrSupportParam(Map<String, Object> params) throws RdsException {
        Set<String> blockedParam = new HashSet<String>() {{
            add("master_info_repository");
            add("relay_log_info_repository");
            add("binlog_transaction_dependency_tracking");
            add("slave_parallel_type");
            add("slave_parallel_workers");
            add("persist_binlog_to_redo");
            add("binlog_parallel_flush");
            add("sync_binlog");
            add("innodb_flush_log_at_trx_commit");

        }};
        for (String name : params.keySet()) {
            name = name.replace("loose_", "");
            if (blockedParam.contains(name)) {
                String errorMsg = String.format("param {} do not support modify in mgr.", name);
                log.error(errorMsg);
                throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_Invalid_Parameters);
            }
        }
    }

    public Endpoint getReplicaSetVpcEndpoint(String requestId, String replicaSetName) throws Exception {
        EndpointListResult endpointListResult = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSetName, null, null, null, null);
        if (CollectionUtils.isEmpty(endpointListResult.getItems())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
        }
        List<Endpoint> endpointList = endpointListResult.getItems()
                .stream().filter(v -> (v.getNetType() == Endpoint.NetTypeEnum.VPC && v.getType() == Endpoint.TypeEnum.NORMAL && Boolean.TRUE.equals(v.getUserVisible())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(endpointList) || endpointList.size() > 1) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
        }
        return endpointList.get(0);
    }

    public boolean isReplicaSetExternalReplication(ReplicaSet replicaSet) throws RdsException {
        try {
            return mysqlParameterHelper.isExternalReplication(Math.toIntExact(replicaSet.getId()));
        } catch (Exception ex) {
            throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
        }
    }

    /**
     *
     * 是否允许开启原生复制（白名单控制）
     *
     * @param replicaSet
     * @return
     */
    private boolean isAllowExternalReplication(ReplicaSet replicaSet) {
        final String ALLOW_EXTERNAL_REPLICATION = "ALLOW_EXTERNAL_REPLICATION";
        try {
            ResourceDO resourceDO = resourceService.getResourceByResKey(ALLOW_EXTERNAL_REPLICATION);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                if ("*".equals(resourceDO.getRealValue())) {
                    // 全部允许
                    return true;
                }
                Set<String> whiteIds = JSON.parseObject(resourceDO.getRealValue(), Set.class);
                String loginId = replicaSet.getUserId();
                String[] arr = StringUtils.split(loginId, "_");
                if (arr != null && arr.length == 2) {
                    return whiteIds.contains(arr[1]);  //在白名单中的，允许开启外部复制
                }
            }
        } catch (Exception e) {
            //ignore
            log.warn("Get ALLOW_EXTERNAL_REPLICATION failed, ignore", e);
        }
        return false; // 默认都是禁止
    }

    /**
     * 原生复制预检查
     */
    public void preCheckForExternalReplication(ReplicaSet replicaSet, String requestId, ExternalReplicationScenario scenario) throws Exception {
        if (ExternalReplicationScenario.activate.equals(scenario)) {
            // 只支持基础版、serverless
            if (!replicaSet.getCategory().equalsIgnoreCase(InstanceLevel.CategoryEnum.BASIC.getValue()) &&
                    !replicaSet.getCategory().equalsIgnoreCase(InstanceLevel.CategoryEnum.SERVERLESS_BASIC.getValue())) {
                log.error("requestId: {}, required category: {}, actual category: {}", requestId, CATEGORY_BASIC + " or serverless_basic", replicaSet.getCategory());
                throw new RdsException(ErrorCode.INVALID_SOURCE_CATEGORY);
            }
            // 不支持带maxscale的实例
            if (custinsService.checkHaveMaxscaleService(replicaSet.getId().intValue()) != null) {
                log.error("requestId: {}, custins with maxscale service is not supported", requestId);
                throw new RdsException(ErrorCode.INVALID_DBINSTANCETYPE);
            }
            // 暂时只支持5.7
            if (!replicaSet.getServiceVersion().equals("5.7")) {
                log.error("requestId: {}, required major version: 5.7, actual major version: {}", requestId, replicaSet.getServiceVersion());
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }
            // 只支持主实例
            if (ReplicaSet.InsTypeEnum.MAIN != replicaSet.getInsType()) {
                log.error("requestId: {}, required type: {}, actual type: {}", requestId, ReplicaSet.InsTypeEnum.MAIN, replicaSet.getInsType());
                throw new RdsException(ErrorCode.INVALID_DBINSTANCETYPE);
            }
            //内核小版本检查
            CustinsParamDO minorVersionInfo = custinsParamService.getCustinsParam(Math.toIntExact(replicaSet.getId()), CUSTINS_PARAM_MINOR_VERSION_KEY);
            if (minorVersionInfo == null) {
                throw new RdsException(ErrorCode.CUSTINS_MINOR_VERSION_ATTR_MISSING);
            }
            String currentMinorVersion = minorVersionInfo.getValue();
            if (StringUtils.isBlank(currentMinorVersion)) {
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION, "Failed to parse replicaSetMinorVersion");
            }
            String currentMinorVersionDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(currentMinorVersion);
            ResourceDO resourceDO = resourceService.getResourceByResKey("EXTERNAL_REPLICATION_MINOR_VERSION");
            if (Objects.isNull(resourceDO) || StringUtils.isBlank(resourceDO.getRealValue())) {
                log.error("requestId: {}, querying resource error, key: {}", requestId, "EXTERNAL_REPLICATION_MINOR_VERSION");
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            JSONObject configMap = JSON.parseObject(resourceDO.getRealValue());
            String requiredMinorVersion = configMap.getString(replicaSet.getServiceVersion());

            if (StringUtils.isBlank(currentMinorVersionDate) || currentMinorVersionDate.compareTo(requiredMinorVersion) < 0) {
                log.error("requestId: {} required minor version: 20240731, actual minor version: {}", requestId, currentMinorVersion);
                throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }
            //判断实例状态是否是运行中
            ReplicaSet.StatusEnum custinsStatus = replicaSet.getStatus();
            if (ReplicaSet.StatusEnum.ACTIVATION != custinsStatus && ReplicaSet.StatusEnum.ACTIVE != custinsStatus) {
                log.error("requestId: {}, current instance status is {}, not support this operation.", requestId, custinsStatus);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            // 白名单逻辑
            if (!isAllowExternalReplication(replicaSet)) {
                log.error("requestId: {}, current user not support this operation.", requestId);
                throw new RdsException(ErrorCode.UNSUPPORTED_USER_PERMISSION);
            }
        } else if (ExternalReplicationScenario.deactivate.equals(scenario) || ExternalReplicationScenario.rebuild.equals((scenario))){
            if (!isReplicaSetExternalReplication(replicaSet)) {
                log.error("requestId: {}, current instance is not external replication instance", requestId);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
        }
    }

    public boolean isAllowEncryptRegularCloudDisk(ReplicaSet replicaSet) {
        final String ALLOW_ENCRYPT_REGULAR_CLOUD_DISK = "ALLOW_ENCRYPT_REGULAR_CLOUD_DISK";
        try {
            ResourceDO resourceDO = resourceService.getResourceByResKey(ALLOW_ENCRYPT_REGULAR_CLOUD_DISK);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                if ("*".equals(resourceDO.getRealValue())) {
                    // 全部允许
                    return true;
                }
                Set<String> whiteIds = JSON.parseObject(resourceDO.getRealValue(), Set.class);
                String loginId = replicaSet.getUserId();
                String[] arr = StringUtils.split(loginId, "_");
                if (arr != null && arr.length == 2) {
                    return whiteIds.contains(arr[1]);  //在白名单中的，允许开启外部复制
                }
            }
        } catch (Exception e) {
            //ignore
            log.warn("Get ALLOW_EXTERNAL_REPLICATION failed, ignore", e);
        }
        return false; // 默认都是禁止
    }
}
