package com.aliyun.dba.base.service;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.gdnmetaapi.ApiException;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstance;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMemberListResult;
import com.aliyun.dba.base.lib.GdnMetaService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class GdnInstanceService {
    @Resource
    private GdnMetaService gdnMetaService;

    public void createGdnInstanceMember(String requestId, String gdnInstanceName, InstanceMember instanceMember) throws RdsException, ApiException {
        getAndCheckGdnInstance(requestId, gdnInstanceName);
        gdnMetaService.getClient().addMember(requestId, gdnInstanceName, instanceMember);
    }

    public GdnInstance getGdnInstance(String requestId, String gdnInstanceName) {
        try {
            return gdnMetaService.getClient().getGdnInstance(requestId, gdnInstanceName, true);
        } catch (ApiException e) {
            return null;
        }
    }

    public GdnInstance getAndCheckGdnInstance(String requestId, String gdnInstanceName) throws ApiException, RdsException {
        GdnInstance gdnInstance = gdnMetaService.getClient().getGdnInstance(requestId, gdnInstanceName, true);
        if (gdnInstance == null) {
            throw new RdsException(MysqlErrorCode.GDN_INSTANCE_NOT_FOUND.toArray());
        }
        return gdnInstance;
    }

    public List<InstanceMember> getGdnMembers(String requestId, String gdnInstanceName, String groupId) throws ApiException {
        InstanceMemberListResult memberListResult = gdnMetaService.getClient().listMembers(requestId, gdnInstanceName, groupId);
        if (null == memberListResult || CollectionUtils.isEmpty(memberListResult.getItems())) {
            return new ArrayList<>();
        }
        return memberListResult.getItems();
    }

    public GdnInstance createGdnInstance(String requestId, String gdnInstanceName, String bid, String uid, String dbType, String dbVersion, GdnInstance.BizTypeEnum bizType, String desc) throws ApiException, RdsException {

        GdnInstance gdnInstance = new GdnInstance();
        gdnInstance.setBid(bid);
        gdnInstance.setAliUid(uid);
        gdnInstance.setInsName(gdnInstanceName);
        gdnInstance.setService(dbType);
        gdnInstance.setServiceVersion(dbVersion);
        gdnInstance.setBizType(bizType);
        gdnInstance.setDescription(desc);
        gdnInstance.setStatus("active");
        gdnInstance = gdnMetaService.getClient().createGdnInstance(requestId, gdnInstance);
        return gdnInstance;
    }

    public InstanceMember getPrimaryMember(String requestId, String dbInstanceName, String gdnInstanceName) throws RdsException, ApiException {
        getAndCheckGdnInstance(requestId, gdnInstanceName);
        List<InstanceMember>  members = getGdnMembers(requestId, gdnInstanceName, dbInstanceName);

        if (CollectionUtils.isEmpty(members)) {
            throw new RdsException(MysqlErrorCode.GDN_MEMBERS_IS_EMPTY.toArray());
        }
        InstanceMember primaryMember = members.stream()
                .filter(m -> ReplicaSet.InsTypeEnum.MAIN.toString().equals(m.getRole()))
                .collect(Collectors.toList()).get(0);
        return primaryMember;
    }
}
