package com.aliyun.dba.base.service;

import com.aliyun.dts20200101.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class DtsClientService {
    private static final String COMMON_ENDPOINT = "dts.regionId.aliyuncs.com";
    private static final String PLACEHOLDER = "regionId";
    private static final String TEST_REGION_ID = "cn-hangzhou";
    @Resource
    private SLRService slrService;

    public Client getDtsClient(String regionId, String aliUid) throws Exception {
        AssumeRoleWithServiceIdentityResponse slrResponse = slrService.getAssumeAuthInfoForSLR(regionId, aliUid);
        if (slrResponse == null) {
            log.error("AssumeRoleWithServiceIdentityResponse is null. Please check.");
            throw new Exception("AssumeRoleWithServiceIdentityResponse is null.");
        }
        String accessKeyId = slrResponse.getCredentials().getAccessKeyId();
        String accessKeySecret = slrResponse.getCredentials().getAccessKeySecret();
        String securityToken = slrResponse.getCredentials().getSecurityToken();

        String endpoint = COMMON_ENDPOINT.replace(PLACEHOLDER, regionId);
        // 预发用这个
        // String endpoint = COMMON_ENDPOINT.replaceAll(PLACEHOLDER, TEST_REGION_ID);
        Config config = new Config();
        config.setRegionId(regionId);
        config.setAccessKeyId(accessKeyId);
        config.setAccessKeySecret(accessKeySecret);
        config.setSecurityToken(securityToken);
        config.setEndpoint(endpoint);
        return new Client(config);
    }
}