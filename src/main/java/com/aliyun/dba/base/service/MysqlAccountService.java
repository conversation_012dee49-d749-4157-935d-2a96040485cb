package com.aliyun.dba.base.service;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.property.RdsException;

import java.io.IOException;
import java.util.Map;

public interface MysqlAccountService {
    Map<String, Object> describeAccountListByDboss(CustInstanceDO custins1, Map<String, String> params) throws RdsException, IOException;

    Map<String, Object> describeAccountList(Map<String, String> params) throws RdsException;
}
