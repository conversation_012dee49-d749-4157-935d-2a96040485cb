package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.DEDICATED_HOST_GOURP_TYPE;

@Service("mysqlEngineCheckService")
public class MysqlEngineCheckServiceImpl implements MysqlEngineCheckService {

    private static final LogAgent logger = LogFactory.getLogAgent(MysqlEngineCheckServiceImpl.class);

    @Autowired
    private CheckService checkService;

    @Autowired
    private CustinsParamService custinsParamService;

    @Resource
    private ResourceService resourceService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private ClusterService clusterService;

    @Override
    public boolean checkCloneBeforeUpgrade(Map<String, String> params, Integer custinsId) throws RdsException {

        Date restoreTime = checkService.getAndCheckTimeByDateStr(
                getParameterValue(params, ParamConstants.RESTORE_TIME),
                DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME);
        // 5.6 升级到5.7 后，在下发实例克隆到时间点，克隆的时间不能晚于升级成功时间.
        long times = restoreTime.getTime();
        List<Integer> custinsIdList = new ArrayList<>();
        List<String> paramList = new ArrayList<>();
        custinsIdList.add(custinsId);
        paramList.add("upgrade_time");
        List<CustinsParamDO> cinsParamList = custinsParamService.getCustinsParamsByCustinsIds(custinsIdList, paramList);
        if (cinsParamList != null && cinsParamList.size() == 1) {
            CustinsParamDO cinsParamDO = cinsParamList.get(0);
            long upgradeTimeMillis = Long.parseLong(cinsParamDO.getValue());
            return times < upgradeTimeMillis;
//          throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }
        return true;

    }

    /**
     * 校验是否能升级到XDB
     *
     * @param custins
     * @return
     */
    @Override
    public boolean checkCanUpgradeToXDB(CustInstanceDO custins) throws RdsException {
        if (CustinsSupport.isProxy(custins.getConnType())) {
            //xdb不支持proxy链路
            logger.warn("custins's conntype is proxy, cannot upgrade to xdb");
            return false;
        }
        int readCount = custinsService.countReadCustInstanceByPrimaryCustinsId(custins.getId());
        if (readCount > 0) {
            //暂时不支持有只读实例
            logger.warn("custins has readins, cannot upgrade to xdb");
            return false;
        }
        if (custinsService.checkHaveMaxscaleService(custins.getId()) != null) {
            //暂时不支持有maxscale
            logger.warn("custins has maxscale, cannot upgrade to xdb");
            return false;
        }
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), "minor_version");
        if (custinsParamDO == null || StringUtils.isBlank(custinsParamDO.getValue())) {
            logger.warn("cannot find minor_version from custins params");
            return false;
        }
        String minorVersion = custinsParamDO.getValue();
        List<String> resourceList = resourceService.getResourceRealValueList("support_upgrade_xdb_max_version_common");
        if (resourceList == null || resourceList.isEmpty()) {
            logger.warn("cannot find support_upgrade_xdb_max_version from resource");
            return false;
        }
        String supportVersions = resourceList.get(0);
        String prefix = StringUtils.substringBefore(minorVersion, "_");
        String supportVersion = JSONObject.parseObject(supportVersions).getString(prefix);
        if (NumberUtils.toInt(StringUtils.substringAfter(minorVersion, "_")) >
                NumberUtils.toInt(StringUtils.substringAfter(supportVersion, "_"))) {
            logger.error("cannot upgrade, minor version is {}, support version is {}", minorVersion, supportVersion);
            return false;
        }


        if (CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(custins.getDbVersion())) {
            CustinsParamDO custInsParamDO = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
            String paramGroupId = custInsParamDO == null ? "" : custInsParamDO.getValue();
            boolean isXengine = "xengine".equalsIgnoreCase(SysParamGroupHelper.getDBStorageEngine(paramGroupId));
            if (isXengine) {
                logger.warn("upgrade from xengine to xdb is not supported");
                return false;
            }
        }


        return true;
    }

    /**
     * 校验集群是否为大客户专享集群
     *
     * @param custins
     * @return
     */
    @Override
    public boolean checkUserClusterCustins(CustInstanceDO custins) {
        ClustersDO clustersDO = clusterService.getClusterByClusterName(custins.getClusterName());
        return clustersDO != null && DEDICATED_HOST_GOURP_TYPE.equals(clustersDO.getType());
    }

    /**
     * MySQL8.0带MaxScale的内核必须>=1.13.33才支持升级
     * MySQL8.0升级至20230930（8.0.34）及以后,代理内核必须>=1.14.5或者20231123
     * 必须先升级MaxScale的内核版本
     * @param custins
     * @param targetReleaseDate
     * @return
     */
    @Override
    public boolean checkCanUpgradeMinorVersionWithMaxScale(CustInstanceDO custins, String targetReleaseDate) {
        if (custins == null) {
            return true; //为空不用校验
        }
        if (StringUtils.equals(custins.getBizType(), ReplicaSet.BizTypeEnum.ALIGROUP.getValue())) {
            return true;  //集团不用校验
        }
        if (!custins.isMysql80()) {
            return true; //非8.0不用校验
        }
        if (custins.isReadOrBackup()) {
            // 如果是只读转为主实例
            custins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            if (custins == null) {
                return true;
            }
        }
        if (CHARACTER_TYPE_PHYSICAL.equals(custins.getCharacterType())) {
            custins = custinsService.getCustInstanceByCustinsId(custins.getParentId());
            if (custins == null) {
                return true;
            }
        }
        List<CustinsServiceDO> custinsServiceDOS = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
        if (CollectionUtils.isEmpty(custinsServiceDOS)) {
            return true;  //不带maxscale不用校验
        }
        Integer maxScaleCustinsId = LangUtil.getInteger(custinsServiceDOS.get(0).getServiceId());
        CustInstanceDO maxscale = custinsService.getCustInstanceByCustinsId(maxScaleCustinsId);
        if (maxscale == null || maxscale.isDeleting()) {
            return true;  //maxscale正在删除不用校验
        }
        CustinsParamDO custinsMinorVersion = custinsParamService.getCustinsParam(maxScaleCustinsId, "minor_version");
        if (custinsMinorVersion != null && StringUtils.isNotBlank(custinsMinorVersion.getValue())) {
            String maxScaleMinorVersion = custinsMinorVersion.getValue();
            // 版本规则为：老架构（maxscale_service_1.13.33）、新架构（Maxscale_MySQL_2.2.12_20230921、Maxscale_MySQL_Serverless_2.2.12_20231026）
            if (StringUtils.equals(maxscale.getDbVersion(), "3.5")) {
                String maxscaleReleaseDate = StringUtils.substringAfterLast(maxScaleMinorVersion, "_");
                if (maxscaleReleaseDate.length() != 8) {
                    return true;  //不符合版本规则，不校验
                }
                if (targetReleaseDate != null && "20230930".compareTo(targetReleaseDate) <= 0 && "20231127".compareTo(maxscaleReleaseDate) > 0) {
                    //https://aone.alibaba-inc.com/v2/project/1190539/req/53314908
                    //MYSQL8.0升级到20230930（8.0.34）及以后版本，需确保maxscale新架构在20231127及其以上版本
                    logger.error("maxscale's version is {}, do not allow upgrade", maxScaleMinorVersion);
                    return false;
                }
            } else {
                String[] vers = StringUtils.split(maxScaleMinorVersion, "_");
                if (vers.length != 3) {
                    return true;   //不符合版本规则，不校验
                }
                String[] vvv = StringUtils.split(vers[2], ".");
                if (vvv.length != 3) {
                    return true; //不符合版本规则，不校验
                }
                Long checkVersion = LangUtil.getLong(StringUtils.join(vvv));
                if (checkVersion == null) {
                    return true; //不符合版本规则，不校验
                }
                if ("1.13.33".compareTo(vers[2]) > 0) {
                    //https://help.aliyun.com/document_detail/186199.html?spm=a2c4g.138705.0.0.30f62a03MyoGt6
                    //小于1.13.33的8.0版本都不允许做升级，否则有字符集问题
                    logger.error("maxscale's version is {}, do not allow upgrade", maxScaleMinorVersion);
                    return false;
                }
                if (targetReleaseDate != null && "20230930".compareTo(targetReleaseDate) <= 0 && "1.14.5".compareTo(vers[2]) > 0) {
                    //https://aone.alibaba-inc.com/v2/project/1190539/req/53314908
                    //MYSQL8.0升级到20230930（8.0.34）及以后版本，需确保maxscale老架构在1.14.5及其以上版本
                    logger.error("maxscale's version is {}, do not allow upgrade", maxScaleMinorVersion);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 代理内核必须>=1.9.23或者20200221
     * @param custins
     * @return
     */
    public boolean checkMinorVersionWithMaxScale(CustInstanceDO custins) {
        if (custins == null) {
            return true; //为空不用校验
        }
        List<CustinsServiceDO> custinsServiceDOS = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
        if (CollectionUtils.isEmpty(custinsServiceDOS)) {
            return true;  //不带maxscale不用校验
        }
        Integer maxScaleCustinsId = LangUtil.getInteger(custinsServiceDOS.get(0).getServiceId());
        CustInstanceDO maxscale = custinsService.getCustInstanceByCustinsId(maxScaleCustinsId);
        if (maxscale == null || maxscale.isDeleting()) {
            return true;  //maxscale正在删除不用校验
        }
        CustinsParamDO custinsMinorVersion = custinsParamService.getCustinsParam(maxScaleCustinsId, "minor_version");
        if (custinsMinorVersion != null && StringUtils.isNotBlank(custinsMinorVersion.getValue())) {
            String maxScaleMinorVersion = custinsMinorVersion.getValue();
            // 代理内核小版本必须大于等于1.9.23或20200221
            if (StringUtils.equals(maxscale.getDbVersion(), "3.5")) {
                String maxscaleReleaseDate = StringUtils.substringAfterLast(maxScaleMinorVersion, "_");
                if (maxscaleReleaseDate.length() != 8) {
                    return true;  //不符合版本规则，不校验
                }
                if ("20200221".compareTo(maxscaleReleaseDate) > 0) {
                    logger.error("maxscale's version is {}, do not ssl force encryption", maxScaleMinorVersion);
                    return false;
                }
            } else {
                String[] vers = StringUtils.split(maxScaleMinorVersion, "_");
                if (vers.length != 3) {
                    return true;   //不符合版本规则，不校验
                }
                String[] vvv = StringUtils.split(vers[2], ".");
                if (vvv.length != 3) {
                    return true; //不符合版本规则，不校验
                }
                Long checkVersion = LangUtil.getLong(StringUtils.join(vvv));
                if (checkVersion == null) {
                    return true; //不符合版本规则，不校验
                }
                if ("1.9.23".compareTo(vers[2]) > 0) {
                    //小于1.9.23的版本都不允许做强制ssl
                    logger.error("maxscale's version is {}, do not ssl force encryption", maxScaleMinorVersion);
                    return false;
                }
            }
        }
        return true;
    }

}
