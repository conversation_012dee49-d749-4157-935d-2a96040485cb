package com.aliyun.dba.base.service;

import com.aliyun.dba.base.dataobject.ApplyResourceDO;
import com.aliyun.dba.base.idao.ApplyResourceIDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class ApplyResourceService {

    @Autowired
    private ApplyResourceIDao applyResourceIDao;

    //返回主键编号
    public Long addApplyResource(ApplyResourceDO applyResourceDO){
        applyResourceIDao.addApplyResource(applyResourceDO);
        return applyResourceDO.getId();
    }

    public List<ApplyResourceDO> queryApplyResourceRecords(Map<String, Object> queryCondition){
        return applyResourceIDao.queryApplyResourceRecords(queryCondition);
    }

    public Integer queryApplyResourceRecordCount(Map<String, Object> queryCondition){
        return applyResourceIDao.queryApplyResourceRecordCount(queryCondition);
    }

    public void cancelApplyResource(Integer userId, Long applyResourceId){
        String description ="用户取消";
        applyResourceIDao.cancelApplyResource(userId, applyResourceId, description);
    }
}

