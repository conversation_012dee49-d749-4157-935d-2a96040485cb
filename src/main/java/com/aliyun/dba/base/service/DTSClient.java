package com.aliyun.dba.base.service;


import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccessKeyListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.poddefault.action.support.GAD.Aes;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dts20200101.Client;

import com.aliyun.teaopenapi.models.Config;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.RDS_ACCOUNT;


@Slf4j
@Component
public class DTSClient {

    @Resource
    private DBaasMetaService dbaasMetaService;

    @Resource
    private DTSClient dtsClient;

    @Resource
    private ResourceService resourceService;

    // 是否使用测试账号
    private static final String ONE_STOP_USE_TEST_ACCOUNT = "ONE_STOP_USE_TEST_ACCOUNT";

    private static final String COMMON_ENDPOINT = "dts.regionId.aliyuncs.com";

    private static final String PLACEHOLDER = "regionId";


    // 测试账号
    private static final String TEST_ACCOUNT = "<EMAIL>";
    private static final String TEST_ACCESS_KEY = "";
    private static final String TEST_ACCESS_SECRET = "";
    private static final String TEST_REGION_ID = "cn-hangzhou";

    private boolean isTestAccount() {
        ResourceDO resource = resourceService.getResourceByResKey(ONE_STOP_USE_TEST_ACCOUNT);
        if (ObjectUtils.isEmpty(resource)) {
            return false;
        }
        String result = resource.getRealValue();
        return StringUtils.isNotEmpty(result) && StringUtils.equalsIgnoreCase("true", result);
    }

    // 正式环境使用RDS大账号
    public Client getDtsPrdClient(String userName, String regionId) throws Exception {
        List<String> accessKey = getAccessKeyIdAndSecret(userName);
        String endpoint = COMMON_ENDPOINT.replaceAll(PLACEHOLDER, regionId);
        Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(accessKey.get(0))
                .setAccessKeySecret(accessKey.get(1));
        // Endpoint 请参考 https://api.aliyun.com/product/Dts
        config.setEndpoint(endpoint);
        config.setConnectTimeout(5000);
        config.setReadTimeout(30000);
        return new Client(config);
    }

    // 预发环境使用测试账号
    public Client getDtsPreClient() throws Exception {

        // 使用测试账号
        String endpoint = COMMON_ENDPOINT.replaceAll(PLACEHOLDER, TEST_REGION_ID);

        Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(TEST_ACCESS_KEY)
                .setAccessKeySecret(TEST_ACCESS_SECRET);
        // Endpoint 请参考 https://api.aliyun.com/product/Dts
        config.setEndpoint(endpoint);
        config.setConnectTimeout(5000);
        config.setReadTimeout(30000);
        return new Client(config);
    }


    // 预发环境使用测试账号
    public Client getDtsClient(String regionId) throws Exception {
        Client client;
        if (isTestAccount()) {
            client = dtsClient.getDtsPreClient();
        } else {
            client = dtsClient.getDtsPrdClient(RDS_ACCOUNT, regionId);
        }
        return client;
    }

    private List<String> getAccessKeyIdAndSecret(String userName) throws Exception {
        AccessKeyListResult accessKeyListResult = null;
        try {
            accessKeyListResult = dbaasMetaService.getDefaultClient().listAccessKeys(RequestSession.getRequestId(), userName, null);
            if (CollectionUtils.isEmpty(accessKeyListResult.getItems())) {
                throw new Exception("Cannot get accessKey from metadb, ecs userName is " + userName);
            }
            String accessKeyId = Aes.decryptAccountPasswd(accessKeyListResult.getItems().get(0).getAccessKeyIdEncrypted(), Aes.PWD_CRYPTKEY);
            String accessKeySecret = Aes.decryptAccountPasswd(accessKeyListResult.getItems().get(0).getAccessKeySecretEncrypted(), Aes.PWD_CRYPTKEY);
            return Arrays.asList(accessKeyId, accessKeySecret);
        } catch (ApiException ex) {
            log.error("get accessKey from metaDB failed. ex : {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            log.error("decrypt failed. ex : {}", ex.getMessage());
            throw ex;
        }
    }
}
