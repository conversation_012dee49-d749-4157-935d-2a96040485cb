package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.MysqlMajorVersionCheckService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


@Component
public class UpgradeMajorVersionPreCheckPhysical57To80 extends UpgradeMajorVersionPreCheck57To80 {

    @Resource
    private InstanceService instanceService;
    @Autowired
    private MinorVersionService minorVersionService;
    @Resource
    private MysqlMajorVersionCheckService mysqlMajorVersionCheckService;
    @Override
    public void upgradePreCheck(CustInstanceDO custins, ReplicaSet replicaSetMeta, String dbVersion, String targetEngineVersion, String requestId) throws Exception {
        super.upgradePreCheck(custins, replicaSetMeta, dbVersion, targetEngineVersion, requestId);
        checkInstanceLink(custins);
        checkMaxscaleKernel(custins);
    }

    //校验实例是否为lvs链路
    private void checkInstanceLink(CustInstanceDO custins) throws RdsException {
        if (custins.isMysql57() && !custins.isLvs() && !custins.isDns()) {
            throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
        }
    }

    //校验maxscale内核小版本是否支持
    public void checkMaxscaleKernel(CustInstanceDO custins) throws RdsException {
        Integer kindCode = custins.getKindCode();
        InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        String category = instanceLevelDO.getCategory();
        List<String> releaseDateList = minorVersionService.getReleaseDateListByTag(
                custins.getDbType(),
                CustinsSupport.DB_VERSION_MYSQL_80,
                kindCode,
                category,
                MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_RPM
        );
        String targetReleaseDate = releaseDateList.isEmpty() ? null : releaseDateList.get(0);
        if (!mysqlMajorVersionCheckService.checkCanUpgradeMajorVersionWithMaxScale(custins, targetReleaseDate)) {
            throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "MaxscaleMinorVersionNotSupport", "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first."});
        }
    }
}
