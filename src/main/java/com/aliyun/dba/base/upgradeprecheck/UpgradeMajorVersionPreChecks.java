package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NC;

@Component
public class UpgradeMajorVersionPreChecks {
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;
    @Resource
    private CustinsService custinsService;
    @Autowired
    private MinorVersionService minorVersionService;

    //校验实例是否存在
    public void checkInstanceExistence(CustInstanceDO custins) throws RdsException {
        if (custins.isDeleting()) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
    }

    //校验实例状态是否为运行中
    public void checkInstanceStatus(CustInstanceDO custins) throws RdsException {
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
    }

    //校验实例是否为主实例
    public void checkInstanceType(ReplicaSet replicaSetMeta) throws RdsException {
        if (!ReplicaSet.InsTypeEnum.MAIN.equals(replicaSetMeta.getInsType())){
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
    }

    //校验是否为锁定状态
    public void checkInstanceLockedState(ReplicaSet replicaSetMeta) throws RdsException {
        if (ReplicaSet.LockModeEnum.NOLOCK != replicaSetMeta.getLockMode()){
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }
    }

    //校验当前实例是否开启tde
    public void checkInstanceTDEState(CustInstanceDO custins) throws RdsException {
        List<String> params = new ArrayList<String>(1);
        List<CustinsParamDO> resultList;
        params.add(CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_TDE);
        resultList = custinsParamService.getCustinsParams(custins.getId(), params);
        String tdeStatus = CustinsParamSupport.CUSTINS_PARAM_VALUE_TDE_ENABLED;
        if (!resultList.isEmpty() && resultList.get(0).getValue().equals(tdeStatus)){
            throw new RdsException(ErrorCode.TDESTATUS_ERROR_CONFIGED);
        }
    }

    //校验当前是否存在大版本校验任务
    public void checkUpgradeCheckTask(CustInstanceDO custins) throws RdsException {
        if (KIND_CODE_NC.equals(custins.getKindCode())){
            Map<String, Object> condition = new HashMap<>();
            condition.put("custinsId", custins.getId());
            condition.put("action", "UpgradeDBMajorVersionPreCheck");
            condition.put("taskKey", "upgrade_major_version_precheck");
            // 0等待  1运行  7暂停 8中断
            int[] status = {0, 1, 7, 8};
            condition.put("status", status);
            Integer count = taskService.countTaskQueueByCondition(condition);
            if (count > 0) {
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }
        }
    }

    //校验当前是否存在大版本升级任务
    public void checkUpgradeTask(CustInstanceDO custins) throws RdsException {
        if (KIND_CODE_NC.equals(custins.getKindCode())) {
            Map<String, Object> condition = new HashMap<>();
            condition.put("custinsId", custins.getId());
            condition.put("action", "UpgradeDBMajorVersion");
            condition.put("taskKey", "upgrade_major_version");
            // 0等待  1运行  7暂停 8中断
            int[] status = {0, 1, 7, 8};
            condition.put("status", status);
            Integer count = taskService.countTaskQueueByCondition(condition);
            if (count > 0) {
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }
        }
    }

    //只支持高可用/基础版升级大版本, 不支持serverless、xdb
    public void checkSourceCategory(ReplicaSet replicaSetMeta) throws Exception {
        if (!InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(replicaSetMeta.getCategory()) &&
                !InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSetMeta.getCategory())&&
                !InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
            throw new RdsException(ErrorCode.INVALID_SOURCE_CATEGORY);
        }
    }

    //校验当前实例规格是否在目标实例规格存在
    public void checkTargetInstanceLevel(ReplicaSet replicaSetMeta, String targetEngineVersion) throws RdsException {
        int custinsId = Objects.requireNonNull(replicaSetMeta.getId()).intValue();
        boolean levelSupport = custinsService.checkInstanceLevelSupportUpgradeTargetMajorVersion(custinsId, targetEngineVersion);
        if (!levelSupport) {
            throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
        }
    }
}
