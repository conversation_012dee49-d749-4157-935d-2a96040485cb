package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import org.springframework.stereotype.Component;

@Component
public class UpgradeMajorVersionPreCheck57To80 implements UpgradeMajorVersionPreCheckStrategy {

    @Override
    public void upgradePreCheck(CustInstanceDO custins, ReplicaSet replicaSetMeta, String dbVersion, String targetEngineVersion, String requestId) throws Exception {
        //5.7升级8.0云盘和本地盘公共校验
    }

}
