package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.physical.action.service.MysqlMajorVersionCheckService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Objects;

import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;


@Component
public class UpgradeMajorVersionPreCheckPodDefault57To80 extends UpgradeMajorVersionPreCheck57To80 {
    @Autowired
    protected ClusterService clusterService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MysqlMajorVersionCheckService mysqlMajorVersionCheckService;


    @Override
    public void upgradePreCheck(CustInstanceDO custins, ReplicaSet replicaSetMeta, String dbVersion, String targetEngineVersion, String requestId) throws Exception {
        super.upgradePreCheck(custins, replicaSetMeta, dbVersion, targetEngineVersion, requestId);
        checkStorageType(replicaSetMeta, requestId);
//        checkReadInstanceExists(replicaSetMeta, requestId);
        checkInstanceLink(custins);
        checkInstanceBizType(replicaSetMeta);
        checkInstanceClusterType(custins,replicaSetMeta);
        checkUpgradeCheckTask(replicaSetMeta, requestId);
        checkUpgradeTask(replicaSetMeta, requestId);
        checkMaxscaleKernel(custins, replicaSetMeta, targetEngineVersion, requestId, dbVersion);
    }

    //校验是否存在只读实例，云盘暂不支持
    public void checkReadInstanceExists(ReplicaSet replicaSetMeta, String requestId) throws RdsException, ApiException {
        ReplicaSetListResult replicaSetListResult = replicaSetService.getReplicaSetSubIns(requestId, replicaSetMeta.getName(), ReplicaSet.InsTypeEnum.READONLY.getValue());
        if (replicaSetListResult != null && Objects.requireNonNull(replicaSetListResult.getItems()).size() > 0) {
            throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_READ_INSTANCE);
        }
    }

    //校验云盘实例是否为lvs链路
    private void checkInstanceLink(CustInstanceDO custins) throws RdsException {
        if (custins.isMysql57() && !custins.isLvs()) {
            throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
        }
    }

    //校验是否为公有云
    private void checkInstanceBizType(ReplicaSet replicaSetMeta) throws RdsException {
        if (!PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
            throw new RdsException(ErrorCode.INVALID_BIZ_TYPE);
        }
    }

    //校验是否为政务云
    private void checkInstanceClusterType(CustInstanceDO custins, ReplicaSet replicaSetMeta) throws RdsException {
        int custinsId = Objects.requireNonNull(replicaSetMeta.getId()).intValue();
        ClustersDO cluster = clusterService.getClusterByCustinsId((long) custinsId);
        if (custins.getClusterName().contains("GOV") || cluster.getLocation().contains("gov")) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }
    }

    //校验当前是否存在大版本校验任务
    public void checkUpgradeCheckTask(ReplicaSet replicaSetMeta, String requestId) throws Exception {
        String taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_PRECHECK;
        boolean taskExist = workFlowService.isTaskExist(requestId, replicaSetMeta.getName(), taskKey);
        if (taskExist) {
            throw new RdsException(ErrorCode.TASK_HAS_EXIST);
        }
    }

    //校验当前是否存在大版本升级任务
    public void checkUpgradeTask(ReplicaSet replicaSetMeta, String requestId) throws Exception {
        String taskKey = null;
        if(InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(replicaSetMeta.getCategory())){
            taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_FOR_STANDARD;
        }else if(InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSetMeta.getCategory())){
            taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_FOR_BASIC;
        }else if(InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(replicaSetMeta.getCategory())){
            taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_FOR_CLUSTER;
        }
        boolean taskExist = workFlowService.isTaskExist(requestId, replicaSetMeta.getName(), taskKey);
        if (taskExist) {
            throw new RdsException(ErrorCode.TASK_HAS_EXIST);
        }
    }

    //校验当前实例是否为SSD云盘
    public void checkStorageType(ReplicaSet replicaSetMeta, String requestId) throws RdsException, ApiException {
        String diskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
        if (Replica.StorageTypeEnum.CLOUD_SSD.toString().equals(diskType)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLOUD_SSD);
        }
    }

    //校验maxscale内核小版本是否支持
    public void checkMaxscaleKernel(CustInstanceDO custins, ReplicaSet replicaSetMeta, String targetEngineVersion, String requestId, String dbVersion) throws RdsException, ApiException {
        String clusterName = replicaSetMeta.getResourceGroupName();
        String dbType = replicaSetMeta.getService();
        String classCode = replicaSetMeta.getClassCode();
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
        boolean isArm = PodCommonSupport.isArm(instanceLevel);
        boolean isDhg = mysqlParamSupport.isDHGCluster(clusterName);
        String dbEngine = "MySQL";
        String diskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
        String targetServiceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                null,
                replicaSetMeta.getBizType(),
                replicaSetMeta.getService(),
                targetEngineVersion,
                dbEngine,
                KIND_CODE_NEW_ARCH,
                instanceLevel,
                diskType,
                isDhg,
                isArm,
                null);
        if (StringUtils.isEmpty(targetServiceSpecTag)) {
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }
        String targetReleaseDate = minorVersionServiceHelper.parseServiceSpecReleaseDate(targetServiceSpecTag);
        if (!mysqlMajorVersionCheckService.checkCanUpgradeMajorVersionWithMaxScale(custins, targetReleaseDate)) {
            throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "MaxscaleMinorVersionNotSupport", "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first."});
        }
    }

}
