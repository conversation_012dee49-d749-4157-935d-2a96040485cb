package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.stereotype.Component;

@Component
public class UpgradeMajorVersionPreCheckStrategyFactory {
    public static UpgradeMajorVersionPreCheckStrategy getStrategy(String dbVersion, String targetEngineVersion, ReplicaSet replicaSetMeta) throws RdsException {
        //校验当前升级版本是否正确
        if (CustinsSupport.DB_VERSION_MYSQL_56.equals(dbVersion) && CustinsSupport.DB_VERSION_MYSQL_57.equals(targetEngineVersion)) {
            return SpringContextUtil.getApplicationContext().getBean(UpgradeMajorVersionPreCheck56To57.class);
        } else if (CustinsSupport.DB_VERSION_MYSQL_57.equals(dbVersion) && CustinsSupport.DB_VERSION_MYSQL_80.equals(targetEngineVersion)) {
            if (CustinsSupport.KIND_CODE_NEW_ARCH.equals(replicaSetMeta.getKindCode()) && InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            } else if(CustinsSupport.KIND_CODE_NEW_ARCH.equals(replicaSetMeta.getKindCode()) && (InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(replicaSetMeta.getCategory())
                    || InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(replicaSetMeta.getCategory()))){
                return SpringContextUtil.getApplicationContext().getBean(UpgradeMajorVersionPreCheckPodDefault57To80.class);
            }else{
                return SpringContextUtil.getApplicationContext().getBean(UpgradeMajorVersionPreCheckPhysical57To80.class);
            }
        } else {
            throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
        }
    }
}
