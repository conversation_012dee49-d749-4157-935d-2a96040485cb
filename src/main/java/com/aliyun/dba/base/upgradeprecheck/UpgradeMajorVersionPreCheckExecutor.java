package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UpgradeMajorVersionPreCheckExecutor {
    @Autowired
    private UpgradeMajorVersionPreChecks upgradePreChecks;

    public void execute(CustInstanceDO custins, ReplicaSet replicaSetMeta, String targetEngineVersion, String requestId) throws Exception {
        String dbVersion = custins.getDbVersion();
        UpgradeMajorVersionPreCheckStrategy strategy = UpgradeMajorVersionPreCheckStrategyFactory.getStrategy(dbVersion, targetEngineVersion, replicaSetMeta);

        upgradePreChecks.checkInstanceExistence(custins);
        upgradePreChecks.checkInstanceStatus(custins);
        upgradePreChecks.checkInstanceType(replicaSetMeta);
        upgradePreChecks.checkInstanceLockedState(replicaSetMeta);
        upgradePreChecks.checkInstanceTDEState(custins);
        upgradePreChecks.checkUpgradeCheckTask(custins);
        upgradePreChecks.checkUpgradeTask(custins);
        upgradePreChecks.checkSourceCategory(replicaSetMeta);
        upgradePreChecks.checkTargetInstanceLevel(replicaSetMeta, targetEngineVersion);
        strategy.upgradePreCheck(custins, replicaSetMeta, dbVersion, targetEngineVersion, requestId);
    }
}
