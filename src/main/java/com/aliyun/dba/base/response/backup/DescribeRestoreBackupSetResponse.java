package com.aliyun.dba.base.response.backup;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.reflect.TypeToken;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.List;

@Data
public class DescribeRestoreBackupSetResponse {
    private Boolean restoreTimeValid;
    private String backupSetInfo;
    private BackupSetInfo backupSetInfoObj;

    public BackupSetInfo getBackupSetInfo() {
        if (backupSetInfoObj == null) {
            backupSetInfoObj = JSONObject.parseObject(backupSetInfo, BackupSetInfo.class);
        }
        return backupSetInfoObj;
    }

    @Data
    public static class BackupSetInfo {
        private String backupId;
        private String backupMethod;
        private String backupMode;
        private String backupType;
        private String backupScale;
        private Long backupSize;
        private String backupStartTime;
        private String backupEndTime;
        private Long consistentTime;
        private String backupStatus;
        private Integer isAvail;
        private String instanceName;
        @JSONField(name = "instanceId")
        private Integer custinsId;
        private Long hostInstanceId;
        private String backupLocation;
        private String backupDownloadURL;
        private String backupIntranetDownloadURL;
        private Integer locks;
        private String storageEntityId;
        private String InstanceKindCode;
        private String extraInfo;
        private ExtraInfo extraInfoObj;

        public ExtraInfo getExtraInfo() {
            if (extraInfoObj == null) {
                extraInfoObj = JSONObject.parseObject(extraInfo, ExtraInfo.class);
            }
            return extraInfoObj;
        }
    }

    @Data
    public static class ExtraInfo {
        private String dSnapshotId;
        private String slaveStatus;
        private SlaveStatus slaveStatusObj;
        private String coldDataFsId;
        private String coldDataSnapshotId;
        private String SNAPSHOT_ID;
        private String MINOR_VERSION;
        private String tdeEncryptionKeyId;
        private boolean tdeEnabled;
        private String clsKeyMode;
        private String clsEncryptionKeyId;
        private String variables;

        public SlaveStatus getSlaveStatus() {
            if (slaveStatusObj == null) {
                slaveStatusObj = JSONObject.parseObject(slaveStatus, SlaveStatus.class);
            }
            return slaveStatusObj;
        }
    }

    @Data
    public static class SlaveStatus {
        private String variables;
        private Map<String, String> variablesObj;
        private String minorVersion;
        private String minor_version;
        private String SNAPSHOT_ID;
        private String insLevelExtraInfo;

        public String getMinorVersion() {
            return minor_version;
        }

        public Map<String, String> getVariables() {
            if (variablesObj == null) {
                Type mapType = new TypeToken<Map<String, String>>(){}.getType();
                variablesObj = JSONObject.parseObject(variables, mapType);
            }
            return variablesObj;
        }
    }
}
