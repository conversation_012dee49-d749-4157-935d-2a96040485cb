package com.aliyun.dba.base.response.backup;

import lombok.Data;

@Data
public class DescribeRestoreLiveMountBackupResponse {

    private Boolean restoreTimeValid;
    private String restoreMessage;
    private BackupSetInfo backupSetInfo;

    @Data
    public static class BackupSetInfo {
        private String backupId;
        private String backupName;
        private String engine;
        private String engineVersion;
        private String backupMethod;
        private String backupMode;
        private String backupType;
        private String backupScale;
        private Long backupSize;
        private String backupStartTime;
        private String backupEndTime;
        private String backupStatus;
        private Boolean isAvail;
        private String backupLocation;
        private ExtraInfo extraInfo;  // 这里是对象还是字符串

        @Data
        public static class ExtraInfo {
            private String snapshotId;
            private String diskId;
        }

    }
}
