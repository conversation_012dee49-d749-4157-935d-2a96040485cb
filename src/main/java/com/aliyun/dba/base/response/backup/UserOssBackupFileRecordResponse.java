package com.aliyun.dba.base.response.backup;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/10/29 23:43
 */
@Data
public class UserOssBackupFileRecordResponse {
    private Integer id;
    private String instanceName;
    private String uid;
    private String bid;
    private String requestId;
    private String ossUrl;
    private Integer ossFileSize;
    private String ossBucket;
    private String ossFilePath;
    private String ossFileName;
    private String ossFileMetaData;
    private String taskId;
    private String dbInstanceName;
    private String backupSetId;
    private String status;
    private String engineVersion;
    private String engine;
    private Date gmtModified;
    private Date gmtCreated;
    private Date gmtCheckend;
    private Integer custinsId;
    private String reason;
    private String binlogInfo;
    private String retention;
    private String restoreSize;
    private String comment;
    private String zoneId;
}
