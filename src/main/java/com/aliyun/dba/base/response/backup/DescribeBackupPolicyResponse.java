package com.aliyun.dba.base.response.backup;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/*
* DBS文档：
* https://alidocs.dingtalk.com/i/nodes/YMyQA2dXW7gYo6MzcAbzjm54WzlwrZgb?cid=681751320%3A2849441530&corpId=dingd8e1123006514592&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&utm_scene=person_space&utm_source=im# 「统一策略接口」
* */
@NoArgsConstructor
@Data
public class DescribeBackupPolicyResponse {
    private String instanceName;
    private String preferredBackupTime;
    private String backupRetentionPolicyOnClusterDeletion;
    private String backupMethod;
    private String category;
    private String backupPolicyType;
    private List<DumpSchedulePolicyResponse> sparseDataPolicies;
    private List<LogPolicyResponse> sparseLogPolicies;



    @NoArgsConstructor
    @Data
    public static class DumpSchedulePolicyResponse {
        private String strategyId;
        private String filterKey;
        private String filterValue;
        private String dumpAction;
        private String retentionType;
        private String retentionValue;
        private String srcType;
        private String srcRegion;
        private String destType;
        private String destRegion;
        private String bakType;
        private String autoCreated;
    }

    @NoArgsConstructor
    @Data
    public static class LogPolicyResponse {
        private String strategyId;
        private Boolean enableLogBackup;
        private String logRetentionType;
        private String logRetentionValue;
        private String srcType;
        private String srcRegion;
        private String destType;
        private String destRegion;
        private String filterValue;
    }
}
