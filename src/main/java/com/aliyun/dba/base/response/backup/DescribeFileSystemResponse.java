package com.aliyun.dba.base.response.backup;

import lombok.Data;

@Data
public class DescribeFileSystemResponse {
    private String fsId;
    private Long dataStorageSize;
    private Long snapshotSize;
    private String ossBucket;
    private String ossEndpoint;
    private String Status;
    private MountInfo mountTarget;

    @Data
    public static class MountInfo {
        private String instanceName;
        private String hostInsId;
        private String mountPath;
        private String status;
    }
}
