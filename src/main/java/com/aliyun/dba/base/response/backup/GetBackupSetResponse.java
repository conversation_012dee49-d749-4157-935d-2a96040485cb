package com.aliyun.dba.base.response.backup;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

@Data
public class GetBackupSetResponse {

    private Long backupSetId;
    private Long backupJobId;
    private String backupStartTime;
    private String backupEndTime;
    private Long backupSetSize;
    private String backupDbList;
    private String backupType;
    private String engine;
    private String engineVersion;
    private Long dbInstanceId;
    private String backupMethod;
    private Integer backupScale;
    private Integer jobMode;
    private Integer isAvail;
    private Integer locks;
    private String slaveStatus;
    private Integer archiveOwnerId;
    private String downloadType;
    private String downloadUrl;
    private String status;

    private String backupsetLocation;
    private Long backupStartTimeTs;
    private SlaveStatus slaveStatusObj;

    public SlaveStatus getSlaveStatusObj() {
        if (slaveStatusObj == null) {
            slaveStatusObj = JSONObject.parseObject(slaveStatus, SlaveStatus.class);

            // 如果没有找到这两个字段，证明备份集来自老架构，兼容老架构的slaveStatus结构
            if (slaveStatusObj.getStatus() == null && slaveStatusObj.getBinlogHinsId() == null) {
                JSONObject jsonObject = JSON.parseObject(slaveStatus);
                for (String key : jsonObject.keySet()) {
                    slaveStatusObj = JSONObject.parseObject(jsonObject.getJSONArray(key).getString(0), SlaveStatus.class);
                    SlaveStatus slaveStatusDouble = JSONObject.parseObject(
                            jsonObject.getJSONArray(key).getJSONObject(0).getString("slave_status"), SlaveStatus.class);
                    slaveStatusObj.setBinLogFile(slaveStatusDouble.getBinLogFile());
                    slaveStatusObj.setBinlogHinsId(slaveStatusDouble.getBinlogHinsId());
                    break;
                }
            }
        }
        return slaveStatusObj;
    }

    @Data
    public static class SlaveStatus {
        // TODO: 当前仅支持xdb snapshot备份，需要支持双节点、本地盘等备份的结构
        private String status;
        private String regionId;
        private Long archiveOwnerid;
        private Long binlogHinsId;
        private String downloadUrl;
        private String snapshotId;
        private Long ecsuserId;
        private String slaveHinsId;
        private String diskId;
        private String downloadType;
        private String type;
        private String binLogFile;
        private String tdeEncryptionKeyId;
        private boolean tdeEnabled;
        private String clsKeyMode;
        private String clsEncryptionKeyId;
        private String variables;
        private String coldDataFsId;
        private String coldDataSnapshotId;
    }
}

