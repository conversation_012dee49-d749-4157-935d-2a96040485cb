package com.aliyun.dba.base.response.backup;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class DescribeBackupStrategyResponse {
    private LogBackupStrategyDTO logBackupStrategy;
    private Boolean supportLocalCrossRegion;
    private String destRegion;
    private FullBackupStrategyDTO fullBackupStrategy;
    private String srcRegion;
    private String instanceName;
    private Boolean supportEncryption;
    private Boolean supportCompress;
    private String destType;
    private String srcType;
    private Integer strategyType;
    private Boolean enableLogBackup;
    private Boolean encryptionEnabled;
    private String destId;
    private List<ChildrenDTO> children;
    private Boolean supportEssdCrossRegion;
    private String srcId;
    private String backupMethod;
    private Integer id;
    private Boolean enableBackup;

    @NoArgsConstructor
    @Data
    public static class LogBackupStrategyDTO {
        private Integer retention;
        private Integer retentionType;
        private String type;
    }

    @NoArgsConstructor
    @Data
    public static class FullBackupStrategyDTO {
        private Integer backupInterval;
        private Long nextBegin;
        private String period;
        private Integer releasedKeepPolicy;
        private Integer retention;
        private Integer retentionType;
        private String secondLevelCategory;
        private Integer seconds;
        private Boolean supportColdRetention;
        private Boolean supportHourlySnapshot;
        private Boolean supportReleasedKeep;
        private Boolean supportSecondLevel;
        private Boolean supportUpdateRetention;
        private String type;
    }

    @NoArgsConstructor
    @Data
    public static class ChildrenDTO {
        private Boolean supportLocalCrossRegion;
        private String destRegion;
        private FullBackupStrategyDTO fullBackupStrategy;
        private String srcRegion;
        private String instanceName;
        private Boolean supportEncryption;
        private Boolean supportCompress;
        private String destType;
        private String srcType;
        private Integer strategyType;
        private Boolean encryptionEnabled;
        private String destId;
        private List<?> children;
        private Boolean supportEssdCrossRegion;
        private String srcId;
        private String backupMethod;
        private Integer id;
        private StoragePoolDTO storagePool;
        private Boolean enableBackup;

        @NoArgsConstructor
        @Data
        public static class FullBackupStrategyDTO {
            private Integer retention;
            private Integer retentionType;
            private String type;
        }

        @NoArgsConstructor
        @Data
        public static class StoragePoolDTO {
            private String cloud;
            private String name;
            private String producer;
            private String id;
            private String region;
            private String type;
            private Integer capacity;
            private String status;
        }
    }


}
