package com.aliyun.dba.base.response.backup;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class DescribeInstanceCrossBackupPolicyResponse {
    private String engineVersion;
    private Integer logRetention;
    private String backupEnabled;
    private String backupPolicyType;
    private String regionCode;
    private String engine;
    private String instanceName;
    private Integer retentType;
    private String crossBackupRegion;
    private Integer logRetentType;
    private String logBackupEnabled;
    private Integer retention;
}
