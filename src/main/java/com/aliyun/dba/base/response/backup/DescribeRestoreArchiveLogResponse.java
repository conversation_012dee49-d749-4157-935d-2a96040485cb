package com.aliyun.dba.base.response.backup;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

@Data
public class DescribeRestoreArchiveLogResponse {
    private Boolean restoreTimeValid;
    private String archiveLogInfo;
    private ArchiveLogInfo archiveLogInfoObj;

    public ArchiveLogInfo getArchiveLogInfo() {
        if (archiveLogInfoObj == null) {
            archiveLogInfoObj = JSONObject.parseObject(archiveLogInfo, ArchiveLogInfo.class);
        }
        return archiveLogInfoObj;
    }

    public Boolean hasMorePage() {
        return getArchiveLogInfo().pageNumber < getArchiveLogInfo().totalPages;
    }

    @Data
    public static class ArchiveLogInfo {
        private Integer pageNumber;
        private Integer pageSize;
        private Integer totalElements;
        private Integer totalPages;
        private String content;
        private List<ArchiveLog> contentObj;
        private String extra;
        private Extra extraObj;

        public List<ArchiveLog> getContent() {
            if (contentObj == null) {
                contentObj = JSONObject.parseArray(content, ArchiveLog.class);
            }
            return contentObj;
        }

        public Extra getExtra() {
            if (extraObj == null) {
                extraObj = JSONObject.parseObject(extra, Extra.class);
            }
            return extraObj;
        }
    }

    @Data
    public static class ArchiveLog {
        private String archiveLogId;
        private String logFileName;
        private String logFileSize;
        private String logBeginTime;
        private String logEndTime;
        private String logStatus;
        private String instanceName;
        private Long hostInstanceId;
        private String location;
        private String checksum;
        private String downloadLink;
        private String intranetDownloadLink;
        private Integer locks;
        private String storageEntityId;
    }

    @Data
    public static class Extra {
        private Integer totalLogSize;
    }
}
