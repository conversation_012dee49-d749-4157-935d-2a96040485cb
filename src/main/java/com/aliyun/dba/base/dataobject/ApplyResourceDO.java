package com.aliyun.dba.base.dataobject;

import lombok.Data;

import java.sql.Timestamp;
import java.util.*;

@Data
public class ApplyResourceDO {

    public static final String APPLY_RESOURCE_PROGRESS_SUBMITTED = "Submitted";
    public static final String APPLY_RESOURCE_PROGRESS_SUBMITTED_DESC = "提交成功";

    public static final String APPLY_RESOURCE_PROGRESS_PROCESSING = "Processing";
    public static final String APPLY_RESOURCE_PROGRESS_PROCESSING_DESC = "资源处理中";

    public static final String APPLY_RESOURCE_PROGRESS_FINISHED = "Finished";
    public static final String APPLY_RESOURCE_PROGRESS_FINISHED_DESC = "已完成";

    public static final String APPLY_RESOURCE_PROGRESS_CANCELLED = "Cancelled";
    public static final String APPLY_RESOURCE_PROGRESS_CANCELLED_DESC = "已取消";

    public static Map<String, String> APPLY_RESOURCE_PROGRESS_MAP = new HashMap<String, String>(){
        {
            this.put(APPLY_RESOURCE_PROGRESS_SUBMITTED,APPLY_RESOURCE_PROGRESS_SUBMITTED_DESC);
            this.put(APPLY_RESOURCE_PROGRESS_PROCESSING,APPLY_RESOURCE_PROGRESS_PROCESSING_DESC);
            this.put(APPLY_RESOURCE_PROGRESS_FINISHED,APPLY_RESOURCE_PROGRESS_FINISHED_DESC);
            this.put(APPLY_RESOURCE_PROGRESS_CANCELLED, APPLY_RESOURCE_PROGRESS_CANCELLED_DESC);
        }
    };

    private Long id;
    private Integer userId;
    private String regionId;
    private String zoneId;
    private String engine;
    private String engineVersion;
    private String category;
    private String storageType;
    private Integer storage;
    private String instanceNetworkType;
    private String classCode;
    private Integer quantity;
    private Date expectedTime;
    private String progress = "Submitted";
    private String description;
    private Integer aoneId;
    private String aoneUrl;
    private String owner;
    private Timestamp gmtCreated;
    private Timestamp gmtModified;

    public ApplyResourceDO(Integer userId, String regionId, String zoneId, String engine, String engineVersion, String category, String storageType, Integer storage, String instanceNetworkType, String classCode, Integer quantity, Date expectedTime, String progress) {
        this.userId = userId;
        this.regionId = regionId;
        this.zoneId = zoneId;
        this.engine = engine;
        this.engineVersion = engineVersion;
        this.category = category;
        this.storageType = storageType;
        this.storage = storage;
        this.instanceNetworkType = instanceNetworkType;
        this.classCode = classCode;
        this.quantity = quantity;
        this.expectedTime = expectedTime;
        this.progress = progress;
    }

    public ApplyResourceDO(Long id, Integer userId, String regionId, String zoneId, String engine, String engineVersion, String category, String storageType, Integer storage, String instanceNetworkType, String classCode, Integer quantity, Timestamp expectedTime, String progress, String description, Integer aoneId, String aoneUrl, String owner) {
        this.id = id;
        this.userId = userId;
        this.regionId = regionId;
        this.zoneId = zoneId;
        this.engine = engine;
        this.engineVersion = engineVersion;
        this.category = category;
        this.storageType = storageType;
        this.storage = storage;
        this.instanceNetworkType = instanceNetworkType;
        this.classCode = classCode;
        this.quantity = quantity;
        this.expectedTime = expectedTime;
        this.progress = progress;
        this.description = description;
        this.aoneId = aoneId;
        this.aoneUrl = aoneUrl;
        this.owner = owner;
    }

    public ApplyResourceDO(Long id, Integer userId, String regionId, String zoneId, String engine, String engineVersion,
                           String category, String storageType, Integer storage,
                           String instanceNetworkType, String classCode, Integer quantity, Timestamp expectedTime,
                           String progress, String description, Integer aoneId, String aoneUrl, String owner,
                           Timestamp gmtCreated, Timestamp gmtModified) {
        this.id = id;
        this.userId = userId;
        this.regionId = regionId;
        this.zoneId = zoneId;
        this.engine = engine;
        this.engineVersion = engineVersion;
        this.category = category;
        this.storageType = storageType;
        this.storage = storage;
        this.instanceNetworkType = instanceNetworkType;
        this.classCode = classCode;
        this.quantity = quantity;
        this.expectedTime = expectedTime;
        this.progress = progress;
        this.description = description;
        this.aoneId = aoneId;
        this.aoneUrl = aoneUrl;
        this.owner = owner;
        this.gmtCreated = gmtCreated;
        this.gmtModified = gmtModified;
    }

    public void dealWithShowAttr(int utcDiffSeconds){
        //this.setProgress(APPLY_RESOURCE_PROGRESS_MAP.get(this.getProgress()));
        this.expectedTime = new Date(this.expectedTime.getTime() - utcDiffSeconds * 1000);
    }
}
