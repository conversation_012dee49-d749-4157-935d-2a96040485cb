package com.aliyun.dba.base.dataobject;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class ZoneDO {

    private Integer id;
    private String zoneId;
    private String regionId;
    private String siteName;
    private String vgwIp;
    private Date gmtCreated;
    private Date gmtModified;

    public ZoneDO(){

    }

    public ZoneDO(Integer id, String zoneId, String regionId, String siteName, String vgwIp, Date gmtCreated, Date gmtModified) {
        this.id = id;
        this.zoneId = zoneId;
        this.regionId = regionId;
        this.siteName = siteName;
        this.vgwIp = vgwIp;
        this.gmtCreated = gmtCreated;
        this.gmtModified = gmtModified;
    }
}
