package com.aliyun.dba.base.parameter;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.commonkindcode.idao.ParamGroupTemplateIDao;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.instance.entity.MycnfTemplateExtra;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.SneakyThrows;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_ENTERPRISE;

/**
 * MysqlParamGroupHelper
 *
 * @blame 宇一
 */
@Component
public class MysqlParamGroupHelper {
    @Resource
    CustinsParamService custinsParamService;
    @Resource
    private ParamGroupTemplateIDao paramGroupTemplateIDao;
    @Resource
    private DbossApi dbossApi;

    /**
     * mysql大版本升级升级系统参数模板 - 升级版本
     *
     * @param custins
     * @param newVersion
     */
    public void mysqlUpgradeUpdateSysParamGroupVersion(CustInstanceDO custins, String newVersion) throws RdsException {
        CustinsParamDO paramGroupId = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
        if (paramGroupId == null || StringUtils.isBlank(paramGroupId.getValue())) {
            return;
        }

        Map paramGroupInfo = SysParamGroupHelper.describeSysParamGroupId(paramGroupId.getValue());
        if (paramGroupInfo.isEmpty()) {
            return;
        }

        paramGroupInfo.put("db_version", newVersion);
        String newParamGroupId = SysParamGroupHelper.getSysParamGroupId(paramGroupInfo);
        mysqlUpgradeUpdateSysParamGroupInfo(custins, newParamGroupId, paramGroupInfo);
    }

    /**
     * mysql大版本升级升级系统参数模板 - 升级版本
     *
     * @param custins
     * @param newVersion
     */
    public void mysqlUpgradeUpdateSysParamGroupCategory(CustInstanceDO custins, String newCategory) throws RdsException {
        CustinsParamDO paramGroupId = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
        if (paramGroupId == null || StringUtils.isBlank(paramGroupId.getValue())) {
            return;
        }

        Map paramGroupInfo = SysParamGroupHelper.describeSysParamGroupId(paramGroupId.getValue());
        if (paramGroupInfo.isEmpty()) {
            return;
        }

        paramGroupInfo.put("category", newCategory);
        String newParamGroupId = SysParamGroupHelper.getSysParamGroupId(paramGroupInfo);

        // 企业版暂不支持系统参数模板，需要将原有模板清除
        if (CATEGORY_ENTERPRISE.equalsIgnoreCase(newCategory)) {
            newParamGroupId = "";
            paramGroupInfo = null;
        }

        mysqlUpgradeUpdateSysParamGroupInfo(custins, newParamGroupId, paramGroupInfo);
    }

    /**
     * mysql大版本升级升级系统参数模板
     */
    public void mysqlUpgradeUpdateSysParamGroupInfo(CustInstanceDO custins, String newParamGroupId, Map newParamGroupInfo) {
        String paramGroupInfoStr = "{}";
        if (newParamGroupInfo != null) {
            paramGroupInfoStr = JSON.toJSONString(newParamGroupInfo);
        }

        custinsParamService.setCustinsParam(custins.getId(),
                CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, newParamGroupId);
        custinsParamService.setCustinsParam(custins.getId(),
                CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, paramGroupInfoStr);
    }

    /**
     * mysql 获取系统模板参数详情
     * 自定义模板参数详情需要查询param_groups_details 表, 系统模板参数详情需要查询mycnf_template_extra表
     * @param paramGroupId
     * @return Map
     */
    public Map<String, Object> getSysParameterGroupDetail(String paramGroupId) {
        List<MycnfTemplateExtra> paramTempList = paramGroupTemplateIDao.getParameterExtraWithParamGroupId(paramGroupId);
        Map<String, Object> parameterMap = new HashMap<>();
        for (MycnfTemplateExtra paramTemp : paramTempList) {
            parameterMap.put(paramTemp.getName(), paramTemp.getDefaultValue());
        }
        return parameterMap;
    }

    /**
     * 通过 SyncMode 判断是否是 MGR
     * 0: 异步
     * 1: 半同步
     * 2: 双通道
     * 4: MGR
     */
    public boolean isMgr(String syncMode) {
        return "4".equals(syncMode);
    }

    @SneakyThrows
    public void checkXengineToInnoDBConversion(Integer custinsId, String parameterGroupId) {
        var requireCheckingTables = !"xengine".equals(SysParamGroupHelper.getDBStorageEngine(parameterGroupId));
        if (requireCheckingTables) {
            var engineCount = dbossApi.queryEngineCount(custinsId, 3);
            var xengineCount = Integer.parseInt(engineCount.getOrDefault("xengine", "0").toString());
            if (xengineCount > 0) {
                throw new RdsException(ErrorCode.OPERATION_DENIED_XENGINE_SWITCH);
            }
        }
    }
}
