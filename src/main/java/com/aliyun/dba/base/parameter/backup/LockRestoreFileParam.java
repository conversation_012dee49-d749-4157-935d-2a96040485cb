package com.aliyun.dba.base.parameter.backup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LockRestoreFileParam {
    /**
    通用参数
     */
    private String requestId;
    private String callerBid;
    private String userId;

    /**
     * 原实例名
     */
    private String instanceName;
    /**
     * 原实例地域
     */
    private String instanceRegion;

    /**
     * 备份集所在地域
     */
    private String regionCode;

    private String sceneType;
    private String lockType;

    private String sourcePlatform;
    private String sourceWorkflowId;
    private String usageType;

    /**
    锁定/解锁备份集参数
     */
    private String backupId;

    /**
    锁定/解锁日志集参数
     */
    private String archiveLogId;
    private String restoreTimePoint;
    private String consistentTime;
}
