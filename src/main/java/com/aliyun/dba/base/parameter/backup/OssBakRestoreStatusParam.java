package com.aliyun.dba.base.parameter.backup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/29 20:52
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssBakRestoreStatusParam {
    private String user_id;
    private String uid;
    private String requestId;
    private String instanceName;


    /**
     * Status状态
     1. 校验中 2. 校验失败 3. 校验成功 4.镜像生产完成, 5.已删除
     */
    private Integer status;

    private String reason;
    
}
