package com.aliyun.dba.base.parameter.backup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DescribeCrossRegionMigrateNewArchStatusParam {
    /**
     通用参数
     */
    private String requestId;
    private String accessId;

    private String callerBid;
    private String userId;

    /**
     * 原实例名
     */
    private String instanceName;
    /**
     * 原实例地域
     */
    private String regionCode;

    private String policyLevel = "1";
}
