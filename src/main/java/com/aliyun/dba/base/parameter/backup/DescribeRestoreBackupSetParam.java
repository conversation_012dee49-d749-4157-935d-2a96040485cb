package com.aliyun.dba.base.parameter.backup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DescribeRestoreBackupSetParam {
    private String requestId;
    private String callerBid;
    private String userId;
    private String instanceName;
    /**
     * 原实例地域
     */
    private String instanceRegion;
    private String accessId;
    private String sceneType;

    private Integer hostInstanceId;
    private Long restoreTimePoint;
    private String backupId;
    private String UnifyBackupSetId;
    /**
     * 备份集地域
     */
    private String regionCode;
    private Boolean instanceIsDeleted;
}
