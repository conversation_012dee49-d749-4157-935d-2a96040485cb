package com.aliyun.dba.base.parameter.backup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DescribeRestoreArchiveLogParam {
    private String requestId;
    private String callerBid;
    private String userId;
    private String instanceName;
    private String instanceRegion;
    private String regionCode;
    private String accessId;
    private String sceneType;
    private Long restoreTimePoint;
    private Long consistentTime;
    private Integer hostInstanceId;
    private String continuousLogCheck;
    private Integer pageNumber;
    private Integer pageSize;
}
