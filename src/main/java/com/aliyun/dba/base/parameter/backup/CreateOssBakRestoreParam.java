package com.aliyun.dba.base.parameter.backup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/29 20:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateOssBakRestoreParam {
    private String user_id;
    private String uid;
    private String requestId;
    private String ossUrl;
    private String ossFileSize;
    private String ossBucket;
    private String ossFilePath;
    private String ossFileName;
    private String ossFileMetaData;
    private String engine;
    private String engineVersion;
    private String comment;
    private String restoreSize;
    private String zoneId;
}
