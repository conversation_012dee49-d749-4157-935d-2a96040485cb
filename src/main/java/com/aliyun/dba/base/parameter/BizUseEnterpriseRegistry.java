package com.aliyun.dba.base.parameter;

import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import io.kubernetes.client.JSON;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

public class BizUseEnterpriseRegistry implements Serializable {
    private static final long serialVersionUID = 9036474857975447016L;
    /**
     * [{
     *     db_type: mysql,
     *     biz_type: [aliyun, gov],
     *     region: [ch-hangzhou]
     * },{
     *     db_type: mongo,
     *     biz_type: [*]
     *     region: [*]
     * }]
     */
    @Getter
    private List<BizInfo> bizInfoList;

    public BizUseEnterpriseRegistry(String jsonString) {
        bizInfoList = new JSON().deserialize(jsonString, new TypeToken<List<BizInfo>>() {}.getType());
    }

    @Data
    public static class BizInfo {
        @SerializedName("db_type")
        String dbType;
        @SerializedName("biz_type_list")
        List<String> bizTypeList;
        @SerializedName("region_list")
        List<String> regionList;
    }
}
