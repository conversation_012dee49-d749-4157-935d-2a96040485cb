package com.aliyun.dba.base.parameter.backup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/29 23:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BackupSetParam {
    /**
     * 运营商ID
     */
    private String user_id;
    /**
     * 用户ID
     */
    private String uid;
    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 内部用户ID
     */
    private String innerUserID;
    /**
     * 实例名称
     */
    private String dbInstanceName;
    /**
     * 实例Id（cust_instance.id）
     */
    private Integer dBInstanceId;
    /**
     * 备份集Id
     */
    private Long backupSetId;
}
