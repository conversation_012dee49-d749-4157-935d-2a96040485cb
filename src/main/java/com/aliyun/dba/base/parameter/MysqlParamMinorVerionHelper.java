package com.aliyun.dba.base.parameter;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.entity.MycnfTemplate;
import com.aliyun.dba.instance.entity.MycnfTemplateExtra;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsSupport.useMycnfTemplateExtra;

/**
 * mysql参数小版本相关方法
 */
@Component
public class MysqlParamMinorVerionHelper {
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;

    private final Integer DEFAULT_MIN_VERISON = -1;
    private static final LogAgent logger = LogFactory.getLogAgent(MysqlParamMinorVerionHelper.class);

    /**
     * 获取当前实例 小版本不兼容的参数列表
     * @param custins
     * @return
     */
    public List<String> getInstanceMinVersionUnsupportParams(CustInstanceDO custins){
        List<String> paramList = new ArrayList<>();
        if (null == custins || !custins.isMysql()) {
            return paramList;
        }
        Integer minVersion = getNumMinorVersionByCustinsId(custins.getId());
        if (DEFAULT_MIN_VERISON == minVersion) {
            return paramList;
        }

        try {
            List<String> params = queryMinVersionUnsupportParams(custins, minVersion);
            paramList.addAll(params);
        } catch (Exception e) {
            logger.error("getInstanceMinVersionUnsupportParams error:" + e.getMessage());
        }
        return paramList;
    }

    private List<String> queryMinVersionUnsupportParams(CustInstanceDO custins, Integer currMinorVersion){
        String characterType = custins.getCharacterType();
        if (custins.isMysql() && custins.isCustinsDockerOnEcs() || custins.isCustinsDockerOnEcsLocalSSD()){
            characterType = CustinsSupport.CHARACTER_TYPE_NORMAL;
        }
        List<String> paramList = new ArrayList<>();

        Boolean useMycnfTemplateExtra = false;
        InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        if(useMycnfTemplateExtra(custins.getDbType(), custins.getDbVersion(), instanceLevelDO.getCategory())){
            useMycnfTemplateExtra = true;
        }
        if (!useMycnfTemplateExtra) {
            List<MycnfTemplate> dataList = instanceService.getMycnfTemplateListByDbType(custins.getDbType(),
                    custins.getDbVersion(), characterType);
            for (MycnfTemplate tmp : dataList) {
                if (StringUtils.isNotBlank(tmp.getMinorVersion())) {
                    if (!checkVersion(currMinorVersion, tmp.getMinorVersion())) {
                        paramList.add(tmp.getName());
                    }
                }
            }
        } else {
            Map<String, MycnfTemplateExtra> dataMap = instanceService.getMycnfTemplateExtraMapIgnoreVisible(custins.getDbType(),
                    custins.getDbVersion(), characterType, instanceLevelDO.getCategory());
            for (MycnfTemplateExtra tmp : dataMap.values()) {
                if (StringUtils.isNotBlank(tmp.getMinorVersion())) {
                    if (!checkVersion(currMinorVersion, tmp.getMinorVersion())) {
                        paramList.add(tmp.getName());
                    }
                }
            }
        }
        return paramList;
    }

    /**
     * 获取当前实例小版本
     * @param custinsId
     * @return Integer 20201231
     */
    public Integer getNumMinorVersionByCustinsId(Integer custinsId) {
        // 获取正在使用小版本 字符串（apsaradb-alios7u-mysql5715_20201231-20210326161055, mysql_20191212, apsaradb-alios7u-mysql80:20201231-20210309150834）
        String minorVersionNowUse = custinsService.getCustInstanceCurrentMinorVersionByCustinsId(custinsId);
        if (StringUtils.isBlank(minorVersionNowUse)) {
            return DEFAULT_MIN_VERISON;
        }
        String sep = "_";
        if (minorVersionNowUse.contains(":")) {
            sep = ":";
        }
        String[] tmp = minorVersionNowUse.split(sep);
        if (tmp.length == 2 && tmp[1].length() >= 8) {
            return Integer.valueOf(tmp[1].substring(0, 8));
        }
        return DEFAULT_MIN_VERISON;
    }


    /**
     *  判断当前实例小版本是否符合小版本限制
     * @param currVer 20201031
     * @param minorVersion [20201031,99999999)
     * @return boolean
     */
    private static boolean checkVersion(Integer currVer, String minorVersion) {
        String[] versionArr = minorVersion.substring(1, minorVersion.length()-1).split(",");
        Integer minVersion = Integer.parseInt(versionArr[0].trim());
        Integer maxVersion = Integer.parseInt(versionArr[1].trim());
        boolean minFalge = true;
        boolean maxFalge = true;

        if (minorVersion.startsWith("[")) {
            minFalge = currVer >= minVersion;
        } else if (minorVersion.startsWith("(")) {
            minFalge = currVer > minVersion;
        }

        if (minorVersion.endsWith("]")) {
            maxFalge = currVer <= maxVersion;
        }else if (minorVersion.endsWith(")")) {
            maxFalge = currVer < maxVersion;
        }
        return minFalge && maxFalge;
    }
}
