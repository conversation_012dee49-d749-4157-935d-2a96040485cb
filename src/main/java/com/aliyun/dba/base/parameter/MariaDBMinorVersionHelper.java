package com.aliyun.dba.base.parameter;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.EngineCompose;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.dockerdefault.service.mariadb.MariaDBUpgradeDBVersionService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class MariaDBMinorVersionHelper {

    @Autowired
    protected MinorVersionService minorVersionService;
    @Autowired
    InstanceService instanceService;
    @Autowired
    CustinsIDao custinsIDao;

    private static final Logger logger = Logger.getLogger(MariaDBUpgradeDBVersionService.class);
    // get related engine compose id from minor version release record
    private static final String ENGINE_COMPOSE_ID_KEY = "engine_compose_id";

    public String getAndCheckTargetMinorVersion(CustInstanceDO custins, String minorVersion) throws RdsException {
        String releaseDate = parseReleaseDate(minorVersion);
        return generateMinorVersionFromReleaseDate(custins, releaseDate);
    }

    public String parseReleaseDate(String minorVersion) throws RdsException {
        String rdsPattern = "^rds_(\\d{8})$";
        String mariadbDockerOnEcsPattern = "^mariadb\\d{3,4}:(\\d{8})-\\d{14}$";
        if (minorVersion == null) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
        Matcher rdsPatternMatcher = Pattern.compile(rdsPattern).matcher(minorVersion);
        Matcher mariadbDockerOnEcsPatternMatcher = Pattern.compile(mariadbDockerOnEcsPattern).matcher(minorVersion);
        if (rdsPatternMatcher.matches()) {
            return rdsPatternMatcher.group(1);
        } else if (mariadbDockerOnEcsPatternMatcher.matches()) {
            return mariadbDockerOnEcsPatternMatcher.group(1);
        } else {
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }
    }

    public MinorVersionReleaseDO getMinorVersionReleaseDOByReleaseDate(CustInstanceDO custins, String releaseDate) throws RdsException {
        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        String category = instanceLevel.getCategory();
        List<MinorVersionReleaseDO> minorVersionList = minorVersionService.querySpecifyMinorVersionListByCondition(
                custins.getDbType(), custins.getDbVersion(), custins.getKindCode(), category, MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_DOCKER_IMAGE, releaseDate);
        // should be only one record
        if (null == minorVersionList || minorVersionList.isEmpty()) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        return minorVersionList.get(0);
    }

    public EngineCompose getEngineComposeByReleaseDate(CustInstanceDO custins, String releaseDate) throws RdsException {
        try {
            MinorVersionReleaseDO minorVersionReleaseDO = getMinorVersionReleaseDOByReleaseDate(custins, releaseDate);
            Map<String, Object> engineComposeInfo = JSON.parseObject(minorVersionReleaseDO.getEngineComposeInfo());
            Integer engineComposeId = (Integer) engineComposeInfo.get(ENGINE_COMPOSE_ID_KEY);
            // get engine compose by id
            EngineCompose engineCompose = custinsIDao.getEngineComposeById(engineComposeId);
            if (engineCompose == null) {
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            return engineCompose;
        } catch (Exception ex) {
            logger.error("Parse engine compose info error ", ex);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public String generateMinorVersionFromReleaseDate(CustInstanceDO custins, String releaseDate) throws RdsException {
        // generate minor version based on input
        String minorVersion = custins.getDbType() + custins.getDbVersion().replace(".", "") + ":" + releaseDate;
        try {
            EngineCompose engineCompose = getEngineComposeByReleaseDate(custins, releaseDate);
            String services = engineCompose.getServices();
            Pattern pattern = Pattern.compile(minorVersion + "-\\d{14}");
            Matcher matcher = pattern.matcher(services);

            if (matcher.find()) {
                return matcher.group();
            } else {
                throw new Exception();
            }
        } catch (Exception ex) {
            logger.error("Check minor version with engine compose failed ", ex);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
