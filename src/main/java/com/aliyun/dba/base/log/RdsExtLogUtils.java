package com.aliyun.dba.base.log;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.apache.log4j.Logger;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * class used to generate log for rdsapi-ext-* mainly used AOP monitor MysqlDBEngineExtAdapter，
 * MysqlOnEcsDBEngineExtAdapter
 */
public class RdsExtLogUtils {

    private static final Logger logger = Logger.getLogger(RdsExtLogUtils.class);
    private static String COMPONENT_NAME = "RDSAPI-EXT-MYSQL";
    private static String START_TIME = "_StartTime";
    private static String METHOD_NAME = "_Method";
    private static String IS_WRITE_LOG = "_IS_WRITE_LOG";

    /**
     * 进去请求
     * */
    public static void doBefore(Map<String, String> actionParams){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss,SSS");
        //记录请求到达时间,临时添加
        actionParams.put(START_TIME, sdf.format(new Date()));
    }

    /**
     * 返回结果
     * */
    public static void doAfterReturn(Map<String, String> actionParams, Map<String, Object> result){

        try {
            String response = JSONObject.toJSONString(result);
            FullLogFormatDO fullLogFormatDO = new FullLogFormatDO();

            //访问失败
            if (result.containsKey("errorCode")) {
                Object[] errCode = (Object[])result.get("errorCode");
                Integer code = (Integer)errCode[0];
                String summary = (String)errCode[1];
                String message = (String)errCode[2];

                fullLogFormatDO.setCode(String.valueOf(code));
                fullLogFormatDO.setRdsCode(summary);
                fullLogFormatDO.setMessage(message);
            } else {

                String code = "200";
                String rdsCode = "successful";
                fullLogFormatDO.setCode(code);
                fullLogFormatDO.setRdsCode(rdsCode);
            }

            Date startDate = null;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss,SSS");
            try {
                startDate = sdf.parse(actionParams.get(START_TIME));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            fullLogFormatDO.setStartTime(startDate);
            actionParams.remove(START_TIME);
            fullLogFormatDO.setEndTime(new Date());
            fullLogFormatDO.setCostTime(
                fullLogFormatDO.getEndTime().getTime() - fullLogFormatDO.getStartTime().getTime());
            actionParams.remove(METHOD_NAME);

            fullLogFormatDO.setRequestId(getParameterValue(actionParams, ParamConstants.REQUEST_ID));
            String action = getParameterValue(actionParams, ParamConstants.ACTION);
            fullLogFormatDO.setAction(action);
            fullLogFormatDO.setRequest(JSONObject.toJSONString(actionParams));
            String isWriteLog = actionParams.get(IS_WRITE_LOG);
            //记录日志或者返回值不等于200，记录日志
            if ("true".equalsIgnoreCase(isWriteLog) || !"200".equalsIgnoreCase(fullLogFormatDO.getCode())) {
                fullLogFormatDO.setResponse(response);
            }
            fullLogFormatDO.setAccessId(getParameterValue(actionParams, ParamConstants.ACCESSID));
            fullLogFormatDO.setComponent(COMPONENT_NAME);

            //logger.info(fullLogFormatDO.toFormatLog());
        } catch(Exception ex){
            logger.error("doAfterReturn error", ex);
        }
    }


    public static void doAfterThrowing(Map<String, String> actionParams, Exception ex){

        try {
            //记录抛出异常信息
            FullLogFormatDO fullLogFormatDO = new FullLogFormatDO();

            if (ex instanceof RdsException) {

                Object[] errCode = ((RdsException)ex).getErrorCode();
                Integer code = (Integer)errCode[0];
                String summary = (String)errCode[1];
                String message = (String)errCode[2];

                fullLogFormatDO.setCode(String.valueOf(code));
                fullLogFormatDO.setRdsCode(summary);
                fullLogFormatDO.setMessage(message);
            }
            //其他非预期异常
            else {

                ErrorCode errorCode = ErrorCode.INTERNAL_FAILURE;

                fullLogFormatDO.setCode(String.valueOf(errorCode.getCode()));
                fullLogFormatDO.setRdsCode(errorCode.getSummary());
                fullLogFormatDO.setMessage(errorCode.getDesc());
            }

            Date startDate = null;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss,SSS");
            try {
                startDate = sdf.parse(actionParams.get(START_TIME));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            fullLogFormatDO.setStartTime(startDate);
            actionParams.remove(START_TIME);
            fullLogFormatDO.setEndTime(new Date());
            fullLogFormatDO.setCostTime(
                fullLogFormatDO.getEndTime().getTime() - fullLogFormatDO.getStartTime().getTime());
            String method = actionParams.get(METHOD_NAME);
            actionParams.remove(METHOD_NAME);
            fullLogFormatDO.setRequestId(getParameterValue(actionParams, ParamConstants.REQUEST_ID));
            fullLogFormatDO.setRequest(JSONObject.toJSONString(actionParams));
            fullLogFormatDO.setAccessId(getParameterValue(actionParams, ParamConstants.ACCESSID));
            fullLogFormatDO.setComponent(COMPONENT_NAME);
            fullLogFormatDO.setAction(getParameterValue(actionParams, ParamConstants.ACTION));
            fullLogFormatDO.setMethod(method);

            //logger.info(fullLogFormatDO.toFormatLog());
        } catch(Exception exp){
            logger.error("doAfterThrowing error", exp);
        }
    }

    private static String getParameterValue(Map<String, String> actionParams, String paramName){
        if(actionParams == null || actionParams.isEmpty()){
            return null;
        }
        if(paramName == null || "".equalsIgnoreCase(paramName)){
            return null;
        }
        return actionParams.get(paramName.toLowerCase());
    }
}