package com.aliyun.dba.base.log;

import com.alibaba.fastjson.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * the class contains all attributes for rdsapi log
 */
public class FullLogFormatDO {

    protected String requestId;

    protected String component;

    protected String action;

    protected String accessId;

    protected String code;

    protected Date startTime;

    protected Date endTime;

    //mills,by this attribute, sunfire can set the request is timeout or not
    protected Long costTime;

    protected String requestIP;

    protected String request;

    protected String response;

    //InternalFail
    protected String rdsCode;

    protected String message;

    protected String method;

    public FullLogFormatDO() {
    }

    public FullLogFormatDO(Map<String, String> actionParams) {}

    public FullLogFormatDO(String requestId, String component, String action, String accessId,
                           String code, Date startTime, Date endTime, Long costTime, String requestIP,
                           String request, String response, String rdsCode, String message, String method) {
        this.requestId = requestId;
        this.component = component;
        this.action = action;
        this.accessId = accessId;
        this.code = code;
        this.startTime = startTime;
        this.endTime = endTime;
        this.costTime = costTime;
        this.requestIP = requestIP;
        this.request = request;
        this.response = response;
        this.rdsCode = rdsCode;
        this.message = message;
        this.method = method;
    }

    //转化为sunfire标准日志记录格式
    public String toFormatLog() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss,SSS");
        return component + "|" + accessId + "|" + action + "|" + requestId + "|" + code + "|" + rdsCode + "|" + sdf
            .format(
                startTime)
            + "|" + sdf.format(endTime) + "|" + costTime + "|" + requestIP + "|" + request + "|" + response;
    }

    public static String transMills2String(Long mills) {

        Date date = new Date(mills);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss,SSS");
        return sdf.format(date);
    }

    //转化为JSON形式
    public String toJSONFormat() {
        if (this == null) {
            return new JSONObject().toJSONString();
        }
        return JSONObject.toJSONString(this);
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getAccessId() {
        return accessId;
    }

    public void setAccessId(String accessId) {
        this.accessId = accessId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getCostTime() {
        return costTime;
    }

    public void setCostTime(Long costTime) {
        this.costTime = costTime;
    }

    public String getRequestIP() {
        return requestIP;
    }

    public void setRequestIP(String requestIP) {
        this.requestIP = requestIP;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getRdsCode() {
        return rdsCode;
    }

    public void setRdsCode(String rdsCode) {
        this.rdsCode = rdsCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
}