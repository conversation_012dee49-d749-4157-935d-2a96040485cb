package com.aliyun.dba.base.lib;

import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.endpoint.service.EndpointService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

/**
 * <AUTHOR> on 2023/12/11
 */
@Service
public class ConnAddrService {

    private static final LogAgent logger = LogFactory.getLogAgent(ConnAddrService.class);

    @Resource
    private ResourceService resourceService;

    @Resource
    private EndpointService endpointService;

    @Resource
    private AVZSupport avzSupport;


    /**
     * 获取原实例保留的连接串（用于回收站的场景）
     * 目前暂时只对白名单用户生效
     *
     * @param params
     * @return
     * @throws RdsException
     */
    public String getRetainSourceConnAddressIfNeeded(Map<String, String> params) throws RdsException {
        String sourceConnectionString = getParameterValue(params, "SourceConnectionString");
        if (StringUtils.isEmpty(sourceConnectionString)) {
            return null;
        }
        String uid = getParameterValue(params, ParamConstants.UID);
        ResourceDO resourceDO = resourceService.getResourceByResKey("RECYCLE_BIN_RETAIN_ADDR_UID");
        if (resourceDO == null || StringUtils.isEmpty(resourceDO.getRealValue())) {
            return null;
        }
        if (!Sets.newHashSet(StringUtils.split(resourceDO.getRealValue(), ",")).contains(uid)) {
            return null;
        }
        //从回收站重建的实例，连接串保持与原实例的一致
        if (StringUtils.contains(sourceConnectionString, ".")) {
            Integer cnt = endpointService.countConnectionAddress(sourceConnectionString);
            if (cnt == 0) {
                logger.info("sourceConnectionString[{}] is in metaDB not found, use it", sourceConnectionString);
                //域名没有占用，可以使用原实例的连接串
                return StringUtils.split(sourceConnectionString, ".")[0];
            } else {
                logger.info("sourceConnectionString[{}] is already exists in metaDB, use new", sourceConnectionString);
            }
        }
        return null;
    }


    /**
     * 判断是否允许在ODBS集群创建DNS链路实例
     *
     * @param subDomain
     * @return
     * @throws RdsException
     */
    public boolean isAllowCreateDnsLinkForODBS(String subDomain) throws RdsException {
        ResourceDO resourceDO = resourceService.getResourceByResKey("BLOCK_DNS_CREATE_FOR_ODBS");
        if (resourceDO == null || StringUtils.isEmpty(resourceDO.getRealValue())) {
            return true;
        }
        if (!LangUtil.getBoolean(resourceDO.getRealValue(), false)) {
            return true;
        }
        //odbs集群不能拦截DNS链路的创建实例
        String regionCategory = avzSupport.getRegionCategory(subDomain);
        return !StringUtils.equalsIgnoreCase(regionCategory, "odbs");
    }


}
