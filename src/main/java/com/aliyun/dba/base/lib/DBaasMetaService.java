package com.aliyun.dba.base.lib;


import com.aliyun.apsaradb.dbaasmetaapi.ApiClient;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.Configuration;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointGroupListResult;
import io.opentracing.contrib.okhttp3.TracingInterceptor;
import io.opentracing.util.GlobalTracer;
import lombok.var;
import okhttp3.OkHttpClient;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Service
public class DBaasMetaService {
    private static final Logger logger = LoggerFactory.getLogger(DBaasMetaService.class);
    public static final int CONNECT_TIMEOUNT = 1200000;
    private static Map<String, DefaultApi> regionToApi = new ConcurrentHashMap<>();

    @Resource
    private NameServiceHelper nameServiceHelper;

    private DefaultApi defaultmetaApi;

    @PostConstruct
    public void init() {
    }

    private void initDefaultClient() throws Exception {
        logger.info("start init dbaas meta service");
        var tracer = GlobalTracer.get();
        OkHttpClient okHttpClient = TracingInterceptor.addTracing(new OkHttpClient().newBuilder()
                .connectTimeout(300, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS)
                .writeTimeout(300, TimeUnit.SECONDS), tracer);

        ApiClient defaultApiClient = Configuration.getDefaultApiClient();
        defaultApiClient.setHttpClient(okHttpClient);
        // 通过名字服务获取domain, 线上环境使用
        String gwSubDomain = nameServiceHelper.getGwSubDomainByType("DBAAS_META_API");
        if (StringUtils.isEmpty(gwSubDomain)) {
            logger.error("Can not get DbaasMetaClient gateway sub domain from name service.");
            throw new Exception("Can not get DbaasMetaClient gateway sub domain from name service.");
        }
        defaultApiClient.setBasePath("http://" + gwSubDomain + "/api/v1.0");
        defaultmetaApi = new DefaultApi();
        defaultmetaApi.setApiClient(defaultApiClient);
    }

    public DefaultApi getMetaDbClient(String regionId) throws ApiException {
        return StringUtils.isEmpty(regionId) ? getDefaultClient() : getRegionClient(regionId);
    }

    public DefaultApi getDefaultClient() {
        if (this.defaultmetaApi == null) {
            synchronized (this) {
                if (this.defaultmetaApi == null) {
                    try {
                        this.initDefaultClient();
                    } catch (Exception e) {
                        logger.error("init default dbaas meta api failed.", e);
                    }
                }
            }
        }
        return this.defaultmetaApi;
    }

    public DefaultApi getRegionClient(String regionId) throws ApiException {
        if (StringUtils.isEmpty(regionId)) {
            return getDefaultClient();
        }

        DefaultApi regionMetaApi = regionToApi.get(regionId);
        if (regionMetaApi != null) {
            return regionMetaApi;
        }
        var tracer = GlobalTracer.get();
        OkHttpClient okHttpClient = TracingInterceptor.addTracing(new OkHttpClient().newBuilder()
                .connectTimeout(300, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS)
                .writeTimeout(300, TimeUnit.SECONDS), tracer);
        ApiClient apiClient = new ApiClient();
        apiClient.setHttpClient(okHttpClient);
        String gwSubDomain = nameServiceHelper.getRegionGwSubDomainByType(regionId, "DBAAS_META_API");
        apiClient.setBasePath("http://" + gwSubDomain + "/api/v1.0");
        regionMetaApi = new DefaultApi();
        regionMetaApi.setApiClient(apiClient);
        regionToApi.put(regionId, regionMetaApi);
        return regionMetaApi;
    }

    public EndpointGroupListResult listEndpointGroups(String requestId, String dbInstanceName) throws ApiException {
        EndpointGroupListResult endpointGroupListResult = this.getDefaultClient().listEndpointGroups(requestId, dbInstanceName);
        if (endpointGroupListResult.getItems() == null) {
            return null;
        }
        endpointGroupListResult.getItems().removeIf(eg -> eg.getStatus() == -1);
        return endpointGroupListResult;
    }
}
