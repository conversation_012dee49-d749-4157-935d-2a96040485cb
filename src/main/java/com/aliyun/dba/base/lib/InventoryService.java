package com.aliyun.dba.base.lib;

import com.alicloud.apsaradb.inventory.ApiClient;
import com.alicloud.apsaradb.inventory.api.DefaultApi;
import com.alicloud.apsaradb.inventory.model.*;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.utils.RequestSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.lib.DBaasMetaService.CONNECT_TIMEOUNT;


/**
 * The new version of resource inventory scheduling basic service, the new inventory interface will only be updated here.
 *
 * <AUTHOR> on 2024/04/24.
 */
@Service
@Slf4j
public class InventoryService {
    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private NameServiceHelper nameServiceHelper;
    private DefaultApi defaultApi;

    public static String NAME_SERVICE_TYPE_RM_INVENTORY = "RM_INVENTORY";

    @PostConstruct
    public void init() {
        //Try to initialize once when the application starts. If it fails, you can continue to obtain it later.
        try {
            defaultApi = initClientApi();
        } catch (Exception e) {
            log.error("init CommonProviderClient failed when start application");
        }
    }

    public DefaultApi getDefaultApi() throws ApiException {
        if (defaultApi == null) {
            synchronized (this) {
                if (defaultApi == null) {
                    defaultApi = initClientApi();
                }
            }
        }
        return defaultApi;
    }

    private DefaultApi initClientApi() throws ApiException {
        String inventoryServiceDomain = nameServiceHelper.getGwSubDomainByType(NAME_SERVICE_TYPE_RM_INVENTORY);
        if (StringUtils.isEmpty(inventoryServiceDomain)) {
            throw new ApiException("Can not get inventory domain from name service.");
        }
        try {
            String inventoryServiceBaseUrl = "http://" + inventoryServiceDomain + "/api";
            log.info("inventoryServiceBaseUrl: {}", inventoryServiceBaseUrl);
            ApiClient apiClient = new ApiClient();
            apiClient.setBasePath(inventoryServiceBaseUrl);
            apiClient.setConnectTimeout(60*1000);
            apiClient.setReadTimeout(60*1000);
            apiClient.setWriteTimeout(60*1000);
            defaultApi = new DefaultApi(apiClient);
            return defaultApi;
        } catch (Exception e) {
            log.error("CommonProviderClient init error.", e);
            throw new ApiException("init CommonProviderClient failed");
        }
    }


    /**
     * the format of EvaluateEcsStockResponse
     *list: [class ECSStockInfo {
     *     account: <EMAIL>
     *     adequacyScore: 3
     *     CPU: 2
     *     cloudDiskQuantity: 17
     *     cloudStorageStocks: class CloudStorageStocks {
     *     [class CloudStorageStock {
     *     category: cloud_efficiency
     *     status: Available
     *     type: ebs
     *     }, class CloudStorageStock {
     *     category: cloud_auto
     *     status: Available
     *     type: ebs
     *     }, class CloudStorageStock {
     *     category: cloud_essd
     *     status: Available
     *     type: ebs
     *     }, class CloudStorageStock {
     *     category: cloud_ssd
     *     status: Available
     *     type: ebs
     *     }]
     *     }
     *     cpuArchitecture: X86
     *     enIQuantity: 2
     *     enginesInfo: [class ECSEngineInfo {
     *     name: mysql
     *     priority: 2
     *     }]
     *     healthScore: 4
     *     hotScore: 2
     *     instanceType: ecs.g6.large
     *     instanceTypeFamily: ecs.g6
     *     isInnerType: false
     *     memory: 8
     *     priority: 2
     *     quota: 500
     *     quotaContent:
     *     regionID: cn-beijing
     *     status: Available
     *     statusCategory: WithStock
     *     supplyScore: 3
     *     zoneID: cn-beijing-i
     *     }]
     */

    public List<String> evaluateEcsStock(InstanceLevel targetInstanceLevel, ReplicaResource replicaResource, Long cpuCores, Long memSizeGB) {
        try {
            // Retrieve ECS host information
            String requestId = RequestSession.getRequestId();
            EcsHost ecsHost = dBaasMetaService.getDefaultClient().getEcsHostByHostName(requestId, replicaResource.getReplica().getHostName(), null);
            boolean isArm = PodCommonSupport.isArm(targetInstanceLevel);

            // Create ECS filter and request object
            EvaluateEcsStockRequest evaluateEcsStockRequest = new EvaluateEcsStockRequest();
            ECSStock ecsFilter = new ECSStock();
            ecsFilter.setAccount(ecsHost.getUserName());
            ecsFilter.setRegionID(ecsHost.getRegion());
            ecsFilter.setZoneID(ecsHost.getZoneId());

            GormextList engines = new GormextList();
            engines.add(targetInstanceLevel.getService());
            ecsFilter.setEngines(engines);
            ecsFilter.setCPU(cpuCores);
            ecsFilter.setMemory(memSizeGB);
            ecsFilter.setCpuArchitecture(isArm ? "ARM" : "X86");
            ecsFilter.setStatus("Available");

            evaluateEcsStockRequest.setFilter(ecsFilter);
            evaluateEcsStockRequest.setPageNumber(1L);
            evaluateEcsStockRequest.setPageSize(100L);

            log.info("{} evaluateEcsStockRequest: {}", requestId, evaluateEcsStockRequest);

            // Call API to evaluate ECS stock
            EvaluateEcsStockResponse response = getDefaultApi().stockEcsV1EvaluatePost(evaluateEcsStockRequest);
            if (response.getCode() != HttpStatus.OK.value()) {
                log.error("Error evaluating ECS stock: {}", response.getMessage());
                return Collections.emptyList();
            }

            List<ECSStockInfo> information = response.getData().getList();
            log.info("{} evaluate ECS stock successfully and the response is [{}]", requestId, response);

            if (information == null || information.isEmpty() || response.getData().getTotalSize() == 0) {
                log.error("Resource not enough: No suitable ECS stock information found.");
                return Collections.emptyList();
            }

            // Convert the first method's output to a list of ECS class codes
            List<String> ecsClassCodes = information.stream()
                    .sorted(Comparator.comparingLong(ECSStockInfo::getPriority))
                    .map(ECSStockInfo::getInstanceType)
                    .collect(Collectors.toList());

            // List to store available class codes
            List<String> availableEcsClassCodes = new ArrayList<>();
            boolean foundAvailableEcsClassCode = false;

            // Perform real-time stock evaluation and return as soon as the first success is found
            for (int i = 0; i < ecsClassCodes.size(); i++) {
                String ecsClassCode = ecsClassCodes.get(i);

                EvaluateECSStockOnceRequest evaluateEcsStockOnceRequest = new EvaluateECSStockOnceRequest();
                evaluateEcsStockOnceRequest.setRequestId(requestId);
                evaluateEcsStockOnceRequest.setAccount(ecsHost.getUserName());
                evaluateEcsStockOnceRequest.setRegion(ecsHost.getRegion());
                evaluateEcsStockOnceRequest.setZone(ecsHost.getZoneId());
                evaluateEcsStockOnceRequest.setInstanceType(ecsClassCode);
                evaluateEcsStockOnceRequest.setEvaluateCount(1L);

                try {
                    // Call API to check real-time inventory
                    EvaluateECSStockOnceResponse responseOnce = getDefaultApi().stockEcsV1EvaluateoncePost(evaluateEcsStockOnceRequest);
                    boolean isStockAvailable = responseOnce != null && responseOnce.getData() != null &&
                            responseOnce.getCode() == 200 &&
                            "NoError".equals(responseOnce.getData().getCode()) &&
                            responseOnce.getData().getAvailableCount() != 0;
                    boolean isRequestInnerFail = responseOnce != null && responseOnce.getData() != null &&
                            "InnerEcsApiRequestFail".equals(responseOnce.getData().getCode());

                    log.info("{} ECS class code is {}. The response is {}", requestId, ecsClassCode, responseOnce);

                    if (isStockAvailable || isRequestInnerFail) {
                        availableEcsClassCodes.add(ecsClassCode);
                        foundAvailableEcsClassCode = true;
                        // Add remaining untested class codes
                        availableEcsClassCodes.addAll(ecsClassCodes.subList(i + 1, ecsClassCodes.size()));
                        break;
                    }
                } catch (Exception e) {
                    log.error("Error while evaluating ECS stock for class code " + ecsClassCode, e);
                }
            }

            if (!foundAvailableEcsClassCode) {
                // If no available class code is found after the loop, return an empty list
                return Collections.emptyList();
            }

            return availableEcsClassCodes;

        } catch (Exception e) {
            log.error("Exception occurred while evaluating ECS stock", e);
            return Collections.emptyList();
        }
    }

}

