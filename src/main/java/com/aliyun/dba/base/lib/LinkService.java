package com.aliyun.dba.base.lib;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.support.property.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Component
public class LinkService {

    private static final Logger logger = LoggerFactory.getLogger(NameServiceHelper.class);

    public Map<String, Object> extractRsp(String rspBody) {
        Map<String, Object> response = new HashMap();
        try {
            JSONObject rspObj = JSON.parseObject(rspBody);
            Integer status = rspObj.getInteger("status");
            if (status == 400 || status == 404) {
                response.put("errorCode", new Object[]{403, rspObj.getString("ErrorCodeName"),
                        rspObj.getString("ErrorCodeMessage")});
                return response;
            }
        } catch (Exception jsonEx) {
            logger.warn(jsonEx.getMessage(), jsonEx);
        }
        return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
    }

    public HashMap<String, Object> extractDataRsp(Object node) {
        HashMap<String, Object> rspDict = new HashMap<>();
        Field[] fields = node.getClass().getDeclaredFields();
        for(Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(node);
                rspDict.put(field.getName(), value);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return rspDict;
    }

}
