package com.aliyun.dba.base.lib;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.BakOwner;
import com.aliyun.apsaradb.dbaasmetaapi.model.BakOwnerListResult;
import com.aliyun.dba.base.common.InvokeHttpUtils;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.base.parameter.backup.*;
import com.aliyun.dba.base.response.backup.*;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * DBS网关
 */

@Service
@Slf4j
public class DbsGateWayService {

    private Map<String, String> dbsGateWayUrls = new HashMap<>();
    private Map<String, String> dbsAccessConfigs = new HashMap<>();
    private final static String DBS_ACCESS_KEY = "accessKey";
    private final static String DBS_ACCESS_SECRET = "accessSecret";
    private final static String DBS_ACCESS_SIGNATURE = "signature";
    private final static String DBS_ACCESS_TIMESTAMP = "timestamp";
    private final Integer DBS_GATEWAY_OWNER_TYPE = 517;

    @Resource
    private DBaasMetaService dBaasMetaService;

    @PostConstruct
    public void init() {
        try {
            BakOwnerListResult bakOwnerListResult = dBaasMetaService.getDefaultClient()
                    .listBakOwners_0("", DBS_GATEWAY_OWNER_TYPE);
            log.info("get dbs owners from db: {}", JSONObject.toJSONString(bakOwnerListResult));

            for (BakOwner owner: bakOwnerListResult.getItems()) {
                Type mapType = new TypeToken<HashMap<String, String>>(){}.getType();
                Map<String, String> urlConfig = JSONObject.parseObject(owner.getApiUrl(), mapType);
                if (urlConfig.containsKey("regionCode")) {
                    String regionId = urlConfig.get("regionCode");
                    String url = owner.getHostName() + "/service";
                    dbsGateWayUrls.put(regionId, url);
                    if(urlConfig.containsKey("access_key")&&urlConfig.containsKey("secret_key")){
                        String accessKey = urlConfig.get("access_key");
                        String secretKey = urlConfig.get("secret_key");
                        dbsAccessConfigs.put(getAccessConfigsMapKeyWithRegionId(regionId, DBS_ACCESS_KEY), accessKey);
                        dbsAccessConfigs.put(getAccessConfigsMapKeyWithRegionId(regionId, DBS_ACCESS_SECRET), secretKey);
                    }
                }
            }
        } catch (Exception e) {
            log.error("init dbs gateways error: {}", e.getMessage(), e);
        }
    }


    // 备份数据恢复API文档
    // https://yuque.antfin-inc.com/docs/share/f4000ccf-33bf-4075-9136-41ef02003f20?#SPuAl

    /**
     * 查看备份恢复需要的备份集
     */
    public DescribeRestoreBackupSetResponse describeRestoreBackupSet(DescribeRestoreBackupSetParam paramObj) throws BaseServiceException {
        final String action = "DescribeRestoreBackup";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), DescribeRestoreBackupSetResponse.class);
    }

    /**
     * 构造dbs请求
     */
    public DescribeRestoreBackupSetParam describeRestoreBackupSetBuilder(Map<String, String> params,String bid) throws BaseServiceException {
        String uid = params.get("uid");
        String region = params.get("regionid");
        String backupSetId = params.get("backupsetid");
        String requestId = params.get("requestid");
        String accessId = params.get("accessid");
        return DescribeRestoreBackupSetParam.builder()
                        .requestId(requestId)
                        .accessId(accessId)
                        .callerBid(bid)
                        .userId(uid)
                        .instanceName(null)
                        .instanceRegion(region)
                        .sceneType(null)
                        .UnifyBackupSetId(backupSetId)
                        .regionCode(region)
                        .build();
    }

    /**
     * 获取docker on ecs架构快照（走单独的逻辑）
     */
    public String describeDockerRestoreSnapshot(Map<String, String> params) throws BaseServiceException,RdsException {
        DescribeRestoreBackupSetParam paramObj = this.describeRestoreBackupSetBuilder(params,params.get("user_id"));
        final String action = "DescribeRestoreBackup";
        Map<String, Object> result = doAction(action, paramObj);
        if (result.isEmpty()) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        Map<String, Object> data =(Map) result.get("Data");
        if (data.isEmpty()) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        Map<String, Object> backupSetInfo =(Map) (data.get("BackupSetInfo"));
        if (backupSetInfo.isEmpty()) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        Map<String, Object> extraInfo = (Map) backupSetInfo.get("ExtraInfo");
        if (extraInfo.isEmpty()) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        List<Map> slave = null;
        for (String key : extraInfo.keySet()) {
             slave = (List) extraInfo.get(key);
        }
        if (slave.isEmpty()) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        Map<String,String> slaveInfo = slave.get(0);
        if (slaveInfo.isEmpty()) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        String snapshot = slaveInfo.get("SNAPSHOT_ID");
        if (StringUtils.isBlank(snapshot)) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        return snapshot;
    }

    /**
     * 查看备份恢复需要的日志文件
     */
    public DescribeRestoreArchiveLogResponse describeRestoreArchiveLog(DescribeRestoreArchiveLogParam paramObj) throws BaseServiceException {
        final String action = "DescribeRestoreArchiveLogFiles";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), DescribeRestoreArchiveLogResponse.class);
    }


    // 备份数据锁定/解锁API文档
    // https://yuque.alibaba-inc.com/dbs/lbmqma/twxhki#PkWVq

    /**
     * 备份集锁定/解锁
     */
    public LockRestoreFileResponse lockRestoreBackupSet(LockRestoreFileParam paramObj) throws BaseServiceException {
        final String action = "LockRestoreBackup";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), LockRestoreFileResponse.class);
    }

    /**
     * 日志文件锁定/解锁
     */
    public LockRestoreFileResponse lockRestoreArchiveLog(LockRestoreFileParam paramObj) throws BaseServiceException {
        final String action = "LockRestoreArchiveLogFiles";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), LockRestoreFileResponse.class);
    }

    /**
     * 查询备份集详情
     *   不需要传入ReplicaSetName
     */
    public DescribeRestoreLiveMountBackupResponse describeRestoreLiveMountBackup(DescribeRestoreLiveMountBackupParam paramObj) throws BaseServiceException {
        final String action = "DescribeRestoreLiveMountBackup";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), DescribeRestoreLiveMountBackupResponse.class);
    }

    public DescribeFileSystemResponse describeFileSystem(DescribeFileSystemParam paramObj) throws Exception {
        final String action = "DescribeFileSystem";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), DescribeFileSystemResponse.class);
    }

    public DescribeBackupStrategyResponse describeBackupStrategy(DescribeBackupStrategyParam paramObj) throws Exception {
        final String action = "DescribeBackupStrategy";
        Map<String, Object> result = doAction(action, paramObj);
        List<DescribeBackupStrategyResponse> responses = JSON.parseArray(JSON.toJSONString(result.get("Data")), DescribeBackupStrategyResponse.class);
        if (Objects.nonNull(responses) && !responses.isEmpty()) {
            return responses.get(0);
        }
        return null;
    }

    public DescribeInstanceCrossBackupPolicyResponse describeInstanceCrossBackupPolicy(DescribeInstanceCrossBackupPolicyParam paramObj) throws Exception {
        final String action = "DescribeInstanceCrossBackupPolicy";
        Map<String, Object> result = doAction(action, paramObj);
        List<DescribeInstanceCrossBackupPolicyResponse> responses = JSON.parseArray(JSON.toJSONString(result.get("Data")), DescribeInstanceCrossBackupPolicyResponse.class);
        if (Objects.nonNull(responses) && !responses.isEmpty()) {
            return responses.get(0);
        }
        return null;
    }

    public DescribeBackupPolicyResponse describeBackupPolicy(DescribeBackupPolicyParam paramObj) throws Exception {
        final String action = "DescribeBackupPolicy";
        Map<String, Object> result = doAction(action, paramObj);
        List<DescribeBackupPolicyResponse> responses = JSON.parseArray(JSON.toJSONString(result.get("Data")), DescribeBackupPolicyResponse.class);
        if (Objects.nonNull(responses) && !responses.isEmpty()) {
            return responses.get(0);
        }
        return null;
    }

    public String describeDumpStrategyGray(DescribeDumpStrategyGrayParam paramObj) throws Exception {
        final String action = "DescribeDumpStrategyGray";
        Map<String, Object> result = doAction(action, paramObj);
        return String.valueOf(result.get("Data"));
    }

    public DescribeCrossRegionMigrateNewArchStatusResponse describeCrossRegionMigrateNewArchStatus(DescribeCrossRegionMigrateNewArchStatusParam paramObj) throws BaseServiceException {
        final String action = "DescribeCrossRegionMigrateNewArchStatus";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), DescribeCrossRegionMigrateNewArchStatusResponse.class);
    }

    /**
     * 将Object对象里面的属性和值转化成Map对象
     *
     * @param obj
     * @return
     * @throws IllegalAccessException
     */
    private Map<String, Object> objectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            if (field.get(obj) != null && StringUtils.isNotBlank(field.get(obj).toString())) {
                map.put(field.getName(), field.get(obj));
            }
        }
        return map;
    }

    private Map<String, Object> doAction(String action, Object requestObj) throws BaseServiceException {
        try {
            Map<String, Object> requestParam = objectToMap(requestObj);
            requestParam.put("Action", action);
            addAuthInfo(requestParam);
            return doHttpAction(requestParam);
        } catch (BaseServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error(action + " exception: ", e);
            throw new BaseServiceException(e.getMessage());
        }
    }

    private Map<String, Object> doHttpAction(Map<String, Object> requestParam) throws BaseServiceException {
        String regionId = requestParam.get("regionCode").toString();
        String dbsUrl = dbsGateWayUrls.get(regionId);
        if (StringUtils.isEmpty(dbsUrl)) {
            log.error("can't find dbs gateway for regionId: {}, urls: {}", regionId, dbsGateWayUrls);
            throw new BaseServiceException("can't find dbs gateway!");
        }

        Map<String, Object> result = InvokeHttpUtils.invokeRestService(dbsUrl, requestParam, "POST", MediaType.APPLICATION_FORM_URLENCODED);
        log.info("dbs http request, url {}, params {}, result {}", dbsUrl, JSONObject.toJSONString(requestParam), JSONObject.toJSONString(result));
        if (!"Success".equals(LangUtil.getString(result.get("Code")))) {
            throw new BaseServiceException(LangUtil.getString(result.get("Code")), LangUtil.getString(result.get("Message")));
        }
        return result;
    }

    /**
     * 该方法会向requestParam中增加认证信息
     * @param requestParam
     */
    private void addAuthInfo(Map<String, Object> requestParam) {
        String regionId = requestParam.get("regionCode").toString();
        String accessKeyWithRegion = getAccessConfigsMapKeyWithRegionId(regionId, DBS_ACCESS_KEY);
        String accessSecretWithRegion = getAccessConfigsMapKeyWithRegionId(regionId, DBS_ACCESS_SECRET);
        if (dbsAccessConfigs.containsKey(accessKeyWithRegion) && dbsAccessConfigs.containsKey(accessSecretWithRegion)) {
            requestParam.put(DBS_ACCESS_KEY, dbsAccessConfigs.get(accessKeyWithRegion));
            requestParam.put(DBS_ACCESS_TIMESTAMP, System.currentTimeMillis());
            requestParam.put(DBS_ACCESS_SIGNATURE, createHmacSha1(requestParam, dbsAccessConfigs.get(accessSecretWithRegion)));
        } else {
            log.warn("use skipAuth, can't find dbs access configs for regionId: {}, accessConfigs: {}", regionId, dbsAccessConfigs);
            requestParam.put("__skipAuth", 1);
        }
    }

    // 签名方法
    private static String createHmacSha1(Map<String, Object> requestParam, String secret) {

        try {
            String data = String.format("/service?%s", requestParam.entrySet()
                    .stream()
                    .filter(entry -> entry.getValue() != null) // 跳过value为null的条目
                    .sorted(Map.Entry.comparingByKey()) // 按key排序
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&")));
            log.info("createHmacSha1 data : {}", data);
            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKeySpec sec = new SecretKeySpec(secret.getBytes(), "HmacSHA1");
            mac.init(sec);
            byte[] digest = mac.doFinal(data.getBytes());
            return new String(new Hex().encode(digest), "UTF-8");
        } catch (NoSuchAlgorithmException | InvalidKeyException | UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    private static String getAccessConfigsMapKeyWithRegionId(String regionId, String key) {
        return String.format("%s-%s", regionId, key);
    }
}

