package com.aliyun.dba.base.lib;

import com.aliyun.apsaradb.gdnmetaapi.ApiClient;
import com.aliyun.apsaradb.gdnmetaapi.Configuration;
import com.aliyun.apsaradb.gdnmetaapi.api.DefaultApi;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Service
public class GdnMetaService {
    private static final Logger logger = LoggerFactory.getLogger(DBaasMetaService.class);
    public static final int CONNECT_TIMEOUNT = 1200000;
    public static final String GDN_SERIVCE_REGION_ID = "cn-hangzhou";

    @Resource
    private NameServiceHelper nameServiceHelper;

    private DefaultApi gdnMetaApi;

    @PostConstruct
    public void init() {
        try {
            ApiClient defaultApiClient = Configuration.getDefaultApiClient();
            defaultApiClient.setConnectTimeout(CONNECT_TIMEOUNT);
            String gwSubDomain = nameServiceHelper.getRegionGwSubDomainByType(null, "GDN_META_API");
            if (StringUtils.isEmpty(gwSubDomain)) {
                logger.error("Can not get GDN_META_API gateway sub domain from name service.");
            }
            defaultApiClient.setBasePath("http://" + gwSubDomain + "/api/v1.0");
            gdnMetaApi = new DefaultApi();
            gdnMetaApi.setApiClient(defaultApiClient);
        } catch (Exception e) {
            logger.error("GdnMetaClient init error.", e);
        }

    }

    public DefaultApi getClient() {
        return this.gdnMetaApi;
    }
}
