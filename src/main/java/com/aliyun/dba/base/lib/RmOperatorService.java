package com.aliyun.dba.base.lib;


import com.alicloud.apsaradb.k8s.resmanager.Client;
import com.aliyun.apsaradb.dbaasmetaapi.ApiClient;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.Configuration;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class RmOperatorService {
    private static final Logger logger = LoggerFactory.getLogger(RmOperatorService.class);

    @Resource
    private NameServiceHelper nameServiceHelper;

    private Client rmOperatorClient;

    @PostConstruct
    public void init() {
        try {
            String nameServiceUrl = nameServiceHelper.getNameServiceUrl();
            rmOperatorClient = Client.NewClient(nameServiceUrl);
        } catch (Exception e) {
            logger.error("DbaasMetaClient init error.", e);
        }
    }

    public Client getRmOperatorClient() {
        return rmOperatorClient;
    }
}
