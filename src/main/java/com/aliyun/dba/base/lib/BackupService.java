package com.aliyun.dba.base.lib;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.aliyun.dba.base.common.InvokeHttpUtils;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.base.parameter.backup.*;
import com.aliyun.dba.base.response.backup.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/10/29 11:30
 * 数据备份基础服务
 * 接口文档 https://yuque.antfin-inc.com/apy94c/wymqqc/qxywtq#RMszs
 */


@Service
@Slf4j
public class BackupService {

    private static final Logger logger = LoggerFactory.getLogger(BackupService.class);

    private String backupBaseUrl;

    @Resource
    private NameServiceHelper nameServiceHelper;

    @Resource
    private DBaasMetaService dBaasMetaService;

    @PostConstruct
    private void init() {
        try{
            // 通过名字服务获取domain, 线上环境使用
            String gwSubDomain = nameServiceHelper.getGwSubDomainByType("BACKUP_API");
            backupBaseUrl = "http://" + gwSubDomain + "/services";
        }
        catch(Exception e){
            logger.error("BackupService init error.", e);
        }
    }

    /**
     * 获取实例的备份集明细
     *
     * @param paramObj
     * @return
     * @throws BaseServiceException
     */
    public GetBackupSetResponse getBackupSet(BackupSetParam paramObj) throws BaseServiceException {
        final String action = "GetBackupSet";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), GetBackupSetResponse.class);
    }

    /**
     * 创建用户OSS备份恢复元数据
     * @param paramObj
    */
    public OssBakRestoreResponse createOssBakRestore(CreateOssBakRestoreParam paramObj) throws BaseServiceException {
        final String action = "CreateOssBakRestore";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), OssBakRestoreResponse.class);
    }

    /**
     * 获取OSS备份恢复任务
     * @param paramObj
     */
    public List<UserOssBackupFileRecordResponse> describeBakOssRestoreRecords(OssBakRestoreParam paramObj ) throws BaseServiceException {
        final String action = "DescBakOssRestoreRecords";
        Map<String, Object> result = doAction(action, paramObj);
        Map<String, Object> items = (Map<String, Object>)result.get("Data");
        return JSON.parseArray(JSON.toJSONString(items.get("Items")), UserOssBackupFileRecordResponse.class);
    }

    /**
     * 更新OSS备份恢复状态
     * @param paramObj
     */
    public OssBakRestoreResponse updateOssBakRestoreStatus(OssBakRestoreStatusParam paramObj) throws BaseServiceException {
        final String action = "UpdateOssBakRestoreStatus";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), OssBakRestoreResponse.class);
    }

    /**
     * 更新OSS备份恢复任务
     * @param paramObj
     */
    public OssBakRestoreResponse updateOssBakRestoreTask(OssBakRestoreTaskParam paramObj) throws BaseServiceException {
        final String action = "UpdateOssBakRestoreTask";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), OssBakRestoreResponse.class);
    }

    /**
     * 更新OSS备份恢复备份集id
     * @param paramObj
     */
    public OssBakRestoreResponse updateOssBakRestoreBackupSetId(OssBakRestoreBackupSetIdParam paramObj) throws BaseServiceException {
        final String action = "UpdateOssBakRestoreBackupSetId";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), OssBakRestoreResponse.class);
    }
    /**
     * 更新OSS备份恢复备份集id
     * @param paramObj
     */
    public OssBakRestoreResponse UpdateUserBakFile(OssUserBakFileParam paramObj) throws BaseServiceException {
        final String action = "UpdateUserBakFile";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), OssBakRestoreResponse.class);
    }

    /**
     * 标记删除用户Oss备份
     * @param paramObj
     */
    public OssBakRestoreResponse deleteOssBakRestore(OssBakRestoreParam paramObj) throws BaseServiceException {
        final String action = "DeleteOssBakRestore";
        Map<String, Object> result = doAction(action, paramObj);
        return JSON.parseObject(JSON.toJSONString(result.get("Data")), OssBakRestoreResponse.class);
    }

    /**
     * 将Object对象里面的属性和值转化成Map对象
     *
     * @param obj
     * @return
     * @throws IllegalAccessException
     */
    private Map<String, Object> objectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            if (field.get(obj) != null && StringUtils.isNotBlank(field.get(obj).toString())) {
                map.put(field.getName(), field.get(obj));
            }
        }
        return map;
    }

    private Map<String, Object> doAction(String action, Object requestObj) throws BaseServiceException {
        try {
            Map<String, Object> requestParam = objectToMap(requestObj);
            requestParam.put("Action", action);
            return doHttpAction(requestParam);
        } catch (BaseServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error(action + " exception: ", e);
            throw new BaseServiceException(e.getMessage());
        }
    }

    private Map<String, Object> doHttpAction(Map<String, Object> requestParam) throws BaseServiceException {
        Map<String, Object> result = InvokeHttpUtils.invokeRestService(backupBaseUrl, requestParam, "POST", MediaType.APPLICATION_FORM_URLENCODED);
        if (!"200".equals(LangUtil.getString(result.get("Code")))) {
            throw new BaseServiceException(LangUtil.getString(result.get("Code")), LangUtil.getString(result.get("Message")));
        }
        return result;
    }
}

