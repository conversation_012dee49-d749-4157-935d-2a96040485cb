package com.aliyun.dba.base.lib;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.workflowapi.ApiClient;
import com.aliyun.apsaradb.workflowapi.ApiException;
import com.aliyun.apsaradb.workflowapi.Configuration;
import com.aliyun.apsaradb.workflowapi.api.OperateControllerApi;
import com.aliyun.apsaradb.workflowapi.api.TaskQueueControllerApi;
import com.aliyun.apsaradb.workflowapi.common.WorkflowClient;
import com.aliyun.dba.base.common.InvokeHttpUtils;
import com.aliyun.dba.base.common.exception.CheckException;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDateTimeUtils;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.apsaradb.workflowapi.common.WorkflowClient.CLIENT_MODE_NAME_SERVICE;


@Service
public class WorkFlowService {
    private static Logger logger = LoggerFactory.getLogger(WorkFlowService.class);

    @Value("${rds.name-service.base-url:no config}")
    private String nameServiceUrl;
    private static final Long FAILED_TASK_ID = 0L;
    private static final int connectTimeout = 15;
    private static final int readTimeout = 15;

    private volatile WorkflowClient workflowClient;
    private volatile OperateControllerApi operateApi;
    private volatile TaskQueueControllerApi taskQueueControllerApi;

    private String workflowBaseUrl;
    public static final int CONNECT_TIMEOUNT = 1200000;

    /**
     * 任务优先级
     */
    public static final int TASK_PRIORITY_COMMON = 0;  //普通等级

    public static final int TASK_PRIORITY_VIP = 1;     //优先级
    @Resource
    private NameServiceHelper nameServiceHelper;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private ReplicaSetService replicaSetService;


    private String getWorkflowAddress() {
        if (workflowBaseUrl == null) {
            String gwSubDomain = nameServiceHelper.getGwSubDomainByType("WORKFLOW_ENGINE_AGENT");
            if (StringUtils.isEmpty(gwSubDomain)) {
                throw new CheckException("Can not get workflowClient gateway sub domain from name service.");
            }
            workflowBaseUrl = "http://" + gwSubDomain;
        }
        return workflowBaseUrl;
    }

    public WorkflowClient getDefaultClient() throws Exception {
        if (this.workflowClient == null) {
            synchronized (WorkFlowService.class) {
                if (workflowClient == null) {
                    try {
                        workflowClient = new WorkflowClient(nameServiceUrl, connectTimeout, readTimeout, CLIENT_MODE_NAME_SERVICE);
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                        throw new Exception("init workflow client failed");
                    }
                }
            }
        }
        return this.workflowClient;
    }

    public TaskQueueControllerApi getTaskQueueControllerApi() {
        if (this.taskQueueControllerApi == null) {
            synchronized (WorkFlowService.class) {
                if (taskQueueControllerApi == null) {
                    try {
                        String workflowAgentAddress = getWorkflowAddress();
                        ApiClient defaultApiClient = Configuration.getDefaultApiClient();
                        defaultApiClient.setBasePath(workflowAgentAddress);
                        taskQueueControllerApi = new TaskQueueControllerApi();
                        taskQueueControllerApi.setApiClient(defaultApiClient);
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                        throw new CheckException("init taskQueueController API failed");
                    }
                }
            }
        }
        return this.taskQueueControllerApi;
    }


    public OperateControllerApi getOperateControllerApi() {
        if (this.operateApi == null) {
            synchronized (WorkFlowService.class) {
                if (operateApi == null) {
                    try {
                        String workflowAgentAddress = getWorkflowAddress();
                        ApiClient defaultApiClient = Configuration.getDefaultApiClient();
                        defaultApiClient.setBasePath(workflowAgentAddress);
                        operateApi = new OperateControllerApi();
                        operateApi.setApiClient(defaultApiClient);
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                        throw new CheckException("init OperateController API failed");
                    }
                }
            }
        }
        return this.operateApi;
    }


    /**
     * 下发任务到ReplicaSet
     *
     * @param replicaSet
     * @param taskKey
     * @param parameter
     * @param priority
     * @return
     * @throws Exception
     */
    public Object dispatchTask(ReplicaSet replicaSet, String taskKey, String parameter, Integer priority) throws Exception {
        return dispatchTask("custins", replicaSet.getName(), replicaSet.getService(), taskKey, parameter, priority);
    }


    public Object dispatchTask(String targetType, String targetId, String domain, String taskKey, String parameter, Integer priority) throws Exception {
        Map<String, Object> addTaskResult = getDefaultClient().addNewTask(
                targetType, targetId, domain, taskKey,
                parameter, 1, priority, 0, "", "");
        if (addTaskResult == null) {
            logger.error("call workflow api to add new task failed, result it empty");
            throw new Exception("dispatchTask failed");
        }
        if (!"SUCCESS".equals(addTaskResult.get("code"))) {
            logger.error("call workflow api to add new task failed, msg is {}", addTaskResult);
            throw new Exception("dispatchTask failed");
        }
        return ((Map<String, Object>) addTaskResult.get("data")).get("taskId");
    }

    /**
     * 通过Post方式发起请求，将参数放在form-data中，避免参数过长产生的异常
     */
    public Object dispatchTaskByPost(String targetType, String targetId, String domain, String taskKey, String parameter, Integer priority, String requestId) throws Exception {
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("targetType", targetType);
        requestParam.put("targetId", targetId);
        requestParam.put("domain", domain);
        requestParam.put("taskKey", taskKey);
        requestParam.put("parameter", parameter);
        requestParam.put("requestId", requestId);
        requestParam.put("priority", priority);

        String dispatchUrl = getWorkflowAddress() +  "/api/v1/task/add";
        Map<String, Object> result = InvokeHttpUtils.invokeRestService(dispatchUrl, requestParam, "POST", MediaType.MULTIPART_FORM_DATA);
        if (Objects.isNull(result)) {
            logger.error("call workflow api to add new task failed, result it empty");
            throw new Exception("dispatchTask failed");
        }
        if (!"SUCCESS".equals(result.get("code"))) {
            logger.error("call workflow api to add new task failed, msg is {}", result);
            throw new Exception("dispatchTask failed");
        }
        return ((Map<String, Object>) result.get("data")).get("taskId");
    }

    public Object modifyTaskRecoverTime(String requestId, String targetId, String targetType, String recoverMode, String recoverTime, String taskId) throws ApiException {
        Map<String, Object>  result = (Map<String, Object>) getOperateControllerApi().modifyRecoverTimeUsingPOST(recoverMode, requestId, null,
                targetId, targetType, taskId, recoverTime);
        return result;
    }

    public Object queryTaskQueueList(String requestId, List<Long> taskIds) throws ApiException {
        Map<String, Object>  result = (Map<String, Object>) getTaskQueueControllerApi().queryTaskQueueListUsingGET(requestId, taskIds, null,
                null, null, null, null,
                null,null,null,null,
                null,null,null,null,null);
        return result;
    }


    /**
     * 获取实例处于暂停状态的任务列表
     * */
    public JSONObject getTaskByStatus(String requestId, String replicaSetName, String taskStatus) throws RdsException {
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("requestId", requestId);
        requestParam.put("domain", "mysql");
        requestParam.put("taskStatusIn", taskStatus);
        requestParam.put("targetId", replicaSetName);
        String submitUrl = getWorkflowAddress() + "/api/v1/task/getList?requestId={requestId}&domain={domain}&taskStatusIn={taskStatusIn}&targetId={targetId}";
        Map<String, Object> result = InvokeHttpUtils.invokeRestGetService(submitUrl, requestParam);
        logger.info(String.valueOf(result));
        if (!"SUCCESS".equalsIgnoreCase((String) result.get("code"))) {
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
        return JSON.parseObject(JSON.toJSONString(result.get("data")));
    }


    /**
     * 获取实例未完成的任务
     * */
    public JSONObject getUnfinishedTask(String requestId, String replicaSetName) throws RdsException {
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("requestId", requestId);
        requestParam.put("domain", "mysql");
        requestParam.put("taskStatusIn", "HUMAN_PROCESSING,PAUSE,RUNNING,WAITING");
        requestParam.put("targetId", replicaSetName);
        String submitUrl = getWorkflowAddress() + "/api/v1/task/getList?requestId={requestId}&domain={domain}&taskStatusIn={taskStatusIn}&targetId={targetId}";
        Map<String, Object> result = InvokeHttpUtils.invokeRestGetService(submitUrl, requestParam);
        logger.info(String.valueOf(result));
        if (!"SUCCESS".equalsIgnoreCase((String) result.get("code"))) {
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
        return JSON.parseObject(JSON.toJSONString(result.get("data")));
    }

    /**
     * 通过OrderId判断实例是否有任务在运行
     * */
    public boolean isTaskAlreadyRunning(String orderId, String replicaSetName) throws RdsException {

        JSONObject tasks = getUnfinishedTask(RequestSession.getRequestId(), replicaSetName);
        JSONArray taskList = tasks.getJSONArray("taskList");
        for (int i = 0; i < taskList.size(); i++) {
            JSONObject task = taskList.getJSONObject(i);
            JSONObject param = JSON.parseObject(task.getString("parameter"));
            String taskOrderId = param.getString(ParamConstants.ORDERID);
            if (taskOrderId != null && taskOrderId.equalsIgnoreCase(orderId)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 判断实例是否有未完成的任务，指定TaskKey
     *
     * @param requestId
     * @param replicaSetName
     * @param taskKey
     * @return
     * @throws Exception
     */
    public boolean isTaskExist(String requestId, String replicaSetName, String taskKey) throws Exception {
        JSONObject runningTask = getUnfinishedTask(requestId, replicaSetName);
        JSONArray taskList = runningTask.getJSONArray("taskList");
        if (taskList.size() == 0) {
            return false;
        }
        for (int index = 0; index < taskList.size(); index++) {
            JSONObject task = taskList.getJSONObject(index);
            if (StringUtils.equals(task.getString("taskKey"), taskKey)) {
                return true;
            }
        }
        return false;
    }


    /**
    * 判断实例是否有未完成的任务
    **/
    public boolean isTaskExist(String requestId, String replicaSetName) throws Exception {
        JSONObject runningTask = getUnfinishedTask(requestId, replicaSetName);
        JSONArray taskList = runningTask.getJSONArray("taskList");
        return taskList.size() > 0;
    }

    /**
     * 修改运维时间切换 & 暂停状态的任务的恢复时间
     * */
    public void modifyPauseTask(String requestId, String replicaSetName) throws Exception {
        JSONObject pauseTasks = getTaskByStatus(requestId, replicaSetName, "PAUSE");
        JSONArray taskList = pauseTasks.getJSONArray("taskList");
        List<Map<String, Object>> recoverTimes = calculateMaintainRecoverTime(requestId, replicaSetName);
        String recoverTime = JSON.toJSONString(recoverTimes);
        for (int index = 0; index < taskList.size(); index++) {
            try {
                JSONObject task = taskList.getJSONObject(index);
                String taskId = task.getString("id");
                String extInfo = task.getJSONObject("workflow").getJSONObject("instanceExtend").getString("extInfo");
                String mode = JSON.parseObject(extInfo).getJSONObject("switch_info").getString("mode");
                if (CustinsSupport.MAINTAIN_MODE.equalsIgnoreCase(mode)) {
                    modifyTaskRecoverTime(requestId, replicaSetName, "custins",
                            CustinsSupport.MAINTAIN_MODE, recoverTime, taskId);
                }
            } catch (Exception e) {
                logger.error("modify maintain time failed.", e);
            }
        }
    }

    /**
     * 根据运维时间计算恢复数据列表，会往后算5天
     * */
    public List<Map<String, Object>> calculateMaintainRecoverTime(String requestId, String replicaSetName) throws Exception {

        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSetName, null);

        Date now = new Date();
        Date toDayStartTime = PodDateTimeUtils.getTodayStartTime();
        Calendar start = Calendar.getInstance();
        start.setTime(toDayStartTime);
        start.add(Calendar.HOUR, replicaSet.getMaintainStart().getHour());
        start.add(Calendar.MINUTE, replicaSet.getMaintainStart().getMinute());
        start.add(Calendar.SECOND, replicaSet.getMaintainStart().getSecond());

        Calendar end = Calendar.getInstance();
        end.setTime(toDayStartTime);
        end.add(Calendar.HOUR, replicaSet.getMaintainEnd().getHour());
        end.add(Calendar.MINUTE, replicaSet.getMaintainEnd().getMinute());
        end.add(Calendar.SECOND, replicaSet.getMaintainEnd().getSecond());

        // 兼容startTime = 23:00:00，endTime = 00:00:00的场景
        if (end.before(start)) {
            end.add(Calendar.DATE, 1);
        }

        if (now.after(end.getTime())) {
            //当前时间在当天运维时间点后，则取第二天的运维时间点
            start.add(Calendar.DATE, 1);
            end.add(Calendar.DATE, 1);
        }
        List<Map<String, Object>> resultList = new ArrayList<>();

        //多给几天时间
        for (int i = 0; i < 5; i++) {
            Map<String, Object> timeMap = new HashMap<>();
            timeMap.put("start_time", PodDateTimeUtils.convertDateToGMT(start.getTime(), PodDateTimeUtils.ISO8601_DATE_FORMAT));
            timeMap.put("end_time", PodDateTimeUtils.convertDateToGMT(end.getTime(), PodDateTimeUtils.ISO8601_DATE_FORMAT));
            resultList.add(timeMap);
            start.add(Calendar.DATE, 1);
            end.add(Calendar.DATE, 1);
        }
        return resultList;
    }

}
