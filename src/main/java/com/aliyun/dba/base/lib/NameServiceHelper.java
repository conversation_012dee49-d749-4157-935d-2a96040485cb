package com.aliyun.dba.base.lib;

import NameServiceCommon.NameServiceClient;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class NameServiceHelper {
    private static final Logger logger = LoggerFactory.getLogger(NameServiceHelper.class);

    @Resource
    private Environment env;

    @Resource
    private DBaasMetaService dBaasMetaService;

    private String globalNameServiceUrl;

    public String getNameServiceUrl() {
        return env.getProperty("rds.name-service.base-url");
    }

    public String getGwSubDomainByType(String serviceType) {
        if (StringUtils.isEmpty(serviceType)) {
            return null;
        }
        String nameServiceUrl = getNameServiceUrl();
        if (StringUtils.isEmpty(nameServiceUrl)) {
            logger.warn("Unit name service config is empty!");
            return null;
        }

        NameServiceClient nsc = new NameServiceClient(nameServiceUrl, 3, 3);
        return this.getNameServiceGwSubDomain(nsc, serviceType);
    }

    public String getRegionGwSubDomainByType(String regionId, String serviceType) throws ApiException {
        if (StringUtils.isEmpty(serviceType)) {
            return null;
        }
        if (null == globalNameServiceUrl) {
            globalNameServiceUrl = dBaasMetaService.getDefaultClient().listConfigs(
                    "GET_GLOBAL_NAME_SERVICE_REQUEST_ID", "NS_ADDR_CENTRAL").getItems().get(0).getValue();
        }
        if (StringUtils.isEmpty(globalNameServiceUrl)) {
            logger.warn("Global name service config is empty!");
            return null;
        }

        boolean isDevEnv = null != regionId && regionId.contains("atp");
        NameServiceClient nsc;
        // 测试环境只用 unitCode, region 是一样的
        if (isDevEnv) {
            nsc = new NameServiceClient(globalNameServiceUrl, regionId, null, 3, 3);
        } else {
            nsc = new NameServiceClient(globalNameServiceUrl, null, regionId, 3, 3);
        }
        return this.getNameServiceGwSubDomain(nsc, serviceType);
    }

    private String getNameServiceGwSubDomain(NameServiceClient nameServiceClient, String serviceType) {
        String version = null;
        String bizType = null;
        String region = null;
        String site = null;
        String tags = null;
        NameServiceModels.Service service = nameServiceClient.findFirstAvailableService(
                serviceType, version, bizType, region, site, tags);
        if (service == null || StringUtils.isEmpty(service.getGwSubDomain())) {
            return null;
        }

        return service.getGwSubDomain();
    }

}
