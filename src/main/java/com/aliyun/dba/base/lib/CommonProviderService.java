package com.aliyun.dba.base.lib;

import com.aliyun.apsaradb.activityprovider.ApiClient;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.Configuration;
import com.aliyun.apsaradb.activityprovider.api.DefaultApi;
import io.opentracing.contrib.okhttp3.TracingInterceptor;
import io.opentracing.util.GlobalTracer;
import lombok.var;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
public class CommonProviderService {

    private final static Logger logger = LoggerFactory.getLogger(CommonProviderService.class);

    @Resource
    private NameServiceHelper nameServiceHelper;

    private final static String NAME_SERVICE_TYPE_COMMON_PROVIDER_SERVICE = "COMMON_ACTIVITY_PROVIDER";

    private volatile DefaultApi defaultApi;


    @PostConstruct
    public void init() {
        //应用启动时候尝试一次初始化，如果失败了后续还可以继续获取
        try {
            defaultApi = initClientApi();
        } catch (Exception e) {
            logger.error("init CommonProviderClient failed when start application");
        }
    }

    public DefaultApi getDefaultApi() throws ApiException {
        if (defaultApi == null) {
            synchronized (this) {
                if (defaultApi == null) {
                    defaultApi = initClientApi();
                }
            }
        }
        return defaultApi;
    }


    private DefaultApi initClientApi() throws ApiException {
        // 通过名字服务获取domain, 线上环境使用
        String address = nameServiceHelper.getGwSubDomainByType(NAME_SERVICE_TYPE_COMMON_PROVIDER_SERVICE);
        if (StringUtils.isEmpty(address)) {
            throw new ApiException("Can not get CommonProviderClient gateway address from name service.");
        }
        try {
            var tracer = GlobalTracer.get();
            Dispatcher dispatcher = new Dispatcher();
            dispatcher.setMaxRequests(512);
            dispatcher.setMaxRequestsPerHost(512);
            OkHttpClient okHttpClient = TracingInterceptor.addTracing(new OkHttpClient().newBuilder()
                    .connectTimeout(5, TimeUnit.MINUTES)
                    .readTimeout(5, TimeUnit.MINUTES)
                    .writeTimeout(5, TimeUnit.MINUTES)
                    .dispatcher(dispatcher), tracer);
            // 通过名字服务获取domain, 线上环境使用
            ApiClient defaultApiClient = Configuration.getDefaultApiClient();
            defaultApiClient.setHttpClient(okHttpClient);
            defaultApiClient.setBasePath("http://" + address + "/api/v1");
            defaultApiClient.setHttpClient(okHttpClient);
            defaultApi = new DefaultApi();
            defaultApi.setApiClient(defaultApiClient);
            logger.info("init commonProviderClient success");
            return defaultApi;
        } catch (Exception e) {
            logger.error("CommonProviderClient init error.", e);
            throw new ApiException("init CommonProviderClient failed");
        }
    }

}
