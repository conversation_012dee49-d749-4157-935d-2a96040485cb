package com.aliyun.dba.base.lib;

import com.aliyun.dba.adb_vip_manager_client.ApiClient;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.api.VpcMappingApi;
import com.aliyun.dba.adb_vip_manager_client.auth.HttpBasicAuth;
import com.aliyun.dba.resource.dataobject.BakOwnerDO;
import com.aliyun.dba.resource.service.ResourceService;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Configuration
public class LinkServiceConfig {
    private static Logger logger = LoggerFactory.getLogger(CommonProviderService.class);

    private String basePath;

    private String userName;

    private String userPwd;

    @Resource
    private Environment env;

    @Resource
    private NameServiceHelper nameServiceHelper;

    @Autowired
    private ResourceService resourceService;

    public static String NAME_SERVICE_VIP_MANAGER_SERVICE = "VIP_MANAGER_SRV";
    public static Integer VIP_MANAGER_OWNER_TYPE = 8157;
    public static final int CONNECT_TIMEOUNT = 1200000;

    private String getSvcEndpoint() {
        try {
            // 通过名字服务获取domain, 线上环境使用
            String gwSubDomain = nameServiceHelper.getGwSubDomainByType(NAME_SERVICE_VIP_MANAGER_SERVICE);
            if (StringUtils.isEmpty(gwSubDomain)) {
                throw new Exception("Can not get CommonProviderClient gateway sub domain from name service.");
            }
            return gwSubDomain;
        }catch (Exception e) {
            logger.warn("vip manager init from ns error.", e);
            List<BakOwnerDO> owners = resourceService.getBakOwnerListByType(VIP_MANAGER_OWNER_TYPE);
            String ip = "";
            Integer port = 0;
            for(BakOwnerDO owner : owners) {
                ip = owner.getIp();
                port = owner.getPort();
                if(owner.getRole() == 0) {
                    return ip + ":" + port;
                }
            }
            logger.info("Init vip manager endpoint:" + ip + ":" + port);
            return ip + ":" + port;
        }
    }

    @Bean
    public ApiClient initVipManagerApiClient() throws Exception {
        this.basePath = this.getSvcEndpoint();
        this.userName = "ADB_VIP_MANAGER";
        this.userPwd = "BXY9hv5x5k1pZAYSOVrWMvE4Xxpsx8H8";
        ApiClient apiClient = new ApiClient();

        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(500);
        dispatcher.setMaxRequestsPerHost(500);
        OkHttpClient okHttpClient = new OkHttpClient().newBuilder()
                .connectTimeout(300, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS)
                .dispatcher(dispatcher)
                .build();
        apiClient.setHttpClient(okHttpClient);
        apiClient.setBasePath(String.format("http://%s/v1", this.basePath));
        HttpBasicAuth basicAuth = (HttpBasicAuth)apiClient.getAuthentication("basicAuth");
        basicAuth.setUsername(userName);
        basicAuth.setPassword(userPwd);
        return apiClient;
    }

    @Bean
    public LinksApi initLinksApi(ApiClient initVipManagerApiClient) throws Exception {
        LinksApi linksApi = new LinksApi();
        linksApi.setApiClient(initVipManagerApiClient);
        return linksApi;
    }

    @Bean
    public VpcMappingApi initVpcMappingApi(ApiClient initVipManagerApiClient) throws Exception {
        VpcMappingApi vpcMappingApi = new VpcMappingApi();
        vpcMappingApi.setApiClient(initVipManagerApiClient);
        return vpcMappingApi;
    }

}