package com.aliyun.dba.base.common.consts;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * 资源调度相关
 * <AUTHOR> on 2024/7/8
 */
public class ResourceScheduleConsts {
    public static final String RESOURCE_GUARANTEE_LEVEL = "resource_guarantee_level";

    public static final String RESOURCE_GUARANTEE_LEVEL_BACKUP = "resource_guarantee_level_backup";

    public static final String RESOURCE_GUARANTEE_LEVEL_TYPE = "resource_guarantee_level_type";



    //定义 class scheduleTemplate 中 resourceGuaranteePolicies的key的枚举值
    public enum ScheduleResourceGuaranteePoliciesKey {
        //资源保障级别
        resourceGuaranteeLevel,
        //允许降低到的资源保障级别
        resourceGuaranteeBackUpLevels,
        //资源保障级别是否允许降级
        resourceGuaranteeLevelType
    }


    //资源保障级别是否允许降级resourceGuaranteeLevelType的枚举值, 只有新架构实例会使用
    public enum ResourceGuaranteeLevelTypeEnum {
        //不允许降级
        force,
        //允许降级
        prefer
    }



    //resource_key
    public enum ResGuaranteeModelResourceKey {
        MYSQL_RES_GUARANTEE_MODEL_VIP_GC_LEVEL("MYSQL_RES_GUARANTEE_MODEL_VIP_GC_LEVEL"),
        MYSQL_RES_GUARANTEE_MODEL_SUPER_VIP_UID("MYSQL_RES_GUARANTEE_MODEL_SUPER_VIP_UID"),
        MYSQL_RES_GUARANTEE_MODEL_USER_MAP("MYSQL_RES_GUARANTEE_MODEL_USER_MAP"),
        MYSQL_RES_GUARANTEE_MODEL_USER_MAP_FOR_REBUILD("MYSQL_RES_GUARANTEE_MODEL_USER_MAP_FOR_REBUILD");

        private final String resourceKey;

        ResGuaranteeModelResourceKey(String resourceKey) {
            this.resourceKey = resourceKey;
        }

        public String getValue() {
            return resourceKey;
        }
    }


    //资源保障方案中的客户等级定义
    public enum UserLevelTag {
        USER_LEVEL_SUPER_VIP("super_vip"),
        USER_LEVEL_VIP("vip"),
        USER_LEVEL_GENERAL_USER("general_user");

        private final String tag;

        UserLevelTag(String tag) {
            this.tag = tag;
        }

        public String getValue() {
            return tag;
        }
    }

}
