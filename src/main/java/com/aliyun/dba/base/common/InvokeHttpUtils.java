package com.aliyun.dba.base.common;

import com.alibaba.fastjson.JSON;

import com.aliyun.dba.base.common.exception.CheckException;
import com.aliyun.dba.base.common.utils.LangUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Slf4j
public class InvokeHttpUtils {

    private static RestTemplate restTemplate;

    static {
        // timeout 10s
        int timeOut = 10000;
        SimpleClientHttpRequestFactory httpRequestFactory = new SimpleClientHttpRequestFactory();
        httpRequestFactory.setReadTimeout(timeOut);
        httpRequestFactory.setConnectTimeout(timeOut);
        restTemplate = new RestTemplate(httpRequestFactory);
    }


    /**
     * Get方式请求
     *
     * @param url
     * @param params
     * @return
     */
    public static Map<String, Object> invokeRestGetService(String url, Map<String, Object> params) {
        try {
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class, params);
            String retStr = responseEntity.getBody();
            return JSON.parseObject(retStr, Map.class);
        } catch (RestClientResponseException e) {
            log.error("invoke {} with {} error, {}", url, params, e.getResponseBodyAsString());
            throw e;
        } catch (Exception e) {
            log.error("invoke " + url + " with " + params + " error ", e);
            throw e;
        }
    }
    /**
     * 自定义方式请求
     *
     * @param url
     * @param params
     * @return
     */
    public static <T> T invokeRestService(String url, Map<String, Object> params,
        String method, MediaType mediaType, Class<T> returnType) {
        try {
            log.info("rest get invoke url: {}, params {}", url, params);
            HttpMethod httpMethod = HttpMethod.resolve(method);
            if (httpMethod == null) {
                throw new CheckException("httpMethod is null");
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(mediaType);
            HttpEntity httpEntity;
            if (mediaType == MediaType.MULTIPART_FORM_DATA || mediaType == MediaType.APPLICATION_FORM_URLENCODED) {
                MultiValueMap<String, Object> formDatas = new LinkedMultiValueMap<String, Object>();
                params.keySet().forEach(key -> formDatas.add(key, LangUtil.getString(params.get(key))));
                httpEntity = new HttpEntity<MultiValueMap<String, Object>>(formDatas, httpHeaders);
            } else {
                httpEntity = new HttpEntity<Map<String, Object>>(params, httpHeaders);
            }
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, httpMethod, httpEntity, String.class);
            return JSON.parseObject(responseEntity.getBody(), returnType);
        } catch (RestClientResponseException e) {
            log.error("invoke {} with {} error, {}", url, params, e.getResponseBodyAsString());
            throw e;
        } catch (Exception e) {
            log.error("invoke " + url + " with " + params + " error ", e);
            throw e;
        }
    }


    /**
     * 自定义方式请求
     *
     * @param url
     * @param params
     * @return
     */
    public static Map<String, Object> invokeRestService(String url, Map<String, Object> params,
                                                        String method, MediaType mediaType) {
        try {
            log.info("rest get invoke url: {}, params {}", url, params);
            HttpMethod httpMethod = HttpMethod.resolve(method);
            if (httpMethod == null) {
                throw new CheckException("httpMethod is null");
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(mediaType);
            HttpEntity httpEntity;
            if (mediaType == MediaType.MULTIPART_FORM_DATA || mediaType == MediaType.APPLICATION_FORM_URLENCODED) {
                MultiValueMap<String, Object> formDatas = new LinkedMultiValueMap<String, Object>();
                params.keySet().forEach(key -> formDatas.add(key, LangUtil.getString(params.get(key))));
                httpEntity = new HttpEntity<>(formDatas, httpHeaders);
            } else {
                httpEntity = new HttpEntity<>(params, httpHeaders);
            }
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, httpMethod, httpEntity, String.class);
            return JSON.parseObject(responseEntity.getBody(), Map.class);
        } catch (RestClientResponseException e) {
            log.error("invoke {} with {} error, {}", url, params, e.getResponseBodyAsString());
            throw e;
        } catch (Exception e) {
            log.error("invoke " + url + " with " + params + " error ", e);
            throw e;
        }
    }
}
