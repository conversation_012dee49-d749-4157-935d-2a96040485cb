package com.aliyun.dba.base.common.consts;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;


public class BlueGreenDeploymentConsts {
    public static final String READONLY_INSTANCE_LIST = "readOnlyInstanceList";
    public static final String REPLICA_LIST = "replicaList";
    public static final String PROXY_INSTANCE = "proxyInstance";
    public static final String DTS_INFO = "dts_info";
    public static final String DTS_INSTANCE_ID = "dtsInstanceId";
    public static final String DTS_JOB_ID = "dtsJobId";
    public static final String DTS_ADMIN = "dts_admin";
    public static final String DTS_END_TIME = "endTime";
    public static final String PRIMARY_MAPPING = "primaryMapping";
    public static final String GREEN_CUSTINS_ID = "greenCustinsId";
    public static final String BLUE_CUSTINS_ID = "blueCustinsId";
    public static final byte CREATING = 0;
    public static final byte ACTIVE = 1;
    public static final byte SWITCHING = 2;
    public static final byte DELETING = 3;
    public static final byte DELETED = 4;
    public static final String DEPLOYMENT_STATUS_CREATING = "deploymentCreating";
    public static final Set<String> PRIMARY_CONFIG_PARAM_SET = new TreeSet<String>(String.CASE_INSENSITIVE_ORDER) {
        {
            add("dbInstanceClass");
            add("dbInstanceStorage");
            add("engineVersion");
            add("minorVersion");
            add("engine");
            add("dbInstanceStorageType");
            add("vSwitchId");
            add("vpcId");
            add("zoneId");
            add("instanceNetworkType");
        }
    };
    public static final Set<String> RO_CONFIG_PARAM_SET = new TreeSet<String>(String.CASE_INSENSITIVE_ORDER) {
        {
            add("roInstanceName");
            add("regionId");
            add("dbInstanceClass");
            add("dbInstanceStorage");
            add("engineVersion");
            add("engine");
            add("dbInstanceStorageType");
            add("dbInstanceNetType");
            add("vSwitchId");
            add("vpcId");
            add("zoneId");
            add("instanceNetworkType");
        }
    };
    public static final Set<String> REPLICA_CONFIG_PARAM_SET = new TreeSet<String>(String.CASE_INSENSITIVE_ORDER) {
        {
            add("replicaName");
            add("dbInstanceClass");
            add("zoneId");
        }
    };
    public static final Set<String> PROXY_CONFIG_PARAM_SET = new TreeSet<String>(String.CASE_INSENSITIVE_ORDER) {
        {
            add("regionId");
            add("instanceNetworkType");
            add("persistentConnectionStatus");
            add("vpcId");
            add("vSwitchId");
            add("dbProxyInstanceType");
            add("dbProxyNodes");
            add("dbProxyInstanceNum");
        }
    };
    public static final Map<String, String> CLOUD_ESSD_MAP = new HashMap<String, String>() {{
        put("PL0", "cloud_essd0");
        put("PL1", "cloud_essd");
        put("PL2", "cloud_essd2");
        put("PL3", "cloud_essd3");
        put("cloud_auto", "general_essd");
    }};
}
