package com.aliyun.dba.base.common.utils;

public class ShuffleUtil {
    public static Integer crc16(String str) {
        try {
            int crc;
            int strLength, r;
            byte sbit;
            int tc;
            strLength = str.length();
            byte[] data = str.getBytes();
            crc = 0x0000FFFF;
            for (int i = 0; i < strLength; i++) {
                tc = crc >>> 8;
                crc = tc ^ data[i];
                for (r = 0; r < 8; r++) {
                    sbit = (byte) (crc & 0x00000001);
                    crc >>>= 1;
                    if (sbit != 0) {
                        crc ^= 0x0000A001;
                    }

                }
            }
            return crc;
        } catch (Exception ex) {
            return 0;
        }
    }
}
