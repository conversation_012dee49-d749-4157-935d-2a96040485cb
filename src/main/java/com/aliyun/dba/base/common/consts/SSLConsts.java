package com.aliyun.dba.base.common.consts;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * <AUTHOR> on 2024/5/20
 */
public class SSLConsts {

    public static final String CA_TYPE_ALIYUN = "aliyun";
    public static final String CA_TYPE_CUSTOM = "custom";

    public static final Set<String> CA_TYPE = Sets.newHashSet(CA_TYPE_ALIYUN, CA_TYPE_CUSTOM);


    public static final String CUSTINS_PARAM_NAME_CA_TYPE = "ca_type";

    public static final String FORCE_ENCRYPTION = "0";

    public static final String FORCE_ENCRYPTION_MINOR_VERSION = "20241130";

}
