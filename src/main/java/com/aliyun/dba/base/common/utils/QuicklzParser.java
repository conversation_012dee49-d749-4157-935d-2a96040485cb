package com.aliyun.dba.base.common.utils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
public class QuicklzParser {
    private final static int HASH_VALUES = 4096;
    private final static int UNCONDITIONAL_MATCHLEN = 6;
    private final static int UNCOMPRESSED_END = 4;
    private final static int CWORD_LEN = 4;
    public static Pair<byte[], Integer> decompress(byte[] source) {
        int size = (int) sizeDecompressed(source);
        int src = headerLen(source);
        int dst = 0;
        long cword_val = 1;
        byte[] destination = new byte[size];
        int[] hashtable = new int[4096];
        byte[] hash_counter = new byte[4096];
        int last_matchstart = size - UNCONDITIONAL_MATCHLEN - UNCOMPRESSED_END - 1;
        int last_hashed = -1;
        int hash;
        int fetch = 0;
        int level = (source[0] >>> 2) & 0x3;
        if (level != 1 && level != 3) {
            throw new RuntimeException("Java version only supports level 1 and 3");
        }
        if ((source[0] & 1) != 1) {
            try {
                byte[] d2 = new byte[size];
                System.arraycopy(source, headerLen(source), d2, 0, size);
                return ImmutablePair.of(d2, headerLen(source) + size);
            }catch (Exception e){
                System.out.println("e");
            }
        }
        for (; ; ) {
            if (cword_val == 1) {
                cword_val = fast_read(source, src, 4);
                src += 4;
                if (dst <= last_matchstart) {
                    if (level == 1) {
                        fetch = (int) fast_read(source, src, 3);
                    } else {
                        fetch = (int) fast_read(source, src, 4);
                    }
                }
            }
            if ((cword_val & 1) == 1) {
                int matchlen;
                int offset2;
                cword_val = cword_val >>> 1;
                if (level == 1) {
                    hash = (fetch >>> 4) & 0xfff;
                    offset2 = hashtable[hash];
                    if ((fetch & 0xf) != 0) {
                        matchlen = (fetch & 0xf) + 2;
                        src += 2;
                    } else {
                        matchlen = ((int) source[src + 2]) & 0xff;
                        src += 3;
                    }
                } else {
                    int offset;
                    if ((fetch & 3) == 0) {
                        offset = (fetch & 0xff) >>> 2;
                        matchlen = 3;
                        src++;
                    } else if ((fetch & 2) == 0) {
                        offset = (fetch & 0xffff) >>> 2;
                        matchlen = 3;
                        src += 2;
                    } else if ((fetch & 1) == 0) {
                        offset = (fetch & 0xffff) >>> 6;
                        matchlen = ((fetch >>> 2) & 15) + 3;
                        src += 2;
                    } else if ((fetch & 127) != 3) {
                        offset = (fetch >>> 7) & 0x1ffff;
                        matchlen = ((fetch >>> 2) & 0x1f) + 2;
                        src += 3;
                    } else {
                        offset = (fetch >>> 15);
                        matchlen = ((fetch >>> 7) & 255) + 3;
                        src += 4;
                    }
                    offset2 = (int) (dst - offset);
                }
                destination[dst + 0] = destination[offset2 + 0];
                destination[dst + 1] = destination[offset2 + 1];
                destination[dst + 2] = destination[offset2 + 2];
                for (int i = 3; i < matchlen; i += 1) {
                    destination[dst + i] = destination[offset2 + i];
                }
                dst += matchlen;
                if (level == 1) {
                    fetch = (int) fast_read(destination, last_hashed + 1, 3); // destination[last_hashed + 1] | (destination[last_hashed + 2] << 8) | (destination[last_hashed + 3] << 16);
                    while (last_hashed < dst - matchlen) {
                        last_hashed++;
                        hash = ((fetch >>> 12) ^ fetch) & (HASH_VALUES - 1);
                        hashtable[hash] = last_hashed;
                        hash_counter[hash] = 1;
                        fetch = fetch >>> 8 & 0xffff | (((int) destination[last_hashed + 3]) & 0xff) << 16;
                    }
                    fetch = (int) fast_read(source, src, 3);
                } else {
                    fetch = (int) fast_read(source, src, 4);
                }
                last_hashed = dst - 1;
            } else {
                if (dst <= last_matchstart) {
                    destination[dst] = source[src];
                    dst += 1;
                    src += 1;
                    cword_val = cword_val >>> 1;
                    if (level == 1) {
                        while (last_hashed < dst - 3) {
                            last_hashed++;
                            int fetch2 = (int) fast_read(destination, last_hashed, 3);
                            hash = ((fetch2 >>> 12) ^ fetch2) & (HASH_VALUES - 1);
                            hashtable[hash] = last_hashed;
                            hash_counter[hash] = 1;
                        }
                        fetch = fetch >> 8 & 0xffff | (((int) source[src + 2]) & 0xff) << 16;
                    } else {
                        fetch = fetch >> 8 & 0xffff | (((int) source[src + 2]) & 0xff) << 16 | (((int) source[src + 3]) & 0xff) << 24;
                    }
                } else {
                    while (dst <= size - 1) {
                        if (cword_val == 1) {
                            src += CWORD_LEN;
                            cword_val = 0x80000000L;
                        }
                        destination[dst] = source[src];
                        dst++;
                        src++;
                        cword_val = cword_val >>> 1;
                    }
                    return ImmutablePair.of(destination, src);
                }
            }
        }
    }
    static long fast_read(byte[] a, int i, int numbytes)
    {
        long l = 0;
        for (int j = 0; j < numbytes; j++) {
            l |= ((((int)a[i + j]) & 0xffL) << j * 8);
        }
        return l;
    }
    static int headerLen(byte[] source)
    {
        return ((source[0] & 2) == 2) ? 9 : 3;
    }
    static public long sizeDecompressed(byte[] source)
    {
        if (headerLen(source) == 9) {
            return fast_read(source, 5, 4);
        } else {
            return fast_read(source, 2, 1);
        }
    }
    static public long sizeCompressed(byte[] source)
    {
        if (headerLen(source) == 9) {
            return fast_read(source, 1, 4);
        } else {
            return fast_read(source, 1, 1);
        }
    }
}