package com.aliyun.dba.base.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class LangUtil {
    private static String[] DATE_FORMAT = new String[]{"yyyyMMdd", "yyyy-MM-dd", "yyyyMMddHHmm", "yyyy-MM-dd HH:mm", "yyyyMMddHHmmss", "yyyy-MM-dd HH:mm:ss", "yyyy"};


    //unicode regx
    static Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");


    public static Map<Class<?>, Class<?>> BASE_TYPES = new HashMap<Class<?>, Class<?>>();

    static {
        BASE_TYPES.put(Boolean.class, Boolean.class);
        BASE_TYPES.put(boolean.class, Boolean.class);
        BASE_TYPES.put(Date.class, Boolean.class);
        BASE_TYPES.put(String.class, Boolean.class);
        BASE_TYPES.put(StringBuilder.class, Boolean.class);
        BASE_TYPES.put(StringBuffer.class, Boolean.class);
        BASE_TYPES.put(Character.class, Boolean.class);
        BASE_TYPES.put(char.class, Boolean.class);
        BASE_TYPES.put(Long.class, Boolean.class);
        BASE_TYPES.put(long.class, Boolean.class);
        BASE_TYPES.put(Float.class, Boolean.class);
        BASE_TYPES.put(float.class, Boolean.class);
        BASE_TYPES.put(Double.class, Boolean.class);
        BASE_TYPES.put(double.class, Boolean.class);
        BASE_TYPES.put(Short.class, Boolean.class);
        BASE_TYPES.put(short.class, Boolean.class);
        BASE_TYPES.put(Integer.class, Boolean.class);
        BASE_TYPES.put(int.class, Boolean.class);
        BASE_TYPES.put(Byte.class, Boolean.class);
        BASE_TYPES.put(byte.class, Boolean.class);
    }


    @SuppressWarnings("rawtypes")
    public static List getList(List datas, int size) {
        return datas == null || datas.size() < size ? datas : datas.subList(0, size);
    }

    public static String[] toList(String key, String[] splitor) {
        if (splitor == null || splitor.length == 0) {
            return StringUtils.isBlank(key) ? new String[0] : new String[]{key};
        }
        if (StringUtils.isBlank(key)) {
            return new String[0];
        }
        for (String split : splitor) {
            if (key.indexOf(split) > 0) {
                return StringUtils.split(key, split);
            }
        }
        return new String[]{key};
    }

    public static boolean isUrl(Object url) {
        if (String.class.isInstance(url) || StringBuilder.class.isInstance(url) || StringBuffer.class.isInstance(url)) {
            String durl = LangUtil.getString(url, "");
            return StringUtils.startsWith(durl, "http://") || StringUtils.startsWith(durl, "https://") || StringUtils.startsWith(durl, "//");
        }
        return false;
    }

    /**
     * �˷�������URLǰ׺\��׺�ж�String�Ƿ�Ϊһ��Html(�ı�)����;
     * <p>Ŀǰ��׺����HTML_SUFFIX_SET�е�����,����ȱ��(��׺û��ȫö��),
     * �����޸ĳ�ͨ��httpClient����,�鿴���ص���ҳtype</p>
     *
     * @param url
     * @return
     */
    public static boolean isHtml(Object url) {
        boolean match = false;
        if (String.class.isInstance(url) || StringBuilder.class.isInstance(url) || StringBuffer.class.isInstance(url)) {
            String sourceUrl = LangUtil.getString(url, "");
            if (sourceUrl.contains("#")) {
                sourceUrl = StringUtils.substringBefore(sourceUrl, "#");
            } //��ȡê����ǰ��URL����
            match = StringUtils.startsWith(sourceUrl, "http://") || StringUtils.startsWith(sourceUrl, "https://") || StringUtils.startsWith(sourceUrl, "//");
            if (!match) {
                //����Ҫ���������ж�
                return false;
            }
            if (match) {
                String fileType = !sourceUrl.contains("?") ? StringUtils.substringAfterLast(sourceUrl, ".") : StringUtils.substringAfterLast(StringUtils.substringBefore(sourceUrl, "?"), ".");
                if (fileType != null && fileType.endsWith("/") && fileType.length() > 1) {
                    fileType = fileType.substring(0, fileType.length() - 1);
                }
                if (fileType != null) {
                    fileType = fileType.toLowerCase();
                }
                match = fileType == null || HTML_SUFFIX_SET.contains(fileType);
                if (match) {
                    return true;
                }
                //��������  http://www.tmall.com/wh/tmall/tmall-3c/3c/act/shoufa ������rest URLҲ��Ҫ�ж�Ϊhtml,
                //Ŀǰ�жϷ�ͼƬ�ľ���html,���ڽ���Ƶ��Ҳ�ж�Ϊhtml����
                return !PICTURE_SUFFIX_SET.contains(fileType);
            }
        }
        return match;
    }

    public static boolean isPicture(Object url) {
        boolean match = false;
        if (String.class.isInstance(url) || StringBuilder.class.isInstance(url) || StringBuffer.class.isInstance(url)) {
            String sourceUrl = LangUtil.getString(url, "");
            if (sourceUrl.contains("#")) {
                sourceUrl = StringUtils.substringBefore(sourceUrl, "#");
            }//��ȡê����ǰ��URL����
            match = StringUtils.startsWith(sourceUrl, "http://") || StringUtils.startsWith(sourceUrl, "https://") || StringUtils.startsWith(sourceUrl, "//");
            if (!match) {
                //����Ҫ���������ж�
                return false;
            }
            if (match) {
                String fileType = !sourceUrl.contains("?") ? StringUtils.substringAfterLast(sourceUrl, ".") : StringUtils.substringAfterLast(StringUtils.substringBefore(sourceUrl, "?"), ".");
                match = fileType == null || PICTURE_SUFFIX_SET.contains(fileType);

            }
        }
        return match;
    }

    public static boolean isHttp(Object url) {
        boolean match = false;
        if (String.class.isInstance(url) || StringBuilder.class.isInstance(url) || StringBuffer.class.isInstance(url)) {
            String sourceUrl = LangUtil.getString(url, "");
            if (sourceUrl.contains("#")) {
                sourceUrl = StringUtils.substringBefore(sourceUrl, "#");
            }//��ȡê����ǰ��URL����
            match = StringUtils.startsWith(sourceUrl, "http://") || StringUtils.startsWith(sourceUrl, "https://") || StringUtils.startsWith(sourceUrl, "//");

        }
        return match;
    }

    private static Set<String> HTML_SUFFIX_SET = new HashSet<String>();

    static {
        HTML_SUFFIX_SET.add("cn");//"http://www.sina.com.cn"
        HTML_SUFFIX_SET.add("com");//"http://www.google.com"
        HTML_SUFFIX_SET.add("htm");
        HTML_SUFFIX_SET.add("html");
        HTML_SUFFIX_SET.add("do");
        HTML_SUFFIX_SET.add("php");
        HTML_SUFFIX_SET.add("php2");
        HTML_SUFFIX_SET.add("hk");//http://tmall.hk/
    }

    private static Set<String> PICTURE_SUFFIX_SET = new HashSet<String>();

    static {
        PICTURE_SUFFIX_SET.add("jpg");
        PICTURE_SUFFIX_SET.add("png");
        PICTURE_SUFFIX_SET.add("jpeg");
        PICTURE_SUFFIX_SET.add("gif");
    }

    @SuppressWarnings("rawtypes")
    public static boolean isContain(List datas, Object data) {
        String dataV = LangUtil.getString(data, "");
        for (Object d : datas) {
            if (StringUtils.equals(LangUtil.getString(d), dataV)) {
                return true;
            }
        }
        return false;
    }

    public static List<Long> toLongList(String key, String[] splitor) {
        String[] datas = toList(key, splitor);
        List<Long> d = new ArrayList<Long>();
        for (String data : datas) {
            d.add(Long.valueOf(data));
        }
        return d;
    }

    public static List<Long> toLongList(String key, String splitter) {
        return toLongList(key, new String[]{splitter});
    }

    public static List<String> toStringList(String key, String[] splitor) {
        String[] datas = toList(key, splitor);
        List<String> d = new ArrayList<String>();
        for (String data : datas) {
            d.add(String.valueOf(data));
        }

        return d;
    }

    public static List<String> toStringList(String key, String splitor) {
        return toStringList(key, new String[]{splitor});
    }

    /**
     * @param objects
     * @param data
     * @return
     */
    public static int getPosition(List<?> objects, Object data) {
        if (objects == null || data == null) {
            return 0;
        }
        int i = 0;
        for (Object o : objects) {
            i++;
            if (o.equals(data)) {
                return i;
            }
        }
        return 0;
    }


    public static Object getObject(Object value, String type) {
        if (StringUtils.isBlank(type)) {
            return value;
        }
        if (value == null) {
            return null;
        }
        type = StringUtils.trimToEmpty(type).toLowerCase();
        if ("object".equals(type) || "java.lang.object".equals(type)) {
            return value;
        } else if ("long".equals(type) || "java.lang.long".equals(type)) {
            return LangUtil.getLong(value, null);
        } else if ("float".equals(type) || "java.lang.float".equals(type)) {
            return LangUtil.getDouble(value, null);
        } else if ("double".equals(type) || "java.lang.double".equals(type)) {
            return LangUtil.getDouble(value, null);
        } else if ("string".equals(type) || "java.lang.string".equals(type)) {
            return LangUtil.getString(value, null);
        } else if ("int".equals(type) || "integer".equals(type) || "java.lang.integer".equals(type)) {
            return LangUtil.getLong(value, null);
        } else if ("short".equals(type) || "java.lang.short".equals(type)) {
            return LangUtil.getLong(value, null);
        } else if ("char".equals(type) || "character".equals(type) || "java.lang.character".equals(type)) {
            return Character.valueOf(type.toString().charAt(0));
        } else if ("byte".equals(type) || "java.lang.byte".equals(type)) {
            return Byte.valueOf(type.toString());
        } else if ("date".equals(type) || "java.util.date".equals(type)) {
            try {
                return DateUtils.parseDate(value.toString(), DATE_FORMAT);
            } catch (Exception e) {
                throw new RuntimeException("parseError{" + value.toString() + "}");
            }
        } else if ("bool".equals(type) || "boolean".equals(type) || "java.lang.boolean".equals(type)) {
            return LangUtil.getBoolean(value, null);
        } else {
            throw new RuntimeException("unsupport type { " + type + "}");
        }

    }

    public static Date getDate(Object data) throws Exception {
        return DateUtils.parseDate(data.toString(), DATE_FORMAT);
    }

    public static String getString(Object data) {
        return getString(data, null);
    }

    public static Long getLong(Object data) {
        return getLong(data, null);
    }

    public static Integer getInteger(Object data) {
        return getInteger(data, null);
    }

    public static Short getShort(Object data) {
        return getShort(data, null);
    }

    public static Byte getByte(Object data) {
        return getByte(data, null);
    }

    public static Double getDouble(Object data) {
        return getDouble(data, null);
    }

    public static Float getFloat(Object data) {
        return getFloat(data, null);
    }

    public static Boolean getBoolean(Object data) {
        return getBoolean(data, false);
    }


    public static String getString(Object data, String defValue) {
        if (String.class.isInstance(data)) {
            return (String) data;
        }
        return data == null ? defValue : data.toString();
    }

    public static Long getLong(Object data, Long defValue) {
        if (Long.class.isInstance(data)) {
            return (Long) data;
        }
        if (data == null || StringUtils.isBlank(data.toString())) {
            return defValue;
        }
        try {
            return Long.valueOf(data.toString().trim()).longValue();
        } catch (Exception nfe) {
            return defValue;
        }
    }

    public static Integer getInteger(Object data, Integer defValue) {
        if (Integer.class.isInstance(data)) {
            return (Integer) data;
        }
        if (data == null || StringUtils.isBlank(data.toString())) {
            return defValue;
        }
        try {
            return Integer.valueOf(data.toString().trim()).intValue();
        } catch (Exception nfe) {
            return defValue;
        }
    }

    public static Short getShort(Object data, Short defValue) {
        if (Short.class.isInstance(data)) {
            return (Short) data;
        }
        if (data == null || StringUtils.isBlank(data.toString())) {
            return defValue;
        }
        try {
            return Short.valueOf(data.toString().trim()).shortValue();
        } catch (Exception nfe) {
            return defValue;
        }

    }


    public static Byte getByte(Object data, Byte defValue) {
        if (Byte.class.isInstance(data)) {
            return (Byte) data;
        }
        if (data == null || StringUtils.isBlank(data.toString())) {
            return defValue;
        }
        try {
            return Byte.valueOf(data.toString().trim()).byteValue();
        } catch (Exception nfe) {
            return defValue;
        }
    }


    public static Double getDouble(Object data, Double defValue) {
        if (Double.class.isInstance(data)) {
            return (Double) data;
        }
        if (data == null || StringUtils.isBlank(data.toString())) {
            return defValue;
        }
        try {
            return Double.valueOf(data.toString().trim());
        } catch (Exception nfe) {
            return defValue;
        }

    }

    public static Float getFloat(Object data, Float defValue) {
        if (Float.class.isInstance(data)) {
            return (Float) data;
        }
        if (data == null || StringUtils.isBlank(data.toString())) {
            return defValue;
        }
        try {
            return Float.valueOf(data.toString().trim());
        } catch (Exception nfe) {
            return defValue;
        }
    }

    public static Boolean getBoolean(Object data, Boolean defValue) {
        if (Boolean.class.isInstance(data)) {
            return (Boolean) data;
        }
        return data == null || data.toString().isEmpty() ? defValue : (("1".equals(data.toString()) ? Boolean.valueOf(true) : Boolean.valueOf(data.toString())));
    }

    public static boolean isNotNull(Object data) {
        return data != null;
    }

    public static boolean isTrue(Boolean data) {
        return data != null && data.booleanValue() == true;
    }

    public static boolean isFalse(Boolean data) {
        return data == null || data.booleanValue() == false;
    }

    public static boolean isNull(Object data) {
        return data == null;
    }

    public static boolean isEmpty(Collection<?> data) {
        return data == null || data.size() == 0;
    }

    public static boolean isEmpty(Map<?, ?> data) {
        return data == null || data.size() == 0;
    }

    public static boolean isNotEmpty(Collection<?> data) {
        return data != null && data.size() > 0;
    }

    public static boolean isNotEmpty(Map<?, ?> data) {
        return data != null && data.size() > 0;
    }

    public static boolean isContainKey(Map<?, ?> data, String key) {
        return data != null && data.containsKey(key);
    }

    public static void throwRuntimeException(RuntimeException e)
        throws RuntimeException {
        throw e;
    }

    public static boolean equal(Object a, Object b) {
        return a == b;
    }

    public static boolean between(Date start, Date end) {
        long cur = System.currentTimeMillis();
        if (start != null && start.getTime() > cur) {
            return false;
        }
        if (end != null && end.getTime() <= cur) {
            return false;
        }
        return true;

    }

    public static boolean logicEqual(long src, long bitSet) {
        return (src & bitSet) == bitSet;
    }

    public static String getException(Throwable e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw));
        return "message:" + e.getMessage() + ",stacktrace:" + sw.getBuffer();
    }


    public static boolean isEqual(List<String> a, List<String> b) {
        if (LangUtil.isEmpty(a) && LangUtil.isEmpty(b)) {
            return true;
        }
        if (LangUtil.isEmpty(a) || LangUtil.isEmpty(b)) {
            return false;
        }
        if (a.size() != b.size()) {
            return false;
        }
        for (String achild : a) {
            if (!b.contains(achild)) {
                return false;
            }
        }
        return true;
    }

    public static String unionString(List<?> datas, char sep) {
        if (LangUtil.isEmpty(datas)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < datas.size(); i++) {
            sb.append(datas.get(i));
            if (i != datas.size() - 1) {
                sb.append(sep);
            }
        }
        return sb.toString();
    }

    public static Map<String, String> getParam(String httpUrl) {
        Map<String, String> param = new HashMap<String, String>();
        httpUrl = StringUtils.trimToEmpty(httpUrl);
        if (httpUrl.contains("?")) {
            httpUrl = StringUtils.substringAfter(httpUrl, "?");
        }
        String[] datas = StringUtils.split(httpUrl, httpUrl.contains("&") ? "&" : ";");
        for (String data : datas) {
            String[] ds = StringUtils.split(data, data.contains("=") ? "=" : ":");
            if (ds.length == 2) {
                param.put(ds[0], StringUtils.trimToEmpty(ds[1]));
            }
        }
        return param;
    }


    public static Long toLong(String value, Long defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        try {
            return Long.valueOf(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Integer toInteger(String value, Integer defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        try {
            return Integer.valueOf(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Double toDouble(String value, Double defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        try {
            return Double.valueOf(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Float toFloat(String value, Float defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        try {
            return Float.valueOf(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * unicode ת�ַ���
     */
    public static String unicodeToString(String str) {
        try {
            if (StringUtils.isEmpty(str)) {
                return str;
            }
            Matcher matcher = pattern.matcher(str);
            char ch;
            while (matcher.find()) {
                ch = (char) Integer.parseInt(matcher.group(2), 16);
                str = str.replace(matcher.group(1), ch + "");
            }
            return str;
        } catch (Exception e) {
            return str;
        }

    }
}
