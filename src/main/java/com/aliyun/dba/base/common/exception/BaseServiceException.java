package com.aliyun.dba.base.common.exception;


public class BaseServiceException extends Exception {

    private String code;

    public BaseServiceException() {
        super();
    }

    public BaseServiceException(String code, String message) {
        super(message);
        this.code = code;
    }


    public BaseServiceException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public BaseServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseServiceException(String message) {
        super(message);
    }

    public BaseServiceException(Throwable cause) {
        super(cause);
    }

    public String getCode() {
        return code;
    }
}

