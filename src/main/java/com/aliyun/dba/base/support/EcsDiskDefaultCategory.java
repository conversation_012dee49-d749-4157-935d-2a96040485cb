package com.aliyun.dba.base.support;

import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;


@Service
public class EcsDiskDefaultCategory {
    private static final Logger logger = Logger.getLogger(EcsDiskDefaultCategory.class);

    @Autowired
    protected ResourceService resourceService;
    public final String RESOURCE_ZONE_ECS_DATA_DISK_CATEGORY = "ZONE_ECS_DATA_DISK_CATEGORY";

    public String getZoneDefaultCategory(String zoneId) throws RdsException {

        if (StringUtils.isNotEmpty(zoneId)){
            logger.info("dockerparameter getDiskType try search zone resource default ecs data disk category, key: " + RESOURCE_ZONE_ECS_DATA_DISK_CATEGORY);
            try {
                ResourceDO resourceDO = this.resourceService.getResourceByResKey(RESOURCE_ZONE_ECS_DATA_DISK_CATEGORY);
                if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())){
                    JSONObject configMap = JSON.parseObject(resourceDO.getRealValue());
                    String dataDiskCategory = configMap.getString(zoneId);
                    if (StringUtils.isNotEmpty(dataDiskCategory)){
                        String category = dataDiskCategory.trim();
                        logger.info("getZoneDefaultCategory get real value: "+ zoneId + " " + category);
                        return category;
                    }
                }
            }catch (Exception e){
                logger.error("try search zone default disk category error, use RESOURCE_ECS_DATA_DISK_CATEGORY");
            }
        }
        logger.info("getDiskType search zone resource default ecs data disk category, key: ECS_DATA_DISK_CATEGORY");
        return  ResourceSupport.getInstance().getStringRealValue(ResourceKey.RESOURCE_ECS_DATA_DISK_CATEGORY).trim();
    }
}
