package com.aliyun.dba.base.support;

import com.udojava.evalex.Expression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class IntervalUtil {
    /**
     * 判断data_value是否在interval区间范围内
     */
    public static boolean isInTheInterval(String dataValue, String interval) throws Exception {
        if (StringUtils.isEmpty(dataValue) || StringUtils.isEmpty(interval)) {
            throw new Exception("invalid input: dataValue '" + dataValue + "', interval: '" + interval + "'");
        }

        dataValue = StringUtils.trim(dataValue);
        interval = StringUtils.trim(interval);
        interval = getStandardInterval(interval);
        // 将区间和data_value转化为可计算的表达式
        String formula = getFormulaByAllInterval(dataValue, interval);
        log.debug("trans interval {} to {}", interval, formula);
        Expression expr = new Expression(formula);
        try {
            //计算表达式
            return expr.eval().equals(BigDecimal.ONE);
        } catch (Exception t) {
            return false;
        }
    }

    private static String getStandardInterval(String interval) throws Exception {
        interval = StringUtils.replace(interval, " ", "");
        String pStr = "[\\[(](-)?\\d+([,-])(-)?\\d+[\\])]";
        Pattern p = Pattern.compile(pStr);
        Matcher matcher = p.matcher(interval);
        if (!matcher.find()) {
            throw new Exception("Invalid interval: " + interval);
        }

        String sep = matcher.group(2);
        if ("-".equalsIgnoreCase(sep)) {
            StringBuilder strBuilder = new StringBuilder(interval);
            strBuilder.setCharAt(matcher.start(2), ',');
            interval = strBuilder.toString();
        }

        return interval;
    }

    /**
     * 将所有阀值区间转化为公式
     */
    private static String getFormulaByAllInterval(String dateValue, String interval) {
        StringBuilder buff = new StringBuilder();
        for (String limit : interval.split("U")) {
            // 如：（125%,135%）U (70%,80%)
            buff.append("(").append(getFormulaByInterval(dateValue, limit)).append(")").append("||");
        }
        String allLimitInterval = buff.toString();
        int index = allLimitInterval.lastIndexOf("||");
        allLimitInterval = allLimitInterval.substring(0, index);
        return allLimitInterval;
    }

    /**
     * 将部分阀值区间转化为公式
     */
    private static String getFormulaByInterval(String dateValue, String interval) {
        StringBuilder buff = new StringBuilder();
        for (String halfInterval : interval.split(",")) {
            // 如：[75,80)、≥80
            buff.append(getFormulaByHalfInterval(halfInterval, dateValue)).append(" && ");
        }
        String limitInterval = buff.toString();
        int index = limitInterval.lastIndexOf(" && ");
        limitInterval = limitInterval.substring(0, index);
        return limitInterval;
    }

    /**
     * 将半个阀值区间转化为公式
     */
    private static String getFormulaByHalfInterval(String halfInterval, String dateValue) {
        halfInterval = halfInterval.trim();
        if (halfInterval.contains("∞")) {
            // 包含无穷大则不需要公式
            return "1 == 1";
        }
        StringBuilder formula = new StringBuilder();
        String data = "";
        String opera = "";

        if (halfInterval.matches("^([<>≤≥\\[\\(](-?\\d+.?\\d*\\%?))$")) {
            // 表示判断方向（如>）在前面 如：≥80%
            opera = halfInterval.substring(0, 1);
            data = halfInterval.substring(1);
        } else {//[130、145)
            opera = halfInterval.substring(halfInterval.length() - 1);
            data = halfInterval.substring(0, halfInterval.length() - 1);
        }

        BigDecimal value = dealPercent(data);
        formula.append(dateValue).append(" ").append(opera).append(" ").append(value);
        String a = formula.toString();

        // 转化特定字符
        return a.replace("[", ">=")
                .replace("(", ">")
                .replace("]", "<=")
                .replace(")", "<")
                .replace("≤", "<=")
                .replace("≥", ">=");
    }

    /**
     * 去除百分号，转为小数
     */
    private static BigDecimal dealPercent(String str) {
        BigDecimal d = BigDecimal.ZERO;
        if (str.contains("%")) {
            str = str.substring(0, str.length() - 1);
            d = (new BigDecimal(str)).divide(new BigDecimal(100));
        } else {
            d = new BigDecimal(str);
        }
        return d;
    }

    public static void main(String[] args) throws Exception {
        System.out.println(isInTheInterval("7", "(-1,6]"));
        System.out.println(isInTheInterval("3", "(-1,6]"));
        System.out.println(isInTheInterval("3", "(-1--6]"));
        System.out.println(isInTheInterval("-1", "[-3,-1]"));
        System.out.println(isInTheInterval("-1.1", "[-3,-1]"));
        System.out.println(isInTheInterval("16777216", "[16777216-10485760]"));
    }
}
