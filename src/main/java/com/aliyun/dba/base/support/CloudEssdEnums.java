package com.aliyun.dba.base.support;

public enum CloudEssdEnums {

    CLOUD_ESSD_PL2("cloud_essd2", "PL2", "cloud_essd"),
    CLOUD_ESSD_PL3("cloud_essd3", "PL3", "cloud_essd"),
    CLOUD_ESSD_PL0("cloud_essd0", "PL0", "cloud_essd");

    private String storageType;
    private String performanceLevel;
    private String diskCategory;

    CloudEssdEnums(String storageType, String performanceLevel, String diskCategory) {
        this.storageType = storageType;
        this.performanceLevel = performanceLevel;
        this.diskCategory = diskCategory;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }

    public String getStorageType() {
        return storageType;
    }

    public String getPerformanceLevel() {
        return performanceLevel;
    }

    public void setPerformanceLevel(String performanceLevel) {
        this.performanceLevel = performanceLevel;
    }

    public String getDiskCategory() {
        return diskCategory;
    }

    public void setDiskCategory(String diskCategory) {
        this.diskCategory = diskCategory;
    }
}