package com.aliyun.dba.base.support;

import java.math.BigDecimal;
import java.util.HashMap;

public class SizeUnitTransTool {
    private static final BigDecimal BASE = new BigDecimal(1024);
    private static final HashMap<String, BigDecimal> unit = new HashMap<String, BigDecimal>() {
        {
            // default B
            this.put("", BASE.pow(0));
            this.put("B", BASE.pow(0));
            this.put("K", BASE.pow(1));
            this.put("KB", BASE.pow(1));
            this.put("M", BASE.pow(2));
            this.put("MB", BASE.pow(2));
            this.put("G", BASE.pow(3));
            this.put("GB", BASE.pow(3));
            this.put("T", BASE.pow(4));
            this.put("TB", BASE.pow(4));
            this.put("P", BASE.pow(5));
            this.put("PB", BASE.pow(5));
        }
    };

    public SizeUnitTransTool() {
    }

    public static BigDecimal trans(String value, String toUnit) throws Exception {
        if (value == null) {
            return BigDecimal.valueOf(0);
        }

        if (toUnit == null) {
            toUnit = "";
        } else {
            toUnit = toUnit.toUpperCase();
        }

        if (unit.get(toUnit) == null) {
            throw new Exception(String.format("unknown unit '%s'", toUnit));
        }

        value = value.toUpperCase();
        StringBuilder numPart = new StringBuilder();
        StringBuilder unitPart = new StringBuilder();
        for (int i = 0; i < value.length(); i++) {
            char c = value.charAt(i);
            if (c >= 48 && c <= 57 || c == '.' || c == '-') {
                numPart.append(value.charAt(i));
            } else {
                unitPart.append(c);
            }
        }

        BigDecimal num = new BigDecimal(numPart.toString());
        String fromUnit = unitPart.toString();
        return num.multiply(unit.get(fromUnit)).divide(unit.get(toUnit), 6, BigDecimal.ROUND_HALF_UP);
    }

    public static void main(String[] args) {
        String a = "1.1K";
        String b = "18446744073709551616";

        try {
            System.out.println(String.format("%.2f", trans(a, null)));
            System.out.println(String.format("%s", trans(b, null)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
