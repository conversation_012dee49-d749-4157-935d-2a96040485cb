package com.aliyun.dba.base.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.dataobject.EcsDiskDO;
import com.aliyun.dba.ecs.service.EcsDiskService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.ecs.support.EcsConstants;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodType;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.instance.support.InstanceSupport.*;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.*;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Service
public class MigratePengineToK8SInstanceClassImpl implements IAction {

    private static final LogAgent LOG_AGENT = LogFactory.getLogAgent(MigratePengineToK8SInstanceClassImpl.class);
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private AVZSupport avzSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private MySQLAvzService mySQLAvzService;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private BakService bakService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private DTZSupport dtzSupport;
    @Resource
    private InstanceService instanceService;
    @Resource
    private TaskService taskService;
    @Resource
    private InstanceIDao instanceIDao;
    @Resource
    private EcsService ecsService;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;
    @Resource
    private BackupService backupService;
    @Resource
    private EcsDiskService ecsDiskService;

    @Resource
    private ClusterService clusterService;
    @Resource
    protected ResourceService resourceService;

    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private CrossArchService crossArchService;

    // 当前支持的版本
    Set<String> supportDBVersion = new HashSet<String>() {
        {
            add("8.0");
            add("5.7");
        }
    };

    // 当前支持的CLOUD ESSD PL磁盘类型
    Set<String> supportCloudEssdPL = new HashSet<String>() {
        {
            add("PL1");
            add("PL2");
            add("PL3");
        }
    };

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        params.put(ParamConstants.DISPENSE_MODE.toLowerCase(), "1");  //只能支持主可用区模式
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        boolean isLockBakSuccess = false;
        String destDBInstanceName = null;
        try {
            String uid = mysqlParamSupport.getUID(params);
            String bid = mysqlParamSupport.getBID(params);

            CustInstanceDO logicCustIns = mysqlParameterHelper.getAndCheckCustInstance();
            if (logicCustIns.getParentId() != 0) {
                logicCustIns = custinsService.getCustInstanceByCustinsId(logicCustIns.getParentId());
            }
            String clusterName = logicCustIns.getClusterName();
            ClustersDO clustersDO = clusterService.getClusterByClusterName(clusterName);
            Boolean isDhg = mysqlParamSupport.isDHGCluster(clusterName);
            if (!PodParameterHelper.isAliYun(logicCustIns.getBizType()) || isDhg) {
                LOG_AGENT.error("only support biz type = aliyun And not is dhg.");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            ReplicaSet.BizTypeEnum bizType = podParameterHelper.getBizType(requestId, clustersDO.getRegion());
            boolean isDockerOnEcs = logicCustIns.getKindCode().equals(KIND_CODE_DOCKER_ON_ECS);
            boolean isEcs = logicCustIns.getKindCode().equals(KIND_CODE_ECS_VM);
            CustInstanceDO physicalCustIns;
            List<CustInstanceDO> childCustinsList;
            Integer readOnlyCount =0;
            if (isDockerOnEcs) {
                // 当前Pengine实例的physical实例
                if (StringUtils.equalsIgnoreCase(logicCustIns.getCharacterType(), CustinsSupport.CHARACTER_TYPE_PHYSICAL)) {
                    physicalCustIns = logicCustIns;
                } else {
                    childCustinsList = custinsService.getCustInstanceUnitByParentIdAndCharacterType(
                            logicCustIns.getId(), CustinsSupport.CHARACTER_TYPE_PHYSICAL);
                    if (childCustinsList.size() == 0) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
                    }
                    // 基础版为单节点，获取physical ins
                    physicalCustIns = childCustinsList.get(0);
                }
                readOnlyCount = custinsService.countReadCustInstanceByPrimaryCustinsId(physicalCustIns.getId());

            } else if (isEcs) {
                physicalCustIns = logicCustIns;
            } else {
                LOG_AGENT.error("only support kind code in (1, 3)");
                throw new RdsException(ErrorCode.UNSUPPORTED_KIND_CODE);
            }

            // check是否重复下任务
            if (!logicCustIns.isActive()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }else if (logicCustIns.isLock()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            // 元数据cluster check新架构是否开区成功
            if (!crossArchService.checkNewArmClusterSupport(logicCustIns.getId())) {
                LOG_AGENT.error(requestId + " Exception: the new arm cluster is not support, please check cluster");
                throw new RdsException(ErrorCode.CLUSTER_NOT_FOUND);
            }

            // 设置切换时间
            Date switchTime = mysqlParameterHelper.getAndCheckSwitchTime(DataSourceMap.DATA_SOURCE_DBAAS);
            String switchMode = mysqlParameterHelper.getAndCheckSwitchTimeMode(ParamConstants.SWITCH_TIME_MODE, switchTime, true);
            Map<String, Object> effMap = this.custinsService.getEffectiveTimeMap(switchMode, switchTime);

            // 确保是lvs链路
            // TODO(wenfeng): 支持经典网络
            String connType = logicCustIns.getConnType();
            if (!CustinsSupport.CONN_TYPE_LVS.equalsIgnoreCase(connType)) {
                throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
            }

            String dbType = mysqlParamSupport.getAndCheckDBType(params, logicCustIns.getDbType());

            // 确保下发的是支持的版本
            String dbVersion = mysqlParamSupport.getCheckDBVersion(params, dbType, logicCustIns.getDbVersion());
            if (!supportDBVersion.contains(dbVersion)) {
                LOG_AGENT.info("unsupport the db version");
                throw new RdsException(ErrorCode.INVALID_PARAM_CATEGORY);
            }

            // 获取源有实例规格，确保是基础版
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(logicCustIns.getLevelId());
            //支持基础版和高可用迁移
            if (!(oldLevel.getCategory().equalsIgnoreCase(CATEGORY_BASIC) || oldLevel.getCategory().equalsIgnoreCase(CATEGORY_STANDARD))) {
                LOG_AGENT.info("unsupport the catagory");
                throw new RdsException(ErrorCode.INVALID_PARAM_CATEGORY);
            }

            // 备份恢复相关

            Integer diskSize = CheckUtils.parseInt(String.valueOf((logicCustIns.getDiskSize() / 1024)), CustinsSupport.ECS_MIN_DISK_SIZE,
                    102400, ErrorCode.INVALID_STORAGE);


            String diskType = getPrimaryCustinsStorageType(physicalCustIns.getId());

            // 当前只有云盘形态
            if (StringUtils.isBlank(diskType) || !ReplicaSetService.isStorageTypeCloudDisk(diskType)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // TODO(wenfeng): 支持经典网络
            List<CustinsConnAddrDO> privateNet = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(physicalCustIns.getId(), CustinsSupport.NET_TYPE_PRIVATE, CustinsSupport.RW_TYPE_NORMAL);
            if (!privateNet.isEmpty()) {
                LOG_AGENT.error("Private Net do not support migrate to k8s yet.");
                throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
            }

            // 获取源实例的用户vpc信息
            String vpcId = null;
            String vswitchId = null;
            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(physicalCustIns.getId(), CustinsSupport.NET_TYPE_VPC, CustinsSupport.RW_TYPE_NORMAL);
            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {

                if (custinsConnAddr.isConnAddrUserVisible() && custinsConnAddr.isVpcNetType()) {
                    vpcId = custinsConnAddr.getVpcId();
                    vswitchId = custinsConnAddr.getVswitchId();
                    if (StringUtils.isBlank(vswitchId)) {
                        vswitchId = custinsService.getVswitchIdByVpcIpAndVpcId(custinsConnAddr.getVip(), vpcId);
                        break;
                    }
                }
            }
            if (StringUtils.isEmpty(vpcId) || StringUtils.isEmpty(vswitchId)) {
                LOG_AGENT.error("vpcId {} or vswitchId {} is null.", vpcId, vswitchId);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            String portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), dbType);

            // 新建的临时实例
            destDBInstanceName = String.format("tmp-%s-%s", logicCustIns.getInsName(), (System.currentTimeMillis() / 1000L));

//            String connectionString = CheckUtils.checkValidForConnAddrCust(destDBInstanceName);

            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, oldLevel.getClassCode(), null);

            // 检验实例实例已经存在
            if (dBaasMetaService.getDefaultClient().getReplicaSet(requestId, destDBInstanceName, true) != null) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }

            String classCode = oldLevel.getClassCode();

            // 获取源实例的avz
            AVZInfo avzInfo = avzSupport.getAVZInfoFromCustInstance(logicCustIns);

            String dbEngine = "MySQL";
            Replica.RoleEnum[] nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER};
            //高可用版，支持两个节点
            if (oldLevel.getCategory().equalsIgnoreCase(CATEGORY_STANDARD)){
                nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE};
            }

            String subDomain = crossArchService.getLocationSubDomain(avzInfo.getRegion());

            // Buid replicaSet resource
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setUserId(bid);
            replicaSetResourceRequest.setUid(uid);
            replicaSetResourceRequest.setPort(portStr);
            replicaSetResourceRequest.setInsType(ReplicaSet.InsTypeEnum.MAIN.toString());
            replicaSetResourceRequest.setReplicaSetName(destDBInstanceName);
//            replicaSetResourceRequest.setDomainPrefix(connectionString);
            replicaSetResourceRequest.setDbType(dbType);
            replicaSetResourceRequest.setDbVersion(dbVersion);
            replicaSetResourceRequest.setConnType(connType);
            replicaSetResourceRequest.setBizType(bizType.toString());
            replicaSetResourceRequest.setClassCode(classCode);
            replicaSetResourceRequest.setStorageType(diskType);
            replicaSetResourceRequest.setDiskSize(diskSize);
            replicaSetResourceRequest.setVpcId(vpcId);
            replicaSetResourceRequest.setVswitchID(vswitchId);
            replicaSetResourceRequest.setSubDomain(subDomain);
            replicaSetResourceRequest.setRegionId(avzInfo.getRegionId());

            //检查实例指定小版本创建
            String targetMinorVersion = mysqlParamSupport.getParameterValue(params, "TargetMinorVersion");

            String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                    targetMinorVersion,
                    bizType,
                    dbType,
                    dbVersion,
                    dbEngine,
                    KindCodeParser.KIND_CODE_NEW_ARCH,
                    instanceLevel,
                    diskType,
                    false,
                    false,
                    false,
                    null);
            replicaSetResourceRequest.setComposeTag(serviceSpecTag);

            // Build replicaResource
            List<ReplicaResourceRequest> replicas = new ArrayList<>();

            boolean isSingleTenant = replicaSetService.isCloudSingleTenant(bizType, diskType, instanceLevel, isDhg);

            replicaSetResourceRequest.setSingleTenant(isSingleTenant);
            replicaSetResourceRequest.setEniDirectLink(false);

            //用户可见的磁盘空间
            replicaSetResourceRequest.setDiskSize(diskSize);

            for (int i = 0; i < nodeRoles.length; i++) {
                ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                replicaResourceRequest.setStorageType(diskType);
                replicaResourceRequest.setClassCode(classCode);
                replicaResourceRequest.setSingleTenant(isSingleTenant);
                if (ReplicaSetService.isStorageTypeCloudDisk(diskType)) {
                    // 赠送逻辑在任务流中进行
                    replicaResourceRequest.setDiskSize(diskSize);
                    // 基于快照来数据恢复
                    VolumeSpec volumeSpec = new VolumeSpec();
                    volumeSpec.setSnapshotId("");
                    volumeSpec.setName("data");
                    replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
                } else {
                    replicaResourceRequest.setDiskSize(diskSize);
                }

                replicaResourceRequest.setRole(nodeRoles[i].toString());
                AvailableZoneInfoDO availableZoneInfoDO = mySQLAvzService.getRoleZoneId(avzInfo, nodeRoles[i].toString());
                replicaResourceRequest.setZoneId(availableZoneInfoDO.getZoneID());

                replicas.add(replicaResourceRequest);
            }

            replicaSetResourceRequest.setReplicaResourceRequestList(replicas);

            //设置云上调度模板
            // TODO: 这里如果支持了只读实例迁移新架构，要正确设置InsType和PrimaryInsName
            replicaSetResourceRequest.setScheduleTemplate(
                    podTemplateHelper.getBizSysScheduleTemplate(
                            PodType.POD_RUNC,
                            ReplicaSet.BizTypeEnum.ALIYUN,
                            "mysql",
                            instanceLevel,
                            isSingleTenant,
                            ReplicaSet.InsTypeEnum.MAIN.toString(),
                            null,
                            null, mysqlParamSupport.getUID(params)
                    ).getValue()
            );

            podTemplateHelper.setSpecSchedulerConfigSpread(replicaSetResourceRequest, logicCustIns.getInsName());

            TransListDO translist = new TransListDO(logicCustIns, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
            translist.setsCinsid(logicCustIns.getId());
            translist.setdCinsid(logicCustIns.getId());
            translist.setsLevelid(oldLevel.getId());
            translist.setdLevelid(oldLevel.getId());
            translist.setSwitchTime(switchTime);
            this.instanceIDao.createTransList(translist);

            String taskKey = "transfer";

            // 新老架构迁移的任务来源
            String migrateTaskSource = mysqlParamSupport.getParameterValue(params, "migrateTaskSource", null);
            // 这里如果从用户小版本升级任务过来的，状态设置成小版本升级中,其他任务就显示其他状态
            String dbInstanceStatusDesc = StringUtils.isNotBlank(migrateTaskSource) ? migrateTaskSource : CustinsState.STATE_MAINTAINING.getComment();

            Map<String, Object> taskparam = new HashMap<>();
            taskparam.put(CustinsSupport.TRANS_ID, translist.getId());
            // 迁移新架构标识
            taskparam.put("readOnlyCount", readOnlyCount);
            taskparam.put("migratePengineToK8S", 1);
            taskparam.put("classCode", classCode);
            taskparam.put("requestId", requestId);
            taskparam.put("switch_info", effMap);

            taskparam.put("srcReplicaSetName", logicCustIns.getInsName());
            taskparam.put("srcPhysicalReplicaSetName", physicalCustIns.getInsName());
            taskparam.put("destReplicaSetName", destDBInstanceName);
            taskparam.put("replicasetName", destDBInstanceName);

            // 以下几个参数用于资源申请下沉
            taskparam.put("replicaSetResourceRequest", replicaSetResourceRequest);
            taskparam.put("category", oldLevel.getCategory());

            // 下发任务
            TaskQueueDO taskQueue = new TaskQueueDO(mysqlParameterHelper.getAction(), mysqlParameterHelper.getOperatorId(), logicCustIns.getId(),
                    TASK_TYPE_CUSTINS, taskKey);
            taskQueue.setParameter(JSONObject.toJSONString(taskparam));
            custinsService.updateCustInstanceStatusByCustinsId(logicCustIns.getId(), CustinsSupport.CUSTINS_STATUS_TRANS, dbInstanceStatusDesc);
            if (!physicalCustIns.getId().equals(logicCustIns.getId())) {
                custinsService.updateCustInstanceStatusByCustinsId(physicalCustIns.getId(), CustinsSupport.CUSTINS_STATUS_TRANS, dbInstanceStatusDesc);
            }
            taskService.createTaskQueue(taskQueue);
            instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
            Integer taskId = taskQueue.getId();
            taskparam.put("TaskId", taskId);

            // 设置锁定状态
            isLockBakSuccess = true;

            return taskparam;

        } catch (RdsException re) {
            LOG_AGENT.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            LOG_AGENT.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 异常需要进行备份锁定释放
            if (isLockBakSuccess) {
                this.unLockBakResource();
            }
        }
    }


    /**
     * 锁定备份资源，避免迁移恢复过程中被清理
     */
    private void lockBakResource(CustInstanceDO custInstance, BakhistoryDO bakHistory, Date restoreTimeBak) {
        if (restoreTimeBak != null) {
            bakService.lockBinlogForRestore(custInstance.getId(), bakHistory.getBakBegin(), restoreTimeBak); // lock binlog for restore
            ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                    JSON.toJSONString(ImmutableMap.of(
                            "custinsId", custInstance.getId().toString(),
                            "begin", bakHistory.getBakBegin().getTime(),
                            "end", restoreTimeBak.getTime())));
        }

        bakService.lockBakHisForRestore(bakHistory.getHisId());
        ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakHistory.getHisId()));
    }

    /**
     * API流程异常时，解锁备份资源
     */
    private void unLockBakResource() {
        if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")) {
            JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
            bakService.unlockBinlogForRestore(
                    Integer.valueOf(lockBinlog.get("custinsId").toString()),
                    new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                    new Date(Long.parseLong(lockBinlog.get("end").toString()))
            );
        }
        if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
            String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
            bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));
        }
    }

    /**
     * 获取 primary physical 实例 storageType
     * 参考 DescribeDBInstanceList接口
     *
     * @return
     */
    public String getPrimaryCustinsStorageType(Integer primaryCustinsId) throws Exception {

        String dataDiskCategory = null;
        if (primaryCustinsId != null) {
            List<EcsDiskDO> ecsDiskList = ecsDiskService.getEcsDiskByCustinsId(primaryCustinsId);
            if (ecsDiskList.size() == 0) {
                throw new RdsException(ErrorCode.INVALID_ECS_DATA_DISK_CATEGORY);
            }
            for (EcsDiskDO ecsDiskDO : ecsDiskList) {
                if (EcsConstants.DISK_TYPE_DATA.equals(ecsDiskDO.getType())) {
                    String performanLevel = ecsDiskService.getEcsTypeFromEcsDiskParamByDiskId(ecsDiskDO.getDiskId());
                    if (StringUtils.isEmpty(performanLevel)) {
                        dataDiskCategory = ecsDiskDO.getCategory();
                    } else {
                        if (supportCloudEssdPL.contains(performanLevel)) {
                            dataDiskCategory = CustinsParamSupport.getEssdPerLevel(performanLevel);
                        } else {
                            LOG_AGENT.info("unsupport the cloud essd pl");
                            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
                        }
                    }
                }
            }

            if (DockerOnEcsConstants.ECS_ClOUD_ESSD2.equalsIgnoreCase(dataDiskCategory)
                    || DockerOnEcsConstants.ECS_ClOUD_ESSD3.equalsIgnoreCase(dataDiskCategory)
                    || DockerOnEcsConstants.ECS_ClOUD_ESSD0.equalsIgnoreCase(dataDiskCategory)) {
                dataDiskCategory = DockerOnEcsConstants.ECS_ClOUD_ESSD;
            }
        }

        return dataDiskCategory;
    }

}

