package com.aliyun.dba.base.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.bak.dataobject.LogPlanDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.ClustersQuery;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class CrossArchService {
    private static final LogAgent logger = LogFactory.getLogAgent(CrossArchService.class);

    @Resource
    private ClusterService clusterService;
    @Resource
    protected ResourceService resourceService;
    @Resource
    private ClusterIDao clusterIDao;
    @Resource
    protected MysqlParameterHelper mysqlParaHelper;
    @Resource
    private BakService bakService;
    @Resource
    private RdsApi rdsApi;

    // 新老架构迁移新架构CLUSTER集群DBTYPE
    public final static String GLOBAL_DB_TYPE = "global";
    public final static String SUBDOMAIN_LOCATION_MAP = "SUBDOMAIN_LOCATION_MAP";

    /**
     * 检查是否开区cluster是否完成
     */
    public Boolean checkNewArmClusterSupport(Integer primaryCustinsId) {
        ClustersDO clustersDO = clusterService.getClusterByCustinsId(primaryCustinsId.longValue());
        ClustersQuery clustersQuery = new ClustersQuery();
        clustersQuery.setDbType(GLOBAL_DB_TYPE);
        String location = getLocationSubDomain(clustersDO.getLocation());
        clustersQuery.setSubDomain(location);
        clustersQuery.setIsAvail(1);
        List<ClustersDO> clustersDOList = clusterIDao.getClusters(clustersQuery);
        if (clustersDOList.size() > 0) {
            return true;
        }
        return false;
    }

    public String getLocationSubDomain(String location) {
        try {
            if (StringUtils.isBlank(location)) {
                return location;
            }
            ResourceDO resourceDO = this.resourceService.getResourceByResKey(SUBDOMAIN_LOCATION_MAP);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                JSONObject configMap = JSON.parseObject(resourceDO.getRealValue());
                String subDomainMap = configMap.getString(location);
                if (StringUtils.isNotEmpty(subDomainMap)) {
                    String newLocation = subDomainMap.trim();
                    logger.info("migrate k8s location {} replace to {} ", location, newLocation);
                    return newLocation;
                }
            }
        } catch (Exception e) {
            logger.error("try get location subdomain failed, error {}", e.getMessage());
        }
        return location;
    }

    public Date validRestoreByTime(CustInstanceDO custins) throws RdsException {
        if (CustinsSupport.CHARACTER_TYPE_PHYSICAL.equals(custins.getCharacterType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
        }
        if (!custins.isMbaseSql()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE);
        }
        LogPlanDO logPlan = bakService.getLogPlanByCustinsId(custins.getId());
        if (custins.isMbaseSql() && (logPlan == null || !logPlan.isEnableBackupLog())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_LOG_STATUS);
        }
        Date restoreTime = mysqlParaHelper.getAndCheckTimeByParam(ParamConstants.RESTORE_TIME,
                DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME, DataSourceMap.DATA_SOURCE_DBAAS);
        mysqlParaHelper.checkRestoreTimeValid(custins, restoreTime, logPlan);

        return restoreTime;
    }

    public Long validRestoreByBakset(CustInstanceDO custins) throws RdsException {
        if (CustinsSupport.CHARACTER_TYPE_PHYSICAL.equals(custins.getCharacterType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
        }
        Long bakId = CheckUtils.parseLong(mysqlParaHelper.getParameterValue("BackupSetID"), null,
                null, ErrorCode.BACKUPSET_NOT_FOUND);
        mysqlParaHelper.getAndCheckBakhistory(custins, bakId);
        return bakId;
    }

    public boolean onecsCloneToK8s(Map<String, String> map) throws RdsException {
        ResourceDO resource = resourceService.getResourceByResKey(PodDefaultConstants.DOCKERONECS_CLONE_TO_K8S_SWITCH);
        return resource != null && resource.getRealValue() != null && Objects.equals(resource.getRealValue(), "ON") && routeToK8s(map);
    }
    private boolean routeToK8s(Map<String, String> map) throws RdsException {
        Map<String, String> request = new HashMap<>();
        request.put("Action", "CheckNewArchCustins");
        request.put("User_ID", map.get("user_id"));
        request.put("UID", map.get("uid"));
        request.put("DBInstanceName", map.get("dbinstancename"));
        request.put("RegionID", map.get("regionid"));
        request.put("ZoneId", map.get("zoneid"));
        request.put("RequestId", map.get("requestid"));
        request.put("EngineVersion", map.get("engineversion"));
        request.put("Engine", map.get("engine"));
        request.put("DBInstanceClass", map.get("dbinstanceclass"));
        logger.info("params for call rdsapi CheckNewArchCustins:{}",JSON.toJSONString(request));
        Map<String, Object> result = rdsApi.getDataByRdsApi(request, ParamConstants.YAOCHI_ACCESS);
        logger.info("rdsapiResult:{}", result);
        return (Boolean) result.get("isNewArch");
    }

}
