/**
 * 
 */
package com.aliyun.dba.base.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.InspectService;
import com.aliyun.dba.base.service.InspectServiceImpl.FailInspectCommandException;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class BaseInspectDBInstanceImpl implements IAction {

	private static final String INSPECT_TASK_ID_RESPONSE_KEY = "TaskId";

	private static final String INSPECT_OUTPUT_RESPONSE_KEY = "output";

	private static final String INSPECT_HOST_ROLE = "inspect_host_role";

	private static final String INSPECT_COMMAND = "inspect_command";

	private static final String USELESS_INSPECT_TASK_IDS = "useless_inspect_task_ids";

	private static final LogAgent logger = LogFactory.getLogAgent(BaseInspectDBInstanceImpl.class);

	@Autowired
	private MysqlParameterHelper paramHelper;

	@Autowired
	private InspectService inspectService;
	
	@Autowired
	protected CustinsService custinsService;
	
	@Autowired
    private MysqlEngineCheckService mysqlEngineCheckService;

	@Override
	public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
			throws RdsException {
		logger.info("actionParams={}", JSONObject.toJSONString(actionParams));
		
		//设置参数
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        
        String requestId = paramHelper.getParameterValue("requestid", "MISSING");
        
        
        Map<String, Object> data = new HashMap<String, Object>(3);
		try {
			CustInstanceDO inputCustins = paramHelper.getAndCheckCustInstance();
			CustInstanceDO primaryCustins = null;
			
			logger.info("actionParams.requestid={}, inputCustins.character_type={}, inputCustins.kind_code={}",
					requestId, inputCustins.getCharacterType(),
					inputCustins.getKindCode());
			// 历史问题导致 mysql on docker on ecs弄了个非常别致的逻辑架构，上面有个logic实例，然后下面跟了个physical实例，
			// 这种两层拓扑里面，需要拿下面的physical实例来做体检，反之对其他的类型都是normal实例下面挂主机实例，没有这种严重的设计缺陷
			if (inputCustins.getCharacterType() != null && 
					 CustinsSupport.CHARACTER_TYPE_LOGIC.equals(inputCustins.getCharacterType()) && 
					 CustinsSupport.KIND_CODE_DOCKER_ON_ECS.equals(inputCustins.getKindCode())) {
				logger.info("actionParams.requestid={}, input custins={} is a logical instance of docker on ecs type", requestId, inputCustins.getId());
				boolean isUserCluster = mysqlEngineCheckService.checkUserClusterCustins(inputCustins);
				logger.info("actionParams.requestid={}, isUserCluster={}", requestId, isUserCluster);
				for (CustInstanceDO childCustIns : custinsService.getCustInstanceUnitByParentIdAndCharacterType(inputCustins.getId(),
	                    CustinsSupport.CHARACTER_TYPE_PHYSICAL)) {
	                if (childCustIns.isPrimary() && !isUserCluster) {
	                    primaryCustins = childCustIns;
	                    break;
	                } else if (isUserCluster && childCustIns.isPrimary() && childCustIns.getIsTmp() != 1) {
	                    primaryCustins = childCustIns;
	                    break;
	                }
	            }
			} else {
				primaryCustins = inputCustins;
			}
			logger.info("actual primary custins with requestid={}, id={}, name={}", requestId, primaryCustins.getId(), primaryCustins.getInsName());
			
			HashMap<String, String> opts = new HashMap<String, String>(1);
			if (paramHelper.hasParameter(USELESS_INSPECT_TASK_IDS)) {
				opts.put(USELESS_INSPECT_TASK_IDS, paramHelper.getParameterValue(USELESS_INSPECT_TASK_IDS));
			}
			
			String inspectCmd = null;
			if (paramHelper.hasParameter(INSPECT_COMMAND)) {
				inspectCmd = paramHelper.getParameterValue(INSPECT_COMMAND);
				opts.put(INSPECT_COMMAND, inspectCmd);
			}
			
			if (paramHelper.hasParameter(INSPECT_HOST_ROLE)) {
				opts.put(INSPECT_HOST_ROLE, paramHelper.getParameterValue(INSPECT_HOST_ROLE));
			}
			
			Integer taskId = inspectService.triggerInspectCustinsTask(paramHelper.getOperatorId(), primaryCustins, opts);
			
			data.put(INSPECT_TASK_ID_RESPONSE_KEY, taskId);
			
			// 单独把逻辑抽出来，便于之后重构
			if (inspectCmd != null) {
				try {
					JSONObject output = inspectService.reloadCommandOutput(taskId.longValue());
					data.put(INSPECT_OUTPUT_RESPONSE_KEY, output);
				} catch (FailInspectCommandException e) {
					logger.error("cannot inspect instance taskId={}, command={}", taskId, inspectCmd);
					data.put(INSPECT_OUTPUT_RESPONSE_KEY, e.getMessage());
				} catch (Exception e) {
					throw e;
				}
			}
		
			
		} catch (Exception e) {
			logger.error("requestid={}, cannot dispatch the inspect task due to {}", requestId, e);
		}

		return data;
	}

}
