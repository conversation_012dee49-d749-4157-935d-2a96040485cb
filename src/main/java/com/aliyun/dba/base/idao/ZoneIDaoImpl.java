package com.aliyun.dba.base.idao;

import com.aliyun.dba.base.dataobject.ZoneDO;
import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Repository("zoneIDao")
public class ZoneIDaoImpl implements ZoneIDao{

    @Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;

    @Override
    public String getZoneIdBySiteAndRegion(String siteName, String regionId) {
        Map<String, Object> condition =new HashMap<>();
        condition.put("siteName", siteName);
        condition.put("regionId", regionId);
        return sqlSessionTemplate.selectOne("getZoneIdBySiteAndRegion", condition);
    }
}
