package com.aliyun.dba.base.idao;

import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Repository("upgradeReportDao")
public class UpgradeReportIDaoImpl implements UpgradeReportIDao {

    @Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;
    @Override
    public List<UpgradeReportDO> getUpgradeReportByCondition(Map<String, Object> condition) {
        return sqlSessionTemplate.selectList("getUpgradeReportByCondition", condition);
    }

    @Override
    public Integer countUpgradeReportByCondition(Map<String, Object> condition) {
        return sqlSessionTemplate.selectOne("countUpgradeReportByCondition", condition);
    }
}
