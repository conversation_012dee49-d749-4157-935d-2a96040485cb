package com.aliyun.dba.base.idao;

import com.aliyun.dba.base.dataobject.ApplyResourceDO;
import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository("applyResourceIDao")
public class ApplyResourceIDaoImpl implements ApplyResourceIDao{

    @Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;

    @Override
    public Integer addApplyResource(ApplyResourceDO applyResourceDO) {

        return sqlSessionTemplate.insert("addApplyResource", applyResourceDO);
    }

    @Override
    public List<ApplyResourceDO> queryApplyResourceRecords(Map<String, Object> queryCondition) {
        return sqlSessionTemplate.selectList("queryApplyResourceByCondition", queryCondition);
    }

    @Override
    public Integer queryApplyResourceRecordCount(Map<String, Object> queryCondition) {
        return sqlSessionTemplate.selectOne("queryApplyResourceRecordCount", queryCondition);
    }

    @Override
    public void cancelApplyResource(Integer userId, Long applyResourceId, String description) {
        Map<String, Object> params =new HashMap<>();
        params.put("userId", userId);
        params.put("applyResourceId", applyResourceId);
        params.put("description", description);
        sqlSessionTemplate.update("cancelApplyResource", params);
    }
}
