package com.aliyun.dba.base.idao;

import java.io.Serializable;
import java.util.Date;

public class UserKmsRelDO implements Cloneable, Serializable {
    private Integer id;
    private Date gmtCreated;
    private Date gmtModified;
    private Integer creator;
    private Integer modifier;

    private Integer userId;
    private Integer kmsId;
    private String masterKey;
    private String comment;

    public Integer getId() {return id;}

    public void setId(Integer id) {this.id = id;}

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getCreator() {return creator;}

    public void setCreator(Integer creator) {this.creator = creator;}

    public Integer getModifier() {return modifier;}

    public void setModifier(Integer modifier) {this.modifier = modifier;}

    public Integer getUserId() {return userId;}

    public void setUserId(Integer userId) {this.userId = userId;}

    public Integer getKmsId() {return kmsId;}

    public void setKmsId(Integer kmsId) {this.kmsId = kmsId;}

    public String getMasterKey() {return masterKey;}

    public void setMasterKey(String masterKey) {this.masterKey = masterKey;}

    public String getComment() {return comment;}

    public void setComment(String comment) {this.comment = comment;}
}
