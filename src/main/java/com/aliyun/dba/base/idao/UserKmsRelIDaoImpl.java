package com.aliyun.dba.base.idao;

import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Map;

@Repository("userKmsRelDao")
public class UserKmsRelIDaoImpl implements UserKmsRelIDao{

    @Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;

    @Override
    public UserKmsRelDO getUserKmsRelByKmsIdAndUserId(Map<String, Object> condition) {
        return sqlSessionTemplate.selectOne("getUserKmsRelByKmsIdAndUserId", condition);
    }

    @Override
    public int addUserKmsRel(UserKmsRelDO userKmsRelDO) {
        return sqlSessionTemplate.update("addUserKmsRel", userKmsRelDO);
    }
}