package com.aliyun.dba.base.idao;

import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("mysqlCustinsParamIDao")
public class MysqlCustinsParamIDaoImpl implements MysqlCustinsParamIDao {

    @Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;

    @Override
    public List<String> getInsNameByCustinsParam(String paramName, String paramValue, String dbType) {

        Map<String, Object> condition = new HashMap<>();
        condition.put("dbType", dbType);
        condition.put("paramName", paramName);
        condition.put("paramValue", paramValue);

        return sqlSessionTemplate.selectList("getInsNameByCondition", condition);
    }

    @Override
    public List<Map<String,Object>> getInsParamGroupByCondition(String paramGroupId, String dbType, String dbVersion) {

        Map<String, Object> condition = new HashMap<>();
        condition.put("paramGroupId", paramGroupId);
        condition.put("dbType", dbType);
        condition.put("dbVersion", dbVersion);

        return sqlSessionTemplate.selectList("getInsParamGroupByCondition", condition);
    }
}
