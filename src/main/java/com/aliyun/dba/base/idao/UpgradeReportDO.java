package com.aliyun.dba.base.idao;

import java.io.Serializable;
import java.util.Date;

public class UpgradeReportDO implements Cloneable, Serializable {
    /**
     * upgrade report ID
     */
    private Integer id;

    /**
     * 创建时间
     */
    private Date gmtCreated;


    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 源实例名
     */
    private String srcInsName;

    /**
     * 源实例版本号
     */
    private String srcVersion;

    /**
     * 目标实例名（pg独有）
     */
    private String dstInsName;

    /**
     * 目标实例版本号
     */
    private String dstVersion;

    /**
     * check：校验;upgrade:升级
     */
    private String type;

    /**
     * switch:割接模式;clone:克隆模式
     * PG独有
     */
    private String upgradeMode;

    /**
     * before:切换前收集统计信息;after:切换后收集
     * PG独有
     */
    private String collectStatMode;


    /**
     * success/fail
     */
    private String result;

    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 校验时间
     */
    private Date checkTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getSrcInsName() {
        return srcInsName;
    }

    public void setSrcInsName(String srcInsName) {
        this.srcInsName = srcInsName;
    }

    public String getSrcVersion() {
        return srcVersion;
    }

    public void setSrcVersion(String srcVersion) {
        this.srcVersion = srcVersion;
    }

    public String getDstInsName() {
        return dstInsName;
    }

    public void setDstInsName(String dstInsName) {
        this.dstInsName = dstInsName;
    }

    public String getDstVersion() {
        return dstVersion;
    }

    public void setDstVersion(String dstVersion) {
        this.dstVersion = dstVersion;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUpgradeMode() {
        return upgradeMode;
    }

    public void setUpgradeMode(String upgradeMode) {
        this.upgradeMode = upgradeMode;
    }

    public String getCollectStatMode() {
        return collectStatMode;
    }

    public void setCollectStatMode(String collectStatMode) {
        this.collectStatMode = collectStatMode;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public Date getSwitchTime() {
        return switchTime;
    }

    public void setSwitchTime(Date switchTime) {
        this.switchTime = switchTime;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    /**
     * 切换时间
     */
    private Date switchTime;

    /**
     * 切换时间
     */
    private Date effectiveTime;

    /**
     * 报告详情
     */
    private String detail;

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }
}
