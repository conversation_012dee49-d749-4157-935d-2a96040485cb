package com.aliyun.dba.base.idao;

import com.aliyun.dba.base.dataobject.ApplyResourceDO;

import java.util.List;
import java.util.Map;

public interface ApplyResourceIDao {

    public Integer addApplyResource(ApplyResourceDO applyResourceDO);

    public List<ApplyResourceDO> queryApplyResourceRecords(Map<String, Object> queryCondition);

    public Integer queryApplyResourceRecordCount(Map<String, Object> queryCondition);

    public void cancelApplyResource(Integer userId, Long applyResourceId, String description);
}
