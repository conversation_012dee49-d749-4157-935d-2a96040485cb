package com.aliyun.dba.base.idao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;

@Repository("pengineDao")
public class PengineIDaoImpl implements PengineIDao {
	
	@Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;

	@Override
	public List<PengineBlobInfo> listPengineBlobInfoByTaskId(Long taskId) {
		Map<String, Object> condition = new HashMap<>();
        condition.put("taskId", taskId);
		return sqlSessionTemplate.selectList("listPengineBlobInfoByTaskId", condition);
	}
}
