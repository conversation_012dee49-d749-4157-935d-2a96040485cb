package com.aliyun.dba.base.cache;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.SimpleIdGenerator;
import org.springframework.util.StringUtils;

@Service
public class LocalCacheService {

    @Autowired
    private DBaasMetaService dBaasMetaService;


    /**
     * 获取value,优先从cache获取，不存在查库放入cache
     *
     * @param key
     * @return value
     */
    @Cacheable(value = "value", key = "#key")
    public String getValue(String key) throws RdsException{
        try {
            ConfigListResult configListResult = dBaasMetaService.getDefaultClient().listConfigs(new SimpleIdGenerator().generateId().toString(), key);
            if (configListResult != null && CollectionUtils.isNotEmpty(configListResult.getItems())) {
                return configListResult.getItems().get(0).getDisplayValue();
            }
        }catch (Exception e){
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataFailed", "meta failed."});
        }
        return null;
    }

    public String getValueOrDefault(String key, String defaultValue) throws RdsException{
        String value = getValue(key);
        return StringUtils.isEmpty(value) ? defaultValue : value;
    }

    /**
     * delete cache value
     * @param key
     * @return
     * @throws ApiException
     */
    @CacheEvict(value = "value", key = "#key")
    public String deleteValue(String key) throws ApiException {
        return key;
    }
}
