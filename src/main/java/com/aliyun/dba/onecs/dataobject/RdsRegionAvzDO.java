package com.aliyun.dba.onecs.dataobject;

public class RdsRegionAvzDO {

    private long id;
    private String region;
    private String regionName;
    private String avz;
    private String avzName;
    private String subDomain;
    private String bizType;
    private String regionCategory;

    public RdsRegionAvzDO(){

    }

    public RdsRegionAvzDO(long id, String region, String regionName, String avz, String avzName,
                          String subDomain, String bizType, String regionCategory) {
        this.id = id;
        this.region = region;
        this.regionName = regionName;
        this.avz = avz;
        this.avzName = avzName;
        this.subDomain = subDomain;
        this.bizType = bizType;
        this.regionCategory = regionCategory;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getAvz() {
        return avz;
    }

    public void setAvz(String avz) {
        this.avz = avz;
    }

    public String getAvzName() {
        return avzName;
    }

    public void setAvzName(String avzName) {
        this.avzName = avzName;
    }


    public String getSubDomain() {
        return subDomain;
    }

    public void setSubDomain(String subDomain) {
        this.subDomain = subDomain;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getRegionCategory() {
        return regionCategory;
    }

    public void setRegionCategory(String regionCategory) {
        this.regionCategory = regionCategory;
    }
}
