package com.aliyun.dba.onecs.dataobject;

public class ClusterDO {

    public long id;
    public String clustername;
    public int priority;
    public int creator;
    public int modifier;
    public long gmtCreated;
    public long gmtModified;
    public String comment;
    public String location;
    public int isAvail;
    public String adminIps;
    public String connType;
    public String dbType;
    public int bcRetention;
    public int syncMode;
    public int isMultiSite;
    public String siteName;
    public int resReservePercent;
    public int maxSellScale;
    public int resourceMode;
    public int hostBufferSize;

    public ClusterDO(){

    }

    public ClusterDO(long id, String clustername, int priority, int creator, int modifier, long gmtCreated,
                     long gmtModified, String comment, String location, int isAvail, String adminIps,
                     String connType, String dbType, int bcRetention, int syncMode, int isMultiSite,
                     String siteName, int resReservePercent, int maxSellScale, int resourceMode, int hostBufferSize) {
        this.id = id;
        this.clustername = clustername;
        this.priority = priority;
        this.creator = creator;
        this.modifier = modifier;
        this.gmtCreated = gmtCreated;
        this.gmtModified = gmtModified;
        this.comment = comment;
        this.location = location;
        this.isAvail = isAvail;
        this.adminIps = adminIps;
        this.connType = connType;
        this.dbType = dbType;
        this.bcRetention = bcRetention;
        this.syncMode = syncMode;
        this.isMultiSite = isMultiSite;
        this.siteName = siteName;
        this.resReservePercent = resReservePercent;
        this.maxSellScale = maxSellScale;
        this.resourceMode = resourceMode;
        this.hostBufferSize = hostBufferSize;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getClustername() {
        return clustername;
    }

    public void setClustername(String clustername) {
        this.clustername = clustername;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public int getCreator() {
        return creator;
    }

    public void setCreator(int creator) {
        this.creator = creator;
    }

    public int getModifier() {
        return modifier;
    }

    public void setModifier(int modifier) {
        this.modifier = modifier;
    }

    public long getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(long gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public long getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(long gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public int getIsAvail() {
        return isAvail;
    }

    public void setIsAvail(int isAvail) {
        this.isAvail = isAvail;
    }

    public String getAdminIps() {
        return adminIps;
    }

    public void setAdminIps(String adminIps) {
        this.adminIps = adminIps;
    }

    public String getConnType() {
        return connType;
    }

    public void setConnType(String connType) {
        this.connType = connType;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public int getBcRetention() {
        return bcRetention;
    }

    public void setBcRetention(int bcRetention) {
        this.bcRetention = bcRetention;
    }

    public int getSyncMode() {
        return syncMode;
    }

    public void setSyncMode(int syncMode) {
        this.syncMode = syncMode;
    }

    public int getIsMultiSite() {
        return isMultiSite;
    }

    public void setIsMultiSite(int isMultiSite) {
        this.isMultiSite = isMultiSite;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public int getResReservePercent() {
        return resReservePercent;
    }

    public void setResReservePercent(int resReservePercent) {
        this.resReservePercent = resReservePercent;
    }

    public int getMaxSellScale() {
        return maxSellScale;
    }

    public void setMaxSellScale(int maxSellScale) {
        this.maxSellScale = maxSellScale;
    }

    public int getResourceMode() {
        return resourceMode;
    }

    public void setResourceMode(int resourceMode) {
        this.resourceMode = resourceMode;
    }

    public int getHostBufferSize() {
        return hostBufferSize;
    }

    public void setHostBufferSize(int hostBufferSize) {
        this.hostBufferSize = hostBufferSize;
    }
}