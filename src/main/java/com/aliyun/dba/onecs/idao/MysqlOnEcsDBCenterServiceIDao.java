package com.aliyun.dba.onecs.idao;

import com.aliyun.dba.ecs.dataobject.EcsImageDO;
import com.aliyun.dba.ecs.dataobject.EcsSecurityGroupDO;

import java.util.List;

public interface MysqlOnEcsDBCenterServiceIDao {

    List<EcsImageDO> getEcsImageDOList(EcsImageDO ecsImageDOCondition, int pageStart, int pageSize);

    List<EcsSecurityGroupDO> getEcsSecurityGroupDOList(EcsSecurityGroupDO ecsSecurityGroupDO, int pageStart, int pageSize);
}
