package com.aliyun.dba.onecs.idao;

import com.aliyun.dba.ecs.dataobject.EcsImageDO;
import com.aliyun.dba.ecs.dataobject.EcsSecurityGroupDO;
import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("mysqlOnEcsDBCenterServiceIDao")
public class MysqlOnEcsDBCenterServiceIDaoImpl implements MysqlOnEcsDBCenterServiceIDao{

    @Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;

    @Override
    public List<EcsImageDO> getEcsImageDOList(EcsImageDO ecsImageDOCondition, int pageStart, int pageSize) {

        Map<String, Object> condition = new HashMap<>();
        condition.put("obj", ecsImageDOCondition);
        condition.put("pageStart", pageStart);
        condition.put("pageSize", pageSize);

        return sqlSessionTemplate.selectList("getEcsImageDOList", condition);
    }

    @Override
    public List<EcsSecurityGroupDO> getEcsSecurityGroupDOList(EcsSecurityGroupDO ecsSecurityGroupDO, int pageStart,
                                                              int pageSize) {
        Map<String, Object> condition = new HashMap<>();
        condition.put("obj", ecsSecurityGroupDO);
        condition.put("pageStart", pageStart);
        condition.put("pageSize", pageSize);

        return sqlSessionTemplate.selectList("getEcsSecurityGroupDOList", condition);
    }
}
