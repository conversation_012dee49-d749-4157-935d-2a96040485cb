package com.aliyun.dba.onecs.idao;

import com.aliyun.dba.onecs.dataobject.ClusterDO;
import com.aliyun.dba.onecs.dataobject.RdsRegionAvzDO;
import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository("mysqlOnEcsRegionServiceIDao")
public class MysqlOnEcsRegionServiceIDaoImpl implements MysqlOnEcsRegionServiceIDao{

    @Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;

    @Override
    public ClusterDO getClusterByName(String clusterName) {
        return sqlSessionTemplate.selectOne("getClusterByName",clusterName);
    }

    @Override
    public List<RdsRegionAvzDO> getRdsRegionAvzDOByLocation(String location){

        List<RdsRegionAvzDO> rdsRegionAvzDOList = sqlSessionTemplate.selectList("getRdsRegionAvzDOByLocation",location);
        return rdsRegionAvzDOList;
    }
}
