package com.aliyun.dba.onecs.idao;

import com.aliyun.dba.onecs.dataobject.ClusterDO;
import com.aliyun.dba.onecs.dataobject.RdsRegionAvzDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MysqlOnEcsRegionServiceIDao {

    ClusterDO getClusterByName(@Param("clusterName") String clusterName);

    List<RdsRegionAvzDO> getRdsRegionAvzDOByLocation(@Param("subDomain") String location);

}
