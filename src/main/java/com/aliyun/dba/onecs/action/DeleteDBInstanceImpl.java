package com.aliyun.dba.onecs.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsSearchDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsSearchService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.api.OpenSearchApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsDeleteDBInstanceImpl")
public class DeleteDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(DeleteDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsSearchService custinsSearchService;
    @Autowired
    protected UserService userService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
        RdsException {

        try {

            custins = mysqlParamSupport.getCustInstance(actionParams);
            if (custins == null) {
                if (custinsService.checkDeletedCustInstanceByInsName(mysqlParamSupport.getDBInstanceName(actionParams))) {
                    //实例已销毁
                    return createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);
                }
                //实例不存在，或者不是实例拥有者
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //原逻辑
            if (!custins.isNormal() && !custins.isMysqlLogic()) {
                logger.warn("Can't delete custins not normal or logic from this api.");
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            //发生容灾切换时，禁止删除主实例和灾备实例
            if ((custins.isGuard() && custins.isLogicPrimary()) || (custins.isPrimary() && custins.isLogicGuard())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            Integer deleteMode = CustinsSupport.GRACE_DELETE;
            if (mysqlParamSupport.hasParameter(actionParams, "deletemode")) {
                deleteMode = CustinsSupport.getDeleteMode(mysqlParamSupport.getParameterValue(actionParams, "deletemode"));
            }
            //如果需要删除的实例是主实例的话，则需要确保该主实例所关联的灾备实例和只读实例已经被删除
            if (custins.isPrimary()) {
                //查询灾备实例，如果有灾备实例的话，则禁止删除
                CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                //过滤掉正在删除的实例
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_GUARD);//灾备实例
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                //查询只读实例，如果有只读实例的话，则禁止删除
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                //过滤掉正在删除的实例
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_READ);//只读实例
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
            }

            //统一优化到一个方法中
            mysqlParamSupport.checkNotDeleteHaProxyCustins(custins);

            //当前mysql实例为读实例
            if (custins.isRead()) {
                CustInstanceDO primaryins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(primaryins.getId(), null, CustinsSupport.RW_TYPE_RW_SPLIT);

                if (taskService.getCustinsRunningTask(primaryins.getId(), TaskSupport.TASK_CREATE_RW_SPLIT_VIP)) {
                    return createErrorResponse(ErrorCode.UNFINISHED_CREATE_RW_TASK);
                }

                if (custinsConnAddrList.size() > 0) {
                    List<CustInstanceDO> readinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(primaryins.getId(), false);
                    List<CustInstanceDO> validReadInslist = new ArrayList<>();
                    for (CustInstanceDO readins : readinsList) {
                        if (!CustinsSupport.CUSTINS_STATUS_DELETING.equals(readins.getStatus()) &&
                            !CustinsSupport.CUSTINS_STATUS_CREATING.equals(readins.getStatus())) {
                            validReadInslist.add(readins);
                        }
                    }
                    if (validReadInslist.size() == 1) {
                        // 如果存在读写分离vip，那么不允许删除最后一个只读实例。
                        // 除非指定参数强制删除只读实例，那么先触发删除主实例的读写分离vip任务，
                        // 再触发删除只读实例任务。
                        if (CustinsSupport.GRACE_DELETE.equals(deleteMode)) {
                            return createErrorResponse(ErrorCode.RW_SPLIT_NETTYPE_EXIST);
                        } else {
                            CustinsConnAddrDO delCustinsConnAddr = custinsConnAddrList.get(0);
                            ConnAddrChangeLogDO delConnAddrChangeLog = ConnAddrSupport
                                .createConnAddrChangeLogForDeleteNetType(
                                    primaryins.getId(),
                                    delCustinsConnAddr.getNetType(),
                                    delCustinsConnAddr.getConnAddrCust(),
                                    delCustinsConnAddr.getVip(),
                                    delCustinsConnAddr.getVport(),
                                    delCustinsConnAddr.getUserVisible(),
                                    delCustinsConnAddr.getTunnelId(),
                                    delCustinsConnAddr.getVpcId(),
                                    null,
                                    CustinsSupport.RW_TYPE_RW_SPLIT);

                            List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(1);
                            connAddrChangeLogs.add(delConnAddrChangeLog);

                            try {
                                Integer taskId = taskService.changeConnAddrTask(mysqlParamSupport.getAction(actionParams), primaryins, connAddrChangeLogs,
                                    CustinsState.STATUS_ACTIVATION, TaskSupport.TASK_CHANGE_CONN_ADDR_DELETE_VIP,
                                    mysqlParamSupport.getOperatorId(actionParams));
                                taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));
                            } catch (Exception ex) {
                                logger.error("Custins: " + primaryins.getId()
                                    + " DeleteDBInstanceRWSplitNetType failed when create task. Details: "
                                    + JSON.toJSONString(connAddrChangeLogs));
                                throw new Exception(ex);
                            }
                        }
                    }
                }
            }
            if (custinsSearchService.checkCustinsSearch(custins)) {
                String apiUrlString = userService.getOpenSearchApiUrlByClusterName(custins.getClusterName());
                if (apiUrlString == null) {
                    return createErrorResponse(ErrorCode.OPENSEARCH_NOT_SUPPORT);
                }
                JSONObject apiUrl = JSON.parseObject(apiUrlString);
                OpenSearchApi api = new OpenSearchApi(apiUrl.getString("host"),
                    apiUrl.getString("accesskey"),
                    apiUrl.getString("secret"));
                CustinsSearchDO custinsSearch = custinsSearchService.getCustinsSearchByCustins(custins);
                api.deleteOpenSearchInstance(custinsSearch.getAppName());
                custinsSearchService.deleteCustinsSearch(custinsSearch);
            }

            //删除实例
            Integer taskId = -1;
            taskId = taskService.deleteCustInstanceAndTask(mysqlParamSupport.getAction(actionParams), custins,
                mysqlParamSupport.getOperatorId(actionParams));
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));

            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
