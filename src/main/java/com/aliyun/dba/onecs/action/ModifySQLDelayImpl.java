package com.aliyun.dba.onecs.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

//onecs 环境下无该接口
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsModifySQLDelayImpl")
public class ModifySQLDelayImpl implements IAction {

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }
}
