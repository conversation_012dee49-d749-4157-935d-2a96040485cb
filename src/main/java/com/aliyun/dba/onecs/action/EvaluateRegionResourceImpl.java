package com.aliyun.dba.onecs.action;

import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.EcsResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.response.EvaluateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_DOCKER;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_DOCKER_ON_ECS;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_ECS_VM;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsEvaluateRegionResourceImpl")
public class EvaluateRegionResourceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(EvaluateRegionResourceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateRegionResourceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected EcsService ecsService;

    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected AVZSupport avzSupport;
    @Resource
    private com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl poddefaultEvaluateDockerToK8SResource;
    @Resource
    private CrossArchService crossArchService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {
            if (crossArchService.onecsCloneToK8s(actionParams)) {
                logger.info("docker restore. evaluate k8s resource.");
                return poddefaultEvaluateDockerToK8SResource.doActionRequest(custins, actionParams);
            }
            String region = mysqlParamSupport.getAndCheckRegion(actionParams);
            // Engine参数必传
            
            String dbType = mysqlParamSupport.getAndCheckDBType(actionParams, null);
            // Docker 实例集群的db_type类型为docker,引入临时变量做调整
            
            String dbTypeForCluster = dbType;
            String classCode = mysqlParamSupport.getAndCheckClassCode(actionParams);

            String dbVersion = mysqlParamSupport.getAndCheckDBVersion(actionParams, dbType, false);
            Integer bizType = mysqlParamSupport.getAndCheckBizType(actionParams);

            mysqlParamSupport.getAndSetContainerTypeAndHostTypeIfEmpty(actionParams, dbType, dbVersion, classCode);

            String containerType = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CONTAINER_TYPE,
                CustinsSupport.CONTAINER_TYPE_HOST);
            
            String hostType = mysqlParamSupport.getAndCheckHostType(actionParams);

            custins = new CustInstanceDO();
            custins.setDbType(dbType);
            custins.setDbVersion(dbVersion);

            custins = mysqlParamSupport.setInstanceLevel(custins, classCode, bizType,
                mysqlParamSupport.getParameterValue(actionParams, ParamConstants.STORAGE));

            if (CustinsSupport.isCustinsOnEcs(hostType, dbVersion, null)) {
                custins.setKindCode(KIND_CODE_ECS_VM);
            }
            if ((CustinsSupport.isContainerTypeDocker(containerType)
                && CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD.equals(hostType))
                || CustinsSupport.isDockeronEcs(dbVersion, dbType)) {
                custins.setKindCode(KIND_CODE_DOCKER_ON_ECS);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }
            if (StringUtils.equals(CustinsSupport.DB_TYPE_POLARDB_MYSQL, dbType)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE);
            }
            if (CustinsSupport.isContainerTypeDocker(containerType)
                && !CustinsSupport.KIND_CODE_DOCKER_ON_ECS.equals(custins.getKindCode())
                && !CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE.equals(custins.getKindCode())) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }
            custins.setDbTypeForCluster(dbTypeForCluster);

            //原逻辑
            region = avzSupport.getMainLocation(actionParams);
            String regionId = mysqlParamSupport.getAndCheckRegionID(actionParams);
            String zoneId = mysqlParamSupport.getAndCheckAvZone(actionParams);
            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(actionParams);
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            String ecsAccount = ecsService.getEcsAccount(custins.getUserId(), regionId);

            Map<String, Object> data = new HashMap<String, Object>();

            List<EcsResModel> ecsResModelList = Lists.newLinkedList();
            EcsResModel ecsResModel = new EcsResModel(null);
            ecsResModel.setEcsAccount(ecsAccount);
            ecsResModel.setInsCount(1);

            // 资源管理器有检验这个
            ecsResModel.setImageId("dummy");
            ecsResModel.setEcsVSwitchId("dummy");
            ecsResModel.setEcsVpcId("dummy");

            ecsResModel.setZoneId(zoneId);
            ecsResModel.setRegionId(regionId);
            ecsResModel.setInstanceType(insLevel.getEcsClassCode());
            ecsResModelList.add(ecsResModel);

            CustinsResModel custinsRes = new CustinsResModel(null);
            custinsRes.setEcsResModelList(ecsResModelList);

            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(
                 actionParams, custins.getDbType());
            resourceContainer.setRequestId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID));
            resourceContainer.addCustinsResModel(custinsRes);
            resourceContainer.setEvaluateNum(evaluateNum);

            Response<EvaluateResRespModel> response = resApi.evaluateRes(resourceContainer);
            if (response.getCode().equals(200)) {
                data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                data.put(ParamConstants.SUBDOMAIN_AVAILABLE_DETAIL, response.getData().getSudomainDetails());
            } else {
                data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                data.put(ParamConstants.ERROR_MESSAGE, ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            data.put(ParamConstants.ENGINE_VERSION, custins.getDbVersion());
            data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(custins.getDbType()));
            return data;
        } catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
