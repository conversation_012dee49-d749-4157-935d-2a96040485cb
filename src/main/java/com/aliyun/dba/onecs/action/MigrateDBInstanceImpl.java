package com.aliyun.dba.onecs.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

/**
 * 处理逻辑同ModifyDBInstanceClassImpl，只是isTransfer为true
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsMigrateDBInstanceImpl")
public class MigrateDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(MigrateDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(MigrateDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {
            boolean isTransfer = true;
            mysqlParamSupport.setParameter(actionParams, "isTransfer", String.valueOf(isTransfer));
            ModifyDBInstanceClassImpl onecsModifyDBInstanceClassImpl = SpringContextUtil.getBeanByClass(ModifyDBInstanceClassImpl.class);
            return onecsModifyDBInstanceClassImpl.doActionRequest(custins, actionParams);
        }
        catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
