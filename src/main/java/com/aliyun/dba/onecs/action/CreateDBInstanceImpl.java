package com.aliyun.dba.onecs.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.commonkindcode.support.helper.TimezoneHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.ecs.service.EcsInsDirector;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.service.MysqlOnEcsBuilder;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_ECS_VM;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_NC;

//action: CreateDBInstance
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsCreateDBInstanceImpl")
public class CreateDBInstanceImpl implements IAction {

    //private static Logger logger =Logger.getLogger(CreateDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CreateDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected EcsInsDirector ecsInsDirector;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Resource
    private DbsGateWayService dbsGateWayService;
    @Resource
    private MySQLServiceImpl mySQLservice;
    @Resource
    private CrossArchService crossArchService;
    @Resource
    private com.aliyun.dba.poddefault.action.CreateDBInstanceImpl poddefaultCreateDBInstance;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            //得到实例类型，版本
            String dbType = mysqlParamSupport.getAndCheckDBType(params, null);
            String dbVersion = mysqlParamSupport.getAndCheckDBVersion(params, dbType, true);
            String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);

            String backupSetId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
            if (mySQLservice.isRebuildBackupSet(backupSetId)) {
                DescribeRestoreBackupSetParam caller = dbsGateWayService.describeRestoreBackupSetBuilder(params,mysqlParamSupport.getBID(params));
                DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(caller);
                if (restoreBackupResponse.getBackupSetInfo() == null) {
                    throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
                }
                String backupId = restoreBackupResponse.getBackupSetInfo().getBackupId();
                if (StringUtils.isBlank(backupId)) {
                    throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
                }
                Integer sourceDBInstanceId = restoreBackupResponse.getBackupSetInfo().getCustinsId();
                params.put("sourcedbinstanceid",String.valueOf(sourceDBInstanceId));
                params.remove("sourcedbinstancename");
                params.put("backupsetid",backupId);
                params.put("restoreType",RESTORE_TYPE_BAKID);
                params.put(ParamConstants.IS_REBUILD_DELETED,ParamConstants.ENABLE_REBUILD);
                mySQLservice.compareBakSizeAndDiskSize(restoreBackupResponse,mysqlParamSupport.getAndCheckStorage(params));
                mySQLservice.checkCustinsAndUser(restoreBackupResponse.getBackupSetInfo().getCustinsId(),mysqlParamSupport.getBID(params),mysqlParamSupport.getUID(params));
                String backupMinorVersion = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getMINOR_VERSION();
                try {
                    String finalMinorVersion = minorVersionServiceHelper.checkAndGetAllMinorVersion(
                            dbType, dbVersion, classCode, KindCodeParser.KIND_CODE_ECS_VM, MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL, backupMinorVersion);
                    params.put("TargetMinorVersion",finalMinorVersion);
                } catch (RdsException re) {
                    logger.warn("rebuild deleted ins, minor version is not find: {}", backupMinorVersion);
                }
                String bakInstanceKindCode = restoreBackupResponse.getBackupSetInfo().getInstanceKindCode();
                if (StringUtils.isBlank(bakInstanceKindCode) || !KindCodeParser.KIND_CODE_ECS_VM.equals(Integer.valueOf(bakInstanceKindCode))){
                    throw new RdsException(ErrorCode.UNSUPPORTED_KIND_CODE);
                }
                if (crossArchService.onecsCloneToK8s(params)) {
                    params.put("restorefromrecyclebin", "true");
                    return poddefaultCreateDBInstance.doActionRequest(custins, params);
                }
            }
            if (mysqlParamSupport.getSourceDBInstanceID(params) != null) {
                CustInstanceDO instance = mysqlParamSupport.getAndCheckCustInstanceById(params, "sourcedbinstanceid");
                if (!instance.getDbType().equals(dbType)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DB_TYPE);
                }

                if (!instance.getDbVersion().equals(dbVersion)) {
                    return createErrorResponse(ErrorCode.INVALID_MINOR_VERSION);
                }
                if (crossArchService.onecsCloneToK8s(params)) {
                    params.put("restorefromrecyclebin", "true");
                    return poddefaultCreateDBInstance.doActionRequest(custins, params);
                }
            }

            if (mysqlParamSupport.hasParameter(params, ParamConstants.DB_PARAM_GROUP_ID)) {
                String paramGroupId = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_PARAM_GROUP_ID,"");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(paramGroupId)) {
                    // 参数模板信息检查
                    // FIXME：此处暂时忽略category校验与存储引擎校验
                    SysParamGroupHelper.sysParamGroupIdValidation(paramGroupId, dbType, dbVersion, "", "");
                    parameterGroupTemplateGenerator.paramGroupMatchValidate(dbType, dbVersion, "", paramGroupId, false);
                }
            }

            mysqlParamSupport.getAndSetContainerTypeAndHostTypeIfEmpty(params, dbType, dbVersion, classCode);

            if (mysqlParamSupport.hasParameter(params, ParamConstants.DB_INSTANCE_ID)) {
                // Resource allocated already. just update it.
                Integer custinsId = Integer.parseInt(
                    mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_ID));
                custins = custinsService.getCustInstanceByCustinsId(custinsId);
                if (custins == null) {
                    return createErrorResponse(ErrorCode.INVALID_INSTANCE_ID);
                }

                Integer userId = mysqlParamSupport.getAndCreateUserId(params);
                custins.setUserId(userId);

            } else {
                custins = new CustInstanceDO(mysqlParamSupport.getAndCreateUserId(params),
                    mysqlParamSupport.getAndCheckServiceType(params),
                    dbType, mysqlParamSupport.getCustinsMaxDbs(dbType),
                    mysqlParamSupport.getCustinsMaxAccounts(dbType));
            }

            custins.setDbVersion(mysqlParamSupport.getAndCheckDBVersion(params, custins.getDbType(), true));

            // 设置实例公共属性
            mysqlParamSupport.updateCustinsCommonProperties(custins, params);
            Integer bizType = mysqlParamSupport.getAndCheckBizType(params);
            //新增实例规格设置
            if (mysqlParamSupport.hasParameter(params, ParamConstants.DB_INSTANCE_CLASS)) {
                custins = mysqlParamSupport.setInstanceLevel(custins,
                    mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS),
                    bizType, mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE));
            }
            //指定创建实例使用的磁盘类型（SSD 或 SATA或ECS_CLOUD_SSD， 默认使用SSD）
            String hostType = mysqlParamSupport.getAndCheckHostType(params);
            String nodeType = mysqlParamSupport.getAndCheckNodeType(params);

            String upgradeMinorVersionOption;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.AUTO_UPGRADE_MINOR_VERSION)) {
                upgradeMinorVersionOption = mysqlParamSupport.getParameterValue(params, ParamConstants.AUTO_UPGRADE_MINOR_VERSION);
                if (!ParamConstants.AUTO_UPGRADE_MINOR_VERSION_OPTIONS.contains(upgradeMinorVersionOption)) {
                    throw new RdsException(ErrorCode.INVALID_ITEM_VALUE);
                }
            } else {
                upgradeMinorVersionOption = resourceService.getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).size() > 0 ? resourceService.getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).get(0) : "Auto";
            }

            if ("5.7".equals(dbVersion)) {
                InstanceLevelDO insLevel = instanceService
                    .getInstanceLevelByClassCode(classCode, dbType, dbVersion, null, null);
                if (insLevel == null) {
                    throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                }
                hostType = String.valueOf(insLevel.getHostType());
                if (CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD.equals(hostType)) {
                    custins.setKindCode(KIND_CODE_ECS_VM);
                } else {
                    custins.setKindCode(KIND_CODE_NC);
                }
            }

            //检查是否有指定版本创建
            String targetMinorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
                dbType,
                dbVersion,
                classCode,
                MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL,
                mysqlParamSupport.getParameterValue(params, "TargetMinorVersion"));

            //指定版本创建
            if(targetMinorVersion != null){
                mysqlParamSupport.setParameter(params, "minor_version", targetMinorVersion);
                //taskQueueParam.put("minor_version", targetMinorVersion);
            }

            String defaultTimeZone = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_PARAM_TIME_ZONE,"");
            if (StringUtils.isNotBlank(defaultTimeZone)) {
                if (!TimezoneHelper.validator(defaultTimeZone)) {
                    logger.warn("invalid custom timezone {}, request id is {}", defaultTimeZone,  mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID,""));
                    throw new RdsException(ErrorCode.INVALID_PARAMETERS, ErrorCode.INVALID_PARAMETERS.getDesc() + "[Invalid defaultTimeZone:" + defaultTimeZone + "]");
                }
            }


            //原有rdsapi mysqlOnecsAdapter的create函数参数中包括nodeCount这一参数，但action自身不包含该参数
            Integer nodeCount = 1;
            mysqlParamSupport.setParameter(params, "nodeCount", String.valueOf(nodeCount));
            logger.warn("before ecsInsDirector,custins: "+JSONObject.toJSONString(custins)+",params:"+JSONObject.toJSONString(params));
            //使用多例模式，每次都重新获取一个builder实例
            MysqlOnEcsBuilder builder = SpringContextUtil.getApplicationContext().getBean(MysqlOnEcsBuilder.class);
            //单节点ESSD实例，会指定ESSD磁盘类型，采用的是5.7单节点相同的架构，不采用docker
            Map<String,Object> result = ecsInsDirector.create(custins, params, builder);
            custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, upgradeMinorVersionOption));
            return result;
        } catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
