package com.aliyun.dba.onecs.action;

import com.aliyun.dba.base.service.MysqlAccountService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsDescribeAccountListImpl")
public class DescribeAccountListImpl implements IAction {
    private final static LogAgent logger = LogFactory.getLogAgent(DescribeAccountListImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected AccountService accountService;

    @Autowired
    protected DbossApi dbossApi;

    @Autowired
    protected MycnfService mycnfService;

    @Autowired
    protected MysqlAccountService mysqlAccountService;

    public Map<String,Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            CustInstanceDO custins1 = mysqlParamSupport.getAndCheckCustInstance(params);
            if (dbossApi.isHandleByDBoss(custins1)) {
                return mysqlAccountService.describeAccountListByDboss(custins1, params);
            } else {
                return mysqlAccountService.describeAccountList(params);
            }
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.warn(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
