package com.aliyun.dba.onecs.action;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.alicloud.apsaradb.resmanager.response.UpgradeResRespModel;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.ecs.api.EcsBaseApi;
import com.aliyun.dba.ecs.api.EcsInsApi;
import com.aliyun.dba.ecs.dataobject.EcsImageDO;
import com.aliyun.dba.ecs.dataobject.EcsSecurityGroupDO;
import com.aliyun.dba.ecs.dataobject.VpcInfoDO;
import com.aliyun.dba.ecs.service.CustInstanceDBService;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.ecs.service.EcsImageService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.host.dataobject.HostInfoDO;
import com.aliyun.dba.host.dataobject.HostInstanceDO;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.service.MysqlOnEcsDBCenterService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.AES;
import com.aliyun.dba.support.utils.AesCfb;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_REMOVE_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_REMOVE;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsModifyDBInstanceClassImpl")
public class ModifyDBInstanceClassImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(ModifyDBInstanceClassImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceClassImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected EcsService ecsService;
    @Autowired
    protected EcsImageService ecsImageService;
    @Autowired
    protected HostService ecsHostService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected TaskService taskService;
    @Autowired
    private CustInstanceDBService custInstanceDBService;
    @Autowired
    private EcsDBService ecsDBService;
    @Autowired
    protected MysqlOnEcsDBCenterService mysqlOnEcsDBCenterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MysqlParamGroupHelper mysqlParamGroupHelper;
    @Autowired
    protected DTZSupport dtzSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try{
            // 该架构暂不支持可用区迁移
            String migratingAvz = mysqlParamSupport.getParameterValue(actionParams, PodDefaultConstants.MIGRATING_AVZ, "false");
            if (Boolean.parseBoolean(migratingAvz)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            //ModifyDBInstanceClass该值为false，MigrateDBInstance为true
            boolean isTransfer = false;
            if(mysqlParamSupport.getAction(actionParams).equalsIgnoreCase("MigrateDBInstance")){
                isTransfer = true;
            }
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            // 切换只读实例任务流会下发升降级的任务..现在ha加了状态,升降级判断实例状态..要放行这个状态
            // 暂时在custins中增加判断readswitch 拆分业务第二阶段再做改造
            if (!custins.isActive() && !mysqlParamSupport.isReadSwitch(custins)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            String dbVersion = null;
            String dbInstanceStatusDesc;
            // 获取目标实例Region
            Boolean isSameAvz = false;
            AVZInfo avzInfo = avzSupport.getAVZInfo(actionParams);
            String region = avzSupport.getMainLocation(actionParams);
            AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            String oldRegion = oldAvzInfo.getMainLocation();
            if (avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.ClassicDispenseMode) &&
                    oldAvzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
                avzInfo = oldAvzInfo;
                isSameAvz = true;
            }
            if(!avzInfo.isValidForModify()){
                avzInfo = oldAvzInfo;
                isSameAvz = true;
            }

             //获取版本
            if (!isTransfer) {
                dbVersion = mysqlParamSupport.getDBVersion(actionParams, custins.getDbType());
                dbInstanceStatusDesc = CustinsState.STATE_CLASS_CHANGING.getComment();
            } else {
                String inputDesc = mysqlParamSupport.getParameterValue(actionParams, "DBInstanceStatusDesc");
                if (StringUtils.isNotBlank(inputDesc)) {
                    dbInstanceStatusDesc = inputDesc;
                } else {
                    dbInstanceStatusDesc = CustinsState.STATE_TRANSING.getComment();
                }
            }

            if (dbVersion == null) {
                dbVersion = custins.getDbVersion();
            }
            //不支持版本升降级
            if (0 != dbVersion.compareTo(custins.getDbVersion())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            String strEcsTransType = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_TRANS_TYPE,
                    CustinsSupport.CUSTINS_TRANS_TYPE_DEFAULT);
            CustinsSupport.checkTransTypeValid(strEcsTransType);
            String levelCode = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.TARGET_DB_INSTANCE_CLASS);
            InstanceLevelDO newLevel;
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if (Validator.isNull(levelCode)) {
                newLevel = oldLevel;
            } else {
                newLevel = instanceService.getInstanceLevelByClassCode(
                        levelCode, custins.getDbType(), dbVersion, custins.getTypeChar(), null);
            }
            if (newLevel.getHostType() == 2) {
                //ECS上暂时不支持跨可用区迁移
                if (!StringUtils.isBlank(region) && !oldRegion.equals(region)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
                // Onecs 不支持直接变云盘高可用
                if (newLevel.isStandardLevel()) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
                return transCustinsOnEcs(actionParams, custins, dbInstanceStatusDesc, strEcsTransType);
            } else {
                //变配到物理机
                String specifyCluster = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CLUSTER_NAME);
                String storage = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.STORAGE);
                Set<Integer> specifyHostIdSet = mysqlParamSupport.getAndCheckHostIdSet(actionParams);
                boolean isAcrossRegion;
                if (!StringUtils.isBlank(region) && !oldRegion.equals(region)) {
                    isAcrossRegion = true;
                } else {
                    region = oldRegion;
                    isAcrossRegion = false;
                }

                List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

                List<VipResModel> vipResModelList = new ArrayList<>();
                if (isAcrossRegion) {
                    // vpc实例需要传入vpc信息才能跨可用区
                    CustinsConnAddrDO vpcConnAddr = ConnAddrSupport.getVPCConnAddr(custinsConnAddrList);
                    if (vpcConnAddr != null) {
                        // VPC 实例， 必须传入tunnelid， vswitchid， ipaddress
                        String vpcId = CheckUtils.checkValidForVPCId(
                            mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VPC_ID));
                        String tunnelId = CheckUtils.checkValidForTunnelId(
                            mysqlParamSupport.getParameterValue(actionParams, ParamConstants.TUNNEL_ID));
                        String vswitchId = CheckUtils.checkValidForVswitchId(
                            mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VSWITCH_ID));
                        String ipaddress = CheckUtils.checkValidForIPAddress(
                            mysqlParamSupport.getParameterValue(actionParams, ParamConstants.IP_ADDRESS));
                        String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(
                            mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID));

                        VipResModel vipResModel = new VipResModel(vpcConnAddr.getNetType());
                        vipResModel.setUserVisible(vpcConnAddr.getUserVisible());
                        vipResModel.setConnAddrCust(
                            mysqlParamSupport.getConnAddrCust(
                                "tmp" + System.currentTimeMillis() + "-" + custins.getInsName().replace('_', '-'),
                                mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()),
                                custins.getDbType()));
                        vipResModel.setVip(ipaddress);
                        vipResModel.setVport(Integer.valueOf(vpcConnAddr.getVport()));
                        vipResModel.setVpcId(vpcId);
                        vipResModel.setTunnelId(CustinsValidator.getRealNumber(tunnelId, -1));
                        vipResModel.setVswitchId(vswitchId);
                        vipResModel.setVpcInstanceId(vpcInstanceId);
                        vipResModelList.add(vipResModel);
                    }
                }

                Long diskSize;
                Integer maxDiskSize = resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
                if (StringUtils.isNotEmpty(storage)) {
                    diskSize =
                        CheckUtils.parseInt(storage, 5, maxDiskSize, ErrorCode.INVALID_STORAGE)
                            * 1024L;
                } else {
                    // ecs磁盘空间可能和物理机范围不一样，超过物理机磁盘空间的ecs不允许迁移物理机
                    diskSize = custins.getDiskSize();
                    if (diskSize > maxDiskSize * 1024) {
                        return createErrorResponse(ErrorCode.INVALID_STORAGE);
                    }
                }

                //校验UTC时间，返回带时区的日期
                Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(actionParams);
                String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(actionParams, utcDate, true);

                UpgradeResContainer container = avzSupport.getUpgradeResContainer(avzInfo);
                container.setRequestId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID));
                container.setClusterName(specifyCluster);
                container.setPreferClusterName(custins.getClusterName());
                container.setAccessId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID));
                container.setOrderId(CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID));

                // init custins res model
                UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
                custinsResModel.setCustinsId(custins.getId());

                // init host ins res model
                HostinsResModel hostinsResModel = new HostinsResModel(newLevel.getId());
                hostinsResModel.setHostType(newLevel.getHostType());
                hostinsResModel.setInsCount(newLevel.isMysqlEnterprise() ? 3 : 2);
                hostinsResModel.setDiskSizeSold(diskSize);
                // get disk size used
                try {
                    InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
                    hostinsResModel.setDiskSizeUsed(new BigDecimal(instancePerf.getDiskCurr()).longValue());
                } catch (Exception e) {
                    logger.error("Get instance perf failed for custins: " + custins.getId(), e);
                    hostinsResModel.setDiskSizeUsed(custins.getDiskSize());
                }

                // init distribute rule
                DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                if (hostinsResModel.getInsCount() > 3) {
                    //四节点
                    distributeRule.setSiteDistributeMode(DistributeMode.AVG_SCATTER);
                } else {
                    distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
                }
                distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);

                distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
                distributeRule.setSpecifyHostIdSet(specifyHostIdSet);
                InsLevelExtraInfo.updateDistributeRule(distributeRule, newLevel.getExtraInfo());
                hostinsResModel.setDistributeRule(distributeRule);

                custinsResModel.setHostinsResModel(hostinsResModel);
                custinsResModel.setVipResModelList(vipResModelList);
                container.addUpgradeCustinsResModel(custinsResModel);

                Response<UpgradeResRespModel> response = resApi.upgradeRes(container);
                UpgradeResRespModel respModel = response.getData();
                if (!response.getCode().equals(200)) {
                    /*mysqlParamSupport.createResourceRecord(actionParams,custins.getClusterName(), custins.getInsName(),
                            JSON.toJSONString(response));*/
                    throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                }

                if (!isSameAvz) {
                    custinsParamService.updateAVZInfo(custins.getId(), avzInfo);
                }

                String inputDesc = mysqlParamSupport.getParameterValue(actionParams, "DBInstanceStatusDesc");


                // category 升级
                if (newLevel != null) {
                    mysqlParamGroupHelper.mysqlUpgradeUpdateSysParamGroupCategory(custins, newLevel.getCategory());
                }


                Integer taskId = instanceService.transMysqlDBTask(
                    mysqlParamSupport.getAction(actionParams), mysqlParamSupport.getOperatorId(actionParams), custins,
                    null, utcDate, diskSize,
                    newLevel, respModel, isTransfer, switchMode, inputDesc, true);
                taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));

                Map<String, Object> data = new HashMap<>(7);
                data.put("MigrationID", 0);
                data.put("DBInstanceID", custins.getId());
                data.put("DBInstanceName", custins.getInsName());
                data.put("SourceDBInstanceClass", oldLevel.getClassCode());
                data.put("TargetDBInstanceClass", newLevel.getClassCode());
                data.put("TaskId", taskId);
                data.put("Region", region);
                data.put("SwitchMode", switchMode);
                return data;
            }
        } catch (RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    /**
     * tranferType: private arg for ops
     * 实例的迁移方式，取值为0|1|2
     * 0（默认值）：系统优先进行本地升降级，若本地资源不足，则进行跨机迁移。（现在接口默认行为）（忽略ClusterName、HostId参数）
     * 1： 本地升降级； 若系统判断实例当前不支持本地升降级，则会报错（如实例要升级数据库版本） （忽略ClusterName、HostId参数） 。
     * 2：跨机迁移；将实例迁移到指定的ClusterName或HostId（HostId不能与当前实例所在主机ID一样，否则会迁移失败）
     */
    private Map transCustinsOnEcs(Map<String, String> actionparams, CustInstanceDO custins, String dbInstanceStatusDesc, String tranferType) throws RdsException {
        String ecsInstanceId = null;
        Boolean createSuccess = false;
        EcsInsApi ecsInsApi = null;

        try {
            Map<String, Object> data = new HashMap<String, Object>(6);
            String levelCode = mysqlParamSupport.getParameterValue(actionparams, "TargetDBInstanceClass");
            String dbVersion = custins.getDbVersion();
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            InstanceLevelDO newLevel = null;

            if (Validator.isNull(levelCode)) {
                newLevel = oldLevel;
            } else {
                newLevel = instanceService
                        .getInstanceLevelByClassCode(levelCode, custins.getDbType(), dbVersion, custins.getTypeChar(), null);
            }
            if (newLevel == null) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
            }

            // 设置切换时间
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(actionparams);
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(actionparams, utcDate, true);

            //List<HostInstanceDO> hostInstanceDOList = custInstanceDBService.getHostInstanceList(custins.getId());
            List<HostInstanceDO> hostInstanceDOList = custInstanceDBService.getHostInstanceList(custins.getId());

            HostInstanceDO hostInstanceDO = null;
            if (hostInstanceDOList != null && hostInstanceDOList.size() > 0) {
                hostInstanceDO = hostInstanceDOList.get(0);
            } else {
                throw new RdsException(ErrorCode.NO_VAILABLE_HOSTINS);
            }

            long diskSize = -1;
            long curDiskSize = custins.getDiskSize().intValue();
            String storage = mysqlParamSupport.getParameterValue(actionparams, "Storage");
            if (StringUtils.isNotEmpty(storage)) {
                logger.error("storage parameter is " + storage);
                try {
                    diskSize = Long.parseLong(storage);
                } catch (Exception e) {
                    throw new RdsException(ErrorCode.INVALID_STORAGE);
                }
                //curDiskSize如果和storage相等，说明是变配，不是升磁盘
                Integer storageInt = Integer.valueOf(storage);
                if(storageInt != curDiskSize / 1024){
                    ResourceKey resourceKey = ResourceKey.RESOURCE_CUSTINS_ON_ECS_MAX_DISK_SIZE;
                    Integer maxDiskSize = new ResourceSupport().getIntegerRealValue(resourceKey);
                    CheckUtils.parseInt(storage, (int) (curDiskSize / 1024), maxDiskSize, ErrorCode.INVALID_STORAGE);
                }
            }
            if (newLevel.getId().equals(oldLevel.getId()) && (!CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE.equals(tranferType))) {
                if (diskSize == -1) {
                    data.put("DBInstanceID", custins.getId());
                    return data;
                }
                if ((1024 * diskSize) == curDiskSize) {
                    data.put("DBInstanceID", custins.getId());
                    return data;
                } else {
                    logger.error("diskSize parameter is " + String.valueOf(diskSize));
                    List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());

                    //TransList translist = new TransList(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
                    TransListDO translist = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
                    translist.setdDisksize(1024L * diskSize);
                    String taskKey = TaskSupport.TASK_TRANSFER;

                    Integer taskId = instanceService.transCustInstanceTaskOnEcs(
                            mysqlParamSupport.getAction(actionparams), mysqlParamSupport.getOperatorId(actionparams), taskKey, custins, translist, dbInstanceStatusDesc, switchMode);
                    taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionparams));
                    data.put("MigrationID", translist.getId());
                    data.put("DBInstanceID", custins.getId());
                    data.put("DBInstanceName", custins.getInsName());
                    data.put("SourceDBInstanceClass", oldLevel.getClassCode());
                    data.put("TargetDBInstanceClass", newLevel.getClassCode());
                    data.put("TaskId", taskId);
                    createSuccess = true;
                    return data;
                }
            }

            String imageId = null;
            String ecsClassCode = null;
            String securityGroupId = null;
            String clusterName = null;
            String siteName = null;
            String ecsAccount = null;
            String regionId = null;
            String zoneId = null;

            //Long hostId = hostInstanceDO.getHostId();
            Integer hostId = hostInstanceDO.getHostId();
            //com.aliyun.rds.dbcenter.thrift.EcsHostDetailDO ecsHostDetailDO =ecsDBService.getEcsHostDetailDOByHostId(hostId);
            EcsHostDetailDO ecsHostDetailDO = ecsDBService.getEcsHostDetailDOByHostId(hostId);

            regionId = ecsHostDetailDO.getRegionId();
            zoneId = ecsHostDetailDO.getZoneId();
            //ecsAccount = ecsHostDetailDO.getUserName();
            ecsAccount = ecsHostDetailDO.getEcsUserName();

            ecsClassCode = newLevel.getEcsClassCode();
            clusterName = custins.getClusterName();
            Boolean isUserVpc = ecsService.isClusterUserVPCArch(custins.getDbType(), custins.getDbVersion(), clusterName);
            ClustersDO clusterDO = clusterService.getClusterByClusterName(clusterName);
            siteName = clusterDO.getSiteName();
            String ipaddress = null;
            String vpcId = null;
            String vSwitchId = null;
            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(
                            custins.getId(), CustinsSupport.NET_TYPE_VPC, CustinsSupport.RW_TYPE_NORMAL);
            if (custinsConnAddrList == null || custinsConnAddrList.size() < 0) {
                throw new RdsException(ErrorCode.NO_VAILABLE_CUSTINS_CONN_ADDR);
            }

            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
                if (custinsConnAddr.isVpcNetType()) {
                    // 获取实例的 ipaddress
                    ipaddress = custinsConnAddr.getVip();
                    vpcId = custinsConnAddr.getVpcId();
                }
            }

            if (custins.isSqlserver() && isUserVpc) {
                if (vpcId == null || ipaddress == null) {
                    throw new RdsException(ErrorCode.NO_VAILABLE_VSWITCH);
                }

                vSwitchId = custinsService.getVswitchIdByVpcIpAndVpcId(
                        ipaddress, vpcId);
            } else {
                VpcInfoDO vpcInfo = ecsService.getVpcInfo(ecsAccount, regionId, zoneId, ipaddress, clusterName);
                vpcId = vpcInfo.getVpcId();
                vSwitchId = ecsService.getEcsVpcApi(vpcId).getAvailableSwitchId(vpcId, 1);
            }

            if (vSwitchId == null) {
                throw new RdsException(ErrorCode.NO_VAILABLE_VSWITCH);
            }

            List<EcsImageDO> ecsImageDOList = mysqlOnEcsDBCenterService.getEcsImageDOList(regionId, ecsAccount, custins.getDbType(), vpcId);
            imageId = ecsImageDOList.get(0).getImageId();

            //SG
            String securityGroupName = null;
            if (!(custins.isSqlserver() && isUserVpc)) {
                securityGroupName = "default";
            }

            List<EcsSecurityGroupDO> ecsSecurityGroupDOList = mysqlOnEcsDBCenterService.getEcsSGDOList(regionId, ecsAccount, vpcId, securityGroupName);
            securityGroupId = ecsSecurityGroupDOList.get(0).getSecurityGroupId();

            if (Validator.isNull(securityGroupId)) {
                String customizedErrorDesc = "SecurityGroupId is null for creating instance";
                logger.error(customizedErrorDesc);
                throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, customizedErrorDesc);
            }

            //List<AccountDO> accountList = custInstanceDBService.getAccountList(custins.getId(), "os", 11, 0, 1);
            List<AccountsDO> accountList = custInstanceDBService.getAccountList(custins.getId(), "os", 11, 0, 1);
            if (accountList == null || accountList.size() < 0) {
                throw new RdsException(ErrorCode.NO_VAILABLE_OS_ACCOUNTS);
            }

            String osPassword = null;
            if (custins.isSqlserver()) {
                osPassword = AES.decryptPassword(accountList.get(0).getPassword(),
                        RdsConstants.PASSWORD_KEY);
            } else {
                try {
                    osPassword = AesCfb.decrypt(accountList.get(0).getBackwardPasswd());
                } catch (Exception e) {
                    String customizedErrorDesc = "fail to decrypt password";
                    throw new RdsException(ErrorCode.INTERNAL_FAILURE, customizedErrorDesc);
                }
            }
            logger.info("regionId:" + regionId + "imageId:" + imageId + "ecsClassCode:"
                    + ecsClassCode + "securityGroupId:" + securityGroupId + "vSwitchId:"
                    + vSwitchId + "vpcId:" + vpcId);
            if (Validator.isNull(ecsAccount) || Validator.isNull(regionId) || Validator
                    .isNull(imageId)
                    || Validator.isNull(ecsClassCode)
                    || Validator.isNull(vSwitchId) || Validator.isNull(vpcId)
                    || Validator.isNull(clusterName) || Validator.isNull(siteName)) {
                logger.info("regionId:" + regionId + "imageId:" + imageId + "ecsClassCode:"
                        + ecsClassCode + "vSwitchId:"
                        + vSwitchId + "vpcId:" + vpcId);
                if (Validator.isNull(osPassword)) {
                    logger.error("osPassword is null or empty string");
                }
                String customizedErrorDesc = "Invalid params to call ecs open api";
                throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, customizedErrorDesc);
            }

            String ecsUserData = null;
            if (custins.isSqlserver() && isUserVpc) {
                ecsUserData = ecsService.getMssqlEcsUserData(clusterName);
            }

            Map<String, Object> createEcsInstanceResult = null;
            //创建ECS实例
            ecsInsApi = ecsService.getEcsInsApi(vpcId);

            for (String eachEcsClassCode : ecsClassCode.trim().split(",")) {
                createEcsInstanceResult =
                        ecsInsApi.createInstance(osPassword, regionId, imageId,
                                eachEcsClassCode.trim(), securityGroupId, vSwitchId, ecsUserData);
                if ((Boolean) createEcsInstanceResult.get(EcsBaseApi.RESULT)) {
                    ecsClassCode = eachEcsClassCode.trim();
                    break;
                } else {
                    logger.warn("Fail to create ecs instance--" + JSON.toJSONString(createEcsInstanceResult.get(EcsBaseApi.ERR_MSG)));
                }
            }
            if (createEcsInstanceResult == null) {
                throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, "Not ecs class code specified.");
            }

            if ((Boolean) createEcsInstanceResult.get(EcsBaseApi.RESULT) == false) {
                String errMsg = (String) createEcsInstanceResult.get(EcsBaseApi.ERR_MSG);
                String customizedErrorDesc = "fail to create ecs instance--" + errMsg;
                throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, customizedErrorDesc);
            }
            ecsInstanceId =
                    (String) createEcsInstanceResult.get(ParamConstants.ECS_INSTANCE_ID);


            //生成hostInfo数据对象
            HostInfoDO ecsHostInfoDO = new HostInfoDO();
            ecsHostInfoDO.initEcsHost();
            ecsHostInfoDO.setGroupName("single_" + ecsInstanceId);
            ecsHostInfoDO.setLevelId(ecsHostService.getEcsHostLevelId());
            ecsHostInfoDO.setClusterName(clusterName);
            ecsHostInfoDO.setDbType(custins.getDbType());
            ecsHostInfoDO.setDbVersion(custins.getDbVersion());
            ecsHostInfoDO.setSiteName(siteName);
            ecsHostInfoDO.setVpcId(vpcId);
            ecsHostInfoDO.setIp(ecsInstanceId);
            ecsHostInfoDO.setHostName(ecsInstanceId);
            String latestLogicGroup = resourceSupport.getStringRealValue(ResourceKey.RESOURCE_LATEST_LOGIC_GROUP).trim();
            ecsHostInfoDO.setLogicGroup(latestLogicGroup);
            //生成ecsHostDetail数据对象
            EcsHostDetailDO newecsHostDetailDO = new EcsHostDetailDO(ecsInstanceId, ecsClassCode);
            newecsHostDetailDO.setEcsUserName(ecsAccount);
            newecsHostDetailDO.setHostName(ecsInstanceId);
            newecsHostDetailDO.setRegionId(regionId);
            newecsHostDetailDO.setZoneId(zoneId);
            newecsHostDetailDO.setEcsImageId(imageId);
            //写入数据库
            ecsHostService.createEcsHost(ecsHostInfoDO, newecsHostDetailDO);

            List<InstanceDO> instanceList =
                    instanceService.getInstanceByCustinsId(custins.getId());
            //use diskSize instend of diskSize*1024
            if (diskSize == -1) {
                diskSize = custins.getDiskSize().intValue();
            } else {
                if (diskSize < 20480) {
                    diskSize = diskSize * 1024;
                }
            }
            TransListDO translist =
                    createTransListDOForTransOnEcs(custins, instanceList, utcDate,
                            newLevel.getId(), ecsInstanceId, osPassword, (long) diskSize);

            String taskKey = TaskSupport.TASK_TRANSFER;

            Integer taskId = instanceService.transCustInstanceTaskOnEcs(
                    mysqlParamSupport.getAction(actionparams), mysqlParamSupport.getOperatorId(actionparams), taskKey, custins, translist, dbInstanceStatusDesc, switchMode);
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionparams));
            data.put("MigrationID", translist.getId());
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("SourceDBInstanceClass", oldLevel.getClassCode());
            data.put("TargetDBInstanceClass", newLevel.getClassCode());
            data.put("TaskId", taskId);
            createSuccess = true;
            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex){
            logger.error("transOnEcs error, detail:", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
        finally {
            if (!createSuccess) {
                if (ecsInstanceId != null) {
                    if (ecsInsApi == null) {
                        //return ;
                    }
                    else{
                        Boolean insDeleted = ecsInsApi.deleteInstance(ecsInstanceId);
                        if (!insDeleted) {
                            String customizedErrorDesc = "fail to call ecs api to delete ecs ins";
                            throw new RdsException(ErrorCode.EXTERNAL_FAILURE, customizedErrorDesc);
                        }
                    }
                }
            }
        }
    }

    /**
     * 对比createTransListForTransOnEcs，创建TransListDO
     */
    private TransListDO createTransListDOForTransOnEcs(CustInstanceDO custins,
                                                       List<InstanceDO> instanceList,
                                                       Date switchTime,
                                                       Integer levelId,
                                                       Long targetDiskSize) {
        TransListDO translist = null;

        // 设置目的主机实例ID
        translist = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        translist.setdHinsid1(instanceList.get(0).getId());
        if (instanceList.size() > 1) {
            translist.setdHinsid2(instanceList.get(1).getId());
        } else {
            translist.setdHinsid2(0);
        }
        // 设置源主机实例ID
        translist.setsHinsid1(instanceList.get(0).getId());
        if (instanceList.size() > 1) {
            translist.setsHinsid2(instanceList.get(1).getId());
        } else {
            translist.setsHinsid2(0);
        }
        translist.setdLevelid(levelId);
        translist.setdDisksize(targetDiskSize);
        translist.setSwitchTime(switchTime);
        return translist;
    }

    /**
     * 对比createTransListForTransOnEcs，创建TransListDO
     */
    private TransListDO createTransListDOForTransOnEcs(CustInstanceDO custins,
                                                       List<InstanceDO> instanceList,
                                                       Date utcDate,
                                                       Integer levelId,
                                                       String ecsInstanceId,
                                                       String ecsOsPassword,
                                                       Long targetDiskSize) {
        TransListDO translist = null;

        // 设置目的主机实例ID
        translist = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        translist.setdHinsid1(instanceList.get(0).getId());
        if (instanceList.size() > 1) {
            translist.setdHinsid2(instanceList.get(1).getId());
        } else {
            translist.setdHinsid2(0);
        }
        // 设置源主机实例ID
        translist.setsHinsid1(instanceList.get(0).getId());
        if (instanceList.size() > 1) {
            translist.setsHinsid2(instanceList.get(1).getId());
        } else {
            translist.setsHinsid2(0);
        }
        translist.setdLevelid(levelId);
        translist.setdDisksize(targetDiskSize);

        //utcDate可能为null，即不是按照时间点切换
        Date metadbSwitchTime = null;
        if(utcDate != null){
            String utcSwitchTimeStr = dtzSupport.transDate2UtcStr(utcDate);
            metadbSwitchTime = dtzSupport.transUtcStr2MetadbDate(utcSwitchTimeStr, DataSourceMap.DATA_SOURCE_DBAAS);
        }
        translist.setSwitchTime(metadbSwitchTime);

        Map<String, Object> translistParamMap = new HashMap<String, Object>(1);
        translistParamMap.put("destEcsInsId", ecsInstanceId);
        translistParamMap.put("destEcsOsPassword", ecsOsPassword);
        translist.setParameter(JSON.toJSONString(translistParamMap));
        return translist;
    }
}
