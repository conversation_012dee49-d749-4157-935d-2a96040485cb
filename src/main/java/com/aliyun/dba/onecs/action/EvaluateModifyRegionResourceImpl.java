package com.aliyun.dba.onecs.action;

import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.response.EvaluateResRespModel;
import com.alicloud.apsaradb.resmanager.response.EvaluateUpgradeResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.service.CustInstanceDBService;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.host.dataobject.HostInstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_TRANS_TYPE_LOCAL;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsEvaluateModifyRegionResourceImpl")
public class EvaluateModifyRegionResourceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(CreateDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateModifyRegionResourceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected CustInstanceDBService custInstanceDBService ;
    @Autowired
    protected EcsDBService ecsDBService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected AVZSupport avzSupport;
    @Resource
    protected DockerCommonService dockerCommonService;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            AVZInfo avzInfo = avzSupport.getAVZInfo(actionParams);
            String region = avzSupport.getMainLocation(actionParams);
            AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            String oldRegion = oldAvzInfo.getMainLocation();
            if (!avzInfo.isValidForModify()) {
                avzInfo = oldAvzInfo;
            }
            Map<String, Object> data = new HashMap<String, Object>();

            String levelCode = mysqlParamSupport.getParameterValue(actionParams,
                ParamConstants.TARGET_DB_INSTANCE_CLASS);
            InstanceLevelDO newLevel;
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if (Validator.isNull(levelCode)) {
                newLevel = oldLevel;
            } else {
                newLevel = instanceService.getInstanceLevelByClassCode(levelCode, custins.getDbType(),
                        custins.getDbVersion(),
                        custins.getTypeChar(), null);
            }
            if (newLevel == null) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            if (newLevel.getHostType() == 2) {
                if (newLevel.isStandardLevel()) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
                }
                if (newLevel.getClassCode().equals(oldLevel.getClassCode())) {
                    String storageType = mysqlParamSupport.getAndCheckStorageType(actionParams);
                    if (StringUtils.isBlank(storageType)) {
                        storageType = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_STORAGE_TYPE);
                    }
                    if (StringUtils.equalsIgnoreCase(DockerOnEcsConstants.ECS_ClOUD_ESSD, storageType) &&
                            dockerCommonService.matchEcsDiskCategory(custins, DockerOnEcsConstants.ECS_ClOUD_SSD)){
                        //老架构基础版不支持变盘，需先升级版本
                        return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
                    } else {
                        //原逻辑
                        // 只升级磁盘不升级规格默认有资源
                        data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                    }

                    data.put(ParamConstants.ENGINE_VERSION, custins.getDbVersion());
                    data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(custins.getDbType()));
                    return data;
                }
                HostInstanceDO hostInstanceDO = null;
                String regionId = null;
                String zoneId = null;
                String ecsAccount = null;
                try {
                    List<HostInstanceDO> hostInstanceDOList = custInstanceDBService.getHostInstanceList(
                        custins.getId());
                    hostInstanceDO = hostInstanceDOList.get(0);

                    Integer hostId = hostInstanceDO.getHostId();
                    EcsHostDetailDO ecsHostDetailDO = ecsDBService.getEcsHostDetailDOByHostId(hostId);
                    regionId = ecsHostDetailDO.getRegionId();
                    zoneId = ecsHostDetailDO.getZoneId();
                    ecsAccount = ecsHostDetailDO.getEcsUserName();
                } catch (Exception e) {
                    logger.warn("NO_VAILABLE_HOSTINS for custins " + custins.getId());
                }

                if (hostInstanceDO == null || zoneId == null || regionId == null || ecsAccount == null) {
                    throw new RdsException(ErrorCode.NO_VAILABLE_HOSTINS);
                }

                List<EcsResModel> ecsResModelList = Lists.newLinkedList();
                EcsResModel ecsResModel = new EcsResModel(null);
                ecsResModel.setEcsAccount(ecsAccount);
                ecsResModel.setInsCount(1);

                // 资源管理器有检验这个
                ecsResModel.setImageId("dummy");
                ecsResModel.setEcsVSwitchId("dummy");
                ecsResModel.setEcsVpcId("dummy");

                ecsResModel.setZoneId(zoneId);
                ecsResModel.setRegionId(regionId);
                ecsResModel.setInstanceType(newLevel.getEcsClassCode());
                ecsResModelList.add(ecsResModel);

                CustinsResModel custinsRes = new CustinsResModel(null);
                custinsRes.setEcsResModelList(ecsResModelList);

                //主可用区添加
                ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo,
                    custins.getDbType());
                resourceContainer.setRequestId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID));
                resourceContainer.addCustinsResModel(custinsRes);
                Response<EvaluateResRespModel> response = resApi.evaluateRes(resourceContainer);
                if (response.getCode().equals(200)) {
                    data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                } else {
                    data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                    data.put(ParamConstants.ERROR_MESSAGE, ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                }
                data.put(ParamConstants.ENGINE_VERSION, custins.getDbVersion());
                data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(custins.getDbType()));
                return data;
            } else {
                String specifyCluster = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CLUSTER_NAME);
                String storage = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.STORAGE);
                Long diskSize;
                if (StringUtils.isNotEmpty(storage)) {
                    Integer maxDiskSize = resourceSupport.getIntegerRealValue(
                        ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
                    diskSize =
                        CheckUtils.parseInt(storage, 5, maxDiskSize, ErrorCode.INVALID_STORAGE)
                            * 1024L;
                } else {
                    diskSize = custins.getDiskSize();
                }

                UpgradeResContainer container = avzSupport.getUpgradeResContainer(avzInfo);
                container.setClusterName(specifyCluster);
                container.setPreferClusterName(custins.getClusterName());

                // init custins res model
                UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
                custinsResModel.setCustinsId(custins.getId());

                // init host ins res model
                HostinsResModel hostinsResModel = new HostinsResModel(newLevel.getId());
                hostinsResModel.setInsCount(2);

                hostinsResModel.setDiskSizeSold(diskSize);
                // get disk size used
                try {
                    InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
                    hostinsResModel.setDiskSizeUsed(new BigDecimal(instancePerf.getDiskCurr()).longValue());
                } catch (Exception e) {
                    logger.error("Get instance perf failed for custins: " + custins.getId(), e);
                    hostinsResModel.setDiskSizeUsed(custins.getDiskSize());
                }

                // init distribute rule
                DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
                distributeRule.setCabinetDistributeMode(DistributeMode.FORCE_SCATTER);
                distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
                distributeRule.setSpecifyHostIdSet(mysqlParamSupport.getAndCheckHostIdSet(actionParams));
                InsLevelExtraInfo.updateDistributeRule(distributeRule, newLevel.getExtraInfo());
                hostinsResModel.setDistributeRule(distributeRule);

                custinsResModel.setHostinsResModel(hostinsResModel);
                container.addUpgradeCustinsResModel(custinsResModel);
                Response<EvaluateUpgradeResRespModel> response = resApi.evaluateUpgradeRes(container);

                data.put("DBInstanceName", custins.getInsName());
                if (response.getCode().equals(200)) {
                    data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                    if (response.getData().getCustinsResRespModelList().get(0).getIsLocalUpgrade() == 0) {
                        data.put(ParamConstants.DB_INSTANCE_TRANS_TYPE, CUSTINS_TRANS_TYPE_REMOTE);
                    } else {
                        data.put(ParamConstants.DB_INSTANCE_TRANS_TYPE, CUSTINS_TRANS_TYPE_LOCAL);
                    }
                } else {
                    data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                    data.put(ParamConstants.ERROR_MESSAGE, ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                }
                data.put(ParamConstants.DB_INSTANCE_CLASS, newLevel.getClassCode());
                data.put(ParamConstants.ENGINE_VERSION, newLevel.getDbVersion());
                data.put("Region", region);
                return data;
            }
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
