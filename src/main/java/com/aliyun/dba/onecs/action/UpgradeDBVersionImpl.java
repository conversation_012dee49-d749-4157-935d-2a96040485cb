package com.aliyun.dba.onecs.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.EcsResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.base.action.MigratePengineToK8SInstanceClassImpl;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.dataobject.RdsRegionAvzDO;
import com.aliyun.dba.onecs.service.MysqlOnEcsDBCenterService;
import com.aliyun.dba.onecs.service.MysqlOnEcsRegionService;
import com.aliyun.dba.onecs.support.EcsCommonSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.UserGrayService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_TRANS;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_REMOVE;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;
import static com.aliyun.dba.task.support.TaskSupport.TASK_UPGRADE_MINOR_VERSION;


/**
 * onECS 实例版本升级, 这里是单节点的版本升级模式, 目前只支持小版本升级
 * 支持本地升级(使用新的小版本重启)和远程(临时实例数据同步链路) 两种模式
 *
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsUpgradeDBVersionImpl")
public class UpgradeDBVersionImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(UpgradeDBVersionImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeDBVersionImpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected EcsCommonSupport ecsCommonSupport;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected EcsService ecsService;
    @Autowired
    protected MysqlOnEcsRegionService mysqlOnEcsRegionService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected MysqlOnEcsDBCenterService mysqlOnEcsDBCenterService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected HostService ecsHostService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected InstanceIDao instanceIDao;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;

    private static final String REMOTE_UPGRADE = "1";

    /*
    onecs基础版已经无可用版本，细化报错信息，提示用户升级新架构。
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            // 实例状态校验
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (custins.isReadAndWriteLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            // 经典网络提示
            List<CustinsConnAddrDO> privateNet = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), CustinsSupport.NET_TYPE_PRIVATE, CustinsSupport.RW_TYPE_NORMAL);
            if (!privateNet.isEmpty()) {
                logger.error("Instance {} have private net.", custins.getId());
                throw new RdsException(ErrorCode.INVALID_CLASSIC_NET_TYPE);
            }
            //ipv6 提示
            List<CustinsConnAddrDO> publicNet = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), CustinsSupport.NET_TYPE_PUBLIC, CustinsSupport.RW_TYPE_NORMAL);
            for (CustinsConnAddrDO netConn: publicNet){
                if (netConn.getVip() != null && netConn.getVip().contains(":")){
                    logger.error("Instance {} public network address contain ipv6.", custins.getId());
                    throw new RdsException(ErrorCode.INVALID_NET_IPV6);
                }
            }

            String remoteUprade = getParameterValue(actionParams, "RemoteUpgrade", "1");
            Map<String, Object> taskParam = new HashMap<>(3);
            taskParam.put("upgrade_mode", "local");

            String uid = getParameterValue(actionParams, ParamConstants.UID);
            String regionIdFromParam = getParameterValue(actionParams, ParamConstants.REGION_ID);
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if (userGrayService.isMigratePengineToK8SSwitchGray(uid, regionIdFromParam, custins, insLevel)) {
                MigratePengineToK8SInstanceClassImpl migrateToK8SInstanceClass = SpringContextUtil.getBeanByClass(MigratePengineToK8SInstanceClassImpl.class);
                actionParams.put("migrateTaskSource".toLowerCase(), CustinsState.STATE_MINOR_VERSION_TRANSING.getComment());
                return migrateToK8SInstanceClass.doActionRequest(custins, actionParams);
            }

            //检查是否有指定版本创建
            String targetMinorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
                custins.getDbType(),
                custins.getDbVersion(),
                instanceService.getInstanceLevelByLevelId(custins.getLevelId()).getClassCode(),
                MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL,
                mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion"));

            //指定版本创建
            if(targetMinorVersion != null){
                taskParam.put("minor_version", targetMinorVersion);
            }

            String clusterName = custins.getClusterName();
            Integer taskId;
            String taskParameter = "";
            // 设置切换时间和模式
            //Date switchTime = checkService.getAndCheckSwitchTime(getParameterValue(actionParams, ParamConstants.SWITCH_TIME));
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(actionParams);
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(actionParams, utcDate, true);
            Map<String, Object> effMap = custinsService.getEffectiveTimeMapTimeZoneSafe(switchMode, utcDate);

            taskParam.put(CustinsSupport.SWITCH_KEY, effMap);
            TransListDO trans = null;
            // 使用远程升级的方式
            if (REMOTE_UPGRADE.equals(remoteUprade)) {
                taskParam.put("upgrade_mode", "remote");

                // 看资源是否已经分配, 如果异常发生, 分配的资源需要业务回滚
                boolean hostAlreadyAllocated = false;
                CustInstanceDO tmpCustins = ecsCommonSupport.getTmpCustinsWithoutUpgrade(custins);
                try {
                    RdsRegionAvzDO regionAvzDO = mysqlOnEcsRegionService.getRdsRegionAvzDOByCluster(clusterName);
                    String regionId = regionAvzDO.getRegion();
                    String zoneId = regionAvzDO.getAvz();
                    // 指定实例的链路类型
                    String ecsClassCode = instanceService.getInstanceLevelByLevelId(tmpCustins.getLevelId()).getEcsClassCode();
                    // 准备 ecs 信息
                    ClustersDO clustersDO = clusterService.getClusterByClusterName(clusterName);
                    String siteName = clustersDO.getSiteName();
                    String ecsAccount = ecsService.getEcsAccount(tmpCustins.getUserId(), regionId);
                    String vpcId = ecsCommonSupport.getSrcCustinsVPCId(custins);
                    String vSwitchId = ecsService.getEcsVpcApi(vpcId).getAvailableSwitchId(vpcId, 1);
                    if (Validator.isNull(vSwitchId)) {
                        throw new RdsException(ErrorCode.INVALID_VSWITCH_ID);
                    }
                    String imageId = mysqlOnEcsDBCenterService.getEcsImageDOList(
                            regionId, ecsAccount, tmpCustins.getDbType(), vpcId).get(0).getImageId();
                    String securityGroupId = mysqlOnEcsDBCenterService.getEcsSGDOList(
                            regionId, ecsAccount, vpcId, "default").get(0).getSecurityGroupId();
                    String osPassword = SupportUtils.getRandomPasswdForEcs(15);
                    boolean ecsReuqestInfoError = Stream.of(
                            ecsAccount, regionId, imageId, ecsClassCode, vpcId, clusterName, siteName, securityGroupId)
                            .anyMatch(Validator::isNull);
                    if (ecsReuqestInfoError) {
                        throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, "Invalid params to call ecs open api");
                    }

                    List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
                    trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_REMOVE);
                    trans.setsCinsReserved(1);
                    trans.setsHinsid1(insIds.get(0));
                    trans.setIsBaseTime(1);
                    trans.setRecoverTime(new Date());  // 任务流里会更新这个时间为选取 binlog 前的时间
                    Map<String, Object> translistParamMap = new HashMap<>(2);
                    translistParamMap.put("restoreType", RESTORE_TYPE_TIME);
                    BakhistoryDO history = ecsCommonSupport.validCloneStorageForEcs(actionParams, custins, tmpCustins, trans, translistParamMap);

                    ResourceContainer resourceContainer = new ResourceContainer(regionAvzDO.getSubDomain(), custins.getDbType());
                    resourceContainer.setClusterName(clusterName);
                    resourceContainer.setUserId(tmpCustins.getUserId());
                    resourceContainer.setAccessId(CustinsParamSupport.getParameterValue(actionParams,ParamConstants.ACCESSID));
                    resourceContainer.setOrderId(CustinsParamSupport.getParameterValue(actionParams,ParamConstants.ORDERID));
                    // custins resource
                    custinsService.createCustInstance(tmpCustins);
                    CustinsResModel custinsResModel = new CustinsResModel(tmpCustins.getId());
                    custinsResModel.setSrcCustinsId(custins.getId());
                    custinsResModel.setConnType(custins.getConnType());
                    custinsResModel.setEcsDiskSize(mysqlParamSupport.getExtendDiskSizeForEcsIns(tmpCustins.getDbType(), tmpCustins.getDiskSize()));
                    // ecs resource
                    EcsResModel resModel = new EcsResModel(imageId);
                    resModel.setOsPassword(osPassword);
                    resModel.setInsCount(1);
                    resModel.setInstanceType(ecsClassCode);
                    resModel.setRegionId(regionId);
                    resModel.setSiteName(siteName);
                    resModel.setZoneId(zoneId);
                    resModel.setSecurityGroupId(securityGroupId);
                    resModel.setEcsVSwitchId(vSwitchId);
                    resModel.setEcsVpcId(vpcId);
                    resModel.setEcsAccount(ecsAccount);
                    resModel.setSnapshotId(history.getDownloadUrl());
                    custinsResModel.setEcsResModel(resModel);
                    resourceContainer.addCustinsResModel(custinsResModel);

                    Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
                    if (!response.getCode().equals(200)) {
                        custinsService.deleteCustInstance(tmpCustins);
                        throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND, ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                    }
                    hostAlreadyAllocated = true;

                    List<Map<String, Object>> insParams = ecsHostService.getHostInstanceGroupByCustinsId(tmpCustins.getId());
                    String ecsInstanceId = (String) insParams.get(0).get("ip");
                    Long srcDiskSize = (history.getBaksetSize() / 1024L);
                    List<Integer> destInsIds = custinsService.getInstanceIdsByCustinsId(tmpCustins.getId());
                    translistParamMap.put("destEcsInsId", ecsInstanceId);
                    trans.setdCinsid(tmpCustins.getId());
                    trans.setdLevelid(tmpCustins.getLevelId());
                    trans.setsDisksize(srcDiskSize);
                    trans.setdDisksize(tmpCustins.getDiskSize());
                    trans.setdHinsid1(destInsIds.get(0));
                    trans.setdHinsid2((destInsIds.size() > 1) ? destInsIds.get(1) : 0);
                    trans.setParameter(JSON.toJSONString(translistParamMap));
                    instanceService.createTransList(trans);
                    taskParam.put("trans_id", String.valueOf(trans.getId()));

                    dbsService.createEcsOsAccount(tmpCustins, osPassword);
                    dbsService.syncUserAccountsforEcs(custins, tmpCustins);
                    ipWhiteListService.syncCustinsIpWhiteList(custins.getId(), tmpCustins.getId());
                } catch (Exception ex) {
                    if (!hostAlreadyAllocated) {
                        custinsService.deleteCustInstance(tmpCustins);
                    }
                    throw ex;
                }
            }

            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_TRANS,
                    CustinsState.STATE_MINOR_VERSION_UPGRADING.getComment());
            TaskQueueDO taskQueue = new TaskQueueDO(mysqlParamSupport.getAction(actionParams), mysqlParamSupport.getOperatorId(actionParams), custins.getId(),
                    TASK_TYPE_CUSTINS, TASK_UPGRADE_MINOR_VERSION);
            taskQueue.setParameter(JSONObject.toJSONString(taskParam));
            taskService.createTaskQueue(taskQueue);
            taskId = taskQueue.getId();
            if (trans != null) {
                instanceIDao.updateTransTaskIdById(trans.getId(), taskQueue.getId());
            }
            taskService.updateTaskPenginePolicy(taskId, CustinsParamSupport.getPenginePolicyID(actionParams));

            CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(),
                    ParamConstants.AUTO_UPGRADE_MINOR_VERSION);
            String upgradeMinorVersionSet = custinsParamDO != null ? custinsParamDO.getValue(): "Manual";
            String latestMinVersion = null;
            if (upgradeMinorVersionSet.equals("Auto")) {
                String grayVersionKey = String.format("MVM_GRAY_RELEASE_VERSION_MYSQL_%s", custins.getDbVersion());
                latestMinVersion = getFinalMinorVersion(grayVersionKey, custins);
            } else {
                String majorVersionKey = String.format("MVM_MAJOR_VERSION_MYSQL_%s", custins.getDbVersion());
                latestMinVersion = getFinalMinorVersion(majorVersionKey, custins);
            }

            if (StringUtils.isEmpty(latestMinVersion)) {
                List<String> resourceMinorVersions = resourceService.getResourceRealValueList("MYSQL_ONECS_57_LASTEST_MIN_VERSION");
                latestMinVersion = !CollectionUtils.isEmpty(resourceMinorVersions)? resourceMinorVersions.get(0): "";
            }

            Map<String, Object> data = new HashMap<>(4);
            data.put("DBInstanceName", custins.getInsName());
            data.put("DBInstanceID", custins.getId());
            data.put("TargetMinorVersion", latestMinVersion.replaceAll("mysql" + "(\\d+)?", "rds"));
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }

    private String getFinalMinorVersion(String resKey, CustInstanceDO custins) {
        List<String> resMinorVersions = resourceService.getResourceRealValueList(resKey);
        if (resMinorVersions.size() > 0) {
            JSONObject minorVersionMap = JSON.parseObject(resMinorVersions.get(0));
            return minorVersionMap.getString(custins.getKindCode().toString());
        }
        return null;
    }
}
