package com.aliyun.dba.onecs.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsCheckDBInstanceAcrossRegionImpl")
public class CheckDBInstanceAcrossRegionImpl implements IAction {

    //private static Logger logger =Logger.getLogger(CheckDBInstanceAcrossRegionImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CheckDBInstanceAcrossRegionImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsParamService custinsParamService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
        RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            boolean available = false;
            if (!custins.isMysql56()) {
                //仅对mysql5.6实例有效
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            String region = mysqlParamSupport.getParameterValue(actionParams, "Region");
            if (Validator.isNull(region)) {
                return createErrorResponse(ErrorCode.INVALID_REGION);
            }

            String targetLevel = mysqlParamSupport.getParameterValue(actionParams, "TargetDBInstanceClass");
            if (Validator.isNull(targetLevel)) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
            }

            String oldRegion = clusterService.getRegionByCluster(custins.getClusterName());
            Boolean isAcrossRegion = !StringUtils.isBlank(region) && !oldRegion.equals(region);

            if (!isAcrossRegion) {
                return createErrorResponse(ErrorCode.INVALID_REGION);
            }

            CustInstanceDO primaryins = null;//当前实例primary_custins_id指向的实例
            CustInstanceDO guardins = null;

            if (custins.isLogicPrimary()) {
                // 如果当前实例为逻辑主实例，则获取其逻辑灾备实例，便于后面判断含有灾备实例情况进行版本升级的合法性。
                guardins = custinsService.getGuardInstanceByPrimaryCustinsId(custins.getId());
            }
            if (custins.getPrimaryCustinsId() != null && custins.getPrimaryCustinsId() > 0) {
                // 如果当前实例为逻辑灾备实例、只读实例或只读实例备节点，获取其primary_custins_id指向的实例
                primaryins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            }

            if (primaryins != null && !primaryins.isActive()) {
                // 主实例状态错误
                return createErrorResponse(ErrorCode.UNSUPPORTED_MASTER_DBINSTANCE_STATUS);
            }
            if (primaryins != null && primaryins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), null, null);

            //VPC实例不支持跨Region
            boolean isVPCInstance = ConnAddrSupport.hasVPCConnAddr(custinsConnAddrList);
            available = checkCustinsAcrossRegion(custins, primaryins, region, isVPCInstance, custinsConnAddrList, guardins);

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("DBInstanceName", custins.getInsName());
            data.put("Region", region);
            data.put("TargetDBInstanceClass", targetLevel);
            data.put("Available", available);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private boolean checkCustinsAcrossRegion(CustInstanceDO custins, CustInstanceDO primaryins, String region, boolean isVPCInstance, List<CustinsConnAddrDO> custinsConnAddrList, CustInstanceDO guardins) throws RdsException {

        if (custins.isCustinsOnEcs()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "can't across-region transfer instance on ecs.");
        }

        // 如果实例为灾备实例或存在灾备实例，则需确保跨Region迁移后灾备实例与主实例不在同一Region
        if (custins.isLogicPrimary() && guardins != null) {
            String guardRegion = clusterService.getRegionByCluster(guardins.getClusterName());
            if (region.equals(guardRegion)) {
                throw new RdsException(ErrorCode.INVALID_REGION, "can't transfer to this sub domain.");
            }
        }
        if (custins.isLogicGuard() && primaryins != null) {
            String primaryRegion = clusterService.getRegionByCluster(primaryins.getClusterName());
            if (region.equals(primaryRegion)) {
                throw new RdsException(ErrorCode.INVALID_REGION, "can't transfer to this sub domain.");
            }
        }

        // 如果当前实例是只读实例或只读实例备节点, 且启用了 BLS 复制器, 需要阻止跨 Region 迁移 (FIXME: BLS支持跨Region移除)
        if (custins.isReadOrBackup() && custins.isMysql()) {// 目前只有 MySQL 只读实例启用 BLS 复制器
            CustinsParamDO param = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_READ_INS_USING_REPLICATOR);
            if (param != null && CustinsParamSupport.CUSTINS_PARAM_VALUE_READ_INS_USING_REPLICATOR_YES.equals(param.getValue())) {
                throw new RdsException(ErrorCode.CROSS_REGION_TRANS_NOT_ALLOWED, "transfer to this sub domain not allowed.");
            }
        }
        return true;
    }
}
