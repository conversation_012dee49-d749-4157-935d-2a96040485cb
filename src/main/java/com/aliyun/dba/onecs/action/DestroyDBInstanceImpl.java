package com.aliyun.dba.onecs.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsSearchDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsSearchService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.api.OpenSearchApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsDestroyDBInstanceImpl")
public class DestroyDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(DestroyDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(DestroyDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsSearchService custinsSearchService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected UserService userService;
    @Autowired
    protected CustinsIDao custinsIDao;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
        RdsException {

        try {

            if (mysqlParamSupport.getParameterValue(actionParams, "dbinstanceid") == null) {
                custins = custinsIDao.getCustInstanceByInsName(mysqlParamSupport.getAndCheckUserId(actionParams),
                    mysqlParamSupport.getAndCheckDBInstanceName(actionParams));
            } else {
                custins = mysqlParamSupport.getAndCheckCustInstanceById(actionParams, "dbinstanceid");
            }
            if (custins == null || custins.getStatus().equals(CustinsSupport.CUSTINS_STATUS_DESTROYED)) {
                //实例不存在，或者不是实例拥有者
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }
            if (custins.isDeleting()) {
                bakService.cancelBackupTaskByCustInstId(custins.getId());
                bakService.destroyBackupSet(custins.getId());

                custinsIDao.updateCustInstanceStatusByCustinsId(custins.getId(),
                    CustinsSupport.CUSTINS_STATUS_DESTROYED,
                    CustinsState.STATUS_DESTORYED.getComment());
                Map<String, Object> data = new HashMap<String, Object>(1);
                data.put("DBInstanceID", custins.getId());
                return data;
            }

            //统一优化到一个方法中
            mysqlParamSupport.checkNotDeleteHaProxyCustins(custins);

            //如果需要删除的实例是主实例的话，则需要确保该主实例所关联的灾备实例和只读实例已经被删除
            if (custins.isPrimary()) {
                //查询灾备实例，如果有灾备实例的话，则禁止删除
                CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                //过滤掉正在删除的实例
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_GUARD);//灾备实例
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                //查询只读实例，如果有只读实例的话，则禁止删除
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                //过滤掉正在删除的实例
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_READ);//只读实例
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
            }

            if (custins.isRead()) {
                CustInstanceDO primaryins = custinsService
                    .getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(primaryins.getId(), null, CustinsSupport.RW_TYPE_RW_SPLIT);

                if (custinsConnAddrList.size() > 0) {
                    List<CustInstanceDO> readinsList = custinsService
                        .getReadCustInstanceListByPrimaryCustinsId(primaryins.getId(), false);
                    List<CustInstanceDO> validReadInslist = new ArrayList<>();
                    for (CustInstanceDO readins : readinsList) {
                        if (!CustinsSupport.CUSTINS_STATUS_DELETING.equals(readins.getStatus()) &&
                            !CustinsSupport.CUSTINS_STATUS_CREATING.equals(readins.getStatus())) {
                            validReadInslist.add(readins);
                        }
                    }
                    if (validReadInslist.size() == 1) {
                        CustinsConnAddrDO delCustinsConnAddr = custinsConnAddrList.get(0);
                        ConnAddrChangeLogDO delConnAddrChangeLog = ConnAddrSupport
                            .createConnAddrChangeLogForDeleteNetType(
                                primaryins.getId(),
                                delCustinsConnAddr.getNetType(),
                                delCustinsConnAddr.getConnAddrCust(),
                                delCustinsConnAddr.getVip(),
                                delCustinsConnAddr.getVport(),
                                delCustinsConnAddr.getUserVisible(),
                                delCustinsConnAddr.getTunnelId(),
                                delCustinsConnAddr.getVpcId(),
                                null,
                                CustinsSupport.RW_TYPE_RW_SPLIT);

                        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(1);
                        connAddrChangeLogs.add(delConnAddrChangeLog);

                        try {
                            Integer taskId = taskService.changeConnAddrTask(
                                mysqlParamSupport.getAction(actionParams), primaryins, connAddrChangeLogs,
                                CustinsState.STATUS_ACTIVATION, TaskSupport.TASK_CHANGE_CONN_ADDR_DELETE_VIP,
                                mysqlParamSupport.getOperatorId(actionParams));
                            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));
                        } catch (Exception ex) {
                            logger.error("Custins: " + primaryins.getId()
                                + " DeleteDBInstanceRWSplitNetType failed when create task. Details: "
                                + JSON.toJSONString(connAddrChangeLogs));
                            throw new Exception(ex);
                        }
                    }
                }

            }

            if (custinsSearchService.checkCustinsSearch(custins)) {
                String apiUrlString = userService.getOpenSearchApiUrlByClusterName(custins.getClusterName());
                if (apiUrlString == null) {
                    return createErrorResponse(ErrorCode.OPENSEARCH_NOT_SUPPORT);
                }
                JSONObject apiUrl = JSON.parseObject(apiUrlString);
                OpenSearchApi api = new OpenSearchApi(apiUrl.getString("host"),
                    apiUrl.getString("accesskey"),
                    apiUrl.getString("secret"));
                CustinsSearchDO custinsSearch = custinsSearchService.getCustinsSearchByCustins(custins);
                api.deleteOpenSearchInstance(custinsSearch.getAppName());
                custinsSearchService.deleteCustinsSearch(custinsSearch);
            }

            bakService.cancelBackupTaskByCustInstId(custins.getId());
            bakService.destroyBackupSet(custins.getId());
            //删除实例
            Integer taskId = taskService.deleteCustInstanceAndTask(mysqlParamSupport.getAction(actionParams), custins,
                mysqlParamSupport.getOperatorId(actionParams));
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));

            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
