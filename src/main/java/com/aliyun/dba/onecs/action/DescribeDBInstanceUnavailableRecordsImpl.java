package com.aliyun.dba.onecs.action;


import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_AVAILABLE;
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsDescribeDBInstanceUnavailableRecordsImpl")
public class DescribeDBInstanceUnavailableRecordsImpl implements IAction {

    //private static Logger logger =Logger.getLogger(DescribeDBInstanceUnavailableRecordsImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceUnavailableRecordsImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsService custinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws
        RdsException {

        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (!SupportUtils.has(custins.getStatus(), CUSTINS_STATUS_AVAILABLE)) {
                //实例状态错误
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            Date startTime = mysqlParamSupport.getAndCheckStartTime(actionParams,DateUTCFormat.MINUTE_UTC_FORMAT);
            Date endTime = mysqlParamSupport.getAndCheckEndTime(actionParams,DateUTCFormat.MINUTE_UTC_FORMAT);
            if (startTime.getTime() >= endTime.getTime()) {
                //结束时间必须大于开始时间
                return createErrorResponse(ErrorCode.INVALID_PARAMETER_COMBINATION);
            }
            Integer pageSize = mysqlParamSupport.getAndCheckPageSize(actionParams);
            Integer pageNo = mysqlParamSupport.getAndCheckPageNo(actionParams);
            Integer total = custinsService.countCustInstanceFailure(custins.getId(), startTime, endTime);
            List<Map> mapList = null;
            if (total > 0) {
                mapList = custinsService.getCustInstanceFailure(custins.getId(), startTime, endTime, pageSize, pageNo);
            }
            Map<String, Object> data = new HashMap<String, Object>(9);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("StartTime", mysqlParamSupport.getStartTime(actionParams));
            data.put("EndTime", mysqlParamSupport.getEndTime(actionParams));
            data.put("TotalRecords", total);
            data.put("MaxRecordsPerPage", pageSize);
            data.put("PageNumbers", pageNo);
            data.put("ItemsNumbers", mapList != null ? mapList.size() : 0);
            data.put("Items", mapList != null ? mapList : new ArrayList<Object>(0));

            return data;
        }
        catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
