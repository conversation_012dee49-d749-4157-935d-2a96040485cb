package com.aliyun.dba.onecs.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsDeleteDBInstanceNetTypeImpl")
public class DeleteDBInstanceNetTypeImpl implements IAction {

    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            com.aliyun.dba.physical.action.DeleteDBInstanceNetTypeImpl deleteDBInstanceNetType = SpringContextUtil
                    .getBeanByClass(com.aliyun.dba.physical.action.DeleteDBInstanceNetTypeImpl.class);
            return deleteDBInstanceNetType.doActionRequest(custins, params);
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
        }
    }
}
