package com.aliyun.dba.onecs.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsCreateDBInstanceNetTypeImpl")
public class CreateDBInstanceNetTypeImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CreateDBInstanceNetTypeImpl.class);

    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            com.aliyun.dba.physical.action.CreateDBInstanceNetTypeImpl createDBInstanceNetType = SpringContextUtil
                    .getBeanByClass(com.aliyun.dba.physical.action.CreateDBInstanceNetTypeImpl.class);
            return createDBInstanceNetType.doActionRequest(custins, params);
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}



