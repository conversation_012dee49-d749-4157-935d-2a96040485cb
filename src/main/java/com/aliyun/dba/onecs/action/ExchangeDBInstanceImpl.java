package com.aliyun.dba.onecs.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.bak.dataobject.CheckBaksetDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.support.EcsCommonSupport;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_LASTEST;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;

//onecs clone接口
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsExchangeDBInstanceImpl")
public class ExchangeDBInstanceImpl implements IAction {

    //private static Logger logger =Logger.getLogger(ExchangeDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(ExchangeDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected EcsCommonSupport ecsCommonSupport;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected MySQLService mySQLService;
    @Autowired
    protected HostService ecsHostService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {

            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);

            //原逻辑
            String action = getParameterValue(actionParams, ParamConstants.ACTION);

            TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
            Map<String, Object> translistParamMap = new HashMap<>(8);

            trans.setsCinsid(custins.getId());
            trans.setsCinsReserved(1);
            List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
            trans.setsHinsid1(insIds.get(0));
            trans.setsLevelid(custins.getLevelId());
            if (insIds.size() > 1) {
                trans.setsHinsid2(insIds.get(1));
            }

            CustInstanceDO peerins = mysqlParamSupport.getAndCheckTargetCustInstance(actionParams);
            List<Integer> peerInsIds = custinsService.getInstanceIdsByCustinsId(peerins.getId());
            trans.setdCinsid(peerins.getId());
            trans.setsLevelid(peerins.getLevelId());
            trans.setdHinsid1(peerInsIds.get(0));
            if (peerInsIds.size() > 1) {
                trans.setdHinsid2(peerInsIds.get(1));
            } else {
                trans.setdHinsid2(0);
            }

            List<Map<String, Object>> insParams = ecsHostService.getHostInstanceGroupByCustinsId(peerins.getId());
            String ecsInstanceId = String.valueOf(insParams.get(0).get("insId"));
            translistParamMap.put("destEcsInsId", ecsInstanceId);
            trans.setParameter(JSON.toJSONString(translistParamMap));

            String taskKey = "exchange_ins";
            Integer id = mySQLService.createExchangeInstanceTask(getAction(actionParams), custins, peerins,
                    getOperatorId(actionParams), trans, taskKey);

            Map<String, Object> data = new HashMap<String, Object>(10);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", id);
            //返回值做任务类型区分
            data.put("TaskKey", taskKey);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
