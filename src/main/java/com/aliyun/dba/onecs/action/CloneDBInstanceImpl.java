package com.aliyun.dba.onecs.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.CheckBaksetDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.support.EcsCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckRestoreType;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;

//onecs clone接口
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("onecsCloneDBInstanceImpl")
public class CloneDBInstanceImpl implements IAction {

    //private static Logger logger =Logger.getLogger(CloneDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CloneDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected EcsCommonSupport ecsCommonSupport;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    private com.aliyun.dba.poddefault.action.CloneDBInstanceImpl poddefaultCloneDBInstance;
    @Autowired
    protected ResourceService resourceService;
    @Resource
    private CrossArchService crossArchService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        boolean isSuccess = false;
        
        // 快捷方式构建一些动态的参数，跟杜康的通用运维做集成.
    	mysqlParameterHelper.buildShortcutCloneParams(custins, actionParams);
    	
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

        try {

            custins = mysqlParamSupport.getAndCheckSourceCustInstance(actionParams);
            mysqlParamSupport.cloneValidSrcCustins(custins);

            String isValidCount = mysqlParamSupport.getParameterValue(actionParams, "isValidCount");
            //默认为校验，CloneDBInstanceForSecurityImpl中不校验，会传递该参数
            if (isValidCount == null) {
                //需要验证数量
                mysqlParamSupport.cloneValidCommon(custins, actionParams);
            }
            if (crossArchService.onecsCloneToK8s(actionParams)) {
                return poddefaultCloneDBInstance.doActionRequest(custins, actionParams);
            }

            TransListDO trans = new TransListDO(custins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
            Map<String, Object> translistParamMap = new HashMap<String, Object>(8);

            trans.setsCinsReserved(1);
            List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
            trans.setsHinsid1(insIds.get(0));

            String restoreType = getAndCheckRestoreType(actionParams);
            translistParamMap.put("restoreType", restoreType);

            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                //Date restoreTime = ecsCommonSupport.validRestoreByTime(actionParams, custins);
                DateTime restoreTimeUTC = custinsService.validRestoreByTimeSafe(actionParams, custins);
                trans.setIsBaseTime(1);
                //写入dbaas库，使用dbaas库时间
                Date recoverTimeBak = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_BAK);
                BakhistoryDO latestBakHisBefore = bakService.getLatestBakHisBefore(custins.getId(), recoverTimeBak, BAKTYPE_FULL, BAKWAY_SNAPSHOT);
                bakService.lockBinlogForRestore(custins.getId(), latestBakHisBefore.getBakBegin(), recoverTimeBak); // 锁定用于恢复的Binlog，避免被删除
                bakService.lockBakHisForRestore(latestBakHisBefore.getHisId());
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                        JSON.toJSONString(ImmutableMap.of(
                                "custinsId", custins.getId().toString(),
                                "begin", latestBakHisBefore.getBakBegin().getTime(),
                                "end", recoverTimeBak.getTime())));
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(latestBakHisBefore.getHisId()));

                trans.setRecoverTime(dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_DBAAS));

            } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                Long bakId = ecsCommonSupport.validRestoreByBakset(actionParams, custins);
                trans.setBakhisId(bakId);
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakId));
                bakService.lockBakHisForRestore(bakId);
            } else if (RESTORE_TYPE_LASTEST.equals(restoreType)) {
                //恢复到最近的时间
                trans.setIsBaseTime(0);
            } else {
                CheckBaksetDO checkBakset = ecsCommonSupport.validRestoreByUser(actionParams, custins);
                translistParamMap.put("downloadUrl", checkBakset.getDownloadUrl());
                translistParamMap.put("baksetName", checkBakset.getName());
            }
            CustInstanceDO cloneCustins = ecsCommonSupport.cloneSrcCustinsWithoutParentid(actionParams, custins,
                !custins.isDockerLogic());

            Integer targetUserId = null;
            String targetUserIdString = mysqlParamSupport.getParameterValue(actionParams, "targetUserId");
            if (targetUserIdString != null) {
                targetUserId = Integer.valueOf(targetUserIdString);
            }
            if (targetUserId != null) {
                cloneCustins.setUserId(targetUserId);
            }

            Integer taskId = ecsCommonSupport.cloneInsOnEcs(actionParams, custins, cloneCustins, trans,
                translistParamMap);

            Map<String, Object> data = new HashMap<String, Object>(10);
            data.put("DBInstanceID", cloneCustins.getId());
            data.put("DBInstanceName", cloneCustins.getInsName());
            data.put("TaskId", taskId);
            isSuccess = true;
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }finally {
            if(!isSuccess) {
                if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")) {
                    JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
                    bakService.unlockBinlogForRestore(
                            Integer.valueOf(lockBinlog.get("custinsId").toString()),
                            new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                            new Date(Long.parseLong(lockBinlog.get("end").toString()))
                    );
                }
                if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
                    String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
                    bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));

                }
            }
        }
    }
}
