package com.aliyun.dba.onecs.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.EcsResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.VipResModel;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.BaklistDO;
import com.aliyun.dba.bak.dataobject.CheckBaksetDO;
import com.aliyun.dba.bak.dataobject.LogPlanDO;
import com.aliyun.dba.bak.idao.BakIDao;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.docker.dataobject.DockerTaskInputParam;
import com.aliyun.dba.dockerdefault.service.DockerManager;
import com.aliyun.dba.ecs.dataobject.EcsImageDO;
import com.aliyun.dba.ecs.dataobject.EcsSecurityGroupDO;
import com.aliyun.dba.ecs.service.EcsImageService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.dataobject.RdsRegionAvzDO;
import com.aliyun.dba.onecs.service.MysqlOnEcsDBCenterService;
import com.aliyun.dba.onecs.service.MysqlOnEcsRegionService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.BAKTYPE_FULL;
import static com.aliyun.dba.bak.support.BakSupport.BAKWAY_SNAPSHOT;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_LASTEST;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAndCheckRestoreType;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_CREATING;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.DEDICATED_HOST_GOURP_TYPE;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_PRIMARY;

/**
 * Ecs环境下常用功能
 */
@Component
public class EcsCommonSupport {

    private static final Logger logger = Logger.getLogger(EcsCommonSupport.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected EcsService ecsService;
    @Autowired
    protected EcsImageService ecsImageService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected HostService ecsHostService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected MysqlOnEcsRegionService mysqlOnEcsRegionService;
    @Autowired
    protected MysqlOnEcsDBCenterService mysqlOnEcsDBCenterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private KmsService kmsService;
    @Autowired
    private BakIDao bakIDao;
    @Autowired
    private DockerManager dockerManager;

    /**
     * ecs上克隆实例
     */
    public Integer cloneInsOnEcs(Map<String, String> actionParams, CustInstanceDO srcCustins, CustInstanceDO cloneCustins, TransListDO trans,
                                 Map<String, Object> translistParamMap) throws RdsException {

        BakhistoryDO history = validCloneStorageForEcs(actionParams, srcCustins, cloneCustins, trans, translistParamMap);
        List<CustinsConnAddrDO> custinsConnAddrList = getTmpConnAddrList(actionParams, srcCustins);

        String ecsInstanceId = null;
        String diskId = null;
        Integer newHostId = null;
        boolean hostAllocated = false;
        try {
            cloneCustins.setKindCode(CustinsSupport.KIND_CODE_ECS_VM);
            //限制账户数和DB数
            cloneCustins.setMaxAccounts(1);
            cloneCustins.setMaxDbs(0);
            /****获取参数****/
            //获取实例所在的地域和可用区
            String clusterName = srcCustins.getClusterName();
            AVZInfo avzInfoFromCustInstance = avzSupport.getAVZInfoFromCustInstance(srcCustins);
            AVZInfo avzInfo = avzSupport.getAVZInfo(actionParams);
            Boolean isUserVpc = ecsService.isClusterUserVPCArch(srcCustins.getDbType(), srcCustins.getDbVersion(), clusterName);

            // 获取实例指定的region(子域)
            String srcRegion = avzInfoFromCustInstance.getRegion();
            String region = avzInfo.getRegion();
            // 获取实例指定的region(子域)
            if(!avzInfo.isValidForNewInstance()){
                avzInfo = avzInfoFromCustInstance;
            }
            boolean isCrossRegion = !srcRegion.equals(region);
            // 通过region/subdomain更新一次clusterName
            if (!isCrossRegion) {
                clusterName = srcCustins.getClusterName();
            } else {
                ClustersDO clusterDO = ecsService.getEcsAvailableClusterDO(region, cloneCustins.getDbType(), cloneCustins.getDbVersion(),
                    CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD, isUserVpc);
                if (clusterDO == null) {
                    throw new RdsException(ErrorCode.NO_VAILABLE_CLUSTER);
                }
                clusterName = clusterDO.getClusterName();
            }

            cloneCustins.setClusterName(clusterName);
            RdsRegionAvzDO regionAvzDO = mysqlOnEcsRegionService.getRdsRegionAvzDOByCluster(clusterName);
            String regionId = regionAvzDO.getRegion();
            String zoneId = regionAvzDO.getAvz();
            // 指定实例的链路类型
            String connType = cloneCustins.getConnType();

            // 获取网络类型
            CustinsConnAddrDO custinsConnAddr = custinsConnAddrList.get(0);
            Integer netType = custinsConnAddr.getNetType();
            //获取实例规格
            InstanceLevelDO levelDO = instanceService.getInstanceLevelByLevelId(cloneCustins.getLevelId());
            String classCode = levelDO.getClassCode();
            //获取链接串
            String connectionString = custinsConnAddr.getConnAddrCust();
            //获取VPC信息
            Integer tunnelId = -1;
            String vswitchId = null;
            String ipaddress = null;
            String userVpcId = null;
            String vpcInstanceId = cloneCustins.getInsName();
            if (CustinsSupport.isVpcNetType(netType)) {
                // VPC 实例， 必须传入vpcId, tunnelid， vswitchid， ipaddress
                userVpcId = custinsConnAddr.getVpcId();
                tunnelId = custinsConnAddr.getTunnelId();
                vswitchId = custinsConnAddr.getVswitchId();
                ipaddress = custinsConnAddr.getVip();
            }


            /****合规判断****/
            // 不支持共享实例
            if (cloneCustins.isShare()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            //不支持聚石塔实例
            if (cloneCustins.isTop()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_TOP_DBINSTANCE);
            }

            /****调用ECS OPEN API接口****/
            JSONObject resultJson = null;
            Map<String, Object> createEcsInstanceResult = null;
            Map<String, Object> createEcsDiskResult = null;
            String imageId = null;
            String securityGroupId = null;

            //call dbcenter to get data
            String ecsClassCode = ecsService.getEcsClassCode(cloneCustins, classCode);
            ClustersDO clustersDO = clusterService.getClusterByClusterName(clusterName);
            String siteName = clustersDO.getSiteName();

            //Boolean isUserVpc = ecsService.isClusterUserVPCArch(srcCustins.getDbType(), srcCustins.getDbVersion(), clusterName);
            // Get ecsAccount, vpcId, vSwitchId
            String ecsAccount = ecsService.getEcsAccount(cloneCustins.getUserId(), regionId);

            String vpcId = null;
            List<AvailableZoneInfoDO> ecsAvzInfoList;

            String restoreTypeApi = getAndCheckRestoreType(actionParams);
            if (RESTORE_TYPE_BAKID.equals(restoreTypeApi) || isCrossRegion) {
                logger.warn("clone_use_bakid_way");
                com.aliyun.dba.ecs.dataobject.VpcInfoDO vpcInfo = ecsService.getVpcInfo(
                        ecsAccount, regionId, zoneId, ipaddress, clusterName);
                vpcId = vpcInfo.getVpcId();
            } else {
                /*if (!srcRegion.equals(region)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_TRANS_TYPE, "Restore to time do not support cross avzone");
                }*/

                logger.warn("clone_use_restore_time_way");
                //clone custins ecs vpcId
                vpcId = getSrcCustinsVPCId(srcCustins);
                if (isDestVpcNet(actionParams)) {
                    logger.warn("dest_clone_ins_is_vpc_nettype");
                    com.aliyun.dba.ecs.dataobject.VpcInfoDO vpcInfo = ecsService.getVpcInfoByVpcId(vpcId);
                    String srcEcsCidrBlock = vpcInfo.getCidrBlock();
                    if (isSameVpcNet(srcEcsCidrBlock, ipaddress)) {
                        //网段相同,冲突
                        logger.error("vpc_address_user_address_conflict");
                        throw new RdsException(MysqlErrorCode.RSCECS_CIDRBLOCK_SAME_WTTH_IPADDRESS.toArray());
                    }
                    logger.warn("vpc_address_user_address_not_conflict, continue");
                } else {
                    logger.warn("not_restore_time, dest_clone_ins_no_vpc_nettype, skip");
                }
            }

            String vSwitchId = ecsService.getEcsVpcApi(vpcId).getAvailableSwitchId(vpcId, 1);
            ecsAvzInfoList = MultiAVZExParamDO.SingleAVZParam(zoneId, vSwitchId);
            // check vswitch info
            for (AvailableZoneInfoDO avzInfoDO : ecsAvzInfoList) {
                if (Validator.isNull(avzInfoDO.getVSwitchID())) {
                    throw new RdsException(ErrorCode.INVALID_VSWITCH_ID);
                }
            }

            //Image
            if (isUserVpc) {
                // 选择用户vpc镜像
                List<EcsImageDO> ecsImageDOList = ecsImageService.getEcsImageList(regionId, cloneCustins.getDbType(), null,
                        cloneCustins.getDbVersion(), 2, CustinsParamSupport.CUSTINS_PARAM_VALUE_ECS_ARCH_VERSION_VPC, null);
                imageId = ecsImageDOList.get(0).getImageId();
            } else {
                logger.warn("------region: " + regionId + "account: " + ecsAccount +
                        "db_type: " + cloneCustins.getDbType() + "vpcid: " + vpcId);
                List<EcsImageDO> ecsImageDOs = mysqlOnEcsDBCenterService.getEcsImageDOList(regionId, ecsAccount,
                    cloneCustins.getDbType(), vpcId);
                imageId = ecsImageDOs.get(0).getImageId();
            }

            if (!isUserVpc) {
                //SG
                List<EcsSecurityGroupDO> ecsSecurityGroupDOList = mysqlOnEcsDBCenterService.getEcsSGDOList(regionId, ecsAccount, vpcId, "default");
                securityGroupId = ecsSecurityGroupDOList.get(0).getSecurityGroupId();
                if (Validator.isNull(securityGroupId)) {
                    String customizedErrorDesc = "SecurityGroupId is null for creating instance";
                    logger.error(customizedErrorDesc);
                    throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, customizedErrorDesc);
                }
            }

            String osPassword = SupportUtils.getRandomPasswdForEcs(15);
            logger.warn("regionId:" + regionId + ",imageId:" + imageId + ",ecsClassCode:"
                    + ecsClassCode + ",vpcId:" + vpcId);
            if (Validator.isNull(ecsAccount) || Validator.isNull(regionId) || Validator
                    .isNull(imageId)
                    || Validator.isNull(ecsClassCode)
                    || Validator.isNull(vpcId)
                    || Validator.isNull(clusterName) || Validator.isNull(siteName)) {
                logger.error("any params is null for creating instance:" + cloneCustins.getInsName()
                        + "---regionId:" + regionId + ",imageId:" + imageId
                        + ",ecsClassCode:"
                        + ecsClassCode + ",vpcId:" + vpcId);
                if (Validator.isNull(osPassword)) {
                    logger.error("osPassword is null or empty string");
                }
                String customizedErrorDesc = "Invalid params to call ecs open api";
                throw new RdsException(ErrorCode.CALL_ECS_API_FAILURE, customizedErrorDesc);
            }

            // 指定还原的DB
            List<String> dbNames = null;
            String dbNamesJson = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_NAMES);
            if (dbNamesJson != null) {
                dbNames = JSONArray.parseArray(dbNamesJson, String.class);
            }

            // 创建实例记录
            custinsService.createCustInstance(cloneCustins);

            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, cloneCustins.getDbType());
            // 目标实例的clusterName需要确保在指定的subdomain下，缺省情况下使用源实例相同的cluster
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setUserId(cloneCustins.getUserId());
            resourceContainer.setAccessId(CustinsParamSupport.getParameterValue(actionParams,ParamConstants.ACCESSID));
            resourceContainer.setOrderId(CustinsParamSupport.getParameterValue(actionParams,ParamConstants.ORDERID));
            resourceContainer.setRequestId(CustinsParamSupport.getParameterValue(actionParams,ParamConstants.REQUEST_ID));

            Integer nodeCount = 1;
            CustinsResModel custinsResModel = new CustinsResModel(cloneCustins.getId());

            //src custins ecs vpcId
            String srcVpcId = getSrcCustinsVPCId(srcCustins);
            if (StringUtils.equals(srcVpcId, vpcId)) {
                //同一个vpcId,设置 srcCustinsId
                custinsResModel.setSrcCustinsId(srcCustins.getId());
            }

            custinsResModel.setConnType(connType);
            custinsResModel.setEcsDiskSize(mysqlParamSupport.getExtendDiskSizeForEcsIns(cloneCustins.getDbType(), cloneCustins.getDiskSize()));
            // ecs resource

            String vSwitchId1 = ecsAvzInfoList.get(0).getVSwitchID();
            EcsResModel ecsResModel = new EcsResModel(imageId);
            ecsResModel.setOsPassword(osPassword);
            ecsResModel.setInsCount(nodeCount);
            ecsResModel.setRegionId(regionId);
            ecsResModel.setSiteName(siteName);
            ecsResModel.setZoneId(zoneId);
            ecsResModel.setInstanceType(ecsClassCode);
            ecsResModel.setSecurityGroupId(securityGroupId);
            ecsResModel.setEcsVSwitchId(vSwitchId1);
            ecsResModel.setEcsVpcId(vpcId);
            ecsResModel.setEcsAccount(ecsAccount);
            ecsResModel.setSnapshotId(history.getDownloadUrl());

            //支持 5.7 单节点ESSD
            String specifyStorageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE, null);
            if(CustinsSupport.STORAGE_TYPE_CLOUD_ESSD.equalsIgnoreCase(specifyStorageType)){
                ecsResModel.setDataDiskCategory(CustinsSupport.STORAGE_TYPE_CLOUD_ESSD);
            }
            custinsResModel.setEcsResModel(ecsResModel);

            // vip resource
            VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
            vipResModel.setUserVisible(1);
            vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
            vipResModel.setVip(custinsConnAddr.getVip());
            vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
            vipResModel.setVpcId(custinsConnAddr.getVpcId());
            vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
            vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
            vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
            custinsResModel.addVipResModel(vipResModel);

            resourceContainer.addCustinsResModel(custinsResModel);
            // call resource manager
            Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
            if (!response.getCode().equals(200)) {
                custinsService.deleteCustInstance(cloneCustins);
                /*this.createResourceRecord(cloneCustins.getClusterName(), cloneCustins.getInsName(),
                    JSON.toJSONString(response));*/
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND, ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }

            /**
             * NOTE:
             * 快速克隆支持，这个是MySQL内部使用，在数据写坏的时候，任务流通过参数来识别这个场景，
             * 不要在用户场景下用这个逻辑!
             */
            String cloneMode = mysqlParaHelper.getParameterValue(MysqlParameterHelper.CLONE_MODE);
            if (cloneMode != null && MysqlParameterHelper.QUICK_MODE.equalsIgnoreCase(cloneMode)) {
        		// NOTE: 快速克隆走独立的逻辑，这是原来逻辑的重载
            	custinsParamService.setCustinsParam(cloneCustins.getId(), MysqlParameterHelper.CLONE_MODE, cloneMode);
        	}// END MySQL 快速克隆支持
            
            avzSupport.updateAVZInfoByInstanceIds(avzInfo, response.getData().getCustinsResRespModelList().get(0).getInstanceIdList());
            custinsParamService.updateAVZInfo(cloneCustins.getId(),avzInfo);

            hostAllocated = true;
            AllocateResRespModel.CustinsResRespModel allocateResRespModel = response.getData().getCustinsResRespModelList().get(0);
            cloneCustins.setClusterName(allocateResRespModel.getClusterName());

            List<Map<String, Object>> insParams = ecsHostService.getHostInstanceGroupByCustinsId(cloneCustins.getId());
            Map<String, Object> insParam = insParams.get(0);
            ecsInstanceId = (String) insParam.get("ip");

            // 下发创建实例任务
            //白名单对象
            trans.setdCinsid(cloneCustins.getId());
            trans.setdLevelid(cloneCustins.getLevelId());

            Long srcDiskSize = (history.getBaksetSize() / 1024L);
            trans.setsDisksize(srcDiskSize);

            trans.setdDisksize(cloneCustins.getDiskSize());
            List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(cloneCustins.getId());
            trans.setdHinsid1(insIds.get(0));
            trans.setdHinsid2((insIds.size() > 1) ? insIds.get(1) : 0);

            // 指定还原的DB
            if(dbNames != null){
                translistParamMap.put("dbNames", dbNames);
            }

            translistParamMap.put("destEcsInsId", ecsInstanceId);
            translistParamMap.put("destEcsOsPassword", osPassword);

            // 多主机的情况下ecsid再存一份在destEcsInsIds
            List<String> destEcsInsIds = new ArrayList<>();
            for (int i = 0; i < insParams.size(); ++i) {
                Map<String, Object> param = insParams.get(i);
                destEcsInsIds.add((String) param.get("ip"));
            }

            translistParamMap.put("destEcsInsIds", destEcsInsIds);

            trans.setParameter(JSON.toJSONString(translistParamMap));

            String restoreType = (String) translistParamMap.get("restoreType");
            Integer taskId = instanceService.cloneCustInstanceTaskOnEcs(mysqlParamSupport.getAction(actionParams), srcCustins,
                    trans, cloneCustins, mysqlParamSupport.getOperatorId(actionParams), cloneCustins.getId(),
                    RESTORE_TYPE_TIME.equals(restoreType), osPassword);
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));
            return taskId;
        } catch (Exception ex) {
            if (!hostAllocated) {
                // ecs未分配的时候，出现异常就清空cust_instance表的clone实例记录
                custinsService.deleteCustInstance(cloneCustins);
            }
            //this.createResourceRecord(cloneCustins.getClusterName(), cloneCustins.getInsName(), ex.getMessage());
            logger.error("cloneInsOnEcs fail to create ins on ecs,rollback now,delete ecs ins and disk");
            throw ex;
        }
    }

    public Long validRestoreByBaksetSingle(CustInstanceDO custins) throws RdsException {

        Long bakId = CheckUtils.parseLong(mysqlParaHelper.getParameterValue("BackupSetID"), null,
            null, ErrorCode.BACKUPSET_NOT_FOUND);
        mysqlParaHelper.getAndCheckBakhistory(custins, bakId);
        return bakId;
    }
    /**
     * 检查Docker实例是否支持按照时间点恢复 按照备份集恢复基本都支持
     */
    private boolean checkSupportRestoreByTime(CustInstanceDO srcCustins) {

        boolean supportRestore = false;
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(srcCustins.getLevelId());
        String composeTag = mysqlParaHelper.selectComposeTag(srcCustins.getClusterName());
        EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
            srcCustins.getDbType(), srcCustins.getDbVersion(), insLevel.getCategory(), composeTag);
        JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());
        for (String service : jsonServer.keySet()) {
            EngineService engineService = new Gson().fromJson(jsonServer.getString(service),
                EngineService.class);
            supportRestore = engineService.isSupportSupportRestoreByTime();
            if (supportRestore) {
                break;
            }
        }

        return supportRestore;
    }

    public Date validRestoreByTimeSingle(CustInstanceDO custins) throws RdsException {
        if (!custins.isMbaseSql()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE);
        }
        LogPlanDO logPlan = bakService.getLogPlanByCustinsId(custins.getId());
        if (custins.isMbaseSql() && (logPlan == null || !logPlan.isEnableBackupLog())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_LOG_STATUS);
        }
        Date restoreTime = mysqlParaHelper.getAndCheckTimeByParam(ParamConstants.RESTORE_TIME,
            DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME, DataSourceMap.DATA_SOURCE_DBAAS);
        mysqlParaHelper.checkRestoreTimeValid(custins, restoreTime, logPlan);

        return restoreTime;
    }
    public Map<String, Object> cloneInsOnDockerOnEcs(CustInstanceDO srcCustins)
        throws RdsException {

        //Now docker on ecs not support RESTORE_TYPE_TIME and RESTORE_TYPE_USER
        String restoreType = mysqlParaHelper.getAndCheckRestoreType();
        if (!RESTORE_TYPE_BAKID.equals(restoreType)) {
            boolean supportRestore = checkSupportRestoreByTime(srcCustins);
            if (!supportRestore) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
            }
        }

        RequestParamsDO params = new RequestParamsDO();
        params.setAvzInfo(avzSupport.getAVZInfo(ActionParamsProvider.ACTION_PARAMS_MAP.get()));
        params.setDbType(srcCustins.getDbType());
        params.setClassCode(mysqlParaHelper.getAndCheckClassCode());
        if (mysqlParaHelper.hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            String desc = SupportUtils.decode(
                mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION));
            params.setDesc(desc);
        }
        //accountMode to Account type
        params.setAccountType(srcCustins.getAccountMode().toString());
        // set region 相关信息
        params.setBizType(CustinsSupport.BIZ_TYPE_RDS);
        params.setType(srcCustins.getType());
        params.setContainerType("docker");
        params.setAction(mysqlParaHelper.getAction());
        params.setOperatorId(mysqlParaHelper.getOperatorId());
        params.setClusterName(srcCustins.getClusterName());
        //permit user chage from
        String region = null;
        region = clusterService.getRegionByCluster(srcCustins.getClusterName());
        if (mysqlParaHelper.hasParameter("SubDomain")) {
            // 从客户端读取子域信息
            region = clusterService.getAndCheckSubDomain(
                mysqlParaHelper.getParameterValue("SubDomain"));
            if (Validator.isNull(region)) {
                throw new RdsException(ErrorCode.INVALID_REGION);
            }
        }
        params.setRegion(region);
        //get regionid and zone info from srccustins cluste
        params.setRegionId(mysqlParaHelper.getAndCheckRegionID());
        params.setZoneId(mysqlParaHelper.getAndCheckAvZone());

        List<CustInstanceDO> childCustinsList = custinsService.getCustInstanceUnitByParentIdAndCharacterType(
            srcCustins.getId(), CustinsSupport.CHARACTER_TYPE_PHYSICAL);
        if (childCustinsList.size() <= 0) {
            if (srcCustins.isMysql57Physical() || srcCustins.isMysql80Physical()) {
                // physical backup clone ecs instance
                params.setHostType(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD);
            } else {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
            }
        } else {
            params.setHostType(custinsService.getCustinsHostType(childCustinsList.get(0).getId()));
        }
        params.setNetType(CustinsSupport.NET_TYPE_VPC);

        // set product info
        params.setEngine(srcCustins.getDbType());
        params.setDbType(srcCustins.getDbType());
        params.setEngineVersion(srcCustins.getDbVersion());
        params.setDbVersion(srcCustins.getDbVersion());
        // set uerinfo
        params.setUserId(srcCustins.getUserId());
        params.setMaintainStartTime(srcCustins.getMaintainStarttime());
        params.setMaintainEndTime(srcCustins.getMaintainEndtime());
        params.setDbBInstanceName(mysqlParaHelper.getDBInstanceName());
        //todo: 磁盘大小需要检查大小
        params.setStorageType(mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE));
        String dataDiskCategory = custinsService.getDataDiskCategory(srcCustins.getId(),
            params.getStorageType(),
            CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD);
        params.setDataDiskCategory(dataDiskCategory);
        params.setStorage(mysqlParaHelper.getParameterValue(ParamConstants.STORAGE));
        //用户vpc场景clone的实例还是用户vpc的
        params.setOptmization(srcCustins.getIsAccept().toString());
        // set vpc net info
        params.setUserVpc(true);
        String vpcId = mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID);
        CheckUtils.checkValidForVPCId(vpcId);
        params.setUserVpcId(vpcId);
        String tunnelId = mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID);
        CheckUtils.checkValidForTunnelId(tunnelId);
        params.setTunnelId(Integer.valueOf(tunnelId));
        String vswitchID = mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID);
        CheckUtils.checkValidForVswitchId(vswitchID);
        params.setVswitchId(vswitchID);
        String ipaddres = mysqlParaHelper.getParameterValue(ParamConstants.IP_ADDRESS);
        CheckUtils.checkValidForIPAddress(ipaddres);
        params.setIpaddress(ipaddres);

        params.setConnType(srcCustins.getConnType());
        params.setConnectionString(mysqlParaHelper.getParameterValue("connectionstring"));
        params.setConnAddrCust(mysqlParaHelper.getConnAddrCust(params.getConnectionString(),
                mysqlParaHelper.getRegionIdByClusterName(srcCustins.getClusterName()), params.getDbType()));
        params.setPortStr(CustinsSupport.getConnPort(mysqlParaHelper.getParameterValue("port"), params.getDbType()));
        //params.setTaskQueueParam(getTaskQueueParam());
        //set proxy 信息
        params.setProxyGroupId(0);
        params.setStorageType(mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE));

        // set byok param
        String keyId;
        if (childCustinsList.size()>0) {
            keyId = kmsService.getSrcCustinsKeyId(srcCustins.getId(), childCustinsList.get(0).getId());
        } else {
            keyId = kmsService.getSrcCustinsKeyId(srcCustins.getId(), srcCustins.getId());
        }
        String roleArn = kmsService.getUserRoleArn(mysqlParaHelper.getUID());
        params.setCmkId(keyId);
        params.setRoleArn(roleArn);
        params.setUid(mysqlParaHelper.getUID());

        //set bak restore info
        BakhistoryDO bakHistory = null;
        params.setRestoreType(Integer.parseInt(restoreType));
        Integer diskSize = Integer.parseInt(params.getStorage());
        Date restoreTime = null;
        if (RESTORE_TYPE_TIME.equals(restoreType)) {
            if ("mariadb".equals(srcCustins.getDbType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
            }
            restoreTime = validRestoreByTimeSingle(srcCustins);
        } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            Long bakId = validRestoreByBaksetSingle(srcCustins);
            params.setBackUpSetId(bakId);
        } else {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
        }

        bakHistory = mysqlParaHelper.validCloneStorageForEcs(srcCustins, diskSize * 1024L, restoreType, restoreTime);
        String baksetType = bakHistory.getBakWay();
        JSONObject bakHistObject = JSONArray.parseObject(bakHistory.getSlaveStatus());
        if (BAKWAY_SNAPSHOT.equals(baksetType)) {
            JSONArray bakHistArray = bakHistObject.getJSONArray(bakHistObject.keySet().toArray()[0].toString());
            JSONObject jsonObject = bakHistArray.getJSONObject(0);
            String snapShotId = jsonObject.getJSONObject("slave_status").getString("SNAPSHOT_ID");
            params.setSnapShotId(snapShotId);
        }

        BaklistDO bakList = (bakIDao.getBaklistByCustinsId(srcCustins.getId(), null, 0)).get(0);

        //init task queue parames
        // 获取备份策略参数，下发实例创建时，这些参数将被写入parameter字段中
        Map<String, Object> taskQueueParam = new HashMap<>(1);
        Map<String, Object> restoreParam = new HashMap<>(4);

        restoreParam.put("snapShotId", params.getSnapShotId());
        //todo: now only support one physical custins clone
        restoreParam.put("srcParentCustId", srcCustins.getId());
        restoreParam.put("restoreType", restoreType);
        restoreParam.put("baksetType", baksetType);
        if (RESTORE_TYPE_TIME.equals(restoreType)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            restoreParam.put("restoreTime", sdf.format(restoreTime));
        }
        restoreParam.put("bakHisID", bakHistory.getHisId());

        Map<String, Object> baklistParam = new HashMap<>(3);
        baklistParam.put("retention", bakList.getRetention());
        baklistParam.put("bak_period", bakList.getBakPeriod());
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        baklistParam.put("bak_begin", sdf.format(bakList.getBakBegin()));

        taskQueueParam.put("restore", restoreParam);
        taskQueueParam.put("backup", baklistParam);
        params.setTaskQueueParam(taskQueueParam);

        if (srcCustins.isMbaseSql() && (srcCustins.isCustinsDockerOnEcs() || srcCustins.isMysql57Physical() || srcCustins.isMysql80Physical()) && !DEDICATED_HOST_GOURP_TYPE.equals(clusterService.getClusterByClusterName(params.getClusterName()).getType())) {
            String multiAVZExParamStr = mysqlParaHelper.getParameterValue(ParamConstants.MULTI_AVZ_EX_PARAM);
            CheckUtils.checkStrNotEmpty(multiAVZExParamStr, ParamConstants.MULTI_AVZ_EX_PARAM + " is empty");
            params.setMultiAVZExParam(JSON.parseObject(multiAVZExParamStr, MultiAVZExParamDO.class));
        }

        Map<String, List<MycnfCustinstanceDO>> mycnfCustinstancesMap = mysqlParaHelper.getAndCheckExternalParameter(srcCustins);
        params.setMycnfCustinstancesMap(mycnfCustinstancesMap);
        params.setClone(true);
        params.setAccountMode(srcCustins.getAccountMode());
        DockerTaskInputParam dockerTaskInputParam = dockerManager.createDockerDbInstance(params);
        dockerTaskInputParam.setSrcCusIns(srcCustins);
        Map<String, Object> responseData = dockerManager.disPatchDockerTask(dockerTaskInputParam, true);
        custinsParamService.setCustinsParam(
            Integer.parseInt(responseData.get(ParamConstants.DB_INSTANCE_ID).toString()),
            CustinsParamSupport.DATADISK_STORAGE_TYPE,
            dataDiskCategory);
        taskService.updateTaskPenginePolicy(Integer.parseInt(responseData.get(ParamConstants.TASK_ID).toString()),
            mysqlParaHelper.getPenginePolicyID());

        return responseData;
    }

    public BakhistoryDO validCloneStorageForEcs(Map<String, String> actionParams, CustInstanceDO srcCustins, CustInstanceDO cloneCustins, TransListDO trans,
                                                 Map<String, Object> translistParamMap)
            throws RdsException {
        String restoreType = (String) translistParamMap.get("restoreType");
        Date recoverTime = trans.getRecoverTime();
        return validCloneStorageForEcs(actionParams, srcCustins, cloneCustins.getDiskSize(), restoreType, recoverTime);
    }

    private BakhistoryDO validCloneStorageForEcs(Map<String, String> actionParams,
                                                 CustInstanceDO srcCustins,
                                                 Long cloneCustinsDiskSize,
                                                 String restoreType,
                                                 Date recoverTime) throws RdsException {
        long diskSize = 0;
        BakhistoryDO history = new BakhistoryDO();
        Integer custinsId = srcCustins.getId();
        if (RESTORE_TYPE_TIME.equals(restoreType)) {
            history = bakService.getBakhistoryByRecoverTime(custinsId, recoverTime, BAKWAY_SNAPSHOT, BAKTYPE_FULL);
            Long maxBinlogSize = bakService.getMaxArchivelogByRecoverTime(custinsId, recoverTime, history.getBakBegin());

            String baksetInfo = history.getBaksetInfo();
            JSONObject baksetInfoJsonObject = JSONObject.parseObject(baksetInfo);
            long diskSizeUsed = baksetInfoJsonObject.getLongValue("disk_size");

            if (cloneCustinsDiskSize < history.getBaksetSize() / 1024) {
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }
            // 克隆实例的磁盘不能小于快照的大小 + 2 * max_binlog，单位KB
            diskSize = diskSizeUsed * 1024 + 2 * maxBinlogSize / 1024;

        } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            Long bakId = CheckUtils.parseLong(mysqlParamSupport.getParameterValue(actionParams, "BackupSetID"), null,
                    null, ErrorCode.BACKUPSET_NOT_FOUND);
            history = bakService.getBakhistoryByBackupSetId(custinsId, bakId);
            // 克隆实例的磁盘不能小于快照的大小，单位KB
            diskSize = history.getBaksetSize();
        } else if (RESTORE_TYPE_LASTEST.equals(restoreType)) {
            DateTime nowUTC = DateTime.now(DateTimeZone.UTC);
            Date nowTime = dtzSupport.getSpecificTimeZoneDate(nowUTC, DATA_SOURCE_BAK);
            history = bakService.getBakhistoryByRecoverTime(custinsId, nowTime, BAKWAY_SNAPSHOT, BAKTYPE_FULL);
            Long maxBinlogSize = bakService.getMaxArchivelogByRecoverTime(custinsId, nowTime, history.getBakBegin());
            String baksetInfo = history.getBaksetInfo();
            JSONObject baksetInfoJsonObject = JSONObject.parseObject(baksetInfo);
            long diskSizeUsed = baksetInfoJsonObject.getLongValue("disk_size");

            if (cloneCustinsDiskSize < history.getBaksetSize() / 1024) {
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }
            // 克隆实例的磁盘不能小于快照的大小 + 2 * max_binlog，单位KB
            diskSize = diskSizeUsed * 1024 + 2 * maxBinlogSize / 1024;
        }

        if (cloneCustinsDiskSize < diskSize / 1024) {
            throw new RdsException(ErrorCode.INVALID_STORAGE);
        }

        return history;
    }

    public List<CustinsConnAddrDO> getTmpConnAddrList(Map<String, String> actionParams, CustInstanceDO srcCustins)
            throws RdsException {
        List<CustinsConnAddrDO> tmpCustinsConnAddrList = new ArrayList<CustinsConnAddrDO>();

        // 如果未传NetType，默认为私网;
        Integer specifyNetType = CustinsSupport.NET_TYPE_PRIVATE;
        if (mysqlParamSupport.hasParameter(actionParams, "DBInstanceNetType")) {
            specifyNetType = CustinsSupport.getNetType(actionParams);
        }
        Boolean isVpcNetType = CustinsSupport.isVpcNetType(specifyNetType);

        String connPrex = CheckUtils.checkNullForConnectionString(
                getParameterValue(actionParams, ParamConstants.CONNECTION_STRING));
        String portStr = CustinsSupport.getConnPort(getParameterValue(actionParams, ParamConstants.PORT),
                srcCustins.getDbType());

        // 创建连接地址对象
        String connAddrCust = mysqlParamSupport.getConnAddrCust(connPrex, mysqlParamSupport.getRegionIdByClusterName(srcCustins.getClusterName()), srcCustins.getDbType());

        // 创建实例连接对象
        String vpcInstanceId = null;
        if (isVpcNetType) {
            vpcInstanceId = getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID);
            if (vpcInstanceId == null) {
                vpcInstanceId = mysqlParamSupport.getDBInstanceName(actionParams);
            }
        }
        CustinsConnAddrDO custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(
                connAddrCust,
                portStr,
                specifyNetType,
                CustinsValidator
                        .getRealNumber(getParameterValue(actionParams, ParamConstants.TUNNEL_ID), -1),
                getParameterValue(actionParams, ParamConstants.VPC_ID),
                getParameterValue(actionParams, ParamConstants.VSWITCH_ID),
                getParameterValue(actionParams, ParamConstants.IP_ADDRESS),
                vpcInstanceId);

        tmpCustinsConnAddrList.add(custinsConnAddr);
        return tmpCustinsConnAddrList;
    }

    public String getSrcCustinsVPCId(CustInstanceDO srcCustins) throws RdsException {
        List<InstanceDO> instances = instanceService.getInstanceByCustinsId(srcCustins.getId());
        if (instances == null || instances.isEmpty()) {
            logger.error("srcCustins_has_no_instances");
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
        String srcVpcId = instances.get(0).getVpcId();
        if (StringUtils.isBlank(srcVpcId)) {
            logger.error("srcCustins_has_no_vpcid");
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
        return srcVpcId;
    }

    private boolean isSameVpcNet(String srcEcsIP, String userIp) {
        logger.warn("src_ecs_ip=" + srcEcsIP + ",user_ip=" + userIp);
        if (userIp.startsWith("10")) {
            return false;
        }
        boolean srcIs192 = is192NetSegment(srcEcsIP);
        boolean userIs192 = is192NetSegment(userIp);
        if (srcIs192 == userIs192) {
            return true;
        }

        return false;
    }

    private boolean isDestVpcNet(Map<String, String> actionParams) throws RdsException {
        Integer specifyNetType = CustinsSupport.NET_TYPE_PRIVATE;
        if (mysqlParamSupport.hasParameter(actionParams, "DBInstanceNetType")) {
            specifyNetType = CustinsSupport.getNetType(actionParams);
        }
        Boolean isVpcNetType = CustinsSupport.isVpcNetType(specifyNetType);
        return isVpcNetType;
    }

    private boolean is192NetSegment(String ip) {
        if (ip.startsWith("192")) {
            return true;
        }
        return false;
    }

    public CustInstanceDO getTmpCustinsWithoutUpgrade(CustInstanceDO srcCustins) {
        CustInstanceDO tmpCusitns = srcCustins.clone();
        tmpCusitns.setId(null);
        tmpCusitns.setStatus(CUSTINS_STATUS_CREATING);
        tmpCusitns.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        tmpCusitns.setIsTmp(1);
        tmpCusitns.setInsName("tmp" + System.currentTimeMillis() + "-" + srcCustins.getInsName());
        return tmpCusitns;
    }

    public CustInstanceDO cloneSrcCustinsWithoutParentid(Map<String, String> actionParams, CustInstanceDO srcCustins, boolean isSetParentId) throws RdsException {
        CustInstanceDO cloneCustins = srcCustins.clone();
        cloneCustins.setId(null);
        cloneCustins.setClusterName("");
        cloneCustins.setStatus(CUSTINS_STATUS_CREATING);
        cloneCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        cloneCustins.setIsTmp(CUSTINS_INSTYPE_PRIMARY);
        cloneCustins.setInsType(CUSTINS_INSTYPE_PRIMARY);
        cloneCustins.setInsName(mysqlParamSupport.getDBInstanceName(actionParams));
        if (isSetParentId) {
            cloneCustins.setParentId(srcCustins.getId());
        }
        cloneCustins.setMaintainStarttime(srcCustins.getMaintainStarttime());
        cloneCustins.setMaintainEndtime(srcCustins.getMaintainEndtime());

        Integer bizType = mysqlParamSupport.getAndCheckBizType(actionParams);
        // 设置实例规格信息(gp不能让用户任意的选择规格,只能继承父实例的规格,包括levelID和groupCount)
        if (!cloneCustins.isGpdb()) {
            setInstanceLevel(cloneCustins, mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_CLASS),
                    bizType, mysqlParamSupport.getParameterValue(actionParams, ParamConstants.STORAGE));
        }

        // 设置实例描述
        if (mysqlParamSupport.hasParameter(actionParams, ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            String desc = SupportUtils
                    .decode(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_DESCRIPTION));
            cloneCustins.setComment(CheckUtils
                    .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION));
        }

        return cloneCustins;
    }

    private CustInstanceDO setInstanceLevel(CustInstanceDO custins, String classCode,
                                            Integer bizType,
                                            String diskSize) throws RdsException {
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
        if (insLevel == null) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        } else {
            custins.setLevelId(insLevel.getId());
            custins.setDiskSize(insLevel.getDiskSize());
        }
        if (Validator.isNotNull(diskSize) && custins.isExcluse()) {
            ResourceKey resourceKey = ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE;
            if (CustinsSupport.BIZ_TYPE_PARTITION.equals(bizType)) {
                resourceKey = ResourceKey.RESOURCE_PARTITION_MAX_DISK_SIZE;
            }
            Integer maxDiskSize = resourceSupport.getIntegerRealValue(resourceKey);
            custins.setDiskSize(CheckUtils.parseInt(diskSize, 5, maxDiskSize, ErrorCode.INVALID_STORAGE) * 1024L);
        }
        return custins;
    }


    public CheckBaksetDO validRestoreByUser(Map<String, String> actionParams, CustInstanceDO custins) throws RdsException {
        if (custins.isCustinsOnEcs()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
        }
        String downloadUrl = mysqlParamSupport.getAndCheckDownloadUrl(actionParams);
        CheckBaksetDO bakset = mysqlParamSupport.getAndCheckCheckBakset(actionParams);
        bakset.setDownloadUrl(downloadUrl);
        return bakset;
    }


    public Date validRestoreByTime(Map<String, String> actionParams, CustInstanceDO custins) throws RdsException {

        LogPlanDO logPlan = bakService.getLogPlanByCustinsId(custins.getId());
        /*如果实例配置binlog不上传，API直接禁止做还原时间点操作*/
        //上传方式 0：不上传 1：落地上传'
        if (custins.isMysql() && (logPlan == null || !logPlan.isEnableBackupLog())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_LOG_STATUS);
        }

        Date restoreTime = mysqlParamSupport.getAndCheckTimeByParam(actionParams, ParamConstants.RESTORE_TIME,
                DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME);
        mysqlParamSupport.checkRestoreTimeValid(custins, restoreTime, logPlan);
        return restoreTime;
    }

    public Long validRestoreByBakset(Map<String, String> actionParams, CustInstanceDO custins) throws RdsException {
        Long bakId = CheckUtils.parseLong(mysqlParamSupport.getParameterValue(actionParams, "BackupSetID"), null,
                null, ErrorCode.BACKUPSET_NOT_FOUND);
        mysqlParamSupport.getAndCheckBakhistory(custins, bakId);
        return bakId;
    }

    /**
     * 获取Ecs上的实例规格信息
     *
     * @param custins
     * @param classCode
     * @return
     * @throws RdsException
     */
    public CustInstanceDO setInstanceLevelOnEcs(CustInstanceDO custins, String classCode,
                                                Integer bizType,
                                                String diskSize) throws RdsException {
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);

        if (insLevel == null) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        } else {
            custins.setLevelId(insLevel.getId());
            custins.setDiskSize(insLevel.getDiskSize());
        }
        if (Validator.isNotNull(diskSize) && custins.isExcluse()) {
            ResourceKey resourceKey = ResourceKey.RESOURCE_CUSTINS_ON_ECS_MAX_DISK_SIZE;
            Integer maxDiskSize = new ResourceSupport().getIntegerRealValue(resourceKey);
            custins.setDiskSize(CheckUtils.parseInt(diskSize, 20, maxDiskSize, ErrorCode.INVALID_STORAGE) * 1024L);
        }
        return custins;
    }

    public void setEcsSgRelParams(Map<String, String> actionParams, Map<String, Object> taskQueueParam) {
        String ecsSecurityGroupId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.ECS_SECURITY_GROUP_ID);
        if (ecsSecurityGroupId != null && !ecsSecurityGroupId.isEmpty()) {
            Map<String, Object> ecsSGParams = new HashMap<String, Object>(2);
            ecsSGParams.put("region_id", mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REGION_ID));
            ecsSGParams.put("sg_id", ecsSecurityGroupId);
            taskQueueParam.put("ecs_sg", ecsSGParams);
        }
    }


}
