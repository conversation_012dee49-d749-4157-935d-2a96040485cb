package com.aliyun.dba.onecs.service;

import com.aliyun.dba.ecs.dataobject.EcsImageDO;
import com.aliyun.dba.ecs.dataobject.EcsSecurityGroupDO;

import java.util.List;

/**
 * 代替DBCenterApi和DBCenterService
 * */
public interface MysqlOnEcsDBCenterService {

    List<EcsImageDO> getEcsImageDOList(String regionId, String ecsAccount, String dbType, String vpcId);

    List<EcsSecurityGroupDO> getEcsSGDOList(String regionId, String ecsAccount, String vpcId, String sgName);
}
