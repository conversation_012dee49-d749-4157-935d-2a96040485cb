package com.aliyun.dba.onecs.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.commonkindcode.support.ParamChecker;
import com.aliyun.dba.commonkindcode.support.helper.TimezoneHelper;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.ecs.dataobject.EcsImageQuery;
import com.aliyun.dba.ecs.service.EcsBuilderTemplate;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by rongqiao.yurq on 2017/9/30.
 * the MysqlOnEcsBuilder must used as multi-instances, cannot be singleton
 */
@Component
@Scope("prototype")
public class MysqlOnEcsBuilder extends EcsBuilderTemplate {
    @Override
    protected void doCheckBefore() throws RdsException {
        Integer bakReten = CheckUtils.parseInt(ecsSupport.getParameterValue(actionParams,"backupretentionperiod"),
                1, 730,
                ErrorCode.INVALID_BACKUPRETENTIONPERIOD);
        if (7 != bakReten) {
            throw new RdsException(ErrorCode.INVALID_BACKUPRETENTION);
        }
    }

    @Override
    protected void doUpdateCustinsMore() throws RdsException {
        // custins_param 记录实例参数模板
        List<CustinsParamDO> custinsParams = custInstanceDO.getCustinsParams();
        String paramGroupId = ecsSupport.getParameterValue(actionParams, ParamConstants.DB_PARAM_GROUP_ID,"");
        String defaultTimeZone = ecsSupport.getParameterValue(actionParams, ParamConstants.DB_PARAM_TIME_ZONE,"");
        if (StringUtils.isNotBlank(defaultTimeZone)) {
            if (!TimezoneHelper.validator(defaultTimeZone)) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, ErrorCode.INVALID_PARAMETERS.getDesc() + "[Invalid defaultTimeZone:" + defaultTimeZone + "]");
            }
        }
        Map<String, String> customMysqlParams = ecsSupport.getAndCheckMysqlCustomParams(actionParams);

        // 记录实例参数模板，参数模板详情，特定参数
        custinsParams.add(new CustinsParamDO(null, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId));
        custinsParams.add(new CustinsParamDO(null, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO,
                JSON.toJSONString(SysParamGroupHelper.describeSysParamGroupId(paramGroupId))));
        custinsParams.add(new CustinsParamDO(null, CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS,
                JSON.toJSONString(customMysqlParams)));

        custInstanceDO.setCustinsParams(custinsParams);
        custInstanceDO.setSqlwallSwitch(
                ecsSupport.getParameterValue(actionParams,"SqlwallSwitch", "false").equalsIgnoreCase("true") ? 1 : 0);
        custInstanceDO.setSqlwallTimeoutEvent(
                Integer.valueOf(ecsSupport.getParameterValue(actionParams,"SqlwallTimeoutEvent", "0")));
        custInstanceDO.setSqlwallInjectEvent(
                Integer.valueOf(ecsSupport.getParameterValue(actionParams,"SqlwallInjectEvent", "0")));
    }

    @Override
    protected List<CustinsParamDO> doGetCustinsParams() throws RdsException {
        List<CustinsParamDO> custinsParams = commonCustinsParams();
        if (custInstanceDO.isTop()) {
            custinsParams.add(new CustinsParamDO(null,
                    CustinsParamSupport.CUSTINS_PARAM_SYNC_USER_ON_MAINTAIN_LIST,
                    resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_SYNC_USER_ON_MAINTAIN_LIST_DEFAULT).toString()));
        }
        return custinsParams;
    }

    @Override
    protected Long doGetDiskMB() throws RdsException {
        Long extendDiskSizeGB = Math.max(Math.round(custInstanceDO.getDiskSize().floatValue() / 1024 * 0.2), 1L);
        return custInstanceDO.getDiskSize() + Math.min(extendDiskSizeGB * 1024, 20 * 1024L);
    }

    @Override
    protected EcsImageQuery doGetImageQuery() throws RdsException {
        return new EcsImageQuery(getEcsAccount(), regionId, getVpcId(), custInstanceDO.getDbType(),
                null, null, 0, null);
    }



    @Override
    protected Map<String, Object> doGetTaskParams() throws RdsException {
        // 获取备份策略参数，下发实例创建时，这些参数将被写入parameter字段中
        Map<String, Object> taskQueueParam = new HashMap<String, Object>(2);
        taskQueueParam.put("backup", getTaskBakParams());
        Map<String, Object> ecsSGParams = getTaskSGParams();
        if(ecsSGParams != null){
            taskQueueParam.put("ecs_sg", ecsSGParams);
        }
        return taskQueueParam;
    }


}
