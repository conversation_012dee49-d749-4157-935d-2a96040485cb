package com.aliyun.dba.onecs.service;

import com.aliyun.dba.onecs.dataobject.ClusterDO;
import com.aliyun.dba.onecs.dataobject.RdsRegionAvzDO;
import com.aliyun.dba.onecs.idao.MysqlOnEcsRegionServiceIDao;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("mysqlOnEcsRegionService")
public class MysqlOnEcsRegionServiceImpl implements MysqlOnEcsRegionService{

    private static final Logger logger = Logger.getLogger(MysqlOnEcsRegionServiceImpl.class);
    @Autowired
    private MysqlOnEcsRegionServiceIDao mysqlOnEcsRegionServiceIDao;

    @Override
    public ClusterDO getClusterDO(String clusterName) {

        ClusterDO clusterDO = mysqlOnEcsRegionServiceIDao.getClusterByName(clusterName);
        if(clusterDO == null){
            logger.warn("can not find ClusterDO by clusterName="+clusterName);
            return null;
        }
        else{
            return clusterDO;
        }
    }

    @Override
    public RdsRegionAvzDO getRdsRegionAvzDOByCluster(String clusterName){
        ClusterDO clusterDO = getClusterDO(clusterName);
        List<RdsRegionAvzDO> rdsRegionAvzDOList =
            mysqlOnEcsRegionServiceIDao.getRdsRegionAvzDOByLocation(clusterDO.getLocation());
        if(rdsRegionAvzDOList.isEmpty()){
            logger.warn("can not find RdsRegionAvzDO by clusterName="+clusterName);
            return null;
        }
        else{
            return rdsRegionAvzDOList.get(0);
        }
    }

}
