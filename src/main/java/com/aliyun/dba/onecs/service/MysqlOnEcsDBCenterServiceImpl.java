package com.aliyun.dba.onecs.service;

import com.aliyun.dba.ecs.dataobject.EcsImageDO;
import com.aliyun.dba.ecs.dataobject.EcsSecurityGroupDO;
import com.aliyun.dba.onecs.idao.MysqlOnEcsDBCenterServiceIDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("mysqlOnEcsDBCenterService")
public class MysqlOnEcsDBCenterServiceImpl implements MysqlOnEcsDBCenterService{

    @Autowired
    private MysqlOnEcsDBCenterServiceIDao mysqlOnEcsDBCenterServiceIDdao;

    @Override
    public List<EcsImageDO> getEcsImageDOList(String regionId, String ecsAccount, String dbType, String vpcId) {

        EcsImageDO ecsImageDOCondition = new EcsImageDO();
        ecsImageDOCondition.setRegionId(regionId);
        ecsImageDOCondition.setUserName(ecsAccount);
        ecsImageDOCondition.setDbType(dbType);
        ecsImageDOCondition.setStatus((byte)0);
        ecsImageDOCondition.setVpcId(vpcId);
        List<EcsImageDO> ecsImageDOList = mysqlOnEcsDBCenterServiceIDdao.getEcsImageDOList(ecsImageDOCondition, 0, 100);
        if (null != ecsImageDOList && ecsImageDOList.size() != 0) {
            return ecsImageDOList;
        }
        return null;
    }

    @Override
    public List<EcsSecurityGroupDO> getEcsSGDOList(String regionId, String ecsAccount, String vpcId, String sgName) {

        EcsSecurityGroupDO ecsSecurityGroupDOCondition = new EcsSecurityGroupDO();
        ecsSecurityGroupDOCondition.setUserName(ecsAccount);
        ecsSecurityGroupDOCondition.setRegionId(regionId);
        ecsSecurityGroupDOCondition.setVpcId(vpcId);
        ecsSecurityGroupDOCondition.setStatus((byte)0);
        ecsSecurityGroupDOCondition.setSecurityGroupName(sgName);
        List<EcsSecurityGroupDO> ecsSecurityGroupDOList = mysqlOnEcsDBCenterServiceIDdao.getEcsSecurityGroupDOList(ecsSecurityGroupDOCondition, 0, 100);
        if (null != ecsSecurityGroupDOList && ecsSecurityGroupDOList.size() != 0) {
            return ecsSecurityGroupDOList;
        }
        return null;
    }
}
