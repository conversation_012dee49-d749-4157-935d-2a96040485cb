package com.aliyun.dba.log4jutils;


import org.apache.log4j.PatternLayout;
import org.apache.log4j.spi.LoggingEvent;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 在日志毫秒数之后添加100 - 999的递增序列号，用于解决一毫秒内多条日志查询排序问题
 * */
public class SerialNumPatternLayout extends PatternLayout {

    private static final int MIN_NUM = 100;
    private static final int MAX_NUM = 999;
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private int serialNum = MIN_NUM;

    public SerialNumPatternLayout() {
        super();
    }

    public SerialNumPatternLayout(String pattern) {
        super(pattern);
    }

    /**
     * 线程安全获取并递增序列号
     * */
    private synchronized int getSerialNum() {
        if (++serialNum > MAX_NUM) {
            serialNum = MIN_NUM;
        }
        return serialNum;
    }

    @Override
    public String format(LoggingEvent event) {
        long currentTimeMillis = event.getTimeStamp();
        Instant instant = Instant.ofEpochMilli(currentTimeMillis);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        String formattedDateTime = dateTime.format(formatter);
        String log = super.format(event);
        return String.format("[%s] %s", formattedDateTime + getSerialNum(), log);
    }
}
