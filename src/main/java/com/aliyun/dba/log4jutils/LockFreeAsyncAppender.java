package com.aliyun.dba.log4jutils;

import com.lmax.disruptor.*;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import org.apache.log4j.Appender;
import org.apache.log4j.AppenderSkeleton;
import org.apache.log4j.Level;
import org.apache.log4j.helpers.AppenderAttachableImpl;
import org.apache.log4j.helpers.LogLog;
import org.apache.log4j.spi.AppenderAttachable;
import org.apache.log4j.spi.Filter;
import org.apache.log4j.spi.LoggingEvent;

import java.util.Enumeration;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;


public class LockFreeAsyncAppender extends AppenderSkeleton
        implements AppenderAttachable {

    private static final String DISCARDED_MESSAGE = "The message is discarded because it is too long.";

    private volatile Disruptor<LoggingEventWrapper> disruptor;

    /**
     * The default buffer size is set to 8192 events.
     */
    public static final int DEFAULT_BUFFER_SIZE = 8192;

    /**
     * Buffer size.
     */
    private int bufferSize = DEFAULT_BUFFER_SIZE;

    /**
     * Nested appenders.
     * */
    AppenderAttachableImpl aai;

    /**
     * Nested appenders.
     */
    private final AppenderAttachableImpl appenders;

    /**
     * Should location info be included in dispatched messages.
     */
    private boolean locationInfo = false;

    /**
     * The max length of single message
     * */
    private int maxLength = 20480;

    /**
     * The min level with locationInfo, default is warn
     * */
    private int minLevelLocationInfo = Level.WARN.getSyslogEquivalent();

    private boolean isDisruptorStartFailed = false;

    /**
     * Create new instance.
     */
    public LockFreeAsyncAppender() {
        appenders = new AppenderAttachableImpl();
        aai = appenders;
    }

    private void startDisruptor() {
        if (disruptor != null) {
            return;
        }
        try {
            // 处理Event的thread的休眠策略
            WaitStrategy waitStrategy = new SleepingWaitStrategy();

            // 处理Event的thread
            ThreadFactory threadFactory =
                    new Log4jThreadFactory("AsyncLogger", true, Thread.NORM_PRIORITY) {
                        @Override
                        public Thread newThread(final Runnable r) {
                            final Thread result = super.newThread(r);
                            return result;
                        }
                    };

            // 处理Event的handler
            EventHandler<LoggingEventWrapper> handler = new EventHandler<LoggingEventWrapper>(){
                @Override
                public void onEvent(LoggingEventWrapper wrapper, long sequence, boolean endOfBatch)
                {
                    try {
                        appenders.appendLoopOnAppenders(wrapper.getEvent());
                    } catch (Exception e) {
                        // ignore
                    }

                }
            };

            // 生成Event
            EventFactory<LoggingEventWrapper> factory = new EventFactory<LoggingEventWrapper>() {
                @Override
                public LoggingEventWrapper newInstance() {
                    return new LoggingEventWrapper();
                }
            };

            disruptor = new Disruptor<>(
                    factory, bufferSize, threadFactory, ProducerType.MULTI, waitStrategy);

            if (disruptor == null) {
                // disruptor start failed.
                isDisruptorStartFailed = true;
                return;
            }

            disruptor.handleEventsWith(handler);

            disruptor.start();
        } catch (Exception e) {
            // disruptor start failed.
            isDisruptorStartFailed = true;
        }
    }

    public void addAppender(final Appender newAppender) {
        synchronized (appenders) {
            appenders.addAppender(newAppender);
        }

        if (!isDisruptorStartFailed && disruptor == null) {
            startDisruptor();
        }
    }

    public void doAppend(LoggingEvent event) {
        if (closed) {
            LogLog.error("Attempted to append to closed appender named ["+name+"].");
            return;
        }

        if(!isAsSevereAsThreshold(event.getLevel())) {
            return;
        }

        Filter f = this.headFilter;

        FILTER_LOOP:
        while(f != null) {
            switch(f.decide(event)) {
                case Filter.DENY: return;
                case Filter.ACCEPT: break FILTER_LOOP;
                case Filter.NEUTRAL: f = f.getNext();
            }
        }

        LoggingEvent loggingEvent = event;
        if (event.getMessage() instanceof String && ((String) event.getMessage()).length() > maxLength) {
            loggingEvent = new LoggingEvent(
                    event.getFQNOfLoggerClass(),
                    event.getLogger(),
                    event.getTimeStamp(),
                    event.getLevel(),
                    DISCARDED_MESSAGE,
                    event.getThrowableInformation() != null ? event.getThrowableInformation().getThrowable() : null);
        }


        if (!isDisruptorStartFailed) {
            this.append(loggingEvent);
        } else if (loggingEvent.getLevel().getSyslogEquivalent() < Level.INFO.getSyslogEquivalent()) {
            // Disruptor start failed, sync log warn\error message.
            appenders.appendLoopOnAppenders(event);
        }

    }

    public void append(final LoggingEvent event) {
        Long sequence = null;
        try {
            sequence = disruptor.getRingBuffer().tryNext();
            LoggingEventWrapper wrapper = disruptor.get(sequence);
            // Set the NDC and thread name for the calling thread as these
            // LoggingEvent fields were not set at event creation time.
            event.getNDC();
            event.getThreadName();
            // Get a copy of this thread's MDC.
            event.getMDCCopy();

            if (locationInfo || event.getLevel().getSyslogEquivalent() <= getMinLevelLocationInfo()) {
                event.getLocationInformation();
            }
            event.getRenderedMessage();
            event.getThrowableStrRep();
            wrapper.setEvent(event);
        } catch (InsufficientCapacityException e) {
            // Oop Buffer full
        } catch (Exception e) {
            // unknown exception
        } finally {
            if (sequence != null) {
                disruptor.getRingBuffer().publish(sequence);
            }
        }
    }

    public void close() {
        closed = true;
        try {
            disruptor.shutdown(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            // ignore
        }

        //
        //    close all attached appenders.
        //
        synchronized (appenders) {
            Enumeration iter = appenders.getAllAppenders();

            if (iter != null) {
                while (iter.hasMoreElements()) {
                    Object next = iter.nextElement();

                    if (next instanceof Appender) {
                        ((Appender) next).close();
                    }
                }
            }
        }
    }


    public Enumeration getAllAppenders() {
        synchronized (appenders) {
            return appenders.getAllAppenders();
        }
    }


    public Appender getAppender(final String name) {
        synchronized (appenders) {
            return appenders.getAppender(name);
        }
    }


    public boolean isAttached(final Appender appender) {
        synchronized (appenders) {
            return appenders.isAttached(appender);
        }
    }

    /**
     * {@inheritDoc}
     */
    public boolean requiresLayout() {
        return false;
    }

    /**
     * Removes and closes all attached appenders.
     */
    public void removeAllAppenders() {
        synchronized (appenders) {
            appenders.removeAllAppenders();
        }
    }

    /**
     * Removes an appender.
     * @param appender appender to remove.
     */
    public void removeAppender(final Appender appender) {
        synchronized (appenders) {
            appenders.removeAppender(appender);
        }
    }

    /**
     * Remove appender by name.
     * @param name name.
     */
    public void removeAppender(final String name) {
        synchronized (appenders) {
            appenders.removeAppender(name);
        }
    }


    public boolean getLocationInfo() {
        return locationInfo;
    }

    public void setLocationInfo(final boolean flag) {
        locationInfo = flag;
    }

    public void setBufferSize(final int size) {
        if (size < 0) {
            throw new NegativeArraySizeException("size");
        }
        bufferSize = (size < 1) ? 1 : size;
    }

    public int getBufferSize() {
        return bufferSize;
    }

    public int getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    public int getMinLevelLocationInfo() {
        return minLevelLocationInfo;
    }

    public void setMinLevelLocationInfo(int minLevelLocationInfo) {
        this.minLevelLocationInfo = minLevelLocationInfo;
    }

    private class LoggingEventWrapper {
        private LoggingEvent event;

        public void setEvent(LoggingEvent event) {
            this.event = event;
        }

        public LoggingEvent getEvent() {
            return this.event;
        }
    }
}
