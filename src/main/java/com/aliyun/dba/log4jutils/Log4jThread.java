package com.aliyun.dba.log4jutils;

/**
 * Prefixes thread names with {@code "Log4j-"}.
 */
public class Log4jThread extends Thread {

    static final String PREFIX = "Log4j-";
    private static String toThreadName(final Object name) {
        return PREFIX + name;
    }

    public Log4jThread(final ThreadGroup group, final Runnable target, final String name, final long stackSize) {
        super(group, target, toThreadName(name), stackSize);
    }

}
