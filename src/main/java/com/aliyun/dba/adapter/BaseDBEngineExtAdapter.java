package com.aliyun.dba.adapter;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.log.RdsExtLogUtils;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.concurrentlimit.ConcurrentRule;
import com.aliyun.dba.concurrentlimit.KeyBasedConcurrentLimiter;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.service.CheckWhenBlueGreenDeployment;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.annocation.DubboProviderAnnotation;
import com.aliyun.dba.support.common.factory.ActionImplFactory;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.concurrentlimit.KeyBasedConcurrentLimiter.RULE_INTERFACE;
import static com.aliyun.dba.concurrentlimit.KeyBasedConcurrentLimiter.RULE_USER;
import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.IS_RDS_CUSTOM;
import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.RDS_CUSTOM_ENV;

public abstract class BaseDBEngineExtAdapter implements DBEngineExtAdapter {

    private static final String APP_NAME = "rdsapi-ext-mysql";

    private static final Logger logger = Logger.getLogger(BaseDBEngineExtAdapter.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private ResourceService resourceService;

    @Autowired
    private InstanceIDao instanceIDao;
    @Autowired
    private CustinsIDao custinsIDao;

    @Autowired
    private CheckWhenBlueGreenDeployment checkWhenBlueGreenDeployment;

    // Resource缓存
    private static final Cache<String, String> resourceCache = CacheBuilder.newBuilder()
            .maximumSize(10)
            .expireAfterWrite(3, TimeUnit.MINUTES)
            .build();

    // 接口维度并发控制，限制每个Action并发执行不超过50个
    public static final KeyBasedConcurrentLimiter keyBasedConcurrentLimiter
            = new KeyBasedConcurrentLimiter(APP_NAME);

    private static final String RESOURCE_KEY_CONCURRENT_LIMITER = "concurrent_limiter_rdsapi_ext_mysql";

    /**
     * @param custins      CustInstanceDO
     * @param actionParams the parameters come from rdsapi
     * @return ervey action returns the result
     */
    public Map<String, Object> getActionResult(CustInstanceDO custins, Map<String, String> actionParams) {
        Map<String, Object> result = new HashMap<>();
        List<ConcurrentRule> concurrentRules = null;
        try {
            RequestSession.init(APP_NAME, actionParams);
            logger.info("Request: " + actionParams);
            String action = CustinsParamSupport.getAction(actionParams);

            RdsExtLogUtils.doBefore(actionParams);

            //获得子类对应的环境类型
            String environmentType = getEnvType(actionParams);

            // 检查实例状态是否支持当前action
            preCheckDbStatusAndAction(environmentType, action, actionParams);

            // 并发控制，避免单个Action或用户打满线程池
            concurrentRules = getConcurrentRules(environmentType, action, actionParams);
            boolean isAcquire = keyBasedConcurrentLimiter.tryAcquire(concurrentRules);
            if (!isAcquire) {
                if (isConcurrentLimit()) {
                    logger.error("The request processing has been concurrent limit.");
                    Map<String, Object> response = new HashMap<>();
                    response.put("errorCode", new Object[]{400, "ConcurrentLimit", "The request processing has been concurrent limit."});
                    return response;
                } else {
                    logger.warn("The request processing should be concurrent limit.");
                }
            }

            ActionImplFactory actionImplFactory = SpringContextUtil.getBeanByClass(ActionImplFactory.class);
            //每个请求创建一个对象，防止并发问题
            IAction actionImpl = actionImplFactory.getPrototypeActionImplObject(environmentType, action);

            // 杭州金融云，进入茅台后统一替换成cn-hangzhou-finance
            if (this instanceof MysqlPodDefaultDBEgngineExtAdapter || this instanceof MysqlDBEngineExtAdapter) {
                mysqlParamSupport.mockHangZhouFinanceRegion(actionParams);
            }

            // 存在蓝绿部署时需要禁止掉的操作
            if (!StringUtils.startsWithIgnoreCase(action, "Describe")) {
                checkWhenBlueGreenDeployment.check(actionParams);
            }

            //请求处理
            logger.warn("action :" + action + " for environmentType: " + environmentType + " deal with:" + actionImpl);
            result = actionImpl.doActionRequest(custins, actionParams);

            //记录返回日志
            RdsExtLogUtils.doAfterReturn(actionParams, result);

        } catch (RdsException ex) {
            logger.error("getActionResult exception: ", ex);
            RdsExtLogUtils.doAfterThrowing(actionParams, ex);
            result = createErrorResponse(ex.getErrorCode());
        } catch (Exception ex){
            logger.error("getActionResult error: ", ex);
            RdsExtLogUtils.doAfterThrowing(actionParams, ex);
            result = createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            logger.info("Response: " + JSON.toJSONString(result));
            // 释放信号量
            if (!CollectionUtils.isEmpty(concurrentRules)) {
                keyBasedConcurrentLimiter.release(concurrentRules);
            }


            RequestSession.remove();

        }

        return result;
    }

    /**
     * 构建限流规则
     * */
    private List<ConcurrentRule> getConcurrentRules(String environmentType, String action, Map<String, String> actionParams) {
        List<ConcurrentRule> concurrentRules = new ArrayList<>();

        // 每个接口最大50并发
        ConcurrentRule interfaceRule = ConcurrentRule.builder()
                .key(getActionKey(action, environmentType))
                .maxPermits(50)
                .tag(RULE_INTERFACE).build();
        concurrentRules.add(interfaceRule);

        String uid = actionParams.getOrDefault(ParamConstants.UID.toLowerCase(), "");
        if (StringUtils.isNotBlank(uid)) {
            // 每个用户最大30并发
            ConcurrentRule userRule = ConcurrentRule.builder()
                    .key(uid)
                    .maxPermits(30)
                    .tag(RULE_USER).build();
            concurrentRules.add(userRule);
        }
        return concurrentRules;
   }

   /**
    * 限流开关
    * */
   private boolean isConcurrentLimit() {
       try {
           String grayValue = resourceCache.get(RESOURCE_KEY_CONCURRENT_LIMITER, new Callable<String>() {
               @Override
               public String call() throws Exception {
                   ResourceDO resource = resourceService.getResourceByResKey(RESOURCE_KEY_CONCURRENT_LIMITER);
                   if (resource == null) {
                       return "false";
                   }
                   return resource.getRealValue();
               }
           });
           return "true".equalsIgnoreCase(grayValue);
       } catch (Exception e) {
           logger.error("isConcurrentLimit failed.", e);
           return false;
       }
   }

    /**
     * get cache key by action and environment type
     *
     * @param action          request action
     * @param environmentType the environment type
     * @return return environmentType+"_"+action
     */
    public String getActionKey(String action, String environmentType) {

        return environmentType + "_" + action;
    }


    public String getEnvType(Map<String, String> actionParams) throws ApiException {
        String isServerless = mysqlParamSupport.getParameterValue(actionParams, ServerlessConstant.SERVERLESS_CATEGORY,"0");
        String isRdsCustom = mysqlParamSupport.getParameterValue(actionParams, IS_RDS_CUSTOM,"false");
        String action = CustinsParamSupport.getAction(actionParams);
        // if rds custom, use rds_custom envType
        if (Boolean.parseBoolean(isRdsCustom)) {
            return RDS_CUSTOM_ENV;
        }

        // First check action，if not the special action，then use old-logic
        if (!ServerlessConstant.serverlessSpecialAction.contains(action.toLowerCase())){
            return this.getClass().getAnnotation(DubboProviderAnnotation.class).kindCode();
        }

        // judge if transmit the IS_SERVERLESS from YAOCHI
        logger.info("getEnvType: isServerless: " + isServerless + " action: " + action);
        if(isServerless.equals(ServerlessConstant.IS_SERVERLESS) && ServerlessConstant.serverlessSpecialAction.contains(action.toLowerCase())){
            return ServerlessConstant.SERVERLESS_ENV;
        }

        // Judge if serverless from instance_level by engine/engine_version/db_instance_class
        String engine = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.ENGINE);
        String engineVersion = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.ENGINE_VERSION);
        String dbInstanceClass = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_CLASS);
        String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID,ServerlessConstant.SERVERLESS_DEFAULT_REQUESTID);
        if(engine != null && engineVersion != null && dbInstanceClass != null){
            InstanceLevelDO instanceLevel = instanceIDao.getInstanceLevelByClassCode(dbInstanceClass,engine,engineVersion,null,null);

            if(instanceLevel != null
                    && ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(Objects.requireNonNull(instanceLevel.getCategory()).toLowerCase())
                    && ServerlessConstant.serverlessSpecialAction.contains(action.toLowerCase())) {

                logger.info(requestId + "getEnvType: make sure isServerless by instanceLevel;" + " category is: " + instanceLevel.getCategory());
                return ServerlessConstant.SERVERLESS_ENV;
            }
        }


        // Use dbinstanceName'category  to judge if serverlessAction;
        String dbInstanceName = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_NAME);
        if(dbInstanceName != null){
            CustInstanceDO custInsDO = custinsIDao.getCustInstanceByInsName(null,dbInstanceName);

            // if not exist the special instance ,then return old-logic
            if(custInsDO == null){
                return this.getClass().getAnnotation(DubboProviderAnnotation.class).kindCode();
            }

            InstanceLevelDO instanceLevelDO = instanceIDao.getInstanceLevelByLevelId(custInsDO.getLevelId());
            if (instanceLevelDO != null
                    && ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(instanceLevelDO.getCategory().toLowerCase())
                    && ServerlessConstant.serverlessSpecialAction.contains(action.toLowerCase())){

                logger.info(requestId + "getEnvType: make sure isServerless by instanceLevel ,category result is:" + instanceLevelDO.getCategory());
                return ServerlessConstant.SERVERLESS_ENV;
            }
        }

        //Miss condition of serverless, then old-logic
        return this.getClass().getAnnotation(DubboProviderAnnotation.class).kindCode();
    }

    protected void preCheckDbStatusAndAction(String environmentType, String action, Map<String, String> actionParams) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID,ServerlessConstant.SERVERLESS_DEFAULT_REQUESTID);

        if (!StringUtils.equalsIgnoreCase(environmentType, "podDefault")) {
            return;
        }

        String dbInstanceName = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_NAME);
        if(dbInstanceName == null) {
            return;
        }

        CustInstanceDO custInsDO = custinsIDao.getCustInstanceByInsName(null,dbInstanceName);
        if(custInsDO == null) {
            return;
        }

        List<String> actionsStoppedDbCanDo = Arrays.asList(
                "StartDBInstance",
                "StopDBInstance",
                "DeleteDBInstance",
                "LockDBInstance"
        );

        // 暂停中实例仅支持上述操作
        if ((CustinsState.STATE_STOPED.getState() == custInsDO.getStatus())
                && !actionsStoppedDbCanDo.contains(action)) {
            logger.info(requestId + " current db status is stopped, can't do this action");
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }
    }
}
