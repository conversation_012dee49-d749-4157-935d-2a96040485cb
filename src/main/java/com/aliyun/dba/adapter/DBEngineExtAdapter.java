package com.aliyun.dba.adapter;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.doctool.doc.anno.*;
import com.aliyun.dba.support.property.RdsException;

import java.io.UnsupportedEncodingException;
import java.util.Map;

@DocApi(version = "0.0.1")
public interface DBEngineExtAdapter {

    @DocResource(name = "CreateDBInstance", resourcePath = "docdir/CreateDBInstance.yaml")
    Map<String, Object> create(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException;

    Map<String, Object> createReadInstance(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException;

    Map<String, Object> exchange(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException;

    Map<String, Object> createDdr(CustInstanceDO custins, Map<String, String> params)
            throws RdsException, UnsupportedEncodingException;

    Map<String, Object> checkCreateDdr(CustInstanceDO custins, Map<String, String> params)
            throws RdsException, UnsupportedEncodingException;

    Map<String, Object> modify(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> migrateFailover(CustInstanceDO custins, Map<String, String> actionParams);

    //MigrateDBInstance也使用的原modify方法
    Map<String, Object> modifyForMigrate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> restore(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //恢复表到原实例
    Map<String, Object> restoreTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> restoreDdrTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> evaluate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    @DocAction(name = "UpgradeDBVersion")
    @DocParams(containBaseParams = true, params = {
            @DocParam(name = "DBInstanceName", necessary = true, paramMark = "实例ID"),
            @DocParam(name = "RegionId", valueExample = "cn-hangzhou", paramMark = "地域信息", supportVersions = "*"),
            @DocParam(name = "SwitchTimeMode", valueOption = "[0|1|2]", valueExample = "0", paramMark = "切换模式,0:立即切换,1:运维时间切换,2:指定时间切换"),
            @DocParam(name = "SwitchTime", valueExample = "19:00Z", paramMark = "切换时间,SwitchTimeMode=2时传递"),
            @DocParam(name = "TargetMinorVersion", valueExample = "rds_20200831", paramMark = "指定版本升级")
    })
    @DocErrorCodes(containBaseErrorCodes = true, errorCodes = {
            @DocErrorCode(httpStatusCode = 400, errorCode = "InvalidMinorVerison.NotFound", errorMessage = "Specify minor version not founud."),
            @DocErrorCode(httpStatusCode = 400, errorCode = "InstanceMissingMinorVersionAttr", errorMessage = "Specify instance has no minor_version attribute."),
            @DocErrorCode(httpStatusCode = 400, errorCode = "InvalidMinorVersionLowerThanInstance", errorMessage = "Specify minor version cannot not be lower than current instance."),
            @DocErrorCode(httpStatusCode = 400, errorCode = "InvalidMinorVersionAlreadyLatest", errorMessage = "Minor version for instance is already the latest version."),
    })
    Map<String, Object> upgradeDbVersion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;



    Map<String, Object> switchCustinsIsolateMode(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> evaluateUpgrade(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> modifySSL(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeSSL(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> clone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> evaluateBakSet(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> checkCanUpgrade(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> cloneForSecurity(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeKernelReleaseNote(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> modifySQLdelay(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> rebuild(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //删除实例
    Map<String, Object> delete(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //销毁实例
    Map<String, Object> destroy(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //查看实例故障信息,只有mysql 使用该接口
    Map<String, Object> failuredetail(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //重启实例
    Map<String, Object> restart(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //修改实例读写属性,physical,redis和mongo三种
    Map<String, Object> modifyDBInstanceRWType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    //刷新aurora_proxy账号
    Map<String, Object> flushAPAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //通过实例和目标可用区判断用户是否可以迁移到该可用区, 仅对mysql5.6主实例生效
    Map<String, Object> checkDBInstanceAcrossRegion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    //静默迁移
    Map<String, Object> silentMigrate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //重置root密码，目前仅支持mysql（乔云）
    Map<String, Object> resetRootPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //修改实例链路类型，实例进行链路切换时，只读实例等会一并进行切换
    Map<String, Object> switchConnType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //查询mysql实例备库延迟
    Map<String, Object> getSlaveLag(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //备库重搭
    Map<String, Object> rebuildSlaveInstance(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    //mysql实例机器执行自定义命令
    Map<String, Object> execCommand(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> attachNetworkInterface(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> createAdminAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> createAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> deleteAccount(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException;

    default Map<String, Object> describeAccountList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    Map<String, Object> stopDBInstance(CustInstanceDO custins, Map<String, String> actionParms) throws RdsException;

    Map<String, Object> startDBInstance(CustInstanceDO custins, Map<String, String> actionParms) throws RdsException;

    Map<String, Object> describeDBInstanceAllParameterList(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException;

    Map<String, Object> describeDBInstanceParameterChangeList(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException;

    Map<String, Object> describeDBInstanceParameterList(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException;

    Map<String, Object> describeParameterTemplateList(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> flushSysParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> modifyDBInstanceNoTemplateParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> modifyDBInstanceParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;


    Map<String, Object> describeTaskIdByRequestID(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> describeAnalyticdbByPrimaryDBInstance(CustInstanceDO custins,
                                                              Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> createDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    Map<String, Object> deleteDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException;

    default Map<String, Object> describeDBInstanceHostInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> switchDBInstanceHA(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }


    default Map<String, Object> createGlobalDatabaseNetwork(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> createGlobalActiveDatabase(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> deleteGlobalActiveDatabase(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> addGlobalActiveDatabaseUnitNode(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> listGlobalActiveDatabases(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> lockDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> unlockDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> resetAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> resetAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> resetAdminAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> modifyAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> modifyConnStr(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> deleteAdminAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    default Map<String, Object> refreshOssStsToken(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    default Map<String, Object> refreshGeneralEssdConf(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    default Map<String, Object> describeDBInstanceColdData(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> InvokeECSAction(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> TagRdsCustomHost(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> DescribeRCInstances(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> DescribeRCMetricList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }



    Map<String, Object> inspect(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    public Map<String, Object> modifyTaskRecoverTime(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeDBInstanceTopoInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> migrateConnectionToOtherZone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    //    Map<String, Object> create(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;
    Map<String, Object> changeDiskPerfLevel(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    default Map<String, Object> deleteDBInstanceLog(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    Map<String, Object> restartDBSlaveIns(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    default Map<String, Object> checkUserBakFile(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    default Map<String, Object> listUserBackupFileRecord(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    default Map<String, Object> deleteUserBackupFileRecord(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    default Map<String, Object> modifyUserBakFile(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    Map<String, Object> modifyDBInstanceVip(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeInstanceLevelList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> modifyDBInstanceAutoUpgradeMinorVersion(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeTaskProgressList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;


    default Map<String, Object> recoverDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException{
        return null;
    }

    Map<String, Object> describeTaskProgressInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    default Map<String, Object> describeDBList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> createDB(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyPrivilege(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    Map<String, Object> migratePengineToK8SInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    default Map<String, Object> createOperatorPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> deleteOperatorPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeOperatorPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> transferResourceRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyDBInstanceMaintainTime(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> checkServiceLinkedRoleForDeleting(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> upgradeDBInstanceNetwork(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyDBInstanceConnType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    //用户资源报备
    default Map<String, Object> customerApplyResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    //用户资源报备信息查询
    default Map<String, Object> describeApplyResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    //用户资源报备信息查询
    default Map<String, Object> describeZoneInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    //取消资源报备
    default Map<String, Object> cancelApplyResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> onlineResizeDBInstanceLogVolume(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> upgradeDbMajorVersion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyDBInstanceTDE(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeDBInstanceTDE(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> refreshKmsConfig(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyDBInstanceCLS(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeDBInstanceCLS(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyDBInstanceConfig(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyReverseNetForMigrateAcrossRegionViaVip(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> transferToDBInstanceCrossNetViaVip(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> isolateDBInstanceTmpfs(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> getLatestEngineImage(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> flushCustinsMaxConn(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyCustinsResourceConfig(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyIORequestTimes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> dispatchReplicaChecksum(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeDBInstanceEndpoints(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> createDBInstanceEndpoint(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyDBInstanceEndpoint(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyDBInstanceEndpointAddress(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> deleteDBInstanceEndpointAddress(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> createDBInstanceEndpointAddress(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> deleteDBInstanceEndpoint(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> createSecret(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> deleteSecret(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeSecrets(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> retrieveSecretValue(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    // 配置实例信息（例如：临时磁盘扩容）(暂时只开放给天宫图 rdseye)
    default Map<String, Object> custinsConfigure(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> createDBNodes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> deleteDBNodes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    // ************ RDS + CK 一站式接口 START *******************//
    Map<String, Object> createAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeAnalyticDBInstanceAttribute(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeAnalyticDBInstanceNetInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> modifyAnalyticDBInstanceClass(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> deleteAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> createAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> deleteAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> modifyAnalyticDBInstanceConnectionString(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeAnalyticDBInstanceDataSource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> createAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> deleteAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeAnalyticAccounts(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> describeSyncInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> modifyAnalyticAccountDescription(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> modifyAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> resetAnalyticAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> suspendAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> startAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> modifyAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    Map<String, Object> evaluateAnalyticRegionResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException;

    /**
     * 蓝绿部署
     */
    default Map<String, Object> createBlueGreenDeployment(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> switchBlueGreenInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> deleteBlueGreenDeployment(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> describeBlueGreenSyncInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> switchBlueGreenInstancePreCheck(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> switchHostinsPerfMeta(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyDBNode(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    // 刷新实例资源使用量
    default Map<String, Object> refreshAllocateResource(CustInstanceDO custInstanceDO, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    //防篡改表相关接口（6个）
    default Map<String, Object> modifyDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> createDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> listDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> deleteDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> signDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }
    default Map<String, Object> verifyDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeSupportOnlineResizeDisk(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeDetachedInsBackupInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> createMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> deleteMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> modifyAccountMaskingPrivilege(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    default Map<String, Object> describeAccountMaskingPrivilege(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new UnsupportedEncodingException();
    }

    /**
     * 大版本升级检查
     */
    default Map<String, Object> upgradeMajorVersionPreCheck(CustInstanceDO custins, Map<String, String> actionParms) throws RdsException {
        return null;
    }

    /**
     * 查询大版本预检查结果
     */
    default Map<String, Object> describeUpgradeMajorVersionResult(CustInstanceDO custins, Map<String, String> actionParms) throws RdsException {
        return null;
    }

    /**
     * 临时实例申请资源
     */
    default Map<String, Object> allocateResourceForTmpIns(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> modifyRCInstanceDescription(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> modifyRCInstanceKeyPair(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> syncRCKeyPair(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    /**
     * 内部接口小版本升级且刷参
     */
    default Map<String, Object> upgradeMinorVersionAndFlushParam(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> modifyDBInstanceConnectionDraining(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }


    default Map<String, Object> recoverNode(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> refreshInsStat(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    /**
     * 开启原生复制
     */
    default Map<String, Object> activateExternalReplication(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }
    /**
     * 关闭原生复制
     */
    default Map<String, Object> deactivateExternalReplication(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }
    /**
     * 查询原生复制
     */
    default Map<String, Object> describeExternalReplication(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }
    /**
     * 原生复制实例用oss备份重建
     */
    default Map<String, Object> rebuildExternalReplicationCustins(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }
    default Map<String, Object> repairDBInstanceParameter(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }
    /**
     * 查询实例压缩状态
     */
    default Map<String, Object> describeDBInstanceStorageCompression(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> exchangeReadOnlyInstanceToPrimary(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> migrateDBNodes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    default Map<String, Object> modifyDBInstanceReadOnlyStatus(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

}
