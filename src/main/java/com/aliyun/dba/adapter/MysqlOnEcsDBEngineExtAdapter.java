package com.aliyun.dba.adapter;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustInstance;
import com.aliyun.dba.support.common.annocation.ActionAnnotation;
import com.aliyun.dba.support.common.annocation.DubboProviderAnnotation;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@DubboProviderAnnotation(engine = "mysql", kindCode = "onecs")
@Service("mysqlOnEcsDBEngineExtAdapter")
public class MysqlOnEcsDBEngineExtAdapter extends BaseDBEngineExtAdapter {

    /**
     * 此处各个引擎需要独立实现引擎相关的EcsInsBuilder功能
     */
    @Override
    @ActionAnnotation(action = "CreateDBInstance")
    public Map<String, Object> create(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> createReadInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "ExchangeDBInstance")
    public Map<String, Object> exchange(CustInstanceDO custins, Map<String, String> params) {
        return this.getActionResult(custins, params);
    }

    @Override
    @ActionAnnotation(action = "CreateDdrDBInstance")
    public Map<String, Object> createDdr(CustInstanceDO custins, Map<String, String> params) throws RdsException, UnsupportedEncodingException {
        throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "CheckCreateDdrDBInstance")
    public Map<String, Object> checkCreateDdr(CustInstanceDO custins, Map<String, String> params) throws RdsException, UnsupportedEncodingException {
        throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceClass")
    public Map<String, Object> modify(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "MigrateDBInstance")
    public Map<String, Object> modifyForMigrate(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        //return null;
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RestoreDBInstance")
    public Map<String, Object> restore(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        //return this.getActionResult(custins, actionParams);
        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    /**
     * 恢复表到原实例
     */
    @Override
    @ActionAnnotation(action = "RestoreTable")
    public Map<String, Object> restoreTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "RestoreDdrTable")
    public Map<String, Object> restoreDdrTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "EvaluateRegionResource")
    public Map<String, Object> evaluate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "UpgradeDBVersion")
    public Map<String, Object> upgradeDbVersion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "SwitchCustinsIsolateMode")
    public Map<String, Object> switchCustinsIsolateMode(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "EvaluateModifyRegionResource")
    public Map<String, Object> evaluateUpgrade(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    /**
     * onecs不支持
     */
    @Override
    @ActionAnnotation(action = "ModifyDBInstanceSSL")
    public Map<String, Object> modifySSL(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceSSL")
    public Map<String, Object> describeSSL(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CloneDBInstance")
    public Map<String, Object> clone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> evaluateBakSet(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> checkCanUpgrade(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "CloneDBInstanceForSecurity")
    public Map<String, Object> cloneForSecurity(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeKernelReleaseNotes")
    public Map<String, Object> describeKernelReleaseNote(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    /**
     * ecs暂不支持
     */
    @Override
    @ActionAnnotation(action = "ModifySQLDelay")
    public Map<String, Object> modifySQLdelay(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    /**
     * ecs不支持
     */
    @Override
    @ActionAnnotation(action = "RebuildSlaveInstance")
    public Map<String, Object> rebuild(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "DeleteDBInstance")
    public Map<String, Object> delete(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DestroyDBInstance")
    public Map<String, Object> destroy(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceUnavailableRecords")
    public Map<String, Object> failuredetail(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RestartDBInstance")
    public Map<String, Object> restart(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceRWType")
    public Map<String, Object> modifyDBInstanceRWType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "FlushAuroraProxyAccount")
    public Map<String, Object> flushAPAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CheckDBInstanceAcrossRegion")
    public Map<String, Object> checkDBInstanceAcrossRegion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    /**
     * onecs不支持
     */
    @Override
    @ActionAnnotation(action = "SilentMigrate")
    public Map<String, Object> silentMigrate(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "ResetRootPassword")
    public Map<String, Object> resetRootPassword(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    /**
     * onecs不支持
     */
    @Override
    @ActionAnnotation(action = "ModifyDBInstanceConnType")
    public Map<String, Object> switchConnType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceSlaveLag")
    public Map<String, Object> getSlaveLag(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    /**
     * onecs不支持
     */
    @Override
    @ActionAnnotation(action = "RebuildSlaveInstance")
    public Map<String, Object> rebuildSlaveInstance(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "ExcuteDBInstanceCommand")
    public Map<String, Object> execCommand(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    //未迁移，不加注解
    @Override
    public Map<String, Object> attachNetworkInterface(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    //未迁移，不加注解
    @Override
    public Map<String, Object> createAdminAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    //未迁移，不加注解
    @Override
    public Map<String, Object> createAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> deleteAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "DescribeAccountList")
    public Map<String, Object> describeAccountList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    // on ecs 不支持停机实例
    @Override
    public Map<String, Object> stopDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> startDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> describeDBInstanceAllParameterList(CustInstanceDO custins,
                                                                  Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeDBInstanceParameterChangeList(CustInstanceDO custins,
                                                                     Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeDBInstanceParameterList(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeParameterTemplateList(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> flushSysParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceNoTemplateParameter(CustInstanceDO custins,
                                                                   Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    public Map<String, Object> describeTaskIdByRequestID(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> describeAnalyticdbByPrimaryDBInstance(CustInstanceDO custins,
                                                                     Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "CreateDBInstanceNetType")
    public Map<String, Object> createDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteDBInstanceNetType")
    public Map<String, Object> deleteDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "InspectDBInstance")
    public Map<String, Object> inspect(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> modifyTaskRecoverTime(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    public Map<String, Object> describeDBInstanceTopoInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> migrateConnectionToOtherZone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> changeDiskPerfLevel(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> restartDBSlaveIns(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceVip(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeInstanceLevelList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceAutoUpgradeMinorVersion(CustInstanceDO custins,
                                                                       Map<String, String> actionParams)
            throws RdsException {
        return null;
    }


    @Override
    public Map<String, Object> describeTaskProgressList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeTaskProgressInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    // pengine迁移新架构
    @Override
    public Map<String, Object> migratePengineToK8SInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    public Map<String, Object> migrateFailover(CustInstanceDO custins, Map<String, String> actionParams) {
        return null;
    }

    @Override
    public Map<String, Object> createAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceAttribute(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceNetInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceClass(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> createAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceConnectionString(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceDataSource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> createAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticAccounts(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeSyncInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticAccountDescription(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> resetAnalyticAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> suspendAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> startAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> evaluateAnalyticRegionResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    /**
     * 判断是否支持在线扩容
     */
    @Override
    @ActionAnnotation(action = "DescribeSupportOnlineResizeDisk")
    public Map<String, Object> describeSupportOnlineResizeDisk(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
}
