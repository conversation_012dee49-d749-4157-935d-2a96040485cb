package com.aliyun.dba.adapter;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.annocation.ActionAnnotation;
import com.aliyun.dba.support.common.annocation.DubboProviderAnnotation;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@DubboProviderAnnotation(engine = "mysql", kindCode = "poddefault")
@Service("mysqlPodDefaultDBEgngineExtAdapter")
public class MysqlPodDefaultDBEgngineExtAdapter extends BaseDBEngineExtAdapter {

    @Override
    @ActionAnnotation(action = "CreateDBInstance")
    public Map<String, Object> create(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceClass")
    public Map<String, Object> modify(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "MigrateFailover")
    public Map<String, Object> migrateFailover(CustInstanceDO custins, Map<String, String> actionParams) {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RebuildSlaveInstance")
    public Map<String, Object> rebuild(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteDBInstance")
    public Map<String, Object> delete(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RestartDBInstance")
    public Map<String, Object> restart(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceParameter")
    public Map<String, Object> modifyDBInstanceParameter(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "MigrateDBInstance")
    public Map<String, Object> modifyForMigrate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CreateReadDBInstance")
    public Map<String, Object> createReadInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceHostInfo")
    public Map<String, Object> describeDBInstanceHostInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "SwitchDBInstanceHA")
    public Map<String, Object> switchDBInstanceHA(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "UpgradeDBVersion")
    public Map<String, Object> upgradeDbVersion(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "UpgradeDBMajorVersion")
    public Map<String, Object> upgradeDbMajorVersion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "SwitchCustinsIsolateMode")
    public Map<String, Object> switchCustinsIsolateMode(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CreateGlobalDatabaseNetwork")
    public Map<String, Object> createGlobalDatabaseNetwork(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CreateGlobalActiveDatabase")
    public Map<String, Object> createGlobalActiveDatabase(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteGlobalActiveDatabase")
    public Map<String, Object> deleteGlobalActiveDatabase(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "AddGlobalActiveDatabaseUnitNode")
    public Map<String, Object> addGlobalActiveDatabaseUnitNode(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ListGlobalActiveDatabases")
    public Map<String, Object> listGlobalActiveDatabases(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    //  ----------------------------------- TODO 还未实现 --------------------------------


    @Override
    public Map<String, Object> exchange(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "CreateDdrDBInstance")
    public Map<String, Object> createDdr(CustInstanceDO custins, Map<String, String> params) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, params);
    }

    @Override
    @ActionAnnotation(action = "CheckCreateDdrDBInstance")
    public Map<String, Object> checkCreateDdr(CustInstanceDO custins, Map<String, String> params) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, params);
    }


    @Override
    public Map<String, Object> restore(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "RestoreTable")
    public Map<String, Object> restoreTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RefreshOssStsToken")
    public Map<String, Object> refreshOssStsToken(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RefreshGeneralEssdConf")
    public Map<String, Object> refreshGeneralEssdConf(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException{
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceColdData")
    public Map<String, Object> describeDBInstanceColdData(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> restoreDdrTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "EvaluateRegionResource")
    public Map<String, Object> evaluate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }


    @Override
    @ActionAnnotation(action = "EvaluateModifyRegionResource")
    public Map<String, Object> evaluateUpgrade(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceSSL")
    public Map<String, Object> modifySSL(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceSSL")
    public Map<String, Object> describeSSL(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CloneDBInstance")
    public Map<String, Object> clone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> evaluateBakSet(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> checkCanUpgrade(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> cloneForSecurity(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeKernelReleaseNote(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "ModifySQLDelay")
    public Map<String, Object> modifySQLdelay(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DestroyDBInstance")
    public Map<String, Object> destroy(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> failuredetail(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceRWType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> flushAPAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> checkDBInstanceAcrossRegion(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> silentMigrate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> resetRootPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> switchConnType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> getSlaveLag(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> rebuildSlaveInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> execCommand(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> attachNetworkInterface(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @ActionAnnotation(action = "CreateAdminAccount")
    @Override
    public Map<String, Object> createAdminAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }


    @ActionAnnotation(action = "CreateAccount")
    @Override
    public Map<String, Object> createAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "StopDBInstance")
    @Override
    public Map<String, Object> stopDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "StartDBInstance")
    @Override
    public Map<String, Object> startDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> describeDBInstanceAllParameterList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeDBInstanceParameterChangeList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeDBInstanceParameterList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeParameterTemplateList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> flushSysParameter(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceNoTemplateParameter(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeTaskIdByRequestID(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticdbByPrimaryDBInstance(CustInstanceDO custins,
                                                                     Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "CreateDBInstanceNetType")
    public Map<String, Object> createDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceConnectionDraining")
    public Map<String, Object> modifyDBInstanceConnectionDraining(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteDBInstanceNetType")
    public Map<String, Object> deleteDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "LockDBInstance")
    public Map<String, Object> lockDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "UnlockDBInstance")
    public Map<String, Object> unlockDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ResetAccount")
    public Map<String, Object> resetAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ResetAccountPassword")
    public Map<String, Object> resetAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ResetAdminAccountPassword")
    public Map<String, Object> resetAdminAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyAccountPassword")
    public Map<String, Object> modifyAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceConnectionString")
    public Map<String, Object> modifyConnStr(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteAccount")
    public Map<String, Object> deleteAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteAdminAccount")
    public Map<String, Object> deleteAdminAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "InspectDBInstance")
    public Map<String, Object> inspect(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        // TODO 需要实现了下游pengine任务流才能放开
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "ModifyTaskRecoverTime")
    public Map<String, Object> modifyTaskRecoverTime(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    public Map<String, Object> describeDBInstanceTopoInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> migrateConnectionToOtherZone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    /**
     * 给主机打标
     *
     * @param custins
     * @param actionParams
     * @return
     * @throws RdsException
     */
    @ActionAnnotation(action = "CreateNodeLabel")
    public Map<String, Object> createNodeLabel(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 删除主机标
     *
     * @param custins
     * @param actionParams
     * @return
     * @throws RdsException
     */
    @ActionAnnotation(action = "DeleteNodeLabel")
    public Map<String, Object> deleteNodeLabel(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ChangeDiskPerfLevel")
    public Map<String, Object> changeDiskPerfLevel(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteDBInstanceLog")
    public Map<String, Object> deleteDBInstanceLog(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> restartDBSlaveIns(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "CheckUserBakFile")
    public Map<String, Object> checkUserBakFile(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyUserBakFile")
    public Map<String, Object> modifyUserBakFile(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ListUserBackupFileRecord")
    public Map<String, Object> listUserBackupFileRecord(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteUserBackupFileRecord")
    public Map<String, Object> deleteUserBackupFileRecord(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceVip")
    public Map<String, Object> modifyDBInstanceVip(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> describeInstanceLevelList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceAutoUpgradeMinorVersion(CustInstanceDO custins,
                                                                       Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
//    @ActionAnnotation(action = "DescribeTaskProgressList")
    public Map<String, Object> describeTaskProgressList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeTaskProgressInfo")
    public Map<String, Object> describeTaskProgressInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeAccountList")
    public Map<String, Object> describeAccountList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }


    @Override
    @ActionAnnotation(action = "DescribeDBList")
    public Map<String, Object> describeDBList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CreateDB")
    public Map<String, Object> createDB(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyAccountPrivilege")
    public Map<String, Object> modifyPrivilege(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }


    // pengine迁移新架构
    @Override
    public Map<String, Object> migratePengineToK8SInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "RecoverDBInstance")
    public Map<String, Object> recoverDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CreateOperatorPermission")
    public Map<String, Object> createOperatorPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteOperatorPermission")
    public Map<String, Object> deleteOperatorPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeOperatorPermission")
    public Map<String, Object> describeOperatorPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "TransferResourceRequest")
    public Map<String, Object> transferResourceRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceMaintainTime")
    public Map<String, Object> modifyDBInstanceMaintainTime(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "UpgradeDBInstanceNetwork")
    public Map<String, Object> upgradeDBInstanceNetwork(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceConnType")
    public Map<String, Object> modifyDBInstanceConnType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "OnlineResizeDBInstanceLogVolume")
    public Map<String, Object> onlineResizeDBInstanceLogVolume(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }


    @Override
    @ActionAnnotation(action = "ModifyDBInstanceTDE")
    public Map<String, Object> modifyDBInstanceTDE(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceTDE")
    public Map<String, Object> describeDBInstanceTDE(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RefreshKmsConfig")
    public Map<String, Object> refreshKmsConfig(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceCLS")
    public Map<String, Object> modifyDBInstanceCLS(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceCLS")
    public Map<String, Object> describeDBInstanceCLS(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 修改实例配置项
     * 不要写@ActionAnnotation，因为很多已有方法实现是在rdsapi中
     */
    @Override
    public Map<String, Object> modifyDBInstanceConfig(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 修改资源模版
     */
    @ActionAnnotation(action = "ModifyRsScheduleTemplate")
    public Map<String, Object> modifyRsScheduleTemplate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 创建资源模版
     */
    @ActionAnnotation(action = "CreateRsScheduleTemplate")
    public Map<String, Object> createRsScheduleTemplate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 查询资源模版
     */
    @ActionAnnotation(action = "DescribeRsScheduleTemplate")
    public Map<String, Object> describeRsScheduleTemplate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 删除资源模版
     */
    @ActionAnnotation(action = "DeleteRsScheduleTemplate")
    public Map<String, Object> deleteRsScheduleTemplate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 设置最大链接数
     */
    @ActionAnnotation(action = "FlushCustinsMaxConn")
    public Map<String, Object> flushCustinsMaxConn(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
    }

    /**
     * 刷新资源配置
     */
    @ActionAnnotation(action = "ModifyDBInstanceResourceConfig")
    public Map<String, Object> modifyCustinsResourceConfig(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
    }

    /**
     * 修改IO请求次数
     */
    @ActionAnnotation(action = "ModifyIORequestTimes")
    public Map<String, Object> modifyIORequestTimes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
    }

    @ActionAnnotation(action = "DispatchReplicaChecksum")
    public Map<String, Object> dispatchReplicaChecksum(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "CreateDBInstanceEndpoint")
    public Map<String, Object> createDBInstanceEndpoint(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "ModifyDBInstanceEndpoint")
    public Map<String, Object> modifyDBInstanceEndpoint(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "ModifyDBInstanceEndpointAddress")
    public Map<String, Object> modifyDBInstanceEndpointAddress(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "CreateDBInstanceEndpointAddress")
    public Map<String, Object> createDBInstanceEndpointAddress(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DescribeDBInstanceEndpoints")
    public Map<String, Object> describeDBInstanceEndpoints(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "RefreshAllocateResource")
    public Map<String, Object> refreshAllocateResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DeleteDBInstanceEndpointAddress")
    public Map<String, Object> deleteDBInstanceEndpointAddress(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DeleteDBInstanceEndpoint")
    public Map<String, Object> deleteDBInstanceEndpoint(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }


    //****** cluster 接口  ******
    /**
     * 集群版新增节点
     */
    @Override
    @ActionAnnotation(action = "CreateDBNodes")
    public Map<String, Object> createDBNodes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 集群版删除节点
     */
    @Override
    @ActionAnnotation(action = "DeleteDBNodes")
    public Map<String, Object> deleteDBNodes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "CreateAnalyticDBInstance")
    @Override
    public Map<String, Object> createAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DescribeAnalyticDBInstanceAttribute")
    @Override
    public Map<String, Object> describeAnalyticDBInstanceAttribute(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DescribeAnalyticDBInstanceNetInfo")
    @Override
    public Map<String, Object> describeAnalyticDBInstanceNetInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "ModifyAnalyticDBInstanceClass")
    @Override
    public Map<String, Object> modifyAnalyticDBInstanceClass(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DeleteAnalyticDBInstance")
    @Override
    public Map<String, Object> deleteAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "CreateAnalyticDBInstanceNetType")
    @Override
    public Map<String, Object> createAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DeleteAnalyticDBInstanceNetType")
    @Override
    public Map<String, Object> deleteAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "ModifyAnalyticDBInstanceConnectionString")
    @Override
    public Map<String, Object> modifyAnalyticDBInstanceConnectionString(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceDataSource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> createAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> deleteAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> describeAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> describeAnalyticAccounts(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> describeSyncInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> modifyAnalyticAccountDescription(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> modifyAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> resetAnalyticAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> suspendAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> startAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "EvaluateAnalyticRegionResource")
    @Override
    public Map<String, Object> evaluateAnalyticRegionResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 创建蓝绿部署
     */
    @Override
    @ActionAnnotation(action = "CreateBlueGreenDeployment")
    public Map<String, Object> createBlueGreenDeployment(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 蓝绿实例切换
     */
    @Override
    @ActionAnnotation(action = "SwitchBlueGreenInstance")
    public Map<String, Object> switchBlueGreenInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 删除蓝绿部署
     */
    @Override
    @ActionAnnotation(action = "DeleteBlueGreenDeployment")
    public Map<String, Object> deleteBlueGreenDeployment(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 查询蓝绿实例同步信息
     */
    @Override
    @ActionAnnotation(action = "DescribeBlueGreenSyncInfo")
    public Map<String, Object> describeBlueGreenSyncInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 蓝绿实例切换预检查
     */
    @Override
    @ActionAnnotation(action = "SwitchBlueGreenInstancePreCheck")
    public Map<String, Object> switchBlueGreenInstancePreCheck(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 集群版节点变配
     */
    @Override
    @ActionAnnotation(action = "ModifyDBNode")
    public Map<String, Object> modifyDBNode(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 开启防篡改表功能
     */
    @Override
    @ActionAnnotation(action = "ModifyDBInstanceTrustTable")
    public Map<String, Object> modifyDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 创建防篡改表
     */
    @Override
    @ActionAnnotation(action = "CreateDBInstanceTrustTable")
    public Map<String, Object> createDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 删除防篡改表
     */
    @Override
    @ActionAnnotation(action = "DeleteDBInstanceTrustTable")
    public Map<String, Object> deleteDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 显示数据库中已有的防篡改表
     */
    @Override
    @ActionAnnotation(action = "ListDBInstanceTrustTable")
    public Map<String, Object> listDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 防篡改表定时签名
     */
    @Override
    @ActionAnnotation(action = "SignDBInstanceTrustTable")
    public Map<String, Object> signDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 校验防篡改表
     */
    @Override
    @ActionAnnotation(action = "VerifyDBInstanceTrustTable")
    public Map<String, Object> verifyDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 判断是否支持在线扩容
     */
    @Override
    @ActionAnnotation(action = "DescribeSupportOnlineResizeDisk")
    public Map<String, Object> describeSupportOnlineResizeDisk(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 创建全密态规则
     */
    @Override
    @ActionAnnotation(action = "CreateMaskingRules")
    public Map<String, Object> createMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 修改全密态规则
     */
    @Override
    @ActionAnnotation(action = "ModifyMaskingRules")
    public Map<String, Object> modifyMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 删除全密态规则
     */
    @Override
    @ActionAnnotation(action = "DeleteMaskingRules")
    public Map<String, Object> deleteMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 查询全密态规则
     */
    @Override
    @ActionAnnotation(action = "DescribeMaskingRules")
    public Map<String, Object> describeMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 修改用户全密态授权信息
     */
    @Override
    @ActionAnnotation(action = "ModifyAccountMaskingPrivilege")
    public Map<String, Object> modifyAccountMaskingPrivilege(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 查询用户全密态授权信息
     */
    @Override
    @ActionAnnotation(action = "DescribeAccountMaskingPrivilege")
    public Map<String, Object> describeAccountMaskingPrivilege(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 大版本升级预检查
     */
    @Override
    @ActionAnnotation(action = "UpgradeMajorVersionPreCheck")
    public Map<String, Object> upgradeMajorVersionPreCheck(CustInstanceDO custins, Map<String, String> actionParms) throws RdsException {
        return this.getActionResult(custins, actionParms);
    }

    /**
     * 大版本升级预检查结果
     */
    @Override
    @ActionAnnotation(action = "DescribeUpgradeMajorVersionResult")
    public Map<String, Object> describeUpgradeMajorVersionResult(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 临时实例申请资源
     */
    @Override
    @ActionAnnotation(action = "AllocateResourceForTmpIns")
    public Map<String, Object> allocateResourceForTmpIns(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RecoverNode")
    public Map<String, Object> recoverNode(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RefreshInsStat")
    public Map<String, Object> refreshInsStat(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }


    /**
     * 开启原生复制
     */
    @Override
    @ActionAnnotation(action = "ActivateExternalReplication")
    public Map<String, Object> activateExternalReplication(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 关闭原生复制
     */
    @Override
    @ActionAnnotation(action = "DeactivateExternalReplication")
    public Map<String, Object> deactivateExternalReplication(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 查询原生复制
     */
    @Override
    @ActionAnnotation(action = "DescribeExternalReplication")
    public Map<String, Object> describeExternalReplication(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 原生复制实例用oss备份重建
     */
    @Override
    @ActionAnnotation(action = "RebuildExternalReplicationCustins")
    public Map<String, Object> rebuildExternalReplicationCustins(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 查询实例压缩状态
     */
    @Override
    @ActionAnnotation(action = "DescribeDBInstanceStorageCompression")
    public Map<String, Object> describeDBInstanceStorageCompression(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "MigrateDBNodes")
    public Map<String, Object> migrateDBNodes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceReadOnlyStatus")
    public Map<String, Object> modifyDBInstanceReadOnlyStatus(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

}
