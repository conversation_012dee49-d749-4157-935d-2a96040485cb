package com.aliyun.dba.dockerdefault.general;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("generalDeleteService")
public class GeneralDeleteService {

    private static final LogAgent logger = LogFactory.getLogAgent(GeneralDeleteService.class);
    private static final String DESTROY_DB_INSTANCE_ACTION = "DestroyDBInstance";
    private static final String TASK_PARAM_IS_GENERAL_KEY = "is_general";
    private static final String TASK_PARAM_DELAY_DELETE_TIME_KEY = "delay_delete_time";

    @Autowired
    private TaskService taskService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    /**
     * 1. Physical readonly cust ins.
     * 2. Logical/Physical master cust ins.
     * <p>
     * condition: only one master in general logical cust instance
     */
    public Map<String, Object> handleDeleteRequest(CustInstanceDO custIns) throws RdsException {
        logger.info("CustIns={} is general, insType={}, character={}, begin to delete.",
                custIns.getId(), custIns.getInsType(), custIns.getCharacterType());

        if (isPhysicalReadonlyCustIns(custIns)) {
            return deleteReadonlyCustIns(custIns);
        } else if (isMasterCustIns(custIns)) {
            return deleteMasterCustIns(custIns);
        } else {
            logger.error("Fail to delete instance, invalid instance={}, insType={}, character={}.",
                    custIns.getId(), custIns.getInsType(), custIns.getCharacterType());
            throw new RdsException(ErrorCode.INVALID_INSTANCE);
        }
    }

    private Map<String, Object> deleteReadonlyCustIns(CustInstanceDO custIns) throws RdsException {
        updateCustInsDeleteStatus(custIns);
        return submitDeleteTask(custIns);
    }

    /**
     * Delete master custins with logical delete flow
     */
    private Map<String, Object> deleteMasterCustIns(CustInstanceDO custIns) throws RdsException {
        if (hasUndeletedReadonlyCustIns(custIns)) {
            return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
        CustInstanceDO logicalCustIns = getLogicalCustIns(custIns);
        mysqlParamSupport.checkMaxscaleStatus(logicalCustIns);
        CustInstanceDO physicalMasterCustIns = getMasterPhysicalCustIns(custIns);

        updateCustInsDeleteStatus(logicalCustIns);
        updateCustInsDeleteStatus(physicalMasterCustIns);

        return submitDeleteTask(logicalCustIns);
    }

    private boolean hasUndeletedReadonlyCustIns(CustInstanceDO custIns) {
        Integer logicalCustInsId = custIns.isLogic() ? custIns.getId() : custIns.getParentId();
        CustInstanceQuery query = new CustInstanceQuery();
        query.setStatusNotEqual(CustinsState.STATUS_DELETING);
        query.setParentId(logicalCustInsId);
        query.setCustInsType(CustInsType.CUST_INS_TYPE_READ);

        return custinsService.countCustIns(query) > 0;
    }

    private CustInstanceDO getLogicalCustIns(CustInstanceDO originalCustIns) {
        return originalCustIns.isLogic() ? originalCustIns :
                custinsService.getCustInstanceByCustinsId(originalCustIns.getParentId());
    }

    private CustInstanceDO getMasterPhysicalCustIns(CustInstanceDO originalCustIns) {
        if (originalCustIns.isLogic()) {
            CustInstanceDO result = custinsService.getCustInstanceByParentId(originalCustIns.getId()).stream()
                    .filter(ci -> RdsConstants.CUSTINS_INSTYPE_PRIMARY.equals(ci.getInsType()))
                    .findFirst().orElse(null);
            Assert.notNull(result, "Should have one master physical cust instance.");
            return result;
        } else {
            Assert.isTrue(CustinsSupport.CHARACTER_TYPE_PHYSICAL.equalsIgnoreCase(originalCustIns.getCharacterType()),
                    "Character type should be physical");
            return originalCustIns;
        }
    }

    private Map<String, Object> submitDeleteTask(CustInstanceDO custIns) throws RdsException {
        Integer delayTime = Integer.valueOf(
                mysqlParaHelper.getParameterValue(ParamConstants.DELETE_DELAY_TIME, "0"));

        // TaskQueueDO taskQueueDO = makeTaskQueue(custIns, delayTime);
        // taskService.createTaskQueue(taskQueueDO);
        Integer taskId = taskService.deleteCustInstanceAndTaskWithDelayTime(mysqlParaHelper.getAction(), custIns,
                mysqlParaHelper.getOperatorId(), delayTime);
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

        Map<String, Object> data = Maps.newHashMap();
        data.put("DBInstanceID", custIns.getId());
        data.put("DBInstanceName", custIns.getInsName());
        data.put("TaskId", taskId);
        return data;
    }

    private TaskQueueDO makeTaskQueue(CustInstanceDO custIns, Integer delayTIme) {
        String taskKey = TaskSupport.TASK_DOCKER_DELETE_CUSTINS;
        Map<String, Object> taskParamMap = Maps.newHashMap();
        taskParamMap.put(TASK_PARAM_IS_GENERAL_KEY, true);
        if (delayTIme > 0) {
            taskParamMap.put(TASK_PARAM_DELAY_DELETE_TIME_KEY, delayTIme);
        }
        String taskParamStr = JSON.toJSONString(taskParamMap);
        return new TaskQueueDO(mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(),
                custIns.getId(), TaskSupport.TASK_TYPE_CUSTINS, taskKey, taskParamStr, 0);
    }

    /**
     * 1. 若为主实例，需要保证logical + physical的状态均置为删除，只读实例由自身删除流程保证
     * 2. 若为只读实例，只需保证自己的physical置为删除
     */
    private void updateCustInsDeleteStatus(CustInstanceDO custIns) {
        if (isDestroyCustIns()) {
            custinsService.updateCustInstanceStatusByCustinsId(
                    custIns.getId(), CustinsSupport.CUSTINS_STATUS_DESTROYED, CustinsState.STATUS_DESTORYED.getComment());
        } else {
            custinsService.updateCustInstanceStatusByCustinsId(
                    custIns.getId(), CustinsSupport.CUSTINS_STATUS_DELETING, CustinsState.STATUS_DELETING.getComment());
        }
    }

    private boolean isPhysicalReadonlyCustIns(CustInstanceDO custIns) {
        return custIns.isPhysicalChild() && RdsConstants.CUSTINS_INSTYPE_READ.equals(custIns.getInsType());
    }

    private boolean isMasterCustIns(CustInstanceDO custIns) {
        return RdsConstants.CUSTINS_INSTYPE_PRIMARY.equals(custIns.getInsType());
    }

    private boolean isDestroyCustIns() {
        return DESTROY_DB_INSTANCE_ACTION.equalsIgnoreCase(mysqlParaHelper.getParameterValue(ParamConstants.ACTION));
    }
}
