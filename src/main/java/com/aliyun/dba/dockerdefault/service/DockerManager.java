package com.aliyun.dba.dockerdefault.service;

import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.DockerInsLevelParseConfig;
import com.aliyun.dba.docker.dataobject.DockerTaskInputParam;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.support.property.RdsException;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 类HbaseService.java的实现描述：HBase相关服务
 */
public interface DockerManager {

    /**
     * 创建HBase集群
     *
     * @param params the params
     * @return map
     * @throws RdsException      the rds exception
     * @throws DBCenterException the db center exception
     */
    public DockerTaskInputParam createDockerDbInstance(RequestParamsDO params)
            throws RdsException;

    public DockerTaskInputParam createDockerDBReadInstance(Map<String, String> actionParams,
                                                           RequestParamsDO params,
                                                           CustInstanceDO primaryCustins,
                                                           CustInstanceDO  parentCustins)
            throws RdsException;

    public DockerTaskInputParam createGeneralDockerDbInstanceNode(Map<String, String> actionParams,RequestParamsDO params, CustInstanceDO parentCustins, CustInstanceDO masterIns)
            throws RdsException;


    public ResourceContainer getDockerResContainerForRebuild(
        CustInstanceDO custins, CustInstanceDO tempCustins, String region,
        List<InstanceDO> instanceList, Integer srcInstanceId, Set<Integer> specifyHostIdSet
    ) throws RdsException;

    public Map<String, Object> disPatchDockerTask(DockerTaskInputParam dockerTaskParam, Boolean isClone) throws
        RdsException;

    public DockerInsLevelParseConfig parseDockerInsExtraInfo(String extraInfo) throws RdsException;

    public void bindHostInstanceRole(CustInstanceDO custins, Integer hostIdForMaster, Integer hostIdForSlave);
}
