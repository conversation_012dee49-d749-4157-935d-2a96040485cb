package com.aliyun.dba.dockerdefault.service;

import com.alicloud.apsaradb.resmanager.EcsResModel;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.ecs.idao.EcsUserInfoIDao;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.aliyun.dba.support.property.RdsConstants.*;
import static com.aliyun.dba.support.property.RdsConstants.rdsToEcsRoleArnPattern;

@Service("cloudSSDEncryptionService")
public class CloudSSDEncryptionService {

    private static final Logger logger = Logger.getLogger(CloudSSDEncryptionService.class);


    @Autowired
    private KmsService kmsService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    private EcsUserInfoIDao ecsUserInfoIDao;
    @Autowired
    private DockerCommonService dockerCommonService;

    /**
     * 检查 个人秘钥有效性
     * 如果有效
     *      标记秘钥tag
     *      维护 role arn
     *      维护 custins_param
     */
    public void checkByokKeyAvail(Integer characterCustinsId, String regionId, String roleArn, String kmsKeyId,
                                  String uid, Integer primaryCustinsId) throws RdsException {
        logger.info("byokCreateCloudDiskCustins" + characterCustinsId + ",uid=" + uid + ",keyId=" + kmsKeyId + ",roleArn=" + roleArn);

        try{
            kmsService.checkKeyIsAvailableForCreation(characterCustinsId, regionId, roleArn, kmsKeyId, uid, primaryCustinsId);
        }catch (Exception e1){
            logger.error("byokCheckCreationKeyFail, custins"+characterCustinsId+",roleArn=" + roleArn +",keyId=" + kmsKeyId+",uid="+uid, e1);
            throw new RdsException(ErrorCode.KMS_API_ERROR);
        }
    }

    /**
     * 检查 个人秘钥有效性
     * 如果有效，则标记秘钥tag & 维护 role arn
     */
    public void checkByokKeyAvail(String regionId, String roleArn, String kmsKeyId, String uid) throws RdsException {
        logger.info("checkByokKeyAvail" + ",uid=" + uid + ",keyId=" + kmsKeyId + ",roleArn=" + roleArn);

        try{
            kmsService.checkKeyIsAvailableForCreation(regionId, roleArn, kmsKeyId, uid);
        }catch (Exception e){
            logger.error("checkByokKeyAvail failed! " +",roleArn=" + roleArn +",keyId=" + kmsKeyId+",uid="+uid, e);
            throw new RdsException(ErrorCode.KMS_API_ERROR);
        }
    }

    /**
     * 获取 roleArn
     */
    private String getRoleArn(String defaultVal, String resKey){
        List<String> realValues = resourceService.getResourceRealValueList(resKey);
        if (realValues == null || realValues.isEmpty()) {
            return defaultVal;
        }
        return realValues.get(0);
    }

    /**
     * 获得 byok授权链
     */
    public List<EcsResModel.Arn> getByokList(String roleArn, String uid, String ecsAccount) throws RdsException {


        List<EcsResModel.Arn> roleArnList = new ArrayList<EcsResModel.Arn>(3);

        EcsUserInfoDO ecsUserInfo = ecsUserInfoIDao.getEcsUserInfo(ecsAccount);
        Long rdsProductUid = Long.parseLong(ecsUserInfo.getAliUid());
        String rdsToEcsRoleArn = String.format(rdsToEcsRoleArnPattern, rdsProductUid);
        String rdsServiceToProductRoleArn = getRoleArn(rdsServiceToProductDefaultVal, rdsServiceToProductResKey);

        EcsResModel.Arn rdsToEcs = new EcsResModel.Arn(rdsToEcsRoleArn, ROLETYPE_SERVICE, rdsProductUid);
        logger.warn("byokLink, rdsRes2ecs=" + rdsToEcsRoleArn + "," + ROLETYPE_SERVICE + "," + rdsProductUid);

        EcsResModel.Arn rdsServiceToRdsProduct = new EcsResModel.Arn(rdsServiceToProductRoleArn, ROLETYPE_USER, null);
        logger.warn("byokLink, rdsSer2Res=" + rdsServiceToProductRoleArn + "," + ROLETYPE_USER);

        EcsResModel.Arn userToRdsService = new EcsResModel.Arn(roleArn, ROLETYPE_SERVICE, Long.valueOf(uid));
        logger.warn("byokLink, user2rdsSer=" + roleArn + "," + ROLETYPE_SERVICE + "," + uid);

        roleArnList.add(rdsToEcs);
        roleArnList.add(rdsServiceToRdsProduct);
        roleArnList.add(userToRdsService);

        return roleArnList;
    }
}
