package com.aliyun.dba.dockerdefault.service;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.task.dataobject.TransListDO;

public interface DockerTransService {
    Integer transCustInstanceTaskOnEcs(String action, Integer operatorId, String taskKey,
                                       CustInstanceDO custins, TransListDO translist,
                                       String dbInstanceStatusDesc, String switchMode,
                                       String dbInstanceDiskStageType) throws Exception;

    Integer transCustInstanceTaskOnEcsAddTag(String action, Integer operatorId, String taskKey,
                                             CustInstanceDO custins, TransListDO translist,
                                             String dbInstanceStatusDesc, String switchMode,
                                             String dbInstanceDiskStageType,
                                             String taskTypeTag) throws Exception;
}
