package com.aliyun.dba.dockerdefault.service;

import com.alibaba.cobar.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsIpWhiteListDO;
import com.aliyun.dba.custins.dataobject.DockerInsLevelExtraInfo;
import com.aliyun.dba.custins.dataobject.DockerInsLevelParseConfig;
import com.aliyun.dba.custins.dataobject.EngineCompose;
import com.aliyun.dba.custins.dataobject.EngineService;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.docker.dataobject.DockerTaskInputParam;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.dataobject.EcsImageDO;
import com.aliyun.dba.ecs.service.AmbariService;
import com.aliyun.dba.ecs.service.EcsImageService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelRelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.service.MySQLGeneralService;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.support.utils.Validator;
import org.apache.commons.codec.binary.Base64;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Time;
import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.DEDICATED_HOST_GOURP_TYPE;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_MASTER;
import static com.aliyun.dba.instance.support.InstanceSupport.INSTANCE_ROLE_SLAVE;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.property.RdsConstants.*;

@Service("dockerManager")
public class DockerManagerImpl implements DockerManager {

    private static final Logger logger = Logger.getLogger(DockerManagerImpl.class);

    @Autowired
    protected EcsImageService ecsImageService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private DockerCustinsService dockerCustinsService;
    @Autowired
    private DbsService dbsService;
    @Autowired
    private ResApi resApi;
    @Autowired
    private EcsService ecsService;
    @Autowired
    private CustinsIDao custinsIDao;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private CloudSSDEncryptionService cloudSSDEncryptionService;
    @Autowired
    private AVZSupport avzSupport;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private IpWhiteListService ipWhiteListService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected HostService hostService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected HostIDao hostIDao;
    @Autowired
    private AmbariService ambariService;
    @Autowired
    private MySQLGeneralService mySQLGeneralService;

    public static final String COMPOSE_TAG_ALIOS = "alios";
    public static final String CUSTINS_PARAM_NAME_COMPOSE_TAG = "compose_tag";

    @Override
    public DockerTaskInputParam createDockerDbInstance(RequestParamsDO params)
            throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        List<CustInstanceDO> characterCustinsList = new ArrayList<>();
        DockerTaskInputParam dockerTaskInputParam = new DockerTaskInputParam();


        boolean success = false;
        try {

            // set user info
            custins.setUserId(params.getUserId());
            //hostType
            String hostType = params.getHostType();
            // set kind code, for on ecs must support hosttype
            if (hostType != null && hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_ECS);
            } else if (hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_POLARSTORE)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE);
            } else {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER);
            }
            custins.setAccountMode(params.getAccountMode());
            if (custins.isSuperAccountMode()) {
                custins.setMaxDbs(500);
                custins.setMaxAccounts(500);
            }
            // set engine info

            String engine = params.getEngine();
            String engineVersion = params.getEngineVersion();
            custins.setDbType(params.getDbType());
            custins.setDbVersion(engineVersion);
            // ip white list
            if (!params.getClone()) {
                Set<String> ipSet = params.getIpSet();
                String whitelistNetType = params.getWhitelistNetType();
                CustinsIpWhiteListDO custinsIpWhiteList = new CustinsIpWhiteListDO(null,
                        SupportUtils.getIpWhiteListStr(ipSet), CustinsIpWhiteListDO.DEFAULT_GROUP_NAME,
                        "", whitelistNetType);
                dockerTaskInputParam.setCustinsIpWhiteList(custinsIpWhiteList);

            }
            //todo: use params

            // update common properties
            updateCustinsCommonProperties(custins, params);

            // init task queue param of backup
            Map<String, Object> taskQueueParam = params.getTaskQueueParam();

            Integer netType = params.getNetType();
            custins.setNetType(netType);
            // TODO: not support  vip multiple port
            Integer port = CustinsValidator.getRealNumber(params.getPortStr());
            if (port < 0) {
                throw new RdsException(ErrorCode.INVALID_PORT);
            }

            // get external config for different db type
            Map<String, List<MycnfCustinstanceDO>> mycnfCustinstancesMap = params.getMycnfCustinstancesMap();

            // get region & cluster & host info
            AVZInfo avzInfo = params.getAvzInfo();
            if (avzInfo == null) {
                throw new RdsException(ErrorCode.INVALID_ACTION);
            }            
            if(!avzInfo.isValidForModify()){
                avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            }
            String region = avzInfo.getMainLocation();
            //String region = params.getRegion();
            String clusterName = params.getClusterName();
            Set<Integer> hostIdSet = new HashSet<>();
            if (StringUtils.isNotBlank(clusterName)) {  // 仅在输入集群名时HostId参数才有效
                hostIdSet = params.getHostIdSet();
            }

            // set instance level info
            String classCode = params.getClassCode();
            com.aliyun.dba.instance.dataobject.InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                    custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
            if (StringUtils.isBlank(classCode) || insLevel == null) {
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }

            custins.setLevelId(insLevel.getId());
            String storage = params.getStorage();
            if (Validator.isNotNull(storage)) {
                custins.setDiskSize(Long.valueOf(storage) * 1024L);
            }
            custins.setCharacterType(insLevel.getCharacterType());
            custins.setConnType(params.getConnType());
            custins.setRegionId(params.getRegionId());
            // create custins
            custinsService.createCustInstance(custins);
            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, CustinsSupport.CONTAINER_TYPE_DOCKER);
            resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));

            Integer bizType = params.getBizType();
            String composeTag = mysqlParaHelper.selectComposeTag(clusterName);
            EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
                    custins.getDbType(), custins.getDbVersion(), insLevel.getCategory(), composeTag);
            logger.info("createDockerDbInstance " + custins.getInsName() + "select compose Tag: " +  composeTag + " clusterName:" + clusterName);
            custinsParamService.setCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
            JSONObject jsonService = JSON.parseObject(engineCompose.getServices());

            if (custins.isLogic()) {
                // init node instance info
                List<InstanceLevelRelDO> instanceLevelRels =
                        instanceService.getInstanceLevelRelByParentLevelId(insLevel.getId(), null);
                if (instanceLevelRels.size() <= 0) {
                    logger.error("No instance level relation found for level id: " + insLevel.getId());
                    throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                }
                for (InstanceLevelRelDO rel : instanceLevelRels) {
                    com.aliyun.dba.instance.dataobject.InstanceLevelDO characterInsLevel = instanceService.getInstanceLevelByLevelId(
                            rel.getCharacterLevelId());
                    String extraInfo = null;
                    DockerInsLevelParseConfig config = null;
                    if (CustinsSupport.isRdsSrvDockerize( new Gson().fromJson(jsonService.getString(characterInsLevel.getDbType()), EngineService.class))) {
                        extraInfo = characterInsLevel.getExtraInfo();
                        config = parseDockerInsExtraInfo(extraInfo);
                        for (int i = 0; i < rel.getCharacterCustinsCount(); i++) {
                            CustInstanceDO characterCustins = custins.clone();
                            if (hostType != null && hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {
                                // for ecs min 20G
                                if (Validator.isNotNull(storage)) {
                                    if (Integer.parseInt(storage) != 0) {
                                        CustinsSupport.setDiskSize(characterCustins, bizType, storage, CustinsSupport.ECS_MIN_DISK_SIZE);
                                    } else {
                                        characterCustins.setDiskSize(0L);
                                    }
                                } else {
                                    characterCustins.setDiskSize(characterInsLevel.getDiskSize());
                                }
                                createDockerInstanceOnEcs(characterCustins, config, characterInsLevel,
                                        port, netType, i, custins.getId(), custins.getInsName(),
                                        resourceContainer, params, characterCustinsList);
                            } else {
                                createDockerInstanceOnNc(characterCustins, custins, storage, characterInsLevel,
                                        bizType, characterCustinsList, config, i, netType, port, hostType,
                                        resourceContainer, hostIdSet);
                            }
                            custinsParamService.setCustinsParam(characterCustins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
                        }
                    } else {
                        createRdsServiceForDocker(custins, config, characterInsLevel,
                                netType, resourceContainer, params, "rds");
                    }
                }


                // init logic custins resource model
                CustinsResModel custinsResModel = new CustinsResModel(
                        custins.getId());
                custinsResModel.setConnType(params.getConnType());

                // put into resource container
                resourceContainer.addCustinsResModel(custinsResModel);
            } else {
                CustinsResModel custinsResModel = new CustinsResModel(custins.getId());

                String extraInfo = insLevel.getExtraInfo();
                DockerInsLevelParseConfig config = parseDockerInsExtraInfo(extraInfo);
                if (config.getVipCount() > 0) {
                    String connType = CustinsSupport.CONN_TYPE_LVS;
                    custinsResModel.setConnType(connType);
                    for (int j = 0; j < config.getVipCount(); j++) {
                        String connAddrCust = mysqlParaHelper.getConnAddrCust(
                                custins.getInsName().replace("_", "-") + j,
                                custins.getRegionId(),
                                CustinsSupport.CONTAINER_TYPE_DOCKER);
                        VipResModel vipResModel = initVipResModel(
                                netType, connAddrCust, port, config.getVportCountPerVip(), connType);
                        custinsResModel.addVipResModel(vipResModel);
                    }
                } else {
                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
                }

                HostinsResModel hostinsResModel = new HostinsResModel(insLevel.getId());
                hostinsResModel.setHostType(insLevel.getHostType());
                hostinsResModel.setInsCount(config.getInsCount());
                hostinsResModel.setInsPortCount(config.getPortCountPerIns());
                hostinsResModel.setDiskType(config.getDiskType());

                DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                distributeRule.setSiteDistributeMode(config.getDistributePolicy().getSite());
                distributeRule.setCabinetDistributeMode(config.getDistributePolicy().getCabinet());
                distributeRule.setHostDistributeMode(config.getDistributePolicy().getHost());
                distributeRule.setSpecifyHostIdSet(hostIdSet);
                // set host res model
                custinsResModel.setHostinsResModel(hostinsResModel);
                // put into resource container
                resourceContainer.addCustinsResModel(custinsResModel);
            }

            Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
            if (!response.getCode().equals(200)) {
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            AllocateResRespModel.CustinsResRespModel custinsResRespModel = response.getData().getCustinsResRespModelList().get(0);

            avzSupport.updateAVZInfoByInstanceIds(avzInfo, custinsResRespModel.getInstanceIdList());
            custinsParamService.updateAVZInfo(custins.getId(),avzInfo);

            dockerTaskInputParam.setAction(params.getAction());
            dockerTaskInputParam.setCharacterCustinsList(characterCustinsList);
            dockerTaskInputParam.setCustIns(custins);
            dockerTaskInputParam.setHostType(hostType);
            dockerTaskInputParam.setTaskQueueParam(taskQueueParam);
            dockerTaskInputParam.setMycnfCustinstancesMap(mycnfCustinstancesMap);
            dockerTaskInputParam.setOperatorId(params.getOperatorId());
            success = true;

            return dockerTaskInputParam;

        } finally {
            if (!success) {
                if (custins.getId() != null) {
                    custinsService.deleteCustInstance(custins);
                }
                for (CustInstanceDO characterCustins : characterCustinsList) {
                    if (characterCustins.getId() != null) {
                        custinsService.deleteCustInstance(characterCustins);
                    }
                }
            }
        }

    }

    @Override
    public DockerTaskInputParam createDockerDBReadInstance(Map<String, String> actionParams, RequestParamsDO params,
                                                           CustInstanceDO primaryCustins, CustInstanceDO parentCustins) throws RdsException {
        boolean success = false;
        CustInstanceDO readins = new CustInstanceDO();
        try {
            DockerTaskInputParam dockerTaskInputParam = new DockerTaskInputParam();

            ClustersDO cluster= clusterService.getClusterByClusterName(primaryCustins.getClusterName());
            boolean isUserCluster = cluster.getType().equals(DEDICATED_HOST_GOURP_TYPE);

            //set cust_instance property
            String instype = params.getInsType();
            boolean createbackupreadins = (instype != null && instype.equals(CUSTINS_INSTYPE_READ_BACKUP.toString()));
            if (!createbackupreadins && custinsService.countReadCustInstanceByPrimaryCustinsId(primaryCustins.getId()) >=
                    ResourceSupport.getInstance().getIntegerRealValue(
                            ResourceKey.RESOURCE_CUSTINS_MYSQL_READINS_COUNT)) {//超过只读实例个数限制
                throw new RdsException(ErrorCode.READINSTANCE_EXCEEDED);
            }

            if (createbackupreadins) {
                readins.setInsType(CUSTINS_INSTYPE_READ_BACKUP);
            } else {
                readins.setInsType(CUSTINS_INSTYPE_READ);
            }
            readins.setUserId(parentCustins.getUserId());
            if(!StringUtil.isEmpty(params.getClusterName())) {
                readins.setClusterName(params.getClusterName());
            }
            readins.setDbType(primaryCustins.getDbType());
            readins.setDbVersion(primaryCustins.getDbVersion());
            readins.setType(primaryCustins.getType());
            readins.setCharacterType(CustinsSupport.CHARACTER_TYPE_PHYSICAL);
            readins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
            readins.setComment(params.getDesc());
            readins.setInsName(CheckUtils.checkValidForInsName(params.getReadInsName()));
            readins.setKindCode(parentCustins.getKindCode());
            // diskSize 单位MB
            Integer minDiskSize = primaryCustins.isCustinsOnDockerOnEcsLocalSSD() ? CustinsSupport.NC_MIN_DISK_SIZE : CustinsSupport.ECS_MIN_DISK_SIZE;
            readins.setDiskSize(CheckUtils.parseInt(params.getStorage(),  minDiskSize,
                            null, ErrorCode.INVALID_STORAGE)* 1024L);

            if (custinsService.hasCustInstanceByInsName(readins.getInsName())) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }

            if (null != params.getInstanceDesc()) {
                readins.setComment(CheckUtils
                        .checkLength(params.getInstanceDesc(), 1, 256,
                                ErrorCode.INVALID_DBINSTANCEDESCRIPTION));
            }
            if (null != params.getExpiredTime()) {
                readins.setGmtExpired(params.getExpiredTime());

            }
            readins.setMaintainStarttime(
                    Time.valueOf(DateSupport.timeWithSecond2str(params.getMaintainStartTime())));
            readins.setMaintainEndtime(
                    Time.valueOf(DateSupport.timeWithSecond2str(params.getMaintainEndTime())));

            // set instance level info
            String classCode = params.getClassCode();
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                    readins.getDbType(), readins.getDbVersion(), readins.getTypeChar(), CustinsSupport.CHARACTER_TYPE_PHYSICAL);
            if (StringUtils.isBlank(classCode) || insLevel == null) {
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            readins.setLevelId(insLevel.getId());



            if("general".equalsIgnoreCase(insLevel.getCategory())){
                //一主多从设置父实例，不设置主实例
                readins.setParentId(parentCustins.getId());
            }else{
                //Read ins only set primary ins id
                readins.setPrimaryCustinsId(primaryCustins.getId());
            }
            // to #********
            readins.setAccountMode(primaryCustins.getAccountMode());

            custinsService.createReadCustInstance(primaryCustins, readins);
            custinsParamService.setCustinsParam(readins.getId(),
                    CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID,
                    mysqlParamSupport.getParameterValue(actionParams, CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, ""));
            // read ins sync_mode should set to 0
            custinsParamService.setCustinsParam(readins.getId(),
                    CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE,
                    CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_ASYNC);
            String composeTag = mysqlParaHelper.selectComposeTag(cluster.getClusterName(), isUserCluster);
            custinsParamService.setCustinsParam(readins.getId(),
                    CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
            custinsParamService.setCustinsParam(readins.getId(),
                    "SwitchWeight", mysqlParamSupport.getParameterValue(actionParams, "SwitchWeight", "100"));

            Integer port = CustinsValidator.getRealNumber(params.getPortStr());
            if (port < 0) {
                throw new RdsException(ErrorCode.INVALID_PORT);
            }


            // get region & cluster & host info
            String region = params.getRegion();
            String zoneId = params.getZoneId();
            String regionId = params.getRegionId();

            ResourceContainer resourceContainer = null;
            // init resource container
            resourceContainer = new ResourceContainer(region, CustinsSupport.CONTAINER_TYPE_DOCKER);
            resourceContainer.setAccessId(mysqlParamSupport.getParameterValue(actionParams, "accessId"));
            resourceContainer.setOrderId(mysqlParamSupport.getParameterValue(actionParams, "orderId"));
            resourceContainer.setUserId(mysqlParaHelper.getAndCheckUserId());
            resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));

            // MyBase用户指定主机
            Integer dedicatedHostIdForMaster = null;
            Integer dedicatedHostIdForSlave = null;
            if (isUserCluster) {
                List<ZoneInfo> zoneInfoList = new ArrayList<>();
                String dedicatedHostNameForMaster = mysqlParaHelper.getParameterValue(ParamConstants.DedicatedHostNameForMaster,mysqlParaHelper.getParameterValue("TargetDedicatedHostIdForMaster"));
                if (StringUtils.isNotEmpty(dedicatedHostNameForMaster)) {
                    logger.info("DHG set master host " + dedicatedHostNameForMaster);
                    Map<String, Object> specifyHostInfo = hostIDao.getHostIdsByDedicatedHostName(dedicatedHostNameForMaster, cluster.getClusterName());
                    dedicatedHostIdForMaster = (Integer) specifyHostInfo.get("id");
                    String masterZoneId = hostIDao.getHostInfoParam(dedicatedHostIdForMaster, "ZoneId");
                    ZoneInfo zoneInfo = new ZoneInfo(masterZoneId, 0);
                    zoneInfo.setHostId(dedicatedHostIdForMaster);
                    zoneInfoList.add(zoneInfo);
                }
                String dedicatedHostNameForSlave = mysqlParaHelper.getParameterValue(ParamConstants.DedicatedHostNameForSlave,mysqlParaHelper.getParameterValue("TargetDedicatedHostIdForSlave"));
                if (StringUtils.isNotEmpty(dedicatedHostNameForSlave)) {
                    logger.info("DHG set slave host " + dedicatedHostNameForSlave);
                    Map<String, Object> slaveSpecifyHostInfo = hostIDao.getHostIdsByDedicatedHostName(dedicatedHostNameForSlave, cluster.getClusterName());
                    dedicatedHostIdForSlave = (Integer) slaveSpecifyHostInfo.get("id");
                    String slaveZoneId = hostIDao.getHostInfoParam(dedicatedHostIdForSlave, "ZoneId");
                    ZoneInfo zoneInfo = new ZoneInfo(slaveZoneId, 1);
                    zoneInfo.setHostId(dedicatedHostIdForSlave);
                    zoneInfoList.add(zoneInfo);
                }
                if (!zoneInfoList.isEmpty()) {
                    resourceContainer.setZoneInfoList(zoneInfoList);
                }
            }

            CustinsResModel custinsResModel = new CustinsResModel(readins.getId());
            String extraInfo = insLevel.getExtraInfo();
            DockerInsLevelParseConfig config = parseDockerInsExtraInfo(extraInfo);

            // OnECS实例备用只读不绑定vip, set back read ins conntype to lvs
            if (config.getVipCount() > 0 && ! createbackupreadins) {
                logger.error("check vip num");
                String connAddrCust = mysqlParaHelper.getConnAddrCust(
                        readins.getInsName(), regionId,
                        readins.getDbType());
                VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
                vipResModel.setVswitchId(params.getVswitchId());
                vipResModel.setVpcId(params.getUserVpcId());
                vipResModel.setUserVisible(1);
                vipResModel.setTunnelId(params.getTunnelId());
                vipResModel.setVip(params.getIpaddress());
                vipResModel.setVport(port);
                vipResModel.setZoneId(zoneId);
                vipResModel.setVpcInstanceId(params.getVpcInstanceId() != null ? params.getVpcInstanceId() : readins.getInsName());
                vipResModel.setConnAddrCust(connAddrCust);
                custinsResModel.addVipResModel(vipResModel);
                readins.setConnType(CustinsSupport.CONN_TYPE_LVS);
            } else {
                readins.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
            }

            custinsResModel.setConnType(readins.getConnType());
            custinsResModel.setClusterName(readins.getClusterName());
            if(parentCustins.isCustinsDockerOnEcsLocalSSD() || isUserCluster) {
                HostinsResModel hostinsResModel = new HostinsResModel(insLevel.getId());
                DockerInsLevelParseConfig dockerInsLevelParseConfig = custinsService.parseDockerInsExtraInfo(extraInfo);
                hostinsResModel.setInsCount(insLevel.getInsCount());
                hostinsResModel.setInsPortCount(dockerInsLevelParseConfig.getPortCountPerIns());
                hostinsResModel.setDiskType(dockerInsLevelParseConfig.getDiskType());
                hostinsResModel.setDiskSizeSold(readins.getDiskSize());
                // get host type of src instance use
                InstanceDO srcInstance = instanceService.getInstanceByCustinsId(primaryCustins.getId()).get(0);
                hostinsResModel.setHostType(srcInstance.getHostType());
                String label = hostService.getHostInfoParam(srcInstance.getHostId(), "label");
                hostinsResModel.setLabel(label);
                if (Validator.isNotNull(extraInfo)){
                    dockerInsLevelParseConfig = custinsService.parseDockerInsExtraInfo(extraInfo);
                    if (dockerInsLevelParseConfig.getCpuSet() != null && dockerInsLevelParseConfig.getCpuSet()) {
                        hostinsResModel.setCpuType(CPUType.CPU_SET);
                    }
                }
                String siteName = clusterService.getSiteNameByAvzone(zoneId);
                // DistributeRule
                DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
                distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER); // 大客户专享集群 只读实例 主机 强制打散
                distributeRule.setSiteDistributeMode(DistributeMode.FORCE_CROWD);
                distributeRule.setSpecifySiteNameSet(new HashSet<String>(){{add(siteName);}});
                Set<Integer> hostIds = new HashSet<>();
                Set<Integer> inferiorHostIdSets = new HashSet<>();
                List<CustInstanceDO> primaryCustIns = custinsService.getCustInstanceByParentId(parentCustins.getId());
                for(CustInstanceDO primaryCustInsPhysical:primaryCustIns){
                    List<InstanceDO> instances = instanceService.getInstanceByCustinsId(primaryCustInsPhysical.getId());
                    if(instances !=null) {
                        for (InstanceDO instance : instances) {
                            inferiorHostIdSets.add(instance.getHostId());
                        }
                    }
                }
                List<CustInstanceDO> readCustins = custinsService.getReadCustInstanceListByPrimaryCustinsId(primaryCustins.getId(), true);
                for(CustInstanceDO oneReadIns:readCustins){
                    List<InstanceDO> instances = instanceService.getInstanceByCustinsId(oneReadIns.getId());
                    if(instances !=null) {
                        for (InstanceDO instance : instances) {
                            inferiorHostIdSets.add(instance.getHostId());
                        }
                    }
                }

                distributeRule.setExcludeHostIdSet(hostIds);
                distributeRule.setInferiorHostIdSet(inferiorHostIdSets);
                custinsResModel.setHostinsResModel(hostinsResModel);
                resourceContainer.setSubDomain(params.getRegion());
                resourceContainer.setClusterName(readins.getClusterName());

            }else{
                // Get ecsAccount, vpcId, vSwitchId
                String ecsAccount = ecsService.getEcsAccount(readins.getUserId(), regionId);
                List<EcsImageDO> ecsImageDOList = ecsImageService.getEcsImageList(params.getRegionId(),
                        CustinsSupport.CONTAINER_TYPE_DOCKER, null, null, null, null, null);
                String imageId = ecsImageDOList.get(0).getImageId();
                String osPassword = SupportUtils.getRandomPasswdForEcs(15);
                dbsService.createEcsOsAccount(readins, osPassword);
                String ecsClass = insLevel.getEcsClassCode();
                EcsResModel ecsResModel = initSingleEcsResModule(imageId, params.getVswitchId(), params, osPassword,
                        insLevel.getInsCount(), regionId, zoneId, ecsClass, ecsAccount, config, insLevel.getId(), 0L);
                custinsResModel.setEcsResModel(ecsResModel);
            }
            resourceContainer.addCustinsResModel(custinsResModel);

            Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
            if (!response.getCode().equals(200)) {
//                super.createResourceRecord(readins.getClusterName(), readins.getInsName(),
//                        JSON.toJSONString(response));
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            if (StringUtils.isEmpty(readins.getClusterName())) {
                List<AllocateResRespModel.CustinsResRespModel> respList = response.getData().getCustinsResRespModelList();
                if (!respList.isEmpty()) {
                    readins.setClusterName(respList.get(0).getClusterName());
                }
            }

            if (isUserCluster) {
                bindHostInstanceRole(readins, dedicatedHostIdForMaster, dedicatedHostIdForSlave);
            }

            // init task queue param of backup
            Map<String, Object> taskQueueParam = params.getTaskQueueParam();
            if (taskQueueParam == null) {
                taskQueueParam = new HashMap<>();
            }
            taskQueueParam.put("isUserCluster", isUserCluster);
            String hostType = custinsService.getCustinsHostType(primaryCustins.getId());
            dockerTaskInputParam.setAction(params.getAction());
            dockerTaskInputParam.setCustIns(readins);
            dockerTaskInputParam.setHostType(hostType);
            dockerTaskInputParam.setTaskQueueParam(taskQueueParam);

            dbsService.syncUserAccountsforEcs(primaryCustins, readins);
            if (readins.isReadBackup()){
                logger.warn("mysql bakreadins_whitelist_127, custinsId="+readins.getId());
                CustinsIpWhiteListDO custinsIpWhiteList = new CustinsIpWhiteListDO(readins.getId(),
                        "127.0.0.1");
                ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
            }else {
                // sync all ip white list group
                ipWhiteListService.syncCustinsIpWhiteList(primaryCustins.getId(), readins.getId());
            }
            dockerTaskInputParam.setOperatorId(params.getOperatorId());
            success = true;

            // to #********
            if (StringUtils.isNotBlank(params.getDataDiskCategory())){
                custinsParamService.setCustinsParam(readins.getId(), CustinsParamSupport.DATADISK_STORAGE_TYPE, params.getDataDiskCategory());
            }

            return dockerTaskInputParam;

        } finally {
            if (!success) {
                if (readins.getId() != null) {
                    custinsService.deleteCustInstance(readins);
                }
            }
        }
    }


    public DockerInsLevelParseConfig parseDockerInsExtraInfo(String extraInfo) throws RdsException {

        DockerInsLevelParseConfig config = new DockerInsLevelParseConfig();
        try {
            DockerInsLevelExtraInfo info = new Gson().fromJson(extraInfo, DockerInsLevelExtraInfo.class);
            List<DockerInsLevelExtraInfo.PortInfo> portMapper = info.getPort_mapper();

            Integer portCountPerIns = 0;
            for (DockerInsLevelExtraInfo.PortInfo portInfo : portMapper) {
                portCountPerIns += portInfo.getNum() == null ? 1 : portInfo.getNum();
            }
            config.setPortCountPerIns(portCountPerIns);

            boolean containLinkPort = false;
            for (DockerInsLevelExtraInfo.PortInfo portInfo : portMapper) {
                if (portInfo.getLabels() == null) {
                    continue;
                }
                Set<String> labelSet = new HashSet<>(portInfo.getLabels());
                if (labelSet.contains(CustinsSupport.PORT_LABEL_LINK)) {
                    containLinkPort = true;
                    break;
                }
            }

            Integer insCount = 0;
            Integer linkPortCount = 0;
            List<DockerInsLevelExtraInfo.Topology> topologies = info.getTopology();
            for (DockerInsLevelExtraInfo.Topology topology : topologies) {
                insCount += topology.getNum();
                if (containLinkPort && topology.getNeed_vip()) {
                    linkPortCount += topology.getNum();
                }
            }
            config.setInsCount(insCount);

            Integer vipCount = 0;
            Integer vportCountPerVip = 0;
            if (linkPortCount > 0) {
                String linkMap = info.getLink_map();
                switch (linkMap) {
                    case CustinsSupport.LINK_MAP_1NN:
                        vipCount = 1;
                        vportCountPerVip = linkPortCount;
                        break;
                    case CustinsSupport.LINK_MAP_11N:
                        vipCount = 1;
                        vportCountPerVip = 1;
                        break;
                    case CustinsSupport.LINK_MAP_111:
                        vipCount = linkPortCount;
                        vportCountPerVip = 1;
                        break;
                    default:
                        String message = "Unknown link map: " + linkMap;
                        logger.error(message);
                        throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL_EXTRA_INFO, message);
                }
            }
            config.setVipCount(vipCount);
            config.setVportCountPerVip(vportCountPerVip);

            Integer diskType = CustinsSupport.getDiskType(info.getDisk());
            if (diskType == null) {
                String message = "Unknown disk type: " + info.getDisk();
                throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL_EXTRA_INFO, message);
            }
            config.setDiskType(diskType);

            if (info.getDistribute_policy() != null) {
                config.setDistributePolicy(info.getDistribute_policy());
            }
            config.setServiceType(info.getType());


        } catch (RdsException e) {
            throw e;
        } catch (Exception e) {
            String message = "Parse ins Level extra info failed: " + extraInfo + "\n" + e;
            logger.error(message, e);
            throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL_EXTRA_INFO, message);
        }
        return config;
    }

    /**
     * @param netType
     * @param connAddrCust
     * @param port
     * @param vportCount
     * @return
     */
    private VipResModel initVipResModel(Integer netType, String connAddrCust,
                                        Integer port, Integer vportCount, String connType) {
        VipResModel vipResModel = new VipResModel(netType);
        List<Integer> vportList = new ArrayList<>();
        for (int k = 0; k < vportCount; k++) {
            vportList.add(port + k);
        }
        // add in 3551. assert by chao.jiangch & jinqiu
        if (netType.equals(CustinsSupport.NET_TYPE_PUBLIC)
                && connType.equals(CustinsSupport.CONN_TYPE_LVS)) {
            vipResModel.setMode(NetMode.FNAT);
        }

        vipResModel.setVportList(vportList);
        vipResModel.setConnAddrCust(connAddrCust);
        return vipResModel;
    }

    /**
     * 设置实例公共属性
     *
     * @param custins
     * @throws RdsException
     */
    private void updateCustinsCommonProperties(CustInstanceDO custins, RequestParamsDO params) throws RdsException {
        // 设置实例名
        if (params.isE2dTask()){
            custins.setInsName(params.getDbBInstanceName());
        }else{
            custins.setInsName(CheckUtils.checkValidForInsName(params.getDbBInstanceName()));
        }

        if (custinsService.hasCustInstanceByInsName(custins.getInsName())) {
            throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
        }
        // 设置ProxyGroupId
        if (params.getProxyGroupId() != null && 0 != params.getProxyGroupId()) {
            custins.setProxyGroupId(params.getProxyGroupId());
        }

        // 设置实例描述
        if (null != params.getInstanceDesc()) {
            custins.setComment(CheckUtils
                    .checkLength(params.getInstanceDesc(), 1, 256,
                            ErrorCode.INVALID_DBINSTANCEDESCRIPTION));
        }

        //设置 e2d src insName
        if (null != params.getE2dsourceCustinsId()){
            custins.setParentId(params.getE2dsourceCustinsId());
        }

        // 设置实例过期时间
        if (null != params.getExpiredTime()) {
            custins.setGmtExpired(params.getExpiredTime());
        }

        custins.setMaintainStarttime(
                Time.valueOf(DateSupport.timeWithSecond2str(params.getMaintainStartTime())));
        custins.setMaintainEndtime(
                Time.valueOf(DateSupport.timeWithSecond2str(params.getMaintainEndTime())));

        // 设置是否接受优化建议服务
        custins.setIsAccept(Integer.valueOf(
                CheckUtils.checkBooleanInt(params.getOptmization(), ErrorCode.INVALID_OPTMIZATIONSERVICE)));
    }

    /**
     * 创建docker化的on ecs实例
     */
    private void createDockerInstanceOnEcs(CustInstanceDO custins,
                                           DockerInsLevelParseConfig config,
                                           com.aliyun.dba.instance.dataobject.InstanceLevelDO characterInsLevel,
                                           Integer port,
                                           Integer netType,
                                           Integer custinsOrder,
                                           Integer parentId,
                                           String paretInsName,
                                           ResourceContainer resourceContainer,
                                           RequestParamsDO params,
                                           List<CustInstanceDO> characterCustinsList) throws RdsException {
        // 设置custins参数
        custins.setDbType(characterInsLevel.getDbType());
        custins.setDbVersion(characterInsLevel.getDbVersion());
        custins.setCharacterType(characterInsLevel.getCharacterType());
        custins.setLevelId(characterInsLevel.getId());
        custins.setMaxAccounts(500);
        custins.setMaxDbs(500);
        custins.setConnType(params.getConnType());
        // TODO: ins name's policy
        if(!characterInsLevel.getCategory().equalsIgnoreCase("general")){
            //一主多从实例名不变
            custins.setInsName(custins.getInsName() + characterInsLevel.getId() + custinsOrder);
        }
        custins.setParentId(parentId);
        // create character custins
        characterCustinsList.add(custins);
        custinsService.createCustInstance(custins);
        String regionId = params.getRegionId();

        String zoneId = params.getZoneId();

        // Get ecsAccount, vpcId, vSwitchId
        String ecsAccount = ecsService.getEcsAccount(custins.getUserId(), regionId);
        // 得到镜像
        List<com.aliyun.dba.ecs.dataobject.EcsImageDO> ecsImageDOList = null;
        if (custins.isMariaDB()) {
            try {
                ecsImageDOList = ecsImageService.getEcsImageList(mysqlParaHelper.getAndCheckRegionID(),
                        custins.getDbType(), null, null, null,
                        CustinsParamSupport.CUSTINS_PARAM_VALUE_ECS_ARCH_VERSION_VPC, null);
            } catch (RdsException e) {
                if (ErrorCode.ECSIMAGE_NOT_FOUND.getDesc().equals(e.getMessage())) {
                    logger.info("get ecs image error, use region get ecs image.., error msg is " + e.getMessage());
                    ecsImageDOList = ecsImageService.getEcsImageList(mysqlParaHelper.getAndCheckRegionID(),
                            CustinsSupport.CONTAINER_TYPE_DOCKER, null, null, null,
                            null, null);
                } else {
                    throw e;
                }
            }
        } else {
            ecsImageDOList = ecsImageService.getEcsImageList(params.getRegionId(),
                    CustinsSupport.CONTAINER_TYPE_DOCKER, null, null, null, null, null);
        }
        String imageId = ecsImageDOList.get(0).getImageId();

        String osPassword = SupportUtils.getRandomPasswdForEcs(15);
        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        custinsResModel.setConnType(params.getConnType());

        if (config.getVipCount() > 0) {
            if (CustinsSupport.isVpcNetType(params.getNetType())) {
                String connAddrCust = mysqlParaHelper.getConnAddrCust(
                        custins.getInsName().replace("_", "-"),
                        custins.getRegionId(),
                        custins.getDbType());
                VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
                vipResModel.setVswitchId(params.getVswitchId());
                vipResModel.setVpcId(params.getUserVpcId());
                vipResModel.setUserVisible(1);
                vipResModel.setTunnelId(params.getTunnelId());
                vipResModel.setVip(params.getIpaddress());
                vipResModel.setVport(port);
                vipResModel.setZoneId(zoneId);
                boolean isCalledFromRM = Boolean.valueOf(mysqlParaHelper.getParameterValue("isCalledFromRM"));
                if (isCalledFromRM) {
                    vipResModel.setVpcInstanceId(mysqlParaHelper.getParameterValue(ParamConstants.VPC_INSTANCE_ID));
                } else {
                    vipResModel.setVpcInstanceId(paretInsName);
                }
                vipResModel.setConnAddrCust(connAddrCust);
                custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                custinsResModel.addVipResModel(vipResModel);
            } else {
                String connType = CustinsSupport.CONN_TYPE_LVS;
                custinsResModel.setConnType(connType);
                for (int j = 0; j < config.getVipCount(); j++) {
                    String connAddrCust = mysqlParaHelper.getConnAddrCust(
                            custins.getInsName().replace("_", "-") + j,
                            custins.getRegionId(),
                            custins.getDbType());
                    if (StringUtils.isBlank(connAddrCust)) {
                        connAddrCust = mysqlParaHelper.getConnAddrCust(
                                custins.getInsName().replace("_", "-") + j,
                                custins.getRegionId(),
                                CustinsSupport.CONTAINER_TYPE_DOCKER);
                    }
                    VipResModel vipResModel = initVipResModel(
                            netType, connAddrCust, port, config.getVportCountPerVip(), connType);
                    custinsResModel.addVipResModel(vipResModel);

                }
            }
        } else {
            custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
        }

        String ecsClass = characterInsLevel.getEcsClassCode();
        if (custins.isMbaseSql() && characterInsLevel.getInsCount() > 1){
            //采用双可用区的参数设置
            List<EcsResModel> ecsResModelList = new ArrayList<EcsResModel>();
            for(AvailableZoneInfoDO ecsAvzInfo: params.getMultiAVZExParam().getAvailableZoneInfoList()) {
                EcsResModel ecsResModel = initSingleEcsResModule(imageId, ecsAvzInfo.getVSwitchID(), params, osPassword,
                        1, regionId, ecsAvzInfo.getZoneID(), ecsClass, ecsAccount, config,
                        characterInsLevel.getId(), custins.getDiskSize(),
                        custins.getId(), parentId);
                ecsResModelList.add(ecsResModel);
                // 专有云环境需要设置Ecs的UserData
                if (custinsService.isInAPCEnvironment()) {
                    ClustersDO clusterDO = ecsService.getEcsAvailableClusterDO(ecsAvzInfo.getRegion(), CustinsSupport.CONTAINER_TYPE_DOCKER,
                            custins.getDbVersion(), params.getHostType(), ecsAvzInfo.getUserSpecified());
                    if (clusterDO == null) {
                        throw new RdsException(ErrorCode.EXTERNAL_FAILURE, "cannot get ecs available cluster ");
                    }
                    String ambariAgentConfig = "/etc/ambari-agent/conf/ambari-agent.ini";
                    // 从bakowner获取天龙VIP
                    String apiUrl = ambariService.getApiUrl(clusterDO.getClusterName());
                    String ambariVIP = ambariService.getAgentVpcVip(apiUrl);
                    if (StringUtils.isBlank(ambariVIP)){
                        throw new RdsException(ErrorCode.EXTERNAL_FAILURE, "Ambari VIP for agent not found for cluster " + custins.getClusterName());
                    }
                    String updateAmbariVIPScript = "#!/bin/sh \n" +
                            String.format("sed -i 's/hostname*.*/hostname = %s/g' %s \n", ambariVIP, ambariAgentConfig) +
                            "ambari-agent restart";
                    ecsResModel.setUserData(new String(Base64.encodeBase64(updateAmbariVIPScript.getBytes())));
                    ecsResModel.setDbType(custins.getDbType());
                    resourceContainer.setUserId(custins.getUserId());
                }
            }

            custinsResModel.setEcsResModelList(ecsResModelList);
        }else{
            EcsResModel ecsResModel = initSingleEcsResModule(imageId, params.getVswitchId(), params, osPassword, characterInsLevel.getInsCount(),
                    regionId, zoneId, ecsClass, ecsAccount, config, characterInsLevel.getId(), custins.getDiskSize(),
                    custins.getId(), parentId);
            // set host res model
            custinsResModel.setEcsResModel(ecsResModel);
            // put into resource container
        }
        // put into resource container
        resourceContainer.addCustinsResModel(custinsResModel);
        // create ecs acc
        dbsService.createEcsOsAccount(custins, osPassword);
    }

    /**
     *
     * @param imageId
     * @param vswitchId
     * @param params
     * @param osPassword
     * @param insCount
     * @param regionId
     * @param zoneId
     * @param ecsClass
     * @param ecsAccount
     * @param config
     * @param levelId
     * @param diskSize 单位MB
     * @return
     * @throws RdsException
     */
    private EcsResModel initSingleEcsResModule(String imageId, String vswitchId, RequestParamsDO params, String osPassword,
                                               Integer insCount, String regionId, String zoneId,
                                               String ecsClass, String ecsAccount, DockerInsLevelParseConfig config,
                                               Integer levelId, Long diskSize) throws RdsException {
        EcsResModel ecsResModel = new EcsResModel(imageId);
        ecsResModel.setVpcType(VpcType.USER_VPC);
        ecsResModel.setEcsVSwitchId(vswitchId);
        ecsResModel.setEcsVpcId(params.getUserVpcId());
        ecsResModel.setOsPassword(osPassword);
        ecsResModel.setInsCount(insCount);
        ecsResModel.setRegionId(regionId);
        ecsResModel.setZoneId(zoneId);
        ecsResModel.setInstanceType(ecsClass);
        ecsResModel.setEcsAccount(ecsAccount);
        ecsResModel.setInsPortCount(config.getPortCountPerIns());
        ecsResModel.setLevelId(levelId);

        /**
         * to #********
         * 补充描述 essd category
         */
        if (StringUtils.isNotBlank(params.getDataDiskCategory()) && params.getDataDiskCategory().contains(CustinsSupport.STORAGE_TYPE_CLOUD_ESSD)){

            // ecs apply
            ecsResModel.setDataDiskCategory(CustinsSupport.STORAGE_TYPE_CLOUD_ESSD);

            // disk apply
            ecsResModel.setPerformanceLevel(DockerOnEcsConstants.getEssdPerLevel(params.getDataDiskCategory()));
        }

        // 显示指定磁盘大小为0或者规格中声明为0
        if(! diskSize.equals(0L)) {
            List<EcsDataDisk> dataDiskList = new ArrayList<>();
            EcsDataDisk dataDisk = new EcsDataDisk();
            dataDisk.setCategory(params.getDataDiskCategory());
            dataDisk.setDevice(CustinsSupport.ECS_DEISK_PREFIX + "/data");
            dataDisk.setSize(mysqlParaHelper.getExtendDiskSizeForEcsIns(CustinsSupport.DB_TYPE_DOCKER, diskSize));
            dataDisk.setRegionId(regionId);
            if (params.getDataDiskCategory().contains(CustinsParamSupport.ECS_ClOUD_ESSD)) {
                dataDisk.setCategory(CustinsParamSupport.ECS_ClOUD_ESSD);
            }else {
                dataDisk.setCategory(params.getDataDiskCategory());
            }
            dataDisk.setZoneId(zoneId);
            if (params.getSnapShotId() != null) {
                dataDisk.setSnapshotId(params.getSnapShotId());
            }
            dataDiskList.add(dataDisk);
            ecsResModel.setEcsDataDiskList(dataDiskList);
        } else {
            ecsResModel.setDataDiskSize(0L);
        }

        if (insCount > 1) {
            // 创建部署集，保证多个主机分布到不同NC
            EcsDeploymentSet ecsDeploymentSet = new EcsDeploymentSet();
            ecsDeploymentSet.setDomain(EcsDeploymentSet.Domain.DEFAULT); // 设置部署集的域
            ecsDeploymentSet.setGranularity(EcsDeploymentSet.Granularity.HOST); // 设置部署集的粒度
            ecsDeploymentSet.setStrategy(EcsDeploymentSet.Strategy.STRICT_DISPERSION); // 设置部署集的策略（打散）
            ecsResModel.setDeploymentSet(ecsDeploymentSet);
        }

        return ecsResModel;
    }
    private EcsResModel initSingleEcsResModule(String imageId, String vswitchId, RequestParamsDO params, String osPassword,
                                               Integer insCount, String regionId, String zoneId,
                                               String ecsClass, String ecsAccount, DockerInsLevelParseConfig config,
                                               Integer levelId, Long diskSize,
                                               Integer characterCustinsId, Integer primaryCustinsId) throws
        RdsException {
        EcsResModel ecsResModel = new EcsResModel(imageId);
        ecsResModel.setVpcType(VpcType.USER_VPC);
        ecsResModel.setEcsVSwitchId(vswitchId);
        ecsResModel.setEcsVpcId(params.getUserVpcId());
        ecsResModel.setOsPassword(osPassword);
        ecsResModel.setInsCount(insCount);
        ecsResModel.setRegionId(mysqlParaHelper.transferRegionId(regionId));
        ecsResModel.setZoneId(mysqlParaHelper.transferZoneId(zoneId));
        ecsResModel.setInstanceType(ecsClass);
        ecsResModel.setEcsAccount(ecsAccount);
        ecsResModel.setInsPortCount(config.getPortCountPerIns());
        ecsResModel.setLevelId(levelId);
        if ("mysql".equals(params.getDbType()) && params.getDataDiskCategory().contains(DockerOnEcsConstants.ECS_ClOUD_ESSD)) {
            ecsResModel.setPerformanceLevel(DockerOnEcsConstants.getEssdPerLevel(params.getDataDiskCategory()));
        }

        String kmsKeyId = params.getCmkId();
        String roleArn = params.getRoleArn();
        String uid = params.getUid();
        if (StringUtils.isNotBlank(kmsKeyId) && StringUtils.isNotBlank(roleArn)){
            // byok

            // check key
            cloudSSDEncryptionService.checkByokKeyAvail(characterCustinsId, regionId, roleArn, kmsKeyId, uid, primaryCustinsId);

            // role list
            List<EcsResModel.Arn> roleArnList = cloudSSDEncryptionService.getByokList(roleArn, uid, ecsAccount);

            ecsResModel.setkMSKeyId(kmsKeyId);
            ecsResModel.setArns(roleArnList);
        }

        // 显示指定磁盘大小为0或者规格中声明为0
        if(! diskSize.equals(0L)) {
            List<EcsDataDisk> dataDiskList = new ArrayList<>();
            EcsDataDisk dataDisk = new EcsDataDisk();
            dataDisk.setCategory(params.getDataDiskCategory());
            dataDisk.setDevice(CustinsSupport.ECS_DEISK_PREFIX + "/data");
            dataDisk.setSize(mysqlParaHelper.getExtendDiskSizeForEcsIns(CustinsSupport.DB_TYPE_DOCKER, diskSize));
            dataDisk.setRegionId(regionId);
            if (params.getDataDiskCategory().contains(DockerOnEcsConstants.ECS_ClOUD_ESSD)) {
                dataDisk.setCategory(DockerOnEcsConstants.ECS_ClOUD_ESSD);
            }else {
                dataDisk.setCategory(params.getDataDiskCategory());
            }
            dataDisk.setZoneId(zoneId);
            if (params.getSnapShotId() != null) {
                dataDisk.setSnapshotId(params.getSnapShotId());
            }
            dataDiskList.add(dataDisk);
            ecsResModel.setEcsDataDiskList(dataDiskList);
        } else {
            ecsResModel.setDataDiskSize(0L);
        }

        if (insCount > 1) {
            // 创建部署集，保证多个主机分布到不同NC
            EcsDeploymentSet ecsDeploymentSet = new EcsDeploymentSet();
            ecsDeploymentSet.setDomain(EcsDeploymentSet.Domain.DEFAULT); // 设置部署集的域
            ecsDeploymentSet.setGranularity(EcsDeploymentSet.Granularity.HOST); // 设置部署集的粒度
            ecsDeploymentSet.setStrategy(EcsDeploymentSet.Strategy.STRICT_DISPERSION); // 设置部署集的策略（打散）
            ecsResModel.setDeploymentSet(ecsDeploymentSet);
        }

        return ecsResModel;
    }

    private void createDockerInstanceOnNc(CustInstanceDO characterCustins, CustInstanceDO custins, String storage,
                                          com.aliyun.dba.instance.dataobject.InstanceLevelDO characterInsLevel,
                                          Integer bizType, List<CustInstanceDO> characterCustinsList,
                                          DockerInsLevelParseConfig config, Integer i, Integer netType,
                                          Integer port, String hostType, ResourceContainer resourceContainer,
                                          Set<Integer> hostIdSet) throws RdsException {
        characterCustins.setDbType(characterInsLevel.getDbType());
        characterCustins.setDbVersion(characterInsLevel.getDbVersion());
        characterCustins.setCharacterType(characterInsLevel.getCharacterType());
        characterCustins.setLevelId(characterInsLevel.getId());
        //for host min 5G
        if (Validator.isNotNull(storage)) {
            if (Integer.parseInt(storage) != 0) {
                CustinsSupport.setDiskSize(characterCustins, bizType, storage, CustinsSupport.NC_MIN_DISK_SIZE);
            } else {
                characterCustins.setDiskSize(0L);
            }
        } else {
            characterCustins.setDiskSize(characterCustins.getDiskSize());
        }
        // TODO: ins name's policy
        if(!characterInsLevel.getCategory().equalsIgnoreCase("general")){
            //一主多从实例名不变
            characterCustins.setInsName(custins.getInsName() + characterInsLevel.getId() + i);
        }
        characterCustins.setParentId(custins.getId());
        // create character custins
        characterCustinsList.add(characterCustins);
        custinsService.createCustInstance(characterCustins);

        CustinsResModel custinsResModel = new CustinsResModel(
                characterCustins.getId());
        // TODO: conn type
        if (config.getVipCount() > 0) {
            String connType = CustinsSupport.CONN_TYPE_LVS;
            custinsResModel.setConnType(connType);
            for (int j = 0; j < config.getVipCount(); j++) {
                String connAddrCust = mysqlParaHelper.getConnAddrCust(
                        characterCustins.getInsName().replace("_", "-") + j,
                        custins.getRegionId(),
                        CustinsSupport.CONTAINER_TYPE_DOCKER);
                VipResModel vipResModel = initVipResModel(
                        netType, connAddrCust, port, config.getVportCountPerVip(), connType);
                custinsResModel.addVipResModel(vipResModel);
            }
        } else {
            custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
        }

        HostinsResModel hostinsResModel = new HostinsResModel(
                characterInsLevel.getId());
        if (hostType != null) {
            hostinsResModel.setHostType(Integer.parseInt(hostType));
        } else {
            hostinsResModel.setHostType(-1);
        }
        hostinsResModel.setInsCount(config.getInsCount());
        hostinsResModel.setInsPortCount(config.getPortCountPerIns());
        hostinsResModel.setDiskType(config.getDiskType());
        // set distribute mode
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSiteDistributeMode(config.getDistributePolicy().getSite());
        distributeRule.setCabinetDistributeMode(config.getDistributePolicy().getCabinet());
        distributeRule.setHostDistributeMode(config.getDistributePolicy().getHost());
        distributeRule.setSpecifyHostIdSet(hostIdSet);
        // set host res model
        custinsResModel.setHostinsResModel(hostinsResModel);
        // put into resource container
        resourceContainer.addCustinsResModel(custinsResModel);

    }

    private void createRdsServiceForDocker(CustInstanceDO custins,
                                           DockerInsLevelParseConfig config,
                                           com.aliyun.dba.instance.dataobject.InstanceLevelDO characterInsLevel,
                                           Integer netType,
                                           ResourceContainer resourceContainer,
                                           RequestParamsDO params,
                                           String serviceType) throws RdsException {
        logger.error("create rds serice");
        //vip?vport 用户名:密码?,白名单--->任务阶段设置

        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        List<RdsResModel> rdsResModelList = new ArrayList<>();
        Map<String, Object> custinsBasicConfig = null;
        RdsResModel rdsResModel = null;
        if (characterInsLevel.getDbType().equalsIgnoreCase("hbase")) {
            custinsBasicConfig = getHbaseResConfig();
            custinsBasicConfig.put("insName", custins.getInsName());
            rdsResModel = getRdsSrvResModel(params, characterInsLevel, custinsBasicConfig);
            rdsResModel.setdBInstanceNetType(CustinsSupport.NET_TYPE_VPC.toString());
            rdsResModel.setIsUserVpc("true");
            //TODO: REMOVE THE PARAMETER
            rdsResModel.setdBInstanceExParam("{\"hostClassInfoList\":[{\"configMap\":{\"hadoop-env.sh\":" +
                    "{\"NN_XMX\":\"2048\",\"JN_XMX\":\"512\",\"NN_XMN\":\"512\"},\"zookeeper-env.sh\":{\"ZK_XMX\":\"1024\"}," +
                    "\"hbase-env.sh\":{\"HM_XMN\":\"256\",\"HM_XMX\":\"1024\",\"HT_XMX\":\"512\"}}," +
                    "\"dataDiskCategory\":\"cloud_ssd\",\"deleteWithInstance\":true,\"diskCount\":1," +
                    "\"hostClass\":\"hbase.n1.large\",\"nodeCount\":1,\"ports\":\"16000,443,2181\"," +
                    "\"role\":\"MASTER1\",\"services\":\"NGINX,ZOOKEEPER,JOURNALNODE,NAMENODE,HBASE_MASTER,ZKFC,GANGLIA_MONITOR\"," +
                    "\"singleDiskStorage\":100},{\"configMap\":{\"hadoop-env.sh\":{\"NN_XMX\":\"2048\",\"JN_XMX\":\"512\",\"NN_XMN\":\"512\"}," +
                    "\"zookeeper-env.sh\":{\"ZK_XMX\":\"1024\"},\"hbase-env.sh\":{\"HM_XMN\":\"256\",\"HM_XMX\":\"1024\"," +
                    "\"HT_XMX\":512}},\"dataDiskCategory\":\"cloud_ssd\",\"deleteWithInstance\":true,\"diskCount\":1," +
                    "\"hostClass\":\"hbase.n1.large\",\"nodeCount\":1,\"ports\":\"16000,2181\",\"role\":\"MASTER2\"," +
                    "\"services\":\"ZOOKEEPER,JOURNALNODE,NAMENODE,ZKFC,HBASE_MASTER,GANGLIA_MONITOR\",\"singleDiskStorage\":100}," +
                    "{\"configMap\":{\"hadoop-env.sh\":{\"JN_XMX\":\"512\",\"DN_XMX\":\"512\"}," +
                    "\"zookeeper-env.sh\":{\"ZK_XMX\":\"1024\"},\"hbase-env.sh\":{\"HR_XMX\":\"4096\",\"HR_XMN\":\"1024\"}}," +
                    "\"dataDiskCategory\":\"cloud_ssd\",\"deleteWithInstance\":true,\"diskCount\":4,\"hostClass\":\"hbase.n1.large\"," +
                    "\"nodeCount\":1,\"ports\":\"16020,2181\",\"role\":\"CORE1\"," +
                    "\"services\":\"ZOOKEEPER,JOURNALNODE,DATANODE,HBASE_REGIONSERVER,GANGLIA_MONITOR\",\"singleDiskStorage\":100}," +
                    "{\"configMap\":{\"hadoop-env.sh\":{\"DN_XMX\":\"512\"},\"hbase-env.sh\":{\"HR_XMX\":\"4096\",\"HR_XMN\":\"1024\"}}," +
                    "\"dataDiskCategory\":\"cloud_ssd\",\"deleteWithInstance\":true,\"diskCount\":4,\"hostClass\":\"hbase.n1.large\"," +
                    "\"nodeCount\":1,\"ports\":\"16020\",\"role\":\"CORE2\",\"services\":\"DATANODE,HBASE_REGIONSERVER,GANGLIA_MONITOR\"," +
                    "\"singleDiskStorage\":100}]}");
            //TODO:ADD STORAGE
            //TODO:support multiple ins count of rds servcie
            //TODO: now hbase didn't support vpc

            rdsResModel.setAccountType(CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON);
            if (CustinsSupport.isVpcNetType(netType)) {
                rdsResModel.setVpcId(params.getUserVpcId());
                rdsResModel.setTunnelId(params.getTunnelId());
                rdsResModel.setVswitchId(params.getVswitchId());
            }

        } else if (characterInsLevel.getDbType().equalsIgnoreCase("mysql")) {
            custinsBasicConfig = getMysqlResConfig();
            custinsBasicConfig.put("insName", custins.getInsName());
            rdsResModel = getRdsSrvResModel(params, characterInsLevel, custinsBasicConfig);
        } else {
            logger.error("Didn't support type");
        }
        rdsResModelList.add(rdsResModel);
        custinsResModel.setRdsResModelList(rdsResModelList);
        resourceContainer.addCustinsResModel(custinsResModel);
    }

    private RdsResModel getRdsSrvResModel(RequestParamsDO params, com.aliyun.dba.instance.dataobject.InstanceLevelDO characterInsLevel,
                                          Map<String, Object> custinsBasicConfig) {


        RdsResModel rdsResModel = new RdsResModel();
        rdsResModel.setRole(custinsBasicConfig.get("srvLabel").toString());
        rdsResModel.setInsCount(Integer.valueOf(custinsBasicConfig.get("custinsCount").toString()));
        rdsResModel.setUser_Id(custinsBasicConfig.get("userId").toString());
        rdsResModel.setUid(custinsBasicConfig.get("uid").toString());
        rdsResModel.setdBInstanceClass(characterInsLevel.getClassCode());
        rdsResModel.setHostType(String.valueOf(characterInsLevel.getHostType()));
        rdsResModel.setZoneId(params.getZoneId());
        rdsResModel.setRegion(params.getRegion());
        if (params.getClusterName() != null) {
            rdsResModel.setClusterName(params.getClusterName());
        }
        rdsResModel.setRegionId(params.getRegionId());
        rdsResModel.setEngine(characterInsLevel.getDbType());
        rdsResModel.setEngineVersion(characterInsLevel.getDbVersion());
        rdsResModel.setdBInstanceNetType(CustinsSupport.NET_TYPE_PRIVATE.toString());
        rdsResModel.setAccountType("1");
        String sourceInsName = custinsBasicConfig.get("insName").toString();

        //原来实例名的10位-rds-随机uuid的10位
        String uuid = UUID.randomUUID().toString().replace("-", "");
        Integer start = new Random().nextInt(16);
        String insName = (new StringBuilder(sourceInsName.substring(0, Math.min(sourceInsName.length(), 9)))).
                append("rds").append(uuid.substring(start, start + 10)).toString();
        insName = insName.replace("_", "").replace("-", "");
        rdsResModel.setDbInstanceName(insName);
        rdsResModel.setConnectionString(insName);

        rdsResModel.setSecurityIPList((custinsBasicConfig.containsKey("whiteIpList") ?
                custinsBasicConfig.get("whiteIpList") : "0.0.0.0/0").toString());
        rdsResModel.setBackupRetentionPeriod(Integer.valueOf((custinsBasicConfig.containsKey("backupReten") ?
                custinsBasicConfig.get("backupReten") : 10).toString()));
        rdsResModel.setPreferredBackupTime((custinsBasicConfig.containsKey("preferredBackupTime") ?
                custinsBasicConfig.get("preferredBackupTime") : "12:00Z").toString());
        return rdsResModel;
    }

    private Map<String, Object> getHbaseResConfig() {
        Map<String, Object> custinsBasicConfig = new HashMap<String, Object>();
        custinsBasicConfig.put("srvLabel", "datasource");
        custinsBasicConfig.put("custinsCount", 1);
        custinsBasicConfig.put("userId", CustinsSupport.HITSDB_RDS_USERID);
        custinsBasicConfig.put("uid", CustinsSupport.HITSDB_RDS_UID);
        custinsBasicConfig.put("whiteIpList", "0.0.0.0/0");
        custinsBasicConfig.put("backupReten", 10);
        custinsBasicConfig.put("preferredBackupTime", "12:00Z");
        return custinsBasicConfig;
    }

    private Map<String, Object> getMysqlResConfig() {
        Map<String, Object> custinsBasicConfig = new HashMap<String, Object>();
        custinsBasicConfig.put("srvLabel", "metadb");
        custinsBasicConfig.put("custinsCount", 1);
        custinsBasicConfig.put("userId", CustinsSupport.CSTORE_RDS_USERID);
        custinsBasicConfig.put("uid", CustinsSupport.CSTORE_RDS_UID);
        custinsBasicConfig.put("whiteIpList", "0.0.0.0/0");
        custinsBasicConfig.put("backupReten", 10);
        custinsBasicConfig.put("preferredBackupTime", "12:00Z");
        return custinsBasicConfig;
    }


    @Override
    public ResourceContainer getDockerResContainerForRebuild(
        CustInstanceDO custins, CustInstanceDO tempCustins, String region,
        List<InstanceDO> instanceList, Integer srcInstanceId, Set<Integer> specifyHostIdSet
    ) throws RdsException {

        com.aliyun.dba.instance.dataobject.InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        DockerInsLevelParseConfig config = parseDockerInsExtraInfo(insLevel.getExtraInfo());

        // DistributeMode siteMode = config.getDistributePolicy().getSite();
        DistributeMode cabinetMode = config.getDistributePolicy().getCabinet();
        DistributeMode hostMode = config.getDistributePolicy().getHost();

        ResourceContainer resourceContainer = new ResourceContainer(region,
                CustinsSupport.DB_TYPE_DOCKER);
        CustinsResModel custinsResModel = new CustinsResModel(tempCustins.getId());
        // init host res model
        HostinsResModel hostinsResModel = new HostinsResModel(custins.getLevelId());
        hostinsResModel.setInsCount(1);
        hostinsResModel.setInsPortCount(config.getPortCountPerIns());
        hostinsResModel.setDiskType(config.getDiskType());

        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSpecifyHostIdSet(specifyHostIdSet);
        for (InstanceDO instance : instanceList) {
            if (instance.getId().equals(srcInstanceId)) {
                // keep site name not changed
                distributeRule.addSpecifySiteName(instance.getSiteName());
                // set current host id low priority
                distributeRule.addInferiorHostId(instance.getHostId());
                // keep host type same
                hostinsResModel.setHostType(instance.getHostType());
            } else {
                if (DistributeMode.FORCE_SCATTER.equals(cabinetMode)) {
                    distributeRule.addExcludeCabinet(instance.getCabinet());
                } else if (DistributeMode.TRY_SCATTER.equals(cabinetMode)) {
                    distributeRule.addInferiorCabinet(instance.getCabinet());
                } else if (DistributeMode.FORCE_CROWD.equals(cabinetMode)) {
                    distributeRule.addSpecifyCabinet(instance.getCabinet());
                }

                if (DistributeMode.FORCE_SCATTER.equals(hostMode)) {
                    distributeRule.addExcludeHostId(instance.getHostId());
                } else if (DistributeMode.TRY_SCATTER.equals(hostMode)) {
                    distributeRule.addInferiorHostId(instance.getHostId());
                } else if (DistributeMode.FORCE_CROWD.equals(hostMode)) {
                    distributeRule.addSpecifyHostId(instance.getHostId());
                }
            }
        }
        // append host res model
        custinsResModel.setHostinsResModel(hostinsResModel);
        // append custins res model
        resourceContainer.addCustinsResModel(custinsResModel);
        return resourceContainer;
    }

    @Override
    public Map<String, Object> disPatchDockerTask(DockerTaskInputParam dockerTaskParam, Boolean isClone)
            throws RdsException {
        CustInstanceDO custins = dockerTaskParam.getCustIns();
        Integer taskId = null;
        if (!isClone) {
            taskId = dockerCustinsService.createDockerInstanceTask(
                    dockerTaskParam.getAction(), dockerTaskParam.getOperatorId(), custins,
                    dockerTaskParam.getCharacterCustinsList(), dockerTaskParam.getTaskQueueParam(),
                    dockerTaskParam.getCustinsIpWhiteList(), dockerTaskParam.getMycnfCustinstancesMap(),
                    dockerTaskParam.getHostType());
        } else {
            taskId = dockerCustinsService.createDockerCloneInstanceTask(
                    dockerTaskParam.getAction(), dockerTaskParam.getOperatorId(), custins, dockerTaskParam.getSrcCusIns(),
                    dockerTaskParam.getCharacterCustinsList(), dockerTaskParam.getTaskQueueParam(),
                    dockerTaskParam.getCustinsIpWhiteList(), dockerTaskParam.getMycnfCustinstancesMap(),
                    dockerTaskParam.getHostType());

        }

        Map<String, Object> data = new HashMap<>();
        data.put(ParamConstants.TASK_ID, taskId);
        data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
        data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
        return data;
    }

    @Override
    public DockerTaskInputParam createGeneralDockerDbInstanceNode(Map<String, String> actionParams, RequestParamsDO params,CustInstanceDO parentCustins, CustInstanceDO masterIns)
            throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        List<CustInstanceDO> characterCustinsList = new ArrayList<>();
        DockerTaskInputParam dockerTaskInputParam = new DockerTaskInputParam();


        boolean success = false;
        try {

            // set user info
            custins.setUserId(params.getUserId());
            //hostType
            String hostType = params.getHostType();
            // set kind code, for on ecs must support hosttype
            if (hostType != null && hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_ECS);
            } else if (hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_POLARSTORE)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE);
            } else {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER);
            }
            // set basic info
            String engine = params.getDbType();
            String engineVersion = params.getDbVersion();
            custins.setDbType(params.getDbType());
            custins.setDbVersion(engineVersion);
            custins.setClusterName(params.getClusterName());
            custins.setComment(params.getDesc());
            custins.setInsName(params.getDbBInstanceName());
            Integer netType = params.getNetType();
            custins.setNetType(netType);
            custins.setMaintainStarttime(
                    Time.valueOf(DateSupport.timeWithSecond2str(params.getMaintainStartTime())));
            custins.setMaintainEndtime(
                    Time.valueOf(DateSupport.timeWithSecond2str(params.getMaintainEndTime())));
            custins.setConnType(params.getConnType());
            String classCode = params.getClassCode();
            com.aliyun.dba.instance.dataobject.InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                    params.getDbType(), params.getDbVersion(), 'x', CustinsSupport.CHARACTER_TYPE_PHYSICAL);
            if (StringUtils.isBlank(classCode) || insLevel == null) {
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }


            Integer port = CustinsValidator.getRealNumber(params.getPortStr());
            if (port < 0) {
                throw new RdsException(ErrorCode.INVALID_PORT);
            }

            if(params.getInsType().equalsIgnoreCase(CUSTINS_INSTYPE_READ.toString())){
                //只读库
                custins.setCharacterType(insLevel.getCharacterType());
                custins.setLevelId(insLevel.getId());
                custins.setDiskSize(CheckUtils.parseInt(params.getStorage(), CustinsSupport.ECS_MIN_DISK_SIZE, null, ErrorCode.INVALID_STORAGE) * 1024L);
                custins.setParentId(parentCustins.getId());
                custins.setAccountMode(masterIns.getAccountMode());
                custins.setKindCode(masterIns.getKindCode());
                custins.setType(masterIns.getType());
                custinsService.createReadCustInstance(masterIns, custins);
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID,
                        mysqlParamSupport.getParameterValue(actionParams, CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, ""));
                // read ins sync_mode should set to 0
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE,
                        CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_ASYNC);
                String composeTag = mysqlParaHelper.selectComposeTag(params.getClusterName(), true);
                custinsParamService.setCustinsParam(custins.getId(),
                        CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);

                // get region & cluster & host info
                String region = params.getRegion();
                String zoneId = params.getZoneId();
                String regionId = params.getRegionId();
                custins.setRegionId(regionId);

                ResourceContainer resourceContainer = null;
                // init resource container
                resourceContainer = new ResourceContainer(region, CustinsSupport.CONTAINER_TYPE_DOCKER);
                resourceContainer.setAccessId(mysqlParamSupport.getParameterValue(actionParams, "accessId"));
                resourceContainer.setOrderId(mysqlParamSupport.getParameterValue(actionParams, "orderId"));
                resourceContainer.setUserId(mysqlParaHelper.getAndCheckUserId());
                resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));

                CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
                String extraInfo = insLevel.getExtraInfo();
                DockerInsLevelParseConfig config = parseDockerInsExtraInfo(extraInfo);

                // OnECS实例备用只读不绑定vip, set back read ins conntype to lvs
                if (config.getVipCount() > 0) {
                    logger.error("check vip num");
                    String connAddrCust = mysqlParaHelper.getConnAddrCust(
                            custins.getInsName(), regionId,
                            custins.getDbType());
                    VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
                    vipResModel.setVswitchId(params.getVswitchId());
                    vipResModel.setVpcId(params.getUserVpcId());
                    vipResModel.setUserVisible(1);
                    vipResModel.setTunnelId(params.getTunnelId());
                    vipResModel.setVip(params.getIpaddress());
                    vipResModel.setVport(port);
                    vipResModel.setZoneId(zoneId);
                    vipResModel.setVpcInstanceId(custins.getInsName());
                    vipResModel.setConnAddrCust(connAddrCust);
                    custinsResModel.addVipResModel(vipResModel);
                    custins.setConnType(CustinsSupport.CONN_TYPE_LVS);
                } else {
                    custins.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
                }

                custinsResModel.setConnType(custins.getConnType());
                custinsResModel.setClusterName(custins.getClusterName());

                // start 组装resouce model
                HostinsResModel hostinsResModel = new HostinsResModel(insLevel.getId());
                DockerInsLevelParseConfig dockerInsLevelParseConfig = custinsService.parseDockerInsExtraInfo(extraInfo);
                hostinsResModel.setInsCount(insLevel.getInsCount());
                hostinsResModel.setInsPortCount(dockerInsLevelParseConfig.getPortCountPerIns());
                hostinsResModel.setDiskType(dockerInsLevelParseConfig.getDiskType());
                hostinsResModel.setDiskSizeSold(custins.getDiskSize());
                // get host type of src instance use
                InstanceDO srcInstance = instanceService.getInstanceByCustinsId(masterIns.getId()).get(0);
                hostinsResModel.setHostType(srcInstance.getHostType());
                String label = hostService.getHostInfoParam(srcInstance.getHostId(), "label");
                hostinsResModel.setLabel(label);
                if (Validator.isNotNull(extraInfo)){
                    dockerInsLevelParseConfig = custinsService.parseDockerInsExtraInfo(extraInfo);
                    if (dockerInsLevelParseConfig.getCpuSet() != null && dockerInsLevelParseConfig.getCpuSet()) {
                        hostinsResModel.setCpuType(CPUType.CPU_SET);
                    }
                }
                String siteName = clusterService.getSiteNameByAvzone(zoneId);
                // DistributeRule
                DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
                distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER); // 大客户专享集群 只读实例 主机 强制打散
                distributeRule.setSiteDistributeMode(DistributeMode.FORCE_CROWD);
                distributeRule.setSpecifySiteNameSet(new HashSet<String>(){{add(siteName);}});
                Set<Integer> hostIds = new HashSet<>();
                Set<Integer> inferiorHostIdSets = new HashSet<>();

                List<InstanceDO> instances = instanceService.getInstanceByCustinsId(masterIns.getId());
                if(instances !=null) {
                    for (InstanceDO instance : instances) {
                        inferiorHostIdSets.add(instance.getHostId());
                    }
                }

                List<CustInstanceDO> readCustins = mySQLGeneralService.getFollowerInsList(parentCustins.getId());
                for(CustInstanceDO oneReadIns:readCustins){
                    instances = instanceService.getInstanceByCustinsId(oneReadIns.getId());
                    if(instances !=null) {
                        for (InstanceDO instance : instances) {
                            inferiorHostIdSets.add(instance.getHostId());
                        }
                    }
                }

                distributeRule.setExcludeHostIdSet(hostIds);
                distributeRule.setInferiorHostIdSet(inferiorHostIdSets);
                custinsResModel.setHostinsResModel(hostinsResModel);
                resourceContainer.setSubDomain(params.getRegion());
                resourceContainer.setClusterName(custins.getClusterName());

                resourceContainer.addCustinsResModel(custinsResModel);

                Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
                if (!response.getCode().equals(200)) {
//                super.createResourceRecord(readins.getClusterName(), readins.getInsName(),
//                        JSON.toJSONString(response));
                    throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                            ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                }
                if (StringUtils.isEmpty(custins.getClusterName())) {
                    List<AllocateResRespModel.CustinsResRespModel> respList = response.getData().getCustinsResRespModelList();
                    if (!respList.isEmpty()) {
                        custins.setClusterName(respList.get(0).getClusterName());
                    }
                }
                // init task queue param of backup
                Map<String, Object> taskQueueParam = params.getTaskQueueParam();
                if (taskQueueParam == null) {
                    taskQueueParam = new HashMap<>();
                }
                taskQueueParam.put("isUserCluster", true);
                taskQueueParam.put("is_general", true);
                dockerTaskInputParam.setAction(params.getAction());
                dockerTaskInputParam.setCustIns(custins);
                dockerTaskInputParam.setHostType(hostType);
                dockerTaskInputParam.setTaskQueueParam(taskQueueParam);

                dbsService.syncUserAccountsforEcs(masterIns, custins);

                // sync all ip white list group
                ipWhiteListService.syncCustinsIpWhiteList(masterIns.getId(), custins.getId());

                dockerTaskInputParam.setOperatorId(params.getOperatorId());
                success = true;
                if (StringUtils.isNotBlank(params.getDataDiskCategory())){
                    custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.DATADISK_STORAGE_TYPE, params.getDataDiskCategory());
                }

                return dockerTaskInputParam;

            }else{
                //主库
                custins.setAccountMode(params.getAccountMode());
                if (custins.isSuperAccountMode()) {
                    custins.setMaxDbs(500);
                    custins.setMaxAccounts(500);
                }
                // ip white list
                if (!params.getClone()) {
                    Set<String> ipSet = params.getIpSet();
                    String whitelistNetType = params.getWhitelistNetType();
                    CustinsIpWhiteListDO custinsIpWhiteList = new CustinsIpWhiteListDO(null,
                            SupportUtils.getIpWhiteListStr(ipSet), CustinsIpWhiteListDO.DEFAULT_GROUP_NAME,
                            "", whitelistNetType);
                    dockerTaskInputParam.setCustinsIpWhiteList(custinsIpWhiteList);

                }

                // update common properties
                updateCustinsCommonProperties(custins, params);

                // init task queue param of backup
                Map<String, Object> taskQueueParam = params.getTaskQueueParam();

                // get external config for different db type
                Map<String, List<MycnfCustinstanceDO>> mycnfCustinstancesMap = params.getMycnfCustinstancesMap();

                // get region & cluster & host info
                AVZInfo avzInfo = params.getAvzInfo();
                if (avzInfo == null) {
                    throw new RdsException(ErrorCode.INVALID_ACTION);
                }
                if(!avzInfo.isValidForModify()){
                    avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
                }
                // create custins
                CustInstanceDO logicIns = custins.clone();
                logicIns.setLevelId(mySQLGeneralService.getDefaultLogicInsLevel(logicIns.getDbType(),logicIns.getDbVersion()).getId());
                logicIns.setInsName(mysqlParamSupport.getAndCheckGeneralCategoryGroupName(actionParams));
                logicIns = custinsService.createCustInstance(logicIns);
                parentCustins = logicIns;

                String region = avzInfo.getMainLocation();
                //String region = params.getRegion();
                String clusterName = params.getClusterName();
                Set<Integer> hostIdSet = new HashSet<>();
                if (StringUtils.isNotBlank(clusterName)) {  // 仅在输入集群名时HostId参数才有效
                    hostIdSet = params.getHostIdSet();
                }

                custins.setLevelId(insLevel.getId());
                String storage = params.getStorage();
                if (Validator.isNotNull(storage)) {
                    custins.setDiskSize(Long.valueOf(storage) * 1024L);
                }
                custins.setCharacterType(insLevel.getCharacterType());
                custins.setLevelId(insLevel.getId());



                ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, CustinsSupport.CONTAINER_TYPE_DOCKER);
                resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));

                Integer bizType = params.getBizType();
                String composeTag = mysqlParaHelper.selectComposeTag(clusterName);
                EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
                        custins.getDbType(), custins.getDbVersion(), insLevel.getCategory(), composeTag);
                logger.info("createDockerDbInstance " + custins.getInsName() + "select compose Tag: " +  composeTag + " clusterName:" + clusterName);
                custinsParamService.setCustinsParam(parentCustins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
                JSONObject jsonService = JSON.parseObject(engineCompose.getServices());


                InstanceLevelDO characterInsLevel = insLevel;
                String extraInfo = null;
                DockerInsLevelParseConfig config = null;
                if (CustinsSupport.isRdsSrvDockerize( new Gson().fromJson(jsonService.getString(characterInsLevel.getDbType()), EngineService.class))) {
                    extraInfo = characterInsLevel.getExtraInfo();
                    config = parseDockerInsExtraInfo(extraInfo);
                    if (hostType != null && hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {
                        // for ecs min 20G
                        if (Validator.isNotNull(storage)) {
                            if (Integer.parseInt(storage) != 0) {
                                CustinsSupport.setDiskSize(custins, bizType, storage, CustinsSupport.ECS_MIN_DISK_SIZE);
                            } else {
                                custins.setDiskSize(0L);
                            }
                        } else {
                            custins.setDiskSize(characterInsLevel.getDiskSize());
                        }
                        createDockerInstanceOnEcs(custins, config, characterInsLevel,
                                port, netType, 0, logicIns.getId(), logicIns.getInsName(),
                                resourceContainer, params, characterCustinsList);
                    } else {
                        createDockerInstanceOnNc(custins, logicIns, storage, characterInsLevel,
                                bizType, characterCustinsList, config, 0, netType, port, hostType,
                                resourceContainer, hostIdSet);
                    }
                    custinsParamService.setCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);

                } else {
                    createRdsServiceForDocker(custins, config, characterInsLevel,
                            netType, resourceContainer, params, "rds");
                }



                // init logic custins resource model
                CustinsResModel logicCustinsResModel = new CustinsResModel(
                        logicIns.getId());
                logicCustinsResModel.setConnType(params.getConnType());
                String zoneId = params.getZoneId();
                //TODO 为 logic ins申请单独的RwLink
//                if (config.getVipCount() > 0) {
////                    if (CustinsSupport.isVpcNetType(params.getNetType())) {
////                        String connAddrCust = mysqlParaHelper.getConnAddrCust(
////                                logicIns.getInsName().replace("_", "-"),
////                                logicIns.getDbType());
////                        VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
////                        vipResModel.setVswitchId(params.getVswitchId());
////                        vipResModel.setVpcId(params.getUserVpcId());
////                        vipResModel.setUserVisible(1);
////                        vipResModel.setTunnelId(params.getTunnelId());
////                        vipResModel.setVip(params.getIpaddress());
////                        vipResModel.setVport(port);
////                        vipResModel.setZoneId(zoneId);
////                        boolean isCalledFromRM = Boolean.valueOf(mysqlParaHelper.getParameterValue("isCalledFromRM"));
////                        if (isCalledFromRM) {
////                            vipResModel.setVpcInstanceId(mysqlParaHelper.getParameterValue(ParamConstants.VPC_INSTANCE_ID));
////                        } else {
////                            vipResModel.setVpcInstanceId(logicIns.getInsName());
////                        }
////                        vipResModel.setConnAddrCust(connAddrCust);
////                        logicCustinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
////                        logicCustinsResModel.addVipResModel(vipResModel);
////                    }
//                }
//                // put into resource container
//                resourceContainer.addCustinsResModel(logicCustinsResModel);

                Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
                if (!response.getCode().equals(200)) {
                    throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                            ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                }
                AllocateResRespModel.CustinsResRespModel custinsResRespModel = response.getData().getCustinsResRespModelList().get(0);

                avzSupport.updateAVZInfoByInstanceIds(avzInfo, custinsResRespModel.getInstanceIdList());
                custinsParamService.updateAVZInfo(custins.getId(),avzInfo);

                taskQueueParam.put("is_general", true);
                taskQueueParam.put("isUserCluster", true);
                dockerTaskInputParam.setAction(params.getAction());
                dockerTaskInputParam.setCharacterCustinsList(characterCustinsList);
                dockerTaskInputParam.setCustIns(logicIns);
                dockerTaskInputParam.setHostType(hostType);
                dockerTaskInputParam.setTaskQueueParam(taskQueueParam);
                dockerTaskInputParam.setMycnfCustinstancesMap(mycnfCustinstancesMap);
                dockerTaskInputParam.setOperatorId(params.getOperatorId());
                success = true;

                return dockerTaskInputParam;
            }


        }finally {
            if (!success) {
                if(custins.getInsType().equals(CUSTINS_INSTYPE_PRIMARY)){
                    if(parentCustins!=null && parentCustins.getInsType()!=null){
                        custinsService.deleteCustInstance(parentCustins);
                    }
                }
                if (custins.getId() != null) {
                    custinsService.deleteCustInstance(custins);
                }
                for (CustInstanceDO characterCustins : characterCustinsList) {
                    if (characterCustins.getId() != null) {
                        custinsService.deleteCustInstance(characterCustins);
                    }
                }
            }
        }
    }

    /**
     * 绑定用户指定的host instance角色
     */
    @Override
    public void bindHostInstanceRole(CustInstanceDO custins, Integer hostIdForMaster, Integer hostIdForSlave) {
        if (hostIdForMaster == null && hostIdForSlave == null) {
            logger.info("skip bindHostInstanceRole since master and slave id is null");
            return;
        }
        CustInstanceDO physicalCustins = custins.isLogic() ? mysqlParaHelper.getPhyscialCustinsByParentId(custins.getId()) : custins;
        List<InstanceDO> instanceDos = instanceService.getInstanceByCustinsId(physicalCustins.getId());
        // 处理只指定master/slave的情况, 此时hostIdForMaster, hostIdForSlave必有一个不为null
        for (InstanceDO instanceDo : instanceDos) {
            Integer hostId = instanceDo.getHostId();
            if (hostIdForMaster == null && !hostIdForSlave.equals(hostId)) {
                hostIdForMaster = hostId;
            } else if (hostIdForSlave == null && !hostIdForMaster.equals(hostId)) {
                hostIdForSlave = hostId;
            }
        }
        for (InstanceDO instanceDo : instanceDos) {
            Integer hostId = instanceDo.getHostId();
            Integer role = instanceDo.getRole();
            if (hostId.equals(hostIdForMaster) && !INSTANCE_ROLE_MASTER.equals(role)) {
                logger.info("bindHostInstanceRole reset role to master, host id=" + hostId);
                instanceService.updateInstanceRoleByInsId(instanceDo.getId(), INSTANCE_ROLE_MASTER);
            } else if (hostId.equals(hostIdForSlave) && !INSTANCE_ROLE_SLAVE.equals(role)) {
                logger.info("bindHostInstanceRole reset role to slave, host id=" + hostId);
                instanceService.updateInstanceRoleByInsId(instanceDo.getId(), INSTANCE_ROLE_SLAVE);
            }
        }
    }


}



