package com.aliyun.dba.dockerdefault.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.autoscale.entity.AutoScaleActionAuditLog;
import com.aliyun.dba.autoscale.service.AutoScaleAuditService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.task.dataobject.TransListDO;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * clouddba autoscale service
 */
@Service("autoScaleService")
public class AutoScaleService {

    private static final Logger logger = Logger.getLogger(AutoScaleService.class);


    @Autowired
    private AutoScaleAuditService autoScaleAuditService;

    /**
     * docker on ecs 系列, das autoscale触发变配
     * 记录任务id
     * */
    public void logForDasAutoScaleAction(String sourceBiz, String requestId,
                                         CustInstanceDO custins, Integer taskId,
                                         InstanceLevelDO oldLevel, InstanceLevelDO newLevel,
                                         TransListDO transList){

        if (!StringUtils.equalsIgnoreCase("AutoScale", sourceBiz)){
            return;
        }

        logger.warn("das modify, requestId=" + requestId +", cid=" + custins.getId() +", insName=" + custins.getInsName() + ",task");

        String utcNow = DateSupport.local2UTC(new Date());

        try{
            Map<String, Object> bizInfoMap = new HashMap<String, Object>();
            bizInfoMap.put("SrcClassCode", oldLevel.getClassCode());
            bizInfoMap.put("SrcStorage", custins.getDiskSize());
            bizInfoMap.put("DestClassCode", newLevel.getClassCode());
            bizInfoMap.put("DestStorage", transList.getdDisksize());

            String bizInfoStr = JSONObject.toJSONString(bizInfoMap);
            AutoScaleActionAuditLog auditLog = new AutoScaleActionAuditLog(requestId,
                    custins.getId(), custins.getInsName(), taskId, utcNow, bizInfoStr);
            logger.warn("das modify snapshot" + auditLog.getSnapShot());

            autoScaleAuditService.createAuditLog(auditLog);
        }catch (Exception e){
            logger.error("newAutoActionFail" , e);
        }
    }

    public AutoScaleActionAuditLog getDasAutoScaleActionTaskId(String requestId){
        AutoScaleActionAuditLog auditLog = null;
        try{
            auditLog = autoScaleAuditService.getAutoScaleAuditLog(requestId);
        }catch (Exception e){
            logger.error("getDasAutoScaleActionTaskId", e);
        }
        return auditLog;
    }
}
