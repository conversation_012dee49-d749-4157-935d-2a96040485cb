package com.aliyun.dba.dockerdefault.service;

import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.RdsResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsIpWhiteListDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.service.MaxscaleCustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("maxscaleEndpointImpl")
public class MaxscaleEndpointImpl {
    private static final Logger logger = Logger.getLogger(MaxscaleEndpointImpl.class);

    @Autowired
    private CustinsService custinsService;

    @Autowired
    private ClusterService clusterService;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private UserService userService;

    @Autowired
    private MaxscaleCustinsService maxscaleCustinsService;

    @Autowired
    private IpWhiteListService ipWhiteListService;

    @Autowired
    private AVZSupport avzSupport;

    public ResourceContainer addMaxscaleToResourceContainer(CustInstanceDO custins,
                                                            ResourceContainer resourceContainer,
                                                            String maxscaleInsName,
                                                            String region,
                                                            Integer netType,
                                                            String vpcId,
                                                            Integer tunnelId,
                                                            String vSwitchId,
                                                            String ipAddress,
                                                            String vpcInstanceId,
                                                            String ipWhiteList,
                                                            String connectionString,
                                                            Integer port,
                                                            String specifiedMaxsclaeClassCode) throws RdsException {
        List<CustinsServiceDO> custinsServiceDOList = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
        if (custinsServiceDOList.size() != 0) {
            logger.error("custins(" + custins.getInsName() + ") already got one maxscale instance: " + custinsServiceDOList.get(0).getServiceName());
            throw new RdsException(ErrorCode.MAXSCALE_ALREADY_EXIST);
        }

        String maxscaleDBVersion = maxscaleCustinsService.queryExpectMaxscaleDBVersion(custins);
        if (maxscaleDBVersion == null) {
            throw new RdsException(ErrorCode.MAXSCALE_NOT_SUPPORT);
        }

        if (StringUtils.isNotBlank(specifiedMaxsclaeClassCode) &&
                instanceService.getInstanceLevelByClassCode(specifiedMaxsclaeClassCode, "maxscale", maxscaleDBVersion, null, "logic") == null) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALE_SPECIFIC_CLASS);
        }

        InstanceLevelDO maxscaleInstanceLevel;
        if (custins.isMysql57DockerOnEcs()) {
            throw new RdsException(ErrorCode.MAXSCALE_NOT_SUPPORT);
        } else if (custins.isMysql()) {
            if (custins.getIsTmp() == 1){
                logger.warn("use specifiedMaxsclaeClassCode: "+specifiedMaxsclaeClassCode);
                maxscaleInstanceLevel = instanceService.getInstanceLevelByClassCode(specifiedMaxsclaeClassCode, "maxscale",
                        maxscaleDBVersion, null, "logic");
            } else {
                logger.warn("calculate maxscaleInstanceLevel by custins");
                Map<String, Object> params = new HashMap<>();
                params.put("targetVersion", maxscaleDBVersion);
                maxscaleInstanceLevel = maxscaleCustinsService.getMaxscaleLevelForRdsCustins(custins, params);
            }
        } else {
            maxscaleInstanceLevel = instanceService.getMappingInstanceLevel(custins, "maxscale", maxscaleDBVersion, "logic");
        }


        if (maxscaleInstanceLevel == null) {
            throw new RdsException(ErrorCode.MAPPING_INSTANCE_LEVEL_NOT_FOUND);
        }

        if (maxscaleInsName == null) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALE_INS_NAME);
        }

        String subDomain = region == null ? clusterService.getRegionByCluster(custins.getClusterName()) : region;
        CustinsResModel custinsResModel = null;
        Integer custinsResModelIndex;
        List<CustinsResModel> custinsResModelList = resourceContainer.getCustinsResModelList();
        for (custinsResModelIndex = 0; custinsResModelIndex < custinsResModelList.size() && custinsResModel == null; custinsResModelIndex++) {
            custinsResModel = custinsResModelList.get(custinsResModelIndex).getCustinsId().equals(custins.getId()) ? custinsResModelList.get(custinsResModelIndex) : null;
        }
        // custinsResModelIndex minus 1 because it has plus 1 before the loop ended.
        custinsResModelIndex--;

        if (custinsResModel == null) {
            logger.error("Can not find target custins(id: " + custins.getId() + ") in resourceContainer.CustinsResModelList.");
            throw new RdsException(ErrorCode.TARGET_CUSTINS_NOT_FOUND);
        }

        UserDO userDO = userService.getUserDOByUserId(custins.getUserId());
        if (userDO == null) {
            logger.error("Can not get user by user_id: " + custins.getUserId() + ", custins_id: " + custins.getId());
            throw new RdsException(ErrorCode.USER_NOT_FOUND);
        }
        String login_id = userDO.getLoginId();
        String bid = login_id.split("_", 2)[0];
        String uid = login_id.split("_", 2)[1];
        // TODO: use getWhiteListByCustinsIdListForProxy to get whitelist of custins
        if (ipWhiteList == null) {
            List<CustinsIpWhiteListDO> ipWhiteListGroupList =
                    ipWhiteListService.getCustinsIpWhiteList(custins.getId(), null, null, null);
            ipWhiteList = "";
            for (CustinsIpWhiteListDO ipWhiteListDO : ipWhiteListGroupList) {
                ipWhiteList = ipWhiteList + ipWhiteListDO.getIpWhiteList() + ",";
            }
            ipWhiteList = ipWhiteList.replace(" ", "").replaceAll(",*$", "");
        }

        if (connectionString == null) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALE_CONNECTION_STRING);
        }

        RdsResModel rdsResModel = new RdsResModel();
        rdsResModel.setRole("maxscale");
        rdsResModel.setUser_Id(bid);
        rdsResModel.setUid(uid);
        rdsResModel.setInsCount(1);
        rdsResModel.setdBInstanceClass(maxscaleInstanceLevel.getClassCode());
        rdsResModel.setRegion(subDomain);
        rdsResModel.setEngine(maxscaleInstanceLevel.getDbType());
        rdsResModel.setEngineVersion(maxscaleInstanceLevel.getDbVersion());
        rdsResModel.setdBInstanceNetType(netType.toString());
        rdsResModel.setDbInstanceName(maxscaleInsName);
        rdsResModel.setConnectionString(connectionString);
        rdsResModel.setPort(port);
        rdsResModel.setSecurityIPList(ipWhiteList);
        rdsResModel.setBackupRetentionPeriod(7);
        rdsResModel.setPreferredBackupTime("00:00Z");

        if (custins.isMysql()){
            AVZInfo dbAVZInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            String regionId = dbAVZInfo.getRegionId();
            String zoneId = dbAVZInfo.getMasterZoneId();
            rdsResModel.setRegionId(regionId);
            rdsResModel.setZoneId(zoneId);
            rdsResModel.setdBInstanceConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
        }

        if (CustinsSupport.isVpcNetType(netType)) {
            rdsResModel.setVpcId(vpcId);
            rdsResModel.setVswitchId(vSwitchId);
            if (!CustinsSupport.CONN_TYPE_PHYSICAL.equals(rdsResModel.getdBInstanceConnType())) {
                rdsResModel.setTunnelId(tunnelId);
                rdsResModel.setIpAddress(ipAddress);
                rdsResModel.setVpcInstanceId(vpcInstanceId);
            }
        }

        List<RdsResModel> rdsResModelList = custinsResModel.getRdsResModelList() != null ? custinsResModel.getRdsResModelList() : new ArrayList<RdsResModel>();
        rdsResModelList.add(rdsResModel);
        custinsResModel.setRdsResModelList(rdsResModelList);
        custinsResModelList.set(custinsResModelIndex, custinsResModel);
        resourceContainer.setCustinsResModelList(custinsResModelList);

        return resourceContainer;
    }

    public RdsResModel getMaxscaleRdsResModel(CustInstanceDO custins, ResourceContainer resourceContainer) {
        CustinsResModel custinsResModel = null;
        List<CustinsResModel> custinsResModelList = resourceContainer.getCustinsResModelList();
        for (int i = 0; i < custinsResModelList.size() && custinsResModel == null; i++) {
            custinsResModel = custinsResModelList.get(i).getCustinsId().equals(custins.getId()) ? custinsResModelList.get(i) : null;
        }
        if (custinsResModel == null) {
            logger.error("Can not get custins " + custins.getId() + "from resource container");
            return null;
        }
        List<RdsResModel> rdsResModelList = custinsResModel.getRdsResModelList() != null ? custinsResModel.getRdsResModelList() : new ArrayList<RdsResModel>();
        for (RdsResModel rdsResModel : rdsResModelList) {
            if (rdsResModel.getEngine().equals("maxscale")) {
                return rdsResModel;
            }
        }
        logger.error("Can not get maxscale RdsResModel.");
        return null;
    }
}
