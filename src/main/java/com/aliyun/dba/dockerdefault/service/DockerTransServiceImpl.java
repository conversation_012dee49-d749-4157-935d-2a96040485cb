package com.aliyun.dba.dockerdefault.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component("trancCusinsDockerOnEcsLogic")
public class DockerTransServiceImpl implements DockerTransService {

    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected InstanceIDao instanceIDao;

    @Autowired
    protected TaskService taskService;


    @Override
    public Integer transCustInstanceTaskOnEcs(String action, Integer operatorId, String taskKey,
                                              CustInstanceDO custins, TransListDO translist,
                                              String dbInstanceStatusDesc, String switchMode,
                                              String dbInstanceDiskStageType) throws Exception {
        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsSupport.CUSTINS_STATUS_TRANS, dbInstanceStatusDesc);
        this.instanceIDao.createTransList(translist);
        String taskparam = this.getTransTaskParameter(translist.getId(), switchMode, translist.getSwitchTime(),
                dbInstanceDiskStageType);
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, custins.getId(), TaskSupport.TASK_TYPE_CUSTINS, taskKey, taskparam);
        this.taskService.createTaskQueue(taskQueue);
        this.instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
        return taskQueue.getId();
    }

    // add task tag
    @Override
    public Integer transCustInstanceTaskOnEcsAddTag(String action, Integer operatorId, String taskKey,
                                                    CustInstanceDO custins, TransListDO translist,
                                                    String dbInstanceStatusDesc, String switchMode,
                                                    String dbInstanceDiskStageType, String taskTypeTag) throws Exception {
        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsSupport.CUSTINS_STATUS_TRANS, dbInstanceStatusDesc);
        this.instanceIDao.createTransList(translist);
        String taskparam = this.getTransTaskParameter(translist.getId(), switchMode, translist.getSwitchTime(),
                dbInstanceDiskStageType, taskTypeTag);
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, custins.getId(), TaskSupport.TASK_TYPE_CUSTINS, taskKey, taskparam);
        this.taskService.createTaskQueue(taskQueue);
        this.instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
        return taskQueue.getId();
    }

    public String getTransTaskParameter(Integer transId, String switchMode, Date switchTime,
                                        String dbInstanceDiskStageType) throws RdsException {
        Map<String, Object> taskparam = new HashMap();
        taskparam.put("trans_id", transId);
        Map<String, Object> effMap = this.custinsService.getEffectiveTimeMap(switchMode, switchTime);
        taskparam.put("switch_info", effMap);
        if (dbInstanceDiskStageType != null) {
            taskparam.put("target_data_disk_category", dbInstanceDiskStageType);
        }
        return JSON.toJSONString(taskparam);
    }

    public String getTransTaskParameter(Integer transId, String switchMode, Date switchTime,
                                        String dbInstanceDiskStageType, String taskTypeTag) throws RdsException {

        Map<String, Object> taskparam = new HashMap();
        taskparam.put("trans_id", transId);
        Map<String, Object> effMap = this.custinsService.getEffectiveTimeMap(switchMode, switchTime);
        taskparam.put("switch_info", effMap);
        if (dbInstanceDiskStageType != null) {
            taskparam.put("target_data_disk_category", dbInstanceDiskStageType);
        }
        if (taskTypeTag != null) {
            taskparam.put("task_type_tag", taskTypeTag);
        }
        return JSON.toJSONString(taskparam);
    }
}
