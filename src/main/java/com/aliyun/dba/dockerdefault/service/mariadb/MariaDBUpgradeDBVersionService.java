package com.aliyun.dba.dockerdefault.service.mariadb;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MariaDBMinorVersionHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service("mariaDBUpgradeDBVersionService")
public class MariaDBUpgradeDBVersionService {

    private static final Logger logger = Logger.getLogger(MariaDBUpgradeDBVersionService.class);

    private static final String TARGET_MINOR_VERSION_KEY = "target_minor_version";

    @Autowired
    private CustinsService custinsService;
    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    private TaskService taskService;
    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private MariaDBMinorVersionHelper mariaDBMinorVersionHelper;

    public Map<String, Object> upgradeDBVersion(CustInstanceDO custInstanceDO, Map<String, String> map) {
        Map<String, Object> result = new HashMap<>();

        // set param map by input
        ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

        CustInstanceDO custIns;
        String targetMinorVersion;

        try {
            custIns = mysqlParameterHelper.getAndCheckCustInstance();
        } catch (RdsException ex) {
            // can't find specified instance
            logger.error("MariaDB upgrade dbVersion: can't find custInstance", ex);
            return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
        }

        if (custIns == null) {
            return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        if (custIns.isReadAndWriteLock()) {
            return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }
        if (!custIns.isActive()) {
            return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);//实例状态错误
        }

        try {
            targetMinorVersion = mysqlParameterHelper.getTargetMinorVersion();
            if (targetMinorVersion == null) {
                if (mysqlParameterHelper.getMinorVersion() != null) {
                    targetMinorVersion = mysqlParameterHelper.getMinorVersion();
                } else {
                    logger.error("minor version and target minor version are both null");
                    throw new RdsException(ErrorCode.INVALID_PARAMETERS);
                }
            }
            targetMinorVersion = mariaDBMinorVersionHelper.getAndCheckTargetMinorVersion(custIns, targetMinorVersion);
        } catch (RdsException ex) {
            logger.error("MariaDB upgrade dbVersion: parse minor version failed", ex);
            return ResponseSupport.createErrorResponse(ex.getErrorCode());
        }

        // now only support minor version upgrade
        String targetTaskKey = TaskSupport.TASK_UPGRADE_MINOR_VERSION;

        // check if there is existing upgrade db version task for this instance
        Map<String, Object> condition = new HashMap<>();
        condition.put("custinsId", custIns.getId());
        condition.put("taskKey", targetTaskKey);
        condition.put("status", TaskSupport.TASK_RUNNING_STATUS);
        if (taskService.countTaskQueueByCondition(condition) > 0) {
            return ResponseSupport.createErrorResponse(ErrorCode.TASK_HAS_EXIST);
        }

        // start initializing task queue param
        Map<String, Object> taskQueueParam = new HashMap<>(5);
        taskQueueParam.put(TARGET_MINOR_VERSION_KEY, targetMinorVersion);

        // parse and set effective time
        try {
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(map);
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(map, utcDate, true);
            Map<String, Object> effMap = custinsService.getEffectiveTimeMapTimeZoneSafe(switchMode, utcDate);
            taskQueueParam.put(CustinsSupport.SWITCH_KEY, effMap);
        } catch (RdsException ex) {
            logger.error("MariaDB upgrade dbVersion: failed to initialize effective time", ex);
            return ResponseSupport.createErrorResponse(ex.getErrorCode());
        }

        // construct pengine task
        Integer taskId;
        try {
            taskId = instanceService.upgradeMinorVersionTask(mysqlParameterHelper.getAction(), custIns, taskQueueParam, mysqlParameterHelper.getOperatorId());
        } catch (RdsException ex) {
            logger.error("MariaDB upgrade dbVersion: failed to initialize pengine task", ex);
            return ResponseSupport.createErrorResponse(ex.getErrorCode());
        }

        // pass in pengine version policy info
        taskService.updateTaskPenginePolicy(taskId, mysqlParameterHelper.getPenginePolicyID());

        // build result map
        result.put("DBInstanceID", custIns.getId());
        result.put("DBInstanceName", custIns.getInsName());
        result.put("TargetMinorVersion", targetMinorVersion);
        result.put("TaskId", taskId);

        // clear param map
        ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        return result;
    }
}
