package com.aliyun.dba.dockerdefault.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.response.EvaluateResRespModel;
import com.alicloud.apsaradb.resmanager.response.EvaluateUpgradeResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.CloudEssdEnums;
import com.aliyun.dba.base.support.EcsDiskDefaultCategory;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.pojo.ShardsInfo;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dockerdefault.action.ModifyDBInstanceClassImpl;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.dataobject.EcsDiskDO;
import com.aliyun.dba.ecs.dataobject.RdsRegionDO;
import com.aliyun.dba.ecs.service.*;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelRelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.dataobject.RegionAVZonDO;
import com.aliyun.dba.resource.dataobject.RegionAVZoneQuery;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_TRANS_TYPE_LOCAL;

@Component
public class DockerCommonService {

    private static final LogAgent logger = LogFactory.getLogAgent(DockerCommonService.class);
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected ResApi resApi;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected EcsService ecsService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected RegionService regionService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ModifyDBInstanceClassImpl modifyDBInstanceClass;
    @Resource
    protected EcsDiskDefaultCategory ecsDiskDefaultCategory;
    @Autowired
    protected CustInstanceDBService custInstanceDBService ;
    @Autowired
    protected EcsDBService ecsDBService;
    @Autowired
    private InstanceIDao instanceIDao;
    @Autowired
    private EcsDiskService ecsDiskService;

    /**
     *  入参没有传递 zoneId, 使用实例集群查
     *  资源评估, 关键参数
     *  预期:
     *      生产实例, 传递 zoneId
     *      变配, 没有传递zoneId
     */
    public String getZoneIdByCustins(CustInstanceDO custins){
        if (StringUtils.isBlank(custins.getClusterName())){
            return "";
        }
        String zoneId = "";
        try{
            zoneId =  clusterService.getZoneIdByClusterName(custins.getClusterName());
        }catch (Exception e){
            logger.error("getZoneIdExcepClusterName=" + custins.getClusterName(), e);
        }
        return zoneId;
    }

    /**
     * 入参没有传递 vswitch, 从实例数据链路获得
     * 资源评估, 非关键参数
     */
    public String getVswitchIdByCustins(CustInstanceDO custins){
        if (StringUtils.isBlank(custins.getClusterName())){
            return "fakeVswitchId";
        }
        String vSwitchId = "";
        try{
            vSwitchId = custinsService.getVswitchIdWithConnByCustinsId(custins.getId());
        }catch (Exception e){
            logger.error("getVswitchIdByCustinsId=" + custins.getId(), e);
        }
        return vSwitchId;
    }

    /**
     *  custins, physcial层面实例
     *  关键参数: zoneId
     */
    public String buildMultiAVZParamStr(String zoneId, String vSwitchId){

        if (StringUtils.isBlank(zoneId)){
            return "";
        }

        try{
            /**
             * {"availableZoneInfoList":[
             *  {"role":"master","vSwitchID":"vsw-xxxecvbiseohe0307v2q8","zoneID":"cn-shanghai-et15-b01"},
             *  {"role":"slave","vSwitchID":"vsw-xxxecvbiseohe0307v2q8","zoneID":"cn-shanghai-et15-b01"}
             * ]}
             */
            List<Map<String, String>> infoList = new ArrayList<Map<String, String>>(2);
            Map<String, String> masterInfoMap = new HashMap<String, String>(3);
            Map<String, String> slaveInfoMap = new HashMap<String, String>(3);

            masterInfoMap.put("role", "master");
            masterInfoMap.put("vSwitchID", vSwitchId);
            masterInfoMap.put("zoneID", zoneId);
            infoList.add(masterInfoMap);

            slaveInfoMap.put("role", "slave");
            slaveInfoMap.put("vSwitchID", vSwitchId);
            slaveInfoMap.put("zoneID", zoneId);
            infoList.add(slaveInfoMap);

            Map<String, List<Map<String, String>>> multiAvzMap = new HashMap<String, List<Map<String, String>>>(1);
            multiAvzMap.put("availableZoneInfoList", infoList);
            return JSONObject.toJSONString(multiAvzMap);
        }catch (Exception e){
            logger.error("excepBuildMultiAvz", e);
        }

        return "";
    }

    /**
     * 判断只读实例磁盘大小是否小于主实例
     * custins
     * storage 磁盘大小， 单位必须为Gb
     */
    public boolean checkReadOnlyDiskSize(CustInstanceDO custins, String storage) throws RdsException{
        if (custins.isRead() && StringUtils.isNotEmpty(storage)){
            if (custins.getPrimaryCustinsId().equals(0)){
                //dhg 只读也有为0
                return true;
            }
            Long diskSize = Long.parseLong(storage);
            CustInstanceDO parentCustInstance = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            if (parentCustInstance != null && ((parentCustInstance.getDiskSize()/1024) > diskSize)){
                throw new RdsException(ErrorCode.UNSUPPORTED_READ_DBINSTANCE_DISKSIZE);
            }
        }
        return true;
    }

    /**
     * 判断是否使用老模式资源评估
     * NewCloudEvaluate 的值为 close 时, 旧版资源评估
     */
    public boolean useNewEvaluateMode(){
        ResourceDO resourceDO = resourceService.getResourceByResKey(RdsConstants.NEW_CLOUD_EVALUATE_RESKEY);
        if (resourceDO != null && resourceDO.getRealValue().equalsIgnoreCase(RdsConstants.NEW_CLOUD_EVALUATE_VALUE_CLOSE)){
            return false;
        }
        return true;
    }

    /**
     * 返回资源评估结果
     * @param resultCode
     * @param custins
     * @return
     */
    public Map<String, Object> evaluateResultMap(Integer resultCode, CustInstanceDO custins){
        String dbType = custins.getDbType();
        String dbVersion = custins.getDbVersion();
        Map<String, Object> data = new HashMap<String, Object>();
        data.put(ParamConstants.DB_INSTANCE_AVAILABLE, resultCode);
        data.put(ParamConstants.ENGINE_VERSION, dbVersion);
        data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(dbType));
        return data;

    }

    public boolean checkEcsClassCodeCanAttchEssd(CustInstanceDO custins) {
        try {
            if (!custins.isCustinsDockerOnEcs()) {
                return true;
            }

            List<CustInstanceDO> physicalCustInstances = custinsService.getCustInstanceByParentId(custins.getId());
            CustInstanceDO physicalCustins = physicalCustInstances.stream().filter(i -> i.getIsTmp() == 0).findFirst().orElse(custins);
            List<InstanceDO> instances = instanceIDao.getInstanceByCustinsId(physicalCustins.getId());
            ResourceDO limitEcsClassStr = resourceService.getResourceByResKey("INSTANCE_CLASS_NOT_ALLOW_CLOUD_ESSD");
            if (StringUtils.isEmpty(limitEcsClassStr.getRealValue())) {
                return true;
            }
            Set<String> limitClassList = Sets.newHashSet(limitEcsClassStr.getRealValue().split(","));
            for (InstanceDO instanceDO : instances) {
                EcsHostDetailDO ecsInfo = ecsDBService.getEcsHostDetailDOByHostId(instanceDO.getHostId());
                if (ecsInfo != null && StringUtils.isNotEmpty(ecsInfo.getEcsClassCode()) && limitClassList.contains(ecsInfo.getEcsClassCode())) {
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("checkEcsDiskCategory error, {}", e.getMessage(), e);
        }
        return true;
    }

    /**
     * 检查主机磁盘是否为给定diskCategory磁盘类型
     * @param diskCategory
     * @param custins
     * @return
     */
    public boolean matchEcsDiskCategory(CustInstanceDO custins, String diskCategory) {
        try {
            logger.info("matchEcsDiskCategory " + diskCategory);
            List<CustInstanceDO> physicalCustInstances = custinsService.getCustInstanceByParentId(custins.getId());
            CustInstanceDO physicalCustins = physicalCustInstances.stream().filter(i -> i.getIsTmp() == 0).findFirst().orElse(custins);
            List<EcsDiskDO> ecsDisks = ecsDiskService.getEcsDiskByCustinsId(physicalCustins.getId());
            for (EcsDiskDO ecsDisk: ecsDisks) {
                if (!ecsDisk.getCategory().equalsIgnoreCase(diskCategory)){
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("matchEcsDiskCategory error, {}", e.getMessage(), e);
        }
        return true;
    }

    /**
     * MySQL on docker on ECS 评估方法，迁移自DockerAdapter中
     *  MySQL 57 on Docker on Ecs 高可用版
     *  MySQL 8.0 on Docker on Ecs 基础版,高可用版
     *  上游业务入口
     *      新购实例: insName和targetClass对齐
     *      实例变配:
     *          云盘原主:insName physical, targetClass logic
     *          云盘只读:insName physical, targetClass physical
     *  返回结果中 , DBInstanceAvailable=1 表示有资源
     *  其它表示没有资源
     */
    public Map<String, Object> evaluate(CustInstanceDO custins) throws RdsException {
        Map<String,String> params = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        String storageType = mysqlParamSupport.getAndCheckStorageType(params);

        String region = avzSupport.getMainLocation(params);
        String clusterName = mysqlParaHelper.getParameterValue(ParamConstants.CLUSTER_NAME);
        String hostType = mysqlParaHelper.getAndCheckHostType();
        String storage = mysqlParaHelper.getParameterValue(ParamConstants.STORAGE);
        String actionName = mysqlParaHelper.getParameterValue(ParamConstants.ACTION);
        Integer insCount = Integer.parseInt(mysqlParaHelper.getParameterValue(ParamConstants.INS_COUNT,"1"));

        if (StringUtils.isBlank(storageType)) {
             storageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
        }

        InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());

        Set<Integer> hostIdSet = new HashSet<>();
        boolean isUserCluster = false;
        if (StringUtils.isNotBlank(clusterName)) { // 仅在输入集群名时HostId参数才有效
            custins.setClusterName(clusterName);
            hostIdSet = mysqlParaHelper.getAndCheckHostIdSet();
            isUserCluster = mysqlEngineCheckService.checkUserClusterCustins(custins);
        }
        // todo 专享主机组, 如果需要评估的规格是物理实例, 则默认是只读实例 后续拓扑有改动 这里有坑 需要改动.. 因为目前inslevel 没有判断是否是只读实例的这种标记
        if (isUserCluster && insLevel.isMySQLPhysical()) {
            custins.setInsType(CustinsSupport.CUSTINS_INS_TYPE_READINS);
        }

        checkReadOnlyDiskSize(custins, storage);

        //支持不评估资源的action
        List<String> modifyActionList=Arrays.asList("ModifyDBInstanceClass","EvaluateModifyRegionResource");
        if (modifyActionList.contains(actionName)){
            String targetDBInstanceClass = mysqlParaHelper.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS);
            InstanceLevelDO targetInsLevel = instanceService.getInstanceLevelByClassCode(targetDBInstanceClass,
                    custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);

            if (targetInsLevel == null || StringUtils.equals(insLevel.getClassCode().replace(".physical", ""), targetDBInstanceClass.replace(".physical", ""))) {
                //这里对不能挂载essd的变盘行为进行拦截
                if (StringUtils.equalsIgnoreCase(DockerOnEcsConstants.ECS_ClOUD_ESSD, storageType) &&
                        matchEcsDiskCategory(custins, DockerOnEcsConstants.ECS_ClOUD_SSD)) {
                    Map<String, Object> data = evaluateResultMap(0, custins);
                    if (insLevel.isBasicLevel()){
                        throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
                    }
                    if (!checkEcsClassCodeCanAttchEssd(custins)){
                        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                    }
                }

                //原逻辑
                Map<String, Object> data = new HashMap<String, Object>();
                data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                data.put(ParamConstants.ENGINE_VERSION, insLevel.getDbVersion());
                data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(insLevel.getDbType()));
                return data;
            }else {
                insLevel = targetInsLevel;
            }
        }

        if (custins.isPhysicalChild() && (insLevel.isMySQLLogic() || insLevel.isMariaDBLogic())){
            List<InstanceLevelRelDO> instanceLevelRels = instanceService.getInstanceLevelRelByParentLevelId(insLevel.getId(), null);
            if (instanceLevelRels != null && instanceLevelRels.size() > 0){
                InstanceLevelDO characterInsLevel = instanceService.getInstanceLevelByLevelId(instanceLevelRels.get(0).getCharacterLevelId());
                if (characterInsLevel != null){
                    insLevel = characterInsLevel;
                }
            }
        }

        Integer bizType = mysqlParaHelper.getAndCheckBizType();
        String connType = mysqlParaHelper.getAndCheckConnType(null);
        if (connType == null) {
            connType = CustinsSupport.CONN_TYPE_LVS;
        }
        custins.setConnType(connType);

        Integer netType = mysqlParaHelper.getAndCheckNetType();
        custins.setNetType(netType);

        // 以下参数仅对创建基于ECS的实例有效
        String regionId = null;
        String zoneId =  mysqlParaHelper.getParameterValue(ParamConstants.ZONE_ID);
        if (StringUtils.isBlank(zoneId)){
            // 入参没有传递ecs zoneid ,查看关联实例
            zoneId = getZoneIdByCustins(custins);
        }
        String vSwitchId = mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID);
        if (StringUtils.isBlank(vSwitchId)){
            // 入参没有传递vswitch, 查看实例 cca, ip_resource
            vSwitchId = getVswitchIdByCustins(custins);
        }

        String multiAVZExParamStr = mysqlParaHelper.getParameterValue(ParamConstants.MULTI_AVZ_EX_PARAM);
        if (custins.isCustinsDockerOnEcs() && !isUserCluster) {
            if (StringUtils.isBlank(multiAVZExParamStr)) {
                String dbTypeForCluster = custins.getDbTypeForCluster();
                if (clusterService.checkRegionWithEcs(region, dbTypeForCluster)) {
                    if (useNewEvaluateMode()){
                        if (StringUtils.isBlank(zoneId)){
                            // 新版评估场景下 zoneId, mulAVZ都为空, 按照旧版评估, 返回有资源
                            return evaluateResultMap(1, custins);
                        }
                    }else {
                        // 旧版资源评估模式
                        return evaluateResultMap(1, custins);
                    }
                } else {

                    // 集群不支持docker
                    return evaluateResultMap(0, custins);
                }
            }
            regionId = mysqlParaHelper.getAndCheckRegionID();
        }



        if (isUserCluster) {
            regionId = mysqlParaHelper.getAndCheckRegionID();
        }

        if (isUserCluster && CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_DEFAULT.equals(hostType)) {
            hostType = custins.isCustinsOnDockerOnEcsLocalSSD() ? CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD: CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD;
        }

        // 以下参数仅对创建基于Polar或NC的实例有效: init shardsInfoMap.
        String shardsInfo = null;
        Map<String, List<ShardsInfo.Node>> nodesMap = new HashMap<String, List<ShardsInfo.Node>>();
        if (custins.isCustinsDockerOnNC()) {
            shardsInfo = mysqlParaHelper.getParameterValue(ParamConstants.SHARDS_INFO);
            nodesMap = mysqlParaHelper.SplitNodesFromShardsInfobyDbTypeAndDbVersion(shardsInfo);
        }

        boolean evaluateSuccess = false;
        Object evaluateDetails = null;
        Map<String, Object> data = new HashMap<String, Object>();
        if(isUserCluster && modifyActionList.contains(actionName)){
            //指定迁移主机
            Set<Integer> specifyHostIdSet = new HashSet<>();
            String dedicatedHostNamesJson = CustinsParamSupport.getParameterValue(params, "DedicatedHostNames");
            String targetDedicatedHostIdForMaster = null;
            if (!StringUtils.isEmpty(dedicatedHostNamesJson)) {
                // 如果传DedicatedHostNames，使用DedicatedHostNames对应的hostids
                Map<String, Object> dedicatedHostNames = JSON.parseObject(dedicatedHostNamesJson);
                targetDedicatedHostIdForMaster = (String) dedicatedHostNames.get("TargetDedicatedHostIdForMaster");
                if (StringUtils.isBlank(targetDedicatedHostIdForMaster)) {
                    logger.error("TargetDedicatedHostIdForMaster must be input.");
                    throw new RdsException(ErrorCode.INVALID_PARAM);
                }
                specifyHostIdSet = modifyDBInstanceClass.getAndCheckHostIdsByDedicatedHostNames(dedicatedHostNames, clusterName, null);
            } else {
                specifyHostIdSet = CustinsParamSupport.getAndCheckHostIdSet(params);
            }

            Long diskSize;
            Integer maxDiskSize = resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
            if (StringUtils.isNotEmpty(storage)) {
                diskSize =
                        CheckUtils.parseInt(storage, 5, maxDiskSize, ErrorCode.INVALID_STORAGE)
                                * 1024L;
            } else {
                // ecs磁盘空间可能和物理机范围不一样，超过物理机磁盘空间的ecs不允许迁移物理机
                diskSize = custins.getDiskSize();
                if (diskSize > maxDiskSize * 1024) {
                    return createErrorResponse(ErrorCode.INVALID_STORAGE);
                }
            }
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            if(!avzInfo.isValidForModify()){
                avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            }
            UpgradeResContainer upgradeResContainer = avzSupport.getUpgradeResContainer(avzInfo, specifyHostIdSet.stream().findFirst().orElse(null));
            upgradeResContainer.setRequestId(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID));
            upgradeResContainer.setClusterName(clusterName);
            upgradeResContainer.setPreferClusterName(custins.getClusterName());
            upgradeResContainer.setAccessId(CustinsParamSupport.getParameterValue(params, ParamConstants.ACCESSID));
            upgradeResContainer.setOrderId(CustinsParamSupport.getParameterValue(params, ParamConstants.ORDERID));

            // init custins res model
            UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
            custinsResModel.setCustinsId(custins.getId());

            custinsResModel.setCustinsId(custins.getId());

            // init host ins res model
            HostinsResModel hostinsResModel = new HostinsResModel(insLevel.getId());
            hostinsResModel.setHostType(insLevel.getHostType());
            hostinsResModel.setInsCount(insLevel.isMysqlEnterprise() ? 3 : 2);
            hostinsResModel.setDiskSizeSold(diskSize);
            hostinsResModel.setDiskType(custins.isCustinsOnDockerOnEcsLocalSSD()?99:2);

            hostinsResModel.setTransType(Integer.valueOf(getAndCheckTransType(params)));
            // get disk size used
            try {
                InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
                hostinsResModel.setDiskSizeUsed(new BigDecimal(instancePerf.getDiskCurr()).longValue());
            } catch (Exception e) {
                logger.error("Get instance perf failed for custins: " + custins.getId(), e);
                hostinsResModel.setDiskSizeUsed(custins.getDiskSize());
            }

            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            distributeRule.setSiteDistributeMode(hostinsResModel.getInsCount() > 3 ? DistributeMode.AVG_SCATTER:DistributeMode.FORCE_SCATTER);
            distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setSpecifyHostIdSet(specifyHostIdSet);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, insLevel.getExtraInfo());

            hostinsResModel.setDistributeRule(distributeRule);

            custinsResModel.setHostinsResModel(hostinsResModel);
//            custinsResModel.setVipResModelList(vipResModelList);  //TODO: 评估vip资源，可暂时不做
            upgradeResContainer.addUpgradeCustinsResModel(custinsResModel);
            Response<EvaluateUpgradeResRespModel> response = resApi.evaluateUpgradeRes(upgradeResContainer);
            evaluateSuccess = response.getCode().equals(200);
            if (evaluateSuccess) {
                evaluateDetails = response.getData().getCustinsResRespModelList();
                if (response.getData().getCustinsResRespModelList().get(0).getIsLocalUpgrade() == 0) {
                    data.put(ParamConstants.DB_INSTANCE_TRANS_TYPE, CUSTINS_TRANS_TYPE_REMOTE);
                } else {
                    data.put(ParamConstants.DB_INSTANCE_TRANS_TYPE, CUSTINS_TRANS_TYPE_LOCAL);
                }
            } else {
                evaluateDetails = ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response);
            }
        }else {
            // init resource container
            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(params,
                    CustinsSupport.CONTAINER_TYPE_DOCKER, isUserCluster, null);
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));

            packageResourceContainer(resourceContainer, custins, insLevel, hostType, storage, bizType, hostIdSet,
                    regionId, zoneId, multiAVZExParamStr, nodesMap, netType, connType, storageType, isUserCluster, insCount);
            resourceContainer.setAccessId(getParameterValue(params, ParamConstants.ACCESSID));

            //调用资源API
            Response<EvaluateResRespModel> response = resApi.evaluateRes(resourceContainer);
            evaluateSuccess = response.getCode().equals(200);
            evaluateDetails = evaluateSuccess?response.getData().getSudomainDetails():ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response);
            if(evaluateSuccess) {
                data.put(ParamConstants.SUBDOMAIN_AVAILABLE_DETAIL, evaluateDetails);
            }
        }
        if (evaluateSuccess) {
            data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
        } else {
            data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
            data.put(ParamConstants.ERROR_MESSAGE, evaluateDetails);
        }
        data.put(ParamConstants.ENGINE_VERSION, insLevel.getDbVersion());
        data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(insLevel.getDbType()));
        return data;
    }

    /**
     * 资源评估
     * @param resourceContainer
     * @param custins
     * @param insLevel
     * @param hostType
     * @param storage
     * @param bizType
     * @param hostIdSet
     * @param regionId
     * @param zoneId
     * @param multiAVZExParamStr
     * @param nodesMap
     * @param netType
     * @param connType
     * @param storageType
     * @throws RdsException
     */
    public void packageResourceContainer(ResourceContainer resourceContainer,
                                         CustInstanceDO custins, InstanceLevelDO insLevel, String hostType,
                                         String storage, Integer bizType, Set<Integer> hostIdSet, String regionId,
                                         String zoneId, String multiAVZExParamStr,
                                         Map<String, List<ShardsInfo.Node>> nodesMap, Integer netType, String connType,
                                         String storageType, boolean isUserCluster, Integer insCount)
        throws RdsException {

        String composeTag = mysqlParaHelper.selectComposeTag(custins.getClusterName(), isUserCluster);

        EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(custins.getDbType(),
                custins.getDbVersion(), insLevel.getCategory(), composeTag);
        JSONObject jsonServices = JSON.parseObject(engineCompose.getServices());
        if (custins.isLogic()) {
            // init node instance info
            List<InstanceLevelRelDO> instanceLevelRels = instanceService
                .getInstanceLevelRelByParentLevelId(insLevel.getId(), null);
            if (instanceLevelRels.size() <= 0) {
                logger.error("No instance level relation found for level id: " + insLevel.getId());
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }

            for (InstanceLevelRelDO rel : instanceLevelRels) {
                DockerInsLevelParseConfig config = null;
                InstanceLevelDO characterInsLevel = instanceService.getInstanceLevelByLevelId(rel.getCharacterLevelId());
                EngineService engineService = new Gson().fromJson(
                    jsonServices.getString(characterInsLevel.getDbType()), EngineService.class);

                if (CustinsSupport.isRdsSrvDockerize(engineService)) {
                    config = custinsService.parseDockerInsExtraInfo(characterInsLevel.getExtraInfo());
                    String uniqueKey = characterInsLevel.getDbType() + characterInsLevel.getDbVersion();
                    for (int i = 0; i < rel.getCharacterCustinsCount(); i++) {
                        CustInstanceDO characterCustins = custins.clone();
                        if (null != engineService.getInsType()) {
                            CustInsType custInsType = CustInsType.getByName(engineService.getInsType());
                            characterCustins.setInsType(custInsType.getValue());
                        }

                        if (custins.isCustinsDockerOnEcs() && !isUserCluster) {
                            constructResourceContainerForCustinsOnDockerOnEcs(storage, bizType,
                                regionId, zoneId, multiAVZExParamStr, resourceContainer,
                                characterInsLevel, config, characterCustins, storageType, isUserCluster);
                        } else if (isUserCluster) {
                            constructResourceContainerForDHGCustins(storage, bizType,
                                    regionId, zoneId, multiAVZExParamStr, resourceContainer,
                                    characterInsLevel, config, characterCustins, storageType, hostType, insCount);
                        } else {
                            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                        }
                    }
                } else {
                    RequestParamsDO params = mysqlParaHelper.inflateDockerParamsWhenEvaluate(custins);
                    constructDependentResourceContainerForCustins(custins, characterInsLevel,
                        netType, resourceContainer, params);
                }
            }
            // init logic custins resource model
            CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
            custinsResModel.setConnType(connType);
            // put into resource container
            resourceContainer.addCustinsResModel(custinsResModel);
        }else{
            DockerInsLevelParseConfig config = null;
            InstanceLevelDO characterInsLevel = insLevel;
            EngineService engineService = new Gson().fromJson(
                jsonServices.getString(characterInsLevel.getDbType()), EngineService.class);

            if (CustinsSupport.isRdsSrvDockerize(engineService)) {
                config = custinsService.parseDockerInsExtraInfo(characterInsLevel.getExtraInfo());
                String uniqueKey = characterInsLevel.getDbType() + characterInsLevel.getDbVersion();
                CustInstanceDO characterCustins = custins.clone();
                if (null != engineService.getInsType()) {
                    CustInsType custInsType = CustInsType.getByName(engineService.getInsType());
                    characterCustins.setInsType(custInsType.getValue());
                }

                if (custins.isCustinsDockerOnEcs() && !isUserCluster) {
                    constructResourceContainerForCustinsOnDockerOnEcs(storage, bizType,
                        regionId, zoneId, multiAVZExParamStr, resourceContainer,
                        characterInsLevel, config, characterCustins, storageType, isUserCluster);
                } else if (isUserCluster) {
                    constructResourceContainerForDHGCustins(storage, bizType,
                            regionId, zoneId, multiAVZExParamStr, resourceContainer,
                            characterInsLevel, config, characterCustins, storageType, hostType, insCount);
                } else {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
            } else {
                RequestParamsDO params = mysqlParaHelper.inflateDockerParamsWhenEvaluate(custins);
                constructDependentResourceContainerForCustins(custins, characterInsLevel,
                    netType, resourceContainer, params);
            }
        }
    }

    private void constructResourceContainerForDHGCustins(String storage,
                                                         Integer bizType,
                                                         String regionId,
                                                         String zoneId,
                                                         String multiAVZExParamStr,
                                                         ResourceContainer resourceContainer,
                                                         InstanceLevelDO characterInsLevel,
                                                         DockerInsLevelParseConfig config,
                                                         CustInstanceDO characterCustins,
                                                         String storageType,
                                                         String hostType,
                                                         Integer insCount) throws RdsException {
        // for ecs min 20G
        if (Validator.isNotNull(storage)) {
            if (Integer.parseInt(storage) != 0) {
                Integer minDiskSize = characterCustins.isCustinsOnDockerOnEcsLocalSSD() ? CustinsSupport.NC_MIN_DISK_SIZE : CustinsSupport.ECS_MIN_DISK_SIZE;
                CustinsSupport.setDiskSize(characterCustins, bizType, storage,
                        minDiskSize);
            } else {
                characterCustins.setDiskSize(0L);
            }
        } else {
            characterCustins.setDiskSize(characterInsLevel.getDiskSize());
        }

        CustinsResModel custinsResModel = new CustinsResModel(characterCustins.getId());
        HostinsResModel hostinsResModel = new HostinsResModel(characterInsLevel.getId());
        hostinsResModel.setInsCount(2);
        if("general".equalsIgnoreCase(characterInsLevel.getCategory())){
            hostinsResModel.setInsCount(insCount);
        }
        hostinsResModel.setHostType(Integer.valueOf(hostType));
        // init distribute rule
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        if (RdsConstants.CUSTINS_INSTYPE_READ.equals(characterCustins.isRead())) {
            // 只读实例 需要机房集中 在同个可用区内
            distributeRule.setSiteDistributeMode(DistributeMode.FORCE_CROWD);
            String siteName = clusterService.getSiteNameByAvzone(zoneId);
            distributeRule.setSpecifySiteNameSet(new HashSet<String>(){{add(siteName);}});
        } else {
            distributeRule.setSiteDistributeMode(DistributeMode.TRY_SCATTER);
        }
        distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
        distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
        InsLevelExtraInfo.updateDistributeRule(distributeRule, characterInsLevel.getExtraInfo());
        // 指定的host ids
//        distributeRule.setSpecifyHostIdSet(CustinsParamSupport.getAndCheckHostIdSet(actionParams));
        hostinsResModel.setDistributeRule(distributeRule);
        if (characterCustins.isCustinsOnDockerOnEcsLocalSSD()) {
            hostinsResModel.setDiskType(99);
        } else {
            hostinsResModel.setDiskType(2);
        }
        custinsResModel.setHostinsResModel(hostinsResModel);
        resourceContainer.addCustinsResModel(custinsResModel);
        if (config.getVipCount() > 0) {
            custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
        } else {
            custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
        }

        resourceContainer.addCustinsResModel(custinsResModel);
        /**
        String ecsAccount;
        String ecsClass = characterInsLevel.getEcsClassCode();
        RdsRegionDO rdsRegionDO =  regionService.getRegion(regionId);
        ecsAccount = rdsRegionDO.getUserName();

        EcsResModel ecsResModel = initSingleEcsResModule(ecsAccount,
                characterInsLevel.getInsCount(), regionId, zoneId, ecsClass, config,
                characterInsLevel.getId(), characterCustins.getDiskSize(), storageType);
        // set host res model
        custinsResModel.setEcsResModel(ecsResModel);
        // put into resource container
        resourceContainer.addCustinsResModel(custinsResModel);
         **/
    }

    /**
     * 资源评估
     * @param storage
     * @param bizType
     * @param regionId
     * @param zoneId
     * @param multiAVZExParamStr
     * @param resourceContainer
     * @param characterInsLevel
     * @param config
     * @param characterCustins
     * @throws RdsException
     */
    @SuppressWarnings("deprecation")
    private void constructResourceContainerForCustinsOnDockerOnEcs(String storage,
                                                                   Integer bizType,
                                                                   String regionId,
                                                                   String zoneId,
                                                                   String multiAVZExParamStr,
                                                                   ResourceContainer resourceContainer,
                                                                   InstanceLevelDO characterInsLevel,
                                                                   DockerInsLevelParseConfig config,
                                                                   CustInstanceDO characterCustins,
                                                                   String storageType,
                                                                   boolean isUserCluster)
        throws RdsException {
        // for ecs min 20G
        if (Validator.isNotNull(storage)) {
            if (Integer.parseInt(storage) != 0) {
                CustinsSupport.setDiskSize(characterCustins, bizType, storage,
                    CustinsSupport.ECS_MIN_DISK_SIZE);
            } else {
                characterCustins.setDiskSize(0L);
            }
        } else {
            characterCustins.setDiskSize(characterInsLevel.getDiskSize());
        }

        CustinsResModel custinsResModel = new CustinsResModel(characterCustins.getId());
        if (config.getVipCount() > 0) {
            custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
        } else {
            custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
        }

        if (characterCustins.isCustinsOnDockerOnEcsLocalSSD()) {
            resourceContainer.addCustinsResModel(custinsResModel);
            return;
        }
        String ecsAccount;
        String ecsClass = characterInsLevel.getEcsClassCode();
        if (isUserCluster) {
            RdsRegionDO rdsRegionDO =  regionService.getRegion(regionId);
            ecsAccount = rdsRegionDO.getUserName();
        } else {
            // Get ecsAccount, vpcId, vSwitchId
            ecsAccount = ecsService.getEcsAccount(characterCustins.getUserId(), regionId);
        }

        if (characterCustins.isMbaseSql()) {
            if (StringUtils.isEmpty(multiAVZExParamStr)){
                EcsResModel ecsResModel = initSingleEcsResModule(ecsAccount,
                    characterInsLevel.getInsCount(), regionId, zoneId, ecsClass, config,
                    characterInsLevel.getId(),characterCustins.getDiskSize(), storageType);

                custinsResModel.setEcsResModel(ecsResModel);
            }else {
                MultiAVZExParamDO multiAVZExParam = JSON.parseObject(multiAVZExParamStr,
                    MultiAVZExParamDO.class);
                //采用双可用区的参数设置
                List<EcsResModel> ecsResModelList = new ArrayList<EcsResModel>();
                for (AvailableZoneInfoDO ecsAvzInfo : multiAVZExParam.getAvailableZoneInfoList()) {
                    EcsResModel ecsResModel = initSingleEcsResModule(ecsAccount, 1, regionId,
                        ecsAvzInfo.getZoneID(), ecsClass, config, characterInsLevel.getId(),
                        characterCustins.getDiskSize(), storageType);
                    ecsResModelList.add(ecsResModel);
                }
                custinsResModel.setEcsResModelList(ecsResModelList);
            }
        } else if (characterCustins.isHiTSDBService()) {
            // HiTSDB不需要磁盘
            EcsResModel ecsResModel = initSingleEcsResModule(ecsAccount,
                characterInsLevel.getInsCount(), regionId, zoneId, ecsClass, config,
                characterInsLevel.getId(), 0L, storageType);
            // set host res model
            custinsResModel.setEcsResModel(ecsResModel);
        } else {
            EcsResModel ecsResModel = initSingleEcsResModule(ecsAccount,
                characterInsLevel.getInsCount(), regionId, zoneId, ecsClass, config,
                characterInsLevel.getId(), characterCustins.getDiskSize(), storageType);
            // set host res model
            custinsResModel.setEcsResModel(ecsResModel);
        }
        // put into resource container
        resourceContainer.addCustinsResModel(custinsResModel);
    }


    /**
     * 创建docker化实例依赖的rds服务
     */
    private void constructDependentResourceContainerForCustins(CustInstanceDO custins,
                                                               InstanceLevelDO characterInsLevel,
                                                               Integer netType,
                                                               ResourceContainer resourceContainer,
                                                               RequestParamsDO params) throws RdsException {

        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        List<RdsResModel> rdsResModelList = new ArrayList<>();
        Map<String, Object> custinsBasicConfig = null;
        RdsResModel rdsResModel = null;
        if (characterInsLevel.getDbType().equalsIgnoreCase("mysql")) {
            // region of logic custins.
            String logicRegion = resourceContainer.getSubDomain();

            String associatedDomainResKey = ResourceSupport.getInstance().getAssociatedDomainResKey(custins.getDbType());
            String configedAssociatedDomain = mysqlParaHelper.selectOneAssociatedDomain(logicRegion, associatedDomainResKey,
                characterInsLevel.getDbType().toLowerCase());
            if (configedAssociatedDomain != null){
                params.setRegion(configedAssociatedDomain);
                params.setClusterName(null);
            }
            custinsBasicConfig = getMysqlResConfig();
            custinsBasicConfig.put("insName", custins.getInsName());
            rdsResModel = getRdsSrvResModel(params, characterInsLevel, custinsBasicConfig);
        } else {
            logger.error("Didn't support type");
        }
        rdsResModelList.add(rdsResModel);
        custinsResModel.setRdsResModelList(rdsResModelList);
        resourceContainer.addCustinsResModel(custinsResModel);
    }

    /**
     * 资源评估
     * @param ecsAccount
     * @param insCount
     * @param regionId
     * @param zoneId
     * @param ecsClass
     * @param config
     * @param levelId
     * @param diskSize
     * @return
     * @throws RdsException
     */
    private EcsResModel initSingleEcsResModule(String ecsAccount, Integer insCount,
                                               String regionId, String zoneId, String ecsClass,
                                               DockerInsLevelParseConfig config, Integer levelId,
                                               Long diskSize,
                                               String storageType
                                               ) throws RdsException {
        // 资源管理器进行资源评估时不check imageId,但目前不传会报错,后续近秋会改掉
        EcsResModel ecsResModel = new EcsResModel("dummy");
        ecsResModel.setEcsAccount(ecsAccount);
        String dataDiskCategory = storageType;

        if (CloudEssdEnums.CLOUD_ESSD_PL2.getStorageType().equals(storageType)) {
            ecsResModel.setDataDiskCategory(CloudEssdEnums.CLOUD_ESSD_PL2.getDiskCategory());
            ecsResModel.setPerformanceLevel(CloudEssdEnums.CLOUD_ESSD_PL2.getPerformanceLevel());
            dataDiskCategory = CustinsParamSupport.ClOUDDISK_CLOUD_EFFICIENCY;
        }else if (CloudEssdEnums.CLOUD_ESSD_PL3.getStorageType().equals(storageType)) {
            ecsResModel.setDataDiskCategory(CloudEssdEnums.CLOUD_ESSD_PL3.getDiskCategory());
            ecsResModel.setPerformanceLevel(CloudEssdEnums.CLOUD_ESSD_PL3.getPerformanceLevel());
            dataDiskCategory = CustinsParamSupport.ClOUDDISK_CLOUD_EFFICIENCY;
        }else if(CloudEssdEnums.CLOUD_ESSD_PL0.getStorageType().equals(storageType)) {
            ecsResModel.setDataDiskCategory(CloudEssdEnums.CLOUD_ESSD_PL0.getDiskCategory());
            ecsResModel.setPerformanceLevel(CloudEssdEnums.CLOUD_ESSD_PL0.getPerformanceLevel());
            dataDiskCategory = CustinsParamSupport.ClOUDDISK_CLOUD_EFFICIENCY;
        }

        // 资源管理器有检验这个
        ecsResModel.setEcsVSwitchId("dummy");
        ecsResModel.setEcsVpcId("dummy");
        if (StringUtils.isEmpty(ecsResModel.getPerformanceLevel())){
            ecsResModel.setDataDiskCategory(storageType);
        }

        ecsResModel.setInsCount(insCount);
        ecsResModel.setRegionId(mysqlParaHelper.transferRegionId(regionId));
        ecsResModel.setZoneId(mysqlParaHelper.transferZoneId(zoneId));
        ecsResModel.setInstanceType(ecsClass);
        ecsResModel.setInsPortCount(config.getPortCountPerIns());
        ecsResModel.setLevelId(levelId);

        // 显示指定磁盘大小为0或者规格中声明为0
        if(StringUtils.isEmpty(storageType)) {
//            dataDiskCategory = ResourceSupport.getInstance()
//                    .getStringRealValue(ResourceKey.RESOURCE_ECS_DATA_DISK_CATEGORY).trim();

            dataDiskCategory = ecsDiskDefaultCategory.getZoneDefaultCategory(zoneId);
        }

        // 显示指定磁盘大小为0或者规格中声明为0
        if (diskSize != 0L) {
            List<EcsDataDisk> dataDiskList = new ArrayList<>();
            EcsDataDisk dataDisk = new EcsDataDisk();
            dataDisk.setCategory(dataDiskCategory);
            dataDisk.setDevice(CustinsSupport.ECS_DEISK_PREFIX + "/data");
            dataDisk.setSize(mysqlParaHelper.getExtendDiskSizeForEcsIns(
                CustinsSupport.DB_TYPE_DOCKER, diskSize));
            dataDisk.setRegionId(regionId);
            dataDisk.setZoneId(zoneId);
            dataDiskList.add(dataDisk);
            ecsResModel.setEcsDataDiskList(dataDiskList);
        }
        return ecsResModel;
    }
}
