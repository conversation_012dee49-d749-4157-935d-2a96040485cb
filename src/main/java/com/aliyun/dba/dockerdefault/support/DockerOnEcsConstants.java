package com.aliyun.dba.dockerdefault.support;

import java.util.HashMap;
import java.util.Map;

public class DockerOnEcsConstants {
    /**
     * essd type
     */
    public static final String ECS_ClOUD_ESSD = "cloud_essd";
    public static final String ECS_ClOUD_ESSD2 = "cloud_essd2";
    public static final String ECS_ClOUD_ESSD3 = "cloud_essd3";
    public static final String ECS_ClOUD_ESSD0 = "cloud_essd0";

    /**
     * ssd type
     */
    public static final String LOCAL_SSD = "local_ssd";
    public static final String ECS_ClOUD_SSD = "cloud_ssd";
    public static final String ECS_CLOUD = "cloud";//普通云盘
    public static final String ECS_CLOUD_EFFICIENCY = "cloud_efficiency";//高效云盘

    public static final String ECS_CLOUD_AUTO = "cloud_auto";

    /**
     * performanceLevel
     */
    public static final String ESSD_PERFORMANCELEVEL = "PL1";
    public static final String ESSD2_PERFORMANCELEVEL = "PL2";
    public static final String ESSD3_PERFORMANCELEVEL = "PL3";
    public static final String ESSD0_PERFORMANCELEVEL = "PL0";

    /**
     * host type
     */
    public static final Integer HOST_TYPE_PHYSICAL = 0;
    public static final Integer HOST_TYPE_ECS = 2;

    /**
     * cn-hangzhou-finance ：RES_KEY
     */
    public static final String DOCKER_FINANCE_ECS_REGION_MAPPING = "HBASE_ECS_REGION_MAPPING";
    public static final String DOCKER_FINANCE_ECS_ZONE_MAPPING = "HBASE_ECS_ZONE_MAPPING";

    /**
     * essd disk size
     */
    public static final Integer ECS_ClOUD_ESSD2_MIN_SIZE = 465;
    public static final Integer ECS_ClOUD_ESSD3_MIN_SIZE = 1265;
    public static final Integer ECS_ClOUD_ESSD0_MIN_SIZE = 10;
    public static final Integer ECS_ClOUD_ESSD_MIN_SIZE = 20;

    public static final Integer ECS_CLOUD_AUTO_MIN_SIZE = 10;

    public static String getEssdPerLevel(String essd_type) {
        Map<String, String> essdPerLevel = new HashMap<>();
        essdPerLevel.put(ECS_ClOUD_ESSD, ESSD_PERFORMANCELEVEL);
        essdPerLevel.put(ECS_ClOUD_ESSD2, ESSD2_PERFORMANCELEVEL);
        essdPerLevel.put(ECS_ClOUD_ESSD3, ESSD3_PERFORMANCELEVEL);
        essdPerLevel.put(ECS_ClOUD_ESSD0, ESSD0_PERFORMANCELEVEL);
        essdPerLevel.put(ESSD3_PERFORMANCELEVEL, ECS_ClOUD_ESSD3);
        essdPerLevel.put(ESSD2_PERFORMANCELEVEL, ECS_ClOUD_ESSD2);
        essdPerLevel.put(ESSD_PERFORMANCELEVEL, ECS_ClOUD_ESSD);
        essdPerLevel.put(ESSD0_PERFORMANCELEVEL, ECS_ClOUD_ESSD0);
        return essdPerLevel.get(essd_type);
    }

    public static String getEssdCategory(String perfLevel) {
        Map<String, String> essdPerLevel = new HashMap<>();
        essdPerLevel.put(ESSD3_PERFORMANCELEVEL, ECS_ClOUD_ESSD3);
        essdPerLevel.put(ESSD2_PERFORMANCELEVEL, ECS_ClOUD_ESSD2);
        essdPerLevel.put(ESSD_PERFORMANCELEVEL, ECS_ClOUD_ESSD);
        essdPerLevel.put(ESSD0_PERFORMANCELEVEL, ECS_ClOUD_ESSD0);
        return essdPerLevel.get(perfLevel);
    }

    public static Map<String, Integer> ESSD_MIN_SIZE_MAP = new HashMap<String, Integer>();
    static {
        ESSD_MIN_SIZE_MAP.put(ECS_ClOUD_ESSD0, ECS_ClOUD_ESSD0_MIN_SIZE);
        ESSD_MIN_SIZE_MAP.put(ECS_ClOUD_ESSD, ECS_ClOUD_ESSD_MIN_SIZE);
        ESSD_MIN_SIZE_MAP.put(ECS_ClOUD_ESSD2, ECS_ClOUD_ESSD2_MIN_SIZE);
        ESSD_MIN_SIZE_MAP.put(ECS_ClOUD_ESSD3, ECS_ClOUD_ESSD3_MIN_SIZE);
    }

    public static Map<String, Integer> ECS_CLOUD_WEIGHT_MAP = new HashMap<String, Integer>();
    static {
        ECS_CLOUD_WEIGHT_MAP.put(ECS_CLOUD, 1);
        ECS_CLOUD_WEIGHT_MAP.put(ECS_CLOUD_EFFICIENCY, 2);
        ECS_CLOUD_WEIGHT_MAP.put(ECS_ClOUD_SSD, 3);
        ECS_CLOUD_WEIGHT_MAP.put(ECS_ClOUD_ESSD0, 4);
        ECS_CLOUD_WEIGHT_MAP.put(ECS_ClOUD_ESSD, 5);
        ECS_CLOUD_WEIGHT_MAP.put(ECS_ClOUD_ESSD2, 6);
        ECS_CLOUD_WEIGHT_MAP.put(ECS_ClOUD_ESSD3, 7);
    }

    // 大客户集群
    public static final Integer DEDICATED_HOST_GOURP_TYPE = 1;

    // 停机实例taskKey
    public static final String TASK_STOP_DBINSTANCE = "docker_stop_ins";

    // 输入资源管理器 磁盘REQ 等级
    public static final Integer DISK_TYPE_NO_NEED = 2;

    public static final Integer DISK_TYPE_ALL = 99;

    /**
     * auto Scale disk param
     */
    public static final String STORAGE_UPPER_BOUND = "StorageUpperBound";
    public static final String STORAGE_AUTO_SCALE = "StorageAutoScale";
    public static final String STORAGE_THRESHOLD = "StorageThreshold";
    public static final String AUTO_SCALE_DISK_PARAM = "AutoScaleDiskParam";

    public static boolean isEssd(String diskType){
        return (null != diskType) && (diskType.equalsIgnoreCase(ECS_ClOUD_ESSD) || diskType.equalsIgnoreCase(ECS_ClOUD_ESSD2)
                || diskType.equalsIgnoreCase(ECS_ClOUD_ESSD3));
    }
}