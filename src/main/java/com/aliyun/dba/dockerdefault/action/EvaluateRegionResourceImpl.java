package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_DOCKER;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_DOCKER_ON_ECS;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_ECS_VM;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultEvaluateRegionResourceImpl")
public class EvaluateRegionResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateRegionResourceImpl.class);

    @Autowired
    private EcsService ecsService;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private DockerCommonService dockerCommonService;
    @Resource
    private com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl poddefaultEvaluateDockerToK8SResource;
    @Resource
    private CrossArchService crossArchService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {
        try {
            if (crossArchService.onecsCloneToK8s(map)) {
                logger.info("docker restore. evaluate k8s resource.");
                return poddefaultEvaluateDockerToK8SResource.doActionRequest(custInstanceDO, map);
            }

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            String region = mysqlParaHelper.getAndCheckRegion();
            // Engine参数必传
            String dbType = mysqlParaHelper.getAndCheckDBType(null);
            // Docker 实例集群的db_type类型为docker,引入临时变量做调整
            String dbTypeForCluster = dbType;
            String classCode = mysqlParaHelper.getAndCheckDBInstanceClassCode(null, dbType);

            String dbVersion = mysqlParaHelper.getAndCheckDBVersion(dbType, false);
            Integer bizType = mysqlParaHelper.getAndCheckBizType();

            mysqlParaHelper.getAndSetContainerTypeAndHostTypeIfEmpty(dbType, dbVersion, classCode);

            String containerType = mysqlParaHelper.getParameterValue(ParamConstants.CONTAINER_TYPE,
                CustinsSupport.CONTAINER_TYPE_HOST);
            String hostType = mysqlParaHelper.getAndCheckHostType();
            String storageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            if (CustinsSupport.STORAGE_TYPE_CLOUD_AUTO.equalsIgnoreCase(storageType)) {
                return ResponseSupport.createErrorResponse(ErrorCode.NOT_SUPPORT_SPECIFIC_DISK_TYPE);
            }

            CustInstanceDO custins = new CustInstanceDO();
            custins.setDbType(dbType);
            custins.setDbVersion(dbVersion);

            custins = mysqlParaHelper.setInstanceLevel(custins, classCode, bizType,
                    mysqlParaHelper.getParameterValue( ParamConstants.STORAGE));

            if (CustinsSupport.isCustinsOnEcs(hostType, dbVersion, null)) {
                custins.setKindCode(KIND_CODE_ECS_VM);
            }
            if ((CustinsSupport.isContainerTypeDocker(containerType)
                && CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD.equals(hostType))) {
                custins.setKindCode(KIND_CODE_DOCKER_ON_ECS);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }

            if (CustinsSupport.isContainerTypeDocker(containerType)
                && !CustinsSupport.KIND_CODE_DOCKER_ON_ECS.equals(custins.getKindCode())
                && !CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE.equals(custins.getKindCode())) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }
            if (CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD.equals(hostType)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD);
            }
            custins.setDbTypeForCluster(dbTypeForCluster);

            if (custins.isCustinsOnDocker() || custins.isCustinsOnDockerOnEcsLocalSSD()) {
                return dockerCommonService.evaluate(custins);
            } else {
                Map<String, Object> data = new HashMap<String, Object>();
                if (custins.isCustinsOnEcs()) {
                    // 其他引擎
                    if (clusterService.checkRegionWithEcs(region, dbTypeForCluster)) {
                        data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                    } else {
                        data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                    }
                } else {
                    data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                }
                data.put(ParamConstants.ENGINE_VERSION, dbVersion);
                data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(dbType));
                return data;
            }
        } catch (RdsException re) {
            logger.error("EvaluateRegionResource ex="+re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (NullPointerException npe) {
        	// 空指针异常抛出来，看一下错误栈
        	logger.error("null pointer exception", npe);
        	throw npe;
        } catch (Exception ex) {
            logger.error("EvaluateRegionResource ex="+ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
