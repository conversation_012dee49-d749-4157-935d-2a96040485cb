package com.aliyun.dba.dockerdefault.action;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 */

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultInspectDBInstanceImpl")
public class InspectDBInstanceImpl extends com.aliyun.dba.base.action.BaseInspectDBInstanceImpl {

}
