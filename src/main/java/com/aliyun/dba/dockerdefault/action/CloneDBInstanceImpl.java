package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.BaklistDO;
import com.aliyun.dba.bak.dataobject.CheckBaksetDO;
import com.aliyun.dba.bak.idao.BakIDao;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.docker.dataobject.DockerTaskInputParam;
import com.aliyun.dba.dockerdefault.service.DockerManager;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.dataobject.RdsRegionDO;
import com.aliyun.dba.ecs.service.EcsImageService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.ecs.service.RegionService;
import com.aliyun.dba.endpoint.dataobject.EndpointDO;
import com.aliyun.dba.endpoint.idao.EndpointConfigIDao;
import com.aliyun.dba.endpoint.idao.EndpointIDao;
import com.aliyun.dba.endpoint.service.EndpointService;
import com.aliyun.dba.endpoint.support.EndpointSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelRelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.service.MySQLGeneralService;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.*;
import static com.aliyun.dba.instance.support.InstanceSupport.*;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;
import static com.aliyun.dba.support.property.ParamConstants.TARGET_UID;
import static com.aliyun.dba.support.property.ParamConstants.TARGET_USER_ID;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_PRIMARY;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultCloneDBInstanceImpl")
public class CloneDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CloneDBInstanceImpl.class);
    public static final String CUSTINS_PARAM_NAME_COMPOSE_TAG = "compose_tag";

    @Autowired
    private BakService bakService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private ResApi resApi;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private BakIDao bakIDao;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private IpWhiteListService ipWhiteListService;
    @Autowired
    private DockerManager dockerManager;
    @Autowired
    private CustinsIDao custinsIDao;
    @Autowired
    private KmsService kmsService;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private CheckService checkService;
    @Autowired
    protected CloneEnabledCustinsService cloneEnabledCustinsService;
    @Autowired
    protected DockerCustinsService dockerCustinsService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected EcsService ecsService;
    @Autowired
    protected EcsImageService ecsImageService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected InstanceIDao instanceIDao;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    private MySQLGeneralService mySQLGeneralService;
    @Autowired
    private EndpointService endpointService;
    @Autowired
    private EndpointConfigIDao endpointConfigIDao;
    @Autowired
    private EndpointIDao endpointIDao;
    @Autowired
    private RegionService regionService;
    @Resource
    private CrossArchService crossArchService;
    @Resource
    private com.aliyun.dba.poddefault.action.CloneDBInstanceImpl poddefaultCloneDBInstance;
    
    
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {
        	
        	// 快捷方式构建一些动态的参数，跟杜康的通用运维做集成.
        	mysqlParaHelper.buildShortcutCloneParams(custInstanceDO, map);

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            Integer targetUserId = null;
            String action = mysqlParaHelper.getAction();
            if ("CloneDBInstanceForSecurity".equalsIgnoreCase(action)) {
                targetUserId = checkService.getAndCheckUserId(
                    mysqlParaHelper.getParameterValue(TARGET_USER_ID),
                    mysqlParaHelper.getParameterValue(TARGET_UID),
                    mysqlParaHelper.getParameterValue(ParamConstants.ACTION));
            }

            //cloneDBInstanceForSecurity，isValidCount检查为false；cloneDBInstance为true（或者null）
            boolean isValidCount = true;
            String isValidCountString = mysqlParaHelper.getParameterValue("isValidCount");
            //默认为校验，CloneDBInstanceForSecurityImpl中不校验，会传递该参数,为null则是cloneDBInstance
            if (isValidCountString != null) {
                isValidCount = Boolean.valueOf(isValidCountString);
            }

            CustInstanceDO srcCustins = mysqlParaHelper.getAndCheckSourceCustInstance();

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(srcCustins, mysqlParaHelper.getUID())) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_KMS_KEY);
            }

            String cloneMode = mysqlParaHelper.getParameterValue(MysqlParameterHelper.CLONE_MODE);
            boolean isUserCluster = clusterService.getClusterByClusterName(srcCustins.getClusterName()).getType().equals(DEDICATED_HOST_GOURP_TYPE);
            if (crossArchService.onecsCloneToK8s(map) && !isUserCluster) {
                return poddefaultCloneDBInstance.doActionRequest(srcCustins, map);
            } else if ((srcCustins.isCustinsDockerOnEcs() || srcCustins.isMysql57Physical() || srcCustins.isMysql80Physical()) && !isUserCluster) {
                if (cloneMode != null && MysqlParameterHelper.QUICK_MODE.equalsIgnoreCase(cloneMode)) {
            		// NOTE: 快速克隆走独立的逻辑，这是原来逻辑的重载
            		return cloneInsOnDockerOnEcs(srcCustins, cloneMode);
            	} else {
            		return cloneInsOnDockerOnEcs(srcCustins);
            	}
            } else if (srcCustins.isCustinsOnDockerOnEcsLocalSSD()||isUserCluster) {
                cloneValidSrcCustins(srcCustins);


                if (isValidCount && !srcCustins.isDockerLogic()) {
                    cloneValidCommon(srcCustins, false);
                }

                //根据下面逻辑，不是dockerLogic实例，抛出不支持
                if (!srcCustins.isDockerLogic()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                List<CustInstanceDO> physicalCustinsList = custinsService.getCustInstanceByParentId(srcCustins.getId());
                InstanceLevelDO srcLevel = instanceService.getInstanceLevelByLevelId(srcCustins.getLevelId());
                if("general".equalsIgnoreCase(srcLevel.getCategory())){
                    physicalCustinsList = mySQLGeneralService.getInsListByInsType(srcCustins.getId(), 0);
                }

                if(physicalCustinsList == null || physicalCustinsList.isEmpty()){
                    throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
                }

                //实际与资源管理器交互过程中，使用物理实例
                CustInstanceDO physicalCustins = physicalCustinsList.get(0);

                //构造trans_list 需要物理实例,
                TransListDO trans = mysqlParaHelper.getDockerOnEcsLocalSSDTrans(srcCustins, physicalCustins);

                // 克隆逻辑实例，物理实例的clone 在 cloneInsOnDocker 实现
                CustInstanceDO cloneCustins = cloneSrcCustinsWithoutParentid(srcCustins, !srcCustins.isDockerLogic());
                if("general".equalsIgnoreCase(srcLevel.getCategory())){
                    cloneCustins = cloneGeneralSrcCustins(srcCustins);
                }

                // 重新json 化translist 参数，传入任务中
                String transListParamString = trans.getParameter();
                Map<String, Object> translistParamMap = JSON.parseObject(transListParamString);


                Integer taskId = null;

                if (targetUserId != null) {
                    cloneCustins.setUserId(targetUserId);
                }
                if (srcCustins.isDockerLogic()) {
                    if("general".equalsIgnoreCase(srcLevel.getCategory())){
                        taskId = cloneGeneralInsOnDocker(srcCustins, cloneCustins, trans, translistParamMap);
                    }else{
                        taskId = cloneInsOnDocker(srcCustins, cloneCustins, trans, translistParamMap);

                    }
                }
                //else {
                //    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                //}

                Map<String, Object> data = new HashMap<>(10);
                data.put("DBInstanceID", cloneCustins.getId());
                data.put("DBInstanceName", cloneCustins.getInsName());
                data.put("TaskId", taskId);
                return data;
            }
            //mysql剩下形态就是 kindCode=10
            else {
                cloneValidSrcCustins(srcCustins);

                if (isValidCount && !srcCustins.isDockerLogic()) {
                    cloneValidCommon(srcCustins, false);
                }

                TransListDO trans = new TransListDO(srcCustins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
                Map<String, Object> translistParamMap = new HashMap<>(8);

                trans.setsCinsReserved(1);
                //根据下面逻辑，不是dockerLogic实例，抛出不支持
                if (!srcCustins.isDockerLogic()) {
                    //List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(srcCustins.getId());
                    //trans.setsHinsid1(insIds.get(0));
                    //if (insIds.size() > 1) {
                    //    trans.setsHinsid2(insIds.get(1));
                    //}
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
                String restoreType = mysqlParaHelper.getAndCheckRestoreType();
                //检查设置备份恢复方式
                checkAndSetRestoreInfo(srcCustins, restoreType, translistParamMap, trans);

                CustInstanceDO cloneCustins = cloneSrcCustinsWithoutParentid(srcCustins, !srcCustins.isDockerLogic());

                Integer taskId = null;

                if (targetUserId != null) {
                    cloneCustins.setUserId(targetUserId);
                }
                if (srcCustins.isDockerLogic()) {
                    taskId = cloneInsOnDocker(srcCustins, cloneCustins, trans, translistParamMap);
                }
                //else {
                //    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                //}

                Map<String, Object> data = new HashMap<>(10);
                data.put("DBInstanceID", cloneCustins.getId());
                data.put("DBInstanceName", cloneCustins.getInsName());
                data.put("TaskId", taskId);
                return data;
            }

        } catch (RdsException re) {
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("cloneDBInstance ex=" + ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            //移除参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }



    //检查设置备份恢复方式和信息
    private void checkAndSetRestoreInfo(CustInstanceDO srcCustins,
                                        String restoreType,
                                        Map<String, Object> translistParamMap,
                                        TransListDO trans) throws RdsException{

        translistParamMap.put("restoreType", restoreType);

        if (RESTORE_TYPE_TIME.equals(restoreType)) {
            Date restoreTime = crossArchService.validRestoreByTime(srcCustins);
            trans.setIsBaseTime(1);
            trans.setRecoverTime(restoreTime);

        } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            Long bakId = crossArchService.validRestoreByBakset(srcCustins);
            trans.setBakhisId(bakId);

        } else {
            CheckBaksetDO checkBakset = mysqlParaHelper.validRestoreByUser(srcCustins);
            translistParamMap.put("downloadUrl", checkBakset.getDownloadUrl());
            translistParamMap.put("baksetName", checkBakset.getName());
        }
    }



    private boolean cloneValidSrcCustins(CustInstanceDO custins) throws RdsException {
        if (custins.isDockerLogic()) {
            return true;
        }
        if (custins.isCustinsDockerOnPolarStore() && custins.isLogic()) {
            return true;
        }
        if (custins.isShare() || custins.isReadOrBackup() || custins.isSub() || !custins.isLogicPrimary()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        if (custins.isCustinsOnEcs()) {
            if (!custins.isMysql()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE);
            }
        } else {
            if (!custins.isMysql()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE);
            }
        }

        // 去除判断源实例是否在clone中，允许并发clone
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        if (custins.isReadAndWriteLock()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }

        return true;
    }

    /**
     * 检查Docker实例是否支持按照时间点恢复 按照备份集恢复基本都支持
     */
    private boolean checkSupportRestoreByTime(CustInstanceDO srcCustins) {

        boolean supportRestore = false;
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(srcCustins.getLevelId());
        String composeTag = mysqlParaHelper.selectComposeTag(srcCustins.getClusterName());
        EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
            srcCustins.getDbType(), srcCustins.getDbVersion(), insLevel.getCategory(), composeTag);
        JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());
        for (String service : jsonServer.keySet()) {
            EngineService engineService = new Gson().fromJson(jsonServer.getString(service),
                EngineService.class);
            supportRestore = engineService.isSupportSupportRestoreByTime();
            if (supportRestore) {
                break;
            }
        }

        return supportRestore;
    }

    /**
     * NOTE: 如果要修改这个方法，涉及改动MySQL的业务，需要同时修改它的重载方法 
     * private Map<String, Object> cloneInsOnDockerOnEcs(CustInstanceDO srcCustins, String cloneMode)
     * @param srcCustins
     * @return
     * @throws RdsException
     */
    private Map<String, Object> cloneInsOnDockerOnEcs(CustInstanceDO srcCustins)
        throws RdsException {

        //Now docker on ecs not support RESTORE_TYPE_TIME and RESTORE_TYPE_USER
        String restoreType = mysqlParaHelper.getAndCheckRestoreType();
        if (!RESTORE_TYPE_BAKID.equals(restoreType)) {
            boolean supportRestore = checkSupportRestoreByTime(srcCustins);
            if (!supportRestore) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
            }
        }

        RequestParamsDO params = new RequestParamsDO();
        params.setAvzInfo(avzSupport.getAVZInfo(ActionParamsProvider.ACTION_PARAMS_MAP.get()));
        params.setDbType(srcCustins.getDbType());
        params.setClassCode(mysqlParaHelper.getAndCheckClassCode());
        if (mysqlParaHelper.hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            String desc = SupportUtils.decode(
                mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION));
            params.setDesc(desc);
        }
        //accountMode to Account type
        params.setAccountType(srcCustins.getAccountMode().toString());
        // set region 相关信息
        params.setBizType(CustinsSupport.BIZ_TYPE_RDS);
        params.setType(srcCustins.getType());
        params.setContainerType("docker");
        params.setAction(mysqlParaHelper.getAction());
        params.setOperatorId(mysqlParaHelper.getOperatorId());
        params.setClusterName(srcCustins.getClusterName());
        //permit user chage from
        String region = null;
        region = clusterService.getRegionByCluster(srcCustins.getClusterName());
        if (mysqlParaHelper.hasParameter("SubDomain")) {
            // 从客户端读取子域信息
            region = clusterService.getAndCheckSubDomain(
                mysqlParaHelper.getParameterValue("SubDomain"));
            if (Validator.isNull(region)) {
                throw new RdsException(ErrorCode.INVALID_REGION);
            }
        }
        params.setRegion(region);
        //get regionid and zone info from srccustins cluste
        params.setRegionId(mysqlParaHelper.getAndCheckRegionID());
        params.setZoneId(mysqlParaHelper.getAndCheckAvZone());

        List<CustInstanceDO> childCustinsList = custinsService.getCustInstanceUnitByParentIdAndCharacterType(
            srcCustins.getId(), CustinsSupport.CHARACTER_TYPE_PHYSICAL);
        InstanceLevelDO srcLevel = instanceService.getInstanceLevelByLevelId(srcCustins.getLevelId());
        if("general".equalsIgnoreCase(srcLevel.getCategory())){
            childCustinsList = mySQLGeneralService.getInsListByInsType(srcCustins.getId(), 0);
        }
        if (childCustinsList.size() <= 0) {
            if (srcCustins.isMysql57Physical() || srcCustins.isMysql80Physical()) {
                // physical backup clone ecs instance
                params.setHostType(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD);
            } else {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
            }
        } else {
            params.setHostType(custinsService.getCustinsHostType(childCustinsList.get(0).getId()));
        }
        params.setNetType(CustinsSupport.NET_TYPE_VPC);

        // set product info
        params.setEngine(srcCustins.getDbType());
        params.setDbType(srcCustins.getDbType());
        params.setEngineVersion(srcCustins.getDbVersion());
        params.setDbVersion(srcCustins.getDbVersion());
        // set uerinfo
        params.setUserId(srcCustins.getUserId());
        params.setMaintainStartTime(srcCustins.getMaintainStarttime());
        params.setMaintainEndTime(srcCustins.getMaintainEndtime());
        params.setDbBInstanceName(mysqlParaHelper.getDBInstanceName());
        //todo: 磁盘大小需要检查大小
        params.setStorageType(mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE));
        String dataDiskCategory = custinsService.getDataDiskCategory(srcCustins.getId(),
            params.getStorageType(),
            CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD);
        params.setDataDiskCategory(dataDiskCategory);
        params.setStorage(mysqlParaHelper.getParameterValue(ParamConstants.STORAGE));
        //用户vpc场景clone的实例还是用户vpc的
        params.setOptmization(srcCustins.getIsAccept().toString());
        // set vpc net info
        params.setUserVpc(true);
        String vpcId = mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID);
        CheckUtils.checkValidForVPCId(vpcId);
        params.setUserVpcId(vpcId);
        String tunnelId = mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID);
        CheckUtils.checkValidForTunnelId(tunnelId);
        params.setTunnelId(Integer.valueOf(tunnelId));
        String vswitchID = mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID);
        CheckUtils.checkValidForVswitchId(vswitchID);
        params.setVswitchId(vswitchID);
        String ipaddres = mysqlParaHelper.getParameterValue(ParamConstants.IP_ADDRESS);
        CheckUtils.checkValidForIPAddress(ipaddres);
        params.setIpaddress(ipaddres);

        params.setConnType(srcCustins.getConnType());
        params.setConnectionString(mysqlParaHelper.getParameterValue("connectionstring"));
        params.setConnAddrCust(mysqlParaHelper.getConnAddrCust(params.getConnectionString(), mysqlParaHelper.getRegionIdByClusterName(srcCustins.getClusterName()), params.getDbType()));
        params.setPortStr(CustinsSupport.getConnPort(mysqlParaHelper.getParameterValue("port"), params.getDbType()));
        //params.setTaskQueueParam(getTaskQueueParam());
        //set proxy 信息
        params.setProxyGroupId(0);
        params.setStorageType(mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE));

        // set byok param
        String keyId;
        if (childCustinsList.size()>0) {
            keyId = kmsService.getSrcCustinsKeyId(srcCustins.getId(), childCustinsList.get(0).getId());
        } else {
            keyId = kmsService.getSrcCustinsKeyId(srcCustins.getId(), srcCustins.getId());
        }
        String roleArn = kmsService.getUserRoleArn(mysqlParaHelper.getUID());
        params.setCmkId(keyId);
        params.setRoleArn(roleArn);
        params.setUid(mysqlParaHelper.getUID());

        //set bak restore info
        BakhistoryDO bakHistory = null;
        params.setRestoreType(Integer.parseInt(restoreType));
        Integer diskSize = Integer.parseInt(params.getStorage());
        Date restoreTime = null;
        if (RESTORE_TYPE_TIME.equals(restoreType)) {
            if (srcCustins.getDbType().equals("mariadb")) {
                throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
            }
            restoreTime = crossArchService.validRestoreByTime(srcCustins);
        } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            Long bakId = crossArchService.validRestoreByBakset(srcCustins);
            params.setBackUpSetId(bakId);
        } else {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
        }

        bakHistory = mysqlParaHelper.validCloneStorageForEcs(srcCustins, diskSize * 1024L, restoreType, restoreTime);
        bakService.lockBakHisForRestore(bakHistory.getHisId()); // 锁定用于恢复的Binlog，避免被删除
        if(restoreTime!=null) {
            bakService.lockBinlogForRestore(srcCustins.getId(),bakHistory.getBakBegin(),restoreTime); // 锁定用于恢复的Binlog，避免被删除
            ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                    JSON.toJSONString(ImmutableMap.of(
                            "custinsId", srcCustins.getId().toString(),
                            "begin", bakHistory.getBakBegin().getTime(),
                            "end", restoreTime.getTime())));
        }
        ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakHistory.getHisId()));

        String baksetType = bakHistory.getBakWay();
        JSONObject bakHistObject = JSONArray.parseObject(bakHistory.getSlaveStatus());
        if (BAKWAY_SNAPSHOT.equals(baksetType)) {
            JSONArray bakHistArray = bakHistObject.getJSONArray(bakHistObject.keySet().toArray()[0].toString());
            JSONObject jsonObject = bakHistArray.getJSONObject(0);
            String snapShotId = jsonObject.getJSONObject("slave_status").getString("SNAPSHOT_ID");
            params.setSnapShotId(snapShotId);
        }

        BaklistDO bakList = (bakIDao.getBaklistByCustinsId(srcCustins.getId(), null, 0)).get(0);

        //init task queue parames
        // 获取备份策略参数，下发实例创建时，这些参数将被写入parameter字段中
        Map<String, Object> taskQueueParam = new HashMap<>(1);
        Map<String, Object> restoreParam = new HashMap<>(4);

        restoreParam.put("snapShotId", params.getSnapShotId());
        //todo: now only support one physical custins clone
        restoreParam.put("srcParentCustId", srcCustins.getId());
        restoreParam.put("restoreType", restoreType);
        restoreParam.put("baksetType", baksetType);
        if (RESTORE_TYPE_TIME.equals(restoreType)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            restoreParam.put("restoreTime", sdf.format(restoreTime));
        }
        restoreParam.put("bakHisID", bakHistory.getHisId());

        Map<String, Object> baklistParam = new HashMap<>(3);
        baklistParam.put("retention", bakList.getRetention());
        baklistParam.put("bak_period", bakList.getBakPeriod());
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        baklistParam.put("bak_begin", sdf.format(bakList.getBakBegin()));

        taskQueueParam.put("restore", restoreParam);
        taskQueueParam.put("backup", baklistParam);
        params.setTaskQueueParam(taskQueueParam);

        if (srcCustins.isMbaseSql() && (srcCustins.isCustinsDockerOnEcs() || srcCustins.isMysql57Physical() || srcCustins.isMysql80Physical()) && !DEDICATED_HOST_GOURP_TYPE.equals(clusterService.getClusterByClusterName(params.getClusterName()).getType())) {
            String multiAVZExParamStr = mysqlParaHelper.getParameterValue(ParamConstants.MULTI_AVZ_EX_PARAM);
            CheckUtils.checkStrNotEmpty(multiAVZExParamStr, ParamConstants.MULTI_AVZ_EX_PARAM + " is empty");
            params.setMultiAVZExParam(JSON.parseObject(multiAVZExParamStr, MultiAVZExParamDO.class));
        }

        Map<String, List<MycnfCustinstanceDO>> mycnfCustinstancesMap = mysqlParaHelper.getAndCheckExternalParameter(srcCustins);
        params.setMycnfCustinstancesMap(mycnfCustinstancesMap);
        params.setClone(true);
        params.setAccountMode(srcCustins.getAccountMode());
        DockerTaskInputParam dockerTaskInputParam = dockerManager.createDockerDbInstance(params);
        dockerTaskInputParam.setSrcCusIns(srcCustins);
        Map<String, Object> responseData = dockerManager.disPatchDockerTask(dockerTaskInputParam, true);
        custinsParamService.setCustinsParam(
            Integer.parseInt(responseData.get(ParamConstants.DB_INSTANCE_ID).toString()),
            CustinsParamSupport.DATADISK_STORAGE_TYPE,
            dataDiskCategory);
        taskService.updateTaskPenginePolicy(Integer.parseInt(responseData.get(ParamConstants.TASK_ID).toString()),
            mysqlParaHelper.getPenginePolicyID());

        return responseData;
    }

    /**
     * NOTE:
     * 1. 这个方法是MySQL专用的，其他业务慎用.
     * 2. 这个方法是内部使用的，与用户相关的业务不要使用.
     * 
     * 重载函数，创建kind_code=3克隆任务，为了避免改动通用逻辑造成不预期影响
     * 这个方法用来下发"快速克隆"，在应急操作的时候可以快速克隆一个临时实例，
     * 下游MySQL的任务会识别这种场景，然后克隆的时候遇到一些SLB/VPC等缺失的步骤
     * 也不会中断，不应用在正常的业务场景.
     * @param srcCustins
     * @param cloneMode
     * @return
     * @throws RdsException
     */
    private Map<String, Object> cloneInsOnDockerOnEcs(CustInstanceDO srcCustins, String cloneMode)
            throws RdsException {

            //Now docker on ecs not support RESTORE_TYPE_TIME and RESTORE_TYPE_USER
            String restoreType = mysqlParaHelper.getAndCheckRestoreType();
            if (!RESTORE_TYPE_BAKID.equals(restoreType)) {
                boolean supportRestore = checkSupportRestoreByTime(srcCustins);
                if (!supportRestore) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
                }
            }

            RequestParamsDO params = new RequestParamsDO();
            params.setAvzInfo(avzSupport.getAVZInfo(ActionParamsProvider.ACTION_PARAMS_MAP.get()));
            params.setDbType(srcCustins.getDbType());
            params.setClassCode(mysqlParaHelper.getAndCheckClassCode());
            if (mysqlParaHelper.hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
                String desc = SupportUtils.decode(
                    mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION));
                params.setDesc(desc);
            }
            //accountMode to Account type
            params.setAccountType(srcCustins.getAccountMode().toString());
            // set region 相关信息
            params.setBizType(CustinsSupport.BIZ_TYPE_RDS);
            params.setType(srcCustins.getType());
            params.setContainerType("docker");
            params.setAction(mysqlParaHelper.getAction());
            params.setOperatorId(mysqlParaHelper.getOperatorId());
            params.setClusterName(srcCustins.getClusterName());
            //permit user chage from
            String region = null;
            region = clusterService.getRegionByCluster(srcCustins.getClusterName());
            if (mysqlParaHelper.hasParameter("SubDomain")) {
                // 从客户端读取子域信息
                region = clusterService.getAndCheckSubDomain(
                    mysqlParaHelper.getParameterValue("SubDomain"));
                if (Validator.isNull(region)) {
                    throw new RdsException(ErrorCode.INVALID_REGION);
                }
            }
            params.setRegion(region);
            //get regionid and zone info from srccustins cluste
            params.setRegionId(mysqlParaHelper.getAndCheckRegionID());
            params.setZoneId(mysqlParaHelper.getAndCheckAvZone());

            List<CustInstanceDO> childCustinsList = custinsService.getCustInstanceUnitByParentIdAndCharacterType(
                srcCustins.getId(), CustinsSupport.CHARACTER_TYPE_PHYSICAL);
            InstanceLevelDO srcLevel = instanceService.getInstanceLevelByLevelId(srcCustins.getLevelId());
            if("general".equalsIgnoreCase(srcLevel.getCategory())){
                childCustinsList = mySQLGeneralService.getInsListByInsType(srcCustins.getId(), 0);
            }
            if (childCustinsList.size() <= 0) {
                if (srcCustins.isMysql57Physical() || srcCustins.isMysql80Physical()) {
                    // physical backup clone ecs instance
                    params.setHostType(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD);
                } else {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
                }
            } else {
                params.setHostType(custinsService.getCustinsHostType(childCustinsList.get(0).getId()));
            }
            params.setNetType(CustinsSupport.NET_TYPE_VPC);

            // set product info
            params.setEngine(srcCustins.getDbType());
            params.setDbType(srcCustins.getDbType());
            params.setEngineVersion(srcCustins.getDbVersion());
            params.setDbVersion(srcCustins.getDbVersion());
            // set uerinfo
            params.setUserId(srcCustins.getUserId());
            params.setMaintainStartTime(srcCustins.getMaintainStarttime());
            params.setMaintainEndTime(srcCustins.getMaintainEndtime());
            params.setDbBInstanceName(mysqlParaHelper.getDBInstanceName());
            //todo: 磁盘大小需要检查大小
            params.setStorageType(mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE));
            String dataDiskCategory = custinsService.getDataDiskCategory(srcCustins.getId(),
                params.getStorageType(),
                CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD);
            params.setDataDiskCategory(dataDiskCategory);
            params.setStorage(mysqlParaHelper.getParameterValue(ParamConstants.STORAGE));
            //用户vpc场景clone的实例还是用户vpc的
            params.setOptmization(srcCustins.getIsAccept().toString());
            // set vpc net info
            params.setUserVpc(true);
            String vpcId = mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID);
            CheckUtils.checkValidForVPCId(vpcId);
            params.setUserVpcId(vpcId);
            String tunnelId = mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID);
            CheckUtils.checkValidForTunnelId(tunnelId);
            params.setTunnelId(Integer.valueOf(tunnelId));
            String vswitchID = mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID);
            CheckUtils.checkValidForVswitchId(vswitchID);
            params.setVswitchId(vswitchID);
            String ipaddres = mysqlParaHelper.getParameterValue(ParamConstants.IP_ADDRESS);
            CheckUtils.checkValidForIPAddress(ipaddres);
            params.setIpaddress(ipaddres);

            params.setConnType(srcCustins.getConnType());
            params.setConnectionString(mysqlParaHelper.getParameterValue("connectionstring"));
            params.setConnAddrCust(mysqlParaHelper.getConnAddrCust(params.getConnectionString(), mysqlParaHelper.getRegionIdByClusterName(srcCustins.getClusterName()), params.getDbType()));
            params.setPortStr(CustinsSupport.getConnPort(mysqlParaHelper.getParameterValue("port"), params.getDbType()));
            //params.setTaskQueueParam(getTaskQueueParam());
            //set proxy 信息
            params.setProxyGroupId(0);
            params.setStorageType(mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE));

            // set byok param
            String keyId;
            if (childCustinsList.size()>0) {
                keyId = kmsService.getSrcCustinsKeyId(srcCustins.getId(), childCustinsList.get(0).getId());
            } else {
                keyId = kmsService.getSrcCustinsKeyId(srcCustins.getId(), srcCustins.getId());
            }
            String roleArn = kmsService.getUserRoleArn(mysqlParaHelper.getUID());
            params.setCmkId(keyId);
            params.setRoleArn(roleArn);
            params.setUid(mysqlParaHelper.getUID());

            //set bak restore info
            BakhistoryDO bakHistory = null;
            params.setRestoreType(Integer.parseInt(restoreType));
            Integer diskSize = Integer.parseInt(params.getStorage());
            Date restoreTime = null;
            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                if (srcCustins.getDbType().equals("mariadb")) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
                }
                restoreTime = crossArchService.validRestoreByTime(srcCustins);
            } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                Long bakId = crossArchService.validRestoreByBakset(srcCustins);
                params.setBackUpSetId(bakId);
            } else {
                throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
            }

            bakHistory = mysqlParaHelper.validCloneStorageForEcs(srcCustins, diskSize * 1024L, restoreType, restoreTime);
            bakService.lockBakHisForRestore(bakHistory.getHisId()); // 锁定用于恢复的Binlog，避免被删除
            if(restoreTime!=null) {
                bakService.lockBinlogForRestore(srcCustins.getId(),bakHistory.getBakBegin(),restoreTime); // 锁定用于恢复的Binlog，避免被删除
                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                        JSON.toJSONString(ImmutableMap.of(
                                "custinsId", srcCustins.getId().toString(),
                                "begin", bakHistory.getBakBegin().getTime(),
                                "end", restoreTime.getTime())));
            }
            ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakHistory.getHisId()));

            String baksetType = bakHistory.getBakWay();
            JSONObject bakHistObject = JSONArray.parseObject(bakHistory.getSlaveStatus());
            if (BAKWAY_SNAPSHOT.equals(baksetType)) {
                JSONArray bakHistArray = bakHistObject.getJSONArray(bakHistObject.keySet().toArray()[0].toString());
                JSONObject jsonObject = bakHistArray.getJSONObject(0);
                String snapShotId = jsonObject.getJSONObject("slave_status").getString("SNAPSHOT_ID");
                params.setSnapShotId(snapShotId);
            }

            BaklistDO bakList = (bakIDao.getBaklistByCustinsId(srcCustins.getId(), null, 0)).get(0);

            //init task queue parames
            // 获取备份策略参数，下发实例创建时，这些参数将被写入parameter字段中
            Map<String, Object> taskQueueParam = new HashMap<>(1);
            Map<String, Object> restoreParam = new HashMap<>(4);

            restoreParam.put("snapShotId", params.getSnapShotId());
            //todo: now only support one physical custins clone
            restoreParam.put("srcParentCustId", srcCustins.getId());
            restoreParam.put("restoreType", restoreType);
            restoreParam.put("baksetType", baksetType);
            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                restoreParam.put("restoreTime", sdf.format(restoreTime));
            }
            restoreParam.put("bakHisID", bakHistory.getHisId());

            Map<String, Object> baklistParam = new HashMap<>(3);
            baklistParam.put("retention", bakList.getRetention());
            baklistParam.put("bak_period", bakList.getBakPeriod());
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm'Z'");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            baklistParam.put("bak_begin", sdf.format(bakList.getBakBegin()));

            // cloneMode: quick:快速克隆
            taskQueueParam.put("cloneMode", cloneMode);
            
            taskQueueParam.put("restore", restoreParam);
            taskQueueParam.put("backup", baklistParam);
            params.setTaskQueueParam(taskQueueParam);

            if (srcCustins.isMbaseSql() && (srcCustins.isCustinsDockerOnEcs() || srcCustins.isMysql57Physical() || srcCustins.isMysql80Physical()) && !DEDICATED_HOST_GOURP_TYPE.equals(clusterService.getClusterByClusterName(params.getClusterName()).getType())) {
                String multiAVZExParamStr = mysqlParaHelper.getParameterValue(ParamConstants.MULTI_AVZ_EX_PARAM);
                CheckUtils.checkStrNotEmpty(multiAVZExParamStr, ParamConstants.MULTI_AVZ_EX_PARAM + " is empty");
                params.setMultiAVZExParam(JSON.parseObject(multiAVZExParamStr, MultiAVZExParamDO.class));
            }

            Map<String, List<MycnfCustinstanceDO>> mycnfCustinstancesMap = mysqlParaHelper.getAndCheckExternalParameter(srcCustins);
            params.setMycnfCustinstancesMap(mycnfCustinstancesMap);
            params.setClone(true);
            params.setAccountMode(srcCustins.getAccountMode());
            DockerTaskInputParam dockerTaskInputParam = dockerManager.createDockerDbInstance(params);
            dockerTaskInputParam.setSrcCusIns(srcCustins);
            Map<String, Object> responseData = dockerManager.disPatchDockerTask(dockerTaskInputParam, true);
            custinsParamService.setCustinsParam(
                Integer.parseInt(responseData.get(ParamConstants.DB_INSTANCE_ID).toString()),
                CustinsParamSupport.DATADISK_STORAGE_TYPE,
                dataDiskCategory);
            taskService.updateTaskPenginePolicy(Integer.parseInt(responseData.get(ParamConstants.TASK_ID).toString()),
                mysqlParaHelper.getPenginePolicyID());

            return responseData;
        }

    
    private boolean cloneValidCommon(CustInstanceDO srcCustins, Boolean checkClone) throws RdsException {
        // 判断克隆实例个数是否达到限制
        Map<String, Object> condition = new HashMap<>(2);
        condition.put("custinsId", srcCustins.getId());
        List<Integer> status = new ArrayList<>();
        status.add(0);
        condition.put("status", status);
        List<Integer> cloneInsList = cloneEnabledCustinsService.getCloneCustInstanceByCondition(condition);
        if (cloneInsList.size() >= ResourceSupport.getInstance().getIntegerRealValue(
            ResourceKey.RESOURCE_CREATING_CLONE_CUSTINS_COUNT)) {
            throw new RdsException(ErrorCode.CREATING_CLONE_INS_EXCEEDED);
        }

        if (!checkClone || mysqlParaHelper.hasParameter(ParamConstants.DB_INSTANCE_NAME)) {
            String cloneInsName = CheckUtils.checkValidForInsName(mysqlParaHelper.getDBInstanceName());
            if (custinsService.hasCustInstanceByInsName(cloneInsName)) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }
        }

        if (!checkClone || mysqlParaHelper.hasParameter(ParamConstants.CONNECTION_STRING)) {
            CheckUtils.checkValidForConnAddrCust(mysqlParaHelper.getParameterValue(ParamConstants.CONNECTION_STRING));
        }

        return true;
    }

    public CustInstanceDO cloneSrcCustinsWithoutParentid(CustInstanceDO srcCustins, boolean isSetParentId)
        throws RdsException {
        CustInstanceDO cloneCustins = srcCustins.clone();
        cloneCustins.setId(null);
        cloneCustins.setClusterName("");
        cloneCustins.setStatus(CUSTINS_STATUS_CREATING);
        cloneCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        cloneCustins.setIsTmp(CUSTINS_INSTYPE_PRIMARY);
        cloneCustins.setInsType(CUSTINS_INSTYPE_PRIMARY);
        cloneCustins.setInsName(mysqlParaHelper.getDBInstanceName());
        if (isSetParentId) {
            cloneCustins.setParentId(srcCustins.getId());
        }
        cloneCustins.setMaintainStarttime(srcCustins.getMaintainStarttime());
        cloneCustins.setMaintainEndtime(srcCustins.getMaintainEndtime());

        Integer bizType = mysqlParaHelper.getAndCheckBizType();
        // 设置实例规格信息(gp不能让用户任意的选择规格,只能继承父实例的规格,包括levelID和groupCount)
        if (!cloneCustins.isGpdb()) {
            mysqlParaHelper.setInstanceLevel(cloneCustins,
                mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_CLASS),
                bizType, mysqlParaHelper.getParameterValue(ParamConstants.STORAGE));
        }

        // 设置实例描述
        if (mysqlParaHelper.hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            String desc = SupportUtils
                .decode(mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION));
            cloneCustins.setComment(CheckUtils
                .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION));
        }

        return cloneCustins;
    }

    public CustInstanceDO cloneGeneralSrcCustins(CustInstanceDO srcCustins)
            throws RdsException {
        CustInstanceDO cloneCustins = srcCustins.clone();
        cloneCustins.setId(null);
        cloneCustins.setClusterName(srcCustins.getClusterName());
        cloneCustins.setStatus(CUSTINS_STATUS_CREATING);
        cloneCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        cloneCustins.setIsTmp(CUSTINS_INSTYPE_PRIMARY);
        cloneCustins.setInsType(CUSTINS_INSTYPE_PRIMARY);
        cloneCustins.setInsName(mysqlParaHelper.getParameterValue("GeneralGroupName"));
        cloneCustins.setMaintainStarttime(srcCustins.getMaintainStarttime());
        cloneCustins.setMaintainEndtime(srcCustins.getMaintainEndtime());
        cloneCustins.setLevelId(srcCustins.getLevelId());
        Integer bizType = mysqlParaHelper.getAndCheckBizType();
        // 设置实例描述
        if (mysqlParaHelper.hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            String desc = SupportUtils
                    .decode(mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION));
            cloneCustins.setComment(CheckUtils
                    .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION));
        }

        return cloneCustins;
    }

    private Integer cloneInsOnDocker(CustInstanceDO srcCustins, CustInstanceDO cloneCustins, TransListDO trans,
                                     Map<String, Object> translistParamMap) throws RdsException {
        boolean success = false;
        List<CustInstanceDO> characterCustinsList = new ArrayList<>();
        try {

            custinsService.createCustInstanceForTrans(srcCustins, cloneCustins);
            // 如果未传NetType，默认为私网;
            Integer specifyNetType = CustinsSupport.NET_TYPE_PRIVATE;
            if (mysqlParaHelper.hasParameter("DBInstanceNetType")) {
                specifyNetType = CustinsSupport.getNetType(
                        mysqlParaHelper.getParameterValue("DBInstanceNetType"));
            }
            Boolean isVpcNetType = CustinsSupport.isVpcNetType(specifyNetType);
            Long diskSize = cloneCustins.getDiskSize();
            Long storageNum = diskSize / 1024L;
            String storage = storageNum.toString();
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(cloneCustins.getLevelId());

            // 克隆实例与源实例在同一个region
            AVZInfo avzInfo = avzSupport.getAVZInfo(ActionParamsProvider.ACTION_PARAMS_MAP.get());
            String clusterName = srcCustins.getClusterName();

            ClustersDO cluster= clusterService.getClusterByClusterName(clusterName);
            boolean isUserCluster = cluster.getType().equals(DEDICATED_HOST_GOURP_TYPE);
            CustInstanceDO physicalCustins = null;
            if (srcCustins.isCustinsOnDockerOnEcsLocalSSD()||isUserCluster) {
                List<CustInstanceDO> physicalCustinsList = custinsService.getCustInstanceByParentId(srcCustins.getId());
                physicalCustins = physicalCustinsList.get(0);
            }
            Integer queryCustinsId = physicalCustins != null ? physicalCustins.getId(): srcCustins.getId();

            String hostType = custinsService.getCustinsHostType(queryCustinsId);
            if (hostType == null) {
                hostType = "4";
            }

            String portStr = CustinsSupport.getConnPort(mysqlParaHelper.getParameterValue("port"),
                    cloneCustins.getDbType());
            Integer port = CustinsValidator.getRealNumber(portStr);


            ResourceContainer resourceContainer = null;
            resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, CustinsSupport.CONTAINER_TYPE_DOCKER);
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));
            Integer bizType = mysqlParaHelper.getAndCheckBizType();
            if (cloneCustins.isLogic()) {
                String composeTag = mysqlParaHelper.selectComposeTag(clusterName, isUserCluster);
                EngineCompose engineCompose = custinsIDao.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
                        cloneCustins.getDbType(), cloneCustins.getDbVersion(), insLevel.getCategory(), composeTag);
                custinsParamService.setCustinsParam(cloneCustins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
                JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());
                // init node instance info
                List<InstanceLevelRelDO> instanceLevelRels =
                        instanceService.getInstanceLevelRelByParentLevelId(cloneCustins.getLevelId(), null);
                if (instanceLevelRels.size() <= 0) {
                    logger.error("No instance level relation found for level id: " + cloneCustins.getLevelId());
                    throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                }

                JSONArray nodes = null;
                HashMap<String, List<JSONObject>> nodeInfo = new HashMap<>();
                List<String> characterNameList = new ArrayList<String>();
                for (InstanceLevelRelDO rel : instanceLevelRels) {
                    InstanceLevelDO characterInsLevel = instanceService.getInstanceLevelByLevelId(rel.getCharacterLevelId());
                    String extraInfo = null;
                    DockerInsLevelParseConfig config = null;
                    List<JSONObject> nodelist = nodeInfo.get(characterInsLevel.getDbType() + characterInsLevel.getDbVersion());

                    if (mysqlParaHelper.isRdsSrvDockerize(characterInsLevel.getDbType()) || hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD)
                            || isUserCluster) {
                        extraInfo = characterInsLevel.getExtraInfo();
                        config = mysqlParaHelper.parseDockerInsExtraInfo(extraInfo);
                        for (int i = 0; i < rel.getCharacterCustinsCount(); i++) {
                            JSONObject node = null;
                            String charcterInsName = null;
                            Integer charcterInsNetType = null;
                            JSONArray connAddrs = null;
                            if (nodelist != null) {
                                node = nodelist.get(i);
                                charcterInsName = node.get(ParamConstants.DB_INSTANCE_NAME).toString();
                                charcterInsNetType = CustinsSupport.getNetType(
                                        node.get(ParamConstants.DB_INSTANCE_NET_TYPE).toString());
                                CheckUtils.checkValidForInsName(charcterInsName);
                                if (custinsService.hasCustInstanceByInsName(charcterInsName) || characterNameList
                                        .contains(charcterInsName)) {
                                    throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
                                }
                                characterNameList.add(charcterInsName);
                                connAddrs = node.getJSONArray("ConnAddrs");
                            }

                            CustInstanceDO characterCustins = cloneCustins.clone();
                            if (hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD) &&!isUserCluster) {
                                RequestParamsDO params = mysqlParaHelper.inflateDockerParamsWhenClone(cloneCustins);
                                // for ecs min 20G
                                if (Validator.isNotNull(storage)) {
                                    if (Integer.parseInt(storage) != 0) {
                                        mysqlParaHelper.setDiskSize(characterCustins, bizType, storage, CustinsSupport.ECS_MIN_DISK_SIZE);
                                    } else {
                                        characterCustins.setDiskSize(0L);
                                    }
                                } else {
                                    characterCustins.setDiskSize(characterInsLevel.getDiskSize());
                                }
                                createDockerInstanceOnEcs(characterCustins, config, characterInsLevel,
                                        port, specifyNetType, i, cloneCustins.getId(), cloneCustins.getInsName(),
                                        resourceContainer, params, characterCustinsList);
                            } else if (hostType != null &&
                                    hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD) || isUserCluster) {
                                characterCustins.setDbType(characterInsLevel.getDbType());
                                characterCustins.setDbVersion(characterInsLevel.getDbVersion());
                                characterCustins.setCharacterType(characterInsLevel.getCharacterType());
                                characterCustins.setLevelId(characterInsLevel.getId());
                                //for host min 5G
                                if (Validator.isNotNull(storage)) {
                                    mysqlParaHelper.setDiskSize(characterCustins, bizType, storage, CustinsSupport.NC_MIN_DISK_SIZE);
                                } else {
                                    cloneCustins.setDiskSize(characterCustins.getDiskSize());
                                }
                                // TODO: ins name's policy
                                String insName = charcterInsName != null ? charcterInsName
                                        : cloneCustins.getInsName() + characterInsLevel.getId() + i;
                                characterCustins.setInsName(insName);
                                characterCustins.setParentId(cloneCustins.getId());
                                // create character custins
                                characterCustinsList.add(characterCustins);
                                custinsService.createCustInstance(characterCustins);
                                custinsParamService.setCustinsParam(
                                        characterCustins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);

                                // trans 设置目标物理实例的id
                                trans.setdCinsid(characterCustins.getId());

                                CustinsResModel custinsResModel = new CustinsResModel(
                                        characterCustins.getId());
                                custinsResModel.setVipResModelList(new ArrayList<>());
                                String connAddrCust = mysqlParaHelper.getConnAddrCust(
                                        characterCustins.getInsName().replace("_", "-"),
                                        mysqlParaHelper.getRegionIdByClusterName(physicalCustins.getClusterName()),
                                        characterCustins.getDbType());
                                VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
                                vipResModel.setVswitchId(mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID));
                                vipResModel.setVpcId(mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID));
                                vipResModel.setUserVisible(1);
                                vipResModel.setTunnelId(Integer.parseInt(mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID)));
                                vipResModel.setVip(mysqlParaHelper.getParameterValue(ParamConstants.IP_ADDRESS));
                                vipResModel.setVport(port);
                                vipResModel.setVpcInstanceId(
                                        mysqlParaHelper.getParameterValue(ParamConstants.VPC_INSTANCE_ID));
                                vipResModel.setConnAddrCust(connAddrCust);
                                custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                                custinsResModel.addVipResModel(vipResModel);

                                HostinsResModel hostinsResModel = new HostinsResModel(
                                        characterInsLevel.getId());
                                hostinsResModel.setHostType(Integer.parseInt(hostType));
                                hostinsResModel.setInsCount(config.getInsCount());
                                hostinsResModel.setInsPortCount(config.getPortCountPerIns());
                                hostinsResModel.setDiskType(config.getDiskType());
                                // set distribute mode
                                DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                                distributeRule.setSiteDistributeMode(DistributeMode.TRY_SCATTER);
                                distributeRule.setCabinetDistributeMode(config.getDistributePolicy().getCabinet());
                                distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);

                                custinsResModel.setHostinsResModel(hostinsResModel);
                                BakhistoryDO bakHistory = mysqlParaHelper.validCloneStorageForEcs(srcCustins, diskSize * 1024L, String.valueOf(translistParamMap.get("restoreType")), trans.getRecoverTime());
                                if(trans.getIsBaseTime()!=0) {
                                    DateTime date = dtzSupport.revertSpecificTimeZoneDateToUTC(trans.getRecoverTime(), DATA_SOURCE_DBAAS);
                                    Date restoreTime = dtzSupport.getSpecificTimeZoneDate(date, DATA_SOURCE_BAK);
                                    bakService.lockBinlogForRestore(srcCustins.getId(), bakHistory.getBakBegin(), restoreTime); // lock binlog for restore
                                    ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                                            JSON.toJSONString(ImmutableMap.of(
                                                    "custinsId", srcCustins.getId().toString(),
                                                    "begin", bakHistory.getBakBegin().getTime(),
                                                    "end", restoreTime.getTime())));
                                }
                                bakService.lockBakHisForRestore(bakHistory.getHisId());
                                ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakHistory.getHisId()));
                                if (hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {
                                    // 设置云盘属性
                                    List<EcsResModel> ecsResModelList = new ArrayList<>();

                                    EcsResModel ecsResModel = new EcsResModel("no-need");
                                    ecsResModel.setEcsAccount("no-need");
                                    ecsResModel.setRegionId(mysqlParaHelper.transferRegionId(mysqlParaHelper.getParameterValue(ParamConstants.REGION_ID)));
                                    ecsResModel.setZoneId(mysqlParaHelper.transferZoneId(mysqlParaHelper.getParameterValue(ParamConstants.ZONE_ID)));
                                    ecsResModel.setInstanceType("no-need");
                                    ecsResModel.setInsCount(characterInsLevel.getInsCount());
                                    ecsResModel.setEcsVSwitchId(mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID));
                                    ecsResModel.setEcsVpcId(mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID));


                                    ecsResModel.setVpcType(VpcType.USER_VPC);

                                    // 显示指定磁盘大小为0或者规格中声明为0
                                    String storageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
                                    String dataDiskCategory = custinsService.getDataDiskCategory(srcCustins.getId(), storageType, hostType);
                                    ecsResModel.setDataDiskCategory(dataDiskCategory);
                                    if(diskSize != 0L) {
                                        List<EcsDataDisk> dataDiskList = new ArrayList<>();
                                        EcsDataDisk dataDisk = new EcsDataDisk();
                                        dataDisk.setCategory(dataDiskCategory);
                                        dataDisk.setDevice(CustinsSupport.ECS_DEISK_PREFIX + "/data");
                                        dataDisk.setSize(diskSize);
                                        JSONObject bakHistObject = JSONArray.parseObject(bakHistory.getSlaveStatus());
                                        JSONArray bakHistArray = bakHistObject.getJSONArray(bakHistObject.keySet().toArray()[0].toString());
                                        JSONObject jsonObject = bakHistArray.getJSONObject(0);
                                        String snapShotId = jsonObject.getJSONObject("slave_status").getString("SNAPSHOT_ID");

                                        ecsResModel.setSnapshotId(snapShotId);
                                        dataDisk.setSnapshotId(snapShotId);
                                        dataDisk.setRegionId(ecsResModel.getRegionId());
                                        dataDisk.setZoneId(ecsResModel.getZoneId());
                                        dataDiskList.add(dataDisk);
                                        ecsResModel.setEcsDataDiskList(dataDiskList);
                                    }
                                    // set host res model
                                    ecsResModelList.add(ecsResModel);
                                    custinsResModel.setEcsResModelList(ecsResModelList);
                                    // 云盘no_need_host_disk
                                    hostinsResModel.setDiskType(DISK_TYPE_NO_NEED);
                                    hostinsResModel.setDiskSizeSold(null);
                                } else {
                                    // 本地盘 disktype 需要设置 disk_type_all
                                    hostinsResModel.setDiskType(DISK_TYPE_ALL);
                                }
                                resourceContainer.addCustinsResModel(custinsResModel);
                                // 用户集群 传入clusterName 参数给资源管理器
                                resourceContainer.setClusterName(srcCustins.getClusterName());
                            } else {
                                characterCustins.setDbType(characterInsLevel.getDbType());
                                if (CustinsSupport.DB_TYPE_POLARDB_MYSQL_RO.equals(characterInsLevel.getDbType())) {
                                    characterCustins.setInsType(3);
                                }
                                characterCustins.setDbVersion(characterInsLevel.getDbVersion());
                                characterCustins.setCharacterType(characterInsLevel.getCharacterType());
                                characterCustins.setLevelId(characterInsLevel.getId());
                                //for host min 5G
                                if (Validator.isNotNull(storage)) {
                                    mysqlParaHelper.setDiskSize(characterCustins, bizType, storage, CustinsSupport.NC_MIN_DISK_SIZE);
                                } else {
                                    cloneCustins.setDiskSize(characterCustins.getDiskSize());
                                }
                                // TODO: ins name's policy
                                String insName = charcterInsName != null ? charcterInsName
                                        : cloneCustins.getInsName() + characterInsLevel.getId() + i;
                                characterCustins.setInsName(insName);
                                characterCustins.setParentId(cloneCustins.getId());
                                // create character custins
                                characterCustinsList.add(characterCustins);
                                custinsService.createCustInstance(characterCustins);

                                CustinsResModel custinsResModel = new CustinsResModel(
                                        characterCustins.getId());
                                // TODO: conn type
                                if (config.getVipCount() > 0) {
                                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                                    for (int j = 0; j < config.getVipCount(); j++) {
                                        String connAddrSting = characterCustins.getInsName().replace("_", "-") + j;
                                        Integer dbNetType = charcterInsNetType != null ? charcterInsNetType
                                                : specifyNetType;
                                        Integer tunnelId = null;
                                        String vpcId = null;
                                        String vswitchId = null;
                                        String ipAddress = null;
                                        String vpcInstanceId = null;
                                        if (connAddrs != null) {
                                            JSONObject connAddr = connAddrs.getJSONObject(j);
                                            connAddrSting = connAddr.get(ParamConstants.CONNECTION_STRING).toString();
                                            tunnelId = CustinsSupport.NET_TYPE_VPC.equals(charcterInsNetType) ?
                                                    Integer.valueOf(connAddr.get(ParamConstants.TUNNEL_ID).toString())
                                                    : null;
                                            vpcId = CustinsSupport.NET_TYPE_VPC.equals(charcterInsNetType) ?
                                                    connAddr.get(ParamConstants.VPC_ID).toString() : null;
                                            vswitchId = CustinsSupport.NET_TYPE_VPC.equals(charcterInsNetType) ?
                                                    connAddr.get(ParamConstants.VSWITCH_ID).toString() : null;
                                            ipAddress = CustinsSupport.NET_TYPE_VPC.equals(charcterInsNetType) ?
                                                    connAddr.get(ParamConstants.IP_ADDRESS).toString() : null;
                                            vpcInstanceId = CustinsSupport.NET_TYPE_VPC.equals(charcterInsNetType) ?
                                                    connAddr.get(ParamConstants.VPC_INSTANCE_ID).toString() : null;
                                        }
                                        String connAddrCust = mysqlParaHelper.getConnAddrCust(connAddrSting,
                                                mysqlParaHelper.getRegionIdByClusterName(physicalCustins.getClusterName()),
                                                CustinsSupport.CONTAINER_TYPE_DOCKER);
                                        VipResModel vipResModel = mysqlParaHelper.initVipResModel(
                                                dbNetType, connAddrCust, port, config.getVportCountPerVip(), tunnelId,
                                                vpcId, vswitchId, ipAddress, vpcInstanceId);
                                        custinsResModel.addVipResModel(vipResModel);
                                    }
                                } else {
                                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
                                }

                                HostinsResModel hostinsResModel = new HostinsResModel(
                                        characterInsLevel.getId());
                                hostinsResModel.setHostType(characterInsLevel.getHostType());
                                hostinsResModel.setInsCount(config.getInsCount());
                                hostinsResModel.setInsPortCount(config.getPortCountPerIns());
                                hostinsResModel.setDiskType(config.getDiskType());
                                // set distribute mode
                                DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                                distributeRule.setSiteDistributeMode(config.getDistributePolicy().getSite());
                                distributeRule.setCabinetDistributeMode(config.getDistributePolicy().getCabinet());
                                distributeRule.setHostDistributeMode(config.getDistributePolicy().getHost());

                                custinsResModel.setHostinsResModel(hostinsResModel);
                                resourceContainer.addCustinsResModel(custinsResModel);
                            }
                        }
                    } else {
                        RequestParamsDO params = mysqlParaHelper.inflateDockerParamsWhenClone(cloneCustins);
                        mysqlParaHelper.createRdsServiceForDocker(cloneCustins, config, characterInsLevel,
                                specifyNetType, resourceContainer, params, "rds");
                    }
                }
                // init logic custins resource model
                CustinsResModel custinsResModel = new CustinsResModel(
                        cloneCustins.getId());
                custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
                if (cloneCustins.isPolarDB()) {
                    VipResModel vipResModel = mysqlParaHelper.initLogicInsVipResMode(cloneCustins);
                    custinsResModel.addVipResModel(vipResModel);
                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                }
                if (isUserCluster) {
                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                }
                // put into resource container
                resourceContainer.addCustinsResModel(custinsResModel);
            } else {
                CustinsResModel custinsResModel = new CustinsResModel(cloneCustins.getId());

                String extraInfo = insLevel.getExtraInfo();
                DockerInsLevelParseConfig config = mysqlParaHelper.parseDockerInsExtraInfo(extraInfo);
                if (config.getVipCount() > 0) {
                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                    for (int j = 0; j < config.getVipCount(); j++) {
                        String connAddrCust = mysqlParaHelper.getConnAddrCust(
                                cloneCustins.getInsName().replace("_", "-") + j,
                                mysqlParaHelper.getRegionIdByClusterName(srcCustins.getClusterName()),
                                CustinsSupport.CONTAINER_TYPE_DOCKER);
                        VipResModel vipResModel = mysqlParaHelper.initVipResModel(
                                specifyNetType, connAddrCust, port, config.getVportCountPerVip());
                        custinsResModel.addVipResModel(vipResModel);
                    }
                } else {
                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
                }

                HostinsResModel hostinsResModel = new HostinsResModel(
                        insLevel.getId());
                hostinsResModel.setHostType(insLevel.getHostType());
                hostinsResModel.setInsCount(config.getInsCount());
                hostinsResModel.setInsPortCount(config.getPortCountPerIns());
                hostinsResModel.setDiskType(config.getDiskType());

                DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                distributeRule.setSiteDistributeMode(config.getDistributePolicy().getSite());
                distributeRule.setCabinetDistributeMode(config.getDistributePolicy().getCabinet());
                distributeRule.setHostDistributeMode(config.getDistributePolicy().getHost());
                custinsResModel.setHostinsResModel(hostinsResModel);
                resourceContainer.addCustinsResModel(custinsResModel);
            }

            Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
            if (!response.getCode().equals(200)) {

                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            if (!isUserCluster) {
                avzSupport.updateAVZInfoByInstanceIds(avzInfo,
                        response.getData().getCustinsResRespModelList().get(0).getInstanceIdList());
                custinsParamService.updateAVZInfo(cloneCustins.getId(), avzInfo);
            } else {
                custinsParamService.updateAVZInfoZoneId(cloneCustins.getId(), avzInfo);
            }

            CustinsIpWhiteListDO custinsIpWhiteList = new CustinsIpWhiteListDO(null,
                    "127.0.0.1");
            if (cloneCustins.isLogic()) {
                custinsIpWhiteList.setCustinsId(cloneCustins.getId());
                ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
                for (CustInstanceDO characterCustins : characterCustinsList) {
                    custinsIpWhiteList.setCustinsId(characterCustins.getId());
                    ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
                }
            } else {
                custinsIpWhiteList.setCustinsId(cloneCustins.getId());
                ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
            }

            if (hostType != null &&
                    hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD)||isUserCluster) {

                final Integer finalPhysicalCustinsId = characterCustinsList.get(0).getId();
                AllocateResRespModel.CustinsResRespModel allocateResRespModel = response.getData().getCustinsResRespModelList().stream().filter(
                        (AllocateResRespModel.CustinsResRespModel res) -> finalPhysicalCustinsId==null || res.getCustinsId().equals(finalPhysicalCustinsId)).findFirst().get();
                List<Integer> cloneInsIds = allocateResRespModel.getInstanceIdList();
                trans.setdHinsid1(cloneInsIds.get(0));
                if (cloneInsIds.size() > 1) {
                    trans.setdHinsid2(cloneInsIds.get(1));
                } else {
                    trans.setdHinsid2(0);
                }
                String restoreType = (String) translistParamMap.get("restoreType");
                Integer taskId = this.cloneDockerOnLocalEcsSSDCustinsTask(trans, srcCustins, cloneCustins,
                        RESTORE_TYPE_TIME.equals(restoreType), mysqlParaHelper.getAction(),
                        mysqlParaHelper.getOperatorId());
                success = true;
                return taskId;
            }

            Map<String, Object> taskQueueParam = new HashMap<String, Object>(1);
            Map<String, Object> baklistParam = new HashMap<String, Object>(3);
            BaklistDO srcBaklist = bakService.getBaklistByCustinsId(srcCustins.getId());
            baklistParam.put("retention", srcBaklist.getRetention());
            baklistParam.put("bak_period", srcBaklist.getBakPeriod());

            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
            String bakBeginString = formatter.format(srcBaklist.getBakBegin());

            baklistParam.put("bak_begin", bakBeginString);
            taskQueueParam.put("backup", baklistParam);

            String restoreType = (String)translistParamMap.get("restoreType");
            taskQueueParam.put("restore_type", restoreType);
            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                taskQueueParam.put("recover_time", trans.getRecoverTime().toString());
            } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                taskQueueParam.put("bakhis_id", trans.getBakhisId());
            }

            Integer taskId = dockerCustinsService.cloneDockerInstanceTask(
                    mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(), srcCustins, cloneCustins,
                    characterCustinsList, taskQueueParam, hostType);
            success = true;
            return taskId;
        } finally {
            if (!success) {

                if (cloneCustins.getId() != null) {
                    custinsService.deleteCustInstance(cloneCustins);
                }
                for (CustInstanceDO characterCustins : characterCustinsList) {
                    if (characterCustins.getId() != null) {
                        custinsService.deleteCustInstance(characterCustins);
                    }
                }
                if(ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")){
                    JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
                    bakService.unlockBinlogForRestore(
                            Integer.valueOf(lockBinlog.get("custinsId").toString()),
                            new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                            new Date(Long.parseLong(lockBinlog.get("end").toString()))
                    );
                }
                if(ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
                    String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
                    bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));

                }
            }

        }
    }


    private Integer cloneGeneralInsOnDocker(CustInstanceDO srcCustins, CustInstanceDO cloneCustins, TransListDO trans,
                                     Map<String, Object> translistParamMap) throws RdsException {
        boolean success = false;
        List<CustInstanceDO> characterCustinsList = new ArrayList<>();
        try {

            custinsService.createCustInstanceForTrans(srcCustins, cloneCustins);
            // 如果未传NetType，默认为私网;
            Integer specifyNetType = CustinsSupport.NET_TYPE_PRIVATE;
            if (mysqlParaHelper.hasParameter("DBInstanceNetType")) {
                specifyNetType = CustinsSupport.getNetType(
                    mysqlParaHelper.getParameterValue("DBInstanceNetType"));
            }
            Boolean isVpcNetType = CustinsSupport.isVpcNetType(specifyNetType);
            CustInstanceDO physicalCustins = mySQLGeneralService.getMasterIns(srcCustins.getId());
            Long diskSize = physicalCustins.getDiskSize();
            Long storageNum = diskSize / 1024L;
            String storage = storageNum.toString();
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(physicalCustins.getLevelId());

            // 克隆实例与源实例在同一个region
            AVZInfo avzInfo = avzSupport.getAVZInfo(ActionParamsProvider.ACTION_PARAMS_MAP.get());
            String clusterName = srcCustins.getClusterName();

            ClustersDO cluster= clusterService.getClusterByClusterName(clusterName);
            boolean isUserCluster = cluster.getType().equals(DEDICATED_HOST_GOURP_TYPE);
            Integer queryCustinsId = physicalCustins != null ? physicalCustins.getId(): srcCustins.getId();

            String hostType = custinsService.getCustinsHostType(queryCustinsId);
            if (hostType == null) {
                hostType = "4";
            }

            String portStr = CustinsSupport.getConnPort(mysqlParaHelper.getParameterValue("port"),
                cloneCustins.getDbType());
            Integer port = CustinsValidator.getRealNumber(portStr);


            ResourceContainer resourceContainer = null;
            resourceContainer = avzSupport.getRegionInitialedResourceContainer(avzInfo, CustinsSupport.CONTAINER_TYPE_DOCKER);
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));
            Integer bizType = mysqlParaHelper.getAndCheckBizType();
            List<EndpointDO> endpointList = new ArrayList<>();
            if (cloneCustins.isLogic()) {
                String composeTag = mysqlParaHelper.selectComposeTag(clusterName, isUserCluster);
                EngineCompose engineCompose = custinsIDao.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
                        cloneCustins.getDbType(), cloneCustins.getDbVersion(), insLevel.getCategory(), composeTag);
                custinsParamService.setCustinsParam(cloneCustins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
                JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());


                JSONArray nodes = null;
                HashMap<String, List<JSONObject>> nodeInfo = new HashMap<>();
                List<String> characterNameList = new ArrayList<String>();
                InstanceLevelDO characterInsLevel = instanceService.getInstanceLevelByLevelId(physicalCustins.getLevelId());
                String extraInfo = null;
                DockerInsLevelParseConfig config = null;
                List<JSONObject> nodelist = nodeInfo.get(characterInsLevel.getDbType() + characterInsLevel.getDbVersion());


                extraInfo = characterInsLevel.getExtraInfo();
                config = mysqlParaHelper.parseDockerInsExtraInfo(extraInfo);
                String connType = mysqlParaHelper.getAndCheckConnType(null);


                JSONObject node = null;
                String charcterInsName = null;
                Integer charcterInsNetType = null;
                JSONArray connAddrs = null;
                if (nodelist != null) {
                    node = nodelist.get(0);
                    charcterInsName = node.get(ParamConstants.DB_INSTANCE_NAME).toString();
                    charcterInsNetType = CustinsSupport.getNetType(
                            node.get(ParamConstants.DB_INSTANCE_NET_TYPE).toString());
                    CheckUtils.checkValidForInsName(charcterInsName);
                    if (custinsService.hasCustInstanceByInsName(charcterInsName) || characterNameList
                            .contains(charcterInsName)) {
                        throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
                    }
                    characterNameList.add(charcterInsName);
                    connAddrs = node.getJSONArray("ConnAddrs");
                }


                CustInstanceDO characterCustins = cloneCustins.clone();
                if (hostType != null &&
                        hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD) || isUserCluster) {
                    characterCustins.setDbType(characterInsLevel.getDbType());
                    characterCustins.setDbVersion(characterInsLevel.getDbVersion());
                    characterCustins.setCharacterType(characterInsLevel.getCharacterType());
                    characterCustins.setLevelId(characterInsLevel.getId());
                    //for host min 5G
                    if (Validator.isNotNull(storage)) {
                        mysqlParaHelper.setDiskSize(characterCustins, bizType, storage, CustinsSupport.NC_MIN_DISK_SIZE);
                    } else {
                        cloneCustins.setDiskSize(characterCustins.getDiskSize());
                    }

                    String insName = mysqlParaHelper.getAndCheckDBInstanceName();
                    characterCustins.setInsName(insName);
                    characterCustins.setParentId(cloneCustins.getId());
                    // create character custins
                    characterCustinsList.add(characterCustins);
                    custinsService.createCustInstance(characterCustins);
                    custinsParamService.setCustinsParam(
                            characterCustins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);

                    // trans 设置目标物理实例的id
                    trans.setdCinsid(characterCustins.getId());

                    CustinsResModel custinsResModel = new CustinsResModel(
                            characterCustins.getId());
                    custinsResModel.setVipResModelList(new ArrayList<>());
                    String connAddrCust = mysqlParaHelper.getConnAddrCust(
                            characterCustins.getInsName().replace("_", "-"),
                            mysqlParaHelper.getRegionIdByClusterName(physicalCustins.getClusterName()),
                            characterCustins.getDbType());
                    VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
                    vipResModel.setVswitchId(mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID));
                    vipResModel.setVpcId(mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID));
                    vipResModel.setUserVisible(1);
                    vipResModel.setTunnelId(Integer.parseInt(mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID)));
                    vipResModel.setVip(mysqlParaHelper.getParameterValue(ParamConstants.IP_ADDRESS));
                    vipResModel.setVport(port);
                    vipResModel.setVpcInstanceId(
                            mysqlParaHelper.getParameterValue(ParamConstants.VPC_INSTANCE_ID));
                    vipResModel.setConnAddrCust(connAddrCust);
                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                    custinsResModel.addVipResModel(vipResModel);

                    HostinsResModel hostinsResModel = new HostinsResModel(
                            characterInsLevel.getId());
                    hostinsResModel.setHostType(Integer.parseInt(hostType));
                    hostinsResModel.setInsCount(config.getInsCount());
                    hostinsResModel.setInsPortCount(config.getPortCountPerIns());
                    hostinsResModel.setDiskType(config.getDiskType());
                    // set distribute mode
                    DistributeRule distributeRule = hostinsResModel.getDistributeRule();
                    distributeRule.setSiteDistributeMode(DistributeMode.TRY_SCATTER);
                    distributeRule.setCabinetDistributeMode(config.getDistributePolicy().getCabinet());
                    distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);

                    custinsResModel.setHostinsResModel(hostinsResModel);
                    BakhistoryDO bakHistory = mysqlParaHelper.validCloneStorageForEcs(srcCustins, diskSize * 1024L, String.valueOf(translistParamMap.get("restoreType")), trans.getRecoverTime());
                    if (trans.getIsBaseTime() != 0) {
                        DateTime date = dtzSupport.revertSpecificTimeZoneDateToUTC(trans.getRecoverTime(), DATA_SOURCE_DBAAS);
                        Date restoreTime = dtzSupport.getSpecificTimeZoneDate(date, DATA_SOURCE_BAK);
                        bakService.lockBinlogForRestore(srcCustins.getId(), bakHistory.getBakBegin(), restoreTime); // lock binlog for restore
                        ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                                JSON.toJSONString(ImmutableMap.of(
                                        "custinsId", srcCustins.getId().toString(),
                                        "begin", bakHistory.getBakBegin().getTime(),
                                        "end", restoreTime.getTime())));
                    }
                    trans.setsHinsid1(bakHistory.getHostinsId());
                    trans.setsCinsid(instanceService.getInstanceByInsId(bakHistory.getHostinsId()).getCustinsId());
                    bakService.lockBakHisForRestore(bakHistory.getHisId());
                    ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakHistory.getHisId()));
                    RdsRegionDO rdsRegionDO =  regionService.getRegion(mysqlParaHelper.getAndCheckRegionID());
                    String ecsAccount = rdsRegionDO.getUserName();
                    List<com.aliyun.dba.ecs.dataobject.EcsImageDO> ecsImageDOList = null;
                    // 得到镜像
                    try {
                        ecsImageDOList = ecsImageService.getEcsImageList(mysqlParaHelper.getAndCheckRegionID(),
                                characterCustins.getDbType(), null, null, null,
                                CustinsParamSupport.CUSTINS_PARAM_VALUE_ECS_ARCH_VERSION_VPC, null);
                    } catch (Exception e) {
                        logger.info("get ecs image error, use region get ecs image.., error msg is " + e.getMessage());
                        ecsImageDOList = ecsImageService.getEcsImageList(mysqlParaHelper.getAndCheckRegionID(),
                                CustinsSupport.CONTAINER_TYPE_DOCKER, null, null, null,
                                null, null);
                    }
                    String imageId = ecsImageDOList.get(0).getImageId();
                    if (hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {

                        String vpcId = mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID);
                        CheckUtils.checkValidForVPCId(vpcId);
                        String tunnelId = mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID);
                        CheckUtils.checkValidForTunnelId(tunnelId);
                        String vswitchID = mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID);
                        // 设置云盘属性
                        List<EcsResModel> ecsResModelList = new ArrayList<>();

                        EcsResModel ecsResModel = new EcsResModel(imageId);
                        ecsResModel.setVpcType(VpcType.USER_VPC);
                        ecsResModel.setEcsVSwitchId(vswitchID);
                        ecsResModel.setEcsVpcId(vpcId);
                        ecsResModel.setInsCount(characterInsLevel.getInsCount());
                        ecsResModel.setRegionId(mysqlParaHelper.transferRegionId(mysqlParaHelper.getAndCheckRegionID()));
                        ecsResModel.setZoneId(mysqlParaHelper.transferZoneId(mysqlParaHelper.getParameterValue(ParamConstants.ZONE_ID)));
                        ecsResModel.setInstanceType(characterInsLevel.getEcsClassCode());
                        ecsResModel.setEcsAccount(ecsAccount);
                        ecsResModel.setInsPortCount(config.getPortCountPerIns());
                        ecsResModel.setLevelId((int) characterInsLevel.getId());
                        ecsResModel.setDbType(physicalCustins.getDbType());
                        String storageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
                        String dataDiskCategory = custinsService.getDataDiskCategory(null, storageType, hostType);
                        if (CustinsSupport.DB_TYPE_MYSQL.equals(physicalCustins.getDbType()) && StringUtils.isNotEmpty(dataDiskCategory)
                                && dataDiskCategory.contains(DockerOnEcsConstants.ECS_ClOUD_ESSD)) {
                            ecsResModel.setPerformanceLevel(DockerOnEcsConstants.getEssdPerLevel(dataDiskCategory));
                            ecsResModel.setDataDiskCategory(dataDiskCategory);
                        }

                        // 显示指定磁盘大小为0或者规格中声明为0

                        List<EcsDataDisk> dataDiskList = new ArrayList<>();
                        if(diskSize != 0L) {
                            EcsDataDisk dataDisk = new EcsDataDisk();
                            dataDisk.setSize(mysqlParaHelper.getExtendDiskSizeForEcsIns(CustinsSupport.DB_TYPE_DOCKER, diskSize));
                            if (dataDiskCategory.contains(DockerOnEcsConstants.ECS_ClOUD_ESSD)) {
                                dataDisk.setCategory(DockerOnEcsConstants.ECS_ClOUD_ESSD);
                            }else {
                                dataDisk.setCategory(dataDiskCategory);
                            }
                            dataDisk.setDevice(CustinsSupport.ECS_DEISK_PREFIX + "/data");
                            dataDisk.setRegionId(mysqlParaHelper.getAndCheckRegionID());

                            dataDisk.setZoneId(mysqlParaHelper.getParameterValue(ParamConstants.ZONE_ID));
                            dataDiskList.add(dataDisk);
                            JSONObject bakHistObject = JSONArray.parseObject(bakHistory.getSlaveStatus());
                            JSONArray bakHistArray = bakHistObject.getJSONArray(bakHistObject.keySet().toArray()[0].toString());
                            JSONObject jsonObject = bakHistArray.getJSONObject(0);
                            String snapShotId = jsonObject.getJSONObject("slave_status").getString("SNAPSHOT_ID");

                            ecsResModel.setSnapshotId(snapShotId);
                            dataDisk.setSnapshotId(snapShotId);
                            ecsResModel.setEcsDataDiskList(dataDiskList);
                        }else{
                            ecsResModel.setDataDiskSize(0L);
                        }

                        // set host res model
                        ecsResModelList.add(ecsResModel);
                        custinsResModel.setEcsResModelList(ecsResModelList);
                        // 云盘no_need_host_disk
                        hostinsResModel.setDiskType(DISK_TYPE_NO_NEED);
                        hostinsResModel.setDiskSizeSold(diskSize);
                    } else {
                        // 本地盘 disktype 需要设置 disk_type_all
                        hostinsResModel.setDiskType(DISK_TYPE_ALL);
                    }


                    resourceContainer.addCustinsResModel(custinsResModel);
                    // 用户集群 传入clusterName 参数给资源管理器
                    resourceContainer.setClusterName(srcCustins.getClusterName());
                }



                // init logic custins resource model
                CustinsResModel custinsResModel = new CustinsResModel(
                    cloneCustins.getId());
                custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
                if (cloneCustins.isPolarDB()) {
                    VipResModel vipResModel = mysqlParaHelper.initLogicInsVipResMode(cloneCustins);
                    custinsResModel.addVipResModel(vipResModel);
                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                }
                cloneCustins.setClusterName(clusterName);
                VipResModel logicInsVipResModel = mysqlParaHelper.initGeneralLogicInsVipResModel(cloneCustins, connType);
                String primaryEndpointName = mysqlParaHelper.getParameterValue("PrimaryEndpointName");
                if (StringUtils.isEmpty(primaryEndpointName)) {
                    primaryEndpointName = cloneCustins.getInsName();
                }

                EndpointDO endpoint = endpointService.getOrCreateEndpoint(primaryEndpointName,
                        EndpointSupport.ENPOINT_TYPE_PRIMARY, cloneCustins.getId(), cloneCustins.getInsName());
                endpointList.add(endpoint);
                logicInsVipResModel.setEndpointId(endpoint.getId());
                custinsResModel.addVipResModel(logicInsVipResModel);
                if (isUserCluster) {
                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                }
                // put into resource container
                resourceContainer.addCustinsResModel(custinsResModel);
            }

            Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
            if (!response.getCode().equals(200)) {
                for (EndpointDO endpoint : endpointList) {
                    endpointIDao.deleteEndpointById(endpoint.getId());
                    endpointConfigIDao.deleteEndpointConfigByEndpointId(endpoint.getId());
                }
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            if (!isUserCluster) {
                avzSupport.updateAVZInfoByInstanceIds(avzInfo,
                        response.getData().getCustinsResRespModelList().get(0).getInstanceIdList());
                custinsParamService.updateAVZInfo(cloneCustins.getId(), avzInfo);
            } else {
                custinsParamService.updateAVZInfoZoneId(cloneCustins.getId(), avzInfo);
            }

            CustinsIpWhiteListDO custinsIpWhiteList = new CustinsIpWhiteListDO(null,
                "127.0.0.1");
            if (cloneCustins.isLogic()) {
                custinsIpWhiteList.setCustinsId(cloneCustins.getId());
                ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
                for (CustInstanceDO characterCustins : characterCustinsList) {
                    custinsIpWhiteList.setCustinsId(characterCustins.getId());
                    ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
                }
            } else {
                custinsIpWhiteList.setCustinsId(cloneCustins.getId());
                ipWhiteListService.createCustinsIpWhiteList(custinsIpWhiteList);
            }

            if (hostType != null &&
                    hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD)||isUserCluster) {

                final Integer finalPhysicalCustinsId = characterCustinsList.get(0).getId();
                AllocateResRespModel.CustinsResRespModel allocateResRespModel = response.getData().getCustinsResRespModelList().stream().filter(
                        (AllocateResRespModel.CustinsResRespModel res) -> finalPhysicalCustinsId==null || res.getCustinsId().equals(finalPhysicalCustinsId)).findFirst().get();
                List<Integer> cloneInsIds = allocateResRespModel.getInstanceIdList();
                trans.setdHinsid1(cloneInsIds.get(0));
                if (cloneInsIds.size() > 1) {
                    trans.setdHinsid2(cloneInsIds.get(1));
                } else {
                    trans.setdHinsid2(0);
                }
                String restoreType = (String) translistParamMap.get("restoreType");
                Integer taskId = this.cloneDockerOnLocalEcsSSDCustinsTask(trans, srcCustins, cloneCustins,
                        RESTORE_TYPE_TIME.equals(restoreType), mysqlParaHelper.getAction(),
                        mysqlParaHelper.getOperatorId());
                success = true;
                return taskId;
            }

            Map<String, Object> taskQueueParam = new HashMap<String, Object>(1);
            Map<String, Object> baklistParam = new HashMap<String, Object>(3);
            BaklistDO srcBaklist = bakService.getBaklistByCustinsId(srcCustins.getId());
            baklistParam.put("retention", srcBaklist.getRetention());
            baklistParam.put("bak_period", srcBaklist.getBakPeriod());

            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
            String bakBeginString = formatter.format(srcBaklist.getBakBegin());

            baklistParam.put("bak_begin", bakBeginString);
            taskQueueParam.put("backup", baklistParam);

            String restoreType = (String)translistParamMap.get("restoreType");
            taskQueueParam.put("restore_type", restoreType);
            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                taskQueueParam.put("recover_time", trans.getRecoverTime().toString());
            } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                taskQueueParam.put("bakhis_id", trans.getBakhisId());
            }

            Integer taskId = dockerCustinsService.cloneDockerInstanceTask(
                mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(), srcCustins, cloneCustins,
                characterCustinsList, taskQueueParam, hostType);
            success = true;
            return taskId;
        } finally {
            if (!success) {

                if (cloneCustins.getId() != null) {
                    custinsService.deleteCustInstance(cloneCustins);
                }
                for (CustInstanceDO characterCustins : characterCustinsList) {
                    if (characterCustins.getId() != null) {
                        custinsService.deleteCustInstance(characterCustins);
                    }
                }
                if(ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")){
                    JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
                    bakService.unlockBinlogForRestore(
                        Integer.valueOf(lockBinlog.get("custinsId").toString()),
                        new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                        new Date(Long.parseLong(lockBinlog.get("end").toString()))
                        );
                }
                if(ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
                    String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
                    bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));

                }
            }

        }
    }


    private void createDockerInstanceOnEcs(CustInstanceDO custins,
                                          DockerInsLevelParseConfig config,
                                          InstanceLevelDO characterInsLevel,
                                          Integer port,
                                          Integer netType,
                                          Integer custinsOrder,
                                          Integer parentId,
                                          String paretInsName,
                                          ResourceContainer resourceContainer,
                                          RequestParamsDO params,
                                          List<CustInstanceDO> characterCustinsList) throws RdsException {
        // 设置custins参数
        custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_ECS);
        custins.setDbType(characterInsLevel.getDbType());
        custins.setDbVersion(characterInsLevel.getDbVersion());
        custins.setCharacterType(characterInsLevel.getCharacterType());
        custins.setLevelId(characterInsLevel.getId());
        custins.setMaxAccounts(1);
        custins.setMaxDbs(0);
        custins.setConnType(params.getConnType());
        // TODO: ins name's policy
        custins.setInsName(custins.getInsName() +
            characterInsLevel.getId() + custinsOrder);
        custins.setParentId(parentId);
        // create character custins
        characterCustinsList.add(custins);
        custinsService.createCustInstance(custins);
        String regionId = params.getRegionId();

        String zoneId = params.getZoneId();
        String vSwitchId = null;
        String vpcId = null;

        // 初始化ECSAPI

        // Get ecsAccount, vpcId, vSwitchId
        String ecsAccount = ecsService.getEcsAccount(custins.getUserId(), regionId);
        // 得到镜像
        List<com.aliyun.dba.ecs.dataobject.EcsImageDO> ecsImageDOList = null;
        if (custins.isMariaDB()) {
            try {
                ecsImageDOList = ecsImageService.getEcsImageList(mysqlParaHelper.getAndCheckRegionID(),
                        custins.getDbType(), null, null, null,
                        CustinsParamSupport.CUSTINS_PARAM_VALUE_ECS_ARCH_VERSION_VPC, null);
            } catch (RdsException e) {
                if (ErrorCode.ECSIMAGE_NOT_FOUND.getDesc().equals(e.getMessage())) {
                    logger.info("get ecs image error, use region get ecs image.., error msg is " + e.getMessage());
                    ecsImageDOList = ecsImageService.getEcsImageList(mysqlParaHelper.getAndCheckRegionID(),
                            CustinsSupport.CONTAINER_TYPE_DOCKER, null, null, null,
                            null, null);
                } else {
                    throw e;
                }
            }
        } else {
            ecsImageDOList = ecsImageService.getEcsImageList(params.getRegionId(),
                    CustinsSupport.CONTAINER_TYPE_DOCKER, null, null, null, null, null);
        }
        String imageId = ecsImageDOList.get(0).getImageId();

        String osPassword = SupportUtils.getRandomPasswdForEcs(15);
        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        custinsResModel.setConnType(params.getConnType());

        if (config.getVipCount() > 0){
            if (CustinsSupport.isVpcNetType(params.getNetType())) {
                String connAddrCust = mysqlParaHelper.getConnAddrCust(
                    custins.getInsName().replace("_", "-"),
                    mysqlParaHelper.getRegionIdByClusterName(custins.getClusterName()),
                    custins.getDbType());
                VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
                vipResModel.setVswitchId(params.getVswitchId());
                vipResModel.setVpcId(params.getUserVpcId());
                vipResModel.setUserVisible(1);
                vipResModel.setTunnelId(params.getTunnelId());
                vipResModel.setVip(params.getIpaddress());
                vipResModel.setVport(port);
                //todo: set value from yaochi
                vipResModel.setVpcInstanceId(paretInsName);
                vipResModel.setConnAddrCust(connAddrCust);
                custinsResModel.addVipResModel(vipResModel);
            } else {
                custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                for (int j = 0; j < config.getVipCount(); j++) {
                    String connAddrCust = mysqlParaHelper.getConnAddrCust(
                        custins.getInsName().replace("_", "-") + j,
                        mysqlParaHelper.getRegionIdByClusterName(custins.getClusterName()),
                        custins.getDbType());
                    if (StringUtils.isBlank(connAddrCust)) {
                        connAddrCust = mysqlParaHelper.getConnAddrCust(
                            custins.getInsName().replace("_", "-") + j,
                                mysqlParaHelper.getRegionIdByClusterName(custins.getClusterName()),
                            CustinsSupport.CONTAINER_TYPE_DOCKER);
                    }
                    VipResModel vipResModel = mysqlParaHelper.initVipResModel(
                        netType, connAddrCust, port, config.getVportCountPerVip());
                    custinsResModel.addVipResModel(vipResModel);

                }
            }
        } else {
            custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
        }


        String ecsClass = characterInsLevel.getEcsClassCode();
        EcsResModel ecsResModel = new EcsResModel(imageId);
        ecsResModel.setVpcType(VpcType.USER_VPC);
        ecsResModel.setEcsVSwitchId(params.getVswitchId());
        ecsResModel.setEcsVpcId(params.getUserVpcId());
        ecsResModel.setOsPassword(osPassword);
        ecsResModel.setInsCount(characterInsLevel.getInsCount());
        ecsResModel.setRegionId(mysqlParaHelper.transferRegionId(regionId));
        ecsResModel.setZoneId(mysqlParaHelper.transferZoneId(zoneId));
        ecsResModel.setInstanceType(ecsClass);
        ecsResModel.setEcsAccount(ecsAccount);
        ecsResModel.setInsPortCount(config.getPortCountPerIns());
        ecsResModel.setLevelId((int) characterInsLevel.getId());

        // 显示指定磁盘大小为0或者规格中声明为0
        String dataDiskCategory = ResourceSupport.getInstance()
            .getStringRealValue(ResourceKey.RESOURCE_ECS_DATA_DISK_CATEGORY)
            .trim();
        if(custins.getDiskSize() != 0L) {
            List<EcsDataDisk> dataDiskList = new ArrayList<>();
            EcsDataDisk dataDisk = new EcsDataDisk();
            dataDisk.setCategory(dataDiskCategory);
            dataDisk.setDevice(CustinsSupport.ECS_DEISK_PREFIX + "/data");
            dataDisk.setSize(custins.getDiskSize());
            dataDisk.setRegionId(regionId);
            dataDisk.setZoneId(zoneId);
            dataDiskList.add(dataDisk);
            ecsResModel.setEcsDataDiskList(dataDiskList);
        }
        // set host res model
        custinsResModel.setEcsResModel(ecsResModel);
        // put into resource container
        resourceContainer.addCustinsResModel(custinsResModel);
        // create ecs acc
        dbsService.createEcsOsAccount(custins, osPassword);
    }

    public Integer cloneDockerOnLocalEcsSSDCustinsTask(TransListDO translist,CustInstanceDO srcCustins,
                                                       CustInstanceDO cloneCustins, Boolean isRestoreByTime,
                                                       String action, Integer operatorId) {
        Map<String, Object> taskQueueParam = new HashMap<>();
        taskQueueParam.put("isUserCluster", true);
        // 克隆实例不修改原实例状态了
        CustInstanceDO srcPhysicalCustins = custinsService.getCustInstanceByCustinsId(translist.getsCinsid());
        CustInstanceDO dstPhysicalCustins = custinsService.getCustInstanceByCustinsId(translist.getdCinsid());

        // 高权限账号需要在此处同步，同步逻辑实例上的高权限账号
        dbsService.syncUserAccountsforEcs(srcCustins, cloneCustins);
        dbsService.syncAllDbsAndAccounts(srcPhysicalCustins, dstPhysicalCustins);
        instanceIDao.createTransList(translist);
        // trans 创建完成之后，再插入
        taskQueueParam.put("trans_list_id", translist.getId());
        BaklistDO srcBaklist = bakService.getBaklistByCustinsId(srcCustins.getId());
        BaklistDO dstBakList = srcBaklist.clone();
        dstBakList.setCustinsId(cloneCustins.getId());
        bakService.createBaklistDO(dstBakList);
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, cloneCustins.getId(), TaskSupport.TASK_TYPE_CUSTINS,
                "clone_ins",JSONObject.toJSONString(taskQueueParam));
        taskService.createTaskQueue(taskQueue);
        instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
        return taskQueue.getId();
    }
}
