package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.pojo.ShardsInfo;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelRelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_DOCKER;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultTransferResourceRequestImpl")
public class TransferResourceRequestImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(EvaluateRegionResourceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(TransferResourceRequestImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Resource
    private MysqlParameterHelper mysqlParaHelper;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Resource
    private DockerCommonService dockerCommonService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params)
            throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);

            String storageType = mysqlParamSupport.getAndCheckStorageType(params);
            String region = avzSupport.getMainLocation(params);
            String clusterName = mysqlParaHelper.getParameterValue(ParamConstants.CLUSTER_NAME);
            String storage = mysqlParaHelper.getParameterValue(ParamConstants.STORAGE);
            String actionName = mysqlParaHelper.getParameterValue(ParamConstants.ACTION);
            if (StringUtils.isBlank(storageType)) {
                storageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            }
            // Engine参数必传
            String dbType = mysqlParaHelper.getAndCheckDBType(null);
            // Docker 实例集群的db_type类型为docker,引入临时变量做调整
            String dbTypeForCluster = dbType;
            String classCode = null;
            if (!CustinsSupport.isHBase(dbType)) {
                classCode = mysqlParaHelper.getAndCheckDBInstanceClassCode(null, dbType);
            }
            String dbVersion = mysqlParaHelper.getAndCheckDBVersion(dbType, false);
            Integer bizType = mysqlParaHelper.getAndCheckBizType();

            mysqlParaHelper.getAndSetContainerTypeAndHostTypeIfEmpty(dbType, dbVersion, classCode);

            String containerType = mysqlParaHelper.getParameterValue(ParamConstants.CONTAINER_TYPE,
                    CustinsSupport.CONTAINER_TYPE_HOST);
            String hostType = mysqlParaHelper.getAndCheckHostType();
            custins = new CustInstanceDO();
            custins.setDbType(dbType);
            custins.setDbVersion(dbVersion);
            custins = mysqlParaHelper.setInstanceLevel(custins, classCode, bizType,
                    mysqlParaHelper.getParameterValue( ParamConstants.STORAGE));
            if (CustinsSupport.isCustinsOnEcs(hostType, dbVersion, null)) {
                custins.setKindCode(KIND_CODE_ECS_VM);
            }
            if ((CustinsSupport.isContainerTypeDocker(containerType)
                    && CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD.equals(hostType))) {
                custins.setKindCode(KIND_CODE_DOCKER_ON_ECS);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }

            if (CustinsSupport.isContainerTypeDocker(containerType)
                    && !CustinsSupport.KIND_CODE_DOCKER_ON_ECS.equals(custins.getKindCode())
                    && !CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE.equals(custins.getKindCode())) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER);
                dbTypeForCluster = DB_TYPE_DOCKER;
            }
            if (CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD.equals(hostType)) {
                custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD);
            }
            custins.setDbTypeForCluster(dbTypeForCluster);

            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            Set<Integer> hostIdSet = new HashSet<>();
            boolean isUserCluster = false;
            if (StringUtils.isNotBlank(clusterName)) { // 仅在输入集群名时HostId参数才有效
                custins.setClusterName(clusterName);
                hostIdSet = mysqlParaHelper.getAndCheckHostIdSet();
                isUserCluster = mysqlEngineCheckService.checkUserClusterCustins(custins);
            }
            // todo 专享主机组, 如果需要评估的规格是物理实例, 则默认是只读实例 后续拓扑有改动 这里有坑 需要改动.. 因为目前inslevel 没有判断是否是只读实例的这种标记
            if (isUserCluster && insLevel.isMySQLPhysical()) {
                custins.setInsType(CustinsSupport.CUSTINS_INS_TYPE_READINS);
            }

            //支持不评估资源的action
            List<String> modifyActionList = Arrays.asList("ModifyDBInstanceClass", "EvaluateModifyRegionResource");
            if (modifyActionList.contains(actionName)) {
                String targetDBInstanceClass = mysqlParaHelper.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS);
                InstanceLevelDO targetInsLevel = instanceService.getInstanceLevelByClassCode(targetDBInstanceClass,
                        custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
                if (targetInsLevel == null || StringUtils.equals(insLevel.getClassCode(), targetDBInstanceClass)) {
                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                    data.put(ParamConstants.ENGINE_VERSION, insLevel.getDbVersion());
                    data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(insLevel.getDbType()));
                    return data;
                } else {
                    insLevel = targetInsLevel;
                }
            }

            if (custins.isPhysicalChild() && insLevel.isMySQLLogic()) {
                List<InstanceLevelRelDO> instanceLevelRels = instanceService.getInstanceLevelRelByParentLevelId(insLevel.getId(), null);
                if (instanceLevelRels != null && instanceLevelRels.size() > 0) {
                    InstanceLevelDO characterInsLevel = instanceService.getInstanceLevelByLevelId(instanceLevelRels.get(0).getCharacterLevelId());
                    if (characterInsLevel != null) {
                        insLevel = characterInsLevel;
                    }
                }
            }
            String connType = mysqlParaHelper.getAndCheckConnType(null);
            if (connType == null) {
                connType = CustinsSupport.CONN_TYPE_LVS;
            }
            custins.setConnType(connType);

            Integer netType = mysqlParaHelper.getAndCheckNetType();
            custins.setNetType(netType);

            // 以下参数仅对创建基于ECS的实例有效
            String regionId = mysqlParaHelper.getAndCheckRegionID();;
            String zoneId = mysqlParaHelper.getParameterValue(ParamConstants.ZONE_ID);
            if (StringUtils.isBlank(zoneId)) {
                // 入参没有传递ecs zoneid ,查看关联实例
                zoneId = dockerCommonService.getZoneIdByCustins(custins);
            }

            String multiAVZExParamStr = mysqlParaHelper.getParameterValue(ParamConstants.MULTI_AVZ_EX_PARAM);
            if (isUserCluster) {
                regionId = mysqlParaHelper.getAndCheckRegionID();
            }

            if (isUserCluster && CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_DEFAULT.equals(hostType)) {
                hostType = custins.isCustinsOnDockerOnEcsLocalSSD() ? CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD : CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD;
            }

            // 以下参数仅对创建基于Polar或NC的实例有效: init shardsInfoMap.
            String shardsInfo = null;
            Map<String, List<ShardsInfo.Node>> nodesMap = new HashMap<String, List<ShardsInfo.Node>>();
            if (custins.isCustinsDockerOnNC()) {
                shardsInfo = mysqlParaHelper.getParameterValue(ParamConstants.SHARDS_INFO);
                nodesMap = mysqlParaHelper.SplitNodesFromShardsInfobyDbTypeAndDbVersion(shardsInfo);
            }

            // init resource container
            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(params,
                    CustinsSupport.CONTAINER_TYPE_DOCKER, isUserCluster, null);
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));

            dockerCommonService.packageResourceContainer(resourceContainer, custins, insLevel, hostType, storage, bizType, hostIdSet,
                    regionId, zoneId, multiAVZExParamStr, nodesMap, netType, connType, storageType, isUserCluster,1);
            //调用资源API
            Map<String, Object> data = new HashMap<String, Object>();
            Map<String, String> requestsData = new HashMap<>();
            requestsData.put("type", "ResourceContainer");
            requestsData.put("value", JSON.toJSONString(resourceContainer));
            data.put("Requests", Collections.singletonList(requestsData));
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
