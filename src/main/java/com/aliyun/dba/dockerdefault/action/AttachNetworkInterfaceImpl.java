package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.DockerCustinsService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultAttachNetworkInterfaceImpl")
public class AttachNetworkInterfaceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(AttachNetworkInterfaceImpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected DockerCustinsService dockerCustinsService;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        CustInstanceDO logicalCustins;
        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            logicalCustins = custinsService.getCustInstanceByInsName(null, mysqlParaHelper.getAndCheckDBInstanceName(),
                0);
            if (logicalCustins == null) {
                return ResponseSupport.createErrorResponse(ErrorCode.DBNAME_NOT_FOUND);
            }

            if (!logicalCustins.isActive()) {
                //实例状态错误
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            // 修改实例状态为修改网络链接中
            logicalCustins.setStatus(CustinsState.STATE_NET_MODIFYING.getState());
            logicalCustins.setStatusDesc(CustinsState.STATE_NET_MODIFYING.getDesc());
            custinsService.updateCustInstance(logicalCustins);

            List<CustInstanceDO> physicalCustinsList = custinsService.getCustInstanceByParentId(
                logicalCustins.getId());

            if (physicalCustinsList == null) {
                return ResponseSupport.createErrorResponse(ErrorCode.DBNAME_NOT_FOUND);
            }

            // 期望子实例数为1
            if (physicalCustinsList.size() != 1) {
                logger.info("physical custins count does not meet our expectations");
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            if (!physicalCustinsList.get(0).isActive()) {
                //实例状态错误
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            Map<String, Object> taskQueueParam = new HashMap<>(4);
            taskQueueParam.put("EcsInsId", mysqlParaHelper.getParameterValue(ParamConstants.ECS_INS_ID));
            taskQueueParam.put("NetworkInterfaceId",
                mysqlParaHelper.getParameterValue(ParamConstants.NETWORK_INTERFACE_ID));
            taskQueueParam.put("NetworkInterfaceIp",
                mysqlParaHelper.getParameterValue(ParamConstants.NETWORK_INTERFACE_IP));
            taskQueueParam.put("NetworkInterfaceMac",
                mysqlParaHelper.getParameterValue(ParamConstants.NETWORK_INTERFACE_MAC));

            Integer taskId = dockerCustinsService.createAttachNetworkInterfaceTask(mysqlParaHelper.getAction(),
                mysqlParaHelper.getOperatorId(),
                physicalCustinsList.get(0), taskQueueParam);
            Map<String, Object> data = new HashMap<>(4);
            data.put(ParamConstants.TASK_ID, taskId);
            data.put(ParamConstants.DB_INSTANCE_ID, logicalCustins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, logicalCustins.getInsName());

            return data;
        } catch (RdsException re) {
            logger.error("AttachNetworkInterface ex=" + re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
