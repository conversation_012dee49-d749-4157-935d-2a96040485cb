package com.aliyun.dba.dockerdefault.action;

import com.alibaba.cobar.util.StringUtil;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.docker.dataobject.DockerTaskInputParam;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.dockerdefault.service.DockerManager;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.dataobject.EcsDiskDO;
import com.aliyun.dba.ecs.support.EcsConstants;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.*;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.aliyun.dba.ecs.service.EcsDiskService;

import java.sql.Time;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.entity.CustinsState.STATE_READINS_MAINTAINING;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getOperatorId;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getPenginePolicyID;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_CLOUDRO_STORAGE_TYPE;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_ESSD_STORAGE_SIZE;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ_BACKUP;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultCreateReadDBInstanceImpl")
public class CreateReadDBInstanceImpl implements IAction {

    //private static final Logger logger = Logger.getLogger(CreateDBInstanceImpl.class);
    private static final LogAgent logger = LogFactory.getLogAgent(CreateReadDBInstanceImpl.class);
    public static final String CUSTINS_PARAM_NAME_COMPOSE_TAG = "compose_tag";

    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected AccountService accountService;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsResourceService custinsResourceService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MySQLService mySQLService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private DockerManager dockerManager;
    @Autowired
    private EcsDiskService ecsDiskService;
    @Autowired
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    private DockerCommonService dockerCommonService;

    /**
     * 获取 primary physical 实例 storageType
     *  参考 DescribeDBInstanceList接口
     *
     * @return
     */
    public String getPrimaryCustinsStorageType(Integer primaryCustinsId){

        String dataDiskCategory = CustinsSupport.STORAGE_TYPE_CLOUD_SSD;// default value

        if (primaryCustinsId != null) {
            List<EcsDiskDO> ecsDiskList = ecsDiskService.getEcsDiskByCustinsId(primaryCustinsId);
            for (EcsDiskDO ecsDiskDO : ecsDiskList) {
                if (EcsConstants.DISK_TYPE_DATA.equals(ecsDiskDO.getType())) {
                    String performanLevel = ecsDiskService.getEcsTypeFromEcsDiskParamByDiskId(ecsDiskDO.getDiskId());
                    if (StringUtils.isEmpty(performanLevel)) {
                        dataDiskCategory = ecsDiskDO.getCategory();
                    }else {
                        dataDiskCategory = DockerOnEcsConstants.getEssdPerLevel(performanLevel);
                    }
                }
            }
        }

        return dataDiskCategory;
    }

    private static Map<String, Integer> metaDBTimeZoneDiffMap = new ConcurrentHashMap<>();

    /**
     * 获取云盘只读 存储类型

     * @param dataDiskCategory, 控制台/瑶池 storageType
     * @param primaryCustinsId, 原主 物理层实例id
     * @param readinsDiskSize, 只读存储空间, 单位MB
     * @return
     * @throws RdsException
     */
    public String getAndCheckCloudReadinStorageType(String dataDiskCategory, Integer primaryCustinsId, Long readinsDiskSize) throws RdsException{

        // 原主 storageType
        String primaryStorageType = getPrimaryCustinsStorageType(primaryCustinsId);

        if (StringUtils.isBlank(dataDiskCategory)){
            // 没有指定, 对齐原主
            dataDiskCategory = primaryStorageType;
        }

        // 1 存储系列不降级检查, 云盘只读存储规格 >= 原主实例
        Integer primaryWeight = DockerOnEcsConstants.ECS_CLOUD_WEIGHT_MAP.get(primaryStorageType);
        Integer readinsWeight = DockerOnEcsConstants.ECS_CLOUD_WEIGHT_MAP.get(dataDiskCategory);
        if (readinsWeight < primaryWeight) {
            throw new RdsException(INVALID_CLOUDRO_STORAGE_TYPE);
        }

        // 2 只读空间检查, essd pl2 >= 465 * 1024MB, pl3 >= 1265 * 1024MB
        Long minSize = DockerOnEcsConstants.ESSD_MIN_SIZE_MAP.get(dataDiskCategory) * 1024L;
        if (minSize != null && readinsDiskSize != null && minSize.compareTo(readinsDiskSize) > 0){
            throw new RdsException(INVALID_ESSD_STORAGE_SIZE);
        }
        return dataDiskCategory;
    }


    /**
     * 创建只读实例
     *
     * @category CreateReadDBInstance
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO srcCustins, Map<String, String> actionParams) throws RdsException {
        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            srcCustins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            // 创建docker化的只读实例
            if (!srcCustins.isCustinsDockerOnEcs() && !srcCustins.isCustinsDockerOnEcsLocalSSD()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_KIND_CODE);
            }

            //获取当前自实例下的主实例
            CustInstanceDO primaryCustins = null;
            boolean isUserCluster = mysqlEngineCheckService.checkUserClusterCustins(srcCustins);
            for (CustInstanceDO childCustIns : custinsService.getCustInstanceUnitByParentIdAndCharacterType(srcCustins.getId(),
                    CustinsSupport.CHARACTER_TYPE_PHYSICAL)) {
                if (childCustIns.isPrimary() && !isUserCluster) {
                    primaryCustins = childCustIns;
                    break;
                } else if (isUserCluster && childCustIns.isPrimary() && childCustIns.getIsTmp() != 1) {
                    primaryCustins = childCustIns;
                    break;
                }
            }
            if (primaryCustins == null) {
                logger.info("Get primary custins from cluster %s." + srcCustins.getId() + "error");
                return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);

            }

            if (!primaryCustins.isPrimary()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);

            }

            if (primaryCustins.isReadAndWriteLock()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);

            }

            if (CustinsState.STATE_READINS_TRANSING.getComment().equals(primaryCustins.getStatusDesc()) ||
                    CustinsState.STATE_MAINTAINING.getComment().equals(primaryCustins.getStatusDesc()) ||
                    CustinsState.STATE_TRANSING.getComment().equals(primaryCustins.getStatusDesc())) {
                // 主实例迁移中不能创建只读
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);

            }

            if (!primaryCustins.isActive() && !primaryCustins.isReadMAorReadTR()) {
                //只读创建过滤 只读只读实例维护 与只读实例升降级状态
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);

            }

			// Fix #26217559 MySQL 8.0基础版不支持只读实例
			if (primaryCustins.isMysql80()) {
				InstanceLevelDO primaryInstanceLevel = instanceService.getInstanceLevelByLevelId(primaryCustins.getLevelId());
				if (CustinsSupport.BASIC_LEVEL.equalsIgnoreCase(primaryInstanceLevel.getCategory())) {
					return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
				}
			}

            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(mysqlParamSupport.getAndCheckClassCode(actionParams),
                            srcCustins.getDbType(), srcCustins.getDbVersion(), srcCustins.getTypeChar(), null);
            if(Objects.nonNull(insLevel) && InstanceLevel.CategoryEnum.BASIC.toString().equals(insLevel.getCategory())){
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
            }


            String instype = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_USED_TYPE);
            boolean createbackupreadins = false;
            if (instype != null && instype.equals(CUSTINS_INSTYPE_READ_BACKUP.toString())) {
                createbackupreadins = true;
            }

            if (!createbackupreadins && custinsService.countReadCustInstanceByPrimaryCustinsId(primaryCustins.getId()) >=
                    ResourceSupport.getInstance().getIntegerRealValue(
                            ResourceKey.RESOURCE_CUSTINS_MYSQL_READINS_COUNT)) {//超过只读实例个数限制
                return ResponseSupport.createErrorResponse(ErrorCode.READINSTANCE_EXCEEDED);
            }

            boolean usingReplicator = this.getIsReplicatorReadDBInstance(); // 确认是否使用 Proxy Binlog 复制器
            String region = mysqlParamSupport.getAndCheckRegion(actionParams);
            if (usingReplicator) {
                // - 对于直连只读实例, 不要求与主实例同 Region (原注释: 2.7.20 只读实例不要求与主实例在同一个 Region)
                // - 由于目前 BLS 限制, 要求 BLS 只读实例同 Region (FIXME: 在 BLS 去除该限制后移除这个校验)
                String custinsRegion = clusterService.getRegionByCluster(primaryCustins.getClusterName());
                if (!region.equals(custinsRegion)) {
                    return ResponseSupport.createErrorResponse(ErrorCode.INVALID_REGION);

                }
            }

            RequestParamsDO params = new RequestParamsDO();
            params.setPrimaryCustinsId(primaryCustins.getId());
            params.setInsType(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_USED_TYPE));
            params.setReadInsName(CheckUtils.checkValidForInsName(mysqlParamSupport.getParameterValue(actionParams, "readdbinstancename")));
            if (custinsService.hasCustInstanceByInsName(params.getReadInsName())) {
                return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);

            }
            params.setDbType(srcCustins.getDbType());
            params.setDbVersion(srcCustins.getDbVersion());

            params.setUsingReplicator(this.getIsReplicatorReadDBInstance()); // 设置启用复制器标记
            params.setNetType(CustinsSupport.getNetType(
                    mysqlParamSupport.getParameterValue(actionParams, "dbinstancenettype",
                            CustinsSupport.NET_TYPE_PRIVATE.toString())));
            params.setRegionId(mysqlParaHelper.getAndCheckRegionID());
            params.setZoneId(mysqlParaHelper.getAndCheckAvZone());

            //vpc info
            params.setVpcInstanceId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID));
            params.setUserVpcId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VPC_ID));
            params.setTunnelId(Integer.valueOf(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.TUNNEL_ID)));
            params.setVswitchId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VSWITCH_ID));
            params.setIpaddress(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.IP_ADDRESS));
            // 获取实例连接端口
            String portStr = CustinsSupport
                    .getConnPort(mysqlParamSupport.getParameterValue(actionParams, "port"), srcCustins.getDbType());
            params.setPortStr(portStr);
            // 获取实例连接地址
            params.setConnAddrCust(CheckUtils
                    .checkValidForConnAddrCust(mysqlParamSupport.getParameterValue(actionParams, "connectionstring")));

            // todo 优先 region参数传入param，其次使用原实例所在region
            if(StringUtil.isEmpty(params.getRegion())){
                params.setRegion(mysqlParamSupport.getParameterValue(actionParams,"Region"));
            }
            if (StringUtil.isEmpty(params.getRegion())){
                params.setRegion(clusterService.getRegionByCluster(srcCustins.getClusterName()));
            }

            // 专享集群的必须要将clustername 透传给res 才能正确分配主机
            if (isUserCluster) {
                params.setClusterName(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CLUSTER_NAME));
            }

            // 创建连接地址对象
            String vpcInstanceId = null;
            if (CustinsSupport.isVpcNetType(params.getNetType())) {
                vpcInstanceId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID);
                if (vpcInstanceId == null) {
                    vpcInstanceId = params.getReadInsName();
                }
                params.setVpcInstanceId(vpcInstanceId);
            }
            params.setClassCode(mysqlParamSupport.getAndCheckClassCode(actionParams));


            Long diskSize = null;
            String storage = mysqlParamSupport.getParameterValue(actionParams, "storage");
            if (Validator.isNotNull(storage)) {
                Integer maxDiskSize = ResourceSupport.getInstance()
                        .getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
                Integer minDiskSize = primaryCustins.isCustinsOnDockerOnEcsLocalSSD() ? CustinsSupport.NC_MIN_DISK_SIZE : CustinsSupport.ECS_MIN_DISK_SIZE;
                diskSize = CheckUtils.parseInt(storage, minDiskSize,
                        maxDiskSize, ErrorCode.INVALID_STORAGE) * 1024L;
            } else {
                diskSize = srcCustins.getDiskSize();
            }

            params.setStorage(Long.toString(diskSize / 1024));

            // get 存储类型
            // 控制台/瑶池 storageType
            String dataDiskCategoryInput = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            String dataDiskCategory = DockerOnEcsConstants.LOCAL_SSD.equals(dataDiskCategoryInput) ? dataDiskCategoryInput : getAndCheckCloudReadinStorageType(dataDiskCategoryInput, primaryCustins.getId(), diskSize);
            //logger.error("debug_getcategory=" + dataDiskCategory);
            params.setDataDiskCategory(dataDiskCategory);

            String comment = "";
            if (mysqlParamSupport.hasParameter(actionParams, "dbinstancedescription")) {
                String desc = SupportUtils.decode(mysqlParamSupport.getParameterValue(actionParams, "dbinstancedescription"));
                comment = CheckUtils
                        .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
            }
            params.setDesc(comment);

            // 设置实例可维护时间
            Date maintainStartTime = mysqlParamSupport
                    .getAndCheckTimeByParam(actionParams, ParamConstants.MAINTAIN_STARTTIME, DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
                            ErrorCode.INVALID_STARTTIME,
                            CustinsSupport.DEFAULT_MAINTAIN_START_TIME);
            Date maintainEndTime = mysqlParamSupport
                    .getAndCheckTimeByParam(actionParams, ParamConstants.MAINTAIN_ENDTIME, DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
                            ErrorCode.INVALID_ENDTIME, CustinsSupport.DEFAULT_MAINTAIN_END_TIME);
            params.setMaintainStartTime(
                    Time.valueOf(DateSupport.timeWithSecond2str(maintainStartTime)));
            params.setMaintainEndTime(
                    Time.valueOf(DateSupport.timeWithSecond2str(maintainEndTime)));

            params.setOperatorId(getOperatorId(actionParams));
            params.setAction(mysqlParamSupport.getAction(actionParams));

            String composeTag = mysqlParaHelper.selectComposeTag(srcCustins.getClusterName(), isUserCluster);
            custinsParamService.setCustinsParam(srcCustins.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);

            //TODO:FIX ME USE LCOAL HTTP CALL--END
            DockerTaskInputParam dockerTaskInputParam = dockerManager.createDockerDBReadInstance(actionParams, params,
                    primaryCustins, srcCustins);// srcCustins, logic实例, primaryCustins, physical实例
            dockerTaskInputParam.setSrcCusIns(srcCustins);
            Map<String, Object> responseData = dockerManager.disPatchDockerTask(dockerTaskInputParam, false);
            taskService.updateTaskPenginePolicy(Integer.parseInt(responseData.get(ParamConstants.TASK_ID).toString()), getPenginePolicyID(actionParams));
            // 大客户专享集群 修改主的父实例状态
            if (isUserCluster) {
                custinsService.updateCustInstanceStatusByCustinsId(srcCustins.getId(), STATE_READINS_MAINTAINING.getState(),
                        STATE_READINS_MAINTAINING.getComment());
            }
            return responseData;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }

    protected boolean getIsReplicatorReadDBInstance() {
        return mysqlParaHelper.getParameterValue("UseReplicator", "0").equals("1");
    }
}
