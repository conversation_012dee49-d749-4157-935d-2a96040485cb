package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.AccountsDTO;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultCreateAdminAccountImpl")
public class CreateAdminAccountImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CreateAdminAccountImpl.class);

    @Autowired
    private AccountService accountService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private DbossApi dbossApi;
    @Autowired
    private AccountIDao accountIDao;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CustinsService custinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);


            String bid = mysqlParaHelper.getBID();
            String uid = mysqlParaHelper.getUID();

            CustInstanceDO custIns = mysqlParaHelper.getAndCheckCustInstance();

            String accountBizType = mysqlParaHelper.getAndCheckAccountBizType(custIns);
            String accountName = mysqlParaHelper.getAccountName();
            String dbInsName = mysqlParaHelper.getDBInstanceName();
            String password = mysqlParaHelper.getAndCheckDecryptedAccountPassword();
            String comment = SupportUtils.decode(mysqlParaHelper.getParameterValue("accountdescription", ""));
            //获取priviledgeType参数，如果用户未指定则赋予默认值PRIVILEDGE_ADMIN_READONLY
            AccountPriviledgeType priviledgeType =
                mysqlParaHelper.getAdminPriviledgeType(AccountPriviledgeType.PRIVILEDGE_ADMIN_READONLY);

            // 增加docker类型实例的账户检查
            if (custIns.isCustinsOnDocker() && !custinsService.checkCreateAccountValidForCustinsOnDocker(custIns)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            AccountsDTO addAccountsDTO = new AccountsDTO();
            addAccountsDTO.setBid(bid);
            addAccountsDTO.setUid(uid);
            addAccountsDTO.setDbInsName(dbInsName);
            addAccountsDTO.setAccount(accountName);
            addAccountsDTO.setPassword(password);
            addAccountsDTO.setComment(comment);
            addAccountsDTO.setPriviledgeType(priviledgeType);//管理员权限
            addAccountsDTO.setBizType(accountBizType);//账户类型
            //action仍旧传入，防止任务状态可查询(action置为null)下沉到下面的方法中

            if (dbossApi.isCreateAdminAccountByDoss(custIns) && addAccountsDTO.isDataTransferPrivilege()) {
                if (custIns.isReadAndWriteLock()) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
                }
                if (!custIns.inAvailableStatus()) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
                String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID, "");

                Map<String, Object> account = new HashMap<>();
                account.put("accountName", accountName);
                account.put("custinsId", custIns.getId());
                account.put("privilegeType", addAccountsDTO.getPriviledgeType());
                account.put("password", password);
                account.put("requestId", requestId);
                if (!Strings.isNullOrEmpty(comment)) {
                    account.put("comment", CheckUtils.checkLength(comment, 1, 256, ErrorCode.INVALID_DBDESCRIPTION));
                }
                AccountsDO dbossaccount = null;
                dbossaccount = accountService.getAndBasicCheckAccount(addAccountsDTO, custIns);
                dbossApi.createAccount(account);
                dbossaccount.setStatus(DbsSupport.STATUS_ACTIVE);
                accountIDao.addAccount(dbossaccount);

                Map<String, Object> data1 = new HashMap<>();
                data1.put(ParamConstants.ACCOUNT_ID, dbossaccount.getId());
                data1.put(ParamConstants.ACCOUNT_NAME, dbossaccount.getAccount());
                data1.put("AccountStatus", DbsSupport.STATUS_ACTIVE);
                data1.put(ParamConstants.ACCOUNT_BIZ_TYPE, dbossaccount.getBizType());
                data1.put(ParamConstants.ACCOUNT_ADMIN_TYPE,
                    AccountPriviledgeType.getAdminTypeByPriviledgeValue(
                        dbossaccount.getPriviledgeType()));
                data1.put("TaskId", 0);
                return data1;
            }

            AccountsDTO accountDTO = null;
            if (custIns.isCustinsOnDocker()) {
                accountDTO = accountService.addAccountForCustinsOnDocker(addAccountsDTO,
                    mysqlParaHelper.getAction(),
                    mysqlParaHelper.getOperatorId());
            } else {
                accountDTO = accountService.addAccount(addAccountsDTO, mysqlParaHelper.getAction(),
                    mysqlParaHelper.getOperatorId(), null);
            }
            taskService.updateTaskPenginePolicy(accountDTO.getTaskId(), mysqlParaHelper.getPenginePolicyID());

            Map<String, Object> data = new HashMap<>(5);
            data.put(ParamConstants.ACCOUNT_ID, accountDTO.getId());
            data.put(ParamConstants.ACCOUNT_NAME, accountDTO.getAccount());
            data.put(ParamConstants.ACCOUNT_STATUS, accountDTO.getStatus());
            data.put(ParamConstants.ACCOUNT_BIZ_TYPE, accountDTO.getBizType());
            //正常情况下priviledgeType不可能为null
            data.put(ParamConstants.ACCOUNT_ADMIN_TYPE,
                AccountPriviledgeType.getAdminTypeByPriviledgeValue(
                    accountDTO.getPriviledgeType()));
            data.put(ParamConstants.TASK_ID, accountDTO.getTaskId());
            return data;
        } catch (RdsException re) {
            logger.error("createAdminAccount ex=" + re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("createAdminAccount ex=" + ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
