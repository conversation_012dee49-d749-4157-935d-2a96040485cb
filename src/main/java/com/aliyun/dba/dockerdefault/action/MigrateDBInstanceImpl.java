package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.physical.action.ModifyDBInstanceClassImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultMigrateDBInstanceImpl")
public class MigrateDBInstanceImpl implements IAction {


    private static final LogAgent logger = LogFactory.getLogAgent(MigrateDBInstanceImpl.class);

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {
            com.aliyun.dba.dockerdefault.action.ModifyDBInstanceClassImpl dockerdefaultModifyDBInstanceClassImpl = SpringContextUtil
                .getBeanByClass(com.aliyun.dba.dockerdefault.action.ModifyDBInstanceClassImpl.class);
            return dockerdefaultModifyDBInstanceClassImpl.doActionRequest(custins, actionParams);
        } catch(RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
