package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_STOP;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.DEDICATED_HOST_GOURP_TYPE;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.TASK_STOP_DBINSTANCE;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultStopDBInstanceClassImpl")
public class StopDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(StopDBInstanceImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsService custinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
            throws RdsException {
        try{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            CustInstanceDO custins = mysqlParaHelper.getAndCheckCustInstance();
            if (!custins.isActive()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);//实例状态错误
            }
            ClustersDO clustersDO = clusterService.getClusterByClusterName(custins.getClusterName());
            if (clustersDO == null || !DEDICATED_HOST_GOURP_TYPE.equals(clustersDO.getType()) ||
                    custins.isCustinsOnDockerOnEcsLocalSSD()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_STOP,
                    CustinsState.STATE_STOPED.getComment());

            TaskQueueDO taskQueueDO = new TaskQueueDO(mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(),
                    custins.getId(), TASK_STOP_DBINSTANCE);
            taskService.createTaskQueue(taskQueueDO);

            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskQueueDO.getId());

            return data;
        } catch (RdsException re) {
            logger.error("StopDBInstance ex="+re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("StopDBInstance ex="+ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
