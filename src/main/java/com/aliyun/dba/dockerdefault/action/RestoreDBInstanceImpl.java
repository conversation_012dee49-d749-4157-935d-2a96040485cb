package com.aliyun.dba.dockerdefault.action;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;

/**
 * docker_on_ecs实例和docker_on_ecs_local_ssd形态的mysql目前均不支持RestoreDBInstance接口
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultRestoreDBInstanceImpl")
public class RestoreDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(RestoreDBInstanceImpl.class);

    @Autowired
    private ClusterService clusterService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private MysqlDBCustinsService mysqlDBCustinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
            
            CustInstanceDO custins = mysqlParaHelper.getAndCheckCustInstance();
            if (custins.isShare()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (custins.isCustinsDockerOnEcs()){
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (mysqlDBCustinsService.isMysqlEnterprise(custins)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isReadAndWriteLock()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (!custins.isLogicPrimary()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // 子实例允许恢复到指定region，仅对恢复到子实例有效，覆盖性恢复无效
            String region = mysqlParaHelper.getParameterValue("region");
            boolean isRestoreByTime = BakSupport.isRestoreByTime(
                mysqlParaHelper.getParameterValue(ParamConstants.RESTORE_TYPE,
                    RESTORE_TYPE_BAKID));// 0：恢复到备份集； 1：恢复到时间点

            //0: 不保留，覆盖性恢复； 1:保留，恢复到子实例
            boolean isRetainInstance = BakSupport.isRetainInstance(isRestoreByTime,
                mysqlParaHelper.getParameterValue("RetainInstance"), custins.getDbType());
            if (!isRetainInstance) {
                // 覆盖性恢复必须为主实例所在region，忽略用户传入的region参数
                region = clusterService.getRegionByCluster(custins.getClusterName());
            } else {
                // 恢复到子实例允许用户指定Region
                region = region == null ? clusterService.getRegionByCluster(custins.getClusterName()) : region;
            }

            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        } catch (RdsException re) {
            logger.error("RestoreDBInstance ex="+re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("RestoreDBInstance ex="+ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    } 
}
