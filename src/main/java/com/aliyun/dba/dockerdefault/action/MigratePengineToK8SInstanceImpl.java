package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.action.MigratePengineToK8SInstanceClassImpl;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

/**
 * 新老架构迁移，isTransfer为true
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultMigratePengineToK8SInstanceImpl")
public class MigratePengineToK8SInstanceImpl implements IAction {

    private static final LogAgent LOG_AGENT = LogFactory.getLogAgent(MigratePengineToK8SInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected MigratePengineToK8SInstanceClassImpl migratePengineToK8SInstanceClass;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {
            return migratePengineToK8SInstanceClass.doActionRequest(custins, actionParams);
        }
        catch(RdsException re){
            LOG_AGENT.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            LOG_AGENT.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
