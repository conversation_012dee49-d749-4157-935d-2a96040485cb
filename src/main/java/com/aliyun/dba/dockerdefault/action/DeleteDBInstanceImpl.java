package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsSearchDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsSearchService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.service.MySQLGeneralService;
import com.aliyun.dba.dockerdefault.general.GeneralDeleteService;
import com.aliyun.dba.support.api.OpenSearchApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.HaProxyService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultDeleteDBInstanceImpl")
public class DeleteDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DeleteDBInstanceImpl.class);

    @Autowired
    private TaskService taskService;
    @Autowired
    private HaProxyService haProxyService;
    @Autowired
    private CustinsSearchService custinsSearchService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    private UserService userService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    private MySQLGeneralService mySQLGeneralService;
    @Autowired
    private InstanceService instanceService;

    @Autowired
    private GeneralDeleteService generalDeleteService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            CustInstanceDO custins = mysqlParaHelper.getCustInstance();
            if (custins == null) {
                if (custinsService.checkDeletedCustInstanceByInsName(mysqlParaHelper.getDBInstanceName())) {
                    return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);//实例已销毁
                }
                return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);//实例不存在，或者不是实例拥有者
            }

            if (!(custins.isActive() || custins.isStopInsStatus())) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());

            // 如果MySQL的general版实例，单独引流到指定删除流程中
            if (mysqlParaHelper.isMysqlGeneral(custins.getLevelId())) {
                return generalDeleteService.handleDeleteRequest(custins);
            }

            if (custins.isLogic()) {

                List<CustInstanceDO> custinsUnit = custinsService.getCustInstanceUnitByParentIdAndCharacterType(
                    custins.getId(),
                    CustinsSupport.CHARACTER_TYPE_PHYSICAL);
                if(!"general".equalsIgnoreCase(instanceLevelDO.getCategory())){
                    for (CustInstanceDO childCustIns : custinsUnit) {
                        if (childCustIns.isPrimary() && !childCustIns.isPolarDB()) {
                            CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                            //查询只读实例，如果有只读实例的话，则禁止删除
                            custInstanceQuery.setPrimaryCustinsId(childCustIns.getId());
                            //过滤掉正在删除的实例
                            custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                            custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_READ);//只读实例
                            if (custinsService.countCustIns(custInstanceQuery) > 0) {
                                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                            }
                        }
                }}else{
                    if(mySQLGeneralService.getFollowerInsList(custins.getId()).size()>0){
                        return ResponseSupport.createErrorResponse(MysqlErrorCode.FOLLOWER_INS_MORE_THAN_1.toArray());
                    }
                }
                mysqlParamSupport.checkMaxscaleStatus(custins);
            } else {
                //不支持直接删除物理实例
                if (custins.isPrimary()) {
                    logger.warn("Can't delete custins not normal or physical from this api.");
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
            }

            Integer deleteMode = CustinsSupport.GRACE_DELETE;
            if (mysqlParaHelper.hasParameter("deletemode")) {
                deleteMode = CustinsSupport.getDeleteMode(mysqlParaHelper.getParameterValue("deletemode"));
            }

            //发生容灾切换时，禁止删除主实例和灾备实例
            if ((custins.isGuard() && custins.isLogicPrimary())//实例发生容灾切换，禁止删除主实例和灾备实例操作
                || (custins.isPrimary() && custins.isLogicGuard())) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //检查防止删除proxy相关实例
            mysqlParaHelper.checkNotDeleteHaProxyCustins(custins);

            if (custins.isRead() && custins.isMysql()) {
                CustInstanceDO primaryins = custinsService
                    .getCustInstanceByCustinsId(custins.getPrimaryCustinsId());

                if(primaryins!=null) {
                    List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                            .getCustinsConnAddrByCustinsId(primaryins.getId(), null, CustinsSupport.RW_TYPE_RW_SPLIT);

                    if (taskService.getCustinsRunningTask(primaryins.getId(), TaskSupport.TASK_CREATE_RW_SPLIT_VIP)) {
                        return ResponseSupport.createErrorResponse(ErrorCode.UNFINISHED_CREATE_RW_TASK);
                    }

                    if (custinsConnAddrList.size() > 0) {
                        List<CustInstanceDO> readinsList = custinsService
                                .getReadCustInstanceListByPrimaryCustinsId(primaryins.getId(), false);
                        List<CustInstanceDO> validReadInslist = new ArrayList<>();
                        for (CustInstanceDO readins : readinsList) {
                            if (!CustinsSupport.CUSTINS_STATUS_DELETING.equals(readins.getStatus()) &&
                                    !CustinsSupport.CUSTINS_STATUS_CREATING.equals(readins.getStatus())) {
                                validReadInslist.add(readins);
                            }
                        }
                        if (validReadInslist.size() == 1) {
                            // 如果存在读写分离vip，那么不允许删除最后一个只读实例。
                            // 除非指定参数强制删除只读实例，那么先触发删除主实例的读写分离vip任务，
                            // 再触发删除只读实例任务。
                            if (CustinsSupport.GRACE_DELETE.equals(deleteMode)) {
                                return ResponseSupport.createErrorResponse(ErrorCode.RW_SPLIT_NETTYPE_EXIST);
                            } else {
                                CustinsConnAddrDO delCustinsConnAddr = custinsConnAddrList.get(0);
                                ConnAddrChangeLogDO delConnAddrChangeLog = ConnAddrSupport
                                        .createConnAddrChangeLogForDeleteNetType(
                                                primaryins.getId(),
                                                delCustinsConnAddr.getNetType(),
                                                delCustinsConnAddr.getConnAddrCust(),
                                                delCustinsConnAddr.getVip(),
                                                delCustinsConnAddr.getVport(),
                                                delCustinsConnAddr.getUserVisible(),
                                                delCustinsConnAddr.getTunnelId(),
                                                delCustinsConnAddr.getVpcId(),
                                                null,
                                                CustinsSupport.RW_TYPE_RW_SPLIT);

                                List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<>(1);
                                connAddrChangeLogs.add(delConnAddrChangeLog);

                                try {
                                    Integer taskId = taskService.changeConnAddrTask(
                                            mysqlParaHelper.getAction(), primaryins, connAddrChangeLogs,
                                            CustinsState.STATUS_ACTIVATION, TaskSupport.TASK_CHANGE_CONN_ADDR_DELETE_VIP,
                                            mysqlParaHelper.getOperatorId());
                                    taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                                } catch (Exception ex) {
                                    logger.error("Custins: " + primaryins.getId()
                                            + " DeleteDBInstanceRWSplitNetType failed when create task. Details: "
                                            + JSON.toJSONString(connAddrChangeLogs));
                                    throw new Exception(ex);
                                }
                            }
                        }
                    }
                }
            }

            if (custinsSearchService.checkCustinsSearch(custins)) {
                String apiUrlString = userService.getOpenSearchApiUrlByClusterName(custins.getClusterName());
                if (apiUrlString == null) {
                    return ResponseSupport.createErrorResponse(ErrorCode.OPENSEARCH_NOT_SUPPORT);
                }
                JSONObject apiUrl = JSON.parseObject(apiUrlString);
                OpenSearchApi api = new OpenSearchApi(apiUrl.getString("host"),
                    apiUrl.getString("accesskey"),
                    apiUrl.getString("secret"));
                CustinsSearchDO custinsSearch = custinsSearchService.getCustinsSearchByCustins(custins);
                api.deleteOpenSearchInstance(custinsSearch.getAppName());
                custinsSearchService.deleteCustinsSearch(custinsSearch);
            }

            //删除实例
            Integer taskId = -1;
            Integer delteDelayTime = Integer.valueOf(
                mysqlParaHelper.getParameterValue(ParamConstants.DELETE_DELAY_TIME, "0"));
            taskId = taskService.deleteCustInstanceAndTaskWithDelayTime(mysqlParaHelper.getAction(), custins,
                mysqlParaHelper.getOperatorId(), delteDelayTime);
            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error("DeleteDBInstance ex=" + re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("DeleteDBInstance ex=" + ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
