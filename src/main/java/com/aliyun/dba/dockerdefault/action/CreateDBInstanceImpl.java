package com.aliyun.dba.dockerdefault.action;

import com.alibaba.cobar.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.DistributeMode;
import com.alicloud.apsaradb.resmanager.CPUType;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.DistributeRule;
import com.alicloud.apsaradb.resmanager.EcsDataDisk;
import com.alicloud.apsaradb.resmanager.EcsDeploymentSet;
import com.alicloud.apsaradb.resmanager.EcsResModel;
import com.alicloud.apsaradb.resmanager.HostinsResModel;
import com.alicloud.apsaradb.resmanager.OssResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.VipResModel;
import com.alicloud.apsaradb.resmanager.VpcType;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.LogPlanDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MariaDBMinorVersionHelper;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.common.dataobject.ShardInfo;
import com.aliyun.dba.common.dataobject.VpcInfo;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsSrv;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.enums.CustinsSrvEnums;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.idao.CustinstServiceIDao;
import com.aliyun.dba.custins.pojo.ShardsInfo;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.docker.dataobject.DockerTaskInputParam;
import com.aliyun.dba.dockerdefault.service.CloudSSDEncryptionService;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.dockerdefault.service.DockerManager;
import com.aliyun.dba.dockerdefault.service.MaxscaleEndpointImpl;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.dataobject.EcsDiskDO;
import com.aliyun.dba.ecs.dataobject.RdsRegionDO;
import com.aliyun.dba.ecs.service.*;
import com.aliyun.dba.ecs.support.EcsConstants;
import com.aliyun.dba.endpoint.dataobject.EndpointConfigDO;
import com.aliyun.dba.endpoint.dataobject.EndpointDO;
import com.aliyun.dba.endpoint.idao.EndpointConfigIDao;
import com.aliyun.dba.endpoint.idao.EndpointIDao;
import com.aliyun.dba.endpoint.service.EndpointService;
import com.aliyun.dba.endpoint.support.EndpointSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelRelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.service.MySQLGeneralService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.*;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Time;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.base.support.MySQLParamConstants.CATEGORY_GENERAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getOperatorId;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getPenginePolicyID;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.*;
import static com.aliyun.dba.support.property.ErrorCode.*;
import static com.aliyun.dba.support.property.RdsConstants.*;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultCreateDBInstanceImpl")
public class CreateDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CreateDBInstanceImpl.class);

    @Autowired
    private BakService bakService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private DbsService dbsService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private ResApi resApi;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private EcsImageService ecsImageService;
    @Autowired
    private InstanceIDao instanceIDao;
    @Autowired
    private EcsService ecsService;
    @Autowired
    private CustinsIDao custinsIDao;
    @Autowired
    private CustinstServiceIDao custinstServiceIDao;
    @Autowired
    private PolarDBMPPService polarDBMPPService;
    @Autowired
    private EndpointIDao endpointIDao;
    @Autowired
    private EndpointService endpointService;
    @Autowired
    private MaxscaleEndpointImpl maxscaleEndpointImpl;
    @Autowired
    private MaxscaleCustinsService maxscaleCustinsService;
    @Autowired
    private EndpointConfigIDao endpointConfigIDao;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private AVZSupport avzSupport;
    @Autowired
    private ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected DockerCustinsService dockerCustinsService;
    @Autowired
    private CloudSSDEncryptionService cloudSSDEncryptionService;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private RegionService regionService;
    @Autowired
    private HostIDao hostIDao;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected MinorVersionService minorVersionService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Autowired
    private AmbariService ambariService;
    @Autowired
    private DockerManager dockerManager;
    @Autowired
    private MySQLGeneralService mySQLGeneralService;
    @Autowired
    private EcsDiskService ecsDiskService;
    @Autowired
    private DockerCommonService dockerCommonService;
    @Autowired
    private MysqlParamGroupHelper mysqlParamGroupHelper;
    @Autowired
    private MariaDBMinorVersionHelper mariadbMinorVersionHelper;
    @Resource
    private DbsGateWayService dbsGateWayService;
    @Resource
    private MySQLServiceImpl mySQLservice;
    @Resource
    private CrossArchService crossArchService;
    @Resource
    private com.aliyun.dba.poddefault.action.CreateDBInstanceImpl poddefaultCreateDBInstance;

    public static final String CUSTINS_PARAM_NAME_COMPOSE_TAG = "compose_tag";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
            //得到实例类型，版本
            String dbType = mysqlParaHelper.getAndCheckDBType(null);
            String dbVersion = mysqlParaHelper.getAndCheckDBVersion(dbType, true);
            String classCode = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_CLASS);

            String backupSetId = mysqlParaHelper.getParameterValue("BackupSetID");
            if (mySQLservice.isRebuildBackupSet(backupSetId)) {
                DescribeRestoreBackupSetParam caller = dbsGateWayService.describeRestoreBackupSetBuilder(map,mysqlParaHelper.getBID());
                DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(caller);
                if (restoreBackupResponse.getBackupSetInfo() == null) {
                    throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
                }
                rebuildDeleteInsConfig(map,restoreBackupResponse);
                mySQLservice.compareBakSizeAndDiskSize(restoreBackupResponse,Integer.valueOf(mysqlParamSupport.getParameterValue(map, "storage")));
                mySQLservice.checkCustinsAndUser(restoreBackupResponse.getBackupSetInfo().getCustinsId(),mysqlParaHelper.getBID(),mysqlParaHelper.getUID());
                String backupMinorVersion = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getMINOR_VERSION();
                try {
                    String finalMinorVersion = minorVersionServiceHelper.checkAndGetAllMinorVersion(
                            dbType, dbVersion, classCode, KindCodeParser.KIND_CODE_DOCKER_ON_ECS,
                            MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL, backupMinorVersion);
                    mysqlParaHelper.setParameter("TargetMinorVersion",finalMinorVersion);
                } catch (RdsException re) {
                    logger.warn("rebuild deleted ins, minor version is not find: {}", backupMinorVersion);
                }
                String bakInstanceKindCode = restoreBackupResponse.getBackupSetInfo().getInstanceKindCode();
                if (StringUtils.isBlank(bakInstanceKindCode) || !KindCodeParser.KIND_CODE_DOCKER_ON_ECS.equals(Integer.valueOf(bakInstanceKindCode))){
                    throw new RdsException(ErrorCode.UNSUPPORTED_KIND_CODE);
                }
                if (crossArchService.onecsCloneToK8s(map)) {
                    map.put("restorefromrecyclebin", "true");
                    return poddefaultCreateDBInstance.doActionRequest(custInstanceDO, map);
                }
            }

            if (mysqlParaHelper.getSourceDBInstanceID() != null) {
                CustInstanceDO instance = mysqlParaHelper.getAndCheckCustInstanceById("sourcedbinstanceid");
                if (!instance.getDbType().equals(dbType)) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DB_TYPE);
                }
                if (!instance.getDbVersion().equals(dbVersion)) {
                    return ResponseSupport.createErrorResponse(ErrorCode.INVALID_MINOR_VERSION);
                }
                if (crossArchService.onecsCloneToK8s(map)) {
                    map.put("restorefromrecyclebin", "true");
                    return poddefaultCreateDBInstance.doActionRequest(custInstanceDO, map);
                }
            }
            mysqlParaHelper.getAndSetContainerTypeAndHostTypeIfEmpty(dbType, dbVersion, classCode);


            //对于一主多从实例，重新构造逻辑
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                    dbType, dbVersion, 'x', null);
            if(insLevel != null && CATEGORY_GENERAL.equalsIgnoreCase(insLevel.getCategory())){
                return createGeneralCategoryDockerInstance(map);
            }

            //创建docker实例
            return createDockerInstance();
        } catch (RdsException re) {
            logger.error("CreateDBInstance ex=" + re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("CreateDBInstance ex=" + ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            //释放参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

    /**
     * 创建Docker实例
     */
    private Map<String, Object> createDockerInstance() throws RdsException {

        CustInstanceDO custins = new CustInstanceDO();
        List<CustInstanceDO> characterCustinsList = new ArrayList<>();
        List<String> externalRdsServiceIdList = new ArrayList<>();
        boolean success = false;
        try {
            Map<String, String> mysqlCustomParams = mysqlParaHelper.getAndCheckMysqlCustomParams();
            String paramGroupId = mysqlParaHelper.getParameterValue(ParamConstants.DB_PARAM_GROUP_ID);
            Integer userId = mysqlParaHelper.getAndCreateUserId();
            mysqlParaHelper.checkUserOperatorCluster(userId);
            custins.setUserId(userId);

            String engine = mysqlParaHelper.getParameterValue(ParamConstants.ENGINE);
            if (StringUtils.isBlank(engine)) {
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }

            Integer insType = mysqlParaHelper.getAndCheckDBInstanceUsedType();
            if (!CUSTINS_INSTYPE_PRIMARY.equals(insType)) {
                throw new RdsException(ErrorCode.INVALID_INS_TYPE);
            }
            String engineVersion = mysqlParaHelper.getParameterValue(ParamConstants.ENGINE_VERSION);
            if (StringUtils.isBlank(engineVersion)) {
                throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
            }
            String dbType = mysqlParaHelper.getAndCheckDBType(engine);
            custins.setDbType(dbType);
            custins.setDbVersion(engineVersion);

            String hostType = mysqlParaHelper.getAndCheckDockerHostType(dbType, engineVersion);
            String storageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            if (STORAGE_TYPE_CLOUD_AUTO.equalsIgnoreCase(storageType)) {
                return ResponseSupport.createErrorResponse(ErrorCode.NOT_SUPPORT_SPECIFIC_DISK_TYPE);
            }
            String dataDiskCategory = custinsService.getDataDiskCategory(null, storageType, hostType);
            //依赖此处获取kindCode
            mysqlParaHelper.checkAndSetKindCode(custins, hostType);
            if (StringUtils.isNotBlank(paramGroupId)) {
                // MGR复制模式，DockerOnECS不支持
                String syncMode = SysParamGroupHelper.getSyncMode(paramGroupId).toString();
                if (mysqlParamGroupHelper.isMgr(syncMode)) {
                    logger.error("local ssd not not support MGR param group {}", paramGroupId);
                    throw new RdsException(ErrorCode.INVALID_PARAM_GROUP_CODE);
                }
                // 参数模板信息检查
                // FIXME：此处暂时忽略category校验与存储引擎校验
                SysParamGroupHelper.sysParamGroupIdValidation(paramGroupId, dbType, engineVersion, "", "");
                parameterGroupTemplateGenerator.paramGroupMatchValidate(dbType, engineVersion, "", paramGroupId, false);
            }

            // init task queue param of backup
            Map<String, Object> taskQueueParam = custins.isKeplerCstore() ? mysqlParaHelper
                .getTaskQueueParamWithBakperiodDefault("0000000") : mysqlParaHelper.getTaskQueueParam();
            CustInstanceDO srcCustins = null;

            String restoreType = mysqlParaHelper.getParameterValue(ParamConstants.RESTORE_TYPE);
            String bakIdStr = mysqlParaHelper.getParameterValue("BackupSetID");

            Long bakSize = mysqlParaHelper.checkRestoreIllegalAndGetSize(custins, bakIdStr, restoreType);
            mysqlParaHelper.setRestoreStorageSize(restoreType, hostType, bakSize);

            if (RESTORE_TYPE_TIME.equals(restoreType)) {
                srcCustins = mysqlParaHelper.validAndGetSrcCust(dbType, engineVersion);
                Date restoreTimeDate = mysqlParaHelper.getAndCheckTimeByParam(ParamConstants.RESTORE_TIME,
                    DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME, DataSourceMap.DATA_SOURCE_DBAAS);
                LogPlanDO logPlan = bakService.getLogPlanByCustinsId(srcCustins.getId());

                if (logPlan == null) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_LOG_STATUS);
                }
                mysqlParaHelper.checkRestoreTimeValid(srcCustins, restoreTimeDate, logPlan);

                mysqlParaHelper.fillRestoreByTimeTaskParam(taskQueueParam, srcCustins);
            } else if (RESTORE_TYPE_SRCCUST.equals(restoreType)) {
                srcCustins = mysqlParaHelper.validAndGetSrcCust(dbType, engineVersion);
                mysqlParaHelper.fillRestoreBySrcCustTaskParam(taskQueueParam, srcCustins);
            } else if (RESTORE_TYPE_BAKID.equals(restoreType) && custins.isPolarDB()) {
                srcCustins = mysqlParaHelper.validAndGetSrcCust(dbType, engineVersion);
            }

            custins.setMaxDbs(0);
            custins.setMaxAccounts(1);

            custinsService.setAccountMode(custins,
                mysqlParaHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE),
                mysqlParaHelper.getParameterValue(ParamConstants.ACCOUNT_NAME),
                mysqlParaHelper.getParameterValue(ParamConstants.REGION));

            CustinsIpWhiteListDO custinsIpWhiteList = mysqlParaHelper.getAndCheckCustinsIpWhiteList(custins);

            Integer vPort = CustinsValidator.getRealNumber(CustinsSupport.getConnPort(
                mysqlParaHelper.getParameterValue(ParamConstants.PORT), custins.getDbType(), custins.getKindCode()));
            if (vPort < 0) {
                throw new RdsException(ErrorCode.INVALID_PORT);
            }
            custins.setvPort(vPort);

            mysqlParaHelper.updateCustinsCommonProperties(custins);

            String connType = mysqlParaHelper.getAndCheckConnType(null);
            if (connType == null) {
                connType = CustinsSupport.CONN_TYPE_LVS;
            }
            custins.setConnType(connType);

            Integer netType = mysqlParaHelper.getAndCheckNetType();
            custins.setNetType(netType);

            if (CustinsSupport.CONN_TYPE_PHYSICAL.equals(connType)) {
                custins.setNetType(null);
            }


            AVZInfo avzInfo = avzSupport.getAVZInfo(ActionParamsProvider.ACTION_PARAMS_MAP.get());
            if (!avzInfo.isValidForNewInstance()) {
                throw new RdsException(ErrorCode.INVALID_REGION);
            }

            String region = avzSupport.getMainLocation(ActionParamsProvider.ACTION_PARAMS_MAP.get());
            String clusterName = mysqlParaHelper.getParameterValue(ParamConstants.CLUSTER_NAME);
            Set<Integer> hostIdSet = new HashSet<>();
            Boolean isUserCluster = false;
            // 仅在输入集群名时HostId参数才有效
            if (StringUtils.isNotBlank(clusterName)) {
                hostIdSet = mysqlParaHelper.getAndCheckHostIdSet();
                ClustersDO clusterDO = clusterService.getClusterByClusterName(clusterName);

                if (clusterDO != null) {
                    isUserCluster = DEDICATED_HOST_GOURP_TYPE.equals(clusterDO.getType());
                }
            }

            String composeTag = mysqlParaHelper.selectComposeTag(clusterName, isUserCluster);
            String classCode = mysqlParaHelper.getAndCheckDBInstanceClassCode(custins, null);
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
            if (StringUtils.isBlank(classCode) || insLevel == null) {
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            custins.setLevelId(insLevel.getId());
            // custins must set levelid
            Map<String, List<MycnfCustinstanceDO>> mycnfCustinstancesMap = mysqlParaHelper
                    .getAndCheckExternalParameter(custins);

            String storage = mysqlParaHelper.getParameterValue(ParamConstants.STORAGE);

            if (StringUtils.isNotEmpty(storage)) {
                //入参磁盘大小是GB
                if (Integer.parseInt(storage) != 0) {
                    if (custins.isCustinsDockerOnEcs()) {
                        CustinsSupport.setDiskSize(custins, CustinsSupport.BIZ_TYPE_RDS, storage, CustinsSupport.ECS_MIN_DISK_SIZE);
                    }
                    else {
                        CustinsSupport.setDiskSize(custins, CustinsSupport.BIZ_TYPE_RDS, storage);
                    }
                } else {
                    custins.setDiskSize(0L);
                }
            } else {
                //客户实例表和规格码中的磁盘大小都是MB
                custins.setDiskSize(insLevel.getDiskSize());
            }
            custins.setCharacterType(insLevel.getCharacterType());

            String snapShotId = null;
            if (custins.isLogic() &&
                    (custins.getKindCode().equals(KIND_CODE_DOCKER_ON_ECS)
                            || custins.getKindCode().equals(KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD))) {
                // init node instance info
                //TODO: change to no rel
                List<InstanceLevelRelDO> instanceLevelRels =
                    instanceService.getInstanceLevelRelByParentLevelId(insLevel.getId(), null);
                if (instanceLevelRels.size() <= 0) {
                    logger.error("No instance level relation found for level id: " + insLevel.getId());
                    throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                }
                if (mysqlParaHelper.getSourceDBInstanceID() != null) {
                    srcCustins = mysqlParaHelper.getAndCheckCustInstanceById("sourcedbinstanceid");
                    //处于删除中的实例，不能从回收站恢复
                    if (CustinsState.STATUS_DELETING.getComment().equals(srcCustins.getStatusDesc()) && srcCustins.getIsDeleted() == 0) {
                        return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                    }
                    custins.setParentId(srcCustins.getId());
                    String bakId = mysqlParaHelper.getParameterValue("BackupSetID");
                    Long bakSetId = null;
                    if (null == bakId) {
                        Map<String, Object> condition = ImmutableMap.<String, Object>of(
                            "ignoredRetention", true,
                            "custinsIds", ImmutableList.of(srcCustins.getId()),
                            "location", BAKUPSET_LOCATION_OSS,
                            "status", "OK");
                        List<Map<String, Object>> bakHistoryMapByCondition = bakService.getBakHistoryMapByCondition(condition);
                        if (bakHistoryMapByCondition.isEmpty()) {
                            return ResponseSupport.createErrorResponse(ErrorCode.BACKUPSET_NOT_FOUND);
                        }
                        bakSetId = (Long)bakHistoryMapByCondition.get(0).get("BackupSetID");
                        bakId = bakSetId.toString();
                    }

                    if(StringUtils.isNotBlank(mysqlParaHelper.getParameterValue("unifybackupsetid"))) {
                        Map<String, Object> restoreParam = new HashMap<>(1);
                        snapShotId = mysqlParaHelper.getParameterValue("snapshotid");
                        restoreParam.put("snapShotId", snapShotId);
                        restoreParam.put("srcParentCustId", srcCustins.getId());
                        restoreParam.put("baksetType", BAKWAY_SNAPSHOT);
                        restoreParam.put("bakHisID", bakId);
                        restoreParam.put("restoreType", "0");
                        taskQueueParam.put("restore", restoreParam);
                        custins.setAccountMode(srcCustins.getAccountMode());
                    } else {
                        BakhistoryDO bakhistoryById = bakService.getBakhistoryById(bakSetId);
                        Map<String, Object> restoreParam = new HashMap<>(1);
                        String baksetType = bakhistoryById.getBakWay();
                        if (BAKWAY_SNAPSHOT.equals(baksetType)) {
                            JSONObject bakHistObject = JSONArray.parseObject(bakhistoryById.getSlaveStatus());
                            JSONArray bakHistArray = bakHistObject.getJSONArray(bakHistObject.keySet().toArray()[0].toString());
                            JSONObject jsonObject = bakHistArray.getJSONObject(0);
                            snapShotId = jsonObject.getJSONObject("slave_status").getString("SNAPSHOT_ID");
                            restoreParam.put("snapShotId", snapShotId);
                        }
                        //todo: now only support one physical custins clone
                        restoreParam.put("srcParentCustId", srcCustins.getId());
                        restoreParam.put("baksetType", baksetType);
                        restoreParam.put("bakHisID", bakhistoryById.getHisId());
                        taskQueueParam.put("restore", restoreParam);
                    }
                }
            }

            // 如果源实例不为空，则当前实例参数模板应与源实例保持一致
            if (srcCustins != null) {
                // 原实例custins_param表中有数据的情况下赋值（针对回收站实例创建）
                if (CollectionUtils.isNotEmpty(custinsParamService.getCustinsParams(srcCustins.getId()))) {
                    paramGroupId = custinsParamService.getCustinsParam(
                            srcCustins.getId(),
                            CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID)
                            .getValue();
                    String mysqlCustomParamsStr = custinsParamService.getCustinsParam(
                            srcCustins.getId(),
                            CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS)
                            .getValue();
                    if (StringUtils.isNotBlank(mysqlCustomParamsStr)) {
                        try {
                            Map<String, String> mysqlCustomParamsTemp = JSONObject.toJavaObject(JSONObject.parseObject(mysqlCustomParamsStr), Map.class);
                            mysqlCustomParams = mysqlCustomParamsTemp;
                        } catch (Exception e) {
                            logger.warn(e);
                        }
                    }
                }
            }

            // 确认 paramGroupId，mysqlCustomParams 不会更变后，将参数放入 task q 中
            // set custom parameter template group
            taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
            taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, SysParamGroupHelper.describeSysParamGroupId(paramGroupId));
            taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS, mysqlCustomParams);

            Integer bizType = mysqlParaHelper.getAndCheckBizType();
            custinsService.createCustInstance(custins);

            String upgradeMinorVersionOption;
            if (mysqlParaHelper.hasParameter(ParamConstants.AUTO_UPGRADE_MINOR_VERSION)) {
                upgradeMinorVersionOption = mysqlParaHelper.getParameterValue(ParamConstants.AUTO_UPGRADE_MINOR_VERSION);
                if (!ParamConstants.AUTO_UPGRADE_MINOR_VERSION_OPTIONS.contains(upgradeMinorVersionOption)) {
                    throw new RdsException(ErrorCode.INVALID_ITEM_VALUE);
                }
            } else {
                upgradeMinorVersionOption = resourceService.getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).size() > 0 ? resourceService.getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).get(0) : "Auto";
            }
            custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, upgradeMinorVersionOption));


            List<EndpointDO> endpointList = new ArrayList<>();
            // init resource container
            String dedicatedHostNameForMaster = mysqlParaHelper.getParameterValue(ParamConstants.DedicatedHostNameForMaster,mysqlParaHelper.getParameterValue("TargetDedicatedHostIdForMaster"));
            String dedicatedHostNameForSlave = mysqlParaHelper.getParameterValue(ParamConstants.DedicatedHostNameForSlave,mysqlParaHelper.getParameterValue("TargetDedicatedHostIdForSlave"));

            Integer dedicatedHostIdForMaster = null;
            if (dedicatedHostNameForMaster != null) {
                Map<String, Object> specifyHostInfo = hostIDao.getHostIdsByDedicatedHostName(dedicatedHostNameForMaster,
                        clusterName);
                dedicatedHostIdForMaster = (Integer) specifyHostInfo.get("id");
            }

            Integer dedicatedHostIdSlave = null;
            String slaveZoneId = null;
            if (dedicatedHostNameForSlave != null) {
                Map<String, Object> slaveSpecifyHostInfo = hostIDao.getHostIdsByDedicatedHostName(dedicatedHostNameForSlave,
                        clusterName);
                dedicatedHostIdSlave = (Integer) slaveSpecifyHostInfo.get("id");
                slaveZoneId = hostIDao.getHostInfoParam(dedicatedHostIdSlave, "ZoneId");

            }


            ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(
                ActionParamsProvider.ACTION_PARAMS_MAP.get(),
                    CustinsSupport.CONTAINER_TYPE_DOCKER, isUserCluster, dedicatedHostIdForMaster, dedicatedHostIdSlave, slaveZoneId);
            resourceContainer.setClusterName(clusterName);
            resourceContainer.setUserId(custins.getUserId());
            resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));

            EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(dbType,
                engineVersion, insLevel.getCategory(), composeTag);
            if (custins.isLogic()) {
                // init node instance info
                List<InstanceLevelRelDO> instanceLevelRels =
                    instanceService.getInstanceLevelRelByParentLevelId(insLevel.getId(), null);
                if (instanceLevelRels.size() <= 0) {
                    logger.error("No instance level relation found for level id: " + insLevel.getId());
                    throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                }
                // init shardsInfoMap.
                String shardsInfo = mysqlParaHelper.getParameterValue(ParamConstants.SHARDS_INFO);
                Map<String, List<ShardsInfo.Node>> nodesMap = mysqlParaHelper.SplitNodesFromShardsInfobyDbTypeAndDbVersion(shardsInfo);
                Map<String, List<ShardInfo>> dbtypeShardsInfoMap = mysqlParaHelper.getShardsInfoMap(shardsInfo);
                List<VpcInfo> vpcInfos = mysqlParaHelper.getVpcInfos(dbtypeShardsInfoMap);

                // init external rds service
                String exRdsServiceIns = mysqlParaHelper.getParameterValue(ParamConstants.RDS_SERVICE_INS_LIST, "[]");
                JSONArray externalRdsServiceInsList = new JSONArray();
                try {
                    externalRdsServiceInsList = JSON.parseArray(exRdsServiceIns);
                } catch (Exception e) {
                    throw new RdsException(ErrorCode.INVALID_RDS_SERVICE_INS_LIST);
                }
                Map<String, List<CustInstanceDO>> externalRdsServiceInsMap = new HashMap<>();
                for (Object rdsServiceInsNameObj : externalRdsServiceInsList) {
                    String rdsServiceInsName = rdsServiceInsNameObj.toString();
                    CustInstanceDO custinsServiceByName = custinsService.getCustInstanceByInsName(null, rdsServiceInsName);
                    if (custinsServiceByName != null) {
                        String tmpInsDbType = custinsServiceByName.getDbType();
                        List<CustInstanceDO> tmpInsList = externalRdsServiceInsMap.get(tmpInsDbType);
                        tmpInsList = tmpInsList == null ? new ArrayList<>() : tmpInsList;
                        tmpInsList.add(custinsServiceByName);
                        externalRdsServiceInsMap.put(tmpInsDbType, tmpInsList);
                    }
                }

                JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());
                for (InstanceLevelRelDO rel : instanceLevelRels) {
                    InstanceLevelDO characterInsLevel = instanceService.getInstanceLevelByLevelId(
                        rel.getCharacterLevelId());
                    List<ShardInfo> shardInfoList = dbtypeShardsInfoMap.get(characterInsLevel.getDbType());
                    DockerInsLevelParseConfig config = null;
                    EngineService engineService
                        = new Gson().fromJson(jsonServer.getString(characterInsLevel.getDbType()), EngineService.class);
                    if (CustinsSupport.isRdsSrvDockerize(engineService)) {
                        config = custinsService.parseDockerInsExtraInfo(characterInsLevel.getExtraInfo());
                        String uniqueKey = characterInsLevel.getDbType() + characterInsLevel.getDbVersion();
                        Integer shardNum = shardInfoList != null ? shardInfoList.size() : rel.getCharacterCustinsCount();

                        for (int i = 0; i < shardNum; i++) {
                            CustInstanceDO characterCustins = custins.clone();
                            if (null != engineService.getInsType()) {
                                CustInsType custInsType = CustInsType.getByName(engineService.getInsType());
                                characterCustins.setInsType(custInsType.getValue());
                            }
                            if (hostType != null &&
                                hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD) && !isUserCluster) {
                                ShardInfo shardInfo = shardInfoList != null ? shardInfoList.get(i) : null;

                                RequestParamsDO params = mysqlParaHelper.inflateDockerParamsWhenCreate(characterCustins, shardInfo, isUserCluster);
                                params.setDataDiskCategory(dataDiskCategory);
                                if (snapShotId != null) {
                                    params.setSnapShotId(snapShotId);
                                    params.setClone(true);
                                }

                                if (vpcInfos != null && vpcInfos.size() > 0 && params.getVpcInfos() == null) {
                                    params.setVpcInfos(vpcInfos);
                                }

                                // get storage from shard info
                                if (shardInfo != null) {
                                    storage = shardInfo.getStorage();
                                    if (shardInfo.getDBInstanceClass() != null) {
                                        characterInsLevel
                                            = instanceService.getInstanceLevelByClassCode(
                                            shardInfo.getDBInstanceClass(),
                                            characterInsLevel.getDbType(), characterInsLevel.getDbVersion(),
                                            custins.getTypeChar(), characterInsLevel.getCharacterType());
                                    }
                                }
                                // for ecs min 20G
                                if (Validator.isNotNull(storage)) {
                                    if (Integer.parseInt(storage) != 0) {
                                        setDiskSize(characterCustins, bizType, storage,
                                            CustinsSupport.ECS_MIN_DISK_SIZE);
                                    } else {
                                        characterCustins.setDiskSize(0L);
                                    }
                                } else {
                                    characterCustins.setDiskSize(characterInsLevel.getDiskSize());
                                }
                                createDockerInstanceOnEcs(characterCustins, config, characterInsLevel, i, custins,
                                    resourceContainer, params, characterCustinsList);
                            }

                            // 判断用户集群
                            else if (isUserCluster) {
                                ShardsInfo.Node node = null;
                                if (nodesMap.get(uniqueKey) != null && nodesMap.get(uniqueKey).size() > 0) {
                                    node = nodesMap.get(uniqueKey).get(i);
                                } else {
                                    node = new ShardsInfo().new Node();
                                }
                                if (null != storage) {
                                    node.setStorage(Integer.valueOf(storage));
                                }
                                createPhysicalCustInsDockerOnHost(characterCustins, custins, config,
                                    characterInsLevel, vPort, bizType, hostType, resourceContainer,
                                    hostIdSet, characterCustinsList, node, engineService);
                                // different with physical instance, apply ecs vip model
                                CustinsResModel custinsResModel = resourceContainer.getCustinsResModelList().get(0);
                                custinsResModel.setVipResModelList(new ArrayList<>());
                                RequestParamsDO params = mysqlParaHelper.inflateDockerParamsWhenCreate(characterCustins,
                                        isUserCluster);
                                String zoneId = params.getZoneId();
                                String regionId = params.getRegionId();
                                if (CustinsSupport.isVpcNetType(params.getNetType())) {
                                    String connAddrCust = mysqlParaHelper.getConnAddrCust(
                                        custins.getInsName().replace("_", "-"),
                                        regionId,
                                        custins.getDbType());
                                    VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
                                    vipResModel.setVswitchId(params.getVswitchId());
                                    vipResModel.setVpcId(params.getUserVpcId());
                                    vipResModel.setUserVisible(1);
                                    vipResModel.setTunnelId(params.getTunnelId());
                                    vipResModel.setVip(params.getIpaddress());
                                    vipResModel.setVport(vPort);
                                    vipResModel.setZoneId(zoneId);
                                    vipResModel.setVpcInstanceId(
                                        mysqlParaHelper.getParameterValue(ParamConstants.VPC_INSTANCE_ID));
                                    vipResModel.setConnAddrCust(connAddrCust);
                                    custinsResModel.setConnType(CustinsSupport.CONN_TYPE_LVS);
                                    custinsResModel.addVipResModel(vipResModel);
                                } else {
                                    connType = CustinsSupport.CONN_TYPE_LVS;
                                    custinsResModel.setConnType(connType);
                                    for (int j = 0; j < config.getVipCount(); j++) {
                                        String connAddrCust = mysqlParaHelper.getConnAddrCust(
                                            custins.getInsName().replace("_", "-") + j,
                                            regionId,
                                            custins.getDbType());
                                        if (StringUtils.isBlank(connAddrCust)) {
                                            connAddrCust = mysqlParaHelper.getConnAddrCust(
                                                custins.getInsName().replace("_", "-") + j,
                                                regionId,
                                                CustinsSupport.CONTAINER_TYPE_DOCKER);
                                        }
                                        VipResModel vipResModel = initVipResModel(
                                            netType, connAddrCust, vPort, config.getVportCountPerVip(), connType);
                                        custinsResModel.addVipResModel(vipResModel);

                                    }
                                }
                                HostinsResModel hostinsResModel = custinsResModel.getHostinsResModel();
                                if (hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {
                                    params.setDataDiskCategory(dataDiskCategory);
                                    if (snapShotId != null) {
                                        params.setSnapShotId(snapShotId);
                                        params.setClone(true);
                                    }
                                    // 设置云盘属性
                                    List<EcsResModel> ecsResModelList = new ArrayList<>();
                                    EcsResModel ecsResModel = initDHGEcsResModule(params, config, characterInsLevel,
                                            characterCustins, custins);
                                    // 需要将扩容的DISK SIZE 传入ecsResModel 容器中
                                    ecsResModel.setDataDiskSize(mysqlParaHelper.getExtendDiskSizeForEcsIns(CustinsSupport.DB_TYPE_DOCKER,
                                            characterCustins.getDiskSize()));
                                    ecsResModelList.add(ecsResModel);
                                    custinsResModel.setEcsResModelList(ecsResModelList);
                                    // 云盘单独设置下磁盘属性
                                    // 云盘no_need_host_disk
                                    hostinsResModel.setDiskType(DISK_TYPE_NO_NEED);
                                    hostinsResModel.setDiskSizeSold(null);
                                } else {
                                    // 本地盘 disktype 需要设置 disk_type_all
                                    hostinsResModel.setDiskType(DISK_TYPE_ALL);
                                }
                                hostinsResModel.getDistributeRule().setSiteDistributeMode(DistributeMode.TRY_SCATTER);
                            } else {
                                ShardsInfo.Node node = null;
                                if (nodesMap.get(uniqueKey) != null && nodesMap.get(uniqueKey).size() > 0) {
                                    node = nodesMap.get(uniqueKey).get(i);
                                }
                                // physical vport.
                                Integer vPortPhysical = vPort;
                                if (custins.getDbType().equalsIgnoreCase(DB_ENGINE_HYBRIDDB)) {
                                    vPortPhysical = CustinsValidator.getRealNumber(CustinsSupport.getConnPort(
                                        mysqlParaHelper.getParameterValue(ParamConstants.PORT),
                                        characterInsLevel.getDbType(), characterCustins.getKindCode()));
                                }
                                createPhysicalCustInsDockerOnHost(characterCustins, custins, config,
                                    characterInsLevel, vPortPhysical, bizType, hostType, resourceContainer,
                                    hostIdSet, characterCustinsList, node, engineService);
                            }
                        }
                    } else {
                        CustInstanceDO characterCustins = custins.clone();
                        characterCustins.setDbType(characterInsLevel.getDbType());
                        characterCustins.setParentCustIns(custins);
                        // 增加CustInstanceDO中的multiAvzoneParams
                        MultiAVZExParamDO multiAVZExParamDO = mysqlParaHelper.fakeMultiAvzExParam(vpcInfos);
                        if (engineService.getMultiAvzone() != null && engineService.getMultiAvzone()) {
                            characterCustins.setMultiAVZExParams(multiAVZExParamDO);
                        } else {
                            characterCustins.setMultiAVZExParams(null);
                        }
                        // 当前只允许每种rds service只存在1个实例
                        List<CustInstanceDO> tmpExternalRdsServiceList = externalRdsServiceInsMap
                            .get(characterInsLevel.getDbType());
                        if (tmpExternalRdsServiceList != null && tmpExternalRdsServiceList.size() >= 1) {
                            CustInstanceDO tmpExternalRdsService = tmpExternalRdsServiceList.get(0);
                            // 使用外部传入的rds service实例信息写入custins_service表
                            CustinsSrv custinsSrv = new CustinsSrv();
                            custinsSrv.setCustinsId(custins.getId());
                            custinsSrv.setServiceId(tmpExternalRdsService.getId().toString());
                            custinsSrv.setServiceName(tmpExternalRdsService.getInsName());
                            custinsSrv.setServiceRole(CustinsSrvEnums.CustinsSrvServiceRole.DATASOURCE.value);
                            custinsSrv.setServiceType(CustinsSrvEnums.CustinsSrvServiceType.RDS.value);
                            custinsSrv.setStatus(CustinsSrvEnums.CustinsSrvStatus.RDS_SERVICE_ACTIVE);
                            Map<String, Object> tmpExtraInfo = new HashMap<>();
                            tmpExtraInfo.put(CustinsSrvEnums.CUSTINS_SRV_LIFE_CYCLE_TYPE_KEY,
                                CustinsSrvEnums.CustinsSrvLifeCycleType.EXTERNAL.value);
                            custinsSrv.setExtraInfo(JSON.toJSONString(tmpExtraInfo));
                            custinstServiceIDao.createCustinsService(custinsSrv);
                            externalRdsServiceIdList.add(tmpExternalRdsService.getId().toString());
                        } else {
                            ShardInfo shardInfo = shardInfoList != null ? shardInfoList.get(0) : null;
                            // 构造ResourceContainer进行依赖rds service创建
                            RequestParamsDO params = mysqlParaHelper.inflateDockerParamsWhenCreate(characterCustins,
                                    shardInfo, isUserCluster);
                            mysqlParaHelper.createRdsServiceForDocker(characterCustins, config, characterInsLevel,
                                netType, resourceContainer, params, "rds");
                        }
                    }
                }
                if (mysqlParaHelper.hasParameter(ParamConstants.ACCOUNT_NAME)) {
                    AccountsDO account = mysqlParaHelper.getAndCheckAccount(custins);
                    dbsService.createDbAndAccount(custins, null, account.clone());
                    List<CustInstanceDO> characterCustInstances = custinsService.getCustInstanceByParentId(
                        custins.getId());
                    for (CustInstanceDO ci : characterCustInstances) {
                        if (CustinsSupport.CHARACTER_TYPE_PHYSICAL.equals(ci.getCharacterType())) {
                            dbsService.createDbAndAccount(ci, null, account.clone());
                        }
                    }
                }

                // init logic custins resource model
                CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
                custinsResModel.setConnType(connType);
                DockerInsLevelParseConfig config = null;
                String extraInfo = null;

                // init cluster end point
                extraInfo = insLevel.getExtraInfo();
                logger.info(
                    "InstanceLevelId=" + insLevel.getId() + ", extraInfo=" + extraInfo + ", ConnType=" + connType);
                if (Validator.isNotNull(extraInfo) && !CustinsSupport.CONN_TYPE_PHYSICAL.equals(connType)) {
                    config = custinsService.parseDockerInsExtraInfo(extraInfo);
                    logger.info("config.getClusterEndpointsCount()=" + config.getClusterEndpointsCount());
                    if (config.getClusterEndpointsCount() > 0) {
                        VipResModel vipResModel = mysqlParaHelper.initLogicInsVipResModel(custins, connType);
                        String primaryEndpointName = mysqlParaHelper.getParameterValue("PrimaryEndpointName");
                        if (StringUtils.isEmpty(primaryEndpointName)) {
                            primaryEndpointName = custins.getInsName();
                        }
                        EndpointDO endpoint = endpointService.getOrCreateEndpoint(primaryEndpointName,
                            EndpointSupport.ENPOINT_TYPE_PRIMARY, custins.getId(), custins.getInsName());
                        endpointList.add(endpoint);
                        vipResModel.setEndpointId(endpoint.getId());
                        custinsResModel.addVipResModel(vipResModel);
                    } else if (connType.equals(CONN_TYPE_PROXY)) {
                        logger.error("not config vip, config conn_type=" + connType + "error.");
                        throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
                    }
                } else if (connType.equals(CONN_TYPE_PROXY)) {
                    logger.error("not config vip, config conn_type=" + connType + "error.");
                    throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
                }
                // put into resource container
                resourceContainer.addCustinsResModel(custinsResModel);
            }

            Map<String, Object> data = new HashMap<>();
            if (mysqlParaHelper.getAndCheckNeedMaxscaleLink()) {
                if (!maxscaleCustinsService.isSupportMaxscale(custins)) {
                    logger.error("custins(" + custins.getInsName() + ") can not be supported by Maxscale!");
                    throw new RdsException(ErrorCode.MAXSCALE_NOT_SUPPORT);
                }

                String maxscaleInsName = mysqlParaHelper.getAndCheckMaxscaleInsName();
                Integer maxscaleNetType = mysqlParaHelper.getAndCheckMaxscaleNetType();
                String maxscaleConnectionString = mysqlParaHelper.getAndCheckMaxscaleConnectionString();
                if (maxscaleConnectionString == null) {
                    maxscaleConnectionString = maxscaleInsName.replace('_', '-');
                }
                Integer maxscalePort = Integer.parseInt(CustinsSupport.getConnPort(null, DB_TYPE_MAXSCALE));
                String specifiedMaxsclaeClassCode = mysqlParaHelper.getParameterValue("SpecificMaxscaleClassCode");
                if (CustinsSupport.isVpcNetType(maxscaleNetType)) {
                    String maxscaleTunnelId = CheckUtils.checkValidForTunnelId(
                        mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_TUNNEL_ID));
                    String maxscaleVpcId = CheckUtils.checkValidForVPCId(
                        mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_VPC_ID));
                    String maxscaleVswitchId = CheckUtils.checkValidForVswitchId(
                        mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_VSWITCH_ID));
                    String maxscaleIpaddress = CheckUtils.checkValidForIPAddress(
                        mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_IP_ADDRESS));
                    String maxscaleVpcInstanceId = CheckUtils.checkValidForIPAddress(
                        mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_VPC_INSTANCE_ID));
                    resourceContainer = maxscaleEndpointImpl.addMaxscaleToResourceContainer(custins, resourceContainer,
                        maxscaleInsName, region,
                        maxscaleNetType,
                        maxscaleVpcId,
                        Integer.valueOf(maxscaleTunnelId),
                        maxscaleVswitchId,
                        maxscaleIpaddress,
                        maxscaleVpcInstanceId,
                        custinsIpWhiteList.getIpWhiteList(),
                        maxscaleConnectionString,
                        maxscalePort,
                        specifiedMaxsclaeClassCode);
                } else {
                    resourceContainer = maxscaleEndpointImpl.addMaxscaleToResourceContainer(custins, resourceContainer,
                        maxscaleInsName, region,
                        maxscaleNetType,
                        null,
                        null,
                        null,
                        null,
                        null,
                        custinsIpWhiteList.getIpWhiteList(),
                        maxscaleConnectionString,
                        maxscalePort,
                        specifiedMaxsclaeClassCode);
                }
                data.put("MaxscaleNetType", maxscaleNetType);
                data.put("MaxscaleConnectionString", maxscaleConnectionString);
                data.put("MaxscalePort", maxscalePort);
                EndpointDO endpoint = endpointService.getOrCreateEndpoint(maxscaleInsName,
                    EndpointSupport.ENPOINT_TYPE_RWSPLIT, custins.getId(), maxscaleInsName);
                this.createRwSplitDefualtEndpointConfig(endpoint,
                    custinsService.getEndpointAllNodesForPolarDB(characterCustinsList));
                endpointList.add(endpoint);
            }

            // 增加 OSS 资源的申请
            resourceContainer = this.addOssResModel(custins, resourceContainer);

            Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
            if (!response.getCode().equals(200)) {
                for (EndpointDO endpoint : endpointList) {
                    endpointIDao.deleteEndpointById(endpoint.getId());
                    endpointConfigIDao.deleteEndpointConfigByEndpointId(endpoint.getId());
                }
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }
            // 大客户专享集群 传入的zoneId 确定 不需要更新zoneId
            if (!isUserCluster) {
                avzSupport.updateAVZInfoByInstanceIds(avzInfo,
                        response.getData().getCustinsResRespModelList().get(0).getInstanceIdList());
                custinsParamService.updateAVZInfo(custins.getId(), avzInfo);
            } else {
                custinsParamService.updateAVZInfoZoneId(custins.getId(), avzInfo);
                dockerManager.bindHostInstanceRole(custins, dedicatedHostIdForMaster, dedicatedHostIdSlave);
            }

            String storageUpperBound = mysqlParaHelper.getParameterValue(DockerOnEcsConstants.STORAGE_UPPER_BOUND);
            String storageAutoScale = mysqlParaHelper.getParameterValue(DockerOnEcsConstants.STORAGE_AUTO_SCALE);
            String storageThreshold = mysqlParaHelper.getParameterValue(DockerOnEcsConstants.STORAGE_THRESHOLD);

            if (StringUtils.isNotBlank(storageUpperBound) || StringUtils.isNotBlank(storageAutoScale) ||
                    StringUtils.isNotBlank(storageThreshold)) {
                Map<String, String> storageAutoParam = new HashMap<>();
                storageAutoParam.put(DockerOnEcsConstants.STORAGE_UPPER_BOUND, storageUpperBound);
                storageAutoParam.put(DockerOnEcsConstants.STORAGE_AUTO_SCALE, storageAutoScale);
                storageAutoParam.put(DockerOnEcsConstants.STORAGE_THRESHOLD, storageThreshold);
                // todo DAS需要传大region ID, 元数据库未记录
                storageAutoParam.put(ParamConstants.REGION_ID, mysqlParaHelper.getParameterValue(ParamConstants.REGION_ID));
                taskQueueParam.put(DockerOnEcsConstants.AUTO_SCALE_DISK_PARAM, storageAutoParam);
            }


            // 记录 bakowner 和 custins 的关联关系
            Map<String, Object> paramsMap = Maps.newHashMap();
            paramsMap.put("custinsId", custins.getId());
            paramsMap.put("serviceType", "oss");

            List<CustinsSrv> custinsSrvList = custinstServiceIDao.findCustinsService(paramsMap);

            if (CollectionUtils.isNotEmpty(custinsSrvList)) {

                JSONObject json = JSON.parseObject(custinsSrvList.get(0).getExtraInfo());
                json.put("bakowner_id", json.get("ownerId"));

                CustinsParamDO custinsParamDO = new CustinsParamDO(
                    custins.getId(), CustinsParamSupport.OSS_CONFIG, json.toJSONString());

                custinsParamService.createCustinsParam(custinsParamDO);
            }

            if (mysqlParaHelper.needSqllogNewVersion(custins)) {
                custinsParamService.setCustinsParam(custins.getId(),
                    CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION,
                    CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
                for (CustInstanceDO character : characterCustinsList) {
                    custinsParamService.setCustinsParam(character.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION,
                        CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
                }
            }
            //mariadb没有custins_param.compose_tag=alios设置非mariadb设置该参数
            if(!DB_ENGINE_MARIADB.equalsIgnoreCase(custins.getDbType())){
                for (CustInstanceDO character : characterCustinsList) {
                    custinsParamService.setCustinsParam(character.getId(),
                            CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
                }
                custinsParamService.setCustinsParam(custins.getId(),
                        CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
            }

            Integer taskId = dockerCustinsService.createDockerInstanceTask(
                    mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(), custins,
                    characterCustinsList, taskQueueParam,
                    custinsIpWhiteList, mycnfCustinstancesMap,
                    hostType);

            // 创建logic实例写入engine-compose映射表
            if (custins.isLogic()) {
                // 创建 logic 实例记录对应的参数模板
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO,
                        JSON.toJSONString(SysParamGroupHelper.describeSysParamGroupId(paramGroupId)));
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS,
                        JSON.toJSONString(mysqlParaHelper.getAndCheckMysqlCustomParams()));
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_BINLOG_MODE,
                        SysParamGroupHelper.getSyncBinlogMode(paramGroupId).toString());
                custinsParamService.setCustinsParam(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE,
                        SysParamGroupHelper.getSyncMode(paramGroupId).toString());

                custinsIDao.createCustInsComposeRel(custins.getId(), engineCompose.getId());
            }
            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
            success = true;
            data.put(ParamConstants.TASK_ID, taskId);
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());


            try{
                String connAddrCust = "";
                Integer connPort = 0;
                connAddrCust = resourceContainer.getCustinsResModelList().get(0).getVipResModelList().get(0).getConnAddrCust();
                connPort = resourceContainer.getCustinsResModelList().get(0).getVipResModelList().get(0).getVport();
                if (StringUtils.isNotBlank(connAddrCust)){
                    data.put("ConnectionString", connAddrCust);
                }
                if (connPort != 0){
                    data.put("Port", connPort);
                }
            }catch (Exception e){
                logger.error("fail to find ConnectionString or Port");
            }

            return data;
        } finally {
            if (!success) {
                if (custins.getId() != null) {
                    custinsService.deleteCustInstance(custins);
                }
                for (CustInstanceDO characterCustins : characterCustinsList) {
                    if (characterCustins.getId() != null) {
                        custinsService.deleteCustInstance(characterCustins);
                    }
                }
                for (String serviceId : externalRdsServiceIdList) {
                    Map<String, Object> custinsIdAndServiceIdMap = new HashMap<>();
                    custinsIdAndServiceIdMap.put("custinsId", custins.getId());
                    custinsIdAndServiceIdMap.put("serviceId", serviceId);
                    custinstServiceIDao.deleteCustinsService(custinsIdAndServiceIdMap);
                }
            }
        }
    }

    private void createDockerInstanceOnEcs(CustInstanceDO custins,
                                           DockerInsLevelParseConfig config,
                                           InstanceLevelDO characterInsLevel,
                                           Integer custinsOrder,
                                           CustInstanceDO parentCustIns,
                                           ResourceContainer resourceContainer,
                                           RequestParamsDO params,
                                           List<CustInstanceDO> characterCustinsList) throws RdsException {
        // 设置custins参数
        custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_ECS);
        custins.setDbType(characterInsLevel.getDbType());
        custins.setDbVersion(characterInsLevel.getDbVersion());
        custins.setCharacterType(characterInsLevel.getCharacterType());
        custins.setLevelId(characterInsLevel.getId());
        custins.setMaxAccounts(1);
        custins.setMaxDbs(0);
        custins.setConnType(params.getConnType());
        custins.setNetType(CustinsSupport.NET_TYPE_VPC);

        // init data disk category
        params.setDataDiskCategory(params.getDataDiskCategory());

        // TODO: ins name's policy
        if (params.getCharacterInsName() == null) {
            custins.setInsName(custins.getInsName() + characterInsLevel.getId() + custinsOrder);
        } else {
            custins.setInsName(params.getCharacterInsName());
        }
        custins.setParentId(parentCustIns.getId());
        // create character custins
        characterCustinsList.add(custins);
        custinsService.createCustInstance(custins);
        String regionId = params.getRegionId();

        String zoneId = params.getZoneId();

        // Get ecsAccount, vpcId, vSwitchId
        String ecsAccount = ecsService.getEcsAccount(custins.getUserId(), regionId);
        List<com.aliyun.dba.ecs.dataobject.EcsImageDO> ecsImageDOList = null;
        // 得到镜像
        try {
            String dbType = custins.getDbType();
            ecsImageDOList = ecsImageService.getEcsImageList(params.getRegionId(),
                dbType, null, null, null,
                CustinsParamSupport.CUSTINS_PARAM_VALUE_ECS_ARCH_VERSION_VPC, null);
        } catch (Exception RdsException) {
            ecsImageDOList = ecsImageService.getEcsImageList(params.getRegionId(),
                CustinsSupport.CONTAINER_TYPE_DOCKER, null, null, null,
                null, null);
        }
        String imageId = ecsImageDOList.get(0).getImageId();

        String osPassword = SupportUtils.getRandomPasswdForEcs(15);
        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        if (config.getVipCount() > 0) {
            if (CustinsSupport.isVpcNetType(params.getNetType())) {
                String connAddrCust = mysqlParaHelper.getConnAddrCust(
                    custins.getInsName().replace("_", "-"),
                    regionId,
                    custins.getDbType());
                String vpcInstanceId = params.getVpcInstanceId() == null ? parentCustIns.getInsName()
                    : params.getVpcInstanceId();
                VipResModel vipResModel = new VipResModel(CustinsSupport.NET_TYPE_VPC);
                vipResModel.setVswitchId(params.getVswitchId());
                vipResModel.setVpcId(params.getUserVpcId());
                vipResModel.setUserVisible(1);
                vipResModel.setTunnelId(params.getTunnelId());
                vipResModel.setVip(params.getIpaddress());
                vipResModel.setVport(parentCustIns.getvPort());
                vipResModel.setZoneId(zoneId);
                //todo: set value from yaochi
                vipResModel.setVpcInstanceId(vpcInstanceId);
                vipResModel.setConnAddrCust(connAddrCust);
                custinsResModel.addVipResModel(vipResModel);
            } else {
                String connType = CustinsSupport.CONN_TYPE_LVS;
                custinsResModel.setConnType(connType);
                for (int j = 0; j < config.getVipCount(); j++) {
                    String connAddrCust = mysqlParaHelper.getConnAddrCust(
                        custins.getInsName().replace("_", "-") + j,
                        regionId,
                        custins.getDbType());
                    if (StringUtils.isBlank(connAddrCust)) {
                        connAddrCust = mysqlParaHelper.getConnAddrCust(
                            custins.getInsName().replace("_", "-") + j,
                            regionId,
                            CustinsSupport.CONTAINER_TYPE_DOCKER);
                    }
                    VipResModel vipResModel
                        = initVipResModel(parentCustIns.getNetType(),
                        connAddrCust, parentCustIns.getvPort(), config.getVportCountPerVip(), connType);
                    custinsResModel.addVipResModel(vipResModel);

                }
            }
        } else {
            custinsResModel.setConnType(CustinsSupport.CONN_TYPE_PHYSICAL);
        }

        String ecsClass = characterInsLevel.getEcsClassCode();
        // 设置部署集合
        EcsDeploymentSet ecsDeploymentSet = null;
        if (characterInsLevel.getInsCount() > 1) {
            // 创建部署集，保证多个主机分布到不同NC
            ecsDeploymentSet = new EcsDeploymentSet();
            ecsDeploymentSet.setDomain(EcsDeploymentSet.Domain.DEFAULT); // 设置部署集的域
            ecsDeploymentSet.setGranularity(EcsDeploymentSet.Granularity.HOST); // 设置部署集的粒度
            ecsDeploymentSet.setStrategy(EcsDeploymentSet.Strategy.STRICT_DISPERSION); // 设置部署集的策略（打散）
        }

        if (custins.isMbaseSql() && characterInsLevel.getInsCount() > 1) {
            //采用双可用区的参数设置
            List<EcsResModel> ecsResModelList = new ArrayList<EcsResModel>();
            for (AvailableZoneInfoDO ecsAvzInfo : params.getMultiAVZExParam().getAvailableZoneInfoList()) {
                EcsResModel ecsResModel = initSingleEcsResModule(imageId, ecsAvzInfo.getVSwitchID(), params, osPassword,
                    1, regionId, ecsAvzInfo.getZoneID(), ecsClass, ecsAccount, config,
                    characterInsLevel.getId(), custins.getDiskSize(), custins.getDbType(),
                    custins.getId(), parentCustIns.getId());
                ecsResModel.setDeploymentSet(ecsDeploymentSet);

                // 专有云环境需要设置Ecs的UserData
                if (custinsService.isInAPCEnvironment()) {
                    ClustersDO clusterDO = ecsService.getEcsAvailableClusterDO(ecsAvzInfo.getRegion(), CustinsSupport.CONTAINER_TYPE_DOCKER,
                            custins.getDbVersion(), params.getHostType(), ecsAvzInfo.getUserSpecified());
                    if (clusterDO == null) {
                        throw new RdsException(ErrorCode.EXTERNAL_FAILURE, "cannot get ecs available cluster ");
                    }
                    String ambariAgentConfig = "/etc/ambari-agent/conf/ambari-agent.ini";
                    // 从bakowner获取天龙VIP
                    String apiUrl = ambariService.getApiUrl(clusterDO.getClusterName());
                    String ambariVIP = ambariService.getAgentVpcVip(apiUrl);
                    if (StringUtils.isBlank(ambariVIP)){
                        throw new RdsException(ErrorCode.EXTERNAL_FAILURE, "Ambari VIP for agent not found for cluster " + custins.getClusterName());
                    }
                    String updateAmbariVIPScript = "#!/bin/sh \n" +
                            String.format("sed -i 's/hostname*.*/hostname = %s/g' %s \n", ambariVIP, ambariAgentConfig) +
                            "ambari-agent restart";
                    ecsResModel.setUserData(new String(Base64.encodeBase64(updateAmbariVIPScript.getBytes())));
                }
                ecsResModelList.add(ecsResModel);
            }
            custinsResModel.setEcsResModelList(ecsResModelList);
        }
        else {
            String dbType = custins.getDbType();
            EcsResModel ecsResModel = initSingleEcsResModule(imageId, params.getVswitchId(), params, osPassword,
                characterInsLevel.getInsCount(),
                regionId, zoneId, ecsClass, ecsAccount, config, characterInsLevel.getId(), custins.getDiskSize(),
                custins.getDbType(),
                custins.getId(), parentCustIns.getId());
            ecsResModel.setDeploymentSet(ecsDeploymentSet);
            custinsResModel.setEcsResModel(ecsResModel);
        }
        resourceContainer.addCustinsResModel(custinsResModel);
        // create ecs acc
        dbsService.createEcsOsAccount(custins, osPassword);
    }

    private void createPhysicalCustInsDockerOnHost(CustInstanceDO characterCustins,
                                                   CustInstanceDO logicCustins,
                                                   DockerInsLevelParseConfig config,
                                                   InstanceLevelDO characterInsLevel,
                                                   Integer vPort,
                                                   Integer bizType,
                                                   String  hostType,
                                                   ResourceContainer resourceContainer,
                                                   Set<Integer> hostIdSet,
                                                   List<CustInstanceDO> characterCustinsList,
                                                   ShardsInfo.Node node,
                                                   EngineService engineService) throws RdsException {
        createPhysicalCustInsDockerOnHost(characterCustins, logicCustins, config,
            characterInsLevel, vPort, bizType, hostType, resourceContainer, hostIdSet, characterCustinsList, node, engineService,true);

    }


    private void createPhysicalCustInsDockerOnHost(CustInstanceDO characterCustins,
                                                   CustInstanceDO logicCustins,
                                                   DockerInsLevelParseConfig config,
                                                   InstanceLevelDO characterInsLevel,
                                                   Integer vPort,
                                                   Integer bizType,
                                                   String  hostType,
                                                   ResourceContainer resourceContainer,
                                                   Set<Integer> hostIdSet,
                                                   List<CustInstanceDO> characterCustinsList,
                                                   ShardsInfo.Node node,
                                                   EngineService engineService, Boolean applyPrivateVip) throws RdsException {

        // TODO fix node is null
        Integer characterInsStorageFromNode = null;
        String characterInsName = null;
        String characterInsDescription = null;
        String characterInsConnType = null;
        Integer characterInsNetType = null;
        List<ShardsInfo.ConnAddr> connAddrs = null;
        if (node != null) {
            characterInsStorageFromNode = node.getStorage();
            characterInsName = node.getDBInstanceName();
            characterInsDescription = node.getDBInstanceDescription();
            characterInsConnType = node.getDBInstanceConnType();
            characterInsNetType = node.getDBInstanceNetType();
            connAddrs = node.getConnAddrs();
        }

        // valid-set for character ins disk_size.
        String characterInsStorage = null;
        if (characterInsStorageFromNode == null) {
            characterInsStorage = Long.toString(characterInsLevel.getDiskSize()/1024);
        }else{
            characterInsStorage = characterInsStorageFromNode.toString();
        }
        // allow not specify disk_size for characterInsLevel.
        setDiskSize(characterCustins, bizType, characterInsStorage, 0, characterInsLevel.getMaxDiskSize() / 1024);

        // valid-check for character ins_name
        if (Validator.isNull(characterInsName)) {
            characterInsName = CustinsSupport.getValidInsName(logicCustins.getInsName(),
                characterInsLevel.getDbType());
        }else{
            CheckUtils.checkValidForInsName(characterInsName);
        }

        // valid-check for character DB_INSTANCE_DESCRIPTION
        if (Validator.isNotNull(characterInsDescription)) {
            characterInsDescription = SupportUtils.decode(characterInsDescription);
            CheckUtils.checkLength(characterInsDescription, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
        }

        // valid-check for character conn_type.
        if (Validator.isNull(characterInsConnType)) {
            characterInsConnType = CustinsSupport.CONN_TYPE_LVS;
        }else{
            CheckUtils.checkConnType(characterInsConnType);
        }

        // valid-check for character net_type.
        if (characterInsNetType == null) {
            characterInsNetType = CustinsSupport.NET_TYPE_PRIVATE;
        }

        characterCustins.setDbType(characterInsLevel.getDbType());
        characterCustins.setDbVersion(characterInsLevel.getDbVersion());
        characterCustins.setCharacterType(characterInsLevel.getCharacterType());
        characterCustins.setLevelId(characterInsLevel.getId());
        characterCustins.setParentId(logicCustins.getId());
        if(!"general".equalsIgnoreCase(characterInsLevel.getCategory())){
            characterCustins.setInsName(characterInsName);
        }
        characterCustins.setConnType(characterInsConnType);
        // keep original behaviour(inherit from logic instance) if characterInsDescription is null
        if (Validator.isNotNull(characterInsDescription)){
            characterCustins.setComment(characterInsDescription);
        }

        // create character custins
        characterCustinsList.add(characterCustins);
        custinsService.createCustInstance(characterCustins);

        CustinsResModel custinsResModel = new CustinsResModel(characterCustins.getId());
        // valid-check for character conn_string.
        if (applyPrivateVip && config.getVipCount() > 0) {
            if (connAddrs != null && connAddrs.size() != config.getVipCount()){
                logger.error("count of ConnAddrs in ShardsInfo is not consist with level config for db_type: "
                    + characterInsLevel.getDbType() + ", db_version: " + characterInsLevel.getDbVersion());
                throw new RdsException(ErrorCode.INVALID_NODES_CONNADDRS);
            }
            for (int j = 0; j < config.getVipCount(); j++) {
                String connString  = characterCustins.getInsName().replace("_", "-") + j;
                Integer tunnelId = null;
                String vpcId = null;
                String vswitchId = null;
                String ipAddress = null;
                String vpcInstanceId = null;
                if(connAddrs != null){
                    // valid-check for character connString.
                    if (connAddrs.get(j).getConnectionString() != null) {
                        connString = connAddrs.get(j).getConnectionString();
                    }
                    CheckUtils.checkValidForConnAddrCust(connString);

                    // valid-check for tunnelId
                    tunnelId = connAddrs.get(j).getTunnelId();
                    if (CustinsSupport.NET_TYPE_VPC.equals(characterInsNetType)){
                        CheckUtils.checkValidForTunnelId(tunnelId == null? null: tunnelId.toString());
                    }

                    vpcId = connAddrs.get(j).getVPCId();
                    vswitchId = connAddrs.get(j).getVSwitchId();
                    ipAddress = connAddrs.get(j).getIPAddress();
                    vpcInstanceId = connAddrs.get(j).getVpcInstanceId();
                    if (CustinsSupport.NET_TYPE_VPC.equals(characterInsNetType)){
                        CheckUtils.checkValidForVPCId(vpcId);
                        CheckUtils.checkValidForVswitchId(vswitchId);
                        CheckUtils.checkValidForIPAddress(ipAddress);
                        CheckUtils.checkValidForVpcInstanceId(vpcInstanceId);
                    }

                }
                // config characterCustins's dns prefix by logicCustins resource config.
                String connAddrCust = mysqlParaHelper.getConnAddrCust4Docker(
                    connString, CustinsSupport.CONTAINER_TYPE_DOCKER, logicCustins.getDbType());
                String vipSite = engineService.getVipSite();
                VipResModel vipResModel = initVipResModel(
                    characterInsNetType, connAddrCust, vPort, config.getVportCountPerVip(),
                    tunnelId, vpcId, vswitchId, ipAddress, vpcInstanceId, characterInsConnType, vipSite);
                custinsResModel.addVipResModel(vipResModel);
            }
        } else {
            // physical conn_type.
            characterInsConnType = CustinsSupport.CONN_TYPE_PHYSICAL;
        }
        custinsResModel.setConnType(characterInsConnType);

        // register host.
        HostinsResModel hostinsResModel = new HostinsResModel(characterInsLevel.getId());
        String extraInfo = null;
        extraInfo = characterInsLevel.getExtraInfo();
        if (Validator.isNotNull(extraInfo)){
            config = custinsService.parseDockerInsExtraInfo(extraInfo);
            if (config.getCpuSet() != null && config.getCpuSet()) {
                hostinsResModel.setCpuType(CPUType.CPU_SET);
            }
        }
        hostinsResModel.setInsCount(config.getInsCount());
        hostinsResModel.setInsPortCount(config.getPortCountPerIns());
        hostinsResModel.setHostType(hostType != null ? Integer.parseInt(hostType): -1);
        hostinsResModel.setDiskType(config.getDiskType());
        hostinsResModel.setDiskSizeSold(Validator.isNotNull(characterInsStorage)?
                Long.parseLong(characterInsStorage)*1024 : characterInsLevel.getDiskSize());

        hostinsResModel.setLabel(engineService.getHostLabel());

        // set distribute mode
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSiteDistributeMode(config.getDistributePolicy().getSite());
        distributeRule.setCabinetDistributeMode(config.getDistributePolicy().getCabinet());
        distributeRule.setHostDistributeMode(config.getDistributePolicy().getHost());
        distributeRule.setSpecifyHostIdSet(hostIdSet);
        // set host res model
        custinsResModel.setHostinsResModel(hostinsResModel);
        // put into resource container
        resourceContainer.addCustinsResModel(custinsResModel);
    }

    private void createRwSplitDefualtEndpointConfig(EndpointDO endpoint, List<String> nodes) {
        Map<String, String> endpointConfig = new HashMap<>();
        endpointConfig.put(EndpointSupport.ENDPOINT_CONFIG_CCR, "1"); // 默认打开读一致性
        endpointConfig.put(EndpointSupport.ENDPOINT_CONFIG_LBS, "load"); // 基于负载的自动调度
        endpointConfig.put(EndpointSupport.ENDPOINT_CONFIG_RWM, "ReadWrite");
        endpointConfig.put(EndpointSupport.ENDPOINT_CONFIG_AANN, "Enable");
        endpointConfig.put(EndpointSupport.ENDPOINT_CONFIG_NODES, StringUtils.join(nodes, ","));

        List<EndpointConfigDO> configList = new ArrayList<>();
        for (Map.Entry<String, String> item : endpointConfig.entrySet()) {
            EndpointConfigDO config = new EndpointConfigDO(endpoint.getId(), item.getKey(), item.getValue());
            configList.add(config);
        }
        endpointConfigIDao.batchCreateEndpointConfig(configList);
    }

    // 大客户专享集群 云盘版 初始化 ecsResModule
    private EcsResModel initDHGEcsResModule(RequestParamsDO params, DockerInsLevelParseConfig config,
                                            InstanceLevelDO characterInsLevel, CustInstanceDO characterCustins,
                                            CustInstanceDO custins) throws RdsException {
        RdsRegionDO rdsRegionDO =  regionService.getRegion(params.getRegionId());
        String ecsAccount = rdsRegionDO.getUserName();
        List<com.aliyun.dba.ecs.dataobject.EcsImageDO> ecsImageDOList = null;
        // 得到镜像
        try {
            ecsImageDOList = ecsImageService.getEcsImageList(params.getRegionId(),
                    characterCustins.getDbType(), null, null, null,
                    CustinsParamSupport.CUSTINS_PARAM_VALUE_ECS_ARCH_VERSION_VPC, null);
        } catch (Exception e) {
            logger.info("get ecs image error, use region get ecs image.., error msg is " + e.getMessage());
            ecsImageDOList = ecsImageService.getEcsImageList(params.getRegionId(),
                    CustinsSupport.CONTAINER_TYPE_DOCKER, null, null, null,
                    null, null);
        }
        String imageId = ecsImageDOList.get(0).getImageId();

        return initSingleEcsResModule(imageId, params.getVswitchId(),
                params, null,
                characterInsLevel.getInsCount(),
                params.getRegionId(), params.getZoneId(),
                characterInsLevel.getEcsClassCode(), ecsAccount,
                config, characterInsLevel.getId(), characterCustins.getDiskSize(),
                characterCustins.getDbType(),
                characterCustins.getId(), custins.getId());

    }


    public EcsResModel initSingleEcsResModule(String imageId, String vswitchId, RequestParamsDO params, String osPassword,
                                              Integer insCount, String regionId, String zoneId,
                                              String ecsClass, String ecsAccount, DockerInsLevelParseConfig config,
                                              Integer levelId, Long diskSize, String dbType,
                                              Integer characterCustinsId, Integer primaryCustinsId) throws RdsException {

        EcsResModel ecsResModel = new EcsResModel(imageId);
        ecsResModel.setVpcType(VpcType.USER_VPC);
        ecsResModel.setEcsVSwitchId(vswitchId);
        ecsResModel.setEcsVpcId(params.getUserVpcId());
        if (CustinsSupport.isAnalyticDB(dbType)
            && vswitchId == null
            && params.getVpcInfos() != null
            && params.getVpcInfos().size() > 0) {
            ecsResModel.setEcsVSwitchId(params.getVpcInfos().get(0).getVswitchId());
            ecsResModel.setEcsVpcId(params.getVpcInfos().get(0).getVpcId());
        }

        ecsResModel.setOsPassword(osPassword);
        ecsResModel.setInsCount(insCount);
        ecsResModel.setRegionId(mysqlParaHelper.transferRegionId(regionId));
        ecsResModel.setZoneId(mysqlParaHelper.transferZoneId(zoneId));
        ecsResModel.setInstanceType(ecsClass);
        ecsResModel.setEcsAccount(ecsAccount);
        ecsResModel.setInsPortCount(config.getPortCountPerIns());
        ecsResModel.setLevelId((int) levelId);
        ecsResModel.setDbType(dbType);
        //云盘分级支持MariaDB
        if ((CustinsSupport.DB_TYPE_MYSQL.equals(dbType) || DB_TYPE_MARIADB.equalsIgnoreCase(dbType)) && StringUtils.isNotEmpty(params.getDataDiskCategory())
                && params.getDataDiskCategory().contains(DockerOnEcsConstants.ECS_ClOUD_ESSD)) {
            ecsResModel.setPerformanceLevel(DockerOnEcsConstants.getEssdPerLevel(params.getDataDiskCategory()));
            ecsResModel.setDataDiskCategory(params.getDataDiskCategory());
        }
        String kmsKeyId = mysqlParaHelper.getParameterValue("EncryptionKey");
        String roleArn = mysqlParaHelper.getParameterValue("RoleARN");
        String uid = mysqlParaHelper.getUID();

        if (StringUtils.isNotBlank(kmsKeyId) && StringUtils.isNotBlank(roleArn)){
            // byok, check key
            cloudSSDEncryptionService.checkByokKeyAvail(characterCustinsId, regionId, roleArn, kmsKeyId, uid, primaryCustinsId);
            // role list
            List<EcsResModel.Arn> roleArnList = cloudSSDEncryptionService.getByokList(roleArn, uid, ecsAccount);
            ecsResModel.setkMSKeyId(kmsKeyId);
            ecsResModel.setArns(roleArnList);
        }

        // 显示指定磁盘大小为0或者规格中声明为0

        List<EcsDataDisk> dataDiskList = new ArrayList<>();
        if(diskSize != 0L) {
            EcsDataDisk dataDisk = new EcsDataDisk();
            dataDisk.setSize(mysqlParaHelper.getExtendDiskSizeForEcsIns(CustinsSupport.DB_TYPE_DOCKER, diskSize));
            if (params.getDataDiskCategory().contains(DockerOnEcsConstants.ECS_ClOUD_ESSD)) {
                dataDisk.setCategory(DockerOnEcsConstants.ECS_ClOUD_ESSD);
            }else {
                dataDisk.setCategory(params.getDataDiskCategory());
            }
            dataDisk.setDevice(CustinsSupport.ECS_DEISK_PREFIX + "/data");
            dataDisk.setRegionId(regionId);
            if(params.getClone() != null && params.getClone()){
                dataDisk.setSnapshotId(params.getSnapShotId());
                ecsResModel.setSnapshotId(params.getSnapShotId());
            }
            dataDisk.setZoneId(zoneId);
            dataDiskList.add(dataDisk);
            ecsResModel.setEcsDataDiskList(dataDiskList);
        }else{
            ecsResModel.setDataDiskSize(0L);
        }

        return ecsResModel;
    }

    /**
     * 申请 OSS 资源，目前是 bucket 资源
     *
     * @param custins 申请实例信息
     * @throws RdsException 异常
     */
    private ResourceContainer addOssResModel(CustInstanceDO custins, ResourceContainer resourceContainer) throws RdsException {

        String ossInfo = mysqlParaHelper.getParameterValue(ParamConstants.OSS_INFO);

        if (StringUtils.isNotBlank(ossInfo)) {
            JSONObject ossInfoJson = JSON.parseObject(ossInfo);

            String ossBucketName = ossInfoJson.getString(ParamConstants.OSS_BUCKET_NAME);
            String ossAccessPrivilege = ossInfoJson.getString(ParamConstants.OSS_ACCESS_PRIVILEGE);
            String ossStorageType = ossInfoJson.getString(ParamConstants.OSS_STORAGE_TYPE);

            // 校验 BucketName 值
            // 可参见：https://help.aliyun.com/document_detail/31827.html
            if (StringUtils.isBlank(ossBucketName)) {
                logger.error("custins(" + custins.getInsName() + ") oss bucket name can't be empty");
                throw new RdsException(ErrorCode.INVALID_OSS_BUCKET_NAME);
            }

            // 默认读写访问权限为 private
            // 可参见：https://help.aliyun.com/document_detail/31843.html
            if (StringUtils.isBlank(ossAccessPrivilege)) {
                ossAccessPrivilege = "private";
            }

            // 默认存储类型为 Standard
            // 可参见：https://help.aliyun.com/document_detail/51374.html
            if (StringUtils.isBlank(ossStorageType)) {
                ossStorageType = "Standard";
            }

            // 装配 OSS Resource Model
            CustinsResModel custinsResModel = new CustinsResModel(custins.getId());

            List<OssResModel> ossResModelList = Lists.newArrayList();

            OssResModel ossResModel = new OssResModel();

            // OSS 资源打标签
            ossResModel.setRole("storage");
            ossResModel.setBucketName(ossBucketName);
            ossResModel.setAccessPrivilege(ossAccessPrivilege);
            ossResModel.setStorageType(ossStorageType);

            ossResModelList.add(ossResModel);

            custinsResModel.setOssResModelList(ossResModelList);

            resourceContainer.addCustinsResModel(custinsResModel);
        }

        return resourceContainer;
    }

    private Integer createBatchGeneralInsTask(CustInstanceDO logicCustins,String orderId, Integer penginePolicyId) throws RdsException{
        try {
            custinsService.createCustInstance(logicCustins);
        }catch (Exception e){
            logicCustins = custinsService.getCustInstanceByInsName(logicCustins.getUserId(), logicCustins.getInsName());
            if(logicCustins == null){
                throw new RdsException(MysqlErrorCode.GENERAL_INS_CREATE_LOGIC_INS_FAILED.toArray());
            }
        }

        try {
            // 下一个新的订单会被block
            custinsParamService.createCustinsParam(new CustinsParamDO(logicCustins.getId(),"BatchId", orderId));
            Map<String, Object> taskparam = new HashMap<>();
            taskparam.put("OrderId", orderId);
            // 下发任务
            TaskQueueDO taskQueue = new TaskQueueDO(mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(), logicCustins.getId(),
                    TASK_TYPE_CUSTINS, "batch_install_general_ins");
            taskQueue.setParameter(JSONObject.toJSONString(taskparam));
            custinsService.updateCustInstanceStatusByCustinsId(logicCustins.getId(), logicCustins.getStatus(), "BATCH_CREATING");
            taskService.createTaskQueue(taskQueue);
            taskService.updateTaskPenginePolicy(taskQueue.getId(), penginePolicyId);
            custinsParamService.createCustinsParam(new CustinsParamDO(logicCustins.getId(),"BatchTaskId", taskQueue.getId().toString()));
            return taskQueue.getId();
        }catch (Exception e){
            logicCustins = custinsService.getCustInstanceByInsName(logicCustins.getUserId(), logicCustins.getInsName());
            CustinsParamDO orderCustinsParamDO = custinsParamService.getCustinsParam(logicCustins.getId(), "BatchId");
            if(orderCustinsParamDO == null){
                throw new RdsException(MysqlErrorCode.GENERAL_INS_BATCH_ID_CREATE_FAILED.toArray());
            }
            if(!orderCustinsParamDO.getValue().equalsIgnoreCase(orderId)){
                // 订单id不一致，是下一批
                throw new RdsException(MysqlErrorCode.GENERAL_INS_BATCH_INSTALLING.toArray());
            }
            CustinsParamDO taskCustinsParamDO = custinsParamService.getCustinsParam(logicCustins.getId(), "BatchTaskId");
            if(taskCustinsParamDO == null){
                throw new RdsException(MysqlErrorCode.GENERAL_INS_BATCH_TASK_ID_CREATE_FAILED.toArray());
            }
            return Integer.parseInt(taskCustinsParamDO.getValue());
        }

    }

    /**
     * 创建一主多从 Docker On Ecs 实例
     */
    private Map<String, Object> createGeneralCategoryDockerInstance(Map<String, String> actionParams) throws RdsException{
        try {

            // Start 参数校验模块

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            //insType=0 主库，insType=1 只读
            String insType = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_USED_TYPE);
            String groupName = mysqlParamSupport.getAndCheckGeneralCategoryGroupName(actionParams);
            Integer userId = mysqlParamSupport.getAndCheckUserId(actionParams);
            String dbType = mysqlParamSupport.getAndCheckDBType(actionParams, "mysql");
            String dbVersion = mysqlParamSupport.getCheckDBVersion(actionParams, "mysql", "5.7");
            String engine = mysqlParamSupport.getParameterValue(actionParams,ParamConstants.ENGINE);
            String engineVersion = mysqlParamSupport.getParameterValue(actionParams,ParamConstants.ENGINE_VERSION);
            String clusterName = mysqlParamSupport.getAndCheckClusterName(actionParams);
            String dbInstanceName = CheckUtils.checkValidForInsName(mysqlParamSupport.getParameterValue(actionParams, "dbinstancename"));
            String connType = mysqlParaHelper.getAndCheckConnType(null);
            String hostType = mysqlParaHelper.getAndCheckDockerHostType(dbType, dbVersion);
            String orderId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.ORDERID);
            if (orderId == null) {
                return ResponseSupport.createErrorResponse(MysqlErrorCode.GENERAL_INS_ORDER_ID_REQUIRED.toArray());
            }
            if (connType == null) {
                connType = CustinsSupport.CONN_TYPE_LVS;
            }
            if (custinsService.hasCustInstanceByInsName(dbInstanceName)) {
                return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }
            Integer netType = CustinsSupport.getNetType(mysqlParamSupport.getParameterValue(actionParams, "dbinstancenettype", CustinsSupport.NET_TYPE_VPC.toString()));
            String comment = "";
            if (mysqlParamSupport.hasParameter(actionParams, "dbinstancedescription")) {
                String desc = SupportUtils.decode(mysqlParamSupport.getParameterValue(actionParams, "dbinstancedescription"));
                comment = CheckUtils
                        .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
            }
            //diskSize校验
            Long diskSize = null;
            String storage = mysqlParamSupport.getParameterValue(actionParams, "storage");
            if (Validator.isNotNull(storage)) {
                Integer maxDiskSize = ResourceSupport.getInstance()
                        .getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
                Integer minDiskSize = CustinsSupport.ECS_MIN_DISK_SIZE;
                diskSize = CheckUtils.parseInt(storage, minDiskSize,
                        maxDiskSize, ErrorCode.INVALID_STORAGE) * 1024L;
            } else {
                return createErrorResponse(MysqlErrorCode.STORAGE_EMPTY.toArray());
            }

            // 设置实例可维护时间
            Date maintainStartTime = mysqlParamSupport
                    .getAndCheckTimeByParam(actionParams, ParamConstants.MAINTAIN_STARTTIME, DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
                            ErrorCode.INVALID_STARTTIME,
                            CustinsSupport.DEFAULT_MAINTAIN_START_TIME);
            Date maintainEndTime = mysqlParamSupport
                    .getAndCheckTimeByParam(actionParams, ParamConstants.MAINTAIN_ENDTIME, DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
                            ErrorCode.INVALID_ENDTIME, CustinsSupport.DEFAULT_MAINTAIN_END_TIME);

            //avz信息
            AVZInfo avzInfo = avzSupport.getAVZInfo(ActionParamsProvider.ACTION_PARAMS_MAP.get());
            if (!avzInfo.isValidForNewInstance()) {
                throw new RdsException(ErrorCode.INVALID_REGION);
            }

            // End 参数校验模块

            // Start 资源参数拼装
            //开始资源申请参数组装
            RequestParamsDO params = new RequestParamsDO();

            //基础信息，专享集群的必须要将clustername 透传给res 才能正确分配主机
            params.setClusterName(clusterName);
            params.setInsType(insType);
            params.setUserId(userId);
            params.setDbBInstanceName(dbInstanceName);
            params.setReadInsName(dbInstanceName);
            params.setDbType(dbType);
            params.setHostType(hostType);
            params.setDbVersion(dbVersion);
            params.setClassCode(mysqlParamSupport.getAndCheckClassCode(actionParams));
            params.setStorage(Long.toString(diskSize / 1024));
            params.setConnType(connType);
            params.setMaintainStartTime(
                    Time.valueOf(DateSupport.timeWithSecond2str(maintainStartTime)));
            params.setMaintainEndTime(
                    Time.valueOf(DateSupport.timeWithSecond2str(maintainEndTime)));
            params.setOperatorId(getOperatorId(actionParams));
            params.setAction(mysqlParamSupport.getAction(actionParams));
            params.setClone(false);
            params.setIpSet(mysqlParamSupport.getAndCheckSecurityIpList(actionParams));
            params.setOptmization(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.OPTMIZATION_SERVICE, "0"));


            //物理拓扑信息
            params.setRegionId(mysqlParaHelper.getAndCheckRegionID());
            params.setZoneId(mysqlParaHelper.getAndCheckAvZone());
            if (StringUtil.isEmpty(params.getRegion())) {
                params.setRegion(mysqlParamSupport.getParameterValue(actionParams, "Region"));
            }
            if (StringUtil.isEmpty(params.getRegion())) {
                params.setRegion(clusterService.getRegionByCluster(clusterName));
            }
            params.setAvzInfo(avzInfo);

            //网络信息
            params.setNetType(netType);
            params.setVpcInstanceId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID));
            params.setUserVpcId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VPC_ID));
            params.setTunnelId(Integer.valueOf(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.TUNNEL_ID)));
            params.setVswitchId(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VSWITCH_ID));
            params.setIpaddress(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.IP_ADDRESS));
            String vpcInstanceId = null;
            if (CustinsSupport.isVpcNetType(params.getNetType())) {
                vpcInstanceId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.VPC_INSTANCE_ID);
                if (vpcInstanceId == null) {
                    vpcInstanceId = params.getReadInsName();
                }
                params.setVpcInstanceId(vpcInstanceId);
            }

            //连接信息
            // 获取实例连接端口
            String portStr = CustinsSupport
                    .getConnPort(mysqlParamSupport.getParameterValue(actionParams, "port"), dbType);
            params.setPortStr(portStr);
            // 获取实例连接地址
            params.setConnAddrCust(CheckUtils.checkValidForConnAddrCust(mysqlParamSupport.getParameterValue(actionParams, "connectionstring")));


            //复制器标记
            boolean usingReplicator = mysqlParaHelper.getParameterValue("UseReplicator", "0").equals("1");// 确认是否使用 Proxy Binlog 复制器
            String region = mysqlParamSupport.getAndCheckRegion(actionParams);
            if (usingReplicator) {
                // - 对于直连只读实例, 不要求与主实例同 Region (原注释: 2.7.20 只读实例不要求与主实例在同一个 Region)
                // - 由于目前 BLS 限制, 要求 BLS 只读实例同 Region (FIXME: 在 BLS 去除该限制后移除这个校验)
                String custinsRegion = clusterService.getRegionByCluster(clusterName);
                if (!region.equals(custinsRegion)) {
                    return ResponseSupport.createErrorResponse(ErrorCode.INVALID_REGION);

                }
            }
            params.setUsingReplicator(usingReplicator); // 设置启用复制器标记

            // 资源参数拼装

            if(CUSTINS_INSTYPE_PRIMARY.toString().equalsIgnoreCase(insType)){


                CustInstanceDO custins = new CustInstanceDO();
                List<CustInstanceDO> characterCustinsList = new ArrayList<>();
                List<String> externalRdsServiceIdList = new ArrayList<>();
                boolean success = false;
                try {
                    Map<String, String> mysqlCustomParams = mysqlParaHelper.getAndCheckMysqlCustomParams();
                    String paramGroupId = mysqlParaHelper.getParameterValue(ParamConstants.DB_PARAM_GROUP_ID);
                    mysqlParaHelper.checkUserOperatorCluster(userId);
                    custins.setUserId(userId);
                    if (StringUtils.isBlank(engine)) {
                        throw new RdsException(ErrorCode.INVALID_ENGINE);
                    }
                    if (StringUtils.isBlank(engineVersion)) {
                        throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
                    }
                    custins.setDbType(dbType);
                    custins.setDbVersion(engineVersion);

                    String storageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
                    String dataDiskCategory = custinsService.getDataDiskCategory(null, storageType, hostType);
                    //依赖此处获取kindCode
                    mysqlParaHelper.checkAndSetKindCode(custins, hostType);
                    if (StringUtils.isNotBlank(paramGroupId)) {
                        // 参数模板信息检查
                        // FIXME：此处暂时忽略category校验与存储引擎校验
                        SysParamGroupHelper.sysParamGroupIdValidation(paramGroupId, dbType, engineVersion, "", "");
                        parameterGroupTemplateGenerator.paramGroupMatchValidate(dbType, engineVersion, "", paramGroupId, false);
                    }

                    // init task queue param of backup
                    Map<String, Object> taskQueueParam = mysqlParaHelper.getTaskQueueParam();
                    CustInstanceDO srcCustins = null;

                    String restoreType = mysqlParaHelper.getParameterValue(ParamConstants.RESTORE_TYPE);
                    String bakIdStr = mysqlParaHelper.getParameterValue("BackupSetID");

                    Long bakSize = mysqlParaHelper.checkRestoreIllegalAndGetSize(custins, bakIdStr, restoreType);
                    mysqlParaHelper.setRestoreStorageSize(restoreType, hostType, bakSize);

                    if (RESTORE_TYPE_TIME.equals(restoreType)) {
                        srcCustins = mysqlParaHelper.validAndGetSrcCust(dbType, engineVersion);
                        Date restoreTimeDate = mysqlParaHelper.getAndCheckTimeByParam(ParamConstants.RESTORE_TIME,
                                DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME, DataSourceMap.DATA_SOURCE_DBAAS);
                        LogPlanDO logPlan = bakService.getLogPlanByCustinsId(srcCustins.getId());

                        if (logPlan == null) {
                            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_LOG_STATUS);
                        }
                        mysqlParaHelper.checkRestoreTimeValid(srcCustins, restoreTimeDate, logPlan);

                        mysqlParaHelper.fillRestoreByTimeTaskParam(taskQueueParam, srcCustins);
                    } else if (RESTORE_TYPE_SRCCUST.equals(restoreType)) {
                        srcCustins = mysqlParaHelper.validAndGetSrcCust(dbType, engineVersion);
                        mysqlParaHelper.fillRestoreBySrcCustTaskParam(taskQueueParam, srcCustins);
                    } else if (RESTORE_TYPE_BAKID.equals(restoreType) && custins.isPolarDB()) {
                        srcCustins = mysqlParaHelper.validAndGetSrcCust(dbType, engineVersion);
                    }

                    custins.setMaxDbs(0);
                    custins.setMaxAccounts(1);

                    custinsService.setAccountMode(custins,
                            mysqlParaHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE),
                            mysqlParaHelper.getParameterValue(ParamConstants.ACCOUNT_NAME),
                            mysqlParaHelper.getParameterValue(ParamConstants.REGION));

                    CustinsIpWhiteListDO custinsIpWhiteList = mysqlParaHelper.getAndCheckCustinsIpWhiteList(custins);

                    Integer vPort = CustinsValidator.getRealNumber(CustinsSupport.getConnPort(
                            mysqlParaHelper.getParameterValue(ParamConstants.PORT), custins.getDbType(), custins.getKindCode()));
                    if (vPort < 0) {
                        throw new RdsException(ErrorCode.INVALID_PORT);
                    }
                    custins.setvPort(vPort);

                    mysqlParaHelper.updateCustinsCommonProperties(custins);
                    custins.setConnType(connType);
                    custins.setNetType(netType);

                    if (CustinsSupport.CONN_TYPE_PHYSICAL.equals(connType)) {
                        custins.setNetType(null);
                    }

                    Set<Integer> hostIdSet = new HashSet<>();
                    Boolean isUserCluster = true;
                    // 仅在输入集群名时HostId参数才有效
                    if (StringUtils.isNotBlank(clusterName)) {
                        hostIdSet = mysqlParaHelper.getAndCheckHostIdSet();
                        ClustersDO clusterDO = clusterService.getClusterByClusterName(clusterName);

                        if (clusterDO != null) {
                            isUserCluster = DEDICATED_HOST_GOURP_TYPE.equals(clusterDO.getType());
                        }
                    }

                    String composeTag = mysqlParaHelper.selectComposeTag(clusterName, isUserCluster);


                    //todo charactor ins
                    String classCode = mysqlParaHelper.getAndCheckDBInstanceClassCode(custins, null);
                    InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                            custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
                    if (StringUtils.isBlank(classCode) || insLevel == null) {
                        throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                    }
                    custins.setLevelId(insLevel.getId());
                    // custins must set levelid
                    Map<String, List<MycnfCustinstanceDO>> mycnfCustinstancesMap = mysqlParaHelper
                            .getAndCheckExternalParameter(custins);

                    //
                    if (StringUtils.isNotEmpty(storage)) {
                        //入参磁盘大小是GB
                        if (Integer.parseInt(storage) != 0) {
                            if (custins.isCustinsDockerOnEcs()) {
                                CustinsSupport.setDiskSize(custins, CustinsSupport.BIZ_TYPE_RDS, storage, CustinsSupport.ECS_MIN_DISK_SIZE);
                            }
                            else {
                                CustinsSupport.setDiskSize(custins, CustinsSupport.BIZ_TYPE_RDS, storage);
                            }
                        } else {
                            custins.setDiskSize(0L);
                        }
                    } else {
                        //客户实例表和规格码中的磁盘大小都是MB
                        custins.setDiskSize(insLevel.getDiskSize());
                    }


                    custins.setCharacterType(insLevel.getCharacterType());
                    //todo end of charactor ins




                    String snapShotId = null;

                    if (mysqlParaHelper.getSourceDBInstanceID() != null) {
                        srcCustins = mysqlParaHelper.getAndCheckCustInstanceById("sourcedbinstanceid");
                        //处于删除中的实例，不能从回收站恢复
                        if (CustinsState.STATUS_DELETING.getComment().equals(srcCustins.getStatusDesc()) && srcCustins.getIsDeleted() == 0) {
                            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                        }
                        custins.setParentId(srcCustins.getId());
                        String bakId = mysqlParaHelper.getParameterValue("BackupSetID");
                        Long bakSetId = null;
                        if (null == bakId) {
                            Map<String, Object> condition = ImmutableMap.<String, Object>of(
                                    "ignoredRetention", true,
                                    "custinsIds", ImmutableList.of(srcCustins.getId()),
                                    "location", BAKUPSET_LOCATION_OSS,
                                    "status", "OK");
                            List<Map<String, Object>> bakHistoryMapByCondition = bakService.getBakHistoryMapByCondition(condition);
                            if (bakHistoryMapByCondition.isEmpty()) {
                                return ResponseSupport.createErrorResponse(ErrorCode.BACKUPSET_NOT_FOUND);
                            }
                            bakSetId = (Long)bakHistoryMapByCondition.get(0).get("BackupSetID");
                            bakId = bakSetId.toString();
                        }

                        BakhistoryDO bakhistoryById = bakService.getBakhistoryById(bakSetId);

                        JSONObject bakHistObject = JSONArray.parseObject(bakhistoryById.getSlaveStatus());
                        JSONArray bakHistArray = bakHistObject.getJSONArray(bakHistObject.keySet().toArray()[0].toString());
                        JSONObject jsonObject = bakHistArray.getJSONObject(0);
                        snapShotId = jsonObject.getJSONObject("slave_status").getString("SNAPSHOT_ID");

                        Map<String, Object> restoreParam = new HashMap<>(1);

                        restoreParam.put("snapShotId", snapShotId);
                        //todo: now only support one physical custins clone
                        restoreParam.put("srcParentCustId", srcCustins.getId());
                        taskQueueParam.put("restore", restoreParam);
                    }

                    // 如果源实例不为空，则当前实例参数模板应与源实例保持一致
                    if (srcCustins != null) {
                        // 原实例custins_param表中有数据的情况下赋值（针对回收站实例创建）
                        if (CollectionUtils.isNotEmpty(custinsParamService.getCustinsParams(srcCustins.getId()))) {
                            paramGroupId = custinsParamService.getCustinsParam(
                                    srcCustins.getId(),
                                    CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID)
                                    .getValue();
                            String mysqlCustomParamsStr = custinsParamService.getCustinsParam(
                                    srcCustins.getId(),
                                    CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS)
                                    .getValue();
                            if (StringUtils.isNotBlank(mysqlCustomParamsStr)) {
                                try {
                                    Map<String, String> mysqlCustomParamsTemp = JSONObject.toJavaObject(JSONObject.parseObject(mysqlCustomParamsStr), Map.class);
                                    mysqlCustomParams = mysqlCustomParamsTemp;
                                } catch (Exception e) {
                                    logger.warn(e);
                                }
                            }
                        }
                    }

                    // 确认 paramGroupId，mysqlCustomParams 不会更变后，将参数放入 task q 中
                    // set custom parameter template group
                    taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
                    taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, SysParamGroupHelper.describeSysParamGroupId(paramGroupId));
                    taskQueueParam.put(CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS, mysqlCustomParams);

                    //检查是否有指定版本创建
                    String targetMinorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
                            dbType,
                            engineVersion,
                            classCode,
                            MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL,
                            mysqlParaHelper.getParameterValue("TargetMinorVersion"));

                    //指定版本创建
                    if(targetMinorVersion != null){
                        taskQueueParam.put("minor_version", targetMinorVersion);
                    }


                    Integer bizType = mysqlParaHelper.getAndCheckBizType();
                    custins.setInsName(groupName);
                    custins.setLevelId(mySQLGeneralService.getDefaultLogicInsLevel(dbType,dbVersion).getId());
                    custins.setCharacterType("logic");
                    custins.setClusterName(clusterName);
                    //create logic ins and start create batch intall ins task
                    Integer batchInstallTaskId = createBatchGeneralInsTask(custins, orderId, getPenginePolicyID(actionParams));
                    taskQueueParam.put("OrderId",orderId);
                    taskQueueParam.put("BatchTaskId", batchInstallTaskId);


                    String upgradeMinorVersionOption;
                    if (mysqlParaHelper.hasParameter(ParamConstants.AUTO_UPGRADE_MINOR_VERSION)) {
                        upgradeMinorVersionOption = mysqlParaHelper.getParameterValue(ParamConstants.AUTO_UPGRADE_MINOR_VERSION);
                        if (!ParamConstants.AUTO_UPGRADE_MINOR_VERSION_OPTIONS.contains(upgradeMinorVersionOption)) {
                            throw new RdsException(ErrorCode.INVALID_ITEM_VALUE);
                        }
                    } else {
                        upgradeMinorVersionOption = resourceService.getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).size() > 0 ? resourceService.getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).get(0) : "Auto";
                    }
                    custins = custinsService.getCustInstanceByInsName(custins.getUserId(), custins.getInsName());
                    //custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(), ParamConstants.AUTO_UPGRADE_MINOR_VERSION, upgradeMinorVersionOption));

                    List<EndpointDO> endpointList = new ArrayList<>();
                    // init resource container
                    String dedicatedHostNameForMaster = mysqlParaHelper.getParameterValue(ParamConstants.DedicatedHostNameForMaster,mysqlParaHelper.getParameterValue("TargetDedicatedHostIdForMaster"));
                    String dedicatedHostNameForSlave = mysqlParaHelper.getParameterValue(ParamConstants.DedicatedHostNameForSlave,mysqlParaHelper.getParameterValue("TargetDedicatedHostIdForSlave"));

                    Integer dedicatedHostIdForMaster = null;
                    if (dedicatedHostNameForMaster != null) {
                        Map<String, Object> specifyHostInfo = hostIDao.getHostIdsByDedicatedHostName(dedicatedHostNameForMaster,
                                clusterName);
                        dedicatedHostIdForMaster = (Integer) specifyHostInfo.get("id");
                    }

                    Integer dedicatedHostIdSlave = null;
                    String slaveZoneId = null;
                    if (dedicatedHostNameForSlave != null) {
                        Map<String, Object> slaveSpecifyHostInfo = hostIDao.getHostIdsByDedicatedHostName(dedicatedHostNameForSlave,
                                clusterName);
                        dedicatedHostIdSlave = (Integer) slaveSpecifyHostInfo.get("id");
                        slaveZoneId = hostIDao.getHostInfoParam(dedicatedHostIdSlave, "ZoneId");

                    }


                    ResourceContainer resourceContainer = avzSupport.getRegionInitialedResourceContainer(
                            ActionParamsProvider.ACTION_PARAMS_MAP.get(),
                            CustinsSupport.CONTAINER_TYPE_DOCKER, isUserCluster, dedicatedHostIdForMaster, dedicatedHostIdSlave, slaveZoneId);
                    resourceContainer.setClusterName(clusterName);
                    resourceContainer.setUserId(custins.getUserId());
                    resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));

                    EngineCompose engineCompose;
                    // mariadb supports specifying minor version
                    if (custins.isMariaDB() && mysqlParaHelper.getTargetMinorVersion() != null) {
                        String releaseDate = mariadbMinorVersionHelper.parseReleaseDate(mysqlParaHelper.getTargetMinorVersion());
                        engineCompose = mariadbMinorVersionHelper.getEngineComposeByReleaseDate(custins, releaseDate);
                    } else {
                        engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(dbType,
                                engineVersion, insLevel.getCategory(), composeTag);
                    }

                    if (engineCompose == null) {
                        throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                    }

                    if (custins.isLogic()) {
                        String exRdsServiceIns = mysqlParaHelper.getParameterValue(ParamConstants.RDS_SERVICE_INS_LIST, "[]");
                        JSONArray externalRdsServiceInsList = new JSONArray();
                        try {
                            externalRdsServiceInsList = JSON.parseArray(exRdsServiceIns);
                        } catch (Exception e) {
                            throw new RdsException(ErrorCode.INVALID_RDS_SERVICE_INS_LIST);
                        }
                        Map<String, List<CustInstanceDO>> externalRdsServiceInsMap = new HashMap<>();
                        for (Object rdsServiceInsNameObj : externalRdsServiceInsList) {
                            String rdsServiceInsName = rdsServiceInsNameObj.toString();
                            CustInstanceDO custinsServiceByName = custinsService.getCustInstanceByInsName(null, rdsServiceInsName);
                            if (custinsServiceByName != null) {
                                String tmpInsDbType = custinsServiceByName.getDbType();
                                List<CustInstanceDO> tmpInsList = externalRdsServiceInsMap.get(tmpInsDbType);
                                tmpInsList = tmpInsList == null ? new ArrayList<>() : tmpInsList;
                                tmpInsList.add(custinsServiceByName);
                                externalRdsServiceInsMap.put(tmpInsDbType, tmpInsList);
                            }
                        }

                        JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());

                        InstanceLevelDO characterInsLevel = insLevel;
                        DockerInsLevelParseConfig config = null;
                        EngineService engineService
                                = new Gson().fromJson(jsonServer.getString(characterInsLevel.getDbType()), EngineService.class);
                        if (CustinsSupport.isRdsSrvDockerize(engineService)) {
                            config = custinsService.parseDockerInsExtraInfo(characterInsLevel.getExtraInfo());
                            String uniqueKey = characterInsLevel.getDbType() + characterInsLevel.getDbVersion();

                            CustInstanceDO characterCustins = custins.clone();
                            if (null != engineService.getInsType()) {
                                CustInsType custInsType = CustInsType.getByName(engineService.getInsType());
                                characterCustins.setInsType(custInsType.getValue());
                            }
                                //添加物理节点连接信息
                                ShardsInfo.Node node = new ShardsInfo().new Node();
                                ShardsInfo.ConnAddr characterCustinsConnAddr = new ShardsInfo().new ConnAddr();
                                node.setStorage(diskSize.intValue() / 1024);
                                characterCustinsConnAddr.setTunnelId(params.getTunnelId());
                                characterCustinsConnAddr.setVpcInstanceId(mysqlParaHelper.getParameterValue(ParamConstants.VPC_INSTANCE_ID));
                                characterCustinsConnAddr.setIPAddress(params.getIpaddress());
                                characterCustinsConnAddr.setVPCId(params.getUserVpcId());
                                characterCustinsConnAddr.setVSwitchId(params.getVswitchId());
                                List<ShardsInfo.ConnAddr> connAddrs = new ArrayList<>();
                                connAddrs.add(characterCustinsConnAddr);
                                node.setConnAddrs(connAddrs);
                                node.setDBInstanceNetType(netType);


                                characterCustins.setInsName(dbInstanceName);
                                createPhysicalCustInsDockerOnHost(characterCustins, custins, config,
                                        characterInsLevel, vPort, bizType, hostType, resourceContainer,
                                        hostIdSet, characterCustinsList, node, engineService);
                                // different with physical instance, apply ecs vip model
                                CustinsResModel characterCustinsResModel = resourceContainer.getCustinsResModelList().get(0);
                                params = mysqlParaHelper.inflateDockerParamsWhenCreate(characterCustins,
                                        isUserCluster);
                                String zoneId = params.getZoneId();
                                HostinsResModel hostinsResModel = characterCustinsResModel.getHostinsResModel();
                                if (hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {
                                    params.setDataDiskCategory(dataDiskCategory);
                                    if (snapShotId != null) {
                                        params.setSnapShotId(snapShotId);
                                        params.setClone(true);
                                    }
                                    // 设置云盘属性
                                    List<EcsResModel> ecsResModelList = new ArrayList<>();
                                    EcsResModel ecsResModel = initDHGEcsResModule(params, config, characterInsLevel,
                                            characterCustins, custins);
                                    // 需要将扩容的DISK SIZE 传入ecsResModel 容器中
                                    ecsResModel.setDataDiskSize(mysqlParaHelper.getExtendDiskSizeForEcsIns(CustinsSupport.DB_TYPE_DOCKER,
                                            characterCustins.getDiskSize()));
                                    ecsResModelList.add(ecsResModel);
                                    characterCustinsResModel.setEcsResModelList(ecsResModelList);
                                    // 云盘单独设置下磁盘属性
                                    // 云盘no_need_host_disk
                                    hostinsResModel.setDiskType(DISK_TYPE_NO_NEED);
                                    hostinsResModel.setDiskSizeSold(null);
                                } else {
                                    // 本地盘 disktype 需要设置 disk_type_all
                                    hostinsResModel.setDiskType(DISK_TYPE_ALL);
                                }
                                hostinsResModel.getDistributeRule().setSiteDistributeMode(DistributeMode.TRY_SCATTER);


                        }
                        //end for


                        if (mysqlParaHelper.hasParameter(ParamConstants.ACCOUNT_NAME)) {
                            AccountsDO account = mysqlParaHelper.getAndCheckAccount(custins);
                            dbsService.createDbAndAccount(custins, null, account.clone());
                            List<CustInstanceDO> characterCustInstances = custinsService.getCustInstanceByParentId(
                                    custins.getId());
                            for (CustInstanceDO ci : characterCustInstances) {
                                if (CustinsSupport.CHARACTER_TYPE_PHYSICAL.equals(ci.getCharacterType())) {
                                    dbsService.createDbAndAccount(ci, null, account.clone());
                                }
                            }
                        }

                        // init logic custins resource model
                        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
                        custinsResModel.setConnType(connType);
                        config = null;
                        String extraInfo = null;

                        // init cluster end point
                        extraInfo = insLevel.getExtraInfo();

                        logger.info("InstanceLevelId=" + insLevel.getId() + ", extraInfo=" + extraInfo + ", ConnType=" + connType);
                        if (Validator.isNotNull(extraInfo) && !CustinsSupport.CONN_TYPE_PHYSICAL.equals(connType)) {
                            config = custinsService.parseDockerInsExtraInfo(extraInfo);
                            logger.info("config.getClusterEndpointsCount()=" + config.getClusterEndpointsCount());
                            custins.setClusterName(clusterName);
                            VipResModel vipResModel = mysqlParaHelper.initGeneralLogicInsVipResModel(custins, connType);
                            String primaryEndpointName = mysqlParaHelper.getParameterValue("PrimaryEndpointName");
                            if (StringUtils.isEmpty(primaryEndpointName)) {
                                primaryEndpointName = custins.getInsName();
                            }
                            EndpointDO endpoint = endpointService.getOrCreateEndpoint(primaryEndpointName,
                                    EndpointSupport.ENPOINT_TYPE_PRIMARY, custins.getId(), custins.getInsName());
                            endpointList.add(endpoint);
                            vipResModel.setEndpointId(endpoint.getId());
                            custinsResModel.addVipResModel(vipResModel);
                            logger.info("end of init cluster end point");

                        } else if (connType.equals(CONN_TYPE_PROXY)) {
                            logger.error("not config vip, config conn_type=" + connType + "error.");
                            throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
                        }
                        // put into resource container
                        resourceContainer.addCustinsResModel(custinsResModel);
                    }

                    Map<String, Object> data = new HashMap<>();
                    if (mysqlParaHelper.getAndCheckNeedMaxscaleLink()) {
                        if (!maxscaleCustinsService.isSupportMaxscale(custins)) {
                            logger.error("custins(" + custins.getInsName() + ") can not be supported by Maxscale!");
                            throw new RdsException(ErrorCode.MAXSCALE_NOT_SUPPORT);
                        }

                        String maxscaleInsName = mysqlParaHelper.getAndCheckMaxscaleInsName();
                        Integer maxscaleNetType = mysqlParaHelper.getAndCheckMaxscaleNetType();
                        String maxscaleConnectionString = mysqlParaHelper.getAndCheckMaxscaleConnectionString();
                        if (maxscaleConnectionString == null) {
                            maxscaleConnectionString = maxscaleInsName.replace('_', '-');
                        }
                        Integer maxscalePort = Integer.parseInt(CustinsSupport.getConnPort(null, DB_TYPE_MAXSCALE));
                        String specifiedMaxsclaeClassCode = mysqlParaHelper.getParameterValue("SpecificMaxscaleClassCode");
                        if (CustinsSupport.isVpcNetType(maxscaleNetType)) {
                            String maxscaleTunnelId = CheckUtils.checkValidForTunnelId(
                                    mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_TUNNEL_ID));
                            String maxscaleVpcId = CheckUtils.checkValidForVPCId(
                                    mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_VPC_ID));
                            String maxscaleVswitchId = CheckUtils.checkValidForVswitchId(
                                    mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_VSWITCH_ID));
                            String maxscaleIpaddress = CheckUtils.checkValidForIPAddress(
                                    mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_IP_ADDRESS));
                            String maxscaleVpcInstanceId = CheckUtils.checkValidForIPAddress(
                                    mysqlParaHelper.getParameterValue(ParamConstants.MAXSCALE_VPC_INSTANCE_ID));
                            resourceContainer = maxscaleEndpointImpl.addMaxscaleToResourceContainer(custins, resourceContainer,
                                    maxscaleInsName, region,
                                    maxscaleNetType,
                                    maxscaleVpcId,
                                    Integer.valueOf(maxscaleTunnelId),
                                    maxscaleVswitchId,
                                    maxscaleIpaddress,
                                    maxscaleVpcInstanceId,
                                    custinsIpWhiteList.getIpWhiteList(),
                                    maxscaleConnectionString,
                                    maxscalePort,
                                    specifiedMaxsclaeClassCode);
                        } else {
                            resourceContainer = maxscaleEndpointImpl.addMaxscaleToResourceContainer(custins, resourceContainer,
                                    maxscaleInsName, region,
                                    maxscaleNetType,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    custinsIpWhiteList.getIpWhiteList(),
                                    maxscaleConnectionString,
                                    maxscalePort,
                                    specifiedMaxsclaeClassCode);
                        }
                        data.put("MaxscaleNetType", maxscaleNetType);
                        data.put("MaxscaleConnectionString", maxscaleConnectionString);
                        data.put("MaxscalePort", maxscalePort);
                        EndpointDO endpoint = endpointService.getOrCreateEndpoint(maxscaleInsName,
                                EndpointSupport.ENPOINT_TYPE_RWSPLIT, custins.getId(), maxscaleInsName);
                        this.createRwSplitDefualtEndpointConfig(endpoint,
                                custinsService.getEndpointAllNodesForPolarDB(characterCustinsList));
                        endpointList.add(endpoint);
                    }
                    //polar_mysql的logic才需要
                    //do_mpp_rely(custins, data, region, resourceContainer, custinsIpWhiteList);

                    // 增加 OSS 资源的申请
                    resourceContainer = this.addOssResModel(custins, resourceContainer);
                    logger.info("before  allocateRes");
                    Response<AllocateResRespModel> response = resApi.allocateRes(resourceContainer);
                    if (!response.getCode().equals(200)) {
                        for (EndpointDO endpoint : endpointList) {
                            endpointIDao.deleteEndpointById(endpoint.getId());
                            endpointConfigIDao.deleteEndpointConfigByEndpointId(endpoint.getId());
                        }
                        throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                                ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                    }
                    // 大客户专享集群 传入的zoneId 确定 不需要更新zoneId
                    custinsParamService.updateAVZInfoZoneId(custins.getId(), avzInfo);


                    String storageUpperBound = mysqlParaHelper.getParameterValue(DockerOnEcsConstants.STORAGE_UPPER_BOUND);
                    String storageAutoScale = mysqlParaHelper.getParameterValue(DockerOnEcsConstants.STORAGE_AUTO_SCALE);
                    String storageThreshold = mysqlParaHelper.getParameterValue(DockerOnEcsConstants.STORAGE_THRESHOLD);

                    if (StringUtils.isNotBlank(storageUpperBound) || StringUtils.isNotBlank(storageAutoScale) ||
                            StringUtils.isNotBlank(storageThreshold)) {
                        Map<String, String> storageAutoParam = new HashMap<>();
                        storageAutoParam.put(DockerOnEcsConstants.STORAGE_UPPER_BOUND, storageUpperBound);
                        storageAutoParam.put(DockerOnEcsConstants.STORAGE_AUTO_SCALE, storageAutoScale);
                        storageAutoParam.put(DockerOnEcsConstants.STORAGE_THRESHOLD, storageThreshold);
                        // todo DAS需要传大region ID, 元数据库未记录
                        storageAutoParam.put(ParamConstants.REGION_ID, mysqlParaHelper.getParameterValue(ParamConstants.REGION_ID));
                        taskQueueParam.put(DockerOnEcsConstants.AUTO_SCALE_DISK_PARAM, storageAutoParam);
                    }


                    // 记录 bakowner 和 custins 的关联关系
                    Map<String, Object> paramsMap = Maps.newHashMap();
                    paramsMap.put("custinsId", custins.getId());
                    paramsMap.put("serviceType", "oss");

                    List<CustinsSrv> custinsSrvList = custinstServiceIDao.findCustinsService(paramsMap);

                    if (CollectionUtils.isNotEmpty(custinsSrvList)) {

                        JSONObject json = JSON.parseObject(custinsSrvList.get(0).getExtraInfo());
                        json.put("bakowner_id", json.get("ownerId"));

                        CustinsParamDO custinsParamDO = new CustinsParamDO(
                                custins.getId(), CustinsParamSupport.OSS_CONFIG, json.toJSONString());

                        custinsParamService.createCustinsParam(custinsParamDO);
                    }

                    if (mysqlParaHelper.needSqllogNewVersion(custins)) {
                        custinsParamService.setCustinsParam(custins.getId(),
                                CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION,
                                CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
                        for (CustInstanceDO character : characterCustinsList) {
                            custinsParamService.setCustinsParam(character.getId(),
                                    CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION,
                                    CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
                        }
                    }


                    for (CustInstanceDO character : characterCustinsList) {
                        custinsParamService.setCustinsParam(character.getId(),
                                CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
                        custinsParamService.setCustinsParam(character.getId(),
                                "SwitchWeight", mysqlParamSupport.getParameterValue(actionParams, "SwitchWeight", "100"));
                    }
                    custinsParamService.setCustinsParam(custins.getId(),
                            CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);
                    Integer taskId = dockerCustinsService.createDockerInstanceTask(
                            mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(), custins,
                            characterCustinsList, taskQueueParam,
                            custinsIpWhiteList, mycnfCustinstancesMap,
                            hostType);

                    custinsParamService.setCustinsParam(custins.getId(), "FirstInstall","1");

                    // 创建logic实例写入engine-compose映射表
                    if (custins.isLogic()) {
                        // 创建 logic 实例记录对应的参数模板
                        custinsParamService.setCustinsParam(custins.getId(),
                                CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
                        custinsParamService.setCustinsParam(custins.getId(),
                                CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO,
                                JSON.toJSONString(SysParamGroupHelper.describeSysParamGroupId(paramGroupId)));
                        custinsParamService.setCustinsParam(custins.getId(),
                                CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS,
                                JSON.toJSONString(mysqlParaHelper.getAndCheckMysqlCustomParams()));
                        custinsParamService.setCustinsParam(custins.getId(),
                                CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_BINLOG_MODE,
                                SysParamGroupHelper.getSyncBinlogMode(paramGroupId).toString());
                        custinsParamService.setCustinsParam(custins.getId(),
                                CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE,
                                SysParamGroupHelper.getSyncMode(paramGroupId).toString());

                        custinsIDao.createCustInsComposeRel(custins.getId(), engineCompose.getId());
                    }
                    taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                    taskService.updateTaskPriority(taskId,1);
                    success = true;
                    data.put(ParamConstants.TASK_ID, taskId);
                    data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
                    data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());

                    try{
                        String connAddrCust = "";
                        Integer connPort = 0;
                        connAddrCust = resourceContainer.getCustinsResModelList().get(0).getVipResModelList().get(0).getConnAddrCust();
                        connPort = resourceContainer.getCustinsResModelList().get(0).getVipResModelList().get(0).getVport();
                        if (StringUtils.isNotBlank(connAddrCust)){
                            data.put("ConnectionString", connAddrCust);
                        }
                        if (connPort != 0){
                            data.put("Port", connPort);
                        }
                    }catch (Exception e){
                        logger.error("fail to find ConnectionString or Port");
                    }

                    return data;
                } finally {
                    if (!success) {
                        // 不删除逻辑实例，逻辑实例的任务会自动删除
//                        if (custins.getId() != null) {
//                            custinsService.deleteCustInstance(custins);
//                        }
                        for (CustInstanceDO characterCustins : characterCustinsList) {
                            if (characterCustins.getId() != null) {
                                custinsService.deleteCustInstance(characterCustins);
                            }
                        }
                        for (String serviceId : externalRdsServiceIdList) {
                            Map<String, Object> custinsIdAndServiceIdMap = new HashMap<>();
                            custinsIdAndServiceIdMap.put("custinsId", custins.getId());
                            custinsIdAndServiceIdMap.put("serviceId", serviceId);
                            custinstServiceIDao.deleteCustinsService(custinsIdAndServiceIdMap);
                        }
                    }
                }

            }else if(CUSTINS_INSTYPE_READ.toString().equalsIgnoreCase(insType)){
                //从库逻辑
                CustInstanceDO logicCustins =  mySQLGeneralService.getLogicIns(userId, groupName);
                CustInstanceDO masterIns = mySQLGeneralService.getMasterIns(logicCustins.getId());
                if (masterIns == null){
                    // 主实例未创建前不能创建只读
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
                if (CustinsState.STATE_MAINTAINING.getComment().equals(masterIns.getStatusDesc()) ||
                        CustinsState.STATE_TRANSING.getComment().equals(masterIns.getStatusDesc())) {
                    // 主库迁移中不能创建只读
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);

                }

                Integer batchInstallTaskId = createBatchGeneralInsTask(logicCustins, orderId, getPenginePolicyID(actionParams));

                //存储类型和主库约束校验
                String dataDiskCategoryInput = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_STORAGE_TYPE);
                String dataDiskCategory = getAndCheckCloudReadinStorageType(dataDiskCategoryInput, masterIns.getId(), diskSize);
                params.setDataDiskCategory(dataDiskCategory);

                String composeTag = mysqlParaHelper.selectComposeTag(clusterName, true);
                custinsParamService.setCustinsParam(masterIns.getId(), CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);

                DockerTaskInputParam dockerTaskInputParam = dockerManager.createDockerDBReadInstance(actionParams, params,
                        masterIns, logicCustins);// srcCustins, logic实例, primaryCustins, physical实例
                dockerTaskInputParam.setSrcCusIns(logicCustins);
                dockerTaskInputParam.getTaskQueueParam().put("isGeneral", 1);
                dockerTaskInputParam.getTaskQueueParam().put("OrderId",orderId);
                dockerTaskInputParam.getTaskQueueParam().put("BatchTaskId", batchInstallTaskId);

                Map<String, Object> responseData = dockerManager.disPatchDockerTask(dockerTaskInputParam, false);
                taskService.updateTaskPenginePolicy(Integer.parseInt(responseData.get(ParamConstants.TASK_ID).toString()), getPenginePolicyID(actionParams));
                return responseData;

            }else{
                return createErrorResponse(MysqlErrorCode.GENERAL_INS_TYPE_NOT_SUPPORTED.toArray());
            }


        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    };

    /**
     * 获取 primary physical 实例 storageType
     *  参考 DescribeDBInstanceList接口
     * @return
     */
    public String getPrimaryCustinsStorageType(Integer primaryCustinsId){

        String dataDiskCategory = CustinsSupport.STORAGE_TYPE_CLOUD_SSD;// default value

        if (primaryCustinsId != null) {
            List<EcsDiskDO> ecsDiskList = ecsDiskService.getEcsDiskByCustinsId(primaryCustinsId);
            for (EcsDiskDO ecsDiskDO : ecsDiskList) {
                if (EcsConstants.DISK_TYPE_DATA.equals(ecsDiskDO.getType())) {
                    String performanLevel = ecsDiskService.getEcsTypeFromEcsDiskParamByDiskId(ecsDiskDO.getDiskId());
                    if (StringUtils.isEmpty(performanLevel)) {
                        dataDiskCategory = ecsDiskDO.getCategory();
                    }else {
                        dataDiskCategory = DockerOnEcsConstants.getEssdPerLevel(performanLevel);
                    }
                }
            }
        }

        return dataDiskCategory;
    }

    private static Map<String, Integer> metaDBTimeZoneDiffMap = new ConcurrentHashMap<>();

    /**
     * 获取云盘只读 存储类型
     * @param dataDiskCategory, 控制台/瑶池 storageType
     * @param primaryCustinsId, 原主 物理层实例id
     * @param readinsDiskSize, 只读存储空间, 单位MB
     * @return
     * @throws RdsException
     */
    public String getAndCheckCloudReadinStorageType(String dataDiskCategory, Integer primaryCustinsId, Long readinsDiskSize) throws RdsException{

        // 原主 storageType
        String primaryStorageType = getPrimaryCustinsStorageType(primaryCustinsId);

        if (StringUtils.isBlank(dataDiskCategory)){
            // 没有指定, 对齐原主
            dataDiskCategory = primaryStorageType;
        }

        // 1 存储系列不降级检查, 云盘只读存储规格 >= 原主实例
        Integer primaryWeight = DockerOnEcsConstants.ECS_CLOUD_WEIGHT_MAP.get(primaryStorageType);
        Integer readinsWeight = DockerOnEcsConstants.ECS_CLOUD_WEIGHT_MAP.get(dataDiskCategory);
        if (readinsWeight < primaryWeight){
            throw new RdsException(INVALID_CLOUDRO_STORAGE_TYPE);
        }

        // 2 只读空间检查, essd pl2 >= 465 * 1024MB, pl3 >= 1265 * 1024MB
        Long minSize = DockerOnEcsConstants.ESSD_MIN_SIZE_MAP.get(dataDiskCategory) * 1024L;
        if (minSize != null && readinsDiskSize != null && minSize.compareTo(readinsDiskSize) > 0){
            throw new RdsException(INVALID_ESSD_STORAGE_SIZE);
        }
        return dataDiskCategory;
    }

    /**
     * 已删除实例备份集恢复参数设置
     */
    private void rebuildDeleteInsConfig(Map<String, String> map, DescribeRestoreBackupSetResponse restoreBackupResponse)
            throws Exception {
        try {
            String snapshotid = dbsGateWayService.describeDockerRestoreSnapshot(map);
            String backupId = restoreBackupResponse.getBackupSetInfo().getBackupId();
            Integer sourceDBInstanceId = restoreBackupResponse.getBackupSetInfo().getCustinsId();
            String sourceName = restoreBackupResponse.getBackupSetInfo().getInstanceName();
            logger.info("rebuild deleted ins3, slave status contains: " + snapshotid);
            mysqlParaHelper.setParameter("unifybackupsetid", mysqlParaHelper.getParameterValue("BackupSetID"));
            mysqlParaHelper.setParameter("snapshotid", snapshotid);
            mysqlParaHelper.setParameter("sourcedbinstanceid", String.valueOf(sourceDBInstanceId));
            mysqlParaHelper.setParameter("backupsetid", backupId);
            mysqlParaHelper.setParameter(ParamConstants.RESTORE_TYPE, RESTORE_TYPE_BAKID);
            mysqlParaHelper.setParameter((ParamConstants.SOURCE_DB_INSTANCE_NAME), sourceName);
        } catch (Exception ex) {
            throw new RdsException(BACKUPSET_NOT_FOUND);
        }
    }

}
