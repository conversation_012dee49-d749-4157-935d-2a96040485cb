package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsSearchService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.api.OpenSearchApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.dataobject.ProxyMetaPool;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaulDestroyDBInstanceImpl")
public class DestroyDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DestroyDBInstanceImpl.class);

    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected CustinsIDao custinsIDao;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsSearchService custinsSearchService;
    @Autowired
    protected UserService userService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map) throws RdsException {
        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            CustInstanceDO custins = null;
            if (mysqlParamSupport.getParameterValue(map, "dbinstanceid") == null) {
                custins = custinsIDao.getCustInstanceByInsName(mysqlParamSupport.getAndCheckUserId(map),
                        mysqlParamSupport.getAndCheckDBInstanceName(map));
            } else {
                custins = mysqlParamSupport.getAndCheckCustInstanceById(map, "dbinstanceid");
            }
            if (custins == null || custins.getStatus().equals(CustinsSupport.CUSTINS_STATUS_DESTROYED)) {
                //实例不存在，或者不是实例拥有者
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }

            if (custins.isDeleting()) {
                bakService.cancelBackupTaskByCustInstId(custins.getId());
                bakService.destroyBackupSet(custins.getId());

                custinsIDao.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsSupport.CUSTINS_STATUS_DESTROYED,
                        CustinsState.STATUS_DESTORYED.getComment());
                Map<String, Object> data = new HashMap<String, Object>(1);
                data.put("DBInstanceID", custins.getId());
                return data;
            }

            mysqlParamSupport.checkNotDeleteHaProxyCustins(custins);

            if (custins.isCustinsOnDocker()) {
                if (custins.getParentId() > 0) {
                    logger.warn("Can't delete custins not normal or physical from this api.");
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
            } else if (!custins.isNormal() && !custins.isMysqlLogic()) {
                logger.warn("Can't delete custins not normal or logic from this api.");
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            //如果需要删除的实例是主实例的话，则需要确保该主实例所关联的灾备实例和只读实例已经被删除
            if (custins.isPrimary()) {
                //查询灾备实例，如果有灾备实例的话，则禁止删除
                CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                //过滤掉正在删除的实例
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_GUARD);//灾备实例
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                //查询只读实例，如果有只读实例的话，则禁止删除
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                //过滤掉正在删除的实例
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_READ);//只读实例
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
            }

            if (custins.isRead() && custins.isMysql()) {
                CustInstanceDO primaryins = custinsService
                        .getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                        .getCustinsConnAddrByCustinsId(primaryins.getId(), null, CustinsSupport.RW_TYPE_RW_SPLIT);

                if (custinsConnAddrList.size() > 0) {
                    List<CustInstanceDO> readinsList = custinsService
                            .getReadCustInstanceListByPrimaryCustinsId(primaryins.getId(), false);
                    List<CustInstanceDO> validReadInslist = new ArrayList<>();
                    for (CustInstanceDO readins : readinsList) {
                        if (!CustinsSupport.CUSTINS_STATUS_DELETING.equals(readins.getStatus()) &&
                                !CustinsSupport.CUSTINS_STATUS_CREATING.equals(readins.getStatus())) {
                            validReadInslist.add(readins);
                        }
                    }
                    if (validReadInslist.size() == 1) {
                        CustinsConnAddrDO delCustinsConnAddr = custinsConnAddrList.get(0);
                        ConnAddrChangeLogDO delConnAddrChangeLog = ConnAddrSupport
                                .createConnAddrChangeLogForDeleteNetType(
                                        primaryins.getId(),
                                        delCustinsConnAddr.getNetType(),
                                        delCustinsConnAddr.getConnAddrCust(),
                                        delCustinsConnAddr.getVip(),
                                        delCustinsConnAddr.getVport(),
                                        delCustinsConnAddr.getUserVisible(),
                                        delCustinsConnAddr.getTunnelId(),
                                        delCustinsConnAddr.getVpcId(),
                                        null,
                                        CustinsSupport.RW_TYPE_RW_SPLIT);

                        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>(1);
                        connAddrChangeLogs.add(delConnAddrChangeLog);

                        try {
                            Integer taskId = taskService.changeConnAddrTask(
                                    mysqlParaHelper.getAction(), primaryins, connAddrChangeLogs,
                                    CustinsState.STATUS_ACTIVATION, TaskSupport.TASK_CHANGE_CONN_ADDR_DELETE_VIP,
                                    mysqlParaHelper.getOperatorId());
                            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                        } catch (Exception ex) {
                            logger.error("Custins: " + primaryins.getId()
                                    + " DeleteDBInstanceRWSplitNetType failed when create task. Details: "
                                    + JSON.toJSONString(connAddrChangeLogs));
                            throw new Exception(ex);
                        }
                    }
                }

            }

            if (custinsSearchService.checkCustinsSearch(custins)){
                String apiUrlString = userService.getOpenSearchApiUrlByClusterName(custins.getClusterName());
                if (apiUrlString == null) {
                    return ResponseSupport.createErrorResponse(ErrorCode.OPENSEARCH_NOT_SUPPORT);
                }
                JSONObject apiUrl = JSON.parseObject(apiUrlString);
                OpenSearchApi api = new OpenSearchApi(apiUrl.getString("host"),
                        apiUrl.getString("accesskey"),
                        apiUrl.getString("secret"));
                CustinsSearchDO custinsSearch = custinsSearchService.getCustinsSearchByCustins(custins);
                api.deleteOpenSearchInstance(custinsSearch.getAppName());
                custinsSearchService.deleteCustinsSearch(custinsSearch);
            }

            bakService.cancelBackupTaskByCustInstId(custins.getId());
            bakService.destroyBackupSet(custins.getId());
            //删除实例
            Integer taskId = taskService.deleteCustInstanceAndTask(mysqlParaHelper.getAction(), custins,
                    mysqlParaHelper.getOperatorId());
            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error("DestroyDBInstance ex=" + re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("DestroyDBInstance ex=" + ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
