package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getPenginePolicyID;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultRestartDBInstanceImpl")
public class RestartDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(RestartDBInstanceImpl.class);

    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected TaskService taskService;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    private KmsService kmsService;

    @Autowired
    private MysqlParameterHelper mysqlParaHelper;

    @Autowired
    private MysqlEngineCheckService mysqlEngineCheckService;

    /**
     * 重启实例
     * @category RestartDBInstance
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO srcCustins, Map<String, String> actionParams) throws RdsException {
        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            CustInstanceDO custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            if (custins.isReadAndWriteLock()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (custins.isShare()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);//不是专享实例
            }
            if (!custins.isActive()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);//实例状态错误
            }
            Map<String,Object> param = null;
            if (custins.isLogic()){
                throw new RdsException(ErrorCode.UNSUPPORTED_CHARACTER_TYPE);
            }
            // PolarDB只允许在集群状态为"运行中"时重启节点
            CustInstanceDO logicCustins = custinsService.getCustInstanceByCustinsId(custins.getParentId());
            if (logicCustins != null && logicCustins.isPolarDB() && !logicCustins.isActive()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            boolean isUserCluster = mysqlEngineCheckService.checkUserClusterCustins(custins);

            param = new HashMap<>();
            Set<Integer> insIdSet = new HashSet<>();
            // check instance id specified exist.
            String insIdsStr = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.INSTANCE_ID);
            // if no ins id specified, restart all instance of the custins
            if (StringUtils.isNotBlank(insIdsStr)) {
                List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
                Set<Integer> allInsIdSet = new HashSet<>();
                for (InstanceDO instance : instanceList) {
                    allInsIdSet.add(instance.getId());
                }
                String[] insIds = insIdsStr.split(",");
                for (String insId : insIds) {
                    Integer srcInstanceId = CustinsValidator
                            .getRealNumber(insId);
                    insIdSet.add(srcInstanceId);
                }

                if (!allInsIdSet.containsAll(insIdSet)) {
                    logger.error("Invalid instance id not found.");
                    throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
                }
            }
            String restartPolicy = mysqlParamSupport.getParameterValue(actionParams,
                    ParamConstants.RESTART_POLICY, CustinsSupport.RESTART_POLICY_HOT);
            if (!CustinsSupport.RESTART_POLICY_COLD.equals(restartPolicy) && !CustinsSupport.RESTART_POLICY_HOT.equals(restartPolicy)) {
                throw new RdsException(ErrorCode.INVALID_RESTART_POLICY);
            }

            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());

            if(instanceList.size()>0){
                InstanceDO levelInstance = instanceList.get(0);
                InstanceLevelDO level = instanceService.getInstanceLevelByLevelId(levelInstance.getLevelId().intValue());
                String levelCategory = level.getCategory();
                if(levelCategory.equals("general")) {
                    param.put("is_general", true);
                }
            }

            param.put("ins_id", insIdSet);
            param.put("restart_policy", restartPolicy);
            param.put("is_user_cluster", isUserCluster);

            Integer taskId = taskService.restartCustInstanceTask(mysqlParaHelper.getAction(), custins, mysqlParaHelper.getOperatorId(),
                    param);
            taskService.updateTaskPenginePolicy(taskId, getPenginePolicyID(actionParams));
            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    public String getUID() {//真正用户的ID（各供应商生成），对应user表dept_name
        return mysqlParaHelper.getParameterValue("uid");
    }
}
