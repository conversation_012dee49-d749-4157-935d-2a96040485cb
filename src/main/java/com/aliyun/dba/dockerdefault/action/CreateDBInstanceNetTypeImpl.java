package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.base.Joiner;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultCreateDBInstanceNetTypeImpl")
public class CreateDBInstanceNetTypeImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CreateDBInstanceNetTypeImpl.class);

    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected AccountService accountService;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsResourceService custinsResourceService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected MySQLService mySQLService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected EcsService ecsService;



    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            Integer netType = CustinsSupport
                    .getNetType(mysqlParamSupport.getParameterValue(params,"dbinstancenettype"));

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (custins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            Map<String, Object> taskQueueParams = new HashMap<>();

            if (!CONN_TYPE_LVS.equals(custins.getConnType())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_CONNTYPE);
            }
            //docker on ecs不支持绑定私有经典内网,for secrity
            if (NET_TYPE_PRIVATE.equals(netType)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // 对于逻辑实例创建vip时先校验instance_level中是否配置了cluster_points内容，若未配置则不支持创建vip
            if (custins.isLogic()) {
                taskQueueParams.put("cluster_endpoint_type", mySQLService.getDockerCustinsClusterEndpointType(custins, params));
            }


            // 检查是否修改次数超过上限
            custinsService.checkConnAddrChangeTimesExceed(custins.getId(), mysqlParamSupport.getAction(params), null);

            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

            Boolean hasVpc = false;
            // 网络类型已存在
            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
                hasVpc = custinsConnAddr.getNetType().equals(CustinsSupport.NET_TYPE_VPC) && custinsConnAddr.getUserVisible() == 1 ? true: hasVpc;
                if (custinsConnAddr.getNetType().equals(netType) && !netType.equals(CustinsSupport.NET_TYPE_VPC)) {
                    return createErrorResponse(ErrorCode.NETTYPE_EXIST);
                }
            }

            String protocol = custins.getDbType();
            String ConnectionString = null;
            List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>();
            CustinsConnAddrDO newCustinsConnAddr = null;
            if (CustinsSupport.isVpcNetType((netType)) && hasVpc){
                //创建一个VPC连接对象
                String tunnelId = CheckUtils.checkValidForTunnelId(mysqlParamSupport.getParameterValue(params, ParamConstants.TUNNEL_ID));
                String vswitchId = CheckUtils.checkValidForVswitchId(mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID));
                String ipaddress = CheckUtils.checkValidForIPAddress(mysqlParamSupport.getParameterValue(params, ParamConstants.IP_ADDRESS));
                String vpcId = CheckUtils.checkValidForVPCId(mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_ID));
                String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_INSTANCE_ID,
                        custins.getInsName()));
                String vpcRegion = mysqlParamSupport.getParameterValue(params, "VPCRegionId");
                taskQueueParams.put("vpc_region_id", vpcRegion);
                String dnsAddr = null;
                String connPrefix = mysqlParamSupport.getParameterValue(params, "connectionstring", "").trim();
                dnsAddr = getRealConnectionAddr(connPrefix, mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()), protocol);
                newCustinsConnAddr = ConnAddrSupport
                        .createCustinsConnAddr(dnsAddr,
                                "3306",
                                netType,
                                CustinsValidator
                                        .getRealNumber(tunnelId, -1),
                                vpcId,
                                vswitchId,
                                ipaddress,
                                vpcInstanceId);
                List<String> connStringList = new ArrayList<String>();
                List<ConnAddrChangeLogDO> currentChangLogs = mySQLService.createConnAddrChangeLogsForCreateVpcNetType(custins,
                        newCustinsConnAddr,
                        vpcRegion);
                connAddrChangeLogs.addAll(currentChangLogs);
                connStringList.add(newCustinsConnAddr.getConnAddrCust());
            } else{
                //创建非VPC链路的场景
                // 连接地址
                Boolean bCurrentIsVPC = false;
                String connAddrCust = null;
                Integer nUserVisible;
                String prefix = mysqlParamSupport.getParameterValue(params, "connectionstring", "").trim();
                if (prefix.isEmpty())//if dns addr is not specified, create an invisible conn_addr.
                    nUserVisible = CustinsConnAddrDO.USER_VISIBLE_NO;
                else {
                    nUserVisible = CustinsConnAddrDO.USER_VISIBLE_YES;
                    connAddrCust = getRealConnectionAddr(prefix, mysqlParamSupport.getRegionIdByClusterName(custins.getClusterName()), protocol);
                }
                String connPort = CustinsSupport.getConnPort(mysqlParamSupport.getParameterValue(params, "port"), custins.getDbType());
                newCustinsConnAddr = new CustinsConnAddrDO(connAddrCust, connPort, netType);
                newCustinsConnAddr.setUserVisible(nUserVisible);
                connAddrChangeLogs = mySQLService.createConnAddrChangeLogsForCreatePublicOrPrivateNetType(
                        custins, newCustinsConnAddr, CustinsSupport.RW_TYPE_NORMAL);
                ConnectionString = newCustinsConnAddr.getConnAddrCust();
            }

            Integer taskId = null;
            String taskKey = TaskSupport.getTaskChangeConnAddrKey(mysqlParamSupport.getAction(params),
                    CustinsSupport.isVpcNetType(netType));
            try {
                taskId = taskService.changeConnAddrTaskWithParam(
                        mysqlParamSupport.getAction(params), custins, connAddrChangeLogs,
                        CustinsState.STATE_NET_CREATING, taskKey,
                        taskQueueParams.isEmpty() ? "" : JSON.toJSONString(taskQueueParams),
                        mysqlParamSupport.getOperatorId(params));
            } catch (Exception ex) {
                // 下发任务失败，但申请VIP成功了，此时会造成资源浪费。日志中记录这种异常信息
                logger.error("Custins: " + custins.getId()
                        + " CreateDBInstanceNetType failed when create task. Details: "
                        + JSON.toJSONString(connAddrChangeLogs));
                throw new Exception(ex);
            }
            taskService.updateTaskPenginePolicy(taskId, CustinsParamSupport.getPenginePolicyID(params));

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("DBInstanceNetType", newCustinsConnAddr.getNetType());
            data.put("ConnectionString", ConnectionString);
            data.put("Port", newCustinsConnAddr.getVport());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
//            DataSourceHolder.setDefaultDataSource();
        }
    }

    /**
     * 防止瑶池传递full addr的兼容方案（redis）待瑶池修复后移除
     *
     * @param prefix
     * @param dbType
     * @return
     * @throws RdsException
     */
    public String getRealConnectionAddr(String prefix, String regionId, String dbType) throws RdsException {
        if (mysqlParaHelper.isValidFullConnectionAddr(prefix, regionId, dbType)) {
            return prefix;
        } else {
            prefix = CheckUtils.checkValidForConnAddrCust(prefix);
            return mysqlParaHelper.getConnAddrCust(prefix, regionId, dbType);
        }
    }

    public Date getClassicNetExpiredTime(Map<String, String> params) throws RdsException {
        Date expiredTime = null;
        String classicExpiredDays = mysqlParamSupport.getParameterValue(params, ParamConstants.CLASSIC_EXPIRED_DAYS);
        if (classicExpiredDays != null) {
            Integer classExpiredDays = CheckUtils.parseInt(
                    classicExpiredDays,
                    1,
                    null,
                    ErrorCode.INVALID_CLASSIC_EXPIRED_DAYS);
            expiredTime = DateUtils.addDays(new Date(), classExpiredDays);
        }
        return expiredTime;
    }
}



