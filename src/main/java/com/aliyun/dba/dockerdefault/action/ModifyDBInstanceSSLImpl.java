package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.common.consts.SSLConsts;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.service.MySQLGeneralService;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME;
import static com.aliyun.dba.support.property.ParamConstants.SSL_VALUE_DISABLED;
import static com.aliyun.dba.support.property.ParamConstants.SSL_VALUE_ENABLED;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultModifyDBInstanceSSLImpl")
public class ModifyDBInstanceSSLImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceSSLImpl.class);

    @Autowired
    private CaServerApi caServerApi;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CustinsIDao custinsIDao;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private TaskService taskService;

    @Autowired
    MysqlParamSupport mysqlParamSupport;

    @Autowired
    MySQLGeneralService mySQLGeneralService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
            CustInstanceDO custins = mysqlParaHelper.getAndCheckCustInstance();

            if (!custins.isDockerLogic()){
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
            }

            String requestId = mysqlParamSupport.getParameterValue(map, ParamConstants.REQUEST_ID);
            if (mySQLGeneralService.basicNotSupport(custInstanceDO, requestId)) {
                logger.info("dockeronecs and onecs not support the basic");
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_BASIC_CATEGORY);
            }

            String caType = mysqlParamSupport.getParameterValue(map, ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
            if (SSLConsts.CA_TYPE_CUSTOM.equalsIgnoreCase(caType)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            try {
                caServerApi.getCAServerConfig(custins.getClusterName());
            } catch (RdsException re) {
                //没有ca server，该集群无法开启ssl
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_ENABLE_SSL);
            }

            return modifySSL(custins);

        } catch (RdsException re) {
            logger.error("ModifyDBInstanceSSL ex="+re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("ModifyDBInstanceSSL ex="+ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }


    public Map<String, Object> modifySSL(CustInstanceDO custins) throws RdsException {

        if (!custins.isActive()) {
            return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        if (custins.isLock()) {
            return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }

        if (!custins.isLogic()){
            //only support task on logical
            return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_CHARACTER_TYPE);
        }

        EngineCompose engineCompose = custinsIDao.getEngineComposeByDbTypeAndDbVersion(custins.getDbType(),
            custins.getDbVersion());
        EngineService engineService = null;

        JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());
        engineService = new Gson().fromJson(jsonServer.getString(custins.getDbType()),
            EngineService.class);

        if(! engineService.isSupportSSL()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        //获取请求参数
        String sslStatus = mysqlParaHelper.getParameterValue(ParamConstants.SSL_ENABLED);
        if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
            //关闭ssl, 0
            sslStatus = SSL_VALUE_DISABLED;
        }else {
            // 开启或更新 ssl, 1
            sslStatus = SSL_VALUE_ENABLED;
        }

        String connectionString = CheckUtils
            .checkNullForConnectionString(
                mysqlParaHelper.getParameterValue(ParamConstants.CONNECTION_STRING));
        if (connectionString.length() > 64) {
            return ResponseSupport.createErrorResponse(ErrorCode.CONNECTIONSTRING_LENGTH_EXCEEDED);
        }

        List<CustInstanceDO> childCustinsList = custinsService.getCustInstanceUnitByParentIdAndCharacterType(
            custins.getId(), CustinsSupport.CHARACTER_TYPE_PHYSICAL);
        if (childCustinsList.size() <= 0){
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
        }
        Boolean connStringValid = false;
        //通过用户传入的连接串，查找当前归属的物理实例，刷新ssl任务，下发到该物理实例
        Integer targetInsId = null;
        //todo 当前对mariadb，mysql等单个物理实例支持良好，后续类型实例需要重新分析
        for (CustInstanceDO physicalcust : childCustinsList){
            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(physicalcust.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
                if (connectionString.equals(custinsConnAddr.getConnAddrCust()) &&
                    custinsConnAddr.isConnAddrUserVisible()) {
                    connStringValid = true;
                    targetInsId = physicalcust.getId();
                    break;
                }
            }
        }

        if (!connStringValid) {
            if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
                logger.warn("close ssl, current connstr doesn't exist, continue");
            } else {
                return ResponseSupport.createErrorResponse(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
            }
        }

        if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)){
            //关闭ssl, 关联链路
            custinsParamService.deleteCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_CERT_CN_NAME);
        }else {
            // 更新实例开启ssl的dns
            CustinsParamDO certCNName = custinsParamService.getCustinsParam(
                custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME);
            if (certCNName == null) {
                custinsParamService.createCustinsParam(
                    new CustinsParamDO(custins.getId(),
                        CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME,
                        connectionString)
                );
            } else {
                custinsParamService.setCustinsParam(
                    custins.getId(),
                    CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME,
                    connectionString
                );
            }
        }

        Integer taskId =  taskService.createModifyInstanceSSLTask(mysqlParaHelper.getAction(),
            mysqlParaHelper.getOperatorId(), custins.getId(), sslStatus,targetInsId);
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

        Map<String, Object> data = new HashMap<String, Object>(3);
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", taskId);
        return data;
    }
}
