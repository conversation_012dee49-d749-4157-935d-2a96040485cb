package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.CPUType;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.DistributeMode;
import com.alicloud.apsaradb.resmanager.DistributeRule;
import com.alicloud.apsaradb.resmanager.HostinsResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.dataobject.DockerInsLevelParseConfig;
import com.aliyun.dba.custins.dataobject.EngineCompose;
import com.aliyun.dba.custins.dataobject.EngineService;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.entity.CustinsState.STATE_SLAVE_INS_TRANSING;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.DEDICATED_HOST_GOURP_TYPE;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultRebuildSlaveInstanceImpl")
public class RebuildSlaveInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(RebuildSlaveInstanceImpl.class);

    @Autowired
    private InstanceService instanceService;
    @Autowired
    private CustinsIDao custinsIDao;
    @Autowired
    private TaskService taskService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private MycnfService mycnfService;
    @Autowired
    private ResApi resApi;
    @Autowired
    private DbsService dbsService;
    @Autowired
    private IpWhiteListService ipWhiteListService;
    @Autowired
    private InstanceIDao instanceIDao;
    @Autowired
    private ModuleService moduleService;
    @Autowired
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    private CustinsParamService custinsParamService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            CustInstanceDO custins = mysqlParaHelper.getAndCheckCustInstance();
            if (custins.isLogic() && !custins.isMysql()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            boolean isUserCluster = mysqlEngineCheckService.checkUserClusterCustins(custins);

            if (custins.isCustinsOnEcs()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (custins.isShare()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            List<InstanceDO> instanceList;
            if (custins.isMysqlLogic()) {
                instanceList = instanceService.getInstanceByParentCustinsId(custins.getId());
            } else {
                instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            }
            // check instance id specified exist.
            Integer srcInstanceId = CustinsValidator
                .getRealNumber(mysqlParaHelper.getParameterValue(ParamConstants.INSTANCE_ID));
            if (srcInstanceId < 0) {
                logger.error(
                    "Invalid instance id format: " + mysqlParaHelper.getParameterValue(ParamConstants.INSTANCE_ID));
                throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
            }
            boolean isExist = false;
            for (InstanceDO instance : instanceList) {
                if (instance.getId().equals(srcInstanceId)) {
                    isExist = true;
                    break;
                }
            }
            if (!isExist) {
                throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
            }

            if (custins.isCustinsDockerOnEcs()) {
                if (custins.isLogic()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
                EngineCompose engineCompose = mysqlParaHelper.getRebuildEngineCompose(custins);

                EngineService engineService = null;
                JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());
                engineService = new Gson().fromJson(jsonServer.getString(custins.getDbType()),
                    EngineService.class);

                if (!engineService.isSupportRebuild()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }

                String rebuildRole = CustinsSupport.ROLE_MAP.get(
                    instanceService.getInstanceByInsId(srcInstanceId).getRole());
                if (!engineService.checkAllowRebuild(rebuildRole)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
            }

            // 获得用户指定备份 hostins 列表 并设置到custins的属性中
            List<Integer> specifyBackupHostinsList = CustinsValidator
                .getRealNumberList(mysqlParaHelper.getParameterValue(ParamConstants.SPECIFY_BACKUP_HOSTINS_LIST));
            custins.setSpecifyBackupHostinsList(specifyBackupHostinsList);

            String rebuildType = mysqlParaHelper.getAndCheckRebuildType();

            if (isUserCluster && custins.isReadBackup() && !CustinsSupport.SLAVE_REBUILD_TYPE_LOCAL.equals(rebuildType)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (isUserCluster && !custins.isActive() &&
                    ParamConstants.YAOCHI_ACCESS.equals(mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID))) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (isUserCluster && !custins.isNoLock()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            // docker on ecs all local rebuild
            if (CustinsSupport.SLAVE_REBUILD_TYPE_LOCAL.equals(rebuildType) || custins.isCustinsDockerOnEcs() && !isUserCluster) {
                return rebuildSlaveInstanceLocalMode(custins, srcInstanceId, isUserCluster);
            } else {
                return rebuildSlaveInstanceRemoteMode(custins, instanceList, srcInstanceId, isUserCluster);
            }

        } catch (RdsException re) {
            logger.error("RebuildSlaveInstance ex=" + re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("RebuildSlaveInstance ex=" + ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

    private void checkLocalRebuildingTask(CustInstanceDO custins) throws RdsException{
        // 本地重搭避免重复下发任务
        String taskKey = TaskSupport.TASK_REBUILD_INS_LOCAL;
        //如果有延迟加载相关任务running，禁止做恢复
        Map<String, Object> condition = new HashMap<>();
        condition.put("custinsId", custins.getId());
        condition.put("taskKey", taskKey);
        int[] status = {0, 1, 7, 8};
        condition.put("status", status);
        if (taskService.countTaskQueueByCondition(condition) > 0) {
            throw new RdsException(ErrorCode.TASK_HAS_EXIST);
        }
    }

    private void checkRemoteRebuildingTask(CustInstanceDO custins) throws RdsException {
        //夸机避免重复下发
        //docker_on_ecs_local_ssd，大客户专享集群
        if(custins.isMysql() && custins.isCustinsOnDockerOnEcsLocalSSD()){
            String taskKey = TaskSupport.TASK_DOCKER_LOCAL_SSD_REBUILD_SLAVE_REMOTE;
            int count = taskService.countTaskByPrimaryCustinsId(custins.getId(), TASK_TYPE_CUSTINS, taskKey);
            if (count > 0) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
        }
    }

    /**
     * 支持强制备份 单机和对机都支持
     *
     * @param custins
     */
    public Map<String, Object> rebuildSlaveInstanceLocalMode(CustInstanceDO custins, Integer srcInstanceId,
                                                             boolean isUserCluster)
        throws RdsException {

        // only mysql or docker instance support local rebuild
        if (!custins.isMysql() && !custins.isCustinsOnDocker()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DB_TYPE);
        }

        Map<String, Object> param = new HashMap<>();
        if (custins.isMysql()) {
            boolean isForce = mysqlParaHelper.getParameterValue("IsForce", "false").equalsIgnoreCase("true");
            if (isForce) {
                param.put("use_backup_on_master", true);
            }
        }
        param.put("rebuild_instance_id", srcInstanceId);

        CustInstanceDO taskCustins = custins;

        if (custins.isCustinsOnDockerOnEcsLocalSSD() && !custins.isReadOrBackup()) {
            taskCustins = custinsService.getCustInstanceByCustinsId(custins.getParentId());
        }

        checkLocalRebuildingTask(taskCustins);

        // 大客户专享集群，备库重搭下发到逻辑实例上
        Integer taskId = instanceService
            .rebuildSlaveInstanceLocalModeTask(mysqlParaHelper.getAction(), taskCustins,
                mysqlParaHelper.getOperatorId(), JSON.toJSONString(param));
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
        if (isUserCluster) {
            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), STATE_SLAVE_INS_TRANSING.getState(),
                    STATE_SLAVE_INS_TRANSING.getComment());
            if (!custins.isReadOrBackup()) {
                custinsService.updateCustInstanceStatusByCustinsId(custins.getParentId(), STATE_SLAVE_INS_TRANSING.getState(),
                        STATE_SLAVE_INS_TRANSING.getComment());
            }
        }

        Map<String, Object> data = new HashMap<>();
        data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
        data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
        data.put(ParamConstants.TASK_ID, taskId);
        return data;
    }

    /**
     * 单机（魔方）备库重搭 暂不支持强制主备备份 只能在单机集群中
     *
     * @param custins
     * @throws RdsException
     */
    public Map<String, Object> rebuildSlaveInstanceRemoteMode(
        CustInstanceDO custins, List<InstanceDO> instanceList, Integer srcInstanceId, boolean isUserCluster) throws RdsException {

        Integer resourceMode = clusterService.getClusterResourceMode(custins.getClusterName());
        // not magic cluster
        if (!CLUSTER_RESOURCE_MODE_SINGLE.equals(resourceMode)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }

        // check have a mirror custins
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setPrimaryCustinsId(custins.getId());
        Long mirrorCount = custinsService.countCustIns(custInstanceQuery);
        if (mirrorCount > 0) {
            throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
        }
        // ecs local ssd 为custins on docker ，先判断实例是否为local ssd，然后下发不同的跨机备库重搭任务流
        if (KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD.equals(custins.getKindCode()) || isUserCluster) {
            CustInstanceDO tempCustins = cloneCustinsForRebuild(custins, true);
            return rebuildLocalSSDResContainerForRebuild(custins, instanceList, srcInstanceId, tempCustins,
                    isUserCluster);
        } else if (custins.isCustinsOnDocker()) {
            if (!custins.isActive()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_STATUS_EXCEPT_ACTIVE);
            }
            CustInstanceDO tempCustins = cloneCustinsForRebuild(custins, true);
            return rebuildInstanceAllocateRes(custins, srcInstanceId, instanceList, tempCustins, isUserCluster);
        }

        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
    }

    public CustInstanceDO cloneCustinsForRebuild(CustInstanceDO custins, boolean saveToDB) throws RdsException {
        CustInstanceDO tmpCustins = cloneCustins(custins, CUSTINS_INSTYPE_MIRROR, "rebuild");
        if (saveToDB) {
            custinsService.createCustInstanceForRebuildSlave(custins, tmpCustins);
        }
        return tmpCustins;
    }

    private CustInstanceDO cloneCustins(CustInstanceDO custins, Integer insType, String namePrefix)
        throws RdsException {
        // 创建临时实例
        Long timestamp = System.currentTimeMillis();
        CustInstanceDO tempCustins = custins.clone();
        tempCustins.setId(null);
        tempCustins.setClusterName("");
        tempCustins.setInsName(namePrefix + timestamp + "_" + custins.getInsName());
        tempCustins.setStatus(CUSTINS_STATUS_CREATING);
        tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
        tempCustins.setInsType(insType);
        tempCustins.setLevelId(custins.getLevelId());
        tempCustins.setDbVersion(custins.getDbVersion());
        tempCustins.setDiskSize(custins.getDiskSize());
        tempCustins.setConnType(custins.getConnType());// 必须和源实例一致
        tempCustins.setPrimaryCustinsId(custins.getId());

        return tempCustins;
    }

    private Map<String, Object> rebuildLocalSSDResContainerForRebuild(CustInstanceDO custins,
                                                                      List<InstanceDO> instanceList,
                                                                      Integer srcInstanceId,
                                                                      CustInstanceDO tempCustins,
                                                                      boolean isUserCluster) throws RdsException {
        mycnfService.syncMycnfCustinstances(custins.getId(), tempCustins.getId());
        Map<String, Object> data = new HashMap<>(6);
        String clusterName = custins.getClusterName();
        ClustersDO clustersDO = clusterService.getClusterByClusterName(clusterName);
        String region = clustersDO.getRegion();
        ResourceContainer resourceContainer;
        Set<Integer> hostIdSet = mysqlParaHelper.getAndCheckHostIdSet();
        String resourceStrategy = mysqlParaHelper.getParameterValue("ResourceStrategy");
        resourceContainer = this.getDockerResContainerForRebuild(custins, tempCustins, region, instanceList,
            srcInstanceId, isUserCluster);
        resourceContainer.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));
        resourceContainer.setAccessId(mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID));
        resourceContainer.setOrderId(mysqlParaHelper.getParameterValue(ParamConstants.ORDERID));
        resourceContainer.setClusterName(clusterName);
        Response response = resApi.allocateRes(resourceContainer);
        if (!response.getCode().equals(200)) {
            custinsService.deleteCustInstance(tempCustins);
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }
        boolean isForce = mysqlParaHelper.getParameterValue("IsForce", "false").equalsIgnoreCase("true");
        TransListDO translist = instanceService.createTransListForRebuildSlave(custins, tempCustins,
            srcInstanceId, instanceList, isForce);

        // ECSLOCALSSD CUSTINS
        Integer taskId = this.rebuildEcsLocalSSDSlaveInstanceTask(
            mysqlParaHelper.getAction(), tempCustins, translist,
                mysqlParaHelper.getOperatorId(), null, null, custins);
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), STATE_SLAVE_INS_TRANSING.getState(),
                STATE_SLAVE_INS_TRANSING.getComment());
        custinsService.updateCustInstanceStatusByCustinsId(custins.getParentId(), STATE_SLAVE_INS_TRANSING.getState(),
                STATE_SLAVE_INS_TRANSING.getComment());

        data.put("MigrationID", translist.getId());
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", taskId);
        return data;
    }

    /**
     * 主机实例节点重搭
     *
     * @param custins
     * @throws RdsException
     */
    private Map<String, Object> rebuildInstanceAllocateRes(CustInstanceDO custins, Integer srcInstanceId,
                                                           List<InstanceDO> instanceList,
                                                           CustInstanceDO tempCustins,
                                                           boolean isUserCluster) throws RdsException {
        String region = clusterService.getRegionByCluster(custins.getClusterName());

        mycnfService.syncMycnfCustinstances(custins.getId(), tempCustins.getId());

        if (!custins.isCustinsOnDocker()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        ResourceContainer resourceContainer = getDockerResContainerForRebuild(
            custins, tempCustins, region, instanceList, srcInstanceId, isUserCluster);

        Response response = resApi.allocateRes(resourceContainer);
        if (!response.getCode().equals(200)) {
            custinsService.deleteCustInstance(tempCustins);
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }

        boolean isForce = mysqlParaHelper.getParameterValue("IsForce", "false").equalsIgnoreCase("true");
        TransListDO translist = instanceService.createTransListForRebuildSlave(custins, tempCustins,
            srcInstanceId, instanceList, isForce);
        Integer taskId = instanceService.rebuildSlaveInstanceTask(
            mysqlParaHelper.getAction(), custins, tempCustins, translist, mysqlParaHelper.getOperatorId());
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

        Map<String, Object> data = new HashMap<String, Object>(6);
        data.put("MigrationID", translist.getId());
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", taskId);
        return data;
    }

    private ResourceContainer getDockerResContainerForRebuild(CustInstanceDO custins,
                                                              CustInstanceDO tempCustins,
                                                              String region,
                                                              List<InstanceDO> instanceList,
                                                              Integer srcInstanceId,
                                                              boolean isUserCluster) throws RdsException {

        InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        DockerInsLevelParseConfig config = custinsService.parseDockerInsExtraInfo(insLevel.getExtraInfo());

        DistributeMode cabinetMode = config.getDistributePolicy().getCabinet();
        DistributeMode hostMode = config.getDistributePolicy().getHost();

        ResourceContainer resourceContainer = new ResourceContainer(region, CustinsSupport.DB_TYPE_DOCKER);
        CustinsResModel custinsResModel = new CustinsResModel(tempCustins.getId());
        // init host res model
        HostinsResModel hostinsResModel = new HostinsResModel(custins.getLevelId());
        String extraInfo = null;
        extraInfo = insLevel.getExtraInfo();
        if (Validator.isNotNull(extraInfo)) {
            config = custinsService.parseDockerInsExtraInfo(extraInfo);
            if (config.getCpuSet() != null && config.getCpuSet()) {
                hostinsResModel.setCpuType(CPUType.CPU_SET);
            }
        }
        hostinsResModel.setInsCount(1);
        hostinsResModel.setInsPortCount(config.getPortCountPerIns());
        hostinsResModel.setDiskType(config.getDiskType());

        // set hostins label
        CustInstanceDO logicCustins = custinsService.getCustInstanceByCustinsId(custins.getParentId());
        if (logicCustins == null && custins.isRead()) {
            CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            logicCustins = custinsService.getCustInstanceByCustinsId(primaryCustins.getParentId());
        }
        String composeTag = mysqlParaHelper.selectComposeTag(custins.getClusterName());
        EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
            logicCustins.getDbType(), logicCustins.getDbVersion(), insLevel.getCategory(), composeTag);
        JSONObject jsonServer = JSON.parseObject(engineCompose.getServices());
        EngineService engineService = new Gson().fromJson(jsonServer.getString(custins.getDbType()),
            EngineService.class);
        hostinsResModel.setLabel(engineService.getHostLabel());

        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        String dedicatedHostNames = mysqlParaHelper.getParameterValue("DedicatedHostNames");
        if (!StringUtils.isEmpty(dedicatedHostNames)) {
            // 如果传DedicatedHostNames，使用DedicatedHostNames对应的hostids
            distributeRule.setSpecifyHostIdSet(
                mysqlParaHelper.getAndCheckHostIdSetByDedicatedHostNames(dedicatedHostNames, custins.getClusterName()));
        } else {
            distributeRule.setSpecifyHostIdSet(mysqlParaHelper.getAndCheckHostIdSet());
        }
        for (InstanceDO instance : instanceList) {
            if (instance.getId().equals(srcInstanceId)) {
                // keep site name not changed
                if (!isUserCluster) {
                    distributeRule.addSpecifySiteName(instance.getSiteName());
                }else{
                    // 大客户专享主可用区，主库可用区不对齐
                    if(ParamConstants.DispenseMode.MultiAVZDispenseMode.equals(custinsParamService.getDispenseMode(logicCustins.getId()))){
                        String masterZoneId = custinsParamService.getMasterZoneId(logicCustins.getId());
                        String siteNameByAvzone = clusterService.getSiteNameByAvzone(masterZoneId);
                        InstanceDO masterInstanceDO = instanceList.stream().filter(i -> i.getRole().equals(CUSTINS_ROLE_MASTER)).findFirst().orElse(null);
                        if(masterInstanceDO!=null && !siteNameByAvzone.equals(masterInstanceDO.getSiteName())){
                            // 主可用区模式下，主库和主可用区不同，从库可用区需要保持不变，以保证切换后主可用区能对齐
                            distributeRule.addSpecifySiteName(instance.getSiteName());
                        }
                    }

                }

                // set current host id low priority
                distributeRule.addInferiorHostId(instance.getHostId());
                // keep host type same
                hostinsResModel.setHostType(instance.getHostType());
            }
            else {
                if (DistributeMode.FORCE_SCATTER.equals(cabinetMode)) {
                    distributeRule.addExcludeCabinet(instance.getCabinet());
                } else if (DistributeMode.TRY_SCATTER.equals(cabinetMode)) {
                    distributeRule.addInferiorCabinet(instance.getCabinet());
                } else if (DistributeMode.FORCE_CROWD.equals(cabinetMode)) {
                    distributeRule.addSpecifyCabinet(instance.getCabinet());
                }

                if (DistributeMode.FORCE_SCATTER.equals(hostMode)) {
                    distributeRule.addExcludeHostId(instance.getHostId());
                } else if (DistributeMode.TRY_SCATTER.equals(hostMode)) {
                    distributeRule.addInferiorHostId(instance.getHostId());
                } else if (DistributeMode.FORCE_CROWD.equals(hostMode)) {
                    distributeRule.addSpecifyHostId(instance.getHostId());
                }
            }
        }
        // append host res model
        custinsResModel.setHostinsResModel(hostinsResModel);
        // specify connType
        custinsResModel.setConnType(custins.getConnType());
        // append custins res model
        resourceContainer.addCustinsResModel(custinsResModel);
        return resourceContainer;
    }

    private Integer rebuildEcsLocalSSDSlaveInstanceTask(String action,
                                                        CustInstanceDO tempCustins, TransListDO translist,
                                                        Integer operatorId, Date switchTime,
                                                        String switchTimeMode, CustInstanceDO srcPhysicalCustins)
            throws RdsException {
        dbsService.syncAllDbsAndAccounts(srcPhysicalCustins, tempCustins);
        ipWhiteListService.syncCustinsIpWhiteList(srcPhysicalCustins.getId(), tempCustins.getId());

        instanceIDao.createTransList(translist);
        // 此处 custins 是原逻辑实例ID
        CustInstanceDO custins = null;
        if (srcPhysicalCustins.getParentId() != 0) {
            custins = custinsService.getCustInstanceByCustinsId(srcPhysicalCustins.getParentId());
        } else if (srcPhysicalCustins.isRead()) {
            CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(srcPhysicalCustins.getPrimaryCustinsId());
            custins = custinsService.getCustInstanceByCustinsId(primaryCustins.getParentId());
        }
        Integer targetId = custins.getId();

        String taskKey = "rebuild_ins_remote";

        // SpecifyBackupHostinsId 该属性只有在物理实例对象上才有
        List specifyBackupHostinsList = srcPhysicalCustins.getSpecifyBackupHostinsId();
        if (!specifyBackupHostinsList.isEmpty()) {
            Map<String, Object> param = new HashMap();
            param.put("specify_bak_hostins_id", specifyBackupHostinsList);
            param.put("trans_id", translist.getId());
            param.put("is_user_cluster", true);
            TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, targetId, TaskSupport.TASK_TYPE_CUSTINS,
                    taskKey, String.valueOf(JSON.toJSONString(param)));
            taskService.createTaskQueue(taskQueue);
            instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
            return taskQueue.getId();
        } else {
            TaskQueueDO taskQueue;
            if (switchTimeMode != null) {
                translist.setSwitchTime(switchTime);
                String transparam = taskService.getTransTaskParameter(translist.getId(), switchTimeMode, switchTime);
                Map<String, Object> params = JSON.parseObject(transparam);
                params.put("is_user_cluster", true);
                taskQueue = new TaskQueueDO(action, operatorId, targetId, TaskSupport.TASK_TYPE_CUSTINS, taskKey,
                        JSONObject.toJSONString(params));
            } else {
                Map<String, Object> param = new HashMap(2);
                param.put("trans_id", translist.getId());
                param.put("is_user_cluster", true);
                taskQueue = new TaskQueueDO(action, operatorId, targetId, TaskSupport.TASK_TYPE_CUSTINS, taskKey,
                        JSONObject.toJSONString(param));
            }

            taskService.createTaskQueue(taskQueue);
            instanceIDao.updateTransTaskIdById(translist.getId(), taskQueue.getId());
            return taskQueue.getId();
        }
    }
}
