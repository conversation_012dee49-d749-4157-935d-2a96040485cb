package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultEvaluateModifyRegionResourceImpl")
public class EvaluateModifyRegionResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateModifyRegionResourceImpl.class);


    @Autowired
    private InstanceService instanceService;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private DockerCommonService dockerCommonService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            CustInstanceDO custins = mysqlParaHelper.getAndCheckCustInstance();

            String region = mysqlParaHelper.getAndCheckRegion();
            String hostType = mysqlParaHelper.getAndCheckHostType();

            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            String levelCode = mysqlParaHelper.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS);
            InstanceLevelDO newLevel = instanceService
                    .getInstanceLevelByClassCode(levelCode, custins.getDbType(), custins.getDbVersion(),
                            custins.getTypeChar(), null);

            // Engine参数必传
            String dbType = custins.getDbType();
            // Docker 实例集群的db_type类型为docker,引入临时变量做调整
            String dbTypeForCluster = dbType;
            String dbVersion = mysqlParaHelper.getAndCheckDBVersion(dbType, false);


            if (oldLevel.isBasicLevel() && newLevel.isStandardLevel()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
            }

            if (newLevel.isClusterLevel()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
            }

            String targetDataDiskCategory = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            if (STORAGE_TYPE_CLOUD_AUTO.equalsIgnoreCase(targetDataDiskCategory)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
            }

            if (mysqlParaHelper.isEcsBasicToDockerStandard(custins)) {
                if (!mysqlParaHelper.supportBasicToDockerStandard(custins)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
                }

                custins.setKindCode(KIND_CODE_DOCKER_ON_ECS);
                custins.setCharacterType(CHARACTER_TYPE_LOGIC);


                if (newLevel.isStandardLevel()) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
                }

                custins.setLevelId(newLevel.getId());
            }
            if(clusterService.checkIsUserCluster(custins.getClusterName())){
                return dockerCommonService.evaluate(custins);
            }
            if (custins.isCustinsDockerOnEcs()) {
                dbTypeForCluster = DB_TYPE_DOCKER;
                custins.setDbTypeForCluster(dbTypeForCluster);
                return dockerCommonService.evaluate(custins);
            } else {
                Map<String, Object> data = new HashMap<String, Object>();
                if (custins.isCustinsOnEcs()) {
                    if (clusterService.checkRegionWithEcs(region, dbTypeForCluster)) {
                        data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                    } else {
                        data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                    }
                } else {
                    data.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                }
                data.put(ParamConstants.ENGINE_VERSION, dbVersion);
                data.put(ParamConstants.ENGINE, CustinsSupport.getEngine(dbType));
                return data;
            }
        } catch (RdsException re) {
            logger.error("EvaluateModifyRegionResource ex="+re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("EvaluateModifyRegionResource ex="+ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
