package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.AccountsDTO;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD;
import static com.aliyun.dba.dbs.support.AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN;
import static com.aliyun.dba.dbs.support.DbsSupport.BIZ_TYPE_USER;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultCreateAccountImpl")
public class CreateAccountImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CreateAccountImpl.class);

    @Autowired
    private AccountService accountService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private DbossApi dbossApi;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private AccountIDao accountIDao;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            CustInstanceDO custIns = mysqlParaHelper.getAndCheckCustInstance();

            if (!custIns.isLogicPrimary()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            String bid = mysqlParaHelper.getBID();
            String uid = mysqlParaHelper.getUID();
            String accountPriviledge = mysqlParaHelper.getAccountPrivilege();
            String accountType = mysqlParaHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,
                CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON);
            // 如果不是灰度后的实例, 并且是 mysql onECS 版本, 默认都是高权限
            boolean createSuperAccountDefault = (!(dbossApi.isHandleByDBoss(custIns))
                && (custIns.isCustinsDockerOnEcs() || (custIns.isMysql() && custIns.isCustinsOnEcs())));
            if (createSuperAccountDefault) {
                accountType = CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER;
            }
            Integer accountPrivilegeType = CustinsSupport.getAccountPrivilegeType(accountType);
            if (accountPrivilegeType == null) {
                throw new RdsException(ErrorCode.INVALID_ACCOUNT_TYPE);
            }
            AccountPriviledgeType accountPriviledgeType =
                AccountPriviledgeType.getAccountPriviledgeType(accountPrivilegeType);
            String accountName
                = CheckUtils.optimizeCheckValidForAccountName(mysqlParaHelper.getAccountName(), custIns.getDbType(),
                custIns.isTop(), accountPriviledgeType, custIns.getDbVersion(),
                custIns.getKindCode(), DbsSupport.BIZ_TYPE_USER);
            String dbInsName = mysqlParaHelper.getDBInstanceName();
            String password = mysqlParaHelper.getAndCheckDecryptedAccountPassword();
            String dbInfo = mysqlParaHelper.getDbInfo();
            List<String> dbNames = mysqlParaHelper.getDBNames();
            String comment = SupportUtils.decode(mysqlParaHelper.getParameterValue("accountdescription", ""));

            if ((CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON.equals(accountType) && dbossApi.isHandleByDBoss(custIns))) {
                AccountsDO checkAccount = accountIDao.queryAccountByAccountName(custIns.getId(),accountName);
                if (checkAccount != null) {
                    throw new RdsException(ErrorCode.ACCOUNTNAME_ALREADYEXISTS);
                }
                if (!custIns.inAvailableStatus()) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
                String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID, "");

                Map<String, Object> account = new HashMap<>(7);
                account.put("accountName", accountName);
                account.put("accountType", accountType);
                account.put("custinsId", custIns.getId());
                account.put("password", password);
                account.put("requestId", requestId);
                if (!Strings.isNullOrEmpty(comment)) {
                    account.put("comment", CheckUtils.checkLength(comment, 1,
                        256, ErrorCode.INVALID_DBDESCRIPTION));
                }
                List<Map<String, String>> privileges
                    = accountService.getDatabasePrivileges(dbNames, accountPriviledge, dbInfo, custIns.getDbType());
                if (!CollectionUtils.isEmpty(privileges)) {
                    account.put("privileges", privileges);
                }
                dbossApi.createAccount(account);

                Map<String, Object> data1 = new HashMap<>();
                data1.put("AccountName", accountName);
                data1.put("AccountID", 0);
                data1.put("AccountStatus", DbsSupport.STATUS_ACTIVE);
                data1.put("TaskId", 0);
                data1.put(ParamConstants.ACCOUNT_TYPE, accountType);
                return data1;
            }

            // 增加docker类型实例的账户检查
            if (custIns.isCustinsOnDocker() && !custinsService.checkCreateAccountValidForCustinsOnDocker(custIns)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            if (custIns.isCustinsOnDocker()&&CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER.equals(accountType)){
                Map<String,Integer> accountCount = dbossApi.getAccountCount(String.valueOf(custIns.getId()),null, RdsConstants.ROLETYPE_USER);
                Integer totalCount = 0;
                if(accountCount != null &&accountCount.size() > 0){
                    totalCount = accountCount.get("accounts");
                    List<Map<String, Object>> accountList = dbossApi.queryAccounts(
                            custIns.getId(), null, null, 0, totalCount, null);
                    for (Map<String, Object> acc: accountList) {
                        String accName = (String) acc.get("accountName");
                        boolean checkName = accName.equalsIgnoreCase(accountName);
                        if (checkName){
                            throw new RdsException(ErrorCode.ACCOUNTNAME_ALREADYEXISTS);
                        }
                    }
                }
            }

            AccountsDTO addAccountsDTO = new AccountsDTO();
            addAccountsDTO.setBid(bid);
            addAccountsDTO.setUid(uid);
            addAccountsDTO.setDbInsName(dbInsName);
            addAccountsDTO.setAccount(accountName);
            addAccountsDTO.setPassword(password);
            addAccountsDTO.setComment(comment);
            addAccountsDTO.setDbInfo(dbInfo);
            addAccountsDTO.setDbNames(dbNames);
            addAccountsDTO.setAccountPrivilegeDesc(accountPriviledge);// 账户DB之间的权限
            addAccountsDTO.setPriviledgeType(accountPriviledgeType);//普通/超级账号权限
            // 逻辑实例单元不能直接创建账户
            String characterType = custIns.getCharacterType();
            if (characterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_DB) ||
                characterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_SPHINX)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_CHARACTER_TYPE);
            }

            AccountsDTO accountDTO = null;
            //定义集群实例taskId 编号容器
            if (custIns.isCustinsOnDocker()) {
                accountDTO = accountService.addAccountForCustinsOnDocker(addAccountsDTO, mysqlParaHelper.getAction(),
                    mysqlParaHelper.getOperatorId());
            } else {
                accountDTO = accountService.addAccount(addAccountsDTO, mysqlParaHelper.getAction(),
                    mysqlParaHelper.getOperatorId(), mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID));

            }
            Map<String, Object> data = new HashMap<>(4);
            taskService.updateTaskPenginePolicy(accountDTO.getTaskId(), mysqlParaHelper.getPenginePolicyID());
            data.put("AccountID", accountDTO.getId());
            data.put(ParamConstants.TASK_ID, accountDTO.getTaskId());
            data.put("AccountName", accountDTO.getAccount());
            data.put("AccountStatus", accountDTO.getStatus());
            data.put(ParamConstants.ACCOUNT_TYPE, accountType);
            return data;
        } catch (RdsException re) {
            logger.error("createAdminAccount ex=" + re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("createAdminAccount ex=" + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //移除参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
