package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.autoscale.entity.AutoScaleActionAuditLog;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.dockerdefault.service.AutoScaleService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.entity.TransList;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * get taskid by requestId
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultDescribeTaskIdByRequestIDImpl")
public class DescribeTaskIdByRequestIDImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeTaskIdByRequestIDImpl.class);

    @Autowired
    private AutoScaleService autoScaleService;

    @Autowired
    private MysqlParameterHelper mysqlParaHelper;

    @Autowired
    private CustinsService custinsService;

    @Autowired
    private TaskService taskService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
            throws RdsException {

        try{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            String insName = mysqlParaHelper.getDBInstanceName();
            CustInstanceDO custins = mysqlParaHelper.getCustInstance();
            if (custins == null) {
                if (custinsService.checkDeletedCustInstanceByInsName(mysqlParaHelper.getDBInstanceName())) {
                    return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);//实例已销毁
                }
                return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);//实例不存在，或者不是实例拥有者
            }

            String orderRequestId = mysqlParaHelper.getParameterValue("OrderRequestID");
            AutoScaleActionAuditLog auditLog = autoScaleService.getDasAutoScaleActionTaskId(orderRequestId);
            if (auditLog == null || auditLog.getTaskId() == null){
                Map<String, Object> data = new HashMap<String, Object>(4);
                data.put("TaskId", -1);
                data.put("OrderRequestID", orderRequestId);
                data.put("DBInstanceName", insName);
                return data;
            }

            /**
             *  taskId: 1243222
                taskGmtCreated:任务创建时间，UTC格式
                bizInfo
                    srcClassCode：起始规格，下发变配任务时的实例规格
                    srcStorage：起始存储，下发变配任务时的实例空间大小
                    destClassCode：目标规格，下发变配任务的目标实例规格
                    destStorage：目标存储，下发变配任务的目标实例空间大小
             */
            Map<String, Object> data = new HashMap<String, Object>();

            // taskQueue status
            data.put("TaskId", auditLog.getTaskId());
            TaskQueueDO taskQueueDO = taskService.getTaskQueueByIdIncludesSubTask(auditLog.getTaskId());
            if (taskQueueDO != null){
                data.put("TaskStatus", taskQueueDO.getStatus());
                if (StringUtils.equalsIgnoreCase("2", taskQueueDO.getStatus())){
                    String utcNow = DateSupport.local2UTC(taskQueueDO.getTaskEnd());
                    data.put("TaskGmtFinished", utcNow);
                }
            }

            // custins meta when modify
            JSONObject bizInfo = JSONObject.parseObject(auditLog.getBizInfo());
            if (bizInfo!=null){
                data.put("SrcClassCode", bizInfo.get("SrcClassCode"));
                data.put("SrcStorage", bizInfo.get("SrcStorage"));
                data.put("DestClassCode", bizInfo.get("DestClassCode"));
                data.put("DestStorage", bizInfo.get("DestStorage"));
            }

            // custins common metainfo
            data.put("OrderRequestID", orderRequestId);
            data.put("DBInstanceName", insName);
            data.put("TaskGmtCreated", auditLog.getTaskGmtCreated());
            return data;
        } catch (Exception ex) {
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }
}
