package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.alicloud.apsaradb.resmanager.response.UpgradeResRespModel;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.docker.dataobject.DockerTaskInputParam;
import com.aliyun.dba.dockerdefault.service.AutoScaleService;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.dockerdefault.service.DockerManager;
import com.aliyun.dba.dockerdefault.service.DockerTransService;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.dataobject.EcsDiskDO;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.ecs.service.EcsDiskService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.host.dataobject.HostInfo;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelRelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.IpResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.service.MySQLGeneralService;
import com.aliyun.dba.support.api.ResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.ECS_ClOUD_ESSD0;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getAction;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.*;
import static com.aliyun.dba.host.support.HostSupport.HOST_STATUS_ONLINE;
import static com.aliyun.dba.instance.support.InstanceSupport.*;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_REGION;
import static com.aliyun.dba.support.property.ParamConstants.ENGINE_VERSION;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

/**
 * mysql 8.0单节点是docker_on_ecs的
 * 如果ecs到物理机，备份恢复子任务需要重新调整
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultModifyDBInstanceClassImpl")
public class ModifyDBInstanceClassImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceClassImpl.class);
    public static final String CUSTINS_PARAM_NAME_COMPOSE_TAG = "compose_tag";

    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private KmsService kmsService;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private EcsDBService ecsDBService;
    @Autowired
    private DockerCommonService dockerCommonService;
    @Autowired
    private AVZSupport avzSupport;
    @Autowired
    private ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    private DockerManager dockerManager;
    @Autowired
    private DockerCustinsService dockerCustinsService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private InstanceIDao instanceIDao;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    private CheckService checkService;
    @Autowired
    private ResApi resApi;
    @Autowired
    private CustinsIDao custinsIDao;
    @Autowired
    private EcsDiskService ecsDiskService;
    @Autowired
    protected DockerTransService dockerTransService;
    @Autowired
    protected HostIDao hostIDao;
    @Autowired
    protected DbsService dbsService;
    @Autowired
    protected AccountService accountService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected HostService hostService;
    @Autowired
    private AutoScaleService autoScaleService;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected MysqlParamGroupHelper mysqlParamGroupHelper;
    @Autowired
    private MySQLGeneralService mySQLGeneralService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected DTZSupport dtzSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {
            // 该架构暂不支持可用区迁移
            String migratingAvz = mysqlParamSupport.getParameterValue(map, PodDefaultConstants.MIGRATING_AVZ, "false");
            if (Boolean.parseBoolean(migratingAvz)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);


            //ModifyDBInstanceClass为false，MigrateDBInstance为true
            boolean isTransfer = CustinsSupport.isTransfer(getAction(map));

            CustInstanceDO custins = mysqlParaHelper.getAndCheckCustInstance();

            // 云盘不允许降级到PL0
            String targetStorageType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            if(StringUtils.equals(ECS_ClOUD_ESSD0,targetStorageType)){
                CustinsParamDO dataDiskParamsDO = custinsParamService.getCustinsParam(custins.getId(),
                        CustinsParamSupport.DATADISK_STORAGE_TYPE);
                String srcStorageType = dataDiskParamsDO == null ? null : dataDiskParamsDO.getValue();
                if (!StringUtils.equals(ECS_ClOUD_ESSD0,srcStorageType)){
                    logger.warn("not support disk high perf level to pl0, src[{}]->target[{}]", srcStorageType,targetStorageType);
                    throw new RdsException(ErrorCode.INVALID_ECS_DATA_DISK_CATEGORY);
                }
            }

            // 切换只读实例任务流会下发升降级的任务..现在ha加了状态,升降级判断实例状态..要放行这个状态
            // 暂时在custins中增加判断readswitch 拆分业务第二阶段再做改造
            if (!custins.isActive() && !custins.isReadSwitch() && !custins.isStopInsStatus()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isShare()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            ClustersDO clusterDO = clusterService.getClusterByClusterName(custins.getClusterName());

            boolean isUserCluster = false;

            if (clusterDO != null) {
                isUserCluster = DEDICATED_HOST_GOURP_TYPE.equals(clusterDO.getType());
            }

            //aone 13054224 将mysql手动锁定的迁移放开.其他实例暂时继续走该逻辑
            // 并且放开因主机锁定而锁定的实例
            if (custins.isLock() && !custins.isMysql() && !custins.isLockByHostLock()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(custins, mysqlParaHelper.getUID())) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_KMS_KEY);
            }

            String dbVersion = null;
            // 获取目标实例Region
            String region = mysqlParaHelper.getParameterValue("Region");
            String oldRegion = clusterService.getRegionByCluster(custins.getClusterName());


            //获取版本
            if (!isTransfer){
                dbVersion = mysqlParaHelper.getDBVersion(custins.getDbType());
            } else {
                String inputDesc = mysqlParaHelper.getParameterValue("DBInstanceStatusDesc");
            }

            // 不支持目标规格是集群版系列
            if (mysqlParaHelper.isClusterCategory(custins)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // ecs basic to docke standard
            if (mysqlParaHelper.isEcsBasicToDockerStandard(custins)){
                try{
                    return transBasic2Standard(custins);
                }catch (RdsException rx){
                    logger.error("basic2standard, dbCenterEx=", rx);
                    throw new RdsException(ErrorCode.INVALID_DBCENTER);
                }
            }

            //mysql8.0 基础版到物理机高可用版本
            if(mysqlParaHelper.is80BasicToStandardPhysical(custins)){
                try{
                    return trans80Basic2StandardPhyscial(custins);
                }
                catch(RdsException re){
                    logger.error("mysql80 basic to physcial standard error", re);
                    throw new RdsException(re.getErrorCode());
                }
            }

            if(!custins.isReadOrBackup()){
                checkRebuildingTask(custins);
            }
            //docker_on_ecs_local_ssd，大客户专享集群
            if(custins.isMysql() && isUserCluster){
                return transOrModifyCustinsForDockerOnEcsLocalSSD(custins, isTransfer);
            }

            // 获取实例使用的磁盘类型（SSD/SATA），在升降级过程中不允许改变
            if (custins.isCustinsDockerOnEcs()) {
                List<CustInstanceDO> physicalCustInstances = custinsService.getCustInstanceByParentId(custins.getId());
                CustInstanceDO physicalCustins = physicalCustInstances.stream().filter(i -> i.getIsTmp()==0).findFirst().orElse(custins);
                //ECS上暂时不支持跨可用区迁移
                if (!StringUtils.isBlank(region) && !region.equals(oldRegion)) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
                if (StringUtils.isBlank(region)){
                    mysqlParaHelper.setParameter("Region", oldRegion);
                }
                //不支持版本升降级
                if (dbVersion != null && 0 != dbVersion.compareTo(custins.getDbVersion())) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }

                String actionName = mysqlParaHelper.getParameterValue(ParamConstants.ACTION);
                if (StringUtils.equals(actionName,"ModifyDBInstanceClass")){
                    if (StringUtils.isBlank(mysqlParaHelper.getParameterValue(ParamConstants.ZONE_ID)) &&
                        StringUtils.isBlank(mysqlParaHelper.getParameterValue(ParamConstants.MULTI_AVZ_EX_PARAM))) {
                        List<InstanceDO> instances = instanceIDao.getInstanceByCustinsId(physicalCustins.getId());
                        Map<String, Object> multiAVZExParam = new HashMap<>();
                        List<Map<String, Object>> availableZoneList = new ArrayList<>();
                        for (InstanceDO i : instances) {
                            InstanceDO instance = instanceService.getInstanceByInsId(i.getId());
                            EcsHostDetailDO ecsInfo = ecsDBService.getEcsHostDetailDOByHostId(instance.getHostId());
                            if (ecsInfo != null){
                                Map<String, Object> availableZone = new HashMap<>();
                                availableZone.put(ParamConstants.ZONE_ID, ecsInfo.getZoneId());
                                if (!availableZoneList.contains(availableZone)){
                                    availableZoneList.add(availableZone);
                                }
                            }
                        }
                        if (availableZoneList.isEmpty()) {
                            logger.error("Get ecs zone info failed. Ecs instance ids: " +
                                    instances.stream().map(InstanceDO::getId).collect(Collectors.toList()) + " Zone info: " + availableZoneList);
                            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                        }
                        else if (availableZoneList.size() == 1) {
                            mysqlParaHelper.setParameter(ParamConstants.ZONE_ID,
                                availableZoneList.get(0).get(ParamConstants.ZONE_ID).toString());
                        } else {
                            multiAVZExParam.put("availableZoneInfoList", availableZoneList);
                            mysqlParaHelper.setParameter(ParamConstants.MULTI_AVZ_EX_PARAM,
                                new JSONObject(multiAVZExParam).toJSONString());
                        }
                    }
                    // docker to docker的评估
                    custins.setDbTypeForCluster(DB_TYPE_DOCKER);
                    Map<String, Object> data = dockerCommonService.evaluate(custins);
                    boolean evaluateSuccess = data.containsKey(ParamConstants.DB_INSTANCE_AVAILABLE)
                            && data.get(ParamConstants.DB_INSTANCE_AVAILABLE).toString().equals("1");
                    if (!evaluateSuccess){
                        logger.error("Evaluate resource failed, ex=" + data);
                        throw new RdsException(ErrorCode.RESOURCE_NOT_ENOUGH);
                    }
                }

                return transCustinsDockerOnECS(custins, isTransfer);
            }

            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        } catch (RdsException re) {
            logger.error("doTransfer ex="+re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("doTransfer ex="+ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
    private void checkRebuildingTask(CustInstanceDO custins) throws RdsException{
        // 本地重搭避免重复下发任务
        if (custins.isMysql()) {
            String taskKey = TaskSupport.TASK_REBUILD_INS_LOCAL;
            int count = taskService.countTaskByParentId(custins.getId(), TASK_TYPE_CUSTINS, taskKey);
            if (count > 0) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
        }
        //夸机避免重复下发
        //docker_on_ecs_local_ssd，大客户专享集群
        if(custins.isMysql() && custins.isCustinsOnDockerOnEcsLocalSSD()){
            String taskKey = TaskSupport.TASK_DOCKER_LOCAL_SSD_REBUILD_SLAVE_REMOTE;
            List<CustInstanceDO> instanceDOS = custinsIDao.getCustInstanceByParentId(custins.getId());
            if (instanceDOS==null || instanceDOS.isEmpty()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            List<Integer> list = new ArrayList<>();
            for (CustInstanceDO ins : instanceDOS) {
                list.add(ins.getId());
            }
            int count = taskService.countRemoteRebuildTask(custins.getId(), TASK_TYPE_CUSTINS, taskKey, list);
            if (count > 0) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
        }
    }

    public Map<String, Object> transCustinsDockerOnECS(CustInstanceDO custins, boolean isTransfer) throws RdsException {


        String ecsTransType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_TRANS_TYPE,
            CustinsSupport.CUSTINS_TRANS_TYPE_DEFAULT);
        //非大客户专享集群的docker_on_ecs，禁止跨机迁移，都是直接变更ecs
        if(CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE.equals(ecsTransType)){
            throw new RdsException(ErrorCode.INVALID_DBINSTANCENETTYPE);
        }
        //only readins support modify dbinstance at physical
        if (!custins.isLogic() && !custins.isRead()){
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
        }
        return trancCusinsDockerOnEcsLogic(custins, ecsTransType, isTransfer);
    }

    // 这里将原逻辑只变磁盘代码抽成函数，避免原逻辑过长
    public Map<String, Object> doOnlyModifyDiskTaskSpec(Map<String, Object> data, String sourceBiz,
                                                        String requestId, CustInstanceDO custins,
                                                        long curDiskSize, long targetDiskSize,
                                                        String srcDataDiskCategory, String targetDataDiskCategory,
                                                        InstanceLevelDO newLevel, InstanceLevelDO oldLevel,
                                                        String switchMode, String dbInstanceStatusDesc) throws Exception {

        if (mysqlParaHelper.isNotModifyDiskSize(curDiskSize, targetDiskSize) &&
                !isModifyCloudSSDToCloudEssd(srcDataDiskCategory, targetDataDiskCategory) &&
                StringUtils.equalsIgnoreCase(srcDataDiskCategory, targetDataDiskCategory)) {
            data.put("DBInstanceID", custins.getId());
            return data;
        } else if (!mysqlParaHelper.isNotModifyDiskSize(curDiskSize, targetDiskSize) &&
                isModifyCloudSSDToCloudEssd(srcDataDiskCategory, targetDataDiskCategory)){
            // 不支持磁盘大小和磁盘ssd类型同时变更
            return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
        } else if (mysqlParaHelper.isNotModifyDiskSize(curDiskSize, targetDiskSize) &&
                isModifyCloudSSDToCloudEssd(srcDataDiskCategory, targetDataDiskCategory)) {

            //对实例再强制做一次ecs规格挂载essd合法性校验
            if (!dockerCommonService.checkEcsClassCodeCanAttchEssd(custins)){
                throw new RdsException(ErrorCode.INVALID_ECS_SYSTEM_DISK_CATEGORY);
            }
            TransListDO translist = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
            translist.setdDisksize(mysqlParaHelper.convertGB2MB(targetDiskSize));
            String taskKey = TaskSupport.TASK_TRANSFER;
            String taskTypeTag = "modify_disktype_ssd_to_essd";

            Integer taskId = dockerTransService.transCustInstanceTaskOnEcsAddTag(mysqlParaHelper.getAction(),
                    mysqlParaHelper.getOperatorId(), taskKey, custins, translist, dbInstanceStatusDesc,
                    switchMode, targetDataDiskCategory, taskTypeTag);
            data.put("MigrationID", translist.getId());
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("SourceDBInstanceClass", oldLevel.getClassCode());
            data.put("TargetDBInstanceClass", newLevel.getClassCode());
            data.put("TaskId", taskId);

            autoScaleService.logForDasAutoScaleAction(sourceBiz, requestId, custins, taskId, oldLevel, newLevel, translist);

            return data;
        } else {

            TransListDO translist = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
            translist.setdDisksize(mysqlParaHelper.convertGB2MB(targetDiskSize));
            String taskKey = TaskSupport.TASK_TRANSFER;

            Integer taskId = dockerTransService.transCustInstanceTaskOnEcs(
                    mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(), taskKey, custins, translist, dbInstanceStatusDesc, switchMode, targetDataDiskCategory);
            data.put("MigrationID", translist.getId());
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("SourceDBInstanceClass", oldLevel.getClassCode());
            data.put("TargetDBInstanceClass", newLevel.getClassCode());
            data.put("TaskId", taskId);

            autoScaleService.logForDasAutoScaleAction(sourceBiz, requestId, custins, taskId, oldLevel, newLevel, translist);

            return data;
        }
    }

    /**
     * docker_on_ecs变更，任务下到逻辑实例上
     * 实际请求资源管理器时，请求实例是物理实例
     * */
    public Map<String, Object> trancCusinsDockerOnEcsLogic(CustInstanceDO custins,
                                                           String tranferType, boolean isTransfer) throws RdsException{
        try {

            String sourceBiz = mysqlParaHelper.getParameterValue(ParamConstants.SOURCE_BIZ);
            String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);

            Map<String, Object> data = new HashMap<String, Object>(6);
            String levelCode = mysqlParaHelper.getParameterValue("TargetDBInstanceClass");
            String dbVersion = custins.getDbVersion();
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            InstanceLevelDO newLevel = null;
            String dbInstanceStatusDesc = CustinsState.STATE_CLASS_CHANGING.getComment();
            if (Validator.isNull(levelCode)) {
                newLevel = oldLevel;
            } else {
                newLevel = instanceService.getInstanceLevelByClassCode(levelCode, custins.getDbType(),
                    dbVersion, custins.getTypeChar(), custins.getCharacterType());
            }
            if (newLevel == null) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
            }

            // 由于各个引擎逻辑不完全一致, 先从 ecs_disk 磁盘类型取, 再从 custins_params 里获取, 都没有就取 resource 里配置的默认值
            String srcDataDiskCategory = null;
//            CustInstanceDO primayCustins = custinsService.getPrimayCustInstanceByLogicId(custins.getId());
//            if (primayCustins != null) {
//                List<EcsDiskDO> ecsDiskList = ecsDiskService.getEcsDiskByCustinsId(primayCustins.getId(), EcsConstants.DISK_TYPE_DATA);
//                if (CollectionUtils.isNotEmpty(ecsDiskList)) {
//                    srcDataDiskCategory = ecsDiskList.get(0).getCategory();
//                }
//            }

            List<CustInstanceDO> physicalCustInstances = custinsService.getCustInstanceByParentId(custins.getId());
            CustInstanceDO physicalCustins = physicalCustInstances.stream().filter(i -> i.getIsTmp() == 0).findFirst().orElse(custins);
            List<EcsDiskDO> ecsDisks = ecsDiskService.getEcsDiskByCustinsId(physicalCustins.getId());

            if (CollectionUtils.isNotEmpty(ecsDisks)) {
                srcDataDiskCategory = ecsDisks.get(0).getCategory();
                // 老架构升级pl时，磁盘等级只更新ecs_disk_param表
                String performanLevel = ecsDiskService.getEcsTypeFromEcsDiskParamByDiskId(ecsDisks.get(0).getDiskId());
                if (!StringUtils.isEmpty(performanLevel)) {
                    srcDataDiskCategory = DockerOnEcsConstants.getEssdCategory(performanLevel);
                }
            }
            if (srcDataDiskCategory == null) {
                CustinsParamDO dataDiskParamsDO = custinsParamService.getCustinsParam(custins.getId(),
                    CustinsParamSupport.DATADISK_STORAGE_TYPE);
                if (dataDiskParamsDO != null){
                    srcDataDiskCategory = dataDiskParamsDO.getValue();
                }
            }
            if (srcDataDiskCategory == null) {
                srcDataDiskCategory = ResourceSupport.getInstance()
                    .getStringRealValue(ResourceKey.RESOURCE_ECS_DATA_DISK_CATEGORY)
                    .trim();
            }
            logger.info(requestId + "ins disk category is: " + srcDataDiskCategory);

            String targetDataDiskCategory = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            if (targetDataDiskCategory == null) {
                targetDataDiskCategory = srcDataDiskCategory;
            }
            if (STORAGE_TYPE_CLOUD_AUTO.equalsIgnoreCase(targetDataDiskCategory)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
            }
            // 允许essd类型之间的转换 只能低转高，不能高转低
            //原逻辑
            //String srcPerLevl = DockerOnEcsConstants.getEssdPerLevel(srcDataDiskCategory);
            //String targetPerLevl = DockerOnEcsConstants.getEssdPerLevel(targetDataDiskCategory);
            //Boolean isSupportChangeESSDType = targetPerLevl != null && srcPerLevl !=null && targetPerLevl.compareTo(srcPerLevl)>=0;
            //if (!srcDataDiskCategory.equals(targetDataDiskCategory) && !isSupportChangeESSDType){
            //    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
            //}
            if (newLevel.getId().equals(oldLevel.getId())) {
                boolean allowModifyDiskCategory = checkModifyDiskCategory(srcDataDiskCategory, targetDataDiskCategory);
                if (!allowModifyDiskCategory){
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
                }
            }

            // PGSQL/PPAS暂时不支持从ecs 迁移到物理机
            //if (custins.isPgsqlOrPpassql() && !newLevel.getHostType().equals(oldLevel.getHostType())) {
            //    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
            //}

            // 设置切换时间
            String switchTimeStr = mysqlParaHelper.getParameterValue(ParamConstants.SWITCH_TIME);
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(switchTimeStr);
            String switchMode = mysqlParaHelper.getAndCheckSwitchTimeMode(ParamConstants.SWITCH_TIME_MODE, utcDate, true);

            long curDiskSize = custins.getDiskSize().intValue();
            long targetDiskSize =  mysqlParaHelper.getAndCheckDiskSize(curDiskSize);
            long targetDiskSizeMB = targetDiskSize * 1024;

            // essd-->essd2 需要满足磁盘空间大于等于465GB
            // essd essd2-->essd3 需要满足磁盘空间大于等于1265GB
            if (DockerOnEcsConstants.ECS_ClOUD_ESSD2.equalsIgnoreCase(targetDataDiskCategory) &&
                targetDiskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD2_MIN_SIZE) {
                throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
            }
            if (DockerOnEcsConstants.ECS_ClOUD_ESSD3.equalsIgnoreCase(targetDataDiskCategory) &&
                targetDiskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD3_MIN_SIZE) {
                throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
            }
            if (DockerOnEcsConstants.ECS_ClOUD_ESSD0.equalsIgnoreCase(targetDataDiskCategory) &&
                    targetDiskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD0_MIN_SIZE) {
                throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
            }


            //仅仅升级磁盘
            if (newLevel.getId().equals(oldLevel.getId())) {
                return doOnlyModifyDiskTaskSpec(data, sourceBiz, requestId, custins, curDiskSize, targetDiskSize,
                        srcDataDiskCategory, targetDataDiskCategory, newLevel, oldLevel, switchMode, dbInstanceStatusDesc);
            }
            //use diskSize instend of diskSize*1024
            if (targetDiskSize == -1) {
                targetDiskSize = custins.getDiskSize().intValue();
            } else {
                targetDiskSize = targetDiskSize * 1024;

            }

            TransListDO translist =
                createTransListForTransOnEcs(custins, custins ,utcDate,
                    newLevel.getId(), (long)targetDiskSize);

            String taskKey = TaskSupport.TASK_TRANSFER;

            // category 升级
            List<CustInstanceDO> physicalCustinsList = custinsService.getCustInstanceByParentId(custins.getId());
            if (physicalCustinsList != null && !physicalCustinsList.isEmpty()) {
                mysqlParamGroupHelper.mysqlUpgradeUpdateSysParamGroupCategory(custins, newLevel.getCategory());
                mysqlParamGroupHelper.mysqlUpgradeUpdateSysParamGroupCategory(physicalCustinsList.get(0), newLevel.getCategory());
            }

            Integer taskId = dockerTransService.transCustInstanceTaskOnEcs(
                mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(), taskKey, custins, translist, dbInstanceStatusDesc, switchMode, targetDataDiskCategory);
            data.put("MigrationID", translist.getId());
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("SourceDBInstanceClass", oldLevel.getClassCode());
            data.put("TargetDBInstanceClass", newLevel.getClassCode());
            data.put("TaskId", taskId);

            autoScaleService.logForDasAutoScaleAction(sourceBiz, requestId, custins, taskId,  oldLevel, newLevel, translist);

            return data;
        } catch (RdsException re) {
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private boolean isModifyCloudSSDToCloudEssd(String srcDataDiskCategory, String targetDataDiskCategory){
        String targetPerLevl = DockerOnEcsConstants.getEssdPerLevel(targetDataDiskCategory);
        try{
            if (srcDataDiskCategory !=null && targetDataDiskCategory !=null){
                if (srcDataDiskCategory.equals(ECS_ClOUD_SSD) && targetPerLevl != null){
                    return true;
                }
            }
        }catch (Exception e){
            logger.info("checkModifyDiskCategory essd failed, use default code continue...");
        }
        return false;
    }

    private boolean checkModifyDiskCategory(String srcDataDiskCategory, String targetDataDiskCategory){
        String srcPerLevl = DockerOnEcsConstants.getEssdPerLevel(srcDataDiskCategory);
        String targetPerLevl = DockerOnEcsConstants.getEssdPerLevel(targetDataDiskCategory);

        boolean isAllowedmodifycloudDisk = isModifyCloudSSDToCloudEssd(srcDataDiskCategory, targetDataDiskCategory);
        if (isAllowedmodifycloudDisk){
            return true;
        }
        // 放开磁盘降pl
        return true;
    }

    private TransListDO createTransListForTransOnEcs(CustInstanceDO custins,
                                                     CustInstanceDO desCustins,
                                                     Date utcDate,
                                                     Integer levelId,
                                                     Long targetDiskSize) {
        TransListDO translist = null;

        // 设置目的主机实例ID
        translist = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        translist.setdCinsid(desCustins.getId());
        translist.setdLevelid(levelId);
        translist.setdDisksize(targetDiskSize);

        //utcDate可能为null，即不是按照时间点切换
        Date metadbSwitchTime = null;
        if(utcDate != null){
            String utcSwitchTimeStr = dtzSupport.transDate2UtcStr(utcDate);
            metadbSwitchTime = dtzSupport.transUtcStr2MetadbDate(utcSwitchTimeStr, DataSourceMap.DATA_SOURCE_DBAAS);
        }
        translist.setSwitchTime(metadbSwitchTime);

        Map<String, Object> translistParamMap = new HashMap<String, Object>(1);
        translistParamMap.put("destCustinsId", desCustins.getId());
        translist.setParameter(JSON.toJSONString(translistParamMap));
        return translist;
    }


    /**
     * 云盘 基础版 to 高可用版
     * 初始化 目标实例元信息
     */
    public Map<String, Object> transBasic2Standard(CustInstanceDO srcCustins)
        throws RdsException {

        Map<String,String> actionParams = ActionParamsProvider.ACTION_PARAMS_MAP.get();

        // 来源实例 必须包含 vpc 链路, 不能包含私网
        if ( !mysqlParaHelper.checkLinkforEcsBasicToDocker(srcCustins.getId())){
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_NO_VPC_LINK);
        }

        // 获得region以及旧region
        String region = avzSupport.getMainLocation(actionParams);
        AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(srcCustins);
        String oldRegion = oldAvzInfo.getMainLocation();
        if (!StringUtils.isBlank(region) && !region.equals(oldRegion)) {
            //暂不支持跨可用区
            return ResponseSupport.createErrorResponse(ErrorCode.CROSS_REGION_TRANS_NOT_ALLOWED);
        }

        List<CustinsConnAddrDO> normalConnections = connAddrCustinsService
            .getCustinsConnAddrByCustinsId(srcCustins.getId(), CustinsSupport.NET_TYPE_VPC, CustinsSupport.RW_TYPE_NORMAL);
        String vpcId = normalConnections.get(0).getVpcId();

        //1 init switchTime
        String switchTimeStr = mysqlParaHelper.getParameterValue(ParamConstants.SWITCH_TIME);
        Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(switchTimeStr);
        String switchMode = mysqlParaHelper.getAndCheckSwitchTimeMode(ParamConstants.SWITCH_TIME_MODE, utcDate, true);

        //2 init targetClass
        RequestParamsDO params = new RequestParamsDO();
        params.setAvzInfo(avzSupport.getAVZInfo(actionParams));
        params.setDbType(srcCustins.getDbType());
        String targetClassCode = mysqlParaHelper.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS);
        params.setClassCode(targetClassCode);
        params.setAccountType(srcCustins.getAccountMode().toString());
        params.setBizType(CustinsSupport.BIZ_TYPE_RDS);
        params.setType(srcCustins.getType());
        params.setContainerType("docker");
        params.setAction(mysqlParaHelper.getAction());
        params.setOperatorId(mysqlParaHelper.getOperatorId());

        //3 init region info
        String subDomain = mysqlParaHelper.getParameterValue("Region");
        String regionId = mysqlParaHelper.getParameterValue("RegionId");
        params.setRegionId(regionId);// 地域, cn-hangzhou: ecs_image
        params.setRegion(subDomain);//subdomain, location, 数据中心, 子域: resmanagerModel

        //4 init dbversion
        params.setEngine(srcCustins.getDbType());
        params.setDbType(srcCustins.getDbType());
        params.setEngineVersion(srcCustins.getDbVersion());
        params.setDbVersion(srcCustins.getDbVersion());
        params.setUserId(srcCustins.getUserId());
        params.setMaintainStartTime(srcCustins.getMaintainStarttime());
        params.setMaintainEndTime(srcCustins.getMaintainEndtime());

        //5 init insName, storage
        params.setDbBInstanceName("tmp" + System.currentTimeMillis() + "_" + srcCustins.getInsName());
        String newStorage = mysqlParaHelper.getParameterValue(ParamConstants.STORAGE);
        Integer ecsStorageGB = null;
        if(StringUtils.isBlank(newStorage)){
            Long srcDiskSize = srcCustins.getDiskSize()/1024;
            ecsStorageGB = srcDiskSize.intValue();
        }else{
            Integer maxEcsDiskSize = ResourceSupport.getInstance()
                .getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_ON_ECS_MAX_DISK_SIZE);
            ecsStorageGB = CheckUtils.parseInt(newStorage, 20, maxEcsDiskSize, ErrorCode.INVALID_STORAGE);
        }
        mysqlParaHelper.checkDiskReduction(srcCustins.getDiskSize(), ecsStorageGB * 1024L);

        params.setStorage(ecsStorageGB.toString());
        params.setOptmization(srcCustins.getIsAccept().toString());
        params.setHostType(mysqlParaHelper.getTargetHostType(targetClassCode, srcCustins.getDbType(), srcCustins.getDbVersion()));

        //6 init conn link
        params.setUserVpcId(vpcId);//vip, ecs资源描述
        params.setNetType(CustinsSupport.NET_TYPE_PRIVATE);
        params.setConnType("lvs");
        params.setConnectionString("");
        params.setConnAddrCust("");
        params.setPortStr(CustinsSupport.getConnPort(mysqlParaHelper.getParameterValue("port"), params.getDbType()));
        params.setProxyGroupId(0);

        //7 init bak info
        Map<String, Object> taskQueueParam = new HashMap<String, Object>(1);
        Map<String, Object> baklistParam = new HashMap<String, Object>(3);
        baklistParam.put("retention", 7);
        baklistParam.put("bak_period", "0000001");
        baklistParam.put("bak_begin", "02:00Z");
        taskQueueParam.put("backup", baklistParam);
        params.setTaskQueueParam(taskQueueParam);

        Map<String, List<MycnfCustinstanceDO>> mycnfCustinstancesMap = mysqlParaHelper.getAndCheckExternalParameter(srcCustins);
        params.setMycnfCustinstancesMap(mycnfCustinstancesMap);
        params.setClone(true);

        //8 init multiavz, create custins
        String multiAVZExParamStr = mysqlParaHelper.getParameterValue(ParamConstants.MULTI_AVZ_EX_PARAM);
        CheckUtils.checkStrNotEmpty(multiAVZExParamStr, ParamConstants.MULTI_AVZ_EX_PARAM + " is empty");
        params.setMultiAVZExParam(JSON.parseObject(multiAVZExParamStr, MultiAVZExParamDO.class));
        params.setE2dsourceCustinsId(srcCustins.getId());
        params.setAccountMode(srcCustins.getAccountMode());

        DockerTaskInputParam dockerTaskInputParam = dockerManager.createDockerDbInstance(params);
        dockerTaskInputParam.setSrcCusIns(srcCustins);

        // 9 sync common attrs
        dockerCustinsService.syncBasic2StandardCustinsMeta(
            dockerTaskInputParam.getAction(), dockerTaskInputParam.getOperatorId(),
            dockerTaskInputParam.getCustIns(), dockerTaskInputParam.getSrcCusIns(),
            dockerTaskInputParam.getCharacterCustinsList(),
            dockerTaskInputParam.getCustinsIpWhiteList(), dockerTaskInputParam.getMycnfCustinstancesMap(),
            dockerTaskInputParam.getHostType());
        CustInstanceDO tmpCustins = dockerTaskInputParam.getCustIns();

        // 10 init trans list update custins status
        Long newDiskSizeMB = ecsStorageGB * 1024L;
        TransListDO transList = new TransListDO(srcCustins, tmpCustins,
            TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        List<CustInstanceDO> subCustins = custinsService.getCustInstanceByParentId(tmpCustins.getId());
        transList.setdCinsid(subCustins.get(0).getId());
        transList = mysqlParaHelper.buildMysql57Ecs2DockerTransListParam(transList, srcCustins, tmpCustins,
            targetClassCode, newDiskSizeMB, utcDate);
        instanceService.createTransList(transList);
        custinsService.updateCustInstanceStatusByCustinsId(srcCustins.getId(), CUSTINS_STATUS_TRANS,
            CustinsState.STATE_CLASS_CHANGING.getComment());

        // 11 init params
        String taskparam = taskService.getTransTaskParameterTimeZoneSafe(transList.getId(), switchMode, utcDate);
        Map<String, Object> paramMap = JSON.parseObject(taskparam);
        paramMap.put(CustinsSupport.DISK_REDUCTION, false);
        String installParams = JSON.toJSONString(dockerTaskInputParam.getTaskQueueParam());
        paramMap.put(CustinsSupport.INSTALL_PARAMS, installParams);
        taskparam = JSON.toJSONString(paramMap);

        // 12 init task_queue;
        TaskQueueDO taskQueue = new TaskQueueDO(mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(), srcCustins.getId(),
            TASK_TYPE_CUSTINS, TaskSupport.TASK_TRANSFER, taskparam);
        taskService.createTaskQueue(taskQueue);
        instanceIDao.updateTransTaskIdById(transList.getId(), taskQueue.getId());

        // 13 init ecs_to_docker custins_param
        List<CustinsParamDO> custinsParams = new ArrayList<CustinsParamDO>();
        custinsParams.add(new CustinsParamDO(tmpCustins.getId(),
            CustinsParamSupport.CUSTINS_PARAM_NAME_ECS2DOCKER, "1"));
        custinsParams.add(new CustinsParamDO(subCustins.get(0).getId(),
            CustinsParamSupport.CUSTINS_PARAM_NAME_ECS2DOCKER, "1"));
        custinsParamService.createCustinsParams(custinsParams);

        InstanceLevelDO srcLevel  = instanceService.getInstanceLevelByLevelId(srcCustins.getLevelId());

        Map<String, Object> data = new HashMap<String, Object>(9);
        data.put("MigrationID", transList.getId());
        data.put("DBInstanceID", srcCustins.getId());
        data.put("DBInstanceName", srcCustins.getInsName());
        data.put("SourceDBInstanceClass", srcLevel.getClassCode());
        data.put("TargetDBInstanceClass", targetClassCode);
        data.put("SwitchTimeMode", switchMode);
        data.put("TargetLogicCustinsId", tmpCustins.getId());
        data.put("TaskId", taskQueue.getId());
        data.put("EcsToDocker", 1);
        return data;
    }




    /**
     * docker on ecs local ssd
     * 此处的custins是逻辑实例
     * （1）传递的规格信息，是逻辑实例的规格，需要对应转为物理规格
     * （2）分配资源操作时，操作物理实例，传递的规格信息是物理规格
     * （3）逻辑实例与物理实例都生成trans_list
     * （3）任务下在逻辑实例上
     * */
    public Map<String, Object> transOrModifyCustinsForDockerOnEcsLocalSSD(CustInstanceDO custInstanDO,
                                                                          boolean isTransfer) throws RdsException {


        //请求的所有参数
        Map<String, String> params = mysqlParaHelper.getParameters();
        CustInstanceDO custins = mysqlParaHelper.getAndCheckCustInstance();
        // 实例停机则需激活
        if(!custins.isActive() && !custins.isStopInsStatus()){
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        if(custins.isShare()){
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
        boolean isDhgCustinsActive = custins.isStopInsStatus();
        //当前传入参数是逻辑实例
        CustInstanceDO physicalCustins;
        CustInstanceDO logicCustins;
        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        if("general".equalsIgnoreCase(instanceLevel.getCategory())){
            physicalCustins = custins;
            if(!physicalCustins.isActive()){
                throw new RdsException(MysqlErrorCode.GENERAL_INS_NOT_READY.toArray());
            }
            logicCustins = custinsService.getCustInstanceByCustinsId(custins.getParentId());
        } else if (custins.isReadOrBackup()) {
            physicalCustins = custins;
//            获取只读实例的主实例
            CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(physicalCustins.getPrimaryCustinsId());
            if (!primaryCustins.isActive()) {
                throw new RdsException(ErrorCode.INVALID_PRIMARY_CUSTINS_STATUS);
            }
//            根据主实例获取逻辑实例
            logicCustins = custinsService.getCustInstanceByCustinsId(primaryCustins.getParentId());
        } else {
            //当前传入参数是逻辑实例
            List<CustInstanceDO> physicalCustinsList = custinsService.getCustInstanceByParentId(custins.getId());
            if(physicalCustinsList == null || physicalCustinsList.isEmpty()){
                throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
            }
            physicalCustins = physicalCustinsList.get(0);
            logicCustins = custins;
        }

        //实际与资源管理器交互过程中，使用物理实例

        //对应clusters的cluster_name字段
        String clusterName = mysqlParaHelper.getParameterValue(ParamConstants.CLUSTER_NAME);
        ClustersDO clustersDO = mysqlParaHelper.getAndCheckCluster(clusterName);
        //DHG默认不会跨Region迁移
        String region = mysqlParaHelper.getParameterValue(ParamConstants.REGION);
        // 大客户专享集群判断 主是否跨可用区
        ParamConstants.DispenseMode dispenseMode = getDispenseMode(params);
        Boolean isAcrossZone = false;
        String oldZoneId;
        String zoneId = mysqlParaHelper.getParameterValue(ParamConstants.ZONE_ID);
        Map<Integer,Integer> roleHostIdMappingNoCrossZone = new HashMap<>();
        MultiAVZExParamDO multiAVZExParamDO = avzSupport.getMultiAVZExParam(params);

        List<InstanceDO> instanceDOList = instanceService.getInstanceByCustinsId(physicalCustins.getId());
        for (InstanceDO i :instanceDOList) {
            AvailableZoneInfoDO availableZoneInfoDO = null;
            try {
                if (!i.getRole().equals(CUSTINS_ROLE_MASTER)) {
                    availableZoneInfoDO = multiAVZExParamDO.getSlaveAvailableZoneInfo().isEmpty() ? null : multiAVZExParamDO.getSlaveAvailableZoneInfo().get(0);
                } else {
                    availableZoneInfoDO = multiAVZExParamDO.getMasterAvailableZoneInfo();
                    if (availableZoneInfoDO != null && zoneId == null) {
                        zoneId = availableZoneInfoDO.getZoneID();
                    }
                }
            } catch (Exception ignored) {
            }
            if(availableZoneInfoDO == null){
                roleHostIdMappingNoCrossZone.put(i.getRole(), i.getHostId());
                continue;
            }
            roleHostIdMappingNoCrossZone.put(i.getRole(),ecsDBService.getEcsHostDetailDOByHostId(i.getHostId()).getZoneId().equals(availableZoneInfoDO.getZoneID())?i.getHostId():null);
        }
        if (zoneId == null) {
            throw new RdsException(ErrorCode.INVALID_AVZONE);
        }
        // 只读实例 或者有传dispensemode 参数的 请求 进行判断是否跨zoneId
        if (dispenseMode.equals(ParamConstants.DispenseMode.MultiAVZDispenseMode) || custins.isReadOrBackup()) {
            isAcrossZone = this.dhgIsArocessZoneId(custins, physicalCustins.getId(), zoneId) && StringUtils.isNotEmpty(mysqlParaHelper.getParameterValue(ParamConstants.IP_ADDRESS));
        }
        //默认不跨集群迁移，都是在专享集群中
        String specifyCluster = clustersDO.getClusterName();
        if(specifyCluster != null && !clustersDO.getClusterName().equalsIgnoreCase(specifyCluster)){
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }

        String specifyTransType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_TRANS_TYPE, CustinsSupport.CUSTINS_TRANS_TYPE_DEFAULT);
        //指定迁移主机
        Set<Integer> specifyHostIdSet = new HashSet<>();
        String dedicatedHostNamesJson = CustinsParamSupport.getParameterValue(params, "DedicatedHostNames");
        String targetDedicatedHostIdForMaster = null;
        if (!StringUtils.isEmpty(dedicatedHostNamesJson)) {
            // 如果传DedicatedHostNames，使用DedicatedHostNames对应的hostids
            Map<String, Object> dedicatedHostNames = JSON.parseObject(dedicatedHostNamesJson);
            targetDedicatedHostIdForMaster = (String) dedicatedHostNames.get("TargetDedicatedHostIdForMaster");
            specifyHostIdSet = getAndCheckHostIdsByDedicatedHostNames(dedicatedHostNames, clusterName, roleHostIdMappingNoCrossZone);
        } else {
            specifyHostIdSet = CustinsParamSupport.getAndCheckHostIdSet(params);
        }

        Integer masterHostId = null;
        if (targetDedicatedHostIdForMaster != null) {
            masterHostId = new ArrayList<>(specifyHostIdSet).get(0);
            //todo 暂时去掉，后续common-lib 更新修复
            //avzSupport.checkMultiAvzMasterSameMultiParamZone(params, masterHostId);
        }

        // 没有指定主， 并且指定host 只指定了一个，此时需要报错
        if (masterHostId == null && specifyHostIdSet.size() == 1) {
            logger.error("HostId input must be two or must be specify master host id..");
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }


        //新逻辑实例规格码
        String levelCode = null;
        //存储，变配时可能需要
        String storage = null;

        //获取版本，升级情况下，不是迁移
        if (!isTransfer) {
            //迁移不变规格和存储，只有变配使用，此处传递的是逻辑实例的规格
            levelCode = mysqlParaHelper.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS);
            storage = mysqlParaHelper.getParameterValue(ParamConstants.STORAGE);
        }

        //原规格，逻辑实例规格
        InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        //逻辑实例目标规格
        InstanceLevelDO newLevel = null;

        //判断是否是xdb企业版
        boolean isXdb = mysqlParaHelper.checkIsXdbEnterprise(custins);
        //规格category=enterprise
        boolean isMysqlEnterpriseCustins = mysqlDBCustinsService.isMysqlEnterprise(custins);
        boolean mysqlCategorySwitch = false;

        String dbVersion = custinsService.getDBVersion(custins.getDbType(), mysqlParaHelper.getParameterValue(ENGINE_VERSION));
        if (dbVersion == null) {
            dbVersion = custins.getDbVersion();
        }
        if (levelCode != null) {
            //获取目标规格
            newLevel = instanceService.getInstanceLevelByClassCode(levelCode, custins.getDbType(), dbVersion, custins.getTypeChar(), null);
            //目标规格不存在
            if(newLevel == null){
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            if(!isXdb){
                //56企业版变非企业版
                mysqlCategorySwitch = newLevel.isMysqlEnterprise() ^ isMysqlEnterpriseCustins;
            }
            //不支持xdb企业版变为非企业版
            if(isXdb && !InstanceSupport.CATEGORY_ENTERPRISE.equalsIgnoreCase(newLevel.getCategory())){
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
        }
        else{
            newLevel = oldLevel;
        }

        boolean isVersionTrans = false;//是否跨版本
        if (!dbVersion.equals(custins.getDbVersion())) {
            Integer versionTrans = dbVersion.compareTo(custins.getDbVersion());
            if (versionTrans != 0) {
                isVersionTrans = true;
            }
            if (versionTrans < 0) {//只能升版本
                return createErrorResponse(ErrorCode.INVALID_ENGINEVERSION);
            }
        }

        if (isVersionTrans) {
            //企业版都不支持大版本升级
            if(isMysqlEnterpriseCustins){
                //企业版实例不支持版本升级
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            if("5.7".equals(dbVersion)){
                //不支持其他mysql版本升级到5.7
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
        }

        //没有传level_code，则规格不变
        if (Validator.isNull(levelCode)) {
            levelCode = oldLevel.getClassCode();
            if (isVersionTrans) {
                //如果是大版本升级，则查询到对应大版本的规格
                newLevel = instanceService.getInstanceLevelByClassCode(levelCode, custins.getDbType(), dbVersion,
                        custins.getTypeChar(), null);
            } else {
                //构造trans_list需要
                newLevel = oldLevel;
            }
        }

        if (newLevel == null) {
            return createErrorResponse(ErrorCode.INVALID_DBINSTANCECLASS);
        }

        if (oldLevel.isBasicLevel() && newLevel.isStandardLevel()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_TYPE);
        }

        //原物理实例规格
        InstanceLevelDO physicalOldLevel = instanceService.getInstanceLevelByLevelId(physicalCustins.getLevelId());
        //物理实例目标规格
        InstanceLevelDO physicalNewLevel;
        if ("general".equalsIgnoreCase(instanceLevel.getCategory())) {
            physicalNewLevel = newLevel;
        } else if (custins.isReadOrBackup()) {
            physicalNewLevel = newLevel;
        } else {
            physicalNewLevel = mysqlParaHelper.getPhyscialNewLevel(newLevel.getClassCode(), physicalCustins);
        }

        //校验UTC时间，返回带时区的日期
        Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(params);
        //立即切换，指定时间切换，运维时间切换
        String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(params, utcDate, true);

        if(specifyTransType.equals(CUSTINS_TRANS_TYPE_LOCAL)) {
            //本机升降级涉及套餐调整的需要检查slave是否宕机，空间不需要; 对与只有单个主机节点的实例不需要（如只读实例）
            if (!newLevel.getId().equals(oldLevel.getId()) && !custins.isReadOrBackup()) {
                if (!instanceService.checkSlaveInstanceHealth(custins.getId())) {
                    specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
                }
            }
        }
        if(!specifyTransType.equals(CUSTINS_TRANS_TYPE_REMOTE)) {
            // 企业版变非企业版只能跨机迁移
            if (mysqlCategorySwitch || isVersionTrans) {
                specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;
            }
        }
        if(isAcrossZone&&specifyTransType.equals(CUSTINS_TRANS_TYPE_LOCAL)){
            throw new RdsException(ErrorCode.UNSUPPORTED_TRANS_TYPE);
        }

        // 大客户专享实例，云盘 不需要设置磁盘大小...
        Long diskSize = custinsService.getAndCheckDiskSize(physicalCustins, storage, mysqlParaHelper.getAndCheckBizType());

        if (custins.isCustinsDockerOnEcs() && diskSize < custins.getDiskSize()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
        }

        // 主机锁定的实例 和 激活实例，需要如下判断
        if ((isDhgCustinsActive || custins.isLockByHostLock()) &&
                CustinsSupport.CUSTINS_TRANS_TYPE_LOCAL.equals(specifyTransType)) {
            // 不允许 激活实例 走本地重搭
            throw new RdsException(ErrorCode.UNSUPPORTED_TRANS_TYPE);
        }

        // 主机锁定的实例，则需要如下判断
        if (custins.isLockByHostLock() && !specifyHostIdSet.isEmpty()) {
            // 检测 传入的主机ID 所在的主机状态是否正常
            for (Integer hostId: specifyHostIdSet) {
                HostInfo hostInfoDO = hostService.getHostInfo(hostId, null, null);
                if (!HOST_STATUS_ONLINE.equals(hostInfoDO.getStatus())) {
                    throw new RdsException(ErrorCode.INVALID_TARGET_TRANSFER_HOSTIDS);
                }
            }
        }

        // 如果是大客户专享集群 则需要拒绝一系列如下操作
        if (isDhgCustinsActive) {
            if("general".equalsIgnoreCase(instanceLevel.getCategory())){
                if(!physicalCustins.isActive()){
                    throw new RdsException(MysqlErrorCode.GENERAL_INS_NOT_READY.toArray());
                }
            }else if (custins.isReadOrBackup()) {
                // 只读实例 检测主实例是否是激活状态
                CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                if (!primaryCustins.isActive() || primaryCustins.getLockMode()!=0) {
                    throw new RdsException(ErrorCode.INVALID_PRIMARY_CUSTINS_STATUS);
                }
            }
            // 激活状态 不允许 变配磁盘
            if (!diskSize.equals(physicalCustins.getDiskSize())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
            }
            // 有指定的主机ID 传入资源管理器 会走半跨机 需要禁止
            if (!specifyHostIdSet.isEmpty()) {
                throw new RdsException(ErrorCode.INVALID_TARGET_TRANSFER_HOSTIDS);
            }
            // 激活实例 必须全跨机
            specifyTransType = CustinsSupport.CUSTINS_TRANS_TYPE_REMOTE;

        }



        //主可用区添加
        Boolean isSameAvz = false;
        AVZInfo avzInfo = avzSupport.getAVZInfo(params);
        AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
        if (avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.ClassicDispenseMode) &&
            oldAvzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
            avzInfo = oldAvzInfo;
            isSameAvz = true;
        }
        if(!avzInfo.isValidForModify()){
            avzInfo = oldAvzInfo;
            isSameAvz = true;
        }

        if(avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.ClassicDispenseMode)
            && avzInfo.getRegion()==null && clusterName==null){
            throw new RdsException(INVALID_REGION);
        }
        //资源相关操作，操作的是物理实例的物理规格，物理实例集群与逻辑实例一致
        UpgradeResContainer container = avzSupport.getUpgradeResContainer(avzInfo, masterHostId);
        // 指定了master的主机Id 并且
        if (container.getZoneInfoList().size() == 0 && masterHostId != null) {
            container.setZoneInfoList(this.assembleDhgZoneInfoList(zoneId, masterHostId));
        }
        container.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));
        container.setClusterName(specifyCluster);
        container.setPreferClusterName(logicCustins.getClusterName());
        container.setAccessId(CustinsParamSupport.getParameterValue(params, ParamConstants.ACCESSID));
        container.setOrderId(CustinsParamSupport.getParameterValue(params, ParamConstants.ORDERID));

        // init custins res model
        UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
        custinsResModel.setCustinsId(physicalCustins.getId());


        List<CustinsServiceDO> maxscaleService = custinsService.getCustinsServicesByCustinsIdAndServiceRole(physicalCustins.getId(), CustinsSupport.DB_TYPE_MAXSCALE);
        if (maxscaleService != null && maxscaleService.size() > 0){
            custinsResModel.setNeedProxyGroup(false);
        }
        // init host ins res model
        String resourceStrategy = mysqlParaHelper.getResourceStrategy();
        //传递的是物理实例的目标规格
        HostinsResModel hostinsResModel = new HostinsResModel(physicalNewLevel.getId());
        if ("general".equalsIgnoreCase(instanceLevel.getCategory())) {
            if (masterHostId != null) {
                specifyTransType = "2";
                container.setTransType(2);
            }
        }
        hostinsResModel.setTransType(Integer.valueOf(specifyTransType));

        // 后面其他逻辑 用到instance 在大客户专享集群中 不会用到.. dns 链路 和 企业版切换
        InstanceDO instance = null;
        if (isDhgCustinsActive) {
            Integer insLevelId = null;
            if("general".equalsIgnoreCase(instanceLevel.getCategory())){
                insLevelId = instanceLevel.getId();
            } else if (custins.isReadOrBackup()) {
                insLevelId = custins.getLevelId();
            } else {
                List<InstanceLevelRelDO> instanceLevelRels =
                        instanceService.getInstanceLevelRelByParentLevelId(custins.getLevelId(), null);
                insLevelId = instanceLevelRels.get(0).getCharacterLevelId();
            }
            InstanceLevelDO characterInsLevel = instanceService.getInstanceLevelByLevelId(insLevelId);
            DockerInsLevelParseConfig config = custinsService.parseDockerInsExtraInfo(characterInsLevel.getExtraInfo());
            hostinsResModel.setInsCount(config.getInsCount());
            hostinsResModel.setInsPortCount(config.getPortCountPerIns());
            hostinsResModel.setHostType(characterInsLevel.getHostType());
        } else {
            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(physicalCustins.getId());
            instance = instanceList.get(0);
            hostinsResModel.setHostType(instance.getHostType());
            hostinsResModel.setInsCount(instanceList.size());
            if (CUSTINS_TYPE_SHARE.equals(instance.getType())) {
                hostinsResModel.setType(instance.getType());
            }
        }



        if (resourceStrategy != null) {
            hostinsResModel.setStrategy(resourceStrategy);
        }
        // 大客户专享集群 云盘实例 不需要disk
        if (logicCustins.isCustinsDockerOnEcs()) {
            hostinsResModel.setDiskType(DISK_TYPE_NO_NEED);
        } else {
            // 本地盘 disktype 需要设置 disk_type_all
            hostinsResModel.setDiskType(DISK_TYPE_ALL);
        }
        if (mysqlCategorySwitch) {
            //企业版和主从版发生切换
            Integer nodeCount = newLevel.getInsCount();
            if (!isMysqlEnterpriseCustins) {
                //变成 多节点
                nodeCount = nodeCount == null ? 3 : nodeCount;
                hostinsResModel.setInsCount(nodeCount);
            } else {
                //变成 主从版, 兼容环境规格问题
                hostinsResModel.setInsCount(2);
            }
        }

        //xdb升级变配，指定主机节点数量为3
        if(isXdb){
            hostinsResModel.setInsCount(3);
        }

        //网络连接使用的是物理实例
        List<VipResModel> vipResModelList = new ArrayList<>();
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(
            physicalCustins.getId(),
            null,
            CustinsSupport.RW_TYPE_NORMAL);

        List<CustinsConnAddrDO> rwSplitConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(
            physicalCustins.getId(),
            null,
            RW_TYPE_RW_SPLIT);

        if (isAcrossZone) {
            // vpc实例需要传入vpc信息才能跨可用区
            CustinsConnAddrDO vpcConnAddr = ConnAddrSupport.getVPCConnAddr(custinsConnAddrList);
            if (vpcConnAddr != null) {
                String vpcId = CheckUtils.checkValidForVPCId(mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID));
                String tunnelId = CheckUtils.checkValidForTunnelId(mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID));
                String vswitchId = CheckUtils.checkValidForVswitchId(
                    mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID));
                String ipaddress = CheckUtils.checkValidForIPAddress(
                    mysqlParaHelper.getParameterValue(ParamConstants.IP_ADDRESS));
                String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(
                    mysqlParaHelper.getParameterValue(ParamConstants.VPC_INSTANCE_ID));

                VipResModel vipResModel = new VipResModel(vpcConnAddr.getNetType());
                vipResModel.setUserVisible(vpcConnAddr.getUserVisible());
                vipResModel.setConnAddrCust(mysqlParaHelper.getConnAddrCust(
                    "tmp" + System.currentTimeMillis() + "-" + physicalCustins.getInsName().replace('_', '-'),
                    mysqlParaHelper.getRegionIdByClusterName(physicalCustins.getClusterName()),
                    physicalCustins.getDbType()));
                vipResModel.setVip(ipaddress);
                vipResModel.setVport(Integer.valueOf(vpcConnAddr.getVport()));
                vipResModel.setVpcId(vpcId);
                vipResModel.setTunnelId(CustinsValidator.getRealNumber(tunnelId, -1));
                vipResModel.setVswitchId(vswitchId);
                vipResModel.setVpcInstanceId(vpcInstanceId);
                vipResModelList.add(vipResModel);
            }
            CustinsConnAddrDO rwSplitVpcConnAddr = ConnAddrSupport.getVPCConnAddr(rwSplitConnAddrList);
            if (rwSplitVpcConnAddr != null) {
                String vpcId = CheckUtils.checkValidForVPCId(mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID));
                String tunnelId = CheckUtils.checkValidForTunnelId(mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID));
                String vswitchId = CheckUtils.checkValidForVswitchId(
                    mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID));
                String ipaddress = CheckUtils.checkValidForIPAddress(
                    mysqlParaHelper.getParameterValue(ParamConstants.RW_SPLIT_IP_ADDRESS));
                String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(
                    mysqlParaHelper.getParameterValue(ParamConstants.RW_SPLIT_VPC_INSTANCE_ID));

                VipResModel vipResModel = new VipResModel(rwSplitVpcConnAddr.getNetType());
                vipResModel.setUserVisible(rwSplitVpcConnAddr.getUserVisible());
                vipResModel.setConnAddrCust(mysqlParaHelper.getConnAddrCust(
                    "tmprw" + System.currentTimeMillis() + "-" + physicalCustins.getInsName().replace('_', '-'),
                    mysqlParaHelper.getRegionIdByClusterName(physicalCustins.getClusterName()),
                    physicalCustins.getDbType()));
                vipResModel.setVip(ipaddress);
                vipResModel.setRwType(RW_TYPE_RW_SPLIT);
                vipResModel.setVport(Integer.valueOf(rwSplitVpcConnAddr.getVport()));
                vipResModel.setVpcId(vpcId);
                vipResModel.setTunnelId(CustinsValidator.getRealNumber(tunnelId, -1));
                vipResModel.setVswitchId(vswitchId);
                vipResModel.setVpcInstanceId(vpcInstanceId);
                vipResModelList.add(vipResModel);
            }
            for (CustinsConnAddrDO custinsConnAddrDO: custinsConnAddrList) {
                if (!CustinsSupport.NET_TYPE_VPC.equals(custinsConnAddrDO.getNetType())) {
                    VipResModel vipResModel = new VipResModel(custinsConnAddrDO.getNetType());
                    String ipVersion = custinsService.getIpVersionByVipAndVpcId(custinsConnAddrDO.getVip(),
                            custinsConnAddrDO.getVpcId());
                    vipResModel.setIpVersion(ipVersion);
                    vipResModel.setVport(Integer.valueOf(custinsConnAddrDO.getVport()));
                    vipResModel.setRwType(custinsConnAddrDO.getrwType());
                    vipResModel.setConnAddrCust(mysqlParaHelper.getConnAddrCust(
                            "tmp" + System.currentTimeMillis() + "-" + physicalCustins.getInsName().replace('_', '-'),
                            mysqlParaHelper.getRegionIdByClusterName(physicalCustins.getClusterName()),
                            physicalCustins.getDbType()));
                    vipResModel.setUserVisible(custinsConnAddrDO.getUserVisible());
                    vipResModelList.add(vipResModel);

                }
            }
        }

        if (mysqlCategorySwitch && !isMysqlEnterpriseCustins && CustinsSupport.isDns(physicalCustins.getConnType())) {
            for (int i = 0; i < hostinsResModel.getInsCount(); ++i) {
                String connAddrNode = mysqlParaHelper.getConnAddrCust(physicalCustins.getInsName() + "-" + (i + 1),
                        mysqlParaHelper.getRegionIdByClusterName(physicalCustins.getClusterName()), physicalCustins.getDbType());
                CustinsConnAddrDO custinsConnAddr = ConnAddrSupport
                    .createCustinsConnAddr(connAddrNode, String.valueOf(instance.getPort()),
                        physicalCustins.getNetType(), -1, null, null, null, null);
                custinsConnAddr.setInsId(-1);
                VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
                vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
                vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
                vipResModel.setVip(custinsConnAddr.getVip());
                vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
                vipResModel.setVpcId(custinsConnAddr.getVpcId());
                vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
                vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
                vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
                vipResModel.setInstanceId(custinsConnAddr.getInsId());
                vipResModelList.add(vipResModel);
            }
        }

        hostinsResModel.setDiskSizeSold(diskSize);
        // get disk size used
        Long diskUsage = instanceService.getInstanceDiskUsage(physicalCustins, 0);
        hostinsResModel.setDiskSizeUsed(diskUsage);
        // init distribute rule
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        if (hostinsResModel.getInsCount() > 3) {
            //四节点
            distributeRule.setSiteDistributeMode(DistributeMode.AVG_SCATTER);
        } else {
            // 大客户专享集群，机房分配策略 尽量打散
            // 强制打散会存在，某个机房中不存在所需机器 就会分配不出来，强制集中 会存在 分配在同一台主机上 机房平均分配 同理 会导致分配出来
            // todo 后期通过集群参数 来确定 分配策略
            if("general".equalsIgnoreCase(instanceLevel.getCategory())){
                String siteName = clusterService.getSiteNameByAvzone(zoneId);
                distributeRule.setSiteDistributeMode(DistributeMode.FORCE_CROWD);
                if (siteName!=null){
                    distributeRule.setSpecifySiteNameSet(new HashSet<String>(){{add(siteName);}});
                }

            } else if (custins.isReadOrBackup()) {
                String siteName = clusterService.getSiteNameByAvzone(zoneId);
                distributeRule.setSiteDistributeMode(DistributeMode.FORCE_CROWD);
                distributeRule.setSpecifySiteNameSet(new HashSet<String>(){{add(siteName);}});
            } else {
                distributeRule.setSiteDistributeMode(DistributeMode.TRY_SCATTER);
            }
        }
        distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
        distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
        if (specifyHostIdSet.size() > 1 ) {
            distributeRule.setSpecifyHostIdSet(specifyHostIdSet);
        }

        if (isDhgCustinsActive) {
            List<EcsDiskDO> ecsDiskDOS = ecsDiskService.getEcsDiskByCustinsId(physicalCustins.getId());
            HashSet<String> zoneIdSets = new HashSet<>();
            for (EcsDiskDO ecsDiskDO: ecsDiskDOS) {
                zoneIdSets.add(ecsDiskDO.getZoneId());
            }
            if (zoneIdSets.size() > 1) {
                distributeRule.setSiteDistributeMode(DistributeMode.AVG_SCATTER);
            }
            distributeRule.setSpecifyZoneIdNameSet(zoneIdSets);
        }

        InsLevelExtraInfo.updateDistributeRule(distributeRule, newLevel.getExtraInfo());
        hostinsResModel.setDistributeRule(distributeRule);
        //docker需要两个端口，access_port和perf_port
        hostinsResModel.setInsPortCount(2);

        Set<Integer> inferiorHostIdSets = new HashSet<>();
        List<InstanceDO> inferiorInstances = new ArrayList<>();

        if("general".equalsIgnoreCase(instanceLevel.getCategory())){
            inferiorInstances.addAll(instanceService.getInstanceByCustinsId(mySQLGeneralService.getMasterIns(physicalCustins.getParentId()).getId()));
            List<CustInstanceDO> readCustinsList = mySQLGeneralService.getFollowerInsList(physicalCustins.getParentId());
//            List<Integer> readInsTmpId = custinsService.getReadCustInstanceTmpInsIdByPrimaryCustinsId(physicalCustins.getPrimaryCustinsId());
//            if (readInsTmpId.size() != 0){
//                for (Integer custinsId: readInsTmpId) {
//                    inferiorInstances.addAll(instanceService.getInstanceByCustinsId(custinsId));
//                }
//            }
            if (readCustinsList != null && readCustinsList.size() > 0) {
                for (CustInstanceDO oldReadIns : readCustinsList) {
                    if (!oldReadIns.getId().equals(physicalCustins.getId())){
                        inferiorInstances.addAll(instanceService.getInstanceByCustinsId(oldReadIns.getId()));
                    }
                }
            }
        } else if (physicalCustins.isReadOrBackup()) {
            inferiorInstances.addAll(instanceService.getInstanceByCustinsId(physicalCustins.getPrimaryCustinsId()));
            List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(physicalCustins.getPrimaryCustinsId(), true);
            List<Integer> readInsTmpId = custinsService.getReadCustInstanceTmpInsIdByPrimaryCustinsId(physicalCustins.getPrimaryCustinsId());
            if (readInsTmpId.size() != 0){
                for (Integer custinsId: readInsTmpId) {
                    inferiorInstances.addAll(instanceService.getInstanceByCustinsId(custinsId));
                }
            }
            if (readCustinsList != null && readCustinsList.size() > 0) {
                for (CustInstanceDO oldReadIns : readCustinsList) {
                    if (!oldReadIns.getId().equals(physicalCustins.getId())){
                        inferiorInstances.addAll(instanceService.getInstanceByCustinsId(oldReadIns.getId()));
                    }
                }
            }
        } else {
            List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(physicalCustins.getId(), true);
            if (readCustinsList != null && readCustinsList.size() > 0) {
                for (CustInstanceDO oldReadIns : readCustinsList) {
                    inferiorInstances.addAll(instanceService.getInstanceByCustinsId(oldReadIns.getId()));
                }
            }
        }

        for (InstanceDO inferiorInstance : inferiorInstances) {
            inferiorHostIdSets.add(inferiorInstance.getId());
        }
        distributeRule.setInferiorHostIdSet(inferiorHostIdSets);

        // 如果dns,需要前后端口一致
        String port = mysqlParaHelper.getParameterValue(ParamConstants.PORT);
        if (physicalCustins.isDns()) {
            PortDistributeRule portDistributeRule = new PortDistributeRule();
            Set<Integer> ports = new HashSet<>(1);
            ports.add(instance.getPort());
            portDistributeRule.setSpecifyPortSet(ports);
            hostinsResModel.setPortDistributeRule(portDistributeRule);
        } else if (!StringUtils.isEmpty(port)) {
            // 由proxy/lvs链路指定切换为dns链路的话，必须保证新实例的端口保证和原vip的vport一致
            PortDistributeRule portDistributeRule = new PortDistributeRule();
            Set<Integer> ports = new HashSet<>(1);
            ports.add(Integer.valueOf(port));
            portDistributeRule.setSpecifyPortSet(ports);
            hostinsResModel.setPortDistributeRule(portDistributeRule);
        }

        custinsResModel.setHostinsResModel(hostinsResModel);
        custinsResModel.setVipResModelList(vipResModelList);
        //不是跨可用区，不需要
        List<RdsResModel> rdsResModelList = new ArrayList<>();
        custinsResModel.setRdsResModelList(rdsResModelList);
        container.addUpgradeCustinsResModel(custinsResModel);

        Response<UpgradeResRespModel> response = resApi.upgradeRes(container);
        logger.warn("resApi.upgradeRes,container"+JSONObject.toJSONString(container)+",response:"+JSONObject.toJSONString(response));
        if (!response.getCode().equals(200)) {
            return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND,
                ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }

        //主可用区添加
        if (physicalCustins.getProxyGroupId() == 0){
            try{
                custinsIDao.updateProxyGroupId(physicalCustins.getId());
            }catch (Exception e){
                logger.warn("update proxy group id for custins error", e);
            }
        }

        UpgradeResRespModel upgradeResRespModel = response.getData();

        if (!isSameAvz && !custins.isReadOrBackup()) {
            custinsParamService.updateAVZInfoZoneId(logicCustins.getId(), avzInfo);
        }

        String inputDesc = mysqlParaHelper.getParameterValue("DBInstanceStatusDesc");
        if (inputDesc == null && !isDhgCustinsActive) {
            if (oldLevel.equals(newLevel) && diskSize.equals(physicalCustins.getDiskSize())) {
                inputDesc = CustinsState.STATE_TRANSING.getComment();
            }
        }
        //为逻辑实例和物理实例分别构造trans_list，并针对逻辑实例下任务
        Integer taskId = createTransOrModifyTask(mysqlParaHelper.getAction(), mysqlParaHelper.getOperatorId(),
                logicCustins, physicalCustins, utcDate, switchMode, diskSize,
                newLevel, physicalNewLevel, upgradeResRespModel, isTransfer, inputDesc, false,
                true, isDhgCustinsActive, masterHostId);

        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

        Map<String, Object> data = new HashMap<>(7);
        data.put("MigrationID", 0);
        data.put("DBInstanceID", logicCustins.getId());
        data.put("DBInstanceName", logicCustins.getInsName());
        data.put("SourceDBInstanceClass", oldLevel.getClassCode());
        data.put("TargetDBInstanceClass", newLevel.getClassCode());
        data.put("TaskId", taskId);
        data.put("Region", region);
        data.put("SwitchMode", switchMode);
        return data;
    }

    public Set<Integer> getAndCheckHostIdsByDedicatedHostNames(Map<String, Object> dedicatedHostNamesForMasterAndSlave, String clusterName, Map<Integer, Integer> roleHostIdMappingNoCrossZone) throws RdsException {
        String targetDedicatedHostIdForMaster = (String) dedicatedHostNamesForMasterAndSlave.get("TargetDedicatedHostIdForMaster");
        String targetDedicatedHostIdForSlave = (String) dedicatedHostNamesForMasterAndSlave.get("TargetDedicatedHostIdForSlave");
        List<String> dedicatedHostNames = new ArrayList<>(2);
        if (!StringUtils.isEmpty(targetDedicatedHostIdForMaster)) {
            dedicatedHostNames.add(targetDedicatedHostIdForMaster);
        }
        if (!StringUtils.isEmpty(targetDedicatedHostIdForSlave)) {
            dedicatedHostNames.add(targetDedicatedHostIdForSlave);
        }
        List<Map<String, Object>> hostinfos = hostIDao.getHostIdsByDedicatedHostNames(dedicatedHostNames, clusterName);

        if (dedicatedHostNames.size() != hostinfos.size()) {
            // 不支持跨集群
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }
        Map<String, Integer> dedicatedHostNameHostIdMapping= new HashMap<>();
        for(Map<String, Object> row:hostinfos) {
            dedicatedHostNameHostIdMapping.put((String)row.get("dedicated_host_name"), (Integer) row.get("id"));
        }
        Set<Integer> hostIdSet = new LinkedHashSet<>();
        if(roleHostIdMappingNoCrossZone!=null && StringUtils.isEmpty(targetDedicatedHostIdForMaster)&&roleHostIdMappingNoCrossZone.containsKey(CUSTINS_ROLE_MASTER)&&roleHostIdMappingNoCrossZone.get(CUSTINS_ROLE_MASTER)!=null){
            hostIdSet.add(roleHostIdMappingNoCrossZone.get(CUSTINS_ROLE_MASTER));
        }
        for (String dedicatedHostName: dedicatedHostNames) {
            hostIdSet.add(dedicatedHostNameHostIdMapping.get(dedicatedHostName));
        }
        if(roleHostIdMappingNoCrossZone!=null && StringUtils.isEmpty(targetDedicatedHostIdForSlave)&&roleHostIdMappingNoCrossZone.containsKey(CUSTINS_ROLE_SLAVE)&&roleHostIdMappingNoCrossZone.get(CUSTINS_ROLE_SLAVE)!=null){
            hostIdSet.add(roleHostIdMappingNoCrossZone.get(CUSTINS_ROLE_SLAVE));
        }
        return hostIdSet;
    }

    /**
     * 1.构建物理实例的trans_list
     * 2.对逻辑实例下任务
     * */
    public Integer createTransOrModifyTask(String action,
                                           Integer operatorId,
                                           CustInstanceDO logicCustins,
                                           CustInstanceDO physicalCustins,
                                           Date utcDate,
                                           String switchMode,
                                           Long diskSize,
                                           InstanceLevelDO logicNewLevel,
                                           InstanceLevelDO physicalNewLevel,
                                           UpgradeResRespModel upgradeResRespModel,
                                           boolean isTransfer,
                                           String inputDesc,
                                           boolean fromEcsToPhysical,
                                           boolean isUserCluster,
                                           boolean isDhgCustinsActive,
                                           Integer masterHostId) throws RdsException{


        UpgradeResRespModel.CustinsResRespModel custinsResRespModel = upgradeResRespModel.getCustinsResRespModelList().get(0);

        TransListDO logicTransList = buildLogicTransList(logicCustins, utcDate, switchMode, diskSize, logicNewLevel, upgradeResRespModel, isTransfer, inputDesc, fromEcsToPhysical);

        TransListDO transList;
        //物理实例迁移信息
        List<Integer> srcInstanceIdList = custinsResRespModel.getSrcInstanceIdList();
        List<Integer> dstInstanceIdList = custinsResRespModel.getDstInstanceIdList();

        boolean isMoreThan3Ins = srcInstanceIdList.size() >= 3;

        String statusDesc = StringUtils.isNotBlank(inputDesc) ? inputDesc: CustinsState.STATE_CLASS_CHANGING.getComment();
        // 通过desc判断该迁移任务是否是运维后台下发
        Integer lockMigrate = 0;
        if (mysqlParaHelper.isActiveOperation(statusDesc)){
            lockMigrate = 1;
        }
        //临时物理实例
        Integer tmpCustinsId = 0;
        // local upgrade happened
        String taskKey = TaskSupport.TASK_TRANSFER;
        if (custinsResRespModel.getIsLocalUpgrade() == 1) {

            transList = new TransListDO(physicalCustins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
            // 多节点本地升级特殊处理
            if (isMoreThan3Ins ) {
                // 三节点与一主两备本地升降级的情况都是trans_hostins
                taskKey = TaskSupport.TASK_TRANSFER_HOSTINS;
                Map<String, Object> translistParamMap = new HashMap<>(1);
                List<Map<String, Object>> transferHostIdMapList = new ArrayList<>(
                    srcInstanceIdList.size());
                for (Integer insId : srcInstanceIdList) {
                    Map<String, Object> transferHostIdMap = new HashMap<>(2);
                    transferHostIdMap.put("s_hid", insId);
                    transferHostIdMap.put("d_hid", insId);
                    transferHostIdMapList.add(transferHostIdMap);
                }
                translistParamMap.put("TransferHostId", transferHostIdMapList);
                transList.setParameter(JSON.toJSONString(translistParamMap));
            }
        } else { // remote upgrade
            CustInstanceDO tempCustins = null;
            Long timestamp = System.currentTimeMillis();
            tempCustins = physicalCustins.clone();
            tempCustins.setId(null);
            tempCustins.setInsName("tmp" + timestamp + "_" + physicalCustins.getInsName());
            tempCustins.setStatus(CUSTINS_STATUS_CREATING);
            tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
            tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
            tempCustins.setInsType(physicalCustins.getInsType());//必须和源实例一致
            tempCustins.setLevelId(physicalNewLevel.getId());
            // 有可能升级版本
            tempCustins.setDbVersion(physicalNewLevel.getDbVersion());
            tempCustins.setDiskSize(diskSize);
            tempCustins.setClusterName(custinsResRespModel.getClusterName());
            tempCustins.setConnType(custinsResRespModel.getConnType());
            tempCustins.setProxyGroupId(custinsResRespModel.getProxyGroupId() == null ? 0:custinsResRespModel.getProxyGroupId());
            if (fromEcsToPhysical){
                tempCustins.setKindCode(CustinsSupport.KIND_CODE_NC); //5.7单节点变配双节点的时候,需要将kind_code更改为0
                if (accountService.countAccountByCustInsId(physicalCustins.getId()) >= physicalCustins.getMaxAccounts()) {
                    tempCustins.setAccountMode(CustinsSupport.CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED);
                }
                if (CustinsSupport.CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE.equals(tempCustins.getAccountMode())){
                    tempCustins.setMaxAccounts(500);
                    tempCustins.setMaxDbs(500);
                }
            }
            //创建临时实例
            custinsService.createCustInstanceForTrans(physicalCustins, tempCustins);
            String composeTag = mysqlParaHelper.selectComposeTag(physicalCustins.getClusterName());
            custinsParamService.setCustinsParam(tempCustins.getId(),
                    CUSTINS_PARAM_NAME_COMPOSE_TAG, composeTag);

            // update instance table
            List<Integer> tmpInstanceList = new ArrayList<>(dstInstanceIdList);
            tmpInstanceList.removeAll(srcInstanceIdList);
            instanceIDao.updateInstanceCustinsIdByInsIds(tmpInstanceList, tempCustins.getId());
            // update custins hostins rel table
            instanceIDao.updateCustinsHostinsRelByInsIds(tmpInstanceList, tempCustins.getId());

            List<Integer> custinsConnAddrIdList = custinsResRespModel.getCustinsConnAddrIdList();
            // update custins conn addr table
            connAddrCustinsService.updateCustinsConnAddrCustinsIdByIds(custinsConnAddrIdList, tempCustins.getId());

            tmpCustinsId = tempCustins.getId();

            // sync db && accounts
            dbsService.syncAllDbsAndAccounts(physicalCustins, tempCustins);

            if (fromEcsToPhysical) {
                // create aurora account for every custins exclude mongoDB
                dbsService.createAuroraAccount(tempCustins);
                dbsService.createAuroraProxyAccount(tempCustins);
                dbsService.dropRdsServiceAccount(tempCustins);
            }

            // sync all ip white list group
            ipWhiteListService.syncCustinsIpWhiteList(physicalCustins.getId(), tempCustins.getId());

            if (!physicalCustins.getDbVersion().equals(tempCustins.getDbVersion())) {
                statusDesc = CustinsState.STATE_VERSION_TRANSING.getComment();
            }

            transList = new TransListDO(physicalCustins, tempCustins,
                TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
            if (masterHostId != null && isUserCluster && dstInstanceIdList.size() == 2) {
                // 资源管理器申请出来的instance role 全都为0，所以需要修改slave的实例role 值
                Integer updateInstanceRoleInsId = null;
                Integer slaveRoleInsId = null;
                List<InstanceDO> instanceDos = instanceService.getInstanceByCustinsId(tmpCustinsId);
                for (InstanceDO instanceDO: instanceDos) {
                    if (!masterHostId.equals(instanceDO.getHostId())) {
                        updateInstanceRoleInsId = instanceDO.getId();
                    }
                    if (INSTANCE_ROLE_SLAVE.equals(instanceDO.getRole())) {
                        slaveRoleInsId = instanceDO.getId();
                    }
                }
                if (slaveRoleInsId == null) {
                    instanceService.updateInstanceRoleByInsId(updateInstanceRoleInsId,
                            INSTANCE_ROLE_SLAVE);
                }
            }


            // 本地升降级节点
            Set<Integer> retainIds = new HashSet<>(srcInstanceIdList);
            retainIds.retainAll(dstInstanceIdList);

            // 跨机节点
            Set<Integer> srcTransIds = new HashSet<>(srcInstanceIdList);
            srcTransIds.removeAll(retainIds);
            Set<Integer> dstTransIds = new HashSet<>(dstInstanceIdList);
            dstTransIds.removeAll(retainIds);

            List destTransIdList = new ArrayList<>(dstTransIds);
            List srcTransIdList = new ArrayList<>(srcTransIds);

            if (isMoreThan3Ins) {
                // 对于一主两备的迁移都是trans_hostins
                if(!physicalNewLevel.isMysqlEnterprise()){
                    taskKey = TaskSupport.TASK_TRANSFER_HOSTINS;
                }
                else{
                    //企业版实例也区分全跨机和半跨机的taskkey
                    if(retainIds.size() > 0){
                        taskKey = TaskSupport.TASK_TRANSFER_HOSTINS;
                    }
                    else{
                        taskKey = TaskSupport.TASK_TRANSFER;
                    }
                }

                // 此处三节点不考虑部分跨机部分本机的情况，将所有对应关系作为列表
                Map<String, Object> translistParamMap = new HashMap<>(1);
                List<Map<String, Object>> transferHostIdMapList = new ArrayList<>(
                    srcInstanceIdList.size());

                // 本地节点和原配对
                for (Integer insId : retainIds) {
                    Map<String, Object> transferHostIdMap = new HashMap<>(2);
                    transferHostIdMap.put("s_hid", insId);
                    transferHostIdMap.put("d_hid", insId);
                    transferHostIdMapList.add(transferHostIdMap);
                }

                // 跨机节点
                for (int i = 0; i < destTransIdList.size(); i++) {
                    Map<String, Object> transferHostIdMap = new HashMap<>(2);
                    transferHostIdMap.put("s_hid", srcTransIdList.get(i));
                    transferHostIdMap.put("d_hid", destTransIdList.get(i));
                    transferHostIdMapList.add(transferHostIdMap);
                }

                translistParamMap.put("TransferHostId", transferHostIdMapList);
                transList.setParameter(JSON.toJSONString(translistParamMap));
            } else if (retainIds.size() > 0) {
                // 非三节点 存在部分跨机 部分本机
                taskKey = TaskSupport.TASK_TRANSFER_HOSTINS;
                // 找到变化的节点
                if (srcTransIdList.size() > 0 && destTransIdList.size() > 0) {
                    Map<String, Object> transferHostIdMap = new HashMap<String, Object>(2);
                    transferHostIdMap.put("s_hid", srcTransIdList.get(0));
                    transferHostIdMap.put("d_hid", destTransIdList.get(0));

                    Map<String, Object> translistParamMap = new HashMap<String, Object>(1);
                    translistParamMap.put("TransferHostId", transferHostIdMap);
                    transList.setParameter(JSON.toJSONString(translistParamMap));
                } else {
                    logger.error("Should not happen.");
                    throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                }
            }
        }
        boolean isXdb = mysqlParaHelper.isXdbEnterprise(physicalCustins);
        boolean isMysqlEnt = mysqlDBCustinsService.isMysqlEnterprise(physicalCustins);
        if(isMysqlEnt ^ physicalNewLevel.isMysqlEnterprise() && !isXdb){
            //三节点和双节点互相切换,强制切换task key
            taskKey = TaskSupport.TASK_SWITCH_CATEGORY;

            String paramValue = CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_SEMI_SYNC;
            if (physicalNewLevel.isMysqlEnterprise()){
                // 主从 to 企业版 强同步
                paramValue = CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_NO_REVERT_SEMI_SYNC;
            }
            custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, paramValue);

            statusDesc = isMysqlEnt ? CustinsState.STATE_CATEGORY_SWITCHING_TO_STANDARD.getComment() : CustinsState.STATE_CATEGORY_SWITCHING_TO_FINANCE.getComment();
        }
        if (isDhgCustinsActive) {
            transList.setsHinsid1(0);
        } else {
            transList.setsHinsid1(srcInstanceIdList.get(0));
            if (srcInstanceIdList.size() > 1) {
                transList.setsHinsid2(srcInstanceIdList.get(1));
            }
        }

        transList.setdHinsid1(dstInstanceIdList.get(0));
        if (dstInstanceIdList.size() > 1) {
            transList.setdHinsid2(dstInstanceIdList.get(1));
        }

        // 原logic 实例 disk size 需要设置
        transList.setsDisksize(physicalCustins.getDiskSize());
        transList.setdLevelid(physicalNewLevel.getId());
        transList.setdDisksize(diskSize);

        //utcDate可能为null，即不是按照时间点切换
        Date metadbSwitchTime = null;
        if(utcDate != null){
            String utcSwitchTimeStr = dtzSupport.transDate2UtcStr(utcDate);
            metadbSwitchTime = dtzSupport.transUtcStr2MetadbDate(utcSwitchTimeStr, DataSourceMap.DATA_SOURCE_DBAAS);
        }
        transList.setSwitchTime(metadbSwitchTime);

        String comment = isDhgCustinsActive ? CustinsState.STATE_STARTING.getComment() : mysqlParaHelper.getStatusDescForReadins(physicalCustins, statusDesc);
        custinsService.updateCustInstanceStatusByCustinsId(physicalCustins.getId(), CUSTINS_STATUS_TRANS, comment);
        boolean changeSameStatus = true;
        String logicComment = comment;

        if (logicCustins != null) {
            if (physicalCustins.isReadOrBackup()) {
                logicComment = CustinsState.STATE_READINS_MAINTAINING.getComment();
            }
            custinsService.updateCustInstanceStatusByCustinsId(
                logicCustins.getId(), CUSTINS_STATUS_TRANS,  logicComment, changeSameStatus);
        }

        instanceIDao.createTransList(transList);

        //将物理实例的trans_list记录到逻辑实例的trans_list的parameter参数中
        Map<String,String> logicTransListParam = new HashMap<>();
        logicTransListParam.put("physical_trans_list", String.valueOf(transList.getId()));
        logicTransList.setParameter(JSON.toJSONString(logicTransListParam));
        instanceIDao.createTransList(logicTransList);

        //任务参数中是逻辑实例的trans_list_id
        String taskparam = taskService.getTransTaskParameterTimeZoneSafe(logicTransList.getId(), switchMode, utcDate);
        // #13054224 mysql实例迁移任务下发,增加一个参数锁定迁移flag,如果为true表示运维后台下发,在任务流中解锁>迁移>锁定.
        Map<String, Object> param_map = new HashMap<String, Object>();
        param_map = JSON.parseObject(taskparam);
        param_map.put(CustinsSupport.LOCK_MIGRATE, lockMigrate);

        boolean isDiskReduction = mysqlParaHelper.judgeDiskReduction(physicalCustins.getDiskSize(), diskSize);
        param_map.put(CustinsSupport.DISK_REDUCTION, isDiskReduction);
        param_map.put("isGeneral", "general".equalsIgnoreCase(physicalNewLevel.getCategory()));
        param_map.put("isUserCluster", isUserCluster);
        param_map.put("isDhgCustinsActive", isDhgCustinsActive);
        if("general".equalsIgnoreCase(physicalNewLevel.getCategory())){
            param_map.put("isGeneral", 1);
        }

        taskparam = JSON.toJSONString(param_map);

        // 任务下到逻辑实例上
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, logicCustins.getId(), TASK_TYPE_CUSTINS, taskKey, taskparam);
        taskService.createTaskQueue(taskQueue);
        instanceIDao.updateTransTaskIdById(transList.getId(), taskQueue.getId());
        return taskQueue.getId();

    }

    /**
     * 为逻辑实例创建trans_list，此时还未提交到数据库
     * 在逻辑实例trans_list的parameter参数中，增加KV：{"physical_trans_list_id":value}
     * */
    public TransListDO buildLogicTransList(CustInstanceDO logicCustins,
                                           Date utcDate,
                                           String switchMode,
                                           Long diskSize,
                                           InstanceLevelDO logicNewLevel,
                                           UpgradeResRespModel upgradeResRespModel,
                                           boolean isTransfer,
                                           String inputDesc,
                                           boolean fromEcsToPhysical) throws RdsException{

        //TransListDO transList = new TransListDO();
        TransListDO transList = new TransListDO(logicCustins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        transList.setsCinsid(logicCustins.getId());
        transList.setsHinsid1(0);
        transList.setsHinsid2(0);
        transList.setsLevelid(logicCustins.getLevelId());
        transList.setsCinstype(logicCustins.getTypeChar());
        transList.setdCinsid(logicCustins.getId());
        transList.setdHinsid1(0);
        transList.setdHinsid2(0);
        transList.setdLevelid(logicNewLevel.getId());
        transList.setdCinstype(logicCustins.getTypeChar());
        transList.setsDisksize(logicCustins.getDiskSize());
        transList.setdDisksize(diskSize);

        //计算元数据库时区时间
        //utcDate可能为null，即不是按照时间点切换
        Date metadbSwitchTime = null;
        if(utcDate != null){
            String utcSwitchTimeStr = dtzSupport.transDate2UtcStr(utcDate);
            metadbSwitchTime = dtzSupport.transUtcStr2MetadbDate(utcSwitchTimeStr, DataSourceMap.DATA_SOURCE_DBAAS);
        }
        transList.setSwitchTime(metadbSwitchTime);

        return transList;
    }

    public Boolean dhgIsArocessZoneId(CustInstanceDO custins, Integer physicalCustinsId, String zoneId) {
        if (zoneId == null) {
            return false;
        }
        String oldZoneId = null;
        boolean isOldMultiLocation = custinsParamService.getDispenseMode(custins.getId()).equals(ParamConstants.DispenseMode.MultiAVZDispenseMode);
        if (isOldMultiLocation && !custins.isReadOrBackup()) {
            oldZoneId = custinsParamService.getMasterZoneId(custins.getId());
            return !zoneId.equals(oldZoneId);
        } else {
            // 需要通过物理实例id 获取链路信息 专享主机组 实例必须存在vpc 链路, 通过获取链路的siteName 和传进来的zoneId 获取siteName 来进行判断是否是跨可用区
            CustinsConnAddrDO custinsConnAddrDO = connAddrCustinsService.getCustinsConnAddrByCustinsId(physicalCustinsId,
                    CustinsSupport.NET_TYPE_VPC,
                    CustinsSupport.RW_TYPE_NORMAL).get(0);
            IpResourceDO ipr= resourceService.getIpResourceByIpAndVpc(
                    custinsConnAddrDO.getVip(),
                    custinsConnAddrDO.getVpcId());
            String newZoneIdSite = clusterService.getSiteNameByAvzone(zoneId);
            return !ipr.getSiteName().equals(newZoneIdSite);
        }
    }

    private List<ZoneInfo> assembleDhgZoneInfoList(String zoneId, Integer masterHostId) {
        ZoneInfo zoneInfo = new ZoneInfo(zoneId, 0);
        zoneInfo.setHostId(masterHostId);
        return Collections.singletonList(zoneInfo);
    }


    /**
     * mysql80 基础版 变配到 物理实例
     * */
    public Map<String, Object> trans80Basic2StandardPhyscial(CustInstanceDO custins) throws RdsException{

        //参数校验
        boolean isTransfer = mysqlParaHelper.getAction().equalsIgnoreCase("MigrateDBInstance");
        custins = mysqlParaHelper.getAndCheckCustInstance();

        if (!custins.isActive() && !mysqlParaHelper.isReadSwitch(custins)) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        if (custins.isShare()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        String dbVersion = null;
        Boolean isSameAvz = false;

        Map<String,String> params = ActionParamsProvider.ACTION_PARAMS_MAP.get();

        //可用区信息
        String region = avzSupport.getMainLocation(params);
        AVZInfo avzInfo = avzSupport.getAVZInfo(params);
        AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
        String oldRegion = oldAvzInfo.getMainLocation();

        if (avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.ClassicDispenseMode) &&
            oldAvzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)
            || !avzInfo.isValidForModify()) {
            avzInfo = oldAvzInfo;
            isSameAvz = true;
        }

        //获取版本
        if (!isTransfer) {
            dbVersion = mysqlParaHelper.getDBVersion(custins.getDbType());
        }

        if (dbVersion == null) {
            dbVersion = custins.getDbVersion();
        }

        //不支持版本升降级
        if (0 != dbVersion.compareTo(custins.getDbVersion())) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        String transType = mysqlParaHelper.getParameterValue(ParamConstants.DB_INSTANCE_TRANS_TYPE,
            CustinsSupport.CUSTINS_TRANS_TYPE_DEFAULT);
        CustinsSupport.checkTransTypeValid(transType);

        String levelCode = mysqlParaHelper.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS);
        InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        InstanceLevelDO newLevel = Validator.isNull(levelCode) ? oldLevel : instanceService.getInstanceLevelByClassCode(
            levelCode, custins.getDbType(), dbVersion, custins.getTypeChar(), null);

        String specifyCluster = mysqlParaHelper.getParameterValue(ParamConstants.CLUSTER_NAME);
        //docker on ecs迁移到物理机，集群的dbType发生变化，需要将目标规格的dbType传递给资源管理器
        //如果传递集群的dbType与规格不一致，则报错，否则使用新规格的dbType
        String specifyDbType = newLevel.getDbType();
        if(specifyCluster != null){
            ClustersDO specifyClusterDO = clusterService.getClusterByClusterName(specifyCluster);
            if(specifyClusterDO != null && !specifyClusterDO.getDbType().equalsIgnoreCase(specifyDbType)){
                throw new RdsException(MysqlErrorCode.INVALID_CLUSTER_NOT_SAME_WITH_CLASS_CODE.toArray());
            }
        }
        String storage = mysqlParaHelper.getParameterValue(ParamConstants.STORAGE);
        Set<Integer> specifyHostIdSet = mysqlParaHelper.getAndCheckHostIdSet();
        boolean isAcrossRegion = !StringUtils.isBlank(region) && !oldRegion.equals(region);
        if(!isAcrossRegion){
            region = oldRegion;
        }

        CustInstanceDO physicalCustins = mysqlParaHelper.getPhyscialCustinsByParentId(custins.getId());
        if(physicalCustins == null){
            throw new RdsException(ErrorCode.INVALID_DBINSTANCENAME);
        }

        //查询时使用物理实例查询custins_conn_addr表
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
            .getCustinsConnAddrByCustinsId(physicalCustins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);
        List<VipResModel> vipResModelList = new ArrayList<>();
        //跨可用区，且原实例为vpc实例，必须传入tunnelId，vswitchId，ipAddress等信息
        if(isAcrossRegion){
            CustinsConnAddrDO vpcConnAddr = ConnAddrSupport.getVPCConnAddr(custinsConnAddrList);
            if(vpcConnAddr != null){
                // VPC 实例， 必须传入tunnelid， vswitchid， ipaddress
                String vpcId = CheckUtils.checkValidForVPCId(
                    mysqlParaHelper.getParameterValue(ParamConstants.VPC_ID));
                String tunnelId = CheckUtils.checkValidForTunnelId(
                    mysqlParaHelper.getParameterValue(ParamConstants.TUNNEL_ID));
                String vswitchId = CheckUtils.checkValidForVswitchId(
                    mysqlParaHelper.getParameterValue(ParamConstants.VSWITCH_ID));
                String ipaddress = CheckUtils.checkValidForIPAddress(
                    mysqlParaHelper.getParameterValue(ParamConstants.IP_ADDRESS));
                String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(
                    mysqlParaHelper.getParameterValue(ParamConstants.VPC_INSTANCE_ID));

                VipResModel vipResModel = new VipResModel(vpcConnAddr.getNetType());
                vipResModel.setUserVisible(vpcConnAddr.getUserVisible());
                vipResModel.setConnAddrCust(
                    mysqlParaHelper.getConnAddrCust(
                        "tmp" + System.currentTimeMillis() + "-" + custins.getInsName().replace('_', '-'),
                        mysqlParaHelper.getRegionIdByClusterName(custins.getClusterName()),
                        custins.getDbType()));
                vipResModel.setVip(ipaddress);
                vipResModel.setVport(Integer.valueOf(vpcConnAddr.getVport()));
                vipResModel.setVpcId(vpcId);
                vipResModel.setTunnelId(CustinsValidator.getRealNumber(tunnelId, -1));
                vipResModel.setVswitchId(vswitchId);
                vipResModel.setVpcInstanceId(vpcInstanceId);
                vipResModelList.add(vipResModel);
            }
        }

        //空间检查
        Long diskSize;
        Integer maxDiskSize = resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
        if (StringUtils.isNotEmpty(storage)) {
            diskSize = CheckUtils.parseInt(storage, 5, maxDiskSize, ErrorCode.INVALID_STORAGE) * 1024L;
        } else {
            // ecs磁盘空间可能和物理机范围不一样，超过物理机磁盘空间的ecs不允许迁移物理机
            diskSize = custins.getDiskSize();
            if (diskSize > maxDiskSize * 1024) {
                return createErrorResponse(ErrorCode.INVALID_STORAGE);
            }
        }

        //设置切换时间
        Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(params);
        String switchMode = mysqlParaHelper.getAndCheckSwitchTimeMode(ParamConstants.SWITCH_TIME_MODE, utcDate, true);

        //资源请求
        UpgradeResContainer container = avzSupport.getUpgradeResContainer(avzInfo);
        container.setRequestId(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));
        container.setClusterName(specifyCluster);
        container.setDbType(specifyDbType);
        container.setPreferClusterName(custins.getClusterName());
        container.setAccessId(mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID));
        container.setOrderId(mysqlParaHelper.getParameterValue(ParamConstants.ORDERID));
        container.setTransType(Integer.valueOf(transType));

        // init custins res model
        UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
        custinsResModel.setCustinsId(physicalCustins.getId());

        // init host ins res model
        HostinsResModel hostinsResModel = new HostinsResModel(newLevel.getId());
        hostinsResModel.setHostType(newLevel.getHostType());
        hostinsResModel.setInsCount(newLevel.isMysqlEnterprise() ? 3 : 2);
        hostinsResModel.setDiskSizeSold(diskSize);
        hostinsResModel.setTransType(Integer.valueOf(transType));
        // get disk size used
        try {
            InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
            hostinsResModel.setDiskSizeUsed(new BigDecimal(instancePerf.getDiskCurr()).longValue());
        } catch (Exception e) {
            logger.error("Get instance perf failed for custins: " + custins.getId(), e);
            hostinsResModel.setDiskSizeUsed(custins.getDiskSize());
        }

        // init distribute rule
        DistributeRule distributeRule = hostinsResModel.getDistributeRule();
        distributeRule.setSiteDistributeMode(hostinsResModel.getInsCount() > 3 ? DistributeMode.AVG_SCATTER:DistributeMode.FORCE_SCATTER);
        distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);
        distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
        distributeRule.setSpecifyHostIdSet(specifyHostIdSet);
        InsLevelExtraInfo.updateDistributeRule(distributeRule, newLevel.getExtraInfo());

        hostinsResModel.setDistributeRule(distributeRule);

        custinsResModel.setHostinsResModel(hostinsResModel);
        custinsResModel.setVipResModelList(vipResModelList);
        container.addUpgradeCustinsResModel(custinsResModel);

        Response<UpgradeResRespModel> response = resApi.upgradeRes(container);
        UpgradeResRespModel respModel = response.getData();
        if (!response.getCode().equals(200)) {
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }

        // 更新可用区信息
        if (!isSameAvz) {
            custinsParamService.updateAVZInfo(custins.getId(), avzInfo);
        }

        String inputDesc = mysqlParaHelper.getParameterValue("DBInstanceStatusDesc");
        Integer taskId = createTransFromDockerOnEcsToPhyscial(mysqlParaHelper.getAction(),
                mysqlParaHelper.getOperatorId(), custins, physicalCustins,
                utcDate, switchMode, diskSize, newLevel, respModel,
                isTransfer, inputDesc, true);
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

        Map<String, Object> data = new HashMap<>(7);
        data.put("MigrationID", 0);
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("SourceDBInstanceClass", oldLevel.getClassCode());
        data.put("TargetDBInstanceClass", newLevel.getClassCode());
        data.put("TaskId", taskId);
        data.put("Region", region);
        data.put("SwitchMode", switchMode);
        return data;
    }

    /**
     * docker on ecs 变配到物理机
     * 与物理实例无关，需要将物理实例整个替换掉
     * trans_list中的src_cins是逻辑实例，dst_cins是目标物理实例
     * 目标实例规格是dstLevel
     * */
    public Integer createTransFromDockerOnEcsToPhyscial(String action,
                                                        Integer operatorId,
                                                        CustInstanceDO logicCustins,
                                                        CustInstanceDO physicalCustins,
                                                        Date utcDate,
                                                        String switchMode,
                                                        Long diskSize,
                                                        InstanceLevelDO dstLevel,
                                                        UpgradeResRespModel upgradeResRespModel,
                                                        boolean isTransfer,
                                                        String inputDesc,
                                                        boolean fromEcsToPhysical) throws RdsException{

        UpgradeResRespModel.CustinsResRespModel custinsResRespModel = upgradeResRespModel.getCustinsResRespModelList().get(0);
        TransListDO transList;
        //物理实例迁移信息
        List<Integer> srcInstanceIdList = custinsResRespModel.getSrcInstanceIdList();
        List<Integer> dstInstanceIdList = custinsResRespModel.getDstInstanceIdList();

        String statusDesc = StringUtils.isNotBlank(inputDesc) ? inputDesc: CustinsState.STATE_CLASS_CHANGING.getComment();
        // 通过desc判断该迁移任务是否是运维后台下发
        Integer lockMigrate = 0;
        if (mysqlParaHelper.isActiveOperation(statusDesc)){
            lockMigrate = 1;
        }
        //临时物理实例
        Integer tmpCustinsId = 0;
        // local upgrade happened
        String taskKey = "trans_docker_on_ecs_to_physical";
        if (custinsResRespModel.getIsLocalUpgrade() == 1) {

            transList = new TransListDO(logicCustins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);

        } else { // remote upgrade
            CustInstanceDO tempCustins = null;
            Long timestamp = System.currentTimeMillis();
            //连接地址在物理实例上
            tempCustins = physicalCustins.clone();
            //clone之后，记得修改临时实例的character_type和parent_id
            tempCustins.setCharacterType(CustinsSupport.CHARACTER_TYPE_NORMAL);
            tempCustins.setParentId(0);
            tempCustins.setId(null);
            tempCustins.setInsName("tmp" + timestamp + "_" + physicalCustins.getInsName());
            tempCustins.setStatus(CUSTINS_STATUS_CREATING);
            tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
            tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
            tempCustins.setInsType(physicalCustins.getInsType());//必须和源实例一致
            tempCustins.setLevelId(dstLevel.getId());
            // 有可能升级版本
            tempCustins.setDbVersion(dstLevel.getDbVersion());
            tempCustins.setDiskSize(diskSize);
            tempCustins.setClusterName(custinsResRespModel.getClusterName());
            tempCustins.setConnType(custinsResRespModel.getConnType());
            tempCustins.setProxyGroupId(custinsResRespModel.getProxyGroupId() == null ? 0:custinsResRespModel.getProxyGroupId());
            if (fromEcsToPhysical){
                tempCustins.setKindCode(CustinsSupport.KIND_CODE_NC); //5.7单节点变配双节点的时候,需要将kind_code更改为0
                if (accountService.countAccountByCustInsId(physicalCustins.getId()) >= physicalCustins.getMaxAccounts()) {
                    tempCustins.setAccountMode(CustinsSupport.CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED);
                }
                if (CustinsSupport.CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE.equals(tempCustins.getAccountMode())){
                    tempCustins.setMaxAccounts(500);
                    tempCustins.setMaxDbs(500);
                }
            }
            //创建临时实例
            custinsService.createCustInstanceForTrans(physicalCustins, tempCustins);

            // update instance table
            List<Integer> tmpInstanceList = new ArrayList<>(dstInstanceIdList);
            tmpInstanceList.removeAll(srcInstanceIdList);
            instanceIDao.updateInstanceCustinsIdByInsIds(tmpInstanceList, tempCustins.getId());
            // update custins hostins rel table
            instanceIDao.updateCustinsHostinsRelByInsIds(tmpInstanceList, tempCustins.getId());

            List<Integer> custinsConnAddrIdList = custinsResRespModel.getCustinsConnAddrIdList();
            // update custins conn addr table
            connAddrCustinsService.updateCustinsConnAddrCustinsIdByIds(custinsConnAddrIdList, tempCustins.getId());

            tmpCustinsId = tempCustins.getId();

            // sync db && accounts
            dbsService.syncAllDbsAndAccounts(physicalCustins, tempCustins);

            if (fromEcsToPhysical) {
                dbsService.createAuroraProxyAccount(tempCustins);
            }

            // sync all ip white list group
            ipWhiteListService.syncCustinsIpWhiteList(physicalCustins.getId(), tempCustins.getId());

            if (!physicalCustins.getDbVersion().equals(tempCustins.getDbVersion())) {
                statusDesc = CustinsState.STATE_VERSION_TRANSING.getComment();
            }

            transList = new TransListDO(logicCustins, tempCustins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
            // 本地升降级节点
            Set<Integer> retainIds = new HashSet<>(srcInstanceIdList);
            retainIds.retainAll(dstInstanceIdList);

            // 跨机节点
            Set<Integer> srcTransIds = new HashSet<>(srcInstanceIdList);
            srcTransIds.removeAll(retainIds);
            Set<Integer> dstTransIds = new HashSet<>(dstInstanceIdList);
            dstTransIds.removeAll(retainIds);

            List destTransIdList = new ArrayList<>(dstTransIds);
            List srcTransIdList = new ArrayList<>(srcTransIds);

            if (retainIds.size() > 0) {
                // 非三节点 存在部分跨机 部分本机
                taskKey = TaskSupport.TASK_TRANSFER_HOSTINS;
                // 找到变化的节点
                if (srcTransIdList.size() > 0 && destTransIdList.size() > 0) {
                    Map<String, Object> transferHostIdMap = new HashMap<String, Object>(2);
                    transferHostIdMap.put("s_hid", srcTransIdList.get(0));
                    transferHostIdMap.put("d_hid", destTransIdList.get(0));

                    Map<String, Object> translistParamMap = new HashMap<String, Object>(1);
                    translistParamMap.put("TransferHostId", transferHostIdMap);
                    transList.setParameter(JSON.toJSONString(translistParamMap));
                } else {
                    logger.error("Should not happen.");
                    throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                }
            }
        }
        boolean isXdb = mysqlParaHelper.isXdbEnterprise(physicalCustins);
        boolean isMysqlEnt = mysqlDBCustinsService.isMysqlEnterprise(physicalCustins);
        if(isMysqlEnt ^ dstLevel.isMysqlEnterprise() && !isXdb){
            //三节点和双节点互相切换,强制切换task key
            taskKey = TaskSupport.TASK_SWITCH_CATEGORY;

            String paramValue = CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_SEMI_SYNC;
            if (dstLevel.isMysqlEnterprise()){
                // 主从 to 企业版 强同步
                paramValue = CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_NO_REVERT_SEMI_SYNC;
            }
            custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, paramValue);

            statusDesc = isMysqlEnt ? CustinsState.STATE_CATEGORY_SWITCHING_TO_STANDARD.getComment() : CustinsState.STATE_CATEGORY_SWITCHING_TO_FINANCE.getComment();
        }

        transList.setsHinsid1(srcInstanceIdList.get(0));
        if (srcInstanceIdList.size() > 1) {
            transList.setsHinsid2(srcInstanceIdList.get(1));
        }
        transList.setdHinsid1(dstInstanceIdList.get(0));
        if (dstInstanceIdList.size() > 1) {
            transList.setdHinsid2(dstInstanceIdList.get(1));
        }
        transList.setdLevelid(dstLevel.getId());
        transList.setdDisksize(diskSize);

        //utcDate可能为null，即不是按照时间点切换
        Date metadbSwitchTime = null;
        if(utcDate != null){
            String utcSwitchTimeStr = dtzSupport.transDate2UtcStr(utcDate);
            metadbSwitchTime = dtzSupport.transUtcStr2MetadbDate(utcSwitchTimeStr, DataSourceMap.DATA_SOURCE_DBAAS);
        }
        transList.setSwitchTime(metadbSwitchTime);

        custinsService.updateCustInstanceStatusByCustinsId(physicalCustins.getId(), CUSTINS_STATUS_TRANS, mysqlParaHelper.getStatusDescForReadins(physicalCustins, statusDesc));
        if (logicCustins != null) {
            boolean changeSameStatus = true;
            custinsService.updateCustInstanceStatusByCustinsId(
                logicCustins.getId(), CUSTINS_STATUS_TRANS,  CustinsState.STATE_CLASS_CHANGING.getComment(), changeSameStatus);
        }

        instanceIDao.createTransList(transList);

        //任务参数中是逻辑实例的trans_list_id
        String taskparam = taskService.getTransTaskParameterTimeZoneSafe(transList.getId(), switchMode, utcDate);
        // #13054224 mysql实例迁移任务下发,增加一个参数锁定迁移flag,如果为true表示运维后台下发,在任务流中解锁>迁移>锁定.
        Map<String, Object> param_map = new HashMap<String, Object>();
        param_map = JSON.parseObject(taskparam);
        param_map.put(CustinsSupport.LOCK_MIGRATE, lockMigrate);

        boolean isDiskReduction = mysqlParaHelper.judgeDiskReduction(physicalCustins.getDiskSize(), diskSize);
        param_map.put(CustinsSupport.DISK_REDUCTION, isDiskReduction);

        taskparam = JSON.toJSONString(param_map);

        // 任务下到逻辑实例上
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, logicCustins.getId(), TASK_TYPE_CUSTINS, taskKey, taskparam);
        taskService.createTaskQueue(taskQueue);
        instanceIDao.updateTransTaskIdById(transList.getId(), taskQueue.getId());
        return taskQueue.getId();
    }
}
