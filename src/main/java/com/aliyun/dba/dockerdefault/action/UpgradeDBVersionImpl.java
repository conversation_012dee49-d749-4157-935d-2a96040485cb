package com.aliyun.dba.dockerdefault.action;

import com.aliyun.dba.base.action.MigratePengineToK8SInstanceClassImpl;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.service.mariadb.MariaDBUpgradeDBVersionService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.service.UserGrayService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultUpgradeDBVersionImpl")
public class UpgradeDBVersionImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeDBVersionImpl.class);

    @Autowired
    private TaskService taskService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    private UserGrayService userGrayService;
    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Resource
    private MariaDBUpgradeDBVersionService mariaDBUpgradeDBVersionService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        if (custInstanceDO.isMariaDB()) {
            return mariaDBUpgradeDBVersionService.upgradeDBVersion(custInstanceDO, map);
        }

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

            String targetDBType = mysqlParaHelper.getParameterValue("targetdbtype");
            CustInstanceDO custins;
            if (StringUtils.equalsIgnoreCase(targetDBType, "maxscale")) {
                custins = custinsService.getCustInstanceByInsName(null, mysqlParaHelper.getAndCheckDBInstanceName(), 0);
                if (custins==null) {
                    return ResponseSupport.createErrorResponse(ErrorCode.DBNAME_NOT_FOUND);
                }
            }else {
                custins = mysqlParaHelper.getAndCheckCustInstance();
            }
            if (custins.isReadAndWriteLock()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (custins.isShare() || (custins.isCustinsOnEcs() && !mysqlDBCustinsService.isMysqlEnterprise(custins))) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);//不是专享实例
            }
            if (!custins.isActive()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);//实例状态错误
            }

            //当前没有使用，统一成TargetMinorVersion
            //String minorVersion = mysqlParaHelper.getMinorVersion();
            //检查是否有指定版本创建
//            String targetMinorVersion = minorVersionServiceHelper.checkAndGetTargetMinorVersion(
//                custins.getDbType(),
//                custins.getDbVersion(),
//                instanceService.getInstanceLevelByLevelId(custins.getLevelId()).getClassCode(),
//                MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL,
//                mysqlParaHelper.getParameterValue("TargetMinorVersion"));

            String majorVersion = mysqlParaHelper.getMajorVersion();
            String instanceId = mysqlParaHelper.getInstanceId();

            // add in 3590
            Map<String, String> extraParams = new HashMap<>();
            extraParams.put(ParamConstants.TARGET_CUSTINS_COUNT, mysqlParaHelper.getAndCheckTargetCustinsCount());
            extraParams.put(ParamConstants.TARGET_DB_INSTANCE_CLASS, mysqlParaHelper.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS));
            extraParams.put(ParamConstants.STORAGE, mysqlParaHelper.getParameterValue(ParamConstants.STORAGE));
            extraParams.put(ParamConstants.REGION, mysqlParaHelper.getParameterValue(ParamConstants.REGION));
            extraParams.put(ParamConstants.CLUSTER_NAME, mysqlParaHelper.getParameterValue(ParamConstants.CLUSTER_NAME));
            extraParams.put(ParamConstants.UPGRADE_POLICY, mysqlParaHelper.getAndCheckUpgradePolicy());

            // default is upgrade minor version
            String targetTaskKey = TaskSupport.TASK_UPGRADE_MINOR_VERSION;
            if (majorVersion != null) {
                targetTaskKey = TaskSupport.TASK_UPGRADE_MAJOR_VERSION;
            }

            Map<String, Object> condition = new HashMap<>();
            condition.put("custinsId", custins.getId());
            condition.put("taskKey", targetTaskKey);
            condition.put("status", TaskSupport.TASK_RUNNING_STATUS);

            if (taskService.countTaskQueueByCondition(condition) > 0) {
                throw new RdsException(ErrorCode.TASK_EXIST);
            }

            Map<String, Object> taskQueueParam = new HashMap<>(5);

            //校验UTC时间，返回带时区的日期
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(map);
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(map, utcDate, true);
            Map<String, Object> effMap = custinsService.getEffectiveTimeMapTimeZoneSafe(switchMode, utcDate);
            taskQueueParam.put(CustinsSupport.SWITCH_KEY, effMap);

            taskQueueParam.put("relative_dbtype",  mysqlParaHelper.getParameterValue("RelativeDbtype", ""));

            // 增加灰度逻辑，基础版小版本升级走新老架构迁移逻辑
            String uid = mysqlParaHelper.getUID();
            String regionId = mysqlParaHelper.getParameterValue(ParamConstants.REGION_ID);
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if (userGrayService.isMigratePengineToK8SSwitchGray(uid, regionId, custins, insLevel)) {
                MigratePengineToK8SInstanceClassImpl migrateToK8SInstanceClass = SpringContextUtil.getBeanByClass(MigratePengineToK8SInstanceClassImpl.class);
                map.put("migrateTaskSource".toLowerCase(), CustinsState.STATE_MINOR_VERSION_TRANSING.getComment());
                CustInstanceDO logicCustins = custinsService.getCustInstanceByCustinsId(custins.getParentId());
                return migrateToK8SInstanceClass.doActionRequest(logicCustins, map);
            }

            if (!mysqlEngineCheckService.checkCanUpgradeMinorVersionWithMaxScale(custins, null)) {
                return ResponseSupport.createErrorResponse(new Object[]{ResultCode.CODE_UNSUPPORTED, "MaxscaleMinorVersionNotSupport", "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first."});
            }

            Integer taskId = instanceService.upgradeMinorVersionTask(
                mysqlParaHelper.getAction(), custins, taskQueueParam, mysqlParaHelper.getOperatorId());

            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            //data.put("TargetMinorVersion", targetMinorVersion);
            data.put("TaskId", taskId);

            return data;
        } catch (RdsException re) {
            logger.error("UpgradeDBVersion ex="+re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("UpgradeDBVersion ex="+ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
