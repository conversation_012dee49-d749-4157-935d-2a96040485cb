package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsSrv;
import com.aliyun.dba.custins.enums.CustinsSrvEnums;
import com.aliyun.dba.custins.idao.CustinstServiceIDao;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateAnalyticDBInstanceImpl")
public class CreateAnalyticDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CreateAnalyticDBInstanceImpl.class);

    @Resource
    protected AliyunInstanceDependency dependency;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected WorkFlowService workFlowService;

    @Autowired
    protected DBaasMetaService dbaasMetaService;

    @Autowired
    ReplicaSetService replicaSetService;

    @Autowired
    protected CheckService checkService;

    @Resource
    protected PodParameterHelper podParameterHelper;

    @Autowired
    protected CommonProviderService commonProviderService;

    @Resource
    protected PodTemplateHelper podTemplateHelper;

    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Resource
    protected ResourceService resourceService;

    @Autowired
    private CustinstServiceIDao custinstServiceIDao;

    @Autowired
    RdsApi rdsApi;

    @Resource
    private PodCommonSupport podCommonSupport;

    // 一站式开关，TRUE: 打开, FLASE: 关闭
    private static final String ONE_STOP_SWITCH = "ONE_STOP_SWITCH";

    // custins: mysql主实例
    // params: 创建CK实例的参数
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        logger.info("Success request CreateCKNode. requestId : {}", requestId);
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        try {

            // 一站式开关
            ResourceDO oneStopSwitch = resourceService.getResourceByResKey(ONE_STOP_SWITCH);
            if (Objects.isNull(oneStopSwitch) || !BooleanUtils.toBoolean(oneStopSwitch.getRealValue())) {
                logger.info("requestId : {}, ONE_STOP_SWITCH is off.", requestId);
                throw new Exception("ONE_STOP_SWITCH is off.");
            }

            String dbInstanceName = custins.getInsName();
            String ckInstanceName = mysqlParamSupport.getParameterValue(params, "AnalyticDBName");
            if (StringUtils.isNotEmpty(ckInstanceName)) {
                // 检查CK实例是否已经生产
                Map<String, Object> paramsMap = Maps.newHashMap();
                paramsMap.put("custinsId", custins.getId());
                paramsMap.put("serviceName", ckInstanceName);
                paramsMap.put("status", CustinsSrvEnums.CustinsSrvStatus.RDS_SERVICE_ACTIVE);
                logger.info("custinsSrvList info : {}", JSONObject.toJSONString(paramsMap));
                List<CustinsSrv> custinsSrvList = custinstServiceIDao.findCustinsService(paramsMap);
                if (custinsSrvList != null && custinsSrvList.size() > 0) {
                    logger.info("AnalyticDB is produced.");
                    return new HashMap<String, Object>() {{
                        put("TaskId", 1);
                        put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
                    }};
                }
            }

            // 检查创建分析实例的限制
            podCommonSupport.checkAnalyticDBInstanceLimit(requestId, custins);
            // 检查CK实例是否已经存在，若存在，则返回错误
            ServiceRelationListResult serviceRelationList = dbaasMetaService.getDefaultClient().listReplicaSetServices(requestId, dbInstanceName);
            if (serviceRelationList.getItems() != null) {
                List<ServiceRelation> clickhouseService = serviceRelationList.getItems().stream()
                        .filter(it -> StringUtils.equalsIgnoreCase(it.getStatus().getValue(), ServiceRelation.StatusEnum.ACTIVE.getValue()))
                        .filter(it -> StringUtils.equalsIgnoreCase(it.getServiceRole(), PodDefaultConstants.DB_TYPE_CK))
                        .collect(Collectors.toList());
                if (clickhouseService.size() > 0) {
                    logger.error("requestId : {}, custins : {} already has analytic instance.", requestId, dbInstanceName);
                    throw new RdsException(ErrorCode.EXIST_RELATED_CLICKHOUSE_INSTANCE);
                }
            }


            // 构造请求，下发创建任务流（先建CK，再建DTS）
            String analyticSyncMode = params.get(ParamConstants.ANALYTIC_SYNC_MODE.toLowerCase());
            if (!PodDefaultConstants.ANALYTIC_SYNC_MODE_SET.contains(analyticSyncMode)) {
                logger.error("requestId : {}, analyticSyncMode is not in ANALYTIC_SYNC_MODE_SET", requestId, analyticSyncMode);
                throw new Exception("analyticSyncMode is not in ANALYTIC_SYNC_MODE_SET");
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            // MySQL params
            jsonObject.put("replicaSetName", custins.getInsName());

            // CK params
            jsonObject.put("ckInstanceName", ckInstanceName);
            jsonObject.put("engineVersion", params.get(ParamConstants.ENGINE_VERSION.toLowerCase()));
            jsonObject.put("scaleMax", Integer.valueOf(params.get(ParamConstants.SCALE_MAX.toLowerCase())));
            jsonObject.put("scaleMin", Integer.valueOf(params.get(ParamConstants.SCALE_MIN.toLowerCase())));
            jsonObject.put("regionId", params.get(ParamConstants.REGION_ID.toLowerCase()));
            jsonObject.put("orderId", params.get(ParamConstants.ORDERID.toLowerCase()));

            String dbList = params.get(ParamConstants.DB_LIST.toLowerCase());

            try {
                JSONObject dbObject = JSONObject.parseObject(dbList);
                jsonObject.put("dbList", JSONObject.toJSONString(dbObject));
            } catch (JSONException ex) {
                logger.error("reqeustId : {}, can not cast to JSONObject.", requestId);
                throw new Exception("can not cast to JSONObject.");
            }

            String taskKey = PodDefaultConstants.TASK_CLUSTER_JOIN_CLICKHOUSE_INSTANCE;
            String parameter = jsonObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;

            // 创建与主实例的关联关系
            createServiceRelation(requestId, ckInstanceName, custins);
            // 记录全库同步的标志
            updateSyncMode(requestId, dbInstanceName, analyticSyncMode);

            logger.info("requestId : {}, task parameter : {}", requestId, parameter);
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
            logger.info("requestId : {}, data : {}", requestId, JSONObject.toJSONString(data));

            return data;
        } catch (RdsException ex) {
            log.error("CreateAnalyticDBInstanceImpl failed: {}", JSONObject.toJSONString(ex));
            throw ex;
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }




    /**
     * 创建与主实例的关联关系
     */
    private void createServiceRelation(String requestId, String ckInstanceName, CustInstanceDO custins) throws Exception {

        ServiceRelation relation = new ServiceRelation();
        relation.setServiceType(PodDefaultConstants.DB_TYPE_CK);
        relation.setServiceRole(PodDefaultConstants.DB_TYPE_CK);
        relation.setServiceName(ckInstanceName);
        relation.setStatus(ServiceRelation.StatusEnum.ACTIVE);
        //CK实例还未创建，暂时以主实例id填充，任务流中会修改
        relation.setServiceId(String.valueOf(custins.getId()));
        Map<String, String> clickhouseParams = new HashMap<>();
        relation.setExtraInfo(JSONObject.toJSONString(clickhouseParams));

        ServiceRelation serviceRelation = dbaasMetaService.getDefaultClient().createReplicaSetService(requestId, custins.getInsName(), relation);
        if (serviceRelation == null) {
            log.error("requetId : {}, create custins service failed.", requestId);
            throw new Exception("Create custins service failed!");
        }
    }

    private void updateSyncMode(String requestId, String custinsName, String analyticSyncMode) throws ApiException {
        Map<String, String> labels = new HashMap<>();
        labels.put(PodDefaultConstants.ANALYTIC_SYNC_MODE, analyticSyncMode);

        log.info("requestId : {}, syncMode : {}", requestId, JSONObject.toJSONString(labels));
        dbaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, custinsName, labels);
        log.info("requestId : {}, update syncMode success.", requestId);
    }

}
