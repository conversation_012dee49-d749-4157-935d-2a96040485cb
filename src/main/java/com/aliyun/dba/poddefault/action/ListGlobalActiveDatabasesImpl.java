package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.gdnmetaapi.ApiException;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstance;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstanceListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.GdnMetaService;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableList;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultListGlobalActiveDatabasesImpl")
public class ListGlobalActiveDatabasesImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ListGlobalActiveDatabasesImpl.class);
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private GdnInstanceService gdnInstanceService;
    @Resource
    private DBaasMetaService metaService;
    @Resource
    private GdnMetaService gdnMetaService;
    @Resource
    private CreateGlobalActiveDatabaseImpl CreateGlobalActiveDatabaseImpl;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String bid = paramSupport.getAndCheckBID(params);
        String uid = paramSupport.getUID(params);
        String gadInstanceName = paramSupport.getParameterValue(params, "GadInstanceName");
        String engine = paramSupport.getAndChangeEngine(params);
        String engineVersion = paramSupport.getParameterValue(params, "EngineVersion");

        List<GdnInstance> gdnInstances = ImmutableList.of();
        Map<String, Object> data = new HashMap<>();
        try {
            if (StringUtils.isEmpty(gadInstanceName)) {
                GdnInstanceListResult gdnInstanceListResult = gdnMetaService.getClient().listGdnInstances(requestId, engine, engineVersion, uid, bid, null, null, null);
                gdnInstances = gdnInstanceListResult.getItems();
            } else {
                GdnInstance gdnInstance = gdnMetaService.getClient().getGdnInstance(requestId, gadInstanceName, false);
                if (gdnInstance.getBid().equals(bid) && gdnInstance.getAliUid().equals(uid)) {
                    gdnInstances = ImmutableList.of(gdnInstance);
                }
            }
            JSONArray jsonResult = (JSONArray) JSON.toJSON(gdnInstances);
            for (Object o : jsonResult) {
                JSONObject gdnInstance = (JSONObject) o;
                JSONArray members = gdnInstance.getJSONArray("members");
                if (members != null) {
                    for (Object o2 : members) {
                        JSONObject gdnInstanceMember = (JSONObject) o2;
                        String memberName = gdnInstanceMember.getString("memberName");
                        String memberKind = gdnInstanceMember.getString("memberKind");
                        Map<String, String> instanceMemberDatas = gdnMetaService.getClient().getInstanceMemberDatas(requestId, memberKind, memberName);
                        // 黑名单数据外都返回
                        gdnInstanceMember.putAll(instanceMemberDatas.entrySet().stream().filter(e -> !ImmutableList.of(
                                "CreateParameters").contains(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
                    }
                }
            }
            data.put("GlobalActiveDatabases", jsonResult);
        } catch (ApiException | NullPointerException e) {
            logger.error(String.format("%s, failed get instance metas", requestId), e);
            throw new RdsException(ErrorCode.SERVICE_UNAVAILABLE);
        }
        return data;
    }
}
