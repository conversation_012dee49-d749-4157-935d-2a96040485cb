package com.aliyun.dba.poddefault.action.service.endpoint;

import com.aliyun.dba.poddefault.action.support.modules.EndPointRealServer;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class EndpointWeightConfig {

    public static final String CONFIG_KEY = "NodeWeights";

    public static final String ENDPOINT_NODE_KEY = "Node";

    private List<WeightConfig> nodeWeights;

    public EndpointWeightConfig(List<EndPointRealServer> realServers) {
        nodeWeights = new ArrayList<>();
        for (EndPointRealServer realServer : realServers) {
            WeightConfig config = new WeightConfig();
            config.setDBInstanceId(realServer.getDbInstanceId());
            config.setNodeId(realServer.getMemberId());
            config.setWeight(realServer.getWeight() == null ? 100 : realServer.getWeight());
            nodeWeights.add(config);
        }
    }

    public EndpointWeightConfig(){}

    @Data
    public static class WeightConfig {
        private String dBInstanceId;
        private String nodeId;
        private Integer weight = 100;
        // 最大延迟时间
        private Integer maxReplicationLag = 0;
        // ONLINE, OFFLINE
        private String status = "ONLINE";
    }
}
