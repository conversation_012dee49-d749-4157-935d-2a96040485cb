package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceConnectionStringImpl")
public class ModifyDBInstanceConnectionStringImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceConnectionStringImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;

    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private CustinsParamService custinsParamService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        EndPoint endPointForAllocate = null;
        ReplicaSet replicaSet = null;
        try {
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            ReplicaListResult replicaList = metaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null,null,null,null);
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            // 检查是否修改次数超过上限
            // TODO: change checkConnAddrChangeTimesExceed custins id to Long
            custinsService.checkConnAddrChangeTimesExceed(Integer.parseInt(replicaSet.getId().toString()), paramSupport.getAction(params), null);

            Integer netType = paramSupport.getNetType(params);
            NetTypeEnum netTypeEnum = PodDefaultConstants.NET_TYPE_ENUM_MAP.get(netType);
            String connectionString = paramSupport.getConnectionString(params);
            if (netType == null && connectionString == null) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            boolean isConnectionStringToSsl = paramSupport.isConnectionStringToSsl(params,custins);
            if (isConnectionStringToSsl){
                throw new RdsException(ErrorCode.INVALID_CONNECTIONSTRING, "The link address has been used by SSL, modification and deletion are prohibited");
            }
            EndpointListResult endpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null);
            final NetTypeEnum finalNetTypeEnum = netTypeEnum;
            Optional<Endpoint> findEndpointExist = endpointListResult.getItems().stream().filter(e -> e.getUserVisible() && ((finalNetTypeEnum == null || e.getNetType().toString().equals(finalNetTypeEnum.toString())) && (connectionString == null || e.getAddress().equals(connectionString)))).findFirst();
            if (!findEndpointExist.isPresent()) {
                throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
            }
            Endpoint endpointExist = findEndpointExist.get();

            // 为集群版形态 链路管理 适配已有接口
            if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                if (endpointExist.getEndpointGroupId() == null) {
                    throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
                }

                EndpointGroup endpointGroup = metaService.getDefaultClient().getEndpointGroupById(requestId, endpointExist.getEndpointGroupId(), false);
                params.put("DBInstanceEndpointId".toLowerCase(), endpointGroup.getGroupName());
                params.put(ParamConstants.CONNECTION_STRING.toLowerCase(), endpointExist.getAddress());
                params.put(ParamConstants.CONN_ADDR_PREFIX.toLowerCase(), paramSupport.getNewConnectionString(params));
                params.put(ParamConstants.PORT.toLowerCase(), paramSupport.getNewPort(params));
                ModifyDBInstanceEndpointAddressImpl modifyDBInstanceEndpointAddress = SpringContextUtil.getBeanByClass(ModifyDBInstanceEndpointAddressImpl.class);

                return modifyDBInstanceEndpointAddress.doActionRequest(null, params);
            }

            String newConnectionString = paramSupport.getNewConnectionString(params);
            String oldConnectionString = endpointExist.getAddress();
            Vpod replicaVpod = metaService.getDefaultClient().getReplicaVpod(requestId, replicaList.getItems().get(0).getId(), null, null);
            if (newConnectionString != null) {
                CheckUtils.checkValidForConnAddrCust(newConnectionString);
            } else {
                String connAddrCustLast = mysqlParamSupport.getConnAddrCustLast(replicaVpod.getRegionId(), replicaSet.getService());
                String prefixOfOldConnectionString = StringUtils.replace(oldConnectionString, connAddrCustLast, "");
                // 兼容region级last conn addr last 配置前后 获取到的last不一样的情况，如果没有替换成功，则再用配置前的替换一次
                if(StringUtils.isNotEmpty(oldConnectionString) && oldConnectionString.equals(prefixOfOldConnectionString)){
                    connAddrCustLast = mysqlParamSupport.getConnAddrCustLast(replicaSet.getService());
                    prefixOfOldConnectionString = StringUtils.replace(oldConnectionString, connAddrCustLast, "");
                }
                newConnectionString = prefixOfOldConnectionString;
            }
            String newPort = paramSupport.getNewPort(params);
            Optional<Replica> replica = metaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems().stream().filter(r -> r.getRole().equals(Replica.RoleEnum.MASTER)).findFirst();
            if (!replica.isPresent()) {
                throw new RdsException(ErrorCode.HOST_INSTANCE_NOT_FOUND);
            }
            String oldPort = endpointExist.getVport().toString();
            if (newPort != null) {
                newPort = CheckUtils.parseInt(newPort, 1000, 65534, ErrorCode.INVALID_PORT).toString();
            } else {
                newPort = oldPort;
            }
            boolean connectionStringChanged = !StringUtils.equals(oldConnectionString, mysqlParamSupport.getConnAddrCust(newConnectionString, replicaVpod.getRegionId(), replicaSet.getService()));
            if (!connectionStringChanged && StringUtils.equals(newPort, oldPort)) {
                throw new RdsException(ErrorCode.INVALID_CONNECTIONSTRING_OR_PORT);
            }
            if (connectionStringChanged) {
                endPointForAllocate = new EndPoint().domainPrefix(newConnectionString)
                        .ip(endpointExist.getVip())
                        .port(newPort)
                        .connType(EndPoint.ConnTypeEnum.valueOf(replicaSet.getConnType().name()))
                        .netType(NetTypeEnum.valueOf(endpointExist.getNetType().name()))
                        .endPointType(EndPoint.EndPointTypeEnum.valueOf(endpointExist.getType().name()));
                try {
                    commonProviderService.getDefaultApi().allocateConnectionString(requestId, replicaSet.getName(), endPointForAllocate);
                } catch (ApiException e) {
                    throw new RdsException(ErrorCode.OTHER_ENDPOINT_EXIST);
                }
            }

            EndpointChangeLog endpointChangeLog = new EndpointChangeLog();
            Integer operatorId = paramSupport.getOperatorId(params);
            endpointChangeLog.action(EndpointChangeLog.ActionEnum.UPDATE)
                    .replicaId(replica.get().getId())
                    .taskId(0)
                    .creator(operatorId)
                    .modifier(operatorId)
                    .fromUserVisible(true)
                    .toUserVisible(true)
                    .fromConnAddrCust(endpointExist.getAddress())
                    .fromVport(oldPort)
                    .netType(EndpointChangeLog.NetTypeEnum.valueOf(endpointExist.getNetType().name()))
                    .rwType(EndpointChangeLog.RwTypeEnum.valueOf(endpointExist.getType().name()))
                    .fromVpcId(endpointExist.getVpcId())
                    .fromVip(endpointExist.getVip())
                    .toConnAddrCust(newConnectionString)
                    .toVip(endpointExist.getVip())
                    .toVport(newPort)
                    .status(EndpointChangeLog.StatusEnum.CREATING);
            EndpointChangeLog changeLogCreated = metaService.getDefaultClient().createReplicaSetEndpointChangeLog(requestId, replicaSet.getName(), endpointChangeLog);

            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put("changeLogId", changeLogCreated.getId());     //任务只能针对当前生成的changeLog去处理
            String taskKey = PodDefaultConstants.TASK_MODIFY_ENDPOINT;
            Object taskId = workFlowService.dispatchTask(replicaSet, taskKey, taskParam.toString(), 0);
            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_MAINTAINING.toString());

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TaskId", taskId);
            endPointForAllocate = null; // 任务流已经下发，finally中不应该再释放已申请资源。
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (endPointForAllocate != null) {
                try {
                    commonProviderService.getDefaultApi().deleteConnectionString(requestId, replicaSet.getName(), endPointForAllocate);
                } catch (ApiException e) {
                    logger.error("release connection string failed!");
                }
            }
        }
    }
}
