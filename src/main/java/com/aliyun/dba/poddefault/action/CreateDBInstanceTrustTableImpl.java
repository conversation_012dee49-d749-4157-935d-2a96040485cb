package com.aliyun.dba.poddefault.action;
import com.aliyun.dba.base.service.TrustTableService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Map;
@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateDBInstanceTrustTableImpl")
public class CreateDBInstanceTrustTableImpl implements IAction {
    @Autowired
    private TrustTableService trustTableService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        return trustTableService.create(custins,params);
    }
}