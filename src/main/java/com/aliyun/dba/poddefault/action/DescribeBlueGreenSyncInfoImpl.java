package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeBlueGreenSyncInfoImpl")
public class DescribeBlueGreenSyncInfoImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeBlueGreenSyncInfoImpl.class);
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private BlueGreenDeploymentService blueGreenDeploymentService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            logger.info("DescribeBlueGreenSyncInfoImpl start. params : {}", JSON.toJSONString(params));
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String aliUid = mysqlParamSupport.getUID(params);
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String dbInstanceName = mysqlParamSupport.getAndCheckDBInstanceName(params);
            Map<String, Object> data = blueGreenDeploymentService.describeBlueGreenSyncInfo(custins, requestId, regionId, aliUid, dbInstanceName);
            logger.info("DescribeBlueGreenSyncInfoImpl end.");
            return data;
        } catch (Exception ex) {
            logger.error("DescribeBlueGreenSyncInfoImpl Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}