package com.aliyun.dba.poddefault.action.support;
/**
 * Alibaba.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */

import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;

import com.alicloud.apsaradb.k8s.resmanager.Constants;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;

/**
 * 用于混部支持相关的检查
 * jingyi.zhy
 */
@Component
public class InsArchHelper {
    private static final Logger logger = Logger.getLogger(InsArchHelper.class);
    @Resource
    ResourceService resourceService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodAvzSupport podAvzSupport;
    @Resource
    private MySQLServiceImpl mySQLService;
    private static final String RES_MYSQL_ARCH_MIX_BLACK_CTL = "RES_MYSQL_ARCH_MIX_BLACK_CTL"; // 这个可以进行指定。暂时还是保留这个字段
    private static final String RES_MYSQL_ARCH_CTL_UID = "uid";
    private static final String RES_MYSQL_ARCH_CTL_REGIONID = "regionId";

    public boolean disableArchMixByResource(String uid, String regionId){
        final ResourceDO resourceByResKey = resourceService.getResourceByResKey(RES_MYSQL_ARCH_MIX_BLACK_CTL);
        if(Objects.isNull(resourceByResKey)){
            return false;
        }
        String jsonStr = resourceByResKey.getRealValue();
        if(StringUtils.isEmpty(jsonStr)){
            return false;
        }
        JSONObject jsonObj = JSON.parseObject(jsonStr);
        logger.info("Get arch whitelist "+jsonObj.toJSONString());
        // 白名单的逻辑是 只要是在这个regionID内就一定支持。
        // 先查region，没有就再查uid
        // regionId whitelist
        if(jsonObj.containsKey(RES_MYSQL_ARCH_CTL_REGIONID)){
            List<String> regionIdList = jsonObj.getJSONArray(RES_MYSQL_ARCH_CTL_REGIONID).toJavaList(String.class);
            for(String regionIdTmp : regionIdList){
                if(StringUtils.isNotBlank(regionId) && regionId.equals(regionIdTmp)){
                    logger.info("disable Arch Mix for regionId:"+regionId);
                    return true;
                }
            }
        }
        // UID whitelist
        if(jsonObj.containsKey(RES_MYSQL_ARCH_CTL_UID)){
            List<String> uidList = jsonObj.getJSONArray(RES_MYSQL_ARCH_CTL_UID).toJavaList(String.class);
            for(String uidTmp : uidList){
                if(StringUtils.isNotBlank(uid) && uid.equals(uidTmp)){
                    logger.info("disable Arch Mix for uid:"+uid);
                    return true;
                }
            }
        }
        return false;
    }

    // in some scenario, like upgrade minor version、serverless basic to standard
    // the target replicaSet's spec may not support arm, we can not force it's arch to arm
    // so we set default to support arch mix

    // also, we did not need check target spec if supports arch
    // because if we set composeTag correct, common provider will check it for us
    public boolean supportArchMix(String uid, String replicaSetName, String requestId){
        try{
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSetName, true);
            if (Objects.isNull(replicaSet)){
                return true;
            }
            if(disableArchMixByResource(uid, podAvzSupport.getAVZInfo(replicaSet).getRegionId())){
                return false;
            }
        } catch (Exception rdsE) {
            logger.warn("check supportArchMix, got exception: ", rdsE);
        }
        return true;
    }

    public ScheduleTemplate updateArchLabelForScheduleTemplate(ScheduleTemplate scheduleTemplate, String originArch){
        ScheduleTemplateNodePolicy nodePolicy = scheduleTemplate.getNodePolicy();
        if(Objects.isNull(nodePolicy)){
            nodePolicy = new ScheduleTemplateNodePolicy();
            scheduleTemplate.setNodePolicy(nodePolicy);
        }
        NodeSelector nodeSelector = nodePolicy.getNodeSelector();
        if(Objects.isNull(nodeSelector)){
            nodeSelector = new NodeSelector();
            nodePolicy.setNodeSelector(nodeSelector);
        }
        for (NodeLabel label : nodeSelector.getLabels()) {
            if(StringUtils.equalsIgnoreCase(label.getKey(), Constants.ArchLabel)){
                label.setValue(originArch);
                return scheduleTemplate;
            }
        }
        NodeLabel archLabel = new NodeLabel();
        archLabel.setKey(Constants.ArchLabel);
        archLabel.setValue(originArch);
        nodeSelector.getLabels().add(archLabel);
        return scheduleTemplate;
    }

    public String getOriginArch(String requestId, String hostName){
        try{
            Map<String, String> hostLabels = dBaasMetaService.getDefaultClient().listHostLabels(requestId, hostName);
            String originArch = hostLabels.get(PodDefaultConstants.PARAM_ARCH_LABEL);
            if(!StringUtils.equals(originArch, CreateReplicaSetDto.ArchEnum.ARM.toString())){
                originArch = CreateReplicaSetDto.ArchEnum.AMD64.toString(); // 非arm，默认指定AMD64
            }
            return originArch;
        }catch (ApiException re){
            logger.warn("getOriginArch failed, return default AMD64 for hostName=:"+hostName, re);
            return CreateReplicaSetDto.ArchEnum.AMD64.toString();
        }
    }

    public String getHostNameByReplicaSetName(String requestId, String replicaSetName){
        try{
            if(StringUtils.isNotBlank(replicaSetName)){
                Replica masterReplica = mySQLService.getReplicaByRole(requestId, replicaSetName, Replica.RoleEnum.MASTER);
                if(Objects.nonNull(masterReplica)){
                    return masterReplica.getHostName();
                }
            }
            return null;
        }catch (ApiException re) {
            logger.warn("getHostNameByReplicaSetName-getReplicaByRole MetaApi error: ", re);
            return null;
        }
    }
}
