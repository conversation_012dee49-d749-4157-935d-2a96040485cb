package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointChangeLog;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointGroup;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.Vpod;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.model.ConfigVipConfBody;
import com.aliyun.dba.adb_vip_manager_client.model.Link;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.ModifyConnectionDrainingService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.ALB_CONNECTION_DRAINING_SWITCH;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceConnectionDrainingImpl")
public class ModifyDBInstanceConnectionDrainingImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceConnectionDrainingImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    public CustinsService custinsService;
    @Resource
    public DBaasMetaService metaService;
    @Resource
    public ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;

    @Resource
    public ModifyConnectionDrainingService modifyConnectionDrainingService;

    @Resource
    public LinksApi linksApi;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params)  {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        ReplicaSet replicaSet = null;
        try {
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //get all connections,and filter lvs type connection,do not support dns/physical connection
            if (replicaSet.getConnType() == null ||
                    replicaSet.getConnType().getValue().equalsIgnoreCase("dns") ||
                    (replicaSet.getConnType().getValue().equalsIgnoreCase("physical") && !ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(replicaSet.getCategory()))
            ) {
                logger.error(String.format("Current connection type is %s", replicaSet.getConnType().getValue()));
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_CONNTYPE);
            }


            return modifyConnectionDrainingService.modifyConnectionDraining(custins, params);


        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
