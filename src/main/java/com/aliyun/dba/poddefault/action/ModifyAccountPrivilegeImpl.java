package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyAccountPrivilegeImpl")
public class ModifyAccountPrivilegeImpl implements IAction {
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private DbossApi dbossApi;
    @Resource
    private DbsService dbsService;
    @Resource
    private AccountService accountService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
        String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            CustInstanceDO custins = mysqlParameterHelper.getAndCheckCustInstance();
            if (custins.isReadAndWriteLock()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (!custins.inAvailableStatus()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String accountName = mysqlParameterHelper.getAndCheckAccountName();
            String accountPrivilege = mysqlParameterHelper.getAccountPrivilege();
            String dbInfo = mysqlParameterHelper.getDbInfo();
            List<String> dbNames = mysqlParameterHelper.getDBNames();
            AccountsDO account = dbsService.getAccountDOByAccountName(custins.getId(), accountName, null);
            if (account != null) {
                if (account.isSync() || account.isSystem()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_ACCOUNT_TYPE);
                }
                if (!account.isActive()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_ACCOUNT_STATUS);
                }
                if (!AccountPriviledgeType.isNormalPriviledgeType(account.getPriviledgeType())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_ACCOUNT_PRIVILEGE_TYPE);
                }
            }

            Map<String, Object> account1 = new HashMap<>();
            account1.put("accountName", accountName);
            account1.put("custinsId", custins.getId());
            account1.put("requestId", requestId);
            List<Map<String, String>> privileges = accountService.getDatabasePrivileges(
                    dbNames, accountPrivilege, dbInfo, custins.getDbType());
            if (!CollectionUtils.isEmpty(privileges)) {
                account1.put("privileges", privileges);
            }
            if (custins.isPgsql() && custins.isCustinsDockerOnEcs()) {
                if (dbNames.size() == 0) {
                    if (dbInfo != null) {
                        JSONObject infoJson = JSON.parseObject(dbInfo);
                        dbNames = JSONObject.parseArray(infoJson.getJSONArray("DBOwner").toJSONString(), String.class);
                    }
                }
                account1.put("dbNames", dbNames);
            }
            dbossApi.updateAccount(account1);

            Map<String, Object> data1 = new HashMap<>(4);
            data1.put("DBInstanceID", custins.getId());
            data1.put("DBInstanceName", custins.getInsName());
            data1.put("AccountStatus", DbsSupport.STATUS_ACTIVE);
            data1.put("AccountName", accountName);
            data1.put("TaskId", 0);
            return data1;

        } catch (RdsException re) {
            log.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
