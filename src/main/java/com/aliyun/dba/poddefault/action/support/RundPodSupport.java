package com.aliyun.dba.poddefault.action.support;

import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Eni;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.Vpod;
import com.aliyun.dba.base.common.utils.ShuffleUtil;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.CrmService;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.modules.RundGrayInfoModules.RundGrayConditions;
import com.aliyun.dba.poddefault.action.support.modules.RundGrayInfoModules.RundRegionalGrayInfo;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2024/10/28
 */
@Slf4j
@Component
public class RundPodSupport {

    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private ResourceService resourceService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private MySQLServiceImpl mySQLService;
    @Resource
    private CrmService crmService;

    private static final LogAgent logger = LogFactory.getLogAgent(RundPodSupport.class);

    public final static String RES_TEMPLATE_NAME = "RS_TEMPLATE_MYSQL_RUND";

    //rund使用打散调度策略
    private static final String RUND_SCHEDULE_STRATEGY = "rds-rund";

    /**
     * 容器运行时的路由规则，判断是否路由到RunD的架构
     * 灰度逻辑：在目标region下从上往下命中。category和zoneId必须匹配，uid命中即返回；未命中则须同时命中gc&ratio。
     * 灰度json：
     * {
     *     "cn-beijing": [
     *         {
     *             "conditions": {
     *                 "category": "basic",
     *                 "zoneId": "cn-beijing-i,cn-beijing-h,cn-beijing-k",
     *                 "uid": "1582064160422497,1692999668519777,1336888804881051,1825188702113631,1984829570443947,1618711616067156,1452886459974240,1875785612443619",
     *                 "gc": "5-7",
     *                 "ratio": "0-0"
     *             },
     *             "result": "ecs_rund"
     *         },
     *         {
     *             "conditions": {
     *                 "category": "serverless_basic",
     *                 "zoneId": "cn-beijing-i,cn-beijing-h,cn-beijing-k",
     *                 "uid": "1582064160422497,1692999668519777,1336888804881051,1825188702113631,1984829570443947,1618711616067156,1452886459974240,1875785612443619,1266348003653919",
     *                 "gc": "5-7",
     *                 "ratio": "0-0"
     *             },
     *             "result": "ecs_vbm"
     *         }
     *     ],
     *     "cn-chengdu":[xxx]
     * }
     *
     * @param regionId
     * @param uid
     * @param instanceLevel
     * @param avzInfo
     * @return
     */
    public PodType getPodTypeByGrayConfig(String regionId, String uid, InstanceLevel instanceLevel, AVZInfo avzInfo) {
        if (instanceLevel == null || instanceLevel.getCategory() == null || PodCommonSupport.isArm(instanceLevel)) {
            return PodType.POD_RUNC;
        }
        try {
            ResourceDO resourceDO = resourceService.getResourceByResKey(PodDefaultConstants.RUND_GRAY_POLICY_MYSQL_BY_CATEGORY);
            if (resourceDO == null || StringUtils.isBlank(resourceDO.getRealValue())) {
                return PodType.POD_RUNC;
            }
            Gson gson = new Gson();
            Type type = new TypeToken<Map<String, List<RundRegionalGrayInfo>>>(){}.getType();
            Map<String, List<RundRegionalGrayInfo>> rundGrayInfo = gson.fromJson(resourceDO.getRealValue(),type);
            if (rundGrayInfo.containsKey(regionId)) {
                List<RundRegionalGrayInfo> regionalPolicyList = rundGrayInfo.get(regionId);
                if (regionalPolicyList == null) {
                    return PodType.POD_RUNC;
                }
                String gcLevel = crmService.getGcLevel(uid);
                for (RundRegionalGrayInfo policy : regionalPolicyList) {
                    RundGrayConditions conditions = policy.getConditions();
                    //匹配category
                    String category = conditions.getCategory();
                    if (!Objects.equals(category, instanceLevel.getCategory().toString())) {
                        continue;
                    }
                    //匹配zoneId
                    Object zoneIdObj = conditions.getZoneId();
                    Set<String> zoneIdSet = zoneIdObj == null ? new HashSet<>() : Arrays.stream(String.valueOf(zoneIdObj).split(",")).map(String::trim).collect(Collectors.toSet());
                    List<String> targetZoneIdList = new ArrayList<>();
                    if (avzInfo.getDispenseMode() == ParamConstants.DispenseMode.ClassicDispenseMode) {
                        targetZoneIdList.add(avzInfo.getMasterZoneId());
                    } else {
                        targetZoneIdList = avzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList().stream().map(AvailableZoneInfoDO::getZoneID).collect(Collectors.toList());
                    }
                    if (!zoneIdSet.containsAll(targetZoneIdList)) {
                        continue;
                    }
                    //匹配UID
                    Object uidObj = conditions.getUid();
                    Set<String> uidSet = uidObj == null ? new HashSet<>() : Arrays.stream(String.valueOf(uidObj).split(",")).map(String::trim).collect(Collectors.toSet());
                    if (uidSet.contains(uid) || uidSet.contains("*")) {
                        String result = String.valueOf(policy.getResult());
                        logger.info("rund matched specific uid. Current uid:{}, uid list:{}", uid, uidSet);
                        return PodType.getMatchedRuntimeType(result);
                    }
                    //匹配GC
                    String level = gcLevel.substring(gcLevel.length() - 1);
                    String gcRange = String.valueOf(conditions.getGc());
                    String[] gcList = gcRange.split("-");
                    if (gcList.length != 2) {
                        logger.warn("unexpected gclevel range for rund config:{}", gcRange);
                        continue;
                    }
                    if (StringUtils.compare(gcList[0], level) > 0 || StringUtils.compare(level, gcList[1]) > 0) {
                        continue;
                    }
                    //匹配uid ratio
                    if (conditions.getRatio() == null) {
                        continue;
                    }
                    int uidRatio = ShuffleUtil.crc16(uid) % 100;
                    //防止 ratio:0-0匹配上
                    if(uidRatio == 0){
                        uidRatio = 1;
                    }
                    String ratioRange = String.valueOf(conditions.getRatio());
                    String[] ratioList = ratioRange.split("-");
                    if (ratioList.length != 2) {
                        logger.warn("unexpected ratio range for rund config:{}", ratioRange);
                        continue;
                    }
                    if (Integer.parseInt(ratioList[0]) <= uidRatio && uidRatio <= Integer.parseInt(ratioList[1])) {
                        String result = String.valueOf(policy.getResult());
                        return PodType.getMatchedRuntimeType(result);
                    }
                }
                return PodType.POD_RUNC;
            }
        } catch (Exception e) {
            log.warn("get rund gray policy failed: {}", e.getMessage());
        }
        return PodType.POD_RUNC;
    }
    public boolean routeToRund(String serviceSpecTag,boolean isOptimizedWrite){
        return minorVersionSupportRund(serviceSpecTag) && !isOptimizedWrite;
    }

    //serviceSpecTag: eg. alisql_cloud_disk_docker_image_20240731
    //rundMiniVersion: eg. alisql_cloud_disk_docker_image_20240430
    public boolean minorVersionSupportRund(String serviceSpecTag) {
        try {
            ResourceDO rundMiniVersion = resourceService.getResourceByResKey(PodDefaultConstants.RUND_SUPPORT_MYSQL_MINI_VERSION);
            if (rundMiniVersion == null || StringUtils.isBlank(rundMiniVersion.getRealValue()) || serviceSpecTag == null) {
                return false;
            }
            String requiredMiniVersion = rundMiniVersion.getRealValue();
            String releaseDate = minorVersionServiceHelper.extractReleaseDate(serviceSpecTag);
            String requiredReleaseDate = minorVersionServiceHelper.extractReleaseDate(requiredMiniVersion);
            logger.info("releaseDate:{},rund required releaseDate:{}", releaseDate, requiredReleaseDate);
            return Long.parseLong(releaseDate) >= Long.parseLong(requiredReleaseDate);
        } catch (Exception e) {
            log.error("failed to parse service spec {},Error {}. Downgrade to runc.", e.getMessage(), serviceSpecTag);
            return false;
        }
    }

    public boolean isRundReplica(ReplicaResource replica) {
        if (replica == null || replica.getVpod() == null) {
            return false;
        }
        return PodType.POD_ECS_RUND.getRuntimeType().equalsIgnoreCase(replica.getVpod().getRuntimeType());
    }

    public boolean isRundReplicaSet(ReplicaSet replicaSet) throws ApiException {
        //可能是老架构克隆到新架构场景
        if (replicaSet.getKindCode() == null || replicaSet.getKindCode() != 18) {
            return false;
        }
        Replica replica = mySQLService.getReplicaByRole(RequestSession.getRequestId(), replicaSet.getName(), Replica.RoleEnum.MASTER);
        ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(RequestSession.getRequestId(), replica.getId(), null);
        return isRundReplica(replicaResource);
    }


    /**
     * 基础调度模版，控制是否到RunD裸金属主机
     *
     * @return
     */
    public ScheduleTemplate generateBaseScheduleTemplate(String replicaSetName, String uid, PodType podType) {
        ScheduleTemplate scheduleTemplate = new ScheduleTemplate();
        // scheduler
        // common variable
        NodeLabel rundNodeLabel = createNodeLabel(PodDefaultConstants.RUND_NODE_KEY, "true");
        scheduleTemplate.setNodePolicy(PodTemplateHelper.constructBlankNodePolicy());
        NodeLabel dedicateNodeLabel;
        if (podType == PodType.POD_VBM_RUND) {
            dedicateNodeLabel = createNodeLabel(PodDefaultConstants.TEMPLATE_ISOLATION_LABEL_KEY, "rds-vbm");
        } else {
            dedicateNodeLabel = createNodeLabel(PodDefaultConstants.TEMPLATE_ISOLATION_LABEL_KEY, "rds-rund");
        }

        scheduleTemplate.getNodePolicy().getNodeSelector().getLabels().add(dedicateNodeLabel);
        scheduleTemplate.getNodePolicy().getNodeSelector().getLabels().add(rundNodeLabel);
        scheduleTemplate.getNodePolicy().setNumaPolicy(ScheduleTemplateNodePolicy.NumaPolicyEnum.SOCKET_ONLY);
        scheduleTemplate.setStrategyName(RUND_SCHEDULE_STRATEGY);
        scheduleTemplate.setIsIgnoreDeploymentSet(true);
        if (scheduleTemplate.getSpread() == null) {
            scheduleTemplate.setSpread(new ArrayList<>());
        }
        scheduleTemplate.getSpread().addAll(podTemplateHelper.getCommonSpreads(replicaSetName, uid));
        return scheduleTemplate;
    }


    /**
     * Only for basic instance
     * 重搭场景，申请同规格计算资源+云盘
     */
    public void completeReplicaNetworkConfig(RebuildReplicaResourceRequest request, Replica srcReplica) throws Exception {
        logger.info("start complete rund resource request in RebuildReplicaResourceRequest.");
        // port
        List<Port> backendPorts = new ArrayList<>();
        backendPorts.add(new Port().name(PodDefaultConstants.ACCESS_PORT_NAME).value(PodDefaultConstants.POD_DEFAULT_PORT));  // 这里指定3306即可
        request.setBackendPorts(backendPorts);
        request.setPortPolicy(PodDefaultConstants.PORT_POLICY_CONTAINER);
        ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(RequestSession.getRequestId(), srcReplica.getId(), null);

        if (isRundReplica(replicaResource)) {
            Eni eni = getEniByReplica(srcReplica);
            if (eni == null) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            request.setVswitchId(eni.getVswitchId());
            request.setVpcId(eni.getVpcId());
        } else {
            Endpoint endpoint = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(RequestSession.getRequestId(), replicaResource.getReplicaSetName(), null, null, null, null)
                    .getItems().stream().filter(ep -> Endpoint.NetTypeEnum.VPC.equals(ep.getNetType()) && Endpoint.TypeEnum.NORMAL.equals(ep.getType())).collect(Collectors.toList()).get(0);
            IpResource ipResource = dBaasMetaService.getDefaultClient().getIpresource(RequestSession.getRequestId(), endpoint.getVip(), endpoint.getVpcId()).getItems().get(0);
            request.setVswitchId(ipResource.getVswitchId());
            request.setVpcId(ipResource.getVpcId());
        }

        request.setZoneId(srcReplica.getZoneId());

        // eni
        request.setRamEni(true);
        request.setUpdateNetworkUniqueMark(true);
    }

    /**
     * 缩容场景，配置rund实例级别参数
     *
     * @param request
     */
    public void completeReplicaSetNetworkConfig(ShrinkReplicaResourceRequest request) {
        // vsw: no need to set vsw, common will set it base on original eni.

        // port
        List<Port> backendPorts = new ArrayList<>();
        backendPorts.add(new Port().name(PodDefaultConstants.ACCESS_PORT_NAME).value(PodDefaultConstants.POD_DEFAULT_PORT));  // 这里指定3306即可

        request.setBackendPorts(backendPorts);
        request.setPortPolicy(PodDefaultConstants.PORT_POLICY_CONTAINER);
        // eni
        request.setRamEni(true);
        request.setUpdateNetworkUniqueMark(true);
        // not reuse eni.
    }

    /**
     * 变配场景，配置rund pod级别参数
     *
     * @param request
     * @param replica
     * @throws Exception
     */
    public void completeReplicaNetworkConfig(ModifyReplicaResourceRequest request, Replica replica) throws Exception {
        // VSwitch
        Eni eni = getEniByReplica(replica);
        if (eni == null) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        request.setVswitchID(eni.getVswitchId());

        // portPolicy
        request.setPortPolicy(PodDefaultConstants.PORT_POLICY_CONTAINER);

        // port
        List<Port> backendPorts = new ArrayList<>();
        backendPorts.add(new Port().name(PodDefaultConstants.ACCESS_PORT_NAME).value(PodDefaultConstants.POD_DEFAULT_PORT));  // 这里指定3306即可
        request.setBackendPorts(backendPorts);
    }

    /**
     * 云盘大版本升级用
     *
     * @param request
     * @param replica
     * @throws Exception
     */
    public void completeReplicaNetworkConfig(ReplicaResourceRequest request, Replica replica) throws Exception {
        // VSwitch
        Eni eni = getEniByReplica(replica);
        if (eni == null) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        request.setVswId(eni.getVswitchId());

        // portPolicy
        request.setPortPolicy(PodDefaultConstants.PORT_POLICY_CONTAINER);

        // port
        List<Port> backendPorts = new ArrayList<>();
        backendPorts.add(new Port().name(PodDefaultConstants.ACCESS_PORT_NAME).value(PodDefaultConstants.POD_DEFAULT_PORT));  // 这里指定3306即可
        request.setBackendPorts(backendPorts);
    }

    /**
     * 新购/扩容/迁移可用区场景，入参含Avz相关信息
     *
     * @param request
     * @param avzInfo
     * @throws RdsException
     */
    public void completeReplicaNetworkConfig(ReplicaResourceRequest request, AVZInfo avzInfo) throws RdsException {
        // VSwitch
        if (avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
            MultiAVZExParamDO multiAVZExParamDO = avzInfo.getMultiAVZExParamDO();
            AvailableZoneInfoDO avz = multiAVZExParamDO.getAvailableZoneInfoList().stream().filter(availableZoneInfoDO -> availableZoneInfoDO.getRole().equals(request.getRole())).findFirst().get();
            request.setVswId(avz.getVSwitchID());
        } else {
            request.setVswId(avzInfo.getMasterVSwitchId());
        }

        // portPolicy
        request.setPortPolicy(PodDefaultConstants.PORT_POLICY_CONTAINER);

        // port
        List<Port> backendPorts = new ArrayList<>();
        backendPorts.add(new Port().name(PodDefaultConstants.ACCESS_PORT_NAME).value(PodDefaultConstants.POD_DEFAULT_PORT));  // 这里指定3306即可
        request.setBackendPorts(backendPorts);
    }

    /**
     * 变配场景，配置rund 实例级别参数
     *
     * @param request
     */
    public void completeReplicaSetNetworkConfig(ModifyReplicaSetResourceRequest request) {
        // ramEni
        request.setRamEni(true);
        // instance_port冲突解决
        request.setUpdateNetworkUniqueMark(true);
        // replicaSet
        request.setEniMode(ModifyReplicaSetResourceRequest.EniModeEnum.PASSTHROUGH);
    }

    /**
     * 配置rund 实例级别参数
     *
     * @param request
     */
    public void completeReplicaSetNetworkConfig(ReplicaSetResourceRequest request) {
        // ramEni
        request.setRamEni(true);
        // instance_port冲突解决
        request.setUpdateNetworkUniqueMark(true);
        // replicaSet
        request.setEniMode(ReplicaSetResourceRequest.EniModeEnum.PASSTHROUGH);
    }


    public Eni getEniByReplica(Replica replica) throws Exception {
        String requestId = RequestSession.getRequestId();
        boolean isEniMode = !(replica.getLinkIp() != null && replica.getLinkIp().equalsIgnoreCase(replica.getCtrlIp()));
        if (isEniMode) {
            EcsHost ecsHostByHostName = dBaasMetaService.getDefaultClient().getEcsHostByHostName(requestId, replica.getHostName(), null);
            EniListResult eniListResult = dBaasMetaService.getDefaultClient().listEcsEnis(requestId, ecsHostByHostName.getEcsInsId());
            if (Objects.nonNull(eniListResult) && CollectionUtils.isNotEmpty(eniListResult.getItems())) {
                Vpod vpod = dBaasMetaService.getDefaultClient().getReplicaVpod(requestId, replica.getId(), null, null);
                return eniListResult.getItems().stream().filter(item -> vpod.getVpodId().equalsIgnoreCase(item.getVpodId())).findAny().orElse(null);
            } else {
                logger.info("cannot find eni for host {}", replica.getHostName());
                return null;
            }
        } else {
            logger.info("Replica {} is not EniMode");
            return null;
        }
    }

    private NodeLabel createNodeLabel(String key, String value) {
        NodeLabel label = new NodeLabel();
        label.setKey(key);
        label.setValue(value);
        return label;
    }

}

