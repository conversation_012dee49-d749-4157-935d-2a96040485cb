package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointGroup;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointService;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointServiceFactoryImpl;
import com.aliyun.dba.resource.dataobject.IpResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeDBInstanceEndpointsImpl")
@Slf4j
public class DescribeDBInstanceEndpointsImpl implements IAction {

    @Resource
    private EndPointServiceFactoryImpl endpointFactory;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private ResourceService resourceService;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private CustinsParamService custinsParamService;

    @Resource
    private CustinsService custinsService;

    @Resource
    private ReplicaSetService replicaSetService;
    
    private final Map<Integer, String> ipType2String;

    {
        this.ipType2String = new HashMap<>();
        ipType2String.put(0, "Public");
        ipType2String.put(2, "Private");
    }


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        custins = mysqlParamSupport.getAndCheckCustInstance(params);

        Map<String, Object> rtn = new HashMap<>();
        try {  // replicaSet -> endpoint -> connectionString
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);  // use DBInstanceName
            EndPointService endPointService = endpointFactory.getService(replicaSet.getConnType());
            String replicaSetName = replicaSet.getName();
            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            List<EndpointGroup> endpointGroupList = endPointService.getAllEndpointGroups(requestId, replicaSetName).stream().filter(eg -> eg.getUserVisible()).collect(Collectors.toList());
            String endpointId = paramSupport.getParameterValue(params, "EndpointId");
            if (endpointId != null) {
                endpointGroupList = endpointGroupList.stream().filter(eg -> eg.getGroupName().equals(endpointId)).collect(Collectors.toList());
            }
            rtn.put("DBInstanceID", custins.getId());
            rtn.put("DBInstanceName", replicaSetName);
            rtn.put("RequestId", requestId);

            // 对返回值排序，primary 放最前面
            endpointGroupList.sort(Comparator.comparingInt(o -> o.getType().toCharArray()[0]));

            // endpoints
            List<Map<String, Object>> dbInstanceEndpoints = new ArrayList<>();
            for (EndpointGroup endpointGroup : endpointGroupList) {
                Map<String, Object> endpointInfo = new HashMap<>();
                endpointInfo.put("EndpointID", endpointGroup.getGroupName());
                endpointInfo.put("EndpointType", endpointGroup.getType());
                endpointInfo.put("EndpointDescription", endpointGroup.getDescription());

                // Nodes
                // 区分 Primary 与 Readonly, Custom, Custom_Readonly_Group
                endpointInfo.put("NodeItems", endPointService.getMysqlClusterNodes(requestId, endpointGroup, replicaSetName));

                // Address
                List<Map<String, Object>> addressItems = new ArrayList<>();

                List<CustinsConnAddrDO> custinsConnAddrDOList = endPointService.getEndpoints(requestId, endpointGroup.getId(), replicaSet.getId().intValue());
                custinsConnAddrDOList.removeIf(custinsConnAddr -> custinsConnAddr.getUserVisible() == 0);

                for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrDOList) {
                    Map<String, Object> addressInfo = new HashMap<>();
                    addressInfo.put("IPType", ipType2String.get(custinsConnAddr.getNetType()));  // Integer
                    addressInfo.put("VPCID", custinsConnAddr.getVpcId());
                    addressInfo.put("VSwitchID", custinsConnAddr.getVswitchId());

                    // Upgradeable
                    IpResourceDO ipResource = null;
                    if (custinsConnAddr.getVip() != null) {
                        ipResource = resourceService.getIpResourceByIpAndVpc(custinsConnAddr.getVip(), custinsConnAddr.getVpcId());
                        addressInfo.put("VSwitchID", ipResource.getvSwitchId());  // custinsConnAddr 内的数据拿不到 VSwitchId
                        addressInfo.put("VPCInstanceID", ipResource.getVpcInstanceId());
                    }
                    addressInfo.put("Upgradeable", (ipResource != null && CustinsSupport.STATUS_UPGRADEABLE.equals(ipResource.getUpgradeStatus())) ? 1 : 0);

                    addressInfo.put("ConnectionString", custinsConnAddr.getConnAddrCust());
                    addressInfo.put("IPAddress", custinsConnAddr.getVip());
                    addressInfo.put("Port", custinsConnAddr.getVport());

                    addressItems.add(addressInfo);
                }
                endpointInfo.put("AddressItems", addressItems);

                dbInstanceEndpoints.add(endpointInfo);
            }
            rtn.put("DBInstanceEndpoints", dbInstanceEndpoints);

            CustinsParamDO securityIPMode = custinsParamService.getCustinsParam(replicaSet.getId().intValue(), CustinsParamSupport.SECURITY_IP_MODE);
            String ipMode = ParamConstants.SECURITY_IPMODE_NORMAL;
            if (securityIPMode != null && ParamConstants.SECURITY_IPMODE_SAFETY.equals(securityIPMode.getValue())) {
                ipMode = ParamConstants.SECURITY_IPMODE_SAFETY;
            }
            rtn.put("SecurityIPMode", ipMode);

            rtn.put("DBInstanceName", replicaSetName);
            String ipVersion = "ipv4";
            if (String.valueOf(custinsService.getIpVersionByCustinsId(custins.getId())).equals("ipv6")) {
                ipVersion = "all";
            }
            rtn.put("IPVersion", ipVersion);

            return rtn;
        } catch (ApiException ex) {
            log.error("CreateDBInstanceEndpoint, Api called failed: ", ex);
            throw new RdsException(ErrorCode.API_CALLING_FAILED, "Meta db calling failed");
        } catch (RdsException ex) {
            log.error("CreateDBInstanceEndpoint failed: ", ex);
            throw ex;
        } catch (Exception e) {
            log.error("CreateDBInstanceEndpoint Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
