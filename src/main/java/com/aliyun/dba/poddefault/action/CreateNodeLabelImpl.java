package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.model.NodeLabel;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateNodeLabelImpl")
public class CreateNodeLabelImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateNodeLabelImpl.class);

    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private PodTemplateHelper podTemplateHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            String hostName = parameterHelper.getParameterValue(ParamConstants.HOST_NAME);
            Validate.notEmpty(hostName,"null hostName");
            String key = parameterHelper.getParameterValue("Key");
            String value = parameterHelper.getParameterValue("Value");

            List<NodeLabel> nodeLabelList = podTemplateHelper.getNodeLabelFromParams(key, value);
            commonProviderService.getDefaultApi().labelNode(requestId, nodeLabelList,hostName);

            Map<String, Object> data = new HashMap<>();
            data.put("HostName", hostName);
            return data;
        } catch (Exception ex) {
            logger.error(requestId + " Create NodeLabel failed: " + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
