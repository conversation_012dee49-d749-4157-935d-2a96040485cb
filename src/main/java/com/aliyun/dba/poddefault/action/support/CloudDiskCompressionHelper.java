package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.UserGrayService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.aliyun.dba.poddefault.action.support.PodCommonSupport.isServerless;
import static com.aliyun.dba.poddefault.action.support.PodCommonSupport.isSingleTenant;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_PARAM;

@Slf4j
@Service
public class CloudDiskCompressionHelper {
    @Resource
    private ResourceService resourceService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private UserGrayService userGrayService;

    public static final String LIMIT_REASON = "limitReason";
    public static final String SUPPORT_COMPRESSION = "supportCompression";
    public static final String SUPPORT_COMPRESSION_CHANGE = "supportCompressionChange";
    public static final String COMPRESSION_MODE = "compressionMode";
    public static final String COMPRESSION_RATIO = "compressionRatio";
    public static final String CUSTINS_PARAM_COMPRESSION_MODE = "compression_mode";
    public static final String CUSTINS_PARAM_COMPRESSION_RATIO = "compress_ratio_adjust";
    public static final String CUSTINS_PARAM_DISK_SIZE_BEFORE_COMPRESSION = "disk_size_before_compression";
    public static final String CUSTINS_PARAM_TMP_DISK_SIZE_BEFORE_COMPRESSION = "tmp_disk_size_before_compression";
    public static final String COMPRESSION_MODE_OFF = "off";
    public static final String COMPRESSION_MODE_ON = "on";
    public static final Double COMPRESSION_RATIO_DEFAULT = 1.0;
    public static final String VALUE_BLANK_REASON = "blank reason";
    public static final String TASK_KEY_MODIFY_COMPRESSION = "modify_cloud_disk_compression";




    private static final String RESOURCE_KEY_CLOUD_DISK_COMPRESSION_LIMIT = "CLOUD_DISK_COMPRESSION_LIMIT";
    private static final String RESOURCE_KEY_RDS_REGION_COMPRESSION_CONFIG = "RDS_REGION_COMPRESSION_CONFIG";
    private static final String RESOURCE_KEY_RDS_UID_COMPRESSION_CONFIG = "RDS_UID_COMPRESSION_CONFIG";

    private static final String KEY_COMPRESSION_MIN_DISK_SIZE_GB = "compressionMinDiskSizeGB";
    private static final String KEY_COMPRESSION_MAX_DISK_SIZE_GB = "compressionMaxDiskSizeGB";
    private static final String KEY_LIMIT_SINGLE_TENANT = "limitSingleTenant";
    private static final String KEY_PRIORITY = "priority";
    private static final String KEY_SUPPORT_STORAGE_TYPES = "supportStorageTypes";
    private static final String KEY_DEFAULT = "default";
    private static final String KEY_MIN = "min";
    private static final String KEY_MAX = "max";

    private static final String doubleRegex = "-?\\d+(\\.\\d+)?";


    @Getter
    public static enum CompressionRatioKeyType {
        LOCAL_COMPRESSION_RATIO("local_compression_ratio"),
        CLOUD_COMPRESSION_RATIO("cloud_compression_ratio");

        private final String key;

        CompressionRatioKeyType(String key) {
            this.key = key;
        }

    }


    private static final Cache<String, Map<String, CloudDiskCompressionLimit>> cloudDiskCompressionLimitCache = CacheBuilder.newBuilder()
            .maximumSize(2048)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    private static final Cache<String, JSONObject> compressionRatioCache = CacheBuilder.newBuilder()
            .maximumSize(2048)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();


    public Map<String, Object> checkCompressionSupportLimit(String requestId, InstanceLevel instanceLevel, String dbEngine, String uid, String regionId, Long diskSizeGB, String storageType, boolean isThrowException) throws RdsException {

        String limitReason = "blank reason";
        boolean isSupport = true;
        CloudDiskCompressionLimit cloudDiskCompressionLimit = getCloudDiskCompressionLimit(uid);
        log.info("{} checkCompressionSupportLimit, uid [{}] ,cloudDiskCompressionLimit [{}]", requestId, uid, cloudDiskCompressionLimit);
        // only support MySQL
        if (StringUtils.isNotBlank(dbEngine) && !StringUtils.equalsIgnoreCase("MySQL", dbEngine)) {
            log.warn("{} ins db engine {}, and compression only support MySQL .", requestId, dbEngine);
            limitReason = "EngineLimit";
            isSupport = false;
        }
        //not support serverless
        if (isSupport && Objects.nonNull(instanceLevel) && isServerless(instanceLevel)) {
            log.warn("{} compression not support serverless, category is [{}] .", requestId, instanceLevel.getCategory());
            limitReason = "ServerlessLimit";
            isSupport = false;
        }
        //not support essd, only support cloud_auto
        if (isSupport && StringUtils.isNotBlank(storageType) && !cloudDiskCompressionLimit.getSupportStorageTypes().contains(storageType)) {
            log.warn("{} compression only support cloud_auto storage type, current storage type is [{}] .", requestId, storageType);
            limitReason = "StorageTypeLimit";
            isSupport = false;
        }
        // only support SingleTenant
        if (isSupport && Objects.nonNull(instanceLevel) && cloudDiskCompressionLimit.limitSingleTenant && !isSingleTenant(instanceLevel)) {
            log.warn("{} compression only support single tenant , isolation type is [{}] .", requestId, instanceLevel.getIsolationType());
            limitReason = "IsolationLimit";
            isSupport = false;
        }
        if (isSupport && Objects.nonNull(diskSizeGB) && !(cloudDiskCompressionLimit.compressionMinDiskSizeGB <= diskSizeGB && diskSizeGB <= cloudDiskCompressionLimit.compressionMaxDiskSizeGB)) {
            log.warn("{} compression disk sizeGB [{}] not support [{}, {}].", requestId, diskSizeGB, cloudDiskCompressionLimit.compressionMinDiskSizeGB, cloudDiskCompressionLimit.compressionMaxDiskSizeGB);
            limitReason = "DiskSizeLimit";
            isSupport = false;
        }
        // gray policy
        if (isSupport && StringUtils.isNotBlank(uid) && StringUtils.isNotBlank(regionId) && !userGrayService.isHitCompression(requestId, uid, regionId)) {
            log.warn("requestId : {}, uid:{}, regionId: {}, not support compression", requestId, uid, regionId);
            limitReason = "BlockLimit";
            isSupport = false;
        }

        // throw Exception
        if (isThrowException && !isSupport) {
            // todo: modify error code
            String errMsg = String.format("cloud compression limited, limit reason: [%s]", limitReason);
            log.warn(errMsg);
            throw new RdsException(ErrorCode.INVALID_PARAM, limitReason);
        }
        Map<String, Object> result = new HashMap<>();
        result.put(LIMIT_REASON, limitReason);
        result.put(SUPPORT_COMPRESSION, isSupport);
        return result;
    }

    /**
     * resource:
     * key:CLOUD_DISK_COMPRESSION_LIMIT
     * value: {
     * "default": {
     * "compressionMaxDiskSizeGB": 25000,
     * "compressionMinDiskSizeGB": 1000
     * "priority": 1
     * "limitSingleTenant": true
     * },
     * "uid":{}
     * }
     *
     * @param uid
     * @return
     */

    private CloudDiskCompressionLimit getCloudDiskCompressionLimit(String uid) {
        Map<String, CloudDiskCompressionLimit> cloudDiskCompressionLimitMap = new ConcurrentHashMap<>();
        try {
            cloudDiskCompressionLimitMap = cloudDiskCompressionLimitCache.get(RESOURCE_KEY_CLOUD_DISK_COMPRESSION_LIMIT, new Callable<Map<String, CloudDiskCompressionLimit>>() {
                @Override
                public Map<String, CloudDiskCompressionLimit> call() throws Exception {
                    return getLimitMapFromResource();
                }
            });
        } catch (ExecutionException e) {
            log.warn("get resource RESOURCE_KEY_CLOUD_DISK_COMPRESSION_LIMIT failed, errorMsg: {}", e.getMessage());
        }
        List<String> resourceValueKeys = new ArrayList<>(Collections.singletonList(KEY_DEFAULT));
        if (StringUtils.isNotBlank(uid)) {
            resourceValueKeys.add(uid);
        }
        return getMaxPriorityCloudDiskCompressionLimit(cloudDiskCompressionLimitMap, resourceValueKeys);

    }

    private CloudDiskCompressionLimit getMaxPriorityCloudDiskCompressionLimit(Map<String, CloudDiskCompressionLimit> limitMap, List<String> resourceValueKeys) {
        PriorityBlockingQueue<CloudDiskCompressionLimit> candidateLimitList = new PriorityBlockingQueue<>(11, (o1, o2) -> o2.getPriority() - o1.getPriority());
        if (CollectionUtils.isNotEmpty(resourceValueKeys) && MapUtils.isNotEmpty(limitMap)) {
            log.info("resourceValueKeys [{}] not null", resourceValueKeys);
            for (String resourceKey : resourceValueKeys) {
                if (limitMap.containsKey(resourceKey)) {
                    candidateLimitList.add(limitMap.get(resourceKey));
                }
            }
        }
        if (candidateLimitList.isEmpty()) {
            log.warn("limitMap and resourceValueKeys not match, candidateLimitList is empty, return default limit");
            return new CloudDiskCompressionLimit();
        }
        return candidateLimitList.peek();
    }

    private Map<String, CloudDiskCompressionLimit> getLimitMapFromResource() {
        ResourceDO resourceDO = resourceService.getResourceByResKey(RESOURCE_KEY_CLOUD_DISK_COMPRESSION_LIMIT);
        Map<String, CloudDiskCompressionLimit> result = new ConcurrentHashMap<>();
        if (Objects.nonNull(resourceDO) && StringUtils.isNotBlank(resourceDO.getRealValue())) {
            String resValue = resourceDO.getRealValue();
            JSONObject jsonObject = JSONObject.parseObject(resValue);
            for (String key : jsonObject.keySet()) {
                String limit = jsonObject.getString(key);
                if (StringUtils.isBlank(limit)) {
                    continue;
                }
                result.put(key, parseLimit(limit));
            }
        }
        return result;
    }


    private CloudDiskCompressionLimit parseLimit(String limit) {

        CloudDiskCompressionLimit cloudDiskCompressionLimit = new CloudDiskCompressionLimit();
        if (StringUtils.isBlank(limit)) {
            return cloudDiskCompressionLimit;
        }
        JSONObject limitJson = JSONObject.parseObject(limit);
        if (Objects.isNull(limitJson)) {
            return cloudDiskCompressionLimit;
        }
        Optional.ofNullable(limitJson.getLong(KEY_COMPRESSION_MAX_DISK_SIZE_GB))
                .ifPresent(cloudDiskCompressionLimit::setCompressionMaxDiskSizeGB);

        Optional.ofNullable(limitJson.getLong(KEY_COMPRESSION_MIN_DISK_SIZE_GB))
                .ifPresent(cloudDiskCompressionLimit::setCompressionMinDiskSizeGB);

        Optional.ofNullable(limitJson.getInteger(KEY_PRIORITY))
                .ifPresent(cloudDiskCompressionLimit::setPriority);

        Optional.ofNullable(limitJson.getBoolean(KEY_LIMIT_SINGLE_TENANT))
                .ifPresent(cloudDiskCompressionLimit::setLimitSingleTenant);

        Optional.ofNullable(limitJson.getString(KEY_SUPPORT_STORAGE_TYPES))
                .map(String::trim) // 去除前后空格
                .filter(StringUtils::isNotBlank)
                .map(s -> Arrays.stream(s.split("\\s*,\\s*")) // 处理逗号分隔符及其周围的空格
                        .filter(StringUtils::isNotBlank) // 过滤掉空字符串
                        .distinct().collect(Collectors.toList()))
                .ifPresent(cloudDiskCompressionLimit::setSupportStorageTypes);

        return cloudDiskCompressionLimit;
    }


    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    @EqualsAndHashCode
    static class CloudDiskCompressionLimit {
        Long compressionMinDiskSizeGB = 1025L;
        Long compressionMaxDiskSizeGB = 25 * 1000L;
        Boolean limitSingleTenant = true;
        Integer priority = 0;
        List<String> supportStorageTypes = Collections.singletonList(CustinsSupport.STORAGE_TYPE_CLOUD_AUTO);
    }


    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    @EqualsAndHashCode
    static class DiskCompressionRatio {
        Double compressionRatioDefault = 2.0;
        Double compressionRatioMin = 1.78;
        Double compressionRatioMax = 2.5;
    }


    public Double getCompressionRatio(String requestId, String replicaSetName, String uid, String regionId, CompressionRatioKeyType type) throws ApiException {
        // if replicaSetName is blank, use default value
        if (StringUtils.isBlank(replicaSetName) && Objects.nonNull(type)) {
            return getCompressionRatioFromResource(uid, regionId, type);
        }
        // if replicaSetName is not null, use srcCompressionRatio
        String srcCompressionRatio = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                requestId, replicaSetName, CUSTINS_PARAM_COMPRESSION_RATIO);
        //check null, if compressionRatio is null or format is invalid
        if (StringUtils.isNotBlank(srcCompressionRatio) && srcCompressionRatio.matches(doubleRegex)) {
            return Double.parseDouble(srcCompressionRatio);
        }
        log.warn("meta data srcCompressionRatio is blank, use COMPRESSION_RATIO_DEFAULT [{}]", COMPRESSION_RATIO_DEFAULT);
        return COMPRESSION_RATIO_DEFAULT;

    }

    public Double getCompressionRatioFromResource(String uid, String regionId, CompressionRatioKeyType type) {
        DiskCompressionRatio uidRatio = getCompressionRatioResourceUid(uid, type);
        if (Objects.nonNull(uidRatio)) {
            return uidRatio.getCompressionRatioDefault();
        }
        DiskCompressionRatio regionRatio = getCompressionRatioResourceRegion(regionId, type);
        if (Objects.nonNull(regionRatio)) {
            return regionRatio.getCompressionRatioDefault();
        }
        return new DiskCompressionRatio().getCompressionRatioDefault();
    }

    private DiskCompressionRatio getCompressionRatioResourceRegion(String regionId, CompressionRatioKeyType type) {
        if (Objects.isNull(type)) {
            return null;
        }
        regionId = StringUtils.isBlank(regionId) ? KEY_DEFAULT : regionId;
        JSONObject result = getJSONObjectFromResourceByKey(RESOURCE_KEY_RDS_REGION_COMPRESSION_CONFIG);
        return getCompressionRatioByKey(result, regionId, type);
    }

    private DiskCompressionRatio getCompressionRatioResourceUid(String uid, CompressionRatioKeyType type) {
        if (Objects.isNull(type) || StringUtils.isBlank(uid)) {
            return null;
        }
        JSONObject result = getJSONObjectFromResourceByKey(RESOURCE_KEY_RDS_UID_COMPRESSION_CONFIG);
        return getCompressionRatioByKey(result, uid, type);
    }

    private DiskCompressionRatio getCompressionRatioByKey(JSONObject result, String key, CompressionRatioKeyType type) {
        DiskCompressionRatio uidRatio = null;
        try {
            uidRatio = Optional.ofNullable(result)
                    .map(map -> map.getOrDefault(key, null))
                    .map(JSONObject.class::cast)
                    .map(jsonObject -> jsonObject.getJSONObject(type.getKey()))
                    .map(this::parseRatio)
                    .orElse(null);
        } catch (Exception e) {
            log.warn("Error [{}] occurred while getCompressionRatioByKey JSON: [{}], Key: [{}], Type: [{}]", e.getMessage(), result, key, type.getKey());
        }
        return uidRatio;
    }

    private JSONObject getJSONObjectFromResourceByKey(String key) {
        JSONObject result = new JSONObject();
        try {
            result = compressionRatioCache.get(key, () -> {
                JSONObject jsonObject = new JSONObject();
                ResourceDO resourceDO = resourceService.getResourceByResKey(key);
                if (Objects.nonNull(resourceDO) && StringUtils.isNotBlank(resourceDO.getRealValue())) {
                    String resValue = resourceDO.getRealValue();
                    jsonObject = JSONObject.parseObject(resValue);
                }
                return jsonObject;
            });
        } catch (ExecutionException e) {
            log.warn("get resource RESOURCE_KEY_RDS_UID_COMPRESSION_CONFIG failed, errorMsg: {}", e.getMessage());
        }
        return result;
    }

    private DiskCompressionRatio parseRatio(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        DiskCompressionRatio diskCompressionRatio = new DiskCompressionRatio();
        Optional.ofNullable(jsonObject.getDouble(KEY_MIN))
                .ifPresent(diskCompressionRatio::setCompressionRatioMin);
        Optional.ofNullable(jsonObject.getDouble(KEY_MAX))
                .ifPresent(diskCompressionRatio::setCompressionRatioMax);
        Optional.ofNullable(jsonObject.getDouble(KEY_DEFAULT))
                .ifPresent(diskCompressionRatio::setCompressionRatioDefault);
        return diskCompressionRatio;
    }

    /**
     * compression ratio parameter: compressionRatio
     * 1. paramCompressionRatio
     * 2. srcCompressionRatio
     * 3. default value: 1.0
     * <p>
     * return double value
     */
    public Double getCompressionRatio(String requestId, String replicaSetName, String paramCompressionRatio) {

        // first priority is paramCompressionRatio
        if (checkCompressionRatioStr(paramCompressionRatio)) {
            return Double.parseDouble(paramCompressionRatio);
        }
        // if replicaSetName is blank, use default value
        if (StringUtils.isBlank(replicaSetName)) {
            return COMPRESSION_RATIO_DEFAULT;
        }
        // if replicaSetName is not null, use srcCompressionRatio
        String srcCompressionRatio = null;
        try {
            srcCompressionRatio = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                    requestId, replicaSetName, CUSTINS_PARAM_COMPRESSION_RATIO);
        } catch (ApiException e) {
            log.warn("requestId: {}, replicaSetName: {}, get compression ratio failed, use default value 1.0", requestId, replicaSetName);
        }
        return checkCompressionRatioStr(srcCompressionRatio) ? Double.parseDouble(srcCompressionRatio) : COMPRESSION_RATIO_DEFAULT;
    }


    public boolean checkCompressionRatioStr(String compressionRatioStr) {
        return StringUtils.isNotBlank(compressionRatioStr) && compressionRatioStr.matches(doubleRegex);
    }


    /**
     * compression mode parameter: compressionMode
     * 1. paramCompressionMode
     * 2. srcCompressionMode
     * 3. default value: off
     *
     * return value [on/off]
     */
    public String getCompressionMode(String requestId, String replicaSetName, String paramCompressionMode) throws ApiException {
        // first priority is paramCompressionMode
        if (checkCompressionModeStr(paramCompressionMode)) {
            return paramCompressionMode;
        }
        // if replicaSetName is blank, use default value: off
        if (org.apache.commons.lang3.StringUtils.isBlank(replicaSetName)) {
            return COMPRESSION_MODE_OFF;
        }
        // if replicaSetName is not null, use srcCompressionMode
        String srcCompressionMode = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                requestId, replicaSetName, CUSTINS_PARAM_COMPRESSION_MODE);

        return checkCompressionModeStr(srcCompressionMode) ? srcCompressionMode : COMPRESSION_MODE_OFF;
    }

    public static boolean isCompressionModeOn(String compressionMode) {
        return StringUtils.isNotBlank(compressionMode) && StringUtils.equals(compressionMode, COMPRESSION_MODE_ON);
    }

    public static boolean isCompressionModeOff(String compressionMode) {
        return StringUtils.isNotBlank(compressionMode) && StringUtils.equals(compressionMode, COMPRESSION_MODE_OFF);
    }

    public static Integer convertDiskSizeGBToMB(Integer diskSizeGB) {
        if (Objects.isNull(diskSizeGB) || diskSizeGB < 0) {
            return 0;
        }
        return diskSizeGB * 1024;
    }

    public static Integer getPhysicalSize(Integer logicalSize, Double compressionRatio) {
        if (Objects.isNull(logicalSize) || Objects.isNull(compressionRatio)) {
            return logicalSize;
        }
        BigDecimal logicalSizeBD = BigDecimal.valueOf(logicalSize);
        BigDecimal compressionRatioBD = BigDecimal.valueOf(compressionRatio);
        BigDecimal fiveBD = BigDecimal.valueOf(5);

        BigDecimal result = logicalSizeBD.divide(compressionRatioBD, 10, RoundingMode.HALF_UP)
                .divide(fiveBD, 0, RoundingMode.DOWN)
                .multiply(fiveBD);
        return result.intValue();
    }

    public static Integer getLogicalSize(Integer physicalSize, Double compressionRatio) {
        if (Objects.isNull(physicalSize) || Objects.isNull(compressionRatio)) {
            return physicalSize;
        }
        BigDecimal physicalSizeBD = BigDecimal.valueOf(physicalSize);
        BigDecimal compressionRatioBD = BigDecimal.valueOf(compressionRatio);
        BigDecimal fiveBD = BigDecimal.valueOf(5);

        BigDecimal result = physicalSizeBD.multiply(compressionRatioBD)
                .divide(fiveBD, 0, RoundingMode.UP)
                .multiply(fiveBD);
        return result.intValue();
    }

    private boolean checkCompressionModeStr(String compressionMode) {
        return StringUtils.isNotBlank(compressionMode) &&
                (StringUtils.equals(compressionMode, COMPRESSION_MODE_OFF) || StringUtils.equals(compressionMode, COMPRESSION_MODE_ON));
    }


    /**
     * check compression change limit, compression mode cannot change with other changes.
     *
     * @param compressionChange
     * @param changeLimits
     * @return
     */
    public Map<String, Object> checkCompressionChangeLimit(Boolean compressionChange, Map<String, Boolean> changeLimits) {
        Map<String, Object> result = new HashMap<>();
        String limitReason = VALUE_BLANK_REASON;
        boolean isSupport = true;
        if (!compressionChange || MapUtils.isEmpty(changeLimits)) {
            result.put(LIMIT_REASON, limitReason);
            result.put(SUPPORT_COMPRESSION_CHANGE, isSupport);
            return result;
        }
        for (Map.Entry<String, Boolean> entry : changeLimits.entrySet()) {
            if (Objects.nonNull(entry.getValue()) && entry.getValue()) {
                limitReason = String.format("%s-Limit", entry.getKey());
                isSupport = false;
                break;
            }
        }
        result.put(LIMIT_REASON, limitReason);
        result.put(SUPPORT_COMPRESSION_CHANGE, isSupport);
        return result;
    }


    public Map<String, Object> checkReadInsCompressionModeOn(String requestId, String primaryReplicaSetName, boolean isThrowException) throws ApiException, RdsException {
        Map<String, Object> result = new HashMap<>();
        String limitReason = VALUE_BLANK_REASON;
        boolean isSupport = true;

        List<ReplicaSet> readOnlyReplicaSetList =
                ObjectUtils.firstNonNull(
                        dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId, primaryReplicaSetName, ReplicaSet.InsTypeEnum.READONLY.toString()).getItems(),
                        new ArrayList<>());

        if (CollectionUtils.isEmpty(readOnlyReplicaSetList)) {
            result.put(LIMIT_REASON, limitReason);
            result.put(SUPPORT_COMPRESSION_CHANGE, isSupport);
            return result;
        }
        for (ReplicaSet readOnlyReplicaSet : readOnlyReplicaSetList) {
            String readOnlyCompressionMode = getCompressionMode(requestId, readOnlyReplicaSet.getName(), null);
            if (isCompressionModeOff(readOnlyCompressionMode)) {
                isSupport = false;
                limitReason = String.format("ro [%s] compression is off", readOnlyReplicaSet.getName());
                break;
            }
        }
        if (isThrowException && !isSupport) {
            // todo: modify error code
            String errMsg = String.format("cloud compression limited, limit reason: [%s]", limitReason);
            log.warn(errMsg);
            throw new RdsException(INVALID_PARAM, limitReason);
        }
        result.put(LIMIT_REASON, limitReason);
        result.put(SUPPORT_COMPRESSION_CHANGE, isSupport);
        return result;
    }


    public static void invalidateCache(String cacheName, String key) {
        switch (cacheName) {
            case "cloudDiskCompressionLimitCache":
                cloudDiskCompressionLimitCache.invalidate(key);
                break;
            case "compressionRatioCache":
                compressionRatioCache.invalidate(key);
                break;
            default:
                throw new IllegalArgumentException("Invalid cache name: " + cacheName);
        }
    }


}
