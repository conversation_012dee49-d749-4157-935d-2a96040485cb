package com.aliyun.dba.poddefault.action;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.*;
import com.aliyun.dba.base.response.backup.UserOssBackupFileRecordResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.service.AligroupCreateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.AliyunCreateDBInstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.aliyun.dba.base.parameter.backup.CreateOssBakRestoreParam;
import com.aliyun.dba.base.response.backup.OssBakRestoreResponse;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.response.backup.UserOssBackupFileRecordResponse;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_STORAGE_TYPE;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultListUserBackupFileRecordImpl")
public class ListUserBackupFileRecordImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ListUserBackupFileRecordImpl.class);

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    private AliyunCreateDBInstanceService aliyunDBInstanceService;
    @Resource
    private AligroupCreateDBInstanceService aligroupDBInstanceService;
    @Resource
    private AligroupService aligroupService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private BackupService backupService;
    @Resource
    private WorkFlowService workFlowService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        Map<String, Object> data = new HashMap<>();
        String bid = paramSupport.getAndCheckBID(params);
        String uid = paramSupport.getUID(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String instanceNames = paramSupport.getParameterValue(params, "BackupIds");
        String comment = paramSupport.getParameterValue(params, ParamConstants.COMMENT);
        String ossUrl = paramSupport.getParameterValue(params, "OssUrl");

        OssBakRestoreParam baseParam = OssBakRestoreParam.builder()
                .user_id(bid)
                .uid(uid)
                .requestId(requestId)
                .instanceName(instanceNames)
                .comment(comment)
                .ossUrl(ossUrl)
                .build();

        try {
            //todo 查询任务状态等信息，回填至user_backup_file_record
            // Object taskinfo = workFlowService.getTaskQueueControllerApi().batchGetByTaskIdsUsingGET("3441","b3a55457-e191-4456-bbc4-1855d715314b");
            List<UserOssBackupFileRecordResponse> records= backupService.describeBakOssRestoreRecords(baseParam);
            data.put("records", records);
            return data;
        } catch (Exception e) {
            logger.error(requestId + " describeBakOssRestoreRecords: " + e.getMessage());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "describeBakOssRestoreRecords: " , e.getMessage()});
        }
    }
}

