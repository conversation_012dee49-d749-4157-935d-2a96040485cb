package com.aliyun.dba.poddefault.action.support.GAD;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.gdnmetaapi.model.GlobalConfig;
import com.aliyun.dba.base.common.exception.CheckException;
import com.aliyun.dba.base.lib.GdnMetaService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.HttpClientConfig;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.ram_inner.model.v20200109.CreateServiceLinkedRoleForOriginalCallerRequest;
import com.aliyuncs.ram_inner.model.v20200109.CreateServiceLinkedRoleForOriginalCallerResponse;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

@Service
public class SLRBaseService {
    @Resource
    private GdnMetaService gdnMetaService;

    private static final LogAgent logger = LogFactory.getLogAgent(SLRBaseService.class);

    public AssumeRoleWithServiceIdentityResponse createAssumeRole(String regionId, String accessKeyId, String secret, String roleArn, String roleSessionName, String callerParentId) throws Exception {

        // init client;
        DefaultProfile.addEndpoint("", "", "Sts", "sts-inner." + regionId + ".aliyuncs.com");
        IClientProfile profile = DefaultProfile.getProfile("", accessKeyId, secret);
        HttpClientConfig clientConfig = HttpClientConfig.getDefault();
        clientConfig.setReadTimeoutMillis(GadConstant.GAD_OPENAPI_READ_TIMEOUT);
        profile.setHttpClientConfig(clientConfig);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        final AssumeRoleWithServiceIdentityRequest request = new AssumeRoleWithServiceIdentityRequest();
        request.setMethod(MethodType.POST);
        request.setRoleArn(roleArn);
        request.setRoleSessionName(roleSessionName);
        request.setAssumeRoleFor(callerParentId); //描述：云帐号UID（即主账号UID），指明服务在替哪个云帐号扮演角色。

        final AssumeRoleWithServiceIdentityResponse response = client.getAcsResponse(request);
        return response;

    }


    public void createGadSlrForCallerUser(String requestId, String callerParentId, String callerUid,Map<String,Object> ramAuthParamsMap) throws Exception {
        //API调用参考http://ramdoc.alibaba.net/doc/open-api-reference/master/ram-api-reference/role/service-linked-role/create-service-linked-role-for-original-caller.html
        //获取GAD服务账号权限

        GlobalConfig gadSLRAccountCfg = this.gdnMetaService.getClient().getGlobalConfig(requestId,GadConstant.GAD_SLR_ACCOUNT);
        Map<String,String> gadSLRAccount = JSON.parseObject(gadSLRAccountCfg.getValue(),Map.class);
        //解密账号
        String gadAccessKey = Aes.decryptPassword(gadSLRAccount.get("accessKey"), GadConstant.GAD_SLR_ACCOUNT_PASSWORD_KEY);
        String gadAccessSecret = Aes.decryptPassword(gadSLRAccount.get("accessSecret"), GadConstant.GAD_SLR_ACCOUNT_PASSWORD_KEY);



        // 构造调用API的client
        String endpoint = "ram-inc-share.aliyuncs.com"; // RAM内部API的endpoint // RAM内部预发API endpoint，host绑定 ************* ram-inc-share.aliyuncs.com
        try {
            DefaultProfile.addEndpoint("", "", "ram-inner", endpoint);
        } catch (ClientException e) {
            // 设置endpoint失败，无法构造client，根据业务自身逻辑进行处理
            throw new Exception(e);
        }
        DefaultProfile profile = DefaultProfile.getProfile(
                "",      // 空字符串即可
                gadAccessKey,      // 服务账号子用户的ak id   GAD服务账号
                gadAccessSecret);     // 服务账号子用户的ak secret
        HttpClientConfig clientConfig = HttpClientConfig.getDefault();
        clientConfig.setReadTimeoutMillis(GadConstant.GAD_OPENAPI_READ_TIMEOUT);
        profile.setHttpClientConfig(clientConfig);
        IAcsClient client = new DefaultAcsClient(profile);


        // 构造参数，调用CreateServiceLinkedRoleForOriginalCaller
        String serviceName = "gad.rds.aliyuncs.com"; //gad-slr固定参数
        //partner：BID帐号（不支持）
        if(String.valueOf(ramAuthParamsMap.get("callerType")).equalsIgnoreCase("partner")){
            throw new CheckException("callerType is partner,not support SLR");
        }

        if(ramAuthParamsMap.get("callerRealIp") == null){
            logger.info(requestId + "[SLRBaseService] can not get callerRealIp, params:{}",ramAuthParamsMap.toString());
        }

        //取值同POP参数ak_mfa_present
        Boolean mfaPresent = String.valueOf(ramAuthParamsMap.get("ak_mfa_present")).equalsIgnoreCase("true");//false;
        Boolean callerRealSecurityTransport = String.valueOf(ramAuthParamsMap.get("callerRealSecurityTransport")).equalsIgnoreCase("true");

        CreateServiceLinkedRoleForOriginalCallerRequest request = new CreateServiceLinkedRoleForOriginalCallerRequest();
        // 设置API业务参数
        request.setServiceName(serviceName);
        request.setActionName("CreateServiceLinkedRoleForOriginalCaller");
        request.setOriginalCallerType(String.valueOf(ramAuthParamsMap.get("callerType")));
        request.setOriginalCallerUid(callerUid);
        request.setOriginalCallerParentId(callerParentId);
        request.setOriginalCallerRealIp(String.valueOf(ramAuthParamsMap.get("callerRealIp")));
        request.setOriginalCallerRealSecurityTransport(callerRealSecurityTransport);
        request.setOriginalMFAPresent(mfaPresent);
        if(Arrays.asList("AssumedRoleUser".toLowerCase(), "ImpersonatedUser".toLowerCase()).contains(String.valueOf(ramAuthParamsMap.get("callerType")).toLowerCase())){
            request.setOriginalAccessKeyId(String.valueOf(ramAuthParamsMap.get("stsAccessKeyId")));
            request.setOriginalSecurityToken(String.valueOf(ramAuthParamsMap.get("stsSecurityToken")));
        }

        logger.info(requestId + "SLRBaseService createSlrForCallerUser invoke request:{}",JSON.toJSONString(request));

        CreateServiceLinkedRoleForOriginalCallerResponse response = client.getAcsResponse(request);
        logger.info(requestId + "SLRBaseService createSlrForCallerUser invoke response:{}",JSON.toJSONString(response));

    }

}
