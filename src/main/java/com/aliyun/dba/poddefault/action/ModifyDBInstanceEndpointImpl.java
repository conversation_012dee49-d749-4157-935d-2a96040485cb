package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointGroup;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.adb_vip_manager_client.model.RealServerResponse;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointService;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointServiceFactoryImpl;
import com.aliyun.dba.poddefault.action.service.endpoint.EndpointWeightConfig;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceEndpointImpl")
@Slf4j
public class ModifyDBInstanceEndpointImpl implements IAction {
    @Resource
    private EndPointServiceFactoryImpl endpointFactory;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private ReplicaSetService replicaSetService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);  // use DBInstanceName
            EndPointService endPointService = endpointFactory.getService(replicaSet.getConnType());
            String replicaSetName = replicaSet.getName();
            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

            if (!MysqlParamSupport.isCluster(replicaSet.getCategory())) {  // 暂时限制只有 Cluster
                return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
            }
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            log.info("{} start modify endpoint", replicaSetName);

            Integer weightChangeNumber = 0;  // 0 -> no changes, 1 -> weight change, 2 -> replica change
            boolean isDescChanged = false;

            // data prepare
            String endpointId = paramSupport.getParameterValue(params, "DBInstanceEndpointId");
            String endpointDesc = paramSupport.getParameterValue(params, "DBInstanceEndpointDesc");
            String NodeItemsString = paramSupport.getParameterValue(params, "NodeItems");
            EndpointWeightConfig newWeightConfig = null;

            // pre-check
            EndpointGroup endpointGroup = endPointService.getAndCheckEndpointGroup(requestId, replicaSetName, endpointId);

            // Compare dbConfig with vipLinkInfo, ensure consistency
            this.compareConfigWithVipConfig(requestId, replicaSetName, endpointGroup, endPointService);

            // Compare dbConfig with newWeightConfig, decide to set or to replace
            if (endpointGroup.getType().equalsIgnoreCase(Endpoint.TypeEnum.PRIMARY.toString()) || endpointGroup.getType().equalsIgnoreCase(Endpoint.TypeEnum.NODE.toString())) {  // primary、直连 不支持修改 nodeItems
                NodeItemsString = null;
            }
            if (endpointDesc != null && !endpointDesc.equals(endpointGroup.getDescription())) {
                isDescChanged = true;
            }

            if (NodeItemsString != null) {
                Map<String, Object> config = new HashMap<>();
                config.put(EndpointWeightConfig.CONFIG_KEY, JSON.parseObject(NodeItemsString, List.class));
                newWeightConfig = JSON.parseObject(JSON.toJSONString(config), EndpointWeightConfig.class);
                if (newWeightConfig.getNodeWeights() != null && !newWeightConfig.getNodeWeights().isEmpty()) {
                    weightChangeNumber = endPointService.compareAndCheckWeightConfigs(requestId, replicaSetName, endpointGroup, newWeightConfig);
                }
                else {
                    throw new RdsException(ErrorCode.INVALID_NODEITEMS_BLANK);
                }
            }


            Object taskId = null;
            if (weightChangeNumber > 0) {
                // dispatch workflow task
                String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
                JSONObject taskParam = new JSONObject();
                taskParam.put(ParamConstants.REQUEST_ID, requestId);
                taskParam.put("endpointName", endpointId);
                taskParam.put("endpointDesc", endpointDesc);
                taskParam.put("nodeItems", JSON.toJSONString(newWeightConfig));
                taskParam.put("changeAction", weightChangeNumber);

                endPointService.inactiveReplicaSet(requestId, replicaSetName);
                taskId = workFlowService.dispatchTask("custins", replicaSetName, domain, PodDefaultConstants.TASK_MODIFY_INS_ENDPOINT, taskParam.toJSONString(), 0);
            }
            else {
                // update desc
                if (isDescChanged){
                    endpointGroup.setDescription(endpointDesc);
                    dBaasMetaService.getDefaultClient().updateEndpointGroup(requestId, replicaSetName, endpointGroup.getGroupName(), endpointGroup);
                }
            }

            // rtn
            Map<String, Object> rtn = new HashMap<>();
            rtn.put("RequestId", requestId);
            rtn.put("DBInstanceName", replicaSetName);
            rtn.put("DBInstanceEndpointId", endpointId);
            if (taskId != null) {
                rtn.put("TaskId", taskId);
            }
            return rtn;
        } catch (ApiException ex) {
            log.error("ModifyDBInstanceEndpoint, Api called failed: ", ex);
            throw new RdsException(ErrorCode.API_CALLING_FAILED);
        } catch (RdsException ex) {
            log.error("ModifyDBInstanceEndpoint failed: ", ex);
            throw ex;
        } catch (JSONException ex) {
            log.error("ModifyDBInstanceEndpoint, JSON parse failed: ", ex);
            return createErrorResponse(ErrorCode.INVALID_NODEITEMS_JSON_FORMAT);
        } catch (Exception e) {
            log.error("ModifyDBInstanceEndpoint Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 将 vip link 的信息同步到 metaDB，存在 weight 变化、replica 变化 两种情况
     */
    private void compareConfigWithVipConfig(String requestId, String replicaSetName, EndpointGroup endpointGroup, EndPointService endPointService) throws ApiException, RdsException, com.aliyun.dba.adb_vip_manager_client.ApiException {
        // primary not need to do check
        if (endpointGroup.getType().equalsIgnoreCase(Endpoint.TypeEnum.PRIMARY.toString())) {
            return;
        }

        EndpointWeightConfig weightConfig = endPointService.getWeightConfig(requestId, replicaSetName, endpointGroup.getId());
        boolean isWeightChanged = false;
        // data prepare
        Set<String> replicaSetNames = new HashSet<>();
        Set<String> replicaNames = new HashSet<>();
        Map<String, Integer> nodeId2Weight = new HashMap<>();
        for (EndpointWeightConfig.WeightConfig config : weightConfig.getNodeWeights()) {
            replicaSetNames.add(config.getDBInstanceId());
            replicaNames.add(config.getNodeId());
            nodeId2Weight.put(config.getNodeId(), config.getWeight());
        }
        Optional<Endpoint> endpointOptional = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSetName, null, null, null, null)
                .getItems().stream().filter(ep -> Objects.equals(ep.getEndpointGroupId(), endpointGroup.getId())).findFirst();
        if (!endpointOptional.isPresent()) {
            throw new RdsException(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
        }
        // vip link detail
        List<Replica> replicas = endPointService.getAllReplicasByNames(requestId, new ArrayList<>(replicaSetNames));

        List<RealServerResponse> validRealServers = endPointService.getRealServerDetail(requestId, replicaSetName, endpointOptional.get()).getData().getRealServers();
        Map<String, Integer> ip2Weight = new HashMap<>();
        for (RealServerResponse realServer : validRealServers) {
            ip2Weight.put(realServer.getIp(), realServer.getWeight());
        }
        // 是否存在同一个实例 ip 相同， 端口不同的情况
        List<Replica> validVipReplicas = replicas.stream().filter(replica -> ip2Weight.containsKey(replica.getLinkIp())).collect(Collectors.toList());
        Set<String> vipReplicaNames = new HashSet<>(validVipReplicas.stream().map(Replica::getName).collect(Collectors.toList()));

        // check
        if (replicaNames.toString().equals(vipReplicaNames.toString())) {  // replica 一致
            for (Replica replica : validVipReplicas) {
                if (!ip2Weight.get(replica.getLinkIp()).equals(nodeId2Weight.get(replica.getName()))) {  // weight change
                    nodeId2Weight.put(replica.getName(), ip2Weight.get(replica.getLinkIp()));
                    isWeightChanged = true;
                }
            }
            if (isWeightChanged) {  // update
                for (EndpointWeightConfig.WeightConfig config : weightConfig.getNodeWeights()) {
                    config.setWeight(nodeId2Weight.get(config.getNodeId()));
                }
                Map<String, String> labels = endpointGroup.getLabels();
                labels.put(EndpointWeightConfig.CONFIG_KEY, JSON.toJSONString(weightConfig));
                endpointGroup.setLabels(labels);
                dBaasMetaService.getDefaultClient().updateEndpointGroup(requestId, replicaSetName, endpointGroup.getGroupName(), endpointGroup);
            }
        }
        else {  // replica change
            Map<String, Replica> ip2Replica = new HashMap<>();
            for (Replica replica : replicas) {
                ip2Replica.put(replica.getLinkIp(), replica);
            }
            EndpointWeightConfig endpointWeightConfig = new EndpointWeightConfig();
            List<EndpointWeightConfig.WeightConfig> weightConfigs = new ArrayList<>();
            for (RealServerResponse realServer : validRealServers) {
                Replica replica = ip2Replica.get(realServer.getIp());
                EndpointWeightConfig.WeightConfig config = new EndpointWeightConfig.WeightConfig();
                config.setDBInstanceId(replica.getReplicaSetName());
                config.setWeight(realServer.getWeight());
                weightConfigs.add(config);
            }
            endpointWeightConfig.setNodeWeights(weightConfigs);
            endpointGroup.getLabels().put(EndpointWeightConfig.CONFIG_KEY, JSON.toJSONString(endpointWeightConfig));
            dBaasMetaService.getDefaultClient().updateEndpointGroup(requestId, replicaSetName, endpointGroup.getGroupName(), endpointGroup);
        }
    }

    private void dealWeightChange(String requestId, String replicaSetName, EndpointGroup endpointGroup, EndpointWeightConfig newWeightConfig, EndPointService endPointService) throws Exception {
        EndpointWeightConfig weightConfig = endPointService.getWeightConfig(requestId, replicaSetName, endpointGroup.getId());
        Map<String, Integer> nodeId2Weight = new HashMap<>();
        for (EndpointWeightConfig.WeightConfig config : weightConfig.getNodeWeights()) {
            nodeId2Weight.put(config.getNodeId(), config.getWeight());
        }
        Map<String, Integer> newNodeId2Weight = new HashMap<>();
        for (EndpointWeightConfig.WeightConfig config : newWeightConfig.getNodeWeights()) {
            newNodeId2Weight.put(config.getNodeId(), config.getWeight());
        }

        List<String> replicaSetNameList = newWeightConfig.getNodeWeights().stream().map(EndpointWeightConfig.WeightConfig::getDBInstanceId).collect(Collectors.toList());
        List<Replica> allReplicas = endPointService.getAllReplicasByNames(requestId, replicaSetNameList);

        List<Replica> changedReplicas = new ArrayList<>();

        for (Replica replica : allReplicas) {
            if (newNodeId2Weight.containsKey(replica.getName()) && !newNodeId2Weight.get(replica.getName()).equals(nodeId2Weight.get(replica.getName()))) {
                changedReplicas.add(replica);
            }
        }

        List<Endpoint> endpointList = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSetName, null, null, null, null)
                .getItems().stream().filter(ep -> Objects.equals(ep.getEndpointGroupId(), endpointGroup.getId())).collect(Collectors.toList());;
        for (Endpoint endpoint : endpointList) {
            endPointService.setRealServerWeight(requestId, replicaSetName, endpoint, newWeightConfig, changedReplicas);
        }
    }

    private void dealReplicaChange(String requestId, String replicaSetName, EndpointGroup endpointGroup, EndpointWeightConfig newWeightConfig, EndPointService endPointService) throws Exception {
        Set<String> nodeIdSet = new HashSet<>();
        for (EndpointWeightConfig.WeightConfig weightConfig : newWeightConfig.getNodeWeights()) {
            nodeIdSet.add(weightConfig.getNodeId());
        }
        // 更改该 endpoint 的所有 address
        List<Endpoint> endpointList = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSetName, null, null, null, null)
                .getItems().stream().filter(ep -> Objects.equals(ep.getEndpointGroupId(), endpointGroup.getId())).collect(Collectors.toList());
        List<Replica> replicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null)
                .getItems().stream().filter(rp -> nodeIdSet.contains(rp.getName())).collect(Collectors.toList());
        for (Endpoint endpoint : endpointList) {
            // refresh
            endPointService.refreshRealServers(requestId, replicaSetName, endpoint, newWeightConfig, replicas);
        }
    }
}

