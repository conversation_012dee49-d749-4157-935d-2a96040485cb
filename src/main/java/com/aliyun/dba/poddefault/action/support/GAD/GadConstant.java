package com.aliyun.dba.poddefault.action.support.GAD;

public class GadConstant {
    public static final Integer GAD_UNIT_COUNT = 10;
    public static final String GAD_ROLE_CENTRAL = "CENTRAL";// 中心节点
    public static final String GAD_ROLE_UNIT = "UNIT";//单元节点
    public static final Integer LOCAL_DISK_MIN_SIZE = 5;//GB
    public static final Integer CLOUD_DISK_MIN_SIZE = 20;//GB
    public static final Integer GDN_RETRY_TIMES = 3;//GB
    public static final String GAD_SLR_ACCOUNT_PASSWORD_KEY = "h3fOp=S;M@[i!6b#";
    public static final String GAD_SLR_ACCOUNT = "GAD_SLR_ACCOUNT";
    public static final String ROLESESSIONNAME = "gad_slr_session";//服务账号的SLR名字；
    public static final Integer GAD_OPENAPI_READ_TIMEOUT = 300000;
    public static final String GAD_SLR_NAME = "aliyunserviceroleforrdsgad";  //GAD服务账号的ARN使用

}
