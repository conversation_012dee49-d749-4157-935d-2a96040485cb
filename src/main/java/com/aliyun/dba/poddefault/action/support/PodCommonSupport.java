package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.service.CustInstanceDBService;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.host.dataobject.HostInstanceDO;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.service.UserGrayService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.MYSQL_VERSION_57;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.property.ErrorCode.MINOR_VERSION_NOT_SUPPORT;
import static com.aliyun.dba.support.property.ErrorCode.UNSUPPORTED_BACKUP_POLICY;
import static com.aliyun.dba.support.property.ParamConstants.*;
import static com.aliyun.dba.support.property.ParamConstants.OPTIMIZED_WRITES;

/**
 * <AUTHOR> on 2020/6/7.
 */
@Component
public class PodCommonSupport {
    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private ReplicaSetService replicaSetService;

    @Resource
    protected PodCommonSupport podCommonSupport;

    @Resource
    protected MysqlParamSupport mysqlParamSupport;

    @Resource
    private CustinsService custinsService;

    @Resource
    private PodAvzSupport podAvzSupport;
    @Resource
    private ResourceService resourceService;
    @Resource
    private UserGrayService userGrayService;

    @Autowired
    protected CustInstanceDBService custInstanceDBService;

    @Autowired
    protected EcsDBService ecsDBService;

    private static final LogAgent logger = LogFactory.getLogAgent(PodCommonSupport.class);
    @Autowired
    private RundPodSupport rundPodSupport;
    @Resource
    private CustinsParamService custinsParamService;

    /**
     * 获取Task的domain
     *
     * @param bizTypeEnum
     * @return
     */
    public static String getTaskDomain(ReplicaSet.BizTypeEnum bizTypeEnum) {
        return PodParameterHelper.isAliGroup(bizTypeEnum) ? PodDefaultConstants.DOMAIN_XDB : PodDefaultConstants.DOMAIN_MYSQL;
    }

    /**
     * 根据业务类型、存储类型、内核类型和版本获取composeTag
     *
     * @param bizType
     * @param storageType
     * @return
     */
    public static String getComposeTag(String coreTag, String storageType, String releaseDate, ReplicaSet.BizTypeEnum bizType, String fileSystem) {
        if ("default".equalsIgnoreCase(coreTag) || "cloud_pfs".equalsIgnoreCase(coreTag)) {
            return coreTag;
        }

        String storageTypeTag;
        boolean isCloudDisk = Replica.StorageTypeEnum.CLOUD_SSD.toString().equalsIgnoreCase(storageType)
                || Replica.StorageTypeEnum.CLOUD_ESSD.toString().equalsIgnoreCase(storageType)
                || Replica.StorageTypeEnum.CLOUD_AUTO.toString().equalsIgnoreCase(storageType)
                || Replica.StorageTypeEnum.CLOUD_EFFICIENCY.toString().equalsIgnoreCase(storageType);
        if (Replica.StorageTypeEnum.LOCAL_SSD.toString().equalsIgnoreCase(storageType)) {
            storageTypeTag = "local_disk";
        } else if (PodParameterHelper.isAliGroup(bizType) && isCloudDisk){
            storageTypeTag = MySQLParamConstants.FILE_SYSTEM_PFS.equalsIgnoreCase(fileSystem) ? "pfs_disk" : "cloud_disk";
        } else if (PodParameterHelper.isAliYun(bizType) && isCloudDisk) {
            storageTypeTag = "cloud_disk";
        } else {
            throw new UnsupportedOperationException("Unsupported storageType");
        }
        return String.format("%s_%s_%s", coreTag, storageTypeTag, releaseDate);
    }


    /**
     * 新架构中支持的磁盘类型
     *
     * @param diskType
     * @return
     */
    public static boolean isSupportDiskType(String diskType) {
        return Replica.StorageTypeEnum.LOCAL_SSD.toString().equalsIgnoreCase(diskType) ||
                Replica.StorageTypeEnum.CLOUD_SSD.toString().equalsIgnoreCase(diskType) ||
                Replica.StorageTypeEnum.CLOUD_ESSD.toString().equalsIgnoreCase(diskType) ||
                // 兼容cloud_auto, autopl
                Replica.StorageTypeEnum.CLOUD_AUTO.toString().equalsIgnoreCase(diskType) ||
                // 兼容cloud_essd和PL2和PL3的
                String.format("%s2", Replica.StorageTypeEnum.CLOUD_ESSD.toString()).equalsIgnoreCase(diskType) ||
                String.format("%s3", Replica.StorageTypeEnum.CLOUD_ESSD.toString()).equalsIgnoreCase(diskType) ||
                String.format("%s0", Replica.StorageTypeEnum.CLOUD_ESSD.toString()).equalsIgnoreCase(diskType);
    }


    /**
     * 构造switch_info
     */
    public static Map<String, Object> getSwitchInfoTime(String switchMode, Date switchTime) throws RdsException {
        //指定时间切换，这里容忍10分钟的延迟
        return getSwitchInfoTime(switchMode,switchTime,10);
    }

    /**
     * 构造switch_info,可以指定切换窗口大小
     */
    public static Map<String, Object> getSwitchInfoTime(String switchMode, Date switchTime, Integer windowMinute) throws RdsException {
        Map<String, Object> switchInfoMap = new HashMap<>();
        Map<String, Object> timeInfo = new HashMap<>();
        if (CustinsSupport.APPOINT_MODE.equalsIgnoreCase(switchMode)) {
            if (switchTime == null) {
                throw new RdsException(ErrorCode.INVALID_SWICTHTIME);
            }
            timeInfo.put("start_time", DateSupport.second2str_gmt0(switchTime));
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(switchTime);
            calendar.add(Calendar.MINUTE, windowMinute);  //指定切换窗口
            timeInfo.put("end_time", DateSupport.second2str_gmt0(calendar.getTime()));
        } else if (CustinsSupport.NOW_MODE.equalsIgnoreCase(switchMode) || CustinsSupport.MAINTAIN_MODE.equalsIgnoreCase(switchMode)) {
            //  do nothing
        } else {
            throw new RdsException(ErrorCode.INVALID_SWICTHTIMEMODE);
        }
        switchInfoMap.put("mode", switchMode);
        switchInfoMap.put("time", Collections.singletonList(timeInfo));
        return switchInfoMap;
    }

    /**
     * 获取主实例
     */
    public Pair<String, ReplicaSet> getPrimaryReplicaSet(String requestId, ReplicaSet replicaSet) throws ApiException {
        ReplicaSet primaryReplicaSet = replicaSet;
        if (ReplicaSet.InsTypeEnum.TMP.equals(replicaSet.getInsType())) {
            primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), null);
        }

        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, primaryReplicaSet.getName());
        String centerReplicaSetName = labels.get("CenterReplicaSetName");
        String centerRegionId = labels.get("CenterRegionId");
        if (StringUtils.isNotEmpty(centerReplicaSetName)) {
            // 跨region只读
            primaryReplicaSet = dBaasMetaService.getRegionClient(centerRegionId).getReplicaSet(requestId, centerReplicaSetName, false);
        } else if (primaryReplicaSet.getPrimaryInsName() != null) {
            // 同region只读
            primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, primaryReplicaSet.getPrimaryInsName(), false);
        }
        return Pair.of(centerRegionId, primaryReplicaSet);
    }

    public static Replica.RoleEnum[] getRoles(String dbEngine, InstanceLevel instanceLevel, boolean isReadIns, AVZInfo avzInfo) {
        Replica.RoleEnum[] nodeRoles = null;
        if ("XDB".equalsIgnoreCase(dbEngine)) {
            if (isReadIns) {
                nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.LEARNER, Replica.RoleEnum.LEARNER_BACK};
            } else if (MysqlParamSupport.isSingleNode(instanceLevel)) {
                nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER};
            } else {
                nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER, Replica.RoleEnum.FOLLOWER, Replica.RoleEnum.LOGGER};
            }
        } else {
            if (instanceLevel.getCategory() == InstanceLevel.CategoryEnum.BASIC) {
                nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER};
            }
            else if(instanceLevel.getCategory() == InstanceLevel.CategoryEnum.SERVERLESS_BASIC){
                nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER};
            }
            else if(instanceLevel.getCategory() == InstanceLevel.CategoryEnum.SERVERLESS_STANDARD){
                nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE};
            }
            else if (MysqlParamSupport.isCluster(instanceLevel.getCategory().toString())) {
                List<Replica.RoleEnum> roles = new ArrayList<Replica.RoleEnum>() {{
                    this.add(Replica.RoleEnum.MASTER);
                    for (int i = 0; i < avzInfo.getMultiAVZExParamDO().getSlaveAvailableZoneInfo().size(); i++) {
                        this.add(Replica.RoleEnum.SLAVE);
                    }
                }};
                nodeRoles = roles.toArray(new Replica.RoleEnum[0]);
            }
            else {
                nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE};
            }
        }
        return nodeRoles;
    }

    public boolean isXEngine(String requestId, String replicaSetName) throws RdsException, ApiException {
        String paramGroupId = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetName, CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
        return  PodDefaultConstants.STORAGE_ENGINE_XENGINE.equalsIgnoreCase(SysParamGroupHelper.getDBStorageEngine(paramGroupId));
    }

    public boolean isXEngine(String paramGroupId) throws RdsException {
        String storageEngine = SysParamGroupHelper.getDBStorageEngine(paramGroupId);
        return storageEngine.equals(PodDefaultConstants.STORAGE_ENGINE_XENGINE);
    }

    public Selector genResourceSelector(String key, String operator, List<String> values) {
        Selector selector = new Selector();
        selector.setKey(key);
        selector.setOperator(operator);
        selector.setValues(new ArrayList<>(values));  // deep copy
        return selector;
    }

    public static boolean isServerless(InstanceLevel instanceLevel) {
        List<InstanceLevel.CategoryEnum> categoryEnumList = new ArrayList<>();
        categoryEnumList.add(InstanceLevel.CategoryEnum.SERVERLESS_BASIC);
        categoryEnumList.add(InstanceLevel.CategoryEnum.SERVERLESS_STANDARD);
        return categoryEnumList.contains(instanceLevel.getCategory());
    }

    public static boolean isArchChange(InstanceLevel srcInstanceLevel, InstanceLevel targetInstanceLevel){
        return PodCommonSupport.isArm(srcInstanceLevel) ^ PodCommonSupport.isArm(targetInstanceLevel);
    }

    public static boolean isArchChange(String srcInstanceLevelExtraInfo, String targetInstanceLevelExtraInfo){
        return PodCommonSupport.isArm(srcInstanceLevelExtraInfo) ^ PodCommonSupport.isArm(targetInstanceLevelExtraInfo);
    }


    public PodType getReplicaSetRuntimeType(String replicaSetName, InstanceLevel targetInstanceLevel) throws ApiException, RdsException {
        // 非基础版的实例，强制使用runc（适配rund单变双）
        if (targetInstanceLevel.getCategory() != InstanceLevel.CategoryEnum.BASIC
                && targetInstanceLevel.getCategory() != InstanceLevel.CategoryEnum.SERVERLESS_BASIC) {
            return PodType.POD_RUNC;
        }
        ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null);
        if (CollectionUtils.isNotEmpty(replicaListResult.getItems())) {
            ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(RequestSession.getRequestId(), replicaListResult.getItems().get(0).getId(), null);
            return getReplicaRuntimeType(replicaResource);
        }
        return PodType.POD_RUNC;
    }

    public PodType getReplicaRuntimeType(ReplicaResource replica) throws RdsException, ApiException {
        if (replica == null) {
            throw new RdsException(ErrorCode.PARAM_NOT_FOUND);
        }
        if (replica.getVpod() == null || replica.getVpod().getRuntimeType() == null) {
            return PodType.POD_RUNC;
        }
        if (!PodType.POD_ECS_RUND.getRuntimeType().equalsIgnoreCase(replica.getVpod().getRuntimeType())) {
            return PodType.POD_RUNC;
        }
        boolean vbmInstance = replicaSetService.isVbmInstance(RequestSession.getRequestId(), replica.getReplicaSetName());
        if (vbmInstance) {
            return PodType.POD_VBM_RUND;
        } else {
            return PodType.POD_ECS_RUND;
        }
    }


    public static boolean isArm(InstanceLevel instanceLevel) {
        //兼容历史逻辑，防止instanceLevel未初始化情况抛NPE
        if(Objects.isNull(instanceLevel)){
            logger.warn("Input instanceLevel is null when check arch, use default value false");
            return false;
        }
        return isArm(instanceLevel.getExtraInfo());
    }

    public static boolean isArm(String instanceLevelExtraInfo) {
        if (StringUtils.isNotEmpty(instanceLevelExtraInfo)) {
            try {
                JSONObject extraInfo = JSON.parseObject(instanceLevelExtraInfo);
                final String instructionSetArch = extraInfo.getString("instructionSetArch");
                if(CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(instructionSetArch)){
                    return true;
                }
            } catch (JSONException e) {
                logger.warn("parse extra_info error", e);
            }
        }
        return false;
    }

    /**
     * 根据AccessID判断访问来源是否为DBS服务
     * @param accessId
     * @return
     */
    public static boolean isAccessFromDBS(String accessId) {
        return StringUtils.equalsIgnoreCase(accessId, MySQLParamConstants.ACCESS_ID_DBS);
    }

    /**
     * 高可用升集群版校验
     */
    public void checkHaToClusterCondition(String requestId, ReplicaSet replicaSetMeta, InstanceLevel instanceLevel) throws RdsException, ApiException {
        // 暂不支持带只读的实例
        ReplicaSetListResult replicaSetListResult = replicaSetService.getReplicaSetSubIns(requestId, replicaSetMeta.getName(), ReplicaSet.InsTypeEnum.READONLY.getValue());
        if (replicaSetListResult != null && replicaSetListResult.getItems().size() > 0) {
            throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_READ_INSTANCE);
        }

        // 暂不支持代理内核版本小于1.14.5或者20231207
        Integer maxscaleInsId = replicaSetService.getMaxscaleInsId(replicaSetMeta.getName());
        if (maxscaleInsId != null) {
            if (!checkMinorVersionWithMaxScale(maxscaleInsId)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_MAXSCALE);
            }
        }
        // 不支持SSD云盘
        String diskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
        if (Replica.StorageTypeEnum.CLOUD_SSD.toString().equals(diskType)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLOUD_SSD);
        }

        // 不支持ARM架构
        if(isArm(instanceLevel)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_ARM_ARCH);
        }
    }


    /**
     * set the io acceleration config according to the request parameters
     * Creating a clone instance and creating a read-only scenario requires strong inheritance of the primary instance. Reuild the instance to use its own IO acceleration label.
     * If the primaryReplicaSet is passed in, the first priority is inherited
     * If action Params is passed in, and the primaryReplicaSet is not passed in, the second priority is to get the console incoming parameters
     * If none of the previous two parameters are passed in, use its own label
     *
     * @param primaryReplicaSet Passing in this parameter means that the IO acceleration status of the main instance needs to be strongly inherited
     * @param actionParams Console incoming parameters are the second priority
     * @throws Exception
     */
    public GeneralCloudDisk setGeneralCloudDiskConfig(String requestId, Map<String, String> actionParams, String dbVersion, InstanceLevel instanceLevel, String diskType, ReplicaSet primaryReplicaSet, String replicaSetName) throws Exception {
        GeneralCloudDisk generalCloudDisk = new GeneralCloudDisk();

        boolean ioAccelerationEnabled = false;
        if (primaryReplicaSet != null && StringUtils.isNotBlank(primaryReplicaSet.getName())) {
            ioAccelerationEnabled = transferIoAccelerationEnabledType(dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, primaryReplicaSet.getName()).get(IO_ACCELERATION_ENABLED));
        } else if (actionParams != null && !actionParams.isEmpty() && mysqlParamSupport.hasParameter(actionParams, IO_ACCELERATION_ENABLED)){
            ioAccelerationEnabled = transferIoAccelerationEnabledType(mysqlParamSupport.getParameterValue(actionParams, IO_ACCELERATION_ENABLED));
        } else if (StringUtils.isNotBlank(replicaSetName)){
            ioAccelerationEnabled = transferIoAccelerationEnabledType(dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetName, IO_ACCELERATION_ENABLED));
        }
        logger.info("ioAccelerationEnabled is {}", ioAccelerationEnabled);

        // warm data disk
        if (ioAccelerationEnabled) {
            WarmDataDisk warmDataDisk = new WarmDataDisk()
                    .ioAccelerationType(WarmDataDisk.IoAccelerationTypeEnum.BPE)
                    .ioAccelerationEnabled(ioAccelerationEnabled);

            generalCloudDisk.warmDataDisk(warmDataDisk);
        }

        // Turning on IO acceleration requires relevant verification
        if (isIoAccelerationEnabled(generalCloudDisk)) {
            checkIoAccelerationCondition(requestId, dbVersion, instanceLevel, diskType);
        }
        return generalCloudDisk;
    }

    public boolean isIoAccelerationEnabled(GeneralCloudDisk generalCloudDisk) {
        return generalCloudDisk != null && generalCloudDisk.getWarmDataDisk() != null
                && Boolean.TRUE.equals(generalCloudDisk.getWarmDataDisk().getIoAccelerationEnabled());
    }

    // todo: 这里资源评估需要下沉到common，上层不再感知IO加速介质的类型
    // todo: 目前第一期不支持
    public boolean isCloudDiskForIoAcceleration(GeneralCloudDisk generalCloudDisk) {
        return generalCloudDisk != null && generalCloudDisk.getWarmDataDisk() != null;
//                && (WarmDataDisk.WarmDataDiskTypeEnum.CLOUDCACHEELASTICEPHEMERALDISK.equals(generalCloudDisk.getWarmDataDisk().getWarmDataDiskType()) ||
//                WarmDataDisk.WarmDataDiskTypeEnum.CLOUDCACHEPLX.equals(generalCloudDisk.getWarmDataDisk().getWarmDataDiskType()));
    }

    /**
     * 开启 IO加速 的限制条件
     * mysql 版本为 8.0
     * 磁盘类型为 Auto PL
     * 暂只支持共享型实例
     */
    public void checkIoAccelerationCondition(String requestId, String dbVersion, InstanceLevel instanceLevel, String diskType) throws Exception {

        if (!MYSQL_VERSION_80.equals(dbVersion)) {
            logger.error("{} ins db version {}, and Buffer Pool Extension only support 8.0 .", requestId, dbVersion);
            throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
        }
        if (!InstanceLevel.IsolationTypeEnum.COMMON.equals(instanceLevel.getIsolationType())) {
            logger.error("{} ins Isolation Type {}, and Buffer Pool Extension only support common .", requestId, instanceLevel.getIsolationType());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
        if (!DockerOnEcsConstants.ECS_CLOUD_AUTO.equalsIgnoreCase(diskType)) {
            logger.error("{} ins diskType {}, and Buffer Pool Extension only support AutoPl.", requestId, diskType);
            throw new RdsException(ErrorCode.NOT_SUPPORT_SPECIFIC_DISK_TYPE);
        }
        // Does not support ARM architecture
        if (isArm(instanceLevel)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_ARM_ARCH);
        }
    }

    /**
     * 校验 IO加速 的内核小版本
     */
    public void checkIoAccelerationSupportedMinorVersion(String serviceSpecTag) throws Exception {
        Integer index = Objects.requireNonNull(serviceSpecTag).lastIndexOf("_");
        String releaseDate = serviceSpecTag.substring(index + 1);
        if (Long.parseLong(releaseDate) < 20230930) {
            logger.error("specify minor version=" + releaseDate + "not found for IO Acceleration");
            throw new RdsException(MINOR_VERSION_NOT_SUPPORT);
        }
    }
    public boolean isColdDataEnabled(ColdDataDisk coldDataDisk) {
        return Objects.nonNull(coldDataDisk) && Objects.nonNull(coldDataDisk.getColdDataEnabled()) && Boolean.TRUE.equals(coldDataDisk.getColdDataEnabled());
    }

    public void checkColdDataSupportLimit(String requestId, InstanceLevel instanceLevel,  String dbEngine, String dbVersion, String diskType, String serviceSpecTag, String minorVersion, String uid, String regionId) throws RdsException {
        // only support MySQL
        if (!"MySQL".equalsIgnoreCase(dbEngine)) {
            logger.error("{} ins db engine {}, and Cold Data only support MySQL .", requestId, dbEngine);
            throw new RdsException(ErrorCode.INCORRECT_DBINSTANCE_ENGINE);
        }
        //not support serverless
        if (isServerless(instanceLevel)){
            logger.error("{} Cold Data not support Serverless, category is [{}] .", requestId, instanceLevel.getCategory());
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CATEGORY);
        }
        // only support 8.0
        if (!MYSQL_VERSION_80.equals(dbVersion)) {
            logger.error("{} ins db version {}, and Cold Data only support 8.0 .", requestId, dbVersion);
            throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
        }
        if(!DockerOnEcsConstants.ECS_CLOUD_AUTO.equalsIgnoreCase(diskType)) {
            logger.error("{} ins diskType {}, and Cold Data only support AutoPl.", requestId, diskType);
            throw new RdsException(ErrorCode.NOT_SUPPORT_SPECIFIC_DISK_TYPE);
        }
        // limit minor version
        if(StringUtils.isNotBlank(serviceSpecTag) || StringUtils.isNotBlank(minorVersion)) {
            checkReleaseDate(getReleaseDate(requestId, serviceSpecTag, minorVersion));
        }
        // gray policy
        if(!userGrayService.isHitColdData(requestId, uid, regionId)){
            logger.error("requestId : {}, uid:{}, regionId: {}, not support coldDataEnabled", requestId, uid, regionId);
            throw new RdsException(ErrorCode.UNSUPPORTED_COLD_DATA);
        }

    }

    public void checkColdDataSupportLimitWithBackupPolicy(String requestId, InstanceLevel instanceLevel, String dbEngine, String dbVersion, String diskType, String serviceSpecTag, String minorVersion, Boolean backupPolicyLimit, String uid, String regionId) throws Exception {
        checkColdDataSupportLimit(requestId, instanceLevel, dbEngine, dbVersion, diskType, serviceSpecTag, minorVersion, uid, regionId);
        //backup policy limit
        if (Boolean.TRUE.equals(backupPolicyLimit)) {
            String errMsg = String.format("%s Cold Data won't open with CrossBackup or Flash Backup, please check Backup Policy", requestId);
            logger.error(errMsg);
            throw new RdsException(UNSUPPORTED_BACKUP_POLICY, errMsg);
        }
    }

    public String getReleaseDate(String requestId, String serviceSpecTag, String minorVersion) throws RdsException {
        String releaseDate;
        if (StringUtils.isNotBlank(minorVersion)) {
            releaseDate = StringUtils.substringAfterLast(minorVersion,
                    StringUtils.contains(minorVersion, "_") ? "_" : ":");
        } else {
            int index = Objects.requireNonNull(serviceSpecTag).lastIndexOf("_");
            releaseDate = serviceSpecTag.substring(index + 1);
        }
        if (!NumberUtils.isNumber(releaseDate)) {
            logger.error("requestId is {}, serviceSpecTag is :{}, minorVersion is {}, releaseDate is {}, releaseDate is not number", requestId, serviceSpecTag, minorVersion, releaseDate);
            throw new RdsException(MINOR_VERSION_NOT_SUPPORT);
        }
        return releaseDate;
    }

    public void checkReleaseDate(String releaseDate) throws RdsException {
        if (Long.parseLong(releaseDate) < 20240131) {
            String errMsg = "specify minor version=" + releaseDate + " check fail, not support for Cold Data/Serverless Read";
            logger.error(errMsg);
            throw new RdsException(MINOR_VERSION_NOT_SUPPORT, errMsg);
        }
    }

    /**
     * 检查支持ARM的xengine版本（8.0 20230630）
     *
     * @param serviceSpecTag
     * @param paramGroupID
     * @throws Exception
     */
    public void checkARMSupportXEngineMinorVersion(String serviceSpecTag, String paramGroupID) throws Exception {
        if (StringUtils.isBlank(paramGroupID)) {
            return;
        }
        if (!PodDefaultConstants.STORAGE_ENGINE_XENGINE.equalsIgnoreCase(SysParamGroupHelper.getDBStorageEngine(paramGroupID))) {
            return;
        }
        int index = Objects.requireNonNull(serviceSpecTag).lastIndexOf("_");
        String releaseDate = serviceSpecTag.substring(index + 1);
        if (!NumberUtils.isNumber(releaseDate)) {
            logger.error("minor version" + releaseDate + " is not number");
            throw new RdsException(MINOR_VERSION_NOT_SUPPORT);
        }
        if (Long.parseLong(releaseDate) < 20230324) {
            logger.error("minor version=" + releaseDate + " not supported for ARM xengine");
            throw new RdsException(MINOR_VERSION_NOT_SUPPORT);
        }
    }


    /**
     * 转换IO加速参数类型
     */

    public boolean transferIoAccelerationEnabledType(String ioAccelerationEnabled) throws Exception {
        if (ioAccelerationEnabled == null || ioAccelerationEnabled.trim().isEmpty()) {
            return false;
        }

        if (!(IO_ACCELERATION_ENABLED_ON.equals(ioAccelerationEnabled) || IO_ACCELERATION_ENABLED_OFF.equals(ioAccelerationEnabled))) {
            String errorMsg = String.format("ioAccelerationEnabled: [%s] is invalided!", ioAccelerationEnabled);
            logger.info(errorMsg);
            throw new Exception(errorMsg);
        }

        if (IO_ACCELERATION_ENABLED_ON.equals(ioAccelerationEnabled)) {
            return true;
        }
        return false;
    }


    /**
     * Convert the Parameter Type for IO Acceleration
     */
    public String transferIoAccelerationEnabledType(boolean ioAccelerationEnabled) throws Exception {
        if (ioAccelerationEnabled) {
            return IO_ACCELERATION_ENABLED_ON;
        }
        return IO_ACCELERATION_ENABLED_OFF;
    }

    public void checkBasicToClusterCondition(String requestId, ReplicaSet replicaSetMeta, InstanceLevel instanceLevel) throws RdsException, ApiException {
        // MySQL的大版本必须为5.7或8.0
        if (!PodParameterHelper.isMysql57or80(replicaSetMeta.getServiceVersion())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_MAJORVERSION_UPGRADE);
        }

        //不支持基础版只读实例升级
        if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_READONLY_INSTANCE);
        }

        //不支持SSD云盘，需升级到ESSD
        String diskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
        if (Replica.StorageTypeEnum.CLOUD_SSD.toString().equals(diskType)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLOUD_SSD);
        }

        //不支持ARM架构
        if(isArm(instanceLevel)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_ARM_ARCH);
        }

        //不支持云盒实例
        boolean isCloudBox = podAvzSupport.isCloudBoxAz(replicaSetMeta.getResourceGroupName());
        if (isCloudBox) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLOUDBOX_INSTANCE);
        }
    }


    public boolean isSupportShrinkV2() {
        ResourceDO resourceDO = resourceService.getResourceByResKey("MYSQL_SUPPORT_SHRINK_V2_GRAY_RELEASE");
        return null != resourceDO && StringUtils.equalsIgnoreCase(resourceDO.getRealValue(),"true");
    }

    /**
     * 检查分析实例和主实例是否关联
     */
    public void checkAnalyticDBInstanceRelation(String requestId, String dbInstance, String analyticDbInstance) throws RdsException {
        try {
            ServiceRelation serviceRelation = dBaasMetaService.getDefaultClient().getReplicaSetService(requestId, dbInstance, analyticDbInstance, false);
            if (Objects.isNull(serviceRelation)) {
                logger.error("analyticIns : {} has no relation with custins : {}", dbInstance, analyticDbInstance);
                throw new RdsException(ErrorCode.CLICKHOUSE_INSTANCE_RELATION_NOT_FOUND);
            }
        } catch (ApiException ex) {
            logger.error("requestId : {}, checkAnalyticDBInstanceRelation error : {}", requestId, JSONObject.toJSONString(ex));
            throw new RdsException(ErrorCode.CLICKHOUSE_INSTANCE_RELATION_NOT_FOUND);
        }
    }

    /**
     * 检查是否支持创建分析型实例
     */
    public void checkAnalyticDBInstanceLimit(String requestId, CustInstanceDO custins) throws RdsException, ApiException {
        InstanceLevel instanceLevel;
        ReplicaSet replicaSet;
        try {
            instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevelById(requestId, Long.valueOf(custins.getLevelId()), false);
            replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, custins.getInsName(), false);
        } catch (ApiException ex) {
            logger.error("requestId : {}, getInstanceLevelById error : {}", requestId, JSONObject.toJSONString(ex));
            throw new ApiException("getInstanceLevelById error.");
        }
        // 基础版实例不支持创建分析型实例
        if (InstanceLevel.CategoryEnum.BASIC.equals(instanceLevel.getCategory())) {
            logger.error("requestId : {}, basic category is not supported.", requestId);
            throw new RdsException(ErrorCode.UNSUPPORTED_BASIC_CATEGORY);
        }

        // 不支持只读实例
        if (ReplicaSet.InsTypeEnum.READONLY == replicaSet.getInsType()) {
            logger.error("requestId : {}, readonly instance is not supported.", requestId);
            throw new RdsException(ErrorCode.UNSUPPORTED_READONLY_INSTANCE);
        }

        // 校验是否是大账号下的RDS实例。不支持大账号之外的RDS实例。
        String userName = null;
        List<HostInstanceDO> hostInstanceList = custInstanceDBService.getHostInstanceList(custins.getId());
        if (hostInstanceList != null &&  hostInstanceList.size() > 0) {
            HostInstanceDO hostInstance = hostInstanceList.get(0);
            Integer hostId = hostInstance.getHostId();
            EcsHostDetailDO ecsHostDetail = ecsDBService.getEcsHostDetailDOByHostId(hostId);
            userName = ecsHostDetail.getUserName();
            logger.info("user name is {}", userName);
        }
        if (!RDS_ACCOUNT.equalsIgnoreCase(userName)) {
            logger.error("This instance is not an <NAME_EMAIL>");
            throw new RdsException(ErrorCode.INVALID_DBINSTANCETYPE);
        }
    }

    /**
     * 获取实例下的keeper集群
     * */
    public ReplicaSet getKeeperReplicaSet(String clusterName) throws ApiException {
        return getClusterReplicaSet(clusterName, PodDefaultConstants.DB_TYPE_CK_KEEPER);
    }

    /**
     * 获取实例下的ck集群
     * */
    public ReplicaSet getCkReplicaSet(String clusterName) throws ApiException {
        return getClusterReplicaSet(clusterName, PodDefaultConstants.DB_TYPE_CK_SERVER);
    }

    private ReplicaSet getClusterReplicaSet(String clusterName, String dbType) throws ApiException {
        ClusterInstance clusterInstance = dBaasMetaService.getDefaultClient().getClusterInstance(RequestSession.getRequestId(), clusterName, false);
        List<InstanceMember> members = clusterInstance.getMembers();
        for (InstanceMember member : members) {
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(RequestSession.getRequestId(), member.getName(), false);
            if (Objects.equals(replicaSet.getService(), dbType) && replicaSet.getInsType() != ReplicaSet.InsTypeEnum.TMP) {
                return replicaSet;
            }
        }
        throw new ApiException(String.valueOf(ErrorCode.DBINSTANCE_NOT_FOUND));
    }

    public String makeClickhouseName(String requestId) throws Exception {
        for (int i = 0; i < 10; i++) {
            String clickhouseInsName = "cc-"+ SupportUtils.getRandomInsName(17);
            try {
                ClusterInstance clusterInstance = dBaasMetaService.getDefaultClient().getClusterInstance(requestId, clickhouseInsName, true);
                if (clusterInstance == null) {
                    return clickhouseInsName;
                }
            } catch (ApiException e) {
                logger.warn(e.getMessage());
            }
        }
        logger.info("requestId : {}, get clickhouseInsName failed.", requestId);
        throw new Exception("Get clickhouseInsName failed.");
    }

    /**
     * get value of cloud_max_storage_size from table: resource.
     *
     * @param defaultSize            default size of the max cloud disk size
     * @param throwExceptionIfAbsent When this value is set to True, if cloud_max_storage_size does not exist in the table, an exception will be thrown
     * @return
     */
    public int getMaxCloudStorageSizeConfig(int defaultSize, boolean throwExceptionIfAbsent) throws RdsException {

        ResourceDO resourceDO = resourceService.getResourceByResKey(ResourceKey.RESOURCE_CLOUD_MAX_STORAGE_SIZE.getValue());
        if (resourceDO == null || StringUtils.isBlank(resourceDO.getRealValue())) {
            if (throwExceptionIfAbsent) {
                logger.error("Get CLOUD_MAX_STORAGE_SIZE from table resource failed.");
                throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
            }
            return defaultSize;
        }

        return Integer.parseInt(resourceDO.getRealValue());
    }

    public boolean isLocalCacheWithIoAccelerationEnabled(GeneralCloudDisk generalCloudDisk) {
        return !podCommonSupport.isCloudDiskForIoAcceleration(generalCloudDisk)
                && podCommonSupport.isIoAccelerationEnabled(generalCloudDisk);
    }

    /**
     * 开启写优化的限制条件
     * mysql 版本为 5.7 or 8.0
     * 仅支持云盘类型
     * 磁盘类型不为 SSD
     */
    public void checkOptimizedWritesCondition(String requestId, String dbVersion, String diskType) throws Exception {

        if (!MYSQL_VERSION_80.equals(dbVersion) && !MYSQL_VERSION_57.equals(dbVersion)) {
            logger.error("{} ins db version {}, but Optimized Writes support 5.7 and 8.0.", requestId, dbVersion);
            throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
        }
        if (DockerOnEcsConstants.ECS_ClOUD_SSD.equalsIgnoreCase(diskType)) {
            logger.error("{} ins diskType {}, and Optimized Writes do not support ssd.", requestId, diskType);
            throw new RdsException(ErrorCode.NOT_SUPPORT_SPECIFIC_DISK_TYPE);
        }
    }

    public boolean getOptimizedWrites(String requestId, Map<String, String> actionParams, String dbVersion, String diskType, ReplicaSet primaryReplicaSet, String replicaSetName) throws Exception {
        boolean isOptimizedWrites = false;
        if (primaryReplicaSet != null && StringUtils.isNotBlank(primaryReplicaSet.getName())) {
            isOptimizedWrites = isOptimizedWrites(dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, primaryReplicaSet.getName()).get(OPTIMIZED_WRITES_INFO));
        } else if (actionParams != null && !actionParams.isEmpty()){
            String optimizedWrites = mysqlParamSupport.getParameterValue(actionParams, OPTIMIZED_WRITES, null);
            logger.info("{} optimizedWrites is {}", requestId, optimizedWrites);
            // 如果没有传递optimizedWrites参数，则arm架构的实例默认开启写优化，x86默认关闭
            if (StringUtils.isBlank(optimizedWrites)) {
                String dbType = mysqlParamSupport.getAndCheckDBType(actionParams, null);
                String classCode = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.DB_INSTANCE_CLASS);
                InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient()
                        .getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
                if (PodCommonSupport.isArm(instanceLevel)) {
                    isOptimizedWrites = true;
                }
                // 如果传递的optimizedWrites参数为optimized，则开启写优化
            }else if (PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites)) {
                isOptimizedWrites = true;
            }
            try {
                String uid = mysqlParamSupport.getParameterValue(actionParams, UID);
                String zoneId = podAvzSupport.getAVZInfo(actionParams).getMasterZoneId();
                isOptimizedWrites = setOptimizedWritesForVip(requestId, uid, zoneId, isOptimizedWrites);
            } catch (Exception e) {
                logger.error("get white list for vip turn on optimized writes failed", e);
            }
        } else if (StringUtils.isNotBlank(replicaSetName)){
            isOptimizedWrites = isOptimizedWrites(dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetName, OPTIMIZED_WRITES_INFO));
        }
        if (isOptimizedWrites) {
            checkOptimizedWritesCondition(requestId, dbVersion, diskType);
        }
        return isOptimizedWrites;
    }

    public boolean isOptimizedWrites(String optimizedWritesInfoString) {
        if (optimizedWritesInfoString == null) {
            return false;
        }
        HashMap<String, Boolean> optimizedWritesInfo = JSON.parseObject(optimizedWritesInfoString, new TypeReference<HashMap<String, Boolean>>(){});
        if (MapUtils.isEmpty(optimizedWritesInfo)) {
            return false;
        }
        return optimizedWritesInfo.getOrDefault(INIT_OPTIMIZED_WRITES, false) &&
                optimizedWritesInfo.getOrDefault(CustinsParamSupport.OPTIMIZED_WRITES, false);
    }

    public boolean isInitOptimizedWrites(String optimizedWritesInfoString) {
        if (optimizedWritesInfoString == null) {
            return false;
        }
        HashMap<String, Boolean> optimizedWritesInfo = JSON.parseObject(optimizedWritesInfoString, new TypeReference<HashMap<String, Boolean>>(){});
        if (MapUtils.isEmpty(optimizedWritesInfo)) {
            return false;
        }
        return optimizedWritesInfo.getOrDefault(INIT_OPTIMIZED_WRITES, false);
    }

    public String getOptimizedWritesInfo(String requestId, String dbVersion, String diskType, ReplicaSet primaryReplicaSet) throws Exception {
        String optimizedWritesInfo = null;
        if (primaryReplicaSet != null && StringUtils.isNotBlank(primaryReplicaSet.getName())) {
            optimizedWritesInfo = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, primaryReplicaSet.getName()).get(OPTIMIZED_WRITES_INFO);
        }

        if (isOptimizedWrites(optimizedWritesInfo)) {
            checkOptimizedWritesCondition(requestId, dbVersion, diskType);
        }
        return optimizedWritesInfo;
    }

    /**
     * change targetSize from MB to GB，round up and adjust upward to a multiple of 5
     * @param targetSizeMB
     * @return softSizeGB
     */
    public static long getTargetSizeGB(double targetSizeMB) {
        long rawSizeGB = (long) Math.ceil(targetSizeMB/1024F);
        long softSizeGB = Math.floorDiv(rawSizeGB + 4L, 5L)*5L;
        logger.info("rawSizeGB: {}, softSizeGB: {}", rawSizeGB, softSizeGB);
        return softSizeGB;
    }

    /**
     * Used to label incremental instances with optimizedWrites
     */
    public Map<String, String> putLabelForOptimizedWrites(String requestId, String dbType, Map<String, String> labels) throws ApiException {
        String config = dbType.toUpperCase() + "_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT";
        ConfigListResult result = dBaasMetaService.getDefaultClient().listConfigs(requestId, config);
        if (result != null && result.getItems() != null && !result.getItems().isEmpty() && Boolean.parseBoolean(result.getItems().get(0).getValue())) {
            logger.info("requestId : {} , hack for ext turn on the optimized writes : {}", requestId, result.getItems().get(0).getValue());
            Map<String, Boolean> optimizedWritesInfo = new HashMap<>();
            optimizedWritesInfo.put(CustinsParamSupport.INIT_OPTIMIZED_WRITES, true);
            optimizedWritesInfo.put(CustinsParamSupport.OPTIMIZED_WRITES, true);
            labels.put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, JSON.toJSONString(optimizedWritesInfo));
        }
        return labels;
    }

    /**
     * vip turn on optimized writes
     */
    public boolean setOptimizedWritesForVip(String requestId, String uid, String zoneId, boolean isOptimizedWrites) {
        try {
            String config = "WHITE_LIST_FOR_VIP_TURN_ON_OPTIMIZED_WRITES";
            ConfigListResult configListResult = dBaasMetaService.getDefaultClient().listConfigs(requestId, config);
            if (configListResult != null && CollectionUtils.isNotEmpty(configListResult.getItems()) && configListResult.getItems().get(0) != null) {
                String azJsonString = configListResult.getItems().get(0).getValue();
                logger.info("get {} uids from resource: {} and uid is {}, zoneId is {}" , "WHITE_LIST_FOR_VIP_TURN_ON_OPTIMIZED_WRITES", azJsonString, uid, zoneId);
                final HashMap<String, String> azObj = new Gson().fromJson(azJsonString, HashMap.class);
                String uids = azObj.get(zoneId);
                if (uids != null && !uids.isEmpty()) {
                    List<String> uidList = Arrays.asList(uids.split(","));
                    if (uidList.contains(uid)) {
                        logger.info("uid is {}, turn on optimized writes", uid);
                        isOptimizedWrites = true;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("get white list for vip turn on optimized writes failed", e);
        }
        return isOptimizedWrites;
    }

    /**
     * check single tenant
     * @param instanceLevel
     * @return
     */
    public static boolean isSingleTenant(InstanceLevel instanceLevel) {
        return !InstanceLevel.IsolationTypeEnum.COMMON.equals(instanceLevel.getIsolationType());
    }

    /**
     * 代理内核必须>=1.14.5或者20231207
     * @param maxScaleCustinsId
     * @return
     */
    public boolean checkMinorVersionWithMaxScale(Integer maxScaleCustinsId) {
        CustInstanceDO maxscale = custinsService.getCustInstanceByCustinsId(maxScaleCustinsId);
        if (maxscale == null || maxscale.isDeleting()) {
            return true;  //maxscale正在删除不用校验
        }
        CustinsParamDO custinsMinorVersion = custinsParamService.getCustinsParam(maxScaleCustinsId, "minor_version");
        if (custinsMinorVersion != null && StringUtils.isNotBlank(custinsMinorVersion.getValue())) {
            String maxScaleMinorVersion = custinsMinorVersion.getValue();
            // 代理内核小版本必须大于等于1.14.5或20231207
            if (StringUtils.equals(maxscale.getDbVersion(), "3.5")) {
                String maxscaleReleaseDate = StringUtils.substringAfterLast(maxScaleMinorVersion, "_");
                if (maxscaleReleaseDate.length() != 8) {
                    return true;  //不符合版本规则，不校验
                }
                if ("20231207".compareTo(maxscaleReleaseDate) > 0) {
                    logger.error("maxscale's version is {}, do not allow modify", maxScaleMinorVersion);
                    return false;
                }
            } else {
                String[] vers = StringUtils.split(maxScaleMinorVersion, "_");
                if (vers.length != 3) {
                    return true;   //不符合版本规则，不校验
                }
                String[] vvv = StringUtils.split(vers[2], ".");
                if (vvv.length != 3) {
                    return true; //不符合版本规则，不校验
                }
                Long checkVersion = LangUtil.getLong(StringUtils.join(vvv));
                if (checkVersion == null) {
                    return true; //不符合版本规则，不校验
                }
                if ("1.14.5".compareTo(vers[2]) > 0) {
                    logger.error("maxscale's version is {}, do not allow modify", maxScaleMinorVersion);
                    return false;
                }
            }
        }
        return true;
    }
}
