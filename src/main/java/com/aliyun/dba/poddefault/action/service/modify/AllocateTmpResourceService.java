package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.k8s.resmanager.Constants;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.val;
import org.apache.commons.lang.NotImplementedException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.ACCESS_ID_RESOURCE;
import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.instance.support.InstanceSupport.*;

/**
 * 临时实例资源申请（不含存储、链路）
 */
@Service
public class AllocateTmpResourceService extends BaseModifyDBInstanceService {


    @Resource
    private InstanceIDao instanceIDao;
    @Resource
    private InstanceService instanceService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodCommonSupport podCommonSupport;


    public AllocateTmpResourceResult make(String requestId, CustInstanceDO custins, PodModifyInsParam modifyInsParam) throws Exception {
        val category = modifyInsParam.getTargetInstanceLevel().getCategory();
        if (category != null) {
            if (ImmutableSet.of(InstanceLevel.CategoryEnum.BASIC, InstanceLevel.CategoryEnum.SERVERLESS_BASIC).contains(category)) {
                return make(requestId, custins, modifyInsParam, category.getValue(), Collections.singletonList(Replica.RoleEnum.MASTER), ImmutableMap.of(), ImmutableMap.of());
            }
            if (ImmutableSet.of(InstanceLevel.CategoryEnum.STANDARD, InstanceLevel.CategoryEnum.SERVERLESS_STANDARD).contains(category)) {
                return make(requestId, custins, modifyInsParam, category.getValue(), Arrays.asList(Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE), ImmutableMap.of(), ImmutableMap.of());
            }
        }
        throw new NotImplementedException();
    }

    public AllocateTmpResourceResult make(
            String requestId,
            CustInstanceDO custins,
            PodModifyInsParam modifyInsParam,
            String category,
            List<Replica.RoleEnum> roles,
            Map<Replica.RoleEnum, String> zoneIdMap,
            Map<Replica.RoleEnum, String> subDomainMap
    ) throws Exception {
        return make(requestId, custins, modifyInsParam, category, roles, zoneIdMap, subDomainMap, null);
    }

    public AllocateTmpResourceResult make(
            String requestId,
            CustInstanceDO custins,
            PodModifyInsParam modifyInsParam,
            String category,
            List<Replica.RoleEnum> roles,
            Map<Replica.RoleEnum, String> zoneIdMap,
            Map<Replica.RoleEnum, String> subDomainMap,
            ArrayList<ReplicaResourceRequest> replicas
    ) throws Exception {
        val resourceRequest = new ReplicaSetResourceRequest();

        val tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName();
        val currentReplicas = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(
                        modifyInsParam.getRequestId(),
                        modifyInsParam.getDbInstanceName(),
                        null, null, null, null
                ).getItems();

        // Why assert??
        assert currentReplicas != null;
        PodModifyInsParam.replicaNameValidate(currentReplicas, tmpReplicaSetName);

        // Check if DHG MyBASE
        if (StringUtils.isNotBlank(modifyInsParam.getClusterName()) && modifyInsParam.isDHG()) {
            resourceRequest.setDedicatedHostGroupId(modifyInsParam.getClusterName());
        }

        // build labels
        resourceRequest.putLabelsItem(PodDefaultConstants.LABEL_ORDER_ID, modifyInsParam.getOrderId());
        resourceRequest.putLabelsItem(ACCESS_ID_RESOURCE, modifyInsParam.getAccessId());
        if (replicaSetService.isServerless(modifyInsParam.getTargetInstanceLevel())) {
            resourceRequest.putLabelsItem(PodDefaultConstants.SCHEDULE_LABEL_SERVERLESS, Boolean.TRUE.toString());
        }

        resourceRequest.userId(modifyInsParam.getBid())
                .uid(modifyInsParam.getUid())
                .replicaSetName(tmpReplicaSetName)
                .dbType(modifyInsParam.getDbType())
                .dbVersion(modifyInsParam.getDbVersion())
                .classCode(modifyInsParam.getTargetClassCode())
                .serverlessRcu(modifyInsParam.getRcu())
                .diskSize(modifyInsParam.getTargetDiskSizeGB())
                .storageType(modifyInsParam.getTargetDiskType())
                .bizType(custins.getBizType())
                .catagory(category)
                //For cloud_essd will use the snapshot to rebuild the disk
                .allocateDisk(false)
                // ServiceTag 与源实例保持一致
                .composeTag(minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId()))
                // 不申请 VIP（默认 lvs）
                .connType(CONN_TYPE_PHYSICAL)
                // 不申请反向 VPC
                .ignoreCreateVpcMapping(true)
                .generalCloudDisk(modifyInsParam.getGeneralCloudDisk());

        // for upgrade minor version, target composeTag is different with src ins
        if (StringUtils.isNotBlank(modifyInsParam.getTargetComposeTag())) {
            resourceRequest.setComposeTag(modifyInsParam.getTargetComposeTag());
        }

        // The read-only instance is because the resource pool is a highly available resource pool, so the primary ins and instance type need to be passed in when applying for temporary resources,
        // otherwise the service_spec will be obtained by mistake, resulting in allocation to the arm machine
        if (modifyInsParam.isReadIns() && modifyInsParam.isIoAccelerationEnabledChange()) {
            resourceRequest.primaryInsName(modifyInsParam.getDbInstanceName());
            resourceRequest.insType(ReplicaSet.InsTypeEnum.TMP.toString());
        }

        boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();
        if (isTargetSingleTenant) {
            // 单租户场景
            resourceRequest.setSingleTenant(true);
            resourceRequest.setEniDirectLink(false);
        }

        resourceRequest.setScheduleTemplate(modifyInsParam.getScheduleTemplate());
        podTemplateHelper.setSpecSchedulerConfigSpread(resourceRequest, modifyInsParam.getDbInstanceName());

        // 强制走单可用区，控制台如果支持再做修改
        val avzInfo = modifyInsParam.getAvzInfo();
        resourceRequest.setSubDomain(avzInfo.getRegion());
        resourceRequest.setRegionId(avzInfo.getRegionId());

        val diskSizeGB = modifyInsParam.getTargetDiskSizeGB();
        val extendedDiskSizeGB = podParameterHelper
                .getExtendDiskSizeGBForPod(
                        modifyInsParam.getReplicaSetMeta().getBizType(),
                        false, // 云上无影响
                        diskSizeGB
                );


        val masterReplica = currentReplicas.stream()
                .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER))
                .findFirst()
                .get();

        Map<Replica.RoleEnum, String> roleHostNameMapping = modifyInsParam.getRoleHostNameMapping();


        // cluster use input replicas
        if (replicas == null || replicas.isEmpty()) {
            replicas = new ArrayList<ReplicaResourceRequest>();
            for (val role : roles) {
                ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
                replicaResourceRequest.setClassCode(modifyInsParam.getTargetClassCode());
                replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
                // 云盘赠送
                replicaResourceRequest.setDiskSize(extendedDiskSizeGB);
                replicaResourceRequest.setHostName(roleHostNameMapping.getOrDefault(role, null));

                // 云盘申请下沉至任务流

                val zoneIdInMap = zoneIdMap.get(role);
                val replica = currentReplicas.stream()
                        .filter(it -> Objects.equals(it.getRole(), role))
                        .findFirst().orElse(masterReplica);
                val zoneId = zoneIdInMap != null ? zoneIdInMap : replica.getZoneId();

                val subDomainInMap = subDomainMap.get(role);
                val subDomain = subDomainInMap != null ? subDomainInMap :
                        Objects.equals(replica.getRole(), role) ? replica.getSubDomain() : null;
                if (subDomain != null) {
                    replicaResourceRequest.setSubDomain(subDomain);
                }
                replicaResourceRequest.setRole(role.toString());
                replicaResourceRequest.setZoneId(zoneId);
                if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
                    rundPodSupport.completeReplicaNetworkConfig(replicaResourceRequest, replica);
                }
                replicas.add(replicaResourceRequest);
            }
        }
        resourceRequest.setReplicaResourceRequestList(replicas);

        val newLevel = instanceService.getInstanceLevelByClassCode(
                modifyInsParam.getTargetInstanceLevel().getClassCode(),
                custins.getDbType(),
                custins.getDbVersion(),
                custins.getTypeChar(),
                modifyInsParam.getTargetInstanceLevel().getCharacterType()
        );

        podReplicaSetResourceHelper.mockReplicaSetResource(resourceRequest);

        // 设置autopl配置
        resourceRequest.setProvisionedIops(modifyInsParam.getProvisionedIops());
        resourceRequest.setBurstingEnabled(modifyInsParam.isBurstingEnabled());
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
            rundPodSupport.completeReplicaSetNetworkConfig(resourceRequest);
            Endpoint vpcEndpoint = replicaSetService.getReplicaSetVpcEndpoint(requestId, custins.getInsName()); //VPC不能变，以实例的为准，
            resourceRequest.setVpcId(vpcEndpoint.getVpcId());
        }
        logger.info("allocate resource request:{}", resourceRequest);
        // 使用 Common 创建实例 API
        val isAllocated = commonProviderService.getDefaultApi()
                .allocateReplicaSetResourceV1(modifyInsParam.getRequestId(), tmpReplicaSetName, resourceRequest);


        // 订正元数据，设置为临时实例
        val client = dBaasMetaService.getDefaultClient();
        val replicaSet = client.getReplicaSet(requestId, tmpReplicaSetName, null);
        replicaSet.setPrimaryInsName(custins.getInsName());
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
        client.updateReplicaSet(requestId, tmpReplicaSetName, replicaSet);

        // 补齐 translist
        val transList = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        transList.setsCinsid(custins.getId());
        transList.setdCinsid(Objects.requireNonNull(replicaSet.getId()).intValue());
        transList.setsLevelid(custins.getLevelId());
        transList.setdLevelid(newLevel.getId());
        transList.setSwitchTime(modifyInsParam.getSwitchTime());
        transList.setsDisksize(custins.getDiskSize());
        transList.setdDisksize(diskSizeGB * 1024L);
        this.instanceIDao.createTransList(transList);


        AllocateTmpResourceResult allocateTmpResourceResult = new AllocateTmpResourceResult();
        allocateTmpResourceResult.setAllocated(isAllocated);
        allocateTmpResourceResult.setTransList(transList);
        allocateTmpResourceResult.setResourceRequest(resourceRequest);
        allocateTmpResourceResult.setReplicaSet(replicaSet);


        return allocateTmpResourceResult;
    }


    public ArrayList<ReplicaResourceRequest> getReplicasForCluster(
            PodModifyInsParam modifyInsParam,
            List<Replica> currentReplicas
    ) {
        ArrayList<ReplicaResourceRequest> replicas = new ArrayList<>();
        for(Replica replica : currentReplicas) {
            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
            replicaResourceRequest.setClassCode(replica.getClassCode());
            replicaResourceRequest.setSingleTenant(modifyInsParam.isTargetSingleTenant());
            // 云盘赠送
            replicaResourceRequest.setDiskSize(podParameterHelper
                    .getExtendDiskSizeGBForPod(
                            modifyInsParam.getReplicaSetMeta().getBizType(),
                            false,
                            modifyInsParam.getDiskSizeGB()
                    ));

            replicaResourceRequest.setHostName(modifyInsParam.getRoleHostNameMapping().getOrDefault(replica.getRole(), null));
            replicaResourceRequest.setSubDomain(replica.getSubDomain());
            replicaResourceRequest.setRole(replica.getRole().toString());
            replicaResourceRequest.setZoneId(replica.getZoneId());
            replicas.add(replicaResourceRequest);
        }
        return replicas;
    }
}
