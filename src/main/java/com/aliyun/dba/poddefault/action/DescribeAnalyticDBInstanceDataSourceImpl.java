package com.aliyun.dba.poddefault.action;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlAccountService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeAnalyticDBInstanceDataSourceImpl")
public class DescribeAnalyticDBInstanceDataSourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeAnalyticDBInstanceDataSourceImpl.class);

    @Resource
    RdsApi rdsApi;

    @Resource
    protected DBaasMetaService dbaasMetaService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    @Autowired
    protected DbossApi dbossApi;

    @Autowired
    protected MysqlAccountService mysqlAccountService;

    @Resource
    private MycnfService mycnfService;

    @Resource
    protected ResourceService resourceService;

    private static final String ONE_STOP_DB_QUANTITY_LIMIT = "ONE_STOP_DB_QUANTITY_LIMIT";
    private static final String ONE_STOP_TABLE_QUANTITY_LIMIT = "ONE_STOP_TABLE_QUANTITY_LIMIT";

    // 配置的时候，查询源库的库&表，通过DBoss获取。用户查询配置了哪些库表时，通过查DTS的接口获取
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String dbName = paramSupport.getParameterValue(params, ParamConstants.DB_NAME);
            String tableName = paramSupport.getParameterValue(params, ParamConstants.TABLE_NAME);
            Map<String, Object> result = new HashMap<>();

            Map<String, Object> dbCountMap = dbossApi.queryDBCount(custins.getId());
            log.info("requestId : {}, dbCountMap : {}",requestId, JSON.toJSONString(dbCountMap));
            Integer dbCount = MapUtils.getInteger(dbCountMap, "totalCount", 0);
            Integer dbQuantityLimit = getQuantityLimit(ONE_STOP_DB_QUANTITY_LIMIT);
            if (dbCount > dbQuantityLimit) {
                logger.warn("requestId : {}, the number of DB for this instance is {}.", requestId, dbCount);
                throw new RdsException(ErrorCode.TOO_MANY_DBS);
            }

            // dbName:null, tableName:null, 查所有库表
            // dbName:null, tableName:not null, 报错
            // dbName:not null, tableName:null, 查某个库下的所有表
            // dbName:not null, tableName:not null, 查表的列
            if (StringUtils.isEmpty(tableName)) {
                // 查MySQL的库表
                List<Map<String, Object>> dbList = getDBsAndTables(custins, dbName, params);
                result.put("dbList", dbList);
            } else if (StringUtils.isNotEmpty(dbName) && StringUtils.isNotEmpty(tableName)) {
                // 查表的列名
                result = getColumnInfos(requestId, custins, dbName, tableName);
            } else {
                logger.info("reqeustId : {}, one of dbName or tableName is null. dbName : {}, tableName : {}", requestId, dbName, tableName);
                throw new RdsException(ErrorCode.PARAM_NOT_FOUND);
            }

            result.put("dbInstanceId", custins.getId());
            result.put("dbInstanceName", custins.getInsName());
            return result;

        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.warn(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private List<Map<String, Object>> getDBsAndTables(CustInstanceDO custins, String dbName, Map<String, String> params) throws Exception {
        int pageNum = Integer.parseInt(paramSupport.getParameterValue(params, ParamConstants.PAGE_NUMBER, "1"));
        int pageSize = Integer.parseInt(paramSupport.getParameterValue(params, ParamConstants.PAGE_SIZE, "500"));

        boolean queryProtectedDB = Boolean.parseBoolean(paramSupport.getParameterValue(params, "QueryProtectedDB", "false"));
        MycnfCustinstanceDO innerSchemaList = mycnfService.getMycnfCustinstance(custins.getId(), RdsConstants.MYCNF_CUSTINS_KEY_INNER_SCHEMA_LIST);
        Set<String> protectedDBSet = new HashSet<>();
        if (null != innerSchemaList) {
            String[] innerDBNameList = StringUtils.split(innerSchemaList.getParaValue(), ",");
            protectedDBSet.addAll(Arrays.asList(innerDBNameList));
        }

        List<Map<String, Object>> dbs = dbossApi.queryDBs(custins.getId(), dbName, (pageNum - 1) * pageSize, pageSize);

        List<Map<String, Object>> dbList = new ArrayList<>();
        Integer tablesCount = 0;
        Integer tableQuantityLimit = getQuantityLimit(ONE_STOP_TABLE_QUANTITY_LIMIT);
        for (Map<String, Object> db : dbs) {
            Map<String, Object> dbInfo = new HashMap<>();

            dbName = String.valueOf(db.remove("dbname"));
            if ((queryProtectedDB && !protectedDBSet.contains(dbName)) || (!queryProtectedDB && protectedDBSet.contains(dbName))) {
                continue;
            }
            List<Map<String, Object>> tableList = dbossApi.queryTables(custins.getId(), dbName, 0, 500, null, null, 0L, 0L);
            tablesCount += tableList.size();
            if (tablesCount > tableQuantityLimit) {
                logger.warn("requestId : {}, the number of table for this instance is {}.", RequestSession.getRequestId(), tablesCount);
                throw new RdsException(ErrorCode.TOO_MANY_TABLES);
            }

            List<String> tableNameList = new ArrayList<>();
            for(Map<String, Object> table : tableList) {
                String tableName = String.valueOf(table.get("tableName"));
                tableNameList.add(tableName);
            }
            dbInfo.put("dbName", dbName);
            dbInfo.put("tables", tableNameList);
            dbList.add(dbInfo);
        }
        return dbList;
    }

    private Map<String, Object> getColumnInfos(String requestId, CustInstanceDO custins, String dbName, String tableName) throws Exception {
        List<Map<String, Object>> tables = dbossApi.queryTables(custins.getId(), dbName, 0, 500, tableName, null, 0L, 0L);
        if (ObjectUtils.isEmpty(tables)) {
            logger.error("requestId : {}. Get tables info failed. The result is null. dbName : {}, tableName : {}", requestId, dbName, tableName);
            throw new Exception("Get tables info failed. The result is null.");
        }
        Map<String, Object> tableInfo = tables.get(0);
        logger.info("tables : {}", JSONObject.toJSONString(tables));


        Map<String, Object> columnInfos = new HashMap<>();
        columnInfos.put("dbName", tableInfo.get("dbName"));
        columnInfos.put("tableName", tableInfo.get("tableName"));
        columnInfos.put("columnInfoList", tableInfo.get("columnsInfos"));
        columnInfos.put("hasPrimKey", tableInfo.get("hasPrimKey"));
        return columnInfos;
    }

    private Integer getQuantityLimit(String reskey) throws Exception {
        ResourceDO resource = resourceService.getResourceByResKey(reskey);
        if (ObjectUtils.isEmpty(resource) || StringUtils.isEmpty(resource.getRealValue())) {
            logger.error("requestId : {}, can not get resource by res key: {}", RequestSession.getRequestId(), reskey);
            throw new Exception("can not get resource by res key.");
        }

        return Integer.valueOf(resource.getRealValue());
    }
}
