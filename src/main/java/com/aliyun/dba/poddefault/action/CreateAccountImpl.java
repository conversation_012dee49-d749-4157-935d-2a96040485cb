package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Account;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccountListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.HashUtils;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.*;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.aliyun.dba.dbs.idao.AccountIDao;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_VERSION_MYSQL_57;
import static com.aliyun.dba.dbs.support.AccountPriviledgeType.PRIVILEDGE_ADMIN_DATA_TRANSFER;
import static com.aliyun.dba.dbs.support.DbsSupport.BIZ_TYPE_USER;
import static com.aliyun.dba.support.property.RdsConstants.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateAccountImpl")
public class CreateAccountImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateAccountImpl.class);

    @Resource
    protected AccountService accountService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DbossApi dbossApi;
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected CustinsService custinsService;
    @Autowired
    private CustinsIDao custinsIDao;
    @Autowired
    private AccountIDao accountIDao;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (replicaSet.getInsType() != ReplicaSet.InsTypeEnum.MAIN) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            } else if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVE && replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String accountPrivilegeDesc = parameterHelper.getAccountPrivilege();
            int adminType = parameterHelper.getAdminType();
            boolean createAdminAccount = adminType > 0;
            String accountType = parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE, CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON);
            Account.PriviledgeTypeEnum accountPrivilegeType = this.getPrivilegeTypeEnum(accountType, adminType);
            String accountName = parameterHelper.checkAccountName(parameterHelper.getAccountName(), replicaSet.getServiceVersion());
            String bizType = parameterHelper.getParameterValue(ParamConstants.ACCOUNT_BIZ_TYPE, BIZ_TYPE_USER);
            String replicaSetName = parameterHelper.getDBInstanceName();
            String password = parameterHelper.getAndCheckDecryptedAccountPassword();
            String dbInfo = parameterHelper.getDbInfo();
            List<String> dbNames = parameterHelper.getDBNames();
            String comment = parameterHelper.getAndCheckAccountDesc();

            boolean createByworkfolow = !ReplicaSetService.isTDDL(replicaSet)
                    && CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER.equals(accountType);
            long taskId;
            Long accountId = 0L;
            Integer accountStatus;

            // 任务流来创建账户, 主要对于需要记录元数据的特殊账户, 包括添加账户元数据和下发创建账户的任务两个流程
            if (createByworkfolow) {
                // FIXME 需要 dbaas meta api 添加新的状态 STATE_ACCOUNT_MODE_UPGRADING, 更新实例状态, 正在创建高权限账户
                // dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetName, ReplicaSet.StatusEnum.STATE_ACCOUNT_MODE_UPGRADING);
                Account queryExample = new Account();
                queryExample.setBizType(BIZ_TYPE_USER);
                queryExample.setStatus(Account.StatusEnum.ACTIVE);
                queryExample.setPriviledgeType(Account.PriviledgeTypeEnum.ALIYUN_SUPER);
                try {
                    // 查询元数据中是否有高权限账号
                    String superName = null;
                    AccountListResult accountListResult = dBaasMetaService.getDefaultClient().listReplicaSetAccountsByExample(requestId, replicaSetName,
                            null, 20, queryExample);
                    if (accountListResult != null && accountListResult.getItems() != null && !accountListResult.getItems().isEmpty()) {
                        // 查询用户实例上是否有高权限账号
                        Account superAccountMeta = accountListResult.getItems().get(0);
                        superName = superAccountMeta.getName();
                    }
                    //遍历用户实例账号列表，判断用户实例上是否有高权限账号
                    //判断高权限账号是否和普通账号类似，如果类似则不让创建，抛出错误
                    Map<String,Integer> accountCount = dbossApi.getAccountCount(String.valueOf(replicaSet.getId().intValue()),null, RdsConstants.ROLETYPE_USER);
                    if (accountCount != null && !accountCount.isEmpty() && accountCount.get("accounts") > 0) {
                        Integer totalCount = accountCount.get("accounts");
                        List<Map<String, Object>> accountList = dbossApi.queryAccounts(
                                replicaSet.getId().intValue(), null, null, 0, totalCount, null);
                        for (Map<String, Object> acc: accountList) {
                            String accName = (String) acc.get("accountName");
                            if (accName.equalsIgnoreCase(accountName)) {
                                throw new RdsException(ErrorCode.ACCOUNTNAME_ALREADYEXISTS);
                            }
                            else if(accName.equals(superName)){
                                // 如果用户使用高权限账号在数据链路创建大小写不同的相似普通账号，之后通过数据链路删除高权限账号，再创建一个完全不同的账号，用ignore会报错
                                throw new RdsException(ErrorCode.ACCOUNTQUOTA_EXCEEDED);
                            }
                        }
                    }

                    if (superName != null) {
                        // 元数据中存在高权限账号，用户实例中不存在，且满足创建该账号的条件，重刷元数据
                        dBaasMetaService.getDefaultClient().deleteReplicaSetAccount(requestId, replicaSetName, superName);
                    }
                } catch (ApiException e) {
                    if (e.getCode() != 404) {
                        throw e;
                    }
                }
                Account accountMeta = new Account();
                accountMeta.setName(accountName);
                accountMeta.setPassword(HashUtils.getMysqlHashString(password));
                accountMeta.setComment(comment);
                accountMeta.setPriviledgeType(accountPrivilegeType);
                accountMeta.setBizType(bizType);
                accountMeta.setStatus(Account.StatusEnum.CREATING);
                accountMeta = dBaasMetaService.getDefaultClient().createAccountForReplicaSet(requestId, replicaSetName, accountMeta);
                accountId = accountMeta.getId();

                // 下发创建账户任务
                Object workflowTaskId = workFlowService.dispatchTask(replicaSet, "create_user_account", "{}", 0);

                // 更新account mode
                custInstanceDO = custinsIDao.getCustInstanceByCustinsId(replicaSet.getId().intValue());
                custInstanceDO.setMaxAccounts(null);
                custInstanceDO.setMaxDbs(null);
                custInstanceDO.setAccountMode(CustinsSupport.NEW_CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED);
                custinsIDao.updateCustinsAccountMode(custInstanceDO);

                custinsService.updateCustInstanceStatusByCustinsId(
                        replicaSet.getId().intValue(), com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_TRANS,
                        CustinsState.STATE_ACCOUNT_MODE_UPGRADING.getComment());

                taskId = Math.round(NumberUtils.toDouble(workflowTaskId.toString()));
                accountStatus = DbsSupport.STATUS_CREATEING;
            // DBOSS 直接创建账户
            } else {
                //防止普通账号和高权限账号名称类似，导致删除，修改权限等报错
                AccountsDO checkAccount = accountIDao.queryAccountByAccountName(replicaSet.getId().intValue(),accountName);
                if (checkAccount != null) {
                    throw new RdsException(ErrorCode.ACCOUNTNAME_ALREADYEXISTS);
                }
                if (!replicaSetService.replicasetInAvailableStatus(replicaSet.getStatus())) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }

                int intAccountPrivilegeType;
                if (createAdminAccount) {
                    AccountPriviledgeType aptEnum = AccountPriviledgeType.getAccountPriviledgeTypeByAdminType(adminType);
                    intAccountPrivilegeType = aptEnum.getValue();
                } else {
                    intAccountPrivilegeType = CustinsSupport.getAccountPrivilegeType(accountType);
                }

                Map<String, Object> account = new HashMap<>(7);
                account.put("accountName", accountName);
                account.put("accountType", accountType);
                account.put("privilegeType", intAccountPrivilegeType);
                account.put("custinsId", replicaSet.getId());
                account.put("password", password);
                account.put("comment", comment);
                account.put("requestId", requestId);
                List<Map<String, String>> privileges = accountService.getDatabasePrivileges(dbNames, accountPrivilegeDesc, dbInfo, replicaSet.getService());
                if (!CollectionUtils.isEmpty(privileges)) {
                    account.put("privileges", privileges);
                }
                dbossApi.createAccount(account);
                taskId = 0L;
                accountStatus = DbsSupport.STATUS_ACTIVE;
                // 是否需要添加元数据
                boolean needAccountMeta = createAdminAccount || CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER.equals(accountType);
                if (needAccountMeta) {
                    Account accountMeta = new Account();
                    accountMeta.setName(accountName);
                    accountMeta.setPassword(HashUtils.getMysqlHashString(password));
                    accountMeta.setComment(comment);
                    accountMeta.setPriviledgeType(accountPrivilegeType);
                    accountMeta.setBizType(bizType);
                    accountMeta.setStatus(Account.StatusEnum.ACTIVE);
                    accountMeta = dBaasMetaService.getDefaultClient().createAccountForReplicaSet(requestId, replicaSetName, accountMeta);
                    accountId = accountMeta.getId();
                }
            }

            Map<String, Object> data = new HashMap<>();
            data.put("AccountName", accountName);
            data.put("AccountID", accountId);
            data.put("AccountStatus", accountStatus);
            data.put("TaskId", taskId);
            data.put(ParamConstants.ACCOUNT_TYPE, accountType);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " CreateAccount failed: " + re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataRequestFailed", "Request dbaas meta API failed."});
        } catch (Exception ex) {
            logger.error(requestId + " CreateAccount failed: " + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }


    private Account.PriviledgeTypeEnum getPrivilegeTypeEnum(String accountType, int adminType) {
        if (PRIVILEDGE_ADMIN_DATA_TRANSFER.getAdminType() == adminType) {
            return Account.PriviledgeTypeEnum.DATA_TRANSFER;
        } else if (CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER.equals(accountType)) {
            return Account.PriviledgeTypeEnum.ALIYUN_SUPER;
        } else {
            return Account.PriviledgeTypeEnum.NORMAL;
        }
    }

}
