package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.OssBakRestoreParam;
import com.aliyun.dba.base.parameter.backup.OssUserBakFileParam;
import com.aliyun.dba.base.response.backup.UserOssBackupFileRecordResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.AligroupCreateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.AliyunCreateDBInstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyUserBakFileImpl")
public class ModifyUserBakFileImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteUserBackupFileRecordImpl.class);

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    private AliyunCreateDBInstanceService aliyunDBInstanceService;
    @Resource
    private AligroupCreateDBInstanceService aligroupDBInstanceService;
    @Resource
    private AligroupService aligroupService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private BackupService backupService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        Integer userId = mysqlParameterHelper.getAndCreateUserId();
        String comment = paramSupport.getParameterValue(params, "Comment");
        Integer retention = null;
        try {
            retention = Integer.valueOf(paramSupport.getParameterValue(params, "Retention"));
        }catch (NumberFormatException ignored){
        }
        Map<String, Object> data = new HashMap<>();
        User user;
        try {
            user = dBaasMetaService.getDefaultClient().getUserById(requestId, userId, false);
        } catch (ApiException e) {
            throw new RdsException(new Object[]{ResultCode.CODE_SERVER, "ModifyUserBakFile: " , e.getMessage()});
        }
        String bid = user.getBid();
        String uid = user.getAliUid();
        String instanceName = paramSupport.getParameterValue(params,  "InstanceName");

        OssBakRestoreParam baseParam = OssBakRestoreParam.builder()
                .user_id(bid)
                .uid(uid)
                .requestId(requestId)
                .instanceName(instanceName)
                .build();

        try {
            UserOssBackupFileRecordResponse result= backupService.describeBakOssRestoreRecords(baseParam).get(0);
            if(retention!=null) { // validate after set retention backup must be valid.
                Calendar cal = Calendar.getInstance();
                cal.setTime(result.getGmtModified());
                cal.add(Calendar.DATE, retention);
                if(cal.getTime().compareTo(new Date()) < 0){
                    throw new RdsException(ErrorCode.INVALID_PARAMETERS);
                }
            }

            backupService.UpdateUserBakFile(OssUserBakFileParam.builder().instanceName(instanceName).uid(uid).user_id(bid).comment(comment).retention(retention).build());

            data.put("InstanceName", result.getInstanceName());
            data.put("Id", result.getId());
            return data;
        } catch (Exception e) {
            logger.error(requestId + " DeleteUserBackupFileRecord: " + e.getMessage());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "DeleteUserBackupFileRecord: " , e.getMessage()});
        }
    }
}

