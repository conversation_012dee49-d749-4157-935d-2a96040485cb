package com.aliyun.dba.poddefault.action.service.migrate.basic;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.RebuildReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.migrate.BaseMigrateDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodType;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

@Service
public class BasicMigrateDBInstanceService extends BaseMigrateDBInstanceService {
    @Override
    public Object allocateResource(String requestId, ReplicaSet replicaSet, JSONObject taskParam, Map<String, String> params) throws Exception{
        RebuildReplicaResourceRequest allocateReplicaResource = null;
        boolean isSuccess = false;
        try {
            // 申请资源
            String resourceStrategy = paramSupport.getResourceStrategy(params);
            PodType podType = paramSupport.getTargetRuntimeType(replicaSet, params);
            allocateReplicaResource = podReplicaSetResourceHelper.allocateRebuildResource4Basic(replicaSet, podType);
            if (StringUtils.isNotEmpty(resourceStrategy)) {
                // 传递资源策略
                if (allocateReplicaResource.getScheduleTemplate() == null) {
                    ScheduleTemplate scheduleTemplate = new ScheduleTemplate();
                    scheduleTemplate.setResourceStrategy(resourceStrategy);
                    allocateReplicaResource.setScheduleTemplate(scheduleTemplate);
                } else {
                    allocateReplicaResource.getScheduleTemplate().setResourceStrategy(resourceStrategy);
                }
            }
            Replica replicaForRecover = mySQLService.getReplicaByRole(requestId, replicaSet.getName(), Replica.RoleEnum.MASTER);
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                rundPodSupport.completeReplicaNetworkConfig(allocateReplicaResource, replicaForRecover);
            }
            commonProviderService.getDefaultApi().allocateReplicaSetResourceForRebuild(
                    requestId, replicaSet.getName(), replicaForRecover.getId(), allocateReplicaResource);
            CustInstanceDO custInstanceDO = custinsService.getCustInstanceByInsName(
                    null, allocateReplicaResource.getTmpReplicaSetName(), CUSTINS_INSTYPE_TMP);
            if (custInstanceDO == null) {
                logger.error("Cannot find tmp custins [{}]", allocateReplicaResource.getTmpReplicaSetName());
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            custInstanceDO.setInsType(CUSTINS_INSTYPE_MIRROR);
            custInstanceDO.setGmtModified(new Date());
            custinsService.updateCustInstance(custInstanceDO);

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.TRANSING.toString());

            taskParam.put("srcReplicaSetName", replicaSet.getName());
            taskParam.put("destReplicaSetName", allocateReplicaResource.getTmpReplicaSetName());
            taskParam.put("srcReplicaId", replicaForRecover.getId());
            taskParam.put("destReplicaId", mySQLService.getReplicaByRole(
                    requestId, allocateReplicaResource.getTmpReplicaSetName(), Replica.RoleEnum.MASTER).getId());

            Integer priority = paramSupport.getPriority(params);
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSet.getName(), "mysql", PodDefaultConstants.TASK_MIGRATE_BASIC_INS, taskParam.toString(), priority);

            isSuccess = true;
            return taskId;
        } catch (Exception e) {
            logger.error(requestId + " Exception: ", e);
            throw e;
        } finally {
            // 处理失败时释放资源
            if (null != allocateReplicaResource && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, allocateReplicaResource.getTmpReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }
}
