package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class StoppedReplicaSetMaintainService {
    @Autowired
    ReplicaSetService replicaSetService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;

    /**
     * 启动暂停实例，以便进行维护任务
     * 注意，启动后不会修改实例status
     */
    public Map<String, Object> dispatchStartTask(String requestId, ReplicaSet replicaSet) throws Exception {
        if (!ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus())) {
            throw new Exception("replicaSet status is not stopped, do not need start!");
        }

        Map<String, Object> data = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", requestId);
        jsonObject.put("replicaSetName", replicaSet.getName());

        // just for compatible，can be deleted after provider published
        if (replicaSetService.isReplicasetInStopStage2(requestId, replicaSet.getName())) {
            jsonObject.put("startInsFromStopStage2", "true");
        }

        // 设置资源模板,以便provider能成功申请到资源
        String rsTemplateName = podTemplateHelper.getRsTemplateNameForReplicaSet(requestId, replicaSet);
        if (StringUtils.isNotBlank(rsTemplateName)) {
            jsonObject.put("rsTemplateName", rsTemplateName);
        }

        String parameter = jsonObject.toJSONString();
        Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(),
                PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_START_STOPPED_INS_FOR_MAINTAIN, parameter, 0);

        // 清除定时任务
        dBaasMetaService.getDefaultClient().deleteReplicaSetLabel(requestId, replicaSet.getName(), PodDefaultConstants.TASK_STOP_INS_STAGE_2);
        dBaasMetaService.getDefaultClient().deleteReplicaSetLabel(requestId, replicaSet.getName(), PodDefaultConstants.TASK_START_INS_FROM_STOP_STAGE_2);

        data.put("TaskId", taskId);
        data.put("DBInstanceName", replicaSet.getName());
        return data;
    }
}
