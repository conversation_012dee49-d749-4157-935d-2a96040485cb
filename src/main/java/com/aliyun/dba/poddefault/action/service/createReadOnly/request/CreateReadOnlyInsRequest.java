package com.aliyun.dba.poddefault.action.service.createReadOnly.request;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.GeneralCloudDisk;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateReadOnlyInsRequest {
    private String requestId;
    private Integer userId;
    private String dbInstanceName;
    private String insType;
    private String dbType;
    private String dbVersion;
    private String gdnInstanceName;
    private String connType;
    private String connStrPortStr;
    private Integer diskSize;
    private String classCode;
    private String clusterName;
    private String tddlBizType;
    private String tddlRegionConfig;
    private String instructionSetArch;
    private String dbEngine;
    private Boolean isDhg;
    private Boolean isArmIns;
    private boolean isAnalyticReadOnlyIns;
    private String rsTemplateName;
    private GeneralCloudDisk generalCloudDisk;
    private String optimizedWritesInfo;
    private String bizType;
    private ReplicaSet primaryReplicaSet;
    private Boolean isCreatingGdnInstance;
    private String centerRegionId;
    private String diskType;
    //autopl 配置
    private Long provisionedIops;
    private boolean burstingEnabled;
    private String compressionMode;
    private Double compressionRatio;
    private Integer diskSizeGBBeforeCompression;
    private String readInsName;
    private String bid;
    private String uid;
    private String insTypeDesc;
    private String vpcId;
    private String vSwitchId;
    private AVZInfo avzInfo;
    private String regionId;
    private int readInsReplicaCount;
    private String performanceLevel;
    private Boolean isSingleTenant;
    private Boolean isXdbEngine;
    private String targetMinorVersion;
    private InstanceLevel primaryInsInstanceLevel;
    private String iPAddress;
    private String vpcInstanceId;
    private String comment;
    private String orderId;
    private String accessId;

    private String connectionString;

    //autoScale
    private String storageAutoScale;
    private String storageUpperBound;
    private String storageThreshold;

    //serverless
    private ServerlessSpec serverlessSpec;

    private Map<String, Boolean> activities;

    private AliyunInstanceDependency dependency;
    private Map<String, String> params;

    private Long maxScaleTaskId;

    public CreateReadOnlyInsRequest(AliyunInstanceDependency dependency, Map<String, String> params) {
        this.dependency = dependency;
        this.params = params;
    }

    public void setServerlessStorageAutoScale() {
        this.storageAutoScale = String.valueOf(ServerlessConstant.SERVERLESS_STORAGE_AUTO_SCALE);
        this.storageUpperBound = String.valueOf(ServerlessConstant.SERVERLESS_STORAGE_UPPER_BOND);
        this.storageThreshold = String.valueOf(ServerlessConstant.SERVERLESS_STORAGE_THRESHOLD);
    }

    public void setServerlessInfo() throws RdsException {
        // 用户信息初始化
        serverlessSpec = new ServerlessSpec(params);
        if (serverlessSpec.getRcu() == null) {
            serverlessSpec.setRcu(serverlessSpec.getScaleMin());
        }

        // RCU range check: 0.5 <= RCU value <= 64 and is a multiple of 0.5
        if(!(0.5 <= serverlessSpec.getScaleMin() &&
                serverlessSpec.getScaleMin() <= serverlessSpec.getScaleMax() &&
                serverlessSpec.getScaleMax() <= 64 &&
                serverlessSpec.getScaleMin() % 0.5 == 0 &&
                serverlessSpec.getScaleMax() % 0.5 == 0)){
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "CreateDB: RCU range should be 0.5 ~ 64 and a multiple of 0.5");
        }

        if (serverlessSpec.getAutoPause()) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "Serverless read doesn't support autoPause");
        }
        
        if (serverlessSpec.getSwitchForce() != null && !serverlessSpec.getSwitchForce()) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "Serverless read doesn't support closing forceSwitch");
        }
    }

    /**
     *设置活动相关参数
     */
    public void setActivities() {
        String activitiesJson = dependency.getMysqlParamSupport().getParameterValue(params, "activities");
        activities = JSONObject.parseObject(activitiesJson, Map.class);
    }
}
