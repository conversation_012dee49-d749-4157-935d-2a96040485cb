package com.aliyun.dba.poddefault.action.service.endpoint;

import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang.NotImplementedException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class EndPointServiceFactoryImpl {

    @Resource
    private EndpointLvsService endpointLvsService;

    public EndPointService getService(ReplicaSet.ConnTypeEnum connType) throws RdsException {
        if (ReplicaSet.ConnTypeEnum.LVS == connType) {
            return endpointLvsService;
        }
        // TODO 优化其他类型链路的错误码
        throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
    }

}
