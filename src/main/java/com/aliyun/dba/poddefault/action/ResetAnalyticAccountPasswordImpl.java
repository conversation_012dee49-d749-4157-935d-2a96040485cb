package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultResetAnalyticAccountPasswordImpl")
public class ResetAnalyticAccountPasswordImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ResetAnalyticAccountPasswordImpl.class);

    @Resource
    RdsApi rdsApi;

    @Resource
    protected DBaasMetaService dbaasMetaService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String analyticInsName = paramSupport.getParameterValue(params, ParamConstants.ANALYTIC_DB_INSTANCE_NAME);
            String uid = paramSupport.getParameterValue(params, ParamConstants.UID);
            String userId = paramSupport.getParameterValue(params, ParamConstants.USER_ID);
            String regionId = paramSupport.getParameterValue(params, ParamConstants.REGION_ID);
            String accountName = paramSupport.getParameterValue(params, ParamConstants.ACCOUNT_NAME);
            String accountPassword = paramSupport.getParameterValue(params, ParamConstants.ACCOUNT_PASSWORD);


            // 查询关联关系
            podCommonSupport.checkAnalyticDBInstanceRelation(requestId, custins.getInsName(), analyticInsName);

            logger.info("requestId : {}, request to ResetAnalyticAccountPassword", requestId);

            String finalRequestId = String.format("%s_%s", requestId, new Random().nextInt(Integer.MAX_VALUE));
            Map<String, Object> result = rdsApi.getDataByRdsApi(new HashMap<String, String>() {{
                put("Action", "ResetAccountPassword");
                put("DBInstanceName", analyticInsName);
                put("UID", uid);
                put("User_id", userId);
                put("RegionID", regionId);
                put("RequestId", finalRequestId);
                put("accountName", accountName);
                put("accountPassword", accountPassword);
                put("DBInstanceModelType", "cluster");
            }}, ParamConstants.YAOCHI_ACCESS);
            logger.info("result:{}", JSONObject.toJSONString(result));
            return result;

        } catch (RdsException ex) {
            log.error("ResetAnalyticAccountPassword failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("ResetAnalyticAccountPassword Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
