package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultResetAdminAccountPasswordImpl")
public class ResetAdminAccountPasswordImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ResetAdminAccountPasswordImpl.class);
    @Autowired
    private ResetAccountPasswordImpl resetAccountPassword;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {
        return resetAccountPassword.doActionRequest(custInstanceDO,map);
    }
}
