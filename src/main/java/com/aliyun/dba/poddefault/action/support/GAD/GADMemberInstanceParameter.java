package com.aliyun.dba.poddefault.action.support.GAD;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;

@Data
public class GADMemberInstanceParameter {
    private String accessKey;
    private String accessSecret;
    private String stsToken;
    private String ramAuthParams;
    private String dbList;
    // 必填
    private String regionId;
    private String zoneId;
    private String instanceNetworkType="Classic"; // VPC：VPC网络 Classic：经典网络（默认）
    private String vpcId;
    private String vSwitchId;
    private String securityIPList = "127.0.0.1"; // 可以给127.0.0.1
    private String dtsInstanceClass = "small"; // 可以给127.0.0.1
    private String dtsConflict;


    // 可以默认从Central实例获取，如果是创建Central实例则必填
    private String dBInstanceNetType; // Internet：公网连接 Intranet：内网连接
    private String dBInstanceClass;
    private String engine;
    private String engineVersion;
    private String dBInstanceStorage;
    private String payType;

    // 以下非必传参数
    private String zoneIdSlave1;
    private String zoneIdSlave2;
    private String dBInstanceDescription;
    private String connectionMode; // Standard：标准访问模式 Safe：数据库代理模式
    private String privateIpAddress;
    private String usedTime;
    private String period;
    private String resourceGroupId;
    private String dBInstanceStorageType;
    private String encryptionKey;
    private String roleARN;
    private String autoRenew;
    private String category; //Basic：基础版 HighAvailability：高可用版 AlwaysOn：集群版 Finance：三节点企业版
    private String dBParamGroupID;
    private String dBTimeZone;
    private String dBIsIgnoreCase;
    private String autoUpgradeMinorVersion;
    private String targetMinorVersion;
    private String storageAutoScale;
    private String storageThreshold;
    private String storageUpperBound;
    private String accountName;
    private String accountPassword;
    private String accountType;//Normal,Super
    private String accountDescription;
    private static Field[] fields = GADMemberInstanceParameter.class.getDeclaredFields();
    public void initFromCentralReplicaSet(ReplicaSet replicaSet){
        dBInstanceNetType = "Intranet"; // 默认都是内网
        dBInstanceClass = replicaSet.getClassCode();
        engine = replicaSet.getService();
        engineVersion = replicaSet.getServiceVersion();
        dBInstanceStorage = Integer.valueOf(Objects.requireNonNull(
                replicaSet.getDiskSizeMB())/1024).toString();
    }
    public void initFromParameterMap(Map<String,String> parameters){
        for (Field f: fields){
            String paramName = f.getName().toLowerCase();
            try {
                if(parameters.containsKey(paramName)){
                    f.set(this, parameters.get(paramName));
                }
            } catch (IllegalAccessException ignored) {
            }
        }

    }
}
