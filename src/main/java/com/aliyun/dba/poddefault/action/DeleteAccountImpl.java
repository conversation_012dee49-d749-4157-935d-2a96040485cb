package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.Account;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.dataobject.AccountsQuery;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.poddefault.action.support.PodAccountConstants;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import io.kubernetes.client.proto.V1Apps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.ErrorCode.ACCOUNT_NOT_FOUND;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteAccountImpl")
public class DeleteAccountImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.DeleteDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    ReplicaSetService replicaSetService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private DbossApi dbossApi;
    @Autowired
    private AccountIDao accountIDao;
    @Autowired
    private CustinsIDao custinsIDao;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String accountName = mysqlParamSupport.getAccountName(params);
            if (PodAccountConstants.RESERVED_ACCOUNTS.contains(accountName)) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_ACCOUNTNAME);
            }
            Account replicaSetAccount = dBaasMetaService.getDefaultClient().getReplicaSetAccount(requestId, replicaSet.getName(), accountName, true);

            Map<String, Object> dbossRequest = new HashMap<>();
            dbossRequest.put("accountName", accountName);
            dbossRequest.put("custinsId", replicaSet.getId());
            dbossRequest.put("requestId", requestId);

            Map<String, Object> responseData = new HashMap<>();
            //  云上高权限账号用任务流删除，其它场景都走 DBOSS
            boolean deleteByWorkflow = ((replicaSetAccount != null)
                    && replicaSetAccount.getPriviledgeType().equals(Account.PriviledgeTypeEnum.ALIYUN_SUPER)
                    && !ReplicaSetService.isTDDL(replicaSet));
            if (deleteByWorkflow) {
                // 幂等操作
                if (Objects.equals(replicaSetAccount.getStatus(), Account.StatusEnum.DELETING)) {
                    return createErrorResponse(ErrorCode.ACCOUNT_NOT_FOUND);
                }
                dBaasMetaService.getDefaultClient().updateReplicaSetAccount(requestId,replicaSet.getName(),accountName,replicaSetAccount.status(Account.StatusEnum.DELETING));
                String domain = PodDefaultConstants.DOMAIN_MYSQL;
                String taskKey = "delete_user_account";
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("requestId", requestId);
                jsonObject.put("replicaSetName", replicaSet.getName());
                String parameter = jsonObject.toJSONString();
                Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, parameter, 0);

                // 更新account mode
                custins = custinsIDao.getCustInstanceByCustinsId(replicaSet.getId().intValue());
                custins.setMaxAccounts(null);
                custins.setMaxDbs(null);
                custins.setAccountMode(CustinsSupport.NEW_CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE);
                custinsIDao.updateCustinsAccountMode(custins);

                responseData.put("TaskId", taskId);
            } else {
                try {
                    dbossApi.deleteAccount(dbossRequest);
                } catch (RdsException e) {
                    if (!ACCOUNT_NOT_FOUND.getSummary().equals(e.getErrorCode()[1])) {
                        throw e;
                    }else{
                        return createErrorResponse(ErrorCode.ACCOUNT_NOT_FOUND);
                    }
                }
                if (replicaSetAccount != null) {
                    accountIDao.deleteAccountByIdDirectly(Integer.valueOf(replicaSetAccount.getId().toString()), accountName);
                }
                responseData.put("TaskId", 0);
            }
            responseData.put("AccountName", accountName);
            responseData.put("DBInstanceName", replicaSet.getName());
            return responseData;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
