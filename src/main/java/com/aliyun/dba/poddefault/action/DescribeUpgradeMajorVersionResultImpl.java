package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.idao.UpgradeReportIDao;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeUpgradeMajorVersionResultImpl")
public class DescribeUpgradeMajorVersionResultImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeUpgradeMajorVersionResultImpl.class);

    @Autowired
    protected UpgradeReportIDao upgradeReportIDao;

    @Autowired
    protected CustinsService custinsService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        IAction actionImpl = SpringContextUtil.getBeanByClass(com.aliyun.dba.physical.action.DescribeUpgradeMajorVersionResultImpl.class);
        return actionImpl.doActionRequest(custins, params);
    }
}
