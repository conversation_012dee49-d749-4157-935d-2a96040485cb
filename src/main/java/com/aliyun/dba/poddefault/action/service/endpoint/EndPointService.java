package com.aliyun.dba.poddefault.action.service.endpoint;

import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointGroup;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.adb_vip_manager_client.model.LinkDetailResponse;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.support.property.RdsException;

import java.util.List;
import java.util.Map;

public interface EndPointService {

    void delete(String requestId, Endpoint endpoint) throws Exception;

    String upperCaseFirst(String val);

    Boolean checkNodeItems(String requestId, String dbInstanceName, EndpointWeightConfig endpointWeightConfig, String endpointType) throws ApiException, RdsException;

    List<EndpointGroup> getAllEndpointGroups(String requestId, String replicaSetName) throws Exception;

    List<CustinsConnAddrDO> getEndpoints(String requestId, Long endpointId, Integer replicaSetId);

    void checkEndpointRSEmpty(EndpointWeightConfig weightConfig) throws RdsException;

    Boolean checkConnAddrCustExist(String requestId, String connAddrCust) throws ApiException;

    Endpoint createPrivateEndpointAddress(String requestId, String replicaSetName, String vpcId, String vSwitchId, String vip,
                                          String connStringPrefix, String port, String endpointType, EndpointWeightConfig weightConfig,
                                          EndpointGroup endpointGroup, Integer userVisible, String vpcInstanceId) throws Exception;

    String createVpcMapping(String requestId, String replicaSetName, String vpcId, String vip, String vport) throws Exception;

    Integer compareAndCheckWeightConfigs(String requestId, String replicaSetName, EndpointGroup endpointGroup, EndpointWeightConfig newWeightConfig) throws RdsException, ApiException, InterruptedException;

    void refreshRealServers(String requestId, String replicaSetName, Endpoint endpoint, EndpointWeightConfig weightConfig, List<Replica> replicas) throws Exception;

    List<Replica> getAllReplicasByNames(String requestId, List<String> replicaSetNameList) throws ApiException;

    void setRealServerWeight(String requestId, String replicaSetName, Endpoint endpoint, EndpointWeightConfig weightConfig, List<Replica> replicas) throws Exception;

    LinkDetailResponse getRealServerDetail(String requestId, String replicaSetName, Endpoint endpoint) throws com.aliyun.dba.adb_vip_manager_client.ApiException;

    List<Map<String, Object>> getMysqlClusterNodes(String requestId, EndpointGroup endpointGroup, String replicaSetName) throws ApiException, RdsException;

    EndpointWeightConfig getWeightConfig(String requestId, String replicaSetName, Long endpointGroupId) throws ApiException;

    void inactiveReplicaSet(String requestId, String replicaSetName) throws ApiException;

    void activeReplicaSet(String requestId, String replicaSetName) throws ApiException;

    EndpointWeightConfig genEndpointWeightConfig(String replicaSetName, String nodeId);

    EndpointWeightConfig createPrimaryWeightConfig(String requestId, String replicaSetName) throws ApiException;

    EndpointGroup getAndCheckEndpointGroup(String requestId, String replicaSetName, String endpointId) throws RdsException, ApiException;

    Endpoint getAndCheckEndpoint(String requestId, String replicaSetName, String connString, Long endpointGroupId) throws ApiException, RdsException;

    EndPoint allocateEndpointResource(String requestId, String replicaSetName, String ipType,
                                      EndPoint.ConnTypeEnum connTypeEnum, String connectionStringPrefix,
                                      EndPoint.EndPointTypeEnum endPointType, String port) throws com.aliyun.apsaradb.activityprovider.ApiException;

    void releaseConnectionString(String requestId, String replicaSetName, String connectionString) throws com.aliyun.dba.adb_vip_manager_client.ApiException;

    void checkConnectionStringPrefix(String requestId, ReplicaSet replicaSet, String connectionStringPrefix) throws RdsException, ApiException;

    List<Endpoint> listReplicaSetEndpoint(String requestId, String replicaSetName) throws ApiException;

    List<Replica> getAllReplicasInReplicaSet(String requestId, String replicaSetName) throws ApiException;

    String allocateConnectionString(String requestId, String replicaSetName, String connectionStringPrefix) throws com.aliyun.dba.adb_vip_manager_client.ApiException, RdsException;

    void checkValidForVpcId(String vpcId) throws RdsException;

    void checkValidForVswId(String vswId) throws RdsException;
}
