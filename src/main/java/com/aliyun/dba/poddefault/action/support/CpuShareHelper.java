package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CpuShareHelper {

    @Resource
    private DBaasMetaService dbaasMetaService;

    private final Cache<String, String> cpuStrategy = CacheBuilder.newBuilder()
            .maximumSize(64)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    private final static String CPU_STRATEGY_PREFIX = "NEW_ARCH_CPU_STRATEGY_";
    private final static String DEFAULT = "default";

    public static String ALLOCATE_CPU_CORES = "allocate_cpu_cores";

    public static String ALLOCATE_MEM_SIZE_MB = "allocate_mem_size_mb";

    /**
     * 获取Replica Cpu limit
     *
     * @param replica
     * @param service
     * @param instanceLevel
     * @return
     */
    public Double getReplicaCpuLimit(String requestId, Replica replica, String service, InstanceLevel instanceLevel) {
        final ImmutableList<InstanceLevel.CategoryEnum> serverless_category = ImmutableList.of(InstanceLevel.CategoryEnum.SERVERLESS_BASIC, InstanceLevel.CategoryEnum.SERVERLESS_STANDARD);
        boolean isServerless = instanceLevel != null && serverless_category.contains(instanceLevel.getCategory());
        if(isServerless){
            log.info("is Serverless skip.");
            return null;
        }
        if (replica.getCpuCores() == null) {
            log.info("cpu cores is null, skip.");
            return null;
        }
        List<CpuShareStrategy> cpuShareStrategies = getStrategy(requestId, service);
        if (CollectionUtils.isEmpty(cpuShareStrategies)) {
            log.info("can not find engine CpuShareStrategy, skip.");
            return null;
        }
        try {
            EcsHost ecsHost = dbaasMetaService.getDefaultClient().getEcsHostByHostName(requestId, replica.getHostName(), true);
            String ecsClassCode = ecsHost.getClassCode();
            Optional<CpuShareStrategy> optional = cpuShareStrategies.stream()
                    .filter(v -> v.getEcsClassCode().stream().anyMatch(e -> StringUtils.startsWith(ecsClassCode, e)))
                    .findFirst();
            if (!optional.isPresent()) {
                log.info("can not find optional CpuShareStrategy, ecs classCode: {}, ", ecsClassCode);
                return null;
            }
            CpuShareStrategy cpuShareStrategy = optional.get();
            Map<String, Number> cpusConfig = cpuShareStrategy.getCpuShares();
            if (!cpusConfig.containsKey(replica.getCpuCores().toString()) && !cpusConfig.containsKey(DEFAULT)) {
                log.info("can not find suitable cpu config, cpuConfig: {}", JSON.toJSONString(cpusConfig));
                return null;
            }
            Number rate = cpusConfig.get(replica.getCpuCores().toString());
            if (rate == null) {
                rate = cpusConfig.get(DEFAULT);
            }
            Double cpuLimit = replica.getCpuCores() * rate.doubleValue();
            log.info("type [{}] replicaId [{}] cpu limit finally is [{}]", cpuShareStrategy.getType(), replica.getId(), cpuLimit);
            return cpuLimit * (replica.getRcu() == null ? PodDefaultConstants.SERVERlESS_RCU_DEFAULT_VALUE : replica.getRcu());
        } catch (Exception e) {
            log.error("get replicaId [{}] cpu limit failed, msg is {}", replica.getId(), e.getMessage());
        }
        return null;
    }


    /**
     * 根据数据库引擎类型获取CpuShare策略
     *
     * @param service
     * @return
     */
    private List<CpuShareStrategy> getStrategy(String requestId, String service) {
        String key = StringUtils.upperCase(CPU_STRATEGY_PREFIX + service);
        try {
            String valueStr = cpuStrategy.get(key, () -> {
                ConfigListResult configListResult = dbaasMetaService.getDefaultClient().listConfigs(requestId, key);
                if (CollectionUtils.isEmpty(configListResult.getItems())) {
                    return null;
                }
                List<Config> configs = configListResult.getItems();
                Config config = configs.get(0);
                String value = config.getValue();
                if (StringUtils.isEmpty(value)) {
                    return null;
                }
                return value;
            });
            if (StringUtils.isBlank(valueStr)) {
                return null;
            }
            List<Map<String, Object>> strategies = JSON.parseObject(valueStr, List.class);
            if (CollectionUtils.isEmpty(strategies)) {
                return null;
            }
            log.info("Service is [{}] has find cpu share strategy, value is {}", service, valueStr);
            return strategies.stream()
                    .filter(e -> (e.get("ecsClassCode") != null) && (e.get("cpuShares") != null))
                    .map(e -> new CpuShareStrategy(String.valueOf(e.get("type")),
                            (List<String>) e.get("ecsClassCode"),
                            (Map<String, Number>) e.get("cpuShares")))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Get cpu share strategy failed, msg is {}", e.getMessage());
        }
        return null;
    }


    @Getter
    @AllArgsConstructor
    private static class CpuShareStrategy {
        String type;
        List<String> ecsClassCode;
        Map<String, Number> cpuShares; //cpu核数和 cpu*x 的映射关系
    }


}