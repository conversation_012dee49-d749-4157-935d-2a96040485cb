package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.DTSService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dts20200101.models.StartDtsJobResponse;
import com.aliyun.dts20200101.models.StartDtsJobResponseBody;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultStartAnalyticDBInstanceSyncJobImpl")
public class StartAnalyticDBInstanceSyncJobImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(StartAnalyticDBInstanceSyncJobImpl.class);

    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private DTSService dtsService;

    @Resource
    private CustinsParamService custinsParamService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String analyticInsName = paramSupport.getParameterValue(params, ParamConstants.ANALYTIC_DB_INSTANCE_NAME);
            ReplicaSet analyticIns = podCommonSupport.getCkReplicaSet(analyticInsName);
            // 查询关联关系
            podCommonSupport.checkAnalyticDBInstanceRelation(requestId, custins.getInsName(), analyticInsName);

            CustinsParamDO custinsParam = custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.DTS_INFO);
            if (ObjectUtils.isEmpty(custinsParam)) {
                logger.error("reqeustId : {}. There is no dts info.", requestId);
                throw new Exception("There is no dts info.");
            }
            JSONObject dtsInfo = JSONObject.parseObject(custinsParam.getValue());
            String dtsInstanceId = String.valueOf(dtsInfo.get(PodDefaultConstants.DTS_INSTANCE_ID));
            String dtsJobId = String.valueOf(dtsInfo.get(PodDefaultConstants.DTS_JOB_ID));
            String regionId = paramSupport.getParameterValue(params, ParamConstants.REGION_ID);

            StartDtsJobResponse response = dtsService.startDtsJob(regionId, dtsInstanceId, dtsJobId);
            if (ObjectUtils.isEmpty(response)) {
                logger.error("requestId : {}, StartDtsJobResponse is empty.", requestId);
                throw new Exception("StartDtsJobResponse is empty.");
            }

            Map<String, Object> result = new HashMap<>();
            StartDtsJobResponseBody body = response.getBody();
            result.put("dbInstanceName", analyticInsName);
            result.put("success", body.getSuccess());
            result.put("dynamicMessage", body.getDynamicMessage());
            result.put("errCode", body.getErrCode());
            result.put("errMessage", body.getErrMessage());
            result.put("requestId", requestId);
            return result;


        } catch(RdsException ex) {
            log.error("StartAnalyticDBInstanceSyncJobImpl failed: {}", JSONObject.toJSONString(ex));
            throw ex;
        } catch (Exception ex) {
            log.error("StartAnalyticDBInstanceSyncJobImpl Exception: {}", JSONObject.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
