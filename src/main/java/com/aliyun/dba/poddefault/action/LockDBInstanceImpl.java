package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.commonkindcode.action.DescribeDBInstanceParameterListImpl;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.poddefault.action.service.StoppedReplicaSetMaintainService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultLockDBInstanceImpl")
public class LockDBInstanceImpl implements IAction {


    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterListImpl.class);
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MycnfService mycnfService;

    @Autowired
    private ReplicaSetService replicaSetService;
    @Autowired
    private DBaasMetaService dBaasMetaService;
    @Autowired
    private MysqlParamSupport paramSupport;
    @Resource
    private StoppedReplicaSetMaintainService stoppedReplicaSetMaintainService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        try {
            String requestId = paramSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID);
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParaHelper.getWithoutCheckCustInstance();

            ReplicaSet replicaSet = replicaSetService.getReplicaSet(actionParams);
            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), null);
            boolean isSingleNode = MysqlParamSupport.isSingleNode(instanceLevel);

            if (PodParameterHelper.isAliGroup(replicaSet.getBizType()) && !isSingleNode) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            //校验入参
            String lockReason = mysqlParaHelper.getParameterValue(ParamConstants.LOCK_REASON);
            lockReason = SupportUtils.decode(lockReason);
            if (lockReason == null || lockReason.length() <= 0 || lockReason.length() > 50) {
                throw new RdsException(ErrorCode.INVALID_LOCKREASON);//锁定理由不能为空
            }

            String lockType = mysqlParaHelper.getParameterValue(ParamConstants.LOCK_TYPE);
            lockType = SupportUtils.decode(lockType);
            boolean isReadIns = ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType());
            if (!(lockType.equals("") || lockType.equals("lock_write") || lockType.equals("lock_read_write") || lockType.equals("user_lock"))) {
                throw new RdsException(ErrorCode.INVALID_LOCKTYPE);//只能是openapi的指定值或为空
            }
            else if (lockType.equals("lock_write") && isReadIns){
                throw new RdsException(ErrorCode.INVALID_LOCKTYPE); //只读实例已经是锁读状态
            }

            String forceLock = mysqlParaHelper.getParameterValue(ParamConstants.FORCE_LOCK);
            forceLock = SupportUtils.decode(forceLock);
            if (forceLock.equals("")){
                forceLock = "true"; //默认强制锁定杀掉进程
            }
            if (!(forceLock.equals("true") || forceLock.equals("false"))){
                throw new RdsException(ErrorCode.INVALID_FORCELOCK);//只能是指定值或为空
            }

            if (!(custins.isActive() || ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus()))) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //设置lockMode
            String lockModeStr = mysqlParaHelper.getParameterValue(ParamConstants.LOCK_MODE);
            Integer lockMode;
            if (lockModeStr != null) {
                //有lockMode传入，如杜康下发锁定
                lockMode = CheckUtils.parseInt(lockModeStr, null, null, ErrorCode.INVALID_PARAMETERS);
            }
            else if (lockType.equals("")){
                //没有lockMode也没有lockType，默认手动锁
                lockMode = CUSTINS_LOCK_YES; //default
            }
            else {
                //openapi用户自定义锁定状态
                if (lockType.equals("lock_write")){
                    lockMode = CUSTINS_LOCK_WRITE_BY_USER;
                }
                else if (lockType.equals("lock_read_write")){
                    lockMode = CUSTINS_LOCK_READ_WRITE_BY_USER;
                    lockType = "lock_read";
                }
                else {
                    //"user_lock"，userlock openapi default
                    lockMode = isReadIns ? CUSTINS_LOCK_READ_WRITE_BY_USER : CUSTINS_LOCK_WRITE_BY_USER;
                    lockType = isReadIns ? "lock_read" : "lock_write";
                }
            }

            //校验优先级
            if (!MysqlParameterHelper.checkLockPrecondition(lockMode, custins)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE,
                        "Cannot set LockMode to " + lockMode
                                + ", because the current LockMode(" + custins.getLockMode() + ") " + "priority is higher.");
            }

            boolean isTaskExists = false;
            try {
                isTaskExists = workFlowService.isTaskExist(requestId, custins.getInsName(), PodDefaultConstants.TASK_LOCK_INS);
                if (!isTaskExists){
                    isTaskExists = workFlowService.isTaskExist(requestId, custins.getInsName(), PodDefaultConstants.TASK_LOCK_INS_WITHOUT_KILL_PROCESS);
                }
            } catch (Exception e) {
                //ignore 查询失败忽略掉，不影响加锁任务下发
            }
            if (isTaskExists) {
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }

            // 实例暂停期间，仅支持过期锁定
            if (ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus())) {
                if (!CUSTINS_LOCK_YES.equals(lockMode)) {
                    logger.error("replicaSet is {}, does not support lock mode {}!", replicaSet.getStatus(), lockMode);
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
                // 下发任务启动实例，以便进行锁定
                stoppedReplicaSetMaintainService.dispatchStartTask(requestId, replicaSet);
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.ACTIVE.toString());
            }

            String taskKey;
            taskKey = forceLock.equals("true")? PodDefaultConstants.TASK_LOCK_INS:PodDefaultConstants.TASK_LOCK_INS_WITHOUT_KILL_PROCESS;
//            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("lockMode",lockMode);
            jsonObject.put("lockReason",lockReason);
            jsonObject.put("lockType",lockType);
            jsonObject.put("forceLock",forceLock);
            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask(replicaSet, taskKey, parameter, 0);

            Map<String, Object> data = new HashMap<String, Object>(7);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("LockMode", lockMode);
            data.put("LockReason", lockReason);
            data.put("LockType", lockType);
            data.put("ForceLock", forceLock);
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
