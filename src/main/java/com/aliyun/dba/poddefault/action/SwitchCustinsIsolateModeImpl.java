package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.IsolateModeService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultSwitchCustinsIsolateModeImpl")
@Slf4j
public class SwitchCustinsIsolateModeImpl implements IAction {
    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private ReplicaSetService replicaSetService;

    @Resource
    private IsolateModeService isolateModeService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params)
        throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            String isolateMode = paramSupport.getParameterValue(params, "IsolateMode");
            Boolean isSuccess = isolateModeService.updateCpuIsolateMode(requestId, replicaSet, isolateMode);

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TargetIsolateMode", isolateMode);
            data.put("IsSuccess", isSuccess);
            return data;
        } catch (ApiException e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            throw new RdsException(
                new Object[] {ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (RdsException e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            return createErrorResponse(e.getErrorCode());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
