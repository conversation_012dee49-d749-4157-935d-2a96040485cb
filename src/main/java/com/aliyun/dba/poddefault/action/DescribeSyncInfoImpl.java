package com.aliyun.dba.poddefault.action;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.DTSService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponse;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponseBody;
import com.aliyun.dts20200101.models.DescribePreCheckStatusResponse;
import com.aliyun.dts20200101.models.DescribePreCheckStatusResponseBody;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeSyncInfoImpl")
public class DescribeSyncInfoImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeSyncInfoImpl.class);

    @Resource
    protected DBaasMetaService dbaasMetaService;

    @Resource
    private CustinsParamService custinsParamService;

    @Resource
    private DTSService dtsService;

    @Resource
    RdsApi rdsApi;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    private static final String DTS_STATUS_PRECHECK_FAILED = "PrecheckFailed";

    private static final String DTS_JOB_STATUS_FAILED = "Failed";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {

        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String analyticInsName = paramSupport.getParameterValue(params, ParamConstants.ANALYTIC_DB_INSTANCE_NAME);
            String regionId = paramSupport.getParameterValue(params, ParamConstants.REGION_ID);

            // 查询关联关系
            podCommonSupport.checkAnalyticDBInstanceRelation(requestId, custins.getInsName(), analyticInsName);
            CustinsParamDO custinsParam = custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.DTS_INFO);
            if (ObjectUtils.isEmpty(custinsParam)) {
                throw new Exception("There is no dts info.");
            }
            JSONObject dtsInfo = JSONObject.parseObject(custinsParam.getValue());

            String dtsInstanceId = String.valueOf(dtsInfo.get(PodDefaultConstants.DTS_INSTANCE_ID));
            String dtsJobId = String.valueOf(dtsInfo.get(PodDefaultConstants.DTS_JOB_ID));

            logger.info("requestId : {}, request to DescribeSyncInfo", requestId);
            DescribeDtsJobDetailResponse response = dtsService.describeDtsJobDetail(regionId, dtsInstanceId, dtsJobId);
            if (ObjectUtils.isEmpty(response)) {
                throw new Exception("DescribeDtsJobDetailResponse is empty.");
            }

            Map<String, Object> result = new HashMap<>();
            DescribeDtsJobDetailResponseBody body = response.getBody();
            // 如果预检查失败，需要透出错误信息
            if (DTS_STATUS_PRECHECK_FAILED.equalsIgnoreCase(body.getStatus())) {
                result = getPreCheckInfo(regionId, dtsInstanceId, dtsJobId, result);
            }
            // 测完删掉
            //else {
            //    String testMessage = "CheckItem : CHECK_SAME_OBJ\n" +
            //            "ErrMsg : CHECK__ERROR_SAME_OBJ\n" +
            //            "RepairMethod : CHECK__ERROR_SAME_OBJ_REPAIR\n" +
            //            "Logs : [{\"ErrMsg\": \"sync_db.abc--->sync_db.abc\",\"ErrType\": \"CHECK__ERROR_SAME_OBJ\",\"LogLevel\": \"ERROR\"}]";
            //    result.put("errorMessage", testMessage);
            //}
            result.put("checkpoint", body.getCheckpoint()); // 当前位点
            result.put("status", body.getStatus()); // 状态
            //result.put("status", "PrecheckFailed"); // 状态
            result.put("createTime", body.getCreateTime()); // 创建时间
            result.put("lastUpdateTime", body.getLastUpdateTime()); //更新时间
            result.put("delay", body.getDelay()); // 延迟
            if (ObjectUtils.isEmpty(body.getDataSynchronizationStatus())) {
                result.put("progress", "0");
            } else {
                result.put("progress", body.getDataSynchronizationStatus().getProgress());
            }
             // 速率
            result.put("dbInstanceName", analyticInsName);
            result.put("dbList", body.getDbObject()); // 同步的对象

            String analyticSyncMode = dbaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, custins.getInsName(), PodDefaultConstants.ANALYTIC_SYNC_MODE);
            if (StringUtils.isNotEmpty(analyticSyncMode)) {
                result.put("analyticSyncMode", analyticSyncMode);
            }

            return result;

        } catch (RdsException ex) {
            log.error("DescribeSyncInfo failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("DescribeSyncInfo Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, Object> getPreCheckInfo(String regionId, String dtsInstanceId, String dtsJobId, Map<String, Object> result) throws Exception {
        DescribePreCheckStatusResponse response = dtsService.describePreCheckStatus(regionId,dtsInstanceId,dtsJobId);
        DescribePreCheckStatusResponseBody body = response.getBody();
        List<DescribePreCheckStatusResponseBody.DescribePreCheckStatusResponseBodyJobProgress>  jobList = body.getJobProgress();
        for (DescribePreCheckStatusResponseBody.DescribePreCheckStatusResponseBodyJobProgress job : jobList) {
            if (DTS_JOB_STATUS_FAILED.equalsIgnoreCase(job.getState())) {

                String checkItem = job.getItem();
                String repairMethod = job.getRepairMethod();
                String errMsg = job.getErrMsg();
                String logs = JSONObject.toJSONString(job.getLogs());

                String errorMessage = "CheckItem : " + checkItem + "\n"
                                    + "ErrMsg : " + errMsg + "\n"
                                    + "RepairMethod : " + repairMethod + "\n"
                                    + "Logs : " + logs;

                result.put("errorMessage", errorMessage);
                break;
            }
        }
        return result;
    }

}
