package com.aliyun.dba.poddefault.action.support.modules;

import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.dba.custins.support.AVZInfo;
import lombok.Builder;
import lombok.Data;
import java.util.Map;

@Data
@Builder
public class MysqlReplicaResourceRequest {

    private String requestId;
    private Replica.RoleEnum[] nodeRoles;
    private Map<Replica.RoleEnum, String> roleHostNameMapping;
    private AVZInfo avzInfo;
    private InstanceLevel instanceLevel;
    private Boolean isSingleTenant;
    private String dbType;
    private String dbEngine;
    private String dbVersion;
    private Integer diskSize;
    private String diskType;
    private String vswitchId;
    private String performanceLevel;
    private String snapshotId;
    private boolean isRunD;

}
