package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultEvaluateAnalyticRegionResourceImpl")
public class EvaluateAnalyticRegionResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateAnalyticRegionResourceImpl.class);

    @Resource
    RdsApi rdsApi;

    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String dbInstanceName = custins.getInsName();
            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String uid = paramSupport.getParameterValue(params, ParamConstants.UID);
            String userId = paramSupport.getParameterValue(params, ParamConstants.USER_ID);
            String regionId = paramSupport.getParameterValue(params, ParamConstants.REGION_ID);
            String engineVersion = paramSupport.getParameterValue(params, ParamConstants.ENGINE_VERSION);
            logger.info("requestId : {}, request to EvaluateAnalyticRegionResourceImpl", requestId);

            // 获取subDomain,zoneId
            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, dbInstanceName, null, null, null, null);
            Replica replica = replicaListResult.getItems().stream().filter(x -> Replica.RoleEnum.MASTER.equals(x.getRole())).collect(Collectors.toList()).get(0);
            String subDomain = replica.getSubDomain();
            String zoneId = replica.getZoneId();
            logger.info("requestId : {}, subDomain : {}, zoneId : {}", requestId, subDomain, zoneId);

            String finalRequestId = String.format("%s_%s", requestId, new Random().nextInt(Integer.MAX_VALUE));

            String ckInstanceName = podCommonSupport.makeClickhouseName(requestId);

            Map<String, Object> result = rdsApi.getDataByRdsApi(new HashMap<String, String>() {{
                put("Action", "EvaluateRegionResource");
                put("DBInstanceName", ckInstanceName);
                put("UID", uid);
                put("User_id", userId);
                put("RegionID", regionId);
                put("Region", regionId);
                put("RequestId", finalRequestId);
                put("DBInstanceModelType", "cluster");
                put("Engine", "clickhouse");
                put("EngineVersion", engineVersion);
                put("Evaluate", "True");
                put("SubDomain", subDomain);
                put("ZoneId", zoneId);
            }}, ParamConstants.YAOCHI_ACCESS);

            logger.info("request : {}, result : {}", requestId, JSONObject.toJSONString(result));
            return result;
        } catch (RdsException ex) {
            log.error("EvaluateAnalyticRegionResourceImpl failed: {}", JSONObject.toJSONString(ex));
            throw ex;
        } catch (Exception ex) {
            log.error("EvaluateAnalyticRegionResourceImpl Exception: {}", JSONObject.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
