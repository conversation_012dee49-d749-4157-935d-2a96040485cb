package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Account;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccountListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.support.property.ParamConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> on 2020/6/18.
 */
@Service
public class PodAccountService {

    @Resource
    private DBaasMetaService dBaasMetaService;

    /**
     * 实例间同步用户高权限 & rds_service账户
     */
    /**
     * 实例间同步用户高权限 & rds_service账户
     */
    public void syncReplicaSetSuperAccount(String requestId, String srcReplicaSetName, String destReplicaSetName) throws ApiException {
        // 查询dbaas中用户高权限账号

        try {
            Account queryExample = new Account();
            queryExample.setBizType("userbiz");
            queryExample.setStatus(Account.StatusEnum.ACTIVE);
            AccountListResult accountListResult = dBaasMetaService.getDefaultClient().listReplicaSetAccountsByExample(requestId, srcReplicaSetName, null, 20, queryExample);
            if (accountListResult != null && !accountListResult.getItems().isEmpty()) {
                for (Account item : accountListResult.getItems()) {
                    item.setStatus(Account.StatusEnum.CREATING);
                    dBaasMetaService.getDefaultClient().createAccountForReplicaSet(requestId, destReplicaSetName, item);
                }
            }
        } catch (ApiException e) {
            if (e.getCode() != 404) {
                throw e;
            }
        }

        try {
            // 查询rds service账号
            Account queryRdsService = new Account();
            queryRdsService.setBizType("system");
            queryRdsService.setName("rds_service");
            queryRdsService.setStatus(Account.StatusEnum.ACTIVE);
            AccountListResult rdsServiceResult = dBaasMetaService
                    .getDefaultClient()
                    .listReplicaSetAccountsByExample(requestId, srcReplicaSetName, null, 1, queryRdsService);
            if (rdsServiceResult != null && !rdsServiceResult.getItems().isEmpty()) {
                for (Account item : rdsServiceResult.getItems()) {
                    dBaasMetaService.getDefaultClient().createAccountForReplicaSet(requestId, destReplicaSetName, item);
                }
            }
        } catch (ApiException e) {
            if (e.getCode() != 404) {
                throw e;
            }
        }

    }

}
