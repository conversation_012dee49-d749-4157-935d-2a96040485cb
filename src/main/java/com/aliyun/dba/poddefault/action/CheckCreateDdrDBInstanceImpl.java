package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreArchiveLogParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.response.backup.DescribeRestoreArchiveLogResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDateTimeUtils;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCheckCreateDdrDBInstanceImpl")
@Slf4j
public class CheckCreateDdrDBInstanceImpl implements IAction {

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private DbsGateWayService dbsGateWayService;

    @Resource
    private PodDateTimeUtils podDateTimeUtils;

    private static final List<InstanceLevel.CategoryEnum> SUPPORT_DDR_CATEGORIES = Arrays.asList(
            InstanceLevel.CategoryEnum.BASIC,
            InstanceLevel.CategoryEnum.STANDARD,
            InstanceLevel.CategoryEnum.CLUSTER
    );

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params)
            throws RdsException {
        try {
            doCheckAndUpdateDdrRestoreParams(params);
            Map<String, Object> data = new HashMap<>();
            data.put("IsValid", true);
            return data;
        } catch (RdsException re) {
            log.error("check create ddr failed: {}", re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error("check create ddr failed: {}", ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public void doCheckAndUpdateDdrRestoreParams(Map<String, String> params) throws Exception {
        // 补充异地备份默认参数
        BakSupport.preProcessForCreateDdr(params);

        // 校验支持实例系列
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
        String dbType = mysqlParamSupport.getAndCheckDBType(params, "MySQL");
        String dbVersion = mysqlParamSupport.getDBVersion(params, dbType);
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient()
                .getInstanceLevel(requestId, dbType, dbVersion, classCode, null);

        if (!SUPPORT_DDR_CATEGORIES.contains(instanceLevel.getCategory())) {
            log.error("requestId: {}, db category: {}, not supported!", requestId, instanceLevel.getCategory());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }

        // 校验恢复参数
        String restoreType = mysqlParamSupport.getAndCheckRestoreType(params);
        String bid = mysqlParamSupport.getAndCheckBID(params);
        String uid = mysqlParamSupport.getAndCheckUID(params);
        String sourceDBInstanceName = mysqlParamSupport.getSourceDBInstanceName(params);
        String sourceRegionId = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_SOURCE_REGION);

        if (StringUtils.isEmpty(sourceDBInstanceName)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "param sourceDBInstanceName is required");
        }
        if (StringUtils.isEmpty(sourceRegionId)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "param sourceRegion is required");
        }
        String backupSetRegionId = getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION);
        if (StringUtils.isEmpty(backupSetRegionId)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "param backupSetRegion is required");
        }

        if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            // 按备份集恢复，校验备份集有效性
            checkAndUpdateBackupSetParams(bid, uid, sourceDBInstanceName, sourceRegionId, params);
        } else if (RESTORE_TYPE_TIME.equals(restoreType)) {
            // 按时间点恢复，先校验备份集，再校验binlog
            checkAndUpdateRestoreByTimeParams(bid, uid, sourceDBInstanceName, sourceRegionId, params);
        } else {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
        }

        // 不支持x86/arm跨架构恢复，double-write开关差异可能导致block原子写检查失败
        String srcInsLevelExtraInfo = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.SRC_INS_LEVEL_EXTRA_INFO);
        boolean isArchChange = PodCommonSupport.isArchChange(srcInsLevelExtraInfo, instanceLevel.getExtraInfo());
        if (isArchChange) {
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
        }

        log.info("updated restore params: {}", JSONObject.toJSONString(params));
    }


    private void checkAndUpdateBackupSetParams(String bid, String uid, String sourceDBInstanceName, String sourceRegionId, Map<String, String> params) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID);
        String backupSetId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        String backupSetRegionId = getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION);

        if (StringUtils.isEmpty(backupSetId)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "param backupSetId is required");
        }

        DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(
                DescribeRestoreBackupSetParam.builder()
                        .requestId(requestId)
                        .accessId(accessId)
                        .callerBid(bid)
                        .userId(uid)
                        .instanceName(sourceDBInstanceName)
                        .instanceRegion(sourceRegionId)
                        .sceneType(PodDefaultConstants.BACK_SCENE_TYPE_REPLICATION)
                        .backupId(backupSetId)
                        .regionCode(backupSetRegionId)
                        .build()
        );
        if (!restoreBackupResponse.getRestoreTimeValid() || restoreBackupResponse.getBackupSetInfo() == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        setBackupSetInfoToParams(restoreBackupResponse.getBackupSetInfo(), params);
    }


    private void checkAndUpdateRestoreByTimeParams(String bid, String uid, String sourceDBInstanceName, String sourceRegionId, Map<String, String> params) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID);
        String backupSetId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        String backupSetRegionId = getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION);

        String restoreTime = mysqlParamSupport.getParameterValue(params, ParamConstants.RESTORE_TIME);
        Long restoreTimePoint;
        try {
            restoreTimePoint = podDateTimeUtils.getUTCDateByDateStr(restoreTime).getTime()/1000;
        } catch (Exception e) {
            log.error("requestId: {}, parse restoreTime failed, value: {}, msg: {}", requestId, restoreTime, e.getMessage(), e);
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }

        // 按时间点恢复，先查找备份集
        var describeBackupSetParam = DescribeRestoreBackupSetParam.builder()
                .requestId(requestId)
                .accessId(accessId)
                .callerBid(bid)
                .userId(uid)
                .instanceName(sourceDBInstanceName)
                .instanceRegion(sourceRegionId)
                .sceneType(PodDefaultConstants.BACK_SCENE_TYPE_REPLICATION)
                .regionCode(backupSetRegionId);

        // 如果用户输入了backupSetId，这里直接指定，否则接口可能选择到最新的备份集
        if (StringUtils.isNotEmpty(backupSetId)) {
            describeBackupSetParam.backupId(backupSetId);
        } else {
            describeBackupSetParam.restoreTimePoint(restoreTimePoint);
        }

        DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(describeBackupSetParam.build());
        if (!restoreBackupResponse.getRestoreTimeValid() || restoreBackupResponse.getBackupSetInfo() == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        setBackupSetInfoToParams(restoreBackupResponse.getBackupSetInfo(), params);

        // 再检查检查日志文件
        String consistentTime = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_CONSISTENT_TIME);
        DescribeRestoreArchiveLogResponse restoreLogResponse = dbsGateWayService.describeRestoreArchiveLog(
                DescribeRestoreArchiveLogParam.builder()
                        .requestId(requestId)
                        .accessId(accessId)
                        .callerBid(bid)
                        .userId(uid)
                        .instanceName(sourceDBInstanceName)
                        .instanceRegion(sourceRegionId)
                        .sceneType(PodDefaultConstants.BACK_SCENE_TYPE_REPLICATION)
                        .regionCode(backupSetRegionId)
                        .restoreTimePoint(restoreTimePoint)
                        .consistentTime(Long.parseLong(consistentTime))
                        .build());
        if (!restoreLogResponse.getRestoreTimeValid()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }
        params.put(PodDefaultConstants.PARAM_RESTORE_TIME_POINT.toLowerCase(), restoreTimePoint.toString());
    }

    /**
     * 设置备份集信息到请求参数中，创建实例时使用
     */
    private void setBackupSetInfoToParams(DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        log.info("requestId:{}, backup set info: {}", requestId, JSONObject.toJSONString(backupSetInfo));

        try {
            String backupSetId = backupSetInfo.getBackupId();
            params.put(ParamConstants.BACKUP_SET_ID.toLowerCase(), backupSetId);

            String snapshotId = backupSetInfo.getExtraInfo().getDSnapshotId();
            params.put(PodDefaultConstants.PARAM_SNAPSHOT_ID.toLowerCase(), snapshotId);

            Long consistentTime = backupSetInfo.getConsistentTime();
            params.put(PodDefaultConstants.PARAM_CONSISTENT_TIME.toLowerCase(), consistentTime.toString());

            // 设置实例内核版本、核心参数，保证实例可拉起
            String originMinorVersion = backupSetInfo.getExtraInfo().getSlaveStatus().getMinorVersion();
            params.put(PodDefaultConstants.PARAM_TARGET_MINOR_VERSION.toLowerCase(), originMinorVersion);

            String instanceLevelExtraInfo = backupSetInfo.getExtraInfo().getSlaveStatus().getInsLevelExtraInfo();
            params.put(PodDefaultConstants.SRC_INS_LEVEL_EXTRA_INFO.toLowerCase(), instanceLevelExtraInfo);
        } catch (Exception e) {
            log.error("requestId: {}, set backup set info to params failed, msg: {}", e.getMessage(), e);
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
    }
}
