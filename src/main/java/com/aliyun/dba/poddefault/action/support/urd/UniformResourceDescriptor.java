package com.aliyun.dba.poddefault.action.support.urd;

import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.property.RdsException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static com.aliyun.dba.custins.support.CustinsSupport.ENTERPRISE_LEVEL;


@Slf4j
public class UniformResourceDescriptor {

    private static final ArrayList<Replica.RoleEnum> DEFAULT_XDB_ROLE_LIST = new ArrayList<Replica.RoleEnum>() {{
        add(Replica.RoleEnum.MASTER);
        add(Replica.RoleEnum.FOLLOWER);
        add(Replica.RoleEnum.LOGGER);
    }};

    private static final ArrayList<Replica.RoleEnum> DEFAULT_MYSQL_ROLE_LIST = new ArrayList<Replica.RoleEnum>() {{
        add(Replica.RoleEnum.MASTER);
        add(Replica.RoleEnum.SLAVE);
    }};

    private static final Integer DEFAULT_MASTER_WEIGHT = 9;
    private static final Integer DEFAULT_SLAVE_WEIGHT = 5;
    private static final Integer DEFAULT_LOGGER_WEIGHT = 1;

    @Getter
    private final Map<String, Set<String>> regionMap;
    @Getter
    private final Map<String, URDRegionDescriptor> regionIdMap;

    public UniformResourceDescriptor() {
        regionMap = new HashMap<>();
        regionIdMap = new HashMap<>();
    }

    public UniformResourceDescriptor(InstanceLevel level, AVZInfo avzInfo, List<Replica.RoleEnum> roleEnumList) throws RdsException {
        regionMap = new HashMap<>();
        regionIdMap = new HashMap<>();

        String masterRegion = avzInfo.getRegion();
        String masterRegionId = avzInfo.getRegionId();
        String masterZoneId = avzInfo.getMasterZoneId();
        boolean isSingleNode = MysqlParamSupport.isSingleNode(level);
        boolean isXCluster = InstanceLevel.CategoryEnum.ENTERPRISE.equals(level.getCategory());

        // add master
        addInstance(masterRegion, masterRegionId, DEFAULT_MASTER_WEIGHT,
                masterZoneId, DEFAULT_MASTER_WEIGHT, Replica.RoleEnum.MASTER, 1);

        if (avzInfo.isMultiAVZExParamDOEmpty() || isSingleNode) {
            return;
        }

        // add slaves
        MultiAVZExParamDO multiAVZExParamDO = avzInfo.getMultiAVZExParamDO();
        List<AvailableZoneInfoDO> slaveZones = multiAVZExParamDO.getSlaveAvailableZoneInfo();

        if (roleEnumList == null || roleEnumList.isEmpty()) {
            roleEnumList = isXCluster ? DEFAULT_XDB_ROLE_LIST : DEFAULT_MYSQL_ROLE_LIST;
        }

        for (int i = 1; i <= slaveZones.size(); i++) {
            Replica.RoleEnum role = roleEnumList.get(i);
            Integer weight = role.equals(Replica.RoleEnum.LOGGER) ? DEFAULT_LOGGER_WEIGHT : DEFAULT_MASTER_WEIGHT;
            addInstance(masterRegion, masterRegionId, weight, slaveZones.get(i - 1).getZoneID(), weight, role, 1);
        }
    }

    public URDRegionDescriptor getMasterRegionDesc() {
        URDZoneDescriptor mainZone = getMasterZoneDesc();
        if (mainZone == null) {
            return null;
        }

        return regionIdMap.get(mainZone.getRegionId());
    }

    public URDZoneDescriptor getMasterZoneDesc() {
        for (URDZoneDescriptor zoneDescriptor : getZoneList()) {
            if (!zoneDescriptor.getInstances().containsKey(Replica.RoleEnum.MASTER)) {
                continue;
            }
            return zoneDescriptor;
        }
        return null;
    }

    public void addRegionId(String region, URDRegionDescriptor regionDescriptor) {
        if (regionDescriptor == null) {
            return;
        }

        String regionId = regionDescriptor.getRegionId();
        addSubdomain(region, regionId);
        if (!regionIdMap.containsKey(regionId)) {
            regionIdMap.put(regionId, regionDescriptor);
            return;
        }

        URDRegionDescriptor existed = regionIdMap.get(regionId);
        regionDescriptor.getZones().values().forEach(existed::addZone);
    }

    public void addInstance(String region,
                            String regionId, Integer regionWeight,
                            String zoneId, Integer zoneWeight,
                            Replica.RoleEnum role, Integer count) {

        addRegionId(region, new URDRegionDescriptor(region, regionId, regionWeight));
        regionIdMap.get(regionId).addZone(new URDZoneDescriptor(region, regionId, zoneId, zoneWeight));
        regionIdMap.get(regionId).getZones().get(zoneId).addInstance(role, count);
    }

    public List<URDZoneDescriptor> getZoneList() {
        List<URDZoneDescriptor> zoneDescriptorList = new ArrayList<>();
        regionIdMap.values().forEach(regionDescriptor -> zoneDescriptorList.addAll(regionDescriptor.getZoneList()));
        return zoneDescriptorList;
    }

    /**
     * 节点遍历
     */
    public void walkNode(URDWalker walker) throws Exception {
        for (URDRegionDescriptor regionDescriptor : regionIdMap.values()) {
            for (URDZoneDescriptor zoneDescriptor : regionDescriptor.getZoneList()) {
                walker.walkZoneDesc(zoneDescriptor);
                for (Map.Entry<Replica.RoleEnum, Integer> entry : zoneDescriptor.getInstances().entrySet()) {
                    Replica.RoleEnum role = entry.getKey();
                    Integer count = entry.getValue();
                    for (int i = 0; i < count; i++) {
                        walker.walkNode(zoneDescriptor, role);
                    }
                }
            }
        }
    }


    private void addSubdomain(String regionId, String subdomain) {
        if (!regionMap.containsKey(regionId)) {
            regionMap.put(regionId, new HashSet<>());
        }
        regionMap.get(regionId).add(subdomain);
    }

}
