package com.aliyun.dba.poddefault.action.service.migrate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.poddefault.action.MigrateDBInstanceImpl;
import com.aliyun.dba.poddefault.action.RebuildSlaveInstanceImpl;
import com.aliyun.dba.poddefault.action.service.AutomaticSlaveZoneService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.SLAVE_REBUILD_TYPE_REMOTE;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_STANDARD;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.SLAVE_REBUILD_TYPE_FORCE_REMOTE;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;

@Service
public class MigrateDBInstanceAvzService {
    @Autowired
    protected MySQLServiceImpl mySQLService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected PodParameterHelper podParameterHelper;
    @Autowired
    protected PodTemplateHelper podTemplateHelper;
    @Autowired
    protected MysqlParamSupport paramSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected KmsService kmsService;
    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Resource
    private PodAvzSupport podAvzSupport;
    @Autowired
    CustinsParamService custinsParamService;
    @Resource
    MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    protected DBaasMetaService metaService;
    @Autowired
    protected InstanceIDao instanceIDao;
    @Autowired
    private InstanceService instanceService;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    private AutomaticSlaveZoneService automaticSlaveZoneService;

    protected static final LogAgent logger = LogFactory.getLogAgent(MigrateDBInstanceImpl.class);


    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        logger.info("Action {}, params {}", "MigrateDBInstanceAvzService", JSON.toJSONString(params));

        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        AllocateTmpResourceResult allocateResult = new AllocateTmpResourceResult();

        boolean isSuccess = false;

        try {
            PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(params);

            final boolean isArchChange = PodCommonSupport.isArchChange(modifyInsParam.getSrcInstanceLevel(), modifyInsParam.getTargetInstanceLevel());
            AVZInfo targetAvzInfo = modifyInsParam.getAvzInfo();
            AVZInfo oldAvzInfo = modifyInsParam.getOldAvzInfo();
            PodType podType = modifyInsParam.getPodType();
            //rund pod如果迁移可用区资源不足，回到runc
            if(PodType.POD_ECS_RUND.getRuntimeType().equalsIgnoreCase(podType.getRuntimeType())) {
                modifyInsParam.correctRuntimeParams();
            }
            //basic check
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            boolean isCloudBox = podAvzSupport
                    .isCloudBoxAz(modifyInsParam.getReplicaSetMeta().getResourceGroupName());

            if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
                logger.error("db instance is not active.");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            } else if (isCloudBox) {
                logger.error("cloudbox ins un-support migrate avz");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // 集群版不走这个接口
            if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "cluster is not supported yet!");
            }

            if (podCommonSupport.isIoAccelerationEnabled(modifyInsParam.getGeneralCloudDisk())) {
                isAZSatisfiesIOAccelerationCriteria(modifyInsParam);
            }

            // 只读检查主实例状态
            if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), false);
                if (primaryReplicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION
                        || primaryReplicaSet.getLockMode() != ReplicaSet.LockModeEnum.NOLOCK) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_MASTER_DBINSTANCE_STATUS);
                }
            }

            if (!PodParameterHelper.isAliYun(replicaSet.getBizType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "biz type " + replicaSet.getBizType() + " not supported yet!");
            }

            if (!InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSet.getCategory())
                    && !InstanceLevel.CategoryEnum.STANDARD.toString().equals(replicaSet.getCategory())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "db category " + replicaSet.getCategory() + " not supported yet!");
            }
            if (!ReplicaSet.InsTypeEnum.MAIN.equals(replicaSet.getInsType())
                    && !ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "db type " + replicaSet.getInsType() + " not supported yet!");
            }

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }

            // 校验变配参数是否合法
//            if (modifyInsParam.isDiskTypeChange()) {
//                throw new RdsException(ErrorCode.INVALID_PARAMETERS, "disk type change is not supported");
//            }

            boolean isModifySpec = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, "migrateAzModifySpec", "false"));
            boolean isModifyParam = modifyInsParam.isClassCodeChange() || modifyInsParam.isDiskSizeChange() || modifyInsParam.isPerformanceLevelChanged() || modifyInsParam.isDiskTypeChange();
            if (isModifySpec ^ isModifyParam) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, "Specified parameter DBInstanceClass or Storage is invalid");
            }

            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                    .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                            null, null, null, null);
            var masterReplica = listReplicasInReplicaSet.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get();
            val targetMasterZoneId = targetAvzInfo.getMasterZoneId();
            boolean isMigrateMaster = !StringUtils.equalsIgnoreCase(masterReplica.getZoneId(), targetMasterZoneId)
                    || params.containsKey(PodDefaultConstants.MIGRATE_IN_SAME_AVZ.toLowerCase());   //测试环境强制迁移标志

            boolean isMigrateSlave = false;
            if (StringUtils.equalsIgnoreCase(replicaSet.getCategory(), InstanceLevel.CategoryEnum.STANDARD.toString())) {
                if (!targetAvzInfo.getMultiAVZExParamDO().getSlaveAvailableZoneInfo().isEmpty()) {
                    var targetSlaveAvz = modifyInsParam.getAvzInfo().getMultiAVZExParamDO().getSlaveAvailableZoneInfo().get(0);
                    var slaveReplica = listReplicasInReplicaSet.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get();
                    isMigrateSlave = !StringUtils.equalsIgnoreCase(slaveReplica.getZoneId(), targetSlaveAvz.getZoneID())
                            || params.containsKey(PodDefaultConstants.MIGRATE_SLAVE_IN_SAME_AVZ.toLowerCase());
                }
            }

            // 主备可用区均不变，不允许变配
            if (!isMigrateMaster && !isMigrateSlave) {
                throw new RdsException(ErrorCode.INVALID_AVZONE, "specified zoneId is equal as previous!");
            }

            // ha后再迁移可能出现该情况，实例当前主可用区与元数据不一致，仅迁移备库会导致ha不托管
            if (InstanceLevel.CategoryEnum.STANDARD.toString().equals(replicaSet.getCategory()) &&
                    !StringUtils.equalsIgnoreCase(masterReplica.getZoneId(), oldAvzInfo.getMasterZoneId())) {
                logger.info("master avz {} is not equals oldAvzInfo {}, force migrate master", masterReplica.getZoneId(), oldAvzInfo.getMasterZoneId());
                isMigrateMaster = true;
            }

            // 非变配，非主可用区变化，进行备库重搭，不会造成用户闪断
            if (!isArchChange && !isMigrateMaster && !isModifySpec) {
                var targetSlaveAvz = modifyInsParam.getAvzInfo().getMultiAVZExParamDO().getSlaveAvailableZoneInfo().get(0);
                var slaveReplica = listReplicasInReplicaSet.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get();

                Map<String, Object> result = rebuildSlaveInstance(slaveReplica.getId().toString(),
                        modifyInsParam.getDbInstanceName(),
                        getParameterValue(params, ParamConstants.USER_ID),
                        getParameterValue(params, ParamConstants.UID),
                        targetSlaveAvz.getRegion(),
                        targetSlaveAvz.getZoneID(),
                        modifyInsParam.isSrcSingleTenant(),
                        modifyInsParam.getRequestId(),
                        targetAvzInfo);

                // 备成功迁移时，改写实例状态，以便用户感知
                if (!result.containsKey("errorCode")) {
                    dBaasMetaService.getDefaultClient().updateReplicaSetStatus(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), ReplicaSet.StatusEnum.TRANSING.toString());
                }

                return result;
            }


            // 申请资源
            allocateResourceForMigrate(requestId, replicaSet, modifyInsParam, params, allocateResult, null, null);
            ReplicaSet tmpReplicaSet = allocateResult.getReplicaSet();

            checkEndPointList(requestId, replicaSet, tmpReplicaSet);

            // vmoc-lite 临时实例需要打上VBM标签，迁移任务流注入xdp注解
            addLabelForTmpReplicaSetIfVbm(modifyInsParam, requestId, tmpReplicaSet.getName());

            // 下发迁移任务
            Integer taskIdInt = addTransferTask(requestId, replicaSet, tmpReplicaSet, modifyInsParam, params);

            // 更新实例状态
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.TRANSING.toString());
            if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
//                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getPrimaryInsName(), ReplicaSet.StatusEnum.INS_MAINTAINING.toString());
                custinsService.updateCustInstanceStatusByCustinsId(custins.getPrimaryCustinsId(), CustinsSupport.CUSTINS_STATUS_TRANS, CustinsState.STATE_READINS_TRANSING.getComment());
            }

            // 准备链路切换
            addSwitchVipChangeLog(requestId, replicaSet, tmpReplicaSet, taskIdInt);
            isSuccess = true;

            // build response
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TaskId", taskIdInt);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getMessage(), e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (allocateResult.isAllocated() && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, allocateResult.getResourceRequest().getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("resource resource for transfer failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    // vmoc-lite 临时实例需要打上VBM标签，迁移任务流注入xdp注解
    public void addLabelForTmpReplicaSetIfVbm(PodModifyInsParam modifyInsParam, String requestId, String tmpReplicaSetName) throws ApiException {
        if(modifyInsParam.isVbm()){
            Map<String, String> labels = new HashMap<>();
            labels.put(VBM_CUSTINS_LABEL_KEY, VBM_CUSTINS_LABEL_VALUE);
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSetName, labels);
        }
    }

    public void allocateResourceForMigrate(String requestId, ReplicaSet replicaSet, PodModifyInsParam modifyInsParam, Map<String, String> params, AllocateTmpResourceResult result, String vpcId, String vswId) throws Exception {
        final boolean isArchChange = PodCommonSupport.isArchChange(modifyInsParam.getSrcInstanceLevel(), modifyInsParam.getTargetInstanceLevel());
        final boolean isArm = PodCommonSupport.isArm(modifyInsParam.getTargetInstanceLevel());
        boolean isAllocate;
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();

        // 申请资源
        CustInstanceDO custins = modifyInsParam.getCustins();

        String tmpReplicaSetName = String.format("tmp-%s-%s", modifyInsParam.getDbInstanceName(), System.currentTimeMillis() / 1000L);
        String connectionString = CheckUtils.checkValidForConnAddrCust(tmpReplicaSetName);
//        String vpcId = CheckUtils.checkValidForVPCId(paramSupport.getParameterValue(params, ParamConstants.VPC_ID));
        Endpoint vpcEndpoint = replicaSetService.getReplicaSetVpcEndpoint(requestId, replicaSet.getName()); //VPC不能变，以实例的为准，

        String vswitchId = CheckUtils.checkValidForVswitchId(paramSupport.getParameterValue(params, ParamConstants.VSWITCH_ID));
        AVZInfo targetAvzInfo = modifyInsParam.getAvzInfo();

        if (StringUtils.isNotBlank(modifyInsParam.getClusterName()) && modifyInsParam.isDHG()) {
            replicaSetResourceRequest.setDedicatedHostGroupId(modifyInsParam.getClusterName());
        }

        String serviceSpecTag;
        //todo: 只读实例架构迁移需要适配
        if (isArchChange) {
            String sourceMinorVersion = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSet.getName(), "minor_version");
            serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(sourceMinorVersion,
                    modifyInsParam.getReplicaSetMeta().getBizType(),
                    modifyInsParam.getDbType(),
                    modifyInsParam.getDbVersion(),
                    "MySQL",
                    KIND_CODE_NEW_ARCH,
                    modifyInsParam.getTargetInstanceLevel(),
                    modifyInsParam.getTargetDiskType(),
                    modifyInsParam.isDHG(),
                    isArm,
                    true,
                    null); //需要查询到下线版本
        } else {
            serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId());
        }
        if (StringUtils.isEmpty(serviceSpecTag)) {
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }

        replicaSetResourceRequest.userId(modifyInsParam.getBid())
                .uid(modifyInsParam.getUid())
                .port(vpcEndpoint.getVport().toString())
                .insType(ReplicaSet.InsTypeEnum.MAIN.toString())
                .replicaSetName(tmpReplicaSetName)
                .domainPrefix(connectionString)

                // 版本规格
                .dbType(modifyInsParam.getDbType())
                .dbVersion(modifyInsParam.getDbVersion())
                .composeTag(serviceSpecTag)
                .bizType(custins.getBizType())
                .catagory(replicaSet.getCategory())
                .classCode(modifyInsParam.getTargetClassCode())

                // 磁盘资源
                .storageType(modifyInsParam.getTargetDiskType())
                .diskSize(modifyInsParam.getTargetDiskSizeGB())
                .burstingEnabled(modifyInsParam.isBurstingEnabled())
                .provisionedIops(modifyInsParam.getProvisionedIops())
                .generalCloudDisk(modifyInsParam.getGeneralCloudDisk())
                .allocateDisk(false)
                .initOptimizedWrites(Boolean.parseBoolean(modifyInsParam.getTargetInitOptimizedWritesString()))

                // 网络资源
                .connType(custins.getConnType())
                .vpcId(vpcEndpoint.getVpcId())
                .vswitchID(vswitchId)
                .cloudInstanceIp(null)
                .vpcInstanceId(null)
                //反向VPC的资源申请下沉到任务流
                .ignoreCreateVpcMapping(true)

                // 地域
                .subDomain(targetAvzInfo.getRegion())
                .regionId(targetAvzInfo.getRegionId());

        boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();
        if (isTargetSingleTenant) {
            // 单租户场景
            replicaSetResourceRequest.singleTenant(true).eniDirectLink(false);
        }

        // 资源模板相关
        replicaSetResourceRequest.setScheduleTemplate(modifyInsParam.getScheduleTemplate());
        podTemplateHelper.setSpecSchedulerConfigSpread(replicaSetResourceRequest, modifyInsParam.getDbInstanceName());

        val diskSizeGB = modifyInsParam.getTargetDiskSizeGB();
        val extendedDiskSizeGB = podParameterHelper
                .getExtendDiskSizeGBForPod(
                        modifyInsParam.getReplicaSetMeta().getBizType(),
                        false, // 云上无影响
                        diskSizeGB
                );

        // 需要迁移的角色
        List<Replica.RoleEnum> roles = new ArrayList<>();
        roles.add(Replica.RoleEnum.MASTER);
        if (CATEGORY_STANDARD.equalsIgnoreCase(replicaSet.getCategory())) {
            roles.add(Replica.RoleEnum.SLAVE);
        }

        val replicas = new ArrayList<ReplicaResourceRequest>();
        for (val rr : roles) {
            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
            replicaResourceRequest.setDiskSize(extendedDiskSizeGB);
            replicaResourceRequest.setClassCode(modifyInsParam.getTargetClassCode());
            replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
            replicaResourceRequest.setRole(rr.getValue());

            // 设置可用区
            String zoneId = "";
            switch (rr) {
                case MASTER:
                    zoneId = targetAvzInfo.getMasterZoneId();
                    break;
                case SLAVE:
                    zoneId = getSlaveZoneIdForMigrating(modifyInsParam);
                    break;
                default:
                    logger.error("unsupported replica role {}", rr.getValue());
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            replicaResourceRequest.setZoneId(zoneId);
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
                rundPodSupport.completeReplicaNetworkConfig(replicaResourceRequest, targetAvzInfo);
            }
            //rund切换vip适配
            if (vswId != null) {
                replicaResourceRequest.setVswId(vswId);
            }
            replicas.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicas);

        podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
        result.setResourceRequest(replicaSetResourceRequest);
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
            rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
        }
        //rund切换vip适配
        if (vpcId != null) {
            replicaSetResourceRequest.vpcId(vpcId);
        }
        if (vswId != null) {
            replicaSetResourceRequest.vswitchID(vswId);
        }
        if(isArm){
            //如果是arm架构需要指定arch
            replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.ARM);
            replicaSetResourceRequest.setUseAsiCluster(true);
        }

        // 申请资源
        logger.info("allocate resoure for {}, request body: {}", tmpReplicaSetName, JSON.toJSONString(replicaSetResourceRequest));
        isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, tmpReplicaSetName, replicaSetResourceRequest);
        result.setAllocated(isAllocate);
        if(isAllocate && isArchChange){
            //label更新, 需要在资源申请前设置COMMON_K8S_CLUSTER_LABEL
            Map<String, String> labels = new HashMap<>();
            //open double write as x86 not support close double write, used for com.aliyun.app.activityprovider.base.param.param.BuildParamParameter.ArchChangedMatcher
            labels.put(PodDefaultConstants.PARAM_ARCH_CHANGED, "1");
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), labels);
        }

        // 更新临时实例参数
        ReplicaSet tmpReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, tmpReplicaSetName, null);
        custinsParamService.updateAVZInfo(tmpReplicaSet.getId().intValue(), targetAvzInfo);
        tmpReplicaSet.setPrimaryInsName(custins.getInsName());
        tmpReplicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
        dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, tmpReplicaSetName, tmpReplicaSet);

        // 只读实例配置白名单同步label
        podParameterHelper.setReadInsSgLabel(requestId, replicaSet, tmpReplicaSetName);
        result.setReplicaSet(tmpReplicaSet);
    }


    private Integer addTransferTask(String requestId, ReplicaSet srcReplicaSet, ReplicaSet destReplicaSet, PodModifyInsParam modifyInsParam, Map<String, String> params) throws Exception {
        // get replica info
        ReplicaListResult srcReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, srcReplicaSet.getName(), null, null, null, null);
        ReplicaListResult destReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, destReplicaSet.getName(), null, null, null, null);
        Long srcMasterReplicaId = srcReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();
        Long destMasterReplicaId = destReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();
        Long srcSlaveReplicaId = 0L;
        Long destSlaveReplicaId = 0L;
        if (InstanceLevel.CategoryEnum.STANDARD.toString().equals(srcReplicaSet.getCategory())) {
            srcSlaveReplicaId = srcReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get().getId();
            destSlaveReplicaId = destReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get().getId();
        }

        // step 1: 写迁移记录
        CustInstanceDO srcCustins = custinsService.getCustInstanceByCustinsId(srcReplicaSet.getId().intValue());
        TransListDO transList = new TransListDO(srcCustins, InstanceSupport.TRANS_STATUS_REMOVE_NB, InstanceSupport.TRANS_TYPE_REMOVE);
        transList.setsHinsid1(srcMasterReplicaId.intValue());
        transList.setsHinsid2(srcSlaveReplicaId.intValue());

        transList.setdCinsid(destReplicaSet.getId().intValue());
        InstanceLevelDO targetLevel = instanceService.getInstanceLevelByClassCode(destReplicaSet.getClassCode(),
                srcCustins.getDbType(), srcCustins.getDbVersion(), null, null);
        transList.setdLevelid(targetLevel.getId());
        transList.setdDisksize((long) (modifyInsParam.getTargetDiskSizeGB() * 1024));
        transList.setdHinsid1(destMasterReplicaId.intValue());
        transList.setdHinsid2(destSlaveReplicaId.intValue());

        transList.setSwitchTime(modifyInsParam.getSwitchTime());

        String comment = "MigrateInsAvz";
        boolean isModifySpec = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, "migrateAzModifySpec", "false"));
        if (isModifySpec) {
            comment = "MigrateInsAvzAndModifySpec";
        }
        transList.setComment(comment);

        this.instanceIDao.createTransList(transList);

        // step 2: 设置任务参数
        JSONObject taskParam = new JSONObject();
        taskParam.put("requestId", requestId);
        taskParam.put(CustinsSupport.TRANS_ID, transList.getId());
        taskParam.put(CustinsSupport.SWITCH_KEY, modifyInsParam.getSwitchInfo());
        taskParam.put("srcReplicaSetName", srcReplicaSet.getName());
        taskParam.put("destReplicaSetName", destReplicaSet.getName());
        taskParam.put("srcReplicaId", srcMasterReplicaId);
        taskParam.put("destReplicaId", destMasterReplicaId);
        taskParam.put("srcReplicaSetResourceGroupName", srcReplicaSet.getResourceGroupName());
        taskParam.put("destReplicaSetResourceGroupName", destReplicaSet.getResourceGroupName());
        taskParam.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());

        // 变配相关参数下发
        if (modifyInsParam.isClassCodeChange()) {
            taskParam.put("destClassCode", modifyInsParam.getTargetClassCode());
        }
        if (modifyInsParam.isDiskSizeChange()) {
            taskParam.put("destDiskSizeMB", modifyInsParam.getTargetDiskSizeGB() * 1024);
        }

        if(PodCommonSupport.isArchChange(modifyInsParam.getSrcInstanceLevel(), modifyInsParam.getTargetInstanceLevel())){
            taskParam.put(PodDefaultConstants.PARAM_ARCH_CHANGED, "1");
        }

        // step3: 下发任务
        String taskKey = PodDefaultConstants.TASK_MIGRATE_BASIC_INS_AVZ;
        if (ReplicaSet.InsTypeEnum.READONLY.equals(srcReplicaSet.getInsType())) {
            taskKey = PodDefaultConstants.TASK_MIGRATE_READ_INS_AVZ;
        } else if (CATEGORY_STANDARD.equalsIgnoreCase(srcReplicaSet.getCategory())) {
            taskKey = PodDefaultConstants.TASK_MIGRATE_HA_INS_AVZ;
        }
        String domain = PodDefaultConstants.DOMAIN_MYSQL;
        Object taskId = workFlowService.dispatchTask(
                "custins", srcReplicaSet.getName(), domain, taskKey, taskParam.toString(), 0);

        Integer taskIdInt = Double.valueOf(taskId.toString()).intValue();
        this.instanceIDao.updateTransTaskIdById(transList.getId(), taskIdInt);

        return taskIdInt;
    }

    public void checkEndPointList(String requestId, ReplicaSet srcReplicaSet, ReplicaSet destReplicateSet) throws Exception {
        EndpointListResult originEndpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, srcReplicaSet.getName(), null, null, null, null);
        Optional<Endpoint> originEndpointExist = originEndpointListResult.getItems().stream().filter(e -> e.getUserVisible() && (e.getNetType().ordinal() == NetTypeEnum.VPC.ordinal())).findFirst();
        if (!originEndpointExist.isPresent()) {
            throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
        }

        EndpointListResult tmpEndpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, destReplicateSet.getName(), null, null, null, null);
        Optional<Endpoint> tmpEndpointExist = tmpEndpointListResult.getItems().stream().filter(e -> e.getUserVisible() && (e.getNetType().ordinal() == NetTypeEnum.VPC.ordinal())).findFirst();
        if (!tmpEndpointExist.isPresent()) {
            throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
        }
    }

    public void addSwitchVipChangeLog(String requestId, ReplicaSet srcReplicaSet, ReplicaSet destReplicateSet, Integer taskId) throws Exception {
        List<EndpointChangeLog> changeLogList = new ArrayList<>();

        EndpointListResult originEndpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, srcReplicaSet.getName(), null, null, null, null);
        Optional<Endpoint> originEndpointExist = originEndpointListResult.getItems().stream().filter(e -> e.getUserVisible() && (e.getNetType().ordinal() == NetTypeEnum.VPC.ordinal())).findFirst();
        if (!originEndpointExist.isPresent()) {
            throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
        }
        val originEndpoint = originEndpointExist.get();

        EndpointListResult tmpEndpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, destReplicateSet.getName(), null, null, null, null);
        Optional<Endpoint> tmpEndpointExist = tmpEndpointListResult.getItems().stream().filter(e -> e.getUserVisible() && (e.getNetType().ordinal() == NetTypeEnum.VPC.ordinal())).findFirst();
        if (!tmpEndpointExist.isPresent()) {
            throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
        }
        val tmpEndpoint = tmpEndpointExist.get();

        EndpointChangeLog updateEndpointChangeLog = new EndpointChangeLog();
        updateEndpointChangeLog
                .action(EndpointChangeLog.ActionEnum.UPDATE)
                .taskId(taskId)
                .fromConnAddrCust(originEndpoint.getAddress())
                .fromVip(originEndpoint.getVip())
                .fromVport(originEndpoint.getVport().toString())
                .fromUserVisible(originEndpoint.getUserVisible())
                .fromTunnelId(originEndpoint.getTunnelId())
                .fromUserVisible(originEndpoint.getUserVisible())
                .fromVpcId(originEndpoint.getVpcId())
                .rwType(EndpointChangeLog.RwTypeEnum.valueOf(originEndpoint.getType().name()));

        updateEndpointChangeLog.toConnAddrCust(tmpEndpoint.getAddress())
                .toVip(tmpEndpoint.getVip())
                .toVport(tmpEndpoint.getVport().toString())
                .toUserVisible(tmpEndpoint.getUserVisible())
                .toConnAddrCust(tmpEndpoint.getAddress())
                .toVpcId(tmpEndpoint.getVpcId())
                .netType(EndpointChangeLog.NetTypeEnum.VPC)
                .status(EndpointChangeLog.StatusEnum.CREATING)
                .creator(77)
                .modifier(77)
                .replicaId(destReplicateSet.getId());

        changeLogList.add(updateEndpointChangeLog);

        for (EndpointChangeLog endpointChangeLog : changeLogList) {
            dBaasMetaService.getDefaultClient().createReplicaSetEndpointChangeLog(requestId, srcReplicaSet.getName(), endpointChangeLog);
        }
    }


    private String getSlaveZoneIdForMigrating(PodModifyInsParam modifyInsParam) throws ApiException {
        var slaveAvzList = modifyInsParam.getAvzInfo().getMultiAVZExParamDO().getSlaveAvailableZoneInfo();

        // 如果用户未指定，使用原备可用区
        if (slaveAvzList.isEmpty()) {
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                    .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                            null, null, null, null);
            var slaveReplica = listReplicasInReplicaSet.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get();
            return slaveReplica.getZoneId();
        }
        return slaveAvzList.get(0).getZoneID();
    }


    private Map<String, Object> rebuildSlaveInstance(String instanceId, String dbInstanceName, String userId, String uid,
                                                     String subDomain, String zoneId, boolean isSingleTenant, String requestId,
                                                     AVZInfo targetAvz) throws RdsException {
        Map<String, String> rebuildParams = new HashMap<>();
        rebuildParams.put(ParamConstants.ACTION.toLowerCase(), "RebuildSlaveInstance");
        rebuildParams.put(ParamConstants.DB_INSTANCE_NAME.toLowerCase(), dbInstanceName);
        rebuildParams.put(ParamConstants.INSTANCE_ID.toLowerCase(), instanceId);
        rebuildParams.put(ParamConstants.USER_ID.toLowerCase(), userId);
        rebuildParams.put(ParamConstants.UID.toLowerCase(), uid);
        rebuildParams.put(ParamConstants.SUB_DOMAIN.toLowerCase(), subDomain);
        rebuildParams.put(ParamConstants.ZONE_ID.toLowerCase(), zoneId);
        rebuildParams.put(ParamConstants.REQUEST_ID.toLowerCase(), requestId);
        rebuildParams.put(PodDefaultConstants.ACTIVE_REPLICA_SET_STATUS.toLowerCase(), "true");

        // 迁移重搭添加参数，传入任务流进行更新
        if(targetAvz.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
            rebuildParams.put("masterLocation".toLowerCase(), targetAvz.getMultiAVZExParamDO().getMasterLocation());
            rebuildParams.put("slaveLocation".toLowerCase(), String.join(",",targetAvz.getMultiAVZExParamDO().getSlaveLocations()));
            rebuildParams.put("multiAvzParams".toLowerCase(), JSON.toJSONString(targetAvz.getMultiAVZExParamDO()));
        }

        // 单租户需要指定强制迁移
        String rebuildType = isSingleTenant ? SLAVE_REBUILD_TYPE_FORCE_REMOTE : SLAVE_REBUILD_TYPE_REMOTE;
        rebuildParams.put(ParamConstants.SLAVE_REBUILD_TYPE.toLowerCase(), rebuildType);

        logger.info("可用区迁移改写为备库重搭，params: " + JSON.toJSONString(rebuildParams));
        RebuildSlaveInstanceImpl rebuildSlaveInstanceImpl = SpringContextUtil.getBeanByClass(RebuildSlaveInstanceImpl.class);
        return rebuildSlaveInstanceImpl.doActionRequest(null, rebuildParams);
    }


    /**
     *  可用区是否满足Io加速条件
     */
    private void isAZSatisfiesIOAccelerationCriteria(PodModifyInsParam modifyInsParam) throws RdsException, Exception {
        String targetZoneId = modifyInsParam.getAvzInfo().getMasterZoneId();

        String regionCode = modifyInsParam.getAvzInfo().getRegionId();

        logger.info("isAZSatisfiesIOAccelerationCriteria regionCode: {}, targetZoneId: {}", regionCode, targetZoneId);

        List<String> azList = automaticSlaveZoneService.getAvailableZones(regionCode, modifyInsParam.getGeneralCloudDisk());

        if (azList == null || azList.isEmpty() || !azList.contains(targetZoneId)) {
            logger.info("{} the masterAz {}  does not satisfy the available zone for enabling IO acceleration. The azList is {}",
                    modifyInsParam.getRequestId(), targetZoneId, JSONObject.toJSONString(azList));
            throw new RdsException(ErrorCode.INVALID_AVZONE);
        }
    }
}
