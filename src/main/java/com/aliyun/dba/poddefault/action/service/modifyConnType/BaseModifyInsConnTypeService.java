package com.aliyun.dba.poddefault.action.service.modifyConnType;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.CreateReadDBInstanceImpl;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.service.modifyConnType.request.ModifyConnTypeRequest;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;

@Service
public abstract class BaseModifyInsConnTypeService {
    private static final LogAgent logger = LogFactory.getLogAgent(BaseModifyInsConnTypeService.class);


    @Resource
    public MysqlParamSupport paramSupport;
    @Resource
    public DBaasMetaService metaService;
    @Resource
    public WorkFlowService workFlowService;
    @Resource
    public GdnInstanceService gdnInstanceService;
    @Resource
    public MysqlParameterHelper mysqlParameterHelper;


    public Object doModifyConnType(ModifyConnTypeRequest request, Map<String, String> params) throws RdsException {
        try {
            //step 1: build special request
            buildSpecialRequest(request, params);

            //step 2: do basic check
            doBasicCheck(request);

            //step 3: do special check
            doSpecialCheck(request);

            // step 10: Dispatch task
            Object taskId = dispatchTask(request);


            return taskId;


        } catch (RdsException re) {
            logger.error(request.getRequestId() + " RdsException: ", re);
            throw new RdsException(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(request.getRequestId() + " Dbaas MetaApi error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "ModifyInsConnTypeFailed", "Write replicaSet meta failed: " + e.getMessage()});
        } catch (Exception ex) {
            logger.error(request.getRequestId() + " Exception: ", ex);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }


    /**
     * submit workflow
     * @param request
     * @return
     */
    private Object dispatchTask(ModifyConnTypeRequest request) throws Exception {
        String domain = "mysql";
        String taskKey = getTaskKey();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", request.getRequestId());
        jsonObject.put("replicaSetName", request.getDbInstanceName());

        String parameter = jsonObject.toJSONString();
        return workFlowService.dispatchTask("custins", request.getDbInstanceName(), domain, taskKey, parameter, 0);
    }

    /**
     * get task key
     * @return
     */
    public abstract String getTaskKey();

    /**
     * specical sub check
     *
     * @param request
     * @throws RdsException
     */
    public abstract void doSpecialCheck(ModifyConnTypeRequest request) throws RdsException, ApiException;

    /**
     * special sub request param
     *
     * @param request
     * @param params
     * @return
     * @throws ApiException
     * @throws RdsException
     */
    public abstract ModifyConnTypeRequest buildSpecialRequest(ModifyConnTypeRequest request, Map<String, String> params) throws ApiException, RdsException;


    /**
     * basic check
     *
     * @param request
     * @return
     * @throws RdsException
     * @throws ApiException
     */
    private Map<String, Object> doBasicCheck(ModifyConnTypeRequest request) throws RdsException {
        //check connType
        if (!request.getCurConnType().equals(request.getReplicaSet().getConnType().getValue())) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }

        //check replicaset status
        if (!(ReplicaSet.StatusEnum.ACTIVE.equals(request.getReplicaSet().getStatus()) ||
                ReplicaSet.StatusEnum.ACTIVATION.equals(request.getReplicaSet().getStatus()))) {
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }


        return null;
    }

}
