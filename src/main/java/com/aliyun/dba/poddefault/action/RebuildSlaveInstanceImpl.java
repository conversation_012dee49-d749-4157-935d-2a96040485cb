package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResource;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_BASIC;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRebuildSlaveInstanceImpl")
public class RebuildSlaveInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.RebuildSlaveInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Autowired
    private KmsService kmsService;
    @Autowired
    protected CustinsService custinsService;
    @Resource
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    protected PodCommonSupport podCommonSupport;
    @Resource
    protected InstanceService instanceService;
    @Resource
    protected IAction poddefaultMigrateFailoverImpl;
    @Resource
    private BakService bakService;
    @Resource
    private ServerlessResourceService serverlessResourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);

            //基础版只读重搭转迁移逻辑
            if (CATEGORY_BASIC.equals(replicaSetMeta.getCategory()) && ReplicaSet.InsTypeEnum.READONLY == replicaSetMeta.getInsType()) {
                logger.info("requestId:{} process basic read ins:{} migrate for rebuild", requestId, replicaSetMeta.getName());
                return poddefaultMigrateFailoverImpl.doActionRequest(custins, params);
            }

            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String bid = user.getBid();
            String uid = user.getAliUid();

            String rebuildNodeType = mysqlParamSupport.getParameterValue(params, "rebuildNodeType");
            String rebuildType = mysqlParamSupport.getParameterValue(params, ParamConstants.SLAVE_REBUILD_TYPE,
                    CustinsSupport.SLAVE_REBUILD_TYPE_REMOTE);
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            String zoneId = mysqlParamSupport.getParameterValue(params, ParamConstants.ZONE_ID);
            boolean isForce = mysqlParamSupport.getParameterValue(params, "IsForce", "false").equalsIgnoreCase("true");
            boolean isAliGroup = ReplicaSetService.isReplicaSetAliGroup(replicaSetMeta);
            params.put(PodDefaultConstants.UNAVAILABLE_INSTANCE_ESCAPE, mysqlParamSupport.getParameterValue(params, PodDefaultConstants.UNAVAILABLE_INSTANCE_ESCAPE, "false"));

            // 状态检查
            if (replicaSetMeta.getStatus() == ReplicaSet.StatusEnum.CREATING
                    || replicaSetMeta.getStatus() == ReplicaSet.StatusEnum.DELETING
                    || replicaSetMeta.getStatus() == ReplicaSet.StatusEnum.HA_SWITCHING) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            // 校验节点是否数据
            ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, dbInstanceName);

            boolean isCluster = MysqlParamSupport.isCluster(replicaSetResource.getReplicaSet().getCategory());

            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                    requestId, dbInstanceName, null, null, null, null);
            List<Replica> replicas = listReplicasInReplicaSet.getItems();
            logger.info("replicas count is {}", replicas.size());
            if (replicaSetMeta.getInsType() == ReplicaSet.InsTypeEnum.MAIN && replicas.size() > 3 && isAliGroup) {
                return ResponseSupport.createErrorResponse(MysqlErrorCode.ALREADY_EXISIT_REBUILD_TASK.toArray());
            }

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSetResource.getReplicaSet(), uid)) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }

            Replica.RoleEnum masterRole;
            boolean isXdbEngine = replicaSetService.isReplicaSetXDB(requestId, replicaSetResource.getReplicaSet().getName());
            if (isReadReplicaSet(replicaSetMeta)) {
                if (isXdbEngine) {
                    masterRole = Replica.RoleEnum.LEARNER;
                } else {
                    masterRole = Replica.RoleEnum.MASTER;
                }
            } else {
                masterRole = Replica.RoleEnum.MASTER;
            }
            Optional<Replica> masterReplica = replicas.stream().filter(r -> Objects.equals(r.getRole(), masterRole)).findFirst();
            if (!masterReplica.isPresent()) {
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
            }

            // TODO: 支持根据角色重搭多个节点
            Long instanceId;
            Replica.RoleEnum roleForRebuild;

            if (mysqlParamSupport.getInstanceId(params) == null && isCluster) {
                logger.error("{} rebuild instance id is required for cluster replicaset.", requestId);
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }

            // 集群版实例不支持通过Role查找重搭节点
            if (!isCluster && StringUtils.isNotEmpty(rebuildNodeType)) {
                roleForRebuild = ImmutableMap.of(
                                "FOLLOWER", Replica.RoleEnum.FOLLOWER,
                                "LOG", Replica.RoleEnum.LOGGER,
                                "SLAVE", Replica.RoleEnum.SLAVE,
                                "LEARNER_BACK", Replica.RoleEnum.LEARNER_BACK)
                        .get(rebuildNodeType);
                Replica.RoleEnum finalRoleForRebuild = roleForRebuild;
                Optional<Replica> instanceForRebuild = Objects.requireNonNull(listReplicasInReplicaSet.getItems()).stream()
                        .filter(r -> Objects.equals(r.getRole(), finalRoleForRebuild))
                        .findFirst();
                instanceId = instanceForRebuild.isPresent()
                        ? instanceForRebuild.get().getId()
                        : Long.valueOf(mysqlParamSupport.getInstanceId(params));
            } else {
                String inputInstanceId = mysqlParamSupport.getInstanceId(params);
                if (StringUtils.isBlank(inputInstanceId) || !NumberUtils.isDigits(inputInstanceId)) {
                    throw new RdsException(ErrorCode.INVALID_PARAM);
                }
                instanceId = Long.valueOf(inputInstanceId);
            }


            if (PodParameterHelper.isAliGroup(replicaSetMeta.getBizType())) {
                if (ReplicaSet.ConnTypeEnum.PHYSICAL.equals(replicaSetMeta.getConnType())
                        || ReplicaSet.ConnTypeEnum.LVS.equals(replicaSetMeta.getConnType())
                        || replicaSetService.isTddlTaskMigrate(requestId, replicaSetMeta)
                        || isReadReplicaSet(replicaSetMeta)) {
                    return rebuildSlaveForAliyun(params, user, instanceId, dbInstanceName, zoneId, replicaSetMeta, replicaSetResource, listReplicasInReplicaSet, rebuildType, isForce);
                }

                return rebuildSlaveForAliGroup(requestId, bid, uid, instanceId, dbInstanceName, zoneId, replicaSetMeta, replicaSetResource, listReplicasInReplicaSet);
            } else {
                // 非arm 集团TDDL_TASK_MIGRATE
                return rebuildSlaveForAliyun(params, user, instanceId, dbInstanceName, zoneId, replicaSetMeta, replicaSetResource, listReplicasInReplicaSet, rebuildType, isForce);
            }

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private boolean isReadReplicaSet(ReplicaSet replicaSetMeta) {
        return ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())
                || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSetMeta.getInsType());
    }

    private Map<String, Object> rebuildSlaveForAliyun(Map<String, String> params, User user, Long instanceId, String dbInstanceName, String zoneId, ReplicaSet replicaSetMeta, ReplicaSetResource replicaSetResource, ReplicaListResult listReplicasInReplicaSet, String rebuidType, boolean backupOnMaster) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        // 云盘版重搭依赖临时备份，资源申请在任务流中实现
        Optional<Replica> replicaForRebuild = Objects.requireNonNull(listReplicasInReplicaSet.getItems()).stream()
                .filter(x -> Objects.equals(x.getId(), instanceId))
                .findFirst();
        if (!replicaForRebuild.isPresent()) {
            throw new RdsException(ErrorCode.HOST_INSTANCE_NOT_FOUND);
        } else if (replicaForRebuild.get().getRole() == Replica.RoleEnum.MASTER) {
            logger.error("{} master replica does not support operations", requestId);
            throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
        }

        // 检查是否存在重搭实例
        boolean isUpgradePriority = false;
        String instanceIdStr = String.valueOf(instanceId);
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setPrimaryCustinsId(Objects.requireNonNull(replicaSetMeta.getId()).intValue());
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
        List<CustInstanceDO> mirrorIns = custinsService.getCustIns(custInstanceQuery);
        if (!mirrorIns.isEmpty()) {
            logger.info("There are tmp instance for rebuild and num is {}. The priority of the next rebuild task needed to be vip level", mirrorIns.size());
            isUpgradePriority = true;
        }
        for (CustInstanceDO custins : mirrorIns) {
            String rebuildLabel = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                                            requestId, custins.getInsName(), PodDefaultConstants.REBUILD_REPLICA);
            if (Objects.equals(rebuildLabel, instanceIdStr)) {
                throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
            }
        }

        // 支持指定备份集
        String backupSetId = mysqlParamSupport.getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        boolean isPengineBackupSet = false;
        if (StringUtils.isNotEmpty(backupSetId)) {
            BakhistoryDO bakHistory = bakService.getBakhistoryByBackupSetId(replicaSetMeta.getId().intValue(), Long.valueOf(backupSetId));
            if (bakHistory == null) {
                logger.error("{} backupset {} not found.", requestId, backupSetId);
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            } else if (!"ok".equalsIgnoreCase(bakHistory.getStatus()) || bakHistory.getIsAvail() != 1) {
                logger.error("{} backupset {} status {} isAvail {}.", requestId, backupSetId, bakHistory.getStatus(), bakHistory.getIsAvail());
                throw new RdsException(ErrorCode.INVALID_BAKSET);
            }
            isPengineBackupSet = !bakHistory.getKindCode().equals(KIND_CODE_NEW_ARCH);
        }

        // 这里使用replica.class_code 兼容变配过程中的实例重搭
        InstanceLevel replicaInsLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(
                requestId, replicaSetMeta.getService(), replicaSetMeta.getServiceVersion(), replicaForRebuild.get().getClassCode(), null);
        String diskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
        Boolean isDhg = mysqlParamSupport.isDHGCluster(replicaSetMeta.getResourceGroupName());

        // General cloud disk initialization
        GeneralCloudDisk generalCloudDisk = podCommonSupport.setGeneralCloudDiskConfig(requestId, null, replicaSetMeta.getServiceVersion(), replicaInsLevel, diskType, null, dbInstanceName);

        String tmpReplicaSetName = String.format("tmp-%s-%s", dbInstanceName, (System.currentTimeMillis() / 1000L));
        boolean isSingleTenant = replicaSetService.isCloudSingleTenant(replicaSetMeta.getBizType(), diskType, replicaInsLevel, isDhg);

        RebuildReplicaResourceRequest rebuildReplicaResourceRequest = new RebuildReplicaResourceRequest();

        RebuildReplicaResourceRequest.RebuildModeEnum rebuildMode = getRebuildMode(isSingleTenant, params);

        rebuildReplicaResourceRequest.setRebuildMode(rebuildMode);
        rebuildReplicaResourceRequest.setSingleTenant(isSingleTenant);
        rebuildReplicaResourceRequest.setTmpReplicaSetName(tmpReplicaSetName);
        rebuildReplicaResourceRequest.setIgnoreCreateVpcMapping(true);
        if (replicaSetService.isServerless(replicaSetMeta)) {
            Double rebuildRcu = serverlessResourceService.getRcuForTmpIns(requestId, replicaSetMeta);
            rebuildReplicaResourceRequest.setServerlessRcu(rebuildRcu);

            rebuildReplicaResourceRequest.putLabelsItem(PodDefaultConstants.SCHEDULE_LABEL_SERVERLESS, Boolean.TRUE.toString());
        }

        // 本地盘缓存介质IO加速实例重搭，需要重新指定资源池
        if (podCommonSupport.isLocalCacheWithIoAccelerationEnabled(generalCloudDisk)) {
            rebuildReplicaResourceRequest.setScheduleTemplate(
                    podTemplateHelper.getReplicaSetScheduleTemp(replicaSetMeta, replicaInsLevel, isSingleTenant,
                            Boolean.TRUE.equals(Objects.requireNonNull(generalCloudDisk.getWarmDataDisk()).getIoAccelerationEnabled()))
            );
        } else {
            rebuildReplicaResourceRequest.setScheduleTemplate(
                    podTemplateHelper.getReplicaSetScheduleTemp(replicaSetMeta, replicaInsLevel, isSingleTenant, null)
            );
        }

        if (replicaSetMeta.getStatus() == ReplicaSet.StatusEnum.CLASS_CHANGING) {
            JSONObject taskRet = workFlowService.getTaskByStatus(requestId, replicaSetMeta.getName(), "HUMAN_PROCESSING");
            JSONArray taskList = taskRet.getJSONArray("taskList");
            for (int index = 0; index < taskList.size(); index++) {
                JSONObject task = taskList.getJSONObject(index);
                String taskKey = task.getString("taskKey");
                if (Sets.newHashSet(PodDefaultConstants.TASK_MODIFY_INS_HA, PodDefaultConstants.TASK_MODIFY_INS_CLUSTER, PodDefaultConstants.TASK_LOCAL_MODIFY_INS_HA).contains(taskKey)) {
                    isUpgradePriority = true;
                }
            }
        }


        //通过获取custins_param中的rs_schedule_template_name，然后查询rs_schedule_template获取到key和value
        if (PodParameterHelper.isAliGroup(replicaSetMeta.getBizType())) {
            PodScheduleTemplate podScheduleTemplate = podTemplateHelper.getReplicaSetPodScheduleTemplate(replicaSetResource.getReplicaSet());
            if (podScheduleTemplate != null) {
                rebuildReplicaResourceRequest.setReplicaScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(podScheduleTemplate, replicaForRebuild.get().getRole().toString()));
            }
        }


        // 如果接口指定了调度策略，则透传下去
        if (StringUtils.isNotEmpty(mysqlParamSupport.getResourceStrategy(params))) {
            if (rebuildReplicaResourceRequest.getScheduleTemplate() == null) {
                rebuildReplicaResourceRequest.setScheduleTemplate(new ScheduleTemplate());
            }
            rebuildReplicaResourceRequest.getScheduleTemplate().setResourceStrategy(mysqlParamSupport.getResourceStrategy(params));
        }

        String hostName = podParameterHelper.getHostNameFromParamsByDedicatedHostName(ActionParamsProvider.ACTION_PARAMS_MAP.get(), "DedicatedHostNames", replicaSetResource.getReplicaSet().getResourceGroupName());
        rebuildReplicaResourceRequest.setHostName(hostName);
        if (!Objects.isNull(mysqlParamSupport.getAndCheckHostName(params))) {  // 优先使用 HostName 来赋值
            rebuildReplicaResourceRequest.setHostName(mysqlParamSupport.getAndCheckHostName(params));
        }
        rebuildReplicaResourceRequest.setCategory(getCategory(requestId, replicaSetMeta));

        // 支持备库跨可用区重搭
        if (StringUtils.isNotBlank(zoneId)) {
            rebuildReplicaResourceRequest.setZoneId(zoneId);
        }

        // 指定composeTag
        String composeTag = minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(requestId, replicaSetMeta.getName());
        rebuildReplicaResourceRequest.setComposeTag(composeTag);

        boolean isPfs = replicaSetService.isCloudPfsDisk(requestId, dbInstanceName);
        String storageType = replicaSetService.getReplicaSetStorageType(dbInstanceName, requestId);
        boolean isLocalDisk = !ReplicaSetService.isStorageTypeCloudDisk(storageType);
        String performanceLevel = null;
        if (replicaSetMeta.getInsType() == ReplicaSet.InsTypeEnum.READONLY) {
            performanceLevel = replicaSetService.getVolumePerfLevel(requestId, dbInstanceName, storageType);
        }

        if (PodParameterHelper.isAliGroup(replicaSetMeta.getBizType())) {
            rebuildReplicaResourceRequest.setVolumeSpecs(buildEssdVolumeSpecList(replicaForRebuild.get(), requestId));
        }

        Replica destReplica;
        boolean isAllocate = false;
        try {
            isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceForRebuild(requestId, dbInstanceName,
                    instanceId, rebuildReplicaResourceRequest);

            // 临时实例更新为镜像实例，规格码为replica.class_code
            CustInstanceDO custInstanceDO = custinsService.getCustInstanceByInsName(null, tmpReplicaSetName, CUSTINS_INSTYPE_TMP);
            if (custInstanceDO == null) {
                logger.error("Cannot find tmp custins [{}]", tmpReplicaSetName);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            if (replicaForRebuild.get().getRole() != Replica.RoleEnum.LOGGER) {
                InstanceLevelDO replicaInsLevelDO = instanceService.getInstanceLevelByClassCode(
                        replicaInsLevel.getClassCode(),
                        replicaInsLevel.getService(),
                        replicaInsLevel.getServiceVersion(),
                        null, null);
                custInstanceDO.setLevelId(replicaInsLevelDO.getId());
            }

            custInstanceDO.setInsType(CUSTINS_INSTYPE_MIRROR);
            custInstanceDO.setGmtModified(new Date());
            custinsService.updateCustInstance(custInstanceDO);

            // 集群版备库重搭适配:重搭时需要在临时实例上新增标签 用于确定该节点是否重复下发重搭
            Map<String, String> labelsForTmp = new HashMap<>();
            labelsForTmp.put(PodDefaultConstants.REBUILD_REPLICA, String.valueOf(instanceId));
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSetName, labelsForTmp);

            // 只读实例配置白名单同步label
            podParameterHelper.updateReadInsLabels(requestId, replicaSetResource.getReplicaSet(), tmpReplicaSetName);

            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, tmpReplicaSetName, null, null, null, null);
            destReplica = replicaListResult.getItems().get(0);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("srcReplicaId", instanceId);
            jsonObject.put("destReplicaId", destReplica.getId());
            jsonObject.put("srcReplicaSetName", dbInstanceName);
            jsonObject.put("srcParentReplicaSetName", replicaSetMeta.getPrimaryInsName());
            jsonObject.put("destReplicaSetName", tmpReplicaSetName);
            jsonObject.put("backupOnMaster", backupOnMaster);
            jsonObject.put("performanceLevel", performanceLevel);
            jsonObject.put("rebuildMode", rebuildMode);
            jsonObject.put("isServerless", replicaSetService.isServerless(replicaSetMeta));

            //For serverless
            if (replicaSetService.isServerless(replicaSetMeta)) {
                Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSetMeta.getName());
                jsonObject.put("scaleMin", labels.get(ServerlessConstant.SCALE_MIN));
                jsonObject.put("scaleMax", labels.get(ServerlessConstant.SCALE_MAX));
                jsonObject.put("autoPause", labels.get(ServerlessConstant.AUTO_PAUSE));
                jsonObject.put("secondsUntilAutoPause", labels.get(ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE));
                jsonObject.put("dasAutoPause", labels.get(ServerlessConstant.DAS_AUTO_PAUSE));
                jsonObject.put("keepRunningTime", labels.get(ServerlessConstant.KEEP_RUNNING_TIME));
            }

            if (StringUtils.isNotEmpty(backupSetId)) {
                jsonObject.put("backupSetId", backupSetId);
                jsonObject.put("isPengineBackupSet", isPengineBackupSet);
            }

            // 备可用区迁移时，下发此参数，任务流中会订正实例状态为active
            String activeReplicaSetStatus = podParameterHelper.getParameterValue(PodDefaultConstants.ACTIVE_REPLICA_SET_STATUS, "");
            if (StringUtils.isNotEmpty(activeReplicaSetStatus)) {
                jsonObject.put("activeReplicaSetStatus", activeReplicaSetStatus);
            }
            String subDomain = podParameterHelper.getParameterValue(ParamConstants.SUB_DOMAIN, "");
            if (StringUtils.isNotBlank(subDomain)) {
                jsonObject.put("zoneId", zoneId);
                jsonObject.put("subDomain", subDomain);
                // 添加请求的avz信息，在任务流中更新
                jsonObject.put("masterLocation", podParameterHelper.getParameterValue("masterLocation", ""));
                jsonObject.put("slaveLocation", podParameterHelper.getParameterValue("slaveLocation", ""));
                jsonObject.put("multiAvzParams", podParameterHelper.getParameterValue("multiAvzParams", ""));
            }

            String parameter = jsonObject.toJSONString();
            logger.info("rebuild slave parameter {}", parameter);

            boolean unavailableInstanceEscape = Boolean.parseBoolean(podParameterHelper.getParameterValue(PodDefaultConstants.UNAVAILABLE_INSTANCE_ESCAPE, false));
            String taskKey = getTaskKey(replicaSetResource, replicaForRebuild.get(), rebuildMode, isPfs, isLocalDisk, unavailableInstanceEscape);
            Integer priority = isUpgradePriority ? WorkFlowService.TASK_PRIORITY_VIP : WorkFlowService.TASK_PRIORITY_COMMON;
            Object taskId = workFlowService.dispatchTask(replicaSetMeta, taskKey, parameter, priority);
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("DBInstanceName", dbInstanceName);
            return data;
        } catch (Exception e) {
            isAllocate = false;
            if (e instanceof ApiException) {
                logger.error(requestId + " Exception: {}", ((ApiException) e).getResponseBody());
                throw e;
            }
            logger.error(requestId + " Exception: {}", e.getMessage());
            throw e;
        } finally {
            if (!isAllocate) {
                //分配失败或者其它异常的情况下，要调用释放资源接口
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, tmpReplicaSetName);
                } catch (ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    private String getCategory(String requestId, ReplicaSet replicaSetMeta) throws com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        String category = replicaSetMeta.getCategory();
        if (isReadReplicaSet(replicaSetMeta)) {
            ReplicaSet primaryReplicaSet = podCommonSupport.getPrimaryReplicaSet(requestId, replicaSetMeta).getRight();
            if (primaryReplicaSet != null) {
                category = primaryReplicaSet.getCategory();
            }
        }
        return category;
    }

    public Map<String, Object> rebuildSlaveForAliGroup(String requestId, String bid, String uid, Long instanceId, String dbInstanceName, String zoneId, ReplicaSet replicaSetMeta,
                                                       ReplicaSetResource replicaSetResource, ReplicaListResult listReplicasInReplicaSet) throws Exception {
        List<Replica> currentReplicas = listReplicasInReplicaSet.getItems().stream().filter(x -> x.getId().equals(instanceId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentReplicas)) {
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "ReplicaNotExists", "Replica not in the current replicaset."});
        }
        Replica replicaWillRebuild = currentReplicas.get(0);
        String diskType = replicaWillRebuild.getStorageType().toString();

        // FIXME 后面封装统一的方法来处理
        CreateReplicaSetDto replicaSet = new CreateReplicaSetDto();
        Map<String, String> lables = replicaSetResource.getReplicaSet().getLabels();
        String dbEngine = lables.getOrDefault("dbEngine", "XDB");

        if (replicaSetMeta.getConnType() == ReplicaSet.ConnTypeEnum.TDDL) {
            String clusterName = lables.get("clusterName");
            String storageEngine = lables.get("storageEngine");
            String timeZone = lables.get("timeZone");
            String charset = lables.get("charset");
            String dbName = clusterName.split("_")[0].toLowerCase();
            replicaSet.setStorageEngine(storageEngine);
            replicaSet.setTimeZone(timeZone);
            replicaSet.setCharset(charset);
            replicaSet.setClusterName(clusterName);
            replicaSet.setAppName(StringUtils.upperCase(dbName));
            // clustername 的 hash
            replicaSet.setClusterId(DigestUtils.md5DigestAsHex(dbName.toUpperCase().getBytes()));
        }
        Integer diskSize = replicaSetMeta.getDiskSizeMB() / 1024;

        //必传实例名，不然资源分配无法打标
        replicaSet.setReplicaSetName(dbInstanceName);
        replicaSet.setUserId(bid);
        replicaSet.setUid(uid);
        replicaSet.setInstanceId(dbInstanceName);
        replicaSet.setDbType(replicaSetMeta.getService());
        replicaSet.setDbVersion(replicaSetMeta.getServiceVersion());
        replicaSet.setConnType(replicaSetMeta.getConnType().toString());
        replicaSet.setInsType(replicaSetMeta.getInsType().toString());
        replicaSet.setBizType("aligroup");
        replicaSet.setDiskSize(diskSize);
        replicaSet.setStorageType(diskType);
        replicaSet.setDbEngine(dbEngine);
        if (replicaSetService.isStorageTypeCloudDisk(diskType)) {
            replicaSet.setComposeTag("cloud_pfs");
        }
        replicaSet.setRequestId(requestId);

        List<ReplicaDto> replicas = new ArrayList<>();
        ReplicaDto replica = new ReplicaDto();
        String role = replicaWillRebuild.getRole().toString();
        replica.setId(dbInstanceName + "-" + role);
        replica.setRole(role);
        replica.setSubDomain(replicaWillRebuild.getSubDomain());
        replica.setZoneId(StringUtils.isNotEmpty(zoneId) ? zoneId : replicaWillRebuild.getZoneId());
        replica.setDiskType(diskType);
        replica.setStorageType(diskType);
        if (ReplicaSetService.isStorageTypeCloudDisk(diskType) && !"logger".equalsIgnoreCase(role)) {
            replica.setVolumeSpecs(replicaSetService.getCloudDiskReplicaVolumeSpecList(diskSize, dbInstanceName));
        }
        replica.setClassCode(replicaWillRebuild.getClassCode());
        replica.setDiskSize(replicaWillRebuild.getDiskSizeMB() / 1024);
        replica.setNodeGroup("mix");
        replica.setNodeType("normal");
        //检查实例是否指定资源调度模版创建
        Map<String, String> labels = replicaSetResource.getReplicaSet().getLabels();
        if (MapUtils.isNotEmpty(labels)) {
            String rsTemplateName = labels.get(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME);
            PodScheduleTemplate podScheduleTemplate = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, rsTemplateName, bid + "_" + uid);

            if (podScheduleTemplate != null) {
                replica.setScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(podScheduleTemplate, role));
            }
        }
        replicas.add(replica);
        replicaSet.setReplicaDtoList(replicas);

        // Alocate resource
        CreateReplicaSetDto response = commonProviderService
                .getDefaultApi().allocateAddMorePod(replicaSet);
        List<ReplicaResource> responseMeta;
        if (response != null) {
            responseMeta = commonProviderService.getDefaultApi().writeMorePod(response);
            if (responseMeta == null) {
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write meta return null."});
            }
        } else {
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
        }

        String destReplicaId = responseMeta.get(0).getReplica().getId().toString();

        // Add Task
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", replicaSet.getRequestId());
        jsonObject.put("srcReplicaId", replicaWillRebuild.getId());
        jsonObject.put("destReplicaId", destReplicaId);
        jsonObject.put("replicaSetName", replicaSet.getReplicaSetName());

        String parameter = jsonObject.toJSONString();
        String domain = StringUtils.equalsIgnoreCase("xdb", dbEngine) ? "xdb" : "mysql";
        String taskKey = "remote_rebuild_xdb_follower";
        if (replicaWillRebuild.getRole().equals(Replica.RoleEnum.LOGGER)) {
            taskKey = "remote_rebuild_xdb_logger";
        }
        Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);

        Map<String, Object> data = new HashMap<>();
        data.put("TaskId", taskId);
        data.put("DestReplicaId", destReplicaId);
        data.put("DBInstanceName", dbInstanceName);
        return data;
    }

    /**
     * 获取重搭方式，包括本地重搭和跨机重搭
     */
    private RebuildReplicaResourceRequest.RebuildModeEnum getRebuildMode(boolean isSingleTenant, Map<String, String> params) {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID);
        String rebuildType = mysqlParamSupport.getParameterValue(params, ParamConstants.SLAVE_REBUILD_TYPE,
                CustinsSupport.SLAVE_REBUILD_TYPE_REMOTE);

        RebuildReplicaResourceRequest.RebuildModeEnum rebuildMode;
        if (StringUtils.equalsIgnoreCase(rebuildType, CustinsSupport.SLAVE_REBUILD_TYPE_LOCAL)) {
            rebuildMode = RebuildReplicaResourceRequest.RebuildModeEnum.INPLACE;
        } else {
            rebuildMode = RebuildReplicaResourceRequest.RebuildModeEnum.MIGRATE;
        }

        // 酒保下发重搭的情况下，单租户使用本地重搭的方式
        boolean isForceRemote = CustinsSupport.SLAVE_REBUILD_TYPE_FORCE_REMOTE.equalsIgnoreCase(rebuildType);
        if (!isForceRemote && isSingleTenant &&
                (StringUtils.equalsAnyIgnoreCase(accessId, ParamConstants.PENGINE_ACCESS, PodDefaultConstants.TFC_ACCESS))) {
            logger.info("AccessId is [{}] and ReplicaSet is single tenant, set rebuild mode to INPLACE.", accessId);
            rebuildMode = RebuildReplicaResourceRequest.RebuildModeEnum.INPLACE;
        }
        return rebuildMode;
    }

    private List<VolumeSpec> buildEssdVolumeSpecList(Replica replica, String requestId) throws Exception {
        List<VolumeSpec> volumeSpecList = new ArrayList<>();
        VolumeSpec dataVolumeSpec = replicaSetService.buildEssdVolumeSpec(replica, "data", requestId);
        if (dataVolumeSpec != null) {
            volumeSpecList.add(dataVolumeSpec);
        }
        VolumeSpec logVolumeSpec = replicaSetService.buildEssdVolumeSpec(replica, "log", requestId);
        if (logVolumeSpec != null) {
            volumeSpecList.add(logVolumeSpec);
        }
        return volumeSpecList;
    }

    /**
     * 获取对应的TaskKey
     */
    private String getTaskKey(ReplicaSetResource replicaSetResource,
                              Replica replicaForRebuild,
                              RebuildReplicaResourceRequest.RebuildModeEnum rebuildMode,
                              boolean isPfs,
                              boolean isLocalDisk,
                              boolean unavailableInstanceEscape) throws RdsException, com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        String taskKey;
        switch (Objects.requireNonNull(replicaForRebuild.getRole())) {
            case SLAVE:
                if (MysqlParamSupport.isCluster(replicaSetResource.getReplicaSet().getCategory())) {
                    if (replicaSetService.isMgr(replicaSetResource.getReplicaSet().getLabels())) {
                        taskKey = rebuildMode == RebuildReplicaResourceRequest.RebuildModeEnum.MIGRATE ?
                                PodDefaultConstants.TASK_REBUILD_MGR_SLAVE :
                                PodDefaultConstants.TASK_REBUILD_MGR_SLAVE_LOCAL;
                    } else {
                        taskKey = rebuildMode == RebuildReplicaResourceRequest.RebuildModeEnum.MIGRATE ?
                                PodDefaultConstants.TASK_REBUILD_CLUSTER_SLAVE :
                                PodDefaultConstants.TASK_REBUILD_CLUSTER_SLAVE_LOCAL;
                    }

                } else {
                    taskKey = rebuildMode == RebuildReplicaResourceRequest.RebuildModeEnum.MIGRATE ?
                            PodDefaultConstants.TASK_REBUILD_SLAVE :
                            PodDefaultConstants.TASK_REBUILD_SLAVE_LOCAL;
                    // 只有跨机重搭才会走逃逸流程
                    if (PodDefaultConstants.TASK_REBUILD_SLAVE.equals(taskKey) && unavailableInstanceEscape) {
                        taskKey = PodDefaultConstants.TASK_REBUILD_SLAVE_FOR_ESCAPE;
                    }
                }
                break;
            case LOGGER:
                taskKey = PodDefaultConstants.TASK_REBUILD_XDB_LOGGER;
                break;
            case FOLLOWER:
                if (CONN_TYPE_TDDL.equalsIgnoreCase(replicaSetResource.getReplicaSet().getConnType().toString())) {
                    taskKey = PodDefaultConstants.TASK_REBUILD_TDDL_XDB_SLAVE;
                } else if (isPfs || isLocalDisk) {
                    taskKey = PodDefaultConstants.TASK_REBUILD_XDB_SLAVE_BY_NC;
                } else {
                    taskKey = PodDefaultConstants.TASK_REBUILD_XDB_SLAVE;
                }
                break;
            case LEARNER_BACK:
                if (CONN_TYPE_TDDL.equalsIgnoreCase(replicaSetResource.getReplicaSet().getConnType().toString())) {
                    taskKey = PodDefaultConstants.TASK_REBUILD_TDDL_XDB_LEARNER;
                } else {
                    taskKey = PodDefaultConstants.TASK_REBUILD_XDB_LEARNER_BACK;
                }
                break;
            default:
                throw new RdsException(ErrorCode.INVALID_INS_TYPE);
        }
        return taskKey;
    }

}
