package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.ColdDataService;
import com.aliyun.dba.poddefault.action.service.CompressionModeService;
import com.aliyun.dba.poddefault.action.service.migrate.MigrateDBInstanceAvzService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES_INFO;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.LOCAL_MODIFY_POD_MODES;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_PARAMETERS;

/**
 * 云上变配实例，待整合
 *
 * <AUTHOR> on 2020/6/12.
 */
@Service
public class AliyunModifyDBInstanceService extends BaseModifyDBInstanceService {
    @Resource
    private ModifyDBInstanceDiskTypeService modifyDBInstanceDiskTypeService;

    @Resource
    private MigrateDBInstanceAvzService migrateDBInstanceAvzService;

    @Resource
    private  ModifyDBInstanceIOAccelerationService modifyDBInstanceIOAccelerationService;

    @Resource
    private ColdDataService coldDataService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private ModifyDBInstanceOptimizedWritesService modifyDBInstanceOptimizedWritesService;

    @Resource
    private CompressionModeService compressionModeService;


    /**
     * 变配实例
     *
     * @param custins
     * @param params
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        Object transfer = null;
        // 标记是否申请资源
        boolean isAllocResource = false;
        // 标记请求是否成功
        boolean isSuccess = false;
        PodModifyInsParam modifyInsParam = null;
        try {
            // 可用区迁移
            String migratingAvz = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false");
            if (Boolean.parseBoolean(migratingAvz)) {
                return migrateDBInstanceAvzService.doActionRequest(custins, params);
            }

            // 初始化变配参数
            modifyInsParam = initPodModifyInsParam(params);
            ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient()
                    .getReplicaSetBundleResource(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName());
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                    .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                            null, null, null, null);

            String tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName();
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            assert currentReplicas != null;
            PodModifyInsParam.replicaNameValidate(currentReplicas, tmpReplicaSetName);

            if (replicaSetService.isMgr(requestId, modifyInsParam.getDbInstanceName())) {
                replicaSetService.isSupportMgr(currentReplicas.size(), modifyInsParam.getTargetInstanceLevel(), null);
            }

            int priority = 0;

            // 记录哪些节点申请了资源，用于在N副本场景下感知节点对应关系
            List<String> modifyReplicas = new ArrayList<>();

            if(PodParameterHelper.isAliGroup(replicaSetResource.getReplicaSet().getBizType()) && listReplicasInReplicaSet.getItems().size() > 2 && (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetResource.getReplicaSet().getInsType())
                    || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSetResource.getReplicaSet().getInsType())) ){
                //集团主实例上云过程中只读形态 不允许变配/迁移
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "Main Ins Read Not Allowed Modify.", "Main Ins Read Not Allowed Modify."});
            }

            //集团实例不处理autopl配置，仅公有云支持
            if(!ReplicaSetService.isReplicaSetAliGroup(modifyInsParam.getReplicaSetMeta()))
            {
                if (modifyInsParam.isCompressionModeChange()) {
                    return compressionModeService.modifyCompressionMode(modifyInsParam);
                }

                if(modifyInsParam.isModifyColdDataConfig()){
                    modifyInsParam.initSrcMinorVersion();
                    if(modifyInsParam.checkModifyColdDataConfigConflict()){
                        return coldDataService.modifyColdDataConfig(modifyInsParam.getDbInstanceName(), modifyInsParam.isColdDataEnabled(), modifyInsParam.getRegionId());
                    }
                    throw new RdsException(ErrorCode.UNSUPPORTED_MODIFY_COLD);
                }
                //修改autopl配置，优先判断
                if (modifyInsParam.isModifyCloudAutoConfig())
                {
                    //检查约束：规格、大小、性能等级是否一块变化，
                    if(modifyInsParam.isOnlyModifyCloudAutoConfig())
                    {
                        return modifyDBInstanceDiskTypeService.doModifyCloudAutoConfig(requestId, modifyInsParam, modifyInsParam.getReplicaSetMeta());
                    }
                    // 不允许规格、大小、性能等级与autopl配置一同变化
                    throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedModifyParam", "burst param must be only modified"});
                }

                // Modify IO Acceleration
                // It is allowed to change the instance specification while changing the IO Acceleration
                if (modifyInsParam.isIoAccelerationEnabledChange()) {
                    // Instances upgraded from the basic version to high availability use the original task flow
                    if (!(MysqlParamSupport.checkCategory(modifyInsParam.getSrcInstanceLevel(), InstanceLevel.CategoryEnum.BASIC) && MysqlParamSupport.checkCategory(modifyInsParam.getTargetInstanceLevel(), InstanceLevel.CategoryEnum.STANDARD))) {
                        return modifyDBInstanceIOAccelerationService.doActionRequest(custins, params);
                    }
                }
            }

            if (mysqlParamSupport.hasParameter(params, ParamConstants.OPTIMIZED_WRITES)) {
                String optimizedWrites = mysqlParamSupport.getParameterValue(params, ParamConstants.OPTIMIZED_WRITES, null);
                String primaryOptimizedWritesInfo;
                try {
                    primaryOptimizedWritesInfo = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, mysqlParamSupport.getDBInstanceName(params), OPTIMIZED_WRITES_INFO);
                } catch (ApiException e) {
                    throw new RuntimeException(e);
                }
                if (StringUtils.isNotBlank(optimizedWrites)) {
                    boolean targetOptimizedWrites = PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites);
                    boolean primaryOptimizedWrites = podCommonSupport.isOptimizedWrites(primaryOptimizedWritesInfo);
                    if (targetOptimizedWrites != primaryOptimizedWrites) {
                        // 规格、大小、性能等级不允许一同变化
                        if (modifyInsParam.isChangeInvolvingOrderModification()) {
                            logger.error("optimized writes must be only modified");
                            throw new RdsException(INVALID_PARAMETERS);
                        }
                        return modifyDBInstanceOptimizedWritesService.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
                    }
                }
            }

            // 控制台请求，在什么都没有发生改变的情况下，直接返回成功
            if (modifyInsParam.isAliyun() &&
                    modifyInsParam.isTransIns() &&
                    (modifyInsParam.isYaoChiRequest() || podParameterHelper.isSingleTenant(modifyInsParam.getReplicaSetMeta()))
            ) {
                logger.info("{} nothing has changed, return it.", modifyInsParam.getRequestId());
                return getResponse(modifyInsParam, null);
            }
            // 在线扩容（大小及性能等级）
            else if (modifyInsParam.isOnLineResize()) {
                priority = 1;
                transfer = buildTransListForOnlineResize(modifyInsParam, currentReplicas);
            }
            // 磁盘类型变更 CLOUD_SSD to CLOUD_ESSD
            else if ((modifyInsParam.isOnlyDiskTypeChange() || InstanceLevel.CategoryEnum.BASIC.equals(modifyInsParam.getTargetInstanceLevel().getCategory())) &&  // 仅放开基础版变配+磁盘类型
                    (modifyInsParam.isModifyCloudSSDToCloudEssd() || modifyInsParam.isPerfLevelChangedToPL0ForBasic()) &&  // 变更到PL0也复用这里的代码
                    !ReplicaSetService.isReplicaSetAliGroup(modifyInsParam.getReplicaSetMeta())) {
                return modifyDBInstanceDiskTypeService.doActionRequest(modifyInsParam, params);
            }
            // 磁盘类型变更 CLOUD_ESSD to CLOUD_AUTO
            else if (modifyInsParam.isModifyCloudESSDToCloudAuto()) {
                if (modifyInsParam.isOnlyDiskTypeChange() &&
                        !ReplicaSetService.isReplicaSetAliGroup(modifyInsParam.getReplicaSetMeta())) {
                    boolean isServerless = PodCommonSupport.isServerless(modifyInsParam.getSrcInstanceLevel());
                    if (isServerless) {
                        // serverless暂时不允许essd到auto的变更
                        throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedModifyServerlessDiskType", "serverless can't modify disk type"});
                    }
                    return modifyDBInstanceDiskTypeService.doModifyCloudAutoConfig(requestId, modifyInsParam, modifyInsParam.getReplicaSetMeta());
                }
                // 暂时不允许essd到auto的变更与磁盘大小和规格同时变更
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedModifyDiskTypeWithOthers", "disk type can't modify with disk size and class code"});
            }
            // 单租户本地升降配
            else if (isSingleTenantLocalModify(modifyInsParam)) {
                transfer = buildTransListForLocalModify(modifyInsParam);
                modifyInsParam.setSingleTenantLocalModify(true);
            }
            // 跨机升降配 & Serverless & 快弹
            else {
                isAllocResource = true;
                transfer = modifyWithAllocateResource(modifyInsParam, currentReplicas, modifyReplicas);
            }

            TransferTask transferTask = getTransferTask(modifyInsParam, transfer);

            String taskKey = getTaskKey(modifyInsParam, transferTask);

            // 只读实例配置白名单同步label
            podParameterHelper.setReadInsSgLabel(requestId, replicaSetResource.getReplicaSet(), transferTask.getDestReplicaSetName());

            // vmoc-lite是设置vbm标签
            if(modifyInsParam.isVbm()){
                podParameterHelper.setVbmLabelForTempReplicaSet(requestId, replicaSetResource.getReplicaSet(), transferTask.getDestReplicaSetName());
            }

//            // IOAcceleration status needs to update the target metadata
//            modifyIoAccelerationStatus(requestId, transferTask.getDestReplicaSetName(), modifyInsParam);

            // dispatch task
            JSONObject taskParamObject = buildTaskParam(modifyInsParam, transferTask, modifyReplicas);
            String taskParam = taskParamObject.toJSONString();
            Object taskId = workFlowService.dispatchTask(
                    modifyInsParam.getReplicaSetMeta(), taskKey, taskParam, priority);

            isSuccess = true;

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                    modifyInsParam.isTransIns() ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

            // build response
            return getResponse(modifyInsParam, taskId);
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (isAllocResource && transfer != null && !isSuccess && modifyInsParam != null) {
                try {
                    // 本地变配的情况下，使用rollback接口
                    if (LOCAL_MODIFY_POD_MODES.contains(modifyInsParam.getModifyMode())) {
                        logger.info("{} rollback resource with rollback api.", modifyInsParam.getRequestId());
                        commonProviderService.getDefaultApi().rollbackReplicaSetResourceForScale(
                                modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                                modifyInsParam.getTmpReplicaSetName(), new ArrayList<>());
                    } else {
                        logger.info("{} rollback resource with release api.", modifyInsParam.getRequestId());
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                                modifyInsParam.getRequestId(), modifyInsParam.getTmpReplicaSetName()
                        );
                    }

                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error("{} rollback resource failed: {}", modifyInsParam.getRequestId(), e.getResponseBody());
                }
            }
        }
    }

    /**
     * 接口返回
     * */
    private Map<String, Object> getResponse(PodModifyInsParam modifyInsParam, Object taskId) {
        Map<String, Object> data = new HashMap<>();
        data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
        data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
        data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
        data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
        data.put("SourceRcu", modifyInsParam.getSrcRcu());
        data.put("TargetRcu", modifyInsParam.getRcu());
        data.put("TaskId", taskId);
        data.put("Region", modifyInsParam.getRegionId());
        return data;
    }
}
