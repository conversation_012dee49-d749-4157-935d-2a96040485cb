package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.upgradeprecheck.UpgradeMajorVersionPreCheckExecutor;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.physical.action.UpgradeDBVersionImpl;
import com.aliyun.dba.physical.action.service.MysqlMajorVersionCheckService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_VERSION_MYSQL_57;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_VERSION_MYSQL_80;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultUpgradeDBMajorVersionImpl")
public class UpgradeDBMajorVersionImpl implements IAction {

    @Resource
    private MysqlMajorVersionCheckService mysqlMajorVersionCheckService;

    @Resource
    private ClusterService clusterService;

    @Resource
    private CustinsService custinsService;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private ReplicaSetService replicaSetService;

    @Resource
    protected DBaasMetaService metaService;

    @Resource
    protected MinorVersionService minorVersionService;

    @Resource
    protected MinorVersionServiceHelper minorVersionServiceHelper;

    @Resource
    protected BaseModifyDBInstanceService baseModifyDBInstanceService;

    @Resource
    protected PodTemplateHelper podTemplateHelper;

    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Resource
    protected CommonProviderService commonProviderService;

    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Resource
    protected CustinsParamGroupsService custinsParamGroupsService;

    @Resource
    protected ResourceService resourceService;

    @Autowired
    private UpgradeMajorVersionPreCheckExecutor preCheckExecutor;

    //大版本升级开关
    private static final String UPGRADE_MAJOR_VERSION_SWITCH = "UPGRADE_MAJOR_VERSION_SWITCH";

    //大版本升级前置检查开关
    private static final String UPGRADE_MAJOR_VERSION_PRECHECK_SWITCH = "UPGRADE_MAJOR_VERSION_PRECHECK_SWITCH";

    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeDBVersionImpl.class);
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID, "");
        ReplicaSet replicaSetMeta;
        try{

            // 大版本升级开关
            ResourceDO upgradeSwitch = resourceService.getResourceByResKey(UPGRADE_MAJOR_VERSION_SWITCH);
            if (Objects.isNull(upgradeSwitch) || !BooleanUtils.toBoolean(upgradeSwitch.getRealValue())) {
                logger.info("requestId : {}, UPGRADE_MAJOR_VERSION_SWITCH is off.", requestId);
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            String dbType = mysqlParamSupport.getAndCheckDBType(actionParams, "mysql");
            replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(actionParams);
            String dbVersion = replicaSetMeta.getServiceVersion();
            String targetMajorVersion = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.MAJOR_VERSION);
            PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(actionParams);
            ResourceDO upgradePreCheckSwitch = resourceService.getResourceByResKey(UPGRADE_MAJOR_VERSION_PRECHECK_SWITCH);
            String newCheckSwitch;
            if (Objects.isNull(upgradePreCheckSwitch) || !BooleanUtils.toBoolean(upgradePreCheckSwitch.getRealValue())
                    || InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSetMeta.getCategory())
                    || ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())) {
                logger.info("requestId : {}, UPGRADE_MAJOR_VERSION_PRECHECK_SWITCH is off.", requestId);
                newCheckSwitch = "false";
                int custinsId = Objects.requireNonNull(replicaSetMeta.getId()).intValue();

                //判断实例状态是否是运行中
                ReplicaSet.StatusEnum custinsStatus = replicaSetMeta.getStatus();
                if (ReplicaSet.StatusEnum.ACTIVATION != custinsStatus && ReplicaSet.StatusEnum.ACTIVE != custinsStatus) {
                    logger.info("Current instance status is " + custinsStatus + ", not support this operation.");
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }

                //锁定状态不支持
                if (ReplicaSet.LockModeEnum.NOLOCK != replicaSetMeta.getLockMode()) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
                }

                //暂时只支持公有云
                if (!PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
                    logger.info("biz type = " + replicaSetMeta.getBizType() + ", not supported yet!");
                    return createErrorResponse(ErrorCode.INVALID_BIZ_TYPE);
                }

                //暂时不支持政务云
                ClustersDO cluster = clusterService.getClusterByCustinsId((long) custinsId);
                logger.info("cluster name = " + custins.getClusterName() + ", cluster.location = "+ cluster.getLocation());
                if (custins.getClusterName().contains("GOV") || cluster.getLocation().contains("gov")) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
                }

                //云盘暂时只有5.7升级8.0

                boolean is57to80 = DB_VERSION_MYSQL_57.equals(dbVersion)&& DB_VERSION_MYSQL_80.equalsIgnoreCase(targetMajorVersion);
                if (!is57to80) {
                    logger.info("The upgrade goal is to upgrade from 5.7 to 8.0, but current db_version = " + dbVersion + ", target db_version = " + targetMajorVersion);
                    return createErrorResponse(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
                }

                //判断5.7实例当前的规格在8.0是否存在
                boolean levelSupport = custinsService.checkInstanceLevelSupportUpgradeTargetMajorVersion(custinsId, targetMajorVersion);
                if (!levelSupport){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
                }

                // 不支持SSD云盘
                String diskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
                if (Replica.StorageTypeEnum.CLOUD_SSD.toString().equals(diskType)) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_CLOUD_SSD);
                }

                // mysql80不支持proxy链路, 因此57升级80, 校验当前实例必须是lvs链路
                if (custins.isMysql57() && !custins.isLvs()) {
                    logger.info("conn_type = " + custins.getConnType() + ", curr instance conn_type is not lvs, not support this operation.");
                    return createErrorResponse(ErrorCode.INVALID_CONN_TYPE);
                }

                //需要是主实例,不能是临时实例
                if (!ReplicaSet.InsTypeEnum.MAIN.equals(replicaSetMeta.getInsType()) && !ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }

                //只读实例只允许主实例从任务流下发
                if(ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType()) && modifyInsParam.isYaoChiRequest()){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }

                //暂时只支持基础版、高可用、集群版升级大版本, 不支持serverless、xdb等
                if (!InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(replicaSetMeta.getCategory()) &&
                        !InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSetMeta.getCategory()) &&
                        !InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                    logger.info("Current instance category is " + replicaSetMeta.getCategory() + ", not support this operation.");
                    return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
                }

                //不支持xdb
                boolean isXDB = modifyInsParam.isXDB();
                if (isXDB) {
                    logger.info("xdb does not support this operation.");
                    return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
                }
            }else{
                newCheckSwitch = "true";
                preCheckExecutor.execute(custins, replicaSetMeta, targetMajorVersion, requestId);
            }
            //检查代理版本是否符合要求
            String dbEngine = "MySQL";
            boolean isDhg = modifyInsParam.isDHG();
            boolean isArm = PodCommonSupport.isArm(modifyInsParam.getSrcInstanceLevel());
            String targetServiceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                    replicaSetMeta.getName(),
                    null,
                    modifyInsParam.getReplicaSetMeta().getBizType(),
                    modifyInsParam.getDbType(),
                    targetMajorVersion,
                    dbEngine,
                    KIND_CODE_NEW_ARCH,
                    modifyInsParam.getTargetInstanceLevel(),
                    modifyInsParam.getTargetDiskType(),
                    isDhg,
                    isArm,
                    null);
            logger.info("targetServiceSpecTag = " + targetServiceSpecTag);
            if (StringUtils.isEmpty(targetServiceSpecTag)) {
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }
            String targetReleaseDate = minorVersionServiceHelper.parseServiceSpecReleaseDate(targetServiceSpecTag);
            logger.info("target release date = " + targetReleaseDate);
            if (!mysqlMajorVersionCheckService.checkCanUpgradeMajorVersionWithMaxScale(custins, targetReleaseDate)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_MAXSCALE_MINOR_VERSION);
            }

            //获取升级至目标大版本后对应的service_spec_id
            int targetServiceSpecId = minorVersionServiceHelper.checkAndGetServiceSpecId(requestId, null, dbType,
                    targetMajorVersion, targetServiceSpecTag, replicaSetMeta.getCategory());

            //参数模版处理: 针对系统参数模版, 根据原参数模版获取目标大版本对应的参数模版; 用户参数模版直接舍弃
            String targetParamGroupId = null;
            Map<String, String> srcInsLabels = new HashMap<>(dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSetMeta.getName()));
            if (!MapUtils.isEmpty(srcInsLabels) && StringUtils.isNotEmpty(srcInsLabels.get(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID)) &&
                                                    StringUtils.isNotEmpty(srcInsLabels.get(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO))) {
                String srcParamGroupId = srcInsLabels.get(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
                if (StringUtils.startsWith(srcParamGroupId, ParamTransHelper.SYS_PARAM_GROUP_ID_PREFIX)) {
                    //系统参数模版
                    List<Map> paramGroupList = custinsParamGroupsService.getParamGroupsByParamGroupId(srcParamGroupId);
                    if (CollectionUtils.isNotEmpty(paramGroupList)) {
                        Map paramGroup = paramGroupList.get(0);
                        String srcParameterGroupName = (String) paramGroup.get("ParameterGroupName");
                        logger.info("src parameterGroupName = " + srcParameterGroupName);
                        // 替换成目标大版本对应的参数模版
                        String targetParameterGroupName = srcParameterGroupName.replace(dbVersion, targetMajorVersion);
                        logger.info("target parameterGroupName = " + targetParameterGroupName);
                        List<Map> targetParamGroupList = custinsParamGroupsService.getParamGroupsByName(targetParameterGroupName);
                        if (CollectionUtils.isEmpty(targetParamGroupList)) {
                            logger.info("target parameterGroupName = " + targetParameterGroupName + ", db_version = " + dbVersion + ", target_db_version = " + targetMajorVersion +", param_groups do not exist");
                        } else {
                            targetParamGroupId = (String) targetParamGroupList.get(0).get("ParamGroupId");
                        }
                    }
                }
            }
            logger.info("get target param_group_id = " + targetParamGroupId);

            //获取目标serviceSpecTag: 升级流程中临时实例先用原版本(5.7)的版本拉起，再原地升级至8.0
            String originalSpecTag = minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(UUID.randomUUID().toString(), replicaSetMeta.getName());
            modifyInsParam.setTargetComposeTag(originalSpecTag);

            logger.info("modifyInsParam = " + modifyInsParam);

            //查询任务记录 判断升级任务流是否已存在
            String taskKey = null;
            if(ReplicaSet.InsTypeEnum.MAIN.equals(replicaSetMeta.getInsType())) {
                if (InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                    taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_FOR_STANDARD;
                } else if (InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                    taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_FOR_BASIC;
                } else if (InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                    taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_FOR_CLUSTER;
                }
            }else if(ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())){
                if (InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                    taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_FOR_BASIC_READ;
                }else if(InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(replicaSetMeta.getCategory())){
                    taskKey = PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_FOR_STANDARD_READ;
                }
            }
            if(taskKey == null){
                return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
            }

            boolean taskExist = workFlowService.isTaskExist(requestId, replicaSetMeta.getName(), taskKey);
            if (taskExist) {
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }

            //下发任务
            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put(CustinsSupport.SWITCH_KEY, modifyInsParam.getSwitchInfo());
            taskParam.put("replicaSetName", replicaSetMeta.getName());
            taskParam.put("srcReplicaSetName", replicaSetMeta.getName());
            taskParam.put("targetMajorVersion", targetMajorVersion);
            taskParam.put("serviceSpecTag", targetServiceSpecTag);
            taskParam.put("serviceSpecId", targetServiceSpecId);
            //防止参数模版为空导致任务流报错
            if (StringUtils.isEmpty(targetParamGroupId)) {
                taskParam.put("targetParamGroupId", "empty");
            } else {
                taskParam.put("targetParamGroupId", targetParamGroupId);
            }
            String primaryReplicaSetName = mysqlParamSupport.getParameterValue(actionParams, PodDefaultConstants.PRIMARY_REPLICASET_NAME);
            if (primaryReplicaSetName != null) {
                taskParam.put(PodDefaultConstants.PRIMARY_REPLICASET_NAME, primaryReplicaSetName);
            }
            String backupSetId = mysqlParamSupport.getParameterValue(actionParams, PodDefaultConstants.BACKUPSET_ID);
            if (backupSetId != null) {
                taskParam.put(PodDefaultConstants.BACKUPSET_ID, backupSetId);
            }
            if (PodParameterHelper.isAliYun(replicaSetMeta.getBizType()) && ReplicaSet.InsTypeEnum.MAIN.equals(replicaSetMeta.getInsType())) {
                // Passthough switch time params into workflow for upgrading attached read ins
                // These params should NEVER be used directly in workflow
                taskParam.put("switch_time_mode", mysqlParamSupport.getParameterValue(actionParams, ParamConstants.SWITCH_TIME_MODE));
                taskParam.put("switch_time", mysqlParamSupport.getParameterValue(actionParams, ParamConstants.SWITCH_TIME));
            }
            taskParam.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
            taskParam.put("targetClassCode", modifyInsParam.getTargetClassCode());
            taskParam.put("destDiskType", modifyInsParam.getTargetDiskType());
            taskParam.put("destDiskSizeMB", modifyInsParam.getTargetDiskSizeGB() * 1024);
            taskParam.put("newCheckSwitch", newCheckSwitch);

            String domain = PodCommonSupport.getTaskDomain(replicaSetMeta.getBizType());
            Object taskId = workFlowService.dispatchTask("custins", replicaSetMeta.getName(), domain, taskKey, taskParam.toString(), 0);

            // 更新实例状态为版本升级中
            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetMeta.getName(), ReplicaSet.StatusEnum.VERSION_TRANSING.toString());

            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TargetMajorVersion", targetMajorVersion);
            data.put("TaskId", taskId);
            return data;

        }catch (RdsException re){
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        }catch (Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
