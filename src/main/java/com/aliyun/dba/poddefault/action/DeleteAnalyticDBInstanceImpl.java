package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceRelation;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteAnalyticDBInstanceImpl")
public class DeleteAnalyticDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DeleteAnalyticDBInstanceImpl.class);
    @Resource
    RdsApi rdsApi;

    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Autowired
    protected WorkFlowService workFlowService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String analyticInsName = paramSupport.getParameterValue(params, ParamConstants.ANALYTIC_DB_INSTANCE_NAME);
            ReplicaSet analyticIns = podCommonSupport.getCkReplicaSet(analyticInsName);

            logger.info("requestId : {}, request to DeleteAnalyticDBInstanceImpl", requestId);
            // 查询关联关系
            podCommonSupport.checkAnalyticDBInstanceRelation(requestId, custins.getInsName(), analyticInsName);
            // 释放与主实例的关联
            releaseServiceRelation(requestId, analyticInsName, custins.getInsName());

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("dbInstanceName", custins.getInsName());
            jsonObject.put("ckInstanceName", analyticInsName);


            String taskKey = PodDefaultConstants.TASK_CLUSTER_REMOVE_CLICKHOUSE_INSTANCE;
            String parameter = jsonObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;

            logger.info("requestId : {}, task parameter : {}", requestId, parameter);
            Object taskId = workFlowService.dispatchTask("custins", custins.getInsName(), domain, taskKey, parameter, 0);
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            logger.info("request : {}, result : {}", requestId, JSONObject.toJSONString(data));

            return data;
        } catch (RdsException ex) {
            log.error("DeleteAnalyticDBInstanceImpl failed: {}", JSONObject.toJSONString(ex));
            throw ex;
        } catch (Exception ex) {
            log.error("DeleteAnalyticDBInstanceImpl Exception: {}", JSONObject.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 释放与主实例的关联关系
     */
    private void releaseServiceRelation(String requestId, String ckInstanceName, String instanceName) throws Exception {
        ServiceRelation serviceRelation = dBaasMetaService.getDefaultClient().deleteReplicaSetService(requestId, instanceName, ckInstanceName);
        if (serviceRelation == null) {
            log.error("requetId : {}, release custins service failed.", requestId);
            throw new Exception("release custins service failed!");
        }
    }
}
