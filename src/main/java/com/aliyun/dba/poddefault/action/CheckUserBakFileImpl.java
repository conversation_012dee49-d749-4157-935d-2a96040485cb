package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.AliyunCreateDBInstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCheckUserBakFileImpl")
public class CheckUserBakFileImpl implements IAction {

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    private AliyunCreateDBInstanceService aliyunDBInstanceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        return aliyunDBInstanceService.createDBInstanceWithOss(params);
    }
}

