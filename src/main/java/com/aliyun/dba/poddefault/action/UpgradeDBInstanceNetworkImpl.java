package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultUpgradeDBInstanceNetworkImpl")
public class UpgradeDBInstanceNetworkImpl implements IAction {
    private static final LogAgent LOG_AGENT = LogFactory.getLogAgent(UpgradeDBInstanceNetworkImpl.class);


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 新架构暂不支持该操作
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

}