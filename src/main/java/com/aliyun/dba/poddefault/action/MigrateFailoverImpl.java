package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.RebuildReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultMigrateFailoverImpl")
public class MigrateFailoverImpl implements IAction {
    private static final LogAgent LOG_AGENT = LogFactory.getLogAgent(MigrateFailoverImpl.class);

    @Autowired
    private ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Resource
    private AliyunInstanceDependency dependency;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private MySQLServiceImpl mySQLService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    protected RundPodSupport rundPodSupport;
    private static final String CATEGORY_BASIC = "basic";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        ReplicaSet replicaSetMeta;
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        String isForce = mysqlParamSupport.getParameterValue(params, "isForce", "false");
        String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
        String dbType = custins.getDbType();
        String dbVersion = custins.getDbVersion();

        try {
            replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!(ReplicaSet.StatusEnum.ACTIVE.equals(replicaSetMeta.getStatus()) ||
                    ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSetMeta.getStatus()))) {
                throw new RdsException(ErrorCode.INVALID_STATUS);
            }

            String classCode = replicaSetMeta.getClassCode();
            String storageType = replicaSetService.getReplicaSetStorageType(dbInstanceName, requestId);

            boolean isSingleNode = isSingleNode(requestId, dbType, classCode, dbVersion);
            if (!isSingleNode) {
                throw new RdsException(ErrorCode.INVALID_EXCEPTION_LEVEL);
            }

            boolean isSingleTenant = isSingleTenant(replicaSetMeta);
            // 如果是单租户的话 就需要去下restart任务
            String domain = PodDefaultConstants.DOMAIN_MYSQL;

            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", requestId);
            taskParamObject.put("replicaSetName", dbInstanceName);
            Map<String, Object> data = new HashMap<>();

            if (CATEGORY_BASIC.equals(replicaSetMeta.getCategory()) && ReplicaSet.InsTypeEnum.READONLY == replicaSetMeta.getInsType()) {
                // 设置切换时间点
                Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);
                taskParamObject.put(CustinsSupport.SWITCH_KEY, switchInfoMap);

                LOG_AGENT.info("requestId:{} process basic read ins:{} migrate", requestId, dbInstanceName);
                Object taskId = migrateForBasicRead(params, taskParamObject);
                data.put("TaskId", taskId);
                return data;
            }

            if (isSingleTenant) {
                LOG_AGENT.info("requestId:{} dbInstanceName:{} is single tenant, ready to restart instance", requestId, dbInstanceName);
                Object taskId = workFlowService.dispatchTask(
                        "custins", dbInstanceName, domain, PodDefaultConstants.TASK_RESTART_INS_WITH_FAILOVER, taskParamObject.toJSONString(), 0);
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, dbInstanceName, ReplicaSet.StatusEnum.RESTARTING.toString());
                data.put("TaskId", taskId);
                return data;
            }

            if (StringUtils.equalsIgnoreCase(isForce, "false") &&
                    CATEGORY_BASIC.equals(replicaSetMeta.getCategory()) && ReplicaSet.InsTypeEnum.MAIN == replicaSetMeta.getInsType()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            //获取用户信息
            User user = dependency.getDBaasMetaService().getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String bid = user.getBid();
            String uid = user.getAliUid();

            List<String> modifyReplicas = new ArrayList<>();

            //检验临时实例
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().
                    listReplicasInReplicaSet(requestId, dbInstanceName, null, null, null, null);
            String tmpReplicaSetName = getTmpReplicaSetName(requestId, dbInstanceName);
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            assert currentReplicas != null;
            PodModifyInsParam.replicaNameValidate(currentReplicas, tmpReplicaSetName);
            PodType targetRuntimeType = mysqlParamSupport.getTargetRuntimeType(replicaSetMeta, params);

            InstanceLevel instanceLevel = dependency.getDBaasMetaService().getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, false);
            ScheduleTemplate scheduleTemplate;
            if (PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
                scheduleTemplate = podTemplateHelper.getBizSysScheduleTemplate(
                        targetRuntimeType,
                        replicaSetMeta.getBizType(),
                        replicaSetMeta.getService(),
                        instanceLevel,
                        isSingleTenant,
                        replicaSetMeta.getInsType().getValue(),
                        replicaSetMeta.getName(),
                        replicaSetMeta.getPrimaryInsName(), podParameterHelper.getUidByLoginId(replicaSetMeta.getUserId())
                ).getValue();
            } else {
                scheduleTemplate = podTemplateHelper.getReplicaSetScheduleTemplate(podTemplateHelper.getReplicaSetPodScheduleTemplate(replicaSetMeta));
            }
            ModifyReplicaSetResourceRequest replicaSetResourceRequest = new ModifyReplicaSetResourceRequest();
            List<ModifyReplicaResourceRequest> replicaRequestList = buildModifyReplicaResourceRequests(currentReplicas, modifyReplicas, replicaSetMeta.getClassCode(), scheduleTemplate, targetRuntimeType);
            Integer transListId;
            replicaSetResourceRequest.setUserId(bid);
            replicaSetResourceRequest.setUid(uid);
            replicaSetResourceRequest.setTmpReplicaSetName(tmpReplicaSetName);
            replicaSetResourceRequest.setDbType(dbType);
            replicaSetResourceRequest.setDbVersion(dbVersion);
            replicaSetResourceRequest.setClassCode(classCode);
            replicaSetResourceRequest.setStorageType(storageType);
            replicaSetResourceRequest.setDiskSize(custins.getDiskSize().intValue() / 1024);
            replicaSetResourceRequest.setReplicaResourceRequestList(replicaRequestList);
            replicaSetResourceRequest.setIgnoreCreateVpcMapping(true);
            replicaSetResourceRequest.setModifyMode(ModifyReplicaSetResourceRequest.ModifyModeEnum.MIGRATE);
            replicaSetResourceRequest.setScheduleTemplate(scheduleTemplate);
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(targetRuntimeType.getRuntimeType())) {
                rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            }

            LOG_AGENT.info("dbinstancename:{} replicaSetResourceRequest:{}", dbInstanceName, JSONObject.toJSONString(replicaSetResourceRequest));
            transListId = commonProviderService.getDefaultApi().allocateReplicaSetResourceForScale(requestId, dbInstanceName, replicaSetResourceRequest);

            // dispatch task

            taskParamObject.put("transTaskId", transListId);

            if (!modifyReplicas.isEmpty()) {
                taskParamObject.put("modifyReplicas", StringUtils.join(modifyReplicas, ","));
            }

            String taskParam = taskParamObject.toJSONString();
            String taskKey = PodDefaultConstants.TASK_MIGRATE_FAIL_OVER;
            if (InstanceLevel.CategoryEnum.SERVERLESS_BASIC.equals(instanceLevel.getCategory())) {
                taskKey = ServerlessConstant.TASK_MIGRATE_SERVERLESS_FAIL_OVER;
            }
            Object taskId = workFlowService.dispatchTask(
                    "custins", dbInstanceName, domain, taskKey, taskParam, 0);
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, dbInstanceName, ReplicaSet.StatusEnum.TRANSING.toString());
            // build response
            data.put("TaskId", taskId);
            return data;
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            LOG_AGENT.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (RdsException e) {
            return createErrorResponse(e.getErrorCode());
        } catch (Exception ex) {
            LOG_AGENT.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }

    private String getTmpReplicaSetName(String requestId, String dbInstanceName) {
        int maxLength = 38;
        String tmpReplicaSetNamePrefix = requestId;
        if (requestId.length() > maxLength - dbInstanceName.length()) {
            tmpReplicaSetNamePrefix = tmpReplicaSetNamePrefix.substring(0, maxLength - dbInstanceName.length());
        }
        return String.format("%s-%s", tmpReplicaSetNamePrefix.toLowerCase(), dbInstanceName);
    }

    private List<ModifyReplicaResourceRequest> buildModifyReplicaResourceRequests(List<Replica> currentReplicas, List<String> modifyReplicas, String classCode, ScheduleTemplate scheduleTemplate, PodType targetRuntimeType) throws Exception {
        List<ModifyReplicaResourceRequest> replicaRequestList = new ArrayList<>();

        for (Replica replica : currentReplicas) {
            ModifyReplicaResourceRequest replicaRequest = new ModifyReplicaResourceRequest();
            replicaRequest.setClassCode(classCode);
            replicaRequest.setRole("master");
            replicaRequest.setStorageType(Objects.requireNonNull(replica.getStorageType()).toString());
            replicaRequest.setZoneId(replica.getZoneId());
            replicaRequest.setDiskSize(replica.getDiskSizeMB() / 1024);
            replicaRequest.setScheduleTemplate(scheduleTemplate);
            // 设置rund 网络相关配置
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(targetRuntimeType.getRuntimeType())) {
                rundPodSupport.completeReplicaNetworkConfig(replicaRequest, replica);
            }
            modifyReplicas.add(Objects.requireNonNull(replica.getId()).toString());
            replicaRequestList.add(replicaRequest);
        }
        return replicaRequestList;
    }

    private boolean isSingleNode(String requestId, String dbType, String classCode, String dbVersion) throws ApiException {
        InstanceLevel srcInstanceLevel = dependency.getDBaasMetaService().getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
        if (srcInstanceLevel == null) {
            return false;
        }
        return srcInstanceLevel.getCategory() == InstanceLevel.CategoryEnum.BASIC || srcInstanceLevel.getCategory() == InstanceLevel.CategoryEnum.SERVERLESS_BASIC ||
                (srcInstanceLevel.getCategory() == InstanceLevel.CategoryEnum.ENTERPRISE &&
                        srcInstanceLevel.getInsCount() != null && srcInstanceLevel.getInsCount() == 1);
    }

    private boolean isSingleTenant(ReplicaSet replicaSet) {
        return podParameterHelper.isSingleTenant(replicaSet);
    }


    private Object migrateForBasicRead(Map<String, String> params, JSONObject taskParam) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        RebuildReplicaResourceRequest allocateReplicaResource = null;
        boolean isSuccess = false;
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);

            if (workFlowService.isTaskExist(requestId, replicaSet.getName(), PodDefaultConstants.TASK_MIGRATE_FAIL_OVER_FOR_BASIC_READ)) {
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }

            // 申请资源
            allocateReplicaResource = podReplicaSetResourceHelper.allocateRebuildResource4Basic(replicaSet, PodType.POD_RUNC);

            allocateReplicaResource.setCategory(podParameterHelper.getCategory(requestId, replicaSet));
            Replica replicaForUpgrade = mySQLService.getReplicaByRole(requestId, replicaSet.getName(), Replica.RoleEnum.MASTER);
            commonProviderService.getDefaultApi().allocateReplicaSetResourceForRebuild(
                    requestId, replicaSet.getName(), replicaForUpgrade.getId(), allocateReplicaResource);
            CustInstanceDO custInstanceDO = custinsService.getCustInstanceByInsName(
                    null, allocateReplicaResource.getTmpReplicaSetName(), CUSTINS_INSTYPE_TMP);
            if (custInstanceDO == null) {
                LOG_AGENT.error("Cannot find tmp custins [{}]", allocateReplicaResource.getTmpReplicaSetName());
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            custInstanceDO.setInsType(CUSTINS_INSTYPE_MIRROR);
            custInstanceDO.setGmtModified(new Date());
            custinsService.updateCustInstance(custInstanceDO);
            // 只读实例配置白名单同步label
            podParameterHelper.setReadInsSgLabel(requestId, replicaSet, allocateReplicaResource.getTmpReplicaSetName());

            // 下发任务 & 更新状态
            taskParam.put("srcReplicaSetName", replicaSet.getName());
            taskParam.put("destReplicaSetName", allocateReplicaResource.getTmpReplicaSetName());
            taskParam.put("srcReplicaId", replicaForUpgrade.getId());
            taskParam.put("destReplicaId", mySQLService.getReplicaByRole(
                    requestId, allocateReplicaResource.getTmpReplicaSetName(), Replica.RoleEnum.MASTER).getId());

            String taskKey = PodDefaultConstants.TASK_MIGRATE_FAIL_OVER_FOR_BASIC_READ;
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSet.getName(), "mysql", taskKey, taskParam.toString(), 0);
            isSuccess = true;
            return taskId;
        } catch (Exception e) {
            LOG_AGENT.error(requestId + " Exception: ", e);
            throw e;
        } finally {
            // 处理失败时释放资源
            if (null != allocateReplicaResource && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, allocateReplicaResource.getTmpReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    LOG_AGENT.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }
}
