package com.aliyun.dba.poddefault.action;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultActivateExternalReplicationImpl")
public class ActivateExternalReplicationImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ActivateExternalReplicationImpl.class);
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected CustinsService custinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            replicaSetService.preCheckForExternalReplication(replicaSet, requestId, PodDefaultConstants.ExternalReplicationScenario.activate);
            // 更新状态为维护中
            custinsService.updateCustInstanceStatusByCustinsId(
                    replicaSet.getId().intValue(), CustinsSupport.CUSTINS_STATUS_TRANS,
                    CustinsState.STATE_MAINTAINING.getComment());
            // 下activate任务
            JSONObject jsonObject = new JSONObject();
            Object taskId = workFlowService.dispatchTask(replicaSet, PodDefaultConstants.TASK_ACTIVATE_EXTERNAL_REPLICATION,
                    jsonObject.toJSONString(), WorkFlowService.TASK_PRIORITY_COMMON);
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("RequestId", requestId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            logger.error(" RequestId: {}, Exception: ", requestId, e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}