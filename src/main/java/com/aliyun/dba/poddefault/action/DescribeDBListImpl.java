package com.aliyun.dba.poddefault.action;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeDBListImpl")
public class DescribeDBListImpl implements IAction {
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private DbossApi dbossApi;
    @Resource
    private MycnfService mycnfService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private DbsService dbsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map) throws RdsException {
        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
            custInstanceDO = mysqlParameterHelper.getAndCheckCustInstance();
            if (custInstanceDO.isReadAndWriteLock()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            if (custInstanceDO.isDiskFullLock() && ((custInstanceDO.isCustinsDockerOnEcs() || custInstanceDO.isCustinsOnEcs()) || custInstanceDO.isReadOrBackup())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            if (!custInstanceDO.inAvailableStatus()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (dbossApi.isHandleByDBoss(custInstanceDO)) {
                return getDBListFromDboss(custInstanceDO);
            }

            return getDBsWithoutDBoss();
        } catch (RdsException re) {
            log.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }

    private Map<String, Object> getDBListFromDboss(CustInstanceDO custInstanceDO) throws IOException, RdsException {
        List<Map<String, Object>> DBs = new ArrayList<>();
        int pageNum = Integer.parseInt(mysqlParameterHelper.getParameterValue(ParamConstants.PAGE_NUMBERS, "1"));
        int pageSize = Integer.parseInt(mysqlParameterHelper.getParameterValue(ParamConstants.MAX_RECORDS_PER_PAGE, "500"));
        boolean queryProtectedDB = Boolean.parseBoolean(mysqlParameterHelper.getParameterValue("QueryProtectedDB", "false"));
        MycnfCustinstanceDO innerSchemaList = mycnfService.getMycnfCustinstance(custInstanceDO.getId(), RdsConstants.MYCNF_CUSTINS_KEY_INNER_SCHEMA_LIST);
        Set<String> protectedDBSet = new HashSet<>();
        if (null != innerSchemaList) {
            String[] innerDBNameList = StringUtils.split(innerSchemaList.getParaValue(), ",");
            protectedDBSet.addAll(Arrays.asList(innerDBNameList));
        }
        String dbNameIn = mysqlParameterHelper.getParameterValue("DBName");
        List<Map<String, Object>> DBList = dbossApi.queryDBs(custInstanceDO.getId(), dbNameIn, (pageNum - 1) * pageSize, pageSize);
        Map<String, Object> dbCountMap = dbossApi.queryDBCount(custInstanceDO.getId());
        log.info("getDBListFromDboss: dbCountMap=" + JSON.toJSONString(dbCountMap));
        Integer count = MapUtils.getInteger(dbCountMap, "totalCount", 0);
        if (StringUtils.isNotEmpty(dbNameIn)) {
            count = DBList.size();
        }
        for (Map<String, Object> db : DBList) {
            Object dbName = db.remove("dbname");
            if ((queryProtectedDB && !protectedDBSet.contains(dbName)) || (!queryProtectedDB && protectedDBSet.contains(dbName))) {
                continue;
            }
            db.put("DBName", dbName);
            db.put("DBID", 0);
            db.put("DBInstanceName", custInstanceDO.getInsName());
            db.put("DBInstanceID", custInstanceDO.getId());
            db.put("Engine", CustinsSupport.getEngine(custInstanceDO.getDbType()));
            db.put("DBStatus", db.remove("status"));
            db.put("CharacterSetName", db.remove("charset"));
            String comment = (String) db.remove("comment");
            db.put("DBDescription", comment != null ? comment : "");
            List<Map<String, Object>> privileges = (List<Map<String, Object>>) db.remove("privileges");
            if (!CollectionUtils.isEmpty(privileges)) {
                for (Map<String, Object> priv : privileges) {
                    priv.put("AccountName", priv.remove("accountName"));
                    priv.put("AccountPrivilege", CheckUtils.getPrivilgeDesc((String) priv.remove("privileges"), custInstanceDO.getDbType()));
                    priv.put("AccountStatus", DbsSupport.STATUS_ACTIVE);
                    priv.put("PrivilegeStatus", DbsSupport.STATUS_ACTIVE);
                }
            }

            db.put("Accounts", privileges);
            db.put("TotalCount", count);
            DBs.add(db);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("ResultTypeKeyName", "NonDataFormat"); // 设置用于兼容不合理的返回数据结构，不能删
        data.put("DBs", DBs);
        return data;
    }

    private Map<String, Object> getDBsWithoutDBoss() throws RdsException {
        Map<String, Object> condition = new HashMap<String, Object>();
        // userId 和 实例名 至少有一个
        Integer userId = mysqlParameterHelper.getAndCheckUserId();
        int pageNum = mysqlParameterHelper.getAndCheckPageNo();
        int pageSize = mysqlParameterHelper.getAndCheckPageSize();

        if ("internal_system".equals(mysqlParameterHelper.getBID()) && !mysqlParameterHelper.hasParameter("dbinstancename")) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCENAME);
        } else {
            condition.put("userId", userId);
        }

        String[] insNames = SupportUtils.splitToArray(mysqlParameterHelper.getDBInstanceName(), ",");
        if (insNames.length != 0) {
            // 此处传了user_id 对于超级账户实际是起不到作用的，根本查不到数据
            if (!custinsService
                    .checkCustInstanceByInsNames(userId, insNames, null, null, null)) {
                throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
            }
            condition.put("insNames", insNames);
        }
        String dbType = mysqlParameterHelper.getAndChangeEngine();
        if (dbType != null) {
            condition.put("dbType", dbType);
        }
        String dbname = mysqlParameterHelper.getDBName();
        if (dbname != null) {
            condition.put("dbname", dbname);
        }
        String dbstatus = mysqlParameterHelper.getParameterValue("DBStatus");
        if (dbstatus != null) {
            condition.put("status", CustinsValidator.getRealNumber(dbstatus));
        }
        condition.put("offset", (pageNum -1) * pageSize);
        condition.put("limit", pageSize);

        List<Map<String, Object>> dbsMapList = dbsService.getDbsMapByCondition(condition);
        Integer count = dbsService.getDbsCountByCondition(condition);
        log.info("getDBsWithoutDBoss: count=" + count);
        if (dbsMapList.size() > 0) {//大于0时才去查询账户
            Map<Integer, Map<String, Object>> dbIdsMap = new HashMap<Integer, Map<String, Object>>(
                    dbsMapList.size());
            for (Map<String, Object> dbMap : dbsMapList) {
                // Engine 转换
                dbMap.put("Engine", CustinsSupport.getEngine((String) dbMap.get("Engine")));
                dbMap.put("Accounts", new ArrayList<Map<String, Object>>());
                if (!CustinsSupport.DB_ENGINE_MSSQL.equals((String) dbMap.get("Engine"))) {
                    dbMap.remove("TDEStatus");
                }
                dbMap.put("TotalCount", count);
                dbIdsMap.put((Integer) dbMap.get("DBID"), dbMap);
            }

            List<Map<String, Object>> accountMapList = dbsService.getAccountListByDbIds(
                    new ArrayList<Integer>(dbIdsMap.keySet()));
            for (Map<String, Object> accountMap : accountMapList) {
                // account privilege 转换
                accountMap.put("AccountPrivilege", DbsSupport.getAccountPrivilege(
                        (Integer) accountMap.get("AccountPrivilege")));
                Map<String, Object> dbMap = dbIdsMap.get((Integer) accountMap.get("DBID"));
                accountMap.remove("DBID");
                List<Map<String, Object>> accounts = (List<Map<String, Object>>) dbMap.get("Accounts");
                accounts.add(accountMap);
            }
        }

        Map<String, Object> data = new HashMap<>();
        data.put("DBs", dbsMapList);
        return data;
    }
}
