package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstance;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateGlobalDatabaseNetworkImpl")
public class CreateGlobalDatabaseNetworkImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.CreateDBInstanceImpl.class);
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private GdnInstanceService gdnInstanceService;
    @Resource
    private DBaasMetaService metaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String bid = paramSupport.getAndCheckBID(params);
            String uid = paramSupport.getUID(params);
            String primaryInstanceName = paramSupport.getParameterValue(params, "PrimaryInstanceName");
            String primaryInstanceRegion = paramSupport.getParameterValue(params, "PrimaryInstanceRegion");
            String desc = paramSupport.getParameterValue(params, "Description", "");

            ReplicaSetResource replicaSetResource = metaService.getRegionClient(primaryInstanceRegion).getReplicaSetBundleResource(requestId, primaryInstanceName);
            if (null == replicaSetResource) {
                return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }

            ReplicaSet primaryReplicaSet = replicaSetResource.getReplicaSet();
            String dbType = primaryReplicaSet.getService();
            String dbVersion = primaryReplicaSet.getServiceVersion();
            ReplicaSet.ConnTypeEnum connType = primaryReplicaSet.getConnType();
            String gdnInstanceName;
            if (ReplicaSet.ConnTypeEnum.TDDL == connType) {
                gdnInstanceName = primaryReplicaSet.getLabels().get("clusterName");
            } else {
                gdnInstanceName = "gdn" + primaryInstanceName;
            }

            // 创建 GDN 实例以及主实例的 Member
            GdnInstance gdnInstance = gdnInstanceService.getGdnInstance(requestId, gdnInstanceName);
            if (null == gdnInstance) {
                gdnInstance = gdnInstanceService.createGdnInstance(
                        requestId, gdnInstanceName, bid, uid, dbType, dbVersion, GdnInstance.BizTypeEnum.ALIYUN, desc);
            }
            InstanceMember instanceMember = new InstanceMember();
            instanceMember.setInsName(gdnInstanceName);
            instanceMember.setService(dbType);
            instanceMember.setServiceVersion(dbVersion);
            instanceMember.setMemberName(primaryInstanceName);
            instanceMember.setMemberGroupId(primaryInstanceName);
            instanceMember.setMemberKind(InstanceMember.MemberKindEnum.REPLICASET);
            instanceMember.setMemberRegion(primaryInstanceRegion);
            instanceMember.setRole(ReplicaSet.InsTypeEnum.MAIN.toString());
            instanceMember.setStatus(ReplicaSet.StatusEnum.ACTIVATION.toString());
            gdnInstanceService.createGdnInstanceMember(requestId, gdnInstanceName, instanceMember);

            Map<String, Object> data = new HashMap<>();
            data.put("GdnInstanceName", gdnInstanceName);
            data.put("GdnInstanceId", gdnInstance.getId());
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.gdnmetaapi.ApiException e) {
            logger.error(requestId + " Gdn meta error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestGdnMetaFailed", "Request gdn meta api failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
