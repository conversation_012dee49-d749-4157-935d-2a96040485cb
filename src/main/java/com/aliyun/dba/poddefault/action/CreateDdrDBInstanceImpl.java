package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.backup.LockRestoreFileParam;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateDdrDBInstanceImpl")
@Slf4j
public class CreateDdrDBInstanceImpl implements IAction {

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private DbsGateWayService dbsGateWayService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        try {
            // 校验并更新恢复参数
            CheckCreateDdrDBInstanceImpl checkCreateDdrDBInstance = SpringContextUtil.getBeanByClass(CheckCreateDdrDBInstanceImpl.class);
            checkCreateDdrDBInstance.doCheckAndUpdateDdrRestoreParams(actionParams);

            // 恢复实例
            CreateDBInstanceImpl createDBInstance = SpringContextUtil.getBeanByClass(CreateDBInstanceImpl.class);
            Map<String, Object> data = createDBInstance.doActionRequest(custins, actionParams);

            if (data.getOrDefault("errorCode", null) != null) {
                log.error("create ddr instance failed, err: {}", JSONObject.toJSONString(data));
                return data;
            }

            // 创建成功，锁定备份集，且忽略异常
            log.info("create ddr instance success, data: {}", JSONObject.toJSONString(data));
            try {
                String taskId = data.get("TaskId").toString().split("\\.")[0];
                lockRestoreFiles(actionParams, taskId);
            } catch (Exception e) {
                log.error("lock restore files failed, msg: {}", e.getMessage(), e);
            }

            return data;
        } catch (RdsException re) {
            log.error("create ddr failed: {}", re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error("create ddr failed: {}", ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public void lockRestoreFiles(Map<String, String> params, String taskId) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String restoreType = mysqlParamSupport.getAndCheckRestoreType(params);
        String bid = mysqlParamSupport.getAndCheckBID(params);
        String uid = mysqlParamSupport.getAndCheckUID(params);
        String sourceDBInstanceName = mysqlParamSupport.getSourceDBInstanceName(params);
        String sourceRegionId = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_SOURCE_REGION);
        String backupSetId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
        String backupSetRegionId = getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION);

        // 锁定备份集
        dbsGateWayService.lockRestoreBackupSet(
                LockRestoreFileParam.builder()
                        .requestId(requestId)
                        .callerBid(bid)
                        .userId(uid)
                        .instanceName(sourceDBInstanceName)
                        .instanceRegion(sourceRegionId)
                        .backupId(backupSetId)
                        .regionCode(backupSetRegionId)
                        .sceneType(PodDefaultConstants.BACK_SCENE_TYPE_REPLICATION)
                        .lockType(PodDefaultConstants.PARAM_LOCK_TYPE_LOCK)
                        .sourcePlatform(PodDefaultConstants.APP_NAME)
                        .sourceWorkflowId(taskId)
                        .build());

        // 锁定日志集
        if (!RESTORE_TYPE_TIME.equals(restoreType)) {
            return;
        }
        String restoreTimePoint = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_RESTORE_TIME_POINT);
        String consistentTime = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.PARAM_CONSISTENT_TIME);

        dbsGateWayService.lockRestoreArchiveLog(
                LockRestoreFileParam.builder()
                        .requestId(requestId)
                        .callerBid(bid)
                        .userId(uid)
                        .instanceName(sourceDBInstanceName)
                        .instanceRegion(sourceRegionId)
                        .restoreTimePoint(restoreTimePoint)
                        .consistentTime(consistentTime)
                        .regionCode(backupSetRegionId)
                        .sceneType(PodDefaultConstants.BACK_SCENE_TYPE_REPLICATION)
                        .lockType(PodDefaultConstants.PARAM_LOCK_TYPE_LOCK)
                        .sourcePlatform(PodDefaultConstants.APP_NAME)
                        .sourceWorkflowId(taskId)
                        .build());
    }
}
