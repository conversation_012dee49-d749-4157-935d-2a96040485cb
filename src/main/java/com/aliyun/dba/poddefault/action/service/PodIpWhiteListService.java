package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.IPWhiteList;
import com.aliyun.apsaradb.dbaasmetaapi.model.IPWhiteListsResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> on 2020/6/18.
 */
@Service
public class PodIpWhiteListService {

    @Resource
    private DBaasMetaService dBaasMetaService;

    /**
     * 实例间同步IP白名单
     *
     * @param srcReplicaSetName
     * @param destReplicaSetName
     */
    public void syncReplicaSetIpWhiteList(String requestId, String srcReplicaSetName, String destReplicaSetName) throws ApiException {
        IPWhiteListsResult ipWhiteListsResult = dBaasMetaService.getDefaultClient().listReplicaSetWhiteIps(requestId, srcReplicaSetName);
        if (CollectionUtils.isEmpty(ipWhiteListsResult.getItems())) {
            return;
        }
        //写入白名单数据
        for (IPWhiteList item : ipWhiteListsResult.getItems()) {
            dBaasMetaService.getDefaultClient().createReplicaSetWhiteIps(requestId, destReplicaSetName, item);
        }
    }

}
