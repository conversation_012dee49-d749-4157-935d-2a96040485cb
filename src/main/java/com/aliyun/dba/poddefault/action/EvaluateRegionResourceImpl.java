package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.BackupSetParam;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.service.SlrCheckService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.DuckDBService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.poddefault.action.support.modules.MysqlReplicaResourceRequest;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.base.service.SlrCheckService.SERVICE_LINKED_ROLE_MYSQL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ParamConstants.*;


/**
 * 新购资源评估接口
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultEvaluateRegionResourceImpl")
public class EvaluateRegionResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateRegionResourceImpl.class);

    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private PodAvzSupport avzSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private MySQLAvzService mySQLAvzService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private AligroupService aligroupService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private MysqlEncryptionService mysqlEncryptionService;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private KmsApi kmsApi;

    @Resource
    private BakService bakService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    private BackupService backupService;
    @Autowired
    private DbsGateWayService dbsGateWayService;

    @Resource
    private SlrCheckService slrCheckService;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    private CrossArchService crossArchService;
    @Resource
    private ClusterService clusterService;
    @Autowired
    private DuckDBService duckDBService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            // 用于Quota评估使用，补充部分Quota评估需要的业务参数
            Boolean isQuotaEvaluate = mysqlParamSupport.getAndFillQuotaEvaluate(params);

            // 获取基本请求参数
            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(params);
            Integer diskSize = CheckUtils.parseInt(
                    mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE),
                    CustinsSupport.ESSD_MIN_DISK_SIZE, 102400, ErrorCode.INVALID_STORAGE);
            String bid = mysqlParamSupport.getBID(params);
            String uid = mysqlParamSupport.getUID(params);
            String dbType = mysqlParamSupport.getAndCheckDBType(params, null);
            String dbVersion = mysqlParamSupport.getAndCheckDBVersion(params, dbType, true);
            String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            String primaryDBInstanceName = mysqlParamSupport.getPrimaryDBInstanceName(params);
            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, true);
            //备份恢复拦截逻辑
            String restoreType = mysqlParamSupport.getParameterValue(params, RESTORE_TYPE);
            if(restoreType !=null) {
                String sourceReplicaSetName = mysqlParamSupport.getAndCheckSourceDBInstanceName(params);
                ReplicaSet sourceReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, sourceReplicaSetName, true);
                //查不到source，可能是异地备份恢复逻辑，跳过拦截
                String regionId = mysqlParamSupport.getParameterValue(params, REGION_ID);
                if (!isCrossRegionRestore(sourceReplicaSet, regionId)) {
                    BakhistoryDO bakHistory = null;
                    CustInstanceDO srcCustins = mysqlParameterHelper.getAndCheckSourceCustInstance();
                    //按时间点恢复，不允许备份版本和源实例版本不一致
                    if (RESTORE_TYPE_TIME.equals(restoreType)) {
                        Date restoreTimeUTC;
                        if(!Objects.equals(srcCustins.getKindCode(), KIND_CODE_NEW_ARCH)){
                            restoreTimeUTC = crossArchService.validRestoreByTime(srcCustins);
                            bakHistory = mysqlParameterHelper.validCloneStorageForEcs(srcCustins, diskSize * 1024L, restoreType, restoreTimeUTC);
                        }else {
                            restoreTimeUTC = podParameterHelper.getAndCheckRestoreTime(requestId, srcCustins);
                            bakHistory = podParameterHelper.getBakhistoryByRecoverTime(srcCustins.getId(), restoreTimeUTC);
                        }
                        if(bakHistory == null || !bakHistory.getDbVersion().equals(sourceReplicaSet.getServiceVersion())){
                            throw new RdsException(ErrorCode.INCORRECT_DBINSTANCE_ENGINE);
                        }
                        //按备份集恢复，允许恢复到不同版本的实例
                    } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                        String bakupSetId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
                        //判断异地备份
                        if(!bakupSetId.startsWith("rp-")){
                            Long bakId = CheckUtils.parseLong(bakupSetId, null,
                                    null, ErrorCode.BACKUPSET_NOT_FOUND);
                            bakHistory = bakService.getBakhistoryByBackupSetId(sourceReplicaSet.getId().intValue(), bakId);
                            if(bakHistory == null){
                                throw new RdsException(ErrorCode.INVALID_BACKUPSET);
                            }
                        }
                    }
                }
            }

            if (instanceLevel == null) {
                throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
            }
            boolean isBasic = instanceLevel.getCategory() == InstanceLevel.CategoryEnum.BASIC;
            //这里是原来的逻辑
//          //String diskType = podParameterHelper.getDiskType(instanceLevel)
            String accessId = getParameterValue(params, ParamConstants.ACCESSID);
            String clusterName = mysqlParamSupport.getParameterValue(params, ParamConstants.CLUSTER_NAME);
            Boolean isDhg = mysqlParamSupport.isDHGCluster(clusterName);
            boolean isArm = PodCommonSupport.isArm(instanceLevel);
            String dbEngine = mysqlParamSupport.isMysqlXDBByParams(params) ? "XDB" : dbType;
            boolean isReadIns = dbInstanceName != null && dbInstanceName.startsWith("rr-") || !Objects.isNull(primaryDBInstanceName);
            if (isReadIns){
                podParameterHelper.resetDispenseModeForReadOnly(params, instanceLevel, dbEngine);
            } else if (!isBasic){
                podParameterHelper.resetDispenseMode(params, instanceLevel, dbEngine, isReadIns);
            }

            if (isReadIns) {
                ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, primaryDBInstanceName, true);
                if (primaryReplicaSet != null) {
                    mysqlEncryptionService.ensureTdeEncryptionKeyValid(requestId, primaryReplicaSet);
                    mysqlEncryptionService.ensureClsEncryptionKeyValid(requestId, primaryReplicaSet);
                }
            }

            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            String encryptionKey = mysqlParamSupport.getEncryptionKey(params);
            String roleArn = mysqlParamSupport.getRoleArn(params);
            if(encryptionKey!=null && roleArn!=null){
                DescribeKeyResponse describeKeyResponse = kmsApi.describeKeyByRegionId(avzInfo.getRegionId(), encryptionKey, roleArn, uid);
                if(describeKeyResponse==null || describeKeyResponse.getKeyMetadata()==null){
                    throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
                }
                String keyState = describeKeyResponse.getKeyMetadata().getKeyState();
                if(keyState.equalsIgnoreCase("Disabled")){
                    throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
                }
                String keyType = describeKeyResponse.getKeyMetadata().getKeySpec();
                //云盘加密只支持对称加密
                if(!keyType.equals(KMS_KEY_TYPE_ALIYN_AES_256) && !keyType.equals(KMS_KEY_TYPE_ALIYN_SM4)){
                    throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
                }
            }

            // 回收站重建以及已删除实例备份检查TDE&CLS状态
            evaluateTdeAndClsEncryptionKeyStatus(params, avzInfo.getRegionId());

            Replica.RoleEnum[] nodeRoles = PodCommonSupport.getRoles(dbEngine, instanceLevel, false, avzInfo);

            String zoneId = CustinsParamSupport.getParameterValue(params, ParamConstants.ZONE_ID);
            if (!isBasic && avzSupport.isMultiZone(zoneId)){
                logger.error("zoneId  {} ,k8s not support multi zone cluster", zoneId);
                return createErrorResponse(ErrorCode.INVALID_MULTIPARAM_ZONEINFO_LIST);
            }

            String diskType = podParameterHelper.getDiskType(instanceLevel, zoneId);

            String performanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(diskType);
            diskType = PodParameterHelper.transferDiskTypeParam(diskType);

            if (Replica.StorageTypeEnum.LOCAL_SSD.toString().equals(diskType) &&
                    DockerOnEcsConstants.HOST_TYPE_ECS.equals(instanceLevel.getHostType())) {
                throw new RdsException(new Object[]{400, "InvalidInstanceLevel.DiskType", "Specified instance level not support request disk type"});
            }
            // 检查云盘容量下限约束，避免退单
            if(Objects.nonNull(diskSize)) {
                PodParameterHelper.checkCloudEssdStorageValidByDiskTypeAndPLEVEL(diskType, performanceLevel, diskSize);
            }

            String insTypeDesc = isReadIns? ReplicaSet.InsTypeEnum.READONLY.toString() : ReplicaSet.InsTypeEnum.MAIN.toString();

            ReplicaSet.BizTypeEnum bizType = podParameterHelper.getBizType(requestId, avzInfo.getRegionId());
            if (aligroupService.isAligroupDHG(avzInfo.getRegionId(), clusterName)) {
                bizType = ReplicaSet.BizTypeEnum.ALIGROUP;
            }
            String optimizedWrites = null;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.OPTIMIZED_WRITES)) {
                optimizedWrites = mysqlParamSupport.getParameterValue(params, ParamConstants.OPTIMIZED_WRITES, null);
                if (StringUtils.isBlank(optimizedWrites) || (!PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites) && !PodDefaultConstants.optimizedWritesTypeEnum.none.name().equals(optimizedWrites))) {
                    return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
                }
                if (PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites)) {
                    podCommonSupport.checkOptimizedWritesCondition(requestId, dbVersion, diskType);
                }
            }

            //检查实例指定版本创建，单写/多写的release_date的8位数字一定不同，与感仰沟通，由内核发布保证
            String specifyReleaseDate = mysqlParamSupport.getParameterValue(params, SpecifyReleaseDate);
            if (StringUtils.isEmpty(specifyReleaseDate) && StringUtils.isNotEmpty(primaryDBInstanceName)) {  // 没有指定目标版本号，并且传了primary ins，根据primary查找到目标release_date
                ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, primaryDBInstanceName, true);
                if (Objects.nonNull(primaryReplicaSet)) {
                    specifyReleaseDate = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, primaryDBInstanceName, "minor_version");
                }
            }
            //检查实例指定的内核小版本
            String targetMinorVersion = mysqlParamSupport.getParameterValue(params, "TargetMinorVersion");
            // check cold data limit
            boolean coldDataEnabled = Boolean.parseBoolean(getParameterValue(params, RDS_COLD_DATA_ENABLED));
            if (coldDataEnabled) {
                podCommonSupport.checkColdDataSupportLimit(requestId, instanceLevel, dbType, dbVersion, diskType, null, targetMinorVersion, uid, avzInfo.getRegionId());
            }

            // 检查是否是分析型只读实例
            // 分析型只读实例的内核版本与主实例解藕
            Map<String, String> tagMap = new HashMap<>();
            if (mysqlParamSupport.hasParameter(params, IS_ANALYTIC_READ_ONLY_INS) && Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, IS_ANALYTIC_READ_ONLY_INS))) {
                duckDBService.checkConditionsForCreateAnalyticsReadOnlyIns(requestId, dbVersion, instanceLevel, isReadIns);
                tagMap.put("isAnalyticReadOnlyIns", "true");
            }

           String serviceSpec = minorVersionServiceHelper.getServiceSpecTag(
                    specifyReleaseDate,
                    bizType,
                    dbType,
                    dbVersion,
                    dbEngine,
                    KindCodeParser.KIND_CODE_NEW_ARCH,
                    instanceLevel,
                    diskType,
                    isDhg,
                    isArm,
                    true,
                    tagMap);
            String sourceDBInstanceId = mysqlParamSupport.getParameterValue(params, ParamConstants.SOURCE_DBINSTANCE_ID);
            String sourceDBInstanceName = mysqlParamSupport.getParameterValue(params, SOURCE_DB_INSTANCE_NAME);
            PodType podType = PodType.POD_RUNC;
            if(StringUtils.isBlank(sourceDBInstanceId) && StringUtils.isBlank(sourceDBInstanceName) && rundPodSupport.routeToRund(serviceSpec,PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites))){
                podType = rundPodSupport.getPodTypeByGrayConfig(avzInfo.getRegionId(), uid, instanceLevel, avzInfo);
            }
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType()) && !PodCommonSupport.isAccessFromDBS(accessId)) {
                Boolean slrAccess = slrCheckService.checkServiceLinkedRole(uid, requestId, SERVICE_LINKED_ROLE_MYSQL, false);
                if(!slrAccess){
                    podType = PodType.POD_RUNC;
                }
            }
            // Buid replicaSet resource
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            if (isDhg) {
                replicaSetResourceRequest.setDedicatedHostGroupId(clusterName);
            }
            replicaSetResourceRequest.setUserId(bid);
            replicaSetResourceRequest.setUid(uid);
            replicaSetResourceRequest.setInsType(insTypeDesc);
            replicaSetResourceRequest.setDbType(dbType);
            replicaSetResourceRequest.setDbVersion(dbVersion);
            replicaSetResourceRequest.setBizType(bizType.toString());
            replicaSetResourceRequest.setClassCode(classCode);
            replicaSetResourceRequest.setStorageType(diskType);
            replicaSetResourceRequest.setDiskSize(diskSize);
            replicaSetResourceRequest.setSubDomain(avzInfo.getRegion());
            replicaSetResourceRequest.setRegionId(avzInfo.getRegionId());
            replicaSetResourceRequest.putLabelsItem(MySQLParamConstants.ACCESS_ID_RESOURCE, getParameterValue(params, MySQLParamConstants.ACCESS_ID));  // 标识请求来源
            replicaSetResourceRequest.setComposeTag(serviceSpec);
            // Build replicaResource
            List<ReplicaResourceRequest> replicas;

            boolean isSingleTenant = replicaSetService.isCloudSingleTenant(bizType, diskType, instanceLevel, isDhg);
            // 多租户场景不允许创建使用用户密钥的云盘加密实例。
            mysqlEncryptionService.checkEncryptionKeyForNewCustins(requestId,params,isSingleTenant);

            Pair<String, ScheduleTemplate> scheduleTemplatePair;
            if (PodCommonSupport.isAccessFromDBS(accessId)) {
                // 来源于DBS的需要指定到DBS的资源池中
                scheduleTemplatePair = podTemplateHelper.getBizSysScheduleTemplateByName(requestId, PodDefaultConstants.RS_TEMPLATE_NAME_SYS_DBS);
            } else {
                scheduleTemplatePair = podTemplateHelper
                        .getBizSysScheduleTemplate(
                                podType,
                                bizType,
                                dbEngine,
                                instanceLevel,
                                isSingleTenant,
                                insTypeDesc,
                                null, null, mysqlParamSupport.getUID(params));
            }
            replicaSetResourceRequest.setScheduleTemplate(scheduleTemplatePair.getValue());
            MysqlReplicaResourceRequest mysqlReplicaResourceRequest = MysqlReplicaResourceRequest.builder()
                    .requestId(requestId)
                    .nodeRoles(nodeRoles)
                    .roleHostNameMapping(new HashMap<>())
                    .avzInfo(avzInfo)
                    .instanceLevel(instanceLevel)
                    .isSingleTenant(isSingleTenant)
                    .dbType(dbType)
                    .dbEngine(dbEngine)
                    .dbVersion(dbVersion)
                    .diskType(diskType)
                    .diskSize(podParameterHelper.getExtendDiskSizeGBForPod(bizType, false, diskSize))
                    .performanceLevel(performanceLevel)
                    .isRunD(PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType()))
                    .build();
            replicas = podReplicaSetResourceHelper.getReplicaResourceRequestList(mysqlReplicaResourceRequest, avzInfo);

            replicaSetResourceRequest.setSingleTenant(isSingleTenant);

            replicaSetResourceRequest.setDiskSize(diskSize);  //用户可见的磁盘空间
            replicaSetResourceRequest.setReplicaResourceRequestList(replicas);

            // evaluate io acceleration resource
            replicaSetResourceRequest.setGeneralCloudDisk(podCommonSupport.setGeneralCloudDiskConfig(requestId, params, dbVersion, instanceLevel, diskType, null, null));
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            }

            Map<String, Object> result = new HashMap<>();
            // 评估资源
            try {
                if (isQuotaEvaluate) {
                    return evaluateReplicaSetResourceQuota(requestId, dbInstanceName, replicaSetResourceRequest, params);
                }

                String transferRequest = mysqlParamSupport.getParameterValue(params, "TransferRequest", false);
                if (Boolean.parseBoolean(transferRequest)) {
                    //大客户报备，做资源请求转化
                    String ret = commonProviderService.getDefaultApi().getEvaluateReplicaSetResourceRequest(requestId, "no_need", evaluateNum, replicaSetResourceRequest);
                    Map<String, Object> data = new HashMap<String, Object>();
                    Map<String, String> requestsData = new HashMap<>();
                    requestsData.put("type", "CreateResourceSetRequest");
                    requestsData.put("value", ret);
                    data.put("Requests", Collections.singletonList(requestsData));
                    return data;
                }
                boolean isResourceSufficient = commonProviderService.getDefaultApi().evaluateReplicaSetResource(requestId, "no_need", evaluateNum, replicaSetResourceRequest);
                if (isResourceSufficient) {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                } else {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                }
            } catch (ApiException e) {
                return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
            }
            result.put(ENGINE_VERSION, instanceLevel.getServiceVersion());
            result.put(ParamConstants.ENGINE, CustinsSupport.getEngine(instanceLevel.getService()));
            return result;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, Object> evaluateReplicaSetResourceQuota(String requestId, String replicaSetName, ReplicaSetResourceRequest replicaSetResourceRequest, Map<String, String> params) throws ApiException {
        // fill simulatedNode
        String simulatedNodeStr = mysqlParamSupport.getParameterValue(params, Simulated_Nodes);
        replicaSetResourceRequest.setSimulatedNodeStr(simulatedNodeStr);
        String quotaMaxCount = mysqlParamSupport.getParameterValue(params, MySQLParamConstants.QUOTA_MAX_COUNT);
        Integer quotaMaxNum = StringUtils.isNoneBlank(quotaMaxCount) ? Integer.parseInt(quotaMaxCount) : null;

        // remove secondNodes
        replicaSetResourceRequest.getReplicaResourceRequestList().removeIf(rp -> Replica.RoleEnum.SLAVE.toString().equalsIgnoreCase(rp.getRole()));
        // remove dbInstanceName spread key
//        replicaSetResourceRequest.getScheduleTemplate().getSpread().removeIf(spread -> Objects.equals(spread.getSpreadKey(), replicaSetName));
        replicaSetResourceRequest.getScheduleTemplate().setSpread(null);

        Map<String, Object> result = new HashMap<>();
        Integer availableQuota = commonProviderService.getDefaultApi().evaluateReplicaSetResourceQuota(requestId, "no_need", quotaMaxNum, replicaSetResourceRequest);
        result.put(Available_Quota, availableQuota);
        result.put(REQUEST_ID, requestId);
        return result;
    }

    private void evaluateTdeAndClsEncryptionKeyStatus(Map<String, String> params, String regionId) throws Exception {
        String bid = mysqlParamSupport.getBID(params);
        String uid = mysqlParamSupport.getUID(params);
        boolean tdeEnabled = false;
        String tdeEncryptionKeyId = null;
        String clsKeyMode = null;
        String clsEncryptionKeyId = null;
        // 重建场景会传入sourceDbInstanceId，根据最近备份集标记检查TDE密钥状态
        String sourceDBInstanceId = mysqlParamSupport.getParameterValue(params, ParamConstants.SOURCE_DBINSTANCE_ID);
        if (StringUtils.isNotBlank(sourceDBInstanceId)) {
            GetBackupSetResponse backupSet = backupService.getBackupSet(
                BackupSetParam.builder()
                              .uid(uid)
                              .user_id(bid)
                              .dBInstanceId(Integer.parseInt(sourceDBInstanceId))
                              .backupSetId(null).build());
            tdeEnabled         = backupSet.getSlaveStatusObj().isTdeEnabled();
            tdeEncryptionKeyId = backupSet.getSlaveStatusObj().getTdeEncryptionKeyId();
            clsKeyMode = backupSet.getSlaveStatusObj().getClsKeyMode();
            clsEncryptionKeyId = backupSet.getSlaveStatusObj().getClsEncryptionKeyId();
            logger.info("rebuild instance tde encryption status, enabled:{}, keyId:{}", tdeEnabled, tdeEncryptionKeyId);
            logger.info("rebuild instance cls encryption status, keymode:{}, keyId:{}", clsKeyMode, clsEncryptionKeyId);
        }
        // 如果已删除备份集恢复，检查备份集TDE密钥状态
        String backupSetId = getParameterValue(params, ParamConstants.BACKUP_SET_ID, null);
        if (StringUtils.isNotBlank(backupSetId) && backupSetId.startsWith(MySQLParamConstants.RBH_PREFIX)) {
            DescribeRestoreBackupSetParam describeRestoreBackupSetParam = dbsGateWayService.describeRestoreBackupSetBuilder(params, mysqlParamSupport.getBID(params));
            DescribeRestoreBackupSetResponse restoreBackupResponse = dbsGateWayService.describeRestoreBackupSet(describeRestoreBackupSetParam);
            if (restoreBackupResponse != null && restoreBackupResponse.getBackupSetInfo() != null && restoreBackupResponse.getBackupSetInfo().getExtraInfo() != null) {
                tdeEnabled         = restoreBackupResponse.getBackupSetInfo().getExtraInfo().isTdeEnabled();
                tdeEncryptionKeyId = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getTdeEncryptionKeyId();
                clsKeyMode = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getClsKeyMode();
                clsEncryptionKeyId = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getClsEncryptionKeyId();
                logger.info("restore from deleted instance's backupSet, tdeEnabled:{}, tdeEncryptionKeyId:{}", tdeEnabled, tdeEncryptionKeyId);
                logger.info("restore from deleted instance's backupSet, clsKeyMode:{}, clsEncryptionKeyId:{}", clsKeyMode, clsEncryptionKeyId);
            }
        }
        if (tdeEnabled && StringUtils.isNotBlank(tdeEncryptionKeyId)) {
            logger.info("checking tde encryption key status");
            String tdeRoleArn = "acs:ram::" + uid + ":role/aliyunrdsinstanceencryptiondefaultrole";
            DescribeKeyResponse describeKeyResponse = kmsApi.describeKeyByRegionId(regionId, tdeEncryptionKeyId, tdeRoleArn, uid);
            if (describeKeyResponse == null || describeKeyResponse.getKeyMetadata() == null) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }
            String keyState = describeKeyResponse.getKeyMetadata().getKeyState();
            if (StringUtils.equalsIgnoreCase("Disabled", keyState)) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }
        }
        if (StringUtils.equalsIgnoreCase(PodDefaultConstants.CLS_MODE_KMS_KEY, clsKeyMode) && StringUtils.isNotBlank(clsEncryptionKeyId)) {
            logger.info("checking cls encryption key status");
            String roleArn = "acs:ram::" + uid + ":role/aliyunrdsinstanceencryptiondefaultrole";
            DescribeKeyResponse describeKeyResponse = kmsApi.describeKeyByRegionId(regionId, clsEncryptionKeyId, roleArn, uid);
            if (describeKeyResponse == null || describeKeyResponse.getKeyMetadata() == null) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }
            String keyState = describeKeyResponse.getKeyMetadata().getKeyState();
            if (StringUtils.equalsIgnoreCase("Disabled", keyState)) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }
        }
    }

    private boolean isCrossRegionRestore(ReplicaSet sourceReplicaSet, String regionId) throws RdsException {
        if (sourceReplicaSet == null) {
            return true;
        }
        CustInstanceDO custins = mysqlParameterHelper.getAndCheckSourceCustInstance();
        ClustersDO cluster = clusterService.getClusterByClusterName(custins.getClusterName());
        return !StringUtils.equalsIgnoreCase(cluster.getRegion(), regionId);
    }

}
