package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteAdminAccountImpl")
public class DeleteAdminAccountImpl implements IAction {
    @Autowired
    private DeleteAccountImpl deleteAccountImpl;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        return deleteAccountImpl.doActionRequest(custins, params);
    }
}
