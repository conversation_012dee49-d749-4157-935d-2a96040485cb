package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteDBNodesImpl")
public class DeleteDBNodesImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteDBNodesImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    ReplicaSetService replicaSetService;
    @Autowired
    protected CheckService checkService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            String orderId = mysqlParamSupport.getParameterValue(params, ParamConstants.ORDERID);
            if (replicaSet.getStatus() == ReplicaSet.StatusEnum.REPLICA_DELETING) {
                if (workFlowService.isTaskAlreadyRunning(orderId, replicaSet.getName())) {
                    logger.info("Task is Already Running.");
                    Map<String, Object> data = new HashMap<>();
                    data.put("TaskId", 0);
                    data.put("DBInstanceName", replicaSet.getName());
                    data.put("nodeIds", "");
                    return data;
                }
            }

            if (!(replicaSet.getStatus() == ReplicaSet.StatusEnum.ACTIVATION)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (replicaSet.getInsType() != ReplicaSet.InsTypeEnum.MAIN) {
                logger.error("dbInstance is not main Instance, do not allow to delete");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), false);
            boolean isMysqlCluster = instanceLevel.getCategory() == InstanceLevel.CategoryEnum.CLUSTER;

            if (!isMysqlCluster){
                logger.error("dbInstance is not cluster, do not allow to delete");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            String nodeIds = mysqlParamSupport.getDbNodeIds(params);
            // node 为空直接返回
            if (StringUtils.isEmpty(nodeIds)){
                logger.info("cluster remove node is empty, return");
                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", 0);
                data.put("DBInstanceName", dbInstanceName);
                data.put("nodeIds", nodeIds);
                return data;
            }
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            List<String> nodeLists = Arrays.asList(nodeIds.split(","));

            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().
                    listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null);
            List<Replica> replicas = replicaListResult.getItems();
            if (replicas == null) {
                throw new RdsException(ErrorCode.DBINSTANCE_DO_NOT_HAVE_STANDBY_NODE);
            }

            boolean checkResult = checkReplicaSetNode(replicas, nodeLists);
            // 没有任何节点可以删除时，直接返回，支持接口幂等
            if (!checkResult) {
                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", null);
                data.put("DBInstanceName", dbInstanceName);
                data.put("nodeIds", nodeIds);
                return data;
            }

            checkClusterCanRemoveNode(requestId, dbInstanceName, replicas, nodeLists);
            String removeNodeIds = getRemoveNodeIds(replicas, nodeLists);

            Map<String, Object> data = new HashMap<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ParamConstants.ORDERID, orderId);
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);
            jsonObject.put("srcReplicaSetName", dbInstanceName);
            jsonObject.put("removeNodeIds", removeNodeIds);

            String taskKey = getTaskKey(replicaSet);
            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);

            replicaSet.setStatus(ReplicaSet.StatusEnum.REPLICA_DELETING);
            dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, dbInstanceName, replicaSet);
            for (Replica replica : replicas) {
                if (nodeLists.contains(replica.getName())) {
                    replicaSetService.updateReplicaStatus(requestId, replica.getId(), Replica.StatusEnum.DELETING);
                }
            }

            data.put("TaskId", taskId);
            data.put("DBInstanceName", dbInstanceName);
            data.put("nodeIds", nodeIds);
            return data;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private String getRemoveNodeIds(List<Replica> replicas, List<String> nodeLists) throws Exception {
        List<String> removeList = new ArrayList<>();
        for (Replica replica : replicas) {
            if (nodeLists.contains(replica.getName())) {
                if (replica.getRole() == Replica.RoleEnum.MASTER){
                    throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "InvalidRemoveMaster", "cluster master node can not remove."});
                }
                removeList.add(replica.getId().toString());
            }
        }
        return StringUtils.join(removeList, ",");
    }

    /**
     * 1. 某个节点不在实例内报错NODE_NOT_FOUND；
     * 2. 节点都不在实例内，返回false；
     * 3. 节点都在实例内，返回True
     * */
    private boolean checkReplicaSetNode(List<Replica> replicas, List<String> nodeLists) throws Exception {
        List<String> replicaNameList = new ArrayList<>();
        replicas.forEach(replica -> replicaNameList.add(replica.getName()));
        List<String> foundNodes = new ArrayList<>();
        for (String node : nodeLists) {
            if (replicaNameList.contains(node)) {
                foundNodes.add(node);
            }
        }
        if (foundNodes.size() > 0 && nodeLists.size() != foundNodes.size()) {
            logger.error("need to remove node: {}, and only found {} ", nodeLists, foundNodes);
            throw new RdsException(ErrorCode.NODE_NOT_FOUND);
        } else if (foundNodes.size() == 0) {
            return false;
        } else {
            return true;
        }
    }

    private void checkClusterCanRemoveNode(String requestId, String replicaSetName, List<Replica> replicas, List<String> nodeLists) throws Exception {
        if (replicas.size() <= 2 || (replicas.size() - nodeLists.size()) < 2) {
            logger.error("replicaSet slave node or after remove node number is less than 2 , not permit delete");
            throw new RdsException(ErrorCode.INVALID_NODE_NUMBER);
        }

        boolean isMgr = replicaSetService.isMgr(RequestSession.getRequestId(), replicaSetName);
        int finalNodeNum = replicas.size() - nodeLists.size();
        // mgr节点需要大于等于3才允许释放一个节点， 校验释放手节点数不小于3
        if (isMgr && (finalNodeNum < 3 || finalNodeNum % 2 != 1)) {
            logger.error("replicaSet is mgr, slave node or after remove node number is less than 3, not permit delete");
            throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_NODE_NUM);
        }
    }
    private String getTaskKey(ReplicaSet replicaSet) throws ApiException {
        if (replicaSetService.isMgr(RequestSession.getRequestId(), replicaSet.getName())) {
            return PodDefaultConstants.TASK_CLUSTER_MGR_REMOVE_NODE;
        } else {
            return PodDefaultConstants.TASK_CLUSTER_REMOVE_NODE;
        }
    }
}
