package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeTaskProgressInfoImpl")
public class DescribeTaskProgressInfoImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeTaskProgressInfoImpl.class);
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private DBaasMetaService metaService;
    @Resource
    private WorkFlowService workFlowService;


    private final static Map<String, Integer> TASK_STATUS_MAPPING = new HashMap<String, Integer>() {{
        this.put("SCHEDULABLE", 0);
        this.put("RUNNING", 1);
        this.put("COMPLETED", 2);
        this.put("HUMAN_PROCESSING", 8);
        this.put("FAILED", 8);
    }};


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        try {
            String replicaSetName = mysqlParamSupport.getDBInstanceName(params);
            ReplicaSet replicaSet = metaService.getDefaultClient().getReplicaSet(requestId, replicaSetName, null);

            String taskId = mysqlParamSupport.getParameterValue(params, "taskId");
            Object taskRetObj = workFlowService.getTaskQueueControllerApi().getTaskSimpleListUsingGET(requestId, Long.valueOf(taskId), replicaSet.getName(), "custins", null, null, null, null, null, null, null, null, null);
            Map<String, Object> taskRet = parseRet(taskRetObj);
            List<Map<String, Object>> taskList = (List<Map<String, Object>>) taskRet.get("taskList");
            if (CollectionUtils.isEmpty(taskList)) {
                throw new RdsException(ErrorCode.TASK_NOT_FOUND);
            }
            Map<String, Object> task = taskList.get(0);
            int status = -1;
            if (TASK_STATUS_MAPPING.containsKey(task.get("status"))) {
                status = TASK_STATUS_MAPPING.get(task.get("status"));
            }

            Map<String, Object> result = new HashMap<>();
            result.put("TaskId", task.get("id"));
            result.put("BeginTime", task.get("taskBegin"));
            result.put("FinishTime", task.get("taskEnd"));
            result.put("Status", status);
            result.put("TaskAction", "");
            result.put("TaskResult", "");

            if (status == 0 || status == -1) {
                //等待调度状态
                result.put("Progress", 0);
                return result;
            }
            String workflowId = task.get("taskFlow") == null ? null : task.get("taskFlow").toString();

            Object workflowRetObj = workFlowService.getDefaultClient().getWorkflowApi().findByWorkflowIdUsingGET(workflowId, requestId, false);
            Map<String, Object> workflowRet = parseRet(workflowRetObj);
            Map<String, Object> workflowInstance = workflowRet.get("workflowInstance") == null ? Collections.EMPTY_MAP : (Map<String, Object>) workflowRet.get("workflowInstance");
            result.put("Progress", workflowInstance.get("progress"));
            return result;
        } catch (RdsException re) {
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            logger.error(requestId + " Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    private Map<String, Object> parseRet(Object ret) throws RdsException {
        Map<String, Object> taskMap = ret == null ? Collections.EMPTY_MAP : (Map<String, Object>) ret;
        if (taskMap.isEmpty()) {
            throw new RdsException(ErrorCode.TASK_NOT_FOUND);
        }
        if (!StringUtils.equalsIgnoreCase("success", taskMap.get("code").toString())) {
            logger.error("errMsg is {}", taskMap.get("message"));
            throw new RdsException(ErrorCode.TASK_NOT_FOUND);
        }
        Map<String, Object> data = taskMap.get("data") == null ? Collections.emptyMap() : (Map<String, Object>) taskMap.get("data");
        if (data.isEmpty()) {
            throw new RdsException(ErrorCode.TASK_NOT_FOUND);
        }
        return data;
    }


}
