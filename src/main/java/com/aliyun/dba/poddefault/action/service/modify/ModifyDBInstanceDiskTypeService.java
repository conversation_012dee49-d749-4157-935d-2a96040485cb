package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.KmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


/**
 * 云上变配实例，变更存储类型逻辑
 *
 */
@Service
public class ModifyDBInstanceDiskTypeService extends BaseModifyDBInstanceService {
    @Autowired
    protected MySQLServiceImpl mySQLService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected PodParameterHelper podParameterHelper;
    @Autowired
    protected PodTemplateHelper podTemplateHelper;
    @Autowired
    protected MysqlParamSupport paramSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected KmsService kmsService;
    @Resource
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    protected PodCommonSupport podCommonSupport;
    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;

    public void checkInstance(ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception{
        if (replicaSetMeta.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
            logger.error("db instance is not active.");
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }

        if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSetMeta, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
            throw new RdsException(ErrorCode.INVALID_KMS_KEY);
        }
    }

    public AllocateTmpResourceResult allocateResourceAndCheck(String requestId, PodModifyInsParam modifyInsParam) throws Exception{
        AllocateTmpResourceResult tmpReplicaSetResource = allocateTmpResourceService.make(requestId, modifyInsParam.getCustins(), modifyInsParam);
        boolean isAllocate = tmpReplicaSetResource.isAllocated();
        if (!isAllocate) {
            throw new RdsException(ErrorCode.RESOURCE_NOT_ENOUGH);
        }

        return tmpReplicaSetResource;
    }

    /**
     * 变配实例
     *
     * @param modifyInsParam
     * @param params
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doActionRequest(PodModifyInsParam modifyInsParam, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        try {
            ReplicaSet replicaSetMeta = modifyInsParam.getReplicaSetMeta();
            checkInstance(replicaSetMeta, params);

            //集团实例直接拒绝
            if (ReplicaSetService.isReplicaSetAliGroup(replicaSetMeta)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            Object taskId;
            // 变配磁盘这里强制限制只能变更存储类型，不能同时变更存储大小/实例规格
            if (modifyInsParam.isModifyCloudSSDToCloudEssd() || modifyInsParam.isPerfLevelChangedToPL0ForBasic()) {
                if (modifyInsParam.isXDB() || modifyInsParam.isCluster()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                } else {
                    taskId = doModifyDiskType(requestId, modifyInsParam, replicaSetMeta);
                }
                //更新实例状态
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, modifyInsParam.getDbInstanceName(),
                        ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", taskId);
                data.put("Region", modifyInsParam.getRegionId());
                data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
                data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
                data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
                data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
                data.put("SourceDBInstanceStorageType", modifyInsParam.getSrcDiskType());
                data.put("TargetDBInstanceStorageType", modifyInsParam.getTargetDiskType());
                data.put("TargetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());

                return data;
            } else {
                logger.error("instance: {} can not support instance modify db instance disk type and other operate together.", modifyInsParam.getDbInstanceName());
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: ", e);
            return createErrorResponse(new Object[]{ResultCode.CODE_ERROR, "AllocateResouceFailed", "Allocate resource failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public Object doModifyDiskType(String requestId, PodModifyInsParam modifyInsParam, ReplicaSet replicaSetMeta) throws Exception{
        boolean isSuccess = false;
        ReplicaSetResourceRequest allocateReplicaResource = null;

        try {
            AllocateTmpResourceResult tmpReplicaSetResource = allocateResourceAndCheck(requestId, modifyInsParam);

            allocateReplicaResource = tmpReplicaSetResource.getResourceRequest();
            String tmpReplicaSetName = allocateReplicaResource.getReplicaSetName();

            //构造任务参数
            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put("task_type_tag", modifyInsParam.isPerfLevelChangedToPL0ForBasic() ? "modify_to_pl0" : "modify_disktype_ssd_to_essd");
            taskParam.put("srcReplicaSetName", replicaSetMeta.getName());
            taskParam.put("destReplicaSetName", tmpReplicaSetName);
            taskParam.put("transTaskId", tmpReplicaSetResource.getTransList().getId());

            taskParam.put("PerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
            taskParam.put("targetDiskType", modifyInsParam.getTargetDiskType());
            taskParam.put("switchInfo", modifyInsParam.getSwitchInfo());

            Object taskId;
            if (modifyInsParam.isBaiscCategory()) {
                //单节点（基础版）
                taskParam.put("srcReplicaId", mySQLService.getReplicaByRole(requestId, replicaSetMeta.getName(), Replica.RoleEnum.MASTER).getId());
                taskParam.put("destReplicaId", mySQLService.getReplicaByRole(requestId, tmpReplicaSetName, Replica.RoleEnum.MASTER).getId());
                taskId = workFlowService.dispatchTask("custins", replicaSetMeta.getName(), "mysql", PodDefaultConstants.TASK_BASIC_MODIFY_INS_DISK_TYPE, taskParam.toString(), 0);
            } else {
                //高可用
                // 只读实例配置白名单同步label
                podParameterHelper.updateReadInsLabels(requestId, modifyInsParam.getReplicaSetMeta(), tmpReplicaSetName);

                taskParam.put("srcReplicaSetName", replicaSetMeta.getName());
                taskParam.put("srcParentReplicaSetName", replicaSetMeta.getPrimaryInsName());
                taskParam.put("destReplicaSetName", tmpReplicaSetName);

                String taskKey = PodDefaultConstants.TASK_STANDARD_MODIFY_INS_DISK_TYPE;
                taskId = workFlowService.dispatchTask("custins", replicaSetMeta.getName(), PodDefaultConstants.DOMAIN_MYSQL, taskKey, taskParam.toJSONString(), 0);
            }

            //任务下发成功标识位
            isSuccess = true;
            return taskId;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                logger.error(requestId + " Exception: {}", ((ApiException) e).getResponseBody());
                throw e;
            }
            logger.error(requestId + " Exception: {}", e.getMessage());
            throw e;
        } finally {
            // 处理失败时释放资源
            if (null != allocateReplicaResource && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, allocateReplicaResource.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for modify disk type failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    /*
     * 1. modify autopl config (burstingEnabled and provisionedIops)
     * 2. modify essd to autopl
     */
    public Map<String, Object> doModifyCloudAutoConfig(String requestId, PodModifyInsParam modifyInsParam, ReplicaSet replicaSetMeta) throws Exception {
        String workflowId = commonProviderService.getDefaultApi().modifyReplicaSetAutoPLInfo(requestId, replicaSetMeta.getName(), modifyInsParam.isBurstingEnabled(), modifyInsParam.getProvisionedIops(), false);
        Map<String, Object> result = new HashMap<>();
        result.put("workflowId", workflowId);
        return result;
    }

}
