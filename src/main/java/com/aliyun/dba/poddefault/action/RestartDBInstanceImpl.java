package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRestartDBInstanceImpl")
public class RestartDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.RestartDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected KmsService kmsService;
    @Resource
    private PodParameterHelper podParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params); // replicaSet do not have lockReason
            if (replicaSet.getLockMode() != ReplicaSet.LockModeEnum.NOLOCK &&
                    !((Objects.equals(replicaSet.getLockMode(), ReplicaSet.LockModeEnum.DISKFULL) ||
                            Objects.equals(replicaSet.getLockMode(), ReplicaSet.LockModeEnum.READINS_DISKFULL))
                            && Objects.equals(custins.getLockReason(), LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE))) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            if (!ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSet.getStatus())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);
            jsonObject.put("replicaIds", convertNodeIds2ReplicaIds(requestId, dbInstanceName, mysqlParamSupport.getDbNodeIds(params)));

            //切换重启需要获取对应的切换info
            Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);
            jsonObject.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
            String parameter = jsonObject.toJSONString();

            int priority = PodDefaultConstants.TASK_PRIORITY_COMMON;
            if (PodParameterHelper.isAliYun(replicaSet.getBizType())) {
                // 不存在重搭实例的实例，优先执行重启任务
                CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
                custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setPrimaryCustinsId(custins.getId());
                Long mirrorCount = custinsService.countCustIns(custInstanceQuery);
                if (mirrorCount == 0) {
                    priority = PodDefaultConstants.TASK_PRIORITY_VIP;
                }
            }

            Map<String, String> taskInfo = getTaskInfo(params, replicaSet, jsonObject.getString("replicaIds"));
            String domain = taskInfo.get("domain");
            String taskKey = taskInfo.get("taskKey");
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, priority);
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, dbInstanceName, ReplicaSet.StatusEnum.RESTARTING.toString());
            if (MysqlParamSupport.isCluster(replicaSet.getCategory())) { // 集群版更新节点状态
                findTargetReplicaAndUpdateStatus(requestId, dbInstanceName, jsonObject.getString("replicaIds"));
            }
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", dbInstanceName);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, String> getTaskInfo(Map params, ReplicaSet replicaSet, String replicaIds) throws RdsException, ApiException {
        //获取启动方式是直接重启还是优雅重启
        String restartMethod = mysqlParamSupport.getAndCheckRestartMethod(params, ParamConstants.RESTART_METHOD);
        ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(RequestSession.getRequestId(), replicaSet.getName());
        //physical链路
        Boolean isPhysical = mysqlParamSupport.getAndCheckIsPhysical(replicaSet);
        //优雅重启
        Boolean isElegant = mysqlParamSupport.getAndCheckRestartElegant(restartMethod);
        String taskKey = PodDefaultConstants.TASK_RESTART_XDB_INS_WITH_FAILOVER;
        //是否是单节点
        boolean isXDB = replicaSetService.isReplicaSetXDB(RequestSession.getRequestId(), replicaSet.getName());
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(RequestSession.getRequestId(), replicaSet.getService(),
                replicaSet.getServiceVersion(), replicaSet.getClassCode(), true);
        boolean isSingleNode = MysqlParamSupport.isSingleNode(instanceLevel);

        String domain = "mysql";
        boolean isReadIns = ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())
                || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSet.getInsType());
        if (PodParameterHelper.isAliGroup(replicaSet.getBizType())) {

            //physical实例且是优雅重启的
            if (isPhysical) {
                domain = "mysql";
                //集团主实例都是优雅重启
                taskKey = PodDefaultConstants.TASK_RESTART_XDB_INS;
                taskKey = (ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSet.getInsType()) ||
                        ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) ? PodDefaultConstants.TASK_RESTART_XDB_READ_INS : taskKey;
            } else if (replicaSetService.isTddlTaskMigrate(RequestSession.getRequestId(), replicaSet)) {
                domain = "mysql";
                taskKey = "restart_tddl_xdb_ins";
                if((ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSet.getInsType()) ||
                        ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType()))){
                    taskKey = replicaSetService.isAligroupDoubleNodeRead(RequestSession.getRequestId(),replicaSet.getName()) ? "restart_double_read_tddl_xdb_ins" : "restart_read_tddl_xdb_ins";
                }
            } else if (isSingleNode) {
                domain = "mysql";
                taskKey = PodDefaultConstants.TASK_RESTART_INS;
            } else {
                domain = "xdb";
                taskKey = PodDefaultConstants.TASK_RESTART_INS;
            }
        }
        else {
            domain = "mysql";
            taskKey = isXDB
                    ? isReadIns
                    ? PodDefaultConstants.TASK_RESTART_XDB_READ_INS
                    : (isElegant ? PodDefaultConstants.TASK_RESTART_XDB_INS : PodDefaultConstants.TASK_RESTART_XDB_INS_WITH_FAILOVER)
                    : replicaSetService.isMgr(replicaSetResource.getReplicaSet().getLabels()) ? PodDefaultConstants.TASK_RESTART_MGR_INS
                    : !Objects.isNull(replicaIds) ? PodDefaultConstants.TASK_RESTART_INS_NODE
                    : PodDefaultConstants.TASK_RESTART_INS_WITH_FAILOVER;
        }
        Map<String, String> result = new HashMap<>();
        result.put("domain", domain);
        result.put("taskKey", taskKey);
        return result;
    }

    private String convertNodeIds2ReplicaIds(String requestId, String replicaSetName, String nodeIds) throws ApiException, RdsException {
        if (StringUtils.isBlank(nodeIds) || !replicaSetService.isCluster(requestId, replicaSetName)) {
            return null;
        }

        Map<String, Replica> node2IdMapping = new HashMap<>();
        Objects.requireNonNull(dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                        requestId, replicaSetName, null, null, null, null).getItems()).
                forEach(rp -> node2IdMapping.put(rp.getName(), rp));
        List<String> targetReplicaIdList = new ArrayList<>();
        if (replicaSetService.isMgr(requestId, replicaSetName)) {
            for (String nodeId : nodeIds.trim().split(" *, *")) {
                // check node exist in replicaSet
                if (!node2IdMapping.containsKey(nodeId)) {
                    throw new RdsException(ErrorCode.CLUSTER_NODE_NOT_FOUND);
                }
                Replica curReplica = node2IdMapping.get(nodeId);
                // MGR 禁止指定 MASTER 重启
                if (Objects.equals(curReplica.getRole(), Replica.RoleEnum.MASTER)) {
                    throw new RdsException(ErrorCode.CLUSTER_MGR_RESTART_NOT_SUPPORTED);
                }
                targetReplicaIdList.add(Long.toString(node2IdMapping.get(nodeId).getId()));
            }
        }
        else {
            for (String nodeId : nodeIds.trim().split(" *, *")) {
                // check node exist in replicaSet
                if (!node2IdMapping.containsKey(nodeId)) {
                    throw new RdsException(ErrorCode.CLUSTER_NODE_NOT_FOUND);
                }
                targetReplicaIdList.add(Long.toString(node2IdMapping.get(nodeId).getId()));
            }
        }
        return StringUtils.join(targetReplicaIdList, ",");
    }

    private void findTargetReplicaAndUpdateStatus(String requestId, String replicaSetName, String replicaIds) throws ApiException {
        try {
            // 构造 replicaIds
            if (StringUtils.isBlank(replicaIds)) {
                List<Replica> replicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems();
                replicaIds = replicaList.stream().filter(rp -> Objects.equals(rp.getRole(), Replica.RoleEnum.MASTER)).findFirst().get().getId().toString();
                if (replicaSetService.isMgr(requestId, replicaSetName)) {
                    replicaIds = StringUtils.join(replicaList.stream().map(Replica::getId), ",");
                }
            }
            for (String replicaId : replicaIds.split(",")) {
                replicaSetService.updateReplicaStatus(requestId, Long.parseLong(replicaId), Replica.StatusEnum.RESTARTING);
            }
        } catch (Exception e) {
            logger.info("update replica status failed, skip with exception: {}", JSON.toJSONString(e));
        }
    }

}
