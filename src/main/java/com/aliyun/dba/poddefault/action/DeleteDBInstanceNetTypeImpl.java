package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteDBInstanceNetTypeImpl")
public class DeleteDBInstanceNetTypeImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteDBInstanceNetTypeImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    private PodParameterHelper podParameterHelper;

    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private CustinsParamService custinsParamService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            // 为集群版形态 链路管理 适配已有接口
            if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                String connectionString = paramSupport.getConnectionString(params);
                Integer netType = paramSupport.getNetType(params);
                NetTypeEnum netTypeEnum = PodDefaultConstants.NET_TYPE_ENUM_MAP.get(netType);

                // find NetType = "Normal" for primary endpoint
                Optional<Endpoint> endpointFound = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null).getItems()
                        .stream().filter(ep -> ep.getUserVisible() &&
                                (netTypeEnum == null || ep.getNetType().toString().equals(netTypeEnum.toString())) &&
                                (connectionString == null || ep.getAddress().equals(connectionString)) &&
                                (ep.getType().equals(Endpoint.TypeEnum.NORMAL))).findFirst();
                Optional<EndpointGroup> endpointGroupFound = metaService.getDefaultClient().listEndpointGroups(requestId, replicaSet.getName()).getItems()
                        .stream().filter(endpointGroup -> endpointGroup.getType().equalsIgnoreCase(Endpoint.TypeEnum.PRIMARY.toString())).findFirst();
                if (!endpointGroupFound.isPresent() || !endpointFound.isPresent()) {
                    throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
                }

                params.put("DBInstanceEndpointId".toLowerCase(), endpointGroupFound.get().getGroupName());
                params.put(ParamConstants.CONNECTION_STRING.toLowerCase(), endpointFound.get().getAddress());
                DeleteDBInstanceEndpointAddressImpl deleteDBInstanceEndpointAddress = SpringContextUtil.getBeanByClass(DeleteDBInstanceEndpointAddressImpl.class);
                logger.info("Mysql Cluster ins start delete netType: [{}]", endpointFound.get().getAddress());
                return deleteDBInstanceEndpointAddress.doActionRequest(null, params);
            }

            // 检查是否修改次数超过上限
            // TODO: change checkConnAddrChangeTimesExceed custins id to Long
            custinsService.checkConnAddrChangeTimesExceed(Integer.parseInt(replicaSet.getId().toString()), paramSupport.getAction(params), null);
            boolean isConnectionStringToSsl = paramSupport.isConnectionStringToSsl(params,custins);
            if (isConnectionStringToSsl){
                throw new RdsException(ErrorCode.INVALID_CONNECTIONSTRING, "The link address has been used by SSL, modification and deletion are prohibited");
            }
            Optional<Replica> replica = metaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems().stream().filter(r -> r.getRole().equals(Replica.RoleEnum.MASTER)).findFirst();
            if (!replica.isPresent()){
                throw new RdsException(ErrorCode.HOST_INSTANCE_NOT_FOUND);
            }
            Integer netType = paramSupport.getNetType(params);
            NetTypeEnum netTypeEnum = PodDefaultConstants.NET_TYPE_ENUM_MAP.get(netType);
            String connectionString = paramSupport.getConnectionString(params);
            if(netType == null && connectionString == null){
                throw new RdsException(ErrorCode.INVALID_PARAM);

            } else if (CustinsSupport.NET_TYPE_VPC.equals(netType) &&
                    podParameterHelper.isClusterUserVPCArch(custins.getDbType(), custins.getDbVersion(), custins.getClusterName())) {
                // Comment by Heyin:
                // Forbid all requests of deleting vpc net type, no matter db version
                // Rewrite this part when classic net type on k8s is ready
                logger.warn("isClusterUserVPCArch cluster, can not delete vpc net.");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);

            }
            EndpointListResult endpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null);
            final NetTypeEnum finalNetTypeEnum = netTypeEnum;
            Optional<Endpoint> findEndpointExist = endpointListResult.getItems().stream().filter(e -> e.getUserVisible() && ((finalNetTypeEnum==null || e.getNetType().toString().equals(finalNetTypeEnum.toString())) && (connectionString == null || e.getAddress().equals(connectionString)))).findFirst();
            if(!findEndpointExist.isPresent()){
                throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
            }
            Endpoint endpointExist = findEndpointExist.get();
            Integer operatorId = paramSupport.getOperatorId(params);
            EndpointChangeLog endpointChangeLog = new EndpointChangeLog().action(EndpointChangeLog.ActionEnum.DELETE).replicaId(replica.get().getId()).taskId(0).creator(operatorId).modifier(operatorId).fromUserVisible(true).toUserVisible(true
            ).fromConnAddrCust(endpointExist.getAddress()).fromVport(endpointExist.getVport().toString()
            ).netType(EndpointChangeLog.NetTypeEnum.valueOf(endpointExist.getNetType().name())).rwType(EndpointChangeLog.RwTypeEnum.valueOf(endpointExist.getType().name())
            ).fromVpcId(endpointExist.getVpcId()).fromVip(endpointExist.getVip()).status(EndpointChangeLog.StatusEnum.CREATING);
            EndpointChangeLog changeLogCreated = metaService.getDefaultClient().createReplicaSetEndpointChangeLog(requestId,replicaSet.getName(), endpointChangeLog);
            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_DELETING.toString());

            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put("changeLogId", changeLogCreated.getId());
            String taskKey = PodDefaultConstants.TASK_MODIFY_ENDPOINT;
            Object taskId = workFlowService.dispatchTask(replicaSet, taskKey, taskParam.toString(), 0);
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("ConnectionString", endpointExist.getAddress());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        }  catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
