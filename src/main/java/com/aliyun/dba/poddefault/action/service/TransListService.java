package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.task.dataobject.TransListDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 迁移恢复日志 service
 * <AUTHOR>
 */
@Service
public class TransListService {
    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    protected CustinsService custinsService;

    @Resource
    protected InstanceIDao instanceIDao;

    /**
     * 创建迁移恢复记录
     * @param srcReplicaSet
     * @param destReplicaSet
     * @param comment
     * @param requestId
     * @return
     * @throws ApiException
     */
    public Integer create(ReplicaSet srcReplicaSet, ReplicaSet destReplicaSet, String comment, String requestId) throws ApiException {
        // step 1: get replicas
        ReplicaListResult srcReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, srcReplicaSet.getName(), null, null, null, null);
        ReplicaListResult destReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, destReplicaSet.getName(), null, null, null, null);

        Long srcMasterReplicaId = srcReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();
        Long destMasterReplicaId = destReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();

        Long srcSlaveReplicaId = 0L;
        Long destSlaveReplicaId = 0L;
        if (InstanceLevel.CategoryEnum.STANDARD.toString().equals(srcReplicaSet.getCategory())) {
            srcSlaveReplicaId = srcReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get().getId();
            destSlaveReplicaId = destReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get().getId();
        }

        // step 2: get custins
        CustInstanceDO srcCustins = custinsService.getCustInstanceByCustinsId(srcReplicaSet.getId().intValue());
        CustInstanceDO destCustins = custinsService.getAllCustinsByCustinsId(destReplicaSet.getId().intValue());

        // step 3: make trans list
        TransListDO transList = new TransListDO(srcCustins, destCustins, InstanceSupport.TRANS_STATUS_REMOVE_NB, InstanceSupport.TRANS_TYPE_REMOVE);
        transList.setsHinsid1(srcMasterReplicaId.intValue());
        transList.setsHinsid2(srcSlaveReplicaId.intValue());

        transList.setdHinsid1(destMasterReplicaId.intValue());
        transList.setdHinsid2(destSlaveReplicaId.intValue());
        transList.setComment(comment);

        // step 4 : insert db
        this.instanceIDao.createTransList(transList);

        return transList.getId();
    }
}
