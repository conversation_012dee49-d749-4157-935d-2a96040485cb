package com.aliyun.dba.poddefault.action.support.exception;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

/**
 * CommonActivityProvider的异常统一处理
 *
 * <AUTHOR> on 2021/10/31
 */
public class CommonProviderExceptionUtils {

    private static final LogAgent logger = LogFactory.getLogAgent(CommonProviderExceptionUtils.class);

    /**
     * 封装CommonProvider的资源申请相关异常
     *
     * @param exception
     * @return
     */
    public static Map<String, Object> resourceWrapper(String requestId, ApiException exception) {
        String responseBody = exception.getResponseBody();
        if (StringUtils.isNotBlank(responseBody)) {
            Map<String, Object> responseMap;
            try {
                responseMap = JSON.parseObject(responseBody, Map.class);
            } catch (Exception e) {
                //ignore
                logger.error("CommonProviderAPI Failed, requestId is {}, {}", requestId, responseBody);
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND);
            }
            String summary = String.valueOf(responseMap.get("code"));
            String message = String.valueOf(responseMap.get("message"));

            logger.error("CommonProviderAPI Failed, requestId is {}, errorCode is {}, message is {}", requestId, summary, message);
            if (StringUtils.equalsIgnoreCase(summary, "CallMateApi.Failed")) {
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE, message);
            } else if (StringUtils.equalsIgnoreCase(summary, "CallVipManager.Failed")) {
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND, "CreateEndpointFailed");
            }
            return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND, message);
        }
        logger.error("CommonProviderAPI Failed, requestId is {}, {}", requestId, exception.getMessage());
        return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND);
    }

}
