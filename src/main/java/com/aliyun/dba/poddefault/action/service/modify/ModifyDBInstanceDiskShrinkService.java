package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_TRANS;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.property.ErrorCode.*;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

@Service
public class ModifyDBInstanceDiskShrinkService extends BaseModifyDBInstanceService {

    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodCommonSupport podCommonSupport;

    /**
     *  云盘缩容服务，仅支持同系列规格变更和云盘容量减少和云盘性能等级变更
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        boolean isSuccess = false;
        Map<String, String> allocateReplicaSets = new HashMap<>();
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        try {
            // 灰度开关
            if(!podParameterHelper.isSupportShrink()){
                throw new RdsException(UNSUPPORTED_REDUCE_DISK_SIZE);
            }

            // 初始化变配参数
            PodModifyInsParam modifyInsParam = initPodModifyInsParam(params);
            custins = modifyInsParam.getCustins();
            ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName());

            if (!modifyInsParam.isAliyun() || !modifyInsParam.isShrinkIns()) {
                logger.error("not support shrink, isAliyun:{}, isShrinkIns:{}", modifyInsParam.isAliyun(), modifyInsParam.isShrinkIns());
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 缩容次数校验,可按照uid加白
            podParameterHelper.checkShrinkLimit(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), modifyInsParam.getUid());

            // 检查可缩容云盘类型
            if(!SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(modifyInsParam.getSrcDiskType()) || !SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(modifyInsParam.getTargetDiskType())){
                throw new RdsException(INVALID_STORAGE);
            }

            // 检查磁盘类型变更
            if (modifyInsParam.isDiskTypeChange()) {
                throw new RdsException(UNSUPPORTED_CHANGE_STORAGE_TYPE);
            }

            // 不允许没达标的集团TDDL实例缩容
            boolean isTddlTaskMigrate = replicaSetService.isTddlTaskMigrate(requestId, modifyInsParam.getReplicaSetMeta());
            if (modifyInsParam.isTDDL() && !isTddlTaskMigrate) {
                logger.error("not support TDDL shrink when tddl task migrate label is false");
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 不允许架构迁移
            boolean isArchChange =  PodCommonSupport.isArchChange(modifyInsParam.getSrcInstanceLevel(), modifyInsParam.getTargetInstanceLevel());
            if (isArchChange) {
                throw new RdsException(UNSUPPORTED_TARGET_CLASS_CODE);
            }

            // 不允许同时category改变，暂不支持serverless、集群版系列
            if (modifyInsParam.getSrcInstanceLevel().getCategory() != modifyInsParam.getTargetInstanceLevel().getCategory() || !SHRINK_SUPPORT_CATEGORY_LIST.contains(modifyInsParam.getSrcInstanceLevel().getCategory().getValue())) {
                throw new RdsException(UNSUPPORTED_TARGET_CLASS_CODE);
            }

            // 不允许同时迁移
            String migratingAvz = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false");
            if (Boolean.parseBoolean(migratingAvz)) {
                logger.error("not support shrink and migrate at the same time");
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 校验当前实例已使用空间是否满足缩容条件
            podParameterHelper.checkShrinkCloudESSDValid(modifyInsParam.getRequestId(), modifyInsParam.getReplicaSetMeta().getId().intValue(), modifyInsParam.getUid(), modifyInsParam.getDiskSizeGB(), modifyInsParam.getTargetDiskSizeGB(), null);

            String replicaSetName = modifyInsParam.getReplicaSetMeta().getName();
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSetName, null);
            Map<Replica.RoleEnum, Replica> replicas = new HashMap<>();
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null);
            listReplicasInReplicaSet.getItems().stream().forEach(r -> replicas.put(r.getRole(), r));

            // 云盘赠送
            int extendedDestDiskSizeGB = podParameterHelper.getExtendDiskSizeGBForPod(modifyInsParam.getReplicaSetMeta().getBizType(), modifyInsParam.isSingleNode(), modifyInsParam.getTargetDiskSizeGB());

            // 资源申请
            allocateForShrink(requestId, allocateReplicaSets, modifyInsParam, replicas, params, extendedDestDiskSizeGB);
            ReplicaListResult destReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, allocateReplicaSets.get("master"), null, null, null, null);
            Long destReplicaId = destReplica.getItems().get(0).getId();
            ReplicaListResult destSlaveReplica = null;
            Long destSlaveReplicaId = null;
            if (null != allocateReplicaSets.get("slave")){
                destSlaveReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, allocateReplicaSets.get("slave"), null, null, null, null);
                destSlaveReplicaId = destSlaveReplica.getItems().get(0).getId();
            }

            // 只读实例的临时实例需要配置白名单同步label
            allocateReplicaSets.forEach((key, value) -> {
                try {
                    podParameterHelper.setReadInsSgLabel(requestId, replicaSetResource.getReplicaSet(), value);
                } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
                    logger.error("set read ins flush white list label fail.", requestId, e.getResponseBody());
                }
            });

            // 构建任务参数
            JSONObject jsonObject = new JSONObject();
            String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            String taskKey = TASK_ONLINE_SHRINK_INS;

            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", replicaSetName);
            jsonObject.put("destReplicaSetName", allocateReplicaSets.get("master"));
            jsonObject.put("destReplicaId", destReplicaId);
            jsonObject.put("destSlaveReplicaSetName", allocateReplicaSets.get("slave"));
            jsonObject.put("destSlaveReplicaId", destSlaveReplicaId);
            jsonObject.put("destDiskSizeMB", modifyInsParam.getTargetDiskSizeGB() * 1024);
            jsonObject.put("extendedDestDiskSizeGB", extendedDestDiskSizeGB);
            jsonObject.put("destClassCode", modifyInsParam.getTargetClassCode());
            jsonObject.put("performanceLevel", modifyInsParam.getTargetPerformanceLevel());
            jsonObject.put("srcParentReplicaSetName", modifyInsParam.getReplicaSetMeta().getPrimaryInsName());
            jsonObject.put("switchInfo", modifyInsParam.getSwitchInfo());

            // 创建transList
            TransferTask task = new TransferTask();
            task.setSrcReplicaSetName(custins.getInsName());
            task.setDestReplicaSetName(custins.getInsName());
            task.setSrcDiskSizeMB(modifyInsParam.getDiskSizeGB() * 1024);
            task.setDestDiskSizeMB(modifyInsParam.getTargetDiskSizeGB() * 1024);
            task.setSrcClassCode(modifyInsParam.getClassCode());
            task.setDestClassCode(modifyInsParam.getTargetClassCode());
            task.setComment("shrink");
            task.setDescription("Shrink Replica DiskSize And ClassCode");
            task.setParameter(JSONObject.toJSONString(jsonObject));
            task.setType(TransferTask.TypeEnum.REMOVE);
            TransferTask transferTask = dependency.getDBaasMetaService().getDefaultClient().createTransferTask(requestId, custins.getInsName(), task);


            // 下发任务
            jsonObject.put(CustinsSupport.TRANS_ID, transferTask.getId());
            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask("custins", replicaSetName, domain, taskKey, parameter, 0);

            custinsService.updateCustInstanceStatusByCustinsId(replicaSet.getId().intValue(), CUSTINS_STATUS_TRANS, CustinsState.STATE_CLASS_CHANGING.getComment());
            isSuccess = true;

            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("Region", modifyInsParam.getRegionId());
            data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
            data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
            data.put("SourceDBInstanceStorage", modifyInsParam.getClassCode());
            data.put("TargetDBInstanceStorage", modifyInsParam.getTargetClassCode());
            data.put("SourcePerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
            data.put("TargetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
            return data;
        } catch (RdsException e) {
            logger.warn(requestId + " RdsException: ", e);
            return createErrorResponse(e.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception e){
            logger.error(requestId + " Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            //释放申请的资源
            if (!isSuccess) {
                allocateReplicaSets.forEach((key, value) -> {
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, value);
                    } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                        logger.error("{} rollback resource failed: {}", requestId, e.getResponseBody());
                    }
                });
            }
        }
    }

    private void allocateForShrink(String requestId, Map<String,String> allocateReplicaSets, PodModifyInsParam modifyInsParam, Map<Replica.RoleEnum, Replica> replicas, Map<String, String> params, int extendedDestDiskSizeGB) throws Exception{
        // 获取源实例信息
        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, modifyInsParam.getReplicaSetMeta().getName(), null);
        // 检查是否存在重搭临时实例
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
        custInstanceQuery.setPrimaryCustinsId(modifyInsParam.getReplicaSetMeta().getId().intValue());
        Long mirrorCount = custinsService.countCustIns(custInstanceQuery);
        if (mirrorCount > 0) {
            throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
        }
        String category = replicaSet.getCategory();
        if (modifyInsParam.isReadIns() && Objects.nonNull(replicaSet.getPrimaryInsName())) {
            ReplicaSet primaryReplicaSet = dependency.getDBaasMetaService().getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), null);
            category = primaryReplicaSet.getCategory();
        }

        // 构建资源申请,缩容允许规格变化,且都走跨机重搭的方式
        ShrinkReplicaResourceRequest shrinkReplicaResourceRequest = new ShrinkReplicaResourceRequest();
        shrinkReplicaResourceRequest.setClassCode(modifyInsParam.getTargetClassCode());
        shrinkReplicaResourceRequest.setDiskSize(extendedDestDiskSizeGB);
        shrinkReplicaResourceRequest.setRebuildMode(ShrinkReplicaResourceRequest.RebuildModeEnum.MIGRATE);
        shrinkReplicaResourceRequest.setSingleTenant(modifyInsParam.isTargetSingleTenant());
        shrinkReplicaResourceRequest.setCategory(category);
        shrinkReplicaResourceRequest.setIgnoreCreateVpcMapping(true);

        // 指定资源调度模板
        shrinkReplicaResourceRequest.setScheduleTemplate(
                podTemplateHelper.getReplicaSetScheduleTemp(replicaSet, modifyInsParam.getTargetInstanceLevel(), modifyInsParam.isTargetSingleTenant(), null)
        );

        // 指定composeTag
        String composeTag = minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(requestId, replicaSet.getName());
        shrinkReplicaResourceRequest.setComposeTag(composeTag);

        // 指定重搭主机
        String hostName = podParameterHelper.getHostNameFromParamsByDedicatedHostName(ActionParamsProvider.ACTION_PARAMS_MAP.get(), "DedicatedHostNames", replicaSet.getResourceGroupName());
        shrinkReplicaResourceRequest.setHostName(hostName);

        // 如果接口指定了调度策略，则透传下去
        if (StringUtils.isNotEmpty(mysqlParamSupport.getResourceStrategy(params))) {
            if (shrinkReplicaResourceRequest.getScheduleTemplate() == null) {
                shrinkReplicaResourceRequest.setScheduleTemplate(new ScheduleTemplate());
            }
            shrinkReplicaResourceRequest.getScheduleTemplate().setResourceStrategy(mysqlParamSupport.getResourceStrategy(params));
        }

        // 云盘在任务流中申请资源
        shrinkReplicaResourceRequest.setVolumeSpecs(null);
        shrinkReplicaResourceRequest.setAllocateDisk(false);

        //===================================重搭master实例资源申请=======================================
        // ---bugfix:先替换源slave所以先申请源slave替换的资源,再申请源master替换的资源,否则可能造成双可用区某一时刻可用区一致导致HA不托管---
        Replica masterReplica = null == replicas.get(Replica.RoleEnum.SLAVE) ? replicas.get(Replica.RoleEnum.MASTER) : replicas.get(Replica.RoleEnum.SLAVE);
        shrinkReplicaResourceRequest.setZoneId(masterReplica.getZoneId());
        shrinkReplicaResourceRequest.setTmpReplicaSetName(modifyInsParam.getTmpReplicaSetName());
        commonProviderService.getDefaultApi().allocateReplicaSetResourceForShrink(requestId, replicaSet.getName(), masterReplica.getId(), shrinkReplicaResourceRequest);
        allocateReplicaSets.put("master", shrinkReplicaResourceRequest.getTmpReplicaSetName());

        //===================================重搭slave实例资源申请=======================================
        if(null == replicas.get(Replica.RoleEnum.SLAVE)){
            return;
        }
        Replica slaveReplica = replicas.get(Replica.RoleEnum.MASTER);
        shrinkReplicaResourceRequest.setTmpReplicaSetName("slave" + modifyInsParam.getTmpReplicaSetName());
        shrinkReplicaResourceRequest.setZoneId(slaveReplica.getZoneId());
        commonProviderService.getDefaultApi().allocateReplicaSetResourceForShrink(requestId, replicaSet.getName(), slaveReplica.getId(), shrinkReplicaResourceRequest);
        allocateReplicaSets.put("slave", shrinkReplicaResourceRequest.getTmpReplicaSetName());
    }
}
