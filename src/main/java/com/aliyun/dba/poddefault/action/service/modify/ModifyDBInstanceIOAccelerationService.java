package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.poddefault.action.service.AutomaticSlaveZoneService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_STANDARD;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE;

/**
 * IO Acceleration modification requires separate logic.
 *
 * <AUTHOR> on 2023/6/1.
 */
@Service
public class ModifyDBInstanceIOAccelerationService extends BaseModifyDBInstanceService {


    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;

    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Resource
    private AutomaticSlaveZoneService automaticSlaveZoneService;

    @Resource
    private ModifyDBInstanceFromStandardToClusterService modifyDBInstanceFromStandardToClusterService;

    /**
     * Modify instance
     *
     * @param custins
     * @param params
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        Integer transListId;
        boolean isSuccess = false;
        boolean isAllocated = false;

        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();

        try {
            // Initialize modify parameters
            PodModifyInsParam modifyInsParam = initPodModifyInsParam(params);
            custins = modifyInsParam.getCustins();

            /********** filter condition Start **********/

            // Only the transformation tasks that switch Io acceleration can go through this flow.
            if (!modifyInsParam.isIoAccelerationEnabledChange()) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }

            // filter test service_spec
            if (minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId()).startsWith(MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }

            AVZInfo avzInfo = modifyInsParam.getAvzInfo();

            /********** filter condition End **********/
            List<Replica> currentReplicas = dBaasMetaService.getDefaultClient()
                    .listReplicasInReplicaSet(
                            modifyInsParam.getRequestId(),
                            modifyInsParam.getDbInstanceName(),
                            null, null, null, null
                    ).getItems();

            Replica masterReplica = currentReplicas
                    .stream()
                    .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER))
                    .findFirst()
                    .get();

            String masterZoneId = masterReplica.getZoneId();
            Map<Replica.RoleEnum, String> azMap = new HashMap<>();
            Map<Replica.RoleEnum, String> subDomainMap = new HashMap<>();
            ArrayList<ReplicaResourceRequest> replicas = new ArrayList<>();
            Integer slaveNumber = 0;

            boolean isStandard = InstanceLevel.CategoryEnum.STANDARD.toString().equals(modifyInsParam.getTargetInstanceLevel().getCategory().getValue());
            boolean isCluster = InstanceLevel.CategoryEnum.CLUSTER.toString().equals(modifyInsParam.getTargetInstanceLevel().getCategory().getValue());
            boolean isBasicPrimary = InstanceLevel.CategoryEnum.BASIC.toString().equals(modifyInsParam.getSrcInstanceLevel().getCategory().getValue());

            // If it is a standard or cluster instance,  need to update the az Map and sub Domain Map
            // standard becomes standard using the original data
            if (isStandard) {
                String slaveAz = null;
                String slaveSubDomain = null;
                logger.info("Use slave isBasicPrimary: {}, requestId : {}", JSONObject.toJSONString(isBasicPrimary), modifyInsParam.getRequestId());
                if (!isBasicPrimary) {
                    List<Replica> slaveReplicaList = currentReplicas
                            .stream()
                            .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.SLAVE))
                            .collect(Collectors.toList());
                    for (Replica slaveReplica : slaveReplicaList) {
                        slaveAz = slaveReplica.getZoneId();
                        slaveSubDomain = automaticSlaveZoneService.getLocationByAz(slaveAz);
                        if (!slaveAz.equalsIgnoreCase(masterZoneId)) {
                            logger.info("Use slave subDomain: {}, requestId : {}", slaveSubDomain, modifyInsParam.getRequestId());
                            subDomainMap.put(Replica.RoleEnum.SLAVE, slaveSubDomain);
                        }
                        azMap.put(Replica.RoleEnum.SLAVE, slaveAz);
                    }
                    slaveNumber = slaveReplicaList.size();
                } else {
                    slaveAz = automaticSlaveZoneService.getRandomSlaveZones(avzInfo.getRegionId(), masterZoneId, modifyInsParam.getGeneralCloudDisk());
                    slaveSubDomain = automaticSlaveZoneService.getLocationByAz(slaveAz);
                    // important!
                    if (!slaveAz.equalsIgnoreCase(masterZoneId)) {
                        logger.info("Use slave subDomain: {}, requestId : {}", slaveSubDomain, modifyInsParam.getRequestId());
                        subDomainMap.put(Replica.RoleEnum.SLAVE, slaveSubDomain);
                    }
                    azMap.put(Replica.RoleEnum.SLAVE, slaveAz);
                }

                if (!azMap.isEmpty()) {
                    logger.info("slaveAz is {}, requestId : {}", JSONObject.toJSONString(azMap), modifyInsParam.getRequestId());
                }
                if (!subDomainMap.isEmpty()) {
                    logger.info("slaveSubDomain is {}, requestId : {}", JSONObject.toJSONString(subDomainMap), modifyInsParam.getRequestId());
                }
            }

            String category = modifyInsParam.getTargetInstanceLevel().getCategory().getValue();
            List<Replica.RoleEnum> replicaList = new ArrayList<>();
            replicaList.add(Replica.RoleEnum.MASTER);
            for (int i = 0; i < slaveNumber; i++) {
                replicaList.add(Replica.RoleEnum.SLAVE);
            }

            // Cluster instances do not need replicaList, azMap, subDomainMap, just use replicas directly.
            if (isCluster) {
                replicas = allocateTmpResourceService.getReplicasForCluster(modifyInsParam, currentReplicas);
                logger.info("the ins is cluster, and replicas is {}, requestId : {}", JSONObject.toJSONString(replicas), modifyInsParam.getRequestId());
            }


            // The category of the read-only instance can only be set to CATEGORY_STANDARD, otherwise it will cause the acquisition of the service_spec of the basic version of the read-only instance to fail,
            // and the allocation will be allocated to the arm machine in the mysqlx resource pool
            AllocateTmpResourceResult result = allocateTmpResourceService.make(requestId,
                    custins,
                    modifyInsParam,
                    ReplicaSet.InsTypeEnum.READONLY.equals(modifyInsParam.getReplicaSetMeta().getInsType()) ? CATEGORY_STANDARD : category,
                    replicaList,
                    azMap,
                    subDomainMap,
                    replicas);


            isAllocated = result.isAllocated();
            transListId = result.getTransList().getId();
            resourceRequest = result.getResourceRequest();

            // Completion of audit log control parameters
            custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
            // Update system parameter template
            modifyDBInstanceFromStandardToClusterService.updateParamGroupId(requestId, dBaasMetaService.getDefaultClient(), modifyInsParam.getDbInstanceName(), modifyInsParam.getTmpReplicaSetName());

            String taskKey = PodDefaultConstants.TASK_MODIFY_INS_FOR_IO_ACCELERATION;

            // dispatch task
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", modifyInsParam.getRequestId());
            taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
            taskParamObject.put("transTaskId", transListId);
            taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
            // The following parameters are passed to Online Resize for use
            taskParamObject.put("srcDiskType", modifyInsParam.getSrcDiskType());
            taskParamObject.put("targetDiskType", modifyInsParam.getTargetDiskType());
            taskParamObject.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
            taskParamObject.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
            taskParamObject.put("targetCategory", category);

            String taskParam = taskParamObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask(
                    "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    modifyInsParam.getRequestId(),
                    modifyInsParam.getDbInstanceName(),
                    modifyInsParam.isTransIns() ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.CLASS_CHANGING.toString()
            );

            // build response
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
            data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
            data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
            data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
            data.put("TaskId", taskId);
            data.put("Region", modifyInsParam.getRegionId());
            isSuccess = true;
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // Failed to release resources
            if (isAllocated && !isSuccess && StringUtils.isNotEmpty(resourceRequest.getReplicaSetName())) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, resourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }

}
