package com.aliyun.dba.poddefault.action.support.GAD;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class Aes {

    private final static String KEY_MODE = "AES";

    private final static String AES_MODE = "AES/ECB/NoPadding";

    public static final String PWD_CRYPTKEY = "h3fOp=S;M@[i!6b#";

    public static final String PWD_TRANS_CRYPTKEY = "h39_s=M&S@[s!6C#";
    /**
     * 私有化
     */
    private Aes() {
    }

    /**
     * 加密
     *
     * @param password 待加密的内容
     * @param cryptKey 密钥
     * @return
     */
    private static byte[] encrypt(String password, String cryptKey) throws Exception {
        try {
            //KeyGenerator kgen = KeyGenerator.getInstance(KEY_MODE); //提供对称密钥生成器的功能
            //kgen.init(128, new SecureRandom(cryptKey.getBytes()));  //指定随机源初始化密钥生成器，并固定密钥大小为128位
            //SecretKey secretKey = kgen.generateKey();  //分组秘密密钥（安全）
            //byte[] enCodeFormat = secretKey.getEncoded(); //原始密钥字节
            SecretKeySpec key = new SecretKeySpec(cryptKey.getBytes(), KEY_MODE); //根据给定的字节数组构造一个密钥
            Cipher cipher = Cipher.getInstance(AES_MODE);// 创建填充模式的密码器  ，提供密码功能
            byte[] byteContent = password.getBytes("utf-8");
            cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化
            return cipher.doFinal(byteContent);   //数据将被加密
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * 解密
     *
     * @param encrypt  待解密内容
     * @param cryptKey 密钥
     * @return
     */
    private static byte[] decrypt(byte[] encrypt, String cryptKey) throws Exception {
        try {
            //KeyGenerator kgen = KeyGenerator.getInstance(KEY_MODE);
            //kgen.init(128, new SecureRandom(cryptKey.getBytes()));
            //SecretKey secretKey = kgen.generateKey();
            //byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec key = new SecretKeySpec(cryptKey.getBytes(), KEY_MODE);
            Cipher cipher = Cipher.getInstance(AES_MODE);// 创建密码器
            cipher.init(Cipher.DECRYPT_MODE, key);// 初始化
            return cipher.doFinal(encrypt);
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * 二进制转换为十六进制
     *
     * @param buf
     * @return
     */
    private static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toLowerCase());
        }
        return sb.toString();
    }

    /**
     * 十六进制转换为二进制
     *
     * @param hexStr
     * @return
     */
    private static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    /**
     * 数据加密
     *
     * @param password
     * @param cryptKey
     * @return
     * @throws Exception
     */
    public final static String encryptData(String password, String cryptKey) throws Exception {
        return parseByte2HexStr(encrypt(password, cryptKey));

    }

    /**
     * 数据解密
     *
     * @param encrypt
     * @param cryptKey
     * @return
     * @throws Exception
     */
    public final static String decryptData(String encrypt, String cryptKey) throws Exception {
        return new String(decrypt(parseHexStr2Byte(encrypt), cryptKey));
    }

    /**
     * 对明文密码加密，密码为48字节，不够末尾补"\0"
     *
     * @param password
     * @param cryptKey
     * @return
     */
    public final static String encryptPassword(String password, String cryptKey) throws Exception {
        if (password.length() > 48) {
            throw new Exception("the password is too long, #pararm[password:" + password + "]");
        }
        StringBuffer buf = new StringBuffer(48);
        buf.append(password);
        while (buf.length() != 48) {
            buf.append("\0");
        }
        return encryptData(buf.toString(), cryptKey);
    }

    /**
     * 对密文密码解密，解密后进行字符串trim()处理，得到原密码
     *
     * @param encrypt
     * @param cryptKey
     * @return
     */
    public final static String decryptPassword(String encrypt, String cryptKey) throws Exception {
        return decryptData(encrypt, cryptKey).trim();
    }

    public final static String decryptAccountPasswd(String encrypt, String cryptKey) throws Exception {
        return decryptData(encrypt, cryptKey).trim();
    }


}
