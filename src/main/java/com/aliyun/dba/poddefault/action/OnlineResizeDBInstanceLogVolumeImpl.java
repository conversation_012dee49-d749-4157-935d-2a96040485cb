package com.aliyun.dba.poddefault.action;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica.StorageTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.Volume;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

/**
 * <AUTHOR> href="<EMAIL>">习武</a>
 * @date 2021/08/30
 * @Description TODO
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultOnlineResizeDBInstanceLogVolumeImpl")
public class OnlineResizeDBInstanceLogVolumeImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(OnlineResizeDBInstanceLogVolumeImpl.class);
    private static final String LOG = "log";
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService metaService;
    @Autowired
    protected ReplicaSetService replicaSetService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params)
        throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            //instanceName
            String dbInstanceName = mysqlParaHelper.getDBInstanceName();
            String instanceRole = mysqlParaHelper.getParameterValue("instanceRole");
            //get cur diskSizeMB
            ReplicaListResult replicaListResult = metaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, dbInstanceName, null, null, null, null);
            Long curDiskSizeMB = 0L;
            Replica destReplica = replicaListResult.getItems().stream().filter(replica -> replica.getRole().toString().equals(instanceRole)).findFirst().get();
            if (isCloudPfsDisk(destReplica)) {
                ReplicaResource replicaResource = metaService.getDefaultClient().getReplica(requestId, destReplica.getId(), null);
                Volume volume = replicaResource.getVolumes().stream().filter(volume1 -> "log".equalsIgnoreCase(volume1.getCategory())).findFirst().get();
                if (curDiskSizeMB < volume.getSizeMB()) {
                    curDiskSizeMB = volume.getSizeMB().longValue();
                }
            }

            Long diskSizeMB = mysqlParaHelper.getAndCheckDiskSize(curDiskSizeMB) * 1024;
            if (diskSizeMB <= curDiskSizeMB) {
                return createErrorResponse(ErrorCode.INVALID_STORAGE);
            }
            // Add work flow task
            String domain = "mysql";
            String taskKey = PodDefaultConstants.TASK_ONLINE_RESIZE_INS_LOG;

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);
            jsonObject.put("replicaId", destReplica.getId());
            jsonObject.put("diskSizeMB", diskSizeMB);
            jsonObject.put("category", "log");
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, jsonObject.toJSONString(), 0);

            Map<String, Object> result = new HashMap<>();
            result.put("DBInstanceName", dbInstanceName);
            result.put("RequestId", requestId);
            result.put("Storage", diskSizeMB);
            result.put("TaskId", taskId);
            return result;
        } catch (Exception e) {
            logger.error("onlineResizeInsLog failed", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private boolean isCloudPfsDisk(Replica replica) {
        return StorageTypeEnum.CLOUD_ESSD.equals(replica.getStorageType())
            || StorageTypeEnum.CLOUD_AUTO.equals(replica.getStorageType())
            || StorageTypeEnum.CLOUD_SSD.equals(replica.getStorageType())
            || StorageTypeEnum.CLOUD_EFFICIENCY.equals(replica.getStorageType());
    }
}
