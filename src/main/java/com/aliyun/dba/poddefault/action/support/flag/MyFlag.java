package com.aliyun.dba.poddefault.action.support.flag;


public class MyFlag {
    Long f = 0L;

    //添加flag
    public MyFlag addFlags(Long newFlags) {
        f |= newFlags;
        return this;
    }

    //清空flag
    public MyFlag clearFlags(Long targetFlags) {
        f &= ~targetFlags;
        return this;
    }

    public MyFlag setFlags(Long newFlags, boolean status) {
        if (status) {
            return addFlags(newFlags);
        } else {
            return clearFlags(newFlags);
        }
    }

    //是否包含某些flag
    public boolean hasFlags(Long targetFlags) {
        return (f & targetFlags) == targetFlags;
    }

    @Override
    public String toString() {
        return "MyFlag{" +
                "f=" + f +
                '}';
    }
}
