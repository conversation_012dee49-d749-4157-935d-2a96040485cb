package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.EvaluateEcsNodeService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.PoddefaultResourceGuaranteeModelService;
import com.aliyun.dba.poddefault.action.service.modify.AliyunModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.ACCESS_ID_RESOURCE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultmodifyDBNodeImpl")
@Slf4j
public class ModifyDBNodeImpl implements IAction {

    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private ReplicaSetService replicaSetService;

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Resource
    protected CommonProviderService commonProviderService;

    @Resource
    protected PodTemplateHelper podTemplateHelper;

    @Resource
    protected PodParameterHelper podParameterHelper;

    @Resource
    protected WorkFlowService workFlowService;

    @Resource
    protected AliyunModifyDBInstanceService aliyunModifyDBInstanceService;

    @Resource
    private EvaluateEcsNodeService evaluateEcsNodeService;

    @Resource
    protected PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        ReplicaSet replicaSet;
        boolean isAllocated = false;
        boolean isSuccess = false;

        try {
            // 检查传参，user dbInstanceName
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);

            String orderId = mysqlParamSupport.getParameterValue(params, ParamConstants.ORDERID);
            if (replicaSet.getStatus() == ReplicaSet.StatusEnum.CLASS_CHANGING) {
                if (workFlowService.isTaskAlreadyRunning(orderId, replicaSet.getName())) {
                    log.info("Task is Already Running, return. ");
                    Map<String, Object> data = new HashMap<>();
                    data.put("TaskId", 0);
                    data.put("DBInstanceName", replicaSet.getName());
                    data.put("ModifiedNodeNum", 0);
                    return data;
                }
            }
            // 校验实例，所有前置检查封装在这里面
            this.preCheckForModifyDBNode(requestId, replicaSet);

            // 检查节点
            List<Replica> replicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                    requestId, replicaSet.getName(), null, null, null, null).getItems();
            String dbNodeString = mysqlParamSupport.getParameterValue(params, "DBNode");

            List<NodeModifyEntity> nodeModifyEntityList = this.buildChangedReplicas(requestId, replicaSet, dbNodeString, replicaList);
            if (!nodeModifyEntityList.isEmpty()) {
                params.put(ParamConstants.TARGET_DB_INSTANCE_CLASS.toLowerCase(), nodeModifyEntityList.get(0).getMasterTargetClassCode());
            }

            // 构造资源参数
            PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(params);

            ModifyReplicaSetResourceRequest modifyReplicaSetResourceRequest = this.buildReplicaSetResourceRequest(replicaSet, modifyInsParam, nodeModifyEntityList);

            try {
                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", null);
                data.put("DBInstanceName", replicaSet.getName());

                Integer transferTask;
                String taskKey;
                if (isSingleTenantLocalNodeModify(nodeModifyEntityList, modifyInsParam)) {  // 单租户节点本地升降配
                    transferTask = buildTransListForLocalModify(modifyInsParam, nodeModifyEntityList);
                    modifyInsParam.setSingleTenantLocalModify(true);
                    taskKey = PodDefaultConstants.TASK_LOCAL_MODIFY_INS_NODE;
                } else if (!Objects.isNull(modifyReplicaSetResourceRequest)) {  // 节点跨机升降配
                    // 测试环境单租户mock ENI
                    podReplicaSetResourceHelper.mockReplicaSetResource(modifyReplicaSetResourceRequest, modifyInsParam.getReplicaSetMeta());

                    // 调用申请资源
                    transferTask = commonProviderService.getDefaultApi().allocateReplicaSetResourceForScale(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), modifyReplicaSetResourceRequest);
                    isAllocated = true;
                    taskKey = PodDefaultConstants.TASK_MODIFY_INS_NODE;

                    data.put("ModifiedNodeNum", modifyReplicaSetResourceRequest.getReplicaResourceRequestList().size());
                }
                else if (modifyInsParam.isOnLineResize()) {
                    transferTask = aliyunModifyDBInstanceService.buildTransListForOnlineResize(modifyInsParam, replicaList);
                    taskKey = PodDefaultConstants.TASK_ONLINE_RESIZE_INS_FOR_CLUSTER;
                }
                else {
                    return data;
                }


                // 构造任务流参数
                JSONObject taskParamObject = this.buildTaskParam(modifyInsParam, transferTask, nodeModifyEntityList);
                taskParamObject.put(ParamConstants.ORDERID, orderId);
                String taskParam = taskParamObject.toJSONString();
                String domain = PodDefaultConstants.DOMAIN_MYSQL;

                Object taskId = workFlowService.dispatchTask("custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);

                // 更新实例状态
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                        modifyInsParam.getRequestId(),
                        modifyInsParam.getDbInstanceName(),
                        ReplicaSet.StatusEnum.CLASS_CHANGING.toString());
                // 更新replica状态
//                for (var nodeModifyEntity : nodeModifyEntityList) {
//                    if (nodeModifyEntity.getChangeFlag().equals(NodeModifyEntity.RESOURCE_CHANGES)) {  // TODO 后续可以做细一点，迁移可用区拆分出来
//                        Long replicaId = nodeModifyEntity.getReplicaId();
//                        replicaSetService.updateReplicaStatus(requestId, replicaId, Replica.StatusEnum.CLASSCHANGING);
//                    }
//                }

                data.put("TaskId", taskId);
                isSuccess = true;
                return data;
            } catch (Exception ex) {
                log.error("Allocate resource or dispatch workflow failed, " + JSON.toJSONString(ex));
                if (ex instanceof com.aliyun.apsaradb.activityprovider.ApiException) {
                    log.error("allocateReplicaSetResourceV1 failed uid={} replicaSetName={}",
                            modifyReplicaSetResourceRequest.getUid(),
                            modifyReplicaSetResourceRequest.getReplicaSetName());
                    return CommonProviderExceptionUtils.resourceWrapper(requestId, (com.aliyun.apsaradb.activityprovider.ApiException) ex);
                }
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            } finally {
                if (isAllocated && !isSuccess) {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, modifyInsParam.getTmpReplicaSetName());
                }
            }
        } catch (RdsException ex) {
            log.error("RdsException: " + JSON.toJSONString(ex));
            return createErrorResponse(ex.getErrorCode());
        } catch (Exception ex) {
            log.error("Exception: " + JSON.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    private boolean isSingleTenantLocalNodeModify(List<NodeModifyEntity> nodeModifyEntityList, PodModifyInsParam modifyInsParam) throws Exception {
        ReplicaSet replicaSet = modifyInsParam.getReplicaSetMeta();
        if (!nodeModifyEntityList.isEmpty() && modifyInsParam.isTargetSingleTenant() && modifyInsParam.isSrcSingleTenant()) {
            Map<Long, InstanceLevel> replica2LevelMapping = new HashMap<>();
            for (NodeModifyEntity nodeModifyEntity : nodeModifyEntityList) {
                replica2LevelMapping.put(nodeModifyEntity.getReplicaId(),
                        dBaasMetaService.getDefaultClient().getInstanceLevel(modifyInsParam.getRequestId(), replicaSet.getService(), replicaSet.getServiceVersion(), nodeModifyEntity.dstClassCode, null));
            }
            return evaluateEcsNodeService.evaluateReplicasCanLocalUpgrade(modifyInsParam.getRequestId(), modifyInsParam.getReplicaSetMeta(), replica2LevelMapping);
        }
        return false;
    }

    /**
     * 构造单租户本地升降配的trans_list
     */
    private Integer buildTransListForLocalModify(PodModifyInsParam modifyInsParam, List<NodeModifyEntity> nodeModifyEntityList) throws ApiException {
        String replicaSetName = modifyInsParam.getReplicaSetMeta().getName();

        TransferTask transferTask = new TransferTask();
        transferTask.setSrcReplicaSetName(replicaSetName);
        transferTask.setDestReplicaSetName(replicaSetName);
        transferTask.setSrcClassCode(modifyInsParam.getClassCode());
        transferTask.setDestClassCode(modifyInsParam.getTargetClassCode());
        transferTask.setSrcDiskSizeMB(modifyInsParam.getDiskSizeGB() * 1024);
        transferTask.setDestDiskSizeMB(modifyInsParam.getTargetDiskSizeGB() * 1024);

        transferTask.setType(TransferTask.TypeEnum.REMOVE);
        transferTask.setComment("Local Node Modify Class Code");

        List<Map<String, Object>> transParamList = new ArrayList<>();
        for (NodeModifyEntity nodeModifyEntity : nodeModifyEntityList) {
            Map<String, Object> transParm = new HashMap<>();
            transParm.put("srcReplicaId", nodeModifyEntity.getReplicaId());
            transParm.put("dstReplicaId", nodeModifyEntity.getReplicaId());
            transParm.put("srcClassCode", nodeModifyEntity.getSrcClassCode());
            transParm.put("dstClassCode", nodeModifyEntity.getDstClassCode());
            transParamList.add(transParm);
        }
        Map<String, List<Map<String, Object>>> parameter = new HashMap<>();
        parameter.put("transParamList", transParamList);
        Map<String, Object> resourceParam = new HashMap<>();
        //适配资源保障策略
        Map<String, String> allResourceGuaranteeLevelMap = poddefaultResourceGuaranteeModelService.getAllResourceGuaranteeLevelMapForUid(modifyInsParam.getUid());
        if (MapUtils.isNotEmpty(allResourceGuaranteeLevelMap)) {
            resourceParam.put("resourceGuaranteeLevel", allResourceGuaranteeLevelMap.get("resourceGuaranteeLevel"));
            resourceParam.put("resourceGuaranteeLevelType", allResourceGuaranteeLevelMap.get("resourceGuaranteeLevelType"));
            //强制指定机型时 备选资源保障标签可能不存在
            resourceParam.put("resourceGuaranteeBackUpLevels", allResourceGuaranteeLevelMap.getOrDefault("resourceGuaranteeBackUpLevels", ""));
        }
        List<Map<String, Object>> resourceParamList = Collections.singletonList(resourceParam);
        parameter.put("resourceParam", resourceParamList);
        transferTask.setParameter(JSON.toJSONString(parameter));

        TransferTask ret = dBaasMetaService.getDefaultClient().createTransferTask(modifyInsParam.getRequestId(), replicaSetName, transferTask);
        return  ret.getId();
    }

    /**
     * 对ReplicaSet做执行前的校验，出错统一抛错异常
     */
    private void preCheckForModifyDBNode(String requestId, ReplicaSet replicaSet) throws RdsException, ApiException {
        if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        if (replicaSet.getInsType() != ReplicaSet.InsTypeEnum.MAIN) {
            log.error("dbInstance is not main Instance, do not allow add node. ");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
        if (!MysqlParamSupport.isCluster(replicaSet.getCategory())){
            log.error("dbInstance is not cluster, do not allow add node. ");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
        if (replicaSetService.isMgr(requestId, replicaSet.getName())) {
            log.error("dbInstance is not normal cluster, MGR is not supported in node modify");
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NODE_MODIFY_FOR_MGR);
        }
    }

    /**
     *
     */
    private List<NodeModifyEntity> buildChangedReplicas(String requestId, ReplicaSet replicaSet, String dbNodeString, List<Replica> oriReplicaList) throws ApiException, RdsException {
        // init variables
        List<NodeModifyEntity> changedNodeList = new ArrayList<>();

        String replicaSetName = replicaSet.getName();
        Map<String, Replica> oriReplicaMapping = new HashMap<>();
        Map<String, NodeModifyEntity> changeNodeMapping = new HashMap<>();
        Set<String> oriReplicaNameSet = oriReplicaList.stream().map(Replica::getName).collect(Collectors.toSet());
        Replica masterReplica = oriReplicaList.stream().filter(rp -> rp.getRole().equals(Replica.RoleEnum.MASTER)).collect(Collectors.toList()).get(0);
        String masterTargetClassCode = masterReplica.getClassCode();

        // build variables
        List<Map<String, String>> dbNodes = JSON.parseObject(dbNodeString, List.class);
        if (Objects.isNull(dbNodes) || dbNodes.isEmpty()) {
            return changedNodeList;
        }
        oriReplicaList.forEach(rp -> oriReplicaMapping.put(rp.getName(), rp));
        for (Map<String, String> dbNode : dbNodes) {  // master target classCode
            if (masterReplica.getName().equals(dbNode.get("nodeId")) && !Objects.isNull(dbNode.get("classCode"))) {
                masterTargetClassCode = dbNode.get("classCode");
                break;
            }
        }

        InstanceLevel masterInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), masterTargetClassCode, true);
        if (Objects.isNull(masterInstanceLevel)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLASS_CODE);
        }

        // start build changed replicas
        for (Map<String, String> dbNode : dbNodes) {
            String nodeId = dbNode.get("nodeId");
            String classCode = dbNode.get("classCode");
            String zoneId = dbNode.get("zoneId");


            // check dbNode param
            if (!oriReplicaNameSet.contains(nodeId)) {  // dbNode 需要包含 nodeId
                throw new RdsException(ErrorCode.NODE_NOT_FOUND);
            }
            Replica mappingReplica = oriReplicaMapping.get(nodeId);

            InstanceLevel curInstanceLevel = null;
            if (!Objects.isNull(classCode)) {
                curInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), classCode, true);
                if (Objects.isNull(curInstanceLevel)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_CLASS_CODE);
                }
            }
            // 初始化、校验 集中做
            NodeModifyEntity nodeModifyEntity = new ModifyDBNodeImpl.NodeModifyEntity(nodeId, mappingReplica.getId(),
                    mappingReplica.getRole().toString(), masterTargetClassCode, masterInstanceLevel, curInstanceLevel,
                    mappingReplica.getClassCode(), classCode,
                    mappingReplica.getZoneId(), null,  // TODO 暂时不支持 迁移可用区，设置zoneId = null
                    null, null);

            if (nodeModifyEntity.getChangeFlag().equals(NodeModifyEntity.NO_CHANGES)) {
                continue;
            }

            changedNodeList.add(nodeModifyEntity);
            changeNodeMapping.put(nodeId, nodeModifyEntity);
        }

        // 整体check是否是满足HA数量限制, 至少一个备节点开启HA
        boolean slaveHAFlag = false;
        for (var rp : oriReplicaList) {
            if (rp.getRole().equals(Replica.RoleEnum.MASTER)) {
                continue;
            }
            String nodeId = rp.getName();
            if (changeNodeMapping.containsKey(nodeId)) {  // 参与变更的节点是目标规格
                if (masterTargetClassCode.equalsIgnoreCase(changeNodeMapping.get(nodeId).getDstClassCode())) {
                    slaveHAFlag = true;
                    break;
                }
            } else if (rp.getClassCode().equalsIgnoreCase(masterTargetClassCode)) {  // 没有变更的节点是目标规格
                slaveHAFlag = true;
                break;
            }
        }
        if (!slaveHAFlag) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_SLAVE_CLASS_CODE);
        }
        log.info("changedNodeList: {}", JSON.toJSONString(changedNodeList));

        return changedNodeList;
    }

    /**
     * 构造任务流参数
     */
    private JSONObject buildTaskParam(PodModifyInsParam modifyInsParam, Integer transferTaskId, List<NodeModifyEntity> nodeModifyEntityList) {
        JSONObject taskParam = new JSONObject();
        taskParam.put("requestId", modifyInsParam.getRequestId());
        taskParam.put("replicaSetName", modifyInsParam.getDbInstanceName());
        taskParam.put("transTaskId", transferTaskId);
        taskParam.put("switchInfo", modifyInsParam.getSwitchInfo());

        // 以下参数传给Online Resize使用
        taskParam.put("srcDiskType", modifyInsParam.getSrcDiskType());
        taskParam.put("targetDiskType", modifyInsParam.getTargetDiskType());
        taskParam.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
        taskParam.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());

        return taskParam;
    }

    /**
     * 构造资源申请的请求体
     * 1. 根据需要变更的节点数量申请replica
     * 2. 实例规格参数跟随主节点的变化
     */
    private ModifyReplicaSetResourceRequest buildReplicaSetResourceRequest(ReplicaSet replicaSet, PodModifyInsParam modifyInsParam, List<NodeModifyEntity> changedReplicaList) throws ApiException {
        if (changedReplicaList.isEmpty()) {
            return null;
        }

        ModifyReplicaSetResourceRequest replicaSetResourceRequest = new ModifyReplicaSetResourceRequest();
        // 基础信息
        replicaSetResourceRequest.replicaSetName(modifyInsParam.getDbInstanceName())
                .userId(modifyInsParam.getBid())
                .uid(modifyInsParam.getUid())
                .tmpReplicaSetName(modifyInsParam.getTmpReplicaSetName())
                .dbType(modifyInsParam.getDbType())
                .dbVersion(modifyInsParam.getDbVersion())
                .classCode(replicaSet.getClassCode())
                .diskSize(modifyInsParam.getTargetDiskSizeGB())
                .storageType(modifyInsParam.getTargetDiskType())
                .scheduleTemplate(modifyInsParam.getScheduleTemplate())
                .classCode(replicaSet.getClassCode())
                .reuseCloudDisk(true);  // 不支持跨可用区迁移


        // build labels
        replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.LABEL_ORDER_ID, modifyInsParam.getOrderId());
        replicaSetResourceRequest.putLabelsItem(ACCESS_ID_RESOURCE, modifyInsParam.getAccessId());

        // replica信息
        List<ModifyReplicaResourceRequest> replicaResourceRequestList = new ArrayList<>();
        for (var nodeModifyEntity : changedReplicaList) {
            if (!nodeModifyEntity.getChangeFlag().equals(NodeModifyEntity.RESOURCE_CHANGES)) {  // 非资源类变更，不构建新的节点
                continue;
            }
            ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(modifyInsParam.getRequestId(), nodeModifyEntity.getReplicaId(), false);
            Replica replica = replicaResource.getReplica();
            ModifyReplicaResourceRequest replicaResourceRequest = new ModifyReplicaResourceRequest();
            replicaResourceRequest.setClassCode(nodeModifyEntity.getDstClassCode());
            replicaResourceRequest.setHostName(modifyInsParam.getRoleHostNameMapping().get(replica.getRole()));
            replicaResourceRequest.setRole(replica.getRole().toString());
            replicaResourceRequest.setZoneId(replica.getZoneId());  // 去掉ZoneId，保持同可用区变配
            if (modifyInsParam.getPodScheduleTemplate() != null) {
                replicaResourceRequest.setScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(modifyInsParam.getPodScheduleTemplate(), replica.getRole().toString()));
            }

            List<VolumeSpec> volumeSpecList = new ArrayList<>();
            VolumeSpec dataVolumeSpec = new VolumeSpec();
            dataVolumeSpec.setName("data");
            dataVolumeSpec.setCategory("data");
            if (ReplicaSetService.isStorageTypeCloudDisk(modifyInsParam.getTargetDiskType())) {
                //设置云盘性能等级
                dataVolumeSpec.setPerformanceLevel(modifyInsParam.getTargetPerformanceLevel());
                if (modifyInsParam.isDiskSizeChange()) {
                    //如果磁盘大小有变化，需要做云盘需要有赠送
                    int diskSizeGB = podParameterHelper
                            .getExtendDiskSizeGBForPod(modifyInsParam.getReplicaSetMeta().getBizType(), false, modifyInsParam.getTargetDiskSizeGB());
                    replicaResourceRequest.setDiskSize(diskSizeGB);
                    dataVolumeSpec.setDiskSize(diskSizeGB);
                } else {
                    // 磁盘大小没有变化，一定要取replica中的diskSizeMB，这里面存储的是赠送后的大小
                    replicaResourceRequest.setDiskSize(replica.getDiskSizeMB() / 1024);
                    dataVolumeSpec.setDiskSize(replica.getDiskSizeMB() / 1024);
                }
            }

            if (StringUtils.isEmpty(replicaResourceRequest.getStorageType())) {
                replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
            }
            volumeSpecList.add(dataVolumeSpec);

            // 指定源replicaPodName
            replicaResourceRequest.setSourcePodId4ReuseDisk(replicaResource.getVpod().getVpodId());

            replicaResourceRequest.setVolumeSpecs(volumeSpecList);
            replicaResourceRequest.setSingleTenant(modifyInsParam.isTargetSingleTenant());

            replicaResourceRequestList.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicaResourceRequestList);

        // ModifyMode
        replicaSetResourceRequest.setModifyMode(modifyInsParam.getModifyMode());

        // 单租户
        if (modifyInsParam.isTargetSingleTenant()) {
            replicaSetResourceRequest.setSingleTenant(true);
            replicaSetResourceRequest.setEniDirectLink(false);
            if (modifyInsParam.isSrcSingleTenant()) {
                //原和目标规格都单租户场景，变配策略强制迁移跨机申请新资源
                replicaSetResourceRequest.setModifyMode(ModifyReplicaSetResourceRequest.ModifyModeEnum.MIGRATE);
            }
        }

        // 反向VPC，不申请，最后会统一调用创建
        replicaSetResourceRequest.setIgnoreCreateVpcMapping(true);

        // master replica更新实例ClassCode
        for (var nodeModifyEntity : changedReplicaList) {
            String role = nodeModifyEntity.getRole();
            if (role.equalsIgnoreCase(Replica.RoleEnum.MASTER.toString())) {
                replicaSetResourceRequest.setClassCode(nodeModifyEntity.getDstClassCode());
            }
        }
        log.info("Build replicaSetResourceRequest success: {}", JSON.toJSONString(replicaSetResourceRequest));

        return CollectionUtils.isEmpty(replicaSetResourceRequest.getReplicaResourceRequestList()) ? null : replicaSetResourceRequest;
    }

    @Data
    public static class NodeModifyEntity {
        public static final Integer NO_CHANGES = 0;
        public static final Integer CONFIG_CHANGES = 1;
        public static final Integer RESOURCE_CHANGES = 2;

        private String nodeId;
        private Long replicaId;
        private String role;
        private String masterTargetClassCode;
        private InstanceLevel masterInstanceLevel;
        private InstanceLevel curInstanceLevel;
        private String srcClassCode;
        private String dstClassCode;
        private String srcZoneId;
        private String dstZoneId;
        private String srcRemark;
        private String dstRemark;

        /**
         * 变更类型，暂定 Flag = 0, 1, 2
         * 0: 表示没有变更，需要跳过这个节点
         * 1: 表示不影响节点资源侧变更
         * 2: 目前表示 变配、迁移可用区等涉及到资源变更
         */
        private Integer changeFlag;


        /**
         * 要求 src 数据要具备，并与当前 replica 数据一致，当 dst 数据不存在时，赋值为 src 数据
         */
        public NodeModifyEntity(String nodeId, Long replicaId, String role, String masterTargetClassCode,
                                InstanceLevel masterInstanceLevel, InstanceLevel curInstanceLevel,
                                String srcClassCode, String dstClassCode,
                                String srcZoneId, String dstZoneId,
                                String srcRemark, String dstRemark) throws RdsException {
            this.nodeId = nodeId;
            this.replicaId = replicaId;
            this.role = role;
            this.masterTargetClassCode = masterTargetClassCode;
            this.masterInstanceLevel = masterInstanceLevel;
            this.curInstanceLevel = curInstanceLevel;
            this.srcClassCode = srcClassCode;
            this.dstClassCode = Objects.isNull(dstClassCode) ? srcClassCode : dstClassCode;
            this.srcZoneId = srcZoneId;
            this.dstZoneId = Objects.isNull(dstZoneId) ? srcZoneId : dstZoneId;
            this.srcRemark = srcRemark;
            this.dstRemark = Objects.isNull(dstRemark) ? srcRemark : dstRemark;

            this.checkChangeValid();

            this.setChangeFlag();
        }

        /**
         * 设置变更的模式，参数变更、资源变更
         */
        private void setChangeFlag() {
            if (!Objects.equals(this.srcClassCode, this.dstClassCode) || !Objects.equals(this.srcZoneId, this.dstZoneId)) {
                this.changeFlag = 2;
            } else {
                this.changeFlag = 0;
            }
        }

        /**
         * 校验变更是否合理 TODO
         * 1. 可用区是否属于region
         * 2. 规则租户类型是否符合
         */
        private void checkChangeValid() throws RdsException {
            // 租户类型校验
            if (!Objects.equals(this.masterInstanceLevel.getIsolationType(), this.curInstanceLevel.getIsolationType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_CLASS_CODE);
            }
            // 可用区校验
        }
    }

}
