package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyVolumeSpecRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaDto;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.modify.AliyunModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MysqlErrorCode.INVALID_CLOUD_DISK_SIZE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE;

@Service
public class AligroupModifyDBInstanceService implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.ModifyDBInstanceClassImpl.class);
    private static final int MAX_TMP_INSTANCE_NAME_LENGTH = 38;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected AligroupService aligroupService;
    @Resource
    private PodTemplateHelper podTemplateHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        try {

            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String bid = user.getBid();
            String uid = user.getAliUid();
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            String regionId = avzInfo.getRegionId();
            String zoneId = avzInfo.getMasterZoneId();
            String subDomain = avzInfo.getRegion();
            String dbType = replicaSetMeta.getService();
            String dbVersion = replicaSetMeta.getServiceVersion();

            if (aligroupService.isXdbMultiWriteEngine(requestId, replicaSetMeta)) {
                logger.info("current replicaset is multi engine");
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            Integer diskSize = replicaSetMeta.getDiskSizeMB() / 1024;
            Integer targetDiskSize;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.STORAGE)) {
                targetDiskSize = CheckUtils.parseInt(
                        mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE),
                        5, 102400, ErrorCode.INVALID_STORAGE);
            } else {
                targetDiskSize = diskSize;
            }
            String classCode = replicaSetMeta.getClassCode();
            String targetClassCode;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.TARGET_DB_INSTANCE_CLASS)) {
                targetClassCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
            } else {
                targetClassCode = classCode;
            }
            String diskType = replicaSetService.getReplicaSetStorageType(dbInstanceName, requestId);

            Date switchTime = mysqlParamSupport.getAndCheckSwitchTime(params);
            String switchMode = mysqlParamSupport.getAndCheckSwitchTimeMode(params, ParamConstants.SWITCH_TIME_MODE, switchTime, false);

            boolean isClassCodeChange = !classCode.equals(targetClassCode);
            boolean isDiskSizeChange = !diskSize.equals(targetDiskSize);
            // 如果规格和磁盘没有变化, 则这个操作为迁移操作
            boolean isTransIns = !isClassCodeChange && !isDiskSizeChange;
            String orderId = getParameterValue(params, "OrderId");

            JSONObject taskParamObject = new JSONObject();
            String taskKey;
            Integer transListId;
            String dbEngine = mysqlParamSupport.isMysqlXDB(dbType, dbVersion, classCode) ? "XDB" : "MySQL";
            if (replicaSetService.isXDBIns(requestId, dbInstanceName)) {
                dbEngine = "XDB";
            }
            ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, dbInstanceName);
            boolean isReduceDiskSize = targetDiskSize < diskSize;
            boolean isForce = mysqlParamSupport.getAndCheckIsForce(params).equals(1);
            if (isReduceDiskSize && !isForce) {
                throw new RdsException(UNSUPPORTED_REDUCE_DISK_SIZE);
            }
            Map<String, String> lables = replicaSetResource.getReplicaSet().getLabels();
            String tddlClusterName = lables.getOrDefault("clusterName", "");
            // FIXME 这里云盘版本使用新的 Common API 来先做支持, 后面本地盘迁移过来去掉本地盘的代码
            /**
             * 20200928 jinxiang add 强制走nc流式直传方式
             */
            if (!replicaSetService.isStorageTypeCloudDisk(diskType) || isForce) {
                CreateReplicaSetDto replicaSet = new CreateReplicaSetDto();
                if (replicaSetMeta.getConnType() == ReplicaSet.ConnTypeEnum.TDDL) {
                    String storageEngine = lables.get("storageEngine");
                    String timeZone = lables.get("timeZone");
                    String charset = lables.get("charset");
                    String dbName = tddlClusterName.split("_")[0].toLowerCase();
                    replicaSet.setStorageEngine(storageEngine);
                    replicaSet.setTimeZone(timeZone);
                    replicaSet.setCharset(charset);
                    replicaSet.setClusterName(tddlClusterName);
                    replicaSet.setAppName(StringUtils.upperCase(dbName));
                    replicaSet.setClusterId(DigestUtils.md5DigestAsHex(dbName.toUpperCase().getBytes()));
                }

                diskSize = targetDiskSize;
                classCode = targetClassCode;
                replicaSet.setReplicaSetName(dbInstanceName);
                replicaSet.setUserId(bid);
                replicaSet.setUid(uid);
                replicaSet.setInstanceId(dbInstanceName);
                replicaSet.setDbType(replicaSetMeta.getService());
                replicaSet.setDbVersion(replicaSetMeta.getServiceVersion());
                replicaSet.setInsType(replicaSetMeta.getInsType().toString());
                replicaSet.setConnType(replicaSetMeta.getConnType().toString());
                replicaSet.setBizType("aligroup");
                replicaSet.setDiskSize(diskSize);
                replicaSet.setDbEngine(dbEngine);
                replicaSet.setRequestId(requestId);
                replicaSet.setStorageType(diskType);
                if (aligroupService.isTddlClusterNeedAllocateDedicatedResourceGroup(requestId, tddlClusterName)) {
                    replicaSet.setDedicatedBizGroup(tddlClusterName);
                }

                if (replicaSetService.isStorageTypeCloudDisk(diskType)) {
                    replicaSet.setComposeTag("cloud_pfs");
                }

                //检查实例指定指定资源调度模板创建
                Map<String, String> labels = replicaSetResource.getReplicaSet().getLabels();
                PodScheduleTemplate podScheduleTemplate = null;
                if (MapUtils.isNotEmpty(labels)) {
                    String rsTemplateName = labels.get(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME);
                    podScheduleTemplate = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, rsTemplateName, bid + "_" + uid);

                    if (podScheduleTemplate != null) {
                        replicaSet.setScheduleTemplate(podTemplateHelper.getReplicaSetScheduleTemplate(podScheduleTemplate));
                    }
                }

                List<ReplicaDto> replicas = new ArrayList<>();
                List<ReplicaResource> currentReplicaResources = replicaSetResource.getReplicaResources();
                Map<String, Replica> roleReplicaMapping = currentReplicaResources.stream()
                        .collect(Collectors.toMap((k -> k.getReplica().getRole().toString()), ReplicaResource::getReplica));
                if (replicaSetResource.getReplicaSet().getInsType().equals(ReplicaSet.InsTypeEnum.READONLY)) {
                    String[] nodeRoles = "XDB".equals(dbEngine) ? new String[]{"learner"} : new String[]{"master"};
                    for (String role : nodeRoles) {
                        Replica srcReplica = roleReplicaMapping.get(role);
                        if (srcReplica == null) {
                            logger.error("role is [{}] cannot find replica", role);
                            throw new RdsException(ErrorCode.NODE_NOT_FOUND);
                        }
                        ReplicaDto replica = new ReplicaDto();
                        replica.setId(String.format("%s-%s", dbInstanceName, role));
                        replica.setSubDomain(subDomain != null ? subDomain : srcReplica.getSubDomain());
                        replica.setZoneId(zoneId != null ? zoneId : srcReplica.getZoneId());
                        replica.setDiskType(diskType);
                        if (podScheduleTemplate != null) {
                            replica.setScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(podScheduleTemplate, role));
                        }


                        replica.setRole("XDB".equals(dbEngine) ? "learner" : role);
                        replica.setClassCode(classCode);
                        replica.setDiskSize(diskSize);
                        replica.setNodeGroup("mix");
                        replica.setNodeType("logger".equals(role) ? "log" : "normal");
                        replicas.add(replica);
                    }
                } else {
                    String[] nodeRoles = "XDB".equals(dbEngine) ? new String[]{"master", "follower", "logger"} : new String[]{"master", "slave"};
                    for (String role : nodeRoles) {
                        Replica srcReplica = roleReplicaMapping.get(role);
                        if (srcReplica == null) {
                            logger.error("role is [{}] cannot find replica", role);
                            throw new RdsException(ErrorCode.NODE_NOT_FOUND);
                        }
                        ReplicaDto replica = new ReplicaDto();
                        replica.setId(String.format("%s-%s", dbInstanceName, role));
                        replica.setRegionId(regionId);
                        replica.setSubDomain(subDomain != null ? subDomain : srcReplica.getSubDomain());
                        replica.setZoneId(zoneId != null ? zoneId : srcReplica.getZoneId());
                        replica.setDiskType(diskType);
                        if (podScheduleTemplate != null) {
                            replica.setScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(podScheduleTemplate, role));
                        }
                        if ("logger".equalsIgnoreCase(role)) {
                            replica.setRole("logger");
                            String loggerClassCode = aligroupService.getAligroupLoggerClassCode(srcReplica.getStorageType().toString());
                            replica.setClassCode(loggerClassCode);
                            InstanceLevel aligroupLoggerLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, loggerClassCode, true);
                            replica.setDiskSize(aligroupLoggerLevel.getDiskSizeMB() / 1024);

                            replica.setDiskType(srcReplica.getStorageType().toString());
                            replica.setStorageType(srcReplica.getStorageType().toString());
                        } else {
                            replica.setRole("XDB".equals(dbEngine) ? "learner" : role);
                            replica.setClassCode(classCode);
                            replica.setDiskSize(diskSize);
                        }
                        replica.setNodeGroup("mix");
                        replica.setNodeType("logger".equals(role) ? "log" : "normal");
                        replicas.add(replica);
                    }
                }
                replicaSet.setReplicaDtoList(replicas);

                CreateReplicaSetDto response;
                try {
                    response = commonProviderService.getDefaultApi().allocateAddMorePod(replicaSet);
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    logger.error("Allocate more pod resource failed: ", e);
                    throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "AllocateResouceFailed", "Allocate resource failed."});
                }

                List<com.aliyun.apsaradb.activityprovider.model.ReplicaResource> responseMeta = commonProviderService.getDefaultApi().writeMorePod(response);

                /* build and save trans_lsit
                   [
                       {"role": "leader", "sourceReplicaId": 1, "destReplicaId": 2},
                       {"role": "foller", "sourceReplicaId": 3, "destReplicaId": 4}
                   ] */
                List<Map<String, Object>> transInfo = new ArrayList<>();
                ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                        requestId, dbInstanceName, null, null, null, null);
                List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
                Set<Long> newReplicaIds = responseMeta.stream().map(x -> x.getReplica().getId()).collect(Collectors.toSet());
                for (com.aliyun.apsaradb.activityprovider.model.ReplicaResource newReplica : responseMeta) {
                    Map<String, Object> transItem = new HashMap<>();
                    for (Replica currentReplica : currentReplicas) {
                        boolean canSwitchRole = false;
                        if ("XDB".equals(dbEngine)) {
                            canSwitchRole = (!Replica.RoleEnum.LOGGER.equals(currentReplica.getRole()) && !"logger".equals(newReplica.getReplica().getRole().toString())
                                    || Replica.RoleEnum.LOGGER.equals(currentReplica.getRole()) && "logger".equals(newReplica.getReplica().getRole().toString()));
                        }
                        if (!newReplicaIds.contains(currentReplica.getId()) && canSwitchRole) {
                            transItem.put("role", currentReplica.getRole().toString());
                            transItem.put("sourceReplicaId", currentReplica.getId());
                            transItem.put("destReplicaId", newReplica.getReplica().getId());
                            transInfo.add(transItem);
                            currentReplicas.remove(currentReplica);
                            break;
                        }
                    }
                }
                JSONObject transParamObject = new JSONObject();
                transParamObject.put("transInfo", transInfo);
                String transParam = transParamObject.toJSONString();
                TransferTask transList = new TransferTask();
                transList.setSrcReplicaSetName(dbInstanceName);
                transList.setDestReplicaSetName(dbInstanceName);
                transList.setSrcClassCode(replicaSetMeta.getClassCode());
                transList.setDestClassCode(classCode);
                transList.setSrcDiskSizeMB(replicaSet.getDiskSize() * 1024);
                transList.setDestDiskSizeMB(diskSize * 1024);
                transList.setParameter(transParam);
                transList.setType(classCode.equals(replicaSetMeta.getClassCode()) ? TransferTask.TypeEnum.RECOVER : TransferTask.TypeEnum.REMOVE);
                transList.setStatus(0);
                transList.setBakFrom(TransferTask.BakFromEnum.BACKUP);
                transList = dBaasMetaService.getDefaultClient().createTransferTask(requestId, dbInstanceName, transList);
                transListId = transList.getId();
                taskKey = replicaSetResource.getReplicaSet().getInsType().equals(ReplicaSet.InsTypeEnum.READONLY) ? "migrate_read_ins" : "migrate_ins";
            } else {
                // 云盘不能降低磁盘空间
                boolean isOnlyDiskSizeChange = isDiskSizeChange && !isClassCodeChange;
                if (targetDiskSize % 10 != 0) {
                    throw new RdsException(INVALID_CLOUD_DISK_SIZE.toArray());
                }
                diskSize = targetDiskSize;
                classCode = targetClassCode != null ? targetClassCode : classCode;
                String tmpReplicaSetNamePrefix = AliyunModifyDBInstanceService.getTempReplicaSetNamePrefix(requestId, dbInstanceName, orderId, MAX_TMP_INSTANCE_NAME_LENGTH);
                ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                        requestId, dbInstanceName, null, null, null, null);
                List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();

                if (isOnlyDiskSizeChange) {
                    List<ModifyVolumeSpecRequest> modifyVolumeSpecRequests = new ArrayList<>();
                    for (Replica replica : currentReplicas) {
                        // Logger 节点不需要变配
                        if (Replica.RoleEnum.LOGGER.equals(replica.getRole())) {
                            continue;
                        }
                        // FIXME 暂时只变数据盘, 后面支持云盘
                        ModifyVolumeSpecRequest modifyVolumeSpecRequest = new ModifyVolumeSpecRequest();
                        VolumeSpec dataVolumeSpec = new VolumeSpec();
                        dataVolumeSpec.setName("data");
                        dataVolumeSpec.setCategory("data");
                        dataVolumeSpec.setDiskSize(diskSize);
                        modifyVolumeSpecRequest.setRole(replica.getRole().toString());
                        modifyVolumeSpecRequest.setVolumeSpec(dataVolumeSpec);
                        modifyVolumeSpecRequests.add(modifyVolumeSpecRequest);

                    }
                    transListId = commonProviderService.getDefaultApi().makeIncreaseVolumeSizeTransList(requestId, dbInstanceName, modifyVolumeSpecRequests);
                    taskKey = "online_resize_ins";
                } else {
                    ModifyReplicaSetResourceRequest replicaSetResourceRequest = new ModifyReplicaSetResourceRequest();
                    replicaSetResourceRequest.setUserId(bid);
                    replicaSetResourceRequest.setUid(uid);
                    replicaSetResourceRequest.setTmpReplicaSetName(String.format("%s-%s", tmpReplicaSetNamePrefix, dbInstanceName));
                    replicaSetResourceRequest.setDbType(replicaSetMeta.getService());
                    replicaSetResourceRequest.setDbVersion(replicaSetMeta.getServiceVersion());
                    replicaSetResourceRequest.setClassCode(classCode);
                    replicaSetResourceRequest.diskSize(diskSize);
                    replicaSetResourceRequest.setStorageType(diskType);
                    if (aligroupService.isTddlClusterNeedAllocateDedicatedResourceGroup(requestId, tddlClusterName)) {
                        replicaSetResourceRequest.setDedicatedBizGroup(tddlClusterName);
                    }

                    List<ModifyReplicaResourceRequest> replicaRequestList = new ArrayList<>();
                    //检查实例指定指定资源调度模板创建
                    Map<String, String> labels = replicaSetResource.getReplicaSet().getLabels();
                    PodScheduleTemplate podScheduleTemplate = null;
                    if (MapUtils.isNotEmpty(labels)) {
                        String rsTemplateName = labels.get(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME);

                        podScheduleTemplate = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, rsTemplateName, bid + "_" + uid);

                        if (podScheduleTemplate != null) {
                            replicaSetResourceRequest.setScheduleTemplate(podTemplateHelper.getReplicaSetScheduleTemplate(podScheduleTemplate));
                        }
                    }
                    for (Replica replica : currentReplicas) {
                        // Logger 节点不需要变配
                        if (Replica.RoleEnum.LOGGER.equals(replica.getRole())) {
                            continue;
                        }
                        ModifyReplicaResourceRequest replicaRequest = new ModifyReplicaResourceRequest();
                        replicaRequest.setClassCode(classCode);
                        replicaRequest.setRole(replica.getRole().toString());
                        replicaRequest.setDiskSize(diskSize);
                        replicaRequest.setStorageType(diskType);
                        if (podScheduleTemplate != null) {
                            replicaRequest.setScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(podScheduleTemplate, replica.getRole().name()));
                        }
                        // FIXME 暂时只变数据盘, 后面支持云盘
                        List<VolumeSpec> volumeSpecList = new ArrayList<>();
                        VolumeSpec dataVolumeSpec = new VolumeSpec();
                        dataVolumeSpec.setName("data");
                        dataVolumeSpec.setCategory("data");
                        dataVolumeSpec.setDiskSize(diskSize);
                        volumeSpecList.add(dataVolumeSpec);

                        replicaRequest.setVolumeSpecs(volumeSpecList);
                        replicaRequestList.add(replicaRequest);
                    }
                    replicaSetResourceRequest.setReplicaResourceRequestList(replicaRequestList);

                    transListId = commonProviderService.getDefaultApi().allocateReplicaSetResourceForScale(requestId, dbInstanceName, replicaSetResourceRequest);
                    taskKey = "modify_cloud_disk_ins";
                }
            }

            // dispatch task
            Map<String, Object> switchInfoMap = PodCommonSupport.getSwitchInfoTime(switchMode, switchTime);
            taskParamObject.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
            taskParamObject.put("requestId", requestId);
            taskParamObject.put("replicaSetName", dbInstanceName);
            taskParamObject.put("transTaskId", transListId);
            String taskParam = taskParamObject.toJSONString();
            String domain = replicaSetService.isReplicaSetXDB(requestId, dbInstanceName) ? "xdb" : "mysql";
            Object taskId = workFlowService.dispatchTask(
                    "custins", dbInstanceName, domain, taskKey, taskParam, 0);
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, dbInstanceName, ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

            // build response
            Map<String, Object> data = new HashMap<>(7);
            data.put("DBInstanceID", replicaSetMeta.getId());
            data.put("DBInstanceName", dbInstanceName);
            data.put("SourceDBInstanceClass", replicaSetMeta.getClassCode());
            data.put("TargetDBInstanceClass", classCode);
            data.put("TaskId", taskId);
            data.put("Region", regionId);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: ", e);
            return createErrorResponse(new Object[]{ResultCode.CODE_ERROR, "AllocateResouceFailed", "Allocate resource failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
