package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.base.support.MySQLParamConstants.LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_RESTART_MYSQLD;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_RESTART_RESOURCE_NODE;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRecoverNodeImpl")
public class RecoverNodeImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(RecoverNodeImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected KmsService kmsService;
    @Autowired
    protected PodParameterHelper podParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String replicaId;

            // 实例无锁定原因
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (replicaSet.getLockMode() != ReplicaSet.LockModeEnum.NOLOCK &&
                !((Objects.equals(replicaSet.getLockMode(), ReplicaSet.LockModeEnum.DISKFULL) ||
                    Objects.equals(replicaSet.getLockMode(), ReplicaSet.LockModeEnum.READINS_DISKFULL))
                    && Objects.equals(custins.getLockReason(), LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE))) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            // 检查实例状态
            if (!ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSet.getStatus())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            // 检查KMS状态
            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }

            // 检查任务参数是否正确
            String recoverMethod = mysqlParamSupport.getParameterValue(params, "recoverMethod");
            if (!TASK_RESTART_RESOURCE_NODE.equals(recoverMethod) && !TASK_RESTART_MYSQLD.equals(recoverMethod)) {
                throw new RdsException(ErrorCode.INVALID_TASK_NAME);
            }

            // 检查下发restart_resource_node的任务时候 实例是否是单租户实例
            if (TASK_RESTART_RESOURCE_NODE.equals(recoverMethod) && !podParameterHelper.isSingleTenant(replicaSet)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_LEVEL);
            }

            // 检查恢复备节点的传参 必须指定replicaId
            if (!mysqlParamSupport.hasParameter(params, "replicaId")) {
                throw new RdsException(ErrorCode.PARAM_NOT_FOUND, "replicaId");
            }

            // 检查实例是否为集群版实例
            if (!MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // 检查是否存在未完成任务
            if (workFlowService.isTaskExist(requestId, dbInstanceName)) {
                logger.error("{} replicaset {} has unfinished tasks.", requestId, dbInstanceName);
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }

            // 检查replicaId的合法性 只允许操作备节点
            replicaId = mysqlParamSupport.getParameterValue(params, "replicaId");
            List<Replica> replicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, dbInstanceName, null, null, null, null).getItems();
            if (replicaList == null || replicaList.isEmpty()) {
                throw new RdsException(ErrorCode.PARAM_NOT_FOUND, dbInstanceName);
            }
            replicaList.stream()
                .filter(replica -> replicaId.equals(replica.getId().toString()) && Objects.equals(replica.getRole(), Replica.RoleEnum.SLAVE))
                .findFirst()
                .orElseThrow(() -> new RdsException(ErrorCode.PARAM_NOT_FOUND, "replicaId"));

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);
            jsonObject.put("wait_node_timeout", mysqlParamSupport.getParameterValue(params, "wait_node_timeout", "300"));
            jsonObject.put("targetId", replicaId);
            jsonObject.put("notThrowable", true);
            jsonObject.put("checkRole", true);
            String parameter = jsonObject.toJSONString();

            String domain = "mysql";
            String targetType = "replica";
            int priority = PodDefaultConstants.TASK_PRIORITY_VIP;
            Object taskId = workFlowService.dispatchTask(targetType, replicaId, domain, recoverMethod, parameter, priority);
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", dbInstanceName);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
