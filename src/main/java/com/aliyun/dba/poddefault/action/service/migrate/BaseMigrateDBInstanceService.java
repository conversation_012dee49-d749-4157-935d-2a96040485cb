package com.aliyun.dba.poddefault.action.service.migrate;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.MigrateDBInstanceImpl;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
@Service
public abstract class BaseMigrateDBInstanceService {
    @Autowired
    protected MySQLServiceImpl mySQLService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected PodParameterHelper podParameterHelper;
    @Autowired
    protected PodTemplateHelper podTemplateHelper;
    @Autowired
    protected MysqlParamSupport paramSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected KmsService kmsService;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    protected MinorVersionServiceHelper minorVersionServiceHelper;

    protected static final LogAgent logger = LogFactory.getLogAgent(MigrateDBInstanceImpl.class);

    public Object doMigrateDbInstance(Map<String, String> params) throws Exception {
        //step 1: get request id
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        JSONObject taskParam = new JSONObject();
        taskParam.put("requestId", requestId);

        //step 2: get and set switch info
        // 设置切换时间点
        Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);
        taskParam.put(CustinsSupport.SWITCH_KEY, switchInfoMap);

        //step 3: basic check
        ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
        if (!InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSet.getCategory()) &&
                !(mysqlParamSupport.isMysqlXDB(replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode()))) {
            logger.error("db instance is not basic.");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        } else if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
            if (replicaSet.getStatus() == ReplicaSet.StatusEnum.INS_MAINTAINING && paramSupport.isIgnoreMaintainingStatus(params)) {
                logger.info("RequestId: {}, Message: {}", requestId, "Ignoring maintaining status and do migrate ins");
            } else {
                logger.error("db instance is not active.");
                throw new RdsException(ErrorCode.INVALID_STATUS);
            }
        } else if (ReplicaSet.LockModeEnum.NOLOCK != replicaSet.getLockMode()) {
            logger.error("db instance is lock.");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }

        if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
            throw new RdsException(ErrorCode.INVALID_KMS_KEY);
        }
        //step 4: allocate resource
        return allocateResource(requestId, replicaSet, taskParam, params);
    }

    public abstract Object allocateResource(String requestId, ReplicaSet replicaSet,JSONObject taskParam, Map<String, String> params) throws Exception;

    protected PodScheduleTemplate getPodScheduleTemplate(String requestId, ReplicaSet replicaSet) throws Exception {
        User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);
        String bid = user.getBid();
        String uid = user.getAliUid();
        Map<String, String> labels = replicaSet.getLabels();
        if(MapUtils.isNotEmpty(labels)) {
            String rsTemplateName = labels.get(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME);
            return podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, rsTemplateName, bid + "_" + uid);
        }
        return null;
    }
}
