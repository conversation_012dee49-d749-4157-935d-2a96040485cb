package com.aliyun.dba.poddefault.action;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;

import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceServiceImpl;
import com.aliyun.dba.poddefault.action.service.TdeKmsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.user.service.UserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceTDEImpl")
public class ModifyDBInstanceTDEImpl implements IAction {
    @Autowired
    private WorkFlowService workflowService;
    @Autowired
    private PodParameterHelper podParameterHelper;
    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private MysqlEncryptionService mysqlEncryptionService;
    @Autowired
    private TdeKmsService tdeKmsService;
    @Autowired
    private UserServiceImpl userService;
    @Autowired
    private CustinsServiceImpl custinsService;
    @Autowired
    private InstanceServiceImpl instanceService;
    @Autowired
    private KmsApi kmsApi;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = RequestSession.getRequestId();
        try {
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            // check tde kill switch
            if (!tdeKmsService.checkTdeSupported(requestId, regionId)) {
                log.error("TDE not supported in this region {}", regionId);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            // required params
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            // 只支持高可用/集群版标准版系列
            Integer levelId = custins.getLevelId();
            InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(levelId);
            if (!instanceLevelDO.isStandardLevel()&&!instanceLevelDO.isClusterLevel()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            // 只允许操作主实例
            if (custins.getInsType() != CustInsType.CUST_INS_TYPE_PRIMARY.getValue()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            // 主实例和只读实例需为activation状态
            List<String> insNames = new ArrayList<>();
            insNames.add(custins.getInsName());
            List<CustInstanceDO> roCustins = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), false);
            if (roCustins == null) {roCustins = Collections.emptyList();}
            roCustins.forEach(roCustinsDO -> insNames.add(roCustinsDO.getInsName()));
            List<Map<String, Object>> insStatusList = custinsService.getCustinsStatusByCustinsNames(insNames);
            if (!insStatusList.isEmpty()) {
                for (Map<String, Object> insStatus : insStatusList) {
                    Integer status = (Integer)insStatus.get("DBInstanceStatus");
                    if (CustinsState.STATUS_ACTIVATION.getState() != status) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_DB_STATUS);
                    }
                }
            }

            String roleArn = mysqlParamSupport.getRoleArn(params);
            String tdeStatus = podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_TDE_STATUS);
            if (StringUtils.isBlank(regionId) || StringUtils.isBlank(tdeStatus)) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            Integer userId = custins.getUserId();
            String uid = userService.getUserDOByUserId(userId).getLoginId().split("_")[1];
            if (StringUtils.isBlank(roleArn)) {
                roleArn = generateDefaultRoleArn(uid);
            }

            // only support enable
            if (!"1".equals(tdeStatus)) {
                throw new RdsException(ErrorCode.INVALID_TDESTATUS);
            }
            // check dbaas if already enabled
            CustinsParamDO tdeEnabled =
                custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.TDE_ENABLED);
            if (tdeEnabled != null) {
                String tdeEnabledValue = tdeEnabled.getValue();
                if (!StringUtils.isBlank(tdeEnabledValue) && "1".equals(tdeEnabledValue)) {
                    throw new RdsException(ErrorCode.TDESTATUS_ALREADY_CONFIGED);
                }
            }
            // check supported db type/version
            if (!custins.isMysqlGt57()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DB_TYPE);
            }
            // check role authorization
            if (!kmsApi.checkAssumeRoleOk(custins.getClusterName(), roleArn, uid)) {
                throw new RdsException(ErrorCode.TDEPARAM_ERROR_ROLE);
            }

            String kmsKeyId = podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_ENCRYPTION_KEY);
            // TODO: check cross-region backup

            JSONObject workflowParams = new JSONObject();
            String tde_mode;
            if (StringUtils.isBlank(kmsKeyId)) {
                // use service key mode
                kmsKeyId = mysqlEncryptionService.getServiceKey(custins.getClusterName(), uid, userId);
                tde_mode = PodDefaultConstants.TDE_MODE_SERVICE;
            } else {
                // use byok mode
                // need to add permission for the role to perform this action
                //tdeKmsService.setDeletionProtection(custins, roleArn, kmsKeyId, uid);
                tde_mode = PodDefaultConstants.TDE_MODE_BYOK;
            }
            workflowParams.put("category", instanceLevelDO.getCategory());
            workflowParams.put("kmsKeyId", kmsKeyId);
            workflowParams.put("encryptionMode", tde_mode);
            // check kms key availability
            tdeKmsService.checkKeyIsAvailable(custins, roleArn, kmsKeyId, uid);
            tdeKmsService.ensureTagExistence(custins, roleArn, kmsKeyId, uid);
            tdeKmsService.ensureUserRoleArn(custins, roleArn, uid);
            custinsParamService.createCustinsParam(
                new CustinsParamDO(custins.getId(), PodDefaultConstants.TDE_ENCRYPTION_KEY_ID, kmsKeyId));
            custinsParamService.createCustinsParam(new CustinsParamDO(custins.getId(), PodDefaultConstants.TDE_MODE, tde_mode));
            custinsParamService
                .createCustinsParam(new CustinsParamDO(custins.getId(), PodDefaultConstants.TDE_ENABLED, "0"));
            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsState.STATE_TDE_MODIFYING.getState(), CustinsState.STATE_TDE_MODIFYING.getComment());
            for (CustInstanceDO roCustinsItem : roCustins) {
                custinsParamService.createCustinsParam(
                    new CustinsParamDO(roCustinsItem.getId(), PodDefaultConstants.TDE_ENCRYPTION_KEY_ID, kmsKeyId));
                custinsParamService.createCustinsParam(new CustinsParamDO(roCustinsItem.getId(), PodDefaultConstants.TDE_MODE, tde_mode));
                custinsParamService
                    .createCustinsParam(new CustinsParamDO(roCustinsItem.getId(), PodDefaultConstants.TDE_ENABLED, "0"));
                custinsService.updateCustInstanceStatusByCustinsId(roCustinsItem.getId(), CustinsState.STATE_TDE_MODIFYING.getState(),
                                                                   CustinsState.STATE_TDE_MODIFYING.getComment());
            }
            // init workflow to enable tde
            Object taskId = workflowService.dispatchTask(PodDefaultConstants.TARGET_TYPE_CUSTINS, custins.getInsName(),
                                                         custins.getDbType(), PodDefaultConstants.TASK_MODIFY_TDE, workflowParams.toJSONString(),
                                                         WorkFlowService.TASK_PRIORITY_COMMON);
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            log.error(requestId + " RdsException: ", re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            log.error(requestId + " Exception: ", e);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private String generateDefaultRoleArn(String uid) {
        return String.format("acs:ram::%s:role/aliyunrdsinstanceencryptiondefaultrole", uid);
    }

}