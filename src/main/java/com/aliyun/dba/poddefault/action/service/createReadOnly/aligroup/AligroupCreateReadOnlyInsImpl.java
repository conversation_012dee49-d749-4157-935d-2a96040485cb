package com.aliyun.dba.poddefault.action.service.createReadOnly.aligroup;

import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.dba.base.cache.LocalCacheService;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.createReadOnly.BaseCreateReadOnlyInsService;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.base.support.MySQLParamConstants.*;

@Service
public class AligroupCreateReadOnlyInsImpl extends BaseCreateReadOnlyInsService {

    @Resource
    private LocalCacheService cacheService;

    @Override
    public String getTaskKey(CreateReadOnlyInsRequest request) {
        String taskKey = "create_xdb_tddl_read_ins";
        if (CONN_TYPE_PHYSICAL.equalsIgnoreCase(request.getConnType())) {
            taskKey = "create_xdb_read_ins";
        }

        return taskKey;
    }

    @Override
    public void addSpecialInstanceLables(CreateReadOnlyInsRequest request, ReplicaSet readReplicaSet,String composeTag, Map<String, String> labels) {
        if (CONN_TYPE_TDDL.equalsIgnoreCase(request.getConnType())) {
            putLabels(labels, "TddlBiztype", request.getTddlBizType(), false);
            putLabels(labels, "TddlRegionConfig", request.getTddlRegionConfig(), false);
            putLabels(labels, TDDL_TASK_MIGRATE, "true", false);
        }

        if (ReplicaSetService.isStorageTypeCloudDisk(request.getDiskType())) {
            putLabels(labels, "cloud_pfs", "true", false);
        }else {
            putLabels(labels, "cloud_pfs", "false", false);
        }
        putLabels(labels, "nodeCount", String.valueOf(request.getReadInsReplicaCount()), false);
    }

    @Override
    public Boolean getAllocateDiskflag(CreateReadOnlyInsRequest request) {
        return true;
    }

    @Override
    public ReplicaSetResourceRequest buildSpecialAllocateRequest(ReplicaSetResourceRequest replicaSetResourceRequest, CreateReadOnlyInsRequest request) throws ApiException {
        if (aligroupService.isTddlClusterNeedAllocateDedicatedResourceGroup(request.getRequestId(), request.getGdnInstanceName())) {
            replicaSetResourceRequest.setDedicatedBizGroup(request.getGdnInstanceName());
        }
        return replicaSetResourceRequest;
    }

    @Override
    public PodScheduleTemplate getPodScheduleTemplate(CreateReadOnlyInsRequest request) throws Exception {
        String rsTemplateName = request.getRsTemplateName();
        String hack128VolumeTemplcat = cacheService.getValueOrDefault("ALIGROUP_128_VOLUME_SWITCH","off");

        if(hack128VolumeTemplcat.equalsIgnoreCase("on")){
            rsTemplateName = PodDefaultConstants.TEMPLATE_ALIGROUP_128_VOLUME;
        }

        if (Strings.isEmpty(rsTemplateName)) {
            rsTemplateName = PodDefaultConstants.RS_TEMPLATE_USER_PREFIX + request.getGdnInstanceName();
        }
        PodScheduleTemplate podScheduleTemplate = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(request.getRequestId(), rsTemplateName, request.getBid() + "_" + request.getUid());
        return podScheduleTemplate;
    }

    @Override
    public ScheduleTemplate getReplicaSetTemplate(CreateReadOnlyInsRequest request, InstanceLevel instanceLevel) throws Exception {
        return null;
    }

    @Override
    public Boolean getIsENI(CreateReadOnlyInsRequest request) {
        return true;
    }

    @Override
    public void doSpecialCheck(CreateReadOnlyInsRequest request) {
        //TODO
    }


    @Override
    public CreateReadOnlyInsRequest buildSpecialRequest(CreateReadOnlyInsRequest request, Map<String, String> params) throws RdsException, ApiException, com.aliyun.apsaradb.gdnmetaapi.ApiException {
        String tddlBizType = paramSupport.getParameterValue(params, "TddlBizType");
        request.setTddlBizType(tddlBizType);

        String tddlRegionConfig = paramSupport.getParameterValue(params, "TddlRegionConfig");
        request.setTddlRegionConfig(tddlRegionConfig);

        AVZInfo avzInfo = avzSupport.getAVZInfo(params);
        String regionId = avzInfo.getRegionId();

        int readInsReplicaCount = getNodeCount(request);
        request.setReadInsReplicaCount(readInsReplicaCount);
        if (request.getIsXdbEngine()) {
            replicaSetService.mazPreCheckForReadIns(params, request.getDbEngine(), readInsReplicaCount);
            avzInfo = avzSupport.getAVZInfo(params);
        }
        request.setAvzInfo(avzInfo);
        request.setRegionId(regionId);

        request.setIsSingleTenant(false);
        String clusterName = request.getClusterName() != null ? request.getClusterName() : String.format(ALIGROU_DHG_PARTERN, request.getRegionId());
        request.setClusterName(clusterName);
        return request;
    }

    /**
     * 只有开关打开，且TDDL链路云盘&physical本地盘才是双节点只读
     * @param request
     * @return
     * @throws RdsException
     */
    @Override
    public Integer getNodeCount(CreateReadOnlyInsRequest request) throws RdsException, com.aliyun.apsaradb.gdnmetaapi.ApiException, ApiException {
        String nodeCount = cacheService.getValue("CREATE_ALIGROUP_READONLY_NODE_COUNT");
        if( StringUtils.isEmpty(nodeCount) || !"2".equals(nodeCount)){
            return 1;
        }
        if(CONN_TYPE_TDDL.equals(request.getConnType()) && request.getDiskType().startsWith("cloud")){
            //tddl 存在单节点只读则目前新建只读只能是单节点
            List<InstanceMember>  instanceMembers = gdnInstanceService.getGdnMembers(request.getRequestId(),request.getGdnInstanceName(),request.getPrimaryReplicaSet().getName());
            if(instanceMembers.size() >1){
                for(InstanceMember instanceMember : instanceMembers){
                    if(!ReplicaSet.InsTypeEnum.MAIN.toString().equals(instanceMember.getRole()) &&
                    !replicaSetService.isAligroupDoubleNodeRead(request.getRequestId(),instanceMember.getMemberRegion(),instanceMember.getMemberName())){
                        return 1;
                    }
                }
            }
            return  2;
        }
        if(CONN_TYPE_PHYSICAL.equals(request.getConnType())){
            return  2;
        }
        return 1;
    }
}
