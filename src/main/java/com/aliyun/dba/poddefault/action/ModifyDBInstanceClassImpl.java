package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamServiceImpl;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.AligroupModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.*;
import com.aliyun.dba.poddefault.action.service.TransferK8sToPhysicalService;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.serverless.action.service.ServerlessUpgradeService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.service.TaskGrayService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceClassImpl")
public class ModifyDBInstanceClassImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceClassImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Autowired
    private ReplicaSetService replicaSetService;
    @Resource
    private AliyunModifyDBInstanceService aliyunModifyDBInstanceService;
    @Resource
    private AligroupModifyDBInstanceService aligroupModifyDBInstanceService;

    @Resource
    private TaskGrayService taskGrayService;

    @Resource
    private TransferK8sToPhysicalService transferK8sToPhysicalService;

    @Resource
    private ModifyDBInstanceFromBasicToStandardService modifyDBInstanceFromBasicToStandardService;
    @Resource
    private ModifyDBInstanceDiskShrinkService modifyDBInstanceDiskShrinkService;
    @Resource
    private ModifyDBInstanceDiskShrinkServiceV2 modifyDBInstanceDiskShrinkServiceV2;
    @Resource
    private ModifyDBInstanceFromBasicToClusterService modifyDBInstanceFromBasicToClusterService;

    @Resource
    private ModifyDBInstanceCpuArchService modifyDBInstanceCpuArchService;
    @Resource
    private ModifyDBInstanceFromStandardToClusterService modifyDBInstanceFromStandardToClusterService;


    @Resource
    private ServerlessUpgradeService serverlessUpgradeService;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;

    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    private CustinsParamServiceImpl custinsParamService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        ReplicaSet replicaSetMeta;
        boolean isTddlTaskMigrate;
        String compressionMode;
        Double compressionRatio;
        try {
            replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!(ReplicaSet.StatusEnum.ACTIVE.equals(replicaSetMeta.getStatus()) ||
                    ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSetMeta.getStatus()))) {
                throw new RdsException(ErrorCode.INVALID_STATUS);
            }

            isTddlTaskMigrate = replicaSetService.isTddlTaskMigrate(requestId, replicaSetMeta);
            compressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, replicaSetMeta.getName(), null);
            compressionRatio = cloudDiskCompressionHelper.getCompressionRatio(requestId, replicaSetMeta.getName(), null, null, null);
        } catch (ApiException e) {
            logger.error(e.getMessage(), e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }

        // 磁盘缩容（允许同系列规格/云盘等级变化，允许磁盘容量变小）
        String storage = mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE);
        // If maxCloudStorageSize not exist, then set its value to 32768 GB.
        int maxCloudStorageSize = podCommonSupport.getMaxCloudStorageSizeConfig(32768, false);
        if (StringUtils.isNotEmpty(storage)) {
            logger.info("Modify DB Class requestId: {},instance storage is modifying to {} GB and cloudMaxStorageSize is {} ", requestId, storage, maxCloudStorageSize);
            Integer targetDiskSize = CheckUtils.parseInt(storage, CustinsSupport.ESSD_MIN_DISK_SIZE, maxCloudStorageSize, ErrorCode.INVALID_STORAGE);
            Integer srcDiskSize = replicaSetMeta.getDiskSizeMB() / 1024;
            if (CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
                targetDiskSize = CloudDiskCompressionHelper.getLogicalSize(targetDiskSize, compressionRatio);
            }
            if (targetDiskSize < srcDiskSize) {
                if (!podCommonSupport.isSupportShrinkV2()) {
                    return modifyDBInstanceDiskShrinkService.doActionRequest(custins, params);
                }
                return modifyDBInstanceDiskShrinkServiceV2.doActionRequest(custins, params);
            }
        }


        // 没达标的集团TDDL实例走 xdb provider
        if (ReplicaSetService.isTDDL(replicaSetMeta) && !isTddlTaskMigrate) {
            return aligroupModifyDBInstanceService.doActionRequest(custins, params);
        }

        /* --------------START 升级产品系列（基础版to高可用 or 高可用to集群版 or 基础版to集群版）-------------- */
        String levelCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
        if (levelCode != null) {
            String dbVersion = replicaSetMeta.getServiceVersion();

            InstanceLevelDO newLevel = instanceService.getInstanceLevelByClassCode(levelCode, replicaSetMeta.getService(), dbVersion, null, null);
            String oldClassCode = replicaSetMeta.getClassCode();
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByClassCode(oldClassCode, replicaSetMeta.getService(), dbVersion, null, null);
            //x86 arm 变配
            if(PodCommonSupport.isArchChange(oldLevel.getExtraInfo(), newLevel.getExtraInfo())){
                logger.info("Detect arch change for oldLevel:{}, newLevel:{}", oldLevel, newLevel);
                return modifyDBInstanceCpuArchService.doActionRequest(custins, params);
            }

            if (newLevel != null) {
                if (newLevel.getHostType() == 0
                        && "5.7".equals(replicaSetMeta.getServiceVersion())
                        && InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSetMeta.getCategory())
                        && InstanceLevel.CategoryEnum.STANDARD.toString().equals(newLevel.getCategory())) {
                    checkTDEEnabled(custins, params);
                    return transferK8sToPhysicalService.doActionRequest(custins, params);
                }

                if (InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSetMeta.getCategory())
                        && InstanceLevel.CategoryEnum.STANDARD.toString().equals(newLevel.getCategory())) {
                    checkTDEEnabled(custins, params);
                    if(ReplicaSet.InsTypeEnum.READONLY == replicaSetMeta.getInsType()){
                        throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
                    }
                    // 单变双
                    return modifyDBInstanceFromBasicToStandardService.doActionRequest(custins, params);
                }
                // 基础版实例升级为集群版实例
                if (InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSetMeta.getCategory())
                        && InstanceLevel.CategoryEnum.CLUSTER.toString().equals(newLevel.getCategory())) {
                    checkTDEEnabled(custins, params);
                    return modifyDBInstanceFromBasicToClusterService.doActionRequest(custins, params);
                }

                // provision 转 serverless
                if (serverlessUpgradeService.isProvisionToServerless(replicaSetMeta, newLevel.getCategory())) {
                    checkTDEEnabled(custins, params);
                    return serverlessUpgradeService.migrateProvisionToServerless(params);
                }

                //只读单节点变配高可用以及高可用变只读单节点不支持
                if (InstanceLevel.CategoryEnum.BASIC.toString().equals(newLevel.getCategory())
                        && InstanceLevel.CategoryEnum.STANDARD.toString().equals(replicaSetMeta.getCategory())
                        && ReplicaSet.InsTypeEnum.READONLY == replicaSetMeta.getInsType()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
                }

                // 高可用实例升级为集群版实例
                if (InstanceLevel.CategoryEnum.STANDARD.toString().equals(replicaSetMeta.getCategory()) &&
                        InstanceLevel.CategoryEnum.CLUSTER.toString().equals(newLevel.getCategory())) {
                    checkTDEEnabled(custins, params);
                    return modifyDBInstanceFromStandardToClusterService.doActionRequest(custins, params);
                }
            }
        }
        /* --------------END 升级产品系列（基础版to高可用 or 高可用to集群版 or 基础版to集群版）-------------- */

        return aliyunModifyDBInstanceService.doActionRequest(custins, params);
    }

    public void checkTDEEnabled(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.TDE_ENABLED);

        if (custinsParamDO != null && StringUtils.equalsIgnoreCase(custinsParamDO.getValue(), "1")) {
            logger.info("TDE enabled, doesn't support change level");
            throw new RdsException(ErrorCode.UNSUPPORTED_DB_TDE_STATUS);
        }

    }
}
