package com.aliyun.dba.poddefault.action;


import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.modifyConnType.BaseModifyInsConnTypeService;
import com.aliyun.dba.poddefault.action.service.modifyConnType.aligroup.ModifyTddlConnToPhysicalImpl;
import com.aliyun.dba.poddefault.action.service.modifyConnType.request.ModifyConnTypeRequest;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceConnTypeImpl")
public class ModifyDBInstanceConnTypeImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceConnTypeImpl.class);

    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private ReplicaSetService replicaSetService;



    private static Map<Map<String,String>, BaseModifyInsConnTypeService> baseModifyInsConnTypeServiceHashMap = Maps.newHashMap();
    static {
        baseModifyInsConnTypeServiceHashMap.put(ImmutableMap.of(ReplicaSet.ConnTypeEnum.TDDL.getValue(),ReplicaSet.ConnTypeEnum.PHYSICAL.getValue()),
                SpringContextUtil.getBeanByClass(ModifyTddlConnToPhysicalImpl.class));
    }

    /**
     * 修改实例ConnType
     *
     * @category CreateReadDBInstance
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            //step 1: set params thread local
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);

            //step 2: build modify conn type request
            ModifyConnTypeRequest modifyConnTypeRequest = buildCommonRequest(params);

            //step 3: modify conn type
            Object result = baseModifyInsConnTypeServiceHashMap.get(ImmutableMap.of(modifyConnTypeRequest.getCurConnType(),modifyConnTypeRequest.getDestConnType())).doModifyConnType(modifyConnTypeRequest,params);

            if(result instanceof Map){
                return (Map<String, Object>) result;
            }

            //step 4: build response
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", result);
            data.put("DBInstanceName", modifyConnTypeRequest.getDbInstanceName());
            return data;
        }catch (RdsException e) {
            logger.error("modifyConnType failed!", e);
            return createErrorResponse(e.getErrorCode());
        } catch (Exception e) {
            logger.error("modifyConnType failed!", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * build common request
     * @param params
     * @return
     * @throws RdsException
     * @throws com.aliyun.apsaradb.dbaasmetaapi.ApiException
     * @throws com.aliyun.apsaradb.gdnmetaapi.ApiException
     */
    private ModifyConnTypeRequest buildCommonRequest(Map<String, String> params) throws RdsException, ApiException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        Integer userId = mysqlParameterHelper.getAndCreateUserId();

        String dbInstanceName = paramSupport.getDBInstanceName(params);
        String curConnType = paramSupport.getCurConnType(params);
        String destConnType = paramSupport.getDestConnType(params);
        ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
        return ModifyConnTypeRequest.builder()
                .dbInstanceName(dbInstanceName)
                .curConnType(curConnType)
                .destConnType(destConnType)
                .requestId(requestId)
                .userId(userId)
                .replicaSet(replicaSet)
                .build();
    }
}