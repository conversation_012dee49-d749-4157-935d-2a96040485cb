package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.model.ConnStringBody;
import com.aliyun.dba.adb_vip_manager_client.model.ConnectionAddrWithStatus;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.PodAvzSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateDBInstanceNetTypeImpl")
public class CreateDBInstanceNetTypeImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateDBInstanceNetTypeImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private LinksApi linksApi;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private PodAvzSupport podAvzSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        // 标记整个请求是否处理成功
        boolean isSuccess = false;
        String replicaSetName = null;
        String connStrAllocated= null;

        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            // 为集群版形态 链路管理 适配已有接口
            if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                List<Endpoint> endpointList = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null).getItems()
                        .stream().filter(ep -> ep.getType() == Endpoint.TypeEnum.NORMAL).collect(Collectors.toList());
                if (endpointList.size() != 1) {
                    throw new RdsException(ErrorCode.INVALID_ENDPOINT_NUM);
                }
                Endpoint primaryEndpoint = endpointList.get(0);
                if (primaryEndpoint.getEndpointGroupId() == null) {
                    throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
                }

                EndpointGroup endpointGroup = dBaasMetaService.getDefaultClient().getEndpointGroupById(requestId, primaryEndpoint.getEndpointGroupId(), false);
                params.put(ParamConstants.CONN_ADDR_PREFIX.toLowerCase(), paramSupport.getConnectionString(params));
                params.put("DBInstanceEndpointId".toLowerCase(), endpointGroup.getGroupName());
                params.put("IPType".toLowerCase(), NetTypeEnum.PUBLIC.toString());
                CreateDBInstanceEndpointAddressImpl createDBInstanceEndpointAddress = SpringContextUtil.getBeanByClass(CreateDBInstanceEndpointAddressImpl.class);

                return createDBInstanceEndpointAddress.doActionRequest(null, params);
            }
            // 检查是否修改次数超过上限
            // TODO: change checkConnAddrChangeTimesExceed custins id to Long
            custinsService.checkConnAddrChangeTimesExceed(Integer.parseInt(replicaSet.getId().toString()), paramSupport.getAction(params), null);
            Optional<Replica> replica = metaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems().stream().filter(r -> r.getRole().equals(Replica.RoleEnum.MASTER)).findFirst();
            if (!replica.isPresent()){
                throw new RdsException(ErrorCode.HOST_INSTANCE_NOT_FOUND);
            }

            Integer netType = paramSupport.getNetType(params);
            NetTypeEnum netTypeEnum = PodDefaultConstants.NET_TYPE_ENUM_MAP.get(netType);
            if (netTypeEnum == null || netTypeEnum.equals(NetTypeEnum.CLASSIC)) {
                throw new RdsException(ErrorCode.ENDPOINT_TYPE_NOT_SUPPORT);
            } else if (NetTypeEnum.PUBLIC.equals(netTypeEnum) && podAvzSupport.isCloudBoxAz(replicaSet.getResourceGroupName())) {
                logger.error("RequestId {} custins is cloudbox, un-support public connection.", requestId);
                throw new RdsException(ErrorCode.ENDPOINT_TYPE_NOT_SUPPORT);
            }

            EndpointListResult endpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null);
            final NetTypeEnum finalNetTypeEnum = netTypeEnum;
            Optional<Endpoint> endpointExist = endpointListResult.getItems().stream().filter(e -> e.getUserVisible() && (e.getNetType().ordinal() == finalNetTypeEnum.ordinal())).findFirst();
            if(endpointExist.isPresent()){
                throw new RdsException(ErrorCode.OTHER_ENDPOINT_EXIST);
            }

            String port = paramSupport.getPort(params);
            if (port != null ) {
                port = CheckUtils.parseInt(port, 1000, 65534, ErrorCode.INVALID_PORT).toString();
            } else {
                port = "3306";
            }

            String connectionString = paramSupport.getConnectionString(params);
            CheckUtils.checkValidForConnAddrCust(connectionString);
            // 预占用连接串
            try {
                ConnStringBody connStringBody = new ConnStringBody();
                connStringBody.setConnectionPrefix(connectionString);
                ConnectionAddrWithStatus connectionAddrWithStatus  = linksApi.allocateConnectionAddress(replicaSet.getName(), connStringBody, requestId);
                logger.info(connectionAddrWithStatus.toString());
                if (connectionAddrWithStatus.getStatus() == 200) {
                    replicaSetName = replicaSet.getName();
                    connStrAllocated = connectionAddrWithStatus.getData().getConnectionAddr();
                } else {
                    throw new RdsException(ErrorCode.OTHER_ENDPOINT_EXIST);
                }
            } catch (com.aliyun.dba.adb_vip_manager_client.ApiException e) {
                logger.error("allocated connection string failed.", e);
                // 版本兼容
                if (e.getCode() == 405) {
                    logger.info("Allocate Conn String Method Not Allowed, ignore it.");
                } else {
                    logger.error("Maybe Connection prefix has already been used.");
                    throw new RdsException(ErrorCode.OTHER_ENDPOINT_EXIST);
                }
            }

            // 分配VIP资源
            EndPoint endPoint = new EndPoint();
            endPoint.setNetType(netTypeEnum);
            endPoint.setConnType(EndPoint.ConnTypeEnum.LVS);
            endPoint.setDomainPrefix(connectionString);
            endPoint.setEndPointType(EndPoint.EndPointTypeEnum.NORMAL);
            endPoint.setPort(port);
            EndPoint allocateEndPointResult = commonProviderService.getDefaultApi().allocateEndPointResource(requestId, replicaSet.getName(), endPoint);

            // 创建Change log
            EndpointChangeLog endpointChangeLog = new EndpointChangeLog();
            Integer operatorId = paramSupport.getOperatorId(params);
            endpointChangeLog
                    .taskId(0)
                    .creator(operatorId)
                    .modifier(operatorId)
                    .replicaId(replica.get().getId())
                    .fromUserVisible(true)
                    .toUserVisible(true)
                    .action(EndpointChangeLog.ActionEnum.ADD)
                    .toConnAddrCust(allocateEndPointResult.getDomainPrefix())
                    .toVip(allocateEndPointResult.getIp())
                    .toVport(port)
                    .netType(EndpointChangeLog.NetTypeEnum.valueOf(netTypeEnum.name()))
                    .rwType(EndpointChangeLog.RwTypeEnum.NORMAL)
                    .status(EndpointChangeLog.StatusEnum.CREATING);
            EndpointChangeLog changeLogCreated = metaService.getDefaultClient().createReplicaSetEndpointChangeLog(requestId,replicaSet.getName(), endpointChangeLog);

            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put("changeLogId", changeLogCreated.getId());     //任务只能针对当前生成的changeLog去处理
            String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            domain = StringUtils.equalsIgnoreCase(PodDefaultConstants.DOMAIN_XDB, domain) ? domain : replicaSet.getService();
            String taskKey = PodDefaultConstants.TASK_MODIFY_ENDPOINT;
            Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, taskParam.toString(), 0);
            isSuccess = true;
            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_CREATING.toString());

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("DBInstanceNetType", allocateEndPointResult.getNetType());
            data.put("ConnectionString", connStrAllocated);
            data.put("Port", allocateEndPointResult.getPort());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        }  catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        }  catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isSuccess && connStrAllocated != null) {
                try {
                    linksApi.releaseConnectionAddress(replicaSetName, connStrAllocated, requestId);;
                } catch (Exception e) {
                    logger.error("clear connection string allocate failed!", e);
                }
            }
        }
    }
}
