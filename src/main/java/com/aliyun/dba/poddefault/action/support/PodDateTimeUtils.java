package com.aliyun.dba.poddefault.action.support;

import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateSupport;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.SimpleTimeZone;
import java.util.TimeZone;


@Component
public class PodDateTimeUtils {
    public static final String ISO8601_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    public static final String DATE_FORMAT_LOCAL = "yyyy-MM-dd HH:mm:ss";

    public String convert2UTCStr(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(ISO8601_DATE_FORMAT);
        simpleDateFormat.setTimeZone(new SimpleTimeZone(0, "GMT"));
        return simpleDateFormat.format(date);
    }

    public Date getUTCDateByDateStr(String utcDateStr) throws Exception {
        if (!DateSupport.issecond_utc(utcDateStr)) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        } else {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(ISO8601_DATE_FORMAT);
            simpleDateFormat.setTimeZone(new SimpleTimeZone(0, "GMT"));
            return simpleDateFormat.parse(utcDateStr);
        }
    }

    public static Date getTodayStartTime() {
        return parseDate(formatehYmd(new Date()) + " " + "00:00:00", null);
    }

    public static Date parseDate(String date, String dateformat) {
        SimpleDateFormat parser;
        if (StringUtils.isBlank(dateformat)) {
            dateformat = DATE_FORMAT_LOCAL;
        }
        parser = new SimpleDateFormat(dateformat);
        if (ISO8601_DATE_FORMAT.equalsIgnoreCase(dateformat)) {
            parser.setTimeZone(TimeZone.getTimeZone("UTC"));
        }
        Date rtn = null;
        try {
            rtn = parser.parse(date);
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }
        return rtn;
    }

    public static String formatehYmd(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    /**
     * 把locale date转换成GMT(UTC)时间
     * 按指定的dateFormat参数格式输出
     * param date
     * param String dateFormat, 输出日期格式, "yyyy-MM-dd HH:mm:ss"
     * return
     */
    public static String convertDateToGMT(Date date, String dateFormat) {
        if (null == date) {
            return null;
        }
        String result = null;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
            simpleDateFormat.setTimeZone(new SimpleTimeZone(0, "GMT"));
            result = simpleDateFormat.format(date);
        } catch (Exception e) {
        }
        return result;
    }

}
