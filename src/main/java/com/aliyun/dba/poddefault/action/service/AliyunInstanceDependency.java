package com.aliyun.dba.poddefault.action.service;

import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.cache.LocalCacheService;
import com.aliyun.dba.base.lib.*;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.service.CustinsParamGroupsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.dockerdefault.service.CloudSSDEncryptionService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.service.ClusterBackUpService;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.service.KmsService;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.host.idao.ClusterIDao;

import javax.annotation.Resource;

@Component
public class AliyunInstanceDependency {
    @Getter
    @Resource
    private ReplicaSetService replicaSetService;
    @Getter
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Getter
    @Resource
    private PodAvzSupport avzSupport;
    @Getter
    @Resource
    private CommonProviderService commonProviderService;
    @Getter
    @Resource
    private WorkFlowService workFlowService;
    @Getter
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Getter
    @Resource
    private MySQLAvzService mySQLAvzService;
    @Getter
    @Resource
    private PodParameterHelper podParameterHelper;
    @Getter
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Getter
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Getter
    @Resource
    private PodCommonSupport podCommonSupport;
    @Getter
    @Resource
    private CustinsParamService custinsParamService;
    @Getter
    @Resource
    private ResourceService resourceService;
    @Getter
    @Resource
    private ClusterBackUpService clusterBackUpService;
    @Getter
    @Resource
    protected BakService bakService;
    @Getter
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Getter
    @Resource
    private BackupService backupService;
    @Getter
    @Resource
    private DbsGateWayService dbsGateWayService;
    @Getter
    @Resource
    private AligroupService aligroupService;
    @Getter
    @Resource
    private LocalCacheService cacheService;
    @Getter
    @Resource
    private CheckService checkService;

    @Getter
    @Resource
    private CloudSSDEncryptionService cloudSSDEncryptionService;

    @Getter
    @Resource
    private KmsService kmsService;

    @Getter
    @Resource
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;

    @Getter
    @Resource
    private ServerlessResourceService serverlessResourceService;

    @Getter
    @Resource
    private InstanceService instanceService;

    @Getter
    @Resource
    private CustinsService custinsService;

    @Getter
    @Resource
    private ConnAddrService connAddrService;

    @Getter
    @Resource
    private ColdDataService coldDataService;

    @Getter
    @Resource
    private MySQLServiceImpl mySQLservice;

    @Getter
    @Resource
    private MysqlEncryptionService mysqlEncryptionService;

    @Getter
    @Resource
    private ClusterIDao clusterIDao;

    @Getter
    @Resource
    private KmsApi kmsApi;

    @Getter
    @Resource
    private RundPodSupport rundPodSupport;

    @Getter
    @Resource
    private CustinsParamGroupsService custinsParamGroupsService;


    @Getter
    @Resource
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;
}
