package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.RebuildReplicaResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper.TASK_KEY_MODIFY_COMPRESSION;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

@Slf4j
@Service
public class CompressionModeService {

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private MySQLServiceImpl mySQLService;

    @Resource
    protected ReplicaSetService replicaSetService;

    @Resource
    protected RundPodSupport rundPodSupport;

    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Resource
    private CommonProviderService commonProviderService;

    @Resource
    private CustinsService custinsService;

    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private PodCommonSupport podCommonSupport;

    public Map<String, Object> modifyCompressionMode(PodModifyInsParam modifyInsParam) throws Exception {
        Map<String, Object> data = new HashMap<>();
        if(!checkModifyInsParam(modifyInsParam)){
            // todo: modify error code
            log.error("modifyInsParam checkModifyInsParam false, cannot modifyCompressionMode.");
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
        String requestId = modifyInsParam.getRequestId();
        String replicaSetName = modifyInsParam.getDbInstanceName();
        RebuildReplicaResourceRequest allocateReplicaResource = null;
        boolean isSuccess = false;
        try {
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSetName, null);
            Replica masterReplica = mySQLService.getReplicaByRole(requestId, replicaSetName, Replica.RoleEnum.MASTER);
            ReplicaResource masterReplicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, masterReplica.getId(), null);
            PodType podType = podCommonSupport.getReplicaRuntimeType(masterReplicaResource);
            // 申请资源
            allocateReplicaResource = podReplicaSetResourceHelper.allocateRebuildResource4Basic(replicaSet, RebuildReplicaResourceRequest.RebuildModeEnum.MIGRATE, podType);
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                rundPodSupport.completeReplicaNetworkConfig(allocateReplicaResource, masterReplica);
            }

            commonProviderService.getDefaultApi().allocateReplicaSetResourceForRebuild(
                    requestId, replicaSet.getName(), masterReplica.getId(), allocateReplicaResource);
            CustInstanceDO custInstanceDO = custinsService.getCustInstanceByInsName(
                    null, allocateReplicaResource.getTmpReplicaSetName(), CUSTINS_INSTYPE_TMP);
            if (custInstanceDO == null) {
                log.error("Cannot find tmp custins [{}]", allocateReplicaResource.getTmpReplicaSetName());
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            custInstanceDO.setInsType(CUSTINS_INSTYPE_MIRROR);
            custInstanceDO.setGmtModified(new Date());
            custinsService.updateCustInstance(custInstanceDO);

            // 只读实例配置白名单同步label
            podParameterHelper.updateReadInsLabels(requestId, replicaSet, allocateReplicaResource.getTmpReplicaSetName());

            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, allocateReplicaResource.getTmpReplicaSetName(), null, null, null, null);
            Replica destReplica = replicaListResult.getItems().get(0);

            // 下发任务 & 更新状态
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", requestId);
            taskParamObject.put("srcReplicaSetName", replicaSetName);
            taskParamObject.put("srcParentReplicaSetName", replicaSet.getPrimaryInsName());
            taskParamObject.put("srcReplicaId", masterReplica.getId());
            taskParamObject.put("destReplicaSetName", allocateReplicaResource.getTmpReplicaSetName());
            taskParamObject.put("destReplicaId", destReplica.getId());

            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSet.getName(), "mysql", TASK_KEY_MODIFY_COMPRESSION, taskParamObject.toJSONString(), 0);
            isSuccess = true;
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

            // build response
            data.put("DBInstanceName", replicaSetName);
            data.put("TaskId", taskId);
            return data;
        } catch (Exception e) {
            log.error("{} Exception: ", requestId, e);
            throw e;
        } finally {
            // 处理失败时释放资源
            if (null != allocateReplicaResource && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, allocateReplicaResource.getTmpReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    log.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }


    private boolean checkModifyInsParam(PodModifyInsParam modifyInsParam) {
        if (Objects.isNull(modifyInsParam)) {
            log.warn("modifyInsParam is null, skip modifyCompressionMode");
            return false;
        }
        if (!modifyInsParam.isCompressionModeChange()) {
            log.warn("modifyInsParam compression mode not change, skip modifyCompressionMode");
            return false;
        }
        if (!CloudDiskCompressionHelper.isCompressionModeOn(modifyInsParam.getTargetCompressionMode())) {
            log.warn("modifyInsParam target compression mode not on, skip modifyCompressionMode");
            return false;
        }
        return true;
    }

}
