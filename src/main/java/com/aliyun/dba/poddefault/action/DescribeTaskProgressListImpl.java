package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeTaskProgressListImpl")
public class DescribeTaskProgressListImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeTaskProgressListImpl.class);

    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private WorkFlowService workFlowService;


    private final static Map<String, Integer> TASK_STATUS_MAPPING = new HashMap<String, Integer>() {{
        this.put("SCHEDULABLE", 0);
        this.put("RUNNING", 1);
        this.put("COMPLETED", 2);
        this.put("HUMAN_PROCESSING", 3);
        this.put("FAILED", 4);
    }};


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        try {
            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            Integer pageNo = mysqlParamSupport.getAndCheckPageNo(params);
            Integer pageSize = Math.min(mysqlParamSupport.getAndCheckPageSize(params), 5);
            String startTime =  mysqlParamSupport.getParameterValue(params, "starttime");
            String endTime =  mysqlParamSupport.getParameterValue(params, "endtime");
            if (DateSupport.str2date(startTime).getTime() > DateSupport.str2date(endTime).getTime()) {
                throw new RdsException(ErrorCode.INVALID_PARAMETER_COMBINATION); //结束时间必须大于开始时间
            }
            List<String> statusIn = Lists.newArrayList(mysqlParamSupport.getParameterValue(params, "status", "0,1").split(","));

            Object taskRetObj = workFlowService.getTaskQueueControllerApi().getTaskSimpleListUsingGET(requestId, null, replicaSetMeta.getName(),
                    "custins", null, null, "mysql", null, null, startTime, endTime, pageNo - 1, pageSize);

            Map<String, Object> taskRet = parseRet(taskRetObj);

            List<Map<String, Object>> taskList = (List<Map<String, Object>>) taskRet.get("taskList");

            List<Map<String, Object>> taskListRet = new ArrayList<>();
            for (Map<String, Object> task : taskList) {
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("BeginTime", task.get("taskBegin"));
                taskInfo.put("FinishTime", task.get("taskEnd"));
                taskInfo.put("TaskId", task.get("id"));
                taskInfo.put("Status", TASK_STATUS_MAPPING.get(task.get("status")));

                String workflowId = task.get("taskFlow") == null ? null : task.get("taskFlow").toString();
                Object workflowRetObj = workFlowService.getDefaultClient().getWorkflowApi().findByWorkflowIdUsingGET(workflowId, requestId, false);
                Map<String, Object> workflowRet = parseRet(workflowRetObj);
                Map<String, Object> workflowInstance = workflowRet.get("workflowInstance") == null ? Collections.EMPTY_MAP : (Map<String, Object>) workflowRet.get("workflowInstance");
                taskInfo.put("Progress", workflowInstance.getOrDefault("progress", 0));
                taskListRet.add(taskInfo);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("DBInstanceID", replicaSetMeta.getId());
            result.put("DBInstanceName", replicaSetMeta.getName());
            result.put("StartTime", mysqlParamSupport.getParameterValue(params, "starttime"));
            result.put("EndTime", mysqlParamSupport.getParameterValue(params, "endtime"));
            result.put("TotalRecords", taskList.size());
            result.put("MaxRecordsPerPage", pageSize);
            result.put("PageNumbers", pageNo);
            result.put("Tasks", taskListRet);

            return result;
        } catch (RdsException e) {
            return createErrorResponse(e.getErrorCode());
        } catch (Exception e) {
            logger.error(requestId + " Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    private Map<String, Object> parseRet(Object ret) throws RdsException {
        Map<String, Object> taskMap = ret == null ? Collections.EMPTY_MAP : (Map<String, Object>) ret;
        if (taskMap.isEmpty()) {
            throw new RdsException(ErrorCode.TASK_NOT_FOUND);
        }
        if (!StringUtils.equalsIgnoreCase("success", taskMap.get("code").toString())) {
            logger.error("errMsg is {}", taskMap.get("message"));
            throw new RdsException(ErrorCode.TASK_NOT_FOUND);
        }
        Map<String, Object> data = taskMap.get("data") == null ? Collections.emptyMap() : (Map<String, Object>) taskMap.get("data");
        if (data.isEmpty()) {
            throw new RdsException(ErrorCode.TASK_NOT_FOUND);
        }
        return data;
    }


}
