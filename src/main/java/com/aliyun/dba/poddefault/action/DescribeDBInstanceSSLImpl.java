package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.model.CertConfig;
import com.aliyun.dba.base.common.consts.SSLConsts;
import com.aliyun.dba.base.service.CAServerApiExt;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.entity.MycnfTemplate;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeDBInstanceSSLImpl")
@Slf4j
public class DescribeDBInstanceSSLImpl implements IAction {

    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private CaServerApi caServerApi;
    @Resource
    private CAServerApiExt caServerApiExt;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> map) throws RdsException {
        custins = mysqlParamSupport.getAndCheckCustInstance(map);
        String requestId = mysqlParamSupport.getParameterValue(map, ParamConstants.REQUEST_ID);
        CustinsParamDO insSslConfig = custinsParamService.getCustinsParam(
                custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL);
        boolean sslEnabled = false;
        if (insSslConfig != null) {
            if (insSslConfig.getValue().equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SSL)) {
                sslEnabled = true;
            }
        }
        CustinsParamDO insSSLForceEncryption = custinsParamService.getCustinsParam(
                custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_FORCE_ENCRYPTION);
        String forceEncryption = insSSLForceEncryption != null ? insSSLForceEncryption.getValue() : "0";
        String caType = SSLConsts.CA_TYPE_ALIYUN;
        CustinsParamDO caTypeConfig = custinsParamService.getCustinsParam(
                custins.getId(), SSLConsts.CUSTINS_PARAM_NAME_CA_TYPE);
        if (caTypeConfig != null) {
            caType = caTypeConfig.getValue();
        }

        Map<String, Object> data = new HashMap<String, Object>();
        try {
            String commonName = "";
            String notAfter = "";
            if (sslEnabled) {
                if (StringUtils.equalsIgnoreCase(caType, SSLConsts.CA_TYPE_CUSTOM)) {
                    CertConfig certConfig = caServerApiExt.getInstanceCustomServerCert(requestId, custins.getInsName());
                    commonName = certConfig.getCommonName();
                    notAfter = certConfig.getNotafter();
                    data.put(ParamConstants.SERVER_CERT, certConfig.getCert());
                    data.put(ParamConstants.SERVER_KEY, certConfig.getKey());
                } else {
                    Map<String, String> result = caServerApi.queryInstanceCert(custins.getInsName(), custins.getClusterName());
                    commonName = result.get("common_name");
                    notAfter = result.get("notafter");
                }
                data.put(ParamConstants.CA_YTPE, caType);
            }
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.CERT_COMMON_NAME, commonName);
            data.put(ParamConstants.SSL_EXPIRED_TIME, notAfter);
            data.put(ParamConstants.SSL_ENABLED, sslEnabled);
            data.put(ParamConstants.SSL_UPDATE_REASON, "");
            data.put(ParamConstants.FORCE_ENCRYPTION, forceEncryption);
            return data;
        } catch (RdsException re) {
            log.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
