package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateDBImpl")
public class CreateDBImpl implements IAction {
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private DbossApi dbossApi;

    private static final String REGEX_DB_NAME_FOR_CREATE = "^[a-z]+[a-z0-9|_|-]*[a-z0-9]+$";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
        String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            CustInstanceDO custins = mysqlParameterHelper.getAndCheckCustInstance();

            if (custins.isReadAndWriteLock()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (!custins.inAvailableStatus()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            return createDbsWithDBoss(custins);
        } catch (RdsException re) {
            log.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, Object> createDbsWithDBoss(CustInstanceDO custins) throws RdsException, IOException {
        String DBName = checkValidForDbNameForCreate(mysqlParameterHelper.getDBName());
        if (Validator.isKeywords(DBName, DbsSupport.getInvalidDbName(custins.getDbType()))) {
            throw new RdsException(ErrorCode.INVALID_DBNAME_KEYWORD);
        }

        String charSet = mysqlParameterHelper.getAndCheckCharacterSetName(custins.getDbType(), custins.getDbVersion());
        String accountPrivilege = mysqlParameterHelper.getAccountPrivilege();
        String accountName = mysqlParameterHelper.getAccountName();
        String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID, "");

        Map<String, Object> db = new HashMap<>();
        db.put("charset", charSet);
        db.put("custinsId", custins.getId());
        db.put("dbname", DBName);
        db.put("requestId", requestId);
        String comment = mysqlParameterHelper.getParameterValue("DBDescription");
        if (!StringUtils.isEmpty(comment)) {
            db.put("comment", CheckUtils.checkLength(comment, 1, 256, ErrorCode.INVALID_DBDESCRIPTION));
        }
        if (!Strings.isNullOrEmpty(accountName) && !Strings.isNullOrEmpty(accountPrivilege)) {
            List<Map<String, String>> privileges = new ArrayList<>();
            Map<String, String> priv = new HashMap<>();
            priv.put("accountName", accountName);
            priv.put("privileges", CheckUtils.getAndCheckPrivilegeLiteral(accountPrivilege, custins.getDbType()));
            privileges.add(priv);
            db.put("privileges", privileges);
        }

        if (custins.isPgsql() && custins.isCustinsDockerOnEcs()) {
            if (StringUtils.isNotBlank(accountName)) {
                db.put("accountName", accountName);
            }
        }

        dbossApi.addDB(db);
        Map<String, Object> data1 = new HashMap<>();
        data1.put("DBName", DBName);
        data1.put("CharacterSetName", charSet);
        data1.put("DBInstanceID", custins.getId());
        data1.put("DBID", 0);
        data1.put("DBStatus", DbsSupport.STATUS_ACTIVE);
        data1.put("AccountName", accountName);
        data1.put("AccountPrivilege", accountPrivilege);
        data1.put("TaskId", 0);
        return data1;
    }

    /**
     * DB里_存储为\_，占两个字符
     *
     * @param dbname
     * @return
     * @throws RdsException
     */
    private static String checkValidForDbNameForCreate(String dbname) throws RdsException {
        if (dbname == null || dbname.replaceAll("_", "__").length() > 64 ||
                !Pattern.compile(REGEX_DB_NAME_FOR_CREATE).matcher(dbname).matches()) {
            throw new RdsException(ErrorCode.INVALID_DBNAME);
        }
        return dbname;
    }
}
