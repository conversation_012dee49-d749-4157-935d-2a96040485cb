package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.activityprovider.model.GeneralCloudDisk;
import com.aliyun.dba.poddefault.action.service.modify.AliyunModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodAvzSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Service
public class AutomaticSlaveZoneService {

    protected static final LogAgent logger = LogFactory.getLogAgent(AutomaticSlaveZoneService.class);
    @Resource
    private PodAvzSupport podAvzSupport;

    @Resource
    private ResourceService resourceService;

    @Resource
    private PodCommonSupport podCommonSupport;

    /**
     * 只在公有云生效
     *
     * @param zoneId
     * @return
     */
    @NotNull
    public String getLocationByAz(@NotNull String zoneId) {
        return podAvzSupport.getLocationByAz(zoneId);
    }

    /**
     * 是否自动选择备节点可用区
     *
     * @return
     */
    public boolean isAutomaticSlaveZone() {
        String SLAVE_AZ_RES_KEY = "SLAVE_AZ_AUTOMATIC";
        ResourceDO res = resourceService.getResourceByResKey(SLAVE_AZ_RES_KEY);
        return res != null && Boolean.parseBoolean(res.getRealValue());
    }


    @Nullable
    public List<String> getAvailableZones(@NotNull String regionCode, GeneralCloudDisk generalCloudDisk) {
        try {
            String SLAVE_AZ_RES_KEY = "K8S_BASIC_TO_STANDARD_SLAVE_AZ_TMP_SOLUTION";

            if (podCommonSupport.isIoAccelerationEnabled(generalCloudDisk)) {
                SLAVE_AZ_RES_KEY = "K8S_BASIC_TO_STANDARD_SLAVE_AZ_TMP_SOLUTION_FOR_IO_ACCELERATION";
            }

            ResourceDO res = resourceService.getResourceByResKey(SLAVE_AZ_RES_KEY);
            String azJsonString = res.getRealValue();
            final HashMap<String, List<String>> azObj = new Gson().fromJson(azJsonString, HashMap.class);
            if (azObj == null) {
                return null;
            }
            return azObj.get(regionCode);
        } catch (Exception ignored) {
            return null;
        }

    }


    public String getRandomSlaveZones(@NotNull String regionCode, @NotNull String masterAz, GeneralCloudDisk generalCloudDisk) {
        logger.info("getRandomSlaveZones regionCode: {}, masterAz: {}", regionCode, masterAz);
        List<String> azList = getAvailableZones(regionCode, generalCloudDisk);
        if (azList == null || azList.size() < 2) {
            return masterAz;
        }

        List<String> filtered = azList.stream().filter(e -> !e.equalsIgnoreCase(masterAz)).collect(Collectors.toList());
        Random random = new Random();
        int index = random.nextInt(filtered.size());
        return filtered.get(index);
    }
}
