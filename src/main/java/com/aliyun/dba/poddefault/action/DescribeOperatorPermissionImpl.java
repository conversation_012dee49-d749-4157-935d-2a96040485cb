package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


/**
 * <AUTHOR>
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeOperatorPermissionImpl")
public class DescribeOperatorPermissionImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeOperatorPermissionImpl.class);

    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Autowired
    private CustinsParamService custinsParamService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        try {
            CustInstanceDO custins = mysqlParamSupport.getAndCheckCustInstance(params);
            Integer custinsId = custins.getId();
            // AccountPrivilege
            int accountPrivilege = Integer.parseInt(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_PRIVILEGE, AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()));

            List<String> param = new ArrayList<String>(3);
            param.add(CustinsParamSupport.CUSTINS_PARAM_NAME_SYSTEM_OPERATOR);
            param.add(PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER);
            if (accountPrivilege == AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()){
                param.add(CustinsParamSupport.CUSTINS_PARAM_NAME_USER_GRANT_INFO);
            }else if(accountPrivilege == AccountPriviledgeType.PRIVILEDGE_INNER_USER_GRANT.getValue()){
                param.add(CustinsParamSupport.CUSTINS_PARAM_NAME_INNER_GRANT_INFO);
            }else{
                return createErrorResponse(ErrorCode.INVALID_ACCOUNTPRIVILEGE);

            }
            // 返回信息初始化
            StringBuffer operatorType = new StringBuffer();
            StringBuffer expiredTime = new StringBuffer();
            StringBuffer createdTime = new StringBuffer();

            // 查询 custins_param表中的数据
            List<CustinsParamDO> listCustinsParamDO = custinsParamService.getCustinsParams(custinsId, param);
            if (!listCustinsParamDO.isEmpty()){
                for (CustinsParamDO custinsParamDO : listCustinsParamDO) {
                    String paramName = custinsParamDO.getName();
                    String paramValue = custinsParamDO.getValue();
                    if (paramName.equals(CustinsParamSupport.CUSTINS_PARAM_NAME_SYSTEM_OPERATOR)){
                        if (paramValue.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SYSTEM_OPERATION)){
                            operatorType.append(CustinsSupport.OPERATOR_TYPE_CONTROL+",");
                        }
                    }else if(paramName.equals(PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER)){
                        if (paramValue.equals(PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER_VALUE)){
                            operatorType.append(CustinsSupport.OPERATOR_TYPE_DATA+",");
                        }
                    }else{
                        JSONObject resultJson = JSON.parseObject(paramValue);
                        createdTime.append(resultJson.getString("created_time"));
                        expiredTime.append(resultJson.getString("expired_time"));
                    }
                }
            }
            // delete last ','
            if (!StringUtils.isBlank(operatorType)){
                operatorType.deleteCharAt(operatorType.length()-1);
            }
            // 返回结果
            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put(ParamConstants.DB_INSTANCE_ID, custinsId);
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.OPERATOR_TYPE, operatorType.toString());
            data.put(ParamConstants.EXPIRED_TIME, expiredTime.toString());
            data.put(ParamConstants.CREATED_TIME, createdTime.toString());
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}