package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlAccountService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeAccountListImpl")
public class DescribeAccountListImpl implements IAction {
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected AccountService accountService;

    @Autowired
    protected DbossApi dbossApi;

    @Autowired
    protected MycnfService mycnfService;

    @Autowired
    protected MysqlAccountService mysqlAccountService;

    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            CustInstanceDO custins1 = mysqlParamSupport.getAndCheckCustInstance(params);
            if (dbossApi.isHandleByDBoss(custins1)) {
                return mysqlAccountService.describeAccountListByDboss(custins1, params);
            } else {
                return mysqlAccountService.describeAccountList(params);
            }
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.warn(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
