package com.aliyun.dba.poddefault.action;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.RebuildReplicaResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRecoverDBInstanceImpl")
@Slf4j
public class RecoverDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeDBVersionImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private MySQLServiceImpl mySQLService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private CommonProviderService commonProviderService;
    @Autowired
    protected DTZSupport dtzSupport;
    @Resource
    private BakService bakService;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private RundPodSupport rundPodSupport;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        JSONObject taskParam = new JSONObject();
        taskParam.put("requestId", requestId);


        // 检查UID
        custins = mysqlParamSupport.getAndCheckCustInstance(params);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                if (!replicaSetService.isActive(replicaSet)) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                } else if (!replicaSetService.isMgr(requestId, replicaSet.getName())) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
                Object taskId = workFlowService.dispatchTask(
                        "custins",
                        replicaSet.getName(), "mysql",
                        "recover_group_replication", taskParam.toString(),
                        WorkFlowService.TASK_PRIORITY_VIP);
                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", custins.getId());
                data.put("DBInstanceName", custins.getInsName());
                data.put("TaskId", taskId);
                dBaasMetaService
                        .getDefaultClient()
                        .updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.HA_SWITCHING.toString());
                return data;
            } else {
                Object taskId = doRecoverDBInstance(params, taskParam, custins);
                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", custins.getId());
                data.put("DBInstanceName", custins.getInsName());
                data.put("TaskId", taskId);
                return data;
            }

        }  catch (RdsException e) {
            logger.error("doRecoverDBInstance failed!", e);
            return createErrorResponse(e.getErrorCode());
        } catch (Exception e) {
            logger.error("doRecoverDBInstance failed!", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }


    }

    public Object doRecoverDBInstance(Map<String, String> params, JSONObject taskParam, CustInstanceDO custins) throws Exception {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        RebuildReplicaResourceRequest allocateReplicaResource = null;
        boolean isSuccess = false;
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSet.getCategory())
                    && !InstanceLevel.CategoryEnum.SERVERLESS_BASIC.toString().equalsIgnoreCase(replicaSet.getCategory())) {
                logger.error("db instance is not basic.");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            String restoreType = getParameterValue(params, ParamConstants.RESTORE_TYPE);
            Long bakId = null;
            BakhistoryDO bakHistory = null;

            // 按备份集恢复
            if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                bakId = CheckUtils.parseLong(getParameterValue(params, ParamConstants.BACKUP_SET_ID), null,
                        null, ErrorCode.BACKUPSET_NOT_FOUND);
                bakHistory = bakService.getBakhistoryByBackupSetId(replicaSet.getId().intValue(), bakId);
            } else if (RESTORE_TYPE_TIME.equals(restoreType)) {
                // 按时间点恢复且指定时间点时，自动获取备份集
                if (StringUtils.isNotEmpty(paramSupport.getParameterValue(params, ParamConstants.RESTORE_TIME))) {
                    Date restoreTimeUTC = podParameterHelper.getAndCheckRestoreTime(requestId, custins);
                    bakHistory = podParameterHelper.getBakhistoryByRecoverTime(custins.getId(), restoreTimeUTC);
                }
                // 按时间点恢复但未指定时间点，表示恢复到最新，此时需要API指定初始化备份集
                else {
                    bakId = CheckUtils.parseLong(getParameterValue(params, ParamConstants.BACKUP_SET_ID), null,
                            null, ErrorCode.BACKUPSET_NOT_FOUND);
                    bakHistory = bakService.getBakhistoryByBackupSetId(replicaSet.getId().intValue(), bakId);
                }
            }

            if (bakHistory == null) {
                logger.error("Cannot find backupset");
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            Replica masterReplica = mySQLService.getReplicaByRole(requestId, replicaSet.getName(), Replica.RoleEnum.MASTER);
            ReplicaResource masterReplicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, masterReplica.getId(), null);
            PodType podType = podCommonSupport.getReplicaRuntimeType(masterReplicaResource);

            // 申请资源
            allocateReplicaResource = podReplicaSetResourceHelper.allocateRebuildResource4Basic(replicaSet, podType);
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                rundPodSupport.completeReplicaNetworkConfig(allocateReplicaResource, masterReplica);
            }
            Replica replicaForRecover = mySQLService.getReplicaByRole(requestId, replicaSet.getName(), Replica.RoleEnum.MASTER);
            commonProviderService.getDefaultApi().allocateReplicaSetResourceForRebuild(
                    requestId, replicaSet.getName(), replicaForRecover.getId(), allocateReplicaResource);
            CustInstanceDO custInstanceDO = custinsService.getCustInstanceByInsName(
                    null, allocateReplicaResource.getTmpReplicaSetName(), CUSTINS_INSTYPE_TMP);
            if (custInstanceDO == null) {
                logger.error("Cannot find tmp custins [{}]", allocateReplicaResource.getTmpReplicaSetName());
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            custInstanceDO.setInsType(CUSTINS_INSTYPE_MIRROR);
            custInstanceDO.setGmtModified(new Date());
            custinsService.updateCustInstance(custInstanceDO);

            // 下发任务
            boolean isPengineBackupSet = !bakHistory.getKindCode().equals(KIND_CODE_NEW_ARCH);

            taskParam.put("srcReplicaSetName", replicaSet.getName());
            taskParam.put("destReplicaSetName", allocateReplicaResource.getTmpReplicaSetName());
            taskParam.put("srcReplicaId", replicaForRecover.getId());

            taskParam.put("backupSetId", bakHistory.getHisId());
            taskParam.put("isPengineBackupSet", isPengineBackupSet);

            taskParam.put("restoreTime", paramSupport.getParameterValue(params, ParamConstants.RESTORE_TIME));
            taskParam.put("restoreType", restoreType);

            taskParam.put("destReplicaId", mySQLService.getReplicaByRole(
                    requestId, allocateReplicaResource.getTmpReplicaSetName(), Replica.RoleEnum.MASTER).getId());

            String taskKey = getTaskForInnerRecovery(replicaSet.getCategory());
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSet.getName(), "mysql", taskKey, taskParam.toString(), 0);
            isSuccess = true;
            return taskId;
        } finally {
            // 处理失败时释放资源
            if (null != allocateReplicaResource && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, allocateReplicaResource.getTmpReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    private String getTaskForInnerRecovery(String category){
        if (InstanceLevel.CategoryEnum.SERVERLESS_BASIC.toString().equalsIgnoreCase(category)){
            return ServerlessConstant.TASK_RECOVERY_SERVERLESS_BASIC;
        }
        return PodDefaultConstants.TASK_RECOVER_BASIC_INS;
    }
}
