package com.aliyun.dba.poddefault.action.support;

import com.aliyun.apsaradb.activityprovider.model.Spread;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Data
@NoArgsConstructor
@Builder
public class PodScheduleTemplate {

    private String appName;

    @Builder.Default
    private List<String> affinitys = Lists.newArrayList();

    @Builder.Default
    private List<String> antiAffinitys = Lists.newArrayList();

    @Builder.Default
    private Map<String,String> nodeLabels = Maps.newHashMap();

    @Builder.Default
    private Map<String,Map<String,String>> roleLabels = Maps.newHashMap();

    @Builder.Default
    private Map<String,Map<String,Integer>> roleCapacity = Maps.newHashMap();//role value in master,slave,logger,learner,app
    
}
