package com.aliyun.dba.poddefault.action.service;

import java.util.Map;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aliyun.apsaradb.dbaasmetaapi.model.UserRoleArnRel;
import com.aliyun.apsaradb.dbaasmetaapi.model.UserRoleArnRelListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/19
 */

@Slf4j
@Service
public class TdeKmsService {
    @Autowired
    public KmsApi kmsApi;
    @Autowired
    public DBaasMetaService dBaasMetaService;

    public boolean checkTdeSupported(String requestId, String regionId) {
        try {
            ConfigListResult result = dBaasMetaService.getDefaultClient().listConfigs(requestId, PodDefaultConstants.RES_KEY_TDE_SUPPORT_SWITCH);
            // only disable TDE if the kill switch exists and it is off
            if (result != null && result.getItems() != null && !result.getItems().isEmpty()) {
                String tdeSwitch = result.getItems().get(0).getValue();
                if (StringUtils.equalsIgnoreCase(tdeSwitch, PodDefaultConstants.TDE_SUPPORT_SWITCH_OFF)) {
                    log.error("TDE is not supported in this region: {}", regionId);
                    return false;
                }
            }
            return true;
        } catch (ApiException e) {
            log.error("failed to get the kill switch, so we disable tde");
            return false;
        }
    }

    public void checkKeyIsAvailable(CustInstanceDO custins, String roleArn, String keyId, String uid) throws Exception {
        DescribeKeyResponse describeKeyResponse = kmsApi.describeKey(custins.getClusterName(), keyId, roleArn, uid);
        String keyState = describeKeyResponse.getKeyMetadata().getKeyState();
        boolean isEnabled = StringUtils.equalsIgnoreCase(keyState, "Enabled");
        String keySpec = describeKeyResponse.getKeyMetadata().getKeySpec();
        boolean isAesKey = StringUtils.startsWithIgnoreCase(keySpec, "Aliyun_AES");
        log.info("describeKeyResponse keyId=" + keyId + ", keySpec=" + keySpec + ", keyState=" + keyState);
        if (!isEnabled || !isAesKey) {
            throw new RdsException(ErrorCode.INVALID_KMS_KEY);
        }
    }

    public void ensureTagExistence(CustInstanceDO custins, String roleArn, String keyId, String uid) throws Exception {
        Map<String, Boolean> resourceTags = kmsApi.resourceTags(custins.getClusterName(), roleArn, keyId, uid);
        boolean hasTag = resourceTags.getOrDefault(CustinsSupport.ROLE_ARN_TAG, false);
        if (!hasTag) {
            kmsApi.tagResource(custins.getClusterName(), roleArn, keyId, uid);
        }
    }

    public void ensureUserRoleArn(CustInstanceDO custins, String roleArn, String uid) throws Exception {
        UserRoleArnRelListResult userRoleArnRelListResult = dBaasMetaService.getDefaultClient()
                .getUserRoleArnRel(RequestSession.getRequestId(), uid, null, "kms", roleArn);
        if (userRoleArnRelListResult != null && !userRoleArnRelListResult.getItems().isEmpty()) {
            UserRoleArnRel result = userRoleArnRelListResult.getItems().get(0);
            if (result.getRoleArn() != null && result.getRoleArn().equalsIgnoreCase(roleArn)) {
                // good, nothing to do
                return;
            }
        }
        UserRoleArnRel userRoleArnRel = new UserRoleArnRel().uid(uid).roleArn(roleArn).type("kms");
        dBaasMetaService.getDefaultClient().createUserRoleArnRel(RequestSession.getRequestId(), uid, userRoleArnRel);
    }

    public void setDeletionProtection(CustInstanceDO custins, String roleArn, String keyId, String uid) throws Exception {
        DescribeKeyResponse describeKeyResponse = kmsApi.describeKey(custins.getClusterName(), keyId, roleArn, uid);
        String keyArn = describeKeyResponse.getKeyMetadata().getArn();
        kmsApi.setDeletionProtection(custins.getClusterName(), roleArn, uid, keyArn);
    }
}
