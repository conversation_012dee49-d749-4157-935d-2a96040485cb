package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.DTSService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponse;
import com.aliyun.dts20200101.models.ModifyDtsJobResponse;
import com.aliyun.dts20200101.models.ModifyDtsJobResponseBody;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyAnalyticDBInstanceSyncJobImpl")
public class ModifyAnalyticDBInstanceSyncJobImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyAnalyticDBInstanceSyncJobImpl.class);

    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private DTSService dtsService;

    @Resource
    private CustinsParamService custinsParamService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String analyticInsName = paramSupport.getParameterValue(params, ParamConstants.ANALYTIC_DB_INSTANCE_NAME);
            ReplicaSet analyticIns = podCommonSupport.getCkReplicaSet(analyticInsName);
            // 查询关联关系
            podCommonSupport.checkAnalyticDBInstanceRelation(requestId, custins.getInsName(), analyticInsName);

            CustinsParamDO custinsParam = custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.DTS_INFO);
            if (ObjectUtils.isEmpty(custinsParam)) {
                logger.error("reqeustId : {}. There is no dts info.", requestId);
                throw new Exception("There is no dts info.");
            }
            JSONObject dtsInfo = JSONObject.parseObject(custinsParam.getValue());
            String dtsInstanceId = String.valueOf(dtsInfo.get(PodDefaultConstants.DTS_INSTANCE_ID));
            String dtsJobId = String.valueOf(dtsInfo.get(PodDefaultConstants.DTS_JOB_ID));

            String regionId = paramSupport.getParameterValue(params, ParamConstants.REGION_ID);
            String dbList = params.get(ParamConstants.DB_LIST.toLowerCase());

            JSONObject dbListObject;
            try {
                dbListObject = JSONObject.parseObject(dbList);
            } catch (JSONException ex) {
                logger.error("reqeustId : {}, can not cast to JSONObject.", requestId);
                throw new Exception("can not cast to JSONObject.");
            }

            DescribeDtsJobDetailResponse describeDtsJobDetailResponse = dtsService.describeDtsJobDetail(regionId, dtsInstanceId, dtsJobId);
            String reserved = describeDtsJobDetailResponse.getBody().getReserved();

            ModifyDtsJobResponse response = dtsService.modifyDtsJob(regionId, dtsInstanceId, dbListObject, reserved);
            if (ObjectUtils.isEmpty(response)) {
                logger.error("requestId : {}, ModifyDtsJobResponse is empty.", requestId);
                throw new Exception("ModifyDtsJobResponse is empty.");
            }

            Map<String, Object> result = new HashMap<>();
            ModifyDtsJobResponseBody body = response.getBody();
            result.put("dbInstanceName", analyticInsName);
            result.put("status", body.getStatus());
            result.put("requestId", body.getRequestId());
            result.put("errCode", body.getErrCode());
            result.put("success", body.getSuccess());
            result.put("errMessage", body.getErrMessage());
            return result;
        } catch(RdsException ex) {
            log.error("ModifyAnalyticDBInstanceSyncJob failed: {}", JSONObject.toJSONString(ex));
            throw ex;
        } catch (Exception ex) {
            log.error("ModifyAnalyticDBInstanceSyncJob Exception: {}", JSONObject.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
