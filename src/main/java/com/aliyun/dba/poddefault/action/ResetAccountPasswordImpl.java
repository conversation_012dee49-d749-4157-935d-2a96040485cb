package com.aliyun.dba.poddefault.action;

import com.alibaba.cobar.util.StringUtil;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.support.HashUtils;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.poddefault.action.support.PodAccountConstants;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultResetAccountPasswordImpl")
public class ResetAccountPasswordImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ResetAccountPasswordImpl.class);
    @Autowired
    private DbsService dbsService;
    @Autowired
    AccountIDao accountIDao;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    private DbossApi dbossApi;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
            String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID, "");

            CustInstanceDO custIns = mysqlParaHelper.getAndCheckCustInstance();

            if (!custIns.isLogicPrimary()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            String accountName = mysqlParaHelper.getAccountName();
            if (PodAccountConstants.RESERVED_ACCOUNTS.contains(accountName)) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_ACCOUNTNAME);
            }
            AccountsDO accountDTO = dbsService.getAccountDOByAccountName(custIns.getId(), accountName,
                    null);
            String oldPassword = null;
            String password = null;
            try {
                oldPassword = mysqlParaHelper.getAndCheckDecryptedOldPassword();
                password = mysqlParaHelper.getAndCheckDecryptedNewPassword();
            }catch (RdsException ignored){
                if(mysqlParaHelper.getAction().equals("ModifyAccountPassword")){ // 修改密码必须提供原密码
                    throw new RdsException(ErrorCode.OLDPASSWORD_WRONG);
                }
                password = mysqlParaHelper.getDecryptedAccountPasswordWithoutCheck("accountpassword", "encryptaccountpassword");
                if(StringUtil.isEmpty(password)){
                    password =  mysqlParaHelper.getDecryptedAccountPasswordWithoutCheck("accountnewpassword", "encryptaccountnewpassword");
                }
            }
            boolean resetPassword = !StringUtil.isEmpty(password);
            if (Strings.isNullOrEmpty(password)) {
                password = SupportUtils.getRandomPasswd(15);
            } else {
                CheckUtils.checkValidForAccountPassword(password);
            }

            if (dbossApi.isHandleByDBoss(custIns)
                    && (!(accountDTO != null && accountDTO.getPriviledgeType().equals(AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue())))) {
                if (custIns.isReadAndWriteLock()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
                }
                if (!custIns.inAvailableStatus()) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
                if (accountDTO != null && accountDTO.getPriviledgeType() == AccountPriviledgeType.PRIVILEDGE_SYSTEM_AURORA.getValue()) {
                    // Aurora账户不能改
                    throw new RdsException(ErrorCode.UNSUPPORTED_ACCOUNT_TYPE);
                }

                Map<String, Object> account = new HashMap<>(3);
                account.put("custinsId", custIns.getId());
                account.put("accountName", accountName);
                if(!StringUtil.isEmpty(oldPassword)){
                    account.put("oldPassword", oldPassword);
                }
                account.put("password", password);
                account.put("requestId", requestId);
                dbossApi.updateAccount(account);

                Map<String, Object> data = new HashMap<String, Object>(6);
                data.put("DBInstanceID", custIns.getId());
                data.put("DBInstanceName", custIns.getInsName());
                data.put("AccountName", accountName);
                data.put("AccountStatus", DbsSupport.STATUS_ACTIVE);
                data.put("AccountNewPassword", password);
                data.put("TaskId", 0);
                return data;
            }

            String mysqlPassword = HashUtils.getMysqlHashString(password);
            if(resetPassword) {
                accountIDao.updateAccountPwd(custIns.getId(), accountDTO.getId(), mysqlPassword, accountDTO.getAccount());
            }
//            accountIDao.updateAccountsStatus(custIns.getId(), ImmutableList.of(accountDTO.getId()),0);
            Object taskId = workFlowService.dispatchTask("custins", custIns.getInsName(), "mysql", "reset_user_account_pwd", "", 0);

            Map<String, Object> data = new HashMap<>(4);
            data.put("AccountID", accountDTO.getId());
            data.put(ParamConstants.TASK_ID, taskId);
            data.put("AccountName", accountDTO.getAccount());
            data.put("AccountStatus", accountDTO.getStatus());
            data.put("AccountNewPassword", password);
            return data;
        } catch (RdsException re) {
            logger.error("createAdminAccount ex=" + re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("createAdminAccount ex=" + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //移除参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
