package com.aliyun.dba.poddefault.action;

import java.util.*;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.BlueGreenDeploymentCommonService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateBlueGreenDeploymentImpl")
public class CreateBlueGreenDeploymentImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.poddefault.action.CreateBlueGreenDeploymentImpl.class);
    @Resource
    private BlueGreenDeploymentService blueGreenDeploymentService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private BlueGreenDeploymentCommonService commonService;
    @Resource
    private ResourceService resourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            ResourceDO newArchSwitch = resourceService.getResourceByResKey("RDS_BLUEGREEN_NEW_ARCH_SWITCH");
            if (newArchSwitch == null || !"true".equalsIgnoreCase(newArchSwitch.getRealValue())) {
                logger.error("new arch switch is off.");
                return createErrorResponse(ErrorCode.UNSUPPORTED_KIND_CODE);
            }
            logger.info("CreateBlueGreenDeploymentImpl start.");
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String aliUid = mysqlParamSupport.getUID(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            // 处理小版本
            String value = mysqlParamSupport.getParameterValue(params, "minorVersion");
            if (value != null) {
                params.put("minorVersion", value.split("_")[1]);
            }
            regionId = commonService.checkAndCorrectRegionId(regionId, custins);
            // 获取用户的绿色实例修改的配置
            Map<String, Object> newPrimaryConfig = commonService.getNewPrimaryConfigFromParams(params);
            logger.info("newPrimaryConfig : {}", JSON.toJSONString(newPrimaryConfig));
            List<Map<String, Object>> newRoConfig = commonService.getNewRoConfigFromParams(params);
            logger.info("newRoConfig : {}", JSON.toJSONString(newRoConfig));
            List<Map<String, Object>> newNodeConfig = commonService.getNewReplicaConfigFromParams(params);
            logger.info("newNodeConfig : {}", JSON.toJSONString(newNodeConfig));
            Map<String, Object> newProxyConfig = commonService.getNewProxyConfigFromParams(params);
            logger.info("newProxyConfig : {}", JSON.toJSONString(newProxyConfig));

            Map<String, Object> data = blueGreenDeploymentService.createBlueGreenDeployment(requestId, regionId, aliUid, custins, newPrimaryConfig, newRoConfig, newNodeConfig, newProxyConfig);
            logger.info("CreateBlueGreenDeploymentImpl end.");
            return data;
        } catch (RdsException ex) {
            logger.error("CreateBlueGreenDeployment failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("CreateBlueGreenDeployment Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}