package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.physical.action.service.DbossApiService;
import com.aliyun.dba.poddefault.action.support.PodDateTimeUtils;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;

@Service
@Slf4j
public class EncdbService {

    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private DbossApiService dbossApiService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    protected DTZSupport dtzSupport;
    @Resource
    protected ResourceService resourceService;
    //查询规则
    private final String SELECT_RULE = "0";
    //查询用户权限
    private final String SELECT_USER = "1";
    private final String ENCDB_SQL_FAILED = "EncDBSQLError";

    public static final String PARAM_ALGO = "global_algo";
    public static final String PARAM_WHITE_LIST_MODE = "white_list_mode";

    public static final String RES_KEY_ALGO = "globalAlgo";
    public static final String RES_KEY_WHITE_LIST_MODE = "whiteListMode";
    public static final String RES_KEY_KMS_MODE = "kmsMode";
    //创建全密态规则
    public Map<String, Object> createMaskingRules(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException{
        custInstanceDO = mysqlParamSupport.getAndCheckCustInstance(params);
        checkCustinsStatusAvailable(custInstanceDO);
        try {
            List<Map<String, Object>> ruleConfigList = (List) describeMaskingRules(custInstanceDO,params).get("encDBInfo");
            if (ruleConfigList != null && !ruleConfigList.isEmpty() && ruleConfigList.get(0) != null && ruleConfigList.get(0).get("ruleConfig") != null) {
                throw new RdsException(ErrorCode.INVALID_PARAM, "rule name is existed");
            }
            return setMaskingRules(custInstanceDO,params,"add","true");
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    //修改全密态规则
    public Map<String, Object> modifyMaskingRules(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException{
        custInstanceDO = mysqlParamSupport.getAndCheckCustInstance(params);
        checkCustinsStatusAvailable(custInstanceDO);
        try {
            String enabled = mysqlParamSupport.getParameterValue(params,"enabled",null);
            return setMaskingRules(custInstanceDO,params,"update",enabled);
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    //创建和修改的公共部分
    private Map<String, Object> setMaskingRules(CustInstanceDO custInstanceDO, Map<String, String> params,String operateType, String enabled) throws RdsException{
        try {
            String ruleName = mysqlParamSupport.getRequiredParameterValue(params,"rulename");
            Map<String,Object> operateMap = new HashMap<>();
            operateMap.put("name",ruleName);
            String ruleConfig = mysqlParamSupport.getParameterValue(params,"RuleConfig",null);
            String maskingAlgo = mysqlParamSupport.getParameterValue(params,"MaskingAlgo",null);
            String defaultAlgo = mysqlParamSupport.getParameterValue(params,"DefaultAlgo",null);
            Map<String, List<String>> metaMap = new HashMap<>();
            //如果是update操作，用户没传ruleConfig，可以从表中获取之前的数据
            if (StringUtils.isNotBlank(operateType) && "update".equals(operateType)) {
                List<Map<String, Object>> ruleConfigList = (List) describeMaskingRules(custInstanceDO,params).get("encDBInfo");
                if (ruleConfigList == null || ruleConfigList.isEmpty()) {
                    throw new RdsException(ErrorCode.INVALID_PARAM, "rule name is not existed");
                }
                Map<String, Object> ruleMap = ruleConfigList.get(0);
                Map<String,List<String>> ruleConfigMap = (Map<String,List<String>>) ruleMap.get("ruleConfig");
                metaMap.put("databases", ruleConfigMap.get("databases"));
                metaMap.put("tables", ruleConfigMap.get("tables"));
                metaMap.put("columns", ruleConfigMap.get("columns"));
                if (StringUtils.isBlank(maskingAlgo)) {
                    maskingAlgo = (String) ruleMap.get("algo");
                }
                if (StringUtils.isBlank(defaultAlgo)) {
                    defaultAlgo = (String) ruleMap.get("defaultAlgo");
                }
                if (StringUtils.isBlank(enabled)) {
                    enabled = (String) ruleMap.get("enabled");
                }
            }
            if ("true".equals(enabled)) {
                operateMap.put("enabled",true);
            } else if ("false".equals(enabled)){
                operateMap.put("enabled",false);
            }
            try {
                if (StringUtils.isBlank(ruleConfig)) {
                    return createErrorResponse(ErrorCode.INVALID_PARAM,"rule config is empty");
                }
                meta ruleConfigMap = JSON.parseObject(ruleConfig, meta.class);
                log.info("get rule config from origin table:{}",JSON.toJSONString(ruleConfigMap));
                //检查库表列的配置信息是否完整
                List<String> databases = ruleConfigMap.getDatabases();
                if (databases != null && !databases.isEmpty() && !databases.contains(null)) {
                    metaMap.put("databases", databases);
                }
                List<String> tables = ruleConfigMap.getTables();
                if (tables != null && !tables.isEmpty() && !tables.contains(null)) {
                    metaMap.put("tables", tables);
                }
                List<String> columns = ruleConfigMap.getColumns();
                if (columns != null && !columns.isEmpty() && !columns.contains(null)) {
                    metaMap.put("columns", columns);
                }
                if (StringUtils.isNotBlank(operateType) && "add".equals(operateType)) {
                    if (databases == null || databases.isEmpty() || databases.contains("") || databases.contains(null)) {
                        throw new RdsException(ErrorCode.PARAM_NOT_FOUND, "databases");
                    }
                    if (tables == null || tables.isEmpty() || tables.contains("") || tables.contains(null)) {
                        throw new RdsException(ErrorCode.PARAM_NOT_FOUND, "tables");
                    }
                    if (columns == null || columns.isEmpty() || columns.contains("")|| columns.contains(null)) {
                        throw new RdsException(ErrorCode.PARAM_NOT_FOUND, "columns");
                    }
                }
                operateMap.put("meta", metaMap);
            } catch (RdsException re) {
                log.error(re.getMessage(), re);
                return createErrorResponse(re.getErrorCode());
            }
            List<String> algoNameList = new ArrayList<>();
            if (StringUtils.isNotBlank(maskingAlgo)) {
                operateMap.put("algo",maskingAlgo);
                try {
                    List<Object> algoList = JSON.parseObject(maskingAlgo,List.class);
                    for (Object m1:algoList) {
                        algoConfig a1 = JSON.parseObject(JSON.toJSONString(m1),algoConfig.class);
                        algoNameList.add(a1.getName());
                    }
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                    return createErrorResponse(ErrorCode.INVALID_PARAM,"masking Algo is wrong");
                }
            }
            if (StringUtils.isNotBlank(defaultAlgo)) {
                //判断default算法是否在配置的masking算法中
                if (!algoNameList.contains(defaultAlgo)) {
                    throw new RdsException(ErrorCode.PARAM_NOT_FOUND,"defaultAlgo");
                }
                operateMap.put("defaultAlgo",defaultAlgo);
            }
            String operateString = JSON.toJSONString(operateMap);
            Map<String, Object> encResult = dbossApiService.operateEncDBRule(custInstanceDO.getId(), operateType, operateString);
            if (encResult != null && encResult.containsKey("code")) {
                String code = (String) encResult.get("code");
                if (StringUtils.isNotBlank(code) && ENCDB_SQL_FAILED.equals(code)) {
                    throw new RdsException(ErrorCode.INVALID_PARAM,(String) encResult.get("message"));
                }
            }
            encResult.put("dbInstanceName",custInstanceDO.getInsName());
            return encResult;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    //查询全密态规则，支持点查和全查
    public Map<String, Object> describeMaskingRules(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException{
        custInstanceDO = mysqlParamSupport.getAndCheckCustInstance(params);
        checkCustinsStatusAvailable(custInstanceDO);
        try {
            String ruleName = mysqlParamSupport.getParameterValue(params,"ruleName",null);
            Map<String, Object> encResult = new HashMap<>();
            if (StringUtils.isNotBlank(ruleName)) {
                List<Map<String, Object>> ruleConfigList = dbossApiService.queryEncDBInfo(custInstanceDO.getId(), ruleName, SELECT_RULE);
                encResult.put("encDBInfo",ruleConfigList);
            } else {
                List<Map<String, Object>> ruleConfigList = dbossApiService.queryAllEncDBInfo(custInstanceDO.getId(), SELECT_RULE);
                encResult.put("encDBInfo",ruleConfigList);
            }
            return encResult;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    //删除全密态规则
    public Map<String, Object> deleteMaskingRules(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException{
        custInstanceDO = mysqlParamSupport.getAndCheckCustInstance(params);
        checkCustinsStatusAvailable(custInstanceDO);
        try {
            String ruleNameList = mysqlParamSupport.getRequiredParameterValue(params,"rulename");
            String[] ruleName = ruleNameList.split(",");
            String deleteList = JSON.toJSONString(Arrays.asList(ruleName));
            Map<String, Object> encResult = dbossApiService.operateEncDBRule(custInstanceDO.getId(), "delete", deleteList);
            encResult.put("getDeleteList",deleteList);
            encResult.put("dbInstanceName",custInstanceDO.getInsName());
            return encResult;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    //修改全密态数据库用户权限
    public Map<String, Object> modifyAccountMaskingPrivilege(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException{
        custInstanceDO = mysqlParamSupport.getAndCheckCustInstance(params);
        checkCustinsStatusAvailable(custInstanceDO);
        try {
            String privilege = mysqlParamSupport.getParameterValue(params,"Privilege",null);
            if (StringUtils.isBlank(privilege)) {
                return null;
            }
            Map<String, Object> ruleNameList = new HashMap<>();
            String userName = mysqlParamSupport.getParameterValue(params,"userName",null);
            List<String> userNameList = Arrays.asList(userName.split(","));
            ruleNameList.put(privilege,userNameList);
            String expireTime = mysqlParamSupport.getParameterValue(params,"expireTime",null);
            try {
                if (StringUtils.isNotBlank(expireTime)) {
                    DateTime utcExpireTime = dtzSupport.getUTCDateByDateStr(expireTime);
                    Date localExpireTime = dtzSupport.getSpecificTimeZoneDate(utcExpireTime, DATA_SOURCE_BAK);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    ruleNameList.put("expired", sdf.format(localExpireTime));
                }
            } catch (Exception e) {
                throw new RdsException(ErrorCode.INVALID_PARAM,"expireTime");
            }
            String ruleNameListString = JSON.toJSONString(ruleNameList);
            Map<String, Object> encResult = dbossApiService.operateEncDBRule(custInstanceDO.getId(), "grant", ruleNameListString);
            if (encResult != null && encResult.containsKey("code")) {
                String code = (String) encResult.get("code");
                if (StringUtils.isNotBlank(code) && ENCDB_SQL_FAILED.equals(code)) {
                    throw new RdsException(ErrorCode.INVALID_PARAM,(String) encResult.get("message"));
                }
            }
            encResult.put("getDeleteList",ruleNameList);
            encResult.put("dbInstanceName",custInstanceDO.getInsName());
            return encResult;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    //查询全密态数据库用户权限，支持点查和全查
    public Map<String, Object> describeAccountMaskingPrivilege(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException{
        custInstanceDO = mysqlParamSupport.getAndCheckCustInstance(params);
        checkCustinsStatusAvailable(custInstanceDO);
        try {
            String userName = mysqlParamSupport.getParameterValue(params,"userName",null);
            Map<String, Object> encResult = new HashMap<>();
            List<Map<String, Object>> userPrivilege = new ArrayList<>();
            if (StringUtils.isNotBlank(userName)) {
                userPrivilege = dbossApiService.queryEncDBInfo(custInstanceDO.getId(), userName, SELECT_USER);
            } else {
                //全量查询
                userPrivilege = dbossApiService.queryAllEncDBInfo(custInstanceDO.getId(), SELECT_USER);
            }
            for (Map<String,Object> user : userPrivilege) {
                if (user == null || user.get("expired") == null) {
                    continue;
                }
                String expireTime =(String) user.get("expired");
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date expireDate = formatter.parse(expireTime);
                DateTime dateTime = dtzSupport.revertSpecificTimeZoneDateToUTC(expireDate, DATA_SOURCE_DBAAS);
                user.put("expired",PodDateTimeUtils.convertDateToGMT(dateTime.toDate(), PodDateTimeUtils.ISO8601_DATE_FORMAT));
            }
            encResult.put("userPrivilege",userPrivilege);
            return encResult;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
    //检查实例状态是否正常，小版本是否支持
    public void checkCustinsStatusAvailable(CustInstanceDO custInstanceDO) throws RdsException {
        if (!custInstanceDO.isMysql57() && !custInstanceDO.isMysql80()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_MAJOR_VERSION);
        }
        if (!custInstanceDO.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        //检查内核小版本，需要大于mysql57_20240115或mysql80_20240131
        String currentMinorVersion = custinsService.getCustInstanceCurrentMinorVersionByCustinsId(custInstanceDO.getId());
        if (StringUtils.isNotBlank(currentMinorVersion)) {
            String currentDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(currentMinorVersion);
            ResourceDO resourceDO57 = resourceService.getResourceByResKey("ENCDB_57_MINOR_VERSION_RELEASE_DATE");
            String releaseData = resourceDO57.getRealValue();
            if (custInstanceDO.isMysql80()) {
                ResourceDO resourceDO80 = resourceService.getResourceByResKey("ENCDB_80_MINOR_VERSION_RELEASE_DATE");
                releaseData = resourceDO80.getRealValue();
            }
            int diff = currentDate.compareTo(releaseData);
            if (diff < 0) {
                throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }
        }
    }
    //全密态库表列配置类
    private static class meta{
        private List<String> databases;
        private List<String> tables;
        private List<String> columns;
        private  meta(){
        }

        public List<String> getDatabases() {
            return databases;
        }

        public void setDatabases(List<String> databases) {
            this.databases = databases;
        }

        public List<String> getTables() {
            return tables;
        }

        public void setTables(List<String> tables) {
            this.tables = tables;
        }

        public List<String> getColumns() {
            return columns;
        }

        public void setColumns(List<String> columns) {
            this.columns = columns;
        }
    }
    //全密态算法配置类
    private static class algoConfig{
        private String name;
        private Map<String,Object> params;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Map<String, Object> getParams() {
            return params;
        }

        public void setParams(Map<String, Object> params) {
            this.params = params;
        }
    }

    // 新版本 20241230以后支持 全局参数配置、查看
    private boolean isValidAlgo(String algo) {
        return Arrays.asList("AES_256_GCM", "AES_256_CBC", "AES_256_ECB", "AES_256_CTR", "AES_128_CBC",
                "AES_128_GCM", "AES_128_ECB", "AES_128_CTR", "SM4_128_CBC", "SM4_128_GCM", "SM4_128_CTR",
                "SM4_128_ECB").contains(algo);
    }

    public boolean isUIDAllowedWithCLSKmsMode(String uid) {
        final String ALLOW_CLS_KMS_MODE = "ALLOW_CLS_KMS_MODE";
        try {
            ResourceDO resourceDO = resourceService.getResourceByResKey(ALLOW_CLS_KMS_MODE);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                if ("*".equals(resourceDO.getRealValue())) {
                    // 全部允许
                    return true;
                }
                Set<String> whiteIds = JSON.parseObject(resourceDO.getRealValue(), Set.class);
                return whiteIds.contains(uid);
            }
        } catch (Exception e) {
            //ignore
            log.warn("Get ALLOW_CLS_WITH_KMS failed, ignore", e);
        }
        return false; // 默认都是禁止
    }

    public void checkCustinsSupportEncdbConfigAndKMS(CustInstanceDO custInstanceDO) throws RdsException {
        // 基础检查
        checkCustinsStatusAvailable(custInstanceDO);
        String currentMinorVersion = custinsService.getCustInstanceCurrentMinorVersionByCustinsId(custInstanceDO.getId());
        ResourceDO resourceDO = resourceService.getResourceByResKey("CLS_KMS_SUPPORT_MINOR_VERSION_RELEASE_DATE");
        String expectedDate = resourceDO.getRealValue();
        if (StringUtils.isNotBlank(currentMinorVersion)) {
            // KMS模式需要大于小版本20241231
            String currentDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(currentMinorVersion);
            int diff = currentDate.compareTo(expectedDate);
            if (diff < 0) {
                throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }
        }
    }
    public void setEncdbGlobalAlgo(CustInstanceDO custInstanceDO, String algo) throws RdsException, IOException {
        if (!isValidAlgo(algo)) {
            log.error("Invalid encdb algorithm " + algo);
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        checkCustinsSupportEncdbConfigAndKMS(custInstanceDO);
        dbossApiService.configEncdbParam(custInstanceDO.getId(), PARAM_ALGO, algo);
    }
    public void setWhiteListMode(CustInstanceDO custInstanceDO, String mode) throws RdsException, IOException {
        if (!StringUtils.equalsIgnoreCase(mode, "true") && !StringUtils.equalsIgnoreCase(mode, "false")) {
            log.error("Invalid encdb white_list_mode " + mode);
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        checkCustinsSupportEncdbConfigAndKMS(custInstanceDO);
        dbossApiService.configEncdbParam(custInstanceDO.getId(), PARAM_WHITE_LIST_MODE, StringUtils.equalsIgnoreCase(mode, "true") ? "true" : "false");
    }
    public Map<String, Object> getEncdbParams(CustInstanceDO custInstanceDO) throws RdsException, IOException {
        checkCustinsSupportEncdbConfigAndKMS(custInstanceDO);
        Map<String, Object> params = dbossApiService.showEncdbParams(custInstanceDO.getId());
        Map<String, Object> ret = new HashMap<>();
        // dboss返回的格式下划线，转换成驼峰形式返回到上层。
        ret.put(RES_KEY_ALGO, ((Map) params.get("encDBParams")).get("global_algo"));
        ret.put(RES_KEY_WHITE_LIST_MODE, ((Map) params.get("encDBParams")).get("white_list_mode"));
        ret.put(RES_KEY_KMS_MODE, ((Map) params.get("encDBParams")).get("kms_mode"));
        return ret;
    }

}
