package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.ALIGROU_DHG_PARTERN;
import static com.aliyun.dba.base.support.MySQLParamConstants.ALIGROU_TEST_DHG_PARTERN;

/**
 * 处理集团业务相关服务接口
 */
@Service
public class AligroupService {
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ClusterService clusterService;
    @Resource
    private ResourceService resourceService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;

    /**
     * 是否需要独占业务资源, 核心集群需要
     *
     * @param requestId
     * @param tddlClusterName
     * @return
     * @throws ApiException
     */
    public Boolean isTddlClusterNeedAllocateDedicatedResourceGroup(String requestId, String tddlClusterName) throws ApiException {
        ConfigListResult configList = metaService.getDefaultClient().listConfigs(requestId, "CORE_BIZ_TDDL_CLUSTER_NAME");
        if (configList == null || CollectionUtils.isEmpty(configList.getItems())) {
            return false;
        }
        Set<String> coreTddlClusterNames = configList.getItems().stream()
                .map(Config::getValue).collect(Collectors.toSet());
        return coreTddlClusterNames.contains(tddlClusterName);
    }

    public String getPromotionClassCode(String requestId, String tddlClusterName, String originClassCode) throws ApiException {
        ConfigListResult configList = metaService.getDefaultClient().listConfigs(requestId, "ALIGROUP_PROMOTION_CLASS_CODE");
        if (configList == null || CollectionUtils.isEmpty(configList.getItems())) {
            return originClassCode;
        }
        Map<String, String> appNameToClassCode = configList.getItems().stream()
                .collect(Collectors.toMap(Config::getDisplayValue, Config::getValue));
        return appNameToClassCode.getOrDefault(tddlClusterName, originClassCode);
    }


    public Boolean isAligroupDHG(String regionId, String dedicatedHostGroupId) {
        return String.format(ALIGROU_DHG_PARTERN, regionId).equalsIgnoreCase(dedicatedHostGroupId)
                || String.format(ALIGROU_TEST_DHG_PARTERN, regionId).equalsIgnoreCase(dedicatedHostGroupId);
    }


    public boolean isFailBackRouteXDBProvider(String action) {
        try {
            if (StringUtils.isBlank(action)) {
                return false;
            }
            ResourceDO resourceDO = resourceService.getResourceByResKey("FAILBACK_ROUTE_XDB_PROVIDER");
            if (resourceDO != null && StringUtils.isNotBlank(resourceDO.getRealValue())) {
                Set<String> actionsSet = JSON.parseObject(resourceDO.getRealValue(), Set.class);
                return actionsSet.contains(action);
            }
            return false;
        } catch (Exception e) {
            //ignore
        }
        return false;
    }



    // 集团需要 2c4g 的 logger
    public String getAligroupLoggerClassCode(String storageType){
        return storageType.startsWith("cloud") ? "mysql.z.xdb.logger.cloud" :"mysql.n2.medium.25";
    }





    /**
     * 判断是否库存多点写内核版本
     *
     * @param replicaSet
     * @return
     */
    public boolean isXdbMultiWriteEngine(String requestId, ReplicaSet replicaSet) throws Exception {
        String dbEngine = mysqlParamSupport.isMysqlXDB(replicaSet.getService(), replicaSet.getServiceVersion(),
                replicaSet.getClassCode()) ? "XDB" : "MySQL";
        if (!dbEngine.equalsIgnoreCase("XDB")) {
            return false;
        }
        Map<String, String> labelMap = metaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
        return labelMap.getOrDefault("composeTag", "").startsWith(MinorVersionServiceHelper.ServiceTag.TAG_XCLUSTER_DOCKER_MULTI_W.getTagPrefix());
    }


}
