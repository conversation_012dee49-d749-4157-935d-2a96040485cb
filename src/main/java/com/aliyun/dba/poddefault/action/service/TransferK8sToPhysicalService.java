package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.*;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.alicloud.apsaradb.resmanager.response.UpgradeResRespModel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.Vpod;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.InsLevelExtraInfo;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.ecs.service.CustInstanceDBService;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.ecs.service.EcsImageService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.service.MysqlOnEcsDBCenterService;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_REMOVE_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_REMOVE;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Service
public class TransferK8sToPhysicalService {


    private static final LogAgent logger = LogFactory.getLogAgent(TransferK8sToPhysicalService.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected EcsService ecsService;
    @Autowired
    protected EcsImageService ecsImageService;
    @Autowired
    protected HostService ecsHostService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlOnEcsDBCenterService mysqlOnEcsDBCenterService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MysqlParamGroupHelper mysqlParamGroupHelper;
    @Autowired
    private CustInstanceDBService custInstanceDBService;
    @Autowired
    private EcsDBService ecsDBService;
    @Autowired
    private DbsService dbsService;
    @Autowired
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    private MinorVersionService minorVersionService;

    @Autowired
    private IpWhiteListService ipWhiteListService;

    @Autowired
    private AccountService accountService;
    @Autowired
    private InstanceIDao instanceIDao;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Autowired
    private DTZSupport dtzSupport;
    @Resource
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;


    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);


            String dbInstanceStatusDesc;
            // 获取目标实例Region
            Boolean isSameAvz = false;
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            String region = avzSupport.getMainLocation(params);
            AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
            String oldRegion = oldAvzInfo.getMainLocation();
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetMeta.getName(), null, null, null, null);
            Vpod replicaVpod = dBaasMetaService.getDefaultClient().getReplicaVpod(requestId, replicaListResult.getItems().get(0).getId(), null, null);
            if (avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.ClassicDispenseMode) &&
                    oldAvzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
                avzInfo = oldAvzInfo;
                isSameAvz = true;
            }
            if (!avzInfo.isValidForModify()) {
                avzInfo = oldAvzInfo;
                isSameAvz = true;
            }

            //获取版本
            String dbVersion = mysqlParamSupport.getDBVersion(params, custins.getDbType());
            dbInstanceStatusDesc = CustinsState.STATE_CLASS_CHANGING.getComment();

            if (dbVersion == null) {
                dbVersion = custins.getDbVersion();
            }
            //不支持版本升降级
            if (0 != dbVersion.compareTo(custins.getDbVersion())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            String strEcsTransType = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_TRANS_TYPE,
                    CustinsSupport.CUSTINS_TRANS_TYPE_DEFAULT);
            CustinsSupport.checkTransTypeValid(strEcsTransType);
            String levelCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
            InstanceLevelDO newLevel;
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if (Validator.isNull(levelCode)) {
                newLevel = oldLevel;
            } else {
                newLevel = instanceService.getInstanceLevelByClassCode(
                        levelCode, custins.getDbType(), dbVersion, custins.getTypeChar(), null);
            }


            String releaseDate = null;
            CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_KEY);
            if (custinsParamDO != null) {
                releaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(custinsParamDO.getValue());
            }
            if (releaseDate == null) {
                throw new RdsException(ErrorCode.MINOR_VERSION_TAG_NOT_FOUND_BY_CUSTINS);
            }

            List<String> releaseDateList = minorVersionService.getReleaseDateListByTag(
                    custins.getDbType(),
                    custins.getDbVersion(),
                    KindCodeParser.KIND_CODE_NC,
                    "standard",
                    MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_RPM
            );

            String expectedReleaseDate;
            if (releaseDateList != null) {
                expectedReleaseDate = releaseDateList.get(0);
            } else {
                throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
            }

            if (expectedReleaseDate.compareTo(releaseDate) < 0) {
                throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
            }


            //变配到物理机
            String specifyCluster = mysqlParamSupport.getParameterValue(params, ParamConstants.CLUSTER_NAME);
            String storage = mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE);
            String compressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, replicaSetMeta.getName(), null);
            if (CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
                // todo : modify errorCode
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }
            Set<Integer> specifyHostIdSet = mysqlParamSupport.getAndCheckHostIdSet(params);
            boolean isAcrossRegion;
            if (!StringUtils.isBlank(region) && !oldRegion.equals(region)) {
                isAcrossRegion = true;
            } else {
                region = oldRegion;
                isAcrossRegion = false;
            }

            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

            List<VipResModel> vipResModelList = new ArrayList<>();
            if (isAcrossRegion) {
                // vpc实例需要传入vpc信息才能跨可用区
                CustinsConnAddrDO vpcConnAddr = ConnAddrSupport.getVPCConnAddr(custinsConnAddrList);
                if (vpcConnAddr != null) {
                    // VPC 实例， 必须传入tunnelid， vswitchid， ipaddress
                    String vpcId = CheckUtils.checkValidForVPCId(
                            mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_ID));
                    String tunnelId = CheckUtils.checkValidForTunnelId(
                            mysqlParamSupport.getParameterValue(params, ParamConstants.TUNNEL_ID));
                    String vswitchId = CheckUtils.checkValidForVswitchId(
                            mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID));
                    String ipaddress = CheckUtils.checkValidForIPAddress(
                            mysqlParamSupport.getParameterValue(params, ParamConstants.IP_ADDRESS));
                    String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(
                            mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_INSTANCE_ID));

                    VipResModel vipResModel = new VipResModel(vpcConnAddr.getNetType());
                    vipResModel.setUserVisible(vpcConnAddr.getUserVisible());
                    vipResModel.setConnAddrCust(
                            mysqlParamSupport.getConnAddrCust(
                                    "tmp" + System.currentTimeMillis() + "-" + custins.getInsName().replace('_', '-'),
                                    replicaVpod.getRegionId(),
                                    custins.getDbType()));
                    vipResModel.setVip(ipaddress);
                    vipResModel.setVport(Integer.valueOf(vpcConnAddr.getVport()));
                    vipResModel.setVpcId(vpcId);
                    vipResModel.setTunnelId(CustinsValidator.getRealNumber(tunnelId, -1));
                    vipResModel.setVswitchId(vswitchId);
                    vipResModel.setVpcInstanceId(vpcInstanceId);
                    vipResModelList.add(vipResModel);
                }
            }

            Long diskSize;
            Integer maxDiskSize = resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE);
            if (StringUtils.isNotEmpty(storage)) {
                diskSize =
                        CheckUtils.parseInt(storage, 5, maxDiskSize, ErrorCode.INVALID_STORAGE)
                                * 1024L;
            } else {
                // ecs磁盘空间可能和物理机范围不一样，超过物理机磁盘空间的ecs不允许迁移物理机
                diskSize = custins.getDiskSize();
                if (diskSize > maxDiskSize * 1024) {
                    return createErrorResponse(ErrorCode.INVALID_STORAGE);
                }
            }

            //校验UTC时间，返回带时区的日期
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(params);
            String switchMode = mysqlParamSupport.getAndCheckSwitchTimeMode(params,
                    ParamConstants.SWITCH_TIME_MODE, utcDate, true);

            UpgradeResContainer container = avzSupport.getUpgradeResContainer(avzInfo);
            container.setRequestId(requestId);
            container.setClusterName(specifyCluster);
            container.setPreferClusterName(custins.getClusterName());
            container.setAccessId(CustinsParamSupport.getParameterValue(params, ParamConstants.ACCESSID));
            container.setOrderId(CustinsParamSupport.getParameterValue(params, ParamConstants.ORDERID));

            // init custins res model
            UpgradeCustinsResModel custinsResModel = new UpgradeCustinsResModel();
            custinsResModel.setCustinsId(custins.getId());

            // init host ins res model
            HostinsResModel hostinsResModel = new HostinsResModel(newLevel.getId());
            hostinsResModel.setHostType(newLevel.getHostType());
            hostinsResModel.setInsCount(newLevel.isMysqlEnterprise() ? 3 : 2);
            hostinsResModel.setDiskSizeSold(diskSize);
            // get disk size used
            try {
                InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
                hostinsResModel.setDiskSizeUsed(new BigDecimal(instancePerf.getDiskCurr()).longValue());
            } catch (Exception e) {
                logger.error("Get instance perf failed for custins: " + custins.getId(), e);
                hostinsResModel.setDiskSizeUsed(custins.getDiskSize());
            }

            // init distribute rule
            DistributeRule distributeRule = hostinsResModel.getDistributeRule();
            if (hostinsResModel.getInsCount() > 3) {
                //四节点
                distributeRule.setSiteDistributeMode(DistributeMode.AVG_SCATTER);
            } else {
                distributeRule.setSiteDistributeMode(DistributeMode.FORCE_SCATTER);
            }
            distributeRule.setCabinetDistributeMode(DistributeMode.TRY_SCATTER);

            distributeRule.setHostDistributeMode(DistributeMode.FORCE_SCATTER);
            distributeRule.setSpecifyHostIdSet(specifyHostIdSet);
            InsLevelExtraInfo.updateDistributeRule(distributeRule, newLevel.getExtraInfo());
            hostinsResModel.setDistributeRule(distributeRule);

            custinsResModel.setHostinsResModel(hostinsResModel);
            custinsResModel.setVipResModelList(vipResModelList);
            container.addUpgradeCustinsResModel(custinsResModel);
            container.setDbType(custins.getDbType());
            Response<UpgradeResRespModel> response = resApi.upgradeRes(container);
            UpgradeResRespModel respModel = response.getData();
            if (!response.getCode().equals(200)) {
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
            }

            if (!isSameAvz) {
                custinsParamService.updateAVZInfo(custins.getId(), avzInfo);
            }

            String inputDesc = mysqlParamSupport.getParameterValue(params, "DBInstanceStatusDesc");


            // category 升级
            mysqlParamGroupHelper.mysqlUpgradeUpdateSysParamGroupCategory(custins, newLevel.getCategory());


            Integer taskId = this.transMysqlDBTask(
                    mysqlParamSupport.getAction(params), mysqlParamSupport.getOperatorId(params), custins,
                    utcDate, diskSize,
                    newLevel, respModel, switchMode, inputDesc);
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(params));

            Map<String, Object> data = new HashMap<>(8);
            data.put("MigrationID", 0);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("SourceDBInstanceClass", oldLevel.getClassCode());
            data.put("TargetDBInstanceClass", newLevel.getClassCode());
            data.put("TaskId", taskId);
            data.put("Region", region);
            data.put("SwitchMode", switchMode);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public boolean isActiveOperation(String statusDesc) {
        return CustinsState.STATE_MAINTAINING.getComment().equals(statusDesc);
    }

    public Integer transMysqlDBTask(
            String action, Integer operatorId, CustInstanceDO custins,
            Date utcDate, Long diskSize, InstanceLevelDO newLevel, UpgradeResRespModel upgradeResRespModel,
            String switchMode, String inputDesc) throws RdsException {
        UpgradeResRespModel.CustinsResRespModel custinsResRespModel =
                upgradeResRespModel.getCustinsResRespModelList().get(0);

        TransListDO transList;
        List<Integer> srcInstanceIdList = custinsResRespModel.getSrcInstanceIdList();
        List<Integer> dstInstanceIdList = custinsResRespModel.getDstInstanceIdList();

        String statusDesc = StringUtils.isNotBlank(inputDesc) ? inputDesc : CustinsState.STATE_CLASS_CHANGING.getComment();
        // 通过desc判断该迁移任务是否是运维后台下发
        Integer lockMigrate = 0;
        if (isActiveOperation(statusDesc)) {
            lockMigrate = 1;
        }
        // local upgrade happened
        String taskKey = TaskSupport.TASK_TRANSFER;
        // remote upgrade
        CustInstanceDO tempCustins = null;
        Long timestamp = System.currentTimeMillis();
        tempCustins = custins.clone();
        tempCustins.setId(null);
        tempCustins.setInsName("tmp" + timestamp + "_" + custins.getInsName());
        tempCustins.setStatus(CUSTINS_STATUS_CREATING);
        tempCustins.setStatusDesc(CustinsState.STATE_CREATING.getComment());
        tempCustins.setIsTmp(CUSTINS_INSTYPE_TMP);
        tempCustins.setInsType(custins.getInsType());//必须和源实例一致
        tempCustins.setLevelId(newLevel.getId());
        // 有可能升级版本
        tempCustins.setDbVersion(newLevel.getDbVersion());
        tempCustins.setDiskSize(diskSize);
        tempCustins.setClusterName(custinsResRespModel.getClusterName());
        tempCustins.setConnType(custinsResRespModel.getConnType());
        tempCustins.setProxyGroupId(custinsResRespModel.getProxyGroupId());
        tempCustins.setKindCode(CustinsSupport.KIND_CODE_NC); //5.7单节点变配双节点的时候,需要将kind_code更改为0
        if (accountService.countAccountByCustInsId(custins.getId()) >= custins.getMaxAccounts()) {
            tempCustins.setAccountMode(CustinsSupport.CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED);
        }
        if (CustinsSupport.CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE.equals(tempCustins.getAccountMode())) {
            tempCustins.setMaxAccounts(500);
            tempCustins.setMaxDbs(500);
        }

        custinsService.createCustInstanceForTrans(custins, tempCustins);

        // update instance table
        List<Integer> tmpInstanceList = new ArrayList<>(dstInstanceIdList);
        tmpInstanceList.removeAll(srcInstanceIdList);
        instanceIDao.updateInstanceCustinsIdByInsIds(tmpInstanceList, tempCustins.getId());
        // update custins hostins rel table
        instanceIDao.updateCustinsHostinsRelByInsIds(tmpInstanceList, tempCustins.getId());

        List<Integer> custinsConnAddrIdList =
                custinsResRespModel.getCustinsConnAddrIdList();
        // update custins conn addr table
        connAddrCustinsService.updateCustinsConnAddrCustinsIdByIds(
                custinsConnAddrIdList, tempCustins.getId());


        // sync db && accounts
        dbsService.syncAllDbsAndAccounts(custins, tempCustins);

        // create aurora account for every custins exclude mongoDB
        dbsService.createAuroraProxyAccount(tempCustins);

        // sync all ip white list group
        ipWhiteListService.syncCustinsIpWhiteList(custins.getId(), tempCustins.getId());

        if (!custins.getDbVersion().equals(tempCustins.getDbVersion())) {
            statusDesc = CustinsState.STATE_VERSION_TRANSING.getComment();
        }

        transList = new TransListDO(custins, tempCustins,
                TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);


        transList.setsHinsid1(srcInstanceIdList.get(0));
        if (srcInstanceIdList.size() > 1) {
            transList.setsHinsid2(srcInstanceIdList.get(1));
        }
        transList.setdHinsid1(dstInstanceIdList.get(0));
        if (dstInstanceIdList.size() > 1) {
            transList.setdHinsid2(dstInstanceIdList.get(1));
        }
        transList.setdLevelid(newLevel.getId());
        transList.setdDisksize(diskSize);

        //计算元数据库时区时间
        //utcDate可能为null，即不是按照时间点切换
        Date metadbSwitchTime = null;
        if(utcDate != null){
            String utcSwitchTimeStr = dtzSupport.transDate2UtcStr(utcDate);
            metadbSwitchTime = dtzSupport.transUtcStr2MetadbDate(utcSwitchTimeStr, DataSourceMap.DATA_SOURCE_DBAAS);
        }
        transList.setSwitchTime(metadbSwitchTime);


        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_TRANS,
                statusDesc);


        instanceIDao.createTransList(transList);

        String taskparam = taskService.getTransTaskParameter(transList.getId(), switchMode, utcDate);
        // #13054224 mysql实例迁移任务下发,增加一个参数锁定迁移flag,如果为true表示运维后台下发,在任务流中解锁>迁移>锁定.
        Map<String, Object> param_map = new HashMap<String, Object>();
        param_map = JSON.parseObject(taskparam);
        param_map.put(CustinsSupport.LOCK_MIGRATE, lockMigrate);

        boolean isDiskReduction = judgeDiskReduction(custins.getDiskSize(), diskSize);
        param_map.put(CustinsSupport.DISK_REDUCTION, isDiskReduction);

        taskparam = JSON.toJSONString(param_map);

        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, custins.getId(),
                TASK_TYPE_CUSTINS, taskKey, taskparam);
        taskService.createTaskQueue(taskQueue);
        instanceIDao.updateTransTaskIdById(transList.getId(), taskQueue.getId());
        return taskQueue.getId();
    }


    public boolean judgeDiskReduction(Long oldSize, Long newSize) {
        if (oldSize == null || newSize == null) {
            return false;
        }
        return oldSize.compareTo(newSize) > 0;
    }

}