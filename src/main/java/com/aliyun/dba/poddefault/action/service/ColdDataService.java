package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ColdDataDisk;
import com.aliyun.apsaradb.activityprovider.model.OssConfig;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.backup.DescribeFileSystemParam;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeFileSystemResponse;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_MODIFY_COLD_OSS;

@Slf4j
@Service
public class ColdDataService {
    @Resource
    private DbsGateWayService dbsGateWayService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MySQLServiceImpl mySQLService;
    @Resource
    private DbossApi dbossApi;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private WorkFlowService workFlowService;

    private final static String COLD_DATA_SIZE="coldDataSize";
    private final static Long MIN_COLD_DATA_SIZE = 0L;
    private final static Long SUPPORT_V2_MIN_MINOR_VERSION = 20241130L;

    public List<Map<String, Object>> describeColdDataFromDboss(String replicaSetName, int pageNum, int pageSize) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        String requestId = RequestSession.getRequestId();
        ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, replicaSetName);
        Map<String, String> replicaSetLabels = replicaSetResource.getReplicaSet().getLabels();

        // check replicaSetLabel coldDataEnabled and coldDataType
        String coldDataEnabledStr = replicaSetLabels.get(ParamConstants.RDS_COLD_DATA_ENABLED);
        String coldDataType = replicaSetLabels.get(ParamConstants.RDS_COLD_DATA_TYPE);
        if (StringUtils.isBlank(coldDataEnabledStr) || StringUtils.isBlank(coldDataType) || !Boolean.parseBoolean(coldDataEnabledStr)) {
            log.info("ReplicaSet coldDataEnabled is invalid or false; requestId:{}, replicaSetName: {} , coldDataEnabeld: {}", requestId, replicaSetName, coldDataEnabledStr);
            return result;
        }
        //call dboss api to get cold data size for replicaSet
        boolean isSupportV2 = checkSupportV2(requestId, replicaSetName);
        if (isSupportV2) {
            result = dbossApi.queryColdTablesWithVersion(replicaSetResource.getReplicaSet().getId(), null, (pageNum - 1) * pageSize, pageSize, 2);
        } else {
            result = dbossApi.queryColdTables(replicaSetResource.getReplicaSet().getId(), null, (pageNum - 1) * pageSize, pageSize);
        }
        log.info("requestId:{} replicaSetName:{},  isSupportV2: {}, result:{}", requestId, replicaSetName, isSupportV2, result);
        return result;
    }

    private boolean checkSupportV2(String requestId, String replicaSetName) {
        boolean isSupportV2 = false;
        try {
            String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(requestId, replicaSetName);
            String releaseDate = podCommonSupport.getReleaseDate(requestId, serviceSpecTag, null);
            isSupportV2 = Long.parseLong(releaseDate) >= SUPPORT_V2_MIN_MINOR_VERSION;
        } catch (Exception e) {
            log.warn("requestId: {}, replicaSetName : {}, checkSupportV2 failed, use version 1. Exception: {}", requestId, replicaSetName, e.getMessage());
        }
        return isSupportV2;
    }

    public Map<String, Object> getColdDataSizeFromDboss(String replicaSetName) throws Exception {
        Map<String, Object> result = new HashMap<>();
        String requestId = RequestSession.getRequestId();
        ReplicaSetResource replicaSetResource =  dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, replicaSetName);
        Map<String,String> replicaSetLabels = replicaSetResource.getReplicaSet().getLabels();

        // check replicaSetLabel coldDataEnabled and coldDataType
        String coldDataEnabledStr = replicaSetLabels.get(ParamConstants.RDS_COLD_DATA_ENABLED);
        String coldDataType = replicaSetLabels.get(ParamConstants.RDS_COLD_DATA_TYPE);
        if (StringUtils.isBlank(coldDataEnabledStr) || StringUtils.isBlank(coldDataType) || !Boolean.parseBoolean(coldDataEnabledStr)) {
            log.info("ReplicaSet coldDataEnabled is invalid or false; requestId:{}, replicaSetName: {} , coldDataEnabeld: {}", requestId, replicaSetName, coldDataEnabledStr);
            return result;
        }
        //call dboss api to get cold data size for replicaSet
        result =  dbossApi.queryColdDataSize(replicaSetResource.getReplicaSet().getId());
        log.info("requestId:{} replicaSetName:{} result:{}", requestId, replicaSetName, result);
        return result;
    }
    public Map<String, Object> getColdDataSizeFromDbs(String replicaSetName) throws Exception {
        Map<String, Object> result = new HashMap<>();
        String requestId = RequestSession.getRequestId();
        ReplicaSetResource replicaSetResource =  dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, replicaSetName);
        Map<String,String> replicaSetLabels = replicaSetResource.getReplicaSet().getLabels();

        // check replicaSetLabel coldDataEnabled and coldDataType
        String coldDataEnabledStr = replicaSetLabels.get(ParamConstants.RDS_COLD_DATA_ENABLED);
        String coldDataType = replicaSetLabels.get(ParamConstants.RDS_COLD_DATA_TYPE);
        if (StringUtils.isBlank(coldDataEnabledStr) || StringUtils.isBlank(coldDataType) || !Boolean.parseBoolean(coldDataEnabledStr)) {
            log.info("ReplicaSet coldDataEnabled is invalid or false; requestId:{}, replicaSetName: {} , coldDataEnabeld: {}", requestId, replicaSetName, coldDataEnabledStr);
            return result;
        }

        //get bid, uid
        ReplicaSet replicaSet = replicaSetResource.getReplicaSet();
        String bid = replicaSet.getUserId().split("_")[0];
        String userId = replicaSet.getUserId().split("_")[1];


        //get ossConfig from master replica hostins_param
        Replica replica = mySQLService.getReplicaByRole(requestId,replicaSetName, Replica.RoleEnum.MASTER);
        Map<String, String> replicaLabelMap = dBaasMetaService.getDefaultClient().listReplicaLabels(requestId, replicaSetName, replica.getId());
        String ossConfigStr = replicaLabelMap.get(ParamConstants.RDS_COLD_OSS_CONFIG);
        OssConfig ossConfig = JSONObject.parseObject(ossConfigStr, OssConfig.class);
        if(!checkOssConfigValid(ossConfig)){
            log.info("RequestId {}  OssConfig {} is invalid", requestId, ossConfig);
            return result;
        }

        //get coldDataSize By dbsGatewayService
        DescribeFileSystemParam describeFileSystemParam = new DescribeFileSystemParam();
        describeFileSystemParam.setCallerBid(ossConfig.getBid());
        describeFileSystemParam.setUserId(ossConfig.getUserId());
        describeFileSystemParam.setRegionCode(ossConfig.getRegionId());
        describeFileSystemParam.setRequestId(requestId);
        describeFileSystemParam.setInstanceName(replicaSetName);
        describeFileSystemParam.setFsId(ossConfig.getJfsId());
        DescribeFileSystemResponse describeFileSystemResponse =  dbsGateWayService.describeFileSystem(describeFileSystemParam);
        if(Objects.nonNull( describeFileSystemResponse.getDataStorageSize())){
            result.put(COLD_DATA_SIZE, describeFileSystemResponse.getDataStorageSize());
        }
        return result;
    }


    public Long getColdDataSize(String replicaSetName) throws Exception {
        Map<String,Object>response = getColdDataSizeFromDboss(replicaSetName);
        Long coldDataSize = 0L;
        if(response.containsKey(COLD_DATA_SIZE) )
        {
            coldDataSize = Long.parseLong(String.valueOf(response.get(COLD_DATA_SIZE)));
        }
        return coldDataSize;
    }

    private Boolean checkOssConfigValid(OssConfig ossConfig) {
        return Objects.nonNull(ossConfig)
                && Objects.nonNull(ossConfig.getReplicaId())
                && Objects.nonNull(ossConfig.getBucket())
                && Objects.nonNull(ossConfig.getPrefix())
                && Objects.nonNull(ossConfig.getEndpoint())
                && Objects.nonNull(ossConfig.getRegionId())
                && Objects.nonNull(ossConfig.getUserId())
                && Objects.nonNull(ossConfig.getBid())
                && Objects.nonNull(ossConfig.getJfsId())
                && Objects.nonNull(ossConfig.getClientToken());
    }

    public Map<String, Object> modifyColdDataConfig(String replicaSetName, Boolean isColdDataEnabled, String regionId) throws Exception {
        String requestId = RequestSession.getRequestId();
        if (!isColdDataEnabled) {
            Long coldDataSize = this.getColdDataSize(replicaSetName);
            if(coldDataSize>MIN_COLD_DATA_SIZE){
                String errMsg = String.format("can't close coldDataEnabled when oss has cold data, please alter oss data first, coldDataSize is %s B",coldDataSize);
                log.error(errMsg);
                throw new RdsException(ErrorCode.UNSUPPORTED_CLOSE_COLD);
            }
        }

        // dispatch task
        JSONObject taskParamObject = new JSONObject();
        taskParamObject.put("requestId", requestId);
        taskParamObject.put("replicaSetName", replicaSetName);
        taskParamObject.put("isColdDataEnabled", isColdDataEnabled);
        taskParamObject.put("restartInstance", true);
        String taskParam = taskParamObject.toJSONString();
        String domain = PodDefaultConstants.DOMAIN_MYSQL;
        Object taskId = workFlowService.dispatchTask(
                "custins", replicaSetName, domain, TASK_MODIFY_COLD_OSS, taskParam, 0);


        dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetName,
                ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

        // build response
        Map<String, Object> data = new HashMap<>();
        data.put("DBInstanceName", replicaSetName);
        data.put("coldDataEnabled", isColdDataEnabled);
        data.put("TaskId", taskId);
        data.put("Region", regionId);
        return data;
    }

    public ColdDataDisk getColdDataDiskByColdDataEnabled(String requestId, Boolean coldDataEnabled, String regionId) throws Exception {
        return getColdDataDiskFromBackupSetId(requestId, coldDataEnabled, regionId,false,null,null,null,null, false);
    }

    public ColdDataDisk getColdDataDiskFromBackupSetId(String requestId, Boolean coldDataEnabled, String regionId, Boolean isRestoreColdData, String replicaSetName, String backupSetId, String bid, String uid, Boolean instanceIsDeleted) throws Exception {
        /**
         * 根据备份集情况确定克隆后实例的冷存开启情况
         * 1. 当前实例开启冷存，克隆实例一定开启冷存（无论备份集有无冷存数据）
         * 2、备份集中有冷存数据，克隆实例也一定开启冷存（无论当前实例是否开启冷存）
         * 3、只有当前实例关闭冷存并且备份集中没有冷存，那么克隆实例才会关闭冷存
         * 4. 如果isRestoreColdData为false，表示不需要恢复冷存数据
         * 4. instanceIsDeleted标识源实例是否删除，用于获取冷存快照
         */
        if(!isRestoreColdData || StringUtils.isBlank(backupSetId) || StringUtils.equalsIgnoreCase(backupSetId, "null"))
        {
            if(coldDataEnabled){
                return new ColdDataDisk().
                        coldDataEnabled(true).
                        coldDataType(ColdDataDisk.ColdDataTypeEnum.COREOSS).
                        regionId(regionId).
                        allocateReplicaOSSResource(true);
            }
            return null;
        }
        DescribeRestoreBackupSetParam describeRestoreBackupSetParam = new DescribeRestoreBackupSetParam();
        describeRestoreBackupSetParam.setRequestId(requestId);
        describeRestoreBackupSetParam.setBackupId(backupSetId);
        describeRestoreBackupSetParam.setCallerBid(bid);
        describeRestoreBackupSetParam.setUserId(uid);
        describeRestoreBackupSetParam.setInstanceName(replicaSetName);
        describeRestoreBackupSetParam.setRegionCode(regionId);
        describeRestoreBackupSetParam.setAccessId("Dukang");
        describeRestoreBackupSetParam.setInstanceRegion(regionId);
        describeRestoreBackupSetParam.setInstanceIsDeleted(instanceIsDeleted);

        DescribeRestoreBackupSetResponse describeRestoreBackupSetResponse = dbsGateWayService.describeRestoreBackupSet(describeRestoreBackupSetParam);
        log.info("describeRestoreBackupSetResponse:{}", describeRestoreBackupSetResponse);
        String coldDataSnapshotId = describeRestoreBackupSetResponse.getBackupSetInfo().getExtraInfo().getColdDataSnapshotId();
        String coldDataJFSId = describeRestoreBackupSetResponse.getBackupSetInfo().getExtraInfo().getColdDataFsId();
        if (coldDataEnabled ||
                (Objects.nonNull(coldDataSnapshotId) && Objects.nonNull(coldDataJFSId))
        ) {
            ColdDataDisk coldDataDisk = new ColdDataDisk().
                    coldDataEnabled(true).
                    coldDataType(ColdDataDisk.ColdDataTypeEnum.COREOSS).
                    regionId(regionId).
                    allocateReplicaOSSResource(true).
                    coldDataJFSId(coldDataJFSId).
                    coldDataSnapshotId(coldDataSnapshotId).
                    coldSourceInstanceName(replicaSetName).
                    coldSourceInstanceDeleted(instanceIsDeleted);
            return coldDataDisk;
        }
        return null;
    }


    public ColdDataDisk getColdDataDiskFromSnapshotOrBackupSetId(String requestId, Boolean coldDataEnabled, String regionId, Boolean isRestoreColdData, String replicaSetName, String backupSetId, String bid, String uid, Boolean instanceIsDeleted, String coldDataSnapshotId, String coldDataJFSId) throws Exception {
        if (!isRestoreColdData || StringUtils.isBlank(backupSetId) || StringUtils.equalsIgnoreCase(backupSetId, "null")) {
            if (coldDataEnabled) {
                return new ColdDataDisk().coldDataEnabled(true).coldDataType(ColdDataDisk.ColdDataTypeEnum.COREOSS).regionId(regionId).allocateReplicaOSSResource(true);
            }
            return null;
        }
        // if getting cold snapshot before ,use it.
        if (StringUtils.isNotBlank(coldDataSnapshotId) && StringUtils.isNotBlank(coldDataJFSId)) {
            return new ColdDataDisk().
                    coldDataEnabled(true).
                    coldDataType(ColdDataDisk.ColdDataTypeEnum.COREOSS).
                    regionId(regionId).
                    allocateReplicaOSSResource(true).
                    coldDataJFSId(coldDataJFSId).
                    coldDataSnapshotId(coldDataSnapshotId).
                    coldSourceInstanceName(replicaSetName).
                    coldSourceInstanceDeleted(instanceIsDeleted);
        }
        // not getting cold snapshot, call dbs api
        return getColdDataDiskFromBackupSetId(requestId, coldDataEnabled, regionId, isRestoreColdData, replicaSetName, backupSetId, bid, uid, instanceIsDeleted);
    }

}
