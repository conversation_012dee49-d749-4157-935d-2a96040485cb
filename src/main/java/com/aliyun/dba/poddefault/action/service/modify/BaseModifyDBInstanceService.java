package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.InternetProtocolEnum;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyVolumeSpecRequest;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.cache.LocalCacheService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.EvaluateEcsNodeService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.NetProtocolEnum;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.PoddefaultResourceGuaranteeModelService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.rule.ModifyInsRuleEngine;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.ACCESS_ID_RESOURCE;

@Service
public class BaseModifyDBInstanceService {
    protected static final LogAgent logger = LogFactory.getLogAgent(AliyunModifyDBInstanceService.class);
    protected static final int MAX_TMP_INSTANCE_NAME_LENGTH = 38;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected PodAvzSupport avzSupport;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    protected PodParameterHelper podParameterHelper;
    @Resource
    protected CustinsParamService custinsParamService;
    @Resource
    protected PodTemplateHelper podTemplateHelper;
    @Resource
    protected AligroupService aligroupService;
    @Resource
    protected AliyunInstanceDependency dependency;
    @Resource
    protected LocalCacheService cacheService;
    @Resource
    protected ModifyInsRuleEngine modifyInsRuleEngine;
    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private EvaluateEcsNodeService evaluateEcsNodeService;
    @Resource
    protected ResourceScheduleHelper resourceScheduleHelper;
    @Resource
    protected RundPodSupport rundPodSupport;

    @Resource
    protected PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;

    protected String getTaskKey(PodModifyInsParam modifyInsParam, TransferTask transferTask) throws Exception {
        String taskKey = null;
        modifyInsParam.setFlags(transferTask);
        if (modifyInsParam.isOnLineResize()) {
            // 单节点包含了XDB单节点形态，优先于isXDB判断
            if (modifyInsParam.isSingleNode()) {
                //单节点扩容方式（基础版/XDB单节点）
                taskKey = PodDefaultConstants.TASK_ONLINE_RESIZE_INS_FOR_BASIC;
            } else if (modifyInsParam.isXDB()) {
                //XDB的扩容方式
                if (modifyInsParam.isPfs()) {
                    taskKey = PodDefaultConstants.TASK_XDB_ONLINE_RESIZE_PFS_INS;
                } else {
                    taskKey = PodDefaultConstants.TASK_ONLINE_RESIZE_INS;
                }
            } else if (modifyInsParam.isCluster()){
                //cluster
                taskKey = PodDefaultConstants.TASK_ONLINE_RESIZE_INS_FOR_CLUSTER;
            } else {
                //高可用
                taskKey = PodDefaultConstants.TASK_ONLINE_RESIZE_INS;
            }
        } else if (!Objects.isNull(modifyInsParam.getTargetInstanceLevel()) && modifyInsParam.isPerfLevelChangedToPL0ForBasic()) {
            taskKey = PodDefaultConstants.TASK_MODIFY_BASIC_INS_FOR_PL1_TO_PL0;
        } else {
            taskKey = modifyInsRuleEngine.matchTask(modifyInsParam.getFlags());
        }
        logger.info("{} get task key {}", modifyInsParam.getRequestId(), taskKey);
        return taskKey;
    }

    /**
     * 入参初始化
     */
    public PodModifyInsParam initPodModifyInsParam(Map<String, String> params) throws Exception {
        PodModifyInsParam modifyInsParam = new PodModifyInsParam(dependency, params);

        // 初始化实例基础信息
        modifyInsParam
                .initCustins()
                .initReplicaSetMeta()
                .multiWriteEngineValidate();

        // 获取用户与实例属性信息
        modifyInsParam
                .initUser()
                .initInstanceName()
                .initDBType()
                .initDBVersion()
                .initClusterName()
                .initOrderId()
                .initTmpReplicaSetName();

        // 设置业务属性标
        modifyInsParam
                .setIsDHG()
                .setClusterNameToParamsForDHG()
                .setIsTDDL()
                .setIsReadIns()
                .setIsEmergencyTransfer()
                .initRoleHostNameMapping()
                .initAccessId();

        // 初始化迁移变配资源基本信息
        modifyInsParam
                .initSrcCompressionMode()
                .initTargetCompressionMode()
                .initDiskSizeGB()               // 获取原实例磁盘大小
                .initTargetDiskSizeGB()         // 获取目标磁盘大小
                .initClassCode()                // 获取原实例规格码
                .initTargetClassCode()          // 获取目标规格码
                .initSrcInstanceLevel()         // 获取原规格对象
                .initTargetInstanceLevel()      // 获取目标规格对象
                .initRcu()                      // 获取Serverless RCU
                .initSrcDiskType()              // 获取原磁盘类型
                .initTargetDiskType()           // 获取目标磁盘类型
                .initAutoPLConfig()              // 获取AutoPL配置
                .initColdDataConfig()           // 获取ColdData冷存配置
                .initPerfLevelChangedToPL0ForBasic()  // 获取是否是变更到PL0
                .initPodType()
                .initVbm()                      //设置vmoc-lite形态标签
                .initNetProtocol()
                .setIsDiskSizeChange()
                .setIsDiskTypeChange()
                .setGeneralCloudDiskConfig()    // Initialize general cloud disk
                .setIsTransIns()
                .setIsPfs()
                .setIsNetProtocolChange()
                .diskSizeValidation();

        // 检查密钥类型，多租户场景不允许创建使用用户密钥的云盘加密实例。
        modifyInsParam.checkEncryptionKey();
        // 初始化AZ相关信息
        modifyInsParam.initOldAVZInfo();
        if (!modifyInsParam.isReadIns()) {
            modifyInsParam.checkDepend("isReadIns").setDispenseMode();
        }

        if (modifyInsParam.getIsEmergencyTransfer()) {
            modifyInsParam.checkDepend("isEmergencyTransfer").setParamsForEmergencyTransfer();
        }
        modifyInsParam
                .initAVZInfo()
                .initRegionId()
                .setIsModifyAvz();

        // 初始化切换信息
        modifyInsParam.initSwitchInfo();

        // 初始化调度信息
        modifyInsParam
                .initIsSrcSingleTenant()
                .initIsTargetSingleTenant()
                .initModifyMode()               // 设置资源申请方式
                .initRsTemplate();

        // 检查云盘加密
        modifyInsParam.isEncryptionKeyAvailable();

        //Initialize optimized writes
        modifyInsParam.initOptimizedWritesInfo();

        // check compression limit
        modifyInsParam.checkCompressionLimit();


        return modifyInsParam;
    }

    /**
     * 构建ReplicaSet资源的变配结构体
     *
     * @param modifyInsParam
     * @param currentReplicas
     * @param isSingleTenant
     * @param podScheduleTemplate
     * @param modifyReplicas
     * @return
     * @throws Exception
     */
    public List<ModifyReplicaResourceRequest> buildModifyReplicaResourceRequests(PodModifyInsParam modifyInsParam,
                                                                                 List<Replica> currentReplicas,
                                                                                 boolean isSingleTenant,
                                                                                 PodScheduleTemplate podScheduleTemplate,
                                                                                 List<String> modifyReplicas) throws Exception {
        List<ModifyReplicaResourceRequest> replicaRequestList = new ArrayList<>();
        for (Replica replica : currentReplicas) {
            // 变配场景Logger角色不需要
            if (Replica.RoleEnum.LOGGER.equals(replica.getRole()) &&
                    ! modifyInsParam.isTransIns() && null == modifyInsParam.getRoleHostNameMapping().get(replica.getRole())) {
                continue;
            }
            if(Replica.RoleEnum.LOGGER.equals(replica.getRole()) && modifyInsParam.isPfs()){
                logger.warn("xdb pfs modify or migrate do not operation logger.");
                continue;
            }

            ModifyReplicaResourceRequest replicaRequest = buildModifyReplicaResourceRequest(modifyInsParam, isSingleTenant, podScheduleTemplate, replica);
            modifyReplicas.add(Objects.requireNonNull(replica.getId()).toString());
            replicaRequestList.add(replicaRequest);
        }
        return replicaRequestList;
    }

    /**
     * 构建Replica资源的变配结构体
     *
     * @param modifyInsParam
     * @param isSingleTenant
     * @param podScheduleTemplate
     * @param replica
     * @return
     * @throws Exception
     */
    protected ModifyReplicaResourceRequest buildModifyReplicaResourceRequest(PodModifyInsParam modifyInsParam, boolean isSingleTenant, PodScheduleTemplate podScheduleTemplate, Replica replica) throws Exception {
        ModifyReplicaResourceRequest replicaRequest = new ModifyReplicaResourceRequest();
        replicaRequest.setClassCode(modifyInsParam.getTargetClassCode());
        replicaRequest.setHostName(modifyInsParam.getRoleHostNameMapping().get(replica.getRole()));
        replicaRequest.setRole(Objects.requireNonNull(replica.getRole()).toString());
        replicaRequest.setZoneId(replica.getZoneId());
        if (podScheduleTemplate != null) {
            replicaRequest.setScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(podScheduleTemplate, replica.getRole().toString()));
        }

        List<VolumeSpec> volumeSpecList = new ArrayList<>();
        VolumeSpec dataVolumeSpec = new VolumeSpec();
        dataVolumeSpec.setName("data");
        dataVolumeSpec.setCategory("data");
        if (Replica.RoleEnum.LOGGER.equals(replica.getRole())) {
            buildReplicaRequestForLogger(modifyInsParam, replicaRequest, dataVolumeSpec);
        } else {
            buildReplicaRequestForNormal(modifyInsParam, replica, replicaRequest, dataVolumeSpec);
        }

        if (StringUtils.isEmpty(replicaRequest.getStorageType())) {
            replicaRequest.setStorageType(modifyInsParam.getTargetDiskType());
        }
        volumeSpecList.add(dataVolumeSpec);

        if (modifyInsParam.getReplicaSetMeta().getBizType() == ReplicaSet.BizTypeEnum.ALIGROUP) {
            // 集团实例不允许通过变配接口改变云盘等级
            VolumeSpec originDataVolumeSpec = replicaSetService.buildEssdVolumeSpec(replica, "data", modifyInsParam.getRequestId());
            if (originDataVolumeSpec != null) {
                dataVolumeSpec.setPerformanceLevel(originDataVolumeSpec.getPerformanceLevel());
            }
            VolumeSpec logVolumeSpec = replicaSetService.buildEssdVolumeSpec(replica, "log", modifyInsParam.getRequestId());
            if (logVolumeSpec != null) {
                volumeSpecList.add(logVolumeSpec);
            }
        }
        if (MysqlParamSupport.isCluster(modifyInsParam.getSrcInstanceLevel())) {  // 集群版需要做pod映射，无法做role映射
            ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(modifyInsParam.getRequestId(), replica.getId(), null);
            replicaRequest.setSourcePodId4ReuseDisk(replicaResource.getVpod().getVpodId());
        }
        replicaRequest.setVolumeSpecs(volumeSpecList);
        replicaRequest.setSingleTenant(isSingleTenant);
        // 设置rund 网络相关配置
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
            rundPodSupport.completeReplicaNetworkConfig(replicaRequest, replica);
        }
        return replicaRequest;
    }

    protected void buildReplicaRequestForNormal(PodModifyInsParam modifyInsParam, Replica replica, ModifyReplicaResourceRequest replicaRequest, VolumeSpec dataVolumeSpec) {
        if (ReplicaSetService.isStorageTypeCloudDisk(modifyInsParam.getTargetDiskType())) {
            setDiskSizeForCloudReplica(modifyInsParam, replica, replicaRequest, dataVolumeSpec);
        } else {
            replicaRequest.setDiskSize(modifyInsParam.getTargetDiskSizeGB());
            dataVolumeSpec.setDiskSize(modifyInsParam.getTargetDiskSizeGB());
        }
    }

    protected void setDiskSizeForCloudReplica(PodModifyInsParam modifyInsParam,
                                              Replica replica,
                                              ModifyReplicaResourceRequest replicaRequest,
                                              VolumeSpec dataVolumeSpec) {
        //设置云盘性能等级
        dataVolumeSpec.setPerformanceLevel(modifyInsParam.getTargetPerformanceLevel());
        if (modifyInsParam.isDiskSizeChange()) {
            //如果磁盘大小有变化，需要做云盘需要有赠送
            int diskSizeGB = podParameterHelper
                    .getExtendDiskSizeGBForPod(modifyInsParam.getReplicaSetMeta().getBizType(), false, modifyInsParam.getTargetDiskSizeGB());
            replicaRequest.setDiskSize(diskSizeGB);
            dataVolumeSpec.setDiskSize(diskSizeGB);
            return;
        }

        // 磁盘大小没有变化，一定要取replica中的diskSizeMB，这里面存储的是赠送后的大小
        replicaRequest.setDiskSize(replica.getDiskSizeMB() / 1024);
        dataVolumeSpec.setDiskSize(replica.getDiskSizeMB() / 1024);
    }

    protected void buildReplicaRequestForLogger(PodModifyInsParam modifyInsParam, ModifyReplicaResourceRequest replicaRequest, VolumeSpec dataVolumeSpec) throws Exception {
        InstanceLevelListResult instanceLevels = dBaasMetaService
                .getDefaultClient().listInstanceLevelChildren(
                        modifyInsParam.getRequestId(),
                        modifyInsParam.getDbType(),
                        modifyInsParam.getDbVersion(),
                        modifyInsParam.getTargetClassCode());
        InstanceLevel loggerLevel = Objects.requireNonNull(instanceLevels.getItems()).stream()
                .filter(x -> Objects.requireNonNull(x.getClassCode()).contains("logger"))
                .collect(Collectors.toList())
                .get(0);

        if (PodParameterHelper.isAliGroup(modifyInsParam.getReplicaSetMeta().getBizType())) {
            // 支持集团Logger磁盘类型可配置
            String loggerStorageType = cacheService.getValueOrDefault("ALIGROUP_XDB_LOGGER_STORAGE_TYPE", "local_ssd");
            replicaRequest.setStorageType(loggerStorageType);

            // 兼容集团Logger规格
            String loggerClassCode = aligroupService.getAligroupLoggerClassCode(loggerStorageType);
            loggerLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(
                    modifyInsParam.getRequestId(),
                    modifyInsParam.getDbType(),
                    modifyInsParam.getDbVersion(),
                    loggerClassCode, true);
        }

        replicaRequest.setClassCode(loggerLevel.getClassCode());
        replicaRequest.setDiskSize(loggerLevel.getDiskSizeMB() / 1024);
        dataVolumeSpec.setDiskSize(loggerLevel.getDiskSizeMB() / 1024);
    }


    public ModifyReplicaSetResourceRequest getModifyReplicaSetResourceRequest(PodModifyInsParam modifyInsParam, String tmpReplicaSetName) {
        ModifyReplicaSetResourceRequest replicaSetResourceRequest = new ModifyReplicaSetResourceRequest();
        if (StringUtils.isNotBlank(modifyInsParam.getClusterName()) && modifyInsParam.isDHG()) {
            replicaSetResourceRequest.setDedicatedHostGroupId(modifyInsParam.getClusterName());
        }
        replicaSetResourceRequest.setReplicaSetName(modifyInsParam.getDbInstanceName());
        replicaSetResourceRequest.setUserId(modifyInsParam.getBid());
        replicaSetResourceRequest.setUid(modifyInsParam.getUid());
        replicaSetResourceRequest.setTmpReplicaSetName(tmpReplicaSetName);
        replicaSetResourceRequest.setDbType(modifyInsParam.getDbType());
        replicaSetResourceRequest.setDbVersion(modifyInsParam.getDbVersion());
        replicaSetResourceRequest.setClassCode(modifyInsParam.getTargetClassCode());
        replicaSetResourceRequest.diskSize(modifyInsParam.getTargetDiskSizeGB());
        replicaSetResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
        replicaSetResourceRequest.setScheduleTemplate(modifyInsParam.getScheduleTemplate());
        if (NetProtocolEnum.IPv4IPv6.equals(modifyInsParam.getTargetNetProtocol())) {
            replicaSetResourceRequest.setInternetProtocol(InternetProtocolEnum.IPV4IPV6);
        }
        replicaSetResourceRequest.setServerlessRcu(modifyInsParam.getRcu());
        return replicaSetResourceRequest;
    }

    public Integer buildTransListForOnlineResize(PodModifyInsParam modifyInsParam, List<Replica> currentReplicas) throws ApiException, com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        Integer transListId;
        List<ModifyVolumeSpecRequest> modifyVolumeSpecRequests = getModifyVolumeSpecRequestsForOnlineResize(modifyInsParam, currentReplicas);
        transListId = commonProviderService.getDefaultApi()
                .makeIncreaseVolumeSizeTransList(
                        modifyInsParam.getRequestId(),
                        modifyInsParam.getDbInstanceName(),
                        modifyVolumeSpecRequests);
        return transListId;
    }


    /**
     * 构建本地升降配的translist
     *
     * @return
     */
    protected Integer buildTransListForLocalModify(PodModifyInsParam modifyInsParam) throws Exception {
        String replicaSetName = modifyInsParam.getReplicaSetMeta().getName();

        TransferTask transferTask = new TransferTask();
        transferTask.setSrcReplicaSetName(replicaSetName);
        transferTask.setSrcClassCode(modifyInsParam.getClassCode());
        transferTask.setSrcDiskSizeMB(modifyInsParam.getDiskSizeGB() * 1024);

        transferTask.setDestReplicaSetName(replicaSetName);
        transferTask.setDestClassCode(modifyInsParam.getTargetClassCode());
        transferTask.setDestDiskSizeMB(modifyInsParam.getTargetDiskSizeGB() * 1024);
        transferTask.setType(TransferTask.TypeEnum.REMOVE);
        transferTask.setComment("Local Modify ClassCode");

        Map<String, Object> transParam  = new HashMap<>();
        transParam.put("srcDiskType", modifyInsParam.getSrcDiskType());
        transParam.put("targetDiskType", modifyInsParam.getTargetDiskType());
        transParam.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
        transParam.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
        //适配资源保障策略
        Map<String, String> allResourceGuaranteeLevelMap = poddefaultResourceGuaranteeModelService.getAllResourceGuaranteeLevelMapForUid(modifyInsParam.getUid());
        if (MapUtils.isNotEmpty(allResourceGuaranteeLevelMap)) {
            transParam.put("resourceGuaranteeLevel", allResourceGuaranteeLevelMap.get("resourceGuaranteeLevel"));
            transParam.put("resourceGuaranteeLevelType", allResourceGuaranteeLevelMap.get("resourceGuaranteeLevelType"));
            //强制指定机型时 备选资源保障标签可能不存在
            transParam.put("resourceGuaranteeBackUpLevels", allResourceGuaranteeLevelMap.getOrDefault("resourceGuaranteeBackUpLevels", ""));
        }
        transferTask.setParameter(JSON.toJSONString(transParam));

        TransferTask ret = dBaasMetaService.getDefaultClient().createTransferTask(modifyInsParam.getRequestId(), replicaSetName, transferTask);
        return ret.getId();
    }


    protected List<ModifyVolumeSpecRequest> getModifyVolumeSpecRequestsForOnlineResize(PodModifyInsParam modifyInsParam, List<Replica> currentReplicas) {
        List<ModifyVolumeSpecRequest> modifyVolumeSpecRequests = new ArrayList<>();
        for (Replica replica : currentReplicas) {
            // Logger 节点不需要变配
            if (Replica.RoleEnum.LOGGER.equals(replica.getRole())) {
                continue;
            }
            ModifyVolumeSpecRequest modifyVolumeSpecRequest = new ModifyVolumeSpecRequest();
            VolumeSpec dataVolumeSpec = new VolumeSpec();
            dataVolumeSpec.setName("data");
            dataVolumeSpec.setCategory("data");
            dataVolumeSpec.setDiskSize(modifyInsParam.getTargetDiskSizeGB());
            modifyVolumeSpecRequest.setRole(Objects.requireNonNull(replica.getRole()).toString());
            modifyVolumeSpecRequest.setVolumeSpec(dataVolumeSpec);
            modifyVolumeSpecRequests.add(modifyVolumeSpecRequest);
        }
        return modifyVolumeSpecRequests;
    }



    public static String getTempReplicaSetNamePrefix(String requestId, String dbInstanceName, String orderId, int maxTmpInstanceNameLength) throws RdsException {
        String tmpReplicaSetNamePrefix = orderId != null ? orderId : requestId;
        if (dbInstanceName.length() >= maxTmpInstanceNameLength) {
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR,
                    "Generate tmp instance name failed, instance name too long",
                    "Generate tmp instance name failed, instance name too long"});
        }
        if (tmpReplicaSetNamePrefix.length() > maxTmpInstanceNameLength - dbInstanceName.length()) {
            tmpReplicaSetNamePrefix = tmpReplicaSetNamePrefix.substring(0, maxTmpInstanceNameLength - dbInstanceName.length());
        }

        return tmpReplicaSetNamePrefix;
    }


    /**
     * 是否能本地升降级
     *
     * @param modifyInsParam
     * @return
     */
    protected boolean isSingleTenantLocalModify(PodModifyInsParam modifyInsParam) throws Exception {
        //单租户之间的升降配走本地升降级
        boolean isSrcSingleTenant = podParameterHelper.isSingleTenant(modifyInsParam.getReplicaSetMeta());
        boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();
        if (PodParameterHelper.isAliYun(modifyInsParam.getReplicaSetMeta().getBizType()) &&
                isSrcSingleTenant && isTargetSingleTenant) {
            //单租户间的变配是走本地升降机，需要做ECS的资源评估
            //评估当前节点是否能支持本地变配
            return evaluateEcsNodeService.evaluateReplicaSetCanLocalUpgrade(modifyInsParam.getRequestId(),
                    modifyInsParam.getReplicaSetMeta(), modifyInsParam.getTargetInstanceLevel());
        }
        return false;
    }

    /**
     * 统一封装TransferId和Transfer String两种类型的TransferTask获取方式
     * */
    public TransferTask getTransferTask(PodModifyInsParam modifyInsParam, Object transfer) throws com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        TransferTask transferTask = null;
        if (transfer instanceof Integer) {
            logger.info("{} get TransferTask by id {}", modifyInsParam.getRequestId(), transfer);
            transferTask = dBaasMetaService.getDefaultClient().getTransferTask(modifyInsParam.getRequestId(), modifyInsParam.getReplicaSetMeta().getName(), (Integer) transfer);
        } else if (transfer instanceof String) {
            logger.info("{} get TransferTask by String {}", modifyInsParam.getRequestId(), transfer);
            transferTask = JSONObject.parseObject(transfer.toString(), TransferTask.class);
        }
        return transferTask;
    }

    /**
     * 申请资源变配场景，支持Serverless & 普通实例
     * */
    public Object modifyWithAllocateResource(PodModifyInsParam modifyInsParam, List<Replica> currentReplicas, List<String> modifyReplicas) throws Exception{
        Object transfer = null;
        String tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName();
        ModifyReplicaSetResourceRequest replicaSetResourceRequest = getModifyReplicaSetResourceRequest(modifyInsParam, tmpReplicaSetName);
        if (modifyInsParam.isTransByNc()) {
            // NC时强制申请新盘
            replicaSetResourceRequest.reuseCloudDisk(false);
        }
        PodScheduleTemplate podScheduleTemplate = modifyInsParam.getPodScheduleTemplate();

        List<ModifyReplicaResourceRequest> replicaRequestList = buildModifyReplicaResourceRequests(
                modifyInsParam, currentReplicas, modifyInsParam.isTargetSingleTenant(), podScheduleTemplate, modifyReplicas);
        replicaSetResourceRequest.setDiskSize(modifyInsParam.getTargetDiskSizeGB());
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
            rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
        }
        replicaSetResourceRequest.setVswitchID(mysqlParamSupport.getParameterValue(modifyInsParam.getParams(), ParamConstants.VSWITCH_ID, null));
        replicaSetResourceRequest.setReplicaResourceRequestList(replicaRequestList);
        replicaSetResourceRequest.setModifyMode(modifyInsParam.getModifyMode());
        replicaSetResourceRequest.setReplicaResourceRequestList(replicaRequestList);
        if (modifyInsParam.getIsEmergencyTransfer()) {
            replicaSetResourceRequest.setModifyMode(ModifyReplicaSetResourceRequest.ModifyModeEnum.MIGRATE);
        }
        if (modifyInsParam.isTargetSingleTenant()) { // 目标规格是单租户场景
            replicaSetResourceRequest.setSingleTenant(true);
            replicaSetResourceRequest.setEniDirectLink(false); //单租户不需要申请ENI网卡直连
            if (modifyInsParam.isSrcSingleTenant()) {
                //原和目标规格都单租户场景，变配策略强制迁移跨机申请新资源
                replicaSetResourceRequest.setModifyMode(ModifyReplicaSetResourceRequest.ModifyModeEnum.MIGRATE);
                //单租户申请资源算力保障
                Pair<String, String> resourceEnsurancePair = resourceScheduleHelper.makeResourceGuaranteeStrategy(modifyInsParam.getUid(), modifyInsParam.getReplicaSetMeta().getName());
                if (resourceEnsurancePair != null) {
                    Map<String, String> resourceLabel = new HashMap<>();
                    resourceLabel.put(resourceEnsurancePair.getLeft(), resourceEnsurancePair.getRight());
                    if (MapUtils.isNotEmpty(replicaSetResourceRequest.getLabels())) {
                        resourceLabel.putAll(replicaSetResourceRequest.getLabels());
                    }
                    replicaSetResourceRequest.setLabels(resourceLabel);
                    logger.info("use resource ensure.insName:{},requestId:{},uid:{}", modifyInsParam.getDbInstanceName(), modifyInsParam.getRequestId(), modifyInsParam.getUid());
                }
            }
        }

        if (modifyInsParam.isPerfLevelChangedToPL0ForBasic()) { // 基础版变更到PL0，走迁移变配的流程
            replicaSetResourceRequest.setModifyMode(ModifyReplicaSetResourceRequest.ModifyModeEnum.MIGRATE);
            replicaSetResourceRequest.setAllocateDisk(false);  // 不申请磁盘，在下层使用migrate_basic_ins创建磁盘
        }

        // build labels
        replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.LABEL_ORDER_ID, modifyInsParam.getOrderId());
        replicaSetResourceRequest.putLabelsItem(ACCESS_ID_RESOURCE, modifyInsParam.getAccessId());

        // 不申请反向VPC的资源
        replicaSetResourceRequest.setIgnoreCreateVpcMapping(true);

        // 测试环境单租户mock ENI
        podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest, modifyInsParam.getReplicaSetMeta());

        logger.info("start allocate resource for scale, {}", JSON.toJSONString(replicaSetResourceRequest));
        // Serverless场景使用不生成trans_list的接口
        if (PodCommonSupport.isServerless(modifyInsParam.getTargetInstanceLevel())) {
            transfer = commonProviderService.getDefaultApi().allocateReplicaSetResourceForScaleTransTask(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), replicaSetResourceRequest);
        } else {
            transfer = commonProviderService.getDefaultApi().allocateReplicaSetResourceForScale(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), replicaSetResourceRequest);
        }

        return transfer;
    }




    /**
     * 构建任务流参数
     * */
    public JSONObject buildTaskParam(PodModifyInsParam modifyInsParam, TransferTask transferTask, List<String> modifyReplicas) throws Exception {
        JSONObject taskParamObject = new JSONObject();
        Optional<Replica> masterReplica = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(modifyInsParam.getRequestId(), transferTask.getDestReplicaSetName(),
                        null, null, null, null).getItems().stream().filter(r -> r.getRole().equals(Replica.RoleEnum.MASTER))
                .findFirst();
        taskParamObject.put("requestId", modifyInsParam.getRequestId());
        taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
        taskParamObject.put("transTaskId", transferTask.getId());
        taskParamObject.put("emergencyTransfer", modifyInsParam.getIsEmergencyTransfer());
        if (modifyInsParam.getRoleHostNameMapping().containsKey(Replica.RoleEnum.MASTER)) {
            taskParamObject.put("preferMasterReplicaId", masterReplica.map(Replica::getId).orElse(null));
        }
        if (modifyInsParam.getAvzInfo() != null && modifyInsParam.getAvzInfo().getMultiAVZExParamDO().getMasterAvailableZoneInfo() != null){
            taskParamObject.put("preferMasterZoneId", modifyInsParam.getAvzInfo().getMasterZoneId());
        }
        taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
        taskParamObject.put("isForceNc", mysqlParamSupport.getAndCheckIsForce(modifyInsParam.getParams()));
        if (!modifyReplicas.isEmpty()) {
            taskParamObject.put("modifyReplicas", StringUtils.join(modifyReplicas, ","));
        }

        taskParamObject.put("netProtocol", modifyInsParam.getTargetNetProtocol().name());

        // 以下参数传给Online Resize使用
        taskParamObject.put("srcDiskType", modifyInsParam.getSrcDiskType());
        taskParamObject.put("targetDiskType", modifyInsParam.getTargetDiskType());
        taskParamObject.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
        taskParamObject.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
        if (PodCommonSupport.isServerless(modifyInsParam.getTargetInstanceLevel())) {
            taskParamObject.put("transferTask", transferTask);
            taskParamObject.put("rcu", modifyInsParam.getRcu());
        }
        // Serverless支持本地升配过程中忽略CPU配置
        taskParamObject.put("serverlessIgnoreCpu", podParameterHelper.getParameterValue("ServerlessIgnoreCpu", false));

        // 基础版 PL1 -> PL0 参数
        if (modifyInsParam.isPerfLevelChangedToPL0ForBasic()) {
            Replica srcReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), null, null, null, null).getItems().get(0);
            taskParamObject.put("srcReplicaId", srcReplica.getId());
            taskParamObject.put("destReplicaId", masterReplica.get().getId());
            taskParamObject.put("srcReplicaSetName", modifyInsParam.getDbInstanceName());
            taskParamObject.put("destReplicaSetName", transferTask.getDestReplicaSetName());
        }

        return taskParamObject;
    }
}
