package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.poddefault.action.service.AutomaticSlaveZoneService;
import com.aliyun.dba.poddefault.action.service.migrate.MigrateDBInstanceAvzService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.commonkindcode.support.ParamTransHelper.SYS_PARAM_GROUP_ID_PREFIX;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.LOCAL_MODIFY_POD_MODES;

@Service
public class ModifyDBInstanceFromStandardToClusterService extends BaseModifyDBInstanceService {

    @Resource
    private ModifyDBInstanceDiskTypeService modifyDBInstanceDiskTypeService;

    @Resource
    private MigrateDBInstanceAvzService migrateDBInstanceAvzService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private AutomaticSlaveZoneService automaticSlaveZoneService;

    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;

    @Resource
    private PodAvzSupport podAvzSupport;

    /**
     * 高可用实例升级为集群版实例
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {

        DefaultApi metaApi = dBaasMetaService.getDefaultClient();
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        Object transfer = null;
        AllocateTmpResourceResult result = null;
        // 标记是否申请资源
        boolean isAllocResource = false;
        // 标记请求是否成功
        boolean isSuccess = false;
        PodModifyInsParam modifyInsParam = null;
        // 标记升级时所需资源是否一致
        boolean isSameResource = false;
        // 设置任务优先级
        int priority = 0;

        try {
            // 初始化变配参数
            modifyInsParam = initPodModifyInsParam(params);
            // 控制台请求，在什么都没有发生改变的情况下，直接返回成功
            if (modifyInsParam.isAliyun() && modifyInsParam.isTransIns() &&
                    (modifyInsParam.isYaoChiRequest() || podParameterHelper.isSingleTenant(modifyInsParam.getReplicaSetMeta()))) {
                logger.info("{} nothing has changed, return it.", modifyInsParam.getRequestId());
                return getResponse(modifyInsParam, null);
            }

            podCommonSupport.checkHaToClusterCondition(requestId, modifyInsParam.getReplicaSetMeta(), modifyInsParam.getSrcInstanceLevel());

            isSameResource = isSameResource(modifyInsParam);
            // 所需资源一致时，走直接升级集群版的任务流
            if (isSameResource) {
                ReplicaListResult listReplicasInReplicaSet = metaApi.listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), null, 6, null, null);
                List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
                TransferTask transferTask = null;

                boolean isStorageChange = isStorageChange(modifyInsParam);
                if (isStorageChange) {
                    //创建transTask
                    Integer transId = buildTransListForOnlineResize(modifyInsParam, currentReplicas);
                    transferTask = getTransferTask(modifyInsParam, transId);
                }

                podReplicaSetResourceHelper.configResource4Cluster(requestId, modifyInsParam.getReplicaSetMeta());

                // dispatch task
                String taskKey = PodDefaultConstants.TASK_MODIFY_INS_HA_TO_CLUSTER_DIRECTLY;
                JSONObject taskParamObject = new JSONObject();
                taskParamObject.put("requestId", modifyInsParam.getRequestId());
                taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
                taskParamObject.put("destClassCode", modifyInsParam.getTargetClassCode());
                taskParamObject.put("srcDiskSizeMB", modifyInsParam.getDiskSizeGB() * 1024);
                taskParamObject.put("destDiskSizeMB", modifyInsParam.getTargetDiskSizeGB() * 1024);
                taskParamObject.put("srcDiskType", modifyInsParam.getSrcDiskType());
                taskParamObject.put("destDiskType", modifyInsParam.getTargetDiskType());
                taskParamObject.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
                taskParamObject.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
                if (transferTask != null) {
                    taskParamObject.put("transTaskId", transferTask.getId());
                }
                taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
                String taskParam = taskParamObject.toJSONString();

                String domain = PodDefaultConstants.DOMAIN_MYSQL;
                Object taskId = workFlowService.dispatchTask("custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, priority);

                metaApi.updateReplicaSetStatus(requestId, modifyInsParam.getDbInstanceName(), ReplicaSet.StatusEnum.CLASS_CHANGING.toString());
                return getResponse(modifyInsParam, taskId);
            } else if (modifyInsParam.isIoAccelerationEnabledChange()) {
                AVZInfo avzInfo = modifyInsParam.getAvzInfo();

                ReplicaListResult replicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                                modifyInsParam.getRequestId(),
                                modifyInsParam.getDbInstanceName(),
                                null,
                                null,
                                null,
                                null);

                if (modifyInsParam.isIoAccelerationEnabled()) {
                    isAZSatisfiesIOAccelerationCriteria(modifyInsParam, replicas);
                }

                Replica masterReplica = replicas.getItems()
                        .stream()
                        .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER))
                        .findFirst()
                        .get();

                Replica slaveReplica = replicas.getItems()
                        .stream()
                        .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.SLAVE))
                        .findFirst()
                        .get();

                String masterZoneId = masterReplica.getZoneId();
                String slaveZoneId = slaveReplica.getZoneId();
                String slaveSubDomain  = automaticSlaveZoneService.getLocationByAz(slaveZoneId);

                Map<Replica.RoleEnum, String> azMap = Collections.singletonMap(Replica.RoleEnum.SLAVE, slaveZoneId);
                Map<Replica.RoleEnum, String> subDomainMap = new HashMap<>();
                if (!slaveReplica.getZoneId().equalsIgnoreCase(masterZoneId)) {
                    logger.info("Use slave subDomain: {}", slaveSubDomain);
                    subDomainMap.put(Replica.RoleEnum.SLAVE, slaveSubDomain);
                }

                result = allocateTmpResourceService.make(requestId,
                        custins,
                        modifyInsParam,
                        String.valueOf(InstanceLevel.CategoryEnum.CLUSTER),
                        Arrays.asList(Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE),
                        azMap,
                        subDomainMap);


                isAllocResource = result.isAllocated();
                Integer transListId = result.getTransList().getId();

                // 集群版实例资源配置
                ReplicaSet destReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, result.getReplicaSet().getName(), false);
                ReplicaSet srcReplicaSet = modifyInsParam.getReplicaSetMeta();
                if (MysqlParamSupport.isCluster(String.valueOf(modifyInsParam.getTargetInstanceLevel().getCategory()))) {
                    podReplicaSetResourceHelper.configResource4Cluster(requestId, destReplicaSet);
                }

                // 审计日志管控参数补全
                custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
                // 更新参数模板（先记录到临时实例中，之后在任务流中进行替换）
                updateParamGroupId(requestId, metaApi, modifyInsParam.getDbInstanceName(), modifyInsParam.getTmpReplicaSetName());


                String taskKey = PodDefaultConstants.TASK_MODIFY_INS_FOR_IO_ACCELERATION;
                // dispatch task
                JSONObject taskParamObject = new JSONObject();
                taskParamObject.put("requestId", modifyInsParam.getRequestId());
                taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
                taskParamObject.put("transTaskId", transListId);
                taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
                // 以下参数传给Online Resize使用
                taskParamObject.put("srcDiskType", modifyInsParam.getSrcDiskType());
                taskParamObject.put("targetDiskType", modifyInsParam.getTargetDiskType());
                taskParamObject.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
                taskParamObject.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
                taskParamObject.put("targetCategory", InstanceLevel.CategoryEnum.CLUSTER.toString());

                String taskParam = taskParamObject.toJSONString();
                String domain = PodDefaultConstants.DOMAIN_MYSQL;
                Object taskId = workFlowService.dispatchTask("custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);

                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                        modifyInsParam.getRequestId(),
                        modifyInsParam.getDbInstanceName(),
                        modifyInsParam.isTransIns() ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.CLASS_CHANGING.toString()
                );


                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
                data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
                data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
                data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
                data.put("TaskId", taskId);
                data.put("Region", modifyInsParam.getRegionId());
                isSuccess = true;

                // build response
                return getResponse(modifyInsParam, taskId);
            }
            // 所需资源不一致时，走跨机升级任务流
            else {
                ReplicaListResult listReplicasInReplicaSet = metaApi.listReplicasInReplicaSet(requestId, modifyInsParam.getDbInstanceName(), null, 6, null, null);
                List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
                assert currentReplicas != null;

                String tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName();
                PodModifyInsParam.replicaNameValidate(currentReplicas, tmpReplicaSetName);

                // 记录哪些节点申请了资源，用于在N副本场景下感知节点对应关系
                List<String> modifyReplicas = new ArrayList<>();

                isAllocResource = true;
                transfer = modifyWithAllocateResource(modifyInsParam, currentReplicas, modifyReplicas);
                TransferTask transferTask = getTransferTask(modifyInsParam, transfer);

                // 集群版实例资源配置
                ReplicaSet replicaSet = metaApi.getReplicaSet(requestId, modifyInsParam.getDbInstanceName(),false);
                if (MysqlParamSupport.isCluster(modifyInsParam.getTargetInstanceLevel().getCategory().toString())) {
                    podReplicaSetResourceHelper.configResource4Cluster(requestId, replicaSet);
                }

                // 更新参数模板（先记录到临时实例中，之后在任务流中进行替换）
                updateParamGroupId(requestId, metaApi, modifyInsParam.getDbInstanceName(), modifyInsParam.getTmpReplicaSetName());

                // dispatch task
                String taskKey = PodDefaultConstants.TASK_MODIFY_INS_HA_TO_CLUSTER;
                JSONObject taskParamObject = buildTaskParam(modifyInsParam, transferTask, modifyReplicas);
                String taskParam = taskParamObject.toJSONString();

                String domain = PodDefaultConstants.DOMAIN_MYSQL;
                Object taskId = workFlowService.dispatchTask("custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, priority);
                isSuccess = true;

                metaApi.updateReplicaSetStatus(requestId, modifyInsParam.getDbInstanceName(), ReplicaSet.StatusEnum.CLASS_CHANGING.toString());
                // build response
                return getResponse(modifyInsParam, taskId);
            }
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (isAllocResource && (transfer != null || result != null) && !isSuccess && modifyInsParam != null) {
                try {
                    // 本地变配的情况下，使用rollback接口
                    if (LOCAL_MODIFY_POD_MODES.contains(modifyInsParam.getModifyMode())) {
                        logger.info("{} rollback resource with rollback api.", requestId);
                        commonProviderService.getDefaultApi().rollbackReplicaSetResourceForScale(
                                modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                                modifyInsParam.getTmpReplicaSetName(), new ArrayList<>());
                    } else {
                        logger.info("{} rollback resource with release api.", requestId);
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                                requestId, modifyInsParam.getTmpReplicaSetName()
                        );
                    }

                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error("{} rollback resource failed: {}", requestId, e.getResponseBody());
                }
            }
        }
    }

    /**
     * 判断存储相关资源的是否一致
     */
    private boolean isStorageChange(PodModifyInsParam modifyInsParam) throws Exception {
        // PL
        String srcPL = modifyInsParam.getSrcPerformanceLevel();
        String targetPL = modifyInsParam.getTargetPerformanceLevel();
        if (!StringUtils.equals(srcPL, targetPL)) {
            return true;
        }

        // 存储容量
        Integer srcDiskSizeGB = modifyInsParam.getDiskSizeGB();
        Integer targetDiskSizeGB = modifyInsParam.getTargetDiskSizeGB();
        if (!Objects.equals(srcDiskSizeGB, targetDiskSizeGB)) {
            return true;
        }

        return false;
    }

    /**
     *  判断两个规格使用的资源是否一致
     */
    private boolean isSameResource(PodModifyInsParam modifyInsParam) throws RdsException, Exception {
        InstanceLevel srcInstanceLevel = modifyInsParam.getSrcInstanceLevel();
        InstanceLevel targetInstanceLevel = modifyInsParam.getTargetInstanceLevel();
        if (srcInstanceLevel == null) {
            throw new Exception("srcInstanceLevel can not be null.");
        }
        if (targetInstanceLevel == null) {
            throw new Exception("targetInstanceLevel can not be null.");
        }

        // CPU
        if (!Objects.equals(srcInstanceLevel.getCpuCores(), targetInstanceLevel.getCpuCores())) {
            return false;
        }
        // Mem
        if (!Objects.equals(srcInstanceLevel.getMemSizeMB(), targetInstanceLevel.getMemSizeMB())) {
            return false;
        }
        // 规格族
        if (!Objects.equals(srcInstanceLevel.getIsolationType(), targetInstanceLevel.getIsolationType())) {
            return false;
        }
        // host_type
        if (!Objects.equals(srcInstanceLevel.getHostType(), targetInstanceLevel.getHostType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CURRENT_HOST_TYPE);
        }
        // 架构
        String srcArch = JSON.parseObject(srcInstanceLevel.getExtraInfo()).getString("instructionSetArch");
        String targetArch = JSON.parseObject(srcInstanceLevel.getExtraInfo()).getString("instructionSetArch");
        if (srcArch == null) {
            srcArch = CreateReplicaSetDto.ArchEnum.X86.toString();
        }
        if (targetArch == null) {
            targetArch = CreateReplicaSetDto.ArchEnum.X86.toString();
        }
        if (!srcArch.equalsIgnoreCase(targetArch)) {
            return false;
        }
        // 经济型，targetExtraInfo为true为经济性
        JSONObject srcExtraInfo = JSONObject.parseObject(srcInstanceLevel.getExtraInfo());
        JSONObject targetExtraInfo = JSONObject.parseObject(targetInstanceLevel.getExtraInfo());
        boolean isSrcEconomic = srcExtraInfo != null && Objects.equals(srcExtraInfo.get("use_economic_pool"), true);
        boolean isTargetEconomic = targetExtraInfo != null && Objects.equals(targetExtraInfo.get("use_economic_pool"), true);
        if (!Objects.equals(isSrcEconomic, isTargetEconomic)) {
            return false;
        }

        // turning on io acceleration must change resource
        if (modifyInsParam.isIoAccelerationEnabledChange()) {
            return false;
        }

        return true;
    }

    public void updateParamGroupId(String requestId, DefaultApi metaApi, String replicaSetName, String tmpReplicaSetName) throws RdsException, ApiException {
        // 更新系统参数模板
        ReplicaSet replicaSet = metaApi.getReplicaSet(requestId, replicaSetName,false);
        CustinsParamDO paramGroupIdDO = custinsParamService.getCustinsParam(replicaSet.getId().intValue(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
        if (paramGroupIdDO == null) {
            logger.warn("custins {}'s params are null.", replicaSet.getId());
            return;
        }

        String paramGroupId = paramGroupIdDO.getValue();
        if (StringUtils.isEmpty(paramGroupId)) {
            logger.warn("paramGroupId is null. replicaset id is {}.", replicaSet.getId());
            return;
        }

        ReplicaSet tmpReplicaSet = null;
        String newParamGroupId;
        if (StringUtils.isNotEmpty(tmpReplicaSetName)) {
            tmpReplicaSet = metaApi.getReplicaSet(requestId, tmpReplicaSetName,false);
        }
        if (paramGroupId.startsWith(SYS_PARAM_GROUP_ID_PREFIX)) {
            Map<String, Object> paramGroupInfo = SysParamGroupHelper.describeSysParamGroupId(paramGroupId);
            paramGroupInfo.put("category", InstanceLevel.CategoryEnum.CLUSTER.toString());
            Map<String, String> map = paramGroupInfo.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> (String) e.getValue()));
            newParamGroupId = SysParamGroupHelper.getSysParamGroupId(map);

            // 如果有临时实例，先更新至临时实例，之后在任务流内进行切换
            logger.info("tmpId: {}, newParamGroupId: {}", tmpReplicaSet.getId().intValue(), newParamGroupId);
            custinsParamService.setCustinsParam(tmpReplicaSet.getId().intValue(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, newParamGroupId);
            custinsParamService.setCustinsParam(tmpReplicaSet.getId().intValue(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, map.toString());
            // next: 在任务流内切换管控参数
        } else {
            // 如果是用户参数模板，直接应用到临时实例
            custinsParamService.setCustinsParam(tmpReplicaSet.getId().intValue(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
        }
    }

    /**
     * 接口返回
     * */
    private Map<String, Object> getResponse(PodModifyInsParam modifyInsParam, Object taskId) {
        Map<String, Object> data = new HashMap<>();
        data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
        data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
        data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
        data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
        data.put("TaskId", taskId);
        data.put("Region", modifyInsParam.getRegionId());
        return data;
    }

    /**
     *  主备可用区是否满足Io加速条件
     */
    private void isAZSatisfiesIOAccelerationCriteria(PodModifyInsParam modifyInsParam, ReplicaListResult replicas) throws RdsException, Exception {
        String regionCode = modifyInsParam.getAvzInfo().getRegionId();

        String masterZoneId = replicas.getItems()
                .stream()
                .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER))
                .findFirst()
                .get()
                .getZoneId();;

        String slaveZoneId = replicas.getItems()
                .stream()
                .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.SLAVE))
                .findFirst()
                .get()
                .getZoneId();

        logger.info("isAZSatisfiesIOAccelerationCriteria regionCode: {}, masterAz: {}, slaveAz: {}", regionCode, masterZoneId, slaveZoneId);

        List<String> azList = automaticSlaveZoneService.getAvailableZones(regionCode, modifyInsParam.getGeneralCloudDisk());

        if (azList == null || azList.isEmpty() || azList.contains(masterZoneId) || azList.contains(slaveZoneId)) {
            logger.info("{} the masterAz {} and slaveAz {} does not satisfy the available zone for enabling IO acceleration. The azList is {}",
                    modifyInsParam.getRequestId(), masterZoneId, slaveZoneId,JSONObject.toJSONString(azList));
            throw new RdsException(ErrorCode.INVALID_AVZONE);
        }
    }
}
