package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointGroup;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointService;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointServiceFactoryImpl;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteDBInstanceEndpointAddressImpl")
@Slf4j
public class DeleteDBInstanceEndpointAddressImpl implements IAction {

    @Resource
    private EndPointServiceFactoryImpl endpointFactory;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private ReplicaSetService replicaSetService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {


        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);  // use DBInstanceName
            EndPointService endPointService = endpointFactory.getService(replicaSet.getConnType());
            String replicaSetName = replicaSet.getName();
            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            if (!MysqlParamSupport.isCluster(replicaSet.getCategory())) {  // 暂时限制只有 Cluster
                return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
            }
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            log.info("{} start modify endpoint", replicaSetName);

            // data prepare
            String endpointId = paramSupport.getParameterValue(params, "DBInstanceEndpointId");
            String connString = paramSupport.getParameterValue(params, ParamConstants.CONNECTION_STRING);

            EndpointGroup endpointGroup = endPointService.getAndCheckEndpointGroup(requestId, replicaSetName, endpointId);
            Endpoint endpoint = endPointService.getAndCheckEndpoint(requestId, replicaSetName, connString, endpointGroup.getId());

            // 暂时只支持删除 public
            if (endpoint.getNetType() != Endpoint.NetTypeEnum.PUBLIC) {
                throw new RdsException(ErrorCode.INVALID_NET_TYPE);
            }

            // delete
            String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            JSONObject taskParam = new JSONObject();
            taskParam.put(ParamConstants.REQUEST_ID, requestId);
            taskParam.put("endpointName", endpointId);
            taskParam.put("connectionString", connString);
            //  inactive replicaSet
            endPointService.inactiveReplicaSet(requestId, replicaSetName);
            Object taskId = workFlowService.dispatchTask("custins", replicaSetName, domain, PodDefaultConstants.TASK_DELETE_INS_ENDPOINT_ADDRESS, taskParam.toJSONString(), 0);

            Map<String, Object> rtn = new HashMap<>();
            rtn.put("RequestId", requestId);
            rtn.put("DBInstanceName", replicaSetName);
            rtn.put("DBInstanceEndpointId", endpointId);
            rtn.put("connectionString", connString);
            rtn.put("TaskId", taskId);
            return rtn;
        } catch (ApiException ex) {
            log.error("DeleteDBInstanceEndpointAddress, Api called failed: ", ex);
            throw new RdsException(ErrorCode.API_CALLING_FAILED, "Meta db calling failed");
        } catch (RdsException ex) {
            log.error("DeleteDBInstanceEndpointAddress failed: ", ex);
            throw ex;
        } catch (Exception e) {
            log.error("DeleteDBInstanceEndpointAddress Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
