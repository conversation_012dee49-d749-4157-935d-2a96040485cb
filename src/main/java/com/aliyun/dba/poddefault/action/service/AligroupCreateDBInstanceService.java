package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.apsaradb.activityprovider.model.InternetProtocolEnum;
import com.aliyun.apsaradb.activityprovider.model.ReplicaDto;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevelListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.cache.LocalCacheService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodCreateInsParam;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.ALIGROU_DHG_PARTERN;
import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.base.support.MySQLParamConstants.REPLICASET_BIZ_TYPE_ALGROUP;
import static com.aliyun.dba.base.support.MySQLParamConstants.TIME_ZONE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_STORAGE_TYPE;

@Service
public class AligroupCreateDBInstanceService {
    private static final LogAgent logger = LogFactory.getLogAgent(AligroupCreateDBInstanceService.class);

    @Autowired
    protected MysqlParamSupport paramSupport;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MySQLAvzService mySQLAvzService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected AligroupService aligroupService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private LocalCacheService cacheService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;

    public Map<String, Object> createDBIntance(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String dbEngine = "XDB";
            String bid = paramSupport.getAndCheckBID(params);
            String uid = paramSupport.getUID(params);
            String dbInstanceName = paramSupport.getDBInstanceName(params);
            String dbType = paramSupport.getAndCheckDBType(params, null);
            String dbVersion = paramSupport.getAndCheckDBVersion(params, dbType, true);
            String storageEngine = paramSupport.getAndCheckStorageEngine(params);
            // XDB如果只传了一个机房，需要补充机房
            replicaSetService.mazPreCheckForXDB(params, dbEngine);
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            String regionId = avzInfo.getRegionId();
            String subDomain = avzInfo.getRegion();
            String timeZone = paramSupport.hasParameter(params, TIME_ZONE)
                    ? getParameterValue(params, TIME_ZONE) : getParameterValue(params, "DBTimeZone");
            String charSet = paramSupport.getAndCheckCharacterSetName(params, dbType, dbVersion);
            // 如果是传了 TDDL 以外链路类型, 则表示需要创建一个没有 TDDL 链路的实例
            String connType = paramSupport.getParameterValue(params, "DBInstanceConnType", CONN_TYPE_TDDL);
            String dbName = paramSupport.getDBName(params);
            String portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), dbType);
            String diskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE, "local_ssd");
            Integer diskSize = CheckUtils.parseInt(
                    paramSupport.getParameterValue(params, ParamConstants.STORAGE),
                    5, 102400, ErrorCode.INVALID_STORAGE);
            String nodeGroup = getParameterValue(params, "NodeGroup", "mix");
            String classCode = paramSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
            String tddlRegionConfig = paramSupport.getParameterValue(params, "TddlRegionConfig");
            String forMigrate = paramSupport.getParameterValue(params, "ForMigrate");
            String tddlClusterName = paramSupport.getParameterValue(params, "TddlClusterName");
            // 网络设置 IPv4 / IPv6 (暂不支持) / IPv4IPv6 双栈
            String netProtocol = paramSupport.getParameterValue(params, "NetProtocol", "IPv4");
            // 主机架构, ARM 或者 X86
            String instructionSetArch = paramSupport.getParameterValue(params, "InstructionSetArch");
            String clusterName = paramSupport.getParameterValue(params, "ClusterName");
            String bizType = REPLICASET_BIZ_TYPE_ALGROUP;
            getAndCreateUser(bid, uid, requestId, "");

            // 构建实例对象 replicaSet
            CreateReplicaSetDto replicaSet = new CreateReplicaSetDto();
            // 实例标签信息
            Map<String, String> labels = new HashMap<>();

            if (CONN_TYPE_TDDL.equals(connType)) {
                bizType = REPLICASET_BIZ_TYPE_ALGROUP;
                clusterName = clusterName != null ? clusterName : String.format(ALIGROU_DHG_PARTERN, regionId);
                tddlClusterName = PodCreateInsParam.reFormatTddlClusterName(dbName, tddlClusterName, "true".equalsIgnoreCase(forMigrate));
                String clusterId = DigestUtils.md5DigestAsHex(tddlClusterName.toUpperCase().getBytes());

                replicaSet.setClusterId(clusterId);
                replicaSet.setStorageEngine(storageEngine);
                replicaSet.setTimeZone(timeZone);
                replicaSet.setCharset(charSet);
                replicaSet.setClusterName(tddlClusterName);
                replicaSet.setAppDbList(Arrays.asList(dbName.split("\\|")));
                replicaSet.setAppName(tddlClusterName);

                labels.put("TddlBiztype", "non_unit");
                labels.put("TddlRegionConfig", tddlRegionConfig);
                labels.put("appDbList", JSON.toJSONString(dbName.split("\\|")));
                labels.put("bizType", bizType);
                labels.put("charset", charSet);
                labels.put("clusterName", tddlClusterName);
                labels.put("dbEngine", dbEngine);
                labels.put("dbType", dbType);
                labels.put("dbVersion", dbVersion);
                labels.put("requestId", requestId);
                labels.put("timeZone", timeZone);
                labels.put("uid", uid);
                labels.put("userId", bid);
            }
            if (replicaSetService.isStorageTypeCloudDisk(diskType)) {
                labels.put("composeTag", "cloud_pfs");
            }

            labels.put("instructionSetArch", instructionSetArch);

            // FIXME 因为核心集群的大促规格没有配置商品和定价, 这里会做一个 hack, 后面区分配置管理
            classCode = aligroupService.getPromotionClassCode(requestId, tddlClusterName, classCode);
            // 检验实例实例已经存在
            if (dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, true) != null) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }
            // 备份相关
            Integer bakRetention = CheckUtils.parseInt(getParameterValue(params, ParamConstants.BACKUP_RETENTION, 7),
                    1, 730,
                    ErrorCode.INVALID_BACKUPRETENTIONPERIOD);
            String preferredBackupTime = getParameterValue(params, "PreferredBackupTime");
            if (!DateSupport.isminuteonly_utc(preferredBackupTime)) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PREFERREDBACKUPTIME);
            }
            String preferredBackupPeriod = CheckUtils.checkValidForBackupPeriod(getParameterValue(params, "preferredbackupperiod"));

            replicaSet.setUserId(bid);
            replicaSet.setUid(uid);
            replicaSet.setPort(portStr);
            replicaSet.setInstanceId(dbInstanceName);
            replicaSet.setReplicaSetName(dbInstanceName);
            replicaSet.setDbType(dbType);
            replicaSet.setDbVersion(dbVersion);
            replicaSet.setDbEngine(dbEngine);
            replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN.toString());
            replicaSet.setRequestId(requestId);
            replicaSet.setClassCode(classCode);
            replicaSet.setConnType(connType);
            replicaSet.setClusterName(tddlClusterName);

            if (StringUtils.isNotEmpty(instructionSetArch)) {
                if (!CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(instructionSetArch)
                        && !CreateReplicaSetDto.ArchEnum.X86.toString().equalsIgnoreCase(instructionSetArch)) {
                    return ResponseSupport.createErrorResponse(MysqlErrorCode.INVALID_INSTRUCTIONSET_ARCH.toArray());
                }
                replicaSet.setArch(CreateReplicaSetDto.ArchEnum.valueOf(instructionSetArch));
            }
            replicaSet.setDedicatedHostGroupId(clusterName);
            replicaSet.setBizType(bizType);
            replicaSet.setDiskSize(diskSize);
            if (aligroupService.isTddlClusterNeedAllocateDedicatedResourceGroup(requestId, tddlClusterName)) {
                replicaSet.setDedicatedBizGroup(tddlClusterName);
            }
            replicaSet.setStorageType(diskType);
            if (replicaSetService.isStorageTypeCloudDisk(diskType)) {
                replicaSet.setComposeTag("cloud_pfs");
            }

            //检查实例指定指定资源调度模板创建
            String rsTemplateName = paramSupport.getParameterValue(params, "RsTemplateName");
            if (Strings.isEmpty(rsTemplateName)) {
                rsTemplateName = "TEMPLATE_" + tddlClusterName;
            }
            PodScheduleTemplate podScheduleTemplate = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, rsTemplateName, bid + "_" + uid);

            if (podScheduleTemplate != null) {
                replicaSet.setScheduleTemplate(podTemplateHelper.getReplicaSetScheduleTemplate(podScheduleTemplate));
            }

            // Build replicas
            List<ReplicaDto> replicas = new ArrayList<>();
            String[] nodeRoles = "XDB".equals(dbEngine) ? new String[]{"master", "follower", "logger"} : new String[]{"master", "slave"};
            for (String role : nodeRoles) {
                ReplicaDto replica = new ReplicaDto();
                replica.setId(String.format("%s-%s", dbInstanceName, role));
                replica.setRole(role);
                replica.setRegionId(regionId);
                replica.setSubDomain(subDomain);
                replica.setZoneId(mySQLAvzService.getRoleZoneId(avzInfo, role).getZoneID());
                replica.setDiskType(diskType);
                replica.setStorageType(diskType);
                if (podScheduleTemplate != null) {
                    replica.setScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(podScheduleTemplate, role));
                }
                if ("logger".equalsIgnoreCase(role)) {
                    InstanceLevelListResult instanceLevels = dBaasMetaService
                            .getDefaultClient().listInstanceLevelChildren(requestId, dbType, dbVersion, paramSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS));
                    InstanceLevel loggerLevel = instanceLevels.getItems().stream()
                            .filter(x -> x.getClassCode().contains("logger")).collect(Collectors.toList()).get(0);
                    // 集团需要 2c4g 的 logger
                    if (aligroupService.isAligroupDHG(regionId, clusterName)) {
                        String loggerStorageType = cacheService.getValueOrDefault("ALIGROUP_XDB_LOGGER_STORAGE_TYPE", "local_ssd");
                        replica.setStorageType(loggerStorageType);
                        replica.setDiskType(loggerStorageType);
                        String loggerClassCode = aligroupService.getAligroupLoggerClassCode(loggerStorageType);
                        replica.setClassCode(loggerClassCode);
                        InstanceLevel aligroupLoggerLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, loggerClassCode, true);
                        replica.setDiskSize(aligroupLoggerLevel.getDiskSizeMB() / 1024);
                    } else {
                        replica.setClassCode(loggerLevel.getClassCode());
                        replica.setDiskSize(loggerLevel.getDefaultDiskSizeMB() / 1024);
                    }
                } else {
                    // FIXME 迁移交易和库存需要固定的规格, 由于没法录入到一套, 只能这里 hack, 迁移上云完后去掉
                    replica.setClassCode(classCode);
                    if (replicaSetService.isStorageTypeCloudDisk(diskType)) {
                        replica.setVolumeSpecs(replicaSetService.getCloudDiskReplicaVolumeSpecList(diskSize, dbInstanceName));
                    }
                    replica.setDiskSize(diskSize);
                }
                replica.setNodeGroup(nodeGroup);
                replica.setNodeType("logger".equals(role) ? "log" : "normal");
                replicas.add(replica);
            }
            replicaSet.setReplicaDtoList(replicas);

            // 设置指定的资源池类型 IPv4 / IPv6 / IPv4IPv6
            switch (netProtocol) {
                case "IPv4":
                    replicaSet.setInternetProtocol(InternetProtocolEnum.IPV4);
                    break;
                case "IPv6":
                    replicaSet.setInternetProtocol(InternetProtocolEnum.IPV6);
                    break;
                case "IPv4IPv6":
                    replicaSet.setInternetProtocol(InternetProtocolEnum.IPV4IPV6);
                    break;
                default:
                    return createErrorResponse(ErrorCode.INVALID_PARAM, "internetProtocolNotSupport");
            }

            try {
                // Allocate resource
                logger.info("allocateReplicaSetResourceRequest: {}", JSONObject.toJSONString(replicaSet));
                CreateReplicaSetDto response = commonProviderService.getDefaultApi().allocateReplicaSetResource(replicaSet);
                // Persist replicaSet meta data
                logger.info("allocateReplicaSetResourceRequestResponse: {}", JSONObject.toJSONString(response));
                commonProviderService.getDefaultApi().writeReplicaSetMeta(response);
            } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                logger.error(requestId + " CommonProvider error: " + e.getResponseBody());
                // 释放元数据和资源分配
                commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, dbInstanceName);
                return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND);
            }

            if(CONN_TYPE_TDDL.equals(connType)) {
                String minorVersion = minorVersionServiceHelper.resetReplicaSetMinorVersion(requestId, dbInstanceName);
                if (StringUtils.isNotBlank(minorVersion)) {
                    labels.put("minor_version", minorVersion);
                }
            }

            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, dbInstanceName, labels);
            //指定实例资源调度模板的写入实例和模版名关联
            ReplicaSet insReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, true);
            podTemplateHelper.createCustInsAndTemplcateRelation(insReplicaSet.getId().intValue(), rsTemplateName);

            // Add work flow task
            String domain = StringUtils.equalsIgnoreCase("xdb", dbEngine) ? "xdb" : "mysql";
            String taskKey = "create_ins";

            JSONObject taskParameter = new JSONObject();
            taskParameter.put("requestId", replicaSet.getRequestId());
            taskParameter.put("replicaSetName", replicaSet.getInstanceId());
            taskParameter.put("dbName", dbName);
            taskParameter.put("charSet", charSet);
            taskParameter.put("instructionSetArch", instructionSetArch);

            // 备份信息
            // 备份保留时间, 比如 7 天
            taskParameter.put("bakRetention", bakRetention);
            // 每周哪几天备份, 比如 0101010
            taskParameter.put("preferredBackupPeriod", preferredBackupPeriod);
            // 备份时间, UTC, 比如 06:35Z
            taskParameter.put("preferredBackupTime", preferredBackupTime);

            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, taskParameter.toJSONString(), 0);

            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("DBInstanceName", dbInstanceName);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getResponseBody());
            return createErrorResponse(new Object[]{ResultCode.CODE_ERROR, "AllocateResouceFailed", "Allocate resource failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private User getAndCreateUser(String bid, String uid, String requestId, String bizType) throws ApiException {
        String loginId = String.format("%s_%s", bid, uid);
        try {
            return dBaasMetaService.getDefaultClient().getUser(requestId, loginId, false);
        } catch (ApiException e) {
            if (404 == e.getCode()) {
                User newUser = new User();
                newUser.setBid(bid);
                newUser.setAliUid(uid);
                newUser.setUserId(loginId);
                newUser.setName(loginId);
                newUser.setBizType(bizType);
                return dBaasMetaService.getDefaultClient().createUser(requestId, newUser);
            }
        }
        return dBaasMetaService.getDefaultClient().getUser(requestId, loginId, true);
    }
}