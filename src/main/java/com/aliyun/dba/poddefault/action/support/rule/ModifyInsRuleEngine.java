package com.aliyun.dba.poddefault.action.support.rule;

import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.flag.MyFlag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.aliyun.dba.poddefault.action.support.PodModifyInsParam.*;

/**
 * 新架构实例迁移变配规则引擎
 * <p>
 * Author 宇一 2021/5/1
 */
@Component
public class ModifyInsRuleEngine {
    private static final List<Rule> rules = new ArrayList<>();

    static {
        // 集团(XDB)
        rules.add(new TaskModifyTDDLReadIns());
        rules.add(new TaskModifyTDDLDoubleReadIns());
        rules.add(new TaskModifyTDDLInsByNC());
        rules.add(new TaskModifyTDDLIns());
        rules.add(new TaskXDBMigrateInsByNC());
        rules.add(new TaskXDBMigratePFSIns());
        rules.add(new TaskXDBModifyIns());
        rules.add(new TaskModifyXDBIns());
        rules.add(new TaskXDBMigrateReadInsByNC());

        // 云上
        rules.add(new TaskMySQLModifyIns());
        rules.add(new TaskMySQLModifyInsHA());
        rules.add(new TaskMySQLMigrateFailOver());
        rules.add(new TaskElasticModifyIns());
        rules.add(new TaskElasticModifyServerlessBasic());
        rules.add(new TaskMySQLModifyInsCluster());

    }

    public String matchTask(MyFlag flags) throws Exception {
        Set<String> matchTasks = rules.stream().filter(r -> r.matchCheck(flags)).map(rule -> rule.getResult(flags)).collect(Collectors.toSet());
        if (matchTasks.size() == 0) {
            throw new Exception("no task matched!");
        }

        if (matchTasks.size() > 1) {
            throw new Exception("matching to the multiple rules: " + StringUtils.join(matchTasks, ","));
        }

        return matchTasks.stream().findFirst().get();
    }

    /**
     * XDB--TDDL实例--迁移变配--NC
     */
    static class TaskModifyTDDLInsByNC implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ENTERPRISE)
                    && flags.hasFlags(F_TDDL)
                    && !flags.hasFlags(F_READ_INS)
                    && flags.hasFlags(F_TRANS_NC);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_XDB_MODIFY_TDDL_INS;
        }
    }

    /**
     * XDB--TDDL实例--PFS云盘--迁移变配--无NC
     */
    static class TaskModifyTDDLIns implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ENTERPRISE)
                    && flags.hasFlags(F_TDDL)
                    && flags.hasFlags(F_PFS)
                    && !flags.hasFlags(F_READ_INS)
                    && !flags.hasFlags(F_TRANS_NC);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_XDB_MODIFY_TDDL_PFS_INS;
        }
    }

    /**
     * XDB--非TDDL实例--PFS云盘--迁移变配--无NC
     */
    static class TaskXDBMigratePFSIns implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ENTERPRISE)
                    && flags.hasFlags(F_PFS)
                    && !flags.hasFlags(F_TDDL)
                    && !flags.hasFlags(F_READ_INS)
                    && !flags.hasFlags(F_TRANS_NC);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_XDB_MIGRATE_PFS_INS;
        }
    }

    /**
     * XDB--非TDDL实例--本地盘--迁移变配--NC
     */
    static class TaskXDBMigrateInsByNC implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ENTERPRISE)
                    && flags.hasFlags(F_LOCAL_SSD)
                    && !flags.hasFlags(F_TDDL)
                    && !flags.hasFlags(F_READ_INS)
                    && !flags.hasFlags(F_SINGLE_NODE);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_XDB_MIGRATE_INS_BY_NC;
        }
    }


    /**
     * XDB--非TDDL实例--本地盘--迁移变配--NC
     */
    static class TaskXDBMigrateReadInsByNC implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ENTERPRISE)
                    && flags.hasFlags(F_READ_INS)
                    && !flags.hasFlags(F_TDDL)
                    && !flags.hasFlags(F_SINGLE_NODE);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_XDB_MIGRATE_READ_INS_BY_NC;
        }
    }

    /**
     * XDB--非TDDL实例--云盘地盘--迁移变配
     */
    static class TaskModifyXDBIns implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ENTERPRISE)
                    && !flags.hasFlags(F_PFS)
                    && !flags.hasFlags(F_LOCAL_SSD)
                    && !flags.hasFlags(F_READ_INS)
                    && !flags.hasFlags(F_TDDL)
                    && !flags.hasFlags(F_SINGLE_NODE);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_MODIFY_XDB_INS;
        }
    }


    /**
     * XDB--单节点--迁移变配
     */
    static class TaskXDBModifyIns implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_SINGLE_NODE)
                    && !flags.hasFlags(F_EMERGENCY_TRANS);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_MODIFY_INS;
        }
    }


    /**
     * XDB只读--TDDL实例--云盘/本地盘--迁移变配
     */
    static class TaskModifyTDDLReadIns implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ENTERPRISE)
                    && flags.hasFlags(F_TDDL)
                    && flags.hasFlags(F_READ_INS)
                    && !flags.hasFlags(F_DOUBLE_NODE);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_MODIFY_TDDL_READ_INS;
        }
    }

    /**
     * XDB只读双节点--TDDL实例--云盘/本地盘--变配
     */
    static class TaskModifyTDDLDoubleReadIns implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_XDB)
                && flags.hasFlags(F_ENTERPRISE)
                && flags.hasFlags(F_TDDL)
                && flags.hasFlags(F_READ_INS)
                && flags.hasFlags(F_DOUBLE_NODE);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_MODIFY_TDDL_DOUBLE_READ_INS;
        }
    }

    /**
     * MYSQL--单节点--变配
     */
    static class TaskMySQLModifyIns implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return !flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_BASIC)
                    && !flags.hasFlags(F_ELASTIC_MOD_POD_MODE)
                    && !flags.hasFlags(F_EMERGENCY_TRANS);
        }

        @Override
        public String getResult(MyFlag flags) {
            if (flags.hasFlags(F_SINGLE_TENANT_LOCAL_MODIFY)) {
                return PodDefaultConstants.TASK_LOCAL_MODIFY_INS;
            } else {
                if(flags.hasFlags(F_RUND)){
                    return PodDefaultConstants.TASK_MODIFY_INS_FOR_RUND;
                }else {
                    return PodDefaultConstants.TASK_MODIFY_INS;
                }
            }
        }
    }

    /**
     * MYSQL--双节点--云盘--变配
     */
    static class TaskMySQLModifyInsHA implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return !flags.hasFlags(F_XDB)
                    && !flags.hasFlags(F_ELASTIC_MOD_POD_MODE)
                    && flags.hasFlags(F_STANDARD);
        }

        @Override
        public String getResult(MyFlag flags) {
            if (flags.hasFlags(F_SINGLE_TENANT_LOCAL_MODIFY)) {
                return PodDefaultConstants.TASK_LOCAL_MODIFY_INS_HA;
            } else {
                return PodDefaultConstants.TASK_MODIFY_INS_HA;
            }

        }
    }

    /**
     * MYSQL--cluster--云盘--变配
     */
    static class TaskMySQLModifyInsCluster implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return !flags.hasFlags(F_XDB)
                    && !flags.hasFlags(F_ELASTIC_MOD_POD_MODE)
                    && flags.hasFlags(F_CLUSTER);
        }

        @Override
        public String getResult(MyFlag flags) {
            if (flags.hasFlags(F_SINGLE_TENANT_LOCAL_MODIFY)) {
                return PodDefaultConstants.TASK_LOCAL_MODIFY_INS_CLUSTER;
            } else {
                return PodDefaultConstants.TASK_MODIFY_INS_CLUSTER;
            }

        }
    }

    /**
     * MYSQL--单节点--紧急迁移
     */
    static class TaskMySQLMigrateFailOver implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return flags.hasFlags(F_SINGLE_NODE)
                    && flags.hasFlags(F_EMERGENCY_TRANS);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_MIGRATE_FAIL_OVER;
        }
    }

    /**
     * MySQL 多租户 弹性变配
     */
    static class TaskElasticModifyIns implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return !flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ELASTIC_MOD_POD_MODE)
                    && !flags.hasFlags(F_CLUSTER)
                    && !flags.hasFlags(F_TARGET_SERVERLESS);
        }

        @Override
        public String getResult(MyFlag flags) {
            if (flags.hasFlags(F_ELASTIC_MOD_POD_LOCAL)) {
                return PodDefaultConstants.TASK_ELASTIC_MODIFY_INS_LOCAL;
            } else {
                return PodDefaultConstants.TASK_ELASTIC_MODIFY_INS;
            }

        }
    }

    /**
     * MySQL Serverless(基础版) 弹性变配
     */
    static class TaskElasticModifyServerlessBasic implements Rule {
        @Override
        public boolean matchCheck(MyFlag flags) {
            return !flags.hasFlags(F_XDB)
                    && flags.hasFlags(F_ELASTIC_MOD_POD_MODE)
                    && flags.hasFlags(F_TARGET_SERVERLESS);
        }

        @Override
        public String getResult(MyFlag flags) {
            return PodDefaultConstants.TASK_ELASTIC_MODIFY_SERVERLESS_BASIC;
        }
    }

}
