package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.consts.SSLConsts;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.CAServerApiExt;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ParamConstants.SSL_VALUE_DISABLED;
import static com.aliyun.dba.support.property.ParamConstants.SSL_VALUE_ENABLED;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceSSLImpl")
public class ModifyDBInstanceSSLImpl implements IAction {
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private CaServerApi caServerApi;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private CAServerApiExt caServerApiExt;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;

    /**
     * 代码移植自本地盘接口
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            String requestId = podParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
            String uid = podParameterHelper.getParameterValue(ParamConstants.UID);
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, custins.getInsName(), null);
            String caType = podParameterHelper.getParameterValue(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
            String forceEncryption = getParameterValue(actionParams, ParamConstants.FORCE_ENCRYPTION,SSLConsts.FORCE_ENCRYPTION);
            if (!SSLConsts.CA_TYPE.contains(caType)) {
                return createErrorResponse(ErrorCode.INVALID_CA_TYPE);
            }
            try {
                caServerApi.getCAServerConfig(custins.getClusterName());
            } catch (RdsException re) {
                //没有ca server，该集群无法开启ssl
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENABLE_SSL);
            }

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if ("physical".equalsIgnoreCase(custins.getConnType())) {
                // physical实例没有链路，证书无法绑定稳定地址
                return createErrorResponse(ErrorCode.UNSUPPORTED_ENABLE_SSL);
            }

            if (custins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            String connectionString = CheckUtils.checkNullForConnectionString(getParameterValue(actionParams, ParamConstants.CONNECTION_STRING));
            if (connectionString.length() > 64) {
                return createErrorResponse(ErrorCode.CONNECTIONSTRING_LENGTH_EXCEEDED);
            }
            String minorVersionStr = custinsParamService.getCustinsParam(custins.getId(), "minor_version").getValue();
            String dbMinorVersionLongStr = "0";
            if (minorVersionStr.contains(":")) {
                int index = minorVersionStr.indexOf(":");
                dbMinorVersionLongStr = minorVersionStr.substring(index + 1, index + 9);
            } else {
                int index = minorVersionStr.lastIndexOf("_");
                dbMinorVersionLongStr = minorVersionStr.substring(index + 1, index + 9);
            }
            long minorVersion = Long.parseLong(dbMinorVersionLongStr);
            //获取请求参数
            String sslStatus = getParameterValue(actionParams, ParamConstants.SSL_ENABLED);
            boolean sslEnable = !StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus);
            boolean sslUpdate = false;
            String serverCert = null;
            String serverKey = null;
            if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
                //关闭ssl, 0
                sslStatus = SSL_VALUE_DISABLED;
            } else {
                if (StringUtils.equalsIgnoreCase(caType, SSLConsts.CA_TYPE_CUSTOM)) {  //自定义证书
                    serverCert = mysqlParameterHelper.getAndCheckCustomsServerCert();
                    serverKey = mysqlParameterHelper.getAndCheckCustomsServerKey();
                }
                // 开启或更新 ssl, 1
                sslStatus = SSL_VALUE_ENABLED;
                String sslEnabledNow = null;
                CustinsParamDO insSslConfig = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL);
                CustinsParamDO forceEncryptionConfig = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_FORCE_ENCRYPTION);
                if (insSslConfig != null) {
                    sslEnabledNow = insSslConfig.getValue();
                }
                if (StringUtils.equalsIgnoreCase(SSL_VALUE_ENABLED, sslEnabledNow)) {
                    sslUpdate = true;
                    //如果是更新，证书类型需一致
                    CustinsParamDO caTypeConfig = custinsParamService.getCustinsParam(custins.getId(), SSLConsts.CUSTINS_PARAM_NAME_CA_TYPE);
                    String caTypeFromMeta = caTypeConfig == null ? SSLConsts.CA_TYPE_ALIYUN : caTypeConfig.getValue();
                    if (!StringUtils.equalsIgnoreCase(caTypeFromMeta, caType)) {
                        return createErrorResponse(ErrorCode.INVALID_CA_TYPE, "Specify ca type is invalid.");
                    }
                    if (forceEncryptionConfig != null){
                        String oldForceEncryption = forceEncryptionConfig.getValue();
                        if (!StringUtils.equalsIgnoreCase(oldForceEncryption, forceEncryption)){
                            sslUpdate = false;
                        }
                    }else if ("1".equals(forceEncryption)){
                        sslUpdate = false;
                    }
                }
            }
            if("1".equals(forceEncryption) && StringUtils.equalsIgnoreCase(SSL_VALUE_ENABLED, sslStatus)){
                String forceEncryptionSwitch = SSLConsts.FORCE_ENCRYPTION_MINOR_VERSION;
                ConfigListResult configs = dBaasMetaService.getDefaultClient().listConfigs(requestId, PodDefaultConstants.MYSQL_SSL_FORCE_ENCRYPTION_SWITCH);
                if (configs.getItems() != null && !configs.getItems().isEmpty()) {
                    Config switchConfig = configs.getItems().get(0);
                    forceEncryptionSwitch = !StringUtils.isBlank(switchConfig.getValue()) ? switchConfig.getValue() : SSLConsts.FORCE_ENCRYPTION_MINOR_VERSION;
                }
                if (minorVersion < Integer.parseInt(forceEncryptionSwitch)){
                    return createErrorResponse(ErrorCode.MINOR_VERSION_NOT_SUPPORT_SSLENABLED);
                }
                if(!mysqlEngineCheckService.checkMinorVersionWithMaxScale(custins)){
                    return createErrorResponse(ErrorCode.UNSUPPORTED_MAXSCALE_MINOR_VERSION);
                }
            }
            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);
            boolean connStringValid = false;
            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {//遍历所有连接地址，判断请求中的连接字符串是否在该mysql实例的连接地址列表中
                if (connectionString.equals(custinsConnAddr.getConnAddrCust()) && custinsConnAddr.isConnAddrUserVisible()) {//vip对用户是否可见，vip是？
                    connStringValid = true;
                }
            }

            if (!connStringValid) {
                if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
                    log.warn("close ssl, current connstr doesn't exists, continue");
                } else {
                    return createErrorResponse(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
                }
            }

            //设置切换时间
            //立即切换，指定时间切换，运维时间切换
            Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(actionParams);

            //自定义证书，通过CaServer上传
            if (StringUtils.equalsIgnoreCase(SSL_VALUE_ENABLED, sslStatus) &&
                    StringUtils.equalsIgnoreCase(caType, SSLConsts.CA_TYPE_CUSTOM)) {
                caServerApiExt.uploadCustomServerCert(requestId, custins.getInsName(), connectionString, serverCert, serverKey);
            }
            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsSupport.CUSTINS_STATUS_SWITCH, CustinsState.STATE_SSL_MODIFYING.getComment());
            JSONObject taskParams = new JSONObject();
            taskParams.put("ssl_status", Integer.valueOf(sslStatus));
            taskParams.put("ca_type", caType);
            taskParams.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
            taskParams.put("force_encryption", Integer.valueOf(forceEncryption));
            String parameter = taskParams.toJSONString();

            String taskKey = getTaskKey(custins, replicaSet, sslUpdate);
            if (sslEnable) {
                custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME, connectionString);
                custinsParamService.setCustinsParam(custins.getId(), SSLConsts.CUSTINS_PARAM_NAME_CA_TYPE, caType);
            } else {
                // 关闭ssl, 关联链路
                custinsParamService.deleteCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_CERT_CN_NAME);
            }
            Object taskId = workFlowService.dispatchTask("custins", custins.getInsName(), "mysql", taskKey, parameter, 0);
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;

        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * MYSQL8.0.16以上版本动态修改ssl的开关
     * 默认未配置就是不开放
     */

    private String getTaskKey(CustInstanceDO custins, ReplicaSet replicaSet, boolean sslUpdate) throws ApiException {
        String taskKey;
        boolean isXDB = replicaSetService.isReplicaSetXDB(mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID), custins.getInsName());
        if (isXDB) {
            taskKey = custins.isInsTypeRead() ? PodDefaultConstants.MODIFY_READ_XDB_SSL_CONFIG : PodDefaultConstants.MODIFY_XDB_SSL_CONFIG;
        } else {
            if (mysqlParameterHelper.isSupportModifySSLDynamic(custins, sslUpdate)) {
                taskKey = PodDefaultConstants.DYNAMIC_MODIFY_SSL;
            } else {
                if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                    taskKey = PodDefaultConstants.MODIFY_SSL_FOR_CLUSTER;
                } else {
                    taskKey = PodDefaultConstants.MODIFY_SSL;
                }
            }
        }
        return taskKey;
    }
}
