package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.upgradeprecheck.UpgradeMajorVersionPreCheckExecutor;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import com.alibaba.fastjson.JSONObject;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultUpgradeMajorVersionPreCheckImpl")
@Slf4j
public class UpgradeMajorVersionPreCheckImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeMajorVersionPreCheckImpl.class);
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    private ReplicaSetService replicaSetService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService metaService;
    @Autowired
    private UpgradeMajorVersionPreCheckExecutor preCheckExecutor;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        try {
            String targetEngineVersion = mysqlParamSupport.getParameterValue(actionParams, "TargetEngineVersion");
            String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID, "");
            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(actionParams);
            preCheckExecutor.execute(custins, replicaSetMeta, targetEngineVersion, requestId);
            //下发任务
            JSONObject taskParam = new JSONObject();
            taskParam.put("targetMajorVersion", targetEngineVersion);
            taskParam.put("requestId", requestId);
            taskParam.put("replicaSetName", replicaSetMeta.getName());
            taskParam.put("srcReplicaSetName", replicaSetMeta.getName());
            taskParam.put("checkType", "check");
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSetMeta.getName(),
                    PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_UPGRADE_MAJOR_VERSION_PRECHECK, taskParam.toString(), 0);
            //更新实例状态
            String dbInstanceStatusDesc = ReplicaSet.StatusEnum.INS_MAINTAINING.toString();
            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetMeta.getName(), dbInstanceStatusDesc);
            //返回信息
            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TargetMajorVersion", targetEngineVersion);
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

}
