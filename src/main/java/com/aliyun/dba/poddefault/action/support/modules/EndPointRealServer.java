package com.aliyun.dba.poddefault.action.support.modules;

import com.aliyun.apsaradb.dbaasmetaapi.model.Host;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import lombok.Data;

import java.util.List;

@Data
public class EndPointRealServer {
    String ip;
    String port;
    String siteName;
    String regionId;
    String ecsId;
    Host host;
    Long replicaId;
    Replica replica;
    Integer backEndPort;
    List<String> sites;
    String dbInstanceId;
    String memberId;
    Integer weight;
    String vpcId;
}
