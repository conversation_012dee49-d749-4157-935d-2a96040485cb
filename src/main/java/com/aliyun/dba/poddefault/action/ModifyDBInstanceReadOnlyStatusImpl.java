package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.base.support.MySQLParamConstants.LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.READ_ONLY_STATUS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceReadOnlyStatusImpl")
public class ModifyDBInstanceReadOnlyStatusImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(RecoverNodeImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected KmsService kmsService;
    @Autowired
    protected PodParameterHelper podParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            Map<String, Object> result = new HashMap<>();
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            // 实例无锁定原因
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (replicaSet.getLockMode() != ReplicaSet.LockModeEnum.NOLOCK &&
                !((Objects.equals(replicaSet.getLockMode(), ReplicaSet.LockModeEnum.DISKFULL) ||
                    Objects.equals(replicaSet.getLockMode(), ReplicaSet.LockModeEnum.READINS_DISKFULL))
                    && Objects.equals(custins.getLockReason(), LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE))) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            // 检查KMS状态
            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }

            // 检查任务参数与实例状态是否正确
            //todo: 关闭是否要验证状态
            String readOnlyStatus = mysqlParamSupport.getParameterValue(params, "readOnlyStatus");
            if (PodDefaultConstants.readOnlyStatus.ON.name().equalsIgnoreCase(readOnlyStatus)) {
                if (!ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSet.getStatus())) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
            } else {
                if (!PodDefaultConstants.readOnlyStatus.OFF.name().equalsIgnoreCase(readOnlyStatus)) {
                    throw new RdsException(ErrorCode.INVALID_PARAMETERS, String.format("readOnlyStatus '%s' is invalid", readOnlyStatus));
                }
            }

//            else if (PodDefaultConstants.readOnlyStatus.OFF.name().equalsIgnoreCase(readOnlyStatus)) {
//                if (!ReplicaSet.StatusEnum.READONLY.equals(replicaSet.getStatus())) {
//                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
//                }
//            } else {
//                throw new RdsException(ErrorCode.INVALID_PARAMETERS, String.format("readOnlyStatus '%s' is invalid", readOnlyStatus));
//            }

            // 检查是否存在未完成任务
            if (workFlowService.isTaskExist(requestId, dbInstanceName)) {
                logger.error("{} replicaset {} has unfinished tasks.", requestId, dbInstanceName);
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }

            if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "db type " + replicaSet.getInsType() + " not supported yet!");
            }

            // 目标状态和当前状态是否为同一个状态
            result.put("DBInstanceID", replicaSet.getId());
            result.put("DBInstanceName", mysqlParamSupport.getDBInstanceName(params));
            result.put("ReadOnlyStatus", readOnlyStatus);
            result.put("taskId", null);

            boolean isReadOnlyStatusUnchanged = false;
            Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
            if (MapUtils.isNotEmpty(labels) && labels.containsKey(READ_ONLY_STATUS)) {
                String srcReadOnlyStatus = labels.get(READ_ONLY_STATUS);
                if (readOnlyStatus.equalsIgnoreCase(srcReadOnlyStatus)) {
                    logger.info("readOnlyStatus is {}, srcReadOnlyStatus is {}.", readOnlyStatus, srcReadOnlyStatus);
                    isReadOnlyStatusUnchanged = true;
                }
            } else {
                // label 不包含 READ_ONLY_STATUS 则认为 readOnlyStatus 为OFF
                if (PodDefaultConstants.readOnlyStatus.OFF.name().equalsIgnoreCase(readOnlyStatus)) {
                    logger.info("readOnlyStatus is {}. And nothing change.", requestId);
                    isReadOnlyStatusUnchanged = true;
                }
            }
            if (isReadOnlyStatusUnchanged) {
                logger.info("{} nothing has changed, return it.", requestId);
                return result;
            }

            String taskKey = PodDefaultConstants.TASK_MODIFY_INS_READ_ONLY_STATUS;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);
            jsonObject.put("readOnlyStatus", readOnlyStatus);
            String parameter = jsonObject.toJSONString();

            String domain = "mysql";
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                requestId,
                dbInstanceName,
                ReplicaSet.StatusEnum.INS_MAINTAINING.toString()
            );

            result.put("TaskId", taskId);
            return result;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
