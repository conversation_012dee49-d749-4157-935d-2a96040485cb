package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.dba.base.cache.LocalCacheService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.AligroupCreateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.AliyunCreateDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateDBInstanceImpl")
@Slf4j
public class CreateDBInstanceImpl implements IAction {

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    private AliyunCreateDBInstanceService aliyunDBInstanceService;
    @Resource
    private AligroupCreateDBInstanceService aligroupDBInstanceService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private LocalCacheService cacheService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String tddlClusterName = paramSupport.getParameterValue(params, "TddlClusterName");
        String connType = paramSupport.getParameterValue(params, "DBInstanceConnType");

        boolean isBizTypeAligroup = (StringUtils.isNotBlank(tddlClusterName) || CONN_TYPE_TDDL.equalsIgnoreCase(connType));
        Integer userId = mysqlParameterHelper.getAndCreateUserId();
        mysqlParameterHelper.checkUserOperatorCluster(userId);

        //mysqlprovider接管集团新实例开关
        String route = cacheService.getValue("CREATE_NEW_XDB_INS_ROUTE");


        if (isBizTypeAligroup && !PodDefaultConstants.DOMAIN_MYSQL.equals(route)) {
            //集团云盘创建
            return aligroupDBInstanceService.createDBIntance(custins, params);
        } else {
            //集团的本地盘的创建
            //云上创建
            return aliyunDBInstanceService.createDBInstance(custins, params);
        }
    }
}
