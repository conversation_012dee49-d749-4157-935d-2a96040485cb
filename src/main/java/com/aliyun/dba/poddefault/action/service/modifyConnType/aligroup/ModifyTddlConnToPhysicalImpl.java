package com.aliyun.dba.poddefault.action.service.modifyConnType.aligroup;


import com.alibaba.cobar.util.StringUtil;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstance;
import com.aliyun.dba.poddefault.action.CreateReadDBInstanceImpl;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.service.modifyConnType.BaseModifyInsConnTypeService;
import com.aliyun.dba.poddefault.action.service.modifyConnType.request.ModifyConnTypeRequest;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.aliyun.dba.base.support.MySQLParamConstants.TDDL_TASK_MIGRATE;

@Service
public class ModifyTddlConnToPhysicalImpl extends BaseModifyInsConnTypeService {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyTddlConnToPhysicalImpl.class);

    @Override
    public String getTaskKey() {
        return "modify_tddl_conn_to_physical";
    }

    @Override
    public void doSpecialCheck(ModifyConnTypeRequest request) throws RdsException, ApiException {
        Map<String, String> labels = metaService.getDefaultClient().listReplicaSetLabels(request.getRequestId(), request.getDbInstanceName());
        String tddlTaskMigrate = labels.get(TDDL_TASK_MIGRATE);
        String gdnInstanceName = labels.get("clusterName");
        if(StringUtil.isEmpty(tddlTaskMigrate) || !Boolean.parseBoolean(tddlTaskMigrate)){
            logger.error("db instance not migrate to mysql provider, please do upgrade minor version first.");
            throw new RdsException(ErrorCode.INVALID_ACTION);
        }

        if(!request.getReplicaSet().getInsType().equals(ReplicaSet.InsTypeEnum.MAIN)){
            logger.error("modify tddl conn type to physical only support main instance yet.");
            throw new RdsException(ErrorCode.INVALID_INS_TYPE);
        }

        GdnInstance gdnInstance = gdnInstanceService.getGdnInstance(request.getRequestId(), gdnInstanceName);
        if (null != gdnInstance) {
            logger.error("db instance have read only instance, not support yet.");
            throw new RdsException(ErrorCode.INVALID_ACTION);
        }
    }

    @Override
    public ModifyConnTypeRequest buildSpecialRequest(ModifyConnTypeRequest request, Map<String, String> params) throws ApiException, RdsException {
        return null;
    }
}
