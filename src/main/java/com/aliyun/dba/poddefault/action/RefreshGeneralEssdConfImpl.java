package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_CLOUD_AUTO;
import static com.aliyun.dba.support.property.ParamConstants.AUTOPL_PROVISIONED_IOPS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRefreshGeneralEssdConfImpl")
public class RefreshGeneralEssdConfImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(RefreshGeneralEssdConfImpl.class);

    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected CommonProviderService commonProviderService;
    @Resource
    protected CloudDiskCompressionHelper cloudDiskCompressionHelper;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            String dbInstanceName = replicaSet.getName();
            String diskType = replicaSetService.getReplicaSetStorageType(dbInstanceName, requestId);
            Integer diskSizeGB = Objects.requireNonNull(replicaSet.getDiskSizeMB()) / 1024;
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);
            String uid = user.getAliUid();
            // if skip check instance status
            boolean isSkipCheckStatus = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, "IsSkipCheckStatus", false));

            // check custInstance lock , status and diskType
            preCheckRefresh(replicaSet, diskType, isSkipCheckStatus);
            // check if skip not zero provisionedIops
            boolean isSkipNotZero = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, "IsSkipNotZero", false));
            String srcProvisionedIops = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                    requestId, dbInstanceName, AUTOPL_PROVISIONED_IOPS);
            boolean provisionedIopsNotZero = (StringUtils.isNumeric(srcProvisionedIops) && Long.parseLong(srcProvisionedIops) > 0);
            if (isSkipNotZero && provisionedIopsNotZero) {
                String message = String.format("RequestId: %s, dbInstanceName: %s, srcProvisionedIops : %s, SkipNotZero ", requestId, dbInstanceName, srcProvisionedIops);
                Map<String, Object> data = new HashMap<>();
                data.put("Message", message);
                data.put("DBInstanceID", replicaSet.getId());
                data.put("DBInstanceName", dbInstanceName);
                return data;
            }
            // get paramProvisionedIops , if empty, use default value by policy
            String paramProvisionedIops = mysqlParamSupport.getParameterValue(params, ParamConstants.AUTOPL_PROVISIONED_IOPS);
            Long provisionedIops = replicaSetService.getAutoConfigProvisionedIops(requestId, dbInstanceName, diskType, paramProvisionedIops, diskSizeGB, uid, true);
            // get paramBurstingEnabled, if empty, use srcBurstingEnabled
            String paramBurstingEnabled = mysqlParamSupport.getParameterValue(params, ParamConstants.AUTOPL_BURSTING_ENABLED);
            Boolean burstingEnabled = replicaSetService.getAutoConfigBurstingEnabled(requestId, dbInstanceName, diskType, paramBurstingEnabled);
            // get compressionMode, if compressionMode is on, provisionedIops should be zero
            String compressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, dbInstanceName, null);
            if (CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
                provisionedIops = 0L;
            }

            String taskId = commonProviderService.getDefaultApi().modifyReplicaSetAutoPLInfo(requestId, dbInstanceName, burstingEnabled, provisionedIops, true);
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("DBInstanceID", replicaSet.getId());
            result.put("DBInstanceName", dbInstanceName);
            result.put("provisionedIops", provisionedIops);
            result.put("burstingEnabled", burstingEnabled);
            return result;
        } catch (RdsException re) {
            logger.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private void preCheckRefresh(ReplicaSet replicaSet, String diskType, boolean isSkipCheckStatus) throws RdsException {
        if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }
        if (!isSkipCheckStatus && ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        if (!ECS_CLOUD_AUTO.equalsIgnoreCase(diskType)) {
            throw new RdsException(ErrorCode.NOT_SUPPORT_SPECIFIC_DISK_TYPE);
        }
    }
}
