package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.ParamConstants.*;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;


@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceConfigImpl")
public class ModifyDBInstanceConfigImpl implements IAction {

    @Autowired
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private PodParameterHelper podParameterHelper;

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private ReplicaSetService replicaSetService;

    @Resource
    protected KmsService kmsService;

    @Autowired
    private KmsApi kmsApi;

    @Getter
    @Resource
    private MysqlEncryptionService mysqlEncryptionService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        String requestId = podParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        ReplicaSet replicaSetMeta;

        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            replicaSetMeta = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, custins.getInsName(), null);

            // do check instance status
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (custins.isRead()) { //不支持只读实例
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            // 集群版mgr不支持存量云盘加密、密钥变更
            if (InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(replicaSetMeta.getCategory()) && replicaSetService.isMgr(requestId, replicaSetMeta.getName())) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // 目前只有加密的配置变更，变更的value不能为空
            String configName = podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_NAME);
            String configValue = podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_VALUE);
            if (!PodDefaultConstants.ENCRYPTION_KEY_CONFIG_NAME.equalsIgnoreCase(configName)) {
                return createErrorResponse(ErrorCode.INVALID_CONFIG_NAME);
            }
            if (StringUtils.isEmpty(configValue)) {
                return createErrorResponse(ErrorCode.INVALID_CONFIG_VALUE);
            }

            // 密钥校验处理
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String uid = user.getAliUid();
            String cluster = replicaSetMeta.getResourceGroupName();
            String roleArn = kmsService.getUserRoleArn(uid);
            Integer userId = mysqlParamSupport.getUserId(actionParams);
            boolean isSingleTenant = replicaSetService.isCloudDiskSingleTenant(requestId, replicaSetMeta);
            boolean isDefaultEncryptionKey = configValue.equalsIgnoreCase(PodDefaultConstants.DEFAULT_ENCRYPTION_KEY);

            // 获取加密的key
            String orgKeyId = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetMeta.getName(), PodDefaultConstants.ENCRYPTION_KEY_LABEL);
            String targetKeyId = isDefaultEncryptionKey? mysqlEncryptionService.getServiceKey(cluster, uid, userId) : configValue;
            log.info("replica set {} change disk encryption key from {} to {}", replicaSetMeta.getName(), orgKeyId, targetKeyId);

            // 确保key打上tag，防止有些kms的操作失败
            mysqlEncryptionService.ensureTagExistence(custins.getClusterName(), roleArn, targetKeyId, uid);

            // 原密钥为空，可能为加密普通云盘实例场景，根据白名单开放
            if (StringUtils.isEmpty(orgKeyId) && !replicaSetService.isAllowEncryptRegularCloudDisk(replicaSetMeta)) {
                return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
            }

            // 包含只读实例的暂时不支持变更
            List<ReplicaSet> readOnlyReplicaSetList = ObjectUtils.firstNonNull(dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId, replicaSetMeta.getName(), ReplicaSet.InsTypeEnum.READONLY.toString()).getItems(), new ArrayList<>());
            if (!readOnlyReplicaSetList.isEmpty()) {
                throw new RdsException(ErrorCode.INVALID_SOURCE_CATEGORY);
            }

            // 普通云盘不支持，只有essd可以
            String diskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
            if (Replica.StorageTypeEnum.CLOUD_SSD.toString().equals(diskType)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_CLOUD_SSD);
            }

            // 变更的密钥不能和之前的密钥一致
            if (targetKeyId.equalsIgnoreCase(orgKeyId)) {
                return createErrorResponse(ErrorCode.INVALID_CONFIG_VALUE);
            }

            // 云盘加密只支持对称加密
            DescribeKeyResponse desTargetKeyResponse = kmsApi.describeKey(cluster, targetKeyId, roleArn, uid);
            String keyType = desTargetKeyResponse.getKeyMetadata().getKeySpec();
            if(!keyType.equals(KMS_KEY_TYPE_ALIYN_AES_256) && !keyType.equals(KMS_KEY_TYPE_ALIYN_SM4)){
                throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
            }

            // 禁用状态的密钥不支持加密
            String keyState = desTargetKeyResponse.getKeyMetadata().getKeyState();
            if(keyState.equalsIgnoreCase("Disabled")){
                throw new RdsException(ErrorCode.UNSUPPORTED_ENCRYPTION_KEY);
            }

            if (isSingleTenant) {
                // 独享规格支持密钥变更，检查可用性即可
                if (!kmsService.isKeyEnable(custins.getClusterName(), targetKeyId, roleArn, uid)) {
                    throw new RdsException(ErrorCode.INVALID_KMS_KEY);
                }
            } else {
                // 通用规格，只支持切换到默认密钥
                if (!isDefaultEncryptionKey) {
                    throw new RdsException(ErrorCode.INVALID_KMS_TYPE_FOR_INSTANCE);
                }
            }

            // dispatch task
            String taskKey = null;
            if (InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                taskKey = PodDefaultConstants.TASK_MODIFY_DISK_ENCRYPTION_KEY_FOR_STANDARD;
            } else if (InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                taskKey = PodDefaultConstants.TASK_MODIFY_DISK_ENCRYPTION_KEY_FOR_CLUSTER;
            } else if (InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                taskKey = PodDefaultConstants.TASK_MODIFY_DISK_ENCRYPTION_KEY_FOR_BASIC;
            }
            if(taskKey == null){
                return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
            }

            //设置切换时间，这个没有参数传递，这里暂时设置为立即切换
            Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(actionParams);
            switchInfoMap.put(CustinsSupport.SWITCH_CODE, CustinsSupport.NOW_MODE);

            JSONObject taskParams = new JSONObject();
            taskParams.put("requestId", requestId);
            taskParams.put("replicaSetName", replicaSetMeta.getName());
            taskParams.put("srcReplicaSetName", replicaSetMeta.getName());
            taskParams.put("encryptionKey", targetKeyId);
            taskParams.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
            String parameter = taskParams.toJSONString();
            Object taskId = workFlowService.dispatchTask("custins", custins.getInsName(), "mysql", taskKey, parameter, 0);

            // modify instance status
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetMeta.getName(), ReplicaSet.StatusEnum.MODIFY_PARAM.toString());

            // format result
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("ConfigName", configName);
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
