package com.aliyun.dba.poddefault.action;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeRsScheduleTemplateImpl")
public class DescribeRsScheduleTemplateImpl implements IAction {
    @Autowired
    protected MysqlParamSupport paramSupport;
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);

        try {
            String bid = paramSupport.getAndCheckBID(params);
            Validate.notEmpty(bid,"null bid");
            String uid = paramSupport.getUID(params);
            Validate.notEmpty(bid,"null uid");
            String name = parameterHelper.getParameterValue("Name");
            Validate.notEmpty(name,"null name");

            PodScheduleTemplate template = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, name, bid + '_' + uid);
            Map<String, Object> data = new HashMap<>();
            data.put("Name", name);
            data.put("Template", template);
            return data;
        } catch (ApiException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND, "cont find RsTemplate" + re.getMessage());
        } catch (Exception ex) {
            log.warn(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
