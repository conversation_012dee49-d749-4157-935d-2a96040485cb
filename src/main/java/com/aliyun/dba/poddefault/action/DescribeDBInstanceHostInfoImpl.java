package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstancePortDO;
import com.aliyun.dba.instance.dataobject.InstancePortQuery;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeDBInstanceHostInfoImpl")
public class DescribeDBInstanceHostInfoImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceHostInfoImpl.class);

    @Resource
    protected ClusterService clusterService;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected InstanceService instanceService;
    @Resource
    protected CustinsParamService custinsParamService;
    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map) throws RdsException {
        String requestId = paramSupport.getParameterValue(map, ParamConstants.REQUEST_ID);
        try {
            String[] insNames = SupportUtils.splitToArray(paramSupport.getDBInstanceName(map), ",");
            if (insNames.length <= 0) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PARAMETERS);
            }

            Map<String, Object> condition = new HashMap<>(1);
            condition.put("insNames", insNames);

            List<CustInstanceDO> custinsList = custinsService.getCustInstanceByCondition(condition);

            List<Map<String, Object>> items = new ArrayList<>();
            for (CustInstanceDO custins : custinsList) {
                Integer custinsId = custins.getId();
                String custinsName = custins.getInsName();
                ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, custinsName, false);
                Map<String, Object> item = new HashMap<>();
                item.put(ParamConstants.DB_INSTANCE_ID, custinsId);
                item.put(ParamConstants.DB_INSTANCE_NAME, custinsName);
                List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custinsId);
                //查询客户实例规格信息(不要获取instance规格，xdb的logger是单独规格)，防止每个instance都查询一次

                List<Map<String, Object>> infos = new ArrayList<>();
                for (InstanceDO instanceDO : instanceList) {
                    Map<String, Object> info = new HashMap<>();
                    info.put(ParamConstants.ROLE, instanceDO.getRole());
                    info.put(ParamConstants.SITE_NAME, instanceDO.getSiteName());
                    info.put(ParamConstants.HOST_IP, instanceDO.getIp());
                    info.put(ParamConstants.HOST_NAME, instanceDO.getHostName());
                    info.put(ParamConstants.CUSTINS_HOST_TYPE, instanceDO.getHostType());
                    info.put(ParamConstants.HOST_PORT, instanceDO.getPort());
                    info.put(ParamConstants.INSTANCE_ID, instanceDO.getId());
                    ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, Long.valueOf(instanceDO.getId()), true);
                    String eniIp = replicaResource.getVpod().getIp();
                    String replicaName = replicaResource.getReplica().getName();
                    info.put("EniIp", eniIp);
                    info.put("HostInstanceName", replicaName);
//                    if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
//                        info.put(ParamConstants.INSTANCE_ID, replicaName);
//                    }

                    List<Map<String, Object>> portMapList = new ArrayList<>();
                    InstancePortQuery query = new InstancePortQuery();
                    query.setInsId(instanceDO.getId());
                    List<InstancePortDO> instancePortDOs = instanceService.getInstancePortList(query);
                    for (InstancePortDO instancePort : instancePortDOs) {
                        Map<String, Object> portMap = new HashMap<>();
                        portMap.put(ParamConstants.PORT_ID, instancePort.getId());
                        portMap.put(ParamConstants.HOST_PORT, instancePort.getPort());
                        portMap.put(ParamConstants.PORT_NAME, instancePort.getName());
                        try {
                            portMap.put(ParamConstants.PORT_LABELS, instancePort.getLabelList());
                        } catch (Exception e) {
                            portMap.put(ParamConstants.PORT_LABELS, new ArrayList<>());
                        }
                        portMapList.add(portMap);
                    }
                    info.put(ParamConstants.PORT_ITEMS, portMapList);
                    infos.add(info);
                }
                item.put(ParamConstants.INFOS, infos);

                items.add(item);
            }

            Map<String, Object> data = new HashMap<String, Object>(1);
            data.put(ParamConstants.ITEMS, items);
            return data;
        } catch (Exception e) {
            logger.error(requestId + " Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
