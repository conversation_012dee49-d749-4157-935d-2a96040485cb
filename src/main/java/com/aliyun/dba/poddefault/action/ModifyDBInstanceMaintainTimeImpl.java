package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_MAXSCALE;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceMaintainTimeImpl")
public class ModifyDBInstanceMaintainTimeImpl implements IAction {
    private static final LogAgent LOG_AGENT = LogFactory.getLogAgent(ModifyDBInstanceMaintainTimeImpl.class);

    @Autowired
    private CustinsServiceImpl custinsService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    protected PodParameterHelper podParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            Date startTime = podParameterHelper.getAndCheckStartTime(DateUTCFormat.MINUTE_ONLY_UTC_FORMAT);
            Date endTime = podParameterHelper.getAndCheckEndTime(DateUTCFormat.MINUTE_ONLY_UTC_FORMAT);
            if (startTime == null) {
                return createErrorResponse(ErrorCode.INVALID_STARTTIME);
            } else if (endTime == null) {
                return createErrorResponse(ErrorCode.INVALID_ENDTIME);
            }
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            custinsService.updateMaintainTimeByCustinsId(
                    custins.getId(), DateSupport.time2str(startTime), DateSupport.time2str(endTime));

            updateRdsServiceMaintainTimeByCustinsId(
                    custins.getId(), DateSupport.time2str(startTime), DateSupport.time2str(endTime),
                    DB_TYPE_MAXSCALE);
            workFlowService.modifyPauseTask(requestId, custins.getInsName());
            Map<String, Object> data = new HashMap<>();
            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 更新关联Service的运维时间
     * */
    public void updateRdsServiceMaintainTimeByCustinsId(Integer custinsId, String startTime, String endTime,
                                                        String serviceRole){
        List<CustinsServiceDO> custinsSrvList = custinsService
                .getCustinsServicesByCustinsIdAndServiceRole(custinsId, serviceRole);
        if (CollectionUtils.isEmpty(custinsSrvList)){
            log.warn(String.format("Custins:%s not found rdsService by role %s",custinsId, serviceRole));
            return;
        }
        for(CustinsServiceDO custinsSrv: custinsSrvList){
            CustInstanceDO rdsSrvCustins = custinsService
                    .getCustInstanceByInsName(null, custinsSrv.getServiceName());
            if(rdsSrvCustins == null){
                continue;
            }
            custinsService.updateMaintainTimeByCustinsId(rdsSrvCustins.getId(), startTime, endTime);
            List<CustInstanceDO> childCustinsList = custinsService
                    .getCustInstanceUnitByParentIdAndCharacterType(
                    rdsSrvCustins.getId(), CustinsSupport.CHARACTER_TYPE_PHYSICAL);

            if (CollectionUtils.isNotEmpty(childCustinsList)){
                for(CustInstanceDO childCustins: childCustinsList){
                    custinsService.updateMaintainTimeByCustinsId(childCustins.getId(), startTime, endTime);
                }
            }
        }
    }


}