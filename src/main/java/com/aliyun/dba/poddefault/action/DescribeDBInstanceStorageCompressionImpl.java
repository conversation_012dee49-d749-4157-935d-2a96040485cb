package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeDBInstanceStorageCompressionImpl")
public class DescribeDBInstanceStorageCompressionImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceStorageCompressionImpl.class);
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        try {
            //set and check params
            logger.info("DescribeDBInstanceStorageCompressionImpl params : {}", params);
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);
            Integer logicDiskSizeGB = Optional.ofNullable(replicaSet.getDiskSizeMB()).orElse(0) / 1024;
            Double compressionRatio = cloudDiskCompressionHelper.getCompressionRatio(requestId, replicaSet.getName(), null, null, null);
            Integer phyDiskSizeGB = CloudDiskCompressionHelper.getPhysicalSize(logicDiskSizeGB, compressionRatio);
            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), null);
            String storageType = replicaSetService.getReplicaSetStorageType(replicaSet.getName(), requestId);
            // check compression mode support
            Map<String, Object> checkResult = cloudDiskCompressionHelper.checkCompressionSupportLimit(
                    requestId,
                    instanceLevel,
                    replicaSet.getService(),
                    user.getAliUid(),
                    regionId,
                    Optional.ofNullable(phyDiskSizeGB).map(Integer::longValue).orElse(null),
                    storageType,
                    false);
            // get phyDiskSize (MB->GB)
            String diskSizeMBBeforeCompression = dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                    requestId, replicaSet.getName(), CUSTINS_PARAM_DISK_SIZE_BEFORE_COMPRESSION);
            if (StringUtils.isNotBlank(diskSizeMBBeforeCompression) && StringUtils.isNumeric(diskSizeMBBeforeCompression)) {
                phyDiskSizeGB = Integer.parseInt(diskSizeMBBeforeCompression) / 1024;
            }

            boolean isReadIns = ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())
                    || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSet.getInsType());
            boolean isSupport = true;
            String limitReason = CloudDiskCompressionHelper.VALUE_BLANK_REASON;
            if (MapUtils.isNotEmpty(checkResult)) {
                isSupport = getBooleanFromMap(checkResult, CloudDiskCompressionHelper.SUPPORT_COMPRESSION, true);
                limitReason = getStringFromMap(checkResult, CloudDiskCompressionHelper.LIMIT_REASON, CloudDiskCompressionHelper.VALUE_BLANK_REASON);
            }

            // check readIns compression mode if ins is master.
            if (isSupport && !isReadIns) {
                Map<String, Object> checkRoResult = cloudDiskCompressionHelper.checkReadInsCompressionModeOn(requestId, replicaSet.getName(), false);
                isSupport = getBooleanFromMap(checkRoResult, CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE, true);
                limitReason = getStringFromMap(checkRoResult, CloudDiskCompressionHelper.LIMIT_REASON, CloudDiskCompressionHelper.VALUE_BLANK_REASON);
            }


            // get compressionsMode
            String compressionMode = Optional.ofNullable(dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSet.getName(), CUSTINS_PARAM_COMPRESSION_MODE)).orElse("off");
            // update is Support
            isSupport = (isSupport || isCompressionModeOn(compressionMode));

            Map<String, Object> result = new HashMap<>();
            result.put(SUPPORT_COMPRESSION, isSupport);
            result.put(LIMIT_REASON, limitReason);
            result.put(COMPRESSION_MODE, compressionMode);
            result.put(COMPRESSION_RATIO, compressionRatio);
            result.put("DbInstanceStorage", phyDiskSizeGB);
            return result;
        } catch (RdsException re) {
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.warn(e.getMessage(), e);
            throw new RdsException(ErrorCode.API_CALLING_FAILED);
        } catch (Exception ex) {
            logger.warn(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private boolean getBooleanFromMap(Map<String, Object> map, String key, boolean defaultValue) {
        return Optional.ofNullable(map)
                .map(m -> m.get(key))
                .map(Object::toString)
                .map(Boolean::parseBoolean)
                .orElse(defaultValue);
    }

    private String getStringFromMap(Map<String, Object> map, String key, String defaultValue) {
        return Optional.ofNullable(map)
                .map(m -> m.get(key))
                .map(Object::toString)
                .orElse(defaultValue);
    }
}
