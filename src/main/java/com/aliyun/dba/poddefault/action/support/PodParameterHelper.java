package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.PerformanceLevelEnum;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet.BizTypeEnum;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.ExtendedLogPlanDO;
import com.aliyun.dba.bak.dataobject.LogPlanDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.EcsDiskDefaultCategory;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.idao.WhitelistTemplateIDao;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.*;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Streams;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.AbstractMap.SimpleEntry;
import java.util.stream.Collectors;

import static com.alibaba.com.caucho.hessian.io.HessianInputFactory.log;
import static com.aliyun.dba.bak.support.BakSupport.BAKTYPE_FULL;
import static com.aliyun.dba.custins.support.CustinsParamSupport.ESSD0_PERFORMANCELEVEL;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_CLOUD_AUTO;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.VBM_CUSTINS_LABEL_KEY;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.VBM_CUSTINS_LABEL_VALUE;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;
import static com.aliyun.dba.support.property.ErrorCode.HOST_NOT_FOUND;
import static com.aliyun.dba.support.property.ErrorCode.SHRINK_COUNT_REACHED_LIMIT;
import static com.aliyun.dba.support.property.ParamConstants.*;

/**
 * <AUTHOR> on 2020/6/3.
 */
@Component
public class PodParameterHelper {

    private static final LogAgent logger = LogFactory.getLogAgent(PodParameterHelper.class);
    private static final List<BizTypeEnum> ALIYUN_BIZS = Arrays.asList(BizTypeEnum.ALIYUN, BizTypeEnum.FINANCE, BizTypeEnum.GOV, BizTypeEnum.ALIYUN_DR);

    @Resource
    protected ResourceSupport resourceSupport;
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    protected HostIDao hostIDao;
    @Resource
    protected DTZSupport dtzSupport;
    @Resource
    private BakService bakService;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private PodDateTimeUtils podDateTimeUtils;
    @Resource
    protected ClusterService clusterService;
    @Resource
    protected CustinsParamService custinsParamService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected EcsDiskDefaultCategory ecsDiskDefaultCategory;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private ResourceService resourceService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private InstanceService instanceService;
    @Resource
    private WhitelistTemplateIDao whitelistTemplateIDao;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;



    /**
     * 云盘计算赠送磁盘空间
     * 如果任务流中使用了在线扩容，这里不要去调用，通过任务流去做
     * */
    public int getExtendDiskSizeGBForPod(ReplicaSet.BizTypeEnum bizType, boolean isSingleNode, Integer diskSizeGB) {
        if (PodParameterHelper.isAliYun(bizType) || isSingleNode) {
            // 每个inode占用存储空间为256Bytes。根据这个计算，2024G的磁盘而外的inode 空间为：2024.0 * 10241024/16 * 256 / 1024/1024/1024  (GB) = 31.6 G空间
            // 修改rds-api创建是时磁盘空间计算公式添加而外的inode空间消耗计算：Math.ceil(disk-quota + 20 + 4) 1.016 ) 小数点最后意味向上取整
            // 0.016 约等于每MB空间inode占用的空间，需添加此部分空间，df看到的实际大小才是匹配的。
            long diskSizeMB = diskSizeGB * 1024L;
            long extendDiskSizeGB = Math.max(Math.round((float) diskSizeMB / 1024 * 0.1), 1L);
            diskSizeMB += (Math.min(extendDiskSizeGB * 1024, 20 * 1024L) + 4096L);
            return new Double(Math.ceil(diskSizeMB) * 1.016).intValue() / 1024;
        } else {
            return diskSizeGB;
        }
    }

    public IPWhiteList getAndCheckReplicaSetIpWhiteList()
            throws RdsException {
        Set<String> ipSet = getAndCheckWhiteIpList();
        if (ipSet == null) {
            return null;
        }
        IPWhiteList.NetTypeEnum whitelistNetType = getAndCheckWhitelistNetType();
        IPWhiteList ipWhiteList = new IPWhiteList();
        ipWhiteList.setWhiteList(SupportUtils.getIpWhiteListStr(ipSet));
        ipWhiteList.setGroupName(CustinsIpWhiteListDO.DEFAULT_GROUP_NAME);
        ipWhiteList.setGroupTag("");
        ipWhiteList.setNetType(whitelistNetType);
        ipWhiteList.setIpType(IPWhiteList.IpTypeEnum.valueOf(getAndCheckSecurityIpType()));
        return ipWhiteList;
    }

    /**
     * 构造切换时间方式
     *
     * @param params
     * @return
     * @throws RdsException
     */
    public Map<String, Object> getSwitchInfo(Map<String, String> params) throws RdsException {
        Date utcSwitchDate = paramSupport.parseCheckSwitchTimeTimeZoneSafe(params);
        String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(params, utcSwitchDate, false);
        Integer switchWindow = paramSupport.getAndCheckSwitchWindow(params);
        if(switchWindow ==null) {
            return PodCommonSupport.getSwitchInfoTime(switchMode, utcSwitchDate);
        }else{
            return PodCommonSupport.getSwitchInfoTime(switchMode, utcSwitchDate, switchWindow);
        }
    }


    public IPWhiteList getDefaultReplicaSetIpWhiteList(String whiteIp) throws RdsException {
        Set<String> ipSet = new HashSet(1);
        ipSet.add(whiteIp);
        IPWhiteList.NetTypeEnum whitelistNetType = getAndCheckWhitelistNetType();
        IPWhiteList ipWhiteList = new IPWhiteList();
        ipWhiteList.setWhiteList(SupportUtils.getIpWhiteListStr(ipSet));
        ipWhiteList.setGroupName(CustinsIpWhiteListDO.DEFAULT_GROUP_NAME);
        ipWhiteList.setGroupTag("");
        ipWhiteList.setNetType(whitelistNetType);
        ipWhiteList.setIpType(IPWhiteList.IpTypeEnum.valueOf(getAndCheckSecurityIpType()));
        return ipWhiteList;
    }

    public boolean hasParameter(String param) {
        param = param.toLowerCase();
        Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        return paramsMap.containsKey(param) && StringUtils.isNotEmpty(paramsMap.get(param));
    }

    public String getParameterValue(String param) {
        Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        return paramsMap.get(param.toLowerCase());
    }

    public String getParameterValue(String param, Object defaultValue) {
        param = param.toLowerCase();
        if (defaultValue != null && !(defaultValue instanceof String)) {
            defaultValue = defaultValue.toString();
        }
        Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        return hasParameter(param) ? paramsMap.get(param) : (String) defaultValue;
    }


    public Set<String> getAndCheckWhiteIpList() throws RdsException {
        String ipList = getParameterValue(ParamConstants.SECURITY_IP_LIST);
        if (ipList == null) {
            return null;
        }
        String ipType = getAndCheckSecurityIpType();
        return CheckUtils.checkIpList(ipList, resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_IP_WHILE_LIST_NUM), ipType);
    }


    public IPWhiteList.NetTypeEnum getAndCheckWhitelistNetType() throws RdsException {
        // 若用户不传WhitelistNetType，则默认为mix，当控制台支持后，netType为必传，只能选择classic和vpc
        String netType = getParameterValue(ParamConstants.WHITELIST_NET_TYPE, CustinsIpWhiteListDO.DEFAULT_NET_TYPE);
        CheckUtils.checkValidForIpWhiteListNetType(netType);
        return IPWhiteList.NetTypeEnum.valueOf(netType.toUpperCase());
    }


    public String getAndCheckSecurityIpType() throws RdsException {
        // 若用户不传 SecurityIpType，则默认为 ipv4
        String ipType = getParameterValue(ParamConstants.SECURITY_IP_TYPE,
                CustinsIpWhiteListDO.DEFAULT_IP_TYPE);
        if (ipType == null || !ParamConstants.SECURITY_IP_TYPE_SET.contains(ipType.toLowerCase())) {
            throw new RdsException(ErrorCode.INVALID_SECURITY_IP_TYPE);
        }
        return ipType.toUpperCase();
    }

    public Map<Replica.RoleEnum, String> getRoleHostNameMapping() throws RdsException {
        String dedicatedHostNamesJson = parameterHelper.getParameterValue("DedicatedHostNames");
        String clusterName = parameterHelper.getParameterValue(CLUSTER_NAME, null);
        Map<Replica.RoleEnum, String> roleParameterName = ImmutableMap.of(
                Replica.RoleEnum.MASTER, "TargetDedicatedHostIdForMaster",
                Replica.RoleEnum.SLAVE, "TargetDedicatedHostIdForSlave",
                Replica.RoleEnum.LOGGER, "TargetDedicatedHostIdForLog",
                Replica.RoleEnum.LEARNER, "TargetDedicatedHostIdForLearner"
        );

        Map<String, Object> dedicatedHostNames;
        if (!StringUtils.isEmpty(dedicatedHostNamesJson)) {
            dedicatedHostNames = JSON.parseObject(dedicatedHostNamesJson).entrySet().stream().collect(Collectors.toMap(e -> e.getKey().toLowerCase(), Map.Entry::getValue));
        } else {
            dedicatedHostNames = roleParameterName.values().stream().map(v -> new SimpleEntry<>(v, parameterHelper.getParameterValue(v, null))).filter(e -> e.getValue() != null).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        Map<Replica.RoleEnum, String> result = new HashMap<>();
        for (Map.Entry<Replica.RoleEnum, String> entry : roleParameterName.entrySet()) {
            String parameterNameForDedicatedHost = entry.getValue();
            String dedicatedHostName = getHostNameFromParamsByDedicatedHostName(dedicatedHostNames, parameterNameForDedicatedHost, clusterName);
            if (StringUtils.isNotEmpty(dedicatedHostName)) {
                result.put(entry.getKey(), dedicatedHostName);
            }
        }

        if (result.isEmpty()) { // 尝试从HostNames 获取指定机器
            result = Streams.zip(ImmutableList.of(Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE, Replica.RoleEnum.LOGGER).stream(),
                    Arrays.stream(StringUtils.split(parameterHelper.getParameterValue("HostNames", ""), ",")),
                    AbstractMap.SimpleEntry::new).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue));
        }
        if (result.isEmpty()) { // 尝试从HostId 获取指定机器
            result = Streams.zip(ImmutableList.of(Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE, Replica.RoleEnum.LOGGER).stream(),
                    Arrays.stream(StringUtils.split(parameterHelper.getParameterValue("HostId", ""), ",")).map(
                            hId -> {
                                try {
                                    return hostIDao.getHostInfo(Integer.parseInt(hId), null, null).getHostName();
                                } catch (Exception e) {
                                    logger.error(String.format("cannot find host by param HostId(%s): %s", hId, Arrays.toString(e.getStackTrace())));
                                }
                                return null;
                            }
                    ).filter(Objects::nonNull), AbstractMap.SimpleEntry::new
            ).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue));
        }

        // 离线迁移会通过HostName参数指定主机
        if (result.isEmpty()) {
            String hostName = parameterHelper.getParameterValue(ParamConstants.HOST_NAME);
            if (StringUtils.isNotBlank(hostName)) {
                result.put(Replica.RoleEnum.MASTER, hostName);
            }
        }

        return result;
    }

    public String getHostNameFromParamsByDedicatedHostName(Map params, String paramName, String clusterName) throws RdsException {
        String dedicatedHostName = paramSupport.getParameterValue(params, paramName, "");
        String hostname = null;
        if (StringUtils.isNotEmpty(dedicatedHostName)) {
            Map<String, Object> specifyHostInfo = hostIDao.getHostIdsByDedicatedHostName(dedicatedHostName, clusterName);
            if (specifyHostInfo != null && specifyHostInfo.get("host_name") != null) {
                hostname = String.valueOf(specifyHostInfo.get("host_name"));
            } else {
                throw new RdsException(HOST_NOT_FOUND);
            }
        }
        return hostname;
    }

    public Date getAndCheckRestoreTime(String requestId, CustInstanceDO custins) throws Exception {
        LogPlanDO logPlan = bakService.getLogPlanByCustinsId(custins.getId());
        if (logPlan == null || !logPlan.isEnableBackupLog()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_LOG_STATUS);
        }
        Date restoreTimeUTC = podDateTimeUtils.getUTCDateByDateStr(getParameterValue(ParamConstants.RESTORE_TIME));
        this.checkRestoreTimeValid(requestId, custins, restoreTimeUTC, logPlan);
        return restoreTimeUTC;
    }

    public BakhistoryDO getBakhistoryByRecoverTime(Integer custinsId, Date restoreTimeUTC) {
        //转化为Bak库时间进行比较
        Date restoreTimeBak = dtzSupport.getSpecificTimeZoneDate(new DateTime(restoreTimeUTC), DATA_SOURCE_BAK);
        logger.info("restoreTimeUTC {} restoreTimeBak {}", restoreTimeUTC, restoreTimeBak);
        return bakService.getBakhistoryByRecoverTime(custinsId, restoreTimeBak, null, BAKTYPE_FULL);
    }

    public void checkRestoreTimeValid(String requestId, CustInstanceDO custins, Date restoreTimeUTC, LogPlanDO logPlan)
            throws RdsException, ApiException {
        Date restoreTimeBak = dtzSupport.getSpecificTimeZoneDate(new DateTime(restoreTimeUTC), DATA_SOURCE_BAK);
        logger.info("restoreTimeUTC {}, restoreTimeBak {}", restoreTimeUTC, restoreTimeBak);
        long times = restoreTimeUTC.getTime();
        logger.warn("times is: " + times + ",System.currentTimeMillis() is: " + System.currentTimeMillis());
        if (times >= System.currentTimeMillis()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }
        logger.warn(
                "times is: " + times + ",custins.getGmtCreated().getTime() is: " + custins.getGmtCreated().getTime());
        if (times <= custins.getGmtCreated().getTime()) {
            throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
        }

        // 不允许恢复到备份保留周期之前的时间点
        Integer retention = bakService.getBaklistByCustinsId(custins.getId()).getRetention();
        if (logPlan != null) {
            Integer logRetention = logPlan.getRetention();
            retention = Math.min(retention, logRetention);
        }
        Date expireTime = DateUtils.addDays(DateSupport.str2date(DateSupport.date2str(new Date())),
                -retention);
        logger.warn("times is: " + times + ",expireTime.getTime() is: " + expireTime.getTime());
        if (times < expireTime.getTime()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }

        if (logPlan != null) {
            if (!logPlan.isEnableBackupLog() || logPlan.getEnableUploadTime().after(restoreTimeUTC)) {
                throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
            }
            // make sure there is a valid backup set between enable upload time and restore time.
            Integer countBakHis = bakService.countBakHisotry(
                    custins.getId(),
                    logPlan.getEnableUploadTime(),
                    restoreTimeBak,
                    BAKTYPE_FULL,
                    1);
            if (countBakHis <= 0) {
                throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
            }
        }

        this.checkBakupSetAvailForRestoreTime(requestId, custins, restoreTimeBak);
    }

    public void checkBakupSetAvailForRestoreTime(String requestId, CustInstanceDO custins, Date restoreTimeBak) throws ApiException, RdsException {
        String bakWay = replicaSetService.getReplicaSetBakWay(requestId, custins.getInsName());
        Date fullBakTimeBefore = bakService.getLatestBakTimeBefore(custins.getId(), restoreTimeBak, BAKTYPE_FULL, bakWay);
        if (fullBakTimeBefore == null) {
            throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
        }
        Boolean checkOK = bakService.checkBinlogNotExpired(custins.getId(), fullBakTimeBefore, restoreTimeBak);
        // 备份集和还原时间点期间的日志不能有被清理的
        if (!checkOK) {
            throw new RdsException(ErrorCode.RECOVERTIME_BINLOG_NOT_FOUND);
        }
    }

    /**
     * 从请求参数中获取磁盘类型，不指定时，mysql basic从resource中取默认值, 其他默认local_ssd
     */
    public String getDiskType(InstanceLevel instanceLevel) throws RdsException {
        String diskType = parameterHelper.getParameterValue(DB_INSTANCE_STORAGE_TYPE, "");
        if (StringUtils.isEmpty(diskType)) {
            diskType = parameterHelper.getParameterValue("storagetype", "");
        }
        if (StringUtils.isNotEmpty(diskType)) {
            return diskType;
        } else if (Arrays.asList(InstanceLevel.CategoryEnum.BASIC, InstanceLevel.CategoryEnum.STANDARD).contains(instanceLevel.getCategory())
                && isMysql57or80(instanceLevel.getServiceVersion()) && instanceLevel.getHostType() == 2) {
            return ResourceSupport.getInstance().getStringRealValue(ResourceKey.RESOURCE_ECS_DATA_DISK_CATEGORY).trim();
        } else {
            return Replica.StorageTypeEnum.LOCAL_SSD.toString();
        }
    }

    public String getDiskType(InstanceLevel instanceLevel, String zoneId) throws RdsException {
        String diskType = parameterHelper.getParameterValue(DB_INSTANCE_STORAGE_TYPE, "");
        if (StringUtils.isEmpty(diskType)) {
            diskType = parameterHelper.getParameterValue("storagetype", "");
        }
        if (StringUtils.isNotEmpty(diskType)) {
            return diskType;
        } else if (Arrays.asList(InstanceLevel.CategoryEnum.BASIC, InstanceLevel.CategoryEnum.STANDARD).contains(instanceLevel.getCategory())
                && isMysql57or80(instanceLevel.getServiceVersion()) && instanceLevel.getHostType() == 2) {
            return ecsDiskDefaultCategory.getZoneDefaultCategory(zoneId);
        } else {
            return Replica.StorageTypeEnum.LOCAL_SSD.toString();
        }
    }


    public static boolean isMysql57or80(String version) {
        return isMysql57(version) || isMysql80(version);
    }

    public static boolean isMysql57(String version) {
        return PodDefaultConstants.MYSQL_VERSION_57.equals(version);
    }

    public static boolean isMysql80(String version) {
        return PodDefaultConstants.MYSQL_VERSION_80.equals(version);
    }

    /**
     * 非基础版实例，强制reset成多可用区模式
     * @param params 接口请求参数
     * @param instanceLevel 实例规格
     * @param dbEngine Mysql/XDB 用于取Replica Role List
     * @param isReadIns 是否只读实例，只读实例强制重建主可用区参数，原因在于接口传参不可信。
     *                  例如：EvaluateRegionResource接口，
     *                  控制台传参：dispense_mode = 0 & multiavzexparam = null；
     *                  OpenAPI传参：dispense_mode = 1 & multiavzexparam = ["role": "master"] 只有master节点
     */
    public void resetDispenseMode(Map<String, String> params, InstanceLevel instanceLevel, String dbEngine, boolean isReadIns) throws RdsException {
        boolean isSingleNode = MysqlParamSupport.isSingleNode(instanceLevel);
        if (instanceLevel.getCategory() == InstanceLevel.CategoryEnum.BASIC || isSingleNode) {
            params.put(ParamConstants.DISPENSE_MODE.toLowerCase(),
                    params.getOrDefault(ParamConstants.DISPENSE_MODE.toLowerCase(), "0"));
        } else {
            if (!"1".equals(CustinsParamSupport.getParameterValue(params, ParamConstants.DISPENSE_MODE))
                    && MysqlParamSupport.isCluster(instanceLevel.getCategory().toString())) {
                throw new RdsException(ErrorCode.INVALID_MULTIPARAM_ZONEINFO_LIST, "zoneinfo List is invaild.");
            }
            // 接口如果指定的非主可用区或只读实例，此时multiAvz参数不可信，直接清空重构
            if (!"1".equals(CustinsParamSupport.getParameterValue(params, ParamConstants.DISPENSE_MODE)) || isReadIns) {
                logger.info(params.getOrDefault(ParamConstants.MULTI_AVZ_EX_PARAM.toLowerCase(), ""));
                logger.info("auto build multi avz before reset dispense mode to MultiAVZ.");
                String region = CustinsParamSupport.getParameterValue(params, "region");
                if (region == null) {
                    region = CustinsParamSupport.getParameterValue(params, "SubDomain");
                }
                Replica.RoleEnum[] roleEnumList = PodCommonSupport.getRoles(dbEngine, instanceLevel, isReadIns, null);
                MultiAVZExParamDO multiAvzInfo = new MultiAVZExParamDO();
                String zoneId = CustinsParamSupport.getParameterValue(params, ParamConstants.ZONE_ID);
                String vSwitchId = CustinsParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID);
                PodAvzSupport.buildAutoMultiAvzInfo(multiAvzInfo, roleEnumList, region, zoneId, vSwitchId, true);
                params.put(ParamConstants.MULTI_AVZ_EX_PARAM.toLowerCase(), JSON.toJSONString(multiAvzInfo));
            }
            logger.info("instance is not basic, reset dispense mode to MultiAVZ.");
            params.put(ParamConstants.DISPENSE_MODE.toLowerCase(), "1");
        }
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
    }

    /**
     * 非基础版实例，强制reset成多可用区模式, 只读高可用都重置为主可用区模式
     * @param params 接口请求参数
     * @param instanceLevel 实例规格
     * @param dbEngine
     */
    public void resetDispenseModeForReadOnly(Map<String, String> params, InstanceLevel instanceLevel, String dbEngine) throws RdsException {
        boolean isSingleNode = MysqlParamSupport.isSingleNode(instanceLevel);
        boolean isReadIns = true;
        if (instanceLevel.getCategory() == InstanceLevel.CategoryEnum.BASIC
                || instanceLevel.getCategory() == InstanceLevel.CategoryEnum.SERVERLESS_BASIC
                || isSingleNode) {
            params.put(ParamConstants.DISPENSE_MODE.toLowerCase(), params.getOrDefault(ParamConstants.DISPENSE_MODE.toLowerCase(), "0"));
        } else {
            // 接口如果指定的非主可用区，此时multiAvz参数不可信，直接清空重构
            if (!"1".equals(CustinsParamSupport.getParameterValue(params, ParamConstants.DISPENSE_MODE))) {
                logger.info(params.getOrDefault(ParamConstants.MULTI_AVZ_EX_PARAM.toLowerCase(), ""));
                logger.info("auto build multi avz before reset dispense mode to MultiAVZ.");
                String region = CustinsParamSupport.getParameterValue(params, "region");
                if (region == null) {
                    region = CustinsParamSupport.getParameterValue(params, "SubDomain");
                }
                Replica.RoleEnum[] roleEnumList = PodCommonSupport.getRoles(dbEngine, instanceLevel, isReadIns, null);
                MultiAVZExParamDO multiAvzInfo = new MultiAVZExParamDO();
                String zoneId = CustinsParamSupport.getParameterValue(params, ParamConstants.ZONE_ID);
                String vSwitchId = CustinsParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID);
                PodAvzSupport.buildAutoMultiAvzInfo(multiAvzInfo, roleEnumList, region, zoneId, vSwitchId, true);
                params.put(ParamConstants.MULTI_AVZ_EX_PARAM.toLowerCase(), JSON.toJSONString(multiAvzInfo));
            }
            logger.info("instance is not basic, reset dispense mode to MultiAVZ.");
            params.put(ParamConstants.DISPENSE_MODE.toLowerCase(), "1");
        }
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
    }

    public boolean isClusterUserVPCArch(String dbType, String dbVersion, String clusterName) {
        ClusterParamDO clusterArchVersionDO = clusterService.getClusterParam(dbType, dbVersion, clusterName,
                ClusterParamSupport.CLUSTER_PARAM_ARCH_VERSION);
        if (clusterArchVersionDO != null && CustinsParamSupport.CUSTINS_PARAM_VALUE_ECS_ARCH_VERSION_VPC.equals(
                clusterArchVersionDO.getValue())) {
            return true;
        }
        return false;
    }



    public boolean isSingleTenant(ReplicaSet replicaSet) {
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(replicaSet.getId().intValue(), "single_tenant");
        return custinsParamDO != null && "true".equalsIgnoreCase(custinsParamDO.getValue());
    }


    /**
     * 转化磁盘类型参数
     *
     * @param diskType
     * @return
     */
    public static String transferDiskTypeParam(String diskType) {
        if (DockerOnEcsConstants.ECS_ClOUD_ESSD2.equalsIgnoreCase(diskType) ||
                DockerOnEcsConstants.ECS_ClOUD_ESSD3.equalsIgnoreCase(diskType)||
                DockerOnEcsConstants.ECS_ClOUD_ESSD0.equalsIgnoreCase(diskType)) {
            return DockerOnEcsConstants.ECS_ClOUD_ESSD;
        }
        return diskType;
    }


    /**
     * 根据云盘的类型做的性能等级转化
     *
     * @param diskType
     * @return
     */
    public static String transferCloudDiskPerfLevel(String diskType) {
        if (DockerOnEcsConstants.ECS_ClOUD_ESSD.equalsIgnoreCase(diskType)) {
            return "PL1";
        } else if (DockerOnEcsConstants.ECS_ClOUD_ESSD2.equalsIgnoreCase(diskType)) {
            return "PL2";
        } else if (DockerOnEcsConstants.ECS_ClOUD_ESSD3.equalsIgnoreCase(diskType)) {
            return  "PL3";
        } else if (DockerOnEcsConstants.ECS_ClOUD_ESSD0.equalsIgnoreCase(diskType)) {
            return  "PL0";
        }
        return null;
    }


    /**
     * 检查CloudEssd大小是否符合预期
     *
     * @param diskType
     * @param diskSize
     * @throws RdsException
     */
    public static void checkCloudEssdStorageValid(String diskType, Integer diskSize) throws RdsException {
        // essd-->essd2 需要满足磁盘空间大于等于465GB
        // essd essd2-->essd3 需要满足磁盘空间大于等于1265GB
        if (DockerOnEcsConstants.ECS_ClOUD_ESSD2.equalsIgnoreCase(diskType) &&
                diskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD2_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
        }
        if (DockerOnEcsConstants.ECS_ClOUD_ESSD3.equalsIgnoreCase(diskType) &&
                diskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD3_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
        }
        if (DockerOnEcsConstants.ECS_ClOUD_ESSD0.equalsIgnoreCase(diskType) &&
                diskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD0_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
        }
        if (ECS_CLOUD_AUTO.equalsIgnoreCase(diskType)  &&
                diskSize < DockerOnEcsConstants.ECS_CLOUD_AUTO_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK, "general essd size can't less than 10GB");
        }
        if (DockerOnEcsConstants.ECS_ClOUD_ESSD.equalsIgnoreCase(diskType) &&
                diskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
        }
    }

    /**
     * check paramProvisionedIops
     * @param replicaSetName
     * @param diskType
     * @param paramProvisionedIops
     * @param diskSizeGB
     * @return
     */
    public static boolean checkProvisionedIopsValid(String replicaSetName, String diskType, String paramProvisionedIops, Integer diskSizeGB) {
        if (!ECS_CLOUD_AUTO.equalsIgnoreCase(diskType)) {
            return false;
        }
        if (StringUtils.isNotBlank(paramProvisionedIops)) {
            // check provisionedIops value limit
            try {
                long provisionedIops = Long.parseLong(paramProvisionedIops);
                return checkProvisionedIopsValueValid(provisionedIops, diskSizeGB);
            } catch (NumberFormatException e) {
                logger.warn(String.format("invalid long param provisionedIops: %s, error: %s", paramProvisionedIops, e.getMessage()));
                return false;
            }
        }
        return true;
    }
    public static boolean checkProvisionedIopsValueValid(Long provisionedIops, Integer diskSizeGB) {
        // limit details in this document: https://next.api.aliyun.com/document/Ecs/2014-05-26/ModifyDiskSpec
        long expectedMaxProvisionedIops = Math.min(50000L, 1000L * diskSizeGB - Math.min(1800L + 50L * diskSizeGB, 50000L));
        long expectedMinProvisionedIops = 0L;
        if (provisionedIops < expectedMinProvisionedIops || provisionedIops > expectedMaxProvisionedIops) {
            logger.warn(String.format("ProvisionedIops : %s,  not in expected range [%s, %s]", provisionedIops, expectedMinProvisionedIops, expectedMaxProvisionedIops));
            return false;
        }
        return true;
    }

    public void checkProvisionedIopsChangeLimit(String requestId, String replicaSetName, boolean isProvisionedIopsChange) throws RdsException, ApiException {
        if (!isProvisionedIopsChange) {
            return;
        }
        /*
          only change 1 time a day
         */
        int changeDaysLimit = 1;
        int changeCountLimit = 1;
        OffsetDateTime earliestTimePoint = OffsetDateTime.now().minusDays(changeDaysLimit);
        LabelChangeLogListResult changeLogListResult = dBaasMetaService.getDefaultClient().listReplicaSetLabelChangeLogs(requestId, replicaSetName);
        logger.info(String.format("requestId: %s, checkProvisionedIopsChangeLimit:  changeCountLimit:[%s], changeDaysLimit: [%s], earliestTimePoint: [%s]", requestId, changeCountLimit, changeDaysLimit, earliestTimePoint));
        logger.info(String.format("requestId: %s, checkProvisionedIopsChangeLimit: changeLogListResult:[%s]", requestId, changeLogListResult));
        if (Objects.nonNull(changeLogListResult) && CollectionUtils.isNotEmpty(changeLogListResult.getItems())) {
            long cnt = changeLogListResult.getItems()
                    .stream()
                    .filter(t -> AUTOPL_PROVISIONED_IOPS.equalsIgnoreCase(t.getName()) && Objects.nonNull(t.getGmtCreated()) && earliestTimePoint.isBefore(t.getGmtCreated()))
                    .count();
            if (cnt >= changeCountLimit) {
                throw new RdsException(new Object[]{ResultCode.CODE_FORBIDDEN, "ParamChangeCountLimit", "provisionedIops param can only change 1 time a day"});
            }
        }
    }

    /**
     * 检查ESSD磁盘等级变更, 不支持其他等级降级至PL0
     *
     * @param srcPerfLevel
     * @param targetPerfLevel
     * @throws RdsException
     */
    public static boolean checkIsSupportModifyEssdPerfLevel(String srcPerfLevel, String targetPerfLevel) throws RdsException {
        return !StringUtils.equalsIgnoreCase(ESSD0_PERFORMANCELEVEL, targetPerfLevel) || StringUtils.equalsIgnoreCase(ESSD0_PERFORMANCELEVEL, srcPerfLevel);
    }

    /**
     * 根据PL等级检查CloudEssd磁盘大小是否符合预期
     *
     * @param performanceLevel
     * @param diskSize
     * @throws RdsException
     */
    public static void checkCloudEssdStorageValidByPLEVEL(String performanceLevel, Integer diskSize) throws RdsException {
        // essd-->essd2 需要满足磁盘空间大于等于465GB
        // essd essd2-->essd3 需要满足磁盘空间大于等于1265GB
        if (PerformanceLevelEnum.PL2.getValue().equals(performanceLevel) &&
                diskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD2_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
        }
        if (PerformanceLevelEnum.PL3.getValue().equals(performanceLevel) &&
                diskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD3_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
        }
        if (PerformanceLevelEnum.PL0.getValue().equals(performanceLevel) &&
                diskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD0_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
        }
        if (PerformanceLevelEnum.PL1.getValue().equals(performanceLevel) &&
                diskSize < DockerOnEcsConstants.ECS_ClOUD_ESSD_MIN_SIZE) {
            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
        }
    }

    /**
     * check disk size in different storage types and performance levels, include cloud_essd(PL0/1/2/3) and cloud_auto
     * @param diskType
     * @param performanceLevel
     * @param diskSize
     * @throws RdsException
     */
    public static void checkCloudEssdStorageValidByDiskTypeAndPLEVEL(String diskType, String performanceLevel, Integer diskSize) throws RdsException {
        if( DockerOnEcsConstants.ECS_ClOUD_ESSD.equalsIgnoreCase(diskType))
        {
            checkCloudEssdStorageValidByPLEVEL(performanceLevel, diskSize);
        }
        else{
            checkCloudEssdStorageValid(diskType, diskSize);
        }
    }

    /**
     * vmoc-lite实例设置vbm标签
     * */
    public void setVbmLabelForTempReplicaSet(String requestId, ReplicaSet replicaSet, String tmpReplicaSetName) throws ApiException{
        if (PodParameterHelper.isAliYun(replicaSet.getBizType())
                && replicaSet.getInsType() == ReplicaSet.InsTypeEnum.MAIN
                && tmpReplicaSetName != null) {
            ReplicaSet tmpReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, tmpReplicaSetName, false);
            if(tmpReplicaSet != null) {
                Map<String, String> labels = new HashMap<>();
                labels.put(VBM_CUSTINS_LABEL_KEY, VBM_CUSTINS_LABEL_VALUE);
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSet.getName(), labels);
            }
        }
    }

    /**
     * 只读实例配置白名单同步label
     * 参考文档: https://yuque.antfin-inc.com/docs/share/033a6e3d-df1d-4d9c-b793-4ab5fc086fb1?#bdSsM
     * */
    public void setReadInsSgLabel(String requestId, ReplicaSet replicaSet, String tmpReplicaSetName) throws ApiException {
        if (PodParameterHelper.isAliYun(replicaSet.getBizType()) && replicaSet.getInsType() == ReplicaSet.InsTypeEnum.READONLY) {
            ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), false);
            Map<String, String> labels = new HashMap<>();
            labels.put(PodDefaultConstants.SG_CLIENT_OF_CUSTINS, primaryReplicaSet.getId().toString());
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(
                    requestId,
                    tmpReplicaSetName != null ? tmpReplicaSetName : replicaSet.getName(),
                    labels);
        }
    }

    public void updateReadInsLabels(String requestId, ReplicaSet replicaSet, String tmpReplicaSetName) throws ApiException {
        setReadInsSgLabel(requestId,replicaSet,tmpReplicaSetName);
        if (replicaSet.getInsType() == ReplicaSet.InsTypeEnum.READONLY && PodParameterHelper.isAliGroup(replicaSet.getBizType())) {
            Map<String, String> labels = new HashMap<>();
            labels.put("CenterReplicaSetName", replicaSet.getLabels().get("CenterReplicaSetName"));
            labels.put("CenterRegionId", replicaSet.getLabels().get("CenterRegionId"));
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(
                    requestId,
                    tmpReplicaSetName != null ? tmpReplicaSetName : replicaSet.getName(),
                    labels);
        }
    }

    public Date getAndCheckStartTime(DateUTCFormat format) throws RdsException {
        String date = getParameterValue("starttime");
        Integer timeZoneDiffSec = dtzSupport.getMetaDBTimeZoneDiffSeconds(DATA_SOURCE_DBAAS);
        return CheckUtils.getAndCheckDateTime(date, format, ErrorCode.INVALID_STARTTIME, timeZoneDiffSec);
    }

    public Date getAndCheckEndTime(DateUTCFormat format) throws RdsException {
        String date = getParameterValue("endtime");
        Integer timeZoneDiffSec = dtzSupport.getMetaDBTimeZoneDiffSeconds(DATA_SOURCE_DBAAS);
        return CheckUtils.getAndCheckDateTime(date, format, ErrorCode.INVALID_ENDTIME, timeZoneDiffSec);
    }

    /**
     * 通过Region，从dbaas.rds_region查询对应的bizType
     * */
    public BizTypeEnum getBizType(String requestId, String regionId) {
        try {
            Region region = dBaasMetaService.getDefaultClient().getRegion(requestId, regionId, false);
            logger.info(String.format("request id %s get Region %s bizType %s",
                    requestId, region.getRegionId(), region.getBizType()));
            return BizTypeEnum.fromValue(region.getBizType());
        } catch (Exception e) {
            logger.error("get bizType by region failed!", e);
            return BizTypeEnum.ALIYUN;
        }

    }

    public String getCategory(String requestId, ReplicaSet replicaSetMeta) throws com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        String category = replicaSetMeta.getCategory();
        if (isReadReplicaSet(replicaSetMeta)) {
            ReplicaSet primaryReplicaSet = podCommonSupport.getPrimaryReplicaSet(requestId, replicaSetMeta).getRight();
            if (primaryReplicaSet != null) {
                category = primaryReplicaSet.getCategory();
            }
        }
        return category;
    }

    private boolean isReadReplicaSet(ReplicaSet replicaSetMeta) {
        return ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())
                || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSetMeta.getInsType());
    }

    /**
     * 判断是否为公有云实例，公有云包括：aliYun\finance(金融)\gov(政务)
     * */
    public static boolean isAliYun(BizTypeEnum bizType) {
        return ALIYUN_BIZS.contains(bizType);
    }

    public static boolean isAliYun(String bizType) {
        for (BizTypeEnum biz : ALIYUN_BIZS) {
            if (biz.toString().equalsIgnoreCase(bizType)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为集团实例
     * */
    public static boolean isAliGroup(ReplicaSet.BizTypeEnum bizType) {
        return bizType == ReplicaSet.BizTypeEnum.ALIGROUP;
    }

    public static boolean isAliGroup(String bizType) {
        return Objects.equals(bizType, BizTypeEnum.ALIGROUP.toString());
    }


    /**
     * 获取变配默认调度方式
     * */
    public ModifyReplicaSetResourceRequest.ModifyModeEnum getModifyModeEnum(
            String requestId,
            ReplicaSet replicaSet,
            InstanceLevel srcInstanceLevel,
            InstanceLevel targetInstanceLevel,
            String sourceDiskType,
            String targetDiskType,
            boolean isDhg,
            boolean isIoAccelerationEnabled) throws RdsException, ApiException {
        ModifyReplicaSetResourceRequest.ModifyModeEnum modifyMode =
                ModifyReplicaSetResourceRequest.ModifyModeEnum.TRYINPLACE;

        if (MysqlParamSupport.isCluster(srcInstanceLevel)){
            // cluster 跨机
            return modifyMode;
        }

        if (isAliYun(replicaSet.getBizType())) {
            // TODO(wenfeng): 灰度控制，全网开放后，去掉这个判断
            List<String> resourceList = resourceService.getResourceRealValueList("MYSQL_ELASTIC_MOD_INS");
            if (CollectionUtils.isNotEmpty(resourceList) && StringUtils.equals("1", resourceList.get(0))) {
                boolean isDiskTypeChange = !Objects.equals(sourceDiskType, targetDiskType);
                boolean isCategoryChanged = !srcInstanceLevel.getCategory().equals(targetInstanceLevel.getCategory());
                // 内存降配默认走跨机；
                boolean isUpgradeMem = targetInstanceLevel.getMemSizeMB() >= srcInstanceLevel.getMemSizeMB();
                boolean isSrcSingleTenant  = replicaSetService.isCloudDiskSingleTenant(requestId, replicaSet);
                boolean isTargetSingleTenant =
                        replicaSetService
                                .isCloudSingleTenant(replicaSet.getBizType(), targetDiskType, targetInstanceLevel, isDhg);
                CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                custInstanceQuery.setPrimaryCustinsId(replicaSet.getId().intValue());
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
                List<CustInstanceDO> mirrorIns = custinsService.getCustIns(custInstanceQuery);

                boolean isRebuildSlave = mirrorIns != null && mirrorIns.size() > 0;
                boolean isArchChange =  PodCommonSupport.isArchChange(srcInstanceLevel, targetInstanceLevel);
                // modifyMode = TRYINPLACEUPDATEREPLICA表示尝试本地升降配。
                // 本地升配条件：
                // 1. 多租户
                // 2. 单纯规格变配
                // 3. 非xengine：xengine的规格参数没有热修改
                // 4. 内存升配：内存降配存在内存回收不了的情况，所以走跨机
                // 5. 实例在备库重搭时，Replica会发生变化，此时不能本地变配。
                // 6. 架构变化不支持本地升配
                // 7. 开启关闭IO加速因为设计资源池的修改，必需走跨级，不支持本地升降配
                if (!isSrcSingleTenant && !isRebuildSlave && !isTargetSingleTenant
                        && !isDiskTypeChange && !isCategoryChanged && isUpgradeMem
                        && !podCommonSupport.isXEngine(requestId, replicaSet.getName())
                        && !isArchChange && !isIoAccelerationEnabled) {
                    modifyMode = ModifyReplicaSetResourceRequest.ModifyModeEnum.TRYINPLACEUPDATEREPLICA;
                }
            }
        }
        return modifyMode;
    }

    /**
     修复 AvailableZoneInfo 信息，vbm实例使用用户ENI，需要主备都有vswitch。用主库vswitch填充备库
     */
    public void fixAvailableZoneInfo(AVZInfo avzInfo){
        if(avzInfo == null || avzInfo.getMultiAVZExParamDO() == null ||
                org.apache.commons.collections.CollectionUtils.isEmpty(avzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList())){
            return;
        }
        Map<String, AvailableZoneInfoDO> role2Az = avzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList().stream().collect(
                Collectors.toMap(az -> StringUtils.lowerCase(StringUtils.defaultString(az.getRole())), az -> az));
        AvailableZoneInfoDO masterAz = role2Az.get(ParamConstants.ROLE_MASTER);
        AvailableZoneInfoDO slaveAz = role2Az.get(ParamConstants.ROLE_SLAVE);
        if(masterAz != null && slaveAz !=null){
            if(slaveAz.getVSwitchID() == null){
                slaveAz.setVSwitchID(masterAz.getVSwitchID());
            }
        }
    }

    /**
     * 根据UID判断是否要切换到ServerlessV2形态
     *
     * @param uid
     */
    public boolean isServerlessV2(String uid, PodType runtimeType) {
        if (runtimeType == PodType.POD_ECS_RUND) {
            return false;
        }
        // 线上已全部切v2，因此改为黑名单控制
        ResourceDO resource = resourceService.getResourceByResKey(ServerlessConstant.MYSQL_SERVERLESS_V1_UID);
        if (resource == null || StringUtils.isBlank(resource.getRealValue())) {
            return true;
        } else {
            return !Arrays.asList(resource.getRealValue().split(",")).contains(uid);
        }
    }

    public String getUidByLoginId(String userId) {
        return Arrays.stream(Objects.toString(userId, "").split("_")).reduce((s1, s2) -> s2).orElse(null);
    }

    /**
     * 判断目标磁盘大小、等级是否满足缩容要求
     */
    public void checkShrinkCloudESSDValid(String requestId, Integer custinsId, String uid, Integer srcDiskSizeGB, Integer targetDiskSizeGB, ExtendedLogPlanDO extendedLogPlanDO) throws Exception {
        InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(custinsId, 0);
        Long currUsedDiskSizeMB = srcDiskSizeGB * 1024L;
        if (null != instancePerf && StringUtils.isNotEmpty(instancePerf.getDiskCurr())) {
            logger.info(String.format("instancePerf diskCurr is [%s]", instancePerf.getDiskCurr()));
            long perfDiskCurrSizeMB = new BigDecimal(instancePerf.getDiskCurr()).longValue();
            if (perfDiskCurrSizeMB <= 0) {
                logger.info(String.format("perfDiskCurrSizeMB is [%s], invalid, use srcDiskSizeGB*1024 as currUsedDiskSizeMB [%s].", perfDiskCurrSizeMB, currUsedDiskSizeMB));
            } else {
                currUsedDiskSizeMB = perfDiskCurrSizeMB;
            }
        }
        // 从resource开关中获取缩容下限比例（含白名单）
        Double ratio = PodDefaultConstants.SHRINK_LIMIT_RATIO;
        Integer offsetGB = PodDefaultConstants.SHRINK_LIMIT_STORAGE_USED_OFFSET_GB;
        int minOffsetGB = PodDefaultConstants.SHRINK_LIMIT_STORAGE_USED_MIN_OFFSET_GB;
        long copySpeedMBPerMinute = PodDefaultConstants.SHRINK_COPY_SPEED_MB_PER_MINUTE;
        double ossApplyBinlogCntPerMinute = PodDefaultConstants.OSS_APPLY_BINLOG_CNT_PER_MINUTE;
        boolean isCheckBinlog = false;
        ResourceDO resourceDO = resourceService.getResourceByResKey("K8S_SHRINK_DISK_MIN_SIZE_LIMIT_CONDITIONS");
        if (null != resourceDO && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
            JSONObject jsonObject = JSON.parseObject(resourceDO.getRealValue());
            ratio = null != jsonObject.get(uid) ? jsonObject.getJSONObject(uid).getDouble("limitRatio") : jsonObject.getJSONObject("default").getDouble("limitRatio");
            offsetGB = null != jsonObject.get(uid) ? jsonObject.getJSONObject(uid).getInteger("limitOffset") : jsonObject.getJSONObject("default").getInteger("limitOffset");
            minOffsetGB = null != jsonObject.get(uid) ? jsonObject.getJSONObject(uid).getIntValue("minOffset") : jsonObject.getJSONObject("default").getIntValue("minOffset");
            isCheckBinlog = null != jsonObject.get(uid) ? jsonObject.getJSONObject(uid).getBooleanValue("checkBinlog") : jsonObject.getJSONObject("default").getBooleanValue("checkBinlog");
            JSONObject subJsonObject = jsonObject.containsKey(uid)?jsonObject.getJSONObject(uid):jsonObject.getJSONObject("default");
            if(Objects.nonNull(subJsonObject)){
                copySpeedMBPerMinute = subJsonObject.containsKey("copySpeedMBPerMin")?subJsonObject.getLongValue("copySpeedMBPerMin"):PodDefaultConstants.SHRINK_COPY_SPEED_MB_PER_MINUTE;
                ossApplyBinlogCntPerMinute = subJsonObject.containsKey("ossApplyBinlogCntPerMin")?subJsonObject.getDoubleValue("ossApplyBinlogCntPerMin"):PodDefaultConstants.OSS_APPLY_BINLOG_CNT_PER_MINUTE;
            }
        }
        logger.info(String.format("shrink size limit condition: ratio-[%s], offsetGB-[%s], checkBinlog-[%s], minOffsetGB-[%s], copySpeedMBPerMinute-[%s], ossApplyBinlogCntPerMinute-[%s]", ratio, offsetGB, isCheckBinlog, minOffsetGB, copySpeedMBPerMinute, ossApplyBinlogCntPerMinute));
        double minValueMB = Math.min(srcDiskSizeGB * 1024L, Math.max(currUsedDiskSizeMB + minOffsetGB * 1024L, Math.min(currUsedDiskSizeMB + offsetGB * 1024L, currUsedDiskSizeMB * ratio)));
        logger.info(String.format("shrink size limit result (without binlog):sizeMB-[%s]", minValueMB));
        if(isCheckBinlog&&Objects.nonNull(extendedLogPlanDO)){
            checkShrinkCloudESSDBinlog(extendedLogPlanDO, custinsId, currUsedDiskSizeMB, copySpeedMBPerMinute, ossApplyBinlogCntPerMinute);
        }
        logger.info(String.format("shrink size limit result: sizeMB-[%s]", minValueMB));
        if (targetDiskSizeGB * 1024L < minValueMB) {
            String errMsg = String.format("Current Instance not support reduce disk size less than limit size [%sMB],(%sGB).", minValueMB, PodCommonSupport.getTargetSizeGB(minValueMB));
            throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "InvalidParam.DiskSize", errMsg});
        }
    }

    /**
     * limit localRetainTime and localRetainCnt, according to currUsedDiskSizeMB
     *
     * @param extendedLogPlanDO:  binlog policy with localRetain
     * @param custinsId:          cust instance id
     * @param currUsedDiskSizeMB: current disk usage (MB)
     * @throws RdsException extendedLogPlanDO example:
     *                      {
     *                      "charge": 1,
     *                      "custinsId": 12,
     *                      "enableBackupLog": true,
     *                      "enableUploadTime": 1714291383000,
     *                      "id": 9,
     *                      "localRetain": 10800,
     *                      "localRetainNum": 60,
     *                      "localRetainSpace": 30,
     *                      "logPlanChanged": false,
     *                      "retention": 7,
     *                      "uploadWay": 2
     *                      }
     */
    public void checkShrinkCloudESSDBinlog(ExtendedLogPlanDO extendedLogPlanDO, Integer custinsId, long currUsedDiskSizeMB, long copySpeedMBPerMinute, double ossApplyBinlogCntPerMinute) throws RdsException {
        // check parameter
        if (Objects.isNull(extendedLogPlanDO) || Objects.isNull(custinsId)) {
            return;
        }
        logger.info("extendedLogPlanDO is {}", JSON.toJSONString(extendedLogPlanDO, SerializerFeature.WriteMapNullValue));
        Map<String, Object> condition = new HashMap<>();
        int durationDay = 1;
        int durationMinutes = 24 * 60;
        condition.put("custinsId", custinsId);
        condition.put("startTime", OffsetDateTime.now().minusDays(durationDay));
        condition.put("endTime", OffsetDateTime.now());
        int binlogCnt = bakService.countBinlogFileListByCondition(condition);

        long shrinkEstimateDurationInSeconds = getShrinkEstimateDurationInSeconds(currUsedDiskSizeMB, copySpeedMBPerMinute);
        double binlogGenerateCntPerMin = getBinlogGenerateCntPerMin(binlogCnt, durationMinutes);
        long binlogCntDuringCopyData = estimateBinlogCountDuringShrinkage(binlogCnt, durationMinutes, currUsedDiskSizeMB, copySpeedMBPerMinute);
        logger.info("shrinkEstimateDurationInSeconds is [{}], extendedLogPlanDO.getLocalRetain() is [{}]; binlogCntDuringCopyData is [{}], extendedLogPlanDO.getLocalRetainNum() is [{}]", shrinkEstimateDurationInSeconds, extendedLogPlanDO.getLocalRetain(), binlogCntDuringCopyData, extendedLogPlanDO.getLocalRetainNum());
        if (shrinkEstimateDurationInSeconds > extendedLogPlanDO.getLocalRetain() || binlogCntDuringCopyData > extendedLogPlanDO.getLocalRetainNum()) {
            logger.info("binlogGenerateCntPerMin is [{}], ossApplyBinlogCntPerMinute is [{}]", binlogGenerateCntPerMin, ossApplyBinlogCntPerMinute);
            if (binlogGenerateCntPerMin >= ossApplyBinlogCntPerMinute) {
                String errMsg = String.format("Binlog generate speed [%s per minute] is too fast, Binlog LocalRetainTime no less than [%s s], Binlog LocalRetainCount no less than %s.", binlogGenerateCntPerMin, shrinkEstimateDurationInSeconds, binlogCntDuringCopyData);
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedReduceDiskSize", errMsg});
            }
        }
    }

    /**
     * estimate of the time (in seconds) required to shrink a disk.
     *
     * @param curUsedSizeMB         : current disk usage(MB)
     * @param copySpeedMBPerMinute: copy speed（MB/Minuter）
     * @return the estimated time (in seconds)
     * @throws IllegalArgumentException if any parameter passed in is a negative value or copySpeedMBPerMinute is 0
     */
    private static long getShrinkEstimateDurationInSeconds(long curUsedSizeMB, long copySpeedMBPerMinute) throws IllegalArgumentException {
        // check parameter
        if (curUsedSizeMB < 0) {
            throw new IllegalArgumentException("curUsedSizeMB must be non-negative.");
        }
        if (copySpeedMBPerMinute <= 0) {
            throw new IllegalArgumentException("copySpeedMBPerMinute must be positive.");
        }

        // computer estimated time（in minutes）
        double shrinkEstimateDurationInMin = (double) curUsedSizeMB / copySpeedMBPerMinute;

        // convert minutes to seconds and round up
        return (long) Math.ceil(shrinkEstimateDurationInMin * 60L);
    }


    /**
     * calculate the number of binlog generated per minute, and keep the result to three decimal places.
     *
     * @param binlogCnt:       total number of binlog
     * @param durationMinutes: the time corresponding to binlogCount (in minutes)
     * @return return the number of Binlog generated per minute, and keep the result to three decimal places.
     * @throws IllegalArgumentException if binlogCnt or durationMinutes is negative, or durationMinutes is 0.
     */
    private static double getBinlogGenerateCntPerMin(int binlogCnt, int durationMinutes) {
        // check parameter
        if (binlogCnt < 0) {
            throw new IllegalArgumentException("binlogCnt must be non-negative.");
        }
        if (durationMinutes <= 0) {
            throw new IllegalArgumentException("durationMinutes must be positive.");
        }

        // calculate the number of binlog generated per minute, and keep the result to three decimal places.
        double rawSpeed = (double) binlogCnt / durationMinutes;
        return Math.round(rawSpeed * 1000) / 1000.0;
    }

    /**
     * estimate the number of binlog generated during the disk shrink process.
     *
     * @param binlogCount:          total number of binlog
     * @param durationMinutes:      the time corresponding to binlogCount (in minutes)
     * @param currentUsedSizeMB:    current disk usage(MB)
     * @param copySpeedMBPerMinute: copy speed（MB/Minuter）
     * @return the estimated number of binlog。
     */
    private static long estimateBinlogCountDuringShrinkage(int binlogCount, int durationMinutes, long currentUsedSizeMB, long copySpeedMBPerMinute) {
        // check parameter
        if (durationMinutes <= 0 || copySpeedMBPerMinute <= 0) {
            throw new IllegalArgumentException("Duration minutes and copy speed must be positive.");
        }
        if (binlogCount < 0 || currentUsedSizeMB < 0) {
            throw new IllegalArgumentException("Binlog count and current used size must be non-negative.");
        }

        // estimate of the time (in minutes) required to shrink a disk.
        double estimatedShrinkageDurationMinutes = (double) currentUsedSizeMB / copySpeedMBPerMinute;
        // calculate the number of binlog generated per minute
        double binlogGenerateCountPerMinute = (double) binlogCount / durationMinutes;

        // estimate the number of binlog generated during the disk shrink process, and round up.
        return (long) Math.ceil(binlogGenerateCountPerMinute * estimatedShrinkageDurationMinutes);
    }


    /**
     * 检查当前实例是否满足缩容次数
     */
    public void checkShrinkLimit(String requestId, String DBInstanceName, String uid) throws Exception {
        Integer shrinkLimitCount = PodDefaultConstants.SHRINK_LIMIT_COUNT;
        Integer shrinkLimitPeriodOfDays = PodDefaultConstants.SHRINK_LIMIT_PERIOD_OF_DAYS;
        ResourceDO resourceDO = resourceService.getResourceByResKey("K8S_SHRINK_LIMIT_COUNT");
        if (null != resourceDO && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
            JSONObject jsonObject = JSON.parseObject(resourceDO.getRealValue());
            shrinkLimitCount = null != jsonObject.get(uid) ? jsonObject.getJSONObject(uid).getInteger("shrinkLimitCount") : jsonObject.getJSONObject("default").getInteger("shrinkLimitCount");
            shrinkLimitPeriodOfDays = null != jsonObject.get(uid) ? jsonObject.getJSONObject(uid).getInteger("shrinkLimitPeriodOfDays") : jsonObject.getJSONObject("default").getInteger("shrinkLimitPeriodOfDays");
        }
        logger.info(String.format("shrink count limit condition: limitCount-[%s], limitPeriodOfDays-[%s]", shrinkLimitCount, shrinkLimitPeriodOfDays));
        TransferTaskListResult transResult = dBaasMetaService.getDefaultClient().listTransferTasks(requestId, DBInstanceName, DBInstanceName, OffsetDateTime.now().minusDays(shrinkLimitPeriodOfDays),null,null);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(transResult.getItems()) && shrinkLimitCount <= transResult.getItems().stream().filter(t -> "shrink".equals(t.getComment())).collect(Collectors.toList()).size()) {
            throw new RdsException(SHRINK_COUNT_REACHED_LIMIT);
        }
    }

    public boolean isSupportShrink() {
        ResourceDO resourceDO = resourceService.getResourceByResKey("MYSQL_SUPPORT_SHRINK_GRAY_RELEASE");
        return null != resourceDO && StringUtils.equalsIgnoreCase(resourceDO.getRealValue(),"true");
    }

    /**
     * serverless实例缩容灰度开关，默认打开
     */
    public boolean isServerlessSupportShrink() {
        ResourceDO resourceDO = resourceService.getResourceByResKey("MYSQL_SERVERLESS_SUPPORT_SHRINK_GRAY_RELEASE");
        return null == resourceDO || StringUtils.equalsIgnoreCase(resourceDO.getRealValue(),"true");
    }
    public IPWhiteList[] getAndCheckTemplateList(int userId) throws RdsException {
        String templateIdRaw= getParameterValue(ParamConstants.WHITELIST_TEMPLATE_LIST,null);
        if (StringUtils.isEmpty(templateIdRaw) || templateIdRaw.equals("0")) {
            return null;
        }
        String[] templateIdStringList=templateIdRaw.split(",");
        if(templateIdStringList.length>10){
            throw new RdsException(ErrorCode.TOO_MANY_WHITELIST_TEMPLATE_IDS);
        }
        try{
            IPWhiteList[] templateIdList=new IPWhiteList[templateIdStringList.length];
            for(int i=0;i<templateIdStringList.length;i++){
                int templateId=Integer.parseInt(templateIdStringList[i]);
                if(templateId<=0){
                    throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
                }
                WhitelistTemplateDO whitelistTemplate = whitelistTemplateIDao.getWhitelistTemplate(userId, templateId);
                if(whitelistTemplate==null || StringUtils.isBlank(whitelistTemplate.getIps())){
                    throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
                }
                IPWhiteList ipWhiteList = new IPWhiteList();
                ipWhiteList.setWhiteList(whitelistTemplate.getIps());
                ipWhiteList.setGroupName(whitelistTemplate.getTemplateName()+ParamConstants.TEMPLATE_SUFFIX);
                ipWhiteList.setGroupTag("template");
                ipWhiteList.setNetType(getAndCheckWhitelistNetType());
                ipWhiteList.setIpType(IPWhiteList.IpTypeEnum.valueOf(getAndCheckSecurityIpType()));
                templateIdList[i]=ipWhiteList;
            }
            return templateIdList;
        }catch (NumberFormatException | RdsException ne){
            throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
        }
    }
    public int[] getAndCheckTemplateIdList() throws RdsException {
        String templateIdRaw= getParameterValue(ParamConstants.WHITELIST_TEMPLATE_LIST,null);
        if (StringUtils.isEmpty(templateIdRaw) || templateIdRaw.equals("0")) {
            return null;
        }
        try {
            String[] tempIdList = templateIdRaw.split(",");
            int[] idList = new int[tempIdList.length];
            for (int i = 0; i < tempIdList.length; i++) {
                int templateId=Integer.parseInt(tempIdList[i]);
                idList[i]=templateId;
            }
            return idList;
        }catch (Exception e){
            throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
        }
    }

    /**
     * 获取接口入参是否自动创建代理。接口未指定的情况下，集群版默认创建。
     * */
    public Boolean getAutoCreateProxy(InstanceLevel instanceLevel) {
        String autoCreateProxy = getParameterValue("AutoCreateProxy", null);
        if (StringUtils.isEmpty(autoCreateProxy)) {

            if (instanceLevel.getCategory() == InstanceLevel.CategoryEnum.CLUSTER) {
                autoCreateProxy = "true";
            } else {
                autoCreateProxy = "false";
            }
        }
        return Boolean.valueOf(autoCreateProxy);
    }
}
