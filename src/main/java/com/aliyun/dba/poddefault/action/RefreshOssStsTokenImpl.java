package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_REFRESH_OSS_STS_TOKEN;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRefreshOssStsTokenImpl")
public class RefreshOssStsTokenImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(RefreshOssStsTokenImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);

            boolean coldDataEnabled = false;
            Map<String, String> replicaSetLabels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, dbInstanceName);
            if (!MapUtils.isEmpty(replicaSetLabels) && replicaSetLabels.containsKey(ParamConstants.RDS_COLD_DATA_ENABLED)) {
                String coldDataEnabledStr = replicaSetLabels.get(ParamConstants.RDS_COLD_DATA_ENABLED);
                if (org.apache.commons.lang.StringUtils.isNotBlank(coldDataEnabledStr) && Boolean.parseBoolean(coldDataEnabledStr)) {
                    coldDataEnabled = true;
                }
            }

            if (!coldDataEnabled) {
                String message = String.format("RequestId: %s, dbInstanceName: %s, coldDataEnabled: %s, cannot refresh oss sts token ", requestId, dbInstanceName, coldDataEnabled);
                Map<String, Object> data = new HashMap<>();
                data.put("Message", message);
                data.put("DBInstanceID", replicaSet.getId());
                data.put("DBInstanceName", dbInstanceName);
                return data;
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);

            String parameter = jsonObject.toJSONString();

            int priority = PodDefaultConstants.TASK_PRIORITY_VIP;

            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, TASK_REFRESH_OSS_STS_TOKEN, parameter, priority);

            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", dbInstanceName);
            return data;
        } catch (RdsException re) {
            logger.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
