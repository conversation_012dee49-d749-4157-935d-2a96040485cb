package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.base.support.MySQLParamConstants.AURORA_SWITCH_FLAG_API_FORCE;
import static com.aliyun.dba.base.support.MySQLParamConstants.AURORA_SWITCH_FLAG_API_NORMAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.ParamConstants.TARGET_INSTANCE_ID;

@Service
public class SwitchReadDBInstanceHAService{
    private static final LogAgent logger = LogFactory.getLogAgent(SwitchReadDBInstanceHAService.class);

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    private PodParameterHelper podParameterHelper;

    public Map<String, Object> switchDBInstanceHa(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String targetInstanceId = paramSupport.getParameterValue(params, TARGET_INSTANCE_ID);
            Integer switchType = CustinsValidator.getRealNumber(
                    paramSupport.getParameterValue(params, ParamConstants.SWITCH_TYPE), CustinsSupport.SWITCH_TYPE_NORMAL);
            if (!CustinsSupport.SWITCH_TYPE_SET.contains(switchType)) {
                return createErrorResponse(ErrorCode.INVALID_SWITCH_TYPE);
            }
            Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);


            // 状态和切换条件判断
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            JSONObject taskParam = new JSONObject();
            // Task parameter format: {"switch_flag":4, "switch_info":{"mode":"immediate"}, "target_replica_id": 111111}
            Integer switchFlag = CustinsSupport.SWITCH_TYPE_NORMAL.equals(switchType) ? AURORA_SWITCH_FLAG_API_NORMAL : AURORA_SWITCH_FLAG_API_FORCE;
            taskParam.put("requestId", requestId);
            taskParam.put("switch_flag", switchFlag);
            taskParam.put("target_replica_id", "");
            taskParam.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            //单节点的只有tddl
            String taskKey = PodDefaultConstants.TASK_READ_HA_SWITCH_INS;
            if(PodParameterHelper.isAliGroup(replicaSet.getBizType()) && replicaSetService.isAligroupDoubleNodeRead(requestId,replicaSet.getName())){
                taskKey = PodDefaultConstants.TASK_TDDL_HA_SWITCH_INS;
                if(replicaSet.getConnType() == ReplicaSet.ConnTypeEnum.PHYSICAL){
                    taskKey = PodDefaultConstants.TASK_HA_SWITCH_INS;
                }
            }
            Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, taskParam.toJSONString(), 0);
            // 更新实例状态为 HA 切换中
            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.HA_SWITCHING.toString());

            Map<String, Object> data = new HashMap<>();
            data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
            data.put(ParamConstants.SWITCH_TYPE, switchType);
            data.put(ParamConstants.TASK_ID, taskId);
            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception e) {
            logger.error(requestId + " Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
