package com.aliyun.dba.poddefault.action;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeAnalyticAccountsImpl")
public class DescribeAnalyticAccountsImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeAnalyticAccountsImpl.class);

    @Resource
    RdsApi rdsApi;

    @Resource
    protected DBaasMetaService dbaasMetaService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    private static final String DTS_ADMIN = "dts_admin";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String analyticInsName = paramSupport.getParameterValue(params, ParamConstants.ANALYTIC_DB_INSTANCE_NAME);
            String uid = paramSupport.getParameterValue(params, ParamConstants.UID);
            String userId = paramSupport.getParameterValue(params, ParamConstants.USER_ID);
            String regionId = paramSupport.getParameterValue(params, ParamConstants.REGION_ID);

            // 查询关联关系
            podCommonSupport.checkAnalyticDBInstanceRelation(requestId, custins.getInsName(), analyticInsName);

            logger.info("requestId : {}, request to DescribeAccountList", requestId);
            Map<String, Object> result = rdsApi.getDataByRdsApi(new HashMap<String, String>() {{
                put("Action", "DescribeAccountList");
                put("DBInstanceName", analyticInsName);
                put("UID", uid);
                put("User_id", userId);
                put("RegionID", regionId);
                put("RequestId", requestId);
                put("DBInstanceModelType", "cluster");
            }}, ParamConstants.YAOCHI_ACCESS);
            logger.info("requestId : {}, result:{}", requestId, JSONObject.toJSONString(result));

            //过滤掉dts_admin账号
            List<Map> accounts = JSONObject.parseArray(JSONObject.toJSONString(result.remove("Accounts")), Map.class);
            accounts = accounts.stream()
                    .filter(account -> !DTS_ADMIN.equalsIgnoreCase(String.valueOf(account.get("AccountName"))))
                    .collect(Collectors.toList());
            result.put("Accounts", accounts);

            return result;

        } catch (RdsException ex) {
            log.error("DescribeAccountList failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("DescribeAccountList Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
