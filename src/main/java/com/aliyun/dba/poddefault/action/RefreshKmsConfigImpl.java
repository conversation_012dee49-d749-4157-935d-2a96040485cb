package com.aliyun.dba.poddefault.action;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * refresh sts token
 */

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRefreshKmsConfigImpl")
class RefreshKmsConfigImpl implements IAction {

    @Autowired
    CustinsParamService custinsParamService;

    @Autowired
    WorkFlowService workFlowService;

    @Autowired
    private MysqlParamSupport mysqlParamSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        String requestId = RequestSession.getRequestId();
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        try {
            // check custins
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams, null);
            // check supported db type/version
            if (!custins.isMysqlGt57()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DB_TYPE);
            }
            // check if tde enabled
            CustinsParamDO param_tde_enabled = custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.TDE_ENABLED);
            if (param_tde_enabled == null || !StringUtils.equalsIgnoreCase("1", param_tde_enabled.getValue())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DB_TDE_STATUS);
            }
            JSONObject parameters =  new JSONObject();
            Object taskId = workFlowService.dispatchTask(PodDefaultConstants.TARGET_TYPE_CUSTINS, custins.getInsName(),
                                                         PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_REFRESH_KMS_SERVICE_CONFIG, parameters.toJSONString(),
                                                         WorkFlowService.TASK_PRIORITY_VIP);
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            log.warn(requestId + " RdsException: " + re );
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            log.warn(requestId + " Exception: " + e);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}