package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.apsaradb.activityprovider.model.GeneralCloudDisk;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.createReadOnly.BaseCreateReadOnlyInsService;
import com.aliyun.dba.poddefault.action.service.createReadOnly.aligroup.AligroupCreateReadOnlyInsImpl;
import com.aliyun.dba.poddefault.action.service.createReadOnly.aliyun.AliyunCreateReadOnlyInsImpl;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_STORAGE_TYPE;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ_BACKUP;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateReadDBInstanceImpl")
public class CreateReadDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CreateReadDBInstanceImpl.class);

    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private DBaasMetaService metaService;
    @Resource
    private GdnInstanceService gdnInstanceService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MysqlEncryptionService mysqlEncryptionService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    public CloudDiskCompressionHelper cloudDiskCompressionHelper;

    /**
     * 创建只读实例
     *
     * @category CreateReadDBInstance
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            //step 1: set params thread local
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);

            //step 2: build read only request
            CreateReadOnlyInsRequest createReadOnlyRequest = buildCommonRequest(params);

            //step 3: do create read only
            BaseCreateReadOnlyInsService baseCreateReadOnlyInsService = SpringContextUtil.getBeanByClassName(AliyunCreateReadOnlyInsImpl.class);
            final ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.fromValue(createReadOnlyRequest.getBizType());
            if(ReplicaSet.BizTypeEnum.ALIGROUP == bizType){
                baseCreateReadOnlyInsService = SpringContextUtil.getBeanByClass(AligroupCreateReadOnlyInsImpl.class);
            }
            Boolean autoCreateProxy = paramSupport.getAutoCreateProxy(params);
            // 下发创建实例的后置任务
            if (autoCreateProxy) {
                logger.info("custins {} start to create proxy.", createReadOnlyRequest.getDbInstanceName());
                workFlowService.dispatchTask("custins", createReadOnlyRequest.getDbInstanceName(), PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_MODIFY_INS_AFTER_CREATE, "", WorkFlowService.TASK_PRIORITY_COMMON);
            }

            Object result = baseCreateReadOnlyInsService.doCreateReadOnly(createReadOnlyRequest, params);
            if(result instanceof Map){
                return (Map<String, Object>) result;
            }

            //step 4: build response
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", result);
            data.put("DBInstanceName", createReadOnlyRequest.getDbInstanceName());
            data.put("ReadDBInstanceName", createReadOnlyRequest.getReadInsName());
            data.put("ConnectionString", getConnnectionString(createReadOnlyRequest));
            data.put("Port", createReadOnlyRequest.getConnStrPortStr());
            return data;
        } catch (RdsException e) {
            logger.error("doCreateReadOnly failed!", e);
            return createErrorResponse(e.getErrorCode());
        } catch (Exception e) {
            logger.error("doRecoverDBInstance failed!", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * build common request
     * @param params
     * @return
     * @throws RdsException
     * @throws com.aliyun.apsaradb.dbaasmetaapi.ApiException
     * @throws com.aliyun.apsaradb.gdnmetaapi.ApiException
     */
    private CreateReadOnlyInsRequest buildCommonRequest(Map<String, String> params) throws RdsException, com.aliyun.apsaradb.dbaasmetaapi.ApiException, com.aliyun.apsaradb.gdnmetaapi.ApiException, Exception {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        Integer userId = mysqlParameterHelper.getAndCreateUserId();
        User user = metaService.getDefaultClient().getUserById(requestId, userId, false);
        String bid = user.getBid();
        String uid = user.getAliUid();
        String dbInstanceName = paramSupport.getDBInstanceName(params);
        String insType = paramSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_USED_TYPE, CUSTINS_INSTYPE_READ.toString());
        String dbType = paramSupport.getAndCheckDBType(params, null);
        String dbVersion = paramSupport.getAndCheckDBVersion(params, dbType, true);
        String gdnInstanceName = paramSupport.getParameterValue(params, "GdnInstanceName");
        String readInsName = CheckUtils.checkValidForInsName(paramSupport.getParameterValue(params, "ReadDBInstanceName"));
        String targetMinorVersion = paramSupport.getParameterValue(params, "TargetMinorVersion");
        Boolean isCreatingGdnInstance = StringUtils.isNotEmpty(gdnInstanceName);
        String rsTemplateName = paramSupport.getParameterValue(params, "RsTemplateName");
        String orderId = paramSupport.getParameterValue(params, ParamConstants.ORDERID);
        String accessId = paramSupport.getParameterValue(params, ParamConstants.ACCESSID);
        String comment = null;
        if (CustinsParamSupport.hasParameterValue(params, ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            comment = CheckUtils.checkLength(SupportUtils.decode(getParameterValue(params, ParamConstants.DB_INSTANCE_DESCRIPTION)),
                    1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
        }

        ReplicaSet primaryReplicaSet = null;
        String centerRegionId = null;
        // 获取主实例信息
        if (isCreatingGdnInstance) {
            // 校验 gdn 相关信息, 并且获取中心节点所在单元
            InstanceMember primaryMember = gdnInstanceService.getPrimaryMember(requestId, dbInstanceName, gdnInstanceName);
            centerRegionId = primaryMember.getMemberRegion();
            primaryReplicaSet = replicaSetService.getAndCheckUserReplicaSetFromCenterRegion(params, centerRegionId);
        }
        if (primaryReplicaSet == null) {
            primaryReplicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
        }
        String connType = paramSupport.getParameterValue(params, "DBInstanceConnType", Objects.requireNonNull(primaryReplicaSet.getConnType()).toString());
        String portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), primaryReplicaSet.getService());
        String connStrPortStr = CheckUtils.parseInt(portStr, 1000, 65534, ErrorCode.INVALID_PORT).toString();
        String bizType = primaryReplicaSet.getBizType().toString();

//        String diskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE, "local_ssd");
        String diskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE);
        if (StringUtils.isEmpty(diskType)){
            diskType = getParameterValue(params, "StorageType");
        }
        if (StringUtils.isEmpty(diskType)){
            ReplicaListResult listReplicasInReplicaSet = metaService.getRegionClient(centerRegionId).listReplicasInReplicaSet(
                    requestId, primaryReplicaSet.getName(), null, null, null, null);
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            Optional<Replica> optReplica = currentReplicas.stream().filter(replica -> Replica.RoleEnum.MASTER.equals(replica.getRole())).findFirst();
            Replica masterReplica = optReplica.isPresent() ? optReplica.get() : currentReplicas.size() > 0 ? currentReplicas.get(0) : null;
            diskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE, masterReplica != null ? masterReplica.getStorageType().toString() : "local_ssd");
        }
        String primaryCompressionMode = null;
        Double primaryCompressionRatio = null;
        if (!isCreatingGdnInstance) {
            // check primary compression
            primaryCompressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, primaryReplicaSet.getName(), null);
            primaryCompressionRatio = cloudDiskCompressionHelper.getCompressionRatio(requestId, primaryReplicaSet.getName(), null, null, null);
        }

        Integer diskSize = CheckUtils.parseInt(paramSupport.getParameterValue(params, ParamConstants.STORAGE, primaryReplicaSet.getDiskSizeMB() / 1024),
                5, 102400, ErrorCode.INVALID_STORAGE);
        Integer diskSizeBeforeCompression = diskSize;
        // modify disk_size to logic_size if compression is on
        if (CloudDiskCompressionHelper.isCompressionModeOn(primaryCompressionMode)) {
            if (paramSupport.hasParameter(params, ParamConstants.STORAGE)) {
                diskSize = CloudDiskCompressionHelper.getLogicalSize(diskSize, primaryCompressionRatio);
            }
        }
        String classCode = paramSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
        String clusterName = paramSupport.getParameterValue(params, "ClusterName");
        String vpcId = getParameterValue(params, ParamConstants.VPC_ID);
        String vpcSwitch = getParameterValue(params, ParamConstants.VSWITCH_ID);
        String ipAddress = getParameterValue(params, ParamConstants.IP_ADDRESS);
        String vpcInstanceId = getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);
        String performanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(diskType);
        diskType = PodParameterHelper.transferDiskTypeParam(diskType);
        String instructionSetArch = paramSupport.getParameterValue(params, "InstructionSetArch");
        //公有云通过instance_level中的扩展信息字段来决定是否是arm架构
        InstanceLevel instanceLevel = metaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
        if(StringUtils.isBlank(instructionSetArch)){
            if(PodCommonSupport.isArm(instanceLevel)){
                instructionSetArch = "ARM";
            }
        }
        String dbEngine = paramSupport.isMysqlXDB(primaryReplicaSet.getService(), primaryReplicaSet.getServiceVersion(), primaryReplicaSet.getClassCode()) ? "XDB" : "MySQL";
        Boolean isDhg = paramSupport.isDHGCluster(clusterName);
        boolean isArmIns = CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(instructionSetArch);
        String insTypeDesc = CUSTINS_INSTYPE_READ_BACKUP.toString().equals(insType)
                ? ReplicaSet.InsTypeEnum.READBACKUP.toString() : ReplicaSet.InsTypeEnum.READONLY.toString();
        boolean isXdbEngine = "XDB".equals(dbEngine);
        dbVersion = isArmIns ? dbVersion : primaryReplicaSet.getServiceVersion();
        InstanceLevel primaryInsInstanceLevel = metaService.getDefaultClient().getInstanceLevel(requestId, primaryReplicaSet.getService(),
                primaryReplicaSet.getServiceVersion(), primaryReplicaSet.getClassCode(), true);
        if (!Objects.equals(primaryInsInstanceLevel.getHostType(), instanceLevel.getHostType())) {
            logger.error("inconsistent level between primary and read-only instances");
            throw new RdsException(ErrorCode.INVALID_LEVEL);
        }
        GeneralCloudDisk generalCloudDisk = new GeneralCloudDisk();
        String optimizedWritesInfo = null;
        boolean isSingleTenant = replicaSetService.isCloudSingleTenant(primaryReplicaSet.getBizType(), diskType, instanceLevel, isDhg);
        // 多租户场景不允许创建使用用户密钥的云盘加密实例。
        if (!isCreatingGdnInstance) {
            mysqlEncryptionService.checkEncryptionKeyByReplicaSet(requestId, primaryReplicaSet, isSingleTenant);
            generalCloudDisk = podCommonSupport.setGeneralCloudDiskConfig(requestId, params, dbVersion, instanceLevel, diskType, primaryReplicaSet, dbInstanceName);
            // 只读实例写优化完全继承主实例配置
            optimizedWritesInfo = podCommonSupport.getOptimizedWritesInfo(requestId, dbVersion, diskType, primaryReplicaSet);
        }

        return CreateReadOnlyInsRequest.builder()
                .bizType(bizType)
                .centerRegionId(centerRegionId)
                .classCode(classCode)
                .clusterName(clusterName)
                .connStrPortStr(connStrPortStr)
                .connType(connType)
                .dbEngine(dbEngine)
                .dbInstanceName(dbInstanceName)
                .dbType(dbType)
                .dbVersion(dbVersion)
                .diskSize(diskSize)
                .gdnInstanceName(gdnInstanceName)
                .instructionSetArch(instructionSetArch)
                .insType(insType)
                .isArmIns(isArmIns)
                .isCreatingGdnInstance(isCreatingGdnInstance)
                .isDhg(isDhg)
                .requestId(requestId)
                .rsTemplateName(rsTemplateName)
                .generalCloudDisk(generalCloudDisk)
                .optimizedWritesInfo(optimizedWritesInfo)
                .userId(userId)
                .diskType(diskType)
                .readInsName(readInsName)
                .bid(bid)
                .uid(uid)
                .primaryReplicaSet(primaryReplicaSet)
                .insTypeDesc(insTypeDesc)
                .vpcId(vpcId)
                .vSwitchId(vpcSwitch)
                .iPAddress(ipAddress)
                .vpcInstanceId(vpcInstanceId)
                .performanceLevel(performanceLevel)
                .isXdbEngine(isXdbEngine)
                .targetMinorVersion(targetMinorVersion)
                .primaryInsInstanceLevel(primaryInsInstanceLevel)
                .comment(comment)
                .orderId(orderId)
                .accessId(accessId)
                .compressionMode(primaryCompressionMode)
                .compressionRatio(primaryCompressionRatio)
                .diskSizeGBBeforeCompression(diskSizeBeforeCompression)
                .build();
    }

    private String getConnnectionString (CreateReadOnlyInsRequest request) throws com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        String connectionString = null;
        String connType = request.getConnType();
        if (!("k8sService".equalsIgnoreCase(connType) || CONN_TYPE_PHYSICAL.equalsIgnoreCase(connType) || CONN_TYPE_TDDL.equalsIgnoreCase(connType))) {
            try {
                EndpointListResult endpointListResult = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(request.getRequestId(), request.getReadInsName(), null, 1, 10, null);
                if (endpointListResult != null && CollectionUtils.isNotEmpty(endpointListResult.getItems())) {
                    connectionString = endpointListResult.getItems().get(0).getAddress();
                }
            } catch (Exception e) {
                //ignore
                logger.warn("Get replicaset's endpoint failed, {}", e.getMessage());
            }
        }
        return connectionString;
    }
}