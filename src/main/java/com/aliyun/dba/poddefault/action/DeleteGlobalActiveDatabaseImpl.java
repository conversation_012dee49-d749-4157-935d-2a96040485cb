package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.gdnmetaapi.ApiException;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstance;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMemberListResult;
import com.aliyun.dba.base.lib.GdnMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.GAD.GADRole;
import com.aliyun.dba.poddefault.action.support.GAD.GadConstant;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteGlobalActiveDatabaseImpl")
public class DeleteGlobalActiveDatabaseImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteGlobalActiveDatabaseImpl.class);
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private GdnMetaService gdnMetaService;

    @Autowired
    protected WorkFlowService workFlowService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        logger.info("DeleteGlobalActiveDatabaseImpl doActionRequest params:{}", JSON.toJSONString(params));
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        List<InstanceMember> unitInstanceMembers = null;
        List<String> deleteUnitList = new ArrayList<>();

        try {
            String bid = paramSupport.getAndCheckBID(params);
            String uid = paramSupport.getUID(params);

            String gadInstanceName = paramSupport.getParameterValue(params, "GadInstanceName");
            String memberInstanceName = paramSupport.getParameterValue(params, "MemberInstanceName");
            String isDeleteGadInstance = paramSupport.getParameterValue(params, "IsDeleteGadInstance", "0");
            GdnInstance gdnInstance = null;
            try {
                gdnInstance = gdnMetaService.getClient().getGdnInstance(requestId, gadInstanceName, true);
            } catch (ApiException e) {
                if (!e.getResponseBody().contains("GdnInstanceNotFound")) {
                    throw e;
                }
            }
            if (null == gdnInstance) {
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "DeleteGlobalActiveDatabaseImpl","gad instance not found"});
            } else if (!gdnInstance.getStatus().equals(ReplicaSet.StatusEnum.ACTIVATION.toString())) {
                logger.error(requestId + " Gad status error, is{}",gdnInstance.getStatus());
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "DeleteGlobalActiveDatabaseImpl","gad instance not in activation status"});
            }
            InstanceMemberListResult instanceMemberListResult = gdnMetaService.getClient().listMembers(requestId, gadInstanceName, gadInstanceName);



            if (isDeleteGadInstance.equalsIgnoreCase("true") || isDeleteGadInstance.equals("1")) {
                unitInstanceMembers = instanceMemberListResult.getItems().stream().filter(i -> i.getRole().equals(GADRole.UNIT.toString())).collect(Collectors.toList());
                logger.info(requestId + " DeleteGlobalActiveDatabaseImpl unitInstanceMembers {}",JSON.toJSONString(unitInstanceMembers));
                for (InstanceMember e : unitInstanceMembers) {
                    logger.info(requestId + " DeleteGlobalActiveDatabaseImpl getDtsInstanceId for member{}",e.getMemberName());
                    String dtsInstanceId = getDtsInstanceId(requestId, e.getMemberName());
                    if (null != dtsInstanceId) {
                        deleteUnitList.add(e.getMemberName());
                    }
                }

//                gdnMetaService.getClient().deleteGdnInstance(requestId, gadInstanceName, uid, bid);

            } else {
                if (StringUtils.isEmpty(memberInstanceName)) {
                    throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "DeleteGlobalActiveDatabaseImpl","invalid param MemberInstanceName!"});
                }
                Optional<InstanceMember> first = instanceMemberListResult.getItems().stream().filter(i -> i.getMemberName().equals(memberInstanceName)).findFirst();
                if (!first.isPresent()) {
                    throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "DeleteGlobalActiveDatabaseImpl","the gadInstance has no member!"});

                }
                if (first.get().getRole().equals(GADRole.CENTRAL.toString())) {
                    throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "DeleteGlobalActiveDatabaseImpl","Central member cannot be deleted, try delete GAD Instance"});
                }
                String dtsInstanceId = getDtsInstanceId(requestId, memberInstanceName);
                if (null != dtsInstanceId) {
                    logger.info(requestId + " DeleteGlobalActiveDatabaseImpl deleteMemberNames's dtsInstanceId exists!");
                    deleteUnitList.add(memberInstanceName);
                }

//                gdnMetaService.getClient().deleteMember(requestId, gadInstanceName, InstanceMember.MemberKindEnum.REPLICASET.toString(), memberInstanceName);
            }


            // create task for delete gad members;
            logger.info(requestId + " DeleteGlobalActiveDatabaseImpl deleteMemberNames is {}",JSON.toJSONString(deleteUnitList));


            JSONObject deleteGadTaskParameters = new JSONObject();
            deleteGadTaskParameters.put("gadInstanceName", gadInstanceName);
            deleteGadTaskParameters.put("deleteType", isDeleteGadInstance.equalsIgnoreCase("true") || isDeleteGadInstance.equals("1") ? "gad":"gad_member");
            deleteGadTaskParameters.put("uid", uid);
            deleteGadTaskParameters.put("bid", bid);
            deleteGadTaskParameters.put("deleteUnitList", String.join(",",deleteUnitList));
            Optional<InstanceMember> centralInstanceMember = instanceMemberListResult.getItems().stream().filter(i -> i.getRole().equals(GADRole.CENTRAL.toString())).findFirst();
            if(!centralInstanceMember.isPresent()){
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "DeleteGlobalActiveDatabaseImpl","the gadInstance has no central member!"});
            }
            Object taskId = workFlowService.dispatchTask("custins", centralInstanceMember.get().getMemberName(), PodDefaultConstants.DOMAIN_MYSQL, "delete_gad_instance_nodes", deleteGadTaskParameters.toJSONString(), 0);

            Map<String, Object> data = new HashMap<>();
            data.put("gadInstanceName", gadInstanceName);
            data.put("taskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " Gdn meta error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestGdnMetaFailed", "Request gdn meta api failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private String getDtsInstanceId(String requestId,String memberName) throws ApiException, IOException {
        Map<String, String> instanceMemberDatas = gdnMetaService.getClient().getInstanceMemberDatas(requestId,InstanceMember.MemberKindEnum.REPLICASET.toString(), memberName);
        // 黑名单数据外都返回
        logger.info(requestId + " DeleteGlobalActiveDatabaseImpl getDtsInstanceId info {}",JSON.toJSONString(instanceMemberDatas));
        String dtsInstanceInfo = instanceMemberDatas.getOrDefault("dtsInstance", null);
        if(dtsInstanceInfo == null) {
            return null;
        }
        Map<String,Object> result = new ObjectMapper().readValue(dtsInstanceInfo, HashMap.class);
        logger.info(requestId + "DeleteGlobalActiveDatabaseImpl getDtsInstanceId{}",result.get("dtsInstanceId").toString());
        return result.get("dtsInstanceId").toString();
    }
}
