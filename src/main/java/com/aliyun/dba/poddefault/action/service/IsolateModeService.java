package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class IsolateModeService {
    @Resource
    private CommonProviderService commonProviderService;

    /**
     * 支持实例级别的独享和共享切换
     *
     * @param requestId
     * @param replicaSet
     * @param expectedIsolateMode
     * @return
     * @throws RdsException
     * @throws ApiException
     */
    public Boolean updateCpuIsolateMode(String requestId, ReplicaSet replicaSet, String expectedIsolateMode)
        throws RdsException, ApiException {
        // 这个接口调度器和Common已经下掉，这里跟着下线
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
    }
}
