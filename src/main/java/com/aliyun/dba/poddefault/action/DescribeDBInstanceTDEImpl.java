package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeDBInstanceTDEImpl")
public class DescribeDBInstanceTDEImpl implements IAction {
    @Autowired
    private MysqlParamSupport mysqlParamSupport;

    @Autowired
    private CustinsParamService custinsParamService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        String requestId = RequestSession.getRequestId();
        try {
            custins = mysqlParamSupport.getCustInstance(actionParams);
            boolean tdeEnabled = false;
            String tdeEncryptionMode = "";
            String tdeEncryptionKey = "";
            CustinsParamDO paramTdeEnabled = custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.TDE_ENABLED);
            if (paramTdeEnabled != null && "1".equals(paramTdeEnabled.getValue())) {
                tdeEnabled = true;
            }
            CustinsParamDO paramTdeMode = custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.TDE_MODE);
            if (paramTdeMode != null && StringUtils.isNotBlank(paramTdeMode.getValue())) {
                tdeEncryptionMode = paramTdeMode.getValue();
            }
            CustinsParamDO paramTdeKmsKeyId = custinsParamService.getCustinsParam(custins.getId(), PodDefaultConstants.TDE_ENCRYPTION_KEY_ID);
            if (paramTdeKmsKeyId != null && StringUtils.isNotBlank(paramTdeKmsKeyId.getValue())) {
                tdeEncryptionKey = paramTdeKmsKeyId.getValue();
            }
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TDEStatus", tdeEnabled ? 1 : 0);
            data.put("TDEMode", tdeEncryptionMode);
            data.put("EncryptionKey", tdeEncryptionKey);
            return data;
        } catch (RdsException re) {
            log.error(requestId + " RdsException: ", re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            log.error("DescribeDBInstanceTDEImpl error, requestId:" + requestId + ", error:" + e);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
