package com.aliyun.dba.poddefault.action.support;

import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.task.dataobject.TransListDO;
import lombok.Builder;
import lombok.Data;

@Data
public class AllocateTmpResourceResult {
    boolean allocated;
    TransListDO transList;
    ReplicaSetResourceRequest resourceRequest;
    ReplicaSet replicaSet;
}
