package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.BackupSetParam;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.poddefault.action.service.*;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.poddefault.action.support.modules.MysqlReplicaResourceRequest;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.base.support.MySQLParamConstants.ACCESS_ID_RESOURCE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_BACK;
import static com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper.*;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;
import static com.aliyun.dba.support.property.ParamConstants.IO_ACCELERATION_ENABLED;
import static com.aliyun.dba.support.property.ParamConstants.IO_ACCELERATION_ENABLED_ON;


/**
 * 包括整个备份集恢复到新实例，库表恢复到新实例
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCloneDBInstanceImpl")
public class CloneDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CloneDBInstanceImpl.class);
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private PodAvzSupport avzSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    protected CustinsService custinsService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private BakService bakService;
    @Resource
    private PodIpWhiteListService podIpWhiteListService;
    @Resource
    private PodAccountService podAccountService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    protected DTZSupport dtzSupport;
    @Resource
    private PodDateTimeUtils podDateTimeUtils;
    @Resource
    private BackupService backupService;
    @Resource
    private KmsService kmsService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private ColdDataService coldDataService;
    @Resource
    private MysqlEncryptionService mysqlEncryptionService;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    protected CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Resource
    private CrossArchService crossArchService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String accessId = getParameterValue(params, ParamConstants.ACCESSID);
        boolean isSuccess = false;
        try {
            Integer userId = mysqlParameterHelper.getAndCreateUserId();
            mysqlParameterHelper.checkUserOperatorCluster(userId);
            String sourceReplicaSetName = mysqlParamSupport.getAndCheckSourceDBInstanceName(params);
            ReplicaSet sourceReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, sourceReplicaSetName, true);
            String restoreType = getParameterValue(params, ParamConstants.RESTORE_TYPE);
            String orderId = getParameterValue(params, ParamConstants.ORDERID);
            String regionId = getParameterValue(params, ParamConstants.REGION_ID);
            String targetStorageType = getParameterValue(params, ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            String restoreUsage = getParameterValue(params, MySQLParamConstants.RESTORE_USAGE);  //数据恢复的用途标识
            boolean isDataApplicationIns = PodDefaultConstants.DATA_RESTORE_USAGE.contains(restoreUsage);  //是否为数据应用型实例
            boolean isRestoreByTime = RESTORE_TYPE_TIME.equals(restoreType);
            if (sourceReplicaSet == null) {
                throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
            }
            String connType = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CONN_TYPE,
                    this.getConnType(sourceReplicaSet.getConnType(), sourceReplicaSet.getCategory()));
            // for serverless clone to provision, force set connType to lvs
            if (!PodCommonSupport.isAccessFromDBS(accessId) &&
                    ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(sourceReplicaSet.getCategory())) {
                connType = ReplicaSet.ConnTypeEnum.LVS.toString();
            }

            if (!StringUtils.equalsAnyIgnoreCase(connType, ReplicaSet.ConnTypeEnum.LVS.toString(), ReplicaSet.ConnTypeEnum.PHYSICAL.toString())) {
                // connType只支持lvs和Physical两种链路
                throw new RdsException(ErrorCode.INVALID_CONN_TYPE);
            }

            String vpcId = null;
            String vswitchId = null;
            String connectionString = null;
            String ipAddress = null;
            String vpcInstanceId = null;
            if (!StringUtils.equalsIgnoreCase(connType, ReplicaSet.ConnTypeEnum.PHYSICAL.toString())) {
                vpcId = CheckUtils.checkValidForVPCId(getParameterValue(params, ParamConstants.VPC_ID));
                vswitchId = CheckUtils.checkValidForVswitchId(getParameterValue(params, ParamConstants.VSWITCH_ID));
                connectionString = CheckUtils
                        .checkValidForConnAddrCust(getParameterValue(params, ParamConstants.CONNECTION_STRING));
                ipAddress = mysqlParamSupport.getParameterValue(params, ParamConstants.IP_ADDRESS);
                vpcInstanceId = mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);
            }


            Map<String, String> srcReplicaSetLabels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, sourceReplicaSetName);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, sourceReplicaSet.getUserId(), null);
            String bid = user.getBid();
            String uid = user.getAliUid();
            boolean isXDB = replicaSetService.isReplicaSetXDB(requestId, sourceReplicaSet.getName());
            CustInstanceDO srcCustins = mysqlParameterHelper.getAndCheckSourceCustInstance();

            BakhistoryDO bakHistory = null;
            Date restoreTimeUTC = null;
            boolean isCloneFromOldArch = !Objects.equals(srcCustins.getKindCode(), KIND_CODE_NEW_ARCH);
            Integer diskSize = CheckUtils.parseInt(
                    mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE),
                    5, 102400, ErrorCode.INVALID_STORAGE);
            if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                Long bakId = CheckUtils.parseLong(getParameterValue(params, ParamConstants.BACKUP_SET_ID), null,
                        null, ErrorCode.BACKUPSET_NOT_FOUND);
                bakHistory = bakService.getBakhistoryByBackupSetId(sourceReplicaSet.getId().intValue(), bakId);
            } else if (isRestoreByTime) {
                if(isCloneFromOldArch){
                    restoreTimeUTC = crossArchService.validRestoreByTime(srcCustins);
                    bakHistory = mysqlParameterHelper.validCloneStorageForEcs(srcCustins, diskSize * 1024L, restoreType, restoreTimeUTC);
                }else {
                    restoreTimeUTC = podParameterHelper.getAndCheckRestoreTime(requestId, srcCustins);
                    bakHistory = podParameterHelper.getBakhistoryByRecoverTime(srcCustins.getId(), restoreTimeUTC);
                }
            }
            if (bakHistory == null) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
            // 拦截实例大版本升级后按时间点回退老版本
            if (!StringUtils.equals(bakHistory.getDbVersion(), sourceReplicaSet.getServiceVersion()) && RESTORE_TYPE_TIME.equals(restoreType)) {
                logger.info("source instance db_version is {}, target backup set is {}, versions are inconsistent.", sourceReplicaSet.getServiceVersion(), bakHistory.getDbVersion());
                throw new RdsException(ErrorCode.INCORRECT_DBINSTANCE_ENGINE);
            }

            boolean isPengineBackupSet = !bakHistory.getKindCode().equals(KIND_CODE_NEW_ARCH);
            GetBackupSetResponse backupSet;
            try {
                backupSet = backupService.getBackupSet(
                        BackupSetParam.builder()
                                .uid(uid)
                                .user_id(bid)
                                .dBInstanceId(srcCustins.getId())
                                .backupSetId(Long.valueOf(bakHistory.getHisId())).build());
            } catch (BaseServiceException e) {
                if (StringUtils.equalsIgnoreCase(e.getCode(), "InvalidBackupSetID.NotFound")) {
                    throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
                }
                throw e;
            }
            String resourceGroupId = getParameterValue(params, ParamConstants.RESOURCE_GROUP_ID, "").trim();
            Long realBacksetSize = bakHistory.getBaksetSize();
            String compressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, sourceReplicaSetName, null);
            Double compressionRatio = cloudDiskCompressionHelper.getCompressionRatio(requestId, sourceReplicaSetName, null, null, null);
            Integer diskSizeBeforeCompression = diskSize;
            if (Objects.nonNull(diskSize) && CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
                diskSize = CloudDiskCompressionHelper.getLogicalSize(diskSize, compressionRatio);
            }

            if (diskSize != null && Long.valueOf(diskSize) * CustinsSupport.GB_TO_KB < realBacksetSize) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
            }

            String snapshotId = null;
            String performanceLevel = null;
            String diskType = StringUtils.isEmpty(targetStorageType) ? replicaSetService.getReplicaSetStorageType(sourceReplicaSetName, requestId) : targetStorageType;

            if (ReplicaSetService.isStorageTypeCloudDisk(PodParameterHelper.transferDiskTypeParam(diskType))) {
                snapshotId = backupSet.getSlaveStatusObj().getSnapshotId();
                if (StringUtils.isBlank(snapshotId)) {
                    logger.error("cannot find snapshotId from bak_history");
                    return createErrorResponse(ErrorCode.INVALID_BAKSET);
                }
                if (StringUtils.isEmpty(targetStorageType)) {
                    // 没有设置目标存储类型，取原实例的数据盘的CloudESSD性能等级
                    performanceLevel = replicaSetService.getVolumePerfLevel(requestId, sourceReplicaSetName, diskType);
                } else {
                    // 设置了目标存储类型
                    PodParameterHelper.checkCloudEssdStorageValid(diskType, diskSize);
                    performanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(diskType);
                    diskType = PodParameterHelper.transferDiskTypeParam(diskType);
                }
            }
            this.lockBakResource(srcCustins, bakHistory, restoreTimeUTC);


            String bizType = sourceReplicaSet.getBizType().toString();
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);

            // 设置实例描述
            String comment = null;
            if (CustinsParamSupport.hasParameterValue(params, ParamConstants.DB_INSTANCE_DESCRIPTION)) {
                comment = CheckUtils.checkLength(SupportUtils.decode(getParameterValue(params, ParamConstants.DB_INSTANCE_DESCRIPTION)),
                        1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
            }
            String dbType = sourceReplicaSet.getService();
            String dbVersion = bakHistory.getDbVersion();
            String portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), dbType);
            IPWhiteList ipWhiteList = podParameterHelper.getAndCheckReplicaSetIpWhiteList();  //支持传入自定义的白名单
            String clusterName = mysqlParamSupport.getParameterValue(params, ParamConstants.CLUSTER_NAME);
            String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
            InstanceLevel targetInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, null);

            // General cloud disk initialization
            // If the incoming disk type is not auto,
            // IO acceleration will no longer be initialized to prevent problems with dts migration.
            GeneralCloudDisk generalCloudDisk = new GeneralCloudDisk();
            if (DockerOnEcsConstants.ECS_CLOUD_AUTO.equalsIgnoreCase(diskType)) {
                generalCloudDisk = podCommonSupport.setGeneralCloudDiskConfig(requestId, params, dbVersion, targetInstanceLevel, diskType, sourceReplicaSet, dbInstanceName);
            } else {
                generalCloudDisk.warmDataDisk(new WarmDataDisk().ioAccelerationEnabled(false));
            }

            // 写优化克隆实例直接继承源实例
            String optimizedWritesInfo = podCommonSupport.getOptimizedWritesInfo(requestId, dbVersion, diskType, sourceReplicaSet);
            boolean isInitOptimizedWrites = podCommonSupport.isInitOptimizedWrites(optimizedWritesInfo);
            if (StringUtils.isNotBlank(dbInstanceName) && dbInstanceName.startsWith("dbs-")) {
                // dbs- 这种实例默认关闭IO加速
                generalCloudDisk.warmDataDisk(new WarmDataDisk().ioAccelerationEnabled(false));
            }

            if (isDataApplicationIns && !MysqlParamSupport.isSingleNode(targetInstanceLevel)) {
                //数据应用型实例只能创建单节点的
                throw new RdsException(ErrorCode.UNSUPPORTED_CLASS_CODE);
            }
            String dbEngine = isXDB ? "XDB" : "MySQL";
            podParameterHelper.resetDispenseMode(params, targetInstanceLevel, dbEngine, false);
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            Replica.RoleEnum[] nodeRoles = PodCommonSupport.getRoles(dbEngine, targetInstanceLevel, false, avzInfo);


            String insTypeDesc = ReplicaSet.InsTypeEnum.MAIN.toString();
            // 检验实例实例已经存在
            if (dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, true) != null) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }
            InstanceLevel srcInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, sourceReplicaSet.getService(), sourceReplicaSet.getServiceVersion(), sourceReplicaSet.getClassCode(), null);
            if (!Objects.equals(targetInstanceLevel.getHostType(), srcInstanceLevel.getHostType())) {
                // 新架构不支持不同hostType的规格之间克隆
                throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL);
            }
            // 备份相关
            Integer bakRetention = CheckUtils.parseInt(getParameterValue(params, ParamConstants.BACKUP_RETENTION, 7),
                    1, 730,
                    ErrorCode.INVALID_BACKUPRETENTIONPERIOD);
            String preferredBackupTime = getParameterValue(params, "PreferredBackupTime");
            if (!DateSupport.isminuteonly_utc(preferredBackupTime)) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PREFERREDBACKUPTIME);
            }
            String preferredBackupPeriod = CheckUtils.checkValidForBackupPeriod(getParameterValue(params, "preferredbackupperiod"));
            String enableBackup = getParameterValue(params, "EnableBackup");
            String enableBackupLog = getParameterValue(params, ParamConstants.ENABLE_BACKUP_LOG);

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(sourceReplicaSet, podParameterHelper.getParameterValue(ParamConstants.UID))) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_KMS_KEY);
            }

            // check compressionMode support
            if (CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
                cloudDiskCompressionHelper.checkCompressionSupportLimit(requestId, targetInstanceLevel, dbEngine, uid, avzInfo.getRegionId(), Optional.ofNullable(diskSizeBeforeCompression).map(Integer::longValue).orElse(null), diskType, true);
            }
            /*
             * autopl config parameter，provisionedIops和burstingEnabled
             */
            String paramProvisionedIops = getParameterValue(params, ParamConstants.AUTOPL_PROVISIONED_IOPS);
            String paramBurstingEnabled = getParameterValue(params, ParamConstants.AUTOPL_BURSTING_ENABLED);
            Long provisionedIops  = replicaSetService.getAutoConfigProvisionedIops(requestId, sourceReplicaSetName, diskType, paramProvisionedIops, diskSize, uid);
            Boolean burstingEnabled  = replicaSetService.getAutoConfigBurstingEnabled(requestId, sourceReplicaSetName, diskType, paramBurstingEnabled);
            // Buid replicaSet resource
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setUserId(bid);
            replicaSetResourceRequest.setUid(uid);
            replicaSetResourceRequest.setPort(portStr);
            replicaSetResourceRequest.setInsType(insTypeDesc);
            replicaSetResourceRequest.setReplicaSetName(dbInstanceName);
            replicaSetResourceRequest.setDomainPrefix(connectionString);
            replicaSetResourceRequest.setDbType(dbType);
            replicaSetResourceRequest.setDbVersion(dbVersion);
            replicaSetResourceRequest.setConnType(connType);
            replicaSetResourceRequest.setBizType(bizType);
            replicaSetResourceRequest.setClassCode(classCode);
            replicaSetResourceRequest.setStorageType(diskType);
            replicaSetResourceRequest.setDiskSize(diskSize);
            replicaSetResourceRequest.setVpcId(vpcId);
            replicaSetResourceRequest.setVswitchID(vswitchId);
            replicaSetResourceRequest.setCloudInstanceIp(ipAddress);
            replicaSetResourceRequest.setVpcInstanceId(vpcInstanceId);
            replicaSetResourceRequest.setSubDomain(avzInfo.getRegion());
            replicaSetResourceRequest.setRegionId(avzInfo.getRegionId());
            replicaSetResourceRequest.setVpcInstanceId(vpcInstanceId);
            replicaSetResourceRequest.setCloudInstanceIp(ipAddress);
            replicaSetResourceRequest.setBurstingEnabled(burstingEnabled);
            replicaSetResourceRequest.setProvisionedIops(provisionedIops);
            replicaSetResourceRequest.setComment(comment);

            // build labels
            replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.LABEL_ORDER_ID, orderId);
            replicaSetResourceRequest.putLabelsItem(ACCESS_ID_RESOURCE, accessId);

            boolean isArchChange = PodCommonSupport.isArchChange(srcInstanceLevel, targetInstanceLevel);
            Boolean isDhg = mysqlParamSupport.isDHGCluster(clusterName);
            boolean isArm = PodCommonSupport.isArm(targetInstanceLevel);
            String serviceSpecTag = null;
            if (isArchChange) {
                String sourceMinorVersion = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, sourceReplicaSet.getName(), "minor_version");
                serviceSpecTag = minorVersionServiceHelper.tryGetServiceSpecTag(sourceMinorVersion,
                        sourceReplicaSet.getBizType(),
                        dbType,
                        dbVersion,
                        dbEngine,
                        KIND_CODE_NEW_ARCH,
                        targetInstanceLevel,
                        diskType,
                        isDhg,
                        isArm,
                        null);  //cpu架构发生变化，如果原内核版本找不到，尝试获取最新的内核版本
            } else if(!StringUtils.equals(bakHistory.getDbVersion(), sourceReplicaSet.getServiceVersion())){
                String minorVersion = null;
                try{
                    JSONObject slaveStatus = JSONObject.parseObject(bakHistory.getSlaveStatus());
                    if(slaveStatus.containsKey("MINOR_VERSION")){
                        minorVersion = (String) slaveStatus.get("MINOR_VERSION");
                    }
                }catch (Exception ignored){
                    logger.info("failed to parse minor version from bakset. Exception:{}",ignored);
                }
                if(minorVersion==null){
                    minorVersion = minorVersionServiceHelper.tryGetLatestMinorVersion(dbType, dbVersion, targetInstanceLevel.getCategory().getValue(), KIND_CODE_NEW_ARCH, minorVersionServiceHelper.getAndCheckMinorVersionTag(srcCustins));
                }
                serviceSpecTag = minorVersionServiceHelper.tryGetServiceSpecTag(minorVersion,
                        sourceReplicaSet.getBizType(),
                        dbType,
                        dbVersion,
                        dbEngine,
                        KIND_CODE_NEW_ARCH,
                        targetInstanceLevel,
                        diskType,
                        isDhg,
                        isArm,
                        null);  //版本升级后备份恢复逻辑
            }else if(isCloneFromOldArch){
                serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                        null,
                        sourceReplicaSet.getBizType(),
                        dbType,
                        dbVersion,
                        dbEngine,
                        KIND_CODE_NEW_ARCH,
                        targetInstanceLevel,
                        diskType,
                        false,
                        false,
                        false,
                        null);
            }else{
                serviceSpecTag = minorVersionServiceHelper
                        .getServiceSpecTagByReplicaSetName(requestId, sourceReplicaSetName);
            }
            if (StringUtils.isEmpty(serviceSpecTag)) {
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }
            replicaSetResourceRequest.setComposeTag(serviceSpecTag);

            // IO加速小版本检验
            if (podCommonSupport.isIoAccelerationEnabled(generalCloudDisk)) {
                podCommonSupport.checkIoAccelerationSupportedMinorVersion(serviceSpecTag);
            }

            if (StringUtils.isNotBlank(clusterName) && isDhg) {
                replicaSetResourceRequest.setDedicatedHostGroupId(clusterName);
            }
            if (srcReplicaSetLabels.containsKey(PodDefaultConstants.ENCRYPTION_KEY_LABEL)) {
                replicaSetResourceRequest.setEncryptionKey(srcReplicaSetLabels.get(PodDefaultConstants.ENCRYPTION_KEY_LABEL));
                replicaSetResourceRequest.setEncryptionType(srcReplicaSetLabels.get(PodDefaultConstants.ENCRYPTION_TYPE_LABEL));
            }
            if (isArm) {
                replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.ARM);
            }

            List<ReplicaResourceRequest> replicas = new ArrayList<>();
            boolean isSingleTenant = replicaSetService.isCloudSingleTenant(sourceReplicaSet.getBizType(), diskType, targetInstanceLevel, isDhg);

            // 多租户场景不允许创建使用用户密钥的云盘加密实例。
            if (!(StringUtils.isNotBlank(dbInstanceName) && dbInstanceName.startsWith("dbs-"))) {
                mysqlEncryptionService.checkEncryptionKeyByReplicaSet(requestId, sourceReplicaSet, isSingleTenant);
            }
            PodType podType = PodType.POD_RUNC;
            if (rundPodSupport.isRundReplicaSet(sourceReplicaSet) && rundPodSupport.routeToRund(serviceSpecTag, isInitOptimizedWrites)) {
                podType = rundPodSupport.getPodTypeByGrayConfig(avzInfo.getRegionId(), uid, targetInstanceLevel, avzInfo);
            }
            boolean isVbm = podType == PodType.POD_VBM_RUND;

            Pair<String, ScheduleTemplate> scheduleTemplatePair;
            if (PodCommonSupport.isAccessFromDBS(accessId)) {
                // 来源于DBS的需要指定到DBS的资源池中
                scheduleTemplatePair = podTemplateHelper.getBizSysScheduleTemplateByName(requestId, PodDefaultConstants.RS_TEMPLATE_NAME_SYS_DBS);
            } else if (podCommonSupport.isLocalCacheWithIoAccelerationEnabled(generalCloudDisk)) {
                scheduleTemplatePair = podTemplateHelper
                        .getBizSysScheduleTemplate(
                                PodType.POD_RUNC,
                                sourceReplicaSet.getBizType(),
                                dbEngine,
                                targetInstanceLevel,
                                isSingleTenant,
                                insTypeDesc,
                                dbInstanceName,
                                null,
                                mysqlParamSupport.getUID(params));
            } else {
                scheduleTemplatePair = podTemplateHelper
                        .getBizSysScheduleTemplate(
                                podType,
                                sourceReplicaSet.getBizType(),
                                dbEngine,
                                targetInstanceLevel,
                                isSingleTenant,
                                insTypeDesc,
                                dbInstanceName, null, mysqlParamSupport.getUID(params));
            }
            replicaSetResourceRequest.setScheduleTemplate(scheduleTemplatePair.getValue());
            String rsTemplateName = scheduleTemplatePair.getKey();

            Map<Replica.RoleEnum, String> roleHostNameMapping = podParameterHelper.getRoleHostNameMapping();

            MysqlReplicaResourceRequest mysqlReplicaResourceRequest = MysqlReplicaResourceRequest.builder()
                    .requestId(requestId)
                    .nodeRoles(nodeRoles)
                    .roleHostNameMapping(roleHostNameMapping)
                    .avzInfo(avzInfo)
                    .instanceLevel(targetInstanceLevel)
                    .isSingleTenant(isSingleTenant)
                    .dbType(dbType)
                    .dbEngine(dbEngine)
                    .dbVersion(dbVersion)
                    .diskType(diskType)
                    .diskSize(diskSize)
                    .vswitchId(vswitchId)
                    .performanceLevel(performanceLevel)
                    .snapshotId(snapshotId)
                    .isRunD(PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType()))
                    .build();
            replicas = podReplicaSetResourceHelper.getReplicaResourceRequestList(mysqlReplicaResourceRequest, avzInfo);


            if (MysqlParamSupport.isCluster(targetInstanceLevel.getCategory().toString())
                    && replicaSetService.isMgr(requestId, srcCustins.getInsName())) {
                replicaSetService.isSupportMgr(replicas.size(), targetInstanceLevel, serviceSpecTag);
            }

            replicaSetResourceRequest.setSingleTenant(isSingleTenant);

            // 专属集群和集团使用eni
            if (isDhg || PodParameterHelper.isAliGroup(bizType)) {
                replicaSetResourceRequest.setEniDirectLink(true);
            } else {
                replicaSetResourceRequest.setEniDirectLink(false);
            }

            replicaSetResourceRequest.setDiskSize(diskSize);  //用户可见的磁盘空间
            replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
            replicaSetResourceRequest.ignoreCreateVpcMapping(true); //反向VPC的资源申请下沉到任务流
            replicaSetResourceRequest.setInitOptimizedWrites(isInitOptimizedWrites);


            podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
            String paramColdDataEnabled = getParameterValue(params, ParamConstants.RDS_COLD_DATA_ENABLED);
            Boolean coldDataEnabled = replicaSetService.getColdDataEnabled(requestId,sourceReplicaSetName,paramColdDataEnabled );
            Boolean isRestoreColdData = true;
            ColdDataDisk coldDataDisk = coldDataService.getColdDataDiskFromBackupSetId(requestId,coldDataEnabled,avzInfo.getRegionId(), isRestoreColdData, sourceReplicaSetName, String.valueOf(backupSet.getBackupSetId()), user.getBid(), user.getAliUid(),false);
            if (podCommonSupport.isColdDataEnabled(coldDataDisk)) {
                podCommonSupport.checkColdDataSupportLimit(requestId, targetInstanceLevel, dbEngine, dbVersion, diskType, serviceSpecTag, null, user.getAliUid(), avzInfo.getRegionId());
            }
            generalCloudDisk.setColdDataDisk(coldDataDisk);
            replicaSetResourceRequest.setGeneralCloudDisk(generalCloudDisk);  //通用云盘参数
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            }

            // 申请资源并写入核心元数据
            boolean isAllocate = false;
            try {
                isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, dbInstanceName, replicaSetResourceRequest);

                ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, null);

                // 集群版实例资源配置
                if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                    podReplicaSetResourceHelper.configResource4Cluster(requestId, replicaSet);
                }

                // 更新主可用区参数
                custinsParamService.updateAVZInfo(replicaSet.getId().intValue(), avzInfo);
                //账户和db同步
                podAccountService.syncReplicaSetSuperAccount(requestId, sourceReplicaSetName, replicaSet.getName());
                //白名单数据同步
                if (ipWhiteList != null) {
                    //如果传入了指定白名单，则只用用户设置的
                    dBaasMetaService.getDefaultClient().createReplicaSetWhiteIps(requestId, dbInstanceName, ipWhiteList);
                } else {
                    podIpWhiteListService.syncReplicaSetIpWhiteList(requestId, sourceReplicaSetName, replicaSet.getName());
                }

                Map<String, String> compressionLabels = new HashMap<>();
                if (CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
                    compressionLabels.put(CUSTINS_PARAM_COMPRESSION_MODE, compressionMode);
                    compressionLabels.put(CUSTINS_PARAM_COMPRESSION_RATIO, String.valueOf(compressionRatio));
                    compressionLabels.put(CUSTINS_PARAM_DISK_SIZE_BEFORE_COMPRESSION, String.valueOf(CloudDiskCompressionHelper.convertDiskSizeGBToMB(diskSizeBeforeCompression)));
                }


                // 克隆实例label配置
                setReplicaSetLabels(requestId, replicaSet, sourceReplicaSet, srcReplicaSetLabels, dbEngine, rsTemplateName, resourceGroupId, isDataApplicationIns, isArchChange, generalCloudDisk, isArm, optimizedWritesInfo, compressionLabels, isVbm);

                String storageAutoScale = podParameterHelper.getParameterValue(DockerOnEcsConstants.STORAGE_AUTO_SCALE);
                String storageUpperBound = podParameterHelper.getParameterValue(DockerOnEcsConstants.STORAGE_UPPER_BOUND);
                String storageThreshold = podParameterHelper.getParameterValue(DockerOnEcsConstants.STORAGE_THRESHOLD);

                // Dispatch task
                String domain = PodCommonSupport.getTaskDomain(sourceReplicaSet.getBizType());
                domain = StringUtils.equalsIgnoreCase(PodDefaultConstants.DOMAIN_XDB, domain) ? domain : sourceReplicaSet.getService();
                String taskKey = null;
                if (isDataApplicationIns) {
                    taskKey = PodDefaultConstants.TASK_RESTORE_DBS_INS;
                } else {
                    taskKey = getTaskKey(replicaSet, isXDB);
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("requestId", requestId);
                jsonObject.put("replicaSetName", dbInstanceName);
                jsonObject.put("sourceReplicaSetName", sourceReplicaSetName);
                // 备份信息
                if (StringUtils.isNotBlank(enableBackup)) {
                    jsonObject.put("enableBackup", enableBackup);
                }
                if (StringUtils.isNotBlank(enableBackupLog)) {
                    jsonObject.put("enableBackupLog", enableBackupLog);
                }
                // 备份保留时间, 比如 7 天
                jsonObject.put("bakRetention", bakRetention);
                // 每周哪几天备份, 比如 0101010
                jsonObject.put("preferredBackupPeriod", preferredBackupPeriod);
                // 备份时间, UTC, 比如 06:35Z
                jsonObject.put("preferredBackupTime", preferredBackupTime);

                jsonObject.put("restoreType", restoreType);
                jsonObject.put("bakHisID", bakHistory.getHisId());
                jsonObject.put("backupSetHostId", bakHistory.getHostinsId());

                // UTC时间用于和各个备份服务进行交互
                jsonObject.put("backupSetStartTime", podDateTimeUtils.convert2UTCStr(bakHistory.getBakBegin()));
                jsonObject.put("restoreTime", podDateTimeUtils.convert2UTCStr(restoreTimeUTC));

                jsonObject.put("startBinlogFile", backupSet.getSlaveStatusObj().getBinLogFile());
                jsonObject.put("isPengineBackupSet", isPengineBackupSet);

                // DAS配置
                if (StringUtils.isNotBlank(storageAutoScale)) {
                    jsonObject.put("storageAutoScale", storageAutoScale);
                    jsonObject.put("storageUpperBound", storageUpperBound);
                    jsonObject.put("storageThreshold", storageThreshold);
                }
                if (isCloneFromOldArch) {
                    jsonObject.put("isPengineBackupSet", isCloneFromOldArch);
                    jsonObject.put("backupKindCode", srcCustins.getKindCode());
                }

                boolean needUpdateSrcStatus = isRestoreByTime &&    //按时间点恢复需要订正原实例状态
                        !ReplicaSet.StatusEnum.STOPPED.equals(sourceReplicaSet.getStatus()) &&    //非停机状态克隆不需要订正原实例
                        !isDataApplicationIns; //数据应用型实例不需要订正原实例

                if (!needUpdateSrcStatus) {
                    jsonObject.put("skipUpdateSrcStatus", "true");
                }

                jsonObject.put("autoCreateProxy", podParameterHelper.getAutoCreateProxy(targetInstanceLevel));

                String parameter = jsonObject.toJSONString();
                Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);

                if (needUpdateSrcStatus) {
                    custinsService.updateCustInstanceStatusByCustinsId(
                            sourceReplicaSet.getId().intValue(), CUSTINS_STATUS_BACK, CustinsState.STATE_INS_CLONING.getComment());
                }

                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", taskId);
                data.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
                data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
                isSuccess = true;
                return data;

            } catch (Exception e) {
                logger.error(e);
                isAllocate = false;
                if (e instanceof ApiException) {
                    return CommonProviderExceptionUtils.resourceWrapper(requestId, (ApiException)e);
                }
                logger.error(requestId + " Exception: {}", e.getMessage());
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            } finally {
                if (!isAllocate) {
                    //分配失败的情况下，要调用释放资源接口
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, dbInstanceName);
                    } catch (ApiException e) {
                        //ignore
                        logger.error(requestId + " release ReplicaSet Exception: {}", e.getResponseBody());
                    }
                }
            }

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isSuccess) {
                this.unLockBakResource();
            }
        }

    }


    /**
     * 锁定备份资源，避免克隆过程中被清理
     */
    public void lockBakResource(CustInstanceDO custInstance, BakhistoryDO bakHistory, Date restoreTimeUTC) {
        if (restoreTimeUTC != null) {
            Date bakTime = dtzSupport.getSpecificTimeZoneDate(new DateTime(restoreTimeUTC), DATA_SOURCE_BAK);
            bakService.lockBinlogForRestore(custInstance.getId(), bakHistory.getBakBegin(), bakTime); // lock binlog for restore
            ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                    JSON.toJSONString(ImmutableMap.of(
                            "custinsId", custInstance.getId().toString(),
                            "begin", bakHistory.getBakBegin().getTime(),
                            "end", bakTime.getTime())));
        }

        bakService.lockBakHisForRestore(bakHistory.getHisId());
        ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakHistory.getHisId()));
    }

    /**
     * API流程异常时，解锁备份资源
     */
    public void unLockBakResource() {
        if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")) {
            JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
            bakService.unlockBinlogForRestore(
                    Integer.valueOf(lockBinlog.get("custinsId").toString()),
                    new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                    new Date(Long.parseLong(lockBinlog.get("end").toString()))
            );
        }
        if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
            String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
            bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));
        }
    }

    /**
     * 克隆实例label配置
     */
    public void setReplicaSetLabels(String requestId, ReplicaSet replicaSet,ReplicaSet sourceReplicaSet, Map<String, String> srcReplicaSetLabels,
                                    String dbEngine, String rsTemplateName, String resourceGroupId, boolean isDataApplicationIns, boolean isArchChange, GeneralCloudDisk generalCloudDisk, boolean isArm, String optimizedWritesInfo,
                                    Map<String, String> compressionLabels, boolean isVbm) throws com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        // 继承label
        List<String> labelsFromSrc = Arrays.asList(
                CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID,
                CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO,
                CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS,
                CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE,
                CustinsParamSupport.CUSTINS_PARAM_NAME_FREQUENCY
        );

        Map<String, String> labels = new HashMap<>();
        if (MapUtils.isNotEmpty(compressionLabels)) {
            labels.putAll(compressionLabels);
        }

        // 继承源实例label
        for (String name : labelsFromSrc) {
            if (StringUtils.isNotEmpty(srcReplicaSetLabels.getOrDefault(name, null))) {
                labels.put(name, srcReplicaSetLabels.get(name));
            }
        }

        // 写优化参数传递 OPTIMIZED_WRITES_INFO来源于他的源实例
        if (StringUtils.isNoneBlank(optimizedWritesInfo)) {
            labels.put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, optimizedWritesInfo);
        }

        labels.put(CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, resourceGroupId);

        if ("XDB".equals(dbEngine)) {
            //xdb 一定是强同步
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_SYNC);
        }

        String minorVersion = minorVersionServiceHelper.resetReplicaSetMinorVersion(requestId, replicaSet.getName());
        if (StringUtils.isNotBlank(minorVersion)) {
            labels.put("minor_version", minorVersion);
        }

        //VBM形态添加xdp注解使用
        if(isVbm){
            labels.put(VBM_CUSTINS_LABEL_KEY, VBM_CUSTINS_LABEL_VALUE);
        }

        // 云上业务需要开启审计日志
        if (PodParameterHelper.isAliYun(replicaSet.getBizType())) {
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
        }

        //指定实例资源调度模板的写入实例和模版名关联
        if (Strings.isNotBlank(rsTemplateName)) {
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME, rsTemplateName);
        }

        if (isDataApplicationIns) {
            labels.put(MySQLParamConstants.IS_INTERNAL, "1"); //标识内部实例，性能采集不会吐数据道云监控
        }

        if (podCommonSupport.isIoAccelerationEnabled(generalCloudDisk)) {
            labels.put(IO_ACCELERATION_ENABLED, IO_ACCELERATION_ENABLED_ON); //IO加速 开启时，需要传入此标识
        }

        if (isArchChange) {
            //open double write as x86 not support close double write, used for com.aliyun.app.activityprovider.base.param.param.BuildParamParameter.ArchChangedMatcher
            labels.put(PodDefaultConstants.PARAM_ARCH_CHANGED, "1");
        }

        // 克隆实例默认小版本自动升级，参考物理机实现
        labels.put(ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto");
        if(!sourceReplicaSet.getServiceVersion().equalsIgnoreCase(replicaSet.getServiceVersion())){
            labels.remove(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
        }

        dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSet.getName(), labels);
    }

    /**
     * 针对Serverless实例转换，修改网络连接
     */
    private String getConnType(ReplicaSet.ConnTypeEnum sourceConnType, String sourceCategory) {
        if (ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(sourceCategory.toLowerCase())) {
            return ReplicaSet.ConnTypeEnum.LVS.toString();
        }

        return sourceConnType.toString();
    }

    private String getTaskKey(ReplicaSet replicaSet, boolean isXDB) {
        String taskKey = isXDB ?
                PodDefaultConstants.TASK_CLONE_XDB_INS :
                MysqlParamSupport.isCluster(replicaSet.getCategory()) ?
                        PodDefaultConstants.TASK_CLONE_CLUSTER_INS : PodDefaultConstants.TASK_CLONE_INS;
        return taskKey;
    }

}
