package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultSwitchBlueGreenInstanceImpl")
public class SwitchBlueGreenInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.poddefault.action.SwitchBlueGreenInstanceImpl.class);
    @Resource
    private BlueGreenDeploymentService blueGreenDeploymentService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private PodParameterHelper podParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            logger.info("this is start.");
            String aliUid = mysqlParamSupport.getUID(params);
            String bid = mysqlParamSupport.getBID(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String deploymentName = params.get("BlueGreenDeploymentName".toLowerCase());
            String greenInstanceName = params.get("GreenDBInstanceId".toLowerCase());
            Map<String, Object> switchInfo = podParameterHelper.getSwitchInfo(params);
            logger.info("switchInfo: " + switchInfo);
            Map<String, Object> data = blueGreenDeploymentService.switchBlueGreenInstance(regionId,
                aliUid,
                bid,
                custins,
                greenInstanceName,
                deploymentName, switchInfo);
            logger.info("this is end.");
            return data;
        } catch (RdsException ex) {
            logger.error("SwitchBlueGreenInstanceImpl failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("SwitchBlueGreenInstanceImpl Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}