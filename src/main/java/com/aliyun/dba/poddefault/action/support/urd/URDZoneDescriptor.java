package com.aliyun.dba.poddefault.action.support.urd;

import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.Data;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class URDZoneDescriptor {
    private String region;
    private String regionId;
    private String zoneId;
    private Integer zoneWeight;
    private Map<Replica.RoleEnum, Integer> instances;

    public URDZoneDescriptor(String region, String regionId, String zoneId, Integer zoneWeight) {
        this.region = region;
        this.regionId = regionId;
        this.zoneId = zoneId;
        this.zoneWeight = zoneWeight;
        instances = new HashMap<>();
    }

    /**
     * 添加节点
     */
    public URDZoneDescriptor addInstance(Replica.RoleEnum roleEnum, Integer count) {
        if (instances.containsKey(roleEnum)) {
            instances.put(roleEnum, instances.get(roleEnum) + count);
            return this;
        }

        instances.put(roleEnum, count);
        return this;
    }

    /**
     * 从JSON输入中解析节点数量，格式如下
     * <p>
     * [{"region":"cn-zhangjiakou-a-aliyun",
     * "regionId":"cn-zhangjiakou",
     * "zoneId":"cn-zhangjiakou-a",
     * "zoneWeight":9,
     * "instances":{"master":1,"follower":2}}]
     */
    public static List<URDZoneDescriptor> parseFromJSON(String jsonStr) {
        Type type = new TypeToken<ArrayList<URDZoneDescriptor>>() {
        }.getType();
        return new Gson().fromJson(jsonStr, type);
    }

}
