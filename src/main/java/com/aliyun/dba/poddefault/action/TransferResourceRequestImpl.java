package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultTransferResourceRequestImpl")
public class TransferResourceRequestImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(TransferResourceRequestImpl.class);

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        EvaluateRegionResourceImpl evaluateRegionResource = SpringContextUtil.getBeanByClass(EvaluateRegionResourceImpl.class);
        params.put("transferrequest", "true");
        return evaluateRegionResource.doActionRequest(custins, params);
    }

}
