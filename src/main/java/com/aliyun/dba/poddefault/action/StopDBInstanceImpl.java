package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultStopDBInstanceImpl")
public class StopDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(StopDBInstanceImpl.class);

    @Autowired
    private DBaasMetaService dBaasMetaService;

    @Autowired
    private MysqlParamSupport mysqlParamSupport;

    @Autowired
    private ReplicaSetService replicaSetService;

    @Autowired
    protected WorkFlowService workFlowService;

    @Autowired
    private CustinsService custinsService;

    @Autowired
    private PodParameterHelper podParameterHelper;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");

        try {
            ReplicaSet replicaSetMeta;
            try {
                replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);

                ConfigListResult configs = dBaasMetaService.getDefaultClient().listConfigs(requestId, PodDefaultConstants.RES_KEY_STOP_DB_INSTANCE_SWITCH);
                if (configs.getItems() != null && !configs.getItems().isEmpty()) {
                    Config switchConfig = configs.getItems().get(0);
                    if (StringUtils.equalsIgnoreCase(switchConfig.getValue(), "off")) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "current region not supported yet");
                    }
                }

                if (!PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "biz type " + replicaSetMeta.getBizType() + " not supported yet!");
                }

                if (!(InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSetMeta.getCategory()) ||
                        InstanceLevel.CategoryEnum.STANDARD.toString().equals(replicaSetMeta.getCategory()))) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "db category " + replicaSetMeta.getCategory() + " not supported!");
                }

                if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "db type readonly not supported!");
                }

                // TODO: 集群版暂不支持启停操作，等支持后再放开
                if (MysqlParamSupport.isCluster(replicaSetMeta.getCategory())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "cluster is not supported yet!");
                }

                if (workFlowService.isTaskExist(requestId, replicaSetMeta.getName())) {
                    logger.error("{} replicaset {} has unfinished tasks.", requestId, replicaSetMeta.getName());
                    throw new RdsException(ErrorCode.TASK_HAS_EXIST);
                }
                // 只支持ssd
                Replica replica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                        requestId, replicaSetMeta.getName(), null, null, null, null).getItems().get(0);
                if (!Replica.StorageTypeEnum.CLOUD_ESSD.equals(replica.getStorageType())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "storage " + replica.getStorageType() + " not supported!");
                }

                // 暂不支持带只读、带proxy的实例
                List<ReplicaSet> readOnlyReplicaSetList = dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId,
                        replicaSetMeta.getName(), ReplicaSet.InsTypeEnum.READONLY.toString()).getItems();
                if (CollectionUtils.isNotEmpty(readOnlyReplicaSetList)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "current instance has read ins, not supported!");
                }

                if (custinsService.checkHaveMaxscaleService(replicaSetMeta.getId().intValue()) != null) {
                    //暂时不支持有maxscale
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "current instance has maxscale, not supported!");
                }

                if (podParameterHelper.isSingleTenant(replicaSetMeta)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "current instance is single tenant, not supported!");
                }

                // 释放计算资源
                boolean stopInsStage2 = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, "stopInsStage2", "false"));
                if (stopInsStage2) {
                    return stopInsStage2(replicaSetMeta, params);
                }

                if (ReplicaSet.LockModeEnum.NOLOCK != replicaSetMeta.getLockMode()) {
                    logger.error("db instance is lock.");
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
                }

                if (!(ReplicaSet.StatusEnum.ACTIVE.equals(replicaSetMeta.getStatus()) ||
                        ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSetMeta.getStatus()))) {
                    throw new RdsException(ErrorCode.INVALID_STATUS);
                }

            } catch (ApiException e) {
                logger.error("StopDBInstance api exception, requestId: {}, msg: {}", requestId, e.getMessage(), e);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }

            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", requestId);
            taskParamObject.put("replicaSetName", replicaSetMeta.getName());

            String taskKey = PodDefaultConstants.TASK_STOP_INS_STAGE_1;
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSetMeta.getName(), PodDefaultConstants.DOMAIN_MYSQL, taskKey, taskParamObject.toJSONString(), 0);

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetMeta.getName(), ReplicaSet.StatusEnum.STOPPING.toString());

            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", replicaSetMeta.getId());
            data.put("DBInstanceName", replicaSetMeta.getName());
            data.put("TaskId", taskId);

            return data;
        } catch (RdsException re) {
            logger.error("StopDBInstance rds exception, requestId: {}, msg: {}", requestId, re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("StopDBInstance exception, requestId: {}, msg: {}", requestId, ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    private Map<String, Object> stopInsStage2(ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");

        if (!(ReplicaSet.StatusEnum.STOPPED.equals(replicaSetMeta.getStatus()))) {
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }

        Map<String, Object> data = new HashMap<String, Object>(4);
        data.put("DBInstanceID", replicaSetMeta.getId());
        data.put("DBInstanceName", replicaSetMeta.getName());

        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSetMeta.getName(), null, null, null, null);

        Optional<Replica> activeReplicas = listReplicasInReplicaSet.getItems().stream().filter(x -> Replica.StatusEnum.ACTIVE.equals(x.getStatus())).findFirst();

        if (!activeReplicas.isPresent()) {
            // 可能是释放任务重复调度，直接返回成功
            logger.error("replicaSet {} has no active replica, return success, requestId: {}", replicaSetMeta.getName(), requestId);
            data.put("extra", "replica stopped already, skip");
        } else {
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", requestId);
            taskParamObject.put("replicaSetName", replicaSetMeta.getName());

            String taskKey = PodDefaultConstants.TASK_STOP_INS_STAGE_2;
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSetMeta.getName(), PodDefaultConstants.DOMAIN_MYSQL, taskKey, taskParamObject.toJSONString(), 0);
            data.put("TaskId", taskId);
        }

        // 删除定时释放任务
        dBaasMetaService.getDefaultClient().deleteReplicaSetLabel(requestId, replicaSetMeta.getName(), PodDefaultConstants.TASK_STOP_INS_STAGE_2);

        return data;
    }
}
