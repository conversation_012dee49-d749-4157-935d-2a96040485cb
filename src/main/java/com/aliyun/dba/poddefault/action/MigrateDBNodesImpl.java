package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.EvaluateEcsNodeService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointService;
import com.aliyun.dba.poddefault.action.service.endpoint.EndpointLvsService;
import com.aliyun.dba.poddefault.action.service.migrate.MigrateDBInstanceAvzService;
import com.aliyun.dba.poddefault.action.service.modify.AliyunModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.rdscustom.action.support.ECSApiHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.opensearch.sdk.dependencies.org.apache.commons.lang.StringEscapeUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultMigrateDBNodesImpl")
@Slf4j
public class MigrateDBNodesImpl implements IAction {

    protected static final LogAgent logger = LogFactory.getLogAgent(MigrateDBInstanceImpl.class);
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected KmsService kmsService;
    @Autowired
    protected CustinsService custinsService;
    @Resource
    protected PodTemplateHelper podTemplateHelper;
    @Resource
    protected PodParameterHelper podParameterHelper;
    @Resource
    protected CommonProviderService commonProviderService;
    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Autowired
    protected InstanceIDao instanceIDao;
    @Resource
    MigrateDBInstanceAvzService migrateDBInstanceAvzService;
    @Resource
    MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Autowired
    private InstanceService instanceService;
    @Resource
    private CustinsParamService custinsParamService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        logger.info("Action {}, params {}", "MigrateDBNodes", JSON.toJSONString(params));

        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        AllocateTmpResourceResult allocateResult = new AllocateTmpResourceResult();

        boolean isSuccess = false;

        try {
            PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(params);

            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);

            // 检查bizType是否为aliyun
            if (!PodParameterHelper.isAliYun(replicaSet.getBizType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "biz type " + replicaSet.getBizType() + " not supported yet!");
            }

            //todo:MGR暂不支持
            if (replicaSetService.isMgr(requestId, replicaSet.getName())) {
                logger.error("dbInstance is not normal cluster, MGR is not supported in node modify");
                throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NODE_MODIFY_FOR_MGR);
            }

            // 该方法只允许cluster迁移
            if (InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSet.getCategory())
                || InstanceLevel.CategoryEnum.STANDARD.toString().equals(replicaSet.getCategory())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "db category " + replicaSet.getCategory() + " not supported yet!");
            }

            if (!ReplicaSet.InsTypeEnum.MAIN.equals(replicaSet.getInsType())
                && !ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "db type " + replicaSet.getInsType() + " not supported yet!");
            }

            // 检查KMS的状态
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);
            String uid = user.getAliUid();
            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, uid)) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }

            // 获取原实例节点信息
            List<Replica> replicaList = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems();
            if (CollectionUtils.isEmpty(replicaList)) {
                throw new RdsException(ErrorCode.DBNAME_NOT_FOUND, "replica set " + replicaSet.getName() + " has no replicas");
            }
            Replica masterReplica = replicaList.stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get();

            // 校验迁移参数合法性
            // 获取传入参数
            String dbNodeString = mysqlParamSupport.getParameterValue(params, "DBNode");
            List<Map<String, String>> dbNodes = JSON.parseObject(dbNodeString, List.class);
            logger.info("dbNodes: {}", dbNodeString);

            if (Objects.isNull(dbNodes) || dbNodes.isEmpty()) {
                throw new RdsException(ErrorCode.NODE_NOT_FOUND, "DBNode is empty, please check input params");
            }

            // 检验传参 以及 判断主节点可用区是否修改
            Set<String> oriReplicaNameSet = replicaList.stream().map(Replica::getName).collect(Collectors.toSet());
            boolean isInsLevelMigration = false;
            Set<String> nodeIdsSet = new HashSet<>();
            for (Map<String, String> dbNode : dbNodes) {
                String nodeId = dbNode.get("nodeId");
                String zoneId = dbNode.get("zoneId");
//                String classCode = dbNode.get("classCode");
//                String vSwitchIdOfReplica = dbNode.get("vSwitchId");

                // 检查nodeId的合法性
                if (!oriReplicaNameSet.contains(nodeId)) {
                    logger.error("nodeId: {} is not found in replicaList: {}", nodeId, JSONObject.toJSONString(oriReplicaNameSet));
                    throw new RdsException(ErrorCode.INVALID_NODEITEMS_NODE_ID);
                }

                // 检查 nodeId 是否重复
                if (!nodeIdsSet.add(nodeId)) {
                    throw new RdsException(ErrorCode.INVALID_NODEITEMS_NODE_ID, "Duplicate nodeId found: " + nodeId);
                }

                // 检查zoneId传递的合法性
                if (zoneId == null) {
                    logger.error("zoneId: {} is invalid", zoneId);
                    throw new RdsException(ErrorCode.INVALID_AVZONE);
                }

                if (Objects.equals(masterReplica.getName(), dbNode.get("nodeId"))) {
                    // 检查主节点可用区是否进行了修改
                    if (!Objects.equals(zoneId, masterReplica.getZoneId())) {
                        logger.info("masterReplica: {} zoneId: {} is not equal to targetZoneId: {}", masterReplica.getName(), masterReplica.getZoneId(), zoneId);
                        isInsLevelMigration = true;
                    }
                }
            }

            // 检查vpc是否传递的合法性 目前实例级别的链路迁移需要验证 节点级别不验证
            String vSwitchId = mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID);
            if (isInsLevelMigration) {
                CheckUtils.checkValidForVswitchId(vSwitchId);
            }

            // 对于传递的dbNode参数进行预处理 以及 构造申请资源的参数
            List<Map<String, String>> dbNodesForResource = new ArrayList<>();
            if (isInsLevelMigration) {
                for (Replica replica : replicaList) {
                    Map<String, String> dbNodeForResource = new HashMap<>();
                    if (dbNodes.stream().anyMatch(x -> x.get("nodeId").equals(replica.getName()))) {
                        dbNodeForResource = dbNodes.stream().filter(x -> x.get("nodeId").equals(replica.getName())).findFirst().get();
                    } else {
                        dbNodeForResource.put("nodeId", replica.getName());
                        dbNodeForResource.put("zoneId", replica.getZoneId());
                    }
                    dbNodeForResource.put("classCode", replica.getClassCode());
                    dbNodeForResource.put("role", replica.getRole().getValue());
                    dbNodesForResource.add(dbNodeForResource);
                }
            } else {
                // 非主节点修改则下发节点迁移
                for (Map<String, String> dbNode : dbNodes) {
                    String nodeId = dbNode.get("nodeId");
                    String zoneId = dbNode.get("zoneId");
                    // 主节点的信息不要带入到申请资源的信息中
                    if (Objects.equals(masterReplica.getName(), nodeId)) {
                        log.info("Because master node not change zone,  dbNodesForResource need to delete master information. Master replica info {}, dbNode {}", masterReplica, JSONObject.toJSONString(dbNode));
                        continue;
                    }

                    Replica replica = replicaList.stream().filter(x -> x.getName().equals(dbNode.get("nodeId"))).findFirst().get();
                    if (Objects.equals(zoneId, replica.getZoneId())) {
                        log.info("Because zone not change,  dbNodesForResource need to delete this information. Replica info {}, dbNode {}", replica, JSONObject.toJSONString(dbNode));
                        continue;
                    }
                    dbNode.put("classCode", replica.getClassCode());
                    dbNode.put("role", replica.getRole().getValue());
                    dbNodesForResource.add(dbNode);
                }

                if (!dbNodesForResource.isEmpty()) {
                    dbNodesForResource.get(0).put("role", Replica.RoleEnum.MASTER.getValue());
                    log.info("Tmp ins need a master replica.");
                }
            }
            log.info("dbNodesForResource for allocate resource: {}", JSONObject.toJSONString(dbNodesForResource));

            if (dbNodesForResource.isEmpty()) {
                log.info("{} nothing has changed, return it.", modifyInsParam.getRequestId());
                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", replicaSet.getId());
                data.put("DBInstanceName", replicaSet.getName());
                data.put("dbNode", dbNodes);
                return data;
            }

            // 申请资源
            allocateResourceForMigrateCluster(requestId, replicaSet, modifyInsParam, allocateResult, dbNodesForResource, masterReplica, vSwitchId, isInsLevelMigration);
            ReplicaSet tmpReplicaSet = allocateResult.getReplicaSet();

            if (isInsLevelMigration) {
                migrateDBInstanceAvzService.checkEndPointList(requestId, replicaSet, tmpReplicaSet);
            }

            // 下发迁移任务
            Integer taskIdInt = addTransferTaskForCluster(requestId, replicaSet, tmpReplicaSet, modifyInsParam, dbNodesForResource, isInsLevelMigration);
            // 更新实例状态
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.TRANSING.toString());
            // 准备链路切换
            if (isInsLevelMigration) {
                migrateDBInstanceAvzService.addSwitchVipChangeLog(requestId, replicaSet, tmpReplicaSet, taskIdInt);
            }
            isSuccess = true;
            // build response
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TaskId", taskIdInt);
            data.put("isSuccess", isSuccess);
            data.put("dbNode", dbNodes);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getMessage(), e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (allocateResult.isAllocated() && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                        requestId, allocateResult.getResourceRequest().getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("resource resource for transfer failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    private void allocateResourceForMigrateCluster(String requestId, ReplicaSet replicaSet, PodModifyInsParam modifyInsParam, AllocateTmpResourceResult result, List<Map<String, String>> dbNodes, Replica masterReplica, String vSwitchId, boolean isInsLevelMigration) throws Exception {
        final boolean isArm = PodCommonSupport.isArm(modifyInsParam.getTargetInstanceLevel());
        boolean isAllocate;
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();

        // 申请资源
        CustInstanceDO custins = modifyInsParam.getCustins();

        String tmpReplicaSetName = String.format("tmp-%s-%s", modifyInsParam.getDbInstanceName(), System.currentTimeMillis() / 1000L);
        String connectionString = CheckUtils.checkValidForConnAddrCust(tmpReplicaSetName);
        Endpoint vpcEndpoint = replicaSetService.getReplicaSetVpcEndpoint(requestId, replicaSet.getName());
        String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId());
        if (StringUtils.isEmpty(serviceSpecTag)) {
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }

        AVZInfo targetAvzInfo = modifyInsParam.getAvzInfo();
        replicaSetResourceRequest.userId(modifyInsParam.getBid())
            .uid(modifyInsParam.getUid())
            .port(vpcEndpoint.getVport().toString())
            .insType(ReplicaSet.InsTypeEnum.MAIN.toString())
            .replicaSetName(tmpReplicaSetName)
            .domainPrefix(connectionString)

            // 版本规格
            .dbType(modifyInsParam.getDbType())
            .dbVersion(modifyInsParam.getDbVersion())
            .composeTag(serviceSpecTag)
            .bizType(custins.getBizType())
            .catagory(replicaSet.getCategory())
            .classCode(masterReplica.getClassCode())

            // 磁盘资源
            .storageType(modifyInsParam.getTargetDiskType())
            .diskSize(modifyInsParam.getTargetDiskSizeGB())
            .burstingEnabled(modifyInsParam.isBurstingEnabled())
            .provisionedIops(modifyInsParam.getProvisionedIops())
            .generalCloudDisk(modifyInsParam.getGeneralCloudDisk())
            .allocateDisk(false)
            .initOptimizedWrites(Boolean.parseBoolean(modifyInsParam.getTargetInitOptimizedWritesString()))

            // 网络资源 先默认不申请VIP（默认 lvs）后续有需要会更新
            .connType(CONN_TYPE_PHYSICAL)
            //反向VPC的资源申请下沉到任务流
            .ignoreCreateVpcMapping(true)

            // 地域
            .subDomain(targetAvzInfo.getRegion())
            .regionId(targetAvzInfo.getRegionId());

        if (isInsLevelMigration) {
            replicaSetResourceRequest.connType(custins.getConnType())
                .vpcId(vpcEndpoint.getVpcId())
                .vswitchID(vSwitchId)
                .cloudInstanceIp(null)
                .vpcInstanceId(null);
        }

        boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();
        if (modifyInsParam.isTargetSingleTenant()) {
            // 单租户场景
            replicaSetResourceRequest.singleTenant(true)
                .eniDirectLink(false);
        }

        // 资源模板相关
        replicaSetResourceRequest.setScheduleTemplate(modifyInsParam.getScheduleTemplate());
        podTemplateHelper.setSpecSchedulerConfigSpread(replicaSetResourceRequest, modifyInsParam.getDbInstanceName());

        Integer diskSizeGB = modifyInsParam.getTargetDiskSizeGB();
        int extendedDiskSizeGB = podParameterHelper
            .getExtendDiskSizeGBForPod(
                modifyInsParam.getReplicaSetMeta().getBizType(),
                false, // 云上无影响
                diskSizeGB
            );

        ArrayList<ReplicaResourceRequest> replicas = new ArrayList<>();
        ReplicaResourceRequest replicaResourceRequest;
        for (Map<String, String> dbNode : dbNodes) {
            replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
            replicaResourceRequest.setDiskSize(extendedDiskSizeGB);
            replicaResourceRequest.setClassCode(dbNode.get("classCode"));
            replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
            replicaResourceRequest.setRole(dbNode.get("role"));
            replicaResourceRequest.setZoneId(dbNode.get("zoneId"));
            replicas.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicas);

        podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
        result.setResourceRequest(replicaSetResourceRequest);

        if (isArm) {
            //如果是arm架构需要指定arch
            replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.ARM);
            replicaSetResourceRequest.setUseAsiCluster(true);
        }

        // 申请资源
        logger.info("allocate resource for {}, request body: {}", tmpReplicaSetName, JSON.toJSONString(replicaSetResourceRequest));
        isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, tmpReplicaSetName, replicaSetResourceRequest);
        result.setAllocated(isAllocate);

        // 更新临时实例参数
        ReplicaSet tmpReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, tmpReplicaSetName, null);
        custinsParamService.updateAVZInfo(tmpReplicaSet.getId().intValue(), targetAvzInfo);
        tmpReplicaSet.setPrimaryInsName(custins.getInsName());
        tmpReplicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
        dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, tmpReplicaSetName, tmpReplicaSet);

        // 只读实例配置白名单同步label
        podParameterHelper.setReadInsSgLabel(requestId, replicaSet, tmpReplicaSetName);
        result.setReplicaSet(tmpReplicaSet);
    }

    private Integer addTransferTaskForCluster(String requestId, ReplicaSet srcReplicaSet, ReplicaSet destReplicaSet, PodModifyInsParam modifyInsParam, List<Map<String, String>> dbNodesForResource, boolean isInsLevelMigration) throws Exception {
        // get replica info
        ReplicaListResult srcReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, srcReplicaSet.getName(), null, null, null, null);
        ReplicaListResult destReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, destReplicaSet.getName(), null, null, null, null);
        Long srcMasterReplicaId = srcReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();
        Long destReplicaId = destReplicas.getItems().stream()
            .filter(replica -> replica.getRole().equals(Replica.RoleEnum.MASTER))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("No replica found matching the role"))
            .getId();
        logger.info("Src Replicas:{}, dest Replicas:{}.", JSON.toJSONString(srcReplicas), JSON.toJSONString(destReplicas));

        // step 1: 写迁移记录
        CustInstanceDO srcInstance = custinsService.getCustInstanceByCustinsId(srcReplicaSet.getId().intValue());
        TransListDO transList = new TransListDO(srcInstance, InstanceSupport.TRANS_STATUS_REMOVE_NB, InstanceSupport.TRANS_TYPE_REMOVE);
        transList.setsHinsid1(srcMasterReplicaId.intValue());
        transList.setdCinsid(destReplicaSet.getId().intValue());
        InstanceLevelDO targetLevel = instanceService.getInstanceLevelByClassCode(destReplicaSet.getClassCode(),
            srcInstance.getDbType(), srcInstance.getDbVersion(), null, null);
        transList.setdLevelid(targetLevel.getId());
        transList.setdDisksize((long) (modifyInsParam.getTargetDiskSizeGB() * 1024));
        transList.setdHinsid1(destReplicaId.intValue());
        transList.setSwitchTime(modifyInsParam.getSwitchTime());

        String comment = "MigrateInsAvz";
        transList.setComment(comment);

        this.instanceIDao.createTransList(transList);

        // step 2: 设置任务参数
        JSONObject taskParam = new JSONObject();
        taskParam.put("requestId", requestId);
        taskParam.put(CustinsSupport.TRANS_ID, transList.getId());
        taskParam.put(CustinsSupport.SWITCH_KEY, modifyInsParam.getSwitchInfo());
        taskParam.put("srcReplicaSetName", srcReplicaSet.getName());
        taskParam.put("destReplicaSetName", destReplicaSet.getName());
        taskParam.put("srcReplicaId", srcMasterReplicaId);
        taskParam.put("destReplicaId", destReplicaId);
        taskParam.put("srcReplicaSetResourceGroupName", srcReplicaSet.getResourceGroupName());
        taskParam.put("destReplicaSetResourceGroupName", destReplicaSet.getResourceGroupName());
        taskParam.put("dbNodes", StringEscapeUtils.escapeJava(JSONObject.toJSONString(dbNodesForResource)));
        logger.info("srcReplicaId:{}, destReplicaId:{}.", srcMasterReplicaId, destReplicaId);

        // step3: 下发任务
        String taskKey;
        if (isInsLevelMigration) {
            taskKey = PodDefaultConstants.TASK_MIGRATE_CLUSTER_INS_AVZ;
        } else {
            taskKey = PodDefaultConstants.TASK_MIGRATE_CLUSTER_NODES;
        }

        String domain = PodDefaultConstants.DOMAIN_MYSQL;
        Object taskId = workFlowService.dispatchTask(
            "custins", srcReplicaSet.getName(), domain, taskKey, taskParam.toString(), 0);
        Integer taskIdInt = Double.valueOf(taskId.toString()).intValue();
        this.instanceIDao.updateTransTaskIdById(transList.getId(), taskIdInt);

        return taskIdInt;
    }
}

