package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteOperatorPermissionImpl")
public class DeleteOperatorPermissionImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteOperatorPermissionImpl.class);

    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try{
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            String privilegeType = parameterHelper.getParameterValue(ParamConstants.ACCOUNT_PRIVILEGE, ParamConstants.USER_AUTH);
            List<String> keys = new ArrayList<>();
            keys.add(CustinsParamSupport.CUSTINS_PARAM_NAME_SYSTEM_OPERATOR);
            keys.add(PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER);
            String paramKey = "";
            if (privilegeType.equals(ParamConstants.USER_AUTH)) {
                paramKey = CustinsParamSupport.CUSTINS_PARAM_NAME_USER_AUTH;
            } else {
                paramKey = CustinsParamSupport.CUSTINS_PARAM_NAME_INNER_AUTH;
            }
            CustinsParamDO authParam = custinsParamService.getCustinsParam(
                    replicaSet.getId().intValue(), paramKey);
            if (authParam == null) {
                return ResponseSupport.createErrorResponse(ErrorCode.OPERATOR_PERMISSION_NOT_FOUNT);
            }
            keys.add(paramKey);
            for (String key: keys) {
                dBaasMetaService.getDefaultClient().deleteReplicaSetLabel(requestId, replicaSet.getName(), key);
            }

            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
            return data;
        }catch (RdsException re) {
            logger.error(requestId + " DeleteOperatorPermission failed: " + re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " DeleteOperatorPermission failed: " + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}