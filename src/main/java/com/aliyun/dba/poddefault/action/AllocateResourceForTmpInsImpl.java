package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.IpResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

/**
 * 新架构申请和原实例规格一致的临时实例
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultAllocateResourceForTmpInsImpl")
@Slf4j
public class AllocateResourceForTmpInsImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(AllocateResourceForTmpInsImpl.class);
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    protected CommonProviderService commonProviderService;
    @Resource
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    protected ReplicaSetService replicaSetService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID);
        try {
            // 初始化实例基础信息
            PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(actionParams);
            custins = modifyInsParam.getCustins();
            //临时实例不允许创建临时实例
            if (custins.getIsTmp() == 1) {
                return createErrorResponse(ErrorCode.INVALID_DBINSTANCETYPE);
            }
            //创建中、已删除实例不允许创建临时实例
            if (custins.getStatus().equals(CustinsSupport.CUSTINS_STATUS_CREATING) || custins.getStatus().equals(CustinsSupport.CUSTINS_STATUS_DELETING) || custins.getStatus().equals(CustinsSupport.CUSTINS_STATUS_DESTROYED)) {
                return createErrorResponse(ErrorCode.INVALID_ACTION);
            }
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, custins.getInsName(), null);
            //判断是否已有临时实例,如果有，直接返回
            String usage = mysqlParamSupport.getParameterValue(actionParams, "usage");
            List<ReplicaSet> tmpReplicaSets = ObjectUtils.firstNonNull(dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId, custins.getInsName(), ReplicaSet.InsTypeEnum.TMP.toString()).getItems(), new ArrayList<>());
            Map<String, Object> data = new HashMap<String, Object>(2);
            for (ReplicaSet tmpReplicaset : tmpReplicaSets) {
                Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, tmpReplicaset.getName());
                if (labels.containsKey(CustinsSupport.TMP_INS_USAGE_LABEL) && labels.get(CustinsSupport.TMP_INS_USAGE_LABEL).equals(usage) && !tmpReplicaset.getStatus().equals(ReplicaSet.StatusEnum.DELETING)) {
                    log.info("tmp replicaset {} has been allocated, skip.", tmpReplicaset.getName());
                    data.put("DBInstanceID", tmpReplicaset.getId());
                    data.put("DBInstanceName", tmpReplicaset.getName());
                    data.put("isAllocated", true);
                    return data;
                }
            }
            String tmpReplicaSetName = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.TMP_REPILICASETNAME);
            //申请资源
            AllocateTmpResourceResult allocateResult = new AllocateTmpResourceResult();
            allocateResource(requestId, modifyInsParam, allocateResult, tmpReplicaSetName);
            logger.info("allocateResult = " + allocateResult);
            boolean isArm = PodCommonSupport.isArm(modifyInsParam.getSrcInstanceLevel());
            if (allocateResult.isAllocated()) {
                ReplicaSet tmpReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, tmpReplicaSetName, null);
                tmpReplicaSet.setPrimaryInsName(custins.getInsName());
                tmpReplicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
                dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, tmpReplicaSetName, tmpReplicaSet);
                Map<String, String> tmpInsLabels = new HashMap<>();
                if (isArm) {
                    //arch arm, 设置这个label的目的是因为刷白不支持源实例和目标实例是不同的cpu架构，会导致arm实例上刷白镜像拉取成x86的
                    tmpInsLabels.put(PodDefaultConstants.PARAM_ARCH_LABEL, ReplicaSetResourceRequest.ArchEnum.ARM.getValue());
                }
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSetName, tmpInsLabels);
                // 只读实例配置白名单同步label
                podParameterHelper.setReadInsSgLabel(requestId, replicaSet, tmpReplicaSetName);
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSetName, ImmutableMap.of(CustinsSupport.TMP_INS_USAGE_LABEL, usage));
                data.put("DBInstanceID", tmpReplicaSet.getId());
                data.put("DBInstanceName", tmpReplicaSet.getName());
            }
            data.put("isAllocated", allocateResult.isAllocated());
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private void allocateResource(String requestId, PodModifyInsParam modifyInsParam, AllocateTmpResourceResult result, String tmpReplicaSetName) throws Exception {
        CustInstanceDO custins = modifyInsParam.getCustins();
        String category = modifyInsParam.getTargetInstanceLevel().getCategory().getValue();
        if (custins.isRead()) {
            ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSetById(requestId, Long.valueOf(custins.getPrimaryCustinsId()));
            category = primaryReplicaSet.getCategory();
        }
        String connectionString = CheckUtils.checkValidForConnAddrCust(tmpReplicaSetName);
        logger.info("avzInfo:{}", modifyInsParam.getAvzInfo());
        AVZInfo avzInfo = modifyInsParam.getAvzInfo();
        //查询镜像实例是否已经存在
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
        custInstanceQuery.setPrimaryCustinsId(custins.getId());
        Long mirrorCount = custinsService.countCustIns(custInstanceQuery);
        logger.info("mirrorCount is " + mirrorCount);
        if (mirrorCount > 0) {
            throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
        }
        // 申请资源
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
        if (StringUtils.isNotBlank(modifyInsParam.getClusterName()) && modifyInsParam.isDHG()) {
            replicaSetResourceRequest.setDedicatedHostGroupId(modifyInsParam.getClusterName());
        }
        logger.info("targetSpecTag:{}", modifyInsParam.getTargetComposeTag());
        logger.info("serviceSpexcTag:{}", minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(requestId, custins.getInsName()));
        replicaSetResourceRequest.userId(modifyInsParam.getBid())
                .uid(modifyInsParam.getUid())
                .insType(ReplicaSet.InsTypeEnum.MAIN.toString())
                .replicaSetName(tmpReplicaSetName)
                .domainPrefix(connectionString)
                // 版本规格
                .dbType(modifyInsParam.getDbType())
                .dbVersion(modifyInsParam.getDbVersion())
                .bizType(custins.getBizType())
                .composeTag(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(requestId, custins.getInsName()))
                .classCode(modifyInsParam.getTargetClassCode())
                .catagory(category)
                // 磁盘资源
                .storageType(modifyInsParam.getTargetDiskType())
                .diskSize(modifyInsParam.getTargetDiskSizeGB())
                //只有跨可用区才申请vpc
                .connType(CONN_TYPE_PHYSICAL)
                .vswitchID(null)
                .cloudInstanceIp(null)
                .vpcInstanceId(null)
                //反向VPC的资源申请下沉到任务流
                .ignoreCreateVpcMapping(true)
                .eniDirectLink(false)
                // 地域
                .subDomain(avzInfo.getRegion())
                .regionId(avzInfo.getRegionId());
        //只申请计算资源
        replicaSetResourceRequest.setAllocateDisk(false);
        // 单租户场景
        boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();
        if (isTargetSingleTenant) {
            replicaSetResourceRequest.singleTenant(true);
        }
        // 资源模板相关
        replicaSetResourceRequest.setScheduleTemplate(modifyInsParam.getScheduleTemplate());
        // 设置rund 网络相关配置
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
            rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            Endpoint vpcEndpoint = replicaSetService.getReplicaSetVpcEndpoint(requestId, custins.getInsName()); //VPC不能变，以实例的为准，
            replicaSetResourceRequest.setVpcId(vpcEndpoint.getVpcId());
        }
        int diskSizeGB = modifyInsParam.getDiskSizeGB();
        int extendedDiskSizeGB = podParameterHelper
                .getExtendDiskSizeGBForPod(
                        modifyInsParam.getReplicaSetMeta().getBizType(),
                        modifyInsParam.isSingleNode(),
                        diskSizeGB
                );
        // 需要迁移的角色
        List<Replica> currentReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId,
                custins.getInsName(), null, null, null, null).getItems();
        if (currentReplicas == null || currentReplicas.isEmpty()) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCENAME);
        }
        List<ReplicaResourceRequest> replicas = new ArrayList<>();
        for (Replica replica : currentReplicas) {
            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
            replicaResourceRequest.setDiskSize(extendedDiskSizeGB);
            replicaResourceRequest.setClassCode(replica.getClassCode());
            replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
            replicaResourceRequest.setRole(replica.getRole().getValue());
            replicaResourceRequest.setZoneId(replica.getZoneId());
            replicaResourceRequest.setSubDomain(replica.getSubDomain());
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
                rundPodSupport.completeReplicaNetworkConfig(replicaResourceRequest, replica);
            }
            replicas.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicas);

        podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
        result.setResourceRequest(replicaSetResourceRequest);
        boolean isArm = PodCommonSupport.isArm(modifyInsParam.getTargetInstanceLevel());
        if (isArm) {
            //如果是arm架构需要指定arch
            replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.ARM);
            replicaSetResourceRequest.setUseAsiCluster(true);
        }
        boolean isAllocate = false;
        try {
            // 申请资源
            logger.info("allocate resource for {}, request body: {}", tmpReplicaSetName, JSON.toJSONString(replicaSetResourceRequest));
            isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, tmpReplicaSetName, replicaSetResourceRequest);
            result.setAllocated(isAllocate);
        } catch (Exception ex) {
            logger.error(requestId + "Allocate tmp replicaSet failed: " + JSON.toJSONString(ex));
            isAllocate = false;
            if (ex instanceof ApiException) {
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
            }
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isAllocate) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, tmpReplicaSetName);
                } catch (ApiException e) {
                    logger.error(requestId + " release ReplicaSet Exception: {}", e.getResponseBody());
                }
            }
        }
    }
}