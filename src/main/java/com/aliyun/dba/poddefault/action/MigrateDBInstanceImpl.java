package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.migrate.BaseMigrateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.migrate.basic.BasicMigrateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.migrate.xdb.XDBMigrateDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.base.support.MySQLParamConstants.REPLICASET_BIZ_TYPE_ALGROUP;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_BASIC;

/**
 * MigrateDBInstance的逻辑与ModifyDBInstanceClass逻辑相同
 * 只是MigrateDBInstance的isTranfer=true
 * ModifyDBInstanceClass的isTransfer=false
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultMigrateDBInstanceImpl")
public class MigrateDBInstanceImpl implements IAction {
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    protected IAction poddefaultMigrateFailoverImpl;


    private static final LogAgent logger = LogFactory.getLogAgent(MigrateDBInstanceImpl.class);

    private static Map<String, BaseMigrateDBInstanceService> migrateDBInstanceMap = Maps.newHashMap();

    static {
        migrateDBInstanceMap.put(InstanceLevel.CategoryEnum.BASIC.toString(), SpringContextUtil.getBeanByClass(BasicMigrateDBInstanceService.class));
        migrateDBInstanceMap.put(InstanceLevel.CategoryEnum.ENTERPRISE.toString(), SpringContextUtil.getBeanByClass(XDBMigrateDBInstanceService.class));
    }

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID);
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(actionParams);

            // 宕机迁移
            boolean isEmergencyTransfer = Integer.parseInt(podParameterHelper.getParameterValue("EmergencyTransfer", "0")) != 0;

            boolean isBasicIns = InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSet.getCategory());

            boolean isTddlLocalToCloud = isAligroupTddlLocalToCloud(actionParams,replicaSet);

            // 基础版只读转迁移逻辑
            if (CATEGORY_BASIC.equals(replicaSet.getCategory()) && ReplicaSet.InsTypeEnum.READONLY == replicaSet.getInsType()) {
                logger.info("requestId:{} process basic read ins:{} migrate for rebuild", requestId, replicaSet.getName());
                return poddefaultMigrateFailoverImpl.doActionRequest(custins, actionParams);
            }

            // 基础版实例离线迁移，用于资源腾挪场景
            if ((isBasicIns && !isEmergencyTransfer) || isTddlLocalToCloud) {
                Object taskId = migrateDBInstanceMap.get(replicaSet.getCategory()).doMigrateDbInstance(actionParams);

                if(taskId instanceof Map){
                    return (Map<String, Object>) taskId;
                }

                // 若 taskId 为 double，先转为long，防止出现12345.0, 2.028782E+7等格式
                if (taskId instanceof Double) {
                    taskId = String.valueOf((long) Math.floor((double) taskId));
                }

                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", custins.getId());
                data.put("DBInstanceName", custins.getInsName());
                data.put("TaskId", taskId);
                return data;
            }
            else if (PodParameterHelper.isAliYun(replicaSet.getBizType()) && !isBasicIns) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            else {
                ModifyDBInstanceClassImpl modifyDBInstanceClassImpl = SpringContextUtil.getBeanByClass(ModifyDBInstanceClassImpl.class);
                return modifyDBInstanceClassImpl.doActionRequest(custins, actionParams);
            }

        }  catch (RdsException e) {
            logger.error("doRecoverDBInstance failed!", e);
            return createErrorResponse(e.getErrorCode());
        } catch (ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception e) {
            logger.error("doRecoverDBInstance failed!", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private boolean isAligroupTddlLocalToCloud(Map<String, String> params, ReplicaSet replicaSet) throws Exception{
        if (REPLICASET_BIZ_TYPE_ALGROUP.equals(replicaSet.getBizType().toString()) && ReplicaSet.ConnTypeEnum.TDDL.toString().equals(replicaSet.getConnType().toString())){
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
            String destStorage = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_STORAGE_TYPE, "");
            String curStorage = replicaSetService.getReplicaSetStorageType(replicaSet.getName(), requestId);
            if(StringUtils.isNotEmpty(destStorage) &&
                    (Replica.StorageTypeEnum.CLOUD_ESSD.toString().equalsIgnoreCase(destStorage)|| Replica.StorageTypeEnum.CLOUD_EFFICIENCY.toString().equalsIgnoreCase(destStorage)) &&
                    Replica.StorageTypeEnum.LOCAL_SSD.toString().equalsIgnoreCase(curStorage)){
                return true;
            }
        }

        return false;
    }
}
