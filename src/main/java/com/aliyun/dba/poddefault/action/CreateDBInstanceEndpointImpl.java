package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointService;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointServiceFactoryImpl;
import com.aliyun.dba.poddefault.action.service.endpoint.EndpointWeightConfig;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateDBInstanceEndpointImpl")
@Slf4j
public class CreateDBInstanceEndpointImpl implements IAction {

    private final Integer DEFAULT_RO_ENDPOINT_COUNT = 1;

    @Resource
    private EndPointServiceFactoryImpl endpointFactory;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private ReplicaSetService replicaSetService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);  // use DBInstanceName
            EndPointService endPointService = endpointFactory.getService(replicaSet.getConnType());
            String replicaSetName = replicaSet.getName();
            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

            // pre-check
            if (!MysqlParamSupport.isCluster(replicaSet.getCategory())) {  // 暂时限制只有 Cluster
                return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
            }
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            log.info("{} start create endpoint", replicaSetName);

            // data prepare
            String endpointType = paramSupport.getParameterValue(params, "DBInstanceEndpointType");
            String endpointDesc = paramSupport.getParameterValue(params, "DBInstanceEndpointDesc");
            String vpcId = paramSupport.getParameterValue(params, "VPCId");
            String vSwitchId = paramSupport.getParameterValue(params, "VSwitchId");
            String privateIPAddress = paramSupport.getParameterValue(params, "PrivateIpAddress");
            String connectionStringPrefix = paramSupport.getParameterValue(params, "ConnectionStringPrefix");
            String vpcInstanceId = paramSupport.getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);
            String port = paramSupport.getPort(params);
            String NodeItemsString = paramSupport.getParameterValue(params, "NodeItems");
            Map<String, Object> config = new HashMap<>();
            config.put(EndpointWeightConfig.CONFIG_KEY, JSON.parseObject(NodeItemsString, List.class));
            EndpointWeightConfig weightConfig = JSON.parseObject(JSON.toJSONString(config), EndpointWeightConfig.class);

            // check ro endpoint num
            List<EndpointGroup> roEndpointGroupList = dBaasMetaService.listEndpointGroups(requestId, replicaSetName).getItems()
                    .stream().filter(eg -> eg.getType().equalsIgnoreCase(Endpoint.TypeEnum.READONLY.toString())).collect(Collectors.toList());
            if (roEndpointGroupList.size() >= DEFAULT_RO_ENDPOINT_COUNT && Endpoint.TypeEnum.READONLY.toString().equalsIgnoreCase(endpointType)) {
                throw new RdsException(ErrorCode.INVALID_ENDPOINT_NUM);
            }

            // check endpointType
            if (endpointType == null) {
                throw new RdsException(ErrorCode.ENDPOINT_TYPE_NOT_SUPPORT);
            }
            endpointType = endPointService.upperCaseFirst(endpointType.toLowerCase());

            // check vpc vsw
            endPointService.checkValidForVpcId(vpcId);
            endPointService.checkValidForVswId(vSwitchId);

            // check nodeItems
            endPointService.checkNodeItems(requestId, replicaSetName, weightConfig, endpointType);

            // check connStringPrefix
            endPointService.checkConnectionStringPrefix(requestId, replicaSet, connectionStringPrefix);

            // check port
            CheckUtils.parseInt(port, 1000, 65534, ErrorCode.INVALID_PORT);

            // generate endpointName, ensure unique name
            String newEndpointName = null;
            EndpointGroup newEndpointGroup = null;
            for (int i = 0; i < 10; i++) {  // 循环十次，都没有合适的 name 就抛错
                newEndpointName = PodReplicaSetResourceHelper.generateEndpointId();
                newEndpointGroup = dBaasMetaService.getDefaultClient().getEndpointGroupByName(requestId, newEndpointName, true);
                if (newEndpointGroup == null) {
                    break;
                }
            }
            if (newEndpointGroup != null) {  // 理论上走不进这一步
                throw new RdsException(ErrorCode.OTHER_ENDPOINT_EXIST);
            }

            // allocate connectionStringPrefix
            Endpoint endpoint = endPointService.createPrivateEndpointAddress(requestId, replicaSetName, vpcId, vSwitchId, privateIPAddress,
                    connectionStringPrefix, port, endpointType, weightConfig, null, 1, vpcInstanceId);

            // dispatch workflow task
            JSONObject taskParam = new JSONObject();
            taskParam.put(ParamConstants.REQUEST_ID, requestId);
            taskParam.put("endpointName", newEndpointName);
            taskParam.put("endpointType", endpointType);
            taskParam.put("endpointDesc", Objects.isNull(endpointDesc) && endpointType.equalsIgnoreCase(Endpoint.TypeEnum.NODE.toString()) ? weightConfig.getNodeWeights().get(0).getNodeId() : endpointDesc);  // 直连地址默认设置desc为节点ID
            taskParam.put("nodeItems", JSON.toJSONString(weightConfig));
            taskParam.put("connectionString", endpoint.getAddress());
            String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            // inactive replicaSet
            endPointService.inactiveReplicaSet(requestId, replicaSetName);
            // dispatch
            Object taskId = workFlowService.dispatchTask("custins", replicaSetName, domain, PodDefaultConstants.TASK_CREATE_INS_ENDPOINT, taskParam.toJSONString(), 0);

            // rtn
            Map<String, Object> rtn = new HashMap<>();
            rtn.put("TaskId", taskId);
            rtn.put("DBInstanceName", replicaSetName);
            rtn.put("DBInstanceEndpointId", newEndpointName);
            rtn.put("ConnectionString", endpoint.getAddress());
            rtn.put("RequestId", requestId);
            return rtn;
        } catch (ApiException ex) {
            log.error("CreateDBInstanceEndpoint, metadb api called failed: ", ex);
            throw new RdsException(ErrorCode.API_CALLING_FAILED, "Meta db calling failed");
        } catch (RdsException ex) {
            log.error("CreateDBInstanceEndpoint failed: ", ex);
            throw ex;
        } catch (JSONException ex) {
            log.error("JSON parse failed: ", ex);
            return createErrorResponse(ErrorCode.INVALID_NODEITEMS_JSON_FORMAT);
        } catch (Exception e) {
            log.error("CreateDBInstanceEndpoint Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
