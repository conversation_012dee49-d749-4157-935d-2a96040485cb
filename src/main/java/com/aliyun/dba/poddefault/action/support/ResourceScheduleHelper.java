package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 资源调度Helper
 * <a href="https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJPPNL2rXIE4XpZNxJzN67Mw4?utm_scene=team_space">方案文档</a>
 * <AUTHOR> on 2023/11/17
 */
@Component
public class ResourceScheduleHelper {

    private static final LogAgent logger = LogFactory.getLogAgent(ResourceScheduleHelper.class);

    private final static String FORCE_SAME_ECS_CLASS_UIDS = "FORCE_SAME_ECS_CLASS_UIDS";

    @Resource
    private ResourceService resourceService;


    /**
     * 资源调度保障算力标识（仅单租户资源生效）
     *
     * @param uid
     * @param replicaSetName
     * @return
     */
    public Pair<String, String> makeResourceGuaranteeStrategy(String uid, String replicaSetName) {
        try {
            ResourceDO resourceDO = resourceService.getResourceByResKey(FORCE_SAME_ECS_CLASS_UIDS);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                Set<String> whiteIds = JSON.parseObject(resourceDO.getRealValue(), Set.class);
                if (whiteIds.contains(uid)) {
                    // 算力保障策略：单租户实例变配不会降级机型代系，白名单用户生效
                    logger.info("Set [{}] compute strategy to computingPowerEnsure", replicaSetName);
                    return Pair.of(PodDefaultConstants.LABEL_ALIGNMENT_STRATEGY, PodDefaultConstants.SINGLE_TENANT_RESOURCE_STRATEGY_COMPUTING);
                }
            }
        } catch (Exception e) {
            //ignore
            logger.warn("Get FORCE_SAME_ECS_CLASS_UIDS resource failed, ignore", e);
        }
        //默认逻辑：资源申请成功率优先
        return null;
    }

}
