package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.api.VpcMappingApi;
import com.aliyun.dba.adb_vip_manager_client.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.migrate.MigrateDBInstanceAvzService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceVipImpl")
public class ModifyDBInstanceVipImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceVipImpl.class);
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private LinksApi linksApi;
    @Resource
    private VpcMappingApi vpcMappingApi;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private MigrateDBInstanceAvzService migrateDBInstanceAvzService;
    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Resource
    private CommonProviderService commonProviderService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        ReplicaSet replicaSet;
        try {
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            Integer targetNetType = paramSupport.getAndCheckNetType(params);

            if (!CustinsSupport.isVpcNetType(targetNetType)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }

            String vpcId = CheckUtils.checkValidForVPCId(paramSupport.getParameterValue(params, ParamConstants.VPC_ID));
            String vswitchId = CheckUtils.checkValidForVswitchId(paramSupport.getParameterValue(params, ParamConstants.VSWITCH_ID));
            String ipAddr = paramSupport.getParameterValue(params, ParamConstants.IP_ADDRESS);

            // 为集群版形态 链路管理 适配已有接口
            if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                List<Endpoint> endpointList = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null).getItems()
                        .stream().filter(ep -> ep.getType() == Endpoint.TypeEnum.NORMAL && Objects.equals(ep.getNetType(), Endpoint.NetTypeEnum.VPC)).collect(Collectors.toList());
                if (endpointList.size() != 1) {
                    throw new RdsException(ErrorCode.INVALID_ENDPOINT_NUM);
                }
                Endpoint primaryEndpoint = endpointList.get(0);
                if (primaryEndpoint.getEndpointGroupId() == null) {
                    throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
                }

                EndpointGroup endpointGroup = dBaasMetaService.getDefaultClient().getEndpointGroupById(requestId, primaryEndpoint.getEndpointGroupId(), false);
                params.put("DBInstanceEndpointId".toLowerCase(), endpointGroup.getGroupName());
                params.put("PrivateIPAddress".toLowerCase(), ipAddr);
                params.put(ParamConstants.CONNECTION_STRING.toLowerCase(), primaryEndpoint.getAddress());

                ModifyDBInstanceEndpointAddressImpl modifyDBInstanceEndpointAddress = SpringContextUtil.getBeanByClass(ModifyDBInstanceEndpointAddressImpl.class);
                return modifyDBInstanceEndpointAddress.doActionRequest(null, params);
            }

            EndpointListResult endpointListResult = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null);
            if (CollectionUtils.isEmpty(endpointListResult.getItems())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }
            List<Endpoint> endpointList = endpointListResult.getItems()
                    .stream().filter(v -> (v.getNetType() == Endpoint.NetTypeEnum.VPC && v.getType() == Endpoint.TypeEnum.NORMAL))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(endpointList) || endpointList.size() > 1) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }

            String tmpConnPrefix = "tmp-";
            Optional<Endpoint> optional = endpointList.stream().filter(v -> (!v.getUserVisible() && v.getAddress().startsWith(tmpConnPrefix))).findFirst();
            if (optional.isPresent()) {
                //已经存在之前申请的临时链接，不允许再多申请
                logger.error("tmp VIP {},{} is already exists", optional.get().getIp(), optional.get().getPort());
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }

            Endpoint existsEndpoint = endpointList.get(0);

            Link linkBody = new Link();
            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId,
                    replicaSet.getName(), null, null, null, null);

            int checkCount = 0;
            Vpod masterVpod = null;
            Replica masterReplica = null;
            for (Replica replica : replicaListResult.getItems()) {
                Vpod vpod = dBaasMetaService.getDefaultClient().getReplicaVpod(requestId, replica.getId(), null, null);
                if (replica.getRole() == Replica.RoleEnum.MASTER) {
                    masterVpod = vpod;
                    masterReplica = replica;
                    checkCount++;
                }
            }
            if (masterVpod == null) {
                logger.error("cannot find master server");
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            if (checkCount > 1) {
                logger.error("may be have more than one master in this replicaset");
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            boolean vpcChanged = !StringUtils.equals(existsEndpoint.getVpcId(), vpcId);
            //rund切换走迁移流程
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(masterVpod.getRuntimeType()) && vpcChanged) {
                return modifyVpcForRund(requestId, replicaSet, vpcId, vswitchId, params);
            }

            linkBody.setVpcId(vpcId);
            linkBody.setVswitchId(vswitchId);
            linkBody.setRwType(Link.RwTypeEnum.NORMAL);
            linkBody.setNetType(CustinsSupport.NET_TYPE_VPC);
            linkBody.setMasterClusterName(masterVpod.getSiteName());
            linkBody.setPort(existsEndpoint.getVport());
            linkBody.setRegionName(masterVpod.getRegionId());
            linkBody.setUserVisible(0);
            linkBody.setIp(ipAddr);
            linkBody.setConnAddrPrefix(tmpConnPrefix + replicaSet.getName());

            RealServer linkRealServer = new RealServer();
            linkRealServer.setEcsId(masterVpod.getEcsInsId());
            linkRealServer.setIp(masterVpod.getIp());
            linkRealServer.setPort(masterVpod.getPorts().get(0));

            if (replicaIsEniMode(masterReplica)) {
                EcsHost ecsHostByHostName = dBaasMetaService.getDefaultClient().getEcsHostByHostName(requestId, masterReplica.getHostName(), null);
                EniListResult eniListResult = dBaasMetaService.getDefaultClient().listEcsEnis(requestId, ecsHostByHostName.getEcsInsId());
                if (Objects.nonNull(eniListResult) && CollectionUtils.isNotEmpty(eniListResult.getItems())) {
                    if (PodType.POD_ECS_RUND.getRuntimeType().equals(masterVpod.getRuntimeType())) {
                        String vpodId = masterVpod.getVpodId();
                        Eni eni = eniListResult.getItems().stream().filter(item -> item.getVpodId().equalsIgnoreCase(vpodId)).findFirst().orElse(null);
                        if (eni != null) {
                            linkRealServer.setVpcId(eni.getVpcId());
                        } else {
                            logger.error("Cannot find eni for rund pod");
                            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                        }
                    } else if (Objects.nonNull(eniListResult.getItems().get(0))
                            && Objects.nonNull(eniListResult.getItems().get(0).getVpcId())) {
                        linkRealServer.setVpcId(eniListResult.getItems().get(0).getVpcId());
                    } else {
                        logger.error("Cannot find eni VpcId from metaDB");
                        throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                    }
                } else {
                    logger.error("Cannot find eni VpcId from metaDB");
                    throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                }
            }
            linkBody.setRealServers(Collections.singletonList(linkRealServer));
            //申请一个临时的域名（DNS + VIP + 反向VPC）
            LinkDetailResponse detailResponse = linksApi.createLink(replicaSet.getName(), linkBody, requestId);
            if (detailResponse.getData() == null || StringUtils.isBlank(detailResponse.getData().getIp())) {
                logger.error("create VIP error, msg is {}", detailResponse.toString());
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }

            LinkResponseWithRs linkResponseWithRs = detailResponse.getData();

            String newVip = linkResponseWithRs.getIp();
            String newPort = linkResponseWithRs.getPort();
            Map<String, Object> retData = new HashMap<>();
            try {
                // 做DNS级别VIP交换，生成changeLog给到任务流，
                List<EndpointChangeLog> changeLogList = new ArrayList<>();

                EndpointChangeLog updateEndpointChangeLog = new EndpointChangeLog();
                updateEndpointChangeLog.action(EndpointChangeLog.ActionEnum.UPDATE);
                updateEndpointChangeLog.fromConnAddrCust(existsEndpoint.getAddress());
                updateEndpointChangeLog.fromVip(existsEndpoint.getVip());
                updateEndpointChangeLog.fromVport(existsEndpoint.getVport().toString());
                updateEndpointChangeLog.fromUserVisible(existsEndpoint.getUserVisible());
                updateEndpointChangeLog.fromTunnelId(existsEndpoint.getTunnelId());
                updateEndpointChangeLog.fromUserVisible(existsEndpoint.getUserVisible());
                updateEndpointChangeLog.fromVpcId(existsEndpoint.getVpcId());

                updateEndpointChangeLog.toConnAddrCust(existsEndpoint.getAddress());
                updateEndpointChangeLog.setToVip(newVip);
                updateEndpointChangeLog.setToVport(newPort);
                updateEndpointChangeLog.setToUserVisible(false);
                updateEndpointChangeLog.setToConnAddrCust(linkResponseWithRs.getConnAddr());
                updateEndpointChangeLog.setToVpcId(vpcId);
                updateEndpointChangeLog.setToVswitchId(vswitchId);
                updateEndpointChangeLog.setNetType(EndpointChangeLog.NetTypeEnum.VPC);
                updateEndpointChangeLog.setStatus(EndpointChangeLog.StatusEnum.CREATING);
                updateEndpointChangeLog.setCreator(77);
                updateEndpointChangeLog.setModifier(77);
                updateEndpointChangeLog.setReplicaId(masterReplica.getId());
                updateEndpointChangeLog.setRwType(EndpointChangeLog.RwTypeEnum.NORMAL);

                //临时的需要释放掉
                EndpointChangeLog deleteEndpointChangeLog = new EndpointChangeLog();
                deleteEndpointChangeLog.action(EndpointChangeLog.ActionEnum.DELETE);
                deleteEndpointChangeLog.setFromConnAddrCust(linkResponseWithRs.getConnAddr());
                deleteEndpointChangeLog.setFromVip(existsEndpoint.getVip());
                deleteEndpointChangeLog.setFromVport(existsEndpoint.getVport().toString());
                deleteEndpointChangeLog.setFromUserVisible(false);
                deleteEndpointChangeLog.setFromVpcId(existsEndpoint.getVpcId());
                deleteEndpointChangeLog.setNetType(EndpointChangeLog.NetTypeEnum.VPC);
                deleteEndpointChangeLog.setStatus(EndpointChangeLog.StatusEnum.CREATING);
                deleteEndpointChangeLog.setCreator(77);
                deleteEndpointChangeLog.setModifier(77);
                deleteEndpointChangeLog.setReplicaId(masterReplica.getId());

                changeLogList.add(updateEndpointChangeLog);
                changeLogList.add(deleteEndpointChangeLog);

                JSONObject taskParam = new JSONObject();
                taskParam.put(ParamConstants.REQUEST_ID, requestId);
                taskParam.put("newVip", newVip);
                taskParam.put("newVport", newPort);
                taskParam.put("vpcId", vpcId);
                String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
                Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, PodDefaultConstants.TASK_MODIFY_VIP, taskParam.toJSONString(), 0);

                for (EndpointChangeLog endpointChangeLog : changeLogList) {
                    endpointChangeLog.setTaskId(Double.valueOf(taskId.toString()).intValue());
                    dBaasMetaService.getDefaultClient().createReplicaSetEndpointChangeLog(requestId, replicaSet.getName(), endpointChangeLog);
                }

                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_MAINTAINING.toString());
                retData.put("DBInstanceID", replicaSet.getId());
                retData.put("DBInstanceName", replicaSet.getName());
                retData.put("TaskId", taskId);
            } catch (Exception e) {
                logger.error("some exception occurs, try to release vip [{}]", newVip, e);
                linksApi.deleteLink(replicaSet.getName(), newVip, requestId);
                logger.error("release vip [{}] success", newVip);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            return retData;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    private boolean replicaIsEniMode(Replica replica) {
        return !(replica.getLinkIp() != null && replica.getLinkIp().equalsIgnoreCase(replica.getCtrlIp()));
    }

    private Map<String, Object> modifyVpcForRund(String requestId, ReplicaSet replicaSet, String vpcId, String vswitchId, Map<String, String> params) throws Exception {
        if (!InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSet.getCategory())) {
            logger.error("only rund basic support modify vpc. current category:{}", replicaSet.getCategory());
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
        PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(params);
        modifyInsParam.setAvzInfo(modifyInsParam.getOldAvzInfo());
        AllocateTmpResourceResult allocateResult = new AllocateTmpResourceResult();
        boolean isSuccess = false;
        Map<String, Object> retData = new HashMap<>();
        try {
            migrateDBInstanceAvzService.allocateResourceForMigrate(requestId, replicaSet, modifyInsParam, params, allocateResult, vpcId, vswitchId);
            ReplicaSet tmpReplicaSet = allocateResult.getReplicaSet();
            // vmoc-lite 临时实例需要打上VBM标签，迁移任务流注入xdp注解
            migrateDBInstanceAvzService.addLabelForTmpReplicaSetIfVbm(modifyInsParam, requestId, tmpReplicaSet.getName());
            ReplicaListResult srcReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null);
            ReplicaListResult destReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, tmpReplicaSet.getName(), null, null, null, null);
            Long srcMasterReplicaId = srcReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();
            Long destMasterReplicaId = destReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();
            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put("srcReplicaSetName", replicaSet.getName());
            taskParam.put("destReplicaSetName", tmpReplicaSet.getName());
            taskParam.put("srcReplicaId", srcMasterReplicaId);
            taskParam.put("destReplicaId", destMasterReplicaId);
            taskParam.put("destVSwitchId", vswitchId);
            taskParam.put("destVpcId", vpcId);
            taskParam.put("srcReplicaSetResourceGroupName", replicaSet.getResourceGroupName());
            taskParam.put("destReplicaSetResourceGroupName", tmpReplicaSet.getResourceGroupName());
            // 下发迁移任务
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSet.getName(), "mysql", PodDefaultConstants.TASK_MODIFY_VIP_FOR_RUND, taskParam.toString(), 0);
            retData.put("DBInstanceID", replicaSet.getId());
            retData.put("DBInstanceName", replicaSet.getName());
            retData.put("TaskId", taskId);

            // 更新实例状态
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_MAINTAINING.toString());
            Integer taskIdInt = Double.valueOf(taskId.toString()).intValue();
            // 准备链路切换
            migrateDBInstanceAvzService.addSwitchVipChangeLog(requestId, replicaSet, tmpReplicaSet, taskIdInt);
            isSuccess = true;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getMessage(), e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (allocateResult.isAllocated() && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, allocateResult.getResourceRequest().getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("resource resource for transfer failed: %s", e.getResponseBody()));
                }
            }
        }
        return retData;
    }

}

