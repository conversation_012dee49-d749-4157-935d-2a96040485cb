package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.instance.support.InstanceSupport.*;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateDBNodesImpl")
@Slf4j
public class CreateDBNodesImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateDBNodesImpl.class);

    @Resource
    protected AliyunInstanceDependency dependency;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    ReplicaSetService replicaSetService;
    @Autowired
    protected CheckService checkService;
    @Resource
    protected PodParameterHelper podParameterHelper;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Resource
    protected PodTemplateHelper podTemplateHelper;
    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private InstanceIDao instanceIDao;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        boolean isSuccess = false;
        boolean isAllocated = false;
        String tmpReplicaName = null;
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            PodModifyInsParam modifyInsParam = null;
            modifyInsParam = initAddNodeParam(params);
            tmpReplicaName = modifyInsParam.getTmpReplicaSetName();
            ReplicaSet replicaSet = modifyInsParam.getReplicaSetMeta();
            if (modifyInsParam.getReplicaSetMeta().getStatus() == ReplicaSet.StatusEnum.REPLICA_ADDING) {
                if (workFlowService.isTaskAlreadyRunning(modifyInsParam.getOrderId(), replicaSet.getName())) {
                    log.info("Task is Already Running.");
                    Map<String, Object> data = new HashMap<>();
                    data.put("TaskId", 0);
                    data.put("DBInstanceName", replicaSet.getName());
                    data.put("addNodeNum", 0);
                    return data;
                }
            }
            if (!(modifyInsParam.getReplicaSetMeta().getStatus() == ReplicaSet.StatusEnum.ACTIVATION)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (replicaSet.getInsType() != ReplicaSet.InsTypeEnum.MAIN) {
                logger.error("dbInstance is not main Instance, do not allow add node");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            boolean isMysqlCluster = MysqlParamSupport.isCluster(replicaSet.getCategory());
            if (!isMysqlCluster){
                logger.error("dbInstance is not cluster, do not allow add node");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            List<Map<String, String>> addNodeList = mysqlParamSupport.getNodesInfo(params);

            // node size =0 直接返回
            if (addNodeList.size() == 0){
                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", 0);
                data.put("DBInstanceName", dbInstanceName);
                data.put("addNodeNum", 0);
                return data;
            }

            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().
                    listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null);
            List<Replica> replicas = replicaListResult.getItems();
            if (replicas == null) {
                throw new RdsException(ErrorCode.DBINSTANCE_DO_NOT_HAVE_STANDBY_NODE);
            }

            if (isOrderProcessed(modifyInsParam.getOrderId(), modifyInsParam.getDbInstanceName(), replicas)) {
                log.info("Order is processed.");
                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", 0);
                data.put("DBInstanceName", dbInstanceName);
                data.put("addNodeNum", 0);
                return data;
            }
            checkClusterCanAddNodes(requestId, dbInstanceName, replicas, addNodeList);
            checkAddNodeInfos(requestId, addNodeList, modifyInsParam.isSrcSingleTenant(), PodCommonSupport.isArm(modifyInsParam.getSrcInstanceLevel()), modifyInsParam.getDbType(), modifyInsParam.getDbVersion());
            List<Map<String, String>> addNodeRoleList = getRoleInfoMap(addNodeList);

            AllocateTmpResourceResult allocateRes = allocateClusterAddNodeRes(requestId, custins, modifyInsParam,
                    InstanceLevel.CategoryEnum.CLUSTER.getValue(), addNodeRoleList);

            Map<String, Object> data = new HashMap<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ParamConstants.ORDERID, modifyInsParam.getOrderId());
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);
            jsonObject.put("srcReplicaSetName", dbInstanceName);
            jsonObject.put("destReplicaSetName", tmpReplicaName);
            jsonObject.put("transTaskId", allocateRes.getTransList().getId());
            jsonObject.put("targetPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());

            String taskKey = getTaskKey(replicaSet);
            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);

            replicaSet.setStatus(ReplicaSet.StatusEnum.REPLICA_ADDING);
            dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, dbInstanceName, replicaSet);
            List<Replica> tmpReplicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, tmpReplicaName, null, null, null, null).getItems();

            isAllocated = allocateRes.isAllocated();
            data.put("TaskId", taskId);
            data.put("DBInstanceName", dbInstanceName);
            data.put("addNodeNum", addNodeRoleList.size());
            data.put("AddNodeIds", String.join(",", tmpReplicaList.stream().map(Replica::getName).collect(Collectors.toList())));
            isSuccess = true;
            return data;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            isAllocated = false;
            if (ex instanceof com.aliyun.apsaradb.activityprovider.ApiException) {
                return CommonProviderExceptionUtils.resourceWrapper(requestId, (com.aliyun.apsaradb.activityprovider.ApiException)ex);
            }
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (isAllocated && !isSuccess && StringUtils.isNotEmpty(tmpReplicaName)) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, tmpReplicaName);
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    private PodModifyInsParam initAddNodeParam(Map<String, String> params) throws Exception {
        PodModifyInsParam modifyInsParam = new PodModifyInsParam(dependency, params);

        // 初始化实例基础信息
        modifyInsParam
                .initCustins()
                .initReplicaSetMeta();

        // 获取用户与实例属性信息
        modifyInsParam
                .initUser()
                .initInstanceName()
                .initDBType()
                .initDBVersion()
                .initClusterName()
                .initOrderId()
                .initTmpReplicaSetName();

        // 设置业务属性标
        modifyInsParam.setIsDHG();

        modifyInsParam
                .initDiskSizeGB()               // 获取原实例磁盘大小
                .initClassCode()                // 获取原实例规格码
                .initSrcInstanceLevel()         // 获取原规格对象
                .initSrcDiskType()              // 获取原磁盘类型
                .initAutoPLConfig()              // 获取原AutoPL配置
                .setGeneralCloudDiskConfig()    // 获取主实例IO加速配置
                .setIsPfs();

        // 初始化AZ相关信息
        modifyInsParam.initOldAVZInfo();
        modifyInsParam
                .initAVZInfo()
                .initRegionId()
                .setIsModifyAvz();

        // 初始化调度信息
        modifyInsParam.initIsSrcSingleTenant()
                .initSrcScheduleTemplate();

        // 检查云盘加密
        modifyInsParam.isEncryptionKeyAvailable();
        return modifyInsParam;
    }

    /*
    校验node的class code信息， 检测单租户/多租户是否与原实例匹配
     */
    public void checkAddNodeInfos(String requestId, List<Map<String, String>> nodeInfos, boolean isSingle, boolean isArm, String dbType, String dbVersion) throws Exception {
        for (Map<String, String> nodeInfo: nodeInfos){
            String nodeClassCode = nodeInfo.getOrDefault("classCode", null);
            String zoneId = nodeInfo.getOrDefault("zoneId", null);
            if (StringUtils.isEmpty(nodeClassCode) || StringUtils.isEmpty(zoneId)){
                throw new RdsException(new Object[]{400, "InvalidParameters.Format", "addNodeInfo format error, zoneId or classCode is empty"});
            }

            InstanceLevel nodeInstanceLevel = dependency.getDBaasMetaService().getDefaultClient()
                    .getInstanceLevel(requestId, dbType, dbVersion, nodeClassCode, true);

            if (nodeInstanceLevel == null){
                log.error("format nodeinfo error, classcode : {} not found", nodeClassCode);
                throw new RdsException(new Object[]{400, "InvalidParameters.Format", "node classCode not found"});
            }
            boolean nodeIsSingle = nodeInstanceLevel.getIsolationType() != InstanceLevel.IsolationTypeEnum.COMMON;

            if (isSingle != nodeIsSingle){
                log.error("src classcode isSingle: {},  dest classcode {} isSingle: {} not match", isSingle, nodeClassCode, nodeIsSingle);
                throw new RdsException(new Object[]{400, "InvalidParameters.Format", "node classCode not found"});
            }

            boolean nodeIsArm = PodCommonSupport.isArm(nodeInstanceLevel);
            if (isArm != nodeIsArm) {
                log.error("src classCode isArm: {}, dest classCode {} isArm: {} not match", isArm, nodeClassCode, nodeIsArm);
                throw new RdsException(ErrorCode.UNSUPPORTED_CLASS_CODE);
            }
        }
    }

    private void checkClusterCanAddNodes(String requestId, String replicaSetName, List<Replica> replicas, List<Map<String, String>> nodeInfos) throws Exception {
        Integer addNum = nodeInfos.size();
        if (replicas.size() >= 9 || (replicas.size() + addNum) > 9) {
            logger.error("replicaSet {} slave node or after added node number is more than 9 , not permit extend node.", replicaSetName);
            throw new RdsException(ErrorCode.INVALID_NODE_NUMBER);
        }
        boolean isMgr = replicaSetService.isMgr(requestId, replicaSetName);
        // mgr检查节点数大于等于3，且为单数，且各节点规格一致。
        if (isMgr) {
            int finalNodeNum = replicas.size() + addNum;
            if (finalNodeNum < 3 || finalNodeNum % 2 != 1) {
                logger.error("replicaSet {} is mgr, slave node number must be  even number, not permit extend mgr", replicaSetName);
                throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_NODE_NUM);
            }
            Set<String> classCodes = new HashSet<>();
            for (Replica r : replicas) {
                classCodes.add(r.getClassCode());
            }
            for (Map<String, String> newNode : nodeInfos) {
                classCodes.add(newNode.get("classCode"));
            }
            if (classCodes.size() > 1) {
                throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_INVALID_NODE_CLASS_CODE);
            }
        }
    }

    /**
     * 判断订单是否处理过
     * */
    private boolean isOrderProcessed(String orderId, String replicaSetName, List<Replica> replicas) throws ApiException {
        for (Replica replica : replicas) {
            Map<String, String> replicaLabels = dBaasMetaService.getDefaultClient().listReplicaLabels(RequestSession.getRequestId(), replicaSetName, replica.getId());
            if (StringUtils.isNotEmpty(orderId) && orderId.equalsIgnoreCase(replicaLabels.getOrDefault(ParamConstants.ORDERID, null))) {
                return true;
            }
        }
        return false;
    }

    private List<Map<String, String>> getRoleInfoMap(List<Map<String, String>> nodeInfos){
        Integer nodeNum = nodeInfos.size();
        List<Replica.RoleEnum> roles = new ArrayList<Replica.RoleEnum>() {{
            this.add(Replica.RoleEnum.MASTER);
            for (int i = 0; i < nodeNum - 1; i++) {
                this.add(Replica.RoleEnum.SLAVE);
            }
        }};

        List<Map<String, String>> result = new ArrayList<>();
        for(Map<String, String> nodeInfo: nodeInfos){
            nodeInfo.put("role",  roles.get(0).getValue());
            result.add(nodeInfo);
            roles.remove(0);
        }
        return result;
    }

    public AllocateTmpResourceResult allocateClusterAddNodeRes(
            String requestId,
            CustInstanceDO custins,
            PodModifyInsParam modifyInsParam,
            String category,
            List<Map<String, String>> nodeInfos
    ) throws Exception {
        val resourceRequest = new ReplicaSetResourceRequest();

        val tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName();
        val currentReplicas = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(
                        modifyInsParam.getRequestId(),
                        modifyInsParam.getDbInstanceName(),
                        null, 20, null, null
                ).getItems();

        // Why assert??
        assert currentReplicas != null;
        PodModifyInsParam.replicaNameValidate(currentReplicas, tmpReplicaSetName);
        if (StringUtils.isNotBlank(modifyInsParam.getOrderId())) {
            resourceRequest.setLabels(Collections.singletonMap(PodDefaultConstants.LABEL_ORDER_ID, modifyInsParam.getOrderId()));
        }

        resourceRequest.userId(modifyInsParam.getBid())
                .uid(modifyInsParam.getUid())
                .replicaSetName(tmpReplicaSetName)
                .dbType(modifyInsParam.getDbType())
                .dbVersion(modifyInsParam.getDbVersion())
                .classCode(modifyInsParam.getClassCode())
                .diskSize(modifyInsParam.getDiskSizeGB())
                .storageType(modifyInsParam.getSrcDiskType())
                .bizType(custins.getBizType())
                // 并不是很确定
                .catagory(category)
                .allocateDisk(false)
                // ServiceTag 与源实例保持一致
                .composeTag(minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId()))
                // 不申请 VIP（默认 lvs）
                .connType(CONN_TYPE_PHYSICAL)
                // 不申请反向 VPC
                .ignoreCreateVpcMapping(true);

        boolean isSrcSingleTenant = modifyInsParam.isSrcSingleTenant();
        if (isSrcSingleTenant) {
            // 单租户场景
            resourceRequest.setSingleTenant(true);
            resourceRequest.setEniDirectLink(false);
        }

        resourceRequest.setScheduleTemplate(modifyInsParam.getScheduleTemplate());
        podTemplateHelper.setSpecSchedulerConfigSpread(resourceRequest, modifyInsParam.getDbInstanceName());

        // 强制走单可用区，控制台如果支持再做修改
        val avzInfo = modifyInsParam.getAvzInfo();
        resourceRequest.setSubDomain(avzInfo.getRegion());
        resourceRequest.setRegionId(avzInfo.getRegionId());

        val diskSizeGB = modifyInsParam.getDiskSizeGB();
        val extendedDiskSizeGB = podParameterHelper
                .getExtendDiskSizeGBForPod(
                        modifyInsParam.getReplicaSetMeta().getBizType(),
                        false, // 云上无影响
                        diskSizeGB
                );

        val replicas = new ArrayList<ReplicaResourceRequest>();

        val masterReplica = currentReplicas.stream()
                .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER))
                .findFirst()
                .get();

        for (val nodeInfo : nodeInfos) {
            val role = nodeInfo.getOrDefault("role", "slave");
            String nodeClassCode = nodeInfo.getOrDefault("classCode", null);
            String zoneId = nodeInfo.getOrDefault("zoneId", null);

            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(modifyInsParam.getSrcDiskType());
            // 这里需要设置不同的class code
            replicaResourceRequest.setClassCode(nodeClassCode);
            replicaResourceRequest.setSingleTenant(isSrcSingleTenant);
            // 云盘赠送
            replicaResourceRequest.setDiskSize(extendedDiskSizeGB);
            replicaResourceRequest.setRole(role);
            replicaResourceRequest.setZoneId(zoneId);
            replicas.add(replicaResourceRequest);
        }
        resourceRequest.setReplicaResourceRequestList(replicas);

        podReplicaSetResourceHelper.mockReplicaSetResource(resourceRequest);

        // 设置autopl配置
        resourceRequest.setProvisionedIops(modifyInsParam.getProvisionedIops());
        resourceRequest.setBurstingEnabled(modifyInsParam.isBurstingEnabled());

        //设置IO加速配置
        resourceRequest.setGeneralCloudDisk(modifyInsParam.getGeneralCloudDisk());

        // 使用 Common 创建实例 API
        val isAllocated = commonProviderService.getDefaultApi()
                .allocateReplicaSetResourceV1(modifyInsParam.getRequestId(), tmpReplicaSetName, resourceRequest);

        // 订正元数据，设置为临时实例
        val client = dBaasMetaService.getDefaultClient();
        val replicaSet = client.getReplicaSet(requestId, tmpReplicaSetName, null);
        replicaSet.setPrimaryInsName(custins.getInsName());
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
        client.updateReplicaSet(requestId, tmpReplicaSetName, replicaSet);

        ReplicaListResult replicaListResult = client.
                listReplicasInReplicaSet(requestId, tmpReplicaSetName, null, null, null, null);
        List<Replica> newReplicas = replicaListResult.getItems();
        Map<String, String> replicaLabels = new HashMap<>();
        replicaLabels.put(ParamConstants.ORDERID, modifyInsParam.getOrderId());
        for (Replica replica : newReplicas) {
            client.updateReplicaLabels(requestId, tmpReplicaSetName, replica.getId(), replicaLabels);
        }
        podReplicaSetResourceHelper.updateReplicaName(requestId, replicaSet, nodeInfos, null);

        // 补齐 translist
        val transList = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        transList.setsCinsid(custins.getId());
        transList.setdCinsid(Objects.requireNonNull(replicaSet.getId()).intValue());
        transList.setsLevelid(custins.getLevelId());
        transList.setdLevelid(custins.getLevelId());
        transList.setsDisksize(custins.getDiskSize());
        transList.setdDisksize(diskSizeGB * 1024L);
        this.instanceIDao.createTransList(transList);

        AllocateTmpResourceResult allocateTmpResourceResult = new AllocateTmpResourceResult();
        allocateTmpResourceResult.setAllocated(isAllocated);
        allocateTmpResourceResult.setTransList(transList);
        allocateTmpResourceResult.setResourceRequest(resourceRequest);
        allocateTmpResourceResult.setReplicaSet(replicaSet);

        return allocateTmpResourceResult;
    }

    private String getTaskKey(ReplicaSet replicaSet) throws ApiException {
        if (replicaSetService.isMgr(RequestSession.getRequestId(), replicaSet.getName())) {
            return PodDefaultConstants.TASK_CLUSTER_MGR_ADD_NODE;
        } else {
            return PodDefaultConstants.TASK_CLUSTER_ADD_NODE;
        }
    }
}
