package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.CreateOssBakRestoreParam;
import com.aliyun.dba.base.response.backup.UserOssBackupFileRecordResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.service.AligroupCreateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.AliyunCreateDBInstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.aliyun.dba.base.parameter.backup.BackupSetParam;
import com.aliyun.dba.base.parameter.backup.OssBakRestoreParam;
import com.aliyun.dba.base.parameter.backup.CreateOssBakRestoreParam;
import com.aliyun.dba.base.response.backup.OssBakRestoreResponse;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.response.backup.UserOssBackupFileRecordResponse;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_STORAGE_TYPE;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteUserBackupFileRecordImpl")
public class DeleteUserBackupFileRecordImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteUserBackupFileRecordImpl.class);

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    private AliyunCreateDBInstanceService aliyunDBInstanceService;
    @Resource
    private AligroupCreateDBInstanceService aligroupDBInstanceService;
    @Resource
    private AligroupService aligroupService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private BackupService backupService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        Integer userId = mysqlParameterHelper.getAndCreateUserId();
        Map<String, Object> data = new HashMap<>();
        User user;
        try {
            user = dBaasMetaService.getDefaultClient().getUserById(requestId, userId, false);
        } catch (ApiException e) {
            throw new RdsException(new Object[]{ResultCode.CODE_SERVER, "DeleteUserBackupFileRecord: " , e.getMessage()});
        }
        String bid = user.getBid();
        String uid = user.getAliUid();
        String instanceName = paramSupport.getParameterValue(params,  "InstanceName");

        OssBakRestoreParam baseParam = OssBakRestoreParam.builder()
                .user_id(bid)
                .uid(uid)
                .requestId(requestId)
                .instanceName(instanceName)
                .build();

        try {
            OssBakRestoreResponse result= backupService.deleteOssBakRestore(baseParam);

            data.put("Success", result.getSuccess());
            data.put("InstanceName", result.getInstanceName());
            data.put("Id", result.getId());
            //实例删除后，无法发起备份集删除，备份集默认保留7天删除；用户只能看到7天以内的校验任务
            return data;
        } catch (Exception e) {
            logger.error(requestId + " DeleteUserBackupFileRecord: " + e.getMessage());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "DeleteUserBackupFileRecord: " , e.getMessage()});
        }
    }
}

