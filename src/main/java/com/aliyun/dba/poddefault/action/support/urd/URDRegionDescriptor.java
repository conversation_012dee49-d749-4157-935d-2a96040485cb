package com.aliyun.dba.poddefault.action.support.urd;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class URDRegionDescriptor {
    private String region;
    private String regionId;
    private Integer regionWeight;
    private Map<String, URDZoneDescriptor> zones;

    public URDRegionDescriptor(String region, String regionId, Integer regionWeight) {
        this.region = region;
        this.regionId = regionId;
        this.regionWeight = regionWeight;
        zones = new HashMap<>();
    }

    public void addZone(URDZoneDescriptor zoneDescriptor) {
        if (zoneDescriptor == null) {
            return;
        }

        String zoneId = zoneDescriptor.getZoneId();
        if (!zones.containsKey(zoneId)) {
            zones.put(zoneId, zoneDescriptor);
            return;
        }

        URDZoneDescriptor existed = zones.get(zoneId);
        zoneDescriptor.getInstances().forEach(existed::addInstance);
        if (zoneDescriptor.getZoneWeight() > existed.getZoneWeight()) {
            existed.setZoneWeight(zoneDescriptor.getZoneWeight());
        }
    }

    public List<URDZoneDescriptor> getZoneList() {
        if (zones.isEmpty()) {
            return new ArrayList<>();
        }
        return new ArrayList<>(zones.values());
    }
}
