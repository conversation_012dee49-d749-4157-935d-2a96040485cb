package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES_INFO;
import static com.aliyun.dba.custins.support.CustinsSupport.CLUSTER_LEVEL;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.property.ErrorCode.*;

/**
 * <AUTHOR> on 2024/2/1.
 */
@Service
public class ModifyDBInstanceOptimizedWritesService extends BaseModifyDBInstanceService {
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private  ModifyDBInstanceDiskShrinkServiceV2 modifyDBInstanceDiskShrinkServiceV2;

    /**
     * Modify instance
     *
     * @param primaryOptimizedWritesInfo
     * @param targetOptimizedWrites
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doActionRequest(String primaryOptimizedWritesInfo, boolean targetOptimizedWrites, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        boolean isSuccess = false;
        PodModifyInsParam modifyInsParam = null;
        Map<String, String> allocateReplicaSets = new HashMap<>();
        List<Integer> transList = new ArrayList<>();

        try {
            // Initialize modify parameters
            modifyInsParam = initPodModifyInsParam(params);
            CustInstanceDO custins = modifyInsParam.getCustins();

            /********** filter condition Start for all situations**********/
            podCommonSupport.checkOptimizedWritesCondition(requestId, modifyInsParam.getDbVersion(), modifyInsParam.getSrcDiskType());

            //It is necessary to disable optimized write for read-only instances and enable optimized write for the main instance·。
            if (modifyInsParam.isReadIns()) {
                String primaryReplicaSetName = modifyInsParam.getReplicaSetMeta().getPrimaryInsName();
                String primaryInsOptimizedWritesInfo = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, primaryReplicaSetName, OPTIMIZED_WRITES_INFO);
                if (podCommonSupport.isOptimizedWrites(primaryInsOptimizedWritesInfo) && !targetOptimizedWrites) {
                    throw new RdsException(UNSUPPORTED_READINSTANCE);
                }
            } else {
                List<CustInstanceDO> readCustinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), true);
                for (CustInstanceDO readCustins : readCustinsList) {
                    String readInsOptimizedWritesInfo = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, readCustins.getInsName(), OPTIMIZED_WRITES_INFO);
                    if (!podCommonSupport.isOptimizedWrites(readInsOptimizedWritesInfo) && targetOptimizedWrites) {
                        throw new RdsException(UNSUPPORTED_INSTANCE_WITH_READ_INSTANCE);
                    }
                }
            }
            /********** filter condition Ens **********/


            String taskKey;
            Map<String, Boolean> optimizedWritesInfoMap = new HashMap<>();
            optimizedWritesInfoMap.put(CustinsParamSupport.OPTIMIZED_WRITES, targetOptimizedWrites);
            boolean targetInitOptimizedWrites = (modifyInsParam.getTargetInitOptimizedWritesString() != null) && Boolean.parseBoolean(modifyInsParam.getTargetInitOptimizedWritesString());
            optimizedWritesInfoMap.put(CustinsParamSupport.INIT_OPTIMIZED_WRITES, targetInitOptimizedWrites);
            String targetOptimizedWritesInfo = JSON.toJSONString(optimizedWritesInfoMap);
            if (podCommonSupport.isInitOptimizedWrites(primaryOptimizedWritesInfo)) {
                taskKey = TASK_MODIFY_OPTIMIZED_WRITES;
            } else {
                //todo: 一期不支持修改初始化写优化配置，只允许新增实例开关写优化
                throw new RdsException(INVALID_EXTERNAL_PARAMETER);
//                taskKey = TASK_MODIFY_OPTIMIZED_WRITES_INFO;
            }

//            // 初始化迁移磁盘缩需要的参数
//            Long destReplicaId = null;
//            Long destSlaveReplicaId = null;
//            ReplicaSet replicaSet = new ReplicaSet();
//            Replica targetReplica = new Replica();
//            TransferTask transTask = null;
//            String replicaSetName = null;
//            Integer transTaskId = null;
//            int extendedDestDiskSizeGB = podParameterHelper.getExtendDiskSizeGBForPod(modifyInsParam.getReplicaSetMeta().getBizType(), modifyInsParam.isSingleNode(), modifyInsParam.getDiskSizeGB());

//            if (TASK_MODIFY_OPTIMIZED_WRITES_INFO.equals(taskKey)) {
//                // 灰度开关
//                if (!podParameterHelper.isSupportShrink()) {
//                    throw new RdsException(UNSUPPORTED_REDUCE_DISK_SIZE);
//                }
//
//                ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName());
//
//                // 不支持缩容同时进行
//                if (!modifyInsParam.isAliyun() || modifyInsParam.isShrinkIns()) {
//                    logger.error("not support shrink, isAliyun:{}, isShrinkIns:{}", modifyInsParam.isAliyun(), modifyInsParam.isShrinkIns());
//                    throw new RdsException(UNSUPPORTED_REDUCE_DISK_SIZE);
//                }
//
//                // 缩容次数校验,可按照uid加白
//                podParameterHelper.checkShrinkLimit(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), modifyInsParam.getUid());
//
//                // 检查可缩容云盘类型
//                if (!SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(modifyInsParam.getSrcDiskType()) || !SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(modifyInsParam.getTargetDiskType())) {
//                    throw new RdsException(INVALID_STORAGE);
//                }
//
//                // 检查磁盘类型变更
//                if (modifyInsParam.isDiskTypeChange()) {
//                    throw new RdsException(UNSUPPORTED_CHANGE_STORAGE_TYPE);
//                }
//
//                // 不允许没达标的集团TDDL实例缩容
//                boolean isTddlTaskMigrate = replicaSetService.isTddlTaskMigrate(requestId, modifyInsParam.getReplicaSetMeta());
//                if (modifyInsParam.isTDDL() && !isTddlTaskMigrate) {
//                    logger.error("not support TDDL shrink when tddl task migrate label is false");
//                    throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
//                }
//
//                // 不允许架构迁移
//                boolean isArchChange =  PodCommonSupport.isArchChange(modifyInsParam.getSrcInstanceLevel(), modifyInsParam.getTargetInstanceLevel());
//                if (isArchChange) {
//                    throw new RdsException(UNSUPPORTED_TARGET_CLASS_CODE);
//                }
//
//                // 不允许同时category改变，暂不支持serverless
//                if (modifyInsParam.getSrcInstanceLevel().getCategory() != modifyInsParam.getTargetInstanceLevel().getCategory() || !SHRINK_SUPPORT_CATEGORY_LIST_V2.contains(modifyInsParam.getSrcInstanceLevel().getCategory())) {
//                    throw new RdsException(UNSUPPORTED_TARGET_CLASS_CODE);
//                }
//
//                // 不允许同时迁移
//                String migratingAvz = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false");
//                if (Boolean.parseBoolean(migratingAvz)) {
//                    logger.error("not support shrink and migrate at the same time");
//                    throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
//                }
//
//                replicaSetName = modifyInsParam.getReplicaSetMeta().getName();
//                replicaSet = modifyInsParam.getReplicaSetMeta();
//                ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null);
//
//                // 集群版mgr条件校验
//                if (replicaSetService.isMgr(requestId, modifyInsParam.getDbInstanceName())) {
//                    replicaSetService.isSupportMgr(listReplicasInReplicaSet.getItems().size(), modifyInsParam.getTargetInstanceLevel(), null);
//                }
//
//               // 适配集群版下同role可有多个replica
//                Map<Replica.RoleEnum, List<Replica>> replicas = new HashMap<>();
//                List<Replica> slaveReplicas = listReplicasInReplicaSet.getItems().stream().filter(r-> Replica.RoleEnum.SLAVE == r.getRole()).collect(Collectors.toList());
//                replicas.put(Replica.RoleEnum.SLAVE, slaveReplicas);
//                Replica masterReplica = listReplicasInReplicaSet.getItems().stream().filter(r-> Replica.RoleEnum.MASTER == r.getRole()).findFirst().get();
//                replicas.put(Replica.RoleEnum.MASTER, Arrays.asList(masterReplica));
//
//                // 适配集群版,指定HA时切换时的源replica
//                targetReplica = CollectionUtils.isEmpty(slaveReplicas) ? masterReplica : slaveReplicas.get(0);
//
//                // 集群版可以指定优先替换某个slave
//                String targetReplicaId = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_ID, "");
//                if (CollectionUtils.isNotEmpty(slaveReplicas) && StringUtils.isNotEmpty(targetReplicaId)) {
//                    targetReplica = slaveReplicas.stream().filter(r -> StringUtils.equals(Objects.requireNonNull(r.getId()).toString(), targetReplicaId)).findFirst().orElse(null);
//                    if (null == targetReplica) {
//                        throw new RdsException(INVALID_INSTANCE_ID);
//                    }
//                }
//
//                // 资源申请
//                transTaskId = modifyDBInstanceDiskShrinkServiceV2.allocateForShrink(requestId, transList, allocateReplicaSets, modifyInsParam, targetReplica, replicas, params, extendedDestDiskSizeGB);
//                Replica destReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, allocateReplicaSets.get("master"), null, null, null, null).getItems().get(0);
//                destReplicaId = destReplica.getId();
//                if (null != allocateReplicaSets.get("slave")){
//                    Replica destSlaveReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, allocateReplicaSets.get("slave"), null, null, null, null).getItems().get(0);
//                    destSlaveReplicaId = destSlaveReplica.getId();
//                }
//
//                // 只读实例的临时实例需要配置白名单同步label
//                allocateReplicaSets.forEach((key, value) -> {
//                    try {
//                        podParameterHelper.setReadInsSgLabel(requestId, replicaSetResource.getReplicaSet(), value);
//                    } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
//                        logger.error("set read ins flush white list label fail.", requestId, e.getResponseBody());
//                    }
//                });
//                // 获取transList并更新参数
//                transTask = dBaasMetaService.getDefaultClient().getTransferTask(requestId, replicaSetName, transTaskId);
//            }


            // dispatch task
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", modifyInsParam.getRequestId());
            taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
            taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
            taskParamObject.put("optimizedWrites", podCommonSupport.isOptimizedWrites(primaryOptimizedWritesInfo));
            taskParamObject.put("targetOptimizedWrites", targetOptimizedWrites);
            taskParamObject.put("OptimizedWritesInfo", primaryOptimizedWritesInfo);
            taskParamObject.put("targetOptimizedWritesInfo", targetOptimizedWritesInfo);
            taskParamObject.put("targetInitOptimizedWrites", true);
//            if (TASK_MODIFY_OPTIMIZED_WRITES_INFO.equals(taskKey)) {
//                taskParamObject.put("destReplicaSetName", allocateReplicaSets.get("master"));
//                taskParamObject.put("destReplicaId", destReplicaId);
//                taskParamObject.put("destSlaveReplicaSetName", allocateReplicaSets.get("slave"));
//                taskParamObject.put("destSlaveReplicaId", destSlaveReplicaId);
//                taskParamObject.put("destDiskSizeMB", modifyInsParam.getTargetDiskSizeGB() * 1024);
//                taskParamObject.put("extendedDestDiskSizeGB", extendedDestDiskSizeGB);
//                taskParamObject.put("destClassCode", modifyInsParam.getTargetClassCode());
//                taskParamObject.put("performanceLevel", modifyInsParam.getTargetPerformanceLevel());
//                taskParamObject.put("srcParentReplicaSetName", modifyInsParam.getReplicaSetMeta().getPrimaryInsName());
//                taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
//                taskParamObject.put("targetReplicaName", StringUtils.equals(replicaSet.getCategory(), CLUSTER_LEVEL) ? targetReplica.getName() : null);
//
//                Map<String, Object> transParams = StringUtils.isEmpty(transTask.getParameter()) ? new HashMap<>() : JSON.parseObject(transTask.getParameter(), Map.class);
//                transParams.put("workflowParams", taskParamObject.toJSONString());
//                transTask.setParameter(JSONObject.toJSONString(transParams));
//                dBaasMetaService.getDefaultClient().updateTransferTask(requestId, replicaSetName, transTaskId, transTask);
//            }


            String taskParam = taskParamObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask(
                    "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    modifyInsParam.getRequestId(),
                    modifyInsParam.getDbInstanceName(),
                    TASK_MODIFY_OPTIMIZED_WRITES_INFO.equals(taskKey) ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.INS_MAINTAINING.toString()
            );

            // build response
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
            data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
            data.put("OptimizedWrites", podCommonSupport.isOptimizedWrites(primaryOptimizedWritesInfo));
            data.put("targetOptimizedWrites", targetOptimizedWrites);
            data.put("OptimizedWritesInfo", primaryOptimizedWritesInfo);
            data.put("targetInitOptimizedWrites", true);
            data.put("TaskId", taskId);
            data.put("Region", modifyInsParam.getRegionId());
            isSuccess = true;
            return data;
        } catch (RdsException re) {
            logger.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            //释放申请的资源
            if (!isSuccess && modifyInsParam != null) {
                allocateReplicaSets.forEach((key, value) -> {
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, value);
                    } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                        logger.error("{} rollback resource failed: {}", requestId, e.getResponseBody());
                    }
                });
                // 将common新增的transList记录删除
                if (CollectionUtils.isNotEmpty(transList) && null != transList.get(0)) {
                    try {
                        dBaasMetaService.getDefaultClient().deleteTransferTask(requestId, modifyInsParam.getReplicaSetMeta().getName(), transList.get(0));
                    } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
                        logger.error("{} delete trans list {} record failed: {}", requestId, transList.get(0), e.getResponseBody());
                    }
                }
            }
        }
    }

}
