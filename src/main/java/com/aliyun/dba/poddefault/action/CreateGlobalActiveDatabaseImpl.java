package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.Volume;
import com.aliyun.apsaradb.gdnmetaapi.ApiException;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstance;
import com.aliyun.apsaradb.gdnmetaapi.model.GlobalConfig;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMemberListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.GdnMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.GAD.Aes;
import com.aliyun.dba.poddefault.action.support.GAD.GADMemberInstanceParameter;
import com.aliyun.dba.poddefault.action.support.GAD.GADRole;
import com.aliyun.dba.poddefault.action.support.GAD.GadConstant;
import com.aliyun.dba.poddefault.action.support.GAD.SLRBaseService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateGlobalActiveDatabaseImpl")
public class CreateGlobalActiveDatabaseImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateGlobalActiveDatabaseImpl.class);
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private GdnMetaService gdnMetaService;
    @Resource
    private DBaasMetaService metaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    SLRBaseService slrBaseService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String bid = paramSupport.getAndCheckBID(params);
            String uid = paramSupport.getUID(params);
            String centralNodeInstanceID = paramSupport.getParameterValue(params, "centraldbinstancename");
            String centralRdsDtsAdminAccount = paramSupport.getParameterValue(params, "centralrdsdtsadminaccount");
            String centralRdsDtsAdminPassword = paramSupport.getParameterValue(params, "centralrdsdtsadminpassword");
            String centralNodeRegionID = paramSupport.getAndCheckRegionID(params);
            String desc = paramSupport.getParameterValue(params, "Description", "");
            String createType = paramSupport.getParameterValue(params, "createType");

            // 根据central节点，确认节点在数据库中存在
            ReplicaSetResource replicaSetResource = metaService.getRegionClient(centralNodeRegionID).getReplicaSetBundleResource(requestId, centralNodeInstanceID);
            if (null == replicaSetResource) {
                return ResponseSupport.createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }

            ReplicaSet centralReplicaSet = replicaSetResource.getReplicaSet();
            String dbType = centralReplicaSet.getService();
            String dbVersion = centralReplicaSet.getServiceVersion();
            ReplicaSet.ConnTypeEnum connType = centralReplicaSet.getConnType();

            //dbStorageType
            String centralDBStorageType;
            if (centralReplicaSet.getKindCode() != null && centralReplicaSet.getKindCode() == 0){
                centralDBStorageType = "local_ssd";
            }else if (centralReplicaSet.getKindCode() != null && centralReplicaSet.getKindCode() == 18){
                List<Volume> dataVolume = replicaSetResource.getReplicaResources().get(0).getVolumes().stream().filter(s->s.getName().equalsIgnoreCase("data")).collect(Collectors.toList());
                String centralDBStorageTypeTmp = dataVolume.isEmpty()?"cloud_essd":dataVolume.get(0).getStorageType().getValue();
                if (!dataVolume.isEmpty() && "cloud_essd".equalsIgnoreCase(centralDBStorageTypeTmp)){
                    String essdPLX = Objects.requireNonNull(dataVolume.get(0).getPerformanceLevel()).getValue().substring(2);
                    centralDBStorageType = "1".equals(essdPLX)?"cloud_essd":"cloud_essd"+essdPLX;
                }
                else {
                    centralDBStorageType = "cloud_essd";
                }

            }else {
                centralDBStorageType = "cloud_essd";
            }


            String gadInstanceName;

            //如果列出不支持的实例类型
            if (ReplicaSet.ConnTypeEnum.TDDL == connType) {
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseFailed.CentralNodeNotSupportGAD", "CentralNode DB not support GAD"});
            }
            String unitMembersParamsStr = paramSupport.getParameterValue(params, "UnitMemberParams", null);
            if(StringUtils.isEmpty(unitMembersParamsStr)){
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseFailed.UnitMemberParamsEmpty", "UnitMemberParams not valid"});
            }
            List<Map<String, String>> unitMembersParams;
            try {
                unitMembersParams = JSON.parseObject(unitMembersParamsStr, new TypeReference<List<Map<String, String>>>() {
                });
            } catch (Exception e) {
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseFailed.UnitMemberParamsError", "UnitMemberParams not valid"});
            }


            // 创建 GDN 实例以及主实例的 Member
            gadInstanceName = paramSupport.getParameterValue(params, "GadInstanceName", "gad-" + centralNodeInstanceID);
            //如果用户输入的GAD实例ID和中心节点拼接出来的GAD实例ID不一样，表示输入错误
            if(!gadInstanceName.equalsIgnoreCase("gad-".concat(centralNodeInstanceID))){
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "CreateGlobalActiveDatabaseFailed.GadNameNotMatchCentralNode", "the gadInstanceName not match with the central node"});
            }

            GdnInstance gdnInstance = null;
            try {
                gdnInstance = gdnMetaService.getClient().getGdnInstance(requestId, gadInstanceName, true);
            } catch (ApiException e) {
                if (!e.getResponseBody().contains("GdnInstanceNotFound")) {
                    throw e;
                }
            }
            // pre check gad member counts
            preCheckGadInstance(requestId,gdnInstance,unitMembersParams);

            preCheckMemberParams(requestId,centralNodeRegionID,centralNodeInstanceID,unitMembersParams);


            //SLR前置检查
            preCheckGADSLR(requestId,centralNodeRegionID,paramSupport.getParameterValue(params, "ramAuthParams", null));


            if(null != gdnInstance && createType.equalsIgnoreCase("gad")) {
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "CreateGlobalActiveDatabaseFailed.GadInstanceExists", "gad instance has exists,please add member!"});

            } else if(null == gdnInstance && createType.equalsIgnoreCase("gad_member")){
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "CreateGlobalActiveDatabaseFailed.GadInstanceNotExists", "gad instance not exists,please create GAD!"});

            } else if (null == gdnInstance) {
                try {
                    gdnInstance = new GdnInstance();
                    gdnInstance.setBid(bid);
                    gdnInstance.setAliUid(uid);//已经在瑶池层，替换为了mainUid；
                    gdnInstance.setInsName(gadInstanceName);
                    gdnInstance.setService(dbType);
                    gdnInstance.setServiceVersion(dbVersion);
                    gdnInstance.setBizType(GdnInstance.BizTypeEnum.ALIYUN);
                    gdnInstance.setDescription(desc);
                    gdnInstance.setStatus(ReplicaSet.StatusEnum.CREATING.toString());
                    gdnMetaService.getClient().createGdnInstance(requestId, gdnInstance);
                }catch (Exception e){
                    logger.error(requestId + "CreateGlobalActiveDatabase delete gdnmetadb info for creating GdnInstance{} failed!",gadInstanceName);
                    gdnMetaService.getClient().deleteGdnInstance(requestId, gadInstanceName, uid, bid);
                    throw e;
                }

            } else if (!gdnInstance.getStatus().equals(ReplicaSet.StatusEnum.ACTIVATION.toString())) {
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "CreateGlobalActiveDatabase.GadInstanceNotActive", "gad instance not in active"});
            } else {
                gdnInstance.setStatus(ReplicaSet.StatusEnum.REPLICA_ADDING.toString());
                gdnInstance = gdnMetaService.getClient().updateGdnInstance(requestId, gdnInstance.getInsName(), gdnInstance); // 更新状态，防止重入
                if (gdnInstance == null) {
                    throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "CreateGlobalActiveDatabase.GadInstanceNotFound", "gad instance not Found"});
                }
            }

            InstanceMemberListResult instanceMemberListResult = gdnMetaService.getClient().listMembers(requestId, gadInstanceName, gadInstanceName);
            if (instanceMemberListResult == null || instanceMemberListResult.getItems() == null) {
                throw new RdsException(new Object[]{ResultCode.CODE_SERVER, "CreateGlobalActiveDatabase.GetInstanceMemberFaied" , "get instance member list failed"});
            }
            List<InstanceMember> gdnMembers = instanceMemberListResult.getItems();

            // 没有中心节点，则创建
            if (gdnMembers.stream().noneMatch(m -> m.getRole().equals(GADRole.CENTRAL.toString()))) {
                try {
                    InstanceMember instanceMember = new InstanceMember();
                    instanceMember.setInsName(gadInstanceName);
                    instanceMember.setService(dbType);
                    instanceMember.setServiceVersion(dbVersion);
                    instanceMember.setMemberName(centralNodeInstanceID);
                    instanceMember.setMemberGroupId(gadInstanceName);
                    instanceMember.setMemberKind(InstanceMember.MemberKindEnum.REPLICASET);
                    instanceMember.setMemberRegion(centralNodeRegionID);
                    instanceMember.setRole(GADRole.CENTRAL.toString()); //需要修改CENTRAL
                    instanceMember.setStatus(ReplicaSet.StatusEnum.ACTIVATION.toString());
                    Exception ex;
                    for(int i=1;i<=GadConstant.GDN_RETRY_TIMES;i++){
                        try {
                            logger.info(requestId + " the {} times CreateGlobalActiveDatabase add member for {}!",i,gadInstanceName);
                            gdnMetaService.getClient().addMember(requestId, gadInstanceName, instanceMember);
                            break;
                        } catch (Exception e) {
                            logger.info(requestId + " Add member failed,Exception is {}!",e);
                            ex = e;
                            TimeUnit.MILLISECONDS.sleep(200);
                        }
                        if(i>=GadConstant.GDN_RETRY_TIMES){
                            throw ex;
                        }
                    }

                }catch (Exception e){
                    logger.error(requestId + "CreateGlobalActiveDatabase delete gdnmetadb info  for  creating GdnInstance {} failed!",gadInstanceName);
                    gdnMetaService.getClient().deleteGdnInstance(requestId, gadInstanceName, uid, bid);
                    throw e;
                }


            }

            // 创建unit member
            int i = 1;
            List<String> unitMemberNames = new ArrayList<>();
            for(Map<String, String> unitMemberParams:unitMembersParams){
                Map<String, String> unitMemberParameters = unitMemberParams.entrySet().stream().map(
                        e -> new AbstractMap.SimpleImmutableEntry<>(e.getKey().toLowerCase(), e.getValue())
                ).collect(Collectors.toMap(AbstractMap.SimpleImmutableEntry::getKey, AbstractMap.SimpleImmutableEntry::getValue)); //参数不区分大小写
                GADMemberInstanceParameter gadMemberInstanceParameter = new GADMemberInstanceParameter();
                gadMemberInstanceParameter.initFromCentralReplicaSet(centralReplicaSet);
                gadMemberInstanceParameter.setDBInstanceStorageType(centralDBStorageType);
                gadMemberInstanceParameter.initFromParameterMap(params);
                gadMemberInstanceParameter.initFromParameterMap(unitMemberParameters);
                String memberName = "uninitialled-unit-" + requestId.substring(0, 13) + "-" + i++; // 用RequestID保证幂等

                if(gdnMembers.stream().noneMatch(m -> m.getMemberName().equals(memberName))) {
                    try {
                        InstanceMember unitInstanceMember = new InstanceMember();

                        unitInstanceMember.setInsName(gadInstanceName);
                        unitInstanceMember.setService(dbType);
                        unitInstanceMember.setServiceVersion(dbVersion);
                        unitInstanceMember.setMemberName(memberName);
                        unitInstanceMember.setMemberGroupId(gadInstanceName);
                        unitInstanceMember.setMemberKind(InstanceMember.MemberKindEnum.REPLICASET);
                        unitInstanceMember.setMemberRegion(gadMemberInstanceParameter.getRegionId());
                        unitInstanceMember.setRole(GADRole.UNIT.toString());
                        unitInstanceMember.setStatus(ReplicaSet.StatusEnum.CREATING.toString());

                        Exception ex;
                        for(int k=1;k<=GadConstant.GDN_RETRY_TIMES;k++){
                            try {
                                logger.info(requestId + " the {} times CreateGlobalActiveDatabase add member for {}!",k,memberName);
                                unitInstanceMember = gdnMetaService.getClient().addMember(requestId, gadInstanceName, unitInstanceMember);
                                gdnMetaService.getClient().updateInstanceMemberDatas(requestId, unitInstanceMember.getMemberKind().toString(), unitInstanceMember.getMemberName(),
                                        ImmutableMap.of("CreateParameters", JSON.toJSONString(gadMemberInstanceParameter)));
                                logger.info(requestId + "CreateGlobalActiveDatabase updateInstanceMemberDatas for CreateParameters, detail: {} !",JSON.toJSONString(gadMemberInstanceParameter));
                                unitMemberNames.add(memberName);
                                break;
                            } catch (Exception e) {
                                logger.info(requestId + " Add member failed,Exception is {}!",e);
                                ex = e;
                                TimeUnit.MILLISECONDS.sleep(200);
                            }
                            if(i>=GadConstant.GDN_RETRY_TIMES){
                                throw ex;
                            }
                        }

                    }catch (Exception e){
                        gdnMetaService.getClient().deleteMember(requestId, gadInstanceName, InstanceMember.MemberKindEnum.REPLICASET.toString(), memberName);
                        logger.error(requestId + "CreateGlobalActiveDatabase delete gdnmember metadb info for  creating member {} failed!",memberName);

                    }
                }

            }
            JSONObject taskParameters = new JSONObject();
            taskParameters.put("centralNodeInstanceId", centralNodeInstanceID);
            taskParameters.put("gadInstanceName", gadInstanceName);
            taskParameters.put("centralRdsDtsAdminAccount", centralRdsDtsAdminAccount);
            taskParameters.put("centralRdsDtsAdminPassword", centralRdsDtsAdminPassword);
            taskParameters.put("unitMemberNames", String.join(",", unitMemberNames));

            Object taskId = workFlowService.dispatchTask("custins", centralNodeInstanceID, PodDefaultConstants.DOMAIN_MYSQL, "create_gad_instance_nodes", taskParameters.toJSONString(), 0);

            Map<String, Object> data = new HashMap<>();
            data.put("gadInstanceName", gadInstanceName);
            data.put("createCount",unitMemberNames.size());
            data.put("taskId", taskId);
            return data;
        } catch (com.aliyun.apsaradb.gdnmetaapi.ApiException e) {
            logger.error(requestId + " Gdn meta error: " + e.getResponseBody());
            return createErrorResponse(new Object[]{ResultCode.CODE_ERROR, "RequestGdnMetaFailed", "Request gdn meta api failed."});
        }catch (RdsException re){
            logger.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        }
        catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private void preCheckGadInstance(String requestId,GdnInstance gdnInstance,List<Map<String, String>> unitMembersParams) throws RdsException, ApiException {
        // check count of node， cannot greater then 11 （1central，10unit）
        if(null == gdnInstance){
            logger.info("preCheckCreateGadInstance new gad gadMemberCount:{}",unitMembersParams.size());
            if(unitMembersParams.size() > GadConstant.GAD_UNIT_COUNT){
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseImpl.preCheckGadInstance", "exceed max member count 10"});
            }
        }else {
            InstanceMemberListResult unitList = gdnMetaService.getClient().listMembers(requestId, gdnInstance.getInsName(), gdnInstance.getInsName());
            Integer totalCount = unitList.getItems().size() + unitMembersParams.size();
            logger.info("preCheckCreateGadInstance  add gad member,totalCount:{}",totalCount);
            logger.info("preCheckCreateGadInstance  add gad member,rawCount:{},newCount{}",unitList.getItems().size(),unitMembersParams.size());

            if(totalCount > GadConstant.GAD_UNIT_COUNT + 1){
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseImpl.preCheckGadInstance", "exceed max member count 10"});
            }
        }

    }

    private void preCheckMemberParams(String requestId,String centralNodeRegionID,String centralNodeInstanceID,List<Map<String, String>> unitMembersParams) throws RdsException, com.aliyun.apsaradb.dbaasmetaapi.ApiException {

        // diskSize
        ReplicaSet centralRdsInfo = metaService.getRegionClient(centralNodeRegionID).getReplicaSet(requestId,centralNodeInstanceID,true);
        if(null == centralRdsInfo){
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseImpl.preCheckMemberParams", "centralNode not exists"});
        }

        // 如果存在存储大小，表示自定义配置
        if(unitMembersParams.get(0).containsKey("dBInstanceStorage")){
            for(Map<String,String> unitMember:unitMembersParams){
                //1、获取规格，查询规格类型对应的host_type
                InstanceLevel instanceLevel = metaService.getRegionClient(centralNodeRegionID).getInstanceLevel(
                        requestId, unitMember.get("engine"),
                        unitMember.get("engineVersion"),
                        unitMember.get("dBInstanceClass"),true);
                if(null==instanceLevel){
                    logger.error("preCheckMemberParams ,instanceLevel for dbClass{} not exists",unitMember.get("dBInstanceClass"));
                    throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseImpl.preCheckMemberParams", "instanceLevel not exists!"});

                }

                //2、磁盘空间小于中心节点，抛出异常；
                if(Integer.parseInt(unitMember.get("dBInstanceStorage")) < Objects.requireNonNull(centralRdsInfo.getDiskSizeMB())/1024){
                    logger.error("preCheckCreateGadInstance  preCheckMemberParams ,input dBInstanceStorage:{} too small than centralInstance:{}",unitMember.get("dBInstanceStorage"),centralRdsInfo.getDiskSizeMB()/1024);
                    throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseImpl.preCheckMemberParams", "dBInstanceStorage should greater than centralInstance"});
                }

                //3、检查磁盘和默认大小约束
                if(Objects.requireNonNull(instanceLevel.getHostType()) == 2) { //cloud-disk must greate than 20G
                    unitMember.put("diskType","cloud");
                    if (Integer.parseInt(unitMember.get("dBInstanceStorage")) < GadConstant.CLOUD_DISK_MIN_SIZE) {
                        logger.error("preCheckCreateGadInstance  preCheckMemberParams ,diskType is cloud, input dBInstanceStorage too small",unitMember.get("dBInstanceStorage"));
                        throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseImpl.preCheckMemberParams", "diskType is cloud,dBInstanceStorage smaller than min"});

                    }
                }else {// local-disk must greater than 5G；
                    unitMember.put("diskType","local_ssd");
                    if (Integer.parseInt(unitMember.get("dBInstanceStorage")) < GadConstant.LOCAL_DISK_MIN_SIZE) {
                        logger.error("preCheckCreateGadInstance  preCheckMemberParams ,diskType is local_ssd, input dBInstanceStorage too small",unitMember.get("dBInstanceStorage"));
                        throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "CreateGlobalActiveDatabaseImpl.preCheckMemberParams", "diskType is local_ssd,dBInstanceStorage smaller than min"});
                    }
                }
            }
        }
        logger.info("preCheckCreateGadInstance  preCheckMemberParams unitMembersParams is{}",JSON.toJSONString(unitMembersParams));

    }

    private void preCheckGADSLR(String requestId,String regionId,String ramAuthParams) throws ApiException, RdsException {
        //if can create assume role,means has slr.
        //if can not create assume,then create slr. if failed means no permission to create slr;

        if(StringUtils.isEmpty(ramAuthParams)){
            throw new RdsException(new Object[]{ResultCode.CODE_NOTFOUND, "CreateGlobalActiveDatabaseImpl preCheckGADSLR get ramAuthParams Failed", "ramAuthParams is null"});
        }
        Map<String,Object> ramAuthParamsMap;
        try{
            ramAuthParamsMap = JSON.parseObject(ramAuthParams,Map.class);

        }catch (Exception e){
            logger.error(requestId + "CreateGlobalActiveDatabaseImpl preCheckGADSLR get ramAuthParamsMap Failed:{},ramAuthParams:{}",e.toString(),ramAuthParams);
            throw new RdsException(new Object[]{ResultCode.CODE_SERVER, "CreateGlobalActiveDatabaseImpl preCheckGADSLR get ramAuthParamsMap Failed", "get ramAuthParamsMap Failed"});
        }


        //获取GAD服务账号权限
        GlobalConfig gadSLRAccountCfg = gdnMetaService.getClient().getGlobalConfig(requestId,GadConstant.GAD_SLR_ACCOUNT);
        Map<String,String> gadSLRAccount = JSON.parseObject(gadSLRAccountCfg.getValue(),Map.class);

        //解密账号
        String gadAccessKey;
        String gadAccessSecret;
        try{
            gadAccessKey = Aes.decryptPassword(gadSLRAccount.get("accessKey"), GadConstant.GAD_SLR_ACCOUNT_PASSWORD_KEY);
            gadAccessSecret = Aes.decryptPassword(gadSLRAccount.get("accessSecret"), GadConstant.GAD_SLR_ACCOUNT_PASSWORD_KEY);
        }catch (Exception e){
            logger.error(requestId + "CreateGlobalActiveDatabaseImpl preCheckGADSLR decryptPassword Failed:{}",e.toString());
            throw new RdsException(new Object[]{ResultCode.CODE_SERVER, "CreateGlobalActiveDatabaseImpl preCheckGADSLR decryptPassword Failed", "decryptPassword For SLR Failed"});
        }


        //首先尝试获取扮演
        String roleArn = "acs:ram::"+ramAuthParamsMap.get("mainUid")+":role/"+ GadConstant.GAD_SLR_NAME;
//        String roleArn = "acs:ram::****************:role/assumroletest";
        String roleSessionName = GadConstant.ROLESESSIONNAME;
        logger.info(requestId + "CreateGlobalActiveDatabaseImpl preCheckGADSLR Create assumeRole for user:{} ,roleArn:{},roleSessionName:{}",ramAuthParamsMap.get("mainUid"),roleArn,roleSessionName);

        try {
            AssumeRoleWithServiceIdentityResponse res = slrBaseService.createAssumeRole(regionId,gadAccessKey,gadAccessSecret,roleArn,roleSessionName,String.valueOf(ramAuthParamsMap.get("mainUid")));
            logger.info(requestId + "preCheckGADSLR invoke api createAssumeRole result:{}",JSON.toJSONString(res));
        }catch (Exception e){
            if(e.toString().contains("EntityNotExist.Role")){
                // 调用创建角色
                logger.info(requestId + "preCheckGADSLR EntityNotExist.Role , invoke api create slr for callser user:{}",ramAuthParamsMap.get("mainUid"));
                try{
                    slrBaseService.createGadSlrForCallerUser(requestId, String.valueOf(ramAuthParamsMap.get("mainUid")),String.valueOf(ramAuthParamsMap.get("callerUid")), ramAuthParamsMap);
                }catch (Exception slrExce){
                    logger.error(requestId + "preCheckGADSLR EntityNotExist.Role , invoke api create slr for callser user:{}",slrExce.toString());
                    throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "CreateGlobalActiveDatabaseImpl preCheckGADSLR Failed", "createGadSlrForCallerUser failed , may be no permission!"});
                }
            }else{
                logger.error(requestId + "preCheckGADSLR EntityNotExist.Role , invoke api create slr for callser user:{}",e.toString());
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "CreateGlobalActiveDatabaseImpl preCheckGADSLR Failed", "create Assume Role failed"});
            }
        }


    }



}
