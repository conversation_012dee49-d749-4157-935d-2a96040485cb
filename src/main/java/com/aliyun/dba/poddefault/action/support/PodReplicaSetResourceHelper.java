package com.aliyun.dba.poddefault.action.support;

import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointServiceFactoryImpl;
import com.aliyun.dba.poddefault.action.support.modules.MysqlReplicaResourceRequest;
import com.aliyun.dba.poddefault.action.support.urd.URDWalker;
import com.aliyun.dba.poddefault.action.support.urd.URDZoneDescriptor;
import com.aliyun.dba.poddefault.action.support.urd.UniformResourceDescriptor;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 */
@Component
public class PodReplicaSetResourceHelper {
    private static final Logger logger = Logger.getLogger(PodReplicaSetResourceHelper.class);

    @Value("${rds.server_env:no config}")
    private String serverEnv;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private MySQLServiceImpl mySQLService;
    @Resource
    private RundPodSupport rundPodSupport;
    @Resource
    private EndPointServiceFactoryImpl endPointServiceFactory;

    /**
     * ATP测试环境，单租户实例配置ENI = True
     * */
    public void mockReplicaSetResource(ReplicaSetResourceRequest replicaSetResourceRequest) {
        try {
            if (isAtpEnv()
                    && replicaSetResourceRequest.getSingleTenant() != null && replicaSetResourceRequest.getSingleTenant()
                    && PodParameterHelper.isAliYun(replicaSetResourceRequest.getBizType())) {
                replicaSetResourceRequest.setEniDirectLink(true);
                for (ReplicaResourceRequest replicaResourceRequest : replicaSetResourceRequest.getReplicaResourceRequestList()) {
                    replicaResourceRequest.setVswId(replicaSetResourceRequest.getVswitchID());
                }
            }
        } catch (Exception e) {
            logger.error("mock replica set failed.", e);
        }
    }

    /**
     * ATP测试环境，单租户实例配置ENI = True
     * */
    public void mockReplicaSetResource(ModifyReplicaSetResourceRequest replicaSetResourceRequest, ReplicaSet replicaSet) {
        try {
            if (isAtpEnv()
                    && replicaSetResourceRequest.getSingleTenant() != null && replicaSetResourceRequest.getSingleTenant()
                    && PodParameterHelper.isAliYun(replicaSet.getBizType())) {
                replicaSetResourceRequest.setEniDirectLink(true);
                for (ModifyReplicaResourceRequest replicaResourceRequest : replicaSetResourceRequest.getReplicaResourceRequestList()) {
                    replicaResourceRequest.setVswitchID(replicaSetResourceRequest.getVswitchID());
                }
            }
        } catch (Exception e) {
            logger.error("mock replica set failed.", e);
        }
    }

    /**
     * ATP环境
     */
    public boolean isAtpEnv() {
        return "atp".equals(serverEnv);
    }


    public RebuildReplicaResourceRequest allocateRebuildResource4Basic(ReplicaSet replicaSet, PodType podType) throws Exception {
        return this.allocateRebuildResource4Basic(replicaSet, RebuildReplicaResourceRequest.RebuildModeEnum.MIGRATE, podType);
    }

    /**
     * 基础版实例申请临时实例
     * 构建申请临时实例的请求，使用rebuild接口，避免创建云盘；
     * 云盘的创建在任务流中，创建快照 -> 创建云盘。
     *
     * */
    public RebuildReplicaResourceRequest allocateRebuildResource4Basic(ReplicaSet replicaSet, RebuildReplicaResourceRequest.RebuildModeEnum rebuildMode, PodType podType) throws Exception {
        String requestId = podParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        String hostName = podParameterHelper.getParameterValue(ParamConstants.HOST_NAME);
        boolean isDhg = mysqlParamSupport.isDHGCluster(replicaSet.getResourceGroupName());

        com.aliyun.apsaradb.dbaasmetaapi.model.Replica replica = mySQLService.getReplicaByRole(requestId, replicaSet.getName(), Replica.RoleEnum.MASTER);
        if (replica == null) {
            String errorMsg = String.format("Can not found master replica in %s", replicaSet.getName());
            logger.error(errorMsg);
            throw new RdsException(ErrorCode.DBINSTANCE_DO_NOT_HAVE_STANDBY_NODE);
        }

        String tmpReplicaSetName = String.format("tmp-%s-%s", replicaSet.getName(), (System.currentTimeMillis() / 1000L));
        final InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId,
                replicaSet.getService(), replicaSet.getServiceVersion(), replica.getClassCode(), true);
        boolean isSingleTenant = replicaSetService.isCloudSingleTenant(replicaSet.getBizType(), replica.getStorageType().toString(),
                instanceLevel, isDhg);

        RebuildReplicaResourceRequest rebuildReplicaResourceRequest = new RebuildReplicaResourceRequest();
        rebuildReplicaResourceRequest.setSingleTenant(isSingleTenant);
        if (StringUtils.isNotEmpty(hostName)) {
            EcsHost ecsHostMeta = dBaasMetaService.getDefaultClient().getEcsHostByHostName(requestId, hostName, true);
            if (ecsHostMeta == null) {
                throw new RdsException(ErrorCode.HOST_NOT_FOUND);
            }
            if (!StringUtils.equalsIgnoreCase(ecsHostMeta.getZoneId(), replica.getZoneId())) {
                //指定主机重搭，所选主机不能跨可用区
                throw new RdsException(ErrorCode.INVALID_AVZONE);
            }
            rebuildReplicaResourceRequest.setHostName(hostName);
        }


        rebuildReplicaResourceRequest.setRebuildMode(rebuildMode);

        rebuildReplicaResourceRequest.setTmpReplicaSetName(tmpReplicaSetName);
        //不申请反向VPC的资源
        rebuildReplicaResourceRequest.setIgnoreCreateVpcMapping(true);
        boolean isXDB = replicaSetService.isReplicaSetXDB(requestId, replicaSet.getName());
        String dbEngine = isXDB ? "XDB" : "MySQL";
        Pair<String, ScheduleTemplate> scheduleTemplatePair = podTemplateHelper
                .getBizSysScheduleTemplate(
                        podType,
                        replicaSet.getBizType(),
                        dbEngine,
                        instanceLevel,
                        isSingleTenant,
                        replicaSet.getInsType().getValue(),
                        replicaSet.getName(),
                        replicaSet.getPrimaryInsName(), podParameterHelper.getUidByLoginId(replicaSet.getUserId())
                );
        rebuildReplicaResourceRequest.setScheduleTemplate(scheduleTemplatePair.getValue());

        return rebuildReplicaResourceRequest;
    }

    /**
     * 构建ReplicaResourceRequest
     * */
    public List<ReplicaResourceRequest> getReplicaResourceRequestList(MysqlReplicaResourceRequest request, AVZInfo avzInfo) throws Exception {
        List<ReplicaResourceRequest> replicas = new ArrayList<>();
        UniformResourceDescriptor urd = new UniformResourceDescriptor(request.getInstanceLevel(), request.getAvzInfo(), Arrays.asList(request.getNodeRoles()));
        urd.walkNode(new URDWalker() {
            @Override
            public void walkNode(URDZoneDescriptor zoneDescriptor, Replica.RoleEnum role) throws Exception {
                ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                if (request.getDbEngine().equalsIgnoreCase("XDB") && role.equals(Replica.RoleEnum.LOGGER)) {
                    InstanceLevelListResult instanceLevels = dBaasMetaService
                            .getDefaultClient()
                            .listInstanceLevelChildren(request.getRequestId(), request.getDbType(), request.getDbVersion(), request.getInstanceLevel().getClassCode());
                    InstanceLevel loggerLevel = instanceLevels.getItems().stream().filter(x -> x.getClassCode().contains("logger")).collect(Collectors.toList()).get(0);
                    replicaResourceRequest.setClassCode(loggerLevel.getClassCode());
                    replicaResourceRequest.setDiskSize(loggerLevel.getDiskSizeMB() / 1024);
                } else {
                    replicaResourceRequest.setClassCode(request.getInstanceLevel().getClassCode());
                    replicaResourceRequest.setDiskSize(request.getDiskSize());
                    if (ReplicaSetService.isStorageTypeCloudDisk(request.getDiskType())) {
                        // 基于快照来数据恢复
                        VolumeSpec volumeSpec = new VolumeSpec();
                        volumeSpec.setSnapshotId(request.getSnapshotId());
                        volumeSpec.setName("data");
                        volumeSpec.setPerformanceLevel(request.getPerformanceLevel());
                        replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
                    }
                }
                replicaResourceRequest.setHostName(request.getRoleHostNameMapping().get(role));
                replicaResourceRequest.setRole(role.toString());
                replicaResourceRequest.setStorageType(request.getDiskType());
                replicaResourceRequest.setSingleTenant(request.getIsSingleTenant());
                replicaResourceRequest.setZoneId(zoneDescriptor.getZoneId());
                replicaResourceRequest.setVswId(request.getVswitchId());
                if (request.isRunD()) {
                    rundPodSupport.completeReplicaNetworkConfig(replicaResourceRequest, avzInfo);
                }
                replicas.add(replicaResourceRequest);
            }
        });
        return replicas;
    }

    /**
     * 集群版实例资源配置，对Replica进行命名，同时创建只读Endpoint
     * */
    public void configResource4Cluster(String requestId, ReplicaSet replicaSet) throws Exception {
        updateReplicaName(requestId, replicaSet);
    }

    /**
     * 实例Replica重命名
     * */
    public void updateReplicaName(String requestId, ReplicaSet replicaSet) throws ApiException {
        updateReplicaName(requestId, replicaSet, null, null);
    }

    public void updateReplicaName(String requestId, ReplicaSet replicaSet, List<Map<String, String>> nodeInfos, Replica.RoleEnum role) throws ApiException {
        Map<String, List<String>> zoneClassMapping = new HashMap<>();
        if (!Objects.isNull(nodeInfos)) {
            for (Map<String, String> nodeInfo : nodeInfos) {
                if (!Objects.isNull(nodeInfo.get("nodeId"))) {
                    String key = nodeInfo.get("zoneId") + "-" + nodeInfo.get("classCode");
                    zoneClassMapping.putIfAbsent(key, new ArrayList<>());
                    zoneClassMapping.get(key).add(nodeInfo.get("nodeId"));
                }
            }
        }

        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(requestId, replicaSet.getName(),
                        null, null, null, null);
        List<Replica> replicas = listReplicasInReplicaSet.getItems();

        for (int i = 0; i < replicas.size(); i++) {
            Replica replica = replicas.get(i);
            if (role != null && replica.getRole() != role) {
                continue;
            }
            String key = replica.getZoneId() + "-" + replica.getClassCode();
            replica.setName(generateReplicaName(replicaSet, i));
            if (!Objects.isNull(zoneClassMapping.get(key))) {
                replica.setName(zoneClassMapping.get(key).get(0));
                zoneClassMapping.get(key).remove(0);
            }
            dBaasMetaService.getDefaultClient().updateReplica(requestId, replicaSet.getName(), replica.getId(), replica);
        }

    }
    /**
     * 生成EndpointId，格式为ep-随机17位字符
     * */
    public static String generateEndpointId() {
        return "ep-" + generateString(17);
    }

    /**
     * 生成节点ID，假设实例ID为rm-xxxNN 转为 rn-xxx01 rn-xxx02 rn-xxx03
     * rn = rds node
     * */
    public static String generateReplicaName(ReplicaSet replicaSet, int i) {
        String insName = replicaSet.getInsType() == ReplicaSet.InsTypeEnum.TMP || replicaSet.getInsType() == ReplicaSet.InsTypeEnum.MIRROR ? replicaSet.getPrimaryInsName() : replicaSet.getName();
        // i 从0开始，这里转成1开始
        i += 1;
        // rm-xxx 去除rm-，保留xxx
        if (insName.contains("-")) {
            String[] insNames = insName.split("-", 2);
            insName = insNames[1];
        }

        // xxx去除后两位字符，通过序号填充
        if (insName.length() > 2) {
            insName = insName.substring(0, insName.length() - 2);
        }

        String ret = String.format("rn-%s%s", insName, i < 10 ? "0" + i : i);
        return ret;
    }

    private static String generateString(Integer length) {
        char[] genChar = new char[length];
        for (int i = 0; i < length; i++) {
            int f = (int) (Math.random() * 2 % 2);
            if (f == 1) {
                genChar[i] = (char) ('a' + Math.random() * 26);
            } else {
                genChar[i] = (char) ('0' + Math.random() * 10);
            }
        }
        return new String(genChar);
    }
}
