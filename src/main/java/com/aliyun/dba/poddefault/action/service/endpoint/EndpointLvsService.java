package com.aliyun.dba.poddefault.action.service.endpoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.metrics.StringUtils;
import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.api.VpcMappingApi;
import com.aliyun.dba.adb_vip_manager_client.model.*;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.modules.EndPointRealServer;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.RequestSession;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.RESOURCE_NOT_EXIST;

@Service
@Slf4j
public class EndpointLvsService implements EndPointService {
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private LinksApi linksApi;

    @Resource
    private ConnAddrCustinsService connAddrCustinsService;

    @Resource
    private VpcMappingApi vpcMappingApi;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private CommonProviderService commonProviderService;

    /**
     * 调用 LinksApi 删除连接地址
     * @param endpoint 连接地址
     */
    @Override
    public void delete(String requestId, Endpoint endpoint) throws Exception {
        try {
            if (endpoint.getNetType().equals(Endpoint.NetTypeEnum.VPC)) {
                vpcMappingApi.deleteVpcMapping(endpoint.getTargetName(), String.format("%s_%s", endpoint.getVip(), endpoint.getVport()), requestId);
            }
        } catch (com.aliyun.dba.adb_vip_manager_client.ApiException e) {
            if (e.getCode() != RESOURCE_NOT_EXIST) {
                log.error("delete vpc mapping error, vip:{} vport: {}", endpoint.getVip(), endpoint.getVport(), e);
                throw e;
            }
        }
        try {
            linksApi.deleteLink(endpoint.getTargetName(), endpoint.getVip() + "_" + endpoint.getVport(), requestId);
        } catch (com.aliyun.dba.adb_vip_manager_client.ApiException e) {
            if (e.getCode() != RESOURCE_NOT_EXIST) {
                log.error("delete link error， name {}, link {}", endpoint.getTargetName(), endpoint.getVip() + "_" + endpoint.getVport(), e);
                throw e;
            }
        }
    }


    private Link makeLinkBody(EndPoint endpoint, EndpointConfig endpointConfig, List<EndPointRealServer> realServerList) throws Exception {

        Link linkBody = new Link();
        linkBody.setNetType(endpoint.getNetType().ordinal());
        if (endpoint.getNetType() == NetTypeEnum.VPC) {
            linkBody.setVpcId(endpoint.getVpcId());
            linkBody.setVswitchId(endpoint.getVswId());
        }

        linkBody.setRegionName(realServerList.get(0).getRegionId());
        if (endpoint.getEndPointType().equals(EndPoint.EndPointTypeEnum.READONLY)) {
            linkBody.setRwType(Link.RwTypeEnum.READ_ONLY);
            linkBody.setRealServersPortDiffrent(1);
        }
        if (endpoint.getEndPointType().equals(EndPoint.EndPointTypeEnum.NORMAL)) {
            linkBody.setRwType(Link.RwTypeEnum.NORMAL);
            linkBody.setRealServersPortDiffrent(0);
        }
        if (endpoint.getEndPointType().equals(EndPoint.EndPointTypeEnum.RWSPLIT)) {
            linkBody.setRwType(Link.RwTypeEnum.READ_WRITE_SPLIT);
        }
        if (endpoint.getEndPointType().toString().equalsIgnoreCase(Endpoint.TypeEnum.NODE.toString())) {  // TODO: 改成metaApi的枚举
            linkBody.setRwType(Link.RwTypeEnum.NODE);
            linkBody.setRealServersPortDiffrent(0);
        }

        if (endpointConfig != null) {
            linkBody.setConnectionDrain(endpointConfig.getConnectionDrain() ? Link.ConnectionDrainEnum.ON : Link.ConnectionDrainEnum.OFF);
            if (endpointConfig.getConnectionDrain()) {
                linkBody.setConnectionDrainTimeout(endpointConfig.getConnectionDrainTimeout());
                log.info("[{}] switch connectionDrain to ON, timeout is {}", endpoint.getDomainPrefix(), endpointConfig.getConnectionDrainTimeout());
            }
        }

        linkBody.setVpcInstanceId(endpoint.getVpcInstanceId());
        linkBody.setIp(endpoint.getIp());
        linkBody.setConnAddrPrefix(endpoint.getDomainPrefix());
        linkBody.setMasterClusterName(realServerList.get(0).getSiteName());
        linkBody.setPort(Integer.valueOf(endpoint.getPort()));
        List<RealServer> linkRealServerList = new ArrayList<>();
        for (EndPointRealServer realServer : realServerList) {
            RealServer linkRealServer = new RealServer();
            linkRealServer.setEcsId(realServer.getEcsId());
            linkRealServer.setPort(Integer.valueOf(realServer.getPort()));
            linkRealServer.setIp(realServer.getIp());
            linkRealServer.setWeight(realServer.getWeight());
            linkRealServer.setVpcId(realServer.getVpcId());
            linkRealServerList.add(linkRealServer);
        }
        linkBody.setRealServers(linkRealServerList);

        log.info("makeLinkBody:params is {}", JSON.toJSONString(linkBody));

        return linkBody;
    }

    protected boolean replicaIsEniMode(String requestId, Replica replica, String replicaSetNmae) throws ApiException {
        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSetNmae, false);
        return !(replica.getLinkIp() != null && replica.getLinkIp().equalsIgnoreCase(replica.getCtrlIp()))
                || ReplicaSet.BizTypeEnum.ALIGROUP == replicaSet.getBizType();
    }

    private Endpoint getVpcLink(String requestId, String replicaSetName) throws Exception {
        LinksResponse linksResponse = linksApi.custinsCustinsNameOrIdLinksGet(replicaSetName, requestId);
        if (linksResponse != null) {
            List<LinkResponse> linkResponses = linksResponse.getData();
            for (LinkResponse linkResponse : linkResponses) {
                if (linkResponse.getNetType().equals(LinkResponse.NetTypeEnum.VPC)) {
                    return dBaasMetaService.getDefaultClient().getEndpoint(requestId, linkResponse.getConnectionAddr(), false);
                }
            }
        }
        return null;
    }

    private String getEniVpcId(String requestId, String replicaSetName, Replica replica) throws Exception {
        if (replicaIsEniMode(requestId, replica, replicaSetName)) {
            EcsHost ecsHostByHostName = dBaasMetaService.getDefaultClient().getEcsHostByHostName(requestId, replica.getHostName(), null);
            EniListResult eniListResult = dBaasMetaService.getDefaultClient().listEcsEnis(requestId, ecsHostByHostName.getEcsInsId());

            if (Objects.nonNull(eniListResult) && CollectionUtils.isNotEmpty(eniListResult.getItems())) {
                Vpod vpod = dBaasMetaService.getDefaultClient().getReplicaVpod(requestId, replica.getId(), null, null);
                Eni eni = eniListResult.getItems().stream().filter(item -> vpod.getVpodId().equalsIgnoreCase(item.getVpodId())).findAny().orElse(null);
                if (eni == null) {
                    return eniListResult.getItems().get(0).getVpcId();
                }
                return eni.getVpcId();
            }
        }

        Endpoint vpcEndPoint = getVpcLink(requestId, replicaSetName);
        if (vpcEndPoint == null) {
            log.info("ReplicaSet {} doesn't have vpc endPoint, try get vpc Info from realServer", replicaSetName);
        } else {
            return vpcEndPoint.getVpcId();
        }
        throw new Exception(requestId + "getEniVpcId error");
    }

    private EndPointRealServer getEndPointRealServer(String requestId, String replicaSetName, Replica replica, Vpod vpod, String portName, Integer weight) throws Exception {
        EndPointRealServer realServer = new EndPointRealServer();
        realServer.setIp(replica.getIp());
        realServer.setPort(getLinkPort(requestId, replica.getId(), portName).toString());
        realServer.setSiteName(vpod.getSiteName());
        realServer.setRegionId(vpod.getRegionId());
        realServer.setEcsId(vpod.getEcsInsId());
        realServer.setDbInstanceId(replicaSetName);
        realServer.setReplicaId(replica.getId());
        realServer.setMemberId(replica.getName());
        realServer.setWeight(weight);
        return realServer;
    }

    private Integer getLinkPort(String requestId, Long replicaId) throws Exception {
        List<Integer> ports = getPortByLabel(requestId, replicaId, "link");
        if (ports.size() == 1) {
            return ports.get(0);
        } else {
            throw new Exception("Link port num is error:" + ports.size());
        }
    }

    /*
     * 查找某个Replica具有的link port，支持指定port name和不指定两种场景。任何情况下 link port的个数不能超过2个
     * @Param name: 待查找的port name，可以不指定*/
    private Integer getLinkPort(String requestId, Long replicaId, String name) throws Exception{
        List<Integer> ports;
        if (Objects.nonNull(name)) {
            ports = getPortByNameAndLabel(requestId, replicaId, name, "link");
        } else {
            ports = getPortByLabel(requestId, replicaId, "link");
        }
        if (ports.size() == 1) {
            return ports.get(0);
        } else {
            throw new Exception("Link port num is error:" + ports.size());
        }
    }


    private  List<Integer> getPortByNameAndLabel(String requestId, Long replicaId, String name, String label) throws ApiException {
        List<ReplicaPort> replicaPorts = dBaasMetaService.getDefaultClient().listReplicaPorts(requestId, replicaId);
        List<Integer> ports = new ArrayList<>();
        for (ReplicaPort replicaPort: replicaPorts) {
            if (replicaPort.getName().equalsIgnoreCase(name) && replicaPort.getLabels().contains(label)) {
                ports.add(replicaPort.getPort());
            }
        }
        return ports;
    }


    private List<Integer> getPortByLabel(String requestId, Long replicaId, String label) throws ApiException {
        List<ReplicaPort> replicaPorts = dBaasMetaService.getDefaultClient().listReplicaPorts(requestId, replicaId);
        List<Integer> ports = new ArrayList<>();
        for (ReplicaPort replicaPort : replicaPorts) {
            if (replicaPort.getLabels().contains(label)) {
                ports.add(replicaPort.getPort());
            }
        }
        return ports;
    }

    /**
     * 创建私网地址
     * endpointGroup != null 时绑定
     */
    @Override
    public Endpoint createPrivateEndpointAddress(String requestId, String replicaSetName, String vpcId, String vSwitchId, String vip,
                                                 String connStringPrefix, String port, String endpointType, EndpointWeightConfig weightConfig,
                                                 EndpointGroup endpointGroup, Integer userVisible, String vpcInstanceId) throws Exception {
        EndPoint endpoint = new EndPoint();
        endpoint.setNetType(NetTypeEnum.VPC);
        endpoint.setEndPointType(EndPoint.EndPointTypeEnum.valueOf(endpointType.toUpperCase()));
        endpoint.setVpcId(vpcId);
        endpoint.setVswId(vSwitchId);
        endpoint.setIp(vip);
        endpoint.setDomainPrefix(connStringPrefix);
        endpoint.setPort(port);
        if (StringUtils.isNotBlank(vpcInstanceId)) {
            endpoint.setVpcInstanceId(vpcInstanceId);
        }

        ConfigListResult configListResult = dBaasMetaService.getDefaultClient().listConfigs(RequestSession.getRequestId(), PodDefaultConstants.CONNECTION_DRAIN_MODE);
        EndpointConfig endpointConfig = new EndpointConfig();
        if (configListResult != null && CollectionUtils.isNotEmpty(configListResult.getItems())) {
            Config config = configListResult.getItems().get(0);
            try {
                Map<String, Object> connectionDrainMode = JSON.parseObject(config.getValue(), Map.class);
                endpointConfig.setConnectionDrain(LangUtil.getBoolean(connectionDrainMode.get("mode"), false));
                if (connectionDrainMode.containsKey("timeout")) {
                    endpointConfig.setConnectionDrainTimeout(LangUtil.getInteger(connectionDrainMode.get("timeout")));
                }
            } catch (Exception e) {
                //ignore
            }
        }
        List<EndPointRealServer> realServers = this.getRealServerByWeightConfig(requestId, replicaSetName, weightConfig.getNodeWeights());
        Link linkBody = this.makeLinkBody(endpoint, endpointConfig, realServers);
        linkBody.setUserVisible(userVisible);
        LinkDetailResponse response = null;
        try {
            // create
            response = linksApi.createLink(replicaSetName, linkBody, requestId);
            // bind endpoingGroupId
            Optional<Endpoint> endpointFound = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSetName, null, null, null, null)
                    .getItems().stream().filter(ep -> ep.getAddress().split("\\.")[0].equals(connStringPrefix)).findFirst();
            if (!endpointFound.isPresent()) {
                throw new RdsException(ErrorCode.API_CALLING_FAILED);
            }
            if (endpointGroup != null) {
                endpointFound.get().setEndpointGroupId(endpointGroup.getId());
                dBaasMetaService.getDefaultClient().updateEndpoint(requestId, endpointFound.get().getAddress(), endpointFound.get());
            }
            return endpointFound.get();
        } catch (Exception ex) {
            log.error("createPrivateEndpointAddress failed: ", ex);
            if (response != null && Objects.equals(response.getStatus(), 200)) {
                Optional<Endpoint> endpointFound = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSetName, null, null, null, null)
                        .getItems().stream().filter(ep -> ep.getAddress().split("\\.")[0].equals(connStringPrefix)).findFirst();
                if (endpointFound.isPresent()) {
                    this.delete(requestId, endpointFound.get());
                }
            }

            throw new RdsException(ErrorCode.API_CALLING_FAILED, "Link api calling failed");
        }
    }

    /**
     * 创建反向 VPC
     */
    @Override
    public String createVpcMapping(String requestId, String replicaSetName, String vpcId, String vip, String vPort) throws Exception {
        VpcMappingCreationBody vpcMappingCreationBody = new VpcMappingCreationBody();
        vpcMappingCreationBody.setVpcIp(vip);
        vpcMappingCreationBody.setVpcId(vpcId);
        vpcMappingCreationBody.setVpcPort(Integer.valueOf(vPort));
        VpcMappingResponse response = vpcMappingApi.createVpcMapping(replicaSetName, vpcMappingCreationBody, requestId);
        if (response == null || response.getStatus() != 200) {
            throw new Exception("createVpcMapping failed with" + JSON.toJSONString(response.getData()));
        }
        return response.getData().getMappingIp() + "_" + response.getData().getMappingPort();
    }

    /**
     * 获取集群只读连接的 RealServer，同一个 replicaSetName，不同的 replica
     */
    private List<EndPointRealServer> getRealServerByWeightConfig(String requestId, String replicaSetName, List<EndpointWeightConfig.WeightConfig> weightConfigs) throws Exception {
        List<EndPointRealServer> realServers = new ArrayList<>();
        Map<String, Integer> weightMapping = new HashMap<>();
        for (EndpointWeightConfig.WeightConfig weightConfig : weightConfigs) {
            weightMapping.put(weightConfig.getNodeId(), weightConfig.getWeight());
        }
        List<Replica> replicas = this.getAllReplicasInReplicaSet(requestId, replicaSetName)
                .stream().filter(replica -> weightMapping.containsKey(replica.getName())).collect(Collectors.toList());
        Set<String> sites = new HashSet<>();
        for (Replica replica : replicas) {
            Vpod vpod = dBaasMetaService.getDefaultClient().getReplicaVpod(requestId, replica.getId(), null, null);
            Integer linkPort = getLinkPort(requestId, replica.getId());
            EndPointRealServer realServer = getEndPointRealServer(requestId, replicaSetName, replica, vpod, null, weightMapping.get(replica.getName()));
            realServer.setBackEndPort(linkPort);
            sites.add(vpod.getSiteName());
            realServers.add(realServer);
        }
        for (EndPointRealServer realServer : realServers) {  // 感觉没有必要
            realServer.setSites(new ArrayList<>(sites));
        }
        return realServers;
    }


    /**
     * 通过 EndpointGroup 的 WeightiConfig 来获取 RealServer
     */
    private List<EndPointRealServer> getRealServerByWeightConfig(String requestId, String replicaSetName, EndpointGroup endpointGroup) throws Exception {
        // primary 没有 weightConfig
        if (endpointGroup.getType().equalsIgnoreCase(Endpoint.TypeEnum.PRIMARY.toString())) {
            Replica replica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null)
                    .getItems().stream().filter(rp -> rp.getRole() == Replica.RoleEnum.MASTER).findFirst().get();
            Vpod vpod = dBaasMetaService.getDefaultClient().getReplicaVpod(requestId, replica.getId(), null, null);
            Integer linkPort = getLinkPort(requestId, replica.getId());
            EndPointRealServer realServer = getEndPointRealServer(requestId, replicaSetName, replica, vpod, null, 100);
            realServer.setBackEndPort(linkPort);

            List<EndPointRealServer> realServers = new ArrayList<>();
            realServers.add(realServer);
            return realServers;
        }

        EndpointWeightConfig endpointWeightConfig = this.getWeightConfig(requestId, replicaSetName, endpointGroup.getId());
        if (endpointWeightConfig == null) {
            throw new RdsException(ErrorCode.INVALID_ENDPOINT_CONFIG);
        }
        List<EndPointRealServer> realServers = new ArrayList<>();
        for (EndpointWeightConfig.WeightConfig weightConfig : endpointWeightConfig.getNodeWeights()) {
            String dbInstanceId = weightConfig.getDBInstanceId();
            String nodeId = weightConfig.getNodeId();
            Integer weight = weightConfig.getWeight();

            Optional<Replica> replicaOptional = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, dbInstanceId, null, null, null, null)
                    .getItems().stream().filter(replica -> replica.getName().equals(nodeId)).findFirst();
            if (!replicaOptional.isPresent()) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            Replica replica = replicaOptional.get();
            Vpod vpod = dBaasMetaService.getDefaultClient().getReplicaVpod(requestId, replica.getId(), null, null);
            Integer linkPort = getLinkPort(requestId, replica.getId());
            EndPointRealServer realServer = getEndPointRealServer(requestId, dbInstanceId, replica, vpod, null, weight);
            realServer.setBackEndPort(linkPort);
            realServers.add(realServer);
        }
        return realServers;
    }

    /**
     * 对比新的 weightConfig 和已有的 config，分析出 modifyWeight or ModifyRS
     */
    @Override
    public Integer compareAndCheckWeightConfigs(String requestId, String replicaSetName, EndpointGroup endpointGroup, EndpointWeightConfig newWeightConfig) throws RdsException, ApiException, InterruptedException {
        this.checkNodeItems(requestId, replicaSetName, newWeightConfig, endpointGroup.getType());

        EndpointWeightConfig weightConfig = JSON.parseObject(endpointGroup.getLabels().get(EndpointWeightConfig.CONFIG_KEY), EndpointWeightConfig.class);

        // data prepare
        Set<String> replicaNames = new HashSet<>();
        Map<String, Integer> nodeId2Weight = new HashMap<>();
        for (EndpointWeightConfig.WeightConfig config : weightConfig.getNodeWeights()) {
            replicaNames.add(config.getNodeId());
            nodeId2Weight.put(config.getNodeId(), config.getWeight());
        }
        List<String> newConfigNodeIds = newWeightConfig.getNodeWeights().stream().map(EndpointWeightConfig.WeightConfig::getNodeId).collect(Collectors.toList());
        Set<String> newReplicaNames = new HashSet<>(newConfigNodeIds);

        // quick check, 通过字符串判断是否修改了 endpoint weightConfig
        if (newWeightConfig.toString().equals(weightConfig.toString())) {
            return 0;
        }

        // 先比较 replica 是否发生变化
        if (!replicaNames.toString().equals(newReplicaNames.toString())) {
            return 2;
        }
        // 比较每一个 weight
        for (EndpointWeightConfig.WeightConfig config : newWeightConfig.getNodeWeights()) {
            if (!config.getWeight().equals(nodeId2Weight.get(config.getNodeId()))) {
                return 1;
            }
        }
        return 0;
    }

    @Override
    public void checkEndpointRSEmpty(EndpointWeightConfig weightConfig) throws RdsException {
        if (weightConfig.getNodeWeights() == null || weightConfig.getNodeWeights().isEmpty()) {
            throw new RdsException(ErrorCode.EMPTY_ENDPOINT_NODE);
        }
    }

    /**
     * 检查 ConnAddrCust 是否存在
     */
    @Override
    public Boolean checkConnAddrCustExist(String requestId, String connAddrCust) throws ApiException {
        return dBaasMetaService.getDefaultClient().getEndpoint(requestId, connAddrCust, true) != null;
    }


    @Override
    public String upperCaseFirst(String val) {
        char[] arr = val.toCharArray();
        arr[0] = Character.toUpperCase(arr[0]);
        return new String(arr);
    }

    /**
     * 检查 NodeItems 是否符合规范
     */
    @Override
    public Boolean checkNodeItems(String requestId, String dbInstanceName, EndpointWeightConfig endpointWeightConfig, String endpointType) throws ApiException, RdsException{
        if (endpointWeightConfig.getNodeWeights() == null || endpointWeightConfig.getNodeWeights().size() == 0) {
            throw new RdsException(ErrorCode.INVALID_NODEITEMS_BLANK);
        }

        List<Replica> replicas = this.getAllReplicasInReplicaSet(requestId, dbInstanceName);

        Set<String> replicaSetNameList = new HashSet<>();
        Set<String> replicaNameList = new HashSet<>();
        for (EndpointWeightConfig.WeightConfig nodeConfig : endpointWeightConfig.getNodeWeights()) {
            replicaSetNameList.add(nodeConfig.getDBInstanceId());
            replicaNameList.add(nodeConfig.getNodeId());
        }
        if (replicaNameList.size() != endpointWeightConfig.getNodeWeights().size()) {  // ensure nodeIds replicaId are all different
            throw new RdsException(ErrorCode.INVALID_NODEITEMS_DUPLICATE_NODE_ID);
        }
        if (endpointType.equalsIgnoreCase(Endpoint.TypeEnum.READONLY.toString())) {
            // 校验集群只读，replica 只能在主实例下
            if (replicaSetNameList.size() != 1) {
                throw new RdsException(ErrorCode.INVALID_NODEITEMS_RO_NODE_ID);
            }
            List<Replica> configReplicas = replicas.stream().filter(replica -> replicaNameList.contains(replica.getName())).collect(Collectors.toList());
            for (Replica replica : configReplicas) {
                if (!Objects.equals(replica.getReplicaSetName(), dbInstanceName)) {
                    throw new RdsException(ErrorCode.INVALID_NODEITEMS_RO_NODE_ID);
                }
                if (replica.getRole() == Replica.RoleEnum.MASTER) {
                    throw new RdsException(ErrorCode.INVALID_NODEITEMS_RO_NODE_ID_MASTER);
                }
            }
        }
        else if (Endpoint.TypeEnum.NODE.toString().equalsIgnoreCase(endpointType)) {  // TODO 改成MetaApi节点直连枚举
            String nodeId = endpointWeightConfig.getNodeWeights().get(0).getNodeId();
            // 校验集群节点直连地址
            if (replicaSetNameList.size() != 1 || replicaNameList.size() != 1) {  // 节点直连地址只有一个real_server
                throw new RdsException(ErrorCode.INVALID_NODEITEMS_NODE_ID);
            }

            // 一个节点只能有一个直连地址
            List<EndpointGroup> endpointGroups = dBaasMetaService.listEndpointGroups(requestId, dbInstanceName).getItems();
            if (CollectionUtils.isNotEmpty(endpointGroups)) {
                List<EndpointGroup> nodeEndpointGroups = endpointGroups.stream().filter(endpointGroup -> Endpoint.TypeEnum.NODE.toString().equalsIgnoreCase(endpointGroup.getType())).collect(Collectors.toList());
                log.info("nodeEndpointGroups: {}", JSON.toJSONString(nodeEndpointGroups));
                for (var nodeEndpointGroup : nodeEndpointGroups) {
                    EndpointGroup curEndpointGroup = getEndpointGroup(requestId, dbInstanceName, nodeEndpointGroup.getId());
                    log.info("nodeId: {}, labels: {}", nodeId, curEndpointGroup.getLabels().toString());
                    if (Objects.nonNull(curEndpointGroup.getLabels()) && Objects.equals(curEndpointGroup.getLabels().get(EndpointWeightConfig.ENDPOINT_NODE_KEY), nodeId)) {
                        throw new RdsException(ErrorCode.OTHER_ENDPOINT_EXIST);
                    }
                }
            }
        }
        else {
            throw new RdsException(ErrorCode.ENDPOINT_TYPE_NOT_SUPPORT);
        }

        //  check nodeId, dbInstanceId
        List<EndpointWeightConfig.WeightConfig> nodeItems = endpointWeightConfig.getNodeWeights();
        var sumWeight = 0;
        for (EndpointWeightConfig.WeightConfig weightConfig : nodeItems) {
            String replicaSetName = weightConfig.getDBInstanceId();
            String nodeId = weightConfig.getNodeId();
            Integer weight = weightConfig.getWeight();

            sumWeight += weight;
            // 校验 0 <= weight <= 100
            if (weight < 0 || weight > 100) {
                throw new RdsException(ErrorCode.INVALID_WEIGHT);
            }

            // DBInstanceId
            if (!dbInstanceName.equals(replicaSetName)) {  // replicaSetName 不一样的时候，针对只读组校验是否是主实例的只读实例
                throw new RdsException(ErrorCode.INVALID_NODEITEMS_INSTANCE_ID);
            }

            // 校验 NodeId，要求所有 NodeId 是集群中的 replicaName
            List<Replica> replicaList = replicas.stream().filter(replica -> replica.getName().equals(nodeId)).collect(Collectors.toList());
            if (replicaList.size() != 1) {
                throw new RdsException(ErrorCode.INVALID_NODEITEMS_NODE_ID);
            }
        }
        if (sumWeight <= 0) {
            throw new RdsException(ErrorCode.INVALID_WEIGHT);
        }
        return true;
    }

    @Override
    public List<EndpointGroup> getAllEndpointGroups(String requestId, String replicaSetName) throws Exception{
        return dBaasMetaService.listEndpointGroups(requestId, replicaSetName).getItems();
    }

    @Override
    public List<CustinsConnAddrDO> getEndpoints(String requestId, Long endpointId, Integer replicaSetId) {
        log.info("Query db to getCustinsConnAddr, requestId " + requestId);
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(replicaSetId, null, null);
        custinsConnAddrList.removeIf(connAddr -> !Objects.equals(connAddr.getEndpointId(), endpointId));
        return custinsConnAddrList;
    }

    @Override
    public List<Map<String, Object>> getMysqlClusterNodes(String requestId, EndpointGroup endpointGroup, String replicaSetName) throws ApiException, RdsException {
        List<Map<String, Object>> nodes = new ArrayList<>();
        if (Endpoint.TypeEnum.PRIMARY.toString().equalsIgnoreCase(endpointGroup.getType())) {
            Map<String, Object> node = new HashMap<>();
            Optional<Replica> replicaFound = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null)
                    .getItems().stream().filter(replica -> replica.getRole() == Replica.RoleEnum.MASTER).findFirst();
            if (!replicaFound.isPresent()) {
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            node.put("DBInstanceId", replicaSetName);
            node.put("nodeId", replicaFound.get().getName());
            node.put("Weight", 100);
            node.put("Status", "ONLINE");
            node.put("MaxReplicationLag", 0);
            nodes.add(node);
        }
        else {
            EndpointWeightConfig weightConfigs = getWeightConfig(requestId, replicaSetName, endpointGroup.getId());
            for (EndpointWeightConfig.WeightConfig weightConfig : weightConfigs.getNodeWeights()) {
                nodes.add(JSON.parseObject(JSON.toJSONString(weightConfig), Map.class));
            }
        }
        return nodes;
    }

    private EndpointGroup getEndpointGroup(String requestId, String replicaSetName, Long endpointGroupId) throws ApiException {
        EndpointGroup endpointGroup = dBaasMetaService.getDefaultClient().getEndpointGroupById(requestId, endpointGroupId, false);
        return dBaasMetaService.getDefaultClient().getEndpointGroup(requestId, replicaSetName, endpointGroup.getGroupName(), false);
    }

    @Override
    public EndpointWeightConfig getWeightConfig(String requestId, String replicaSetName, Long endpointGroupId) throws ApiException {
        EndpointGroup endpointGroup = getEndpointGroup(requestId, replicaSetName, endpointGroupId);
        if (!Objects.requireNonNull(endpointGroup.getLabels()).containsKey(EndpointWeightConfig.CONFIG_KEY)) {
            return null;
        }
        return JSONObject.parseObject(endpointGroup.getLabels().get(EndpointWeightConfig.CONFIG_KEY), EndpointWeightConfig.class);
    }

    /**
     * 通过实例的 nameList 获取所有 replica
     */
    @Override
    public List<Replica> getAllReplicasByNames(String requestId, List<String> replicaSetNameList) throws ApiException {
        Set<String> replicaSetNameSet = new HashSet<>(replicaSetNameList);
        List<Replica> replicas = new ArrayList<>();
        for (String replicaSetName : replicaSetNameSet) {
            List<Replica> replicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems();
            if (replicaList != null) {
                replicas.addAll(replicaList);
            }
        }
        return replicas;
    }

    /**
     * 获取实例连接的详细信息，weight等
     */
    @Override
    public LinkDetailResponse getRealServerDetail(String requestId, String replicaSetName, Endpoint endpoint) throws com.aliyun.dba.adb_vip_manager_client.ApiException {
        return linksApi.getLinkDetail(replicaSetName, endpoint.getVip(), requestId);
    }

    /**
     * 刷新 RealServer 的节点配置以及权重
     *      当节点(replicas)没有发生变化时，仅修改权重不会生效
     */
    @Override
    public void refreshRealServers(String requestId, String replicaSetName, Endpoint endpoint, EndpointWeightConfig weightConfig, List<Replica> replicas) throws Exception {
        ChangeRsBody changeRsBody = new ChangeRsBody();
        List<RealServer> realServers = new ArrayList<>();
        Map<String, Integer> nodeId2Weight = new HashMap<>();
        for (EndpointWeightConfig.WeightConfig config : weightConfig.getNodeWeights()) {
            nodeId2Weight.put(config.getNodeId(), config.getWeight());
        }
        for (Replica replica: replicas) {
            RealServer r = new RealServer();
            r.setIp(replica.getLinkIp());
            r.setPort(replica.getPorts().get(0));
            r.setWeight(nodeId2Weight.get(replica.getName()));
            realServers.add(r);
        }
        changeRsBody.setRealServers(realServers);
        changeRsBody.setRequestId(requestId);

        APISuccess response = linksApi.replaceBackServers(replicaSetName, endpoint.getVip(), changeRsBody, requestId);
        if (response.getStatus() != 200) {
            throw new Exception("call linksApi to replaceBackServers failed");
        }
    }

    /**
     * 更新 RealServer 的权重信息
     *      该接口仅可更新权重，不支持修改节点组合
     */
    @Override
    public void setRealServerWeight(String requestId, String replicaSetName, Endpoint endpoint, EndpointWeightConfig weightConfig, List<Replica> replicas) throws com.aliyun.dba.adb_vip_manager_client.ApiException {
        Map<String, Integer> nodeId2Weight = new HashMap<>();
        for (EndpointWeightConfig.WeightConfig config : weightConfig.getNodeWeights()) {
            nodeId2Weight.put(config.getNodeId(), config.getWeight());
        }

        for (Replica replica : replicas) {
            String rs = String.format("%s_%s", replica.getLinkIp(), replica.getPorts().get(0).toString());
            UpdateRsWeightBody body = new UpdateRsWeightBody();
            body.setWeight(nodeId2Weight.get(replica.getName()));
            linksApi.setRealServerWeight(replicaSetName, endpoint.getVip(), rs, body, requestId);
        }
    }

    /**
     * 将 replicaSet 状态置为 NET_MAINTAINING
     */
    @Override
    public void inactiveReplicaSet(String requestId, String replicaSetName) throws ApiException {
        dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetName, ReplicaSet.StatusEnum.NET_MAINTAINING.toString());
    }

    /**
     * 将 replicaSet 状态置为 ACTIVATION
     */
    @Override
    public void activeReplicaSet(String requestId, String replicaSetName) throws ApiException {
        dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetName, ReplicaSet.StatusEnum.ACTIVATION.toString());
    }

    @Override
    public EndpointWeightConfig genEndpointWeightConfig(String replicaSetName, String nodeId) {
        EndpointWeightConfig weightConfig = new EndpointWeightConfig();
        EndpointWeightConfig.WeightConfig config = new EndpointWeightConfig.WeightConfig();
        config.setDBInstanceId(replicaSetName);
        config.setNodeId(nodeId);
        weightConfig.setNodeWeights(Collections.singletonList(config));
        return weightConfig;
    }

    @Override
    public EndpointWeightConfig createPrimaryWeightConfig(String requestId, String replicaSetName) throws ApiException {
        Replica replica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems()
                .stream().filter(rp -> rp.getRole() == Replica.RoleEnum.MASTER).findFirst().get();
        return genEndpointWeightConfig(replicaSetName, replica.getName());
    }

    /**
     * 获取并校验 endpointGroup 是否存在，并抛出异常。
     * 不能用于返回 null 判断
     */
    @Override
    public EndpointGroup getAndCheckEndpointGroup(String requestId, String replicaSetName, String endpointId) throws RdsException, ApiException {
        EndpointGroup endpointGroup = null;
        try {
            endpointGroup = dBaasMetaService.getDefaultClient().getEndpointGroup(requestId, replicaSetName, endpointId, true);
        } catch (ApiException ex) {
            if (ex.getCode() != 404) {
                throw ex;
            }
        }
        if (endpointGroup == null) {
            throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
        }
        return endpointGroup;
    }

    /**
     * 获取并校验 ConnectionString，校验与 endpoint_id 的关系
     */
    @Override
    public Endpoint getAndCheckEndpoint(String requestId, String replicaSetName, String connString, Long endpointGroupId) throws ApiException, RdsException {
        Endpoint endpoint = null;
        try {
            endpoint = dBaasMetaService.getDefaultClient().getEndpoint(requestId, connString, true);
        } catch (ApiException ex) {
            if (ex.getCode() != 404) {
                throw ex;
            }
        }
        if (endpoint == null) {
            throw new RdsException(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
        }
        if (!replicaSetName.equals(endpoint.getTargetName()) || !Objects.equals(endpoint.getEndpointGroupId(), endpointGroupId)) {
            throw new RdsException(ErrorCode.INVALID_CONNECTIONSTRING);
        }
        return endpoint;
    }

    /**
     * 获取replicaSetName 下的所有 ConnectionString
     */
    @Override
    public List<Endpoint> listReplicaSetEndpoint(String requestId, String replicaSetName) throws ApiException {
        return dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSetName, null, null, null, null).getItems();
    }

    /**
     * 获取该 replicaSet 下的所有 replica，包括只读实例的 replica
     */
    @Override
    public List<Replica> getAllReplicasInReplicaSet(String requestId, String replicaSetName) throws ApiException {
        List<ReplicaSet> readOnlyReplicaSetList = dBaasMetaService.getDefaultClient().listReplicaSetSubIns(
                requestId, replicaSetName, ReplicaSet.InsTypeEnum.READONLY.toString()).getItems();

        List<Replica> replicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems();
        for (Replica replica : replicas) {
            replica.setReplicaSetName(replicaSetName);
        }

        if (readOnlyReplicaSetList != null) {
            for (ReplicaSet replicaSet : readOnlyReplicaSetList) {
                List<Replica> readOnlyReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems();
                for (Replica replica : readOnlyReplicas) {
                    replica.setReplicaSetName(replicaSet.getName());
                }
                replicas.addAll(readOnlyReplicas);
            }
        }

        return replicas;
    }

    /**
     * 为 replicaSetName 预占用一个连接串
     */
    @Override
    public String allocateConnectionString(String requestId, String replicaSetName, String connectionStringPrefix) throws com.aliyun.dba.adb_vip_manager_client.ApiException, RdsException {
        try {
            log.info("allocateConnectionString start: " + replicaSetName + ", " + connectionStringPrefix);
            ConnectionAddrWithStatus connectionAddrWithStatus = linksApi.allocateConnectionAddress(replicaSetName, new ConnStringBody().connectionPrefix(connectionStringPrefix), requestId);
            log.info("allocateConnectionString end: {}", connectionAddrWithStatus);
            if (connectionAddrWithStatus.getStatus() != 200) {
                throw new RdsException(ErrorCode.CONNECTIONSTRING_ALREADYEXISTS);
            }
            return connectionAddrWithStatus.getData().getConnectionAddr();
        } catch (com.aliyun.dba.adb_vip_manager_client.ApiException e) {
            log.error("Maybe Connection prefix has already been used.");
            throw new RdsException(ErrorCode.CONNECTIONSTRING_ALREADYEXISTS);
        }
    }

    @Override
    public EndPoint allocateEndpointResource(String requestId, String replicaSetName, String ipType,
                                             EndPoint.ConnTypeEnum connTypeEnum, String connectionStringPrefix,
                                             EndPoint.EndPointTypeEnum endPointType, String port) throws com.aliyun.apsaradb.activityprovider.ApiException {
        EndPoint endPoint = new EndPoint();
        endPoint.setNetType(NetTypeEnum.valueOf(ipType.toUpperCase()));
        endPoint.setConnType(connTypeEnum);
        endPoint.setDomainPrefix(connectionStringPrefix);
        endPoint.setEndPointType(endPointType);
        endPoint.setPort(port);
        return commonProviderService.getDefaultApi().allocateEndPointResource(requestId, replicaSetName, endPoint);
    }

    @Override
    public void releaseConnectionString(String requestId, String replicaSetName, String connectionString) throws com.aliyun.dba.adb_vip_manager_client.ApiException {
        linksApi.releaseConnectionAddress(replicaSetName, connectionString, requestId);
    }

    @Override
    public void checkConnectionStringPrefix(String requestId, ReplicaSet replicaSet, String connectionStringPrefix) throws RdsException, ApiException {
        CheckUtils.checkValidForConnAddrCust(connectionStringPrefix);
        String connAddrCust = paramSupport.getConnAddrCust(connectionStringPrefix, replicaSet.getService());
        if (this.checkConnAddrCustExist(requestId, connAddrCust)) {
            throw new RdsException(ErrorCode.CONNECTIONSTRING_ALREADYEXISTS);
        }
    }

    @Override
    public void checkValidForVswId(String vswId) throws RdsException {
        if (!(StringUtils.isNotBlank(vswId) && Pattern.compile("^vsw-[a-z0-9]+$").matcher(vswId).matches())) {
            throw new RdsException(ErrorCode.INVALID_VSWITCH_ID);
        }
    }

    @Override
    public void checkValidForVpcId(String vpcId) throws RdsException {
        if (!(StringUtils.isNotBlank(vpcId) && Pattern.compile("^vpc-[a-z0-9]+$").matcher(vpcId).matches())) {
            throw new RdsException(ErrorCode.INVALID_VPC_ID);
        }
    }
}
