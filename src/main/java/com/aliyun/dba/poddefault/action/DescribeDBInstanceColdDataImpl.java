package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.ColdDataService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeDBInstanceColdDataImpl")
public class DescribeDBInstanceColdDataImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceColdDataImpl.class);
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected ColdDataService coldDataService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        try {
            //设置参数
            logger.info("DescribeDBInstanceColdDataImpl params : {}", params);
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            int pageNum = Integer.parseInt(mysqlParamSupport.getParameterValue(params, ParamConstants.PAGE_NUMBERS, "1"));
            int pageSize = Integer.parseInt(mysqlParamSupport.getParameterValue(params, ParamConstants.MAX_RECORDS_PER_PAGE, "500"));
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>>coldTables =  coldDataService.describeColdDataFromDboss(dbInstanceName, pageNum, pageSize);
            long totalSize = 0L;
            int totalCount = 0;
            for(Map<String,Object>coldTable : coldTables){
                totalSize = ((Number) coldTable.getOrDefault("totalSize", 0)).longValue();
                totalCount = ((Number) coldTable.getOrDefault("totalCount", 0)).intValue();
                break;
            }
            result.put("totalSize", totalSize);
            result.put("totalCount", totalCount);
            result.put(ParamConstants.PAGE_NUMBERS, pageNum);
            result.put(ParamConstants.MAX_RECORDS_PER_PAGE, pageSize);
            result.put("coldTables", coldTables);
            return result;
        } catch (RdsException re) {
            logger.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.warn(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
