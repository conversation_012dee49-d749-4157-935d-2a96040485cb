package com.aliyun.dba.poddefault.action.service.migrate.xdb;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.TransferTask;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.migrate.BaseMigrateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

/**
 * XDB 三节点迁移
 */
@Service
public class XDBMigrateDBInstanceService extends BaseMigrateDBInstanceService {
    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Resource
    protected CustinsParamService custinsParamService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Override
    public Object allocateResource(String requestId, ReplicaSet replicaSet, JSONObject taskParam, Map<String, String> params) throws Exception{
        ModifyReplicaSetResourceRequest replicaSetResourceRequest = null;
        boolean isSuccess = false;
        Integer transListId = null;
        try {
            PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(params);
            CustInstanceDO custins = modifyInsParam.getCustins();

            // 变配逻辑
            ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient()
                    .getReplicaSetBundleResource(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName());
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                    .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                            null, null, null, null);

            // 记录哪些节点申请了资源，用于在N副本场景下感知节点对应关系
            List<String> modifyReplicas = new ArrayList<>();

            String tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName();
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            assert currentReplicas != null;
            PodModifyInsParam.replicaNameValidate(currentReplicas, tmpReplicaSetName);

            boolean isForce = mysqlParamSupport.getAndCheckIsForce(params).equals(1);
            boolean isTransByNc = !modifyInsParam.isCloudDisk() || isForce;
            boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();

            replicaSetResourceRequest = baseModifyDBInstanceService.getModifyReplicaSetResourceRequest(modifyInsParam, tmpReplicaSetName);
            if (isTransByNc) {
                // NC时强制申请新盘
                replicaSetResourceRequest.reuseCloudDisk(false);
            }

            //检查实例是否指定资源调度模版创建
            PodScheduleTemplate podScheduleTemplate = getPodScheduleTemplate(requestId,replicaSetResource.getReplicaSet());
            if (podScheduleTemplate != null) {
                replicaSetResourceRequest.setScheduleTemplate(podTemplateHelper.getReplicaSetScheduleTemplate(podScheduleTemplate));
            }

            List<ModifyReplicaResourceRequest> replicaRequestList = baseModifyDBInstanceService.buildModifyReplicaResourceRequests(
                    modifyInsParam, currentReplicas, isTargetSingleTenant, podScheduleTemplate, modifyReplicas);

            if (isTargetSingleTenant) {
                replicaSetResourceRequest.setSingleTenant(true);
                replicaSetResourceRequest.setEniDirectLink(false); //单租户不需要申请ENI网卡直连
            }

            replicaSetResourceRequest.setDiskSize(modifyInsParam.getTargetDiskSizeGB());
            replicaSetResourceRequest.setVswitchID(mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID, null));
            replicaSetResourceRequest.setReplicaResourceRequestList(replicaRequestList);
            replicaSetResourceRequest.setModifyMode(modifyInsParam.getModifyMode());
            replicaSetResourceRequest.setReplicaResourceRequestList(replicaRequestList);
            replicaSetResourceRequest.setInsType(ReplicaSet.InsTypeEnum.MIRROR.getValue());
            if (modifyInsParam.getIsEmergencyTransfer()) {
                replicaSetResourceRequest.setModifyMode(ModifyReplicaSetResourceRequest.ModifyModeEnum.MIGRATE);
            }
            String minorVersion = replicaSetResource.getReplicaSet().getLabels().get("minor_version");
            String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                    minorVersion,
                    ReplicaSet.BizTypeEnum.ALIGROUP,
                    modifyInsParam.getDbType(),
                    modifyInsParam.getDbVersion(),
                    "XDB",
                    KindCodeParser.KIND_CODE_NEW_ARCH,
                    modifyInsParam.getTargetInstanceLevel(),
                    modifyInsParam.getTargetDiskType(),
                    modifyInsParam.isDHG(),
                    false,
                    null);

            replicaSetResourceRequest.setComposeTag(serviceSpecTag);
            replicaSetResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
            transListId = commonProviderService.getDefaultApi().allocateReplicaSetResourceForScale(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), replicaSetResourceRequest);
            custinsParamService.updateAVZInfo(custins.getId(), modifyInsParam.getAvzInfo());

            String taskKey = "tddl_migrate_local_to_cloud";

            TransferTask transferTask = dBaasMetaService.getDefaultClient().getTransferTask(modifyInsParam.getRequestId(), modifyInsParam.getReplicaSetMeta().getName(), transListId);
            Optional<Replica> masterReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(modifyInsParam.getRequestId(), transferTask.getDestReplicaSetName(), null, null, null, null).getItems().stream().filter(r -> r.getRole().equals(Replica.RoleEnum.MASTER)).findFirst();


            // dispatch task
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", modifyInsParam.getRequestId());
            taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
            taskParamObject.put("transTaskId", transListId);
            taskParamObject.put("emergencyTransfer", modifyInsParam.getIsEmergencyTransfer());
            if (modifyInsParam.getRoleHostNameMapping().containsKey(Replica.RoleEnum.MASTER)) {
                taskParamObject.put("preferMasterReplicaId", masterReplica.map(Replica::getId).orElse(null));
            }
            taskParamObject.put("preferMasterZoneId", modifyInsParam.getAvzInfo().getMasterZoneId());
            taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
            taskParamObject.put("isForceNc", mysqlParamSupport.getAndCheckIsForce(params));
            if (!modifyReplicas.isEmpty()) {
                taskParamObject.put("modifyReplicas", StringUtils.join(modifyReplicas, ","));
            }

            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask(
                    "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParamObject.toJSONString(), 0);
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                    modifyInsParam.isTransIns() ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

            ReplicaSetResource tmpReplicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, tmpReplicaSetName);
            ReplicaSet tmpReplicaSet = tmpReplicaSetResource.getReplicaSet();
            tmpReplicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);

            //更新insType为tmp
            dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, tmpReplicaSetName, tmpReplicaSet);

            if(replicaSetService.isStorageTypeCloudDisk(modifyInsParam.getTargetDiskType())){
                tmpReplicaSet.getLabels().put("cloud_pfs","true");
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSetName, tmpReplicaSet.getLabels());
            }

            isSuccess = true;
            return taskId;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            throw new RdsException(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: ", e);
            return createErrorResponse(new Object[]{ResultCode.CODE_ERROR, "AllocateResouceFailed", "Allocate resource failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }finally {
            if (null != transListId && !isSuccess && null != replicaSetResourceRequest && StringUtils.isNotEmpty(replicaSetResourceRequest.getTmpReplicaSetName())) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, replicaSetResourceRequest.getTmpReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }
}
