package com.aliyun.dba.poddefault.action;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifySQLDelayImpl")
public class ModifySQLDelayImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.ModifySQLDelayImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected WorkFlowService workFlowService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
        if (!custins.isRead()) { //只支持只读实例
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
        if (!custins.isActive()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
        if (mysqlParamSupport.isMysqlXdbByCustins(primaryCustins)) {
            //XDB的只读不支持设置sqldelay
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
        try {
            String sqlDelay = getParameterValue(actionParams, "SqlDelay");
            CheckUtils.parseInt(sqlDelay, 0, Integer.MAX_VALUE, ErrorCode.INVALID_SQL_DELAY_TIME);
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("delaySeconds", sqlDelay); //sql_delay use in task_queue
            taskParamObject.put("replicaSetName", custins.getInsName());
            String taskParam = taskParamObject.toJSONString();
            Object taskId = workFlowService.dispatchTask(
                    "custins", custins.getInsName(), "mysql", PodDefaultConstants.READ_INS_SET_SQL_DELAY, taskParam, 0);
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("TaskId", taskId);
            return data;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
