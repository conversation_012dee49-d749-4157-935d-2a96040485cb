package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.workflowapi.ApiException;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.aspectj.util.LangUtil;
import org.joda.time.DateTime;
import org.joda.time.DateTimeUtils;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SimpleTimeZone;
import java.util.TimeZone;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static org.apache.log4j.helpers.AbsoluteTimeDateFormat.ISO8601_DATE_FORMAT;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyTaskRecoverTimeImpl")
public class ModifyTaskRecoverTimeImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyTaskRecoverTimeImpl.class);

    @Autowired
    protected MysqlParamSupport paramSupport;

    @Autowired
    protected WorkFlowService workFlowService;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    private DBaasMetaService dBaasMetaService;



    public static final String ISO8601_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    public static final String DATE_FORMAT_LOCAL = "yyyy-MM-dd HH:mm:ss";

    public static final String IMMEDIATELY = "Immediately";
    public static final String MAINTAINTIME = "MaintainTime";
    public static final String SPECIFIED = "Specified";
    public static final String TIMEPOINT = "timepoint";





    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        custins = mysqlParamSupport.getCustInstance(params);
        if (custins == null) {
            //实例不存在，或者不是实例拥有者
            return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        if (CustinsSupport.CUSTINS_STATUS_DELETING.equals(custins.getStatus())) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        custins.getMaintainStarttime();
        custins.getMaintainEndtime();
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String targetId = custins.getInsName();
        String targetType = paramSupport.getParameterValue(params, "TargetType", "custins");
        String recoverMode = paramSupport.getParameterValue(params, "EffectiveTime");
        String recoverTime = paramSupport.getParameterValue(params, "SpecifiedTime");
        String taskId = paramSupport.getParameterValue(params, "TaskId");
        try {
            if (!StringUtils.isEmpty(taskId)) {// 对taskId做鉴权
                Long taskIdLong = Long.valueOf(taskId);
                List<Long> taskIds = Arrays.asList(taskIdLong);
                Map result = (Map)workFlowService.queryTaskQueueList(requestId, taskIds);
                JSONObject jsonObject = new JSONObject(result);
                JSONObject dataJson = jsonObject.getJSONObject("data");
                if (dataJson != null){
                    JSONArray taskList =  dataJson.getJSONArray("taskList");
                    if (taskList != null && taskList.size() == 1) {
                        String insName = taskList.getJSONObject(0).getString("targetId");
                        if (!custins.getInsName().equals(insName)) {
                            return createErrorResponse(ErrorCode.TASK_NOT_FOUND);
                        }
                    } else {
                        return createErrorResponse(ErrorCode.TASK_NOT_FOUND);
                    }
                }
            }
            recoverTime = getAndCheckRecoverTime(requestId, targetId, recoverMode, recoverTime);
            recoverMode = getAndCheckTimeMode(recoverMode, recoverTime);
            Map result = (Map) workFlowService.modifyTaskRecoverTime(requestId, targetId, targetType, recoverMode, recoverTime, taskId);
            logger.info("Call workFlowService.modifyTaskRecoverTime result is "+ JSON.toJSONString(result));
            if (result == null || !"SUCCESS".equals(result.get("code"))) {
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "ModifyTaskRecoverTimeFailed", "Call WorkflowApi failed."});
            }
            return result;
        }catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        }catch (ApiException e){
            logger.error(requestId + " WorkflowApi error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "ModifyTaskRecoverTimeFailed", "Call WorkflowApi failed."});
        }catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private String getAndCheckRecoverTime(String requestId, String targetId, String recoverMode, String recoverTime) throws Exception {
        if (StringUtils.isEmpty(recoverTime)){
            return null;
        }
        if (MAINTAINTIME.equals(recoverMode)){
            return calculateMaintainRecoverTime(requestId, targetId);
        }
        Date startDate = null;
        Date endDate = null;
        DateTime now = new DateTime(DateTimeZone.UTC);
        try {
            startDate = DateSupport.str2second_gmt(recoverTime);
            if (startDate.getTime() <= now.getMillis()) {
                throw new RdsException(ErrorCode.INVALID_STARTTIME);
            }
            endDate = DateUtils.addHours(startDate, 1);
        } catch (ParseException | RdsException e) {
            throw new RdsException(ErrorCode.INVALID_STARTTIME);
        }
        String startTimeStr = DateSupport.second2str_gmt0(startDate);
        String endTimeStr = DateSupport.second2str_gmt0(endDate);

        JSONObject object = new JSONObject();
        object.put("start_time", startTimeStr);
        object.put("end_time", endTimeStr);
        JSONArray array = new JSONArray();
        array.add(object);
        System.out.println(array.toJSONString());
        return array.toJSONString();
    }

    private String getAndCheckTimeMode(String recoverMode, String recoverTime) throws RdsException {
        if (IMMEDIATELY.equals(recoverMode)) {
            //立即切换
            return CustinsSupport.NOW_MODE;
        } else if (MAINTAINTIME.equals(recoverMode)) {
            //可运维时间
            return CustinsSupport.MAINTAIN_MODE;
        } else if (SPECIFIED.equals(recoverMode)) {
            //指定时间点
            if(recoverTime == null){
                throw new RdsException(ErrorCode.INVALID_SWICTHTIME);
            }else{
                return TIMEPOINT;
            }
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
    }


    /**
     * 计算任务的运维切换时间
     * @return
     */
    public String calculateMaintainRecoverTime(String requestId,String targetId) throws Exception {
        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, targetId, null);

        Date now = new Date();

        Calendar start = Calendar.getInstance();
        start.setTime(getTodayStartTime());
        start.add(Calendar.HOUR, replicaSet.getMaintainStart().getHour());
        start.add(Calendar.MINUTE, replicaSet.getMaintainStart().getMinute());
        start.add(Calendar.SECOND, replicaSet.getMaintainStart().getSecond());

        Calendar end = Calendar.getInstance();
        end.setTime(getTodayStartTime());
        end.add(Calendar.HOUR, replicaSet.getMaintainEnd().getHour());
        end.add(Calendar.MINUTE, replicaSet.getMaintainEnd().getMinute());
        end.add(Calendar.SECOND, replicaSet.getMaintainEnd().getSecond());

        if (now.after(end.getTime())) {
            //当前时间在当天运维时间点后，则取第二天的运维时间点
            start.add(Calendar.DATE, 1);
            end.add(Calendar.DATE, 1);
        }
        JSONArray array = new JSONArray();
        for (int i = 0; i < 5; i++) {   //多给几天时间
            JSONObject object = new JSONObject();
            object.put("start_time", convertDateToGMT(start.getTime(), ISO8601_DATE_FORMAT));
            object.put("end_time", convertDateToGMT(end.getTime(), ISO8601_DATE_FORMAT));
            array.add(object);
            start.add(Calendar.DATE, 1);
            end.add(Calendar.DATE, 1);
        }
        return array.toJSONString();
    }

    public static Date getTodayStartTime() {
        return parseDate(formatehYmd(new Date()) + " " + "00:00:00", null);
    }

    public static Date getTodayEndTime() {
        return parseDate(formatehYmd(new Date()) + " " + "23:59:59", null);
    }


    public static String formatehYmd(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    private static Date parseDate(String date, String dateformat) {
        SimpleDateFormat parser;
        if (StringUtils.isBlank(dateformat)) {
            dateformat = DATE_FORMAT_LOCAL;
        }
        parser = new SimpleDateFormat(dateformat);
        if (ISO8601_DATE_FORMAT.equalsIgnoreCase(dateformat)) {
            parser.setTimeZone(TimeZone.getTimeZone("UTC"));
        }
        Date rtn = null;
        try {
            rtn = parser.parse(date);
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }
        return rtn;
    }

    /**
     * 把locale date转换成GMT(UTC)时间
     * 按指定的dateFormat参数格式输出
     * param date
     * param String dateFormat, 输出日期格式, "yyyy-MM-dd HH:mm:ss"
     * return
     */
    public static String convertDateToGMT(Date date, String dateFormat) {
        if (null == date) {
            return null;
        }
        String result = null;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
            simpleDateFormat.setTimeZone(new SimpleTimeZone(0, "GMT"));
            result = simpleDateFormat.format(date);
        } catch (Exception e) {
        }
        return result;
    }



}
