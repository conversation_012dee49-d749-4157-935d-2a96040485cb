package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.base.support.MySQLParamConstants.AURORA_SWITCH_FLAG_TASK;
import static com.aliyun.dba.base.support.MySQLParamConstants.AURORA_SWITCH_FLAG_API_FORCE;
import static com.aliyun.dba.base.support.MySQLParamConstants.AURORA_SWITCH_FLAG_API_NORMAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.ParamConstants.TARGET_INSTANCE_ID;

@Service
public class SwitchMasterDBInstanceHAService {
    private static final LogAgent logger = LogFactory.getLogAgent(SwitchMasterDBInstanceHAService.class);

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    private PodParameterHelper podParameterHelper;

    public Map<String, Object> switchDBInstanceHa(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String targetInstanceId = paramSupport.getParameterValue(params, TARGET_INSTANCE_ID);
            Integer switchType = CustinsValidator.getRealNumber(
                    paramSupport.getParameterValue(params, ParamConstants.SWITCH_TYPE), CustinsSupport.SWITCH_TYPE_NORMAL);
            if (!CustinsSupport.SWITCH_TYPE_SET.contains(switchType)) {
                return createErrorResponse(ErrorCode.INVALID_SWITCH_TYPE);
            }
            Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);

            // 状态和切换条件判断
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            boolean isXDB = replicaSetService.isReplicaSetXDB(requestId, replicaSet.getName());
            Replica targetReplica;

            if (targetInstanceId != null) {
                targetReplica = getTargetReplica(replicaSet, targetInstanceId);
                if (targetReplica == null) {
                    return createErrorResponse(ErrorCode.INVALID_INSTANCE_ID);
                }
            } else {
                Replica.RoleEnum role = isXDB ? Replica.RoleEnum.FOLLOWER : Replica.RoleEnum.SLAVE;
                targetReplica = replicaSetService.getSpecificReplica(requestId, replicaSet.getName(), null, null, role);
            }

            if (targetReplica == null || targetReplica.getRole().equals(Replica.RoleEnum.MASTER)) {
                return createErrorResponse(ErrorCode.INVALID_INSTANCE_ID);
            }
            JSONObject taskParam = new JSONObject();
            // Task parameter format: {"switch_flag":4, "switch_info":{"mode":"immediate"}, "target_replica_id": 111111}
            Integer switchFlag;
            if (PodParameterHelper.isAliGroup(replicaSet.getBizType())) {
                switchFlag = AURORA_SWITCH_FLAG_TASK;
            } else {
                switchFlag = CustinsSupport.SWITCH_TYPE_NORMAL.equals(switchType) ? AURORA_SWITCH_FLAG_API_NORMAL : AURORA_SWITCH_FLAG_API_FORCE;
            }
            taskParam.put("requestId", requestId);
            taskParam.put("switch_flag", switchFlag);
            taskParam.put("target_replica_id", targetReplica.getId());

            // 集群版里面，用户看到的节点ID是replica.name
            if (MysqlParamSupport.isCluster(replicaSet.getCategory()) && targetInstanceId != null) {
                taskParam.put("targetReplicaName", targetReplica.getName());
            }

            taskParam.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
            String domain = ReplicaSetService.isTDDL(replicaSet) ? PodDefaultConstants.DOMAIN_XDB : replicaSet.getService();
            String taskKey = PodDefaultConstants.TASK_HA_SWITCH_INS;
            if (replicaSetService.isTddlTaskMigrate(requestId, replicaSet)) {
                domain = PodDefaultConstants.DOMAIN_MYSQL;
                taskKey = PodDefaultConstants.TASK_TDDL_HA_SWITCH_INS;
            }
            Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, taskParam.toJSONString(), 1);
            // 更新实例状态为 HA 切换中
            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.HA_SWITCHING.toString());

            Map<String, Object> data = new HashMap<>();
            data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
            data.put(TARGET_INSTANCE_ID, targetReplica.getId());
            data.put(ParamConstants.SWITCH_TYPE, switchType);
            data.put(ParamConstants.TASK_ID, taskId);
            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception e) {
            logger.error(requestId + " Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 获取切换目标节点
     * 针对集群版，兼容传节点ID和节点Name两种场景。杜康等内部平台会传节点ID，瑶池OPENAPI、控制台会传节点Name
     * */
    private Replica getTargetReplica(ReplicaSet replicaSet, String targetInstanceId) throws ApiException {
        Replica targetReplica;
        if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
            if (StringUtils.isNumeric(targetInstanceId)) {
                targetReplica = replicaSetService.getSpecificReplica(
                        RequestSession.getRequestId(), replicaSet.getName(), null, Long.valueOf(targetInstanceId), null);
            } else {
                targetReplica = replicaSetService.getSpecificReplica(
                        RequestSession.getRequestId(), replicaSet.getName(), targetInstanceId, null, null);
            }
        } else {
            targetReplica = replicaSetService.getSpecificReplica(
                    RequestSession.getRequestId(), replicaSet.getName(), null, Long.valueOf(targetInstanceId), null);
        }
        return targetReplica;
    }
}
