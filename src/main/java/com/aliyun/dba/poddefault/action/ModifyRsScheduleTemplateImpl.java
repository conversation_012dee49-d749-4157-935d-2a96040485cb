package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.RsScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyRsScheduleTemplateImpl")
public class ModifyRsScheduleTemplateImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyRsScheduleTemplateImpl.class);
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            String bid = parameterHelper.getParameterValue(ParamConstants.USER_ID);
            Validate.notEmpty(bid,"null userId");
            String uid = parameterHelper.getParameterValue(ParamConstants.UID);
            Validate.notEmpty(uid,"null uid");
            String name = parameterHelper.getParameterValue("Name");
            Validate.notEmpty(name,"null name");
            String templateString = parameterHelper.getParameterValue(ParamConstants.Template);
            Validate.notEmpty(templateString,"null template");
            podTemplateHelper.checkTemplateString(templateString);
            RsScheduleTemplate newTemplate = new RsScheduleTemplate();
            newTemplate.setTemplate(templateString);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, bid + "_" + uid, false);
            PodScheduleTemplate podTemplate = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, name, user.getBid() + "_" + user.getAliUid());
            if (podTemplate == null) {
                throw new Exception(String.format("RsTemplateName:%s is not exist", name));
            }

            String template = podTemplateHelper.updatePodScheduleTemplate(requestId, bid + "_" + uid, name, newTemplate);
            Map<String, Object> data = new HashMap<>();
            data.put("UserId", user.getUserId());
            data.put("Name", name);
            data.put("Template", template);
            return data;
        } catch (Exception ex) {
            logger.error(requestId + " Modify RsScheduleTemplate failed: " + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE, ex.getMessage());
        } finally{
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
