package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.CpuShareHelper;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRefreshAllocateResourceImpl")
public class RefreshAllocateResourceImpl implements IAction {

    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Resource
    protected MysqlParamSupport paramSupport;

    @Resource
    protected ReplicaSetService replicaSetService;

    @Resource
    protected CpuShareHelper cpuShareHelper;

    @Resource
    protected PodParameterHelper podParameterHelper;

    @Resource
    protected WorkFlowService workFlowService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String replicaSetName = null;

        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            Map<String, Object> checkResult = precheckForRefresh(replicaSet, params);
            if (Objects.nonNull(checkResult)) {
                return checkResult;
            }
            replicaSetName = replicaSet.getName();
            List<Replica> replicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems();

            Object taskId = null;
            boolean isModifyMemory = Boolean.parseBoolean(paramSupport.getParameterValue(params, "IsModifyMemory", false));
            boolean isModifyCgroup = Boolean.parseBoolean(paramSupport.getParameterValue(params, "IsModifyCgroup", false));
            if (!isModifyCgroup) {  // only update ins meta
                for (Replica rp : Objects.requireNonNull(replicas)) {
                    InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), rp.getClassCode(), false);
                    // 独享实例不刷送核
                    if (Objects.equals(instanceLevel.getIsolationType(), InstanceLevel.IsolationTypeEnum.DEDICATED)) {  // check single tenant again
                        continue;
                    }
                    try {
                        String allocateCpuCores = cpuShareHelper.getReplicaCpuLimit(requestId, rp, replicaSet.getService(), instanceLevel).toString();
                        Map<String, String> labels = new HashMap<>(2);
                        if (Objects.nonNull(allocateCpuCores)) {
                            labels.put(CpuShareHelper.ALLOCATE_CPU_CORES, allocateCpuCores);
                        }
                        if (isModifyMemory && Objects.nonNull(instanceLevel.getLimitMemoryMB())) {
                            labels.put(CpuShareHelper.ALLOCATE_MEM_SIZE_MB, instanceLevel.getLimitMemoryMB().toString());
                        }
                        if (labels.size() > 0) {
                            dBaasMetaService.getDefaultClient().updateReplicaLabels(requestId, replicaSetName, rp.getId(), labels);
                        }
                    } catch (Exception ex) {
                        log.error("Get or update allocate resource meta failed, ex: {}", JSON.toJSONString(ex));
                        throw new RdsException(ErrorCode.UNSUPPORTED_CLASS_CODE);
                    }
                }
            } else {
                String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
                String taskKey = PodDefaultConstants.TASK_REFRESH_INS_CGROUP;
                JSONObject taskParams = new JSONObject();
                taskParams.put("IsModifyMemory", isModifyMemory);
                taskParams.put("DBInstanceName", replicaSetName);
                taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, taskParams.toString(), 0);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("DBInstanceName", replicaSetName);
            result.put("TaskId", taskId);
            result.put("RequestId", requestId);
            return result;
        } catch (RdsException ex) {
            log.error("refresh instance allocate resource failed, ins: {}, error: {}", replicaSetName, JSON.toJSONString(ex));
            throw ex;
        } catch (ApiException ex) {
            log.error("call meta api failed, ins: {}, error: {}", replicaSetName, JSON.toJSONString(ex));
            throw new RdsException(ErrorCode.API_CALLING_FAILED);
        } catch (Exception ex) {
            log.error("interface error, ex: {}", JSON.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 查看权限使用
     */
    private Map<String, Object> precheckForRefresh(ReplicaSet replicaSet, Map<String, String> params) {
        if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }
        if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        if (podParameterHelper.isSingleTenant(replicaSet)) {  // not support single tenant
            return createErrorResponse(new Object[]{ResultCode.CODE_ERROR, "InvalidTenantType", "The specific tenant type is not valid."});
        }

        return null;
    }
}
