package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.model.PerformanceLevelEnum;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultChangeDiskPerfLevelImpl")
@Slf4j
public class ChangeDiskPerfLevelImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ChangeDiskPerfLevelImpl.class);

    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    protected DBaasMetaService dBaasMetaService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String performanceLevel = mysqlParamSupport.getParameterValue(params, "PerformanceLevel");
            if (StringUtils.isBlank(performanceLevel)) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!PodParameterHelper.isAliGroup(replicaSetMeta.getBizType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);  //只支持集团实例
            } if (replicaSetMeta.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetMeta.getName(), null, null, null, null);
            List<Replica> replicaList = replicaListResult.getItems();
            for (Replica replica : replicaList) {
                ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, replica.getId(), null);
                Replica currentReplica = replicaResource.getReplica();
                if (currentReplica.getRole() == Replica.RoleEnum.LOGGER) {
                    continue;
                }
                commonProviderService.getDefaultApi().modifyDiskPerformanceLevel(requestId, replicaSetMeta.getName(),
                        currentReplica.getId(), performanceLevel);
            }
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceName", replicaSetMeta.getName());
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
