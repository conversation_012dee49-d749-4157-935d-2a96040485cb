package com.aliyun.dba.poddefault.action;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ClusterInstance;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceRelation;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceRelationListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeAnalyticDBInstanceAttributeImpl")
public class DescribeAnalyticDBInstanceAttributeImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeAnalyticDBInstanceAttributeImpl.class);

    @Resource
    RdsApi rdsApi;

    @Resource
    protected DBaasMetaService dbaasMetaService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String analyticInsName = paramSupport.getParameterValue(params, ParamConstants.ANALYTIC_DB_INSTANCE_NAME);
            boolean checkAnalyticIns = Boolean.parseBoolean(paramSupport.getParameterValue(params, "checkAnalyticIns"));
            if (checkAnalyticIns) {
                ClusterInstance clusterInstance = dbaasMetaService.getDefaultClient().getClusterInstance(requestId, analyticInsName, true);
                return parseToResult(clusterInstance);
            }

            // 不传analyticInsName，返回关联的分析型实例信息
            if (StringUtils.isEmpty(analyticInsName)) {
                String dbInstanceName = mysqlParamSupport.getAndCheckDBInstanceName(params);
                List<ServiceRelation> serviceRelationList = dbaasMetaService.getDefaultClient().listReplicaSetServices(requestId, dbInstanceName).getItems();
                if (ObjectUtils.isEmpty(serviceRelationList)) {
                    logger.warn("requestId : {}. serviceRelation is null. params : {}", requestId, JSONObject.toJSONString(params));
                    return new HashMap<>();
                }
                serviceRelationList = serviceRelationList.stream()
                        .filter(it -> StringUtils.equalsIgnoreCase(it.getStatus().getValue(), ServiceRelation.StatusEnum.ACTIVE.getValue()))
                        .filter(it -> StringUtils.equalsIgnoreCase(it.getServiceRole(), PodDefaultConstants.DB_TYPE_CK))
                        .collect(Collectors.toList());
                ServiceRelation serviceRelation = serviceRelationList.size() > 0 ? serviceRelationList.get(0) : null;
                if (!ObjectUtils.isEmpty(serviceRelation)) {
                    analyticInsName = serviceRelation.getServiceName();
                } else {
                    logger.warn("requestId : {}. serviceRelation is null. params : {}", requestId, JSONObject.toJSONString(params));
                    return new HashMap<>();
                }
            } else {
                ReplicaSet analyticIns = podCommonSupport.getCkReplicaSet(analyticInsName);
                // 查询关联关系
                podCommonSupport.checkAnalyticDBInstanceRelation(requestId, custins.getInsName(), analyticInsName);
            }

            String uid = paramSupport.getParameterValue(params, ParamConstants.UID);
            String userId = paramSupport.getParameterValue(params, ParamConstants.USER_ID);
            String regionId = paramSupport.getParameterValue(params, ParamConstants.REGION_ID);

            logger.info("requestId : {}, request to DescribeDBInstance", requestId);
            String finalAnalyticInsName = analyticInsName;
            Map<String, Object> result = rdsApi.getDataByRdsApi(new HashMap<String, String>() {{
                put("Action", "DescribeDBInstance");
                put("DBInstanceName", finalAnalyticInsName);
                put("UID", uid);
                put("User_id", userId);
                put("RegionID", regionId);
                put("RequestId", requestId);
                put("DBInstanceModelType", "cluster");
            }}, ParamConstants.YAOCHI_ACCESS);

            logger.info("result:{}", JSONObject.toJSONString(result));

            return result;

        } catch (RdsException ex) {
            log.error("DescribeAnalyticDBInstanceAttribute failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("DescribeAnalyticDBInstanceAttribute Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, Object> parseToResult(ClusterInstance clusterInstance) {
        Map<String, Object> result = new HashMap<>();
        result.put("dbInstanceName", clusterInstance == null ? null : clusterInstance.getName());
        result.put("dbInstanceId", clusterInstance == null ? null : clusterInstance.getId());
        return result;
    }


}
