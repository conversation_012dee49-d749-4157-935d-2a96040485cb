package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.model.ConnStringBody;
import com.aliyun.dba.adb_vip_manager_client.model.UpdatePortBody;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointService;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointServiceFactoryImpl;
import com.aliyun.dba.poddefault.action.service.endpoint.EndpointWeightConfig;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.IpResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceEndpointAddressImpl")
@Slf4j
public class ModifyDBInstanceEndpointAddressImpl implements IAction {
    @Resource
    private EndPointServiceFactoryImpl endpointFactory;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private ResourceService resourceService;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private ReplicaSetService replicaSetService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String connectionStringOccupied = null;
        boolean executeSuccess = false;
        EndPointService endPointService = null;
        String replicaSetName = null;
        String requestId = null;
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);  // use DBInstanceName
            endPointService = endpointFactory.getService(replicaSet.getConnType());
            replicaSetName = replicaSet.getName();
            requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

            if (!MysqlParamSupport.isCluster(replicaSet.getCategory())) {  // 暂时限制只有 Cluster
                return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
            }
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            log.info("{} start modify endpoint address", replicaSetName);

            // data prepare
            String endpointId = paramSupport.getParameterValue(params, "DBInstanceEndpointId");
            String connString = paramSupport.getParameterValue(params, ParamConstants.CONNECTION_STRING);

            String connStringPrefix = paramSupport.getParameterValue(params, ParamConstants.CONN_ADDR_PREFIX);
            String port = paramSupport.getPort(params);

            String vpcId = paramSupport.getParameterValue(params, ParamConstants.VPC_ID);
            String vSwitchId = paramSupport.getParameterValue(params, ParamConstants.VSWITCH_ID);
            String privateIPAddress = paramSupport.getParameterValue(params, "PrivateIPAddress");

            if (connStringPrefix == null && port == null && vpcId == null && vSwitchId == null && privateIPAddress == null) {
                throw new RdsException(ErrorCode.PARAM_NOT_FOUND);
            }

            EndpointGroup endpointGroup = endPointService.getAndCheckEndpointGroup(requestId, replicaSetName, endpointId);
            Endpoint endpoint = endPointService.getAndCheckEndpoint(requestId, replicaSetName, connString, endpointGroup.getId());
            // 检测 config 跳过 primary、直连 节点
            if (!endpoint.getType().equals(Endpoint.TypeEnum.NORMAL) || endpoint.getType().toString().equalsIgnoreCase("Node")) {
                EndpointWeightConfig weightConfig = JSON.parseObject(endpointGroup.getLabels().get(EndpointWeightConfig.CONFIG_KEY), EndpointWeightConfig.class);
                endPointService.checkEndpointRSEmpty(weightConfig);
            }

            if ((connStringPrefix != null || port != null) && (vpcId != null || vSwitchId != null || privateIPAddress != null)) {
                throw new RdsException(ErrorCode.INVALID_PARAM_COMBINATION);
            }
            // modify
            String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            JSONObject taskParam = new JSONObject();
            taskParam.put(ParamConstants.REQUEST_ID, requestId);
            taskParam.put("endpointName", endpointId);
            taskParam.put("connectionString", connString);
            Object taskId = null;
            if (connStringPrefix != null || port != null) {
                // prefix 为空，或者 prefix 与原有的前缀相同，将 prefix 置为空
                if (connStringPrefix == null || connStringPrefix.equals(connString.split("\\.")[0])) {
                    connStringPrefix = null;
                }
                else {
                    endPointService.checkConnectionStringPrefix(requestId, replicaSet, connStringPrefix);
                    // 占用一个 connectionString
                    connectionStringOccupied = endPointService.allocateConnectionString(requestId, replicaSetName, connStringPrefix);
                }
                if (port == null || port.equals(endpoint.getVport().toString())) {
                    port = null;
                }
                else  {
                    CheckUtils.parseInt(port, 1000, 65534, ErrorCode.INVALID_PORT);
                }
                taskParam.put("connectionStringPrefix", connStringPrefix);
                taskParam.put("port", port);

                endPointService.inactiveReplicaSet(requestId, replicaSetName);
                taskId = workFlowService.dispatchTask("custins", replicaSetName, domain, PodDefaultConstants.TASK_MODIFY_ENDPOINT_ADDRESS, taskParam.toJSONString(), 0);
                executeSuccess = true;
            }
            else if ((vpcId != null && vSwitchId != null) || privateIPAddress != null) {
                String tmpConnectionString = this.doModifyVip(replicaSet, requestId, replicaSetName, endpointId, connString, vpcId, vSwitchId, privateIPAddress);
                // 当没有任何修改的情况下会返回 null
                if (tmpConnectionString != null) {
                    taskParam.put("tmpConnectionString", tmpConnectionString);
                    endPointService.inactiveReplicaSet(requestId, replicaSetName);
                    taskId = workFlowService.dispatchTask("custins", replicaSetName, domain, PodDefaultConstants.TASK_MODIFY_ENDPOINT_VIP, taskParam.toJSONString(), 0);
                }
            }
            else {
                throw new RdsException(ErrorCode.INVALID_PARAM_COMBINATION);
            }

            // rtn
            Map<String, Object> rtn = new HashMap<>();
            if (taskId != null) {
                rtn.put("TaskId", taskId);
            }
            rtn.put("DBInstanceName", replicaSetName);
            rtn.put("DBInstanceEndpointId", endpointId);
            rtn.put("RequestId", requestId);
            return rtn;
        } catch (ApiException ex) {
            log.error("ModifyDBInstanceEndpointAddress, Api called failed: ", ex);
            throw new RdsException(ErrorCode.API_CALLING_FAILED);
        } catch (RdsException ex) {
            log.error("ModifyDBInstanceEndpointAddress failed: ", ex);
            throw ex;
        } catch (Exception e) {
            log.error("ModifyDBInstanceEndpointAddress Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!executeSuccess && connectionStringOccupied != null) {
                try {
                    endPointService.releaseConnectionString(requestId, replicaSetName, connectionStringOccupied);
                } catch (com.aliyun.dba.adb_vip_manager_client.ApiException e) {
                    log.error("clear connection string allocate failed!", e);
                }
            }
        }
    }

    /**
     * do modify vip(vpc, vsw, privateIPAddress)
     */
    private String doModifyVip(ReplicaSet replicaSet, String requestId, String replicaSetName, String endpointId,
                             String connectionString, String vpcId, String vSwitchId, String privateIPAddress) throws Exception {
        EndPointService endPointService = endpointFactory.getService(replicaSet.getConnType());

        // data prepare
        EndpointGroup endpointGroup = endPointService.getAndCheckEndpointGroup(requestId, replicaSetName, endpointId);
        Endpoint endpoint = endPointService.getAndCheckEndpoint(requestId, replicaSetName, connectionString, endpointGroup.getId());
        String connStringPrefix = endpoint.getAddress().split("\\.")[0];
        String tmpConnString = "tmp-" + connStringPrefix;

        // check
        if (endpoint.getNetType() != Endpoint.NetTypeEnum.VPC) {
            throw new RdsException(ErrorCode.ENDPOINT_TYPE_NOT_SUPPORT);
        }
        if ((vpcId != null && vSwitchId == null) || (vpcId == null && vSwitchId != null)) {  // 保证 vpcId vSwitchId 两者同时出现或者同时不出现
            throw new RdsException(ErrorCode.INVALID_VSWITCH_ID);
        }
        IpResourceDO ipResource = resourceService.getIpResourceByIpAndVpc(endpoint.getVip(), endpoint.getVpcId());
        if (vpcId == null && vSwitchId == null) {
            vpcId = endpoint.getVpcId();
            vSwitchId = ipResource.getvSwitchId();
        }
        if (Objects.equals(vpcId, endpoint.getVpcId()) && vSwitchId.equals(ipResource.getvSwitchId()) &&
                (privateIPAddress == null || privateIPAddress.equals(endpoint.getVip()))) {
            return null;
        }
        endPointService.checkValidForVpcId(vpcId);
        endPointService.checkValidForVswId(vSwitchId);

        try {
            // create
            EndpointWeightConfig weightConfig;
            if (endpoint.getType() == Endpoint.TypeEnum.NORMAL) {  // 主链路
                weightConfig = endPointService.createPrimaryWeightConfig(requestId, replicaSetName);
            }
            else if (Endpoint.TypeEnum.NODE.toString().equalsIgnoreCase(endpointGroup.getType())) {  // 直连链路
                weightConfig = endPointService.genEndpointWeightConfig(replicaSetName, endpointGroup.getLabels().get(EndpointWeightConfig.ENDPOINT_NODE_KEY));
            }
            else {
                weightConfig = JSON.parseObject(endpointGroup.getLabels().get(EndpointWeightConfig.CONFIG_KEY), EndpointWeightConfig.class);
            }
            // 设置为 userVisible = 0, 任务流中需要将该 connectionString userVisible 置为 1
            Endpoint privateEndpoint = endPointService.createPrivateEndpointAddress(requestId, replicaSetName, vpcId, vSwitchId, privateIPAddress,
                    tmpConnString, endpoint.getVport().toString(), endpoint.getType().toString(), weightConfig, endpointGroup, 0, null);
            return privateEndpoint.getAddress();
        } catch (Exception ex) {
            log.error("create link failed: ", ex);
            Optional<Endpoint> newEndpointFound = endPointService.listReplicaSetEndpoint(requestId, replicaSetName).stream().filter(ep -> ep.getAddress().split("\\.")[0].equals(tmpConnString)).findFirst();
            if (newEndpointFound.isPresent()) {
                endPointService.delete(requestId, newEndpointFound.get());
            }
            throw new RdsException(ErrorCode.API_CALLING_FAILED, "Link api calling failed");
        }
    }

}
