package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateAdminAccountImpl")
public class CreateAdminAccountImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CreateAdminAccountImpl.class);
    @Autowired
    private DbsService dbsService;
    @Autowired
    AccountIDao accountIDao;
    @Autowired
    private AccountService accountService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    private DbossApi dbossApi;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CreateAccountImpl createAccountImpl;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
        throws RdsException {
        return createAccountImpl.doActionRequest(custInstanceDO,map);
    }
}
