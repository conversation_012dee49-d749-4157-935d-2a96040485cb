package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.StoppedReplicaSetMaintainService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteDBInstanceImpl")
public class DeleteDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.physical.action.DeleteDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    ReplicaSetService replicaSetService;
    @Autowired
    protected CheckService checkService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private StoppedReplicaSetMaintainService stoppedReplicaSetMaintainService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!(replicaSet.getStatus() == ReplicaSet.StatusEnum.ACTIVATION || replicaSet.getStatus() == ReplicaSet.StatusEnum.STOPPED)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            String domain = ReplicaSetService.isTDDL(replicaSet) ? PodDefaultConstants.DOMAIN_XDB : PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = PodDefaultConstants.TASK_DELETE_INS;
            String accessId = getParameterValue(params, ParamConstants.ACCESSID);
            // 存在只读或者分析实例不能做主实例删除
            if (replicaSet.getInsType() == ReplicaSet.InsTypeEnum.MAIN) {
                List<ReplicaSet> readOnlyReplicaSetList = dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId, replicaSet.getName(), ReplicaSet.InsTypeEnum.READONLY.toString()).getItems();
                if (CollectionUtils.isNotEmpty(readOnlyReplicaSetList)) {
                    long readCount = readOnlyReplicaSetList.stream().filter(v -> (v.getStatus() != ReplicaSet.StatusEnum.DELETING && v.getStatus() != ReplicaSet.StatusEnum.DELETED)).count();
                    if (readCount > 0) {
                        logger.error("there are {} readins, do not allow to delete", readCount);
                        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                    }
                }

                ServiceRelationListResult serviceRelationList = dBaasMetaService.getDefaultClient().listReplicaSetServices(requestId, dbInstanceName);
                if (serviceRelationList.getItems() != null) {
                    List<ServiceRelation> clickhouseService = serviceRelationList.getItems().stream()
                            .filter(it -> StringUtils.equalsIgnoreCase(it.getStatus().getValue(), ServiceRelation.StatusEnum.ACTIVE.getValue()))
                            .filter(it -> StringUtils.equalsIgnoreCase(it.getServiceRole(), PodDefaultConstants.DB_TYPE_CK))
                            .collect(Collectors.toList());
                    if (clickhouseService.size() > 0) {
                        logger.error("requestId : {}, custins : {}. there is analytic ins, do not allow to delete.", requestId, dbInstanceName);
                        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                    }
                }
            }
            boolean isTddlTaskMigrate = replicaSetService.isTddlDeleteIns(requestId, replicaSet);
            if (isTddlTaskMigrate) {
                domain = PodDefaultConstants.DOMAIN_MYSQL;
                taskKey = PodDefaultConstants.TASK_DELETE_TDDL_INS;
            }

            Map<String, Object> data = new HashMap<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);

            if (PodCommonSupport.isAccessFromDBS(accessId) && !StringUtils.startsWith(dbInstanceName, "dbs-")) {
                //来源是DBS的删除实例调用，强制校验实例名规则，保护实例不会被误删除
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            if (replicaSetService.isReplicaSetAliGroup(replicaSet)) {
                Integer slientHours = mysqlParamSupport.getSlientHours(params);
                //获取静默的模式(默认指定时间点静默)
                String slientMode = CustinsParamSupport.getParameterValue(params, ParamConstants.SWITCH_TIME_MODE, CustinsSupport.SWITCH_POINT);
                if (CustinsSupport.SWITCH_POINT.equalsIgnoreCase(slientMode)) {
                    // 可以指定时间静默时间(默认静默24小时)
                    if (!params.containsKey(ParamConstants.SWITCH_TIME.toLowerCase())) {
                        params.put(ParamConstants.SWITCH_TIME.toLowerCase(), mysqlParamSupport.getUtcTimeForSliceHours(slientHours));
                        params.put(ParamConstants.SWITCH_TIME_MODE.toLowerCase(), CustinsSupport.SWITCH_POINT);
                    }
                }
                // 如果指定立即切换模式，静默时间是0.如果不指定切换模式相当于直接走指定静默24小时
                Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);

                jsonObject.put("slient_Info", switchInfoMap);
                // 判断是否跳过备份，支持传参和传标两种方式
                if (mysqlParamSupport.isSkipBackup(params)) {
                    jsonObject.put("skipBackup", true);
                } else {
                    // 由于当前若曼蒂不支持传参，需要河源裁撤不备份，这里当前通过实例打标的方式来识别(默认需要备份)
                    ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, dbInstanceName);
                    String skip_backup = replicaSetResource.getReplicaSet().getLabels().get(PodDefaultConstants.SKIP_BACKUP_LABLE);
                    if (StringUtils.isNotBlank(skip_backup)) {
                        jsonObject.put("skipBackup", true);
                    }
                }
            }

            if (ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus())) {
                stoppedReplicaSetMaintainService.dispatchStartTask(requestId, replicaSet);
            }

            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask(replicaSet, taskKey, parameter, WorkFlowService.TASK_PRIORITY_COMMON);

            // TODO 集团TDDL链路实例先不要在这里做标记删除
            if (!ReplicaSetService.isTDDL(replicaSet) || isTddlTaskMigrate) {
                replicaSet.setStatus(ReplicaSet.StatusEnum.DELETING);
                dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, dbInstanceName, replicaSet);
            }

            data.put("TaskId", taskId);
            data.put("DBInstanceName", dbInstanceName);
            return data;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private String getRsTemplateForReplicaSet(String requestId, ReplicaSet replicaSet) throws ApiException {
        boolean isSingleTenant = podParameterHelper.isSingleTenant(replicaSet);
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId,
                replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), false);

        return podTemplateHelper.getBizSysScheduleTemplateName(requestId,
                replicaSet.getBizType(), replicaSet.getService(), instanceLevel, isSingleTenant, replicaSet.getInsType().toString(),
                podParameterHelper.getUidByLoginId(replicaSet.getUserId()));
    }

}
