package com.aliyun.dba.poddefault.action.support;

import com.alibaba.cobar.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.ClusterParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.ClustersQuery;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.dataobject.RegionAVZonDO;
import com.aliyun.dba.resource.dataobject.RegionAVZoneQuery;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;


/**
 * <AUTHOR> on 2020/6/7.
 */
@Component
public class PodAvzSupport {
    private static final LogAgent logger = LogFactory.getLogAgent(PodAvzSupport.class);
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ResourceService resourceService;
    @Resource
    private ClusterIDao clusterIDao;

    public AVZInfo getAVZInfoFromCustInstance(CustInstanceDO custins) throws RdsException {
        ParamConstants.DispenseMode dispenseMode = custinsParamService.getDispenseMode(custins.getId());
        String regionID = null;
        String regionCategory = null;
        String region = null;
        String zoneID = null;
        MultiAVZExParamDO multiAVZExParamDO = null;
        if (dispenseMode.equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
            region = custinsParamService.getMasterLocation(custins.getId());
            multiAVZExParamDO = custinsParamService.getMultiAVZExParam(custins.getId());
            RegionAVZoneQuery query = new RegionAVZoneQuery();
            query.setSubDomain(region);
            List<RegionAVZonDO> regionAVZoneList = resourceService.getRegionAVZoneList(query);
            if (CollectionUtils.isEmpty(regionAVZoneList)) {
                throw new RdsException(ErrorCode.INVALID_REGION);
            }
            RegionAVZonDO regionAVZonDO = regionAVZoneList.get(0);
            regionID = regionAVZonDO.getRegion();
            regionCategory = regionAVZonDO.getRegionCategory();
        } else {
            region = clusterService.getRegionByCluster(custins.getClusterName());
            if (StringUtils.isNotEmpty(region)) {
                RegionAVZoneQuery query = new RegionAVZoneQuery();
                query.setSubDomain(region);
                List<RegionAVZonDO> regionAVZoneList = resourceService.getRegionAVZoneList(query);
                if (!CollectionUtils.isEmpty(regionAVZoneList)) {
                    RegionAVZonDO regionAVZonDO = regionAVZoneList.get(0);
                    regionID = regionAVZonDO.getRegion();
                    zoneID = regionAVZonDO.getAvz();
                }
            }
        }

        return new AVZInfo(dispenseMode, region, zoneID, null, regionID, regionCategory, multiAVZExParamDO);
    }

    public AVZInfo getAVZInfo(ReplicaSet replicaSet) throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(replicaSet.getId().intValue());
        custins.setClusterName(replicaSet.getResourceGroupName());
        return getAVZInfoFromCustInstance(custins);
    }

    public AVZInfo getAVZInfo(Map<String, String> actionParams) throws RdsException {
        String region = CustinsParamSupport.getParameterValue(actionParams, "region");
        if (region == null) {
            region = CustinsParamSupport.getParameterValue(actionParams, "SubDomain");
        }
        ParamConstants.DispenseMode dispenseMode = getDispenseMode(actionParams);
        String regionID = CustinsParamSupport.getParameterValue(actionParams, "regionid");
        MultiAVZExParamDO multiAvzInfo = null;
        String regionCategory = null;
        String zoneId = CustinsParamSupport.getParameterValue(actionParams, ParamConstants.ZONE_ID);
        String vSwitchId = CustinsParamSupport.getParameterValue(actionParams, ParamConstants.VSWITCH_ID);
        // FIXME 专享主机必传clusterName
        String clusterName = CustinsParamSupport.getParameterValue(actionParams, ParamConstants.CLUSTER_NAME);
        if (dispenseMode.equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
            multiAvzInfo = getMultiAVZExParam(actionParams);
            regionCategory = clusterName != null && clusterService.checkIsUserCluster(clusterName) ? null : getRegionCategory(region);
        }
        return new AVZInfo(dispenseMode, region, zoneId, vSwitchId, regionID, regionCategory, multiAvzInfo);
    }

    public ParamConstants.DispenseMode getDispenseMode(Map<String, String> actionParams) throws RdsException {
        ParamConstants.DispenseMode[] dispenseModes = ParamConstants.DispenseMode.values();
        String dispenseModeStr = CustinsParamSupport.getParameterValue(actionParams, ParamConstants.DISPENSE_MODE);
        if (StringUtil.isEmpty(dispenseModeStr)) {
            dispenseModeStr = "0";
        }
        if (StringUtil.isInt(dispenseModeStr)) {
            int dispenseModeIndex = CheckUtils.parseInt(dispenseModeStr, 0
                    , dispenseModes.length - 1, ErrorCode.INVALID_PARAMETERS);
            return dispenseModes[dispenseModeIndex];
        }
        return ParamConstants.DispenseMode.valueOf(dispenseModeStr);
    }


    public MultiAVZExParamDO getMultiAVZExParam(Map<String, String> actionParams) throws RdsException {
        String multiAVZExParamStr = CustinsParamSupport.getParameterValue(actionParams, ParamConstants.MULTI_AVZ_EX_PARAM);
        if (!StringUtil.isEmpty(multiAVZExParamStr)) {
            try {
                MultiAVZExParamDO multiAVZExParamDO = JSON.parseObject(multiAVZExParamStr, MultiAVZExParamDO.class);
                if (CollectionUtils.isNotEmpty(multiAVZExParamDO.getAvailableZoneInfoList())) {
                    multiAVZExParamDO.getAvailableZoneInfoList().forEach(v -> v.setUserSpecified(true));
                }
                return multiAVZExParamDO;
            } catch (Exception e) {
                throw new RdsException(ErrorCode.INVALID_AVZONE);
            }
        }
        return null;
    }


    public String getRegionCategory(String region) throws RdsException {
        if (region.contains(",")) {
            region = region.split(",")[0].trim();
        }
        RegionAVZoneQuery query = new RegionAVZoneQuery();
        query.setSubDomain(region);
        List<RegionAVZonDO> regionAVZonDO = resourceService.getRegionAVZoneList(query);
        if (regionAVZonDO.isEmpty()) {
            throw new RdsException(ErrorCode.REGION_NOT_FOUND);
        }
        return regionAVZonDO.get(0).getRegionCategory();
    }

    /**
     * 接口参数异常的情况下，自动构建MultiAVZExParamDO
     * */
    public static void buildAutoMultiAvzInfo(MultiAVZExParamDO multiAvzInfo, Replica.RoleEnum[] roles, String subDomain, String zoneId, String vswId, boolean isUserSpecified) {
        List<AvailableZoneInfoDO>  availableZoneInfos = new ArrayList<>();
        for (Replica.RoleEnum role : roles) {
            String roleStr = role.toString();
            if (Arrays.asList(Replica.RoleEnum.FOLLOWER, Replica.RoleEnum.LOGGER, Replica.RoleEnum.LEARNER_BACK).contains(role)) {
                roleStr = "slave";
            } else if (Arrays.asList(Replica.RoleEnum.LEARNER).contains(role)) {
                roleStr = "master";
            }
            AvailableZoneInfoDO avzInfo = new AvailableZoneInfoDO();
            avzInfo.setRole(roleStr);
            avzInfo.setRegion(subDomain);
            avzInfo.setZoneID(zoneId);
            avzInfo.setVSwitchID(vswId);
            avzInfo.setUserSpecified(isUserSpecified);
            availableZoneInfos.add(avzInfo);
        }
        multiAvzInfo.setAvailableZoneInfoList(availableZoneInfos);
    }

    /**
     * 判断是否为云盒AZ
     * */
    public boolean isCloudBoxAz(String clusterName) {
        try {
            List<String> params = new ArrayList<String>(1);
            params.add("cloudbox");
            List<ClusterParamDO> clusterParams = clusterIDao.getClusterParams(clusterName, params);
            return clusterParams.size() > 0 && "1".equals(clusterParams.get(0).getValue());
        } catch (Exception e) {
            logger.error("check zone is cloudbox failed.", e);
        }
        return false;
    }
    public String getLocationByAz(String zoneId) {
        String defaultSubDomain = zoneId + "-aliyun";
        try {
            String RES_KEY = "K8S_BASIC_TO_STANDARD_AZ_SUBDOMAIN_MAPPING";
            ResourceDO res = resourceService.getResourceByResKey(RES_KEY);
            String jsonString = res.getRealValue();
            final HashMap<String, String> obj = new Gson().fromJson(jsonString, HashMap.class);
            if (obj == null) {
                return defaultSubDomain;
            }
            String subDomain = obj.get(zoneId);
            if (subDomain == null) {
                return defaultSubDomain;
            } else {
                return subDomain;
            }
        } catch (Exception ignored) {
            return defaultSubDomain;
        }
    }

    /**
     * 判断是否为云盒AZ
     * */
    public boolean isCloudBoxAzBySunDomain(String subDomain) {
        try {
            ClustersDO clustersDO = getClusterBySubDomain(subDomain);
            if (clustersDO != null) {
                return isCloudBoxAz(clustersDO.getClusterName());
            }
        } catch (Exception e) {
            logger.error("check zone is cloudbox failed.", e);
        }
        return false;
    }

    public ClustersDO getClusterBySubDomain(String subDomain) {
        ClustersQuery clustersQuery = new ClustersQuery();
        clustersQuery.setIsAvail(1);
        clustersQuery.setSubDomain(subDomain);
        clustersQuery.setType(0);
        clustersQuery.setDbType("global");
        List<ClustersDO> clustersDOS = clusterIDao.getClusters(clustersQuery);
        return clustersDOS.size() > 0 ? clustersDOS.get(0) : null;
    }

    /**
     * 检查云盒单可用区需求
     * */
    public void validateCloudBoxZone(String requestId, AVZInfo avzInfo) throws RdsException {
        Set<String> subDomains = new HashSet<>();
        subDomains.add(avzInfo.getRegion());
        for (AvailableZoneInfoDO slaveZoneInfo : avzInfo.getMultiAVZExParamDO().getSlaveAvailableZoneInfo()) {
            subDomains.add(slaveZoneInfo.getRegion());
        }
        if (subDomains.size() > 1) {
            for (String subDomain : subDomains) {
                if (isCloudBoxAzBySunDomain(subDomain)) {
                    logger.error("RequestId {} custins is cloudbox with multi zones {}", requestId, subDomains);
                    throw new RdsException(ErrorCode.INVALID_AVZONE);
                }
            }
        }
    }

    public boolean isMultiZone(String zone) {
        List<String> MZ_SPLIT = Arrays.asList("MAZ10", "MAZ2", "MAZ3", "MAZ4", "MAZ5", "MAZ6", "MAZ7", "MAZ8", "MAZ9", "MAZ1");

        if(StringUtils.isEmpty(zone)){
            return false;
        }
        for (String miz : MZ_SPLIT) {
            if (zone.contains(miz)) {
                return true;
            }
        }
        return false;
    }

    public List<AvailableZoneInfoDO> checkAndCorrectAvailableZoneInfo(String requestId, List<AvailableZoneInfoDO> availableZoneInfoList, Map<String, String> params) throws RdsException {
        ParamConstants.DispenseMode dispenseMode = getDispenseMode(params);
        boolean isMultiAVZDispenseMode = dispenseMode.equals(ParamConstants.DispenseMode.MultiAVZDispenseMode);
        if (isMultiAVZDispenseMode) {
            logger.info("requestId : {}, clear availableZoneInfoList", requestId);
            availableZoneInfoList.clear();
        }
        return availableZoneInfoList;
    }

}
