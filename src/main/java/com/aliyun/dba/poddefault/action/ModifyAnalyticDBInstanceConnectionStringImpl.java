package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyAnalyticDBInstanceConnectionStringImpl")
public class ModifyAnalyticDBInstanceConnectionStringImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyAnalyticDBInstanceConnectionStringImpl.class);

    @Resource
    RdsApi rdsApi;

    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private MysqlParamSupport paramSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            String analyticInsName = paramSupport.getParameterValue(params, ParamConstants.ANALYTIC_DB_INSTANCE_NAME);
            ReplicaSet analyticIns = podCommonSupport.getCkReplicaSet(analyticInsName);

            // 查询关联关系
            podCommonSupport.checkAnalyticDBInstanceRelation(requestId, custins.getInsName(), analyticInsName);

            String uid = paramSupport.getParameterValue(params, ParamConstants.UID);
            String userId = paramSupport.getParameterValue(params, ParamConstants.USER_ID);
            String regionId = paramSupport.getParameterValue(params, ParamConstants.REGION_ID);
            String connectionString = paramSupport.getParameterValue(params, ParamConstants.CONNECTION_STRING);
            String connectionStringPrefix = paramSupport.getParameterValue(params, ParamConstants.CONNECTION_STRING_PREFIX);


            logger.info("requestId : {}, request to ModifyAnalyticDBInstanceConnectionString", requestId);

            Map<String, Object> result = rdsApi.getDataByRdsApi(new HashMap<String, String>() {{
                put("Action", "ModifyDBInstanceConnectionString");
                put("DBInstanceName", analyticInsName);
                put("UID", uid);
                put("User_id", userId);
                put("RegionID", regionId);
                put("RequestId", requestId);
                put("ConnectionString", connectionString);
                put("ConnectionStringPrefix", connectionStringPrefix);
                put("DBInstanceModelType", "cluster");
            }}, ParamConstants.YAOCHI_ACCESS);

            logger.info("request : {}, result : {}", requestId, JSONObject.toJSONString(result));
            return result;

        } catch (RdsException ex) {
            log.error("ModifyAnalyticDBInstanceConnectionString failed: {}", JSONObject.toJSONString(ex));
            throw ex;
        } catch (Exception ex) {
            log.error("ModifyAnalyticDBInstanceConnectionString Exception: {}", JSONObject.toJSONString(ex));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }


}
