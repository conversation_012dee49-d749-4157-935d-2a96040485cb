package com.aliyun.dba.poddefault.action.support;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.utils.RequestSession;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Range;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

import static com.aliyun.dba.support.property.ParamConstants.AUTOPL_PROVISIONED_IOPS_DEFAULT_VALUE;

@Slf4j
@Service
public class ProvisionedIopsHelper {

    @Resource
    private ResourceService resourceService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    private static final Cache<String, Map<String, ProvisionedIopsPolicy>> resourceCache = CacheBuilder.newBuilder()
            .maximumSize(2048)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();


    private final static String RESOURCE_KEY_ADAPTIVE_PROVISIONED_IOPS_POLICY = "ADAPTIVE_PROVISIONED_IOPS_POLICY";
    private final static String CUSTINS_PARAM_CUSTOM_PROVISIONED_IOPS_POLICY = "customProvisionedIopsPolicy";
    private final static String KEY_MAX_PROVISIONED_IOPS_LIMIT = "maxProvisionedIopsLimit";
    private final static String KEY_MIN_PROVISIONED_IOPS_LIMIT = "minProvisionedIopsLimit";
    private final static String KEY_PRIORITY = "priority";
    private final static String KEY_STORAGE_RANGE_VALUE_LIMIT = "storageRangeValueLimit";
    private final static String KEY_STORAGE_RANGE_RATIO_LIMIT = "storageRangeRatioLimit";
    private final static String KEY_START = "start";
    private final static String KEY_END = "end";
    private final static String KEY_VALUE = "value";

    private final static String KEY_DEFAULT = "default";


    /**
     * get default provisionedIops from dbaas.resource and dbaas.custins_param
     * resource:
     * key:ADAPTIVE_PROVISIONED_IOPS_POLICY
     * value: {
     *     "default": {
     *         "maxProvisionedIopsLimit": 25000,
     *         "minProvisionedIopsLimit": 0
     *         "priority": 1
     *         "storageRangeValueLimit": 【
     *              { "start": 0, "end": 49, "value": 0 },
     *              { "start": 50, "end": 99, "value": 0 },
     *              { "start": 100, "end": 199, "value": 0 },
     *              { "start": 200, "end": 299, "value":1000 },
     *          ],
     *          "storageRangeRatioLimit": 【
     *              { "start": 0, "end": 49, "value": 0 },
     *              { "start": 50, "end": 99, "value": 0 },
     *              { "start": 100, "end": 199, "value": 0 },
     *              { "start": 200, "end": 299, "value":10 },
     *          ]
     *     },
     *     "uid":{}
     * }
     * <p>
     * custins_param:
     * key:
     * customProvisionedIopsPolicy
     * value:
     *      {
     *         "maxProvisionedIopsLimit": 25000,
     *         "minProvisionedIopsLimit": 0
     *         "priority": 1
     *         "storageRangeValueLimit": 【
     *              { "start": 0, "end": 49, "value": 0 },
     *              { "start": 50, "end": 99, "value": 0 },
     *              { "start": 100, "end": 199, "value": 0 },
     *              { "start": 200, "end": 299, "value":1000 },
     *          ],
     *          "storageRangeRatioLimit": 【
     *              { "start": 0, "end": 49, "value": 0 },
     *              { "start": 50, "end": 99, "value": 0 },
     *              { "start": 100, "end": 199, "value": 0 },
     *              { "start": 200, "end": 299, "value":10 },
     *          ]
     *      }
     */
    public Long getDefaultProvisionedIops(Integer diskSizeGB, String uid, String replicaSetName) {
        ProvisionedIopsPolicy policy = getProvisionedIopsPolicy(uid, replicaSetName);
        Long defaultProvisionedIops = getProvisionedIopsByPolicy(diskSizeGB, policy);
        if (!PodParameterHelper.checkProvisionedIopsValueValid(defaultProvisionedIops, diskSizeGB)) {
            log.warn(String.format("ProvisionedIopsByPolicy is not valid, use default provisioned iops [%s]", AUTOPL_PROVISIONED_IOPS_DEFAULT_VALUE));
            defaultProvisionedIops = Long.valueOf(AUTOPL_PROVISIONED_IOPS_DEFAULT_VALUE);
        }
        return defaultProvisionedIops;
    }


    private ProvisionedIopsPolicy getProvisionedIopsPolicy(String uid, String replicaSetName) {
        // get resource policy Map
        Map<String, ProvisionedIopsPolicy> resourcePolicyMap = new ConcurrentHashMap<>();
        try {
            resourcePolicyMap = resourceCache.get(RESOURCE_KEY_ADAPTIVE_PROVISIONED_IOPS_POLICY, new Callable<Map<String, ProvisionedIopsPolicy>>() {
                @Override
                public Map<String, ProvisionedIopsPolicy> call() throws Exception {
                    return getResourceProvisionedIopsPolicy();
                }
            });
        } catch (ExecutionException e) {
            log.warn("get resource RESOURCE_KEY_ADAPTIVE_PROVISIONED_IOPS_POLICY failed, errorMsg: {}", e.getMessage());
        }
        // get provisionedIops policy
        List<String> resourceKeys = Arrays.asList(KEY_DEFAULT, uid);
        ProvisionedIopsPolicy customPolicy = getCustomProvisionedPolicyFromDB(replicaSetName);
        return getProvisionedIopsPolicyByKeysV2(resourcePolicyMap, resourceKeys, customPolicy);
    }

    private Long getProvisionedIopsByPolicy(Integer diskSizeGB, ProvisionedIopsPolicy policy) {
        // check diskSize
        if (diskSizeGB == null || diskSizeGB < 0) {
            log.warn("Invalid diskSizeGB: {}. Returning default IOPS value.", diskSizeGB);
            return Long.valueOf(AUTOPL_PROVISIONED_IOPS_DEFAULT_VALUE);
        }
        // check policy
        if (Objects.isNull(policy) || Objects.isNull(policy.getMaxProvisionedIopsLimit()) || Objects.isNull(policy.getMinProvisionedIopsLimit())) {
            log.warn("RESOURCE_KEY_ADAPTIVE_PROVISIONED_IOPS_POLICY invalid, use default provisionedIops: {}", AUTOPL_PROVISIONED_IOPS_DEFAULT_VALUE);
            return Long.valueOf(AUTOPL_PROVISIONED_IOPS_DEFAULT_VALUE);
        }

        // get provisionedIops value by policy
        if (CollectionUtils.isNotEmpty(policy.getStorageRangeValueLimit())) {
            for (RangeValue rangeValue : policy.getStorageRangeValueLimit()) {
                if (rangeValue.getRange().contains(diskSizeGB)) {
                    return (long) Math.max(policy.getMinProvisionedIopsLimit(), Math.min(policy.getMaxProvisionedIopsLimit(), rangeValue.getValue()));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(policy.getStorageRangeRatioList())) {
            for (RangeValue rangeValue : policy.getStorageRangeRatioList()) {
                if (rangeValue.getRange().contains(diskSizeGB)) {
                    return (long) Math.max(policy.getMinProvisionedIopsLimit(), Math.min(policy.getMaxProvisionedIopsLimit(), diskSizeGB * rangeValue.getValue()));
                }
            }
        }
        return Long.valueOf(policy.getMinProvisionedIopsLimit());
    }



    private ProvisionedIopsPolicy getCustomProvisionedPolicyFromDB(String replicaSetName) {
        String customPolicyStr = null;
        ProvisionedIopsPolicy customPolicy = null;
        if (StringUtils.isBlank(replicaSetName)) {
            return customPolicy;
        }
        try {
            customPolicyStr = dBaasMetaService.getDefaultClient().getReplicaSetLabel(RequestSession.getRequestId(), replicaSetName, CUSTINS_PARAM_CUSTOM_PROVISIONED_IOPS_POLICY);
        } catch (ApiException e) {
            log.warn("get custins_param customProvisionedIopsPolicy failed, errMsg: {}", e.getMessage());
        }
        if (StringUtils.isNotBlank(customPolicyStr)) {
            customPolicy = parsePolicy(customPolicyStr);
        }
        return customPolicy;
    }


    private static ProvisionedIopsPolicy getProvisionedIopsPolicyByKeysV2(Map<String, ProvisionedIopsPolicy> policyMap, List<String> resourceValueKeys, ProvisionedIopsPolicy customPolicy) {
        PriorityBlockingQueue<ProvisionedIopsPolicy> candidatePolicyList = new PriorityBlockingQueue<>(11, (o1, o2) -> o2.getPriority() - o1.getPriority());
        if (CollectionUtils.isNotEmpty(resourceValueKeys) && MapUtils.isNotEmpty(policyMap)) {
            log.info("resourceValueKeys [{}] not null", resourceValueKeys);
            for (String resourceKey : resourceValueKeys) {
                if (policyMap.containsKey(resourceKey)) {
                    candidatePolicyList.add(policyMap.get(resourceKey));
                }
            }
        }
        if (Objects.nonNull(customPolicy)) {
            log.info("customPolicy [{}] not null", customPolicy);
            candidatePolicyList.add(customPolicy);
        }
        if (candidatePolicyList.isEmpty()) {
            log.warn("policyMap and resourceValueKeys not match , and customPolicy is null, return null policy");
            return null;
        }
        return candidatePolicyList.peek();
    }

    private Map<String, ProvisionedIopsPolicy> getResourceProvisionedIopsPolicy() {
        ResourceDO resourceDO = resourceService.getResourceByResKey(RESOURCE_KEY_ADAPTIVE_PROVISIONED_IOPS_POLICY);
        Map<String, ProvisionedIopsPolicy> result = new ConcurrentHashMap<>();
        if (Objects.nonNull(resourceDO) && Objects.nonNull(resourceDO.getRealValue())) {
            String resValue = resourceDO.getRealValue();
            if (Objects.isNull(resValue)) {
                log.info("Resource [{}] is null, stop initProvisionedIopsPolicy", RESOURCE_KEY_ADAPTIVE_PROVISIONED_IOPS_POLICY);
                return result;
            }
            JSONObject jsonObject = JSONObject.parseObject(resValue);
            for (String key : jsonObject.keySet()) {
                String policy = jsonObject.getString(key);
                if (Objects.isNull(policy)) {
                    continue;
                }
                result.put(key, parsePolicy(policy));
            }
        }
        return result;
    }

    private ProvisionedIopsPolicy parsePolicy(String policy) {
        ProvisionedIopsPolicy provisionedIopsPolicy = new ProvisionedIopsPolicy();
        JSONObject policyJson = JSONObject.parseObject(policy);
        if (Objects.nonNull(policyJson) && policyJson.containsKey(KEY_MAX_PROVISIONED_IOPS_LIMIT)) {
            Integer value = policyJson.getInteger(KEY_MAX_PROVISIONED_IOPS_LIMIT);
            if (Objects.nonNull(value)) {
                provisionedIopsPolicy.setMaxProvisionedIopsLimit(value);
            }
        }
        if (Objects.nonNull(policyJson) && policyJson.containsKey(KEY_MIN_PROVISIONED_IOPS_LIMIT)) {
            Integer value = policyJson.getInteger(KEY_MIN_PROVISIONED_IOPS_LIMIT);
            if (Objects.nonNull(value)) {
                provisionedIopsPolicy.setMinProvisionedIopsLimit(value);
            }
        }
        if (Objects.nonNull(policyJson) && policyJson.containsKey(KEY_PRIORITY)) {
            Integer value = policyJson.getInteger(KEY_PRIORITY);
            if (Objects.nonNull(value)) {
                provisionedIopsPolicy.setPriority(value);
            }
        }
        if (Objects.nonNull(policyJson) && policyJson.containsKey(KEY_STORAGE_RANGE_VALUE_LIMIT)) {
            String storageRangeValue = policyJson.getString(KEY_STORAGE_RANGE_VALUE_LIMIT);
            if (Objects.nonNull(storageRangeValue)) {
                provisionedIopsPolicy.setStorageRangeValueLimit(parseRangeValue(storageRangeValue));
            }
        }
        if (Objects.nonNull(policyJson) && policyJson.containsKey(KEY_STORAGE_RANGE_RATIO_LIMIT)) {
            String storageRangeRatio = policyJson.getString(KEY_STORAGE_RANGE_RATIO_LIMIT);
            if (Objects.nonNull(storageRangeRatio)) {
                provisionedIopsPolicy.setStorageRangeRatioList(parseRangeValue(storageRangeRatio));
            }
        }
        return provisionedIopsPolicy;
    }


    private List<RangeValue> parseRangeValue(String storageRangeValue) {
        List<RangeValue> result = new ArrayList<>();
        try {
            JSONArray jsonArray = JSONArray.parseArray(storageRangeValue);
            if (Objects.isNull(jsonArray)) {
                return result;
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                int start = jsonObject.getIntValue(KEY_START);
                int end = jsonObject.getIntValue(KEY_END);
                int value = jsonObject.getIntValue(KEY_VALUE);
                result.add(new RangeValue(Range.closed(start, end), value));
            }
        } catch (Exception e) {
            log.error("Error parsing storage range value: {}", e.getMessage());
        }
        return result;
    }


    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    static class RangeValue {
        /**
         * storage range,，close
         */
        Range<Integer> range;
        /**
         * ratio or value
         */
        Integer value;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    static class ProvisionedIopsPolicy {
        Integer maxProvisionedIopsLimit;
        Integer minProvisionedIopsLimit = 0;
        Integer priority = 0;
        List<RangeValue> storageRangeValueLimit;
        List<RangeValue> storageRangeRatioList;
    }
}
