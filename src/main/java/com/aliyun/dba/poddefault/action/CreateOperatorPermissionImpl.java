package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.*;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateOperatorPermissionImpl")
public class CreateOperatorPermissionImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateOperatorPermissionImpl.class);

    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try{
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!(replicaSet.getStatus() == ReplicaSet.StatusEnum.ACTIVATION || replicaSet.getStatus() == ReplicaSet.StatusEnum.BACKING
                    || replicaSet.getStatus() == ReplicaSet.StatusEnum.RESTARTING || replicaSet.getStatus() == ReplicaSet.StatusEnum.TRANSING)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            // created time
            String createdTime = DateSupport.second2str_gmt0(new Date());
            Date expiredTime = null;
            // expired time
            if (parameterHelper.hasParameter(ParamConstants.EXPIRED_TIME)) {
                expiredTime = parameterHelper.getAndCheckTimeByParam(ParamConstants.EXPIRED_TIME,
                        DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_EXPIREDTIME);
                if (expiredTime.getTime() <= System.currentTimeMillis()) {
                    return ResponseSupport.createErrorResponse(ErrorCode.INVALID_EXPIREDTIME);
                }
            }else{
                expiredTime = DateUtils.addHours(new Date(), 24);
            }
            int accountPrivilege = Integer.parseInt(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_PRIVILEGE, AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()));
            if(accountPrivilege != AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue() && accountPrivilege != AccountPriviledgeType.PRIVILEDGE_INNER_USER_GRANT.getValue()){
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_ACCOUNTPRIVILEGE);
            }
            // OperatorType, support: Crontrol,Data
            String operatorType = parameterHelper.getParameterValue(ParamConstants.OPERATOR_TYPE);
            List<String> operatorTypeList = new ArrayList<String>(2);
            if (StringUtils.isBlank(operatorType)){
                return ResponseSupport.createErrorResponse(ErrorCode.MISSING_OPERATOR_TYPE);
            }else{
                String[] operatorTypeSet = SupportUtils.splitToArray(operatorType, ",");
                for (String operator:operatorTypeSet){
                    if(operator.equals(CustinsSupport.OPERATOR_TYPE_CONTROL) || operator.equals(CustinsSupport.OPERATOR_TYPE_DATA)){
                        // 杜康不能设置或包含OperatorType为Control
                        if (operator.equals(CustinsSupport.OPERATOR_TYPE_CONTROL) && accountPrivilege == AccountPriviledgeType.PRIVILEDGE_INNER_USER_GRANT.getValue()){
                            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_OPERATOR_TYPE);
                        }
                        if (!operatorTypeList.contains(operator)){
                            operatorTypeList.add(operator);
                        }
                    }else{
                        return ResponseSupport.createErrorResponse(ErrorCode.INVALID_OPERATOR_TYPE);
                    }
                }
            }
            String paramKey;
            Map<String, Object> user_property = new HashMap<>();;
            user_property.put("created_time", createdTime);
            user_property.put("expired_time", DateSupport.second2str_gmt0(expiredTime));
            String newParamGroupInfo = JSONObject.toJSONString(user_property);
            if (accountPrivilege == AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()){
                paramKey = CustinsParamSupport.CUSTINS_PARAM_NAME_USER_GRANT_INFO;
            }else if(accountPrivilege == AccountPriviledgeType.PRIVILEDGE_INNER_USER_GRANT.getValue()){
                paramKey = CustinsParamSupport.CUSTINS_PARAM_NAME_INNER_GRANT_INFO;
            }else{
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_ACCOUNTPRIVILEGE);
            }
            if (operatorTypeList.contains(CustinsSupport.OPERATOR_TYPE_CONTROL)){
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSet.getName(),
                        ImmutableMap.of(paramKey, newParamGroupInfo, CustinsParamSupport.CUSTINS_PARAM_NAME_SYSTEM_OPERATOR,CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SYSTEM_OPERATION));
                logger.info("update custins_param: replicaSetName = {}, paramKey = {}, system_operator = {}",
                        replicaSet.getName(), newParamGroupInfo,CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SYSTEM_OPERATION);
            }else if(operatorTypeList.contains(CustinsSupport.OPERATOR_TYPE_DATA)){
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSet.getName(),
                        ImmutableMap.of(paramKey, newParamGroupInfo, PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER, PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER_VALUE));
                logger.info("update custins_param: replicaSetName = {}, paramKey = {}, k8s_data_permission_identifier = {}",
                        replicaSet.getName(), newParamGroupInfo, PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER_VALUE);
            }else{
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSet.getName(),
                        ImmutableMap.of(paramKey, newParamGroupInfo));
                logger.info("update custins_param: replicaSetName = {}, paramKey = {}",
                        replicaSet.getName(), newParamGroupInfo);
            }

            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
            data.put(ParamConstants.EXPIRED_TIME, DateSupport.second2str_gmt0(expiredTime));
            return data;
        }catch (RdsException re) {
            logger.error(requestId + " CreateOperatorPermission failed: " + re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " CreateOperatorPermission failed: " + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}