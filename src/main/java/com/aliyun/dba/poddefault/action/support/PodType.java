package com.aliyun.dba.poddefault.action.support;

/**
 * <AUTHOR> on 2024/10/31
 */
public enum PodType {
    POD_ECS_RUND("rund", "ecs"),
    POD_VBM_RUND("rund", "vbm"),
    POD_RUNC("runc", "ecs");
    private String runtimeType;
    private String hostType;

    PodType(String runtimeType, String hostType) {
        this.runtimeType = runtimeType;
        this.hostType = hostType;
    }

    public static PodType getMatchedRuntimeType(String type) {
        try {
            for (PodType podType : PodType.values()) {
                String combinedType = podType.hostType + "_" + podType.runtimeType;
                if (combinedType.equals(type)) {
                    return podType;
                }
            }
        } catch (Exception e) {
            return PodType.POD_RUNC;
        }
        return PodType.POD_RUNC;
    }

    public String getRuntimeType() {
        return runtimeType;
    }
}
