package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.backup.BackupSetParam;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDispatchReplicaChecksumImpl")
@Slf4j
public class DispatchReplicaChecksumImpl implements IAction {

    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private PodAvzSupport avzSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private BakService bakService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private BackupService backupService;
    @Resource
    protected ResourceService resourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String destReplicaSetName = null;
        boolean isSuccess = false;
        boolean isAllocated = false;
        ReplicaSet replicaSet = null;

        try {
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);

            // 只读实例不做check
            if (!replicaSet.getInsType().equals(ReplicaSet.InsTypeEnum.MAIN)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            String dbInstanceName = replicaSet.getName();
            destReplicaSetName = String.format("checksum-%s", dbInstanceName);
            ReplicaSet destReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, destReplicaSetName, true);
            if (destReplicaSet != null) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }

            Replica masterReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems()
                    .stream().filter(replica -> replica.getRole().equals(Replica.RoleEnum.MASTER)).collect(Collectors.toList()).get(0);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);

            String printLogsString = mysqlParamSupport.getParameterValue(params, "printLogs");
            Integer printLogs = 0;
            if (printLogsString != null) {
                printLogs = CheckUtils.parseInt(printLogsString, null, null, ErrorCode.INVALID_PARAM);
            }


            // 获取快照信息
            String backupSetId = mysqlParamSupport.getParameterValue(params, ParamConstants.BACKUP_SET_ID);  // 指定备份集
            BakhistoryDO bakHistory;
            if (StringUtils.isNotEmpty(backupSetId)) {
                bakHistory = bakService.getBakhistoryByBackupSetId(replicaSet.getId().intValue(), Long.valueOf(backupSetId));
                if (bakHistory == null) {
                    log.error("backup_set {} not found.", backupSetId);
                    throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
                } else if (!"ok".equalsIgnoreCase(bakHistory.getStatus()) || bakHistory.getIsAvail() != 1) {
                    log.error("backup_set {} status {} isAvail {}.", backupSetId, bakHistory.getStatus(), bakHistory.getIsAvail());
                    throw new RdsException(ErrorCode.INVALID_BAKSET);
                }
            } else {  // 不指定备份集
                Date latestTime = new Date();
                bakHistory = bakService.getBakhistoryByRecoverTime(replicaSet.getId().intValue(), latestTime, null, BakSupport.BAKTYPE_FULL);
            }
            if (bakHistory == null) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
            String snapshot = getTargetSnapshotId(replicaSet, user, bakHistory);
            boolean isPengineBackupSet = !bakHistory.getKindCode().equals(KIND_CODE_NEW_ARCH);

            ReplicaSetResourceRequest request = buildReplicaSetResource(requestId, user, replicaSet, masterReplica, destReplicaSetName, snapshot);
            log.info("start allocate resource: {}", JSON.toJSONString(request));
            isAllocated = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, destReplicaSetName, request);

            // 订正元数据，改成临时实例
            destReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, destReplicaSetName, false);
            destReplicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
            dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, destReplicaSetName, destReplicaSet);
            Map<String, String> destLabels = destReplicaSet.getLabels();
            if (destLabels == null) {
                destLabels = new HashMap<>();
            }
            destLabels.put(MySQLParamConstants.IS_INTERNAL, "1");
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, destReplicaSetName, destLabels);

            String taskKey = PodDefaultConstants.CHECK_REPLICA_IBD;
            String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            JSONObject taskParam = new JSONObject();
            taskParam.put("srcReplicaSetName", dbInstanceName);
            taskParam.put("destReplicaSetName", destReplicaSetName);
            taskParam.put("isPengineBackupSet", isPengineBackupSet);
            taskParam.put("printLogs", printLogs);
            taskParam.put("bakHisID", bakHistory.getHisId());

            Object taskId = workFlowService.dispatchTask("custins", destReplicaSetName, domain, taskKey, taskParam.toJSONString(), 0);

            Map<String, Object> ret = new HashMap<>();
            ret.put("TaskId", taskId);
            ret.put(ParamConstants.DB_INSTANCE_NAME, destReplicaSetName);
            ret.put(ParamConstants.DB_INSTANCE_ID, destReplicaSet.getId());
            ret.put(ParamConstants.BACKUP_SET_ID, bakHistory.getHisId());
            isSuccess = true;
            return ret;
        } catch (RdsException e) {
            log.error("execute failed with exception: {}", JSON.toJSONString(e));
            throw e;
        } catch (ApiException e) {
            log.error("common api calling, allocate resource failed: {}", JSON.toJSONString(e));
            return createErrorResponse(ErrorCode.RESOURCE_NOT_ENOUGH);
        } catch (Exception e) {
            log.error("dispatch checksum task failed: {}", JSON.toJSONString(e));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isSuccess && isAllocated) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, destReplicaSetName);
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    log.error("release replicaSet failed, {}", JSON.toJSONString(e));
                }
            }
        }

    }

    /**
     * 构造资源申请请求
     */
    private ReplicaSetResourceRequest buildReplicaSetResource(String requestId,
                                                              User user,
                                                              ReplicaSet srcReplicaSet,
                                                              Replica masterReplica,
                                                              String destReplicaSetName,
                                                              String snapshot) throws Exception {
        AVZInfo avzInfo = avzSupport.getAVZInfo(srcReplicaSet);
        String storageType = replicaSetService.getReplicaSetStorageType(srcReplicaSet.getName(), requestId);

        ResourceDO resourceDO = resourceService.getResourceByResKey(PodDefaultConstants.CHECK_IBD_INSTANCE_LEVEL_KEY);
        String classCode;
        if (resourceDO != null) {
            String instanceLevelMapper = resourceDO.getRealValue();
            if (StringUtils.isBlank(instanceLevelMapper)) {
                throw new RdsException(ErrorCode.INVALID_KEY);
            }
            JSONObject mapper = (JSONObject) JSON.parse(instanceLevelMapper);
            classCode = mapper.getString("ClassCode");
        }
        else {
            classCode = srcReplicaSet.getClassCode();
        }
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, srcReplicaSet.getService(), srcReplicaSet.getServiceVersion(), classCode, false);
        Boolean isSingleTenant = replicaSetService.isCloudSingleTenant(srcReplicaSet.getBizType(), storageType, instanceLevel, false);

        int extendDiskSizeGB = podParameterHelper.getExtendDiskSizeGBForPod(srcReplicaSet.getBizType(), false, srcReplicaSet.getDiskSizeMB() / 1024);

        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
        replicaSetResourceRequest
                .userId(user.getBid())  // user信息
                .uid(user.getAliUid())
                .insType(ReplicaSet.InsTypeEnum.MAIN.toString())  // 实例类型
                .replicaSetName(destReplicaSetName)  // 实例名
                .connType(MySQLParamConstants.CONN_TYPE_PHYSICAL)  // 链路类型
                .dbType(srcReplicaSet.getService())  // 引擎类型
                .dbVersion(srcReplicaSet.getServiceVersion())  // 引擎版本
                .bizType(srcReplicaSet.getBizType().toString())  // bizType
                .singleTenant(isSingleTenant)
                .classCode(instanceLevel.getClassCode())  // 规格
                .subDomain(avzInfo.getRegion())  // subDomain
                .regionId(avzInfo.getRegionId())  // region
                .storageType(storageType)  // 存储类型 essd、
                .catagory(instanceLevel.getCategory().toString())  // 系列
                .diskSize(extendDiskSizeGB)  // 存储大小
                .composeTag(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(requestId, srcReplicaSet.getName()))  // serviceSpec
                .eniDirectLink(false)
                .ignoreCreateVpcMapping(true);  // 忽略vpcMapping

        Pair<String, ScheduleTemplate> scheduleTemplate = podTemplateHelper.getBizSysScheduleTemplate(PodType.POD_RUNC,
                srcReplicaSet.getBizType(), srcReplicaSet.getService(), instanceLevel, isSingleTenant,
                ReplicaSet.InsTypeEnum.MAIN.toString(), srcReplicaSet.getName(), null, user.getAliUid());
        replicaSetResourceRequest.setScheduleTemplate(scheduleTemplate.getValue());

        // 初始化 replica 信息
        ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
        replicaResourceRequest
                .hostName(podParameterHelper.getRoleHostNameMapping().get(Replica.RoleEnum.MASTER))
                .role(Replica.RoleEnum.MASTER.toString())
                .storageType(storageType)
                .singleTenant(isSingleTenant)
                .zoneId(masterReplica.getZoneId())
                .diskSize(extendDiskSizeGB)
                .classCode(instanceLevel.getClassCode());
        //  volume
        VolumeSpec volumeSpec = new VolumeSpec();
        volumeSpec.setSnapshotId(snapshot);
        volumeSpec.setName("data");
        volumeSpec.setPerformanceLevel(replicaSetService.getVolumePerfLevel(requestId, srcReplicaSet.getName(), storageType));
        replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
        List<ReplicaResourceRequest> replicaResourceRequestList = new ArrayList<>();
        replicaResourceRequestList.add(replicaResourceRequest);
        replicaSetResourceRequest.setReplicaResourceRequestList(replicaResourceRequestList);

        // mock
        podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);

        return replicaSetResourceRequest;
    }

    private String getTargetSnapshotId(ReplicaSet replicaSet, User user, BakhistoryDO bakHistory) throws RdsException, BaseServiceException {
        GetBackupSetResponse backupSetResponse;
        try {
            log.info("Start query backupSet: " + bakHistory.getHisId());
            backupSetResponse = backupService.getBackupSet(
                    BackupSetParam.builder()
                            .uid(user.getAliUid())
                            .user_id(user.getBid())
                            .dBInstanceId(replicaSet.getId().intValue())
                            .backupSetId(Long.valueOf(bakHistory.getHisId()))
                            .build()
            );
        } catch (BaseServiceException ex) {
            log.error("GetBackupSet failed: ", ex);
            if (StringUtils.equalsIgnoreCase(ex.getCode(), "InvalidBackupSetID.NotFound")) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
            throw ex;
        }
        log.info("GetBackupSet success: {}", JSON.toJSONString(backupSetResponse));

        return  backupSetResponse.getSlaveStatusObj().getSnapshotId();
    }
}
