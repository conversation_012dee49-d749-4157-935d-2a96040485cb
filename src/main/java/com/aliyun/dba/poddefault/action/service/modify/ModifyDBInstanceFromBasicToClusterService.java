package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.poddefault.action.service.AutomaticSlaveZoneService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.google.gson.Gson;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE;

@Service
public class ModifyDBInstanceFromBasicToClusterService extends BaseModifyDBInstanceService {


    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;

    @Resource
    private PodAvzSupport podAvzSupport;

    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private EcsDBService ecsDBService;

    @Resource
    private AutomaticSlaveZoneService automaticSlaveZoneService;

    /**
     * 变配实例
     *
     * @param custins
     * @param params
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();
        Integer transListId;
        boolean isSuccess = false;
        boolean isAllocated = false;

        try {
            // 初始化变配参数
            PodModifyInsParam modifyInsParam = initPodModifyInsParam(params);
            custins = modifyInsParam.getCustins();

            /********** 过滤条件 Start **********/

            podCommonSupport.checkBasicToClusterCondition(requestId, modifyInsParam.getReplicaSetMeta(), modifyInsParam.getSrcInstanceLevel());

            // 过滤测试的service_spec
            if (minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId()).startsWith(MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE)) {
                 throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }
            AVZInfo avzInfo = modifyInsParam.getAvzInfo();

            /********** 过滤条件 End **********/

            Replica masterReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                            modifyInsParam.getRequestId(),
                            modifyInsParam.getDbInstanceName(),
                            null,
                            null,
                            null,
                            null)
                    .getItems()
                    .stream()
                    .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER))
                    .findFirst()
                    .get();

            String masterZoneId = masterReplica.getZoneId();
            boolean isAutomaticSlaveZone = automaticSlaveZoneService.isAutomaticSlaveZone();


            String slaveAz = null;
            String slaveSubDomain = null;

            if (podCommonSupport.isIoAccelerationEnabled(modifyInsParam.getGeneralCloudDisk())) {
                slaveAz = automaticSlaveZoneService.getRandomSlaveZones(avzInfo.getRegionId(), masterZoneId, modifyInsParam.getGeneralCloudDisk());
                slaveSubDomain = automaticSlaveZoneService.getLocationByAz(slaveAz);
            } else if (isAutomaticSlaveZone) {
                slaveAz = PodDefaultConstants.ZONE_AUTOMATIC;
            } else {
                slaveAz = automaticSlaveZoneService.getRandomSlaveZones(avzInfo.getRegionId(), masterZoneId, null);
                slaveSubDomain = automaticSlaveZoneService.getLocationByAz(slaveAz);
            }

            if (!slaveAz.isEmpty()) {
                logger.info("slaveAz is {}, requestId : {}", JSONObject.toJSONString(slaveAz), modifyInsParam.getRequestId());
            }
            if (!slaveSubDomain.isEmpty()) {
                logger.info("slaveSubDomain is {}, requestId : {}", JSONObject.toJSONString(slaveSubDomain), modifyInsParam.getRequestId());
            }

            Map<Replica.RoleEnum, String> azMap = new HashMap<>();
            azMap.put(Replica.RoleEnum.SLAVE, slaveAz);

            Map<Replica.RoleEnum, String> subDomainMap = new HashMap<>();

            if (!isAutomaticSlaveZone && !slaveAz.equalsIgnoreCase(masterZoneId)) {
                logger.info("Use slave subDomain: {}", slaveSubDomain);
                subDomainMap.put(Replica.RoleEnum.SLAVE, slaveSubDomain);
            }

            AllocateTmpResourceResult result = allocateTmpResourceService.make(requestId,
                    custins,
                    modifyInsParam,
                    String.valueOf(InstanceLevel.CategoryEnum.CLUSTER),
                    Arrays.asList(Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE),
                    azMap,
                    subDomainMap);


            isAllocated = result.isAllocated();
            transListId = result.getTransList().getId();
            resourceRequest = result.getResourceRequest();


            MultiAVZExParamDO multiAVZExParamDO = avzInfo.getMultiAVZExParamDO();
            List<AvailableZoneInfoDO> availableZoneInfoList = multiAVZExParamDO.getAvailableZoneInfoList();

            availableZoneInfoList = podAvzSupport.checkAndCorrectAvailableZoneInfo(requestId, availableZoneInfoList, params);

            String masterLocation = avzInfo.getRegion(); // Master 子域
            AvailableZoneInfoDO masterDO = new AvailableZoneInfoDO(masterLocation, "master");


            Replica tmpSlaveReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                            modifyInsParam.getRequestId(),
                            result.getReplicaSet().getName(),
                            null,
                            null,
                            null,
                            null)
                    .getItems()
                    .stream()
                    .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.SLAVE))
                    .findFirst()
                    .get();
            String slaveLocation = isAutomaticSlaveZone ? "" : tmpSlaveReplica.getSubDomain(); //自动选择的话，备库的子域可以不设置，标识自动分配
            AvailableZoneInfoDO slaveDO = new AvailableZoneInfoDO(slaveLocation, "slave");


            masterDO.setZoneID(masterReplica.getZoneId());
            slaveDO.setZoneID(tmpSlaveReplica.getZoneId());
            availableZoneInfoList.add(masterDO);
            availableZoneInfoList.add(slaveDO);

            AVZInfo tmpAvzInfo = new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, avzInfo.getRegion(), avzInfo.getRegionId(), avzInfo.getRegionCategory(), multiAVZExParamDO);

            int tmpCustinsId = Objects.requireNonNull(result.getReplicaSet().getId()).intValue();
            custinsParamService.updateAVZInfo(tmpCustinsId, tmpAvzInfo);
            custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_SLAVE_LOCATION, slaveLocation);

            // 集群版实例资源配置
            ReplicaSet destReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, result.getReplicaSet().getName(), false);
            ReplicaSet srcReplicaSet = modifyInsParam.getReplicaSetMeta();
            if (MysqlParamSupport.isCluster(String.valueOf(modifyInsParam.getTargetInstanceLevel().getCategory()))) {
                podReplicaSetResourceHelper.updateReplicaName(requestId, destReplicaSet, null, Replica.RoleEnum.SLAVE);
                podReplicaSetResourceHelper.updateReplicaName(requestId, srcReplicaSet, null, Replica.RoleEnum.MASTER);
            }

            // 审计日志管控参数补全
            custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
            // 更新系统参数模板
            CustinsParamDO paramGroupIdDO = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
            if (paramGroupIdDO != null) {
                String paramGroupId = paramGroupIdDO.getValue();
                if (StringUtils.isNotEmpty(paramGroupId)) {
                    if (paramGroupId.startsWith(ParamTransHelper.SYS_PARAM_GROUP_ID_PREFIX)) {
                        Map<String, Object> paramGroupInfo = SysParamGroupHelper.describeSysParamGroupId(paramGroupId);
                        paramGroupInfo.put("category", InstanceLevel.CategoryEnum.CLUSTER.toString());
                        Map<String, String> map = paramGroupInfo.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> (String) e.getValue()));
                        String newParamGroupId = SysParamGroupHelper.getSysParamGroupId(map);
                        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, newParamGroupId);
                        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, JSON.toJSONString(map));
                        // next: 在任务流内切换管控参数
                    } else {
                        // 如果是用户参数模板，直接应用到临时实例
                        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
                    }
                }
            }


            String taskKey = PodDefaultConstants.TASK_MODIFY_INS_BASIC_TO_HA_OR_CLUSTER;
            // dispatch task
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", modifyInsParam.getRequestId());
            taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
            taskParamObject.put("transTaskId", transListId);
            taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
            // 以下参数传给Online Resize使用
            taskParamObject.put("srcDiskType", modifyInsParam.getSrcDiskType());
            taskParamObject.put("targetDiskType", modifyInsParam.getTargetDiskType());
            taskParamObject.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
            taskParamObject.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
            taskParamObject.put("targetCategory", InstanceLevel.CategoryEnum.CLUSTER.toString());

            String taskParam = taskParamObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask("custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    modifyInsParam.getRequestId(),
                    modifyInsParam.getDbInstanceName(),
                    modifyInsParam.isTransIns() ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.CLASS_CHANGING.toString()
            );


            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
            data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
            data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
            data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
            data.put("TaskId", taskId);
            data.put("Region", modifyInsParam.getRegionId());
            isSuccess = true;
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 变配失败释放资源
            if (isAllocated && !isSuccess && StringUtils.isNotEmpty(resourceRequest.getReplicaSetName())) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, resourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }

}
