package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointGroup;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointService;
import com.aliyun.dba.poddefault.action.service.endpoint.EndPointServiceFactoryImpl;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateDBInstanceEndpointAddressImpl")
@Slf4j
public class CreateDBInstanceEndpointAddressImpl implements IAction {
    @Resource
    private EndPointServiceFactoryImpl endpointFactory;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private ReplicaSetService replicaSetService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String connectionStringOccupied = null;
        boolean executeSuccess = false;
        EndPointService endPointService = null;
        String replicaSetName = null;
        String requestId = null;

        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            endPointService = endpointFactory.getService(replicaSet.getConnType());
            replicaSetName = replicaSet.getName();
            requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

            // pre-check
            if (!MysqlParamSupport.isCluster(replicaSet.getCategory())) {  // 暂时限制只有 Cluster
                return createErrorResponse(ErrorCode.INVALID_SOURCE_CATEGORY);
            }
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (Objects.equals(replicaSet.getBizType(), ReplicaSet.BizTypeEnum.FINANCE)) {
                return createErrorResponse(ErrorCode.INVALID_BIZ_TYPE);
            }
            log.info("{} start create endpoint address", replicaSetName);

            // data prepare
            String ipType = paramSupport.getParameterValue(params, "IPType");
            String connectionStringPrefix = paramSupport.getParameterValue(params, ParamConstants.CONN_ADDR_PREFIX);
            String port = paramSupport.getPort(params);
            String endpointId = paramSupport.getParameterValue(params, "DBInstanceEndpointId");

            // pre-check
            endPointService.checkConnectionStringPrefix(requestId, replicaSet, connectionStringPrefix);
            CheckUtils.checkValidForConnAddrCust(connectionStringPrefix);
            CheckUtils.parseInt(port, 1000, 65534, ErrorCode.INVALID_PORT);

            //  endpoint num check
            EndpointGroup endpointGroup = endPointService.getAndCheckEndpointGroup(requestId, replicaSetName, endpointId);
            List<Endpoint> dbEndpointList = endPointService.listReplicaSetEndpoint(requestId, replicaSetName)
                    .stream().filter(ep -> Objects.equals(ep.getEndpointGroupId(), endpointGroup.getId()) && ep.getNetType() == Endpoint.NetTypeEnum.PUBLIC).collect(Collectors.toList());
            if (dbEndpointList.size() > 0) {
                throw new RdsException(ErrorCode.NETTYPE_EXIST);
            }
            // ipType check
            if (!ipType.equalsIgnoreCase(Endpoint.NetTypeEnum.PUBLIC.toString())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }
            ipType = endPointService.upperCaseFirst(ipType.toLowerCase());

            // allocate connectionStringPrefix
            connectionStringOccupied = endPointService.allocateConnectionString(requestId, replicaSetName, connectionStringPrefix);

            // allocate vip
            EndPoint allocateEndPointResult = endPointService.allocateEndpointResource(
                    requestId, replicaSetName, ipType, EndPoint.ConnTypeEnum.LVS, connectionStringPrefix,
                    EndPoint.EndPointTypeEnum.valueOf(endpointGroup.getType().toUpperCase()), port);

            // dispatch workflow task
            String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            JSONObject taskParam = new JSONObject();
            taskParam.put(ParamConstants.REQUEST_ID, requestId);
            taskParam.put("endpointName", endpointId);
            taskParam.put("connectionString", connectionStringOccupied);
            taskParam.put("vip", allocateEndPointResult.getIp());
            taskParam.put("port", port);

            // inactive replicaSet
            endPointService.inactiveReplicaSet(requestId, replicaSetName);
            // dispatch
            Object taskId = workFlowService.dispatchTask("custins", replicaSetName, domain, PodDefaultConstants.TASK_CREATE_ENDPOINT_PUBLIC_ADDRESS, taskParam.toJSONString(), 0);

            // rtn
            Map<String, Object> rtn = new HashMap<>();
            rtn.put("TaskId", taskId);
            rtn.put("DBInstanceName", replicaSetName);
            rtn.put("RequestId", requestId);
            rtn.put("ConnectionString", connectionStringOccupied);
            rtn.put("Vip", allocateEndPointResult.getIp());
            rtn.put("DBInstanceEndpointId", endpointId);
            executeSuccess = true;
            return rtn;
        } catch (ApiException ex) {
            log.error("CreateDBInstanceEndpointAddress, Api called failed: ", ex);
            throw new RdsException(ErrorCode.API_CALLING_FAILED, "Meta db calling failed");
        } catch (RdsException ex) {
            log.error("CreateDBInstanceEndpointAddress failed: ", ex);
            throw ex;
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception e) {
            log.error("CreateDBInstanceEndpointAddress Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // rollback
            if (!executeSuccess && connectionStringOccupied != null) {
                try {
                    endPointService.releaseConnectionString(requestId, replicaSetName, connectionStringOccupied);
                } catch (com.aliyun.dba.adb_vip_manager_client.ApiException e) {
                    log.error("clear connection string allocate failed!", e);
                }
            }
        }
    }
}
