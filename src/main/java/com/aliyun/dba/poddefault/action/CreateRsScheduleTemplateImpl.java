package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.RsScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultCreateRsScheduleTemplateImpl")
public class CreateRsScheduleTemplateImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateRsScheduleTemplateImpl.class);
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            String template;
            String bid = parameterHelper.getParameterValue(ParamConstants.USER_ID);
            Validate.notEmpty(bid,"null userId");
            String uid = parameterHelper.getParameterValue(ParamConstants.UID);
            Validate.notEmpty(uid,"null uid");
            String name = parameterHelper.getParameterValue("Name");
            Validate.notEmpty(name,"null name");
            String bizType = parameterHelper.getParameterValue(ParamConstants.BIZ_TYPE);
            Validate.notEmpty(bizType,"null bizType");
            String templateString = parameterHelper.getParameterValue(ParamConstants.Template);
            Validate.notEmpty(templateString,"null template");
            podTemplateHelper.checkTemplateString(templateString);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, bid + "_" + uid, false);
            PodScheduleTemplate podTemplate = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, name, user.getBid() + "_" + user.getAliUid());
            if (podTemplate == null) {
                RsScheduleTemplate newTemplate = new RsScheduleTemplate();
                newTemplate.setName(name);
                newTemplate.setBizType(bizType);
                newTemplate.setTemplate(templateString);
                template = podTemplateHelper.createPodScheduleTemplate(requestId, user.getUserId(), newTemplate);
            } else {
                template = JSON.toJSONString(podTemplate);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("UserId", user.getUserId());
            data.put("Name", name);
            data.put("BizType", bizType);
            data.put("Template", template);
            return data;
        } catch (Exception ex) {
            logger.error(requestId + " Create RsScheduleTemplate failed: " + ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
