package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultSwitchBlueGreenInstancePreCheckImpl")
public class SwitchBlueGreenInstancePreCheckImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(SwitchBlueGreenInstancePreCheckImpl.class);
    @Resource
    private BlueGreenDeploymentService blueGreenDeploymentService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            String aliUid = mysqlParamSupport.getUID(params);
            String bid = mysqlParamSupport.getBID(params);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String blueGreenDeploymentName = params.get("blueGreenDeploymentName".toLowerCase());
            String dbInstanceName = mysqlParamSupport.getAndCheckDBInstanceName(params);
            String greenDBInstanceId = params.get("greenDBInstanceId".toLowerCase());
            Boolean skipStatusCheck = Boolean.valueOf(params.get("skipStatusCheck".toLowerCase()));
            Map<String, Object> data = blueGreenDeploymentService.switchBlueGreenInstancePreCheck(regionId, aliUid, bid, custins, blueGreenDeploymentName,
                    dbInstanceName, greenDBInstanceId, skipStatusCheck);
            return data;
        } catch (RdsException ex) {
            logger.error("SwitchBlueGreenInstancePreCheckImpl failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("SwitchBlueGreenInstancePreCheckImpl Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}