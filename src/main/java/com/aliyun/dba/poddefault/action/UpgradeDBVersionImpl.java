package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.apsaradb.activityprovider.model.RebuildReplicaResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet.BizTypeEnum;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.UNSUPPORTED_MINOR_VERSION;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_MIRROR;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultUpgradeDBVersionImpl")
public class UpgradeDBVersionImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeDBVersionImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private MySQLServiceImpl mySQLService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private KmsService kmsService;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private InstanceService instanceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (ReplicaSet.LockModeEnum.NOLOCK != replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String uid =  mysqlParamSupport.getParameterValue(params, ParamConstants.UID);
            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, uid)) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }
            String minorVersion = paramSupport.getParameterValue(params, "MinorVersion");
            if (StringUtils.isEmpty(minorVersion)) {
                minorVersion = paramSupport.getParameterValue(params, "TargetMinorVersion");
            }
            boolean isXDB = replicaSetService.isReplicaSetXDB(requestId, replicaSet.getName());
            BizTypeEnum bizType = replicaSet.getBizType();
            //集群版有两次切换，切换窗口放开到30min
            if(!isXDB && PodParameterHelper.isAliYun(bizType) && MysqlParamSupport.isCluster(replicaSet.getCategory())){
                params.put(ParamConstants.SWITCH_WINDOW.toLowerCase(),"30");
            }
            Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);



            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put(CustinsSupport.SWITCH_KEY, switchInfoMap);

            // 连接方式为TDDL的XDB引擎的小版本升级仍然走XDB工作流，其他改走MySQL工作流
            String connType = replicaSet.getConnType().toString();
            boolean isTDDL = isXDB && CONN_TYPE_TDDL.equalsIgnoreCase(connType);
            boolean isReadIns = ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())
                    || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSet.getInsType());
            boolean isTddlTaskMigrate = replicaSetService.isTddlTaskMigrate(requestId, replicaSet);
            boolean isTddlChangeSpecTag = replicaSetService.isTddlChangeSpecTag(requestId, replicaSet.getName(), connType);
            Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
            boolean isArm =  CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(labels.get("instructionSetArch"));

            if (!isTDDL || isReadIns || isTddlTaskMigrate || isTddlChangeSpecTag) {
                ReplicaSet primaryIns = getPrimaryReplicaSet(requestId, replicaSet).getRight();
                taskParam.put("primaryReplicaSetName", primaryIns.getName());

                String dbType = replicaSet.getService();
                String dbVersion = replicaSet.getServiceVersion();
                String classCode = primaryIns.getClassCode();
                String dbEngine = paramSupport.isMysqlXDB(dbType, dbVersion, classCode) ? "XDB" : "MySQL";
                // TDDL 只读实例的规格是standard，所以会找不到tag
                if (isReadIns && isTDDL) {
                    dbEngine = "XDB";
                }

                String diskType = replicaSetService.getReplicaSetStorageType(replicaSet.getName(), requestId);
                String clusterName = replicaSet.getResourceGroupName();
                Boolean isDhg = paramSupport.isDHGCluster(clusterName);
                InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType,
                        dbVersion, classCode, null);
                logger.info(MessageFormat.format(
                        "dbEngine:{0},dbType:{1},dbVersion:{2},classCode:{3},diskType:{4},clusterName:{5},isDhg:{6},"
                                + "instanceLevel:{7}",
                        dbEngine, dbType, dbVersion, classCode, diskType, clusterName, isDhg, instanceLevel));

                String serviceSpecTag;
                boolean isChangeSubType = minorVersionServiceHelper.isChangeSubType(replicaSet, minorVersion);
                if (isTddlChangeSpecTag || isChangeSubType) {
                    serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                            minorVersion,
                            bizType,
                            dbType,
                            dbVersion,
                            dbEngine,
                            KIND_CODE_NEW_ARCH,
                            instanceLevel,
                            diskType,
                            isDhg,
                            isArm,
                            null);
                } else {
                    serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(replicaSet.getName(),
                            minorVersion, bizType, dbType,
                            dbVersion, dbEngine, KindCodeParser.KIND_CODE_NEW_ARCH, instanceLevel, diskType, isDhg, isArm, null);
                }
                taskParam.put("serviceSpecTag", serviceSpecTag);
                logger.info("serviceSpecTag:{}", serviceSpecTag);

                int serviceSpecId = minorVersionServiceHelper.checkAndGetServiceSpecId(requestId, minorVersion, dbType,
                        dbVersion, serviceSpecTag, instanceLevel.getCategory().toString());
                taskParam.put("serviceSpecId", serviceSpecId);
                logger.info("serviceSpecId:{}", serviceSpecId);

                String expectedReleaseDate = minorVersionServiceHelper.checkAndGetReleaseDate(requestId, replicaSet,
                        primaryIns.getCategory(), serviceSpecTag, minorVersion);

                if (isReadIns && PodParameterHelper.isAliYun(bizType)) {
                    // 校验只读实例的内核版本不能低于主实例，否则可能会出现拉不起来的情况
                    String primaryMinorVersion = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, primaryIns.getName(), "minor_version");
                    if (StringUtils.isBlank(primaryMinorVersion)) {
                        return ResponseSupport.createErrorResponse(new Object[]{ResultCode.CODE_ERROR, "MissingMinorVersion", "Primary's minor version not found."});
                    }
                    String currentPrimaryReleaseDate = minorVersionServiceHelper.parseReleaseDate(primaryMinorVersion, dbType, dbEngine, MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL);
                    String targetReleaseDate = minorVersionServiceHelper.parseServiceSpecReleaseDate(serviceSpecTag);
                    if (StringUtils.compare(currentPrimaryReleaseDate, targetReleaseDate) > 0) {
                        logger.error("invalid minor_version, expected:{}, primary current:{}", targetReleaseDate, currentPrimaryReleaseDate);
                        throw new RdsException(UNSUPPORTED_MINOR_VERSION);
                    }
                }
                String originServiceSpecTag = minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(requestId, replicaSet.getName());
                String originTag = minorVersionServiceHelper.parseOriginalSpecTag(originServiceSpecTag);
                String targetTag = minorVersionServiceHelper.parseOriginalSpecTag(serviceSpecTag);
                if (!StringUtils.equals(originTag, targetTag) && !Objects.equals(bizType, BizTypeEnum.ALIGROUP)) {
                    minorVersionServiceHelper.checkCrossTagRules(replicaSet, originTag, targetTag, uid);
                }

                // TDDL 默认版本下，替换版本信息为传入的信息
                if (isTDDL && "lastest".equalsIgnoreCase(expectedReleaseDate)) {
                    expectedReleaseDate = StringUtils.substringAfterLast(minorVersion,
                            StringUtils.contains(minorVersion, "_") ? "_" : ":");
                    logger.info("set release data to {}", expectedReleaseDate);
                }

                taskParam.put("releaseDate", expectedReleaseDate);
                logger.info("expectedReleaseDate:{}", expectedReleaseDate);
                logger.info("taskParam:{}", taskParam);
            }

            String targetReleaseDate = StringUtils.substringAfterLast(minorVersion, StringUtils.contains(minorVersion, "_") ? "_" : ":");
            if (!mysqlEngineCheckService.checkCanUpgradeMinorVersionWithMaxScale(custins, targetReleaseDate)) {
                //从rdsapi下来的请求会将custins对象传入
                return ResponseSupport.createErrorResponse(new Object[]{ResultCode.CODE_UNSUPPORTED, "MaxscaleMinorVersionNotSupport", "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first."});
            }

            taskParam.put("minor_version", minorVersion);

            boolean isPrimary = ReplicaSet.InsTypeEnum.MAIN.equals(replicaSet.getInsType());
            boolean isBasicCategory = InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSet.getCategory());


            if (PodParameterHelper.isAliYun(bizType) && isPrimary && !isBasicCategory) {
                // Passthough switch time params into workflow for upgrading attached read ins
                // These params should NEVER be used directly in workflow

                taskParam.put("switch_time_mode", paramSupport.getParameterValue(params, ParamConstants.SWITCH_TIME_MODE));
                taskParam.put("switch_time", paramSupport.getParameterValue(params, ParamConstants.SWITCH_TIME));
            }

            Object taskId;
            // 基础版小版本升级使用临时实例的方式
            //判断是否是xdb企业版
            boolean isXdb = replicaSetService.isReplicaSetXDB(requestId, replicaSet.getName());
            if (PodParameterHelper.isAliYun(bizType) && InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSet.getCategory())) {
                taskId = doUpgradeDBVersionForBasic(params, taskParam);
            } else if (!isXdb && PodParameterHelper.isAliYun(bizType) && InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(replicaSet.getCategory())) {
                taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_UPGRADE_MINOR_VERSION_HA, taskParam.toString(), 0);
            } else if (!isXdb && PodParameterHelper.isAliYun(bizType) && MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_UPGRADE_MINOR_VERSION_CLUSTER, taskParam.toString(), 0);
            }
            else {
                String domain = !isTDDL || isReadIns ? "mysql" : "xdb";
                String taskKey = isTDDL && isReadIns
                        ? TASK_TDDL_READINS_UPGRADE_MINOR_VERSION : isReadIns
                        ? TASK_UPGRADE_MINOR_VERSION_FOR_READ : TASK_UPGRADE_MINOR_VERSION;
                if (isTddlTaskMigrate || isTddlChangeSpecTag) {
                    domain = "mysql";
                    taskKey = TASK_TDDL_XDB_UPGRADE_MINOR_VERSION;
                    if(isReadIns){
                        taskKey = replicaSetService.isAligroupDoubleNodeRead(requestId,replicaSet.getName()) ? TASK_TDDL_DOUBLE_READINS_UPGRADE_MINOR_VERSION :TASK_TDDL_READINS_UPGRADE_MINOR_VERSION;
                    }

                }
                taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, taskParam.toString(), 0);
            }

            // 更新实例状态为小版本升级中
            metaService.getDefaultClient().updateReplicaSetStatus(
                    requestId, replicaSet.getName(), ReplicaSet.StatusEnum.VERSION_TRANSING.toString());

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TargetMinorVersion", minorVersion);
            data.put("TaskId", NumberUtils.isNumber(taskId.toString()) ? Double.valueOf(taskId.toString()).longValue() : taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 获取主实例
     */
    public Pair<String, ReplicaSet> getPrimaryReplicaSet(String requestId, ReplicaSet replicaSet) throws ApiException {
        ReplicaSet primaryReplicaSet = replicaSet;
        if (ReplicaSet.InsTypeEnum.TMP.equals(replicaSet.getInsType())) {
            primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), null);
        }

        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, primaryReplicaSet.getName());
        String centerReplicaSetName = labels.get("CenterReplicaSetName");
        String centerRegionId = labels.get("CenterRegionId");
        if (StringUtils.isNotEmpty(centerReplicaSetName)) {
            // 跨region只读
            primaryReplicaSet = dBaasMetaService.getRegionClient(centerRegionId).getReplicaSet(requestId, centerReplicaSetName, false);
        } else if (primaryReplicaSet.getPrimaryInsName() != null) {
            // 同region只读
            primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, primaryReplicaSet.getPrimaryInsName(), false);
        }
        return Pair.of(centerRegionId, primaryReplicaSet);
    }

    /**
     * 基础版小版本升级
     * 独立函数，便于维护
     */
    public Object doUpgradeDBVersionForBasic(Map<String, String> params, JSONObject taskParam) throws Exception {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        RebuildReplicaResourceRequest allocateReplicaResource = null;
        boolean isSuccess = false;
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            Replica replica = mySQLService.getReplicaByRole(requestId, replicaSet.getName(), Replica.RoleEnum.MASTER);
            ReplicaResource masterReplica = dBaasMetaService.getDefaultClient().getReplica(requestId, replica.getId(), null);
            PodType podType = podCommonSupport.getReplicaRuntimeType(masterReplica);
            // 申请资源
            allocateReplicaResource = podReplicaSetResourceHelper.allocateRebuildResource4Basic(replicaSet, RebuildReplicaResourceRequest.RebuildModeEnum.TRYINPLACE, podType);
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                rundPodSupport.completeReplicaNetworkConfig(allocateReplicaResource, replica);
            }
            allocateReplicaResource.setComposeTag(taskParam.getString("serviceSpecTag"));
            allocateReplicaResource.setCategory(podParameterHelper.getCategory(requestId, replicaSet));
            Replica replicaForUpgrade = mySQLService.getReplicaByRole(requestId, replicaSet.getName(), Replica.RoleEnum.MASTER);
            commonProviderService.getDefaultApi().allocateReplicaSetResourceForRebuild(
                    requestId, replicaSet.getName(), replicaForUpgrade.getId(), allocateReplicaResource);
            CustInstanceDO custInstanceDO = custinsService.getCustInstanceByInsName(
                    null, allocateReplicaResource.getTmpReplicaSetName(), CUSTINS_INSTYPE_TMP);
            if (custInstanceDO == null) {
                logger.error("Cannot find tmp custins [{}]", allocateReplicaResource.getTmpReplicaSetName());
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            custInstanceDO.setInsType(CUSTINS_INSTYPE_MIRROR);
            custInstanceDO.setGmtModified(new Date());
            custinsService.updateCustInstance(custInstanceDO);

            if(ReplicaSet.InsTypeEnum.READONLY == replicaSet.getInsType()){
                // 只读实例配置白名单同步label
                podParameterHelper.setReadInsSgLabel(requestId, replicaSet, allocateReplicaResource.getTmpReplicaSetName());
            }

            // 下发任务 & 更新状态
            taskParam.put("srcReplicaSetName", replicaSet.getName());
            taskParam.put("destReplicaSetName", allocateReplicaResource.getTmpReplicaSetName());
            taskParam.put("srcReplicaId", replicaForUpgrade.getId());
            taskParam.put("destReplicaId", mySQLService.getReplicaByRole(
                    requestId, allocateReplicaResource.getTmpReplicaSetName(), Replica.RoleEnum.MASTER).getId());

            String taskKey = ReplicaSet.InsTypeEnum.READONLY == replicaSet.getInsType() ? "upgrade_minor_version_for_basic_read" : "upgrade_minor_version_for_basic";
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSet.getName(), "mysql", taskKey, taskParam.toString(), 0);
            isSuccess = true;
            return taskId;
        } catch (Exception e) {
            logger.error(requestId + " Exception: ", e);
            throw e;
        } finally {
            // 处理失败时释放资源
            if (null != allocateReplicaResource && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, allocateReplicaResource.getTmpReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }


}
