package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsSearchService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.api.OpenSearchApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDestroyDBInstanceImpl")
public class DestroyDBInstanceImpl  implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.dockerdefault.action.DestroyDBInstanceImpl.class);

    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private CustinsService custinsService;
    @Autowired
    protected BakService bakService;
    @Autowired
    protected CustinsIDao custinsIDao;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected CustinsSearchService custinsSearchService;
    @Autowired
    protected UserService userService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        // 销毁实例，删除所有备份集，且任务流中不做备份
        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            if (mysqlParamSupport.getParameterValue(params, "dbinstanceid") == null) {
                custInstanceDO = custinsIDao.getCustInstanceByInsName(mysqlParamSupport.getAndCheckUserId(params),
                        mysqlParamSupport.getAndCheckDBInstanceName(params));
            } else {
                custInstanceDO = mysqlParamSupport.getAndCheckCustInstanceById(params, "dbinstanceid");
            }
            if (custInstanceDO == null || custInstanceDO.getStatus().equals(CustinsSupport.CUSTINS_STATUS_DESTROYED)) {
                //实例不存在，或者不是实例拥有者
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }

            if (custInstanceDO.isDeleting()) {
                bakService.cancelBackupTaskByCustInstId(custInstanceDO.getId());
                bakService.destroyBackupSet(custInstanceDO.getId());

                custinsIDao.updateCustInstanceStatusByCustinsId(custInstanceDO.getId(), CustinsSupport.CUSTINS_STATUS_DESTROYED,
                        CustinsState.STATUS_DESTORYED.getComment());
                Map<String, Object> data = new HashMap<String, Object>(1);
                data.put("DBInstanceID", custInstanceDO.getId());
                return data;
            }
            String accessId = getParameterValue(params, ParamConstants.ACCESSID);

            if (PodCommonSupport.isAccessFromDBS(accessId) && !StringUtils.startsWith(custInstanceDO.getInsName(), "dbs-")) {
                //来源是DBS的删除实例调用，强制校验实例名规则，保护实例不会被误删除
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            mysqlParamSupport.checkNotDeleteHaProxyCustins(custInstanceDO);

            //如果需要删除的实例是主实例的话，则需要确保该主实例所关联的灾备实例和只读实例已经被删除
            if (custInstanceDO.isPrimary()) {
                //查询灾备实例，如果有灾备实例的话，则禁止删除
                CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                custInstanceQuery.setPrimaryCustinsId(custInstanceDO.getId());
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_GUARD);
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                // 查询只读实例，如果有只读实例的话，则禁止删除
                custInstanceQuery.setPrimaryCustinsId(custInstanceDO.getId());
                custInstanceQuery.setStatusNotEqual(CustinsState.STATUS_DELETING);
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_READ);
                if (custinsService.countCustIns(custInstanceQuery) > 0) {
                    return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }
            }

            if (custInstanceDO.isRead() && custInstanceDO.isMysql()) {
                CustInstanceDO primaryins = custinsService
                        .getCustInstanceByCustinsId(custInstanceDO.getPrimaryCustinsId());
                List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                        .getCustinsConnAddrByCustinsId(primaryins.getId(), null, CustinsSupport.RW_TYPE_RW_SPLIT);

                if (custinsConnAddrList.size() > 0) {
                    // TODO: 删除连接
                }
            }

            if (custinsSearchService.checkCustinsSearch(custInstanceDO)) {
                String apiUrlString = userService.getOpenSearchApiUrlByClusterName(custInstanceDO.getClusterName());
                if (apiUrlString == null) {
                    return ResponseSupport.createErrorResponse(ErrorCode.OPENSEARCH_NOT_SUPPORT);
                }
                JSONObject apiUrl = JSON.parseObject(apiUrlString);
                OpenSearchApi api = new OpenSearchApi(apiUrl.getString("host"),
                        apiUrl.getString("accesskey"),
                        apiUrl.getString("secret"));
                CustinsSearchDO custinsSearch = custinsSearchService.getCustinsSearchByCustins(custInstanceDO);
                api.deleteOpenSearchInstance(custinsSearch.getAppName());
                custinsSearchService.deleteCustinsSearch(custinsSearch);
            }

            // 取消备份任务 & 销毁所有备份
            bakService.cancelBackupTaskByCustInstId(custInstanceDO.getId());
            bakService.destroyBackupSet(custInstanceDO.getId());

            //删除实例
            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID));
            taskParam.put("replicaSetName", custInstanceDO.getInsName());
            taskParam.put("destroy", true);
            Object taskId = workFlowService.dispatchTask(
                    "custins", custInstanceDO.getInsName(),
                    PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_DELETE_INS, taskParam.toJSONString(), 0);

            custinsIDao.updateCustInstanceStatusByCustinsId(custInstanceDO.getId(), CustinsSupport.CUSTINS_STATUS_DESTROYED,
                    CustinsState.STATUS_DESTORYED.getComment());

            Map<String, Object> data = new HashMap<String, Object>(3);
            data.put("DBInstanceID", custInstanceDO.getId());
            data.put("DBInstanceName", custInstanceDO.getInsName());
            data.put("TaskId", taskId);
            return data;

        } catch (RdsException re) {
            logger.error("DestroyDBInstance ex=" + re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("DestroyDBInstance ex=" + ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
