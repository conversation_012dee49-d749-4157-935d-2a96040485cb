package com.aliyun.dba.poddefault.action;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.RebuildReplicaResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.service.ClusterBackUpService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_REBUILD_EXTERNAL_REPLICATION_CUSTINS;

/**
 * 新架构使用用户上传的oss备份重建开启外部复制的实例
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRebuildExternalReplicationCustinsImpl")
@Slf4j
public class RebuildExternalReplicationCustinsImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(RebuildExternalReplicationCustinsImpl.class);
    public static final String AliyunServiceRoleForRdsImport = "AliyunServiceRoleForRdsImport";
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected CustinsParamService custinsParamService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected PodTemplateHelper podTemplateHelper;
    @Resource
    protected PodParameterHelper podParameterHelper;
    @Resource
    protected PodCommonSupport podCommonSupport;
    @Resource
    protected CommonProviderService commonProviderService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    protected MySQLServiceImpl mySQLService;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    protected ServerlessResourceService serverlessResourceService;
    @Autowired
    private ClusterBackUpService clusterBackUpService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID);
        RebuildReplicaResourceRequest allocateReplicaResource = null;
        try {
            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(actionParams);
            replicaSetService.preCheckForExternalReplication(replicaSetMeta, requestId, PodDefaultConstants.ExternalReplicationScenario.rebuild);
            Replica replica = mySQLService.getReplicaByRole(requestId, replicaSetMeta.getName(), Replica.RoleEnum.MASTER);
            ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, replica.getId(), null);
            String replicaRuntimeType = Objects.requireNonNull(replicaResource.getVpod()).getRuntimeType();
            if (!PodType.POD_ECS_RUND.getRuntimeType().equalsIgnoreCase(replicaRuntimeType)) {
                throw new RdsException(ErrorCode.INVALID_TARGET_RUNTIME_TYPE);
            }
            String mode = mysqlParamSupport.getParameterValue(actionParams, "Mode");
            JSONObject taskParamObject = new JSONObject();
            if ("stream".equalsIgnoreCase(mode)) {
                String sourceInfo = mysqlParamSupport.getParameterValue(actionParams, "SourceInfo");
                JSONObject sourceInfoObject = JSONObject.parseObject(sourceInfo);
                if (sourceInfoObject == null) {
                    log.error("Missing required parameter 'SourceInfo'");
                    throw new RdsException(ErrorCode.INVALID_PARAM);
                }
                String sourceIp = sourceInfoObject.getString("sourceIp");
                String sourcePort = sourceInfoObject.getString("sourcePort");
                if (StringUtils.isBlank(sourceIp) || StringUtils.isBlank(sourcePort)) {
                    log.error("Invalid SourceInfo: {}", sourceInfo);
                    throw new RdsException(ErrorCode.INVALID_PARAM);
                }
                taskParamObject.put("sourceIp", sourceIp);
                taskParamObject.put("sourcePort", sourcePort);
                taskParamObject.put("isStream", true);
            } else {
                clusterBackUpService.reloadOssParams(actionParams);
                taskParamObject.put("baksetName", mysqlParamSupport.getParameterValue(actionParams, "OssFileName"));
                taskParamObject.put("downloadUrl", mysqlParamSupport.getParameterValue(actionParams, "OssUrl"));
                taskParamObject.put("stsToken", mysqlParamSupport.getParameterValue(actionParams, "StsToken"));
            }
            // build replication logic
            boolean buildReplication = mysqlParamSupport.isBuildReplication(actionParams);
            taskParamObject.put("buildReplication", buildReplication);
            if (buildReplication) {
                String masterInfo = mysqlParamSupport.getParameterValue(actionParams, "MasterInfo");
                JSONObject masterInfoObject = JSONObject.parseObject(masterInfo);
                if (masterInfoObject == null) {
                    log.error("Missing required parameter 'MasterInfo'");
                    throw new RdsException(ErrorCode.INVALID_PARAM);
                }
                String masterIp = masterInfoObject.getString("masterIp");
                String masterPort = masterInfoObject.getString("masterPort");
                String masterUser = masterInfoObject.getString("masterUser");
                String masterPassword = masterInfoObject.getString("masterPassword");
                if (StringUtils.isBlank(masterIp) || StringUtils.isBlank(masterPort) || StringUtils.isBlank(masterUser) || StringUtils.isBlank(masterPassword)) {
                    log.error("Invalid MasterInfo: {}", masterInfo);
                    throw new RdsException(ErrorCode.INVALID_PARAM);
                }
                taskParamObject.put("masterIp", masterIp);
                taskParamObject.put("masterPort", masterPort);
                taskParamObject.put("masterUser", masterUser);
                taskParamObject.put("masterPassword", masterPassword);
            }
            //申请资源
            if (InstanceLevel.CategoryEnum.BASIC.getValue().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                //基础版实例申请资源
                allocateReplicaResource = podReplicaSetResourceHelper.allocateRebuildResource4Basic(replicaSetMeta, PodType.POD_ECS_RUND);
            } else if (InstanceLevel.CategoryEnum.SERVERLESS_BASIC.getValue().equalsIgnoreCase(replicaSetMeta.getCategory())) {
                //serverless基础版实例申请资源
                allocateReplicaResource = podReplicaSetResourceHelper.allocateRebuildResource4Basic(replicaSetMeta, PodType.POD_ECS_RUND);
                Double rebuildRcu = serverlessResourceService.getRcuForTmpIns(requestId, replicaSetMeta);
                allocateReplicaResource.setServerlessRcu(rebuildRcu);
                allocateReplicaResource.putLabelsItem(PodDefaultConstants.SCHEDULE_LABEL_SERVERLESS, Boolean.TRUE.toString());
            }

            rundPodSupport.completeReplicaNetworkConfig(allocateReplicaResource, replica);

            boolean isSuccess = false;
            try {
                logger.info("Print tmp replicaSetResourceRequest: " + JSON.toJSONString(allocateReplicaResource));
                commonProviderService.getDefaultApi().allocateReplicaSetResourceForRebuild(
                        requestId, replicaSetMeta.getName(), replica.getId(), allocateReplicaResource);
                ReplicaListResult destReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, allocateReplicaResource.getTmpReplicaSetName(), null, null, null, null);
                if (destReplica.getItems() == null || destReplica.getItems().isEmpty()) {
                    logger.error("Cannot find tmp custins [{}]", allocateReplicaResource.getTmpReplicaSetName());
                    throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                }
                isSuccess = true;
                Long destReplicaId = destReplica.getItems().get(0).getId();
                //下发任务
                Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(actionParams);
                taskParamObject.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
                taskParamObject.put("requestId", requestId);
                taskParamObject.put("replicaSetName", replicaSetMeta.getName());
                taskParamObject.put("srcReplicaSetName", replicaSetMeta.getName());
                taskParamObject.put("destReplicaSetName", allocateReplicaResource.getTmpReplicaSetName());
                taskParamObject.put("srcReplicaId", replica.getId());
                taskParamObject.put("destReplicaId", destReplicaId);
                taskParamObject.put("require_user_backup", "false");
                Object taskId = workFlowService.dispatchTask("custins", replicaSetMeta.getName(), "mysql", TASK_REBUILD_EXTERNAL_REPLICATION_CUSTINS, taskParamObject.toString(), 0);
                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", replicaSetMeta.getId());
                data.put("DBInstanceName", replicaSetMeta.getName());
                data.put("TaskId", NumberUtils.isNumber(taskId.toString()) ? Double.valueOf(taskId.toString()).longValue() : taskId);
                data.put("success", true);
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetMeta.getName(), ReplicaSet.StatusEnum.INS_MAINTAINING.toString());
                return data;
            } catch (Exception e) {
                logger.error(requestId + " Exception:" + e.getMessage(), e);
                isSuccess = false;
                if (e instanceof ApiException) {
                    logger.error("allocateReplicaSetResourceV1 failed requestId={} replicaSetName={}",
                            requestId, allocateReplicaResource.getTmpReplicaSetName());
                    return CommonProviderExceptionUtils.resourceWrapper(requestId, (ApiException) e);
                }
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            } finally {
                if (!isSuccess) {
                    //分配失败或者其它异常的情况下，要调用释放资源接口
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, allocateReplicaResource.getTmpReplicaSetName());
                    } catch (ApiException e) {
                        //ignore
                        logger.error(requestId + " release ReplicaSet Exception: {}", e.getResponseBody());
                    }
                }
            }
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}