package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.commonkindcode.action.DescribeDBInstanceParameterListImpl;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_LOCK_NO;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_TRANS;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultUnlockDBInstanceImpl")
public class UnlockDBInstanceImpl implements IAction {


    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterListImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MycnfService mycnfService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    private ReplicaSetService replicaSetService;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParaHelper.getWithoutCheckCustInstance();
            if (!isAllowUnlock(custins)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            ReplicaSet replicaSet = replicaSetService.getReplicaSet(actionParams);

            String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);
            String unlockModeStr = mysqlParaHelper.getParameterValue(ParamConstants.UNLOCK_MODE);

            if (unlockModeStr != null) {
                Integer unlockMode =
                        CheckUtils.parseInt(unlockModeStr, null, null, ErrorCode.INVALID_PARAMETERS);
                if (!MysqlParameterHelper.checkUnlockPrecondition(unlockMode, custins)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE,
                            "Cannot unlock for UnlockMode " + unlockMode +
                                    ", because the current LockMode is " + custins.getLockMode());
                }
            }
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("LockMode", CUSTINS_LOCK_NO);
            if (custins.isNoLock()) {
                // 已经是解锁状态，直接返回，避免重复下发任务
                return data;
            }

            boolean isTaskExists = false;
            try {
                isTaskExists = workFlowService.isTaskExist(requestId, custins.getInsName(), PodDefaultConstants.TASK_UNLOCK_INS);
            } catch (Exception e) {
                //ignore 查询失败忽略掉，不影响加锁任务下发
            }
            if (isTaskExists) {
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }
            String taskKey = PodDefaultConstants.TASK_UNLOCK_INS;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("lockMode","0");
            jsonObject.put("lockReason","");
            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask(replicaSet, taskKey, parameter, 0);
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }


    private boolean isAllowUnlock(CustInstanceDO custins) {
        boolean expiredLock = custins.getLockMode() == CustinsSupport.CUSTINS_LOCK_YES.intValue() && StringUtils.equalsIgnoreCase(custins.getLockReason(), "instance_expired");
        boolean isClasCodeChanging = custins.getStatus() == CustinsState.STATE_CLASS_CHANGING.getState() && StringUtils.equalsIgnoreCase(custins.getStatusDesc(), CustinsState.STATE_CLASS_CHANGING.getComment());
        // 实例状态为Active或者实例过期且状态变配中，允许下发解锁
        return custins.isActive() || (expiredLock && isClasCodeChanging);
    }

}
