package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.backup.BackupSetParam;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.ColdDataService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.support.TaskSupport;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_BACK;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;

/**
 * 库表恢复到原实例
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultRestoreTableImpl")
@Slf4j
public class RestoreTableImpl implements IAction {

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private MysqlParamSupport paramSupport;

    @Resource
    private ReplicaSetService replicaSetService;

    @Resource
    private CustinsService custinsService;

    @Resource
    private BakService bakService;

    @Resource
    protected CheckService checkService;

    @Resource
    private CommonProviderService commonProviderService;

    @Resource
    private BackupService backupService;

    @Resource
    private PodParameterHelper podParameterHelper;

    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Resource
    private PodTemplateHelper podTemplateHelper;

    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Resource
    private PodDateTimeUtils podDateTimeUtils;

    @Resource
    private PodAvzSupport avzSupport;

    @Resource
    protected InstanceService instanceService;
    @Resource
    private DTZSupport dtzSupport;

    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private ColdDataService coldDataService;
    @Resource
    private RundPodSupport rundPodSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        var requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);

        boolean  isSuccess = false;
        boolean isStatusModified = false;
        String tmpReplicaSetName = null;
        ReplicaSet replicaSet = null;

        try {
            log.info("Start restore table, params: " + JSON.toJSONString(params));
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            custins = paramSupport.getAndCheckCustInstance(params);

            MysqlParamSupport.checkLockMode(replicaSet);
            MysqlParamSupport.checkInstanceActive(replicaSet);
            // 暂时不放开 serverless
            if (replicaSetService.isServerless(replicaSet)) {
                throw new RdsException(ErrorCode.INVALID_SOURCE_CATEGORY);
            }
            // 拥有临时实例
            if (custinsService.hasChildCustInstanceByCustinsId(replicaSet.getId().intValue())) {
                return createErrorResponse(ErrorCode.CHILDINSTANCE_EXIST);
            }

            // 先将实例状态置为恢复中，避免申请资源过程中重复提交请求
            custinsService.updateCustInstanceStatusByCustinsId(replicaSet.getId().intValue(), CUSTINS_STATUS_BACK, CustinsState.STATE_RECOVER_CUSTINS.getComment());
            isStatusModified = true;

            var replicaSetName = replicaSet.getName();

            // user
            var user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);
            var bid = user.getBid();
            var uid = user.getAliUid();

            // 时间点 or 备份集
            var restoreType = paramSupport.getParameterValue(params, ParamConstants.RESTORE_TYPE, BakSupport.RESTORE_TYPE_BAKID);

            // 获取备份 history
            var backupSetId = paramSupport.getParameterValue(params, ParamConstants.BACKUP_SET_ID);
            BakhistoryDO bakHistory = null;
            Date restoreTimeUTC = null;
            // 按备份种类分类
            if (BakSupport.RESTORE_TYPE_BAKID.equals(restoreType)) {  // 按备份集
                var bakId = CheckUtils.parseLong(backupSetId, null, null, ErrorCode.BACKUPSET_NOT_FOUND);
                bakHistory = bakService.getBakhistoryByBackupSetId(replicaSet.getId().intValue(), bakId);
            } else if (BakSupport.RESTORE_TYPE_TIME.equals(restoreType)) {  // 按时间点
                restoreTimeUTC = podParameterHelper.getAndCheckRestoreTime(requestId, custins);
                bakHistory = podParameterHelper.getBakhistoryByRecoverTime(custins.getId(), restoreTimeUTC);
            }
            if (bakHistory == null) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }

            // 确定备份存在并加锁
            GetBackupSetResponse backupSetResponse;
            try {
                log.info("Start query backupSet: " + bakHistory.getHisId());
                backupSetResponse = backupService.getBackupSet(
                        BackupSetParam.builder()
                                .uid(uid)
                                .user_id(bid)
                                .dBInstanceId(custins.getId())
                                .backupSetId(Long.valueOf(bakHistory.getHisId()))
                                .build()
                );
            } catch (BaseServiceException ex) {
                log.error("GetBackupSet failed: ", ex);
                if (StringUtils.equalsIgnoreCase(ex.getCode(), "InvalidBackupSetID.NotFound")) {
                    throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
                }
                throw ex;
            }
            this.lockBakResource(custins, bakHistory, restoreTimeUTC);

            // 获取 tableMeta 信息
            var tableMeta = paramSupport.getAndCheckTableMeta(params, bakHistory, "restore", replicaSet.getServiceVersion(), true);

            // 校验磁盘空间
            var needSpaces = paramSupport.getAndCheckStorageForRestoreDbtables(tableMeta, bakHistory) / 1024 / 1024;  // MB
            var instancePerf = this.instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
            if (instancePerf == null) {
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }
            var diskCurr = (new BigDecimal(instancePerf.getDiskCurr())).longValue();
            var diskSize = custins.getDiskSize();
            // 校验磁盘大小 与 当前磁盘大小与需要的空间大小
            if ((double) (diskSize.intValue()) <= (double) (diskCurr + needSpaces)) {
                log.error("restore tables, instance space is not enough, diskSize: " + diskSize + ", diskCurr: " + diskCurr + ", needSpaces: " + needSpaces + ".");
                throw new RdsException(ErrorCode.INSTANCE_DISK_NOT_ENOUGH);
            }

            // 通过 DBossApi 检查实例是否有重复的库表名称
            var checkWithDBossMap = paramSupport.checkTableMetaWithDboss(custins, tableMeta);
            if (!( (boolean) checkWithDBossMap.get("result"))) {
                return createErrorResponse(ErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE);
            }

            // allocate resource
            var replicaSetResourceRequest = this.allocateTempCustins(custins, replicaSet, params, user, backupSetResponse);
            tmpReplicaSetName = replicaSetResourceRequest.getReplicaSetName();

            // pengine backup set
            var isPengineBackupSet = !bakHistory.getKindCode().equals(KIND_CODE_NEW_ARCH);

            // dispatch workflow
            var domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            var taskKey = TaskSupport.TASK_RECOVER_INS_DBSTABLES;
            var jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", replicaSetName);
            jsonObject.put("tmpReplicaSetName", tmpReplicaSetName);

            //  dispatch backup info
            jsonObject.put("tableMeta", tableMeta);
            jsonObject.put("restoreType", restoreType);
            jsonObject.put("restoreTime", podDateTimeUtils.convert2UTCStr(restoreTimeUTC));
            jsonObject.put("backupSetStartTime", podDateTimeUtils.convert2UTCStr(bakHistory.getBakBegin()));
            jsonObject.put("backupSetId", backupSetId);
            jsonObject.put("bakHisID", bakHistory.getHisId());
            jsonObject.put("backupSetHostId", bakHistory.getHostinsId());
            jsonObject.put("startBinlogFile", backupSetResponse.getSlaveStatusObj().getBinLogFile());
            jsonObject.put("destDiskSizeMB", replicaSet.getDiskSizeMB());
            jsonObject.put("isPengineBackupSet", isPengineBackupSet);

//            jsonObject.put("useCDM", useCDM);

            //  dispatch
            var taskId = workFlowService.dispatchTaskByPost("custins", replicaSetName, domain, taskKey, jsonObject.toJSONString(), 0, requestId);

            log.info("Start recover db and tables, RestoreTableImpl: " + jsonObject.toJSONString());
            var rtnData = new HashMap<String, Object>();
            rtnData.put("TaskId", taskId);
            rtnData.put(ParamConstants.DB_INSTANCE_NAME, replicaSetName);
            rtnData.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());

            isSuccess = true;
            return rtnData;
        } catch (RdsException ex) {
            log.error("RestoreTable failed: ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("RestoreTable failed: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isSuccess) {
                // 释放备份锁
                this.unLockBakResource();
                // 变更实例状态为active
                if (isStatusModified && replicaSet != null) {
                    try {
                        dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.ACTIVATION.toString());
                    } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException ex) {
                        log.error("active replicaSet failed with exception: ", ex);
                    }
                }
                // 释放已经申请成功的资源
                if (StringUtils.isNotBlank(tmpReplicaSetName)) {
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, tmpReplicaSetName);
                    } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                        //ignore
                        log.error(String.format("release for restore_tables failed: %s", e.getResponseBody()));
                    }
                }
            }
        }
    }

    /**
     * 预分配临时实例资源
     */
    private ReplicaSetResourceRequest allocateTempCustins(CustInstanceDO custins, ReplicaSet replicaSet, Map<String, String> params, User user, GetBackupSetResponse backupSetResponse) throws Exception {
        var replicaSetName = replicaSet.getName();
        var tmpReplicaSetName = "tmp-" + System.currentTimeMillis() + "-" + replicaSetName;
        var requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        var uid = user.getAliUid();
        var bid = user.getBid();
        var storageType = replicaSetService.getReplicaSetStorageType(replicaSetName, requestId);
        var bizType = custins.getBizType();
        var dbType = replicaSet.getService();
        var dbVersion = replicaSet.getServiceVersion();
        var classCode = replicaSet.getClassCode();
        var isSingleTenant = replicaSetService.isCloudDiskSingleTenant(requestId, replicaSet);
        var avzInfo = avzSupport.getAVZInfoFromCustInstance(custins);
        var snapshotId = backupSetResponse.getSlaveStatusObj().getSnapshotId();
        var extendedDiskSizeGB = podParameterHelper.getExtendDiskSizeGBForPod(replicaSet.getBizType(), false, replicaSet.getDiskSizeMB() / 1024);
        var masterReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems()
                .stream().filter(rp -> rp.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get();
        var serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId());
        ReplicaResource masterReplicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, masterReplica.getId(), null);
        PodType podType = podCommonSupport.getReplicaRuntimeType(masterReplicaResource);

        // 初始化 replicaSet 信息
        var resourceRequest = new ReplicaSetResourceRequest();
        resourceRequest.userId(bid)
                .uid(uid)
                .replicaSetName(tmpReplicaSetName)
                .dbType(dbType)
                .classCode(replicaSet.getClassCode())
                .diskSize(replicaSet.getDiskSizeMB() / 1024)
                .dbVersion(dbVersion)
                .primaryInsName(replicaSetName)
                .subDomain(avzInfo.getRegion())
                .regionId(avzInfo.getRegionId())
                .insType(ReplicaSet.InsTypeEnum.TMP.toString())
                .storageType(storageType)
                .bizType(bizType)
                .catagory(replicaSet.getCategory())
                .composeTag(serviceSpecTag)
                .connType(MySQLParamConstants.CONN_TYPE_PHYSICAL)
                .ignoreCreateVpcMapping(true);

        // 库表恢复使用 基础版、mysqlx 的资源池，后续需要调整到 DBS资源池
//        var scheduleTemplatePair = podTemplateHelper.getBizSysScheduleTemplateByName(requestId, PodDefaultConstants.RS_TEMPLATE_NAME_SYS_DBS);;

        var instanceLevel = dBaasMetaService.getDefaultClient()
                .getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
        Pair<String, ScheduleTemplate> scheduleTemplatePair = podTemplateHelper.getBizSysScheduleTemplate(
                podType,
                replicaSet.getBizType(),
                "mysql",
                instanceLevel,
                isSingleTenant,
                custins.getInsType().toString(),
                replicaSetName, null, paramSupport.getUID(params));
        resourceRequest.setScheduleTemplate(scheduleTemplatePair.getValue());

        // 初始化 replica 信息
        var replicaResources = new ArrayList<ReplicaResourceRequest>();
        var replicaResource = new ReplicaResourceRequest();
        replicaResource.setDiskSize(replicaSet.getDiskSizeMB() / 1024);  // 指定购买大小，任务流中执行resize2fs变成赠送大小
        replicaResource.setClassCode(classCode);
        if (ReplicaSetService.isStorageTypeCloudDisk(storageType)) {
            var volumeSpec = new VolumeSpec();
            volumeSpec.setSnapshotId(snapshotId);
            volumeSpec.setName("data");
            volumeSpec.setPerformanceLevel(replicaSetService.getVolumePerfLevel(requestId, replicaSetName, storageType));
            replicaResource.setVolumeSpecs(Collections.singletonList(volumeSpec));
        }
        replicaResource.setHostName(podParameterHelper.getRoleHostNameMapping().get(Replica.RoleEnum.MASTER));
        replicaResource.setRole(Replica.RoleEnum.MASTER.toString());  // 临时实例 master
        replicaResource.setStorageType(storageType);
        replicaResource.setSingleTenant(isSingleTenant);
        replicaResource.setZoneId(masterReplica.getZoneId());  // TODO 指定某个可用备库的 zone
        replicaResource.setVswId(null);
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
            rundPodSupport.completeReplicaNetworkConfig(replicaResource, masterReplica);
            rundPodSupport.completeReplicaSetNetworkConfig(resourceRequest);
            //rund 创建eni依赖vpcId，需要单独处理
            resourceRequest.setVpcId(rundPodSupport.getEniByReplica(masterReplica).getVpcId());
        }
        replicaResources.add(replicaResource);
        resourceRequest.setReplicaResourceRequestList(replicaResources);

        // mock
        podReplicaSetResourceHelper.mockReplicaSetResource(resourceRequest);

        GeneralCloudDisk generalCloudDisk = new GeneralCloudDisk();
        if (DockerOnEcsConstants.ECS_CLOUD_AUTO.equalsIgnoreCase(storageType)) {
            generalCloudDisk = podCommonSupport.setGeneralCloudDiskConfig(requestId, params, dbVersion, instanceLevel, storageType, replicaSet, null);
        } else {
            generalCloudDisk.warmDataDisk(new WarmDataDisk().ioAccelerationEnabled(false));
        }

        Boolean coldDataEnabled = replicaSetService.getColdDataEnabled(requestId, replicaSetName, null);
        Boolean isRestoreColdData = true;
        ColdDataDisk coldDataDisk = coldDataService.getColdDataDiskFromBackupSetId(requestId,coldDataEnabled,avzInfo.getRegionId(),isRestoreColdData,replicaSetName, String.valueOf(backupSetResponse.getBackupSetId()),user.getBid(), user.getAliUid(),false);
        if (podCommonSupport.isColdDataEnabled(coldDataDisk)) {
            podCommonSupport.checkColdDataSupportLimit(requestId, instanceLevel, dbType, dbVersion, storageType, serviceSpecTag, null, user.getAliUid(), avzInfo.getRegionId());
        }
        if(Objects.isNull(resourceRequest.getGeneralCloudDisk())){
            resourceRequest.setGeneralCloudDisk(generalCloudDisk);
        }
        resourceRequest.getGeneralCloudDisk().setColdDataDisk(coldDataDisk);

        String optimizedWritesInfo = podCommonSupport.getOptimizedWritesInfo(requestId, dbVersion, storageType, replicaSet);
        boolean isInitOptimizedWrites = podCommonSupport.isInitOptimizedWrites(optimizedWritesInfo);
        resourceRequest.setInitOptimizedWrites(isInitOptimizedWrites);

        // call api
        var isAllocate = false;
        try {
            log.info("Start allocate resource, tmpReplicaSet: " + tmpReplicaSetName + ", resourceRequest: " + JSON.toJSONString(resourceRequest));
            isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, tmpReplicaSetName, resourceRequest);
        } catch (Exception ex) {
            log.error("Allocate tmp replicaSet failed: " + JSON.toJSONString(ex));
            if (ex instanceof ApiException) {
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND);
            }
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isAllocate) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, tmpReplicaSetName);
                } catch (ApiException ex) {
                    log.error("Release tmp replicaSet failed: " + JSON.toJSONString(ex));
                }
            }
        }
        return resourceRequest;
    }

    /**
     * 锁定备份资源，避免克隆过程中被清理
     * */
    private void lockBakResource(CustInstanceDO custInstance, BakhistoryDO bakHistory, Date restoreTimeUTC) {
        if (restoreTimeUTC != null) {
            Date bakTime = dtzSupport.getSpecificTimeZoneDate(new DateTime(restoreTimeUTC), DATA_SOURCE_BAK);
            bakService.lockBinlogForRestore(custInstance.getId(), bakHistory.getBakBegin(), bakTime); // lock binlog for restore
            ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                    JSON.toJSONString(ImmutableMap.of(
                            "custinsId", custInstance.getId().toString(),
                            "begin", bakHistory.getBakBegin().getTime(),
                            "end", bakTime.getTime())));
        }

        bakService.lockBakHisForRestore(bakHistory.getHisId());
        ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakHistory.getHisId()));
    }

    /**
     * API流程异常时，解锁备份资源
     * */
    private void unLockBakResource() {
        if(ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")) {
            JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
            bakService.unlockBinlogForRestore(
                    Integer.valueOf(lockBinlog.get("custinsId").toString()),
                    new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                    new Date(Long.parseLong(lockBinlog.get("end").toString()))
            );
        }
        if(ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
            String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
            bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));
        }
    }
}


