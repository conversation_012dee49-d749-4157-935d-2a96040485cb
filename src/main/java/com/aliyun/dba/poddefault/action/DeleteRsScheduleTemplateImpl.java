package com.aliyun.dba.poddefault.action;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.aliyun.apsaradb.dbaasmetaapi.model.RsScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDeleteRsScheduleTemplateImpl")
public class DeleteRsScheduleTemplateImpl implements IAction {
    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);

        try {
            String bid = parameterHelper.getParameterValue(ParamConstants.USER_ID);
            Validate.notEmpty(bid,"null userId");
            String uid = parameterHelper.getParameterValue(ParamConstants.UID);
            Validate.notEmpty(uid,"null uid");
            String name = parameterHelper.getParameterValue("Name");
            Validate.notEmpty(name,"null name");
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, bid + "_" + uid, false);
            PodScheduleTemplate podTemplate = podTemplateHelper.getPodTemplateByRsTemplateNameAndLoginId(requestId, name, user.getBid() + "_" + user.getAliUid());
            if (podTemplate == null) {
                throw new Exception(String.format("RsTemplateName:%s is not exist", name));
            }

            podTemplateHelper.deletePodTemplateByRsTemplateNameAndLoginId(requestId, name, bid + '_' + uid);
            Map<String, Object> data = new HashMap<>();
            data.put("UserId", user.getUserId());
            data.put("Name", name);
            data.put("Template", podTemplate);
            return data;
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.warn(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE, ex.getMessage());
        }
    }
}
