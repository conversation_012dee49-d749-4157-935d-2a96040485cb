package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigChangeLog;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.instance.service.InstanceServiceImpl;
import com.aliyun.dba.poddefault.action.service.EncdbService;
import com.aliyun.dba.poddefault.action.service.TdeKmsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.user.service.UserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.Collections;
import java.util.HashMap;


@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceCLSImpl")
public class ModifyDBInstanceCLSImpl implements IAction {

    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Autowired
    private PodParameterHelper podParameterHelper;
    @Autowired
    private InstanceServiceImpl instanceService;
    @Autowired
    private CustinsServiceImpl custinsService;
    @Autowired
    private UserServiceImpl userService;
    @Autowired
    private CustinsParamService custinsParamService;
    @Autowired
    private EncdbService encdbService;
    @Autowired
    private TdeKmsService tdeKmsService;
    @Autowired
    private WorkFlowService workflowService;
    @Autowired
    private KmsApi kmsApi;

    private String generateDefaultRoleArn(String uid) {
        return String.format("acs:ram::%s:role/aliyunrdsinstanceencryptiondefaultrole", uid);
    }

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO primCustins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = RequestSession.getRequestId();

        try {
            primCustins = mysqlParamSupport.getAndCheckCustInstance(params);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String clsStatus = podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS);
            if (StringUtils.isBlank(regionId) || StringUtils.isBlank(clsStatus)) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            // 直接复用tde的region检查
            if (!tdeKmsService.checkTdeSupported(requestId, regionId)) {
                log.error("CLS not supported in this region {}", regionId);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            // 当前cls_key_mode
            CustinsParamDO curClsKeyMode =
                    custinsParamService.getCustinsParam(primCustins.getId(), PodDefaultConstants.CLS_KEY_MODE);
            String curClsKeyModeStr = curClsKeyMode == null ? PodDefaultConstants.CLS_MODE_NONE : curClsKeyMode.getValue();
            JSONObject workFlowParams = new JSONObject();
            // 只允许操作主实例。列加密相关操作只读都通过主节点进行同步
            if (primCustins.getInsType() != CustInsType.CUST_INS_TYPE_PRIMARY.getValue()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            // 主&只读实例状态检查 主要是检查状态是否为active
            List<CustInstanceDO> allCustins = new ArrayList<>(custinsService.getReadCustInstanceListByPrimaryCustinsId(primCustins.getId(), false));
            allCustins.add(primCustins);
            for (CustInstanceDO ins : allCustins) {
                encdbService.checkCustinsStatusAvailable(ins);
            }
            if (!clsStatus.equals("1")) {
                if (curClsKeyModeStr.equals(PodDefaultConstants.CLS_MODE_NONE)) {
                    log.error("instance CLS is not enabled, cannot set to mode 0.");
                    throw new RdsException(ErrorCode.INVALID_STATUS);
                }
                for (CustInstanceDO ins : allCustins) {
                    custinsParamService.setCustinsParam(ins.getId(), PodDefaultConstants.CLS_ENCRYPTION_KEY_ID, "");
                    custinsParamService.setCustinsParam(ins.getId(), PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_NONE);
                }
                // 关闭列加密，包括：关闭加密、删除实例本地密钥配置
                workFlowParams.put("encryptionStatus", 0);
            } else {
                // 启用列加密。
                workFlowParams.put("encryptionStatus", 1);
                // 算法 & 黑白名单模式配置可以支持所有云盘实例，通过dboss设置不需要区分具体产品形态。
                try {
                    String clsEncAlgo = podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO);
                    if (!StringUtils.isBlank(clsEncAlgo)) {
                        encdbService.setEncdbGlobalAlgo(primCustins, clsEncAlgo);
                    }
                    String clsWhitListMode = podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_WHITELIST_MODE);
                    if (!StringUtils.isBlank(clsWhitListMode)) {
                        encdbService.setWhiteListMode(primCustins, clsWhitListMode);
                    }
                } catch (RdsException re) {
                    log.error(requestId + " RdsException: ", re);
                    return ResponseSupport.createErrorResponse(re.getErrorCode());
                } catch (IOException ioe) {
                    log.error(requestId + " IOException: ", ioe);
                    return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
                }
                String kmsKeyId = podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_ENCRYPTION_KEY);
                // 根据cls_key_mode，检查参数，下发任务流
                String clsKeyMode = podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE);
                String isRoate = podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_IS_ROTATE, "0");
                if (!PodDefaultConstants.CLS_MODE_KMS_KEY.equals(clsKeyMode) && !PodDefaultConstants.CLS_MODE_CLIENT_KEY.equals(clsKeyMode)) {
                    throw new RdsException(ErrorCode.INVALID_PARAM);
                }
                if (PodDefaultConstants.CLS_MODE_KMS_KEY.equals(clsKeyMode)) {
                    Integer userId = primCustins.getUserId();
                    String uid = userService.getUserDOByUserId(userId).getLoginId().split("_")[1];
                    // uid白名单检查
                    if (!encdbService.isUIDAllowedWithCLSKmsMode(uid)) {
                        log.error("uid {} not allowd with CLS in this region {}", uid, regionId);
                        throw new RdsException(ErrorCode.UNSUPPORTED_USER_PERMISSION);
                    }
                    encdbService.checkCustinsSupportEncdbConfigAndKMS(primCustins);
                    // 设置密钥任务流
                    if (!curClsKeyModeStr.equals(PodDefaultConstants.CLS_MODE_KMS_KEY)) {
                        String roleArn = mysqlParamSupport.getRoleArn(params);
                        if (StringUtils.isBlank(roleArn)) {
                            roleArn = generateDefaultRoleArn(uid);
                        }
                        if (StringUtils.isBlank(kmsKeyId)) {
                            throw new RdsException(ErrorCode.INVALID_PARAM);
                        }
                        // check kms key availability
                        tdeKmsService.checkKeyIsAvailable(primCustins, roleArn, kmsKeyId, uid);
                        // same tag as TDE
                        tdeKmsService.ensureTagExistence(primCustins, roleArn, kmsKeyId, uid);
                        tdeKmsService.ensureUserRoleArn(primCustins, roleArn, uid);
                        for (CustInstanceDO ins : allCustins) {
                            custinsParamService.setCustinsParam(ins.getId(), PodDefaultConstants.CLS_ENCRYPTION_KEY_ID, kmsKeyId);
                            custinsParamService.setCustinsParam(ins.getId(), PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_KMS_KEY);
                        }
                        workFlowParams.put("kmsKeyId", kmsKeyId);
                        // check role authorization
                        if (!kmsApi.checkAssumeRoleOk(primCustins.getClusterName(), roleArn, uid)) {
                            throw new RdsException(ErrorCode.TDEPARAM_ERROR_ROLE);
                        }
                    } else {
                        if ("1".equals(isRoate)) {
                            // TODO(yangxiao)：支持轮转
                            log.error("key rotation not supported yet");
                            throw new RdsException(ErrorCode.UNSUPPORTED_OPTION_VALUE);
                        } else {
                            // 当前已经是kms_key模式，重复配置无意义。返回。
                            log.error("instance CLS is already configured with kms_key mode");
                            Map<String, Object> data = new HashMap<>();
                            data.put("DBInstanceID", primCustins.getId());
                            data.put("DBInstanceName", primCustins.getInsName());
                            return data;
                        }
                    }
                } else {
                    // 不支持从kms密钥模式切回普通模式
                    if (curClsKeyModeStr.equals(PodDefaultConstants.CLS_MODE_KMS_KEY)) {
                        throw new RdsException(ErrorCode.INVALID_STATUS);
                    }
                    // 启用加密，普通模式非kms
                    for (CustInstanceDO ins : allCustins) {
                        custinsParamService.setCustinsParam(ins.getId(), PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_CLIENT_KEY);
                    }
                }
            }
            // 实例状态
            for (CustInstanceDO ins : allCustins) {
                custinsService.updateCustInstanceStatusByCustinsId(ins.getId(), CustinsState.STATE_CLS_MODIFYING.getState(), CustinsState.STATE_CLS_MODIFYING.getComment());
            }
            // 任务流
            Object taskId = workflowService.dispatchTask(PodDefaultConstants.TARGET_TYPE_CUSTINS, primCustins.getInsName(),
                    primCustins.getDbType(), PodDefaultConstants.TASK_MODIFY_CLS, workFlowParams.toJSONString(),
                    WorkFlowService.TASK_PRIORITY_COMMON);
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", primCustins.getId());
            data.put("DBInstanceName", primCustins.getInsName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            log.error(requestId + " RdsException: ", re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            log.error(requestId + " Exception: ", e);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

}
