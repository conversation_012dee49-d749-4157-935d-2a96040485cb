package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.activityprovider.model.PerformanceLevelEnum;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.TransListService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultStartDBInstanceImpl")
public class StartDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(StartDBInstanceImpl.class);

    @Autowired
    private DBaasMetaService dBaasMetaService;

    @Autowired
    private MysqlParamSupport mysqlParamSupport;

    @Autowired
    private ReplicaSetService replicaSetService;

    @Autowired
    protected WorkFlowService workFlowService;

    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;

    @Resource
    protected PodParameterHelper podParameterHelper;

    @Autowired
    protected CommonProviderService commonProviderService;

    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected InstanceIDao instanceIDao;

    @Autowired
    protected TransListService transListService;

    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    protected PodTemplateHelper podTemplateHelper;
    @Resource
    protected RundPodSupport rundPodSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        try {
            String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
            ReplicaSet replicaSetMeta;

            try {
                replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);

                // TODO: 集群版暂不支持启停操作，等支持后再放开
                if (MysqlParamSupport.isCluster(replicaSetMeta.getCategory())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "cluster is not supported yet!");
                }
                else if (!(ReplicaSet.StatusEnum.STOPPED.equals(replicaSetMeta.getStatus()))) {
                    throw new RdsException(ErrorCode.INVALID_STATUS);
                }
                else if (workFlowService.isTaskExist(requestId, replicaSetMeta.getName())) {
                    logger.error("{} replicaset {} has unfinished tasks.", requestId, replicaSetMeta.getName());
                    throw new RdsException(ErrorCode.TASK_HAS_EXIST);
                }
            } catch (ApiException e) {
                logger.error("StartDBInstance api exception, requestId: {}, msg: {}", requestId, e.getMessage(), e);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }

            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                    requestId, replicaSetMeta.getName(), null, null, null, null);
            Optional<Replica> stoppedReplica = listReplicasInReplicaSet.getItems().stream().filter(x -> Replica.StatusEnum.STOPPED.equals(x.getStatus())).findFirst();

            Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSetMeta.getName());
            String taskStopReplicas = labels.get(PodDefaultConstants.TASK_STOP_INS_STAGE_2);

            // 资源已释放/正在释放，重新申请资源，拉起实例
            if (stoppedReplica.isPresent() || StringUtils.isEmpty(taskStopReplicas)) {
                return startInsFromStopStage2(replicaSetMeta, params);
            }

            // 直接下发任务，解锁实例即可
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", requestId);
            taskParamObject.put("replicaSetName", replicaSetMeta.getName());

            Object taskId = workFlowService.dispatchTask("custins", replicaSetMeta.getName(),
                    PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_START_INS_FROM_STOP_STAGE_1, taskParamObject.toJSONString(), 0);

            dBaasMetaService.getDefaultClient().deleteReplicaSetLabel(requestId, replicaSetMeta.getName(), PodDefaultConstants.TASK_STOP_INS_STAGE_2);
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetMeta.getName(), ReplicaSet.StatusEnum.STARTING.toString());

            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", replicaSetMeta.getId());
            data.put("DBInstanceName", replicaSetMeta.getName());
            data.put("TaskId", taskId);

            return data;
        } catch (RdsException re) {
            logger.error("StartDBInstance ex=" + re, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("StartDBInstance ex=" + ex, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }


    private Map<String, Object> startInsFromStopStage2(ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");

        boolean isSuccess = false;
        boolean isAllocated = false;
        String tmpReplicaSetName = String.format("%s-start-%s", replicaSetMeta.getName(), System.currentTimeMillis());

        try {
            // 申请计算资源
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(params);

            if (StringUtils.isNotBlank(modifyInsParam.getClusterName()) && modifyInsParam.isDHG()) {
                replicaSetResourceRequest.setDedicatedHostGroupId(modifyInsParam.getClusterName());
            }

            replicaSetResourceRequest.userId(modifyInsParam.getBid())
                    .bizType(replicaSetMeta.getBizType().toString())
                    .uid(modifyInsParam.getUid())
                    .sourceReplicaSetName(modifyInsParam.getDbInstanceName())
                    .replicaSetName(tmpReplicaSetName)
                    .regionId(modifyInsParam.getOldAvzInfo().getRegionId())

                    .primaryInsName(modifyInsParam.getDbInstanceName())
                    .insType(ReplicaSet.InsTypeEnum.TMP.toString())
                    .composeTag(minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, replicaSetMeta.getId().intValue()))
                    // 版本规格
                    .dbType(modifyInsParam.getDbType())
                    .dbVersion(modifyInsParam.getDbVersion())
                    .classCode(modifyInsParam.getTargetClassCode())
                    // 磁盘资源
                    .storageType(modifyInsParam.getTargetDiskType())
                    .diskSize(modifyInsParam.getTargetDiskSizeGB())
                    .scheduleTemplate(modifyInsParam.getScheduleTemplate())

                    //网络资源
                    .connType(ReplicaSet.ConnTypeEnum.PHYSICAL.toString())
                    .ignoreCreateVpcMapping(true);

            podTemplateHelper.setSpecSchedulerConfigSpread(replicaSetResourceRequest, modifyInsParam.getDbInstanceName());
            boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();
            if (isTargetSingleTenant) {
                // 单租户场景
                replicaSetResourceRequest.singleTenant(true).eniDirectLink(false);
            }
            boolean isArm = PodCommonSupport.isArm(modifyInsParam.getTargetInstanceLevel());
            if(isArm){
                //如果是arm架构需要指定arch
                replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.ARM);
            }
            // 需要迁移的角色
            List<Replica> currentReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId,
                    replicaSetMeta.getName(), null, null, null, null).getItems();
            val replicasRequest = new ArrayList<ReplicaResourceRequest>();
            for (val replica : currentReplicas) {
                ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
                if (StringUtils.isNotBlank(modifyInsParam.getTargetPerformanceLevel())) {
                    replicaResourceRequest.setPerformanceLevel(PerformanceLevelEnum.fromValue(modifyInsParam.getTargetPerformanceLevel()));
                }
                replicaResourceRequest.setDiskSize(replica.getDiskSizeMB()/1024);
                replicaResourceRequest.setClassCode(modifyInsParam.getTargetClassCode());
                replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
                replicaResourceRequest.setRole(replica.getRole().getValue());
                replicaResourceRequest.setZoneId(replica.getZoneId());
                replicaResourceRequest.setSubDomain(replica.getSubDomain());
                if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
                    rundPodSupport.completeReplicaNetworkConfig(replicaResourceRequest, replica);
                }
                replicasRequest.add(replicaResourceRequest);
            }
            replicaSetResourceRequest.setReplicaResourceRequestList(replicasRequest);
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
                rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            }

            logger.info("allocate resoure for start {} , request body: {}", tmpReplicaSetName, JSON.toJSONString(replicaSetResourceRequest));
            podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
            // 资源申请完毕
            isAllocated = commonProviderService.getDefaultApi().allocateResourceForResume(requestId, replicaSetMeta.getName(), replicaSetResourceRequest);

            // 临时实例刷主实例白名单
            Map<String, String> labels = ImmutableMap.of(PodDefaultConstants.SG_USE_OTHER_CUSTINS, replicaSetMeta.getId().toString());
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSetName, labels);
            // 只读实例配置白名单同步label
            podParameterHelper.setReadInsSgLabel(requestId, replicaSetMeta, tmpReplicaSetName);

            ReplicaSet tmpReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, tmpReplicaSetName, true);
            Integer transListId = transListService.create(replicaSetMeta, tmpReplicaSet, "start stopped ins", requestId);

            // 下发迁移任务
            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);

            taskParam.put("srcReplicaSetName", replicaSetMeta.getName());
            taskParam.put("destReplicaSetName", tmpReplicaSet.getName());
            taskParam.put(CustinsSupport.TRANS_ID, transListId);

            Object taskId = workFlowService.dispatchTask("custins", replicaSetMeta.getName(),
                    PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_START_INS_FROM_STOP_STAGE_2, taskParam.toString(), 0);

            Integer taskIdInt = Double.valueOf(taskId.toString()).intValue();
            this.instanceIDao.updateTransTaskIdById(transListId, taskIdInt);

            dBaasMetaService.getDefaultClient().deleteReplicaSetLabel(requestId, replicaSetMeta.getName(), PodDefaultConstants.TASK_START_INS_FROM_STOP_STAGE_2);
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetMeta.getName(), ReplicaSet.StatusEnum.STARTING.toString());

            isSuccess = true;
            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", replicaSetMeta.getId());
            data.put("DBInstanceName", replicaSetMeta.getName());
            data.put("TaskId", taskId);

            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getMessage(), e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (isAllocated && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, tmpReplicaSetName);
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    logger.error(requestId + " release resource for start ins failed: " + e.getMessage(), e);
                }
            }
        }
    }
}
