package com.aliyun.dba.poddefault.action;

import com.alibaba.metrics.StringUtils;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultDescribeExternalReplicationImpl")
public class DescribeExternalReplicationImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CloneDBInstanceImpl.class);
    private static final String CUSTINS_PARAM_EXTERNAL_REPLICATION = "externalReplication";
    private static final String RES_PARAM_EXTERNAL_REPLICATION = "ExternalReplication";
    private static final String RES_PARAM_REPLICATION_SOURCE = "ReplicationSource";
    private static final String RES_PARAM_REPLICATION_STATE = "ReplicationState";
    private static final String RES_PARAM_REPLICATION_LAG = "ReplicationLag";
    private static final String RES_PARAM_REPLICATION_ERROR = "ReplicationError";

    @Resource
    protected MysqlParameterHelper parameterHelper;
    @Resource
    protected CustinsParamService custinsParamService;
    @Resource
    protected DbossApi dbossApi;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    protected ReplicaSetService replicaSetService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = parameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        Map<String, Object> data = new HashMap<>();
        data.put("RequestId", requestId);
        CustinsParamDO externalReplicationParam = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_EXTERNAL_REPLICATION);
        if (!Objects.isNull(externalReplicationParam)) {
            String mode = externalReplicationParam.getValue();
            if (StringUtils.isBlank(mode)) {
                data.put(RES_PARAM_EXTERNAL_REPLICATION, "OFF");
            } else{
                data.put(RES_PARAM_EXTERNAL_REPLICATION, mode);
            }

            if ("ON".equalsIgnoreCase(mode)) {
                try {
                    // 检查复制状态并返回
                    Map<String, Object> slaveStatus = dbossApi.showSlaveStatus(custins.getId(), null);
                    if (Objects.isNull(slaveStatus)) {
                        logger.warn(requestId + " slave status is null");
                    } else {
                        Object masterHost = slaveStatus.get("Master_Host");
                        Object slaveIORunning = slaveStatus.get("Slave_IO_Running");
                        Object slaveSQLRunning = slaveStatus.get("Slave_SQL_Running");
                        Object lastIOErrno = slaveStatus.get("Last_IO_Errno");
                        Object lastIOError = slaveStatus.get("Last_IO_Error");
                        Object lastSQLErrno = slaveStatus.get("Last_SQL_Errno");
                        Object lastSQLError = slaveStatus.get("Last_SQL_Error");
                        Object secondsBehindMaster = slaveStatus.get("Seconds_Behind_Master");

                        data.put(RES_PARAM_REPLICATION_SOURCE, masterHost);
                        data.put(RES_PARAM_REPLICATION_LAG, secondsBehindMaster);
                        if ((!Objects.isNull(lastIOError) && StringUtils.isNotBlank((String) lastIOError)) ||
                                (!Objects.isNull(lastSQLError) && StringUtils.isNotBlank((String) lastSQLError))) {
                            data.put(RES_PARAM_REPLICATION_STATE, "Error");
                            Map<String, Object> errMap = new HashMap<>();
                            errMap.put("Last_IO_Errno", lastIOErrno);
                            errMap.put("Last_IO_Error", lastIOError);
                            errMap.put("Last_SQL_Errno", lastSQLErrno);
                            errMap.put("Last_SQL_Error", lastSQLError);
                            data.put(RES_PARAM_REPLICATION_ERROR, errMap);
                        } else if (!Objects.isNull(slaveIORunning) && !Objects.isNull(slaveSQLRunning)) {
                            if (String.valueOf(slaveIORunning).equalsIgnoreCase("Yes") && String.valueOf(slaveSQLRunning).equalsIgnoreCase("Yes")) {
                                data.put(RES_PARAM_REPLICATION_STATE, "Running");
                            } else if (String.valueOf(slaveIORunning).equalsIgnoreCase("Connecting")) {
                                data.put(RES_PARAM_REPLICATION_STATE, "Connecting");
                            } else {
                                data.put(RES_PARAM_REPLICATION_STATE, "Stopped");
                            }
                        } else {
                            logger.warn(requestId + " slave status: " + slaveStatus);
                        }
                    }
                } catch (Exception ex) {
                    logger.error(requestId + " Call dboss api failed, exception: " + ex);
                }
            }
        } else {
            data.put(RES_PARAM_EXTERNAL_REPLICATION, "OFF");
        }
        return data;
    }
}
