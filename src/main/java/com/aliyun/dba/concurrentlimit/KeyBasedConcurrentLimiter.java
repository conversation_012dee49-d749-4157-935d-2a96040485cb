package com.aliyun.dba.concurrentlimit;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Metrics;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * 多维度限流器，支持指定Key及对应并发量，控制每个维度的并发。
 * */
public class KeyBasedConcurrentLimiter {
    private String app = "";
    private final MeterRegistry metrics = Metrics.globalRegistry;

    public static final String RULE_USER = "user";
    public static final String RULE_INTERFACE = "interface";
    public static final String RULE_ENGINE = "engine";

    // 默认限流阈值
    private static final Integer DEFAULT_MAX_PERMITS = 50;

    // 最大保留的限流Key
    private static final Integer MAX_CACHE_KEY = 20480;

    // Key无访问后的过期时间
    private static final Integer KEY_EXPIRE_SECONDS = 300;

    // 每个Key对应的限流
    private Cache<String, Semaphore> limiters;

    // 每个Key对应的监控信息
    private final ConcurrentHashMap<String, Gauge> gauges = new ConcurrentHashMap<>();

    RemovalListener<String, Semaphore> listener = this::onRemoval;


    public KeyBasedConcurrentLimiter(String app) {
        this.app = app;

        limiters = CacheBuilder.newBuilder()
                .maximumSize(MAX_CACHE_KEY)
                .expireAfterAccess(KEY_EXPIRE_SECONDS, TimeUnit.SECONDS)
                .removalListener(listener)
                .build();

        Gauge.builder("concurrent_limiter_pool_size", limiters, Cache::size)
                .description("the pool size of concurrent limiter")
                .tag("application_name", app)
                .register(metrics);
    }


    /**
     * 多维度限流，所有维度锁定成功返回True，任意维度锁定失败返回False
     * */
    public boolean tryAcquire(List<ConcurrentRule> rules) {
        // 标记是否全部锁定成功
        boolean allIsLocked = true;
        for (ConcurrentRule rule : rules) {
            boolean lock = tryAcquire(rule);
            rule.setLock(lock);
            if (!lock) {
                allIsLocked = false;
                break;
            }
        }

        // 锁定失败时，回滚已经锁定的信号量
        if (!allIsLocked) {
            release(rules);
        }

        return allIsLocked;
    }

    /**
     * 尝试抢占信号量，如果抢占成功，返回true，否则返回false
     * */
    private boolean tryAcquire(ConcurrentRule rule) {
        Semaphore s = limiters.getIfPresent(rule.getKey());
        if (s == null) {
            synchronized (this) {
                s = limiters.getIfPresent(rule.getKey());
                if (s == null) {
                    s = new Semaphore(rule.getMaxPermits() == null ? DEFAULT_MAX_PERMITS : rule.getMaxPermits());
                    limiters.put(rule.getKey(), s);
                    // 非User级别的限流直接加入监控
                    if (!RULE_USER.equalsIgnoreCase(rule.getTag())) {
                        addToMetrics(rule, s);
                    }
                }
            }
        }
        boolean isAcquired = s.tryAcquire();

        // User级别的限流，只有UID受限后才加入监控
        if (!isAcquired && RULE_USER.equalsIgnoreCase(rule.getTag())) {
            addToMetrics(rule, s);
        }

        return isAcquired;
    }

    /**
     * 限流加入监控
     * */
    private void addToMetrics(ConcurrentRule rule, Semaphore s) {
        Gauge gauge = Gauge.builder("concurrent_limiter_available", s, Semaphore::availablePermits)
                .description("the state of concurrent limiter")
                .tag("application_name", app)
                .tag("action", rule.getKey())
                .tag("tag", rule.getTag())
                .register(metrics);
        gauges.put(rule.getKey(), gauge);
    }

    /**
     * 释放信号量
     * */
    public void release(List<ConcurrentRule> rules) {
        for (ConcurrentRule rule : rules) {
            release(rule);
        }
    }

    /**
     * 释放信号量
     * */
    private void release(ConcurrentRule rule) {
        if (rule.isLock()) {
            Semaphore semaphore = limiters.getIfPresent(rule.getKey());
            if (semaphore != null) {
                semaphore.release();
                rule.setLock(false);
            }
        }
    }

    /**
     * 缓存Key过期时，清理监控指标
     * */
    private void onRemoval(RemovalNotification<String, Semaphore> notification) {
        // 移除Gauge
        Gauge gauge = gauges.remove(notification.getKey());
        if (gauge != null) {
            metrics.remove(gauge.getId());
        }
    }

}
