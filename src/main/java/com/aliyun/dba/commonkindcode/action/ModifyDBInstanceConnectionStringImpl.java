package com.aliyun.dba.commonkindcode.action;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.response.AllocateVipRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.ConnAddrChangeLogDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsResourceService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.datasource.DataSourceContextHolder;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeModifyDBInstanceConnectionStringImpl")
public class ModifyDBInstanceConnectionStringImpl implements IAction {


    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterListImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MycnfService mycnfService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private IResApi resApi;
    @Autowired
    protected CustinsResourceService custinsResourceService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParaHelper.getAndCheckCustInstance();

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (custins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

//commented out by ligw 2017-03-29, don't check it since
//1. The task only processes the change logs of the current task, don't do garbage change logs
//2. A user can do nothing for such "internal error", but give a work order.
//3. check it in the bakend task implement.
//            if (custinsService.hasConnAddrChangeLogNotApplied(custins.getId())) {
//                logger.error("Custins:" + custins.getId() + " is ACTIVATE but still have "
//                        + "conn addr change log is not applied! This should not happen.");
//                super.printErrorJson(ErrorCode.INTERNAL_FAILURE);
//                return;
//            }

            // 检查是否修改次数超过上限
            custinsService.checkConnAddrChangeTimesExceed(custins.getId(), mysqlParaHelper.getAction(), null);
            boolean isConnectionStringToSsl = mysqlParamSupport.isConnectionStringToSsl(actionParams, custins);
            if (isConnectionStringToSsl){
                throw new RdsException(ErrorCode.INVALID_CONNECTIONSTRING, "The link address has been used by SSL, modification and deletion are prohibited");
            }
            List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                    .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

            String connAddrCust = mysqlParaHelper.getParameterValue("connectionstring");
            String netTypeStr = mysqlParaHelper.getParameterValue("dbinstancenettype", null);
            Integer netType = null;

            if (netTypeStr != null) {
                netType = CustinsSupport.getNetType(netTypeStr);
            }
            // 至少要传一个参数
            if (netType == null && connAddrCust == null) {
                return createErrorResponse(ErrorCode.MISSING_ALL_PARAMETERS);
            }
            CustinsConnAddrDO updateCustinsConnAddr = null;
            List<CustinsConnAddrDO> custinsConnAddrAll = new ArrayList<CustinsConnAddrDO>();

            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
                // 过滤，获取要删除的网络连接信息
                if (netType != null && !netType.equals(custinsConnAddr.getNetType())) {
                    continue;
                }
                if (connAddrCust != null && !connAddrCust
                        .equals(custinsConnAddr.getConnAddrCust())) {
                    continue;
                }
                updateCustinsConnAddr = custinsConnAddr;
                custinsConnAddrAll.add(custinsConnAddr);
            }

            if (updateCustinsConnAddr == null || !updateCustinsConnAddr.isConnAddrUserVisible()) {
                return createErrorResponse(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
            }

            // 环境下判断是否需要更换vip
            Integer status = custinsService.getUpgradeStatusByVipAndVpcId(updateCustinsConnAddr.getVip(), updateCustinsConnAddr.getVpcId());

            // 检查新地址及端口的合法性
            String newConnAddrCustPrex = mysqlParaHelper.getParameterValue("newconnectionstring");
            String newPort = mysqlParaHelper.getParameterValue("newport");
            if (newConnAddrCustPrex == null && newPort == null) {
                return createErrorResponse(ErrorCode.MISSING_ALL_PARAMETERS);
            }
            String newConnAddrCust = null;
            if(StringUtils.isNotEmpty(newConnAddrCustPrex)) {
                // 支持指定conn的DBType后缀
                String theConnDBType = mysqlParaHelper.getParameterValue("theconndbtype", custins.getDbType());
                String regionId= mysqlParaHelper.getRegionIdByClusterName(custins.getClusterName());
                String oldPrefix = StringUtils.replace(connAddrCust, mysqlParaHelper.getConnAddrCustLast(regionId, theConnDBType), "");
                // 兼容region级last conn addr last 配置前后 获取到的last不一样的情况，如果没有替换成功，则再用配置前的替换一次
                if(Objects.equals(oldPrefix, connAddrCust)) {
                    oldPrefix = StringUtils.replace(connAddrCust, mysqlParaHelper.getConnAddrCustLast(theConnDBType), "");
                }
                if(!Objects.equals(oldPrefix, newConnAddrCustPrex)) {
                    newConnAddrCustPrex = CheckUtils.checkValidForConnAddrCust(newConnAddrCustPrex);
                    newConnAddrCust = mysqlParaHelper.getConnAddrCust(newConnAddrCustPrex, regionId, theConnDBType);
                }
            }

            if (newPort != null && custins.isMysql()) {
                newPort = CheckUtils.parseInt(newPort, 1000, 65534, ErrorCode.INVALID_PORT).toString();
            }
            boolean isModifyConnAddrCust = false;
            boolean isModifyPort = false;
            if (newConnAddrCust != null && !newConnAddrCust
                    .equals(updateCustinsConnAddr.getConnAddrCust())) {
                isModifyConnAddrCust = true;
            }
            if (newPort != null && !newPort.equals(updateCustinsConnAddr.getVport())) {
                isModifyPort = true;
            }

            if (newConnAddrCust == null) {
                newConnAddrCust = updateCustinsConnAddr.getConnAddrCust();
            }
            if (newPort == null) {
                newPort = updateCustinsConnAddr.getVport();
            }
            boolean isSqlServerOnEcs = custins.isCustinsOnEcs() && custins.isSqlserver();
            Boolean isValidForModifyPort = custins.isMysql() || custins.isMariaDB() || isSqlServerOnEcs;
            if (custins.isDockerCustins() && !isValidForModifyPort) {
                if (isModifyPort) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
            }

            // 连接地址和端口至少有一个与现在的值不一致
            if (!isModifyConnAddrCust && !isModifyPort) {
                return createErrorResponse(ErrorCode.INVALID_CONNECTIONSTRING_OR_PORT);// 新增错误码
            }

            List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<ConnAddrChangeLogDO>();
            // 修改端口需要重新申请vip
            // 已经完成了vip的更换，不需要任何处理
            if (isModifyPort) {
                if (CustinsSupport.isVpcNetType(updateCustinsConnAddr.getNetType()) && !isValidForModifyPort) {
                    return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
                }
                // 如果 DNS 也修改, 校验 DNS 是否被占用
                if (isModifyConnAddrCust) {
                    Response<AllocateVipRespModel> response = resApi
                            .allocateDns(custins.getId(), updateCustinsConnAddr.getNetType(), newConnAddrCust,
                                    0);
                    if (!response.getCode().equals(200)) {
                        custinsResourceService.createResourceRecord(mysqlParaHelper.getAction(),mysqlParaHelper.getUUID(),custins.getClusterName(), custins.getInsName(),
                                JSON.toJSONString(response));
                        throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                                ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                    }
                }
                // 默认完成了vip的更换
                for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrAll) {
                    connAddrChangeLogs.addAll(createConnAddrChangeLogsForUpgradeNetWork(
                            custins,
                            custinsConnAddr,
                            custinsConnAddr.getConnAddrCust(),
                            custinsConnAddr.getVport(),
                            newConnAddrCust,
                            newPort));
                }
            }
            // 修改连接串
            else {
                Response<AllocateVipRespModel> response = resApi
                        .allocateDns(custins.getId(), updateCustinsConnAddr.getNetType(), newConnAddrCust,
                                0);
                if (!response.getCode().equals(200)) {
                    custinsResourceService.createResourceRecord(mysqlParaHelper.getAction(),mysqlParaHelper.getUUID(),custins.getClusterName(), custins.getInsName(),
                            JSON.toJSONString(response));
                    throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                            ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
                }

                for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrAll) {
                    connAddrChangeLogs.addAll(createConnAddrChangeLogsForUpdate(
                            custins,
                            custinsConnAddr,
                            custinsConnAddr.getConnAddrCust(),
                            custinsConnAddr.getVport(),
                            newConnAddrCust,
                            newPort));
                }
            }

            Integer taskId = null;
            String taskKey = TaskSupport.getTaskChangeConnAddrKey(mysqlParaHelper.getAction(),
                    CustinsSupport.isVpcNetType(netType));
            try {
                taskId = taskService.changeConnAddrTask(
                        mysqlParaHelper.getAction(), custins, connAddrChangeLogs,
                        CustinsState.STATE_NET_MODIFYING, taskKey, mysqlParaHelper.getOperatorId());
            } catch (Exception ex) {
                logger.error("Custins: " + custins.getId()
                        + " ModifyDBInstanceConnectionString failed when create task. Details: "
                        + JSON.toJSONString(connAddrChangeLogs));
                throw new Exception(ex);
            }
            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("NewConnectionString", newConnAddrCust);
            data.put("NewPort", newPort);
            data.put("OldConnectionString", updateCustinsConnAddr.getConnAddrCust());
            data.put("OldPort", updateCustinsConnAddr.getVport());
            data.put("DBInstanceNetType", updateCustinsConnAddr.getNetType());
            data.put("TaskId", taskId);
            return (data);

        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("modify conn string failed:", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
        }
    }

    /**
     * 10G 升级40G的需求，经典与vpc的流程不同
     *
     * @param custins
     * @param custinsConnAddr
     * @return
     * @throws RdsException
     */
    private List<ConnAddrChangeLogDO> createConnAddrChangeLogsForUpgradeNetWork(
            CustInstanceDO custins, CustinsConnAddrDO custinsConnAddr,
            String fromDns, String fromPort, String toDns, String toPort) throws RdsException {
        if (custinsConnAddr.isVpcNetType()) {
            // vpc情况下需要到任务流中去升级lb id
            return createConnAddrChangeLogsForUpdate(custins, custinsConnAddr, fromDns, fromPort, toDns, toPort);
        } else {
            // 经典网络需要更换vip
            return createConnAddrChangeLogsForChangePublicOrPrivateVIP(custins, custinsConnAddr, fromDns, fromPort, toDns, toPort);
        }
    }

    /**
     * @param custins
     * @param custinsConnAddr vpc连接
     * @return
     * @throws RdsException
     */
    public List<ConnAddrChangeLogDO> createConnAddrChangeLogsForUpdate(
            CustInstanceDO custins, CustinsConnAddrDO custinsConnAddr,
            String fromDns, String fromPort, String toDns, String toPort) throws RdsException {

        String vswitchId = "";
        // 如果是vpc实例
        if (CustinsSupport.isVpcNetType(custinsConnAddr.getNetType())) {
            // 获取VswithcID
            vswitchId = custinsService.getVswitchIdByVpcIpAndVpcId(
                    custinsConnAddr.getVip(), custinsConnAddr.getVpcId());
        }

        // 更新
        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<>(1);
        ConnAddrChangeLogDO updateChangeLog = ConnAddrSupport
                .createConnAddrChangeLogForUpdateNetType(
                        custins.getId(),
                        custinsConnAddr.getNetType(),
                        fromDns,
                        custinsConnAddr.getVip(),
                        fromPort,
                        custinsConnAddr.getUserVisible(),
                        custinsConnAddr.getTunnelId(),
                        custinsConnAddr.getVpcId(),
                        vswitchId,
                        toDns,
                        custinsConnAddr.getVip(),
                        toPort,
                        custinsConnAddr.getUserVisible(),
                        custinsConnAddr.getTunnelId(),
                        custinsConnAddr.getVpcId(),
                        vswitchId);

        connAddrChangeLogs.add(updateChangeLog);
        return connAddrChangeLogs;
    }

    /**
     * @param custins
     * @param custinsConnAddr 经典连接更换vip
     * @return
     * @throws RdsException
     */
    public List<ConnAddrChangeLogDO> createConnAddrChangeLogsForChangePublicOrPrivateVIP(
            CustInstanceDO custins, CustinsConnAddrDO custinsConnAddr,
            String fromDns, String fromPort, String toDns, String toPort) throws RdsException {

        if (!CustinsSupport.NET_TYPE_PUBLIC.equals(custinsConnAddr.getNetType()) &&
                !CustinsSupport.NET_TYPE_PRIVATE.equals(custinsConnAddr.getNetType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
        }

        List<ConnAddrChangeLogDO> connAddrChangeLogs = new ArrayList<>(2);
        ConnAddrChangeLogDO delConnAddrLog = ConnAddrSupport
                .createConnAddrChangeLogForDeleteNetType(
                        custins.getId(),
                        custinsConnAddr.getNetType(),
                        fromDns,
                        custinsConnAddr.getVip(),
                        fromPort,
                        custinsConnAddr.getUserVisible(),
                        custinsConnAddr.getTunnelId(),
                        custinsConnAddr.getVpcId(),
                        custinsConnAddr.getVswitchId(),
                        custinsConnAddr.getrwType());


        String ipVersion = custinsService.getIpVersionByVipAndVpcId(custinsConnAddr.getVip(), custinsConnAddr.getVpcId());

        Response<AllocateVipRespModel> response;

        if (CustinsSupport.NET_TYPE_PUBLIC.equals(custinsConnAddr.getNetType())) {
            response = resApi.allocateVipV6(custins.getId(),
                    custinsConnAddr.getNetType(),
                    toDns,
                    0,
                    ipVersion,
                    custinsConnAddr.getVip(),
                    custinsConnAddr.getVswitchId(),
                    custinsConnAddr.getTunnelId(),
                    custinsConnAddr.getVpcId(),
                    custinsConnAddr.getVpcInstanceId(),
                    custinsConnAddr.getrwType());
        } else {
            response = resApi.allocateVip(custins.getId(),
                    custinsConnAddr.getNetType(),
                    toDns,
                    0,
                    custinsConnAddr.getVip(),
                    custinsConnAddr.getVswitchId(),
                    custinsConnAddr.getTunnelId(),
                    custinsConnAddr.getVpcId(),
                    custinsConnAddr.getVpcInstanceId(),
                    custinsConnAddr.getrwType());
        }

        // 申请VIP & DNS
        if (!response.getCode().equals(200)) {
            custinsResourceService.createResourceRecord(mysqlParaHelper.getAction(),mysqlParaHelper.getUUID(),
                    custins.getClusterName(), custins.getInsName(),
                    JSON.toJSONString(response));
            throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                    ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(response));
        }

        String vipInfo;
        if (ipVersion.equals("ipv6")) {
            vipInfo = response.getData().getVipV6();
        } else {
            vipInfo = response.getData().getVip();
        }

        // vip 变更需求
        boolean safeDelete = mysqlParaHelper.getAndCheckSafeDelete();
        ConnAddrChangeLogDO updateConnAddrLog = null;
        boolean needUpdateChangeLog = false;
        if (safeDelete && !custinsConnAddr.getNetType().equals(CustinsSupport.NET_TYPE_VPC)) {
            if (custins.getKindCode().equals(KIND_CODE_NC)){
                // 物理机
                if (custins.isMysql56()){
                    needUpdateChangeLog = true;
                }
            }else if (custins.getKindCode().equals(KIND_CODE_ECS_VM)){
                // on ecs
                if (custins.isMysql57()){
                    needUpdateChangeLog = true;
                }
            } else if (custins.getKindCode().equals(KIND_CODE_DOCKER_ON_ECS)){
                // docker on 物理机
                if (custins.isMysql57()){
                    needUpdateChangeLog = true;
                }
            }
        }
        if (needUpdateChangeLog) {
            String bakAddr = "bakunlc4m8jy." + custinsConnAddr.getConnAddrCust();
            updateConnAddrLog = ConnAddrSupport
                    .createConnAddrChangeLogForUpdateNetType(
                            custins.getId(),
                            custinsConnAddr.getNetType(),
                            custinsConnAddr.getConnAddrCust(),
                            custinsConnAddr.getVip(),
                            custinsConnAddr.getVport(),
                            custinsConnAddr.getUserVisible(),
                            0,
                            "",
                            "",
                            bakAddr,
                            custinsConnAddr.getVip(),
                            custinsConnAddr.getVport(),
                            CustinsConnAddrDO.USER_VISIBLE_NO,
                            0,
                            "",
                            "");
        }

        // 添加
        ConnAddrChangeLogDO addConnAddrLog = ConnAddrSupport
                .createConnAddrChangeLogForCreateNetType(
                        custins.getId(),
                        custinsConnAddr.getNetType(),
                        toDns,
                        vipInfo,
                        toPort,
                        custinsConnAddr.getUserVisible(),
                        custinsConnAddr.getTunnelId(),
                        custinsConnAddr.getVpcId(),
                        custinsConnAddr.getVswitchId(),
                        custinsConnAddr.getrwType());

        connAddrChangeLogs.add(delConnAddrLog);
        if (updateConnAddrLog != null) {
            connAddrChangeLogs.add(updateConnAddrLog);
        }
        connAddrChangeLogs.add(addConnAddrLog);
        return connAddrChangeLogs;
    }

}
