package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.model.TopologyResponseWithStatus;
import com.aliyun.dba.adb_vip_manager_client.model.TopoConnection;
import com.aliyun.dba.adb_vip_manager_client.model.TopoNode;
import com.aliyun.dba.adb_vip_manager_client.ApiException;

import com.aliyun.dba.base.lib.LinkService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeDBInstanceTopoInfoImpl")
public class DescribeDBInstanceTopoInfoImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterListImpl.class);

    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;

    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;

    @Autowired
    protected LinksApi linksApi;

    @Autowired
    protected LinkService linkService;

    @Autowired
    protected ResourceService resourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParaHelper.getAndCheckCustInstance();
            String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);
            Map<String, Object> data = new HashMap<>(10);
            data.put("InstanceName", custins.getInsName());
            TopologyResponseWithStatus topoRespStat = linksApi.getTopology(custins.getInsName(), requestId);
            // get nodes detail
            List<TopoNode> topoNodes = topoRespStat.getData().getNodes();
            List<HashMap<String, Object>> nodes = new ArrayList<>(10);
            for (TopoNode node : topoNodes) {
                nodes.add(linkService.extractDataRsp(node));
            }
            data.put("Nodes", nodes);
            // get connections details
            List<TopoConnection> topoConnections = topoRespStat.getData().getConnections();
            List<HashMap<String, Object>> connections = new ArrayList<>(10);
            for(TopoConnection conn : topoConnections) {
                connections.add(linkService.extractDataRsp(conn));
            }
            data.put("Connections", connections);
            return data;
        } catch (ApiException ex) {
            String exBody = ex.getResponseBody();
            return linkService.extractRsp(exBody);
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
