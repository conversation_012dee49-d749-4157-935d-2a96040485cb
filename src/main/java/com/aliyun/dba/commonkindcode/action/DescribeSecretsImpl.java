package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dataapi.service.RDSDataAPIService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeSecretsImpl")
public class DescribeSecretsImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeSecretsImpl.class);

    @Autowired
    private RDSDataAPIService rdsDataAPIService;

    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

        try {
            String regionId = mysqlParameterHelper.getParameterValue("regionId");
            Long uid = Long.valueOf(mysqlParameterHelper.getUID());
            String bid = mysqlParameterHelper.getBID();
            String dbInstanceName = mysqlParameterHelper.getParameterValue("dbInstanceName");
            Long pageSize = Long.valueOf(mysqlParameterHelper.getParameterValue("pageSize"));
            Long pageNumber = Long.valueOf(mysqlParameterHelper.getParameterValue("pageNumber"));

            Map<String, Object> result = rdsDataAPIService.describeSecrets(regionId, uid, bid, dbInstanceName, pageSize, pageNumber);

            String requestId = mysqlParameterHelper.getParameterValue("requestId");
            if (requestId == null) {
                requestId = UUID.randomUUID().toString().toUpperCase(Locale.ROOT);
            }

            result.put("RequestId", requestId);
            return result;

        } catch (Exception ex) {
            logger.error("describe secrets action failed: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
