package com.aliyun.dba.commonkindcode.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceSpec;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.google.gson.Gson;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

/**
 * CBM 选择小版本使用
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeGetLatestEngineImageImpl")
@Slf4j
public class GetLatestEngineImageImpl implements IAction {

    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private MinorVersionService minorVersionService;
    @Resource
    private DBaasMetaService dBaasMetaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        val type = paramSupport.getRequiredParameterValue(params, "Engine");
        val version = paramSupport.getRequiredParameterValue(params, "EngineVersion");
        val category = paramSupport.getRequiredParameterValue(params, "Category");
        val tag = ObjectUtils.firstNonNull(paramSupport.getParameterValue(params, "Tag"), MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_DOCKER_IMAGE);
        val requestId = paramSupport.getRequiredParameterValue(params, ParamConstants.REQUEST_ID);
        val regionId = paramSupport.getParameterValue(params, ParamConstants.REGION);


        val releaseDateList = minorVersionService.getReleaseDateListByTag(
                type,
                version,
                KindCodeParser.KIND_CODE_NEW_ARCH,
                category,
                tag
        );


        if (releaseDateList.isEmpty()) {
            return createErrorResponse(ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND, "No existing versions.");
        }

        val releaseDate = releaseDateList.get(0);


        val specTag = String.format("%s_%s_%s", tag, "cloud_disk", releaseDate);
        val client = dBaasMetaService.getDefaultClient();

        ServiceSpec spec = null;
        try {
            spec = client.getServiceSpec(
                    requestId, type, version, "pod_arch_v1",
                    category, specTag, false
            );
        } catch (ApiException e) {
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE, e.getMessage());
        }

        val specContent = spec.getSpec();
        val gson = new Gson();
        val specObj = gson.fromJson(specContent, HashMap.class);
        val containers = (List<Map<String, Object>>) specObj.get("containers");
        val container = containers.get(0);
        val image = (String) container.get("image");


        Map<String, Object> response = new HashMap<>();
        response.put("ReleaseDate", releaseDate);
        response.put("Image", image);

        if (!StringUtils.isEmpty(regionId)) {
            val i = image.split("/");
            val namespace = i[1];
            val rest = i[2];
            val config = getDockerHubEndpointAndNamespace();

            if (config != null) {
                val endpoint = config.getFirst();
                val ns = ObjectUtils.firstNonNull(config.getSecond(), namespace);

                if (endpoint != null) {
                    val img = String.join("/", endpoint, ns, rest);
                    response.put("ImageIntranet", img);
                    response.put("ImageInternet", img);
                } else {
                    String imageIntranet;
                    String imageInternet;
                    try {
                        if (MysqlParamSupport.useEnterpriseRegistry(dBaasMetaService, regionId, MySQLParamConstants.DEFAULT_DB_TYPE, null)) {
                            imageIntranet = String.join("/", String.format("registry-%s.apsaradb-cr.aliyuncs.com", regionId), ns, rest);
                            imageInternet = imageIntranet;
                        }
                        else {
                            imageIntranet = String.join("/", String.format("registry-vpc.%s.aliyuncs.com", regionId), ns, rest);
                            imageInternet = String.join("/", String.format("registry.%s.aliyuncs.com", regionId), ns, rest);
                        }

                    } catch (ApiException e) {
                        log.info("query DBaasMetaService failed, exception: " + JSON.toJSONString(e));
                        throw new RdsException(ErrorCode.INVALID_REGION);
                    }
                    response.put("ImageIntranet", imageIntranet);
                    response.put("ImageInternet", imageInternet);
                }
            }
        }

        return response;
    }


    @Nullable
    public Tuple2<String, String> getDockerHubEndpointAndNamespace() {
        val requestId = UUID.randomUUID().toString();
        List<Config> configList = null;
        try {
            configList = dBaasMetaService.getDefaultClient().listConfigs(requestId, "DOCKER_REGISTRY").getItems();
        } catch (ApiException e) {
            return null;
        }

        if (configList == null || configList.isEmpty()) {
            return null;
        }

        val content = configList.get(0).getValue();

        if (StringUtils.isBlank(content)) {
            return null;
        }

        val info = new Gson().fromJson(content, HashMap.class);

        Map<String, String> vpc = (Map<String, String>) info.get("vpc");

        if (vpc == null || vpc.isEmpty()) {
            return null;
        }

        return new Tuple2<>(vpc.get("endpoint"), vpc.get("namespace"));
    }

}
