package com.aliyun.dba.commonkindcode.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsIpWhiteListDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeModifyDBInstanceNoTemplateParameterImpl")
public class ModifyDBInstanceNoTemplateParameterImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceNoTemplateParameterImpl.class);


    private static final String ALL_CIDRS = "0.0.0.0/0";

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected IpWhiteListService ipWhiteListService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;

    /**
     * 已处理，直接设置参数，与mycnf_template表无关联
     * */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            String insName = mysqlParaHelper.getAndCheckDBInstanceName();
            custins = mysqlParaHelper.getAndCheckCustInstance();
            if (custins == null) {
                if (custinsService.checkDeletedCustInstanceByInsName(insName)) {
                    return createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);
                }
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }
            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isReadAndWriteLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            Map<String, Object> parameterMap = mysqlParaHelper.getAndCheckParameters();

            if (parameterMap.containsKey(CustinsParamSupport.CUSTINS_PARAM_NAME_PASSWORD_FREE_FLAGS) &&
                !parameterMap.get(CustinsParamSupport.CUSTINS_PARAM_NAME_PASSWORD_FREE_FLAGS).toString().equals("0")) {
                // check ip whitelist, if whitelist == "0.0.0.0/0" 禁止设置免密码
                List<CustinsIpWhiteListDO> ipWhiteListGroupList = ipWhiteListService.getCustinsIpWhiteList(
                    custins.getId(), null, null, null);
                if (ipWhiteListGroupList != null) {
                    for (CustinsIpWhiteListDO ip_white: ipWhiteListGroupList) {
                        if (ip_white.getIpWhiteList().indexOf(ALL_CIDRS) != -1) { /* find '0.0.0.0/0 '*/
                            return createErrorResponse(ErrorCode.INVALID_PASSWORD_FREE_WHITELIST_CIDR);
                        }
                    }
                }
            }

            taskService.checkTaskNotStartExceed(custins.getId());

            String accessId = mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID);
            Integer taskId = -1;
            if (custins.getCharacterType().equals("logic") || custins.getCharacterType().equals("normal")) {
                // first set custins_param
                for (String key : parameterMap.keySet()) {
                    custinsParamService.setCustinsParam(custins.getId(), key, parameterMap.get(key).toString());
                }
                taskId = createModifyParamTask(parameterMap, custins, false);
                taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
            } else {
                return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
            }


            Map<String, Object> data = new HashMap<>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;

        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

    protected Integer createModifyParamTask(Map<String, Object> parameterMap, CustInstanceDO custins, Boolean preferRestart) {
        Integer custinsId = custins.getId();
        Integer taskId = instanceService.updateInstanceNoTemplateParameterTask(
            mysqlParaHelper.getAction(), custinsId, custins.getDbType(), JSONObject.toJSONString(parameterMap),
            mysqlParaHelper.getOperatorId());
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
        return taskId;
    }
}
