package com.aliyun.dba.commonkindcode.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceConfig;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceSpec;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.parameter.MysqlParamMinorVerionHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.commonkindcode.support.ParamChecker;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsRelationIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.entity.MycnfChangeLog;
import com.aliyun.dba.instance.entity.MycnfTemplate;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.kpi.service.KpiService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_UP;
import static com.aliyun.dba.custins.support.CustinsSupport.useMycnfTemplateExtra;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_RELATION_NO_REL_CUSTINS_ID;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_RELATION_SYNC_TO;
import static com.aliyun.dba.task.support.TaskSupport.TASK_FLUSH_PARAMS;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeModifyDBInstanceParameterImpl")
public class ModifyDBInstanceParameterImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceParameterImpl.class);

    @Autowired
    protected CustinsService custinsService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private MycnfService mycnfService;
    @Autowired
    protected DockerCustinsService dockerCustinsService;
    @Autowired
    private CustinsParamGroupsService custinsParamGroupsService;
    @Autowired
    private KmsService kmsService;
    @Autowired
    private KpiService kpiService;
    @Resource
    private CustinsRelationIDao custinsRelationIDao;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    private MysqlParamGroupHelper mysqlParamGroupHelper;

    @Autowired
    private MysqlParamMinorVerionHelper mysqlParamMinorVerionHelper;

    private static final String PARAM_FLUSH_PLACES = "FlushPlaces";
    private static final String PARAM_DRY_RUN = "DryRun";

    private static final Set<String> FLUSH_PLACE_LIST = new HashSet<String>() {{
        this.add("Metadb");
        this.add("Mycnf");
        this.add("Instance");
    }};
    /**
     * 已处理
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map)
            throws RdsException {

        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
            String insName = mysqlParaHelper.getAndCheckDBInstanceName();
            CustInstanceDO custins = mysqlParaHelper.getAndCheckCustInstance();

            if ("true".equals(mysqlParaHelper.getParameterValue(PARAM_DRY_RUN))) {
                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", custins.getId());
                data.put("DBInstanceName", custins.getInsName());
                return data;
            }

            if (custins == null && custinsService.checkDeletedCustInstanceByInsName(insName)) {
                return createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);//实例已销毁
            }
            if (custins == null) {
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);//实例不存在，或者不是实例拥有者
            }

            // 兼容接口传logic实例的场景
            if (custins.isLogic() && !custins.isMysqlSphinx()) {
                custins = mysqlParaHelper.getPhyscialCustinsByParentId(custins.getId());
            }

            if (custins.isShare()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);//实例非激活
            }
            if (!custins.isNoLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            taskService.checkTaskNotStartExceed(custins.getId());

            // 基础信息
            Integer taskId = -1;

            String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);
            String accessId = mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID);
            String dbType = custins.getDbType();
            String dbVersion = mysqlParaHelper.getCfgDbVersion(custins);
            String characterType = custins.getCharacterType();
            Map<String, Object> parameterMap = null;

            // 参数模板ID
            Long pId = null;
            // 传入的参数模板id
            String parameterGroupId = null;
            boolean modifiedWithParameters = false;
            boolean isSysParamGroup = false;

            // 获取参数
            if (mysqlParaHelper.hasParameter("Parameters")) {
                modifiedWithParameters = true;
                parameterMap = mysqlParaHelper.getAndCheckParameters();
                logger.info("modifyDBInstanceParameter in ext-mysql: Parameters " + JSON.toJSONString(parameterMap));
                // 2---- 入参连接数值与参数快照对比
                if (null != parameterMap.get("max_connections")) {
                    InstanceConfig maxConnConfig = dBaasMetaService.getDefaultClient().getReplicaSetConfig(requestId, custins.getInsName(), "max_connections");
                    if (StringUtils.equalsIgnoreCase(maxConnConfig.getValue(), String.valueOf(parameterMap.get("max_connections")))) {
                        logger.warn("max_connections param value is equal to previous");
                        parameterMap.remove("max_connections");
                    }
                    // 检查修改参数是否为空
                    if (parameterMap.isEmpty()) {
                        Map<String, Object> data = new HashMap<>();
                        data.put("TaskId", null);
                        data.put("DBInstanceID", custins.getId());
                        data.put("DBInstanceName", custins.getInsName());
                        data.put("Message", "no params need to modify");
                        return data;
                    }
                }

                Object defaultTimeZone = parameterMap.get(MySQLParamConstants.DEFAULT_TIME_ZONE);
                if (StringUtils.equalsIgnoreCase(String.valueOf(defaultTimeZone), MySQLParamConstants.SYSTEM_TIME_ZONE)) {
                    logger.error("default_time_zone=SYSTEM may cause master slave time_zone inconsistent!");
                    return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
                }
            }

            InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());

            // 获取参数模板
            if (mysqlParaHelper.hasParameter("ParameterGroupId")) {
                parameterGroupId = mysqlParaHelper.getParameterValue("ParameterGroupId");
                Map paramGroup = parameterGroupTemplateGenerator.paramGroupMatchValidate(dbType, dbVersion, instanceLevelDO.getCategory(), parameterGroupId, false);
                pId = Long.valueOf(paramGroup.get("ParameterGroupId").toString());
                isSysParamGroup = "0".equals(paramGroup.get("ParameterGroupType").toString());
            }

            // 生成基础的参数模板
            boolean isPolarxHatp = custinsParamService.getCustinsParam(custins.getId(), "is_polarx_hatp") != null;
            CustinsParamDO custInsParamDO = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_PARAM_GROUP_ID);

            //实例当前绑定的参数模板id
            String paramGroupId = custInsParamDO == null ? "" : custInsParamDO.getValue();
            MycnfCustinstanceDO collationServer = mycnfService.getMycnfCustinstance(custins.getId(), "collation_server");

            ParamContext paramContext = new ParamContext(custins, paramGroupId);
            paramContext.setIgnoreVisible(true);
            paramContext.setAliGroup(false);
            paramContext.setArm(false);
            paramContext.setPolarxHatp(isPolarxHatp);
            paramContext.setCategory(instanceLevelDO.getCategory());
            paramContext.setCpuCoreCount(instanceLevelDO.getCpuCores());
            paramContext.setMemSize(instanceLevelDO.getMemSize().longValue());
            paramContext.setDiskSize(custins.getDiskSize());
            paramContext.setMaxConnection(instanceLevelDO.getMaxConn());
            if (collationServer != null) {
                paramContext.setExistCollationServer(true);
            }

            if (custins.isReadOrBackup() && custins.getPrimaryCustinsId() != 0) {
                CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                if (primaryCustins != null) {
                    InstanceLevelDO primaryLevel = instanceService.getInstanceLevelByLevelId(primaryCustins.getLevelId());
                    if (useMycnfTemplateExtra(custins.getDbType(), custins.getDbVersion(), primaryLevel.getCategory())) {
                        paramContext.setCategory(primaryLevel.getCategory());
                    }
                }
            }

            // 兼容8.0冷香短暂上线过一个beta版本，线上还有几百个实例，docker on ecs形态。2023.05.31
            if (KIND_CODE_NEW_ARCH.equals(custins.getKindCode())) {
                ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpecById(
                        requestId, dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(
                                requestId, custins.getInsName()));
                String tag = serviceSpec.getTag();
                paramContext.set80Beta(StringUtils.startsWith(tag,
                        MinorVersionServiceHelper.ServiceTag.TAG_ALISQL_BETA_DOCKER_IMAGE.getTagPrefix())
                );
            }

            Map<String, MycnfTemplate> mycnfTemp = parameterGroupTemplateGenerator.getSysBaseParamTempMap(paramContext);
            // 已开通 byok
            if (modifiedWithParameters &&
                    kmsService.checkCustinsOpenByok(custins, mysqlParaHelper.getUID()) &&
                    checkContainRestartParam(parameterMap, mycnfTemp) &&
                    !kmsService.isCustinsByokAndKeyEnableOrNoByok(custins, mysqlParaHelper.getUID())) {
                logger.error("byokPrecheckRestartParamfail, custins" + custins.getId());
                return createErrorResponse(ErrorCode.INVALID_KMS_KEY);
            }

            // xengine 形态拦截
            boolean isXengine = "xengine".equalsIgnoreCase(SysParamGroupHelper.getDBStorageEngine(paramGroupId));
            if (modifiedWithParameters && isXengine && parameterMap.containsKey("innodb_buffer_pool_size")) {
                return createErrorResponse(ErrorCode.INVALID_PARAM_DB_STORAGE_ENGINE);
            }

            // 创建刷参任务
            List<CustInstanceDO> readOnlyInstanceList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(),true);
            Integer minorVersion = mysqlParamMinorVerionHelper.getNumMinorVersionByCustinsId(custins.getId());
            paramContext.setMinorVersion(minorVersion);
            if (modifiedWithParameters) {
                // docker 形态刷参 ======================================================================================
                if (custins.isCustinsOnDocker()) {
                    taskId = modifyParameterForDockerIns(custins, parameterMap, mycnfTemp, accessId, paramContext);
                    taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                    modifyReadonlyInstance(readOnlyInstanceList,parameterMap,mycnfTemp,accessId,pId, paramContext);
                }

                // SPHINX实例（已下线，但存量需要兼容）=====================================================================
                else if (custins.isMysqlLogic()) {

                    String inputCharacterType = mysqlParaHelper.getParameterValue("CharacterType",
                            CustinsSupport.CHARACTER_TYPE_MYSQL_DB);
                    if (!inputCharacterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_DB) &&
                            !inputCharacterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_SPHINX)) {
                        return createErrorResponse(ErrorCode.INVALID_CHARACTER_TYPE);
                    }

                    if (!inputCharacterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_SPHINX)) {
                        //此处查询getMycnfTemplateMap忽略了is_visible，checkParameters会检查请求是否来源于瑶池，是瑶池，不允许修改的，都不显示
                        assert parameterMap != null;
                        ParamChecker.parameterCheck(parameterMap, accessId,
                                instanceService.getMycnfTemplateMap(custins.getDbType(), custins.getDbVersion(),
                                        CustinsSupport.CHARACTER_TYPE_NORMAL), paramContext);
                    }

                    CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                    custInstanceQuery.setParentId(custins.getId());
                    custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_PRIMARY);
                    custInstanceQuery.setCharacterType(inputCharacterType);
                    List<CustInstanceDO> characterCustinsList = custinsService.getCustIns(custInstanceQuery);

                    taskId = createMysqlLogicModifyParamTask(parameterMap, custins, characterCustinsList,
                            inputCharacterType);
                    taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                }

                // 其他形态刷参 =========================================================================================
                else {
                    if (!characterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_SPHINX)) {
                        ParamChecker.parameterCheck(parameterMap, accessId, mycnfTemp, paramContext);
                        logger.info("parameterMap:" + parameterMap);
                    }

                    if (custins.getCharacterType().equals("logic")) {
                        List<CustInstanceDO> custList = custinsService
                                .getCustInstanceUnitByParentIdAndCharacterType(custins.getId(), CustinsSupport.CHARACTER_TYPE_DB);
                        for (CustInstanceDO unit : custList) {
                            createModifyParamTask(parameterMap, mycnfTemp, unit, false);
                            //readonly db
                            Long group_id = unit.getId().longValue();
                            List<CustinsRelationDO> custinsRelationList = custinsRelationIDao.getCustinsRelationByGroup(group_id);
                            for (CustinsRelationDO relation : custinsRelationList) {
                                if (relation.getRelation().equals(CUSTINS_RELATION_SYNC_TO)
                                        && !relation.getRelCustinsId().equals(CUSTINS_RELATION_NO_REL_CUSTINS_ID)) {
                                    CustInstanceDO readInstance = custinsService.getCustInstanceByCustinsId(
                                            relation.getRelCustinsId().intValue());
                                    taskId = createModifyParamTask(parameterMap, mycnfTemp, readInstance, false);
                                    taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                                }
                            }
                        }
                    } else if (custins.getCharacterType().equals("normal")
                            || custins.getCharacterType().equals(CustinsSupport.CHARACTER_TYPE_GPDB_GROUP)
                            || custins.getCharacterType().equals(CustinsSupport.CHARACTER_TYPE_MYSQL_SPHINX)) {

                        taskId = createModifyParamTask(parameterMap, mycnfTemp, custins, false);
                        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                        modifyReadonlyInstance(readOnlyInstanceList,parameterMap,mycnfTemp,accessId,pId, paramContext);
                        logger.error("add flush params task for normal instance, taskId:" + taskId);
                    } else {
                        return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
                    }
                }
            } else {
                // 使用参数模板修改参数 ==================================================================================
                if (pId == null) {
                    return createErrorResponse(ErrorCode.PARAM_GORUPS_NOT_EXIST);
                }

                // SPHINX不支持参数模板刷参
                if (custins.isMysqlLogic()) {
                    return createErrorResponse(ErrorCode.PARAM_GROUPS_DBTYPE_ERROR);
                }

                // xengine拦截，xengine模板不允许刷回InnoDB模板
                // 用户参数模板校验
                if (!isSysParamGroup) {
                    parameterMap = ParamChecker.getParameterGroupDetail(pId, custinsParamGroupsService);
                    // 形态拦截
                    for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
                        if (entry.getKey().equals("innodb_buffer_pool_size") && isXengine) {
                            return createErrorResponse(ErrorCode.INVALID_PARAM_DB_STORAGE_ENGINE);
                        }
                    }

                    ParamChecker.parameterCheck(parameterMap, accessId, mycnfTemp, paramContext);
                }

                if (isXengine) {
                    mysqlParamGroupHelper.checkXengineToInnoDBConversion(custins.getId(), parameterGroupId);
                }

                // 如果备份模板超过10个，自动删除时间最早的模板
                Integer backUpCount = custinsParamGroupsService.getBackUpParamGroupsByInsName(custins.getInsName());
                if (backUpCount >= 10) {
                    custinsParamGroupsService.deleteBackUpParamGroupsByInsName(custins.getInsName());
                }
                // 系统参数模板数据从mycnf_template_extra表取数据，用户自定义模板从param_groups_detail表取数据
                if (isSysParamGroup) {
                    parameterMap = mysqlParamGroupHelper.getSysParameterGroupDetail(parameterGroupId);
                } else {
                    parameterMap = ParamChecker.getParameterGroupDetail(pId, custinsParamGroupsService);
                }
                taskId = createModifyParamGroupTask(custins, pId.toString());
                taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                modifyReadonlyInstance(readOnlyInstanceList,parameterMap,mycnfTemp,accessId,pId, paramContext);
            }

            /*
             * 如果用户修改sync_binlog，需要额外记录到custins_param表当中
             */
            mysqlParaHelper.checkAddUserSyncBinlogCustinsParam(parameterMap, custins.getId());

            Map<String, Object> data = new HashMap<>(3);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("TaskId", taskId);
            return data;

        } catch (RdsException re) {
            logger.error("TuneAcion.modcustins exception", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("TuneAcion.modcustins exception", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

    /**
     * 对只读实例参数同步修改
     */
    private void modifyReadonlyInstance(List<CustInstanceDO> readOnlyInstanceList, Map<String, Object> parameterMap,
                                        Map<String, MycnfTemplate> mycnfTemp, String accessId,Long pId, ParamContext context) throws Exception {

         if(readOnlyInstanceList!=null && readOnlyInstanceList.size()>0){
            for (String param : parameterMap.keySet()) {
                if(param.equals(MySQLParamConstants.LOWER_CASE_TABLE_NAMES) || param.equals(MySQLParamConstants.INNODB_LARGE_PREFIX)){
                    for (CustInstanceDO custInstanceDO : readOnlyInstanceList) {
                        if(custInstanceDO.isCustinsOnDocker() && pId==null){
                            Integer taskId = modifyParameterForDockerIns(custInstanceDO, parameterMap, mycnfTemp, accessId, context);
                            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                        }
                        if(custInstanceDO.getCharacterType().equals("normal") && pId==null){
                            Integer taskId = createModifyParamTask(parameterMap, mycnfTemp, custInstanceDO, false);
                            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
                        }
                        if(pId!=null){
                            Integer readOnlyTaskId = createModifyParamGroupTask(custInstanceDO, pId.toString());
                            taskService.updateTaskPenginePolicy(readOnlyTaskId, mysqlParaHelper.getPenginePolicyID());
                        }
                    }

                }
            }
        }
    }

    /**
     * 是否包含重启参数
     *
     */
    private boolean checkContainRestartParam(Map<String, Object> parameterMap, Map<String, MycnfTemplate> mycnfCheckTemp) {
        for (String key : parameterMap.keySet()) {
            try {
                if (mycnfCheckTemp.containsKey(key)) {

                    MycnfTemplate template = mycnfCheckTemp.get(key);
                    if (template.getIsDynamic().equals(0)) {
                        // 非动态参数,需要重启
                        return true;
                    }
                }
            } catch (Exception e) {
                logger.error("byokCheckRetartParam1custins" , e);
                return false;
            }
        }
        return false;
    }

    private Integer modifyParameterForDockerIns(
            CustInstanceDO custins,
            Map<String, Object> parameterMap,
            Map<String, MycnfTemplate> mycnfTemp,
            String accessId,
            ParamContext context)
            throws Exception {

        List<MycnfCustinstanceDO> externalParams = mycnfService.getExternalMycnfCustinstances(
                custins.getId(), custins.getDbType(), custins.getDbVersion());

        Map<String, Object> externalParamMap = new HashMap<>();
        for (MycnfCustinstanceDO cnf : externalParams) {
            externalParamMap.put(cnf.getName(), cnf.getParaValue());
        }

        Map<String, Object> modifyExternalParamMap = new HashMap<>();
        Map<String, Object> modifyParamMap = new HashMap<>();

        for (String paramName : parameterMap.keySet()) {
            if (externalParamMap.containsKey(paramName)) {
                // 外部自定义参数不校验
                modifyExternalParamMap.put(paramName, parameterMap.get(paramName));
            } else {
                modifyParamMap.put(paramName, parameterMap.get(paramName));
            }
        }

        // 校验管控定义实例参数有效性
        if (modifyParamMap.size() > 0) {
            ParamChecker.parameterCheck(parameterMap, accessId, mycnfTemp, context);
        }

        // 修改外部自定义参数必须重启
        boolean preferRestart = modifyExternalParamMap.size() > 0;
        modifyParamMap.putAll(modifyExternalParamMap);
        return createModifyParamTask(modifyParamMap, mycnfTemp, custins, preferRestart);
    }

    protected Integer createModifyParamGroupTask(CustInstanceDO logicCustins, String paramGroupId) {
        Integer taskId = instanceService.modifyMysqlLogicCustinsParamByParamGroupTask(mysqlParaHelper.getAction(),
                logicCustins, paramGroupId, mysqlParaHelper.getOperatorId());
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
        return taskId;
    }

    protected Integer createMysqlLogicModifyParamTask(Map<String, Object> parameterMap, CustInstanceDO logicCustins,
                                                      List<CustInstanceDO> characterCustInsList, String characterType) {
        Boolean isRestart = false;
        Date date = new Date();
        List<List<MycnfChangeLog>> changeLogsList = new ArrayList<>();
        String tempCharacterType = characterType;
        if (characterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_DB)) {
            tempCharacterType = CustinsSupport.CHARACTER_TYPE_NORMAL;
        }
        for (CustInstanceDO characterCustIns : characterCustInsList) {
            List<MycnfChangeLog> changeLogs = new ArrayList<>(parameterMap.size());
            for (String key : parameterMap.keySet()) {
                if (!isRestart
                        && instanceService.countMycnfTemplateByName(
                        characterCustIns.getDbType(),
                        characterCustIns.getDbVersion(),
                        key,
                        tempCharacterType) > 0) {
                    if (!checkSlaveLagExceeded(characterCustIns)) {
                        return -1;
                    }
                    isRestart = true;
                }

                MycnfChangeLog changelog = instanceService.getMycnfCustinsHistoryByName(characterCustIns.getId(), key);
                String oldValue = null;
                if (changelog == null) {
                    MycnfTemplate temp = instanceService.getMycnfTemplateByName(
                            characterCustIns.getDbType(),
                            characterCustIns.getDbVersion(),
                            key,
                            tempCharacterType);
                    oldValue = temp.getDefaultValue();
                } else {
                    oldValue = changelog.getNewValue();
                }
                changeLogs.add(new MycnfChangeLog(characterCustIns.getId(), key, oldValue, parameterMap.get(key), date));
            }
            changeLogsList.add(changeLogs);
        }
        Integer taskId = instanceService.modifyMysqlLogicCustinsParameterTask(
                mysqlParaHelper.getAction(),
                logicCustins,
                changeLogsList,
                isRestart,
                mysqlParaHelper.getOperatorId(),
                characterType);
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
        return taskId;
    }

    protected Integer createModifyParamTask(
            Map<String, Object> parameterMap, Map<String, MycnfTemplate> mycnfTemp, CustInstanceDO custins,
            boolean preferRestart) throws RdsException {

        boolean isRestart = preferRestart;
        Integer custinsId = custins.getId();
        Date date = new Date();
        List<MycnfChangeLog> changeLogs = new ArrayList<>(parameterMap.size());

        for (String key : parameterMap.keySet()) {
            MycnfTemplate temp = mycnfTemp.get(key);
            if (!isRestart && 0 == temp.getIsDynamic()) {
                isRestart = true;
            }

            if (isRestart && !checkSlaveLagExceeded(custins)) {
                logger.error("here return taskId -1, do nothing");
                return -1;
            }
            // 刷参数据写入change_log前，用trim() 去掉空格 (aone:38203755)
            String newValue = String.valueOf(parameterMap.get(key)).trim();

            MycnfChangeLog changelog = instanceService.getMycnfCustinsHistoryByName(custinsId, key);
            String oldValue = changelog != null ? changelog.getNewValue() : temp.getDefaultValue();
            changeLogs.add(new MycnfChangeLog(custinsId, key, oldValue, newValue, date));
        }
//        Boolean enterpriseFlag = false;
//        InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
//        if(useMycnfTemplateExtra(custins.getDbType(), custins.getDbVersion(), instanceLevelDO.getCategory())){
//            enterpriseFlag = true;
//        }
//        // mysql非企业版实例，bp_size刷参(且不包含bp_instances 刷参时)，将bp_instance 刷成默认值, 925270
//        if (parameterMap.containsKey("innodb_buffer_pool_size") &&
//                !parameterMap.containsKey("innodb_buffer_pool_instances") && !enterpriseFlag) {
//            MycnfTemplate temp = mycnfTemp.get("innodb_buffer_pool_instances");
//            MycnfChangeLog changelog = instanceService.getMycnfCustinsHistoryByName(custinsId, "innodb_buffer_pool_instances");
//            String oldValue = changelog != null ? changelog.getNewValue() : temp.getDefaultValue();
//            changeLogs.add(new MycnfChangeLog(custinsId, "innodb_buffer_pool_instances",
//                                                            oldValue, temp.getDefaultValue(), date));
//        }

        Integer taskId = modifyCustinsParameterTask(
                mysqlParaHelper.getAction(), custins, changeLogs, isRestart,
                mysqlParaHelper.getOperatorId());
        taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());
        return taskId;
    }

    private boolean checkSlaveLagExceeded(CustInstanceDO custins) {
        try {
            Map<String, Object> keyMap = kpiService.getKpiKeyInsByKeyname("slavestat");
            //DataSourceHolder.setDefaultDataSource();
            //kpiService.setNewDrdsDataSource(custins);
            Map<String, Object> valueMap = kpiService
                    .getLastKeyValueInsByCustinsId(custins.getId(), (Integer) keyMap.get("KeyId"),
                            InstanceSupport.INSTANCE_ROLE_SLAVE);
            //DataSourceHolder.setDefaultDataSource();
            if (valueMap != null
                    && System.currentTimeMillis() - ((Date) valueMap.get("Date")).getTime()
                    < 600000) {
                String valueFormat = (String) keyMap.get("ValueFormat");
                String[] valueArr = valueFormat.split("&");
                int lagIdx = 0;
                for (; lagIdx < valueArr.length; lagIdx++) {
                    if (valueArr[lagIdx].equals("slave-lag")) {
                        break;
                    }
                }
                String value = ((String) valueMap.get("Value")).split("&")[lagIdx];
                if (value.toLowerCase().equals("null")) {//获取的slave-lag为null时。
                    createErrorResponse(ErrorCode.SLAVE_LAG_EXCEEDED);
                    return false;
                }
                Integer slaveLag = Integer.valueOf(value);
                if (slaveLag > 3600) {
                    createErrorResponse(ErrorCode.SLAVE_LAG_EXCEEDED);
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            //DataSourceHolder.setDefaultDataSource();
        }
        return true;
    }


    private Integer modifyCustinsParameterTask(String action,
                                               CustInstanceDO custins,
                                               List<MycnfChangeLog> changeLogs,
                                               boolean isRestart,
                                               Integer operatorId) {
        for (MycnfChangeLog log : changeLogs) {
            log.setCreator(99999);
            log.setModifier(999999);
            // baseService.save
            // 老架构连接数开放后，用特定的creator和modifier来标识开放后的change_log记录
            if (StringUtils.equals("max_connections", log.getName()) || StringUtils.equals("max_user_connections", log.getName())) {
                log.setCreator(382438);
                log.setModifier(382438);
            }
            mycnfService.createMycnfChangeLog(log);
        }

        if (isRestart) {
            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CUSTINS_STATUS_UP,
                    CustinsState.STATE_RESTARTING.getComment());
        }

        TaskQueueDO taskQueue = null;
        taskQueue = new TaskQueueDO(action, operatorId, custins.getId(),
                TASK_TYPE_CUSTINS, TASK_FLUSH_PARAMS);

        String accessId = mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID);
        if ("RdsEye".equalsIgnoreCase(accessId)) {
            String flushPlaceString = mysqlParaHelper.getParameterValue(PARAM_FLUSH_PLACES);
            List<String> flushPlaceList = new ArrayList<>();
            if (!StringUtils.isEmpty(flushPlaceString)) {
                String[] places = flushPlaceString.split(",");
                for (String placeName : places) {
                    if (FLUSH_PLACE_LIST.contains(placeName)) {
                        flushPlaceList.add(placeName);
                    }
                }
            }

            Map<String, Object> taskParams = new HashMap<>();
            taskParams.put("access_id", accessId);
            if (flushPlaceList.size() > 0) {
                taskParams.put("flush_places", flushPlaceList);
            }
            taskQueue.setParameter(JSONObject.toJSONString(taskParams));
        }

        taskService.createTaskQueue(taskQueue);
        return taskQueue.getId();
    }
}
