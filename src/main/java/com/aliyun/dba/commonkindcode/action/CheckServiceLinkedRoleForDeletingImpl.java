package com.aliyun.dba.commonkindcode.action;

import com.aliyun.apsaradb.gdnmetaapi.ApiException;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstance;
import com.aliyun.apsaradb.gdnmetaapi.model.GdnInstanceListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.GdnMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamServiceImpl;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import io.kubernetes.client.util.common.Collections;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeCheckServiceLinkedRoleForDeletingImpl")
public class CheckServiceLinkedRoleForDeletingImpl implements IAction {


    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterListImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MycnfService mycnfService;
    @Resource
    private GdnMetaService gdnMetaService;
    @Autowired
    private UserService userService;
    @Autowired
    private CustinsParamServiceImpl custinsParamService;
    @Autowired
    private CustinsServiceImpl custinsService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        try {
            String roleArn = mysqlParaHelper.getParameterValue("RoleArn");
            String serviceName = mysqlParaHelper.getParameterValue("ServiceName");
            String SPIRegionId = mysqlParaHelper.getParameterValue("SPIRegionId");
            if (Validator.isNull(roleArn) || Validator.isNull(serviceName) || Validator.isNull(SPIRegionId)) {
                throw new RdsException(ErrorCode.MISSING_ALL_PARAMETERS);
            }
            Map<String, Object> data = new HashMap<>(2);
            Map<String, Object> usage = new HashMap<>(2);
            usage.put("Region", SPIRegionId);
            List<String> resources = new ArrayList<>();
            usage.put("Resources", resources);
            String uid;
            String bid;
            try {
                uid = roleArn.split(":")[3];
                UserDO userDO = userService.getUserDOByUid(uid);
                bid = userDO.getLoginId().split("_")[0];
            } catch (Exception ignored) {
                throw new RdsException(new Object[]{ResultCode.CODE_NOTFOUND, "RoleArnUidNotFound", "get uid from roleArn failed"});
            }
            data.put("Deletable", checkServiceLinkedRoleForDeleting(serviceName, resources, uid, bid));
            List<Map<String, Object>> usages = new ArrayList<>();
            usages.add(usage);
            data.put("RoleUsages", usages);
            return data;
        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }

    private Boolean checkServiceLinkedRoleForDeleting(String serviceName, List<String> resources, String uid, String bid) throws RdsException, ApiException {
        Asserts.notBlank(serviceName, "serviceName");
        if (serviceName.equalsIgnoreCase("gad.rds.aliyuncs.com")) {
            GdnInstanceListResult gdnInstanceListResult = gdnMetaService.getClient().listGdnInstances(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID), "mysql", "5.7", uid, bid, null, null, null);
            if (gdnInstanceListResult != null && !Collections.isEmptyCollection(gdnInstanceListResult.getItems())) {
                resources.addAll(gdnInstanceListResult.getItems().stream().map(GdnInstance::getInsName).collect(Collectors.toList()));
                return false;
            }
        } else if (serviceName.equalsIgnoreCase("rds-import.rds.aliyuncs.com")) {
            List<Integer> custinsIds = custinsParamService.getCustinsParamsByParamName("externalReplication").stream().filter(entry -> "ON".equalsIgnoreCase(entry.getValue())).map(CustinsParamDO::getCustinsId).collect(Collectors.toList());
            if (!custinsIds.isEmpty()) {
                for (Integer custinsId : custinsIds) {
                    resources.add(custinsService.getCustInstanceByCustinsId(custinsId).getInsName());
                }
                return false;
            }
        } else {
            throw new RdsException(new Object[]{ResultCode.CODE_NOTFOUND, "SLRServiceNotFound", "SLR service name not found"});
        }
        return true;
    }
}
