package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.adb_vip_manager_client.ApiException;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.model.UpdateTopoBody;
import com.aliyun.dba.adb_vip_manager_client.model.UpdateTopoResponse;
import com.aliyun.dba.base.lib.LinkService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.common.action.IAction;

import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeMigrateConnectionToOtherZoneImpl")
public class MigrateConnectionToOtherZoneImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(MigrateConnectionToOtherZoneImpl.class);

    @Autowired
    protected LinksApi linksApi;

    @Autowired
    protected LinkService linkService;

    @Autowired
    protected CustinsService custinsService;

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            custins = mysqlParaHelper.getAndCheckCustInstance();
            String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);
            String connAddr = mysqlParaHelper.getParameterValue(ParamConstants.CONNECTION_STRING);
            String targetZoneId = mysqlParaHelper.getParameterValue(ParamConstants.ZONE_ID);
            if(StringUtils.isBlank(connAddr) || StringUtils.isBlank(targetZoneId)) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            UpdateTopoBody updateBody = new UpdateTopoBody();
            updateBody.setConnAddr(connAddr);
            updateBody.setTargetZoneId(targetZoneId);
            UpdateTopoResponse rsp = linksApi.modifyTopology(custins.getInsName(), updateBody, requestId).getData();
            return linkService.extractDataRsp(rsp);
        } catch (ApiException ex) {
            String exBody = ex.getResponseBody();
            return linkService.extractRsp(exBody);
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch(Exception ex){
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
