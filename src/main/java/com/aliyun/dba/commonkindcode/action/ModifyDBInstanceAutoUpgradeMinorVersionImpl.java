package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.aop.service.AopService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.aop.service.AopService.AOP_MINOR_VERSION_FIX_TYPE_NORMAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_MYSQL;

/**
 * 设置实例自动升级/手动升级 先设置实例的AutoUpgradeMinorVersion属性 检查实例是否可升级，如果可以升级，则立即下发主动运维预约 预约升级时间在1周左右
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeModifyDBInstanceAutoUpgradeMinorVersionImpl")
public class ModifyDBInstanceAutoUpgradeMinorVersionImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceAutoUpgradeMinorVersionImpl.class);

    public static final String AUTO_UPGRADE_MINOR_VERSION = "AutoUpgradeMinorVersion";
    public static final ArrayList<String> AUTO_UPGRADE_MINOR_VERSION_OPTIONS = new ArrayList<>();

    static {
        AUTO_UPGRADE_MINOR_VERSION_OPTIONS.add("Auto");
        AUTO_UPGRADE_MINOR_VERSION_OPTIONS.add("Manual");
        AUTO_UPGRADE_MINOR_VERSION_OPTIONS.add("No");
    }

    @Autowired
    protected MysqlParameterHelper mysqlParameterHelper;

    @Autowired
    protected CustinsParamService custinsParamService;

    @Autowired
    protected MinorVersionService minorVersionService;

    @Autowired
    protected MinorVersionServiceHelper minorVersionServiceHelper;

    @Autowired
    protected AopService aopService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            custins = mysqlParameterHelper.getAndCheckCustInstance();

            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            String paramValue = mysqlParameterHelper.getParameterValue(AUTO_UPGRADE_MINOR_VERSION);
            if (paramValue != null) {
                if (!AUTO_UPGRADE_MINOR_VERSION_OPTIONS.contains(paramValue)) {
                    throw new RdsException(ErrorCode.INVALID_ITEM_VALUE);
                }
            } else {
                throw new RdsException(ErrorCode.MISSING_ALL_PARAMETERS);
            }
            CustinsParamDO custinsParamsValue = custinsParamService.getCustinsParam(custins.getId(),
                AUTO_UPGRADE_MINOR_VERSION);
            if (custinsParamsValue != null) {
                custinsParamService.setCustinsParam(custins.getId(), AUTO_UPGRADE_MINOR_VERSION, paramValue);
            } else {
                CustinsParamDO newCustinsParamDO = new CustinsParamDO();
                newCustinsParamDO.setCustinsId(custins.getId());
                newCustinsParamDO.setName(AUTO_UPGRADE_MINOR_VERSION);
                newCustinsParamDO.setValue(paramValue);
                custinsParamService.createCustinsParam(newCustinsParamDO);
            }

            //设置为自动升级的，需要检查预约AOP
            //当前物理机立即可以预约升级，其他形态等待统一后，再支持预约
            checkAndAssignAopTask(custins, paramValue);

            Map<String, Object> data = new HashMap<String, Object>();
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(AUTO_UPGRADE_MINOR_VERSION, paramValue);
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            return data;
        } catch (RdsException re) {
            logger.error("ModifyDBInstanceAutoUpgradeMinorVersion error", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            logger.error("ModifyDBInstanceAutoUpgradeMinorVersion error", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    //不要影响主流程
    private void checkAndAssignAopTask(CustInstanceDO custins, String destSetValue) {
        try {

            if(!"Auto".equalsIgnoreCase(destSetValue)){
                logger.info("custins "+custins.getInsName()+" set No or Manual,skip assign aop.");
                return;
            }

            //当前只先预约物理机，云盘无法拿到最新版本信息
            if (custins.getKindCode() != 0) {
                return;
            }
            //三节点的polarx_hatp实例，需要指定版本升级，此处不提供自动升级
            if (mysqlParameterHelper.isPolarxHatp(custins.getId())) {
                logger.warn("custins_id:" + custins.getId() + ", is_polarx_hatp, skip");
                return;
            }

            //版本缺失实例不预约
            CustinsParamDO minorVersion = custinsParamService.getCustinsParam(custins.getId(), "minor_version");
            if (minorVersion == null) {
                logger.warn("custins_id:" + custins.getId() + ", has no minor_version info, skip");
                return;
            }
            String nowReleaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(minorVersion.getValue());
            String category = minorVersionServiceHelper.getMinorVersionCategory(custins);
            String minorVersionTag = minorVersionServiceHelper.getAndCheckMinorVersionTag(custins);
            List<MinorVersionReleaseDO> minorVersionReleaseDOS = minorVersionService.queryMinorVersionListByCondition(
                custins.getDbType(),
                custins.getDbVersion(),
                custins.getKindCode(),
                category,
                minorVersionTag,
                nowReleaseDate);
            //当前实例已经是最新版本，不预约
            if (minorVersionReleaseDOS == null || minorVersionReleaseDOS.size() == 0) {
                logger.warn("custins_id:" + custins.getId() + ", now is already latest version, skip");
                return;
            }

            //其他场景，需要预约AOP升级
            //已有小版本升级任务，暂时不预约
            if(aopService.checkInsHasTodoUpgradeVersionAopTask(
                mysqlParameterHelper.getBID(),
                mysqlParameterHelper.getUID(),
                custins.getInsName())){
                logger.warn("custins:"+custins.getInsType()+" already has upgrade minor version task.");
                return;
            }
            else{
                //下发小版本升级，7～15天升级，会在控制台待办事件中看到，主动运维可取消
                List<String> insNameList = new ArrayList<>();
                insNameList.add(custins.getInsName());
                Integer taskId = aopService.assignUpgradeMinorVersionAopTask(insNameList, AOP_MINOR_VERSION_FIX_TYPE_NORMAL, "139159", null,  DB_TYPE_MYSQL);
                logger.warn("assign aop task id:"+taskId+", for custins:"+custins.getInsName());
                return;
            }

        } catch (Exception ex) {
            logger.error("check and assign aop task error", ex);
        }
    }
}
