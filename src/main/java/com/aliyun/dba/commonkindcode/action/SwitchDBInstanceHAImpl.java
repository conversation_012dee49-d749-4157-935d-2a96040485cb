package com.aliyun.dba.commonkindcode.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.AuroraListDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeSwitchDBInstanceHAImpl")
public class SwitchDBInstanceHAImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(SwitchDBInstanceHAImpl.class);

    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private InstanceService instanceService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private TaskService taskService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            CustInstanceDO custins = mysqlParameterHelper.getAndCheckCustInstance();
            if (!custins.isActive()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            // custins do HA switch must has two or more instance(exclude kepler rc).
            if (custins.isCustinsOnEcs()) {
                if (instanceList.size() < 2) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
                }
            }
            InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if (!"general".equalsIgnoreCase(instanceLevelDO.getCategory())) {
                if (custins.isShare()
                        || (instanceList.size() < 2 && !custins.isKeplerRc())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }
            }

            Integer switchType = CustinsValidator.getRealNumber(
                    mysqlParameterHelper.getParameterValue(ParamConstants.SWITCH_TYPE),
                    CustinsSupport.SWITCH_TYPE_NORMAL);
            if (!CustinsSupport.SWITCH_TYPE_SET.contains(switchType)) {
                throw new RdsException(ErrorCode.INVALID_SWITCH_TYPE);
            }
            // when instance is expire locked, aurora will be disabled, mongodb will auto enable aurora in switch role task
            AuroraListDO auroraList = custinsService.getAuroraListByCustinsId(custins.getId());
            if (!auroraList.isEnable()) {
                if (!custins.isMongoDB() || (!(custins.getLockMode().equals(CustinsSupport.CUSTINS_LOCK_YES_AUTO) ||
                        custins.getLockMode().equals(CustinsSupport.CUSTINS_LOCK_YES)))) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_HA_STATUS);
                }
            }

            Date utcSwitchTime = paramSupport.parseCheckSwitchTimeTimeZoneSafe(params);
            String switchMode = CustinsSupport.getAndCheckSwitchTimeMode(params, utcSwitchTime, false);

            String haCause = mysqlParameterHelper.getParameterValue("HaCause");

            InstanceDO targetInstance = null;
            if (mysqlParameterHelper.hasParameter(ParamConstants.TARGET_INSTANCE_ID)) {
                Integer targetInstanceId = CustinsValidator.getRealNumber(
                        mysqlParameterHelper.getParameterValue(ParamConstants.TARGET_INSTANCE_ID));
                if (!"general".equalsIgnoreCase(instanceLevelDO.getCategory())) {
                    for (InstanceDO instance : instanceList) {
                        if (instance.getId().equals(targetInstanceId)) {
                            targetInstance = instance;
                            break;
                        }
                    }
                    // 指定要切换到的主机实例必须为备库
                    if (targetInstance == null || !targetInstance.isSlave()) {
                        throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
                    }
                } else {
                    targetInstance = instanceService.getInstanceByInsId(targetInstanceId);
                    if (targetInstance == null) {
                        throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
                    }
                    CustInstanceDO targetCustins = custinsService.getCustInstanceByCustinsId(targetInstance.getCustinsId());
                    if (!targetCustins.getParentId().equals(custins.getParentId())) {
                        throw new RdsException(ErrorCode.INVALID_INSTANCE_ID);
                    }

                    if (!targetCustins.isActive()) {
                        throw new RdsException(ErrorCode.INVALID_STATUS);
                    }
                }
            }
            logger.info("custins is [{}], targetInstance is [{}], switchMode is [{}]", custins.getInsName(),
                    targetInstance == null ? null : targetInstance.getId(), switchMode);
            Integer taskId = this.createSwitchHATask(mysqlParameterHelper.getAction(), custins,
                    targetInstance == null ? null : targetInstance.getIp(),
                    targetInstance == null ? null : targetInstance.getPort(),
                    switchType, mysqlParameterHelper.getOperatorId(), switchMode, utcSwitchTime, haCause);
            taskService.updateTaskPenginePolicy(taskId, mysqlParameterHelper.getPenginePolicyID());

            Map<String, Object> data = new HashMap<String, Object>();
            data.put(ParamConstants.DB_INSTANCE_ID, custins.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, custins.getInsName());
            data.put(ParamConstants.TARGET_INSTANCE_ID,
                    targetInstance == null ? -1 : targetInstance.getId());
            data.put(ParamConstants.SWITCH_TYPE, switchType);
            data.put(ParamConstants.TASK_ID, taskId);
            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception e) {
            logger.error("switchDBInstanceHA failed", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }


    private Integer createSwitchHATask(String action, CustInstanceDO custins, String targetIp, Integer targetPort,
                                       Integer switchType, Integer operatorId, String switchMode, Date switchTime,
                                       String haCause) throws RdsException {
        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(),
                CustinsSupport.CUSTINS_STATUS_SWITCH, CustinsState.STATE_HA_SWITCHING.getComment());
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, custins.getId(),
                TaskSupport.TASK_HA_SWITCH);
        Map<String, Object> parameterMap = new HashMap<>();
        Map<String, Object> haMap = new HashMap<>();
        // may be null. If so, do HA choice by Aurora
        haMap.put("target_ip", targetIp);
        haMap.put("target_port", targetPort);
        // switchType=0 对应 switchFlag=4；switchType=1 对应 switchFlag=5
        Integer switchFlag = CustinsSupport.SWITCH_TYPE_NORMAL.equals(switchType) ? 4 : 5;
        haMap.put("switch_flag", switchFlag);
        parameterMap.put("ha_info", haMap);
        parameterMap.put("switch_cause", parseSwitchCause(haCause));
        Map<String, Object> switchInfoMap = custinsService.getEffectiveTimeMapTimeZoneSafe(switchMode, switchTime);
        if (!switchInfoMap.isEmpty()){
            parameterMap.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
        }

        taskQueue.setParameter(JSON.toJSONString(parameterMap));
        taskService.createTaskQueue(taskQueue);
        return taskQueue.getId();
    }


    /**
     * String haCause = "{\"switch_cause\": \"xxx\"}";
     */
    private String parseSwitchCause(String haCause) {
        if (StringUtils.isBlank(haCause)) {
            return "";
        }
        try {
            Map<String, String> map = JSON.parseObject(haCause, Map.class);
            return map.get("switch_cause");
        } catch (Exception e) {
            return "";
        }
    }
}
