package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamGroupsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeDBInstanceParameterGroupImpl")
public class DescribeDBInstanceParameterGroupImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterGroupImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected CustinsParamGroupsService custinsParamGroupsService;
    @Autowired
    protected CustinsParamService custinsParamService;

    /**
     * 根据传入实例ID返回实例使用的参数模板，
     * 参数模板信息存放在 custins_param 表中，key 为 param_group_id。
     * 参数模板的录入有以下几种情况：
     * 1. 通过系统参数模板创建的实例
     * 2. 通过用户自定义模板创建的实例
     * 3. 刷过系统参数模板的实例
     *
     * @param custins      实例信息
     * @param actionParams 参数
     * @return
     * @throws RdsException
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            custins = mysqlParaHelper.getAndCheckCustInstance();
            if (custins == null) {
                throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
            }

            CustinsParamDO paramGroupIdDO = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);

            String paramGroupId = "";
            Map paramGroupIdDetail = null;
            Map paramGroupInfo = null;
            if (paramGroupIdDO != null) {
                paramGroupId = paramGroupIdDO.getValue();
                paramGroupIdDetail = SysParamGroupHelper.describeSysParamGroupId(paramGroupId);
                List<Map> paramGroupList = custinsParamGroupsService.getParamGroupsByParamGroupId(paramGroupId);
                if (!paramGroupList.isEmpty()) {
                    paramGroupInfo = paramGroupList.get(0);
                }
            }

            Map<String, Object> data = new HashMap<>(7);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("ParameterGroupID", paramGroupId);
            data.put("ParameterGroupIDDetail", paramGroupIdDetail);
            data.put("ParameterGroupInfo", paramGroupInfo);

            return data;
        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
