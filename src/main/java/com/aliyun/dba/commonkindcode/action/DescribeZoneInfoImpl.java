package com.aliyun.dba.commonkindcode.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.dataobject.ApplyResourceDO;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ApplyResourceService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.onecs.dataobject.ClusterDO;
import com.aliyun.dba.poddefault.action.support.PodAvzSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.dataobject.ApplyResourceDO.APPLY_RESOURCE_PROGRESS_MAP;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;

/**
 * 查询用户资源报备请求
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeZoneInfoImpl")
public class DescribeZoneInfoImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeZoneInfoImpl.class);
    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    protected PodAvzSupport podAvzSupport;
    @Resource
    private DBaasMetaService dBaasMetaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        try{
            String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
            String zoneId = mysqlParameterHelper.getParameterValue(ParamConstants.ZONE_ID);
            String insName = mysqlParameterHelper.getParameterValue(ParamConstants.DB_INSTANCE_NAME);

            boolean isCloudBox = false;
            String clusterName = null;
            if (StringUtils.isNotEmpty(insName)) {
                ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, insName, true);
                if (replicaSet != null) {
                    clusterName = replicaSet.getResourceGroupName();
                    logger.info("{} get cluster name {} from replicaset {}",
                            requestId, clusterName, insName);
                }
            }
            if (StringUtils.isEmpty(clusterName)) {
                if (Objects.isNull(zoneId)) {
                    throw new RdsException(ErrorCode.INVALID_AVZONE);
                }
                String subDomain = podAvzSupport.getLocationByAz(zoneId);
                logger.info("{} zoneId {} transfer subdomain {}",
                        requestId, zoneId, subDomain);
                ClustersDO clustersDO = podAvzSupport.getClusterBySubDomain(subDomain);
                if (clustersDO == null) {
                    throw new RdsException(ErrorCode.CLUSTER_NOT_FOUND);
                }
                clusterName = clustersDO.getClusterName();
                logger.info("{} get cluster name {} from subdomain {}",
                        requestId, clusterName, subDomain);
            }

            isCloudBox = podAvzSupport.isCloudBoxAz(clusterName);

            Map<String, Object> result =new HashMap<>();
            result.put("RequestId", requestId);
            result.put("ZoneId", zoneId);
            result.put("ClusterName", clusterName);
            result.put("CloudBox", isCloudBox);
            return result;
        } catch (RdsException ex) {
            logger.error(ex);
            throw ex;
        } catch (Exception ex){
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
