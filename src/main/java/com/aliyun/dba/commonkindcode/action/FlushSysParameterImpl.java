package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeFlushSysParameterImpl")
public class FlushSysParameterImpl implements IAction {


    private static final LogAgent logger = LogFactory.getLogAgent(FlushSysParameterImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected TaskService taskService;

    /**
     * 已处理
     * */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);


            String insName = mysqlParaHelper.getAndCheckDBInstanceName();
            String toolImage = mysqlParaHelper.getFlushSysImage();
            custins = mysqlParaHelper.getAndCheckCustInstance();
            if (custins == null) {
                if (custinsService.checkDeletedCustInstanceByInsName(insName)) {
                    return createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);
                }
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }
            if (!custins.isCustinsDockerOnEcs()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            if (custins.isReadAndWriteLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            if (!custins.isLogic()) {
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_LOGIC);
            }

            Map<String, Object> taskParamMap = new HashMap<>(1);
            if (toolImage != null){
                taskParamMap.put("toolImage", toolImage);
            }
            Integer taskId = taskService.flushCustInstanceSysParameterTask(mysqlParaHelper.getAction(), custins, mysqlParaHelper.getOperatorId(),  taskParamMap);

            taskService.updateTaskPenginePolicy(taskId, mysqlParaHelper.getPenginePolicyID());

            Map<String, Object> data = new HashMap<>(4);
            data.put("TaskId", taskId);

            return data;

        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
