package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.dataapi.service.RDSDataAPIService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeRetrieveSecretValueImpl")
public class RetrieveSecretValueImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(RetrieveSecretValueImpl.class);

    @Autowired
    private RDSDataAPIService rdsDataAPIService;

    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
        try {
            String regionId = mysqlParameterHelper.getParameterValue("regionId");
            String dbInstanceId = mysqlParameterHelper.getParameterValue("dbInstanceName");
            Long uid = Long.valueOf(mysqlParameterHelper.getUID());
            String bid = mysqlParameterHelper.getBID();
            String secretArn = mysqlParameterHelper.getParameterValue("secretArn");
            String secretName = mysqlParameterHelper.getParameterValue("secretName");
            String username = mysqlParameterHelper.getParameterValue("username");

            Map<String, Object> result;

            if (secretArn != null) {
                result = rdsDataAPIService.retrieveSecretValueByARN(regionId, uid, bid, secretArn);
            } else if (secretName != null) {
                if (null == dbInstanceId) {
                    logger.error("SecretName needs to be used with dbInstanceId");
                    return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
                }
                result = rdsDataAPIService.retrieveSecretValueByName(regionId, dbInstanceId, uid, bid, secretName);
            } else {
                logger.error("secretArn, secretName are all null");
                return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
            }

            String requestId = mysqlParameterHelper.getParameterValue("requestId");
            if (requestId == null) {
                requestId = UUID.randomUUID().toString().toUpperCase(Locale.ROOT);
            }

            result.put("RequestId", requestId);
            return result;
        } catch (Exception ex) {
            logger.error("retrieve secret value action failed: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
