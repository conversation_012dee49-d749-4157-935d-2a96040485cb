package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.dataobject.ApplyResourceDO;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ApplyResourceService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.base.dataobject.ApplyResourceDO.APPLY_RESOURCE_PROGRESS_SUBMITTED;
import static com.aliyun.dba.base.dataobject.ApplyResourceDO.APPLY_RESOURCE_PROGRESS_SUBMITTED_DESC;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;

/**
 * 用户资源报备
 * 用户账号可能不存在，需要检查创建账号
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeCustomerApplyResourceImpl")
public class CustomerApplyResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CustomerApplyResourceImpl.class);

    private static final String CATEGORY_BASIC = "basic";
    private static final String CATEGORY_HA = "highavailability";
    private static final String CATEGORY_FINANCE = "finance";

    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private MysqlParamSupport mysqlParamSupport;

    @Autowired
    private ApplyResourceService applyResourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        try{
            //如果账号不存在，则创建
            Integer userId = mysqlParameterHelper.getAndCreateUserId();
            //前端检查校验RegionId
            String regionId = mysqlParameterHelper.getParameterValue("RegionId");
            //前端检查校验ZoneId
            String zoneId = mysqlParameterHelper.getParameterValue("ZoneId");
            String dbType = mysqlParameterHelper.getParameterValue("Engine");
            if(!CustinsSupport.DB_ENGINE_MYSQL.equalsIgnoreCase(dbType) && !DB_ENGINE_MARIADB.equalsIgnoreCase(dbType)){
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }
            String dbVersion = mysqlParameterHelper.getParameterValue("EngineVersion");
            List<String> mysqlValidEngineVersionList = new ArrayList<String>(){
                {
                    this.add(DB_VERSION_MYSQL_80);
                    this.add(DB_VERSION_MYSQL_57);
                    this.add(DB_VERSION_MYSQL_56);
                    this.add(DB_VERSION_MYSQL_55);
                    this.add("10.3");
                }
            };
            if(!mysqlValidEngineVersionList.contains(dbVersion)){
                throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
            }
            List<String> categoryList = new ArrayList<String>(){
                {
                    this.add(CATEGORY_BASIC);
                    this.add(CATEGORY_HA);
                    this.add(CATEGORY_FINANCE);
                }
            };
            String category = mysqlParameterHelper.getParameterValue("Category");
            if(category == null || !categoryList.contains(category.toLowerCase(Locale.ROOT))){
                throw new RdsException(MysqlErrorCode.INVALID_CATEGORY.toArray());
            }
            List<String> validStorageTypeList = new ArrayList<String>(){
                {
                    this.add(STORAGE_TYPE_LOCAL_SSD);
                    this.add(STORAGE_TYPE_CLOUD_SSD);
                    this.add(STORAGE_TYPE_CLOUD_ESSD);
                    this.add(STORAGE_TYPE_CLOUD_ESSD_PL2);
                    this.add(STORAGE_TYPE_CLOUD_ESSD_PL3);
                }
            };
            String storageType = mysqlParameterHelper.getParameterValue("DBInstanceStorageType");
            if(storageType == null || !validStorageTypeList.contains(storageType)){
                throw new RdsException(MysqlErrorCode.INVALID_DB_INSTANCE_STORAGE_TYPE.toArray());
            }

            //5的整数倍，云盘最小为20
            Integer storage = null;
            String storageStr = mysqlParameterHelper.getParameterValue("Storage");
            try{
                if(storageStr != null){
                    storage = Integer.valueOf(storageStr);
                    //不能小于5，云盘不能小于20，必须为5的整数倍
                    if(storage < 5 || (!STORAGE_TYPE_LOCAL_SSD.equalsIgnoreCase(storageType) && storage < 20) || storage % 5 !=0){
                        logger.error("存储空间不能小于5GB，云盘不能小于20GB，且必须为5的整数倍");
                        throw new RdsException(ErrorCode.INVALID_STORAGE);
                    }
                }
            }
            catch(Exception ex){
                logger.error("parse storage fail", ex);
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }

            String instanceNetworkType = mysqlParameterHelper.getParameterValue("InstanceNetworkType");
            if(!"VPC".equalsIgnoreCase(instanceNetworkType) && !"Classic".equalsIgnoreCase(instanceNetworkType)){
                throw new RdsException(ErrorCode.INVALID_NET_TYPE);
            }
            String classCode = mysqlParameterHelper.getParameterValue("DBInstanceClass");
            if(classCode == null){
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            //检查规格码是否存在
            InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion, null, null);
            if(instanceLevelDO == null){
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            //规格与盘类型是否匹配
            //本地盘规格指定了云盘类型，云盘规格指定了本地盘规格
            if(instanceLevelDO.getHostType() ==0 && !STORAGE_TYPE_LOCAL_SSD.equalsIgnoreCase(storageType)
                || instanceLevelDO.getHostType() == 2 && STORAGE_TYPE_LOCAL_SSD.equalsIgnoreCase(storageType)){
                throw new RdsException(MysqlErrorCode.INVALID_DB_INSTANCE_CLASS_MISMATCH_STORAGE_TYPE.toArray());
            }

            //默认一个,最多10个
            Integer quantity = 1;
            Integer MAX_QUANTITY = 10;
            String RESOURCE_APPLY_MAX_QUANTITY_CONFIG = "RESOURCE_APPLY_MAX_QUANTITY_CONFIG";
            try{
                String quantityStr = mysqlParameterHelper.getParameterValue("Quantity");
                //可能数值过大，会出现溢出
                quantity = Integer.valueOf(quantityStr);
                List<String> configList = resourceService.getResourceRealValueList(RESOURCE_APPLY_MAX_QUANTITY_CONFIG);
                if(!configList.isEmpty()){
                    MAX_QUANTITY = Integer.valueOf(configList.get(0));
                }
                if(quantity <= 0 || quantity > MAX_QUANTITY){
                    throw new RdsException(MysqlErrorCode.INVALID_APPLY_RESOURCE_QUANTITY.toArray());
                }
            }
            catch(Exception ex){
                throw new RdsException(MysqlErrorCode.INVALID_APPLY_RESOURCE_QUANTITY.toArray());
            }

            String expectedTimeUtc = mysqlParameterHelper.getParameterValue("ExpectedTime");
            Date utcDate = mysqlParamSupport.parseCheckApplyResourceTimeZoneSafe(expectedTimeUtc);

            ApplyResourceDO applyResourceDO = new ApplyResourceDO(userId, regionId, zoneId, dbType, dbVersion,
                   category, storageType, storage, instanceNetworkType, classCode, quantity, utcDate, APPLY_RESOURCE_PROGRESS_SUBMITTED);

            Long id = applyResourceService.addApplyResource(applyResourceDO);

            String requestId = mysqlParameterHelper.getParameterValue("RequestId");
            if(requestId == null){
                requestId = UUID.randomUUID().toString().toUpperCase(Locale.ROOT);
            }

            Map<String, Object> result = new HashMap<String, Object>();
            result.put("RequestId", requestId);
            result.put("ApplyResourceId", id);
            result.put("ApplyResourceProgress", APPLY_RESOURCE_PROGRESS_SUBMITTED);
            return result;
        }
        catch(RdsException re){
            logger.error("CustomerApplyResourceImpl fail", re);
            return createErrorResponse(re.getErrorCode());
        }
        catch(Exception ex){
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
