package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.idao.MysqlCustinsParamIDao;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeCustinsConfigureImpl")
public class CustinsConfigureImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CustinsConfigureImpl.class);

    private final static String DISK_SIZE_TEMP = "disk_size_temp";// 设置临时磁盘空间对应的管控参数

    private final static String DISK_SPACE_SET = "DISK_SPACE_SET";// 设置临时磁盘空间
    private final static String DISK_SPACE_RESTORE = "DISK_SPACE_RESTORE";// 还原临时磁盘空间

    private final static List<String> typeList = Arrays.asList(
            new String[] {DISK_SPACE_SET, DISK_SPACE_RESTORE}
    );

    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;
    @Autowired
    protected MysqlCustinsParamIDao mysqlCustinsParamIDao;
    @Autowired
    protected CustinsParamService custinsParamService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> map) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(map);
        try {
            custins = mysqlParameterHelper.getAndCheckCustInstance();
            String type = mysqlParameterHelper.getParameterValue("type");
            String setValue = mysqlParameterHelper.getParameterValue("setValue");
            if (!typeList.contains(type)) {
                Object[] notSupportOpt = new Object[]{ResultCode.CODE_UNSUPPORTED, "InvalidType", "curr type is invalid."};
                return createErrorResponse(notSupportOpt);
            }
            if (DISK_SPACE_SET.equals(type)) { // 临时设置磁盘
                try {
                    Integer.parseInt(setValue);
                } catch (NumberFormatException e) {
                    Object[] notSupportOpt = new Object[]{ResultCode.CODE_UNSUPPORTED, "InvalidParam", "curr setValue is invalid."};
                    return createErrorResponse(notSupportOpt);
                }
                custinsParamService.setCustinsParam(custins.getId(), DISK_SIZE_TEMP, setValue);
            } else if (DISK_SPACE_RESTORE.equals(type)) { // 临时设置磁盘 还原(删除)
                custinsParamService.deleteCustinsParam(custins.getId(), DISK_SIZE_TEMP);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            return data;
        } catch (Exception ex) {
            logger.error("CustinsConfigure action failed: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
