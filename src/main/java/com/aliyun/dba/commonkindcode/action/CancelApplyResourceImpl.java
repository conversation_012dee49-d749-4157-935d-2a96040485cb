package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ApplyResourceService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static com.aliyun.dba.base.dataobject.ApplyResourceDO.APPLY_RESOURCE_PROGRESS_CANCELLED;
import static com.aliyun.dba.base.dataobject.ApplyResourceDO.APPLY_RESOURCE_PROGRESS_CANCELLED_DESC;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeCancelApplyResourceImpl")
public class CancelApplyResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CancelApplyResourceImpl.class);

    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;

    @Autowired
    private ApplyResourceService applyResourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        try{
            Integer userId = mysqlParameterHelper.getAndCheckUserId();
            Long applyResourceId = null;

            try{
                String applyResourceIdStr = mysqlParameterHelper.getParameterValue("ApplyResourceId");
                if(applyResourceIdStr == null){
                    throw new RdsException(MysqlErrorCode.INVALID_APPLY_RESOURCE_ID.toArray());
                }
                applyResourceId = Long.valueOf(applyResourceIdStr);
            }
            catch(Exception ex){
                logger.error("parse applyResourceId fail", ex);
                throw new RdsException(MysqlErrorCode.INVALID_APPLY_RESOURCE_ID.toArray());
            }

            applyResourceService.cancelApplyResource(userId, applyResourceId);

            String requestId = mysqlParameterHelper.getParameterValue("RequestId");
            if(requestId == null){
                requestId = UUID.randomUUID().toString().toUpperCase(Locale.ROOT);
            }

            Map<String, Object> result = new HashMap<String, Object>();
            result.put("RequestId", requestId);
            result.put("ApplyResourceId", applyResourceId);
            result.put("ApplyResourceProgress", APPLY_RESOURCE_PROGRESS_CANCELLED);
            return result;
        }
        catch(RdsException re){
            logger.error("CancelApplyResourceImpl fail", re);
            return createErrorResponse(re.getErrorCode());
        }
        catch(Exception ex){
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
        finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
