package com.aliyun.dba.commonkindcode.action;

import com.alicloud.apsaradb.resmanager.InventoryItem;
import com.alicloud.apsaradb.resmanager.response.AvailableLevelRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.lib.RmOperatorService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.resource.dataobject.RegionAVZonDO;
import com.aliyun.dba.resource.dataobject.RegionAVZoneQuery;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_ENGINE_PGSQL;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_PGSQL;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeInstanceLevelListImpl")
public class DescribeInstanceLevelListImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeInstanceLevelListImpl.class);

    @Resource
    private MysqlParamSupport mysqlParamSupport;

    @Resource
    private ResourceService resourceService;

    @Resource
    private EcsService ecsService;

    @Resource
    private RmOperatorService rmOperatorService;

    @Resource
    private IResApi resApi;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String dbType =mysqlParamSupport.getAndCheckDBType(params, "mysql");
            String dbVersion = mysqlParamSupport.getDBVersion(params, dbType);
            String subDomain = mysqlParamSupport.getAndCheckRegion(params);
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String zoneId = mysqlParamSupport.getAndCheckAvZone(params);
            String hostType = mysqlParamSupport.getParameterValue(params, ParamConstants.CUSTINS_HOST_TYPE);
            String category = mysqlParamSupport.getParameterValue(params, ParamConstants.CATEGORY);

            String ecsAccount = null;
            try {
                // 杭州金融云下传的regionId=cn-hangzhou，需要转换为cn-hangzhou-finance来查ecsAccount
                String regionIdUse = regionId;
                if ("cn-hangzhou".equals(regionId)) {
                    RegionAVZoneQuery query = new RegionAVZoneQuery();
                    query.setSubDomain(subDomain);
                    query.setAvz(zoneId);
                    List<RegionAVZonDO> regionAVZonDO = resourceService.getRegionAVZoneList(query);
                    if (!regionAVZonDO.isEmpty()) {
                        String regionIdTemp = regionAVZonDO.get(0).getRegion();
                        if ("cn-hangzhou-finance".equals(regionIdTemp)) {
                            regionIdUse = regionIdTemp;
                        }
                    }
                }
                ecsAccount = ecsService.getEcsAccount(0, regionIdUse);
            } catch (Exception e) {

            }
            Response<AvailableLevelRespModel> resp = resApi.getAvaliableLevels(regionId, subDomain, dbType, dbVersion, hostType, category, zoneId, ecsAccount, requestId, false);
            if (!resp.getCode().equals(200)) {
                throw new RdsException(ErrorCode.RESOURCE_NOT_FOUND,
                        ErrorCode.RESOURCE_NOT_FOUND.customizedErrorDesc(resp));
            }

            List<InventoryItem> levels = resp.getData().getInventoryItemList();
            List<Map<String, Object>> levelResult = new ArrayList<>();

            for (InventoryItem item : levels) {
                Map<String, Object> itemMap = new HashMap<String, Object>();
                itemMap.put(ParamConstants.INS_LEVEL_CLASS_CODE, item.getClassCode());
                itemMap.put(ParamConstants.ENGINE, item.getDbType());
                if (DB_TYPE_PGSQL.equalsIgnoreCase(item.getDbType())) {
                    itemMap.put(ParamConstants.ENGINE, DB_ENGINE_PGSQL);
                }
                itemMap.put(ParamConstants.ENGINE_VERSION, item.getDbVersion());
                itemMap.put(ParamConstants.CUSTINS_HOST_TYPE, item.getHostType());
                itemMap.put(ParamConstants.CATEGORY, item.getCategory());
                itemMap.put(ParamConstants.DB_INSTANCE_STORAGE_TYPE, item.getStorage());
                levelResult.add(itemMap);
            }
            return Collections.singletonMap(ParamConstants.INS_LEVEL_CLASS_CODE_LIST, levelResult);
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }

    }

}
