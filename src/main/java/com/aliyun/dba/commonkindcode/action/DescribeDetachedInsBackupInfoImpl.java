package com.aliyun.dba.commonkindcode.action;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.service.ClusterBackUpService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
/**
 * 通过dbs接口查询已删除实例的备份集信息，返回给瑶池
 * 可通过备份集id查询已删除实例的部分信息
 */
@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeDetachedInsBackupInfoImpl")
public class DescribeDetachedInsBackupInfoImpl implements IAction {
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected UserService userService;
    @Resource
    private ClusterBackUpService clusterBackUpService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        try {
            return clusterBackUpService.getDetachedInsBackupInfo(params);
        } catch (RdsException re) {
            log.error(re.getMessage(),re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
