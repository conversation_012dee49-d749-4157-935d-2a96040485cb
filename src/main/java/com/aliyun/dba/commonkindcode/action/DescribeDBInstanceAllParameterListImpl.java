package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeDBInstanceAllParameterListImpl")
public class DescribeDBInstanceAllParameterListImpl implements IAction {


    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterListImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MycnfService mycnfService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            custins = mysqlParaHelper.getAndCheckCustInstance();
            //query mycnf_custinstance, dont need mycnf_template_extra
            List<MycnfCustinstanceDO> mycnfCustinstanceList = mycnfService.getMycnfCustinstanceList(custins.getId());
            Map<String, Object> data = new HashMap<>();
            for (MycnfCustinstanceDO mycnf: mycnfCustinstanceList) {
                data.put(mycnf.getName(), mycnf.getParaValue());
            }
            return data;
        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
