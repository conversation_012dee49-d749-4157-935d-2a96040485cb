package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.dataobject.ApplyResourceDO;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ApplyResourceService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.base.dataobject.ApplyResourceDO.APPLY_RESOURCE_PROGRESS_MAP;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;

/**
 * 查询用户资源报备请求
 * */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeApplyResourceImpl")
public class DescribeApplyResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeApplyResourceImpl.class);

    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;

    @Autowired
    private ApplyResourceService applyResourceService;

    @Autowired
    private DTZSupport dtzSupport;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {

        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        try{
            //如果账号不存在，则创建
            Integer userId = mysqlParameterHelper.getAndCheckUserId();
            String regionId = mysqlParameterHelper.getParameterValue("RegionId");
            String zoneId = mysqlParameterHelper.getParameterValue("ZoneId");
            String engine = mysqlParameterHelper.getParameterValue("Engine");
            //engine非必须
            if(engine != null && !CustinsSupport.DB_ENGINE_MYSQL.equalsIgnoreCase(engine)){
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }
            String progress = mysqlParameterHelper.getParameterValue("Progress");
            //progress非必须
            if(progress != null && !APPLY_RESOURCE_PROGRESS_MAP.keySet().contains(progress)){
                throw new RdsException(MysqlErrorCode.INVALID_APPLY_RESOURCE_QUERY_CONDITION.toArray());
            }
            Integer pageNumber = 1;
            try{
                String pageNumberStr = mysqlParameterHelper.getParameterValue("PageNumber");
                pageNumber = Integer.valueOf(pageNumberStr);
                if(pageNumber < 1){
                    throw new RdsException(MysqlErrorCode.INVALID_APPLY_RESOURCE_QUERY_CONDITION.toArray());
                }
            }
            catch(Exception ex){
                logger.error("parse pageNumber fail", ex);
                throw new RdsException(MysqlErrorCode.INVALID_APPLY_RESOURCE_QUERY_CONDITION.toArray());
            }

            Integer pageSize = 20;
            try{
                String pageSizeStr = mysqlParameterHelper.getParameterValue("PageSize");
                pageSize = Integer.valueOf(pageSizeStr);
            }
            catch(Exception ex){
                //默认20
            }

            Map<String, Object> queryCondition = new HashMap<>();
            queryCondition.put("userId", userId);
            queryCondition.put("regionId", regionId);
            queryCondition.put("zoneId", zoneId);
            queryCondition.put("engine", engine);
            queryCondition.put("progress", progress);
            queryCondition.put("offset", (pageNumber - 1) * pageSize);
            queryCondition.put("limitCount", pageSize);

            //总数
            Integer totalCount = applyResourceService.queryApplyResourceRecordCount(queryCondition);

            //时间转为UTC时间
            Integer timeZoneDiff = dtzSupport.getMetaDBTimeZoneDiffSeconds(DATA_SOURCE_DBAAS);

            List<ApplyResourceDO> records = applyResourceService.queryApplyResourceRecords(queryCondition);
            records.stream().forEach(r -> r.dealWithShowAttr(timeZoneDiff));

            String requestId = UUID.randomUUID().toString().toUpperCase(Locale.ROOT);
            if(mysqlParameterHelper.getParameterValue("RequestId") != null){
                requestId = mysqlParameterHelper.getParameterValue("RequestId");
            }

            Map<String, Object> result =new HashMap<>();
            result.put("RequestId", requestId);
            result.put("TotalCount", totalCount);
            result.put("Data", records);

            return result;
        }
        catch(RdsException re){
            logger.error("DescribeApplyResourceImpl fail", re);
            return createErrorResponse(re.getErrorCode());
        }
        catch (Exception ex){
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
        finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
