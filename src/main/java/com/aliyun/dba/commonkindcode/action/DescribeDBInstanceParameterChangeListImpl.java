package com.aliyun.dba.commonkindcode.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.instance.entity.MycnfChangeLog;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeDBInstanceParameterChangeListImpl")
public class DescribeDBInstanceParameterChangeListImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterChangeListImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MycnfService mycnfService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected InstanceService instanceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            Date startTime = mysqlParaHelper.getAndCheckStartTime(DateUTCFormat.MINUTE_UTC_FORMAT);
            Date endTime = mysqlParaHelper.getAndCheckEndTime(DateUTCFormat.MINUTE_UTC_FORMAT);
            if (startTime.getTime() > endTime.getTime()) {
                return createErrorResponse(ErrorCode.INVALID_PARAMETER_COMBINATION);
            }
            custins = mysqlParaHelper.getCustInstance();
            if (custins == null) {
                if (custinsService.checkDeletedCustInstanceByInsName(mysqlParaHelper.getDBInstanceName())) {
                    return createErrorResponse(ErrorCode.DBINSTANCE_IS_DELETED);
                }
                return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
            }
            String paramName = mysqlParaHelper.getParameterName();

            if (StringUtils.isNotEmpty(paramName)) {
                return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
            }

            Map<String, Object> data = new HashMap<>(7);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("Engine", custins.getDbType());
            data.put("EngineVersion", custins.getDbVersion());
            data.put("StartTime", mysqlParaHelper.getStartTime());
            data.put("EndTime", mysqlParaHelper.getEndTime());

            //查询mycnf_change_log表
            List<MycnfChangeLog> parameters = instanceService.getMycnfCustinsHistoryList(custins.getId(), startTime, endTime);
            //保持返回格式不变
            data.put("Parameters", ParamTransHelper.getMapListFromMycnfChangeLogList(parameters));
            return data;

        } catch (RdsException re) {
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally{
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
