package com.aliyun.dba.commonkindcode.action;


import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dataapi.service.RDSDataAPIService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.dao.DuplicateKeyException;

import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeCreateSecretImpl")
public class CreateSecretImpl implements IAction {


    private static final LogAgent logger = LogFactory.getLogAgent(CreateSecretImpl.class);

    @Autowired
    private RDSDataAPIService rdsDataAPIService;

    @Autowired
    private MysqlParameterHelper mysqlParameterHelper;

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;

    @Autowired
    protected CustinsIDao custinsIDao;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> map) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(map);

        CustInstanceDO custins = null;
        if (mysqlParamSupport.getParameterValue(map, "dbinstanceid") == null) {
            custins = custinsIDao.getCustInstanceByInsName(mysqlParamSupport.getAndCheckUserId(map),
                    mysqlParamSupport.getAndCheckDBInstanceName(map));
        } else {
            custins = mysqlParamSupport.getAndCheckCustInstanceById(map, "dbinstanceid");
        }
        if (custins == null || custins.getStatus().equals(CustinsSupport.CUSTINS_STATUS_DESTROYED)) {
            //实例不存在，或者不是实例拥有者
            return createErrorResponse(ErrorCode.DBINSTANCE_NOT_FOUND);
        }

        try {
            logger.info("do request:" + mysqlParameterHelper.getParameterValue("requestId") );
            String regionId = mysqlParameterHelper.getParameterValue("regionId");
            Long uid = Long.valueOf(mysqlParameterHelper.getUID());
            String bid = mysqlParameterHelper.getBID();
            String username = mysqlParameterHelper.getParameterValue("username");
            String password = mysqlParameterHelper.getParameterValue("password");
            String dbInstanceName = mysqlParameterHelper.getParameterValue("dbInstanceName");
            String resourceGroupId = mysqlParameterHelper.getParameterValue("resourceGroupId");
            String description = mysqlParameterHelper.getParameterValue("description");
            String secretName = mysqlParameterHelper.getParameterValue("secretName");
            String dbNames = mysqlParameterHelper.getParameterValue("dbNames");
            String[] dbNameArr = {};
            if (dbNames != null) {
                dbNameArr = dbNames.split(",");
            }

            logger.info("do request with get result:" + mysqlParameterHelper.getParameterValue("requestId") );
            Map<String, Object> result = rdsDataAPIService.createSecret(regionId, uid, bid, username, password, dbInstanceName, resourceGroupId, description, secretName, dbNameArr);

            String requestId = mysqlParameterHelper.getParameterValue("requestId");
            if (requestId == null) {
                requestId = UUID.randomUUID().toString().toUpperCase(Locale.ROOT);
            }

            result.put("RequestId", requestId);
            return result;
        } catch (Exception ex) {
            logger.error("create secret action failed: " + ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
