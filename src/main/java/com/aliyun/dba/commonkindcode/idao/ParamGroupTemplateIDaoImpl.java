package com.aliyun.dba.commonkindcode.idao;

import com.aliyun.dba.instance.entity.MycnfTemplateExtra;
import com.aliyun.dba.support.datasource.sqltemplate.DynamicSqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Repository("paramGroupTemplateIDao")
public class ParamGroupTemplateIDaoImpl implements ParamGroupTemplateIDao {
    @Resource(name = "dbaasSqlSessionTemplate")
    private DynamicSqlSessionTemplate sqlSessionTemplate;

    @Override
    public List<MycnfTemplateExtra> getParameterExtraWithParamGroupId(String paramGroupId) {
        Map<String, Object> params = new TreeMap<>();
        params.put("paramGroupId", paramGroupId);

        return this.sqlSessionTemplate.selectList("getParameterExtraWithParamGroupId", params);
    }
}
