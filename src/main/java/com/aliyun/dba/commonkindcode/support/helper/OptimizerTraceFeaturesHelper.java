package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;

/*
 *   针对用户要修改的特定参数 optimizer_trace_feature  或者  loose_optimizer_trace_feature
 *   做严格的参数校验，其中的键值对的key需要在"greedy_search", "range_optimizer", "dynamic_range", "repeated_subselect"里面
 *   其中的键值对的value需要为on或者off
 */
public class OptimizerTraceFeaturesHelper implements BaseHelper{

    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException {
        String paramValue = parameters.get(key).toString();
        if (StringUtils.isEmpty(paramValue) || paramValue.endsWith(",")) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "optimizer_tracer_feature is invalid");
        }

        // 避免传参里面带入空格
        else if (paramValue.split(" ").length > 1) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "optimizer_tracer_feature is invalid");
        }

        List<String> supportedOptimizerTraceFeaturePrefix = asList("greedy_search", "range_optimizer", "dynamic_range", "repeated_subselect");

        String[] optArr = paramValue.split(",");
        Map<String, String> optMap = new HashMap<>();
        boolean overrideOptimizerSwitch = false;
        for (String optStr : optArr) {
            String[] unitArr = optStr.split("=");

            if (unitArr.length != 2 || StringUtils.isBlank(unitArr[0]) ||
                    (!"on".equalsIgnoreCase(unitArr[1]) && !"off".equalsIgnoreCase(unitArr[1]))  ||  !supportedOptimizerTraceFeaturePrefix.contains(unitArr[0])
            ) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, "optimizer_tracer_feature is invalid");
            }
            if (optMap.containsKey(unitArr[0])) {
                if (optMap.get(unitArr[0]).equals(unitArr[1])) {
                    overrideOptimizerSwitch = true;
                } else {
                    throw new RdsException(ErrorCode.INVALID_PARAMETERS, "optimizer_tracer_feature is invalid");
                }
            }
            optMap.put(unitArr[0], unitArr[1]);
        }
        if (overrideOptimizerSwitch) {
            String overrideParamValue = optMap.keySet().stream().sorted(Comparator.comparingInt(paramValue::indexOf)).map(optKey -> String.format("%s=%s", optKey, optMap.get(optKey))).collect(Collectors.joining(","));
            parameters.put(key, overrideParamValue);
        }
        return true;
    }

    public static void main(String[] args) {
        try {
            System.out.println(new OptimizerTraceFeaturesHelper().validator(
                    new HashMap<String, Object>() {{put("optimizer_trace_features","greedy_search=on,range_optimizer=on,dynamic_range=on,repeated_subselect=on");}},
                    "optimizer_trace_features",
                    null));
            System.out.println(new OptimizerTraceFeaturesHelper().validator(
                    new HashMap<String, Object>() {{put("optimizer_trace_features","greedy=on,range_optimizer=on,dynamic_range=on,repeated_subselect=on");}},
                    "optimizer_trace_features",
                    null));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}
