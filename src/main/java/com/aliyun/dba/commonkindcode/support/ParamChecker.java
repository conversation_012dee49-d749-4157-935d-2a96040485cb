package com.aliyun.dba.commonkindcode.support;

import com.aliyun.dba.commonkindcode.support.checker.IntValueChecker;
import com.aliyun.dba.commonkindcode.support.checker.MinorVersionChecker;
import com.aliyun.dba.commonkindcode.support.checker.StringValueChecker;
import com.aliyun.dba.commonkindcode.support.helper.*;
import com.aliyun.dba.custins.service.CustinsParamGroupsService;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.instance.entity.MycnfTemplate;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ParamChecker 参数检查
 *
 * @blame yuyi
 */
@Slf4j
public class ParamChecker {

    private static final Logger logger = LoggerFactory.getLogger(ParamChecker.class);

    private static final StringValueChecker stringValueChecker = new StringValueChecker();

    private static final IntValueChecker intValueChecker = new IntValueChecker();

    private static final MinorVersionChecker minorVersionChecker = new MinorVersionChecker();

    private static final Map<String, BaseHelper> specialParamValidator = new HashMap<String, BaseHelper>(){
        {
            put("default_time_zone", new TimezoneHelper());
            put("loose_query_cache_size", new QueryCacheSizeHelper());
            put("loose_optimizer_switch", new OptimizerSwitchHelper());
            put("innodb_temp_data_file_path", new InnodbTempDataFilePathHelper());
            put("optimizer_trace_features", new OptimizerTraceFeaturesHelper());
            put("loose_optimizer_trace_features", new OptimizerTraceFeaturesHelper());
            put("optimizer_trace", new OptimizerTraceHelper());
            put("loose_optimizer_trace", new OptimizerTraceHelper());
            put("lower_case_table_names", new LowerCaseTableNamesHelper());
            put("innodb_large_prefix", new InnodbLargePrefixHelper());
            put("character_set_server", new CharacterSetServerHelper());
            put("collation_server", new CollationServerHelper());
            put("sql_mode", new SqlModeHelper());
            put("transaction_write_set_extraction", new TransactionWriteSetExtractionHelper());
            put("binlog_transaction_dependency_tracking", new BinlogTransactionDependencyTrackingHelper());
            put("innodb_log_file_size", new InnodbLogFileSizeHelper());

        }
    };

    /**
     * 封装 CustinsValidator.checkParameters 方法，以支持表达式检查
     */
    public static void parameterCheck(Map<String, Object> parameters, String accessId,
                                      Map<String, ? extends MycnfTemplate> mycnfTemp,
                                      ParamContext context) throws Exception {

        if (parameters == null) {
            return;
        }

        Map<String, Object> parsedExprValueMap = new HashMap<>(1);
        ParamExpressionParser parser = context.getParser();
        // copy parameters
        Map<String, Object> parameterToValidate = new HashMap<>(parameters);
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String param = entry.getKey();
            Object value = entry.getValue();
            //检验模板是否存在
            MycnfTemplate parameter = mycnfTemp.get(param);
            if (mycnfTemp.get(param) == null) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, String.format("%s not found in config template", param));
            }

            //校验key， value不包含非法字符：
            stringValueChecker.check(param);
            stringValueChecker.check(value.toString());

            // 解析和校验表达式
            BigInteger parsedValue = intValueChecker.checkAndParse(param, value.toString(), parameter.getUnit(), parser);
            if (parsedValue != null) {
                parsedExprValueMap.put(param, parsedValue);
            }

            // 检验小版本
            minorVersionChecker.check(param, (String) value, context.getDbVersion(), context.getMinorVersion());

            // 复杂参数额外校验
            if (specialParamValidator.containsKey(param)) {
                if(!specialParamValidator.get(param).validator(parameterToValidate, param, context)) {
                    // 匹配一下上层瑶池针对错误码的处理，以展示出具体错误的参数名
                    // todo 验证无误后，validator()实现里面的throw也需要处理
                    throw new RdsException(ErrorCode.INVALID_PARAMETERS, String.format("%s[%s:%s]", ErrorCode.INVALID_PARAMETERS.getDesc(), param, value));
                }
            }
        }
        parameters.putAll(parameterToValidate);
        Map<String, Object> parameterValus = new HashMap<>(parameters);
        parameterValus.putAll(parsedExprValueMap);

        // default_time_zone collation_server 参数通过校验后无需再进入通用校验
        parameterValus.remove("default_time_zone");
        parameterValus.remove("collation_server");

        // 常规的mycnf_tempalte中的参数校验器
        try {
            CustinsValidator.checkParameters(parameterValus, accessId, mycnfTemp);
        } catch (RdsException ex) {
            logger.error("checkParameters failed. Message : " + ex.getErrorCode()[2].toString());
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }

    }

    /**
     * 当前方法用于 新老架构 用户自定义参数模板刷参时 参数模板详情查询
     * @param pId
     * @param custinsParamGroupsService
     * @return map
     * @throws RdsException
     */
    public static Map<String, Object> getParameterGroupDetail(long pId, CustinsParamGroupsService custinsParamGroupsService) throws RdsException {
        List<Map> details = custinsParamGroupsService.getParamGroupsDetailByParamGroupId(pId);
        Map<String, Object> finalParameterMap = new HashMap<>();
        details.forEach(param -> {
            String key = param.get("ParamName").toString();
            Object value = param.get("ParamValue");
            finalParameterMap.put(key, value);
        });

        // 用户自定义参数模板刷参时，innodb_buffer_pool_size 等必须是表达式 (aone:37190156)
        for (String key : ParamExpressionParser.MUST_EXPR) {
            if (finalParameterMap.containsKey(key)
                    && !finalParameterMap.get(key).toString().trim().startsWith("{")) {
                finalParameterMap.remove(key);
            }
        }
        return finalParameterMap;
    }

    public static void main(String[] args) {
        Map<String, MycnfTemplate> mycnfTemp = new HashMap<>();
        mycnfTemp.put("innodb_buffer_pool_size", new MycnfTemplate(){{setUnit("INT");}});
        mycnfTemp.put("default_time_zone", new MycnfTemplate());
        mycnfTemp.put("loose_query_cache_size", new MycnfTemplate());
        mycnfTemp.put("loose_optimizer_switch", new MycnfTemplate());
        mycnfTemp.put("innodb_temp_data_file_path", new MycnfTemplate());
        mycnfTemp.put("optimizer_trace_features", new MycnfTemplate());
        mycnfTemp.put("loose_optimizer_trace_features", new MycnfTemplate());
        mycnfTemp.put("optimizer_trace", new MycnfTemplate());
        mycnfTemp.put("loose_optimizer_trace", new MycnfTemplate());
        mycnfTemp.put("lower_case_table_names", new MycnfTemplate());
        mycnfTemp.put("innodb_large_prefix", new MycnfTemplate());
        mycnfTemp.put("character_set_server", new MycnfTemplate());

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("innodb_buffer_pool_size", "{DBInstanceClassMemory*1/2}");
        //parameters.put("default_time_zone", "Asia/Shanghai");
        parameters.put("loose_query_cache_size", "1000");
        //parameters.put("loose_optimizer_switch", "index_merge=on,index_merge_union=on,index_merge_sort_union=on");
        parameters.put("innodb_temp_data_file_path", "ibtmp1:12M:autoextend:max:123M");
        parameters.put("optimizer_trace_features", "greedy_search=on,range_optimizer=on,dynamic_range=on,repeated_subselect=on");
        parameters.put("loose_optimizer_trace_features", "greedy_search=on,range_optimizer=on,dynamic_range=on,repeated_subselect=on");
        parameters.put("optimizer_trace", "enabled=off,one_line=off");
        parameters.put("loose_optimizer_trace", "enabled=off,one_line=off");
        parameters.put("lower_case_table_names", "1");
        parameters.put("innodb_large_prefix", "on");
        parameters.put("character_set_server", "utf8");


        ParamContext context = new ParamContext();
        context.setDbType("mysql");
        context.setDbVersion("5.6");
        context.setCharacterType("normal");
        context.setInsType(1);
        context.setParamGroupId("");
        context.setIgnoreVisible(true);
        context.setAliGroup(false);
        context.setArm(false);
        context.setPolarxHatp(false);
        context.setCategory("standard");
        context.setCpuCoreCount(10);
        context.setMemSize(10240L);
        context.setDiskSize(102400L);
        context.setMaxConnection(500);
        try {
            ParamChecker.parameterCheck(parameters, "YaoChi", mycnfTemp, context);
        } catch (RdsException e) {
            System.out.println(e.getErrorCode()[2]);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
