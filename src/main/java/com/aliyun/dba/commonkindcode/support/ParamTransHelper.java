package com.aliyun.dba.commonkindcode.support;

import com.aliyun.dba.instance.dataobject.MycnfCustinsConfigDO;
import com.aliyun.dba.instance.dataobject.MycnfRunningParamDO;
import com.aliyun.dba.instance.entity.MycnfChangeLog;
import com.aliyun.dba.instance.entity.MycnfTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ParamTransHelper {
    public static final String SYS_PARAM_GROUP_ID_PREFIX = "rpg-sys-";

    public static final Integer PARAM_GROUP_TYPE_SYS = 0;
    public static final Integer PARAM_GROUP_TYPE_USER = 1;
    public static final Integer PARAM_GROUP_TYPE_BAK = 2;

    public static final String PARAM_MAP_KEY_PARAMETER_NAME = "ParameterName";
    public static final String PARAM_MAP_KEY_PARAMETER_VALUE = "ParameterValue";
    public static final String PARAM_MAP_KEY_REVISABLE = "Revisable";
    public static final String PARAM_MAP_KEY_EFFECTIVE = "Effective";
    public static final String PARAM_MAP_KEY_CHECKING_CODE = "CheckingCode";
    public static final String PARAM_MAP_KEY_UNIT = "Unit";
    public static final String PARAM_MAP_KEY_FACTOR = "Factor";
    public static final String PARAM_MAP_KEY_PARAMETER_DESC = "ParameterDescription";
    public static final String PARAM_MAP_KEY_OLD_PARAM_VALUE = "OldParameterValue";
    public static final String PARAM_MAP_KEY_NEW_PARAM_VALUE = "NewParameterValue";
    public static final String PARAM_MAP_KEY_IS_APPLIED = "IsApplied";
    public static final String PARAM_MAP_KEY_CHANGE_TIME = "ChangeTime";

    public static List<Map<String, Object>> getMapListFromMycnfChangeLogList(List<MycnfChangeLog> mycnfChangeLogList) {
        List<Map<String, Object>> list = new ArrayList<>();

        if (mycnfChangeLogList == null || mycnfChangeLogList.isEmpty()) {
            return list;
        }

        for (Object mycnfObj : mycnfChangeLogList) {
            list.add(getMapFromMycnfObject(mycnfObj));
        }

        return list;
    }


    public static List<Map<String, Object>> getMapListFromMycnfCustinsConfigList(List<MycnfCustinsConfigDO> mycnfConfigList) {
        List<Map<String, Object>> list = new ArrayList<>();

        if (mycnfConfigList == null || mycnfConfigList.isEmpty()) {
            return list;
        }

        for (Object mycnfObj : mycnfConfigList) {
            list.add(getMapFromMycnfObject(mycnfObj));
        }

        return list;

    }

    public static List<Map<String, Object>> getMapListFromMycnfRunningList(List<MycnfRunningParamDO> mycnfRunningList) {
        List<Map<String, Object>> list = new ArrayList<>();

        if (mycnfRunningList == null || mycnfRunningList.isEmpty()) {
            return list;
        }

        for (Object mycnfObj : mycnfRunningList) {
            list.add(getMapFromMycnfObject(mycnfObj));
        }

        return list;
    }


    public static List<Map<String, Object>> getMapListFromMycnfExtraList(List<? extends MycnfTemplate> mycnfObjList) {
        List<Map<String, Object>> list = new ArrayList<>();

        if (mycnfObjList == null || mycnfObjList.isEmpty()) {
            return list;
        }

        for (Object mycnfObj : mycnfObjList) {
            list.add(getMapFromMycnfObject(mycnfObj));
        }

        return list;
    }

    public static List<MycnfTemplate> coverParameters(List<? extends MycnfTemplate> originCnf,
                                                      List<? extends MycnfTemplate> newCnf) {

        List<MycnfTemplate> list = new ArrayList<>();
        Map<String, String> uniqMap = new HashMap<>();
        for (MycnfTemplate newParameter : newCnf) {
            if (uniqMap.get(newParameter.getName()) != null) {
                continue;
            }

            uniqMap.put(newParameter.getName(), newParameter.getDefaultValue());
            list.add(newParameter);
        }

        for (MycnfTemplate newParameter : originCnf) {
            if (uniqMap.get(newParameter.getName()) != null) {
                continue;
            }

            uniqMap.put(newParameter.getName(), newParameter.getDefaultValue());
            list.add(newParameter);
        }

        return list;
    }


    public static Map<String, Object> getMapFromMycnfObject(Object mycnfObj) {
        Map<String, Object> map = new HashMap<>();

        if (mycnfObj == null) {
            return map;
        }

        if (mycnfObj instanceof MycnfTemplate) {
            // cause MycnfTemplateExtra extends MycnfTemplate
            // so MycnfTemplateExtra instanceof MycnfTemplate == true
            MycnfTemplate mycnfObjTemp = (MycnfTemplate) mycnfObj;
            map.put(PARAM_MAP_KEY_PARAMETER_NAME, mycnfObjTemp.getName());
            map.put(PARAM_MAP_KEY_PARAMETER_VALUE, mycnfObjTemp.getDefaultValue());
            map.put(PARAM_MAP_KEY_REVISABLE, mycnfObjTemp.getIsUserChangable());
            map.put(PARAM_MAP_KEY_EFFECTIVE, mycnfObjTemp.getIsDynamic());
            map.put(PARAM_MAP_KEY_CHECKING_CODE, mycnfObjTemp.getOptional());
            map.put(PARAM_MAP_KEY_UNIT, mycnfObjTemp.getUnit());
            map.put(PARAM_MAP_KEY_FACTOR, mycnfObjTemp.getDivideBase());
            map.put(PARAM_MAP_KEY_PARAMETER_DESC, mycnfObjTemp.getComment());
        } else if (mycnfObj instanceof MycnfRunningParamDO) {
            MycnfRunningParamDO mycnfRunningParamDO = (MycnfRunningParamDO) mycnfObj;
            map.put(PARAM_MAP_KEY_PARAMETER_NAME, mycnfRunningParamDO.getName());
            map.put(PARAM_MAP_KEY_PARAMETER_VALUE, mycnfRunningParamDO.getParamValue());
            map.put(PARAM_MAP_KEY_REVISABLE, mycnfRunningParamDO.getIsDynamic());
            map.put(PARAM_MAP_KEY_EFFECTIVE, mycnfRunningParamDO.getIsDynamic());
            map.put(PARAM_MAP_KEY_CHECKING_CODE, mycnfRunningParamDO.getOptional());
            map.put(PARAM_MAP_KEY_UNIT, mycnfRunningParamDO.getUnit());
            map.put(PARAM_MAP_KEY_FACTOR, mycnfRunningParamDO.getDivideBase());
            map.put(PARAM_MAP_KEY_PARAMETER_DESC, mycnfRunningParamDO.getComment());
        } else if (mycnfObj instanceof MycnfChangeLog) {
            MycnfChangeLog mycnfChangeLog = (MycnfChangeLog) mycnfObj;
            map.put(PARAM_MAP_KEY_PARAMETER_NAME, mycnfChangeLog.getName());
            map.put(PARAM_MAP_KEY_OLD_PARAM_VALUE, mycnfChangeLog.getOldValue());
            map.put(PARAM_MAP_KEY_NEW_PARAM_VALUE, mycnfChangeLog.getNewValue());
            map.put(PARAM_MAP_KEY_IS_APPLIED, mycnfChangeLog.getIsApplied());
            map.put(PARAM_MAP_KEY_CHANGE_TIME, mycnfChangeLog.getGmtCreated());
        }

        return map;
    }
}
