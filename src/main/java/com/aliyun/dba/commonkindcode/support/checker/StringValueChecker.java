package com.aliyun.dba.commonkindcode.support.checker;

import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang.StringUtils;

public class StringValueChecker {
    public void check(String value) throws RdsException {
        //[a-zA-Z0-9_-.]＋
        if (StringUtils.isEmpty(value)){
            return;
        }

        for (char c : value.toCharArray()) {
            if (c == '\n' || c == '\r') {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, String.format("include invalidate char ,for value %s", value));
            }
        }

    }
}
