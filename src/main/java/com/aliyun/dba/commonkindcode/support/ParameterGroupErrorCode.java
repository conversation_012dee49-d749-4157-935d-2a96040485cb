package com.aliyun.dba.commonkindcode.support;

import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.support.property.ResultCode;

public enum ParameterGroupErrorCode {
    PARAM_GROUP_NOT_FOUND(ResultCode.CODE_NOTFOUND, "NotFound.ParamGroupId", "Current ParamGroupId not found."),
    INVALID_PARAM_GROUP_DB_VERSION(ResultCode.CODE_FORBIDDEN, "Invalid.ParamGroupDBVersion", "ParamGroup '%s' version is %s, not %s."),
    INVALID_PARAM_GROUP_DB_TYPE(ResultCode.CODE_FORBIDDEN, "Invalid.ParamGroupDBType", "ParamGroup '%s' type is %s, not %s."),
    INVALID_PARAM_GROUP_DB_CATEGORY(ResultCode.CODE_FORBIDDEN, "Invalid.ParamGroupDBCategory", "ParamGroup '%s' category is %s, not %s."),
    INVALID_PARAM_GROUP_DB_STORAGE_ENGINE(ResultCode.CODE_FORBIDDEN, "Invalid.ParamGroupDBStorageEngine", "ParamGroup '%s' storage engine is %s, not %s.");

    private int code;
    private String summary;
    private String desc;

    ParameterGroupErrorCode(int code, String summary, String desc) {
        this.code = code;
        this.summary = summary;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getSummary() {
        return summary;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String customizedErrorDesc(Response resp) {
        return resp.getMessage() + "(" + resp.getDescription() + ")";
    }

    public Object[] toArray() {
        return new Object[]{this.code, this.summary, this.desc};
    }
}