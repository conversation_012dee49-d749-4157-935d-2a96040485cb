package com.aliyun.dba.commonkindcode.support;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/*
 * 辅助参数的上下文信息
 */
@Data
public class ParamContext {
    private Integer custinsId;
    private String dbType;
    private String dbVersion;
    private Integer minorVersion;
    private String characterType;
    private String category;
    private String paramGroupId;
    private Integer insType;

    private Long memSize;
    private Long diskSize;
    private Integer cpuCoreCount;
    private Integer maxConnection;

    private boolean ignoreVisible;
    private boolean isAliGroup;
    private boolean isArm;
    private boolean isPolarxHatp;
    private boolean is80Beta;
    private boolean isXengine;
    /**
     * 用户是否具有 collation_server 参数
     */
    private boolean existCollationServer;

    public  ParamContext() {}
    public ParamContext(CustInstanceDO custins, String paramGroupId) {
        if (custins != null) {
            custinsId = custins.getId();
            dbType = custins.getDbType();
            dbVersion = custins.getDbVersion();
            characterType = custins.getCharacterType();
            insType = custins.getInsType();
        }
        this.paramGroupId = paramGroupId;
        if ("mysql".equalsIgnoreCase(dbType)) {
            if (!"normal".equalsIgnoreCase(characterType)) {
                characterType = "normal";
            }
        }
    }

    public boolean isIs80Beta() {
        return StringUtils.equals("8.0", dbVersion) && is80Beta;
    }

    public ParamExpressionParser getParser() throws Exception {
        return new ParamExpressionParser(cpuCoreCount, memSize, diskSize, maxConnection);
    }
}
