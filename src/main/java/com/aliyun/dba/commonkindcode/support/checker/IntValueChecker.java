package com.aliyun.dba.commonkindcode.support.checker;

import com.aliyun.dba.base.support.SizeUnitTransTool;
import com.aliyun.dba.commonkindcode.support.ParamExprException;
import com.aliyun.dba.commonkindcode.support.ParamExpressionParser;

import java.math.BigInteger;

public class IntValueChecker  {
    public BigInteger checkAndParse(String key, String value, String unit, ParamExpressionParser parser) throws Exception {
        // 检验INT、B 型支持参数表达式
        if ("INT".equals(unit) || "B".equals(unit)) {
            boolean isExpr = value.trim().startsWith("{");
            // max_connections禁止使用参数表达式
            if ("max_connections".equals(key) && isExpr) {
                throw new ParamExprException("max_connections parameter value cannot be expr");
            }
            // 参数表达式限制，某些参数只支持参数表达式
            if (ParamExpressionParser.MUST_EXPR.contains(key) && !isExpr) {
                throw new ParamExprException("parameter value must be expr");
            }

            // 表达式不支持小数
            if (isExpr && value.contains(".")) {
                throw new ParamExprException("expr not support decimal");
            }

            BigInteger parsedValue = isExpr ?
                    // 解析表达式
                    parser.parse(value) :
                    // 支持单位换算
                    SizeUnitTransTool.trans(value, "B").toBigInteger();

            // max validate
            if (ParamExpressionParser.MAX_DEFINE.get(key) != null) {
                BigInteger maxValue = parser.parse(ParamExpressionParser.MAX_DEFINE.get(key));
                if (parsedValue.compareTo(maxValue) > 0) {
                    throw new ParamExprException(String.format("parsed value %d out of range (>%d)", parsedValue, maxValue));
                }
            }

            return parsedValue;
        }
        return null;
    }

    public static String parseParamValueSwitchByUnit(String value, String unit) {
        if ("INT".equalsIgnoreCase(unit)) {
            if ("ON".equalsIgnoreCase(value)) {
                return "1";
            } else if ("OFF".equalsIgnoreCase(value)) {
                return "0";
            }
        } else if ("STRING".equalsIgnoreCase(unit)) {
            if ("1".equalsIgnoreCase(value)) {
                return "ON";
            } else if ("0".equalsIgnoreCase(value)) {
                return "OFF";
            }
        }
        return value;
    }

    public static void main(String[] args) {

    }
}
