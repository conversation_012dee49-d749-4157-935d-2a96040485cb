package com.aliyun.dba.commonkindcode.support;

import com.aliyun.dba.base.support.SizeUnitTransTool;
import com.aliyun.dba.poddefault.action.support.RundPodSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.udojava.evalex.AbstractFunction;
import com.udojava.evalex.Expression;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.util.*;

/**
 * 参数表达式解析类，功能基于 EvalEx - Java Expression Evaluator
 * https://github.com/uklimaschewski/EvalEx
 *
 * @blame 宇一
 */
public class ParamExpressionParser {
    @Getter
    @Setter
    private Long memSize;
    @Getter
    @Setter
    private Long diskSize;
    @Getter
    @Setter
    private Integer cpuCoreCount;
    @Getter
    @Setter
    private Integer maxConnection;

    private static final LogAgent logger = LogFactory.getLogAgent(ParamExpressionParser.class);


    public static final ArrayList<String> FUNC = new ArrayList<String>() {
        {
            add("GREATEST");
            add("LEAST");
            add("SUM");
        }
    };


    public static final ArrayList<String> VARIABLES = new ArrayList<String>() {
        {
            add("AllocatedStorage");
            add("DBInstanceClassMemory");
            add("DBInstanceClassCPU");
            add("DBInstanceClassConnections");
        }
    };

    public static final ArrayList<String> OPS = new ArrayList<String>() {
        {
            add("/");
            add("*");
        }
    };

    public static final ArrayList<String> SEPARATORS = new ArrayList<String>() {
        {
            add("(");
            add(")");
            add("{");
            add("}");
            add("/");
            add("*");
            add(",");

        }
    };

    /**
     * 最大值公式
     */
    public static final HashMap<String, String> MAX_DEFINE = new HashMap<String, String>() {
        {
            this.put("innodb_buffer_pool_size", "{DBInstanceClassMemory*4/5}");
        }
    };

    /**
     * 参数公式黑名单，在名单中的参数只能设置参数表达式
     */
    public static final ArrayList<String> MUST_EXPR = new ArrayList<String>() {
        {
            add("innodb_buffer_pool_size");
        }
    };


    public static ArrayList<String> separate(String str) throws ParamExprException {
        String word = "";
        ArrayList<String> words = new ArrayList<>();
        Stack<String> bigQuota = new Stack<>();
        Stack<String> smallQuota = new Stack<>();


        char[] expr = str.replace(" ", "").toCharArray();

        for (char c : expr) {
            if ("{".equals(String.valueOf(c))) {
                bigQuota.push(String.valueOf(c));

            }
            if ("}".equals(String.valueOf(c)) && !bigQuota.isEmpty()) {
                bigQuota.pop();
            }

            if ("(".equals(String.valueOf(c))) {
                smallQuota.push(String.valueOf(c));

            }
            if (")".equals(String.valueOf(c)) && !smallQuota.isEmpty()) {
                smallQuota.pop();
            }

            if (!String.valueOf(c).matches("[a-z || A-Z || 0-9 || .]")) {
                if (!SEPARATORS.contains(String.valueOf(c))) {
                    throw new ParamExprException("Function " + str + " error in expression");
                }
            }

            ArrayList<String> separate = new ArrayList<>();
            separate.add("(");
            separate.add(")");
            separate.add("{");
            separate.add("}");

            if (!smallQuota.isEmpty()) {
                separate.add(",");
            }
            if (!bigQuota.isEmpty()) {
                separate.add("/");
                separate.add("*");
            }


            if (separate.contains(String.valueOf(c))) {
                if (word.length() > 0) {
                    words.add(word);
                    word = "";
                }
                words.add(String.valueOf(c));
            } else {
                word = word.concat(String.valueOf(c));
            }


        }
        if (word.length() > 0) {
            words.add(word);
        }
        return words;
    }


    public void parseExp(String str) throws ParamExprException {
        Stack<String> ops = new Stack<>();
        ArrayList<String> words = separate(str);
        Stack<String> bigQuota = new Stack<>();
        Stack<String> smallQuota = new Stack<>();
        for (String word : words) {
            if ("{".equals(word)) {
                bigQuota.push(word);
            } else if ("}".equals(word)) {
                if (bigQuota.isEmpty() || !"{".equals(bigQuota.get(bigQuota.size() - 1))) {
                    throw new ParamExprException("Function " + str + " error in expression");
                }
                bigQuota.pop();
            } else if ("(".equals(word)) {
                smallQuota.push(word);
                ops.push(word);
            } else if (")".equals(word)) {
                while (!ops.isEmpty()) {
                    String op = ops.pop();
                    if ("(".equals(op)) {
                        if (ops.isEmpty() || !FUNC.contains(ops.get(ops.size() - 1))) {
                            throw new ParamExprException("Function " + str + " error in expression");
                        }
                        ops.pop();
                        break;
                    }
                }
                smallQuota.pop();
            } else if (OPS.contains(word)) {
                if (ops.isEmpty()) {
                    ops.push(word);
                } else {
                    while (!ops.isEmpty() && OPS.contains(ops.get(ops.size() - 1))) {
                        ops.pop();
                    }
                    ops.add(word);
                }
            } else if (FUNC.contains(word)) {
                ops.add(word);
            } else if (",".equals(word)) {
                while (!ops.isEmpty() && !"(".equals(ops.get(ops.size() - 1))) {
                    ops.pop();
                }
            } else {
                int wordInt = 0;
                if (!bigQuota.isEmpty() && VARIABLES.contains(word)) {
                    wordInt = 1;
                }
                if (wordInt == 0) {
                    if (!smallQuota.isEmpty() && !SEPARATORS.contains(word)) {
                        try {
                            new BigInteger(word);

                        } catch (Exception e) {
                            throw new ParamExprException("Function " + str + " error in expression");
                        }
                    }
                }
            }


        }
        if (!bigQuota.isEmpty() || ops.contains("(")) {
            throw new ParamExprException("Function " + str + " error in expression");
        }
    }

    /**
     * 支持函数
     * GREATEST()
     * 返回整数型或者参数公式列表中最大的值。
     * 语法
     * GREATEST(argument1, argument2,...argumentn)
     * 返回整数。
     * LEAST()
     * 返回整数型或者参数公式列表中最小的值。
     * 语法
     * LEAST(argument1, argument2,...argumentn)
     * 返回整数。
     * SUM()
     * 添加指定整数型或者参数公式的值。
     * 语法
     * SUM(argument1, argument2,...argumentn)
     * 返回整数。
     */
    public static final ArrayList<AbstractFunction> FUNCTIONS = new ArrayList<AbstractFunction>() {
        {
            // SUM
            add(new AbstractFunction("sum", -1) {
                @Override
                public BigDecimal eval(List<BigDecimal> parameters) {
                    if (parameters.size() < 1) {
                        throw new Expression.ExpressionException("SUM requires at least one parameter");
                    }
                    BigDecimal sum = new BigDecimal(0);
                    for (BigDecimal parameter : parameters) {
                        sum = sum.add(parameter);
                    }
                    return sum;
                }
            });
            // GREATEST
            add(new AbstractFunction("greatest", -1) {
                @Override
                public BigDecimal eval(List<BigDecimal> parameters) {
                    if (parameters.size() < 1) {
                        throw new Expression.ExpressionException("GREATEST requires at least one parameter");
                    }
                    BigDecimal max = parameters.get(0);
                    for (BigDecimal parameter : parameters) {
                        if (max.compareTo(parameter) < 1) {
                            max = parameter;
                        }
                    }
                    return max;
                }
            });
            // LEAST
            add(new AbstractFunction("least", -1) {
                @Override
                public BigDecimal eval(List<BigDecimal> parameters) {
                    if (parameters.size() < 1) {
                        throw new Expression.ExpressionException("LEAST requires at least one parameter");
                    }
                    BigDecimal min = parameters.get(0);
                    for (BigDecimal parameter : parameters) {
                        if (min.compareTo(parameter) > -1) {
                            min = parameter;
                        }
                    }
                    return min;
                }
            });
        }
    };

    public ParamExpressionParser(Integer cpuCoreCount, Long memSize, Long diskSize, Integer maxConnection) throws Exception {
        this.cpuCoreCount = cpuCoreCount;
        // 获取的内存大小单位是M，要转换成B
        this.memSize = SizeUnitTransTool.trans(String.valueOf(memSize)+"M", "B").longValue();
        this.diskSize = diskSize;
        this.maxConnection = maxConnection;
    }
    /**
     * 解析表达式，计算值
     */
    public BigInteger parse(String exprStr) throws ParamExprException {
        // 公式检查
        parseExp(exprStr);

        // 公式解析
        if (exprStr.trim().startsWith("{")) {
            exprStr = exprStr.trim().substring(1);
            exprStr = exprStr.substring(0, exprStr.length() - 1);
        }

        // MathContext.DECIMAL128 用来指定精度
        Expression expression = new Expression(exprStr, MathContext.DECIMAL128);
        // functions
        FUNCTIONS.forEach(expression::addFunction);
        // parameters
        expression
                .with("AllocatedStorage", BigDecimal.valueOf(this.diskSize))
                .and("DBInstanceClassMemory", BigDecimal.valueOf(this.memSize))
                .and("DBInstanceClassCPU", BigDecimal.valueOf(this.cpuCoreCount))
                .and("DBInstanceClassConnections", BigDecimal.valueOf(this.maxConnection));
        try {
            return expression.eval().toBigInteger();
        } catch (Expression.ExpressionException e) {
            logger.error("expression:{}, error:{}", expression, e.getMessage());
            throw new ParamExprException("Function " + expression + " error in expression");
        }
    }

    /**
     * 测试
     */
    public static void main(String[] args) {
        List<String> mem = new ArrayList<String>(){{
            add("1024");
            add("{DBInstanceClassMemory*1/2}");
            add("{DBInstanceClassCPU*4/5}");
            add("{GREATEST(DBInstanceClassMemory*1/2, DBInstanceClassCPU*4/5)}");
            add("{LEAST(DBInstanceClassMemory*1/2, DBInstanceClassCPU*4/5)}");
            add("{SUM(DBInstanceClassMemory*1/2, DBInstanceClassCPU*4/5)}");
        }};

        ParamExpressionParser parser;
        try {
            parser = new ParamExpressionParser(10,10240L,1024000L,100);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        mem.forEach(memSize -> {
            try {
                System.out.println(memSize);
                System.out.println(parser.parse(memSize));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
}
