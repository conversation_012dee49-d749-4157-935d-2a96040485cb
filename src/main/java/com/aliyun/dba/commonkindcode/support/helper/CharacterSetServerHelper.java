package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Component
public class CharacterSetServerHelper implements BaseHelper {
	private static final LogAgent logger = LogFactory.getLogAgent(CharacterSetServerHelper.class);

	@Override
	public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException, IOException {
		if (context.getInsType() == 3 || context.getInsType() == 4) {
			throw new RdsException(ErrorCode.INVALID_PARAMETERS, "character_set_server can not be set on read instances");
		}

		String characterSetServer = parameters.get(key).toString();
		String collationServer = parameters.get("collation_server") == null ? null : parameters.get("collation_server").toString();

		DbossApi dboss = SpringContextUtil.getBeanByClass(DbossApi.class);
		List<Map<String, String>> supportedCollations = dboss.getCollations(context.getCustinsId(), characterSetServer);

		if (StringUtils.isEmpty(collationServer)) {
			logger.info("character_set_server is [{}], but collation_server is empty", characterSetServer);
			// 检查用户是否设置过collation，也就是参数快照表里面是否有collation
			if (!context.isExistCollationServer()) {
				logger.info("since collation_server does not exist, it is not set");
				return true;
			}

			for (Map<String, String> collation : supportedCollations) {
				if ("Yes".equalsIgnoreCase(collation.get("is_default"))) {
					// character_set_server 联动  collation_server刷参的先注释掉 发现有bug 先注释掉后  20230725
					parameters.put("collation_server", collation.get("collation_name"));
					logger.info("since collation_server exists, it is set to [{}]", collation.get("collation_name"));
					return true;
				}
			}
		} else {
			logger.info("character_set_server is [{}], collation_server is [{}]", characterSetServer, collationServer);
			// 如果同时更改了character和collation，则校验该组合是否有效
			for (Map<String, String> collation : supportedCollations) {
				String collationName = collation.get("collation_name");
				if (StringUtils.startsWith(collationName, "utf8mb3_")) {
					// 如果是utf8mb3，需要把utf8_替换成utf8mb3_
					collationServer = collationServer.replaceAll("^utf8_", "utf8mb3_");
				}
				if (collationServer.equalsIgnoreCase(collationName)) {
					// 匹配
					logger.info("character_set_server and collation_server are matched");
					return true;
				}
				logger.warn("character_set_server and collation_server are not matched");
			}
		}
		return false;
	}

	public static void main(String[] args) {
		System.out.println("utf8mb3".replaceAll("^utf8mb3*", "utf8"));
		System.out.println("utf8".replaceAll("^utf8mb3*", "utf8"));
		System.out.println("utf8mb3_test".replaceAll("^utf8mb3*", "utf8"));
	}
}
