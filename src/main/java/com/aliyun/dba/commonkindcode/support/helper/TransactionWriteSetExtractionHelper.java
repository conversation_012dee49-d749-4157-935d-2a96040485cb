package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * 条件1：When WRITESET or WRITESET_SESSION is set as the value for binlog_transaction_dependency_tracking,
 * transaction_write_set_extraction must be set to specify an algorithm (not set to OFF).
 * 条件2：While the current value of binlog_transaction_dependency_tracking is WRIT<PERSON>ET or WRITESET_SESSION,
 * you cannot change the value of transaction_write_set_extraction.
 */
@Component
public class TransactionWriteSetExtractionHelper implements BaseHelper {
    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException, IOException {
        String b = parameters.get("binlog_transaction_dependency_tracking") == null ? null : parameters.get("binlog_transaction_dependency_tracking").toString();

        if (StringUtils.isEmpty(b)) {
            // 如果本次只设置t，则校验条件2
            DbossApi dboss = SpringContextUtil.getBeanByClass(DbossApi.class);
            Map<String, Object> value = dboss.getParameter(context.getCustinsId(), "binlog_transaction_dependency_tracking");
            b = value == null ? null : String.valueOf(value.get("binlog_transaction_dependency_tracking"));
            return !"WRITESET".equalsIgnoreCase(b) && !"WRITESET_SESSION".equalsIgnoreCase(b);
        } else {
            // 如果同时更改了t和b，留给BinlogTransactionDependencyTrackingHelper去校验
            return true;
        }
    }

    public static void main(String[] args) {

    }
}
