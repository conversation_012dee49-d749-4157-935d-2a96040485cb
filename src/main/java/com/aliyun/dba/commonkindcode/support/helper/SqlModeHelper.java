package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/06/13
 **/

public class SqlModeHelper implements BaseHelper {
    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException {
        String sqlMode = parameters.get("sql_mode").toString();
        if (sqlMode.startsWith(",") || sqlMode.endsWith(",")) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS,
                    ErrorCode.INVALID_PARAMETERS.getDesc() + "[sql_mode: Commas should not be used to initiate or conclude this parameter value.]");
        }
        return true;
    }
}
