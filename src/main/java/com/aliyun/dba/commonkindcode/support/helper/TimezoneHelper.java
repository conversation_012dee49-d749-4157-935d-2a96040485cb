package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Component
public class TimezoneHelper implements BaseHelper {
    //配置key
    private static final String MYSQL_SUPPORT_NAMED_ZONE_LIST_KEY = "MYSQL_SUPPORT_NAMED_ZONE_LIST";
    private static final String MYSQL_SUPPORT_NAMED_ZONE_SEPARATOR = ";";
    //5分钟缓存
    private static final int CACHE_EXPIRED_MILLIS = 1000 * 5 * 60;
    //上次更新时间
    private static volatile long lastUpdateMillis = 0;
    //缓存
    private static List<String> validNamedZoneList = new ArrayList<>();
    public static final String AFRICA_ABIDJAN = "Africa/Abidjan";
    public static final String AFRICA_ACCRA = "Africa/Accra";
    public static final String AFRICA_ADDIS_ABABA = "Africa/Addis_Ababa";
    public static final String AFRICA_ALGIERS = "Africa/Algiers";
    public static final String AFRICA_ASMARA = "Africa/Asmara";
    public static final String AFRICA_ASMERA = "Africa/Asmera";
    public static final String AFRICA_BAMAKO = "Africa/Bamako";
    public static final String AFRICA_BANGUI = "Africa/Bangui";
    public static final String AFRICA_BANJUL = "Africa/Banjul";
    public static final String AFRICA_BISSAU = "Africa/Bissau";
    public static final String AFRICA_BLANTYRE = "Africa/Blantyre";
    public static final String AFRICA_BRAZZAVILLE = "Africa/Brazzaville";
    public static final String AFRICA_BUJUMBURA = "Africa/Bujumbura";
    public static final String AFRICA_CAIRO = "Africa/Cairo";
    public static final String AFRICA_CASABLANCA = "Africa/Casablanca";
    public static final String AFRICA_CEUTA = "Africa/Ceuta";
    public static final String AFRICA_CONAKRY = "Africa/Conakry";
    public static final String AFRICA_DAKAR = "Africa/Dakar";
    public static final String AFRICA_DAR_ES_SALAAM = "Africa/Dar_es_Salaam";
    public static final String AFRICA_DJIBOUTI = "Africa/Djibouti";
    public static final String AFRICA_DOUALA = "Africa/Douala";
    public static final String AFRICA_EL_AAIUN = "Africa/El_Aaiun";
    public static final String AFRICA_FREETOWN = "Africa/Freetown";
    public static final String AFRICA_GABORONE = "Africa/Gaborone";
    public static final String AFRICA_HARARE = "Africa/Harare";
    public static final String AFRICA_JOHANNESBURG = "Africa/Johannesburg";
    public static final String AFRICA_JUBA = "Africa/Juba";
    public static final String AFRICA_KAMPALA = "Africa/Kampala";
    public static final String AFRICA_KHARTOUM = "Africa/Khartoum";
    public static final String AFRICA_KIGALI = "Africa/Kigali";
    public static final String AFRICA_KINSHASA = "Africa/Kinshasa";
    public static final String AFRICA_LAGOS = "Africa/Lagos";
    public static final String AFRICA_LIBREVILLE = "Africa/Libreville";
    public static final String AFRICA_LOME = "Africa/Lome";
    public static final String AFRICA_LUANDA = "Africa/Luanda";
    public static final String AFRICA_LUBUMBASHI = "Africa/Lubumbashi";
    public static final String AFRICA_LUSAKA = "Africa/Lusaka";
    public static final String AFRICA_MALABO = "Africa/Malabo";
    public static final String AFRICA_MAPUTO = "Africa/Maputo";
    public static final String AFRICA_MASERU = "Africa/Maseru";
    public static final String AFRICA_MBABANE = "Africa/Mbabane";
    public static final String AFRICA_MOGADISHU = "Africa/Mogadishu";
    public static final String AFRICA_MONROVIA = "Africa/Monrovia";
    public static final String AFRICA_NAIROBI = "Africa/Nairobi";
    public static final String AFRICA_NDJAMENA = "Africa/Ndjamena";
    public static final String AFRICA_NIAMEY = "Africa/Niamey";
    public static final String AFRICA_NOUAKCHOTT = "Africa/Nouakchott";
    public static final String AFRICA_OUAGADOUGOU = "Africa/Ouagadougou";
    public static final String AFRICA_PORTO_NOVO = "Africa/Porto-Novo";
    public static final String AFRICA_SAO_TOME = "Africa/Sao_Tome";
    public static final String AFRICA_TIMBUKTU = "Africa/Timbuktu";
    public static final String AFRICA_TRIPOLI = "Africa/Tripoli";
    public static final String AFRICA_TUNIS = "Africa/Tunis";
    public static final String AFRICA_WINDHOEK = "Africa/Windhoek";
    public static final String AMERICA_ADAK = "America/Adak";
    public static final String AMERICA_ANCHORAGE = "America/Anchorage";
    public static final String AMERICA_ANGUILLA = "America/Anguilla";
    public static final String AMERICA_ANTIGUA = "America/Antigua";
    public static final String AMERICA_ARAGUAINA = "America/Araguaina";
    public static final String AMERICA_ARGENTINA_BUENOS_AIRES = "America/Argentina/Buenos_Aires";
    public static final String AMERICA_ARGENTINA_CATAMARCA = "America/Argentina/Catamarca";
    public static final String AMERICA_ARGENTINA_COMODRIVADAVIA = "America/Argentina/ComodRivadavia";
    public static final String AMERICA_ARGENTINA_CORDOBA = "America/Argentina/Cordoba";
    public static final String AMERICA_ARGENTINA_JUJUY = "America/Argentina/Jujuy";
    public static final String AMERICA_ARGENTINA_LA_RIOJA = "America/Argentina/La_Rioja";
    public static final String AMERICA_ARGENTINA_MENDOZA = "America/Argentina/Mendoza";
    public static final String AMERICA_ARGENTINA_RIO_GALLEGOS = "America/Argentina/Rio_Gallegos";
    public static final String AMERICA_ARGENTINA_SALTA = "America/Argentina/Salta";
    public static final String AMERICA_ARGENTINA_SAN_JUAN = "America/Argentina/San_Juan";
    public static final String AMERICA_ARGENTINA_SAN_LUIS = "America/Argentina/San_Luis";
    public static final String AMERICA_ARGENTINA_TUCUMAN = "America/Argentina/Tucuman";
    public static final String AMERICA_ARGENTINA_USHUAIA = "America/Argentina/Ushuaia";
    public static final String AMERICA_ARUBA = "America/Aruba";
    public static final String AMERICA_ASUNCION = "America/Asuncion";
    public static final String AMERICA_ATIKOKAN = "America/Atikokan";
    public static final String AMERICA_ATKA = "America/Atka";
    public static final String AMERICA_BAHIA = "America/Bahia";
    public static final String AMERICA_BAHIA_BANDERAS = "America/Bahia_Banderas";
    public static final String AMERICA_BARBADOS = "America/Barbados";
    public static final String AMERICA_BELEM = "America/Belem";
    public static final String AMERICA_BELIZE = "America/Belize";
    public static final String AMERICA_BLANC_SABLON = "America/Blanc-Sablon";
    public static final String AMERICA_BOA_VISTA = "America/Boa_Vista";
    public static final String AMERICA_BOGOTA = "America/Bogota";
    public static final String AMERICA_BOISE = "America/Boise";
    public static final String AMERICA_BUENOS_AIRES = "America/Buenos_Aires";
    public static final String AMERICA_CAMBRIDGE_BAY = "America/Cambridge_Bay";
    public static final String AMERICA_CAMPO_GRANDE = "America/Campo_Grande";
    public static final String AMERICA_CANCUN = "America/Cancun";
    public static final String AMERICA_CARACAS = "America/Caracas";
    public static final String AMERICA_CATAMARCA = "America/Catamarca";
    public static final String AMERICA_CAYENNE = "America/Cayenne";
    public static final String AMERICA_CAYMAN = "America/Cayman";
    public static final String AMERICA_CHICAGO = "America/Chicago";
    public static final String AMERICA_CHIHUAHUA = "America/Chihuahua";
    public static final String AMERICA_CORAL_HARBOUR = "America/Coral_Harbour";
    public static final String AMERICA_CORDOBA = "America/Cordoba";
    public static final String AMERICA_COSTA_RICA = "America/Costa_Rica";
    public static final String AMERICA_CRESTON = "America/Creston";
    public static final String AMERICA_CUIABA = "America/Cuiaba";
    public static final String AMERICA_CURACAO = "America/Curacao";
    public static final String AMERICA_DANMARKSHAVN = "America/Danmarkshavn";
    public static final String AMERICA_DAWSON = "America/Dawson";
    public static final String AMERICA_DAWSON_CREEK = "America/Dawson_Creek";
    public static final String AMERICA_DENVER = "America/Denver";
    public static final String AMERICA_DETROIT = "America/Detroit";
    public static final String AMERICA_DOMINICA = "America/Dominica";
    public static final String AMERICA_EDMONTON = "America/Edmonton";
    public static final String AMERICA_EIRUNEPE = "America/Eirunepe";
    public static final String AMERICA_EL_SALVADOR = "America/El_Salvador";
    public static final String AMERICA_ENSENADA = "America/Ensenada";
    public static final String AMERICA_FORT_NELSON = "America/Fort_Nelson";
    public static final String AMERICA_FORT_WAYNE = "America/Fort_Wayne";
    public static final String AMERICA_FORTALEZA = "America/Fortaleza";
    public static final String AMERICA_GLACE_BAY = "America/Glace_Bay";
    public static final String AMERICA_GODTHAB = "America/Godthab";
    public static final String AMERICA_GOOSE_BAY = "America/Goose_Bay";
    public static final String AMERICA_GRAND_TURK = "America/Grand_Turk";
    public static final String AMERICA_GRENADA = "America/Grenada";
    public static final String AMERICA_GUADELOUPE = "America/Guadeloupe";
    public static final String AMERICA_GUATEMALA = "America/Guatemala";
    public static final String AMERICA_GUAYAQUIL = "America/Guayaquil";
    public static final String AMERICA_GUYANA = "America/Guyana";
    public static final String AMERICA_HALIFAX = "America/Halifax";
    public static final String AMERICA_HAVANA = "America/Havana";
    public static final String AMERICA_HERMOSILLO = "America/Hermosillo";
    public static final String AMERICA_INDIANA_INDIANAPOLIS = "America/Indiana/Indianapolis";
    public static final String AMERICA_INDIANA_KNOX = "America/Indiana/Knox";
    public static final String AMERICA_INDIANA_MARENGO = "America/Indiana/Marengo";
    public static final String AMERICA_INDIANA_PETERSBURG = "America/Indiana/Petersburg";
    public static final String AMERICA_INDIANA_TELL_CITY = "America/Indiana/Tell_City";
    public static final String AMERICA_INDIANA_VEVAY = "America/Indiana/Vevay";
    public static final String AMERICA_INDIANA_VINCENNES = "America/Indiana/Vincennes";
    public static final String AMERICA_INDIANA_WINAMAC = "America/Indiana/Winamac";
    public static final String AMERICA_INDIANAPOLIS = "America/Indianapolis";
    public static final String AMERICA_INUVIK = "America/Inuvik";
    public static final String AMERICA_IQALUIT = "America/Iqaluit";
    public static final String AMERICA_JAMAICA = "America/Jamaica";
    public static final String AMERICA_JUJUY = "America/Jujuy";
    public static final String AMERICA_JUNEAU = "America/Juneau";
    public static final String AMERICA_KENTUCKY_LOUISVILLE = "America/Kentucky/Louisville";
    public static final String AMERICA_KENTUCKY_MONTICELLO = "America/Kentucky/Monticello";
    public static final String AMERICA_KNOX_IN = "America/Knox_IN";
    public static final String AMERICA_KRALENDIJK = "America/Kralendijk";
    public static final String AMERICA_LA_PAZ = "America/La_Paz";
    public static final String AMERICA_LIMA = "America/Lima";
    public static final String AMERICA_LOS_ANGELES = "America/Los_Angeles";
    public static final String AMERICA_LOUISVILLE = "America/Louisville";
    public static final String AMERICA_LOWER_PRINCES = "America/Lower_Princes";
    public static final String AMERICA_MACEIO = "America/Maceio";
    public static final String AMERICA_MANAGUA = "America/Managua";
    public static final String AMERICA_MANAUS = "America/Manaus";
    public static final String AMERICA_MARIGOT = "America/Marigot";
    public static final String AMERICA_MARTINIQUE = "America/Martinique";
    public static final String AMERICA_MATAMOROS = "America/Matamoros";
    public static final String AMERICA_MAZATLAN = "America/Mazatlan";
    public static final String AMERICA_MENDOZA = "America/Mendoza";
    public static final String AMERICA_MENOMINEE = "America/Menominee";
    public static final String AMERICA_MERIDA = "America/Merida";
    public static final String AMERICA_METLAKATLA = "America/Metlakatla";
    public static final String AMERICA_MEXICO_CITY = "America/Mexico_City";
    public static final String AMERICA_MIQUELON = "America/Miquelon";
    public static final String AMERICA_MONCTON = "America/Moncton";
    public static final String AMERICA_MONTERREY = "America/Monterrey";
    public static final String AMERICA_MONTEVIDEO = "America/Montevideo";
    public static final String AMERICA_MONTREAL = "America/Montreal";
    public static final String AMERICA_MONTSERRAT = "America/Montserrat";
    public static final String AMERICA_NASSAU = "America/Nassau";
    public static final String AMERICA_NEW_YORK = "America/New_York";
    public static final String AMERICA_NIPIGON = "America/Nipigon";
    public static final String AMERICA_NOME = "America/Nome";
    public static final String AMERICA_NORONHA = "America/Noronha";
    public static final String AMERICA_NORTH_DAKOTA_BEULAH = "America/North_Dakota/Beulah";
    public static final String AMERICA_NORTH_DAKOTA_CENTER = "America/North_Dakota/Center";
    public static final String AMERICA_NORTH_DAKOTA_NEW_SALEM = "America/North_Dakota/New_Salem";
    public static final String AMERICA_OJINAGA = "America/Ojinaga";
    public static final String AMERICA_PANAMA = "America/Panama";
    public static final String AMERICA_PANGNIRTUNG = "America/Pangnirtung";
    public static final String AMERICA_PARAMARIBO = "America/Paramaribo";
    public static final String AMERICA_PHOENIX = "America/Phoenix";
    public static final String AMERICA_PORT_AU_PRINCE = "America/Port-au-Prince";
    public static final String AMERICA_PORT_OF_SPAIN = "America/Port_of_Spain";
    public static final String AMERICA_PORTO_ACRE = "America/Porto_Acre";
    public static final String AMERICA_PORTO_VELHO = "America/Porto_Velho";
    public static final String AMERICA_PUERTO_RICO = "America/Puerto_Rico";
    public static final String AMERICA_RAINY_RIVER = "America/Rainy_River";
    public static final String AMERICA_RANKIN_INLET = "America/Rankin_Inlet";
    public static final String AMERICA_RECIFE = "America/Recife";
    public static final String AMERICA_REGINA = "America/Regina";
    public static final String AMERICA_RESOLUTE = "America/Resolute";
    public static final String AMERICA_RIO_BRANCO = "America/Rio_Branco";
    public static final String AMERICA_ROSARIO = "America/Rosario";
    public static final String AMERICA_SANTA_ISABEL = "America/Santa_Isabel";
    public static final String AMERICA_SANTAREM = "America/Santarem";
    public static final String AMERICA_SANTIAGO = "America/Santiago";
    public static final String AMERICA_SANTO_DOMINGO = "America/Santo_Domingo";
    public static final String AMERICA_SAO_PAULO = "America/Sao_Paulo";
    public static final String AMERICA_SCORESBYSUND = "America/Scoresbysund";
    public static final String AMERICA_SHIPROCK = "America/Shiprock";
    public static final String AMERICA_SITKA = "America/Sitka";
    public static final String AMERICA_ST_BARTHELEMY = "America/St_Barthelemy";
    public static final String AMERICA_ST_JOHNS = "America/St_Johns";
    public static final String AMERICA_ST_KITTS = "America/St_Kitts";
    public static final String AMERICA_ST_LUCIA = "America/St_Lucia";
    public static final String AMERICA_ST_THOMAS = "America/St_Thomas";
    public static final String AMERICA_ST_VINCENT = "America/St_Vincent";
    public static final String AMERICA_SWIFT_CURRENT = "America/Swift_Current";
    public static final String AMERICA_TEGUCIGALPA = "America/Tegucigalpa";
    public static final String AMERICA_THULE = "America/Thule";
    public static final String AMERICA_THUNDER_BAY = "America/Thunder_Bay";
    public static final String AMERICA_TIJUANA = "America/Tijuana";
    public static final String AMERICA_TORONTO = "America/Toronto";
    public static final String AMERICA_TORTOLA = "America/Tortola";
    public static final String AMERICA_VANCOUVER = "America/Vancouver";
    public static final String AMERICA_VIRGIN = "America/Virgin";
    public static final String AMERICA_WHITEHORSE = "America/Whitehorse";
    public static final String AMERICA_WINNIPEG = "America/Winnipeg";
    public static final String AMERICA_YAKUTAT = "America/Yakutat";
    public static final String AMERICA_YELLOWKNIFE = "America/Yellowknife";
    public static final String ANTARCTICA_CASEY = "Antarctica/Casey";
    public static final String ANTARCTICA_DAVIS = "Antarctica/Davis";
    public static final String ANTARCTICA_DUMONTDURVILLE = "Antarctica/DumontDUrville";
    public static final String ANTARCTICA_MACQUARIE = "Antarctica/Macquarie";
    public static final String ANTARCTICA_MAWSON = "Antarctica/Mawson";
    public static final String ANTARCTICA_MCMURDO = "Antarctica/McMurdo";
    public static final String ANTARCTICA_PALMER = "Antarctica/Palmer";
    public static final String ANTARCTICA_ROTHERA = "Antarctica/Rothera";
    public static final String ANTARCTICA_SOUTH_POLE = "Antarctica/South_Pole";
    public static final String ANTARCTICA_SYOWA = "Antarctica/Syowa";
    public static final String ANTARCTICA_TROLL = "Antarctica/Troll";
    public static final String ANTARCTICA_VOSTOK = "Antarctica/Vostok";
    public static final String ARCTIC_LONGYEARBYEN = "Arctic/Longyearbyen";
    public static final String ASIA_ADEN = "Asia/Aden";
    public static final String ASIA_ALMATY = "Asia/Almaty";
    public static final String ASIA_AMMAN = "Asia/Amman";
    public static final String ASIA_ANADYR = "Asia/Anadyr";
    public static final String ASIA_AQTAU = "Asia/Aqtau";
    public static final String ASIA_AQTOBE = "Asia/Aqtobe";
    public static final String ASIA_ASHGABAT = "Asia/Ashgabat";
    public static final String ASIA_ASHKHABAD = "Asia/Ashkhabad";
    public static final String ASIA_BAGHDAD = "Asia/Baghdad";
    public static final String ASIA_BAHRAIN = "Asia/Bahrain";
    public static final String ASIA_BAKU = "Asia/Baku";
    public static final String ASIA_BANGKOK = "Asia/Bangkok";
    public static final String ASIA_BEIRUT = "Asia/Beirut";
    public static final String ASIA_BISHKEK = "Asia/Bishkek";
    public static final String ASIA_BRUNEI = "Asia/Brunei";
    public static final String ASIA_CALCUTTA = "Asia/Calcutta";
    public static final String ASIA_CHITA = "Asia/Chita";
    public static final String ASIA_CHOIBALSAN = "Asia/Choibalsan";
    public static final String ASIA_CHONGQING = "Asia/Chongqing";
    public static final String ASIA_CHUNGKING = "Asia/Chungking";
    public static final String ASIA_COLOMBO = "Asia/Colombo";
    public static final String ASIA_DACCA = "Asia/Dacca";
    public static final String ASIA_DAMASCUS = "Asia/Damascus";
    public static final String ASIA_DHAKA = "Asia/Dhaka";
    public static final String ASIA_DILI = "Asia/Dili";
    public static final String ASIA_DUBAI = "Asia/Dubai";
    public static final String ASIA_DUSHANBE = "Asia/Dushanbe";
    public static final String ASIA_GAZA = "Asia/Gaza";
    public static final String ASIA_HARBIN = "Asia/Harbin";
    public static final String ASIA_HEBRON = "Asia/Hebron";
    public static final String ASIA_HO_CHI_MINH = "Asia/Ho_Chi_Minh";
    public static final String ASIA_HONG_KONG = "Asia/Hong_Kong";
    public static final String ASIA_HOVD = "Asia/Hovd";
    public static final String ASIA_IRKUTSK = "Asia/Irkutsk";
    public static final String ASIA_ISTANBUL = "Asia/Istanbul";
    public static final String ASIA_JAKARTA = "Asia/Jakarta";
    public static final String ASIA_JAYAPURA = "Asia/Jayapura";
    public static final String ASIA_JERUSALEM = "Asia/Jerusalem";
    public static final String ASIA_KABUL = "Asia/Kabul";
    public static final String ASIA_KAMCHATKA = "Asia/Kamchatka";
    public static final String ASIA_KARACHI = "Asia/Karachi";
    public static final String ASIA_KASHGAR = "Asia/Kashgar";
    public static final String ASIA_KATHMANDU = "Asia/Kathmandu";
    public static final String ASIA_KATMANDU = "Asia/Katmandu";
    public static final String ASIA_KHANDYGA = "Asia/Khandyga";
    public static final String ASIA_KOLKATA = "Asia/Kolkata";
    public static final String ASIA_KRASNOYARSK = "Asia/Krasnoyarsk";
    public static final String ASIA_KUALA_LUMPUR = "Asia/Kuala_Lumpur";
    public static final String ASIA_KUCHING = "Asia/Kuching";
    public static final String ASIA_KUWAIT = "Asia/Kuwait";
    public static final String ASIA_MACAO = "Asia/Macao";
    public static final String ASIA_MACAU = "Asia/Macau";
    public static final String ASIA_MAGADAN = "Asia/Magadan";
    public static final String ASIA_MAKASSAR = "Asia/Makassar";
    public static final String ASIA_MANILA = "Asia/Manila";
    public static final String ASIA_MUSCAT = "Asia/Muscat";
    public static final String ASIA_NICOSIA = "Asia/Nicosia";
    public static final String ASIA_NOVOKUZNETSK = "Asia/Novokuznetsk";
    public static final String ASIA_NOVOSIBIRSK = "Asia/Novosibirsk";
    public static final String ASIA_OMSK = "Asia/Omsk";
    public static final String ASIA_ORAL = "Asia/Oral";
    public static final String ASIA_PHNOM_PENH = "Asia/Phnom_Penh";
    public static final String ASIA_PONTIANAK = "Asia/Pontianak";
    public static final String ASIA_PYONGYANG = "Asia/Pyongyang";
    public static final String ASIA_QATAR = "Asia/Qatar";
    public static final String ASIA_QYZYLORDA = "Asia/Qyzylorda";
    public static final String ASIA_RANGOON = "Asia/Rangoon";
    public static final String ASIA_RIYADH = "Asia/Riyadh";
    public static final String ASIA_SAIGON = "Asia/Saigon";
    public static final String ASIA_SAKHALIN = "Asia/Sakhalin";
    public static final String ASIA_SAMARKAND = "Asia/Samarkand";
    public static final String ASIA_SEOUL = "Asia/Seoul";
    public static final String ASIA_SHANGHAI = "Asia/Shanghai";
    public static final String ASIA_SINGAPORE = "Asia/Singapore";
    public static final String ASIA_SREDNEKOLYMSK = "Asia/Srednekolymsk";
    public static final String ASIA_TAIPEI = "Asia/Taipei";
    public static final String ASIA_TASHKENT = "Asia/Tashkent";
    public static final String ASIA_TBILISI = "Asia/Tbilisi";
    public static final String ASIA_TEHRAN = "Asia/Tehran";
    public static final String ASIA_TEL_AVIV = "Asia/Tel_Aviv";
    public static final String ASIA_THIMBU = "Asia/Thimbu";
    public static final String ASIA_THIMPHU = "Asia/Thimphu";
    public static final String ASIA_TOKYO = "Asia/Tokyo";
    public static final String ASIA_UJUNG_PANDANG = "Asia/Ujung_Pandang";
    public static final String ASIA_ULAANBAATAR = "Asia/Ulaanbaatar";
    public static final String ASIA_ULAN_BATOR = "Asia/Ulan_Bator";
    public static final String ASIA_URUMQI = "Asia/Urumqi";
    public static final String ASIA_UST_NERA = "Asia/Ust-Nera";
    public static final String ASIA_VIENTIANE = "Asia/Vientiane";
    public static final String ASIA_VLADIVOSTOK = "Asia/Vladivostok";
    public static final String ASIA_YAKUTSK = "Asia/Yakutsk";
    public static final String ASIA_YEKATERINBURG = "Asia/Yekaterinburg";
    public static final String ASIA_YEREVAN = "Asia/Yerevan";
    public static final String ATLANTIC_AZORES = "Atlantic/Azores";
    public static final String ATLANTIC_BERMUDA = "Atlantic/Bermuda";
    public static final String ATLANTIC_CANARY = "Atlantic/Canary";
    public static final String ATLANTIC_CAPE_VERDE = "Atlantic/Cape_Verde";
    public static final String ATLANTIC_FAEROE = "Atlantic/Faeroe";
    public static final String ATLANTIC_FAROE = "Atlantic/Faroe";
    public static final String ATLANTIC_JAN_MAYEN = "Atlantic/Jan_Mayen";
    public static final String ATLANTIC_MADEIRA = "Atlantic/Madeira";
    public static final String ATLANTIC_REYKJAVIK = "Atlantic/Reykjavik";
    public static final String ATLANTIC_SOUTH_GEORGIA = "Atlantic/South_Georgia";
    public static final String ATLANTIC_ST_HELENA = "Atlantic/St_Helena";
    public static final String ATLANTIC_STANLEY = "Atlantic/Stanley";
    public static final String AUSTRALIA_ACT = "Australia/ACT";
    public static final String AUSTRALIA_ADELAIDE = "Australia/Adelaide";
    public static final String AUSTRALIA_BRISBANE = "Australia/Brisbane";
    public static final String AUSTRALIA_BROKEN_HILL = "Australia/Broken_Hill";
    public static final String AUSTRALIA_CANBERRA = "Australia/Canberra";
    public static final String AUSTRALIA_CURRIE = "Australia/Currie";
    public static final String AUSTRALIA_DARWIN = "Australia/Darwin";
    public static final String AUSTRALIA_EUCLA = "Australia/Eucla";
    public static final String AUSTRALIA_HOBART = "Australia/Hobart";
    public static final String AUSTRALIA_LHI = "Australia/LHI";
    public static final String AUSTRALIA_LINDEMAN = "Australia/Lindeman";
    public static final String AUSTRALIA_LORD_HOWE = "Australia/Lord_Howe";
    public static final String AUSTRALIA_MELBOURNE = "Australia/Melbourne";
    public static final String AUSTRALIA_NSW = "Australia/NSW";
    public static final String AUSTRALIA_NORTH = "Australia/North";
    public static final String AUSTRALIA_PERTH = "Australia/Perth";
    public static final String AUSTRALIA_QUEENSLAND = "Australia/Queensland";
    public static final String AUSTRALIA_SOUTH = "Australia/South";
    public static final String AUSTRALIA_SYDNEY = "Australia/Sydney";
    public static final String AUSTRALIA_TASMANIA = "Australia/Tasmania";
    public static final String AUSTRALIA_VICTORIA = "Australia/Victoria";
    public static final String AUSTRALIA_WEST = "Australia/West";
    public static final String AUSTRALIA_YANCOWINNA = "Australia/Yancowinna";
    public static final String BRAZIL_ACRE = "Brazil/Acre";
    public static final String BRAZIL_DENORONHA = "Brazil/DeNoronha";
    public static final String BRAZIL_EAST = "Brazil/East";
    public static final String BRAZIL_WEST = "Brazil/West";
    public static final String CET = "CET";
    public static final String CST6CDT = "CST6CDT";
    public static final String CANADA_ATLANTIC = "Canada/Atlantic";
    public static final String CANADA_CENTRAL = "Canada/Central";
    public static final String CANADA_EAST_SASKATCHEWAN = "Canada/East-Saskatchewan";
    public static final String CANADA_EASTERN = "Canada/Eastern";
    public static final String CANADA_MOUNTAIN = "Canada/Mountain";
    public static final String CANADA_NEWFOUNDLAND = "Canada/Newfoundland";
    public static final String CANADA_PACIFIC = "Canada/Pacific";
    public static final String CANADA_SASKATCHEWAN = "Canada/Saskatchewan";
    public static final String CANADA_YUKON = "Canada/Yukon";
    public static final String CHILE_CONTINENTAL = "Chile/Continental";
    public static final String CHILE_EASTERISLAND = "Chile/EasterIsland";
    public static final String CUBA = "Cuba";
    public static final String EET = "EET";
    public static final String EST = "EST";
    public static final String EST5EDT = "EST5EDT";
    public static final String EGYPT = "Egypt";
    public static final String EIRE = "Eire";
    public static final String ETC_GMT = "Etc/GMT";
    public static final String ETC_GMT_ADD_0 = "Etc/GMT+0";
    public static final String ETC_GMT_ADD_1 = "Etc/GMT+1";
    public static final String ETC_GMT_ADD_10 = "Etc/GMT+10";
    public static final String ETC_GMT_ADD_11 = "Etc/GMT+11";
    public static final String ETC_GMT_ADD_12 = "Etc/GMT+12";
    public static final String ETC_GMT_ADD_2 = "Etc/GMT+2";
    public static final String ETC_GMT_ADD_3 = "Etc/GMT+3";
    public static final String ETC_GMT_ADD_4 = "Etc/GMT+4";
    public static final String ETC_GMT_ADD_5 = "Etc/GMT+5";
    public static final String ETC_GMT_ADD_6 = "Etc/GMT+6";
    public static final String ETC_GMT_ADD_7 = "Etc/GMT+7";
    public static final String ETC_GMT_ADD_8 = "Etc/GMT+8";
    public static final String ETC_GMT_ADD_9 = "Etc/GMT+9";
    public static final String ETC_GMT_0 = "Etc/GMT-0";
    public static final String ETC_GMT_1 = "Etc/GMT-1";
    public static final String ETC_GMT_10 = "Etc/GMT-10";
    public static final String ETC_GMT_11 = "Etc/GMT-11";
    public static final String ETC_GMT_12 = "Etc/GMT-12";
    public static final String ETC_GMT_13 = "Etc/GMT-13";
    public static final String ETC_GMT_14 = "Etc/GMT-14";
    public static final String ETC_GMT_2 = "Etc/GMT-2";
    public static final String ETC_GMT_3 = "Etc/GMT-3";
    public static final String ETC_GMT_4 = "Etc/GMT-4";
    public static final String ETC_GMT_5 = "Etc/GMT-5";
    public static final String ETC_GMT_6 = "Etc/GMT-6";
    public static final String ETC_GMT_7 = "Etc/GMT-7";
    public static final String ETC_GMT_8 = "Etc/GMT-8";
    public static final String ETC_GMT_9 = "Etc/GMT-9";
    public static final String ETC_GMT0 = "Etc/GMT0";
    public static final String ETC_GREENWICH = "Etc/Greenwich";
    public static final String ETC_UCT = "Etc/UCT";
    public static final String ETC_UTC = "Etc/UTC";
    public static final String ETC_UNIVERSAL = "Etc/Universal";
    public static final String ETC_ZULU = "Etc/Zulu";
    public static final String EUROPE_AMSTERDAM = "Europe/Amsterdam";
    public static final String EUROPE_ANDORRA = "Europe/Andorra";
    public static final String EUROPE_ATHENS = "Europe/Athens";
    public static final String EUROPE_BELFAST = "Europe/Belfast";
    public static final String EUROPE_BELGRADE = "Europe/Belgrade";
    public static final String EUROPE_BERLIN = "Europe/Berlin";
    public static final String EUROPE_BRATISLAVA = "Europe/Bratislava";
    public static final String EUROPE_BRUSSELS = "Europe/Brussels";
    public static final String EUROPE_BUCHAREST = "Europe/Bucharest";
    public static final String EUROPE_BUDAPEST = "Europe/Budapest";
    public static final String EUROPE_BUSINGEN = "Europe/Busingen";
    public static final String EUROPE_CHISINAU = "Europe/Chisinau";
    public static final String EUROPE_COPENHAGEN = "Europe/Copenhagen";
    public static final String EUROPE_DUBLIN = "Europe/Dublin";
    public static final String EUROPE_GIBRALTAR = "Europe/Gibraltar";
    public static final String EUROPE_GUERNSEY = "Europe/Guernsey";
    public static final String EUROPE_HELSINKI = "Europe/Helsinki";
    public static final String EUROPE_ISLE_OF_MAN = "Europe/Isle_of_Man";
    public static final String EUROPE_ISTANBUL = "Europe/Istanbul";
    public static final String EUROPE_JERSEY = "Europe/Jersey";
    public static final String EUROPE_KALININGRAD = "Europe/Kaliningrad";
    public static final String EUROPE_KIEV = "Europe/Kiev";
    public static final String EUROPE_LISBON = "Europe/Lisbon";
    public static final String EUROPE_LJUBLJANA = "Europe/Ljubljana";
    public static final String EUROPE_LONDON = "Europe/London";
    public static final String EUROPE_LUXEMBOURG = "Europe/Luxembourg";
    public static final String EUROPE_MADRID = "Europe/Madrid";
    public static final String EUROPE_MALTA = "Europe/Malta";
    public static final String EUROPE_MARIEHAMN = "Europe/Mariehamn";
    public static final String EUROPE_MINSK = "Europe/Minsk";
    public static final String EUROPE_MONACO = "Europe/Monaco";
    public static final String EUROPE_MOSCOW = "Europe/Moscow";
    public static final String EUROPE_NICOSIA = "Europe/Nicosia";
    public static final String EUROPE_OSLO = "Europe/Oslo";
    public static final String EUROPE_PARIS = "Europe/Paris";
    public static final String EUROPE_PODGORICA = "Europe/Podgorica";
    public static final String EUROPE_PRAGUE = "Europe/Prague";
    public static final String EUROPE_RIGA = "Europe/Riga";
    public static final String EUROPE_ROME = "Europe/Rome";
    public static final String EUROPE_SAMARA = "Europe/Samara";
    public static final String EUROPE_SAN_MARINO = "Europe/San_Marino";
    public static final String EUROPE_SARAJEVO = "Europe/Sarajevo";
    public static final String EUROPE_SIMFEROPOL = "Europe/Simferopol";
    public static final String EUROPE_SKOPJE = "Europe/Skopje";
    public static final String EUROPE_SOFIA = "Europe/Sofia";
    public static final String EUROPE_STOCKHOLM = "Europe/Stockholm";
    public static final String EUROPE_TALLINN = "Europe/Tallinn";
    public static final String EUROPE_TIRANE = "Europe/Tirane";
    public static final String EUROPE_TIRASPOL = "Europe/Tiraspol";
    public static final String EUROPE_UZHGOROD = "Europe/Uzhgorod";
    public static final String EUROPE_VADUZ = "Europe/Vaduz";
    public static final String EUROPE_VATICAN = "Europe/Vatican";
    public static final String EUROPE_VIENNA = "Europe/Vienna";
    public static final String EUROPE_VILNIUS = "Europe/Vilnius";
    public static final String EUROPE_VOLGOGRAD = "Europe/Volgograd";
    public static final String EUROPE_WARSAW = "Europe/Warsaw";
    public static final String EUROPE_ZAGREB = "Europe/Zagreb";
    public static final String EUROPE_ZAPOROZHYE = "Europe/Zaporozhye";
    public static final String EUROPE_ZURICH = "Europe/Zurich";
    public static final String GB = "GB";
    public static final String GB_EIRE = "GB-Eire";
    public static final String GMT = "GMT";
    public static final String GMT_ADD_0 = "GMT+0";
    public static final String GMT_0 = "GMT-0";
    public static final String GMT0 = "GMT0";
    public static final String GREENWICH = "Greenwich";
    public static final String HST = "HST";
    public static final String HONGKONG = "Hongkong";
    public static final String ICELAND = "Iceland";
    public static final String INDIAN_ANTANANARIVO = "Indian/Antananarivo";
    public static final String INDIAN_CHAGOS = "Indian/Chagos";
    public static final String INDIAN_CHRISTMAS = "Indian/Christmas";
    public static final String INDIAN_COCOS = "Indian/Cocos";
    public static final String INDIAN_COMORO = "Indian/Comoro";
    public static final String INDIAN_KERGUELEN = "Indian/Kerguelen";
    public static final String INDIAN_MAHE = "Indian/Mahe";
    public static final String INDIAN_MALDIVES = "Indian/Maldives";
    public static final String INDIAN_MAURITIUS = "Indian/Mauritius";
    public static final String INDIAN_MAYOTTE = "Indian/Mayotte";
    public static final String INDIAN_REUNION = "Indian/Reunion";
    public static final String IRAN = "Iran";
    public static final String ISRAEL = "Israel";
    public static final String JAMAICA = "Jamaica";
    public static final String JAPAN = "Japan";
    public static final String KWAJALEIN = "Kwajalein";
    public static final String LIBYA = "Libya";
    public static final String MET = "MET";
    public static final String MST = "MST";
    public static final String MST7MDT = "MST7MDT";
    public static final String MEXICO_BAJANORTE = "Mexico/BajaNorte";
    public static final String MEXICO_BAJASUR = "Mexico/BajaSur";
    public static final String MEXICO_GENERAL = "Mexico/General";
    public static final String NZ = "NZ";
    public static final String NZ_CHAT = "NZ-CHAT";
    public static final String NAVAJO = "Navajo";
    public static final String PACIFIC_APIA = "Pacific/Apia";
    public static final String PACIFIC_AUCKLAND = "Pacific/Auckland";
    public static final String PACIFIC_BOUGAINVILLE = "Pacific/Bougainville";
    public static final String PACIFIC_CHATHAM = "Pacific/Chatham";
    public static final String PACIFIC_CHUUK = "Pacific/Chuuk";
    public static final String PACIFIC_EASTER = "Pacific/Easter";
    public static final String PACIFIC_EFATE = "Pacific/Efate";
    public static final String PACIFIC_ENDERBURY = "Pacific/Enderbury";
    public static final String PACIFIC_FAKAOFO = "Pacific/Fakaofo";
    public static final String PACIFIC_FIJI = "Pacific/Fiji";
    public static final String PACIFIC_FUNAFUTI = "Pacific/Funafuti";
    public static final String PACIFIC_GALAPAGOS = "Pacific/Galapagos";
    public static final String PACIFIC_GAMBIER = "Pacific/Gambier";
    public static final String PACIFIC_GUADALCANAL = "Pacific/Guadalcanal";
    public static final String PACIFIC_GUAM = "Pacific/Guam";
    public static final String PACIFIC_HONOLULU = "Pacific/Honolulu";
    public static final String PACIFIC_JOHNSTON = "Pacific/Johnston";
    public static final String PACIFIC_KIRITIMATI = "Pacific/Kiritimati";
    public static final String PACIFIC_KOSRAE = "Pacific/Kosrae";
    public static final String PACIFIC_KWAJALEIN = "Pacific/Kwajalein";
    public static final String PACIFIC_MAJURO = "Pacific/Majuro";
    public static final String PACIFIC_MARQUESAS = "Pacific/Marquesas";
    public static final String PACIFIC_MIDWAY = "Pacific/Midway";
    public static final String PACIFIC_NAURU = "Pacific/Nauru";
    public static final String PACIFIC_NIUE = "Pacific/Niue";
    public static final String PACIFIC_NORFOLK = "Pacific/Norfolk";
    public static final String PACIFIC_NOUMEA = "Pacific/Noumea";
    public static final String PACIFIC_PAGO_PAGO = "Pacific/Pago_Pago";
    public static final String PACIFIC_PALAU = "Pacific/Palau";
    public static final String PACIFIC_PITCAIRN = "Pacific/Pitcairn";
    public static final String PACIFIC_POHNPEI = "Pacific/Pohnpei";
    public static final String PACIFIC_PONAPE = "Pacific/Ponape";
    public static final String PACIFIC_PORT_MORESBY = "Pacific/Port_Moresby";
    public static final String PACIFIC_RAROTONGA = "Pacific/Rarotonga";
    public static final String PACIFIC_SAIPAN = "Pacific/Saipan";
    public static final String PACIFIC_SAMOA = "Pacific/Samoa";
    public static final String PACIFIC_TAHITI = "Pacific/Tahiti";
    public static final String PACIFIC_TARAWA = "Pacific/Tarawa";
    public static final String PACIFIC_TONGATAPU = "Pacific/Tongatapu";
    public static final String PACIFIC_TRUK = "Pacific/Truk";
    public static final String PACIFIC_WAKE = "Pacific/Wake";
    public static final String PACIFIC_WALLIS = "Pacific/Wallis";
    public static final String PACIFIC_YAP = "Pacific/Yap";
    public static final String POLAND = "Poland";
    public static final String PORTUGAL = "Portugal";
    public static final String DEFAULT_TIMEZONE = "SYSTEM";


    /**
     * 时区设置白，用户可设置为下列时区
     * FIXME: 设置为可配置化的代码
     */
    private static final ArrayList<String> AVAILABLE_TIMEZONE_ID = new ArrayList<String>() {
        {
            add(AFRICA_ABIDJAN);
            add(AFRICA_ACCRA);
            add(AFRICA_ADDIS_ABABA);
            add(AFRICA_ALGIERS);
            add(AFRICA_ASMARA);
            add(AFRICA_ASMERA);
            add(AFRICA_BAMAKO);
            add(AFRICA_BANGUI);
            add(AFRICA_BANJUL);
            add(AFRICA_BISSAU);
            add(AFRICA_BLANTYRE);
            add(AFRICA_BRAZZAVILLE);
            add(AFRICA_BUJUMBURA);
            add(AFRICA_CAIRO);
            add(AFRICA_CASABLANCA);
            add(AFRICA_CEUTA);
            add(AFRICA_CONAKRY);
            add(AFRICA_DAKAR);
            add(AFRICA_DAR_ES_SALAAM);
            add(AFRICA_DJIBOUTI);
            add(AFRICA_DOUALA);
            add(AFRICA_EL_AAIUN);
            add(AFRICA_FREETOWN);
            add(AFRICA_GABORONE);
            add(AFRICA_HARARE);
            add(AFRICA_JOHANNESBURG);
            add(AFRICA_JUBA);
            add(AFRICA_KAMPALA);
            add(AFRICA_KHARTOUM);
            add(AFRICA_KIGALI);
            add(AFRICA_KINSHASA);
            add(AFRICA_LAGOS);
            add(AFRICA_LIBREVILLE);
            add(AFRICA_LOME);
            add(AFRICA_LUANDA);
            add(AFRICA_LUBUMBASHI);
            add(AFRICA_LUSAKA);
            add(AFRICA_MALABO);
            add(AFRICA_MAPUTO);
            add(AFRICA_MASERU);
            add(AFRICA_MBABANE);
            add(AFRICA_MOGADISHU);
            add(AFRICA_MONROVIA);
            add(AFRICA_NAIROBI);
            add(AFRICA_NDJAMENA);
            add(AFRICA_NIAMEY);
            add(AFRICA_NOUAKCHOTT);
            add(AFRICA_OUAGADOUGOU);
            add(AFRICA_PORTO_NOVO);
            add(AFRICA_SAO_TOME);
            add(AFRICA_TIMBUKTU);
            add(AFRICA_TRIPOLI);
            add(AFRICA_TUNIS);
            add(AFRICA_WINDHOEK);
            add(AMERICA_ADAK);
            add(AMERICA_ANCHORAGE);
            add(AMERICA_ANGUILLA);
            add(AMERICA_ANTIGUA);
            add(AMERICA_ARAGUAINA);
            add(AMERICA_ARGENTINA_BUENOS_AIRES);
            add(AMERICA_ARGENTINA_CATAMARCA);
            add(AMERICA_ARGENTINA_COMODRIVADAVIA);
            add(AMERICA_ARGENTINA_CORDOBA);
            add(AMERICA_ARGENTINA_JUJUY);
            add(AMERICA_ARGENTINA_LA_RIOJA);
            add(AMERICA_ARGENTINA_MENDOZA);
            add(AMERICA_ARGENTINA_RIO_GALLEGOS);
            add(AMERICA_ARGENTINA_SALTA);
            add(AMERICA_ARGENTINA_SAN_JUAN);
            add(AMERICA_ARGENTINA_SAN_LUIS);
            add(AMERICA_ARGENTINA_TUCUMAN);
            add(AMERICA_ARGENTINA_USHUAIA);
            add(AMERICA_ARUBA);
            add(AMERICA_ASUNCION);
            add(AMERICA_ATIKOKAN);
            add(AMERICA_ATKA);
            add(AMERICA_BAHIA);
            add(AMERICA_BAHIA_BANDERAS);
            add(AMERICA_BARBADOS);
            add(AMERICA_BELEM);
            add(AMERICA_BELIZE);
            add(AMERICA_BLANC_SABLON);
            add(AMERICA_BOA_VISTA);
            add(AMERICA_BOGOTA);
            add(AMERICA_BOISE);
            add(AMERICA_BUENOS_AIRES);
            add(AMERICA_CAMBRIDGE_BAY);
            add(AMERICA_CAMPO_GRANDE);
            add(AMERICA_CANCUN);
            add(AMERICA_CARACAS);
            add(AMERICA_CATAMARCA);
            add(AMERICA_CAYENNE);
            add(AMERICA_CAYMAN);
            add(AMERICA_CHICAGO);
            add(AMERICA_CHIHUAHUA);
            add(AMERICA_CORAL_HARBOUR);
            add(AMERICA_CORDOBA);
            add(AMERICA_COSTA_RICA);
            add(AMERICA_CRESTON);
            add(AMERICA_CUIABA);
            add(AMERICA_CURACAO);
            add(AMERICA_DANMARKSHAVN);
            add(AMERICA_DAWSON);
            add(AMERICA_DAWSON_CREEK);
            add(AMERICA_DENVER);
            add(AMERICA_DETROIT);
            add(AMERICA_DOMINICA);
            add(AMERICA_EDMONTON);
            add(AMERICA_EIRUNEPE);
            add(AMERICA_EL_SALVADOR);
            add(AMERICA_ENSENADA);
            add(AMERICA_FORT_NELSON);
            add(AMERICA_FORT_WAYNE);
            add(AMERICA_FORTALEZA);
            add(AMERICA_GLACE_BAY);
            add(AMERICA_GODTHAB);
            add(AMERICA_GOOSE_BAY);
            add(AMERICA_GRAND_TURK);
            add(AMERICA_GRENADA);
            add(AMERICA_GUADELOUPE);
            add(AMERICA_GUATEMALA);
            add(AMERICA_GUAYAQUIL);
            add(AMERICA_GUYANA);
            add(AMERICA_HALIFAX);
            add(AMERICA_HAVANA);
            add(AMERICA_HERMOSILLO);
            add(AMERICA_INDIANA_INDIANAPOLIS);
            add(AMERICA_INDIANA_KNOX);
            add(AMERICA_INDIANA_MARENGO);
            add(AMERICA_INDIANA_PETERSBURG);
            add(AMERICA_INDIANA_TELL_CITY);
            add(AMERICA_INDIANA_VEVAY);
            add(AMERICA_INDIANA_VINCENNES);
            add(AMERICA_INDIANA_WINAMAC);
            add(AMERICA_INDIANAPOLIS);
            add(AMERICA_INUVIK);
            add(AMERICA_IQALUIT);
            add(AMERICA_JAMAICA);
            add(AMERICA_JUJUY);
            add(AMERICA_JUNEAU);
            add(AMERICA_KENTUCKY_LOUISVILLE);
            add(AMERICA_KENTUCKY_MONTICELLO);
            add(AMERICA_KNOX_IN);
            add(AMERICA_KRALENDIJK);
            add(AMERICA_LA_PAZ);
            add(AMERICA_LIMA);
            add(AMERICA_LOS_ANGELES);
            add(AMERICA_LOUISVILLE);
            add(AMERICA_LOWER_PRINCES);
            add(AMERICA_MACEIO);
            add(AMERICA_MANAGUA);
            add(AMERICA_MANAUS);
            add(AMERICA_MARIGOT);
            add(AMERICA_MARTINIQUE);
            add(AMERICA_MATAMOROS);
            add(AMERICA_MAZATLAN);
            add(AMERICA_MENDOZA);
            add(AMERICA_MENOMINEE);
            add(AMERICA_MERIDA);
            add(AMERICA_METLAKATLA);
            add(AMERICA_MEXICO_CITY);
            add(AMERICA_MIQUELON);
            add(AMERICA_MONCTON);
            add(AMERICA_MONTERREY);
            add(AMERICA_MONTEVIDEO);
            add(AMERICA_MONTREAL);
            add(AMERICA_MONTSERRAT);
            add(AMERICA_NASSAU);
            add(AMERICA_NEW_YORK);
            add(AMERICA_NIPIGON);
            add(AMERICA_NOME);
            add(AMERICA_NORONHA);
            add(AMERICA_NORTH_DAKOTA_BEULAH);
            add(AMERICA_NORTH_DAKOTA_CENTER);
            add(AMERICA_NORTH_DAKOTA_NEW_SALEM);
            add(AMERICA_OJINAGA);
            add(AMERICA_PANAMA);
            add(AMERICA_PANGNIRTUNG);
            add(AMERICA_PARAMARIBO);
            add(AMERICA_PHOENIX);
            add(AMERICA_PORT_AU_PRINCE);
            add(AMERICA_PORT_OF_SPAIN);
            add(AMERICA_PORTO_ACRE);
            add(AMERICA_PORTO_VELHO);
            add(AMERICA_PUERTO_RICO);
            add(AMERICA_RAINY_RIVER);
            add(AMERICA_RANKIN_INLET);
            add(AMERICA_RECIFE);
            add(AMERICA_REGINA);
            add(AMERICA_RESOLUTE);
            add(AMERICA_RIO_BRANCO);
            add(AMERICA_ROSARIO);
            add(AMERICA_SANTA_ISABEL);
            add(AMERICA_SANTAREM);
            add(AMERICA_SANTIAGO);
            add(AMERICA_SANTO_DOMINGO);
            add(AMERICA_SAO_PAULO);
            add(AMERICA_SCORESBYSUND);
            add(AMERICA_SHIPROCK);
            add(AMERICA_SITKA);
            add(AMERICA_ST_BARTHELEMY);
            add(AMERICA_ST_JOHNS);
            add(AMERICA_ST_KITTS);
            add(AMERICA_ST_LUCIA);
            add(AMERICA_ST_THOMAS);
            add(AMERICA_ST_VINCENT);
            add(AMERICA_SWIFT_CURRENT);
            add(AMERICA_TEGUCIGALPA);
            add(AMERICA_THULE);
            add(AMERICA_THUNDER_BAY);
            add(AMERICA_TIJUANA);
            add(AMERICA_TORONTO);
            add(AMERICA_TORTOLA);
            add(AMERICA_VANCOUVER);
            add(AMERICA_VIRGIN);
            add(AMERICA_WHITEHORSE);
            add(AMERICA_WINNIPEG);
            add(AMERICA_YAKUTAT);
            add(AMERICA_YELLOWKNIFE);
            add(ANTARCTICA_CASEY);
            add(ANTARCTICA_DAVIS);
            add(ANTARCTICA_DUMONTDURVILLE);
            add(ANTARCTICA_MACQUARIE);
            add(ANTARCTICA_MAWSON);
            add(ANTARCTICA_MCMURDO);
            add(ANTARCTICA_PALMER);
            add(ANTARCTICA_ROTHERA);
            add(ANTARCTICA_SOUTH_POLE);
            add(ANTARCTICA_SYOWA);
            add(ANTARCTICA_TROLL);
            add(ANTARCTICA_VOSTOK);
            add(ARCTIC_LONGYEARBYEN);
            add(ASIA_ADEN);
            add(ASIA_ALMATY);
            add(ASIA_AMMAN);
            add(ASIA_ANADYR);
            add(ASIA_AQTAU);
            add(ASIA_AQTOBE);
            add(ASIA_ASHGABAT);
            add(ASIA_ASHKHABAD);
            add(ASIA_BAGHDAD);
            add(ASIA_BAHRAIN);
            add(ASIA_BAKU);
            add(ASIA_BANGKOK);
            add(ASIA_BEIRUT);
            add(ASIA_BISHKEK);
            add(ASIA_BRUNEI);
            add(ASIA_CALCUTTA);
            add(ASIA_CHITA);
            add(ASIA_CHOIBALSAN);
            add(ASIA_CHONGQING);
            add(ASIA_CHUNGKING);
            add(ASIA_COLOMBO);
            add(ASIA_DACCA);
            add(ASIA_DAMASCUS);
            add(ASIA_DHAKA);
            add(ASIA_DILI);
            add(ASIA_DUBAI);
            add(ASIA_DUSHANBE);
            add(ASIA_GAZA);
            add(ASIA_HARBIN);
            add(ASIA_HEBRON);
            add(ASIA_HO_CHI_MINH);
            add(ASIA_HONG_KONG);
            add(ASIA_HOVD);
            add(ASIA_IRKUTSK);
            add(ASIA_ISTANBUL);
            add(ASIA_JAKARTA);
            add(ASIA_JAYAPURA);
            add(ASIA_JERUSALEM);
            add(ASIA_KABUL);
            add(ASIA_KAMCHATKA);
            add(ASIA_KARACHI);
            add(ASIA_KASHGAR);
            add(ASIA_KATHMANDU);
            add(ASIA_KATMANDU);
            add(ASIA_KHANDYGA);
            add(ASIA_KOLKATA);
            add(ASIA_KRASNOYARSK);
            add(ASIA_KUALA_LUMPUR);
            add(ASIA_KUCHING);
            add(ASIA_KUWAIT);
            add(ASIA_MACAO);
            add(ASIA_MACAU);
            add(ASIA_MAGADAN);
            add(ASIA_MAKASSAR);
            add(ASIA_MANILA);
            add(ASIA_MUSCAT);
            add(ASIA_NICOSIA);
            add(ASIA_NOVOKUZNETSK);
            add(ASIA_NOVOSIBIRSK);
            add(ASIA_OMSK);
            add(ASIA_ORAL);
            add(ASIA_PHNOM_PENH);
            add(ASIA_PONTIANAK);
            add(ASIA_PYONGYANG);
            add(ASIA_QATAR);
            add(ASIA_QYZYLORDA);
            add(ASIA_RANGOON);
            add(ASIA_RIYADH);
            add(ASIA_SAIGON);
            add(ASIA_SAKHALIN);
            add(ASIA_SAMARKAND);
            add(ASIA_SEOUL);
            add(ASIA_SHANGHAI);
            add(ASIA_SINGAPORE);
            add(ASIA_SREDNEKOLYMSK);
            add(ASIA_TAIPEI);
            add(ASIA_TASHKENT);
            add(ASIA_TBILISI);
            add(ASIA_TEHRAN);
            add(ASIA_TEL_AVIV);
            add(ASIA_THIMBU);
            add(ASIA_THIMPHU);
            add(ASIA_TOKYO);
            add(ASIA_UJUNG_PANDANG);
            add(ASIA_ULAANBAATAR);
            add(ASIA_ULAN_BATOR);
            add(ASIA_URUMQI);
            add(ASIA_UST_NERA);
            add(ASIA_VIENTIANE);
            add(ASIA_VLADIVOSTOK);
            add(ASIA_YAKUTSK);
            add(ASIA_YEKATERINBURG);
            add(ASIA_YEREVAN);
            add(ATLANTIC_AZORES);
            add(ATLANTIC_BERMUDA);
            add(ATLANTIC_CANARY);
            add(ATLANTIC_CAPE_VERDE);
            add(ATLANTIC_FAEROE);
            add(ATLANTIC_FAROE);
            add(ATLANTIC_JAN_MAYEN);
            add(ATLANTIC_MADEIRA);
            add(ATLANTIC_REYKJAVIK);
            add(ATLANTIC_SOUTH_GEORGIA);
            add(ATLANTIC_ST_HELENA);
            add(ATLANTIC_STANLEY);
            add(AUSTRALIA_ACT);
            add(AUSTRALIA_ADELAIDE);
            add(AUSTRALIA_BRISBANE);
            add(AUSTRALIA_BROKEN_HILL);
            add(AUSTRALIA_CANBERRA);
            add(AUSTRALIA_CURRIE);
            add(AUSTRALIA_DARWIN);
            add(AUSTRALIA_EUCLA);
            add(AUSTRALIA_HOBART);
            add(AUSTRALIA_LHI);
            add(AUSTRALIA_LINDEMAN);
            add(AUSTRALIA_LORD_HOWE);
            add(AUSTRALIA_MELBOURNE);
            add(AUSTRALIA_NSW);
            add(AUSTRALIA_NORTH);
            add(AUSTRALIA_PERTH);
            add(AUSTRALIA_QUEENSLAND);
            add(AUSTRALIA_SOUTH);
            add(AUSTRALIA_SYDNEY);
            add(AUSTRALIA_TASMANIA);
            add(AUSTRALIA_VICTORIA);
            add(AUSTRALIA_WEST);
            add(AUSTRALIA_YANCOWINNA);
            add(BRAZIL_ACRE);
            add(BRAZIL_DENORONHA);
            add(BRAZIL_EAST);
            add(BRAZIL_WEST);
            add(CET);
            add(CST6CDT);
            add(CANADA_ATLANTIC);
            add(CANADA_CENTRAL);
            add(CANADA_EAST_SASKATCHEWAN);
            add(CANADA_EASTERN);
            add(CANADA_MOUNTAIN);
            add(CANADA_NEWFOUNDLAND);
            add(CANADA_PACIFIC);
            add(CANADA_SASKATCHEWAN);
            add(CANADA_YUKON);
            add(CHILE_CONTINENTAL);
            add(CHILE_EASTERISLAND);
            add(CUBA);
            add(EET);
            add(EST);
            add(EST5EDT);
            add(EGYPT);
            add(EIRE);
            add(ETC_GMT);
            add(ETC_GMT_ADD_0);
            add(ETC_GMT_ADD_1);
            add(ETC_GMT_ADD_10);
            add(ETC_GMT_ADD_11);
            add(ETC_GMT_ADD_12);
            add(ETC_GMT_ADD_2);
            add(ETC_GMT_ADD_3);
            add(ETC_GMT_ADD_4);
            add(ETC_GMT_ADD_5);
            add(ETC_GMT_ADD_6);
            add(ETC_GMT_ADD_7);
            add(ETC_GMT_ADD_8);
            add(ETC_GMT_ADD_9);
            add(ETC_GMT_0);
            add(ETC_GMT_1);
            add(ETC_GMT_10);
            add(ETC_GMT_11);
            add(ETC_GMT_12);
            add(ETC_GMT_13);
            add(ETC_GMT_14);
            add(ETC_GMT_2);
            add(ETC_GMT_3);
            add(ETC_GMT_4);
            add(ETC_GMT_5);
            add(ETC_GMT_6);
            add(ETC_GMT_7);
            add(ETC_GMT_8);
            add(ETC_GMT_9);
            add(ETC_GMT0);
            add(ETC_GREENWICH);
            add(ETC_UCT);
            add(ETC_UTC);
            add(ETC_UNIVERSAL);
            add(ETC_ZULU);
            add(EUROPE_AMSTERDAM);
            add(EUROPE_ANDORRA);
            add(EUROPE_ATHENS);
            add(EUROPE_BELFAST);
            add(EUROPE_BELGRADE);
            add(EUROPE_BERLIN);
            add(EUROPE_BRATISLAVA);
            add(EUROPE_BRUSSELS);
            add(EUROPE_BUCHAREST);
            add(EUROPE_BUDAPEST);
            add(EUROPE_BUSINGEN);
            add(EUROPE_CHISINAU);
            add(EUROPE_COPENHAGEN);
            add(EUROPE_DUBLIN);
            add(EUROPE_GIBRALTAR);
            add(EUROPE_GUERNSEY);
            add(EUROPE_HELSINKI);
            add(EUROPE_ISLE_OF_MAN);
            add(EUROPE_ISTANBUL);
            add(EUROPE_JERSEY);
            add(EUROPE_KALININGRAD);
            add(EUROPE_KIEV);
            add(EUROPE_LISBON);
            add(EUROPE_LJUBLJANA);
            add(EUROPE_LONDON);
            add(EUROPE_LUXEMBOURG);
            add(EUROPE_MADRID);
            add(EUROPE_MALTA);
            add(EUROPE_MARIEHAMN);
            add(EUROPE_MINSK);
            add(EUROPE_MONACO);
            add(EUROPE_MOSCOW);
            add(EUROPE_NICOSIA);
            add(EUROPE_OSLO);
            add(EUROPE_PARIS);
            add(EUROPE_PODGORICA);
            add(EUROPE_PRAGUE);
            add(EUROPE_RIGA);
            add(EUROPE_ROME);
            add(EUROPE_SAMARA);
            add(EUROPE_SAN_MARINO);
            add(EUROPE_SARAJEVO);
            add(EUROPE_SIMFEROPOL);
            add(EUROPE_SKOPJE);
            add(EUROPE_SOFIA);
            add(EUROPE_STOCKHOLM);
            add(EUROPE_TALLINN);
            add(EUROPE_TIRANE);
            add(EUROPE_TIRASPOL);
            add(EUROPE_UZHGOROD);
            add(EUROPE_VADUZ);
            add(EUROPE_VATICAN);
            add(EUROPE_VIENNA);
            add(EUROPE_VILNIUS);
            add(EUROPE_VOLGOGRAD);
            add(EUROPE_WARSAW);
            add(EUROPE_ZAGREB);
            add(EUROPE_ZAPOROZHYE);
            add(EUROPE_ZURICH);
            add(GB);
            add(GB_EIRE);
            add(GMT);
            add(GMT_ADD_0);
            add(GMT_0);
            add(GMT0);
            add(GREENWICH);
            add(HST);
            add(HONGKONG);
            add(ICELAND);
            add(INDIAN_ANTANANARIVO);
            add(INDIAN_CHAGOS);
            add(INDIAN_CHRISTMAS);
            add(INDIAN_COCOS);
            add(INDIAN_COMORO);
            add(INDIAN_KERGUELEN);
            add(INDIAN_MAHE);
            add(INDIAN_MALDIVES);
            add(INDIAN_MAURITIUS);
            add(INDIAN_MAYOTTE);
            add(INDIAN_REUNION);
            add(IRAN);
            add(ISRAEL);
            add(JAMAICA);
            add(JAPAN);
            add(KWAJALEIN);
            add(LIBYA);
            add(MET);
            add(MST);
            add(MST7MDT);
            add(MEXICO_BAJANORTE);
            add(MEXICO_BAJASUR);
            add(MEXICO_GENERAL);
            add(NZ);
            add(NZ_CHAT);
            add(NAVAJO);
            add(PACIFIC_APIA);
            add(PACIFIC_AUCKLAND);
            add(PACIFIC_BOUGAINVILLE);
            add(PACIFIC_CHATHAM);
            add(PACIFIC_CHUUK);
            add(PACIFIC_EASTER);
            add(PACIFIC_EFATE);
            add(PACIFIC_ENDERBURY);
            add(PACIFIC_FAKAOFO);
            add(PACIFIC_FIJI);
            add(PACIFIC_FUNAFUTI);
            add(PACIFIC_GALAPAGOS);
            add(PACIFIC_GAMBIER);
            add(PACIFIC_GUADALCANAL);
            add(PACIFIC_GUAM);
            add(PACIFIC_HONOLULU);
            add(PACIFIC_JOHNSTON);
            add(PACIFIC_KIRITIMATI);
            add(PACIFIC_KOSRAE);
            add(PACIFIC_KWAJALEIN);
            add(PACIFIC_MAJURO);
            add(PACIFIC_MARQUESAS);
            add(PACIFIC_MIDWAY);
            add(PACIFIC_NAURU);
            add(PACIFIC_NIUE);
            add(PACIFIC_NORFOLK);
            add(PACIFIC_NOUMEA);
            add(PACIFIC_PAGO_PAGO);
            add(PACIFIC_PALAU);
            add(PACIFIC_PITCAIRN);
            add(PACIFIC_POHNPEI);
            add(PACIFIC_PONAPE);
            add(PACIFIC_PORT_MORESBY);
            add(PACIFIC_RAROTONGA);
            add(PACIFIC_SAIPAN);
            add(PACIFIC_SAMOA);
            add(PACIFIC_TAHITI);
            add(PACIFIC_TARAWA);
            add(PACIFIC_TONGATAPU);
            add(PACIFIC_TRUK);
            add(PACIFIC_WAKE);
            add(PACIFIC_WALLIS);
            add(PACIFIC_YAP);
            add(POLAND);
            add(PORTUGAL);
            add(DEFAULT_TIMEZONE);
        }
    };

    /**
     * 获取对应region的默认时区
     */
    public static String getDefaultTimeZone(String region) {
        // all region: https://help.aliyun.com/document_detail/40654.html
        switch (StringUtils.lowerCase(region)) {
            case "me-east-1":
                return ASIA_DUBAI;
            case "us-east-1":
                return AMERICA_NEW_YORK;
            case "ap-northeast-1":
                return ASIA_TOKYO;
            case "ap-northeast-2":
                return ASIA_SEOUL;
            case "cn-hongkong":
                return ASIA_HONG_KONG;
            case "ap-south-1":
                return ASIA_KOLKATA;
            case "us-west-1":
                return AMERICA_LOS_ANGELES;
            case "me-central-1":
                return ASIA_RIYADH;
            case "ap-southeast-6":
                return ASIA_MANILA;
            case "ap-southeast-5":
                return ASIA_JAKARTA;
            case "ap-southeast-1":
                return ASIA_SINGAPORE;
            case "ap-southeast-3":
                return ASIA_KUALA_LUMPUR;
            case "ap-southeast-2":
                return AUSTRALIA_SYDNEY;
            case "eu-west-1":
                return EUROPE_LONDON;
            case "eu-central-1":
                return EUROPE_BERLIN;
            default:
                return ASIA_SHANGHAI;
        }
    }

    /**
     * 根据配置获取支持的命名时区，查询不到，则以本地为准
     * */
    private static List<String> getNamedZoneList(){

        List<String> supportedNamedZoneList = new ArrayList<>();
        if(System.currentTimeMillis() - lastUpdateMillis > CACHE_EXPIRED_MILLIS){
            ResourceService resourceService = SpringContextUtil.getBean("resourceService");
            List<String> configList = resourceService.getResourceRealValueList(MYSQL_SUPPORT_NAMED_ZONE_LIST_KEY);
            configList.stream().forEach(r -> supportedNamedZoneList.addAll(Arrays.asList(r.split(MYSQL_SUPPORT_NAMED_ZONE_SEPARATOR))));
            if(!supportedNamedZoneList.isEmpty()){
                validNamedZoneList = supportedNamedZoneList;
            }
            lastUpdateMillis = System.currentTimeMillis();
        }
        if(validNamedZoneList.isEmpty()){
            validNamedZoneList = TimezoneHelper.AVAILABLE_TIMEZONE_ID;
        }
        return validNamedZoneList;
    }

    // default_time_zone 除UTC时间外，命名时区需要单独校验
    // 命名时区长度太长，不适合使用optional方式进行校验
    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) {
        String timeZone = parameters.get(key).toString();
        return validator(timeZone);
    }

    public static boolean validator(String timeZone) {
        // ignore check timezone set like '+8:00'
        if (timeZone.trim().startsWith("-") || timeZone.trim().startsWith("+")) {
            String pattern = "[\\+\\-]\\d{1,2}:\\d{2}";
            return Pattern.matches(pattern, timeZone);
        }

        // check time zone id is valid
        boolean isValidTimezone = false;
        for (String tzID : getNamedZoneList()) {
            if (tzID.equalsIgnoreCase(timeZone)) {
                isValidTimezone = true;
                break;
            }
        }

        return isValidTimezone;
    }

    public static void main(String[] args) {
        try {
            System.out.println(TimezoneHelper.validator("+8:00"));
            System.out.println(TimezoneHelper.validator("Asia/Shanghai"));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}
