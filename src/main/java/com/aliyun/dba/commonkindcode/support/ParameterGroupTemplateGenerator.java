package com.aliyun.dba.commonkindcode.support;

import com.aliyun.dba.commonkindcode.idao.ParamGroupTemplateIDao;
import com.aliyun.dba.custins.service.CustinsParamGroupsService;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.instance.entity.MycnfTemplate;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.dba.commonkindcode.support.ParamTransHelper.*;
import static com.aliyun.dba.commonkindcode.support.ParameterGroupErrorCode.*;
import static com.aliyun.dba.custins.support.CustinsSupport.*;

/**
 * 参数模板统一生成类
 */
@Component
public class ParameterGroupTemplateGenerator {
    @Resource
    protected InstanceService instanceService;
    @Resource
    protected CustinsParamGroupsService custinsParamGroupsService;
    @Resource
    private ParamGroupTemplateIDao paramGroupTemplateIDao;


    /**
     * 集团特有参数param_group_id 标识
     */
    public static final String ALIGROUP_CONFIG_GROUPID = "aligroup_diff_config";

    /**
     * 集团特有参数param_group_id 标识
     */
    public static final String ALIGROUP_LEVEL_CONFIG_GROUPID = "aligroup_level_diff_config";

    /**
     * 集团特有参数param_group_id 标识
     */
    public static final String ARM_CONFIG_GROUPID = "aligroup_arm_basic_config";

    /**
     * alisql 创新班参数模板
     */
    public static final String ANNV_CONFIG_GROUPID = "alisql_innovation";

    public static final String POLARX_HATP_ADDITION_GROUPID = "polarx_hatp_addition_config";


    /**
     * getSysBaseParamTempMapList
     * 用于生成系统参数模板：
     * 1. 根据 dbType, dbVersion, characterType, category 信息生成基础参数模板
     * 2. 如果 category == enterprise 则使用 mycnf_template_extra 表生成基础参数
     * <p>
     * NOTICE:
     * 预期终态：MySQL 所有参数管理迁移至 mycnf_template_extra，目前处于灰度迁移阶段，
     * 目前(19-12-26) 只有 5.7、8.0 的 xdb 模板迁移至 mycnf_template_extra 表。
     * 后续迁移应注意 pengine-mysql 流程参数生成的适配。
     */
    public List<Map<String, Object>> getSysBaseParamTempMapList(ParamContext paramContext) throws RdsException {

        List<? extends MycnfTemplate> mycnfList = getMycnfTemplate(
                paramContext.getDbType(),
                paramContext.getDbVersion(),
                paramContext.getCharacterType(),
                paramContext.getCategory(),
                paramContext.getParamGroupId(),
                paramContext.isIgnoreVisible(),
                paramContext.isAliGroup(),
                paramContext.isArm(),
                paramContext.isPolarxHatp(),
                paramContext.isIs80Beta()
        );

        return getMapListFromMycnfExtraList(mycnfList);
    }

    // 获取系统基础模板
    public Map<String, MycnfTemplate> getSysBaseParamTempMap(ParamContext paramContext) throws RdsException {
        Map<String, MycnfTemplate> map = new HashMap<>();
        List<? extends MycnfTemplate> mycnfList = getMycnfTemplate(
                paramContext.getDbType(),
                paramContext.getDbVersion(),
                paramContext.getCharacterType(),
                paramContext.getCategory(),
                paramContext.getParamGroupId(),
                paramContext.isIgnoreVisible(),
                paramContext.isAliGroup(),
                paramContext.isArm(),
                paramContext.isPolarxHatp(),
                paramContext.isIs80Beta()
        );

        for (MycnfTemplate mycnfTemplate : mycnfList) {
            map.put(mycnfTemplate.getName(), mycnfTemplate);
        }

        return map;
    }


    private List<? extends MycnfTemplate> getMycnfTemplate(String dbType,
                                                           String dbVersion,
                                                           String characterType,
                                                           String category,
                                                           String paramGroupId,
                                                           boolean ignoreVisible,
                                                           boolean isAliGroup,
                                                           boolean isArm,
                                                           boolean isPolarxHatp,
                                                           boolean isAnnv) throws RdsException {
        List<? extends MycnfTemplate> mycnfList;

        // category == enterprise && dbVersion != 5.6
        // 查询 mycnf_template_extra 表
        List<MycnfTemplate> finalMycnfList = new ArrayList<>();
        if (isMysql(dbType) && ENTERPRISE_LEVEL.equalsIgnoreCase(category) && !DB_VERSION_MYSQL_56.equalsIgnoreCase(dbVersion)) {
            instanceService.getMycnfTemplateExtraMapIgnoreVisible(
                    dbType, dbVersion, characterType, category).forEach((key, value) -> finalMycnfList.add(value));
        } else {
            instanceService.getMycnfTemplateMap(dbType, dbVersion, characterType).
                    forEach((key, value) -> finalMycnfList.add(value));
        }
        mycnfList = finalMycnfList.stream().filter(config -> ignoreVisible || config.getIsVisible().equals(1)).collect(Collectors.toList());

        // 集团参数需要再覆盖集团参数模板
        if (isAliGroup) {
            mycnfList = coverParameters(mycnfList, paramGroupTemplateIDao.getParameterExtraWithParamGroupId(ALIGROUP_CONFIG_GROUPID));
            mycnfList = coverParameters(mycnfList, paramGroupTemplateIDao.getParameterExtraWithParamGroupId(ALIGROUP_LEVEL_CONFIG_GROUPID));
        }

        // polarx hatp私有协议参数，需要额外添加
        if (isPolarxHatp) {
            mycnfList = coverParameters(mycnfList, paramGroupTemplateIDao.getParameterExtraWithParamGroupId(POLARX_HATP_ADDITION_GROUPID));
        }

        // 支持国产化参数模板
        if (isArm) {
            mycnfList = coverParameters(mycnfList, paramGroupTemplateIDao.getParameterExtraWithParamGroupId(ARM_CONFIG_GROUPID));
        }

        // 创新版本
        if (isAnnv) {
            mycnfList = coverParameters(mycnfList, paramGroupTemplateIDao.getParameterExtraWithParamGroupId(ANNV_CONFIG_GROUPID));
        }

        // 参数模板为空直接返回
        if (StringUtils.isBlank(paramGroupId)) {
            return mycnfList;
        }

        List<Map> paramGroupList = custinsParamGroupsService.getParamGroupsByParamGroupId(paramGroupId);
        if (CollectionUtils.isEmpty(paramGroupList)) {
            return mycnfList;
        }
        Map paramGroup = paramGroupList.get(0);
        Integer paramGroupType = (Integer) paramGroup.get("ParameterGroupType");
        // 指定的参数模板ID如果不是系统模板则直接返回
        if (!PARAM_GROUP_TYPE_SYS.equals(paramGroupType)) {
            return mycnfList;
        }

        // 获取系统参数模板指定参数
        List<MycnfTemplate> mycnfTemplates = coverParameters(mycnfList, paramGroupTemplateIDao.getParameterExtraWithParamGroupId(paramGroupId));
        mycnfTemplates = mycnfTemplates.stream().filter(config -> ignoreVisible || config.getIsVisible().equals(1)).collect(Collectors.toList());

        return mycnfTemplates;
    }


    /**
     * paramGroupMatchValidate
     * 参数模板实例信息匹配校验，检查参数模板是否存在，校验数据库类型、版本、规格
     *
     * @param dbType       数据库类型
     * @param dbVersion    数据库版本
     * @param category     类型
     * @param paramGroupId 参数模板ID
     * @return Map 返回校验完成的参数模板
     * @throws RdsException
     */
    public Map paramGroupMatchValidate(String dbType, String dbVersion, String category, String paramGroupId, boolean ignoreCheck) throws RdsException {
        // 获取参数模板的信息，如果参数模板不存在则报错
        List<Map> paramGroupInfo = custinsParamGroupsService.getParamGroupsByParamGroupId(paramGroupId);
        if (paramGroupInfo.isEmpty()) {
            throw new RdsException(PARAM_GROUP_NOT_FOUND.toArray());
        }
        Map paramGroup = paramGroupInfo.get(0);

        if (!ignoreCheck) {
            // 数据库类型校验
            if (paramGroup.get("Engine") != null && !paramGroup.get("Engine").toString().equalsIgnoreCase(dbType)) {
                ParameterGroupErrorCode errorCode = INVALID_PARAM_GROUP_DB_TYPE;
                errorCode.setDesc(String.format(errorCode.getDesc(), paramGroupId, paramGroup.get("Engine"), dbType));
                throw new RdsException(errorCode.toArray());
            }

            // 数据库版本校验
            if (paramGroup.get("EngineVersion") != null && !paramGroup.get("EngineVersion").toString().equalsIgnoreCase(dbVersion)) {
                ParameterGroupErrorCode errorCode = INVALID_PARAM_GROUP_DB_VERSION;
                errorCode.setDesc(String.format(errorCode.getDesc(), paramGroupId, paramGroup.get("EngineVersion"), dbVersion));
                throw new RdsException(errorCode.toArray());
            }

            // 如果category不为空，则进行category校验
            if (StringUtils.isNotBlank(category) && paramGroup.get("ParameterGroupType").toString().equals("0")) {
                String paramGroupCategory = SysParamGroupHelper.getCategory(paramGroupId);
                if (StringUtils.isNotBlank(paramGroupCategory) && !paramGroupCategory.equalsIgnoreCase(category)) {
                    ParameterGroupErrorCode errorCode = INVALID_PARAM_GROUP_DB_CATEGORY;
                    errorCode.setDesc(String.format(errorCode.getDesc(), paramGroupId, paramGroupCategory, category));
                    throw new RdsException(errorCode.toArray());
                }
            }
        }

        return paramGroup;
    }
}
