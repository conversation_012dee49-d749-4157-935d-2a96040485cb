package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;

import java.util.Map;

// 只读实例不允许刷lower_case_table_names和innodb_large_prefix, 这两个参数只允许在主实例上面修改
public class InnodbLargePrefixHelper implements BaseHelper{
    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException {
        if (context.getInsType() == 3 || context.getInsType() == 4) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "innodb_large_prefix can not be set on read instances");
        }
        return true;
    }
}
