package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.base.support.SizeUnitTransTool;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * innodb logfile size helper
 * check innodb_log_file_size * innodb_log_files_in_group and disk size is enough
 * 
 * <AUTHOR> on 2024/9/6
 */
@Slf4j
public class InnodbLogFileSizeHelper implements BaseHelper {

    private static final LogAgent logger = LogFactory.getLogAgent(InnodbLogFileSizeHelper.class);

    private final static String LOG_FILE_SIZE_KEY = "innodb_log_file_size";
    private final static String LOG_FILE_IN_GROUP_KEY = "innodb_log_files_in_group";

    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException {
        String value = parameters.get(LOG_FILE_SIZE_KEY) == null ? null : parameters.get(LOG_FILE_SIZE_KEY).toString();
        if (StringUtils.isBlank(value)) {
            return true;
        }
        try {
            DbossApi dbossApi = SpringContextUtil.getBeanByClass(DbossApi.class);
            Map<String, Object> bValue = dbossApi.getParameter(context.getCustinsId(), LOG_FILE_IN_GROUP_KEY);
            int fileInGroups = bValue == null ? 2 : Integer.parseInt(bValue.get(LOG_FILE_IN_GROUP_KEY).toString());  //default group is 2
            long logFileSizeBytes = SizeUnitTransTool.trans(value, "B").longValue();
            long diskSizeBytes = context.getDiskSize() * 1024 * 1024;
            if (diskSizeBytes <= (fileInGroups * logFileSizeBytes)) {
                logger.warn("innodb_log_file_size is [{}] innodb_log_files_in_group is [{}] disk_size_bytes is [{}]", logFileSizeBytes, fileInGroups, diskSizeBytes);
                return false;
            }
        } catch (Exception e) {
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, e.getMessage());
        }
        return true;
    }
}
