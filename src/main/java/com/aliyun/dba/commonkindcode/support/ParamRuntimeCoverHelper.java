package com.aliyun.dba.commonkindcode.support;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.support.SizeUnitTransTool;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.entity.MycnfTemplate;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static java.util.Arrays.asList;

@Component
public class ParamRuntimeCoverHelper {
    private static final LogAgent logger = LogFactory.getLogAgent(ParamRuntimeCoverHelper.class);
    @Autowired
    protected DbossApi dbossApi;
    @Autowired
    protected ResourceService resourceService;

    // 0:本地盘，3：DockerOnEcs， 18：新架构
    private static final List<Integer> kindCodeEnableList = asList(CustinsSupport.KIND_CODE_NC,
            CustinsSupport.KIND_CODE_DOCKER_ON_ECS, 18);

    private static final List<InstanceLevel.CategoryEnum> categoryEnumUnableList = asList(
            InstanceLevel.CategoryEnum.SERVERLESS_BASIC,
            InstanceLevel.CategoryEnum.SERVERLESS_STANDARD);

    private static final List<String> switchValues = asList("1", "0", "TRUE", "FALSE", "ON", "OFF");

    // 参数黑名单，这些参数不覆盖runtime的值,因为导出参数模板接口对这些参数有特殊的校验,具体参考rdsapi中ComplexParamsVerifyService.validate 的逻辑
    private static final List<String> PARAM_BLACK_LIST =
            asList("loose_optimizer_switch", "loose_optimizer_trace_features",
                    "loose_optimizer_trace", "rds_sql_select_filter");

    // loose_ 前缀 是为了同一大版本下 不同内核小版本的实例启动不报错的参数兼容方案,
    // 元数据中有loose_，runtime没有loose_
    private static final String LOOSE_ = "loose_";

    /**
     *  通过dboos接口查询实例运行态参数值 覆盖 管控参数快照表的值，返回实例参数列表
     * @param custins
     * @param runningParameters 管控参数表数据
     * @param templateMap 管控参数模板
     * @param requestId 用来打印日志，方便sls定位
     */
    public List<Map<String, Object>> coverRuntimeParam(CustInstanceDO custins,
                                                       List<Map<String, Object>> runningParameters,
                                                       Map<String, MycnfTemplate> templateMap, String requestId) {

        Map<String, String> dbossParams = new HashMap<>();
        dbossParams.put("custinsId", custins.getId() + "");
        dbossParams.put("role", "master");
        dbossParams.put("parameter", "%%");
        Map<String, Object> dbossRuntimeParamMap = null;
        try {
            // 获取实例运行态参数值
            dbossRuntimeParamMap = dbossApi.doDbossInvoke(dbossParams,"mysql-op/variables");
        } catch (Exception e) {
            logger.error("requestId={}, dboss query param error: {}",requestId, e);
            return runningParameters;
        }
        Map<String, String> needCoverParamMap = new HashMap<>();
        Map<String, String> noPassParamMap = new HashMap<>();
        for (Map<String, Object> metaMap : runningParameters) {
            String paramName = (String)metaMap.get(ParamTransHelper.PARAM_MAP_KEY_PARAMETER_NAME);
            String paramValue = (String)metaMap.get(ParamTransHelper.PARAM_MAP_KEY_PARAMETER_VALUE);
            if (PARAM_BLACK_LIST.contains(paramName)) {
                continue;
            }

            String runtimeParamName = paramName;
            if (null != paramName && paramName.startsWith(LOOSE_)) {
                runtimeParamName = runtimeParamName.replace(LOOSE_, "");
            }
            if (!dbossRuntimeParamMap.containsKey(runtimeParamName)) {
                continue;
            }
            String runtimeParamValue = (String)dbossRuntimeParamMap.get(runtimeParamName);
            // 当元数据参数值为表达式参数 或 与运行态参数值一致的场景下，直接应用元数据值 不覆盖
            if (StringUtils.isBlank(paramName) || StringUtils.isBlank(paramValue) || paramValue.startsWith("{")
                    || StringUtils.isBlank(runtimeParamValue) || paramValue.equalsIgnoreCase(runtimeParamValue)) {
                continue;
            }
            // 按照元数据参数值格式 转换 运行态参数值
            String transfValue = paramValueTransfer(paramValue, runtimeParamValue);
            if (paramValue.equalsIgnoreCase(transfValue)) {
                continue;
            }

            // 检查转换后的参数值是否符合要求, 检查通过后覆盖元数据值
            if (checkParamMatchTemplate(paramName, transfValue, templateMap)) {
                metaMap.put(ParamTransHelper.PARAM_MAP_KEY_PARAMETER_VALUE, transfValue);
                needCoverParamMap.put(paramName, paramValue + "<==>" + transfValue);
            } else {
                noPassParamMap.put(paramName, paramValue + "<==>" + transfValue);
            }
        }
        logger.info("requestId={}, needCoverParamMap={}, noPassParamMap={}", requestId, needCoverParamMap, noPassParamMap);
        return runningParameters;
    }

    /**
     * 检查参数值是否符合模板要求(逻辑参考 com.aliyun.dba.custins.support.CustinsValidator.checkParameters)
     */
    private boolean checkParamMatchTemplate(String paramName, String paramValue, Map<String, MycnfTemplate> templateMap) {
        MycnfTemplate template = templateMap.get(paramName);
        if (template==null) {
            return false;
        }
        if (".*".equals(template.getOptional())) {
            return true;
        }
        if ("INT".equals(template.getUnit()) || "B".equals(template.getUnit())) {
            // 支持单位换算
            try {
                paramValue = SizeUnitTransTool.trans(paramValue, "B").toBigInteger().toString();
            } catch (Exception e) {
                logger.error("SizeUnitTransTool.trans error");
                return false;
            }
        }
        String[] tempStr;
        if (Pattern.compile("^\\[[0-9]*(-)[0-9]*\\]$").matcher(template.getOptional()).matches()) {
            tempStr = template.getOptional().substring(1, template.getOptional().length() - 1).split("-");
            if (Validator.isSizeRight(paramValue, tempStr[0], tempStr[1]) && isRightValue(template, paramValue)) {
                return true;
            }
        } else if (Pattern.compile("^\\[-[1-9]*(-)[0-9]*\\]$").matcher(template.getOptional()).matches()) {
            tempStr = template.getOptional().substring(1, template.getOptional().length() - 1).split("-");
            if (Validator.isSizeRight(paramValue, "-" + tempStr[1], tempStr[2]) && isRightValue(template, paramValue)) {
                return true;
            }
        } else if (Pattern.compile("^\\[(.+)(\\|)(.+)+\\]$").matcher(template.getOptional()).matches()) {
            if (Validator.isKeywords(paramValue, template.getOptional().substring(1, template.getOptional().length() - 1))) {
                return true;
            }
        } else if (template.getUnit().equalsIgnoreCase("DOUBLE")
                && Pattern.compile("^\\[[0-9]*(.)[0-9]*(-)[0-9]*(.)[0-9]*\\]$").matcher(template.getOptional()).matches()) {
            tempStr = template.getOptional().substring(1, template.getOptional().length() - 1).split("-");
            if (Validator.isDoubleRight(paramValue, tempStr[0], tempStr[1]) && isRightValue(template, paramValue)) {
                return true;
            }
        } else if (Pattern.compile("^" + template.getOptional() + "$").matcher(paramValue).matches()) {
            return true;
        }
        return false;
    }

    /**
     *  按照元数据参数值格式 转换 运行态参数值，并校验转化后的值是否符合参数模板范式
     *  转换成功且校验通过，返回转换后的值，否则返回元数据值
     *
     * @param metaValue 管控快照表中元数据值
     * @param runtimeValue dboss接口查询出的参数运行态的值
     */
    private String paramValueTransfer(String metaValue, String runtimeValue) {
        // 转换开关值(例：ON==1==TRUE)
        if (checkBothSwitchValue(metaValue, runtimeValue)) {
            return transfSwitchValue(metaValue, runtimeValue);
        }
        // 转换单位值(例:150M==157286400)
        if (checkBothSizeValue(metaValue, runtimeValue)) {
            return transferSizeValue(metaValue, runtimeValue);
        }
        // 转换数字值(例:1==1.000000)
        if (checkBothNumValue(metaValue, runtimeValue)) {
            return transferNumValue(metaValue, runtimeValue);
        }
        return runtimeValue;
    }

    private String transferNumValue(String metaValue, String runtimeValue) {
        return runtimeValue.replace(".000000", "");
    }

    private boolean checkBothNumValue(String metaValue, String runtimeValue) {
        Boolean metaFlag = Pattern.compile("^[0-9]*").matcher(metaValue).matches();
        Boolean runtimeFlag = Pattern.compile("^[0-9]*.000000").matcher(runtimeValue).matches();
        return metaFlag && runtimeFlag;
    }

    private boolean checkBothSizeValue(String metaVal, String runtimeVal) {
        Boolean metaFlag = Pattern.compile("^[0-9]*(K|KB|M|MB|G|GB)").matcher(metaVal).matches();
        Boolean runtimeFlag = Pattern.compile("^[0-9]*").matcher(runtimeVal).matches();

        return metaFlag && runtimeFlag;
    }

    private static String transferSizeValue(String metaVal, String runtimeVal) {
        String parsedVal = metaVal;
        try {
            String intValue = Pattern.compile("[^0-9]").matcher(metaVal).replaceAll("").trim();
            String unit = metaVal.replace(intValue, "");
            parsedVal = SizeUnitTransTool.trans(runtimeVal, unit).toBigInteger() + unit;
        } catch (Exception e) {
            return metaVal;
        }
        return parsedVal;
    }

    /**
     * 根据metaV的值类型转换runV的值 （比如 metaV:ON, runV:O, 会将run转换为OFF并返回）
     */
    private static String transfSwitchValue(String metaV, String runV) {
        if ("0,1".indexOf(metaV)>-1) {
            if ("1,TRUE,ON".indexOf(runV)>-1) {
                return "1";
            }
            return "0";
        }

        if ("OFF,ON".indexOf(metaV)>-1) {
            if ("1,TRUE,ON".indexOf(runV)>-1) {
                return "ON";
            }
            return "OFF";
        }

        if ("TRUE,FALSE".indexOf(metaV)>-1) {
            if ("1,TRUE,ON".indexOf(runV)>-1) {
                return "TRUE";
            }
            return "FALSE";
        }
        return runV;
    }

    private static boolean checkBothSwitchValue(String a1, String a2) {
        if (switchValues.contains(a1) && switchValues.contains(a2)) {
            return true;
        }
        return false;
    }

    /**
     * copy from com.aliyun.dba.custins.support.CustinsValidator.isRightValue (源方法是private修饰的,故copy一下)
     */
    public static Boolean isRightValue(MycnfTemplate template, String value) {
        BigDecimal divideBase;
        if (!template.getUnit().equalsIgnoreCase("INT") && !template.getUnit().equalsIgnoreCase("B")) {
            if (template.getUnit().equalsIgnoreCase("DOUBLE")) {
                divideBase = (new BigDecimal(1.0D)).divide(new BigDecimal(Math.pow(10.0D, (double)template.getDivideBase())));
                if ((new BigDecimal(value)).compareTo(new BigDecimal(0)) != 0) {
                    BigDecimal a = (new BigDecimal(value)).remainder(divideBase);
                    return a.compareTo(new BigDecimal(0)) == 0;
                }
            }
        } else if (!value.equals("0")) {
            if (template.getDivideBase() != null && template.getDivideBase() == 0) {
                return true;
            }
            divideBase = (new BigDecimal(value)).remainder(new BigDecimal(template.getDivideBase()));
            return divideBase.compareTo(new BigDecimal(0)) == 0;
        }

        return true;
    }


    /**
     *  检查是否用dboos接口查出的运行态参数值 去覆盖 管控参数表元数据的参数值
     *  1、检查当前实例是否是 本地盘高可用、dockerOnEcs、新架构k8s; 企业版和serveless实例不支持
     *  2、检查resource表中配置的开关是否打开
     *  3、针对公有云等大单元 还有一个resource开关用来 进行hangzhou shenzhen等小单元的灰度开关
     *
     * @param custins
     * @param effectiveLevel 规格对象，如果是只读实例会传入对应的主实例规格信息
     * @param regionId 灰度过滤判断依据
     * @return boolean
     */
    public boolean paramCoverCheck(CustInstanceDO custins, InstanceLevelDO effectiveLevel, String regionId) {
        if (categoryEnumUnableList.contains(effectiveLevel.getCategory())
                || effectiveLevel.getClassCode().contains("serverless")) {
            return false;
        }
        if (!kindCodeEnableList.contains(custins.getKindCode())) {
            return false;
        }

        ResourceDO paramCoverSwitch = resourceService.getResourceByResKey("OPEN_MYSQL_PARAM_COVER_RUNITME_VALUE");
        Boolean isSwitchOpen = paramCoverSwitch != null && "1".equals(paramCoverSwitch.getRealValue());
        if (isSwitchOpen) {
            ResourceDO regionConfig = resourceService.getResourceByResKey("MYSQL_PARAM_COVER_ENABLED_REGIONS");
            if (regionConfig != null && regionId != null && !regionConfig.getRealValue().contains(regionId)) {
                return false;
            }
            return true;
        }
        return false;
    }
}