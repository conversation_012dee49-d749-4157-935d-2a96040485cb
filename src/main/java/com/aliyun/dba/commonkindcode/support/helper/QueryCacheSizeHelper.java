package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.base.support.SizeUnitTransTool;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.commonkindcode.support.ParamExpressionParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;

import java.math.BigInteger;
import java.util.Map;

public class QueryCacheSizeHelper implements BaseHelper{
    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException {
        if (!parameters.containsKey("loose_query_cache_size") || !parameters.containsKey("loose_query_cache_type")) {
            return true;
        }

        String queryCacheType = parameters.get("loose_query_cache_type").toString();
        if (queryCacheType.equals("0")) {
            return true;
        }
        BigInteger queryCacheSizeByte;
        BigInteger innodbBufferPoolSizeByte;
        BigInteger totalUseMemeSizeByte;
        BigInteger totalMemory80p;
        // 获取 BP 的大小
        String innodbBufferPoolSize = parameters.get("innodb_buffer_pool_size").toString();
        // 获取 query cache 的大小
        String queryCacheSize = parameters.get("loose_query_cache_size").toString();
        try {
            ParamExpressionParser parser = context.getParser();
            boolean isExpr = queryCacheSize.trim().startsWith("{");
            queryCacheSizeByte = isExpr ?
                    parser.parse(queryCacheSize) :
                    SizeUnitTransTool.trans(queryCacheSize, "B").toBigInteger();

            isExpr = innodbBufferPoolSize.trim().startsWith("{");
            innodbBufferPoolSizeByte = isExpr ?
                    parser.parse(innodbBufferPoolSize) :
                    SizeUnitTransTool.trans(innodbBufferPoolSize, "B").toBigInteger();
            // 计算是否超限
            totalUseMemeSizeByte = innodbBufferPoolSizeByte.add(queryCacheSizeByte);
            totalMemory80p = parser.parse("{DBInstanceClassMemory*4/5}");
        } catch (Exception e) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
        if (totalUseMemeSizeByte.compareTo(totalMemory80p) < 0) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS,
                    String.format("loose_query_cache_type(%d) + innodb_buffer_pool_size(%d) > {DBInstanceClassMemory*4/5}(%d)",
                            queryCacheSizeByte, innodbBufferPoolSizeByte, totalMemory80p));
        }
        return true;
    }
}
