package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * innodb_temp_data_file_path 参数单独校验 (aone:42081205)
 * 默认值：ibtmp1:12M:autoextend， 可修改最大值，最大值范围为 12M-1024000M
 */
public class InnodbTempDataFilePathHelper implements BaseHelper {
    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException {
        String innodbTempDataFilePath = parameters.get(key).toString();
        if ("ibtmp1:12M:autoextend".equals(innodbTempDataFilePath)) {
            return true;
        }
        Boolean boo = Pattern.compile("^ibtmp1:12M:autoextend:max:[0-9]*[M|G]").matcher(innodbTempDataFilePath).matches();
        if (boo && innodbTempDataFilePath.length()>27) {
            String maxV = innodbTempDataFilePath.substring(26,innodbTempDataFilePath.length()-1);
            Integer maxVInt = Integer.parseInt(maxV);
            if (innodbTempDataFilePath.endsWith("G")) {
                maxVInt = maxVInt*1024;
            }
            return 12 <= maxVInt && maxVInt <= 1024000;
        }
        throw new RdsException(ErrorCode.INVALID_PARAMETERS, "innodb_temp_data_file_path is invalid");
    }

    public static void main(String[] args) {
        try {
            System.out.println(new InnodbTempDataFilePathHelper().validator(
                    new HashMap<String, Object>() {{put("innodb_temp_file_path","ibtmp1:12M:autoextend:max:123M");}},
                    "innodb_temp_file_path",
                    null));
            System.out.println(new InnodbTempDataFilePathHelper().validator(
                    new HashMap<String, Object>() {{put("innodb_temp_file_path","ibtmp1:12M:autoextend");}},
                    "innodb_temp_file_path",
                    null));
            System.out.println(new InnodbTempDataFilePathHelper().validator(
                    new HashMap<String, Object>() {{put("innodb_temp_file_path","12M:autoextend:max:123M");}},
                    "innodb_temp_file_path",
                    null));
            System.out.println(new InnodbTempDataFilePathHelper().validator(
                    new HashMap<String, Object>() {{put("innodb_temp_file_path","ibtmp1:12M:autoextend:max:102401M");}},
                    "innodb_temp_file_path",
                    null));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}
