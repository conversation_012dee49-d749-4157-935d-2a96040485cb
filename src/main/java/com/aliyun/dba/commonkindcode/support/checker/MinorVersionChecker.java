package com.aliyun.dba.commonkindcode.support.checker;

import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Range;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.function.BiPredicate;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @description 参数小版本检查器
 * @date 2023/03/08
 **/
public class MinorVersionChecker {

    /**
     * 规则<p>
     * dbType-dbVersion 确定参数条件<p>
     * key 确定参数<p>
     * value 确定参数值<p>
     * 使用{@link Range}来表示小版本的区间：<p>
     * {@code Range.atLeast(20221231)} 表示 [20221231, +∞)<p>
     * {@code Range.atMost(20221231)} 表示 (-∞, 20221231]<p>
     * {@code Range.lessThan(20221231)} 表示 (-∞, 20221231)<p>
     * {@code Range.greaterThan(20221231)} 表示 (20221231, +∞)<p>
     * {@code Range.closed(20221231, 20230201)} 表示 [20221231, 20230201]<p>
     * {@code Range.open(20221231, 20230201)} 表示 (20221231, 20230201)<p>
     * {@code Range.closedOpen(20221231, 20230201)} 表示 [20221231, 20230201)<p>
     * {@code Range.openClosed(20221231, 20230201)} 表示 (20221231, 20230201]<p>
     * {@code Range.all()} 表示 (-∞, +∞) //应该不需要<p>
     */
    static final LinkedList<Prescript> RULE_LIST = new LinkedList<Prescript>() {{

        // region tls_version:
        Range<Integer> atLeastOfTlsVersion = Range.atLeast(20221231);
        // mysql:5.7:tls_version，小版本至少为20221231，参数值TLSv1.3才支持
        add(new Prescript("mysql", "5.7", "tls_version", atLeastOfTlsVersion, "TLSv1.3"));
        add(new Prescript("mysql", "8.0", "tls_version", atLeastOfTlsVersion, "TLSv1.3"));
        // endregion
    }};

    static final BiPredicate<String, String> predicate = (paramValue, ruleValue) ->
            Arrays.stream(ruleValue.split(","))
                    .anyMatch((Predicate<? super String>) s -> StringUtils.containsIgnoreCase(paramValue, s));


    /**
     * @param key          用户的参数
     * @param value        用户值
     * @param dbVersion    dbVersion
     * @param minorVersion 小版本
     * @return null: 通过; not null: 拦截
     */
    public void check(String key, String value, String dbVersion, Integer minorVersion) throws RdsException {
        for (Prescript prescript : RULE_LIST) {
            if (prescript.dbVersion.equalsIgnoreCase(dbVersion)    // 版本相同
                    && key.equalsIgnoreCase(prescript.key)         // 参数名相同
                    && predicate.test(value, prescript.value)             // 参数值满足规则
                    && !prescript.range.contains(minorVersion)                 // 但是小版本不在允许范围内
            ) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, prescript.generateErrorDesc()) ;
            }
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    static class Prescript {

        /**
         * 数据库类型
         */
        String dbType;
        /**
         * 数据库版本
         */
        String dbVersion;

        /**
         * 参数名
         */
        String key;

        /**
         * 小版本区间
         */
        Range<Integer> range;

        /**
         * 受限的参数值
         */
        String value;

        private String generateErrorDesc() {
            return String.format("%s,支持的小版本区间为%s", getValue(), range.toString());
        }

    }
}
