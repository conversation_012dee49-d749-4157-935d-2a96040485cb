package com.aliyun.dba.commonkindcode.support.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.Arrays.asList;

/*
 *   针对用户要修改的特定参数 loose_optimizer_switch
 *   做严格的参数校验，其中的键值对的key需要在"index_merge", "index_merge_union", "index_merge_sort_union", "index_merge_intersection",
 *           "engine_condition_pushdown","index_condition_pushdown","mrr","mrr_cost_based","block_nested_loop","batched_key_access","materialization",
 *           "semijoin","loosescan","firstmatch","subquery_materialization_cost_based","use_index_extensions" ,"prefer_ordering_index"里面
 *   其中的键值对的value需要为on或者off
 */
public class OptimizerSwitchHelper implements BaseHelper {
    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException {
        String optimizerSwitch = parameters.get(key).toString();
        ResourceService resourceService = SpringContextUtil.getBean("resourceService");
        ResourceDO optimizerSwitchTemplate = resourceService.getResourceByResKey("MYSQL_PARAM_VALUE_MINOR_RULE");
        if (optimizerSwitchTemplate == null || optimizerSwitchTemplate.getRealValue().isEmpty() || !ParamKeyValueValidator.JsonStructureValidator(Objects.requireNonNull(optimizerSwitchTemplate).getRealValue(),"loose_optimizer_switch","min_version","max_version")){
            Object validateResult = optimizerSwitchValidateBasic(optimizerSwitch);
            if (validateResult instanceof Boolean) {
                return (Boolean)validateResult;
            }
            parameters.put(key, validateResult);
            return true;
        }
        Object validateResult = ParamKeyValueValidator.KvOptimizerSwitchValidate("loose_optimizer_switch",
                optimizerSwitch,context.getDbVersion(), context.getMinorVersion(), optimizerSwitchTemplate.getRealValue());
        if (validateResult instanceof Boolean) {
            return (Boolean)validateResult;
        }
        parameters.put(key, validateResult);
        return true;
    }
    private static Object optimizerSwitchValidateBasic(String optimizerSwitch) {
        if (StringUtils.isEmpty(optimizerSwitch) || optimizerSwitch.endsWith(",")) {
            return Boolean.FALSE;
        }

        // 避免传参里面带入空格
        else if (optimizerSwitch.split(" ").length > 1) {
            return Boolean.FALSE;
        }

        List<String> supportedOptimizerSwitchPrefix = asList("index_merge", "index_merge_union", "index_merge_sort_union", "index_merge_intersection",
                "engine_condition_pushdown","index_condition_pushdown","mrr","mrr_cost_based","block_nested_loop","batched_key_access","materialization",
                "semijoin","loosescan","firstmatch","subquery_materialization_cost_based","use_index_extensions", "prefer_ordering_index");


        String[] optArr = optimizerSwitch.split(",");
        Map<String, String> optMap = new HashMap<>();
        boolean overrideOptimizerSwitch = false;
        for (String optStr : optArr) {
            String[] unitArr = optStr.split("=");
            if (unitArr.length != 2 || StringUtils.isBlank(unitArr[0]) ||
                    (!"on".equalsIgnoreCase(unitArr[1]) && !"off".equalsIgnoreCase(unitArr[1])) || !supportedOptimizerSwitchPrefix.contains(unitArr[0])) {
                return Boolean.FALSE;
            }
            if (optMap.containsKey(unitArr[0])) {
                if (optMap.get(unitArr[0]).equals(unitArr[1])) {
                    overrideOptimizerSwitch = true;
                } else {
                    return Boolean.FALSE;
                }
            }
            optMap.put(unitArr[0], unitArr[1]);
        }
        if (overrideOptimizerSwitch) {
            return optMap.keySet().stream().sorted(Comparator.comparingInt(optimizerSwitch::indexOf)).map(optKey -> String.format("%s=%s", optKey, optMap.get(optKey))).collect(Collectors.joining(","));
        }
        return Boolean.TRUE;
    }

    public static class ParamKeyValueValidator {


        /**
         * 目前用来判断配置的optimizerSwitch参数结构是否合法
         * optimizerSwitch 参数配置再 resource key 中
         * 结构为
         * {
         *     "loose_optimizer_switch": {
         *         "5.7": {
         *             "params": {
         *                 "min_version": "20221020",
         *                 "max_version": "99999999"
         *             },
         *         },
         *         "8.0": {
         *             "params": {
         *                 "min_version": "20221020",
         *                 "max_version": "99999999"
         *             }
         *         }
         *     }
         * }
         */
        public static boolean JsonStructureValidator(String targetJson,String rootNodeName,String leafNodeNameFirst,String leafNodeNameSeconde){

            JSONObject rootNode = null;

            try {
                JSONObject jsonObject = JSON.parseObject(targetJson);

                // 校验是否有根节点
                if (!jsonObject.containsKey(rootNodeName)) {
                    return false;
                }

                rootNode = jsonObject.getJSONObject(rootNodeName);

            } catch (JSONException ex) {
                return false;
            }

            // 校验该节点是否为对象类型
            if (rootNode == null || rootNode.isEmpty()) {
                return false;
            }

            // 遍历版本号节点
            for (Map.Entry<String, Object> entry : rootNode.entrySet()) {
                String version = entry.getKey();

                // 校验版本号节点是否为字符串类型
                if (!version.matches("^\\d+.\\d+$")) {
                    return false;
                }
                JSONObject versionSwitchNode = (JSONObject) entry.getValue();

                // 校验版本号节点是否为对象类型
                if (versionSwitchNode == null || versionSwitchNode.isEmpty()) {
                    return false;
                }

                // 遍历子节点
                for (Map.Entry<String, Object> paramEntry : versionSwitchNode.entrySet()) {
                    JSONObject paramInfoNode = (JSONObject) paramEntry.getValue();

                    // 校验子节点是否为对象类型
                    if (paramInfoNode == null || paramInfoNode.isEmpty()) {
                        return false;
                    }

                    // 校验参数节点中是否有leafNodeFirst 和 leafNodeSeconde节点
                    if (!paramInfoNode.containsKey(leafNodeNameFirst) || !paramInfoNode.containsKey(leafNodeNameSeconde)) {
                        return false;
                    }

                    // 校验leafNodeFirst 和 leafNodeSeconde节点是否为字符串类型
                    String leafNodeFirst = paramInfoNode.getString(leafNodeNameFirst);
                    String leafNodeSeconde = paramInfoNode.getString(leafNodeNameSeconde);
                    if (leafNodeFirst == null || leafNodeSeconde == null || leafNodeFirst.isEmpty() || leafNodeSeconde.isEmpty()) {
                        return false;
                    }

                    // 校验leafNode节点值是否是数字类型
                    if (!(leafNodeFirst.matches("^\\d+$") && leafNodeSeconde.matches("^\\d+$"))) {
                        return false;
                    }
                }
            }
            return true;
        }


        /**
         * 校验客户当前实例所属版本是否支持修改该参数
         * @param parameterName 客户实例参数
         * @param majorVersion 数据库版本
         * @param minorVersion 小版本
         * @param resourceKeyTemplateJson resource key中配置的参数模板
         */
        public static Object KvOptimizerSwitchValidate(String parameterName,String parameterValue, String majorVersion, Integer minorVersion, String resourceKeyTemplateJson) {
            try {

                if (StringUtils.isEmpty(parameterValue) || parameterValue.endsWith(",")) {
                    return Boolean.FALSE;
                }

                // 避免传参里面带入空格
                else if (parameterValue.split(" ").length > 1) {
                    return Boolean.FALSE;
                }

                String[] optArr = parameterValue.split(",");
                Map<String, String> optMap = new HashMap<>();
                boolean overrideOptimizerSwitch = false;
                for (String optStr : optArr) {
                    String[] unitArr = optStr.split("=");

                    if (unitArr.length != 2 || StringUtils.isBlank(unitArr[0]) ||
                            (!"on".equalsIgnoreCase(unitArr[1]) && !"off".equalsIgnoreCase(unitArr[1]))) {
                        return Boolean.FALSE;
                    }

                    //对比大版本信息
                    JSONObject looseOptimizerSwitch = JSON.parseObject(resourceKeyTemplateJson).getJSONObject(parameterName);
                    JSONObject jsonVersionTemplate = looseOptimizerSwitch.getJSONObject(majorVersion);
                    if (jsonVersionTemplate == null || jsonVersionTemplate.isEmpty()) {
                        return Boolean.FALSE;
                    }
                    //对比参数信息
                    JSONObject SwitchTemplateJson = jsonVersionTemplate.getJSONObject(unitArr[0]);
                    if (SwitchTemplateJson == null || SwitchTemplateJson.isEmpty()) {
                        return Boolean.FALSE;
                    }

                    //获取小版本信息
                    String minVersionTemplate = SwitchTemplateJson.getString("min_version");
                    String maxVersionTemplate = SwitchTemplateJson.getString("max_version");

                    if (minVersionTemplate == null || minVersionTemplate.isEmpty() || maxVersionTemplate == null || maxVersionTemplate.isEmpty()) {
                        return Boolean.FALSE;
                    }

                    boolean compareResult = IntStream.of(Integer.parseInt(minVersionTemplate), Integer.parseInt(maxVersionTemplate), minorVersion)
                            .sorted()
                            .toArray()[1] == minorVersion;

                    if (!compareResult) {
                        return Boolean.FALSE;
                    }
                    if (optMap.containsKey(unitArr[0])) {
                        if (optMap.get(unitArr[0]).equals(unitArr[1])) {
                            overrideOptimizerSwitch = true;
                        } else {
                            return Boolean.FALSE;
                        }
                    }
                    optMap.put(unitArr[0], unitArr[1]);
                }
                if (overrideOptimizerSwitch) {
                    return optMap.keySet().stream().sorted(Comparator.comparingInt(parameterValue::indexOf)).map(optKey -> String.format("%s=%s", optKey, optMap.get(optKey))).collect(Collectors.joining(","));
                }
                return Boolean.TRUE;
            }catch (Exception ex) {
                return Boolean.FALSE;
            }
        }
    }

    public static void main(String[] args) {
        try {
            System.out.println(OptimizerSwitchHelper.optimizerSwitchValidateBasic("index_merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on,engine_condition_pushdown=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=on,block_nested_loop=on,batched_key_access=off,materialization=on,semijoin=on,loosescan=on,firstmatch=on,subquery_materialization_cost_based=on,use_index_extensions=on"));
            System.out.println(OptimizerSwitchHelper.optimizerSwitchValidateBasic("merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on,engine_condition_pushdown=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=on,block_nested_loop=on,batched_key_access=off,materialization=on,semijoin=on,loosescan=on,firstmatch=on,subquery_materialization_cost_based=on,use_index_extensions=on"));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}
