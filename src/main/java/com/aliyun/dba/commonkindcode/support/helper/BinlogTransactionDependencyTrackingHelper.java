package com.aliyun.dba.commonkindcode.support.helper;

import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.function.Predicate;

/* 条件1：When WRITESET or WRITESET_SESSION is set as the value for binlog_transaction_dependency_tracking,
 *      transaction_write_set_extraction must be set to specify an algorithm (not set to OFF).
 * 条件2：The value of this variable cannot be set to anything other than COMMIT_ORDER if transaction_write_set_extraction is OFF.
 */
@Component
public class BinlogTransactionDependencyTrackingHelper implements BaseHelper {
    static final LogAgent logger = LogFactory.getLogAgent(BaseHelper.class);

    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException, IOException {
        String b = parameters.get(key).toString();
        String t = parameters.get("transaction_write_set_extraction") == null ? null : parameters.get("transaction_write_set_extraction").toString();
        DbossApi dboss = SpringContextUtil.getBeanByClass(DbossApi.class);

        if (StringUtils.isEmpty(t)) {
            // 如果本次只设置b，则校验条件2
            Map<String, Object> value = dboss.getParameter(context.getCustinsId(), "transaction_write_set_extraction");
            t = value == null ? null : String.valueOf(value.get("transaction_write_set_extraction"));
            return !"OFF".equalsIgnoreCase(t) || "COMMIT_ORDER".equalsIgnoreCase(b);
        } else {
            Predicate<String> belongToWriteset = v -> "WRITESET".equalsIgnoreCase(v) || "WRITESET_SESSION".equalsIgnoreCase(v);
            Map<String, Object> bValue = dboss.getParameter(context.getCustinsId(), key);
            Map<String, Object> tValue = dboss.getParameter(context.getCustinsId(), "transaction_write_set_extraction");
            // 理论上 value 一定有值
            String bCurrent = bValue == null ? null : String.valueOf(bValue.get(key));
            String tCurrent = tValue == null ? null : String.valueOf(tValue.get("transaction_write_set_extraction"));
            if (belongToWriteset.test(bCurrent) && belongToWriteset.test(b)) {
                // 此时，传入的t和实例的t不一样，才拦截
                if (!t.equalsIgnoreCase(tCurrent)) {
                    logger.warn("current value of transaction_write_set_extraction is {}, binlog_transaction_dependency_tracking can not be changed", tCurrent);
                    return false;
                }
            }
            // 如果同时更改了t和b，则校验条件1
            return !belongToWriteset.test(b) || !"OFF".equalsIgnoreCase(t);
        }
    }

    public static void main(String[] args) {

    }
}
