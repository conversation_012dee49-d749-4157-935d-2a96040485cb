package com.aliyun.dba.commonkindcode.support.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.cms.bc.BcKeyTransRecipientInfoGenerator;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class CollationServerHelper implements BaseHelper {
    @Override
    public boolean validator(Map<String, Object> parameters, String key, ParamContext context) throws RdsException, IOException {
        if (context.getInsType() == 3 || context.getInsType() == 4) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "collation_server can not be set on read instances");
        }

        String collationServer = parameters.get(key).toString();
        String characterSetServer = parameters.get("character_set_server") == null ? null : parameters.get("character_set_server").toString();

        if (StringUtils.isEmpty(characterSetServer)) {
            // 如果只设置了collation，则校验characterset和collation的组合是否有效
            DbossApi dboss = SpringContextUtil.getBeanByClass(DbossApi.class);
            List<Map<String, String>> supportedCollations = dboss.getCollations(context.getCustinsId(), null);
            for (Map<String, String> collation : supportedCollations) {
                String collationName = collation.get("collation_name");
                // 如果是utf8mb3，需要把utf8_替换成utf8mb3_
                if (StringUtils.startsWith(collationName, "utf8mb3_")) {
                    collationServer = collationServer.replaceAll("^utf8_", "utf8mb3_");
                }
                if (collationServer.equalsIgnoreCase(collationName)) {
                    return true;
                }
            }
            return false;
        } else {
            // 如果同时更改了character和collation，跳过校验，留给character_helper校验
            return true;
        }
    }

    public static void main(String[] args) {
    }

}
