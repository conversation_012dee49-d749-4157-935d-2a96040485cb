package com.aliyun.dba.serverless.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointGroup;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ServerlessEndpointService {
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private ServerlessResourceService serverlessResourceService;

    public String getEndpointGroup(String requestId, String replicaSetName, String connectionString) throws Exception {
        String maxScaleName = serverlessResourceService.getMaxScaleName(requestId, replicaSetName);
        if (StringUtils.isEmpty(maxScaleName)) {
            log.error("requestId: {}, cannot find maxScale for replicaset: {}", requestId, replicaSetName);
            throw new Exception("cannot find maxScale");
        }

        val endpointList = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(
                requestId, maxScaleName, null, null, null, null);

        val endpoint = endpointList.getItems().stream()
                .filter(e -> StringUtils.isEmpty(connectionString) || StringUtils.equalsIgnoreCase(e.getAddress(), connectionString))
                .findFirst().orElse(null);

        if (endpoint == null) {
            log.error("requestId: {}, cannot find endpoint for maxScale: {}, connectionString: {}", requestId, maxScaleName, connectionString);
            throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
        }

        EndpointGroup endpointGroup = dBaasMetaService.getDefaultClient().getEndpointGroupById(requestId, endpoint.getEndpointGroupId(), true);

        if (endpointGroup == null) {
            log.error("requestId: {}, cannot find endpoint for maxScale: {}, connectionString: {}, endpointGroupId: {}", requestId, maxScaleName, connectionString, endpoint.getEndpointGroupId());
            throw new Exception("cannot find endpoint group");
        }

        return endpointGroup.getGroupName();
    }
}
