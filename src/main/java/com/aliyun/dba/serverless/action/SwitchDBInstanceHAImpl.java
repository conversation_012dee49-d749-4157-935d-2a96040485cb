package com.aliyun.dba.serverless.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.SwitchMasterDBInstanceHAService;
import com.aliyun.dba.poddefault.action.service.SwitchReadDBInstanceHAService;
import com.aliyun.dba.serverless.action.service.ServerlessSwitchDBInstanceHAService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessSwitchDBInstanceHAImpl")
public class SwitchDBInstanceHAImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(SwitchDBInstanceHAImpl.class);

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private ServerlessSwitchDBInstanceHAService serverlessSwitchDBInstanceHAService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            String dbInstanceName = paramSupport.getDBInstanceName(params);
            ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, dbInstanceName);
            Validate.notNull(replicaSetResource);
            ReplicaSet replicaSet = replicaSetResource.getReplicaSet();
            Validate.notNull(replicaSet);

            return serverlessSwitchDBInstanceHAService.serverlessSwitchDBInstanceHa(custInstanceDO, params);

        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        }
    }
}
