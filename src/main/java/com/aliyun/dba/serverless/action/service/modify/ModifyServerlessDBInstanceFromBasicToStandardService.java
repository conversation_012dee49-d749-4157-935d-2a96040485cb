package com.aliyun.dba.serverless.action.service.modify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.AllocateTmpResourceService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.AllocateTmpResourceResult;
import com.aliyun.dba.poddefault.action.support.PodAvzSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.service.ServerlessModifyDBInstanceService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_STANDARD;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE;

/**
 * 基础版升级高可用
 *
 * <AUTHOR> 2023/03/20.
 */
@Service
public class ModifyServerlessDBInstanceFromBasicToStandardService extends BaseModifyDBInstanceService {

    private static final LogAgent logger = LogFactory.getLogAgent(ModifyServerlessDBInstanceFromBasicToStandardService.class);
    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;
    @Resource
    private ResourceService resourceService;
    @Resource
    private PodAvzSupport podAvzSupport;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private ServerlessModifyDBInstanceService serverlessModifyDBInstanceService;
    @Resource
    private ServerlessResourceService serverlessResourceService;

    /**
     * 变配实例
     *
     * @param custins
     * @param params
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        Integer transListId;
        ReplicaSet replicaSetMeta;
        boolean isSuccess = false;
        boolean isAllocated = false;

        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();

        try {
            replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!(ReplicaSet.StatusEnum.ACTIVE.equals(replicaSetMeta.getStatus()) ||
                    ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSetMeta.getStatus()))) {
                throw new RdsException(ErrorCode.INVALID_STATUS);
            }

            if (replicaSetService.isReplicaSetExternalReplication(replicaSetMeta)) {
                logger.error("serverless external replication ins does not support upgrading to serverless_standard");
                throw new RdsException(ErrorCode.INVALID_INSTANCE_UPGRADE_TYPE);
            }

            // 初始化变配参数
            val modifyInsParam = serverlessModifyDBInstanceService.initServerlessConfigPodModifyInsParam(params);
            custins = modifyInsParam.getCustins();

            // innovation kernel filter
            if (minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId()).startsWith(MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE)) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }


            //zone config, do not allowe custom to decide the slave instance's zone
            val avzInfo = modifyInsParam.getAvzInfo();
            val masterReplica = Objects.requireNonNull(dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(modifyInsParam.getRequestId(),
                            modifyInsParam.getDbInstanceName(), null, null, null, null).getItems())
                    .stream().filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER)).findFirst().get();


            // Use the same zone as the source-instance
            String slaveAz = masterReplica.getZoneId();

            val azMap = new HashMap<Replica.RoleEnum, String>();
            azMap.put(Replica.RoleEnum.SLAVE, slaveAz);

            val subDomainMap = new HashMap<Replica.RoleEnum, String>();

            //Tune the RCU for migrate quickly
            Double tmpRcu = serverlessResourceService.getRcuForTmpInsByCurrent(requestId, modifyInsParam.getSrcServerlessSpec().getRcu(), modifyInsParam.getTargetServerlessSpec());
            modifyInsParam.setRcu(tmpRcu);
            AllocateTmpResourceResult result = allocateTmpResourceService.make(requestId, custins, modifyInsParam,
                    ServerlessConstant.SERVERLESS_STANDARD, Arrays.asList(Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE), azMap, subDomainMap);

            logger.info("Serverless modify AllocateResource result final is:{}", JSONObject.toJSONString(result));
            isAllocated = result.isAllocated();
            transListId = result.getTransList().getId();
            resourceRequest = result.getResourceRequest();


            // Update the tmp instance's avz info;
            val multiAVZExParamDO = avzInfo.getMultiAVZExParamDO();
            val availableZoneInfoList = multiAVZExParamDO.getAvailableZoneInfoList();

            val tmpMasterReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(modifyInsParam.getRequestId(),
                            result.getReplicaSet().getName(), null, null, null, null).getItems()
                    .stream().filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER)).findFirst().get();

            logger.info("TmpMasterReplica final is:{}", JSONObject.toJSONString(tmpMasterReplica));
            val masterLocation = tmpMasterReplica.getSubDomain();
            val masterAVZInfoDO = new AvailableZoneInfoDO(masterLocation, "master");
            val tmpSlaveReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(modifyInsParam.getRequestId(),
                            result.getReplicaSet().getName(), null, null, null, null).getItems()
                    .stream().filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.SLAVE)).findFirst().get();
            val slaveLocation = tmpSlaveReplica.getSubDomain();
            val slaveAVZInfoDO = new AvailableZoneInfoDO(slaveLocation, "slave");

            masterAVZInfoDO.setZoneID(masterReplica.getZoneId());
            slaveAVZInfoDO.setZoneID(tmpSlaveReplica.getZoneId());
            availableZoneInfoList.add(masterAVZInfoDO);
            availableZoneInfoList.add(slaveAVZInfoDO);
            val tmpCustinsId = Objects.requireNonNull(result.getReplicaSet().getId()).intValue();

            custinsParamService.setMultiAVZExParam(tmpCustinsId, JSON.toJSONString(multiAVZExParamDO));
            custinsParamService.setMasterLocation(tmpCustinsId,masterLocation);
            custinsParamService.setSlaveLocations(tmpCustinsId, Collections.singletonList(slaveLocation).toArray(new String[0]));

            // 审计日志管控参数补全
            // 集团业务不会使用此 Service，直接设置
            custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);

            // 更新系统参数模板
            val paramGroupIdDO = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
            if (paramGroupIdDO != null) {
                val paramGroupId = paramGroupIdDO.getValue();
                if (StringUtils.isNotEmpty(paramGroupId)) {
                    if (paramGroupId.startsWith("rpg-sys-")) {
                        val paramGroupInfo = SysParamGroupHelper.describeSysParamGroupId(paramGroupId);
                        paramGroupInfo.put("category", "standard");
                        val map = paramGroupInfo.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> (String) e.getValue()));
                        val newParamGroupId = SysParamGroupHelper.getSysParamGroupId(map);
                        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, newParamGroupId);
                        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, JSON.toJSONString(paramGroupInfo));
                        // next: 在任务流内切换管控参数
                    } else {
                        // 如果是用户参数模板，直接应用到临时实例
                        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
                    }
                }
            }

            // Create the task
            String taskKey = ServerlessConstant.TASK_MODIFY_SERVERLESS_BASIC_TO_STANDARD;
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", modifyInsParam.getRequestId());
            taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
            taskParamObject.put("transTaskId", transListId);
            taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
            // serverless信息
            taskParamObject.put(ServerlessConstant.SCALE_MIN, modifyInsParam.getTargetServerlessSpec().getScaleMin());
            taskParamObject.put(ServerlessConstant.SCALE_MAX, modifyInsParam.getTargetServerlessSpec().getScaleMax());
            taskParamObject.put(ServerlessConstant.AUTO_PAUSE, modifyInsParam.getTargetServerlessSpec().getAutoPause());


            String taskParam = taskParamObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask(
                    "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);

            // 实例参数Update
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, modifyInsParam.getDbInstanceName(), modifyInsParam.getTargetServerlessSpec().getLabels());

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    modifyInsParam.getRequestId(),
                    modifyInsParam.getDbInstanceName(),
                    modifyInsParam.isTransIns() ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.CLASS_CHANGING.toString()
            );

            // RCU上限从<=16被改为>16时，升级maxscale代理从1c到2c
            serverlessModifyDBInstanceService.modifyMaxscaleIns(modifyInsParam, params);

            // build response
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
            data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
            data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
            data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
            data.put("TaskId", taskId);
            data.put("Region", modifyInsParam.getRegionId());
            isSuccess = true;
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 变配失败释放资源
            if (isAllocated && !isSuccess && StringUtils.isNotEmpty(resourceRequest.getReplicaSetName())) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, resourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    

}
