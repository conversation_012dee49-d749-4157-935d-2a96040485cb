package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.TransferTask;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.serverless.action.service.ServerlessModifyDBInstanceService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessStopDBInstanceImpl")
public class StopDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(StopDBInstanceImpl.class);

    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private ServerlessResourceService serverlessResourceService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");

        // 通过openapi暂停serverless实例，会造成scc状态不一致&计费不更新，本期暂拦截，未来支持
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID, "");
        if (!StringUtils.equalsIgnoreCase(accessId, ServerlessConstant.SERVERLESS_SCC_ACCESS)) {
            logger.error(requestId + " serverless only support auto stop!");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        try {
            ReplicaSet replicaSetMeta;
            replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);

            if (!serverlessResourceService.isServerlessV2(replicaSetMeta)) {
                logger.error("{} ReplicaSet is not serverless v2.", requestId);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }
            else if (!PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "biz type " + replicaSetMeta.getBizType() + " not supported yet!");
            }
            else if (workFlowService.isTaskExist(requestId, replicaSetMeta.getName())) {
                logger.error("{} replicaset {} has unfinished tasks.", requestId, replicaSetMeta.getName());
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }

            Object taskId = null;
            boolean stopIns = replicaSetMeta.getStatus() == ReplicaSet.StatusEnum.STOPPED;
            // 释放计算资源
            if (stopIns) {
                taskId = stopIns(replicaSetMeta, params);
            } else {
                taskId = pauseIns(replicaSetMeta, params);
            }

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("DBInstanceID", replicaSetMeta.getId());
            data.put("DBInstanceName", replicaSetMeta.getName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error("StopDBInstance rds exception, requestId: {}, msg: {}", requestId, re.getMessage(), re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("StopDBInstance exception, requestId: {}, msg: {}", requestId, ex.getMessage(), ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 关停实例，释放计算资源
     * */
    private Object stopIns(ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");

        if (!ReplicaSet.StatusEnum.STOPPED.equals(replicaSetMeta.getStatus())) {
            logger.error("{} replicaset{} is not stopped.", requestId, replicaSetMeta.getStatus());
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }

        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSetMeta.getName(), null, null, null, null);

        Optional<Replica> activeReplicas = listReplicasInReplicaSet.getItems().stream().filter(x -> Replica.StatusEnum.ACTIVE.equals(x.getStatus())).findFirst();
        Object taskId = null;
        if (!activeReplicas.isPresent()) {
            // 可能是释放任务重复调度，直接返回成功
            logger.error("replicaSet {} has no active replica, return success, requestId: {}", replicaSetMeta.getName(), requestId);
        } else {
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", requestId);
            taskParamObject.put("replicaSetName", replicaSetMeta.getName());

            taskId = workFlowService.dispatchTask(
                    "custins",
                    replicaSetMeta.getName(),
                    PodDefaultConstants.DOMAIN_MYSQL,
                    ServerlessConstant.TASK_STOP_SERVERLESS_INS,
                    taskParamObject.toJSONString(),
                    0
            );
        }

        return taskId;
    }

    /**
     * 暂停实例
     * */
    private Object pauseIns(ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");


        List<ReplicaSet.StatusEnum> status = Arrays.asList(ReplicaSet.StatusEnum.ACTIVE, ReplicaSet.StatusEnum.ACTIVATION);
        if (!status.contains(replicaSetMeta.getStatus())) {
            logger.error("{} replicaset{} is not active.", requestId, replicaSetMeta.getStatus());
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }

        if (!(ReplicaSet.StatusEnum.ACTIVE.equals(replicaSetMeta.getStatus()) ||
                ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSetMeta.getStatus()))) {
            throw new RdsException(ErrorCode.INVALID_STATUS);
        }

        JSONObject taskParamObject = new JSONObject();
        taskParamObject.put("requestId", requestId);
        taskParamObject.put("replicaSetName", replicaSetMeta.getName());

        Object taskId = workFlowService.dispatchTask(
                "custins",
                replicaSetMeta.getName(),
                PodDefaultConstants.DOMAIN_MYSQL,
                ServerlessConstant.TASK_PAUSE_SERVERLESS_INS,
                taskParamObject.toJSONString(),
                0
        );
        dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                requestId,
                replicaSetMeta.getName(),
                ReplicaSet.StatusEnum.STOPPING.toString()
        );
        return taskId;
    }
}
