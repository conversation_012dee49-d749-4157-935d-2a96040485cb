package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.api.VpcMappingApi;
import com.aliyun.dba.adb_vip_manager_client.model.Link;
import com.aliyun.dba.adb_vip_manager_client.model.LinkDetailResponse;
import com.aliyun.dba.adb_vip_manager_client.model.LinkResponseWithRs;
import com.aliyun.dba.adb_vip_manager_client.model.RealServer;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessModifyDBInstanceVipImpl")
public class ModifyDBInstanceVipImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceVipImpl.class);
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private LinksApi linksApi;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private ServerlessResourceService serverlessResourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        ReplicaSet replicaSet;
        try {
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);

            // TODO: 等Proxy支持切换vsw和vpc后再放开
            if (serverlessResourceService.isServerlessV2(replicaSet)) {
                logger.info("{} Serverless with Proxy un support.");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            Integer targetNetType = paramSupport.getAndCheckNetType(params);

            if (!CustinsSupport.isVpcNetType(targetNetType)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }

            String vpcId = CheckUtils.checkValidForVPCId(paramSupport.getParameterValue(params, ParamConstants.VPC_ID));
            String vswitchId = CheckUtils.checkValidForVswitchId(paramSupport.getParameterValue(params, ParamConstants.VSWITCH_ID));
            String ipAddr = paramSupport.getParameterValue(params, ParamConstants.IP_ADDRESS);
            EndpointListResult endpointListResult = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null);
            if (CollectionUtils.isEmpty(endpointListResult.getItems())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }
            List<Endpoint> endpointList = endpointListResult.getItems()
                    .stream().filter(v -> (v.getNetType() == Endpoint.NetTypeEnum.VPC && v.getType() == Endpoint.TypeEnum.NORMAL))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(endpointList) || endpointList.size() > 1) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }

            String tmpConnPrefix = "tmp-";
            Optional<Endpoint> optional = endpointList.stream().filter(v -> (!v.getUserVisible() && v.getAddress().startsWith(tmpConnPrefix))).findFirst();
            if (optional.isPresent()) {
                //已经存在之前申请的临时链接，不允许再多申请
                logger.error("tmp VIP {},{} is already exists", optional.get().getIp(), optional.get().getPort());
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
            }

            Endpoint existsEndpoint = endpointList.get(0);
            if (!StringUtils.equalsIgnoreCase(existsEndpoint.getVpcId(), vpcId)) {
                throw new RdsException(ErrorCode.INVALID_VPC_ID);
            }

            Link linkBody = new Link();
            ReplicaListResult replicaListResult = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId,
                    replicaSet.getName(), null, null, null, null);

            int checkCount = 0;
            Vpod masterVpod = null;
            Replica masterReplica = null;
            for (Replica replica : replicaListResult.getItems()) {
                Vpod vpod = dBaasMetaService.getDefaultClient().getReplicaVpod(requestId, replica.getId(), null, null);
                if (replica.getRole() == Replica.RoleEnum.MASTER) {
                    masterVpod = vpod;
                    masterReplica = replica;
                    checkCount++;
                }
            }
            if (masterVpod == null) {
                logger.error("cannot find master server");
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            if (checkCount > 1) {
                logger.error("may be have more than one master in this replicaset");
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            linkBody.setVpcId(vpcId);
            linkBody.setVswitchId(vswitchId);
            linkBody.setRwType(Link.RwTypeEnum.NORMAL);
            linkBody.setNetType(CustinsSupport.NET_TYPE_VPC);
            linkBody.setMasterClusterName(masterVpod.getSiteName());
            linkBody.setPort(3306);
            linkBody.setRegionName(masterVpod.getRegionId());
            linkBody.setUserVisible(0);
            linkBody.setIp(ipAddr);
            linkBody.setConnAddrPrefix(tmpConnPrefix + replicaSet.getName());

            RealServer linkRealServer = new RealServer();
            linkRealServer.setEcsId(masterVpod.getEcsInsId());
            linkRealServer.setIp(masterVpod.getIp());
            linkRealServer.setPort(masterVpod.getPorts().get(0));

            if (replicaIsEniMode(masterReplica)) {
                EcsHost ecsHostByHostName = dBaasMetaService.getDefaultClient().getEcsHostByHostName(requestId, masterReplica.getHostName(), null);
                EniListResult eniListResult = dBaasMetaService.getDefaultClient().listEcsEnis(requestId, ecsHostByHostName.getEcsInsId());
                if (Objects.nonNull(eniListResult)
                        && CollectionUtils.isNotEmpty(eniListResult.getItems())
                        && Objects.nonNull(eniListResult.getItems().get(0))
                        && Objects.nonNull(eniListResult.getItems().get(0).getVpcId())) {
                    linkRealServer.setVpcId(eniListResult.getItems().get(0).getVpcId());
                } else {
                    logger.error("Cannot find eni VpcId from metaDB");
                    throw new RdsException(ErrorCode.INTERNAL_FAILURE);
                }
            }
            linkBody.setRealServers(Collections.singletonList(linkRealServer));
            //申请一个临时的域名（DNS + VIP + 反向VPC）
            LinkDetailResponse detailResponse = linksApi.createLink(replicaSet.getName(), linkBody, requestId);
            if (detailResponse.getData() == null || StringUtils.isBlank(detailResponse.getData().getIp())) {
                logger.error("create VIP error, msg is {}", detailResponse.toString());
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }

            LinkResponseWithRs linkResponseWithRs = detailResponse.getData();

            String newVip = linkResponseWithRs.getIp();
            String newPort = linkResponseWithRs.getPort();
            Map<String, Object> retData = new HashMap<>();
            try {
                // 做DNS级别VIP交换，生成changeLog给到任务流，
                List<EndpointChangeLog> changeLogList = new ArrayList<>();

                EndpointChangeLog updateEndpointChangeLog = new EndpointChangeLog();
                updateEndpointChangeLog.action(EndpointChangeLog.ActionEnum.UPDATE);
                updateEndpointChangeLog.fromConnAddrCust(existsEndpoint.getAddress());
                updateEndpointChangeLog.fromVip(existsEndpoint.getVip());
                updateEndpointChangeLog.fromVport(existsEndpoint.getVport().toString());
                updateEndpointChangeLog.fromUserVisible(existsEndpoint.getUserVisible());
                updateEndpointChangeLog.fromTunnelId(existsEndpoint.getTunnelId());
                updateEndpointChangeLog.fromUserVisible(existsEndpoint.getUserVisible());
                updateEndpointChangeLog.fromVpcId(existsEndpoint.getVpcId());

                updateEndpointChangeLog.toConnAddrCust(existsEndpoint.getAddress());
                updateEndpointChangeLog.setToVip(newVip);
                updateEndpointChangeLog.setToVport(newPort);
                updateEndpointChangeLog.setToUserVisible(false);
                updateEndpointChangeLog.setToConnAddrCust(linkResponseWithRs.getConnAddr());
                updateEndpointChangeLog.setToVpcId(vpcId);
                updateEndpointChangeLog.setToVswitchId(vswitchId);
                updateEndpointChangeLog.setNetType(EndpointChangeLog.NetTypeEnum.VPC);
                updateEndpointChangeLog.setStatus(EndpointChangeLog.StatusEnum.CREATING);
                updateEndpointChangeLog.setCreator(77);
                updateEndpointChangeLog.setModifier(77);
                updateEndpointChangeLog.setReplicaId(masterReplica.getId());
                updateEndpointChangeLog.setRwType(EndpointChangeLog.RwTypeEnum.NORMAL);

                //临时的需要释放掉
                EndpointChangeLog deleteEndpointChangeLog = new EndpointChangeLog();
                deleteEndpointChangeLog.action(EndpointChangeLog.ActionEnum.DELETE);
                deleteEndpointChangeLog.setFromConnAddrCust(existsEndpoint.getAddress());
                deleteEndpointChangeLog.setFromVip(existsEndpoint.getVip());
                deleteEndpointChangeLog.setFromVport(existsEndpoint.getVport().toString());
                deleteEndpointChangeLog.setFromUserVisible(false);
                deleteEndpointChangeLog.setFromVpcId(vpcId);
                deleteEndpointChangeLog.setFromConnAddrCust(linkResponseWithRs.getConnAddr());
                deleteEndpointChangeLog.setNetType(EndpointChangeLog.NetTypeEnum.VPC);
                deleteEndpointChangeLog.setStatus(EndpointChangeLog.StatusEnum.CREATING);
                deleteEndpointChangeLog.setCreator(77);
                deleteEndpointChangeLog.setModifier(77);
                deleteEndpointChangeLog.setReplicaId(masterReplica.getId());

                changeLogList.add(updateEndpointChangeLog);
                changeLogList.add(deleteEndpointChangeLog);

                JSONObject taskParam = new JSONObject();
                taskParam.put(ParamConstants.REQUEST_ID, requestId);
                taskParam.put("newVip", newVip);
                taskParam.put("newVport", newPort);
                taskParam.put("vpcId", vpcId);
                String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
                Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, PodDefaultConstants.TASK_MODIFY_VIP, taskParam.toJSONString(), 0);

                for (EndpointChangeLog endpointChangeLog : changeLogList) {
                    endpointChangeLog.setTaskId(Double.valueOf(taskId.toString()).intValue());
                    dBaasMetaService.getDefaultClient().createReplicaSetEndpointChangeLog(requestId, replicaSet.getName(), endpointChangeLog);
                }

                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_MAINTAINING.toString());
                retData.put("DBInstanceID", replicaSet.getId());
                retData.put("DBInstanceName", replicaSet.getName());
                retData.put("TaskId", taskId);
            } catch (Exception e) {
                logger.error("some exception occurs, try to release vip [{}]", newVip, e);
                linksApi.deleteLink(replicaSet.getName(), newVip, requestId);
                logger.error("release vip [{}] success", newVip);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            return retData;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }



    private boolean replicaIsEniMode(Replica replica) {
        return !(replica.getLinkIp() != null && replica.getLinkIp().equalsIgnoreCase(replica.getCtrlIp()));
    }

}

