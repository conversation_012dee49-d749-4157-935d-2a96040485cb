package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class StoppedServerlessMaintainService {
    @Autowired
    ReplicaSetService replicaSetService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Autowired
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;

    /**
     * 启动暂停实例，以便进行删除
     * if targetStatus is null, we will not change ins status during task
     * if targetStatus is not null, we will set ins status to starting
     * and update it to targetStatus when starting finished
     */
    public Map<String, Object> dispatchStartTask(String requestId, ReplicaSet replicaSet, ReplicaSet.StatusEnum targetStatus) throws Exception {
        if (!replicaSetService.isServerless(replicaSet)) {
            throw new Exception("replicaSet is not serverless!");
        }

        if (!ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus())) {
            throw new Exception("serverless status is not stopped, do not need start!");
        }

        Map<String, Object> data = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", requestId);
        jsonObject.put("replicaSetName", replicaSet.getName());

        // just for compatible，can be deleted after provider published
        if (replicaSetService.isReplicasetInStopStage2(requestId, replicaSet.getName())) {
            jsonObject.put("startInsFromStopStage2", "true");
        }

        // 设置资源模板,以便provider能成功申请到资源
        String rsTemplateName = podTemplateHelper.getRsTemplateNameForReplicaSet(requestId, replicaSet);
        if (StringUtils.isNotBlank(rsTemplateName)) {
            jsonObject.put("rsTemplateName", rsTemplateName);
        }

        if (targetStatus != null) {
            jsonObject.put("targetStatus", targetStatus.getValue());
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.STARTING.toString());
        }

        String parameter = jsonObject.toJSONString();
        Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(),
                PodDefaultConstants.DOMAIN_MYSQL, ServerlessConstant.TASK_START_SERVERLESS_FOR_MAINTAIN, parameter, 0);

        data.put("TaskId", taskId);
        data.put("DBInstanceName", replicaSet.getName());
        return data;
    }
}
