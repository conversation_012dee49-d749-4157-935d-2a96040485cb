package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.AES;
import com.aliyun.dba.support.utils.SignatureUtil;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.catalina.Server;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.*;

import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.SERVERLESS_CATEGORY_TYPE;

@Service
public class ServerlessResourceService {
    protected static final LogAgent logger = LogFactory.getLogAgent(ServerlessResourceService.class);

    private static final String DEFAULT_ACCESS_ID = "Pengine";

    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private PodAvzSupport podAvzSupport;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private RdsApi rdsApi;

    /**
     * 启动实例时，获取资源请求结构
     * */
    public ReplicaSetResourceRequest getResumeReplicasetResource(String requestId, String replicaSetName, Double rcu)
            throws Exception {
        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSetName, false);
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
        User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), false);
        String storageType = replicaSetService.getReplicaSetStorageType(replicaSet.getName(), requestId);
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(
                requestId,
                replicaSet.getService(),
                replicaSet.getServiceVersion(),
                replicaSet.getClassCode(),
                false
        );
        boolean isSingleTenant = replicaSetService
                .isCloudSingleTenant(replicaSet.getBizType(), storageType, instanceLevel, false);
        AVZInfo avzInfo = podAvzSupport.getAVZInfo(replicaSet);

        Endpoint endpoint = null;
        if (replicaSet.getConnType() != ReplicaSet.ConnTypeEnum.PHYSICAL) {
            EndpointListResult endpointList = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(
                    requestId, replicaSet.getName(), null, null, null, null);
            endpoint = endpointList.getItems().stream()
                    .filter(e -> Endpoint.NetTypeEnum.VPC.equals(e.getNetType())).findFirst().orElse(null);
            logger.info("replicaset %s, endpoint %s", replicaSet.getName(), endpoint);
            if (endpoint == null) {
                throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
            }
        }

        String tmpReplicaSetName = String.format("%s-start-%s", replicaSet.getName(), System.currentTimeMillis());

        replicaSetResourceRequest
                .userId(user.getBid())
                .uid(user.getAliUid())
                .bizType(replicaSet.getBizType().toString())
                .regionId(avzInfo.getRegionId())

                .primaryInsName(replicaSet.getName())
                .sourceReplicaSetName(replicaSet.getName())
                .replicaSetName(tmpReplicaSetName)
                .insType(ReplicaSet.InsTypeEnum.TMP.toString())

                // 版本
                .composeTag(minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, replicaSet.getId().intValue()))
                .dbType(replicaSet.getService())
                .dbVersion(replicaSet.getServiceVersion())

                // 规格
                .classCode(replicaSet.getClassCode())
                .serverlessRcu(rcu)

                // 磁盘资源
                .storageType(replicaSetService.getReplicaSetStorageType(replicaSet.getName(), requestId))
                .diskSize(replicaSet.getDiskSizeMB() / 1024)
                .scheduleTemplate(
                        podTemplateHelper.getReplicaSetScheduleTemp(replicaSet, instanceLevel, isSingleTenant, null)
                )

                //网络资源
                .connType(replicaSet.getConnType().toString())
                .vpcId(endpoint == null ? null : endpoint.getVpcId())
                .vswitchID(endpoint == null ? null : avzInfo.getMasterVSwitchId())
                .ignoreCreateVpcMapping(true);

        podTemplateHelper.setSpecSchedulerConfigSpread(replicaSetResourceRequest, replicaSet.getName());

        replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.SCHEDULE_LABEL_SERVERLESS, Boolean.TRUE.toString());

        // 需要迁移的角色
        List<Replica> currentReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId,
                replicaSet.getName(), null, null, null, null).getItems();
        List<ReplicaResourceRequest> replicasRequest = new ArrayList<ReplicaResourceRequest>();
        for (Replica replica : currentReplicas) {
            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(storageType);
            replicaResourceRequest.setDiskSize(replica.getDiskSizeMB() / 1024);
            replicaResourceRequest.setClassCode(replica.getClassCode());
            replicaResourceRequest.setSingleTenant(false);
            replicaResourceRequest.setRole(replica.getRole().getValue());
            replicaResourceRequest.setZoneId(replica.getZoneId());
            replicaResourceRequest.setSubDomain(replica.getSubDomain());

            replicasRequest.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicasRequest);

        return replicaSetResourceRequest;
    }

    /**
     * 获取实例的Serverless信息, 包括rcu, scale_min, scale_max
     * */
    public ServerlessSpec getServerlessSpec(String requestId, String replicaSetName) throws ApiException {
        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSetName);
        return new ServerlessSpec(labels);
    }


    // 根据目标实例获取rcu
    public Double getRcuForTmpInsByCurrent(String requestId, Double currRcu, ServerlessSpec serverlessSpec) {
        double targetScaleMax = serverlessSpec.getScaleMax();
        double targetScaleMin = serverlessSpec.getScaleMin();

        logger.info("get tmp ins from modify, requestId: {}, currRcu: {}, targetMin: {}, targetMax: {}", requestId, currRcu, targetScaleMin,targetScaleMax);

        // 比目标的最小还小，直接返回目标
        if (currRcu <= targetScaleMin) {
            return targetScaleMin;
        }

        // 比目标规格大，理论上前面就应该卡掉
        if (currRcu >= targetScaleMax) {
            return currRcu;
        }

        // 默认在中间，给的值稍微高一点，防止出问题
        return Math.min(Math.min(currRcu * 2, currRcu + 4), targetScaleMax);
    }


    // 根据原实例获取rcu
    public Double getRcuForTmpIns(String requestId, ReplicaSet replicaSet) throws Exception {
        if (!replicaSetService.isServerless(replicaSet)) {
            throw new Exception("only support get rcu for serverless!");
        }
        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
        double scaleMax = Double.parseDouble(labels.get(ServerlessConstant.SCALE_MAX));
        double currRcu = Double.parseDouble(labels.get(ServerlessConstant.SERVERLESS_RCU));
        if (currRcu >= scaleMax) {
            return currRcu;
        }
        return Math.min(Math.min(currRcu * 2, currRcu + 4), scaleMax);
    }

    /**
     * 更新实例的Serverless信息, 包括scale_min, scale_max, auto_pause, seconds_until_auto_pause, dasAutoPause, keepRunningTime
     * */
    public void setServerlessSpec(String requestId, String replicaSetName, ServerlessSpec serverlessSpec) throws ApiException {
        dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSetName, serverlessSpec.getLabels());
    }

    /**
     * 获取实例的MaxScale实例ID
     * */
    public Integer getMaxScaleID(String requestId, String replicaSetName) throws ApiException {
        Integer maxScaleID = null;
        ServiceRelation serviceRelation = getMaxScaleService(requestId, replicaSetName);
        if (serviceRelation != null) {
            maxScaleID = Integer.valueOf(serviceRelation.getServiceId());
        }
        return maxScaleID;
    }

    /**
     * 获取实例的MaxScale实例名
     * */
    public String getMaxScaleName(String requestId, String replicaSetName) throws ApiException {
        String maxScaleName = null;
        ServiceRelation serviceRelation = getMaxScaleService(requestId, replicaSetName);
        if (serviceRelation != null) {
            maxScaleName = serviceRelation.getServiceName();
        }
        return maxScaleName;
    }

    /**
     * 获取实例对应MaxScale的ServiceRelation
     * */
    public ServiceRelation getMaxScaleService(String requestId, String replicaSetName) throws ApiException {
        ServiceRelation serviceRelation = null;
        ServiceRelationListResult serviceRelationList = dBaasMetaService.getDefaultClient()
                .listReplicaSetServices(requestId, replicaSetName);
        List<ServiceRelation.StatusEnum> statusEnumList = new ArrayList<ServiceRelation.StatusEnum>() {{
            this.add(ServiceRelation.StatusEnum.ACTIVE);
            this.add(ServiceRelation.StatusEnum.CREATING);
        }};
        if (serviceRelationList.getItems() != null) {
            Optional<ServiceRelation> s = serviceRelationList
                    .getItems()
                    .stream()
                    .filter(r -> "maxscale".equalsIgnoreCase(r.getServiceRole()) && statusEnumList.contains(r.getStatus()))
                    .findFirst();
            if (s.isPresent()) {
                serviceRelation = s.get();
            }
        }
        return serviceRelation;
    }

    /**
     * 升级maxscale内核版本
     * */
    public Map<String,Object> upgradeMaxscaleDBVersion(ReplicaSet replicaSet,String requestId,Map<String, String> params) throws RdsException {
        try {

            Map<String, String> proxyApiParams = getProxyParam(params);
            proxyApiParams.put(ParamConstants.ACTION.toLowerCase(), "UpgradeDBVersion");
            proxyApiParams.put(ParamConstants.SWITCH_TIME,mysqlParamSupport.getParameterValue(params,ParamConstants.SWITCH_TIME));
            proxyApiParams.put(ParamConstants.SWITCH_TIME_MODE,mysqlParamSupport.getParameterValue(params,ParamConstants.SWITCH_TIME_MODE));
            proxyApiParams.put(ParamConstants.DB_INSTANCE_NAME,getMaxScaleName(requestId,replicaSet.getName()));
            proxyApiParams.put(ParamConstants.SHARDS_INFO,mysqlParamSupport.getParameterValue(params,ParamConstants.SHARDS_INFO));
            proxyApiParams.put("MinorVersion",mysqlParamSupport.getParameterValue(params, "MinorVersion"));
            Map<String, Object> proxyApiData = invokeProxyApi(proxyApiParams);

            return proxyApiData;
        } catch (Exception e) {
            logger.error("{} upgrade maxscale minorversion failed. Exception: {}", requestId, e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public boolean isServerlessV1(ReplicaSet replicaSet) {
        return SERVERLESS_CATEGORY_TYPE.contains(replicaSet.getCategory()) && replicaSet.getConnType() == ReplicaSet.ConnTypeEnum.LVS;
    }

    public boolean isServerlessV2(ReplicaSet replicaSet) {
        return SERVERLESS_CATEGORY_TYPE.contains(replicaSet.getCategory()) && replicaSet.getConnType() == ReplicaSet.ConnTypeEnum.PHYSICAL;
    }

    // if serverless basic Type
    public boolean isServerlessBasic(ReplicaSet replicaSet) {
        return ServerlessConstant.SERVERLESS_BASIC.equalsIgnoreCase(replicaSet.getCategory());
    }


    public Map<String, String> getProxyParam(Map<String, String> params) {
        // 使用原requestId 加上随机后缀，以方便日志搜索
        String requestId;
        String oriRequestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        if (StringUtils.isNotBlank(oriRequestId)) {
            requestId = String.format("%s_%s", oriRequestId, new Random().nextInt(Integer.MAX_VALUE));
        } else {
            requestId = SignatureUtil.getRequestId();
        }

        Map<String, String> proxyApiParams = new HashMap<>();
        proxyApiParams.put(ServerlessConstant.RDS_API_WITH_LOCK, "false");
        proxyApiParams.put("TimeStamp", SignatureUtil.getTimeStamp());
        proxyApiParams.put(ParamConstants.ACCESSID, DEFAULT_ACCESS_ID);
        proxyApiParams.put(ParamConstants.REQUEST_ID, requestId);
        proxyApiParams.put(ParamConstants.DB_INSTANCE_NAME, mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_NAME));
        proxyApiParams.put(ParamConstants.REGION_ID, mysqlParamSupport.getParameterValue(params, ParamConstants.REGION_ID));
        proxyApiParams.put(ParamConstants.USER_ID, mysqlParamSupport.getParameterValue(params, ParamConstants.USER_ID));
        proxyApiParams.put(ParamConstants.UID, mysqlParamSupport.getParameterValue(params, ParamConstants.UID));
        return proxyApiParams;
    }

    public String transferRcu(Double minRcu, Double maxRcu) {
        Map<String, String> serverlessClass = new HashMap();
        Double minCpu = minRcu;
        Double maxCpu = maxRcu;
        Double minMemory = 2 * minRcu;
        Double maxMemory = 2 * maxRcu;

        serverlessClass.put("minCpu", String.valueOf(minCpu));
        // Use 'Gi' or 'Mi', 'Mb' will cause errors.
        serverlessClass.put("minMemory", String.valueOf(minMemory.intValue()) + "Gi");
        serverlessClass.put("maxCpu", String.valueOf(maxCpu));
        serverlessClass.put("maxMemory", String.valueOf(maxMemory.intValue()) + "Gi");
        return JSONObject.toJSONString(serverlessClass);
    }

    // Pass RCU limits to Lafite when Create and Start serverless instance
    public Map<String, String> getAnnotations(Map<String, Boolean> activities, Double minRcu, Double maxRcu) {
        Map<String, String> annotations = new HashMap();
        StringJoiner joiner = new StringJoiner(",");
        if(null != minRcu && null != maxRcu) {
            String serverlessClass = transferRcu(minRcu, maxRcu);
            annotations.put(ServerlessConstant.KEY_SERVERLESS_CU_LIMIT, serverlessClass);
        }

        if(activities != null) {
            for(String activity : activities.keySet()){
                if (activities.get(activity)){
                    // if freeTierActivity ins modify maxRcu > 2.0, don't pass "freeTierActivity" label
                    if ("freeTierActivity".equals(activity) && maxRcu > 2.0){
                        continue;
                    }
                    joiner.add(activity);
                }
            }
            annotations.put(ServerlessConstant.KEY_BIZ_TAGS, joiner.toString());
        }
        logger.info("Annotations : {}", JSONObject.toJSONString(annotations));
        return annotations;
    }

    public Map<String, Object> invokeProxyApi(Map<String, String> params) throws RdsException {
        try {
            Map<String, String> proxyApiParams = new HashMap<>();

            // 排除NULL的字段，避免签名生成不一致
            for (String key : params.keySet()) {
                if (params.get(key) != null) {
                    proxyApiParams.put(key, params.get(key));
                }
            }

            logger.info("Call Proxy API Params {}", proxyApiParams);
            return rdsApi.doRdsApiCallWithSignature(proxyApiParams, proxyApiParams.get(ParamConstants.ACCESSID));
        } catch (RdsException e) {
            logger.error(e);
            throw e;
        } catch (Exception e) {
            logger.error("doRdsApiCallWithSignature error occured: " + e.getMessage());
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
