package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.TransferTask;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.AligroupModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.TransferK8sToPhysicalService;
import com.aliyun.dba.poddefault.action.service.modify.AliyunModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.ModifyDBInstanceFromBasicToStandardService;
import com.aliyun.dba.serverless.action.service.ServerlessModifyDBInstanceService;
import com.aliyun.dba.serverless.action.service.modify.ModifyServerlessDBInstanceFromBasicToStandardService;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.serverless.action.service.ServerlessUpgradeService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.service.TaskGrayService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessModifyDBInstanceClassImpl")
public class ModifyDBInstanceClassImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceClassImpl.class);
//    @Resource
//    protected MysqlParamSupport paramSupport;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Autowired
    private ReplicaSetService replicaSetService;
    @Resource
    private ServerlessModifyDBInstanceService serverlessModifyDBInstanceService;
    @Resource
    private ModifyServerlessDBInstanceFromBasicToStandardService modifyServerlessDBInstanceFromBasicToStandardService;

    @Resource
    private ServerlessUpgradeService serverlessUpgradeService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        ReplicaSet replicaSetMeta;
        boolean isTddlTaskMigrate;
        // add remote scale if not exist for history instance
        String allowRemoteScale;
        // Get instance info
        try {
            replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            isTddlTaskMigrate = replicaSetService.isTddlTaskMigrate(requestId, replicaSetMeta);
            allowRemoteScale = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetMeta.getName(), ServerlessConstant.ALLOW_REMOTE_SCALE);
        } catch (ApiException e) {
            logger.error(e.getMessage(), e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }

        // 集团TDDL实例 NOT SUPPORT
        if (ReplicaSetService.isTDDL(replicaSetMeta) && !isTddlTaskMigrate) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCETYPE);
        }

        // add if not exist remote config

        if (StringUtils.isEmpty(allowRemoteScale)) {
            Map<String, String> tempLabel = ImmutableMap.of(ServerlessConstant.ALLOW_REMOTE_SCALE, ServerlessConstant.DEFAULT_ALLOW_REMOTE_SCALE);
            try {
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSetMeta.getName(), tempLabel);
            } catch (ApiException e) {
                logger.error("add allow remote scale failed", e);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
        }

        // Do  support change category;
        String targetLevelCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
        if (targetLevelCode != null) {
            String dbVersion = replicaSetMeta.getServiceVersion();
            InstanceLevelDO newLevel = instanceService.getInstanceLevelByClassCode(targetLevelCode, replicaSetMeta.getService(), dbVersion, null, null);
            String oldClassCode = replicaSetMeta.getClassCode();
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByClassCode(oldClassCode, replicaSetMeta.getService(), dbVersion, null, null);
            if (!newLevel.getCategory().equals(oldLevel.getCategory()) && !replicaSetService.isActive(replicaSetMeta)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            //支持修改category from basic to standard
            if (newLevel.getCategory().equalsIgnoreCase(ServerlessConstant.SERVERLESS_STANDARD)
                    && oldLevel.getCategory().equalsIgnoreCase(ServerlessConstant.SERVERLESS_BASIC)){
                return modifyServerlessDBInstanceFromBasicToStandardService.doActionRequest(custins, params);
            }


            //Not support from standard to  basic
            if (InstanceLevel.CategoryEnum.SERVERLESS_BASIC.toString().equals(newLevel.getCategory())
                    && InstanceLevel.CategoryEnum.SERVERLESS_STANDARD.toString().equals(oldLevel.getCategory())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
            }

            // serverless provision 互转
            if (serverlessUpgradeService.isProvisionToServerless(replicaSetMeta, newLevel.getCategory())) {
                return serverlessUpgradeService.migrateProvisionToServerless(params);
            }
            if (serverlessUpgradeService.isServerlessToProvision(replicaSetMeta, newLevel.getCategory())) {
                return serverlessUpgradeService.migrateServerlessToProvision(params);
            }

            //不支持修改category
            if (!newLevel.getCategory().equals(replicaSetMeta.getCategory())){
                throw new RdsException(ErrorCode.INVALID_PARAM_CATEGORY);
            }

        }


        // judge Modify type
        String accessID = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID, ServerlessConstant.SERVERLESS_OPENAPI_ACCESS);
        if (accessID.equalsIgnoreCase(ServerlessConstant.SERVERLESS_SCC_ACCESS)){
            return serverlessModifyDBInstanceService.doSccActionRequest(custins, params);
        } else {
            return serverlessModifyDBInstanceService.doActionRequest(custins, params);
        }

    }


}
