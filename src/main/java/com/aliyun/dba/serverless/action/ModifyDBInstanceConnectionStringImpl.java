package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.serverless.action.service.ServerlessEndpointService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.DOMAIN_MYSQL;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_SERVERLESS_MODIFY_NET_TYPE;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessModifyDBInstanceConnectionStringImpl")
public class ModifyDBInstanceConnectionStringImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceConnectionStringImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;
    @Resource
    private ServerlessEndpointService serverlessEndpointService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (serverlessResourceService.isServerlessV2(replicaSet)) {
                return modifyDBProxyEndpointAddress(custins, params);
            } else {
                return modifyDBInstanceConnectionString(custins, params);
            }
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(String.format("%s get replica set failed.", requestId), e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }

    }

    /**
     * 普通实例
     * */
    private Map<String, Object> modifyDBInstanceConnectionString(CustInstanceDO custins, Map<String, String> params) {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        EndPoint endPointForAllocate = null;
        ReplicaSet replicaSet = null;
        try {
            replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            ReplicaListResult replicaList = metaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null,null,null,null);
            if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }
            if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            // 检查是否修改次数超过上限
            // TODO: change checkConnAddrChangeTimesExceed custins id to Long
            custinsService.checkConnAddrChangeTimesExceed(Integer.parseInt(replicaSet.getId().toString()), paramSupport.getAction(params), null);

            Integer netType = paramSupport.getNetType(params);
            NetTypeEnum netTypeEnum = PodDefaultConstants.NET_TYPE_ENUM_MAP.get(netType);
            String connectionString = paramSupport.getConnectionString(params);
            if (netType == null && connectionString == null) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            EndpointListResult endpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null);
            final NetTypeEnum finalNetTypeEnum = netTypeEnum;
            Optional<Endpoint> findEndpointExist = endpointListResult.getItems().stream().filter(e -> e.getUserVisible() && ((finalNetTypeEnum == null || e.getNetType().toString().equals(finalNetTypeEnum.toString())) && (connectionString == null || e.getAddress().equals(connectionString)))).findFirst();
            if (!findEndpointExist.isPresent()) {
                throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
            }
            Endpoint endpointExist = findEndpointExist.get();

            String newConnectionString = paramSupport.getNewConnectionString(params);
            String oldConnectionString = endpointExist.getAddress();
            Vpod replicaVpod = metaService.getDefaultClient().getReplicaVpod(requestId, replicaList.getItems().get(0).getId(), null, null);
            if (newConnectionString != null) {
                CheckUtils.checkValidForConnAddrCust(newConnectionString);
            } else {
                String connAddrCustLast = mysqlParamSupport.getConnAddrCustLast(replicaVpod.getRegionId(), replicaSet.getService());
                String prefixOfOldConnectionString = StringUtils.replace(oldConnectionString, connAddrCustLast, "");
                // 兼容region级last conn addr last 配置前后 获取到的last不一样的情况，如果没有替换成功，则再用配置前的替换一次
                if(StringUtils.isNotEmpty(oldConnectionString) && oldConnectionString.equals(prefixOfOldConnectionString)){
                    connAddrCustLast = mysqlParamSupport.getConnAddrCustLast(replicaSet.getService());
                    prefixOfOldConnectionString = StringUtils.replace(oldConnectionString, connAddrCustLast, "");
                }
                newConnectionString = prefixOfOldConnectionString;
            }
            String newPort = paramSupport.getNewPort(params);
            Optional<Replica> replica = metaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems().stream().filter(r -> r.getRole().equals(Replica.RoleEnum.MASTER)).findFirst();
            if (!replica.isPresent()) {
                throw new RdsException(ErrorCode.HOST_INSTANCE_NOT_FOUND);
            }
            String oldPort = endpointExist.getVport().toString();
            if (newPort != null) {
                newPort = CheckUtils.parseInt(newPort, 1000, 65534, ErrorCode.INVALID_PORT).toString();
            } else {
                newPort = oldPort;
            }
            boolean connectionStringChanged = !StringUtils.equals(oldConnectionString, mysqlParamSupport.getConnAddrCust(newConnectionString, replicaVpod.getRegionId(), replicaSet.getService()));
            if (!connectionStringChanged && StringUtils.equals(newPort, oldPort)) {
                throw new RdsException(ErrorCode.INVALID_CONNECTIONSTRING_OR_PORT);
            }
            if (connectionStringChanged) {
                endPointForAllocate = new EndPoint().domainPrefix(newConnectionString)
                        .ip(endpointExist.getVip())
                        .port(newPort)
                        .connType(EndPoint.ConnTypeEnum.valueOf(replicaSet.getConnType().name()))
                        .netType(NetTypeEnum.valueOf(endpointExist.getNetType().name()))
                        .endPointType(EndPoint.EndPointTypeEnum.valueOf(endpointExist.getType().name()));
                try {
                    commonProviderService.getDefaultApi().allocateConnectionString(requestId, replicaSet.getName(), endPointForAllocate);
                } catch (ApiException e) {
                    throw new RdsException(ErrorCode.OTHER_ENDPOINT_EXIST);
                }
            }

            EndpointChangeLog endpointChangeLog = new EndpointChangeLog();
            Integer operatorId = paramSupport.getOperatorId(params);
            endpointChangeLog.action(EndpointChangeLog.ActionEnum.UPDATE)
                    .replicaId(replica.get().getId())
                    .taskId(0)
                    .creator(operatorId)
                    .modifier(operatorId)
                    .fromUserVisible(true)
                    .toUserVisible(true)
                    .fromConnAddrCust(endpointExist.getAddress())
                    .fromVport(oldPort)
                    .netType(EndpointChangeLog.NetTypeEnum.valueOf(endpointExist.getNetType().name()))
                    .rwType(EndpointChangeLog.RwTypeEnum.valueOf(endpointExist.getType().name()))
                    .fromVpcId(endpointExist.getVpcId())
                    .fromVip(endpointExist.getVip())
                    .toConnAddrCust(newConnectionString)
                    .toVip(endpointExist.getVip())
                    .toVport(newPort)
                    .status(EndpointChangeLog.StatusEnum.CREATING);
            EndpointChangeLog changeLogCreated = metaService.getDefaultClient().createReplicaSetEndpointChangeLog(requestId, replicaSet.getName(), endpointChangeLog);

            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put("changeLogId", changeLogCreated.getId());

            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = PodDefaultConstants.TASK_MODIFY_ENDPOINT;
            Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, taskParam.toString(), 0);
            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_MAINTAINING.toString());

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (endPointForAllocate != null) {
                try {
                    commonProviderService.getDefaultApi().deleteConnectionString(requestId, replicaSet.getName(), endPointForAllocate);
                } catch (ApiException e) {
                    logger.error("release connection string failed!");
                }
            }
        }
    }

    /**
     * Serverless with Proxy架构
     * */
    private Map<String, Object> modifyDBProxyEndpointAddress(CustInstanceDO custins, Map<String, String> params) {
        String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION && replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVE) {
                logger.error("{} replicaset is not active.", requestId);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String connectionString = mysqlParameterHelper.getParameterValue(ParamConstants.CONNECTION_STRING);
            Integer netType = paramSupport.getNetType(params);
            if(netType == null && connectionString == null){
                logger.error("{} One of the parameters of NetType or ConnectionString must be specified", requestId);
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }

            if (netType == null) {
                Integer maxScaleId = serverlessResourceService.getMaxScaleID(requestId, replicaSet.getName());
                if (maxScaleId == null) {
                    logger.error("{} MaxScale not found for {}", requestId, custins.getInsName());
                    throw new RdsException(ErrorCode.INVALID_DBMXS_INS_NAME);
                }
                List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                        .getCustinsConnAddrByCustinsId(maxScaleId, null, CustinsSupport.RW_TYPE_NORMAL);
                for (CustinsConnAddrDO connAddrDO : custinsConnAddrList) {
                    if (connectionString.equals(connAddrDO.getConnAddrCust()) && connAddrDO.isConnAddrUserVisible()) {
                        netType = connAddrDO.getNetType();
                    }
                }
            }

            if (netType == null) {
                logger.error("{} NetType not found.", requestId);
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }

            String endpointGroup = serverlessEndpointService.getEndpointGroup(requestId, replicaSet.getName(), connectionString);

            Map<String, String> proxyApiParams = serverlessResourceService.getProxyParam(params);
            proxyApiParams.put(ParamConstants.ACTION.toLowerCase(), "ModifyDBProxyEndpointAddress");
            proxyApiParams.put(ParamConstants.ENDPOINT_NAME, endpointGroup);
            proxyApiParams.put(ParamConstants.CONNECTION_STRING, mysqlParameterHelper.getParameterValue(ParamConstants.CONNECTION_STRING));
            proxyApiParams.put("NewConnectionStringPrefix", mysqlParameterHelper.getParameterValue("newconnectionstring"));
            proxyApiParams.put("NewPort", mysqlParameterHelper.getParameterValue("NewPort"));
            proxyApiParams.put(ParamConstants.DB_INSTANCE_NET_TYPE.toLowerCase(), netType.toString());

            logger.info("{} ProxyAPI Request {}", proxyApiParams);
            Map<String, Object> proxyApiResponse = serverlessResourceService.invokeProxyApi(proxyApiParams);
            logger.info("{} ProxyAPI Response {}", proxyApiResponse);
            Map<String, Object> proxyApiData = (Map<String, Object>) proxyApiResponse.get("Data");
            String proxyTaskId = proxyApiData.get("TaskId").toString();

            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_MAINTAINING.toString());

            JSONObject taskParams = new JSONObject();
            taskParams.put("proxyTaskId", proxyTaskId);
            Object taskId = workFlowService.dispatchTask(
                    "custins",
                    replicaSet.getName(),
                    DOMAIN_MYSQL,
                    TASK_SERVERLESS_MODIFY_NET_TYPE,
                    taskParams.toString(),
                    0);

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TaskId", taskId);
            return data;

        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } catch (Exception e) {
            logger.error(requestId + " unexpected exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
