package com.aliyun.dba.serverless.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.serverless.action.service.ServerlessCreateDBInstanceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessCreateDBInstanceImpl")
@Slf4j
public class CreateDBInstanceImpl implements IAction {

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    private ServerlessCreateDBInstanceService aliyunDBInstanceService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);

        Integer userId = mysqlParameterHelper.getAndCreateUserId();
        mysqlParameterHelper.checkUserOperatorCluster(userId);


        return aliyunDBInstanceService.createDBInstance(custins, params);

    }
}
