package com.aliyun.dba.serverless.action.service.modify;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;

import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;

/**
 * <AUTHOR> on 2024/2/26.
 */
@Service
public class ModifyServerlessDBInstanceOptimizedWritesService extends BaseModifyDBInstanceService {
    @Resource
    private PodCommonSupport podCommonSupport;

    /**
     * Modify instance
     *
     * @param primaryOptimizedWritesInfo
     * @param targetOptimizedWrites
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doActionRequest(String primaryOptimizedWritesInfo, boolean targetOptimizedWrites, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        PodModifyInsParam modifyInsParam = null;
        try {
            // Initialize modify parameters
            modifyInsParam = initPodModifyInsParam(params);
            /********** filter condition Start for all situations**********/
            podCommonSupport.checkOptimizedWritesCondition(requestId, modifyInsParam.getDbVersion(), modifyInsParam.getSrcDiskType());

            /********** filter condition Ens **********/
            String taskKey;
            Map<String, Boolean> optimizedWritesInfoMap = new HashMap<>();
            optimizedWritesInfoMap.put(CustinsParamSupport.OPTIMIZED_WRITES, targetOptimizedWrites);
            boolean targetInitOptimizedWrites = (modifyInsParam.getTargetInitOptimizedWritesString() != null) && Boolean.parseBoolean(modifyInsParam.getTargetInitOptimizedWritesString());
            optimizedWritesInfoMap.put(CustinsParamSupport.INIT_OPTIMIZED_WRITES, targetInitOptimizedWrites);
            String targetOptimizedWritesInfo = JSON.toJSONString(optimizedWritesInfoMap);
            if (podCommonSupport.isInitOptimizedWrites(primaryOptimizedWritesInfo)) {
                taskKey = TASK_MODIFY_OPTIMIZED_WRITES;
            } else {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // dispatch task
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", modifyInsParam.getRequestId());
            taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
            taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());
            taskParamObject.put("optimizedWrites", podCommonSupport.isOptimizedWrites(primaryOptimizedWritesInfo));
            taskParamObject.put("targetOptimizedWrites", targetOptimizedWrites);
            taskParamObject.put("OptimizedWritesInfo", primaryOptimizedWritesInfo);
            taskParamObject.put("targetOptimizedWritesInfo", targetOptimizedWritesInfo);
            taskParamObject.put("targetInitOptimizedWrites", true);

            String taskParam = taskParamObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask(
                    "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    modifyInsParam.getRequestId(),
                    modifyInsParam.getDbInstanceName(),
                    ReplicaSet.StatusEnum.INS_MAINTAINING.toString()
            );
            // build response
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
            data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
            data.put("OptimizedWrites", podCommonSupport.isOptimizedWrites(primaryOptimizedWritesInfo));
            data.put("targetOptimizedWrites", targetOptimizedWrites);
            data.put("OptimizedWritesInfo", primaryOptimizedWritesInfo);
            data.put("targetInitOptimizedWrites", true);
            data.put("TaskId", taskId);
            data.put("Region", modifyInsParam.getRegionId());
            return data;
        } catch (RdsException re) {
            logger.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
        // Not involved in resource application yet.
    }
}