package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.serverless.action.service.ServerlessEndpointService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.DOMAIN_MYSQL;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_SERVERLESS_DELETE_NET_TYPE;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessDeleteDBInstanceNetTypeImpl")
public class DeleteDBInstanceNetTypeImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(DeleteDBInstanceNetTypeImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Resource
    private ServerlessEndpointService serverlessEndpointService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (serverlessResourceService.isServerlessV2(replicaSet)) {
                return deleteDBProxyEndpointAddress(custins, params);
            } else {
                return deleteDBInstanceNetType(custins, params);
            }
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        }  catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, Object> deleteDBInstanceNetType(CustInstanceDO custins, Map<String, String> params) throws Exception {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
        if (ReplicaSet.LockModeEnum.LOCKMANUAL == replicaSet.getLockMode()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }
        if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        // 检查是否修改次数超过上限
        // TODO: change checkConnAddrChangeTimesExceed custins id to Long
        custinsService.checkConnAddrChangeTimesExceed(Integer.parseInt(replicaSet.getId().toString()), paramSupport.getAction(params), null);

        Optional<Replica> replica = metaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems().stream().filter(r -> r.getRole().equals(Replica.RoleEnum.MASTER)).findFirst();
        if (!replica.isPresent()){
            throw new RdsException(ErrorCode.HOST_INSTANCE_NOT_FOUND);
        }
        Integer netType = paramSupport.getNetType(params);
        NetTypeEnum netTypeEnum = PodDefaultConstants.NET_TYPE_ENUM_MAP.get(netType);
        String connectionString = paramSupport.getConnectionString(params);
        if(netType == null && connectionString == null){
            throw new RdsException(ErrorCode.INVALID_PARAM);

        } else if (CustinsSupport.NET_TYPE_VPC.equals(netType) &&
                podParameterHelper.isClusterUserVPCArch(custins.getDbType(), custins.getDbVersion(), custins.getClusterName())) {
            // Comment by Heyin:
            // Forbid all requests of deleting vpc net type, no matter db version
            // Rewrite this part when classic net type on k8s is ready
            logger.warn("isClusterUserVPCArch cluster, can not delete vpc net.");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);

        }

        EndpointListResult endpointListResult = metaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, null, null, null);
        final NetTypeEnum finalNetTypeEnum = netTypeEnum;
        Optional<Endpoint> findEndpointExist = endpointListResult.getItems().stream().filter(e -> e.getUserVisible() && ((finalNetTypeEnum==null || e.getNetType().toString().equals(finalNetTypeEnum.toString())) && (connectionString == null || e.getAddress().equals(connectionString)))).findFirst();
        if(!findEndpointExist.isPresent()){
            throw new RdsException(ErrorCode.ENDPOINT_NOT_FOUND);
        }
        Endpoint endpointExist = findEndpointExist.get();
        Integer operatorId = paramSupport.getOperatorId(params);
        EndpointChangeLog endpointChangeLog = new EndpointChangeLog().action(EndpointChangeLog.ActionEnum.DELETE).replicaId(replica.get().getId()).taskId(0).creator(operatorId).modifier(operatorId).fromUserVisible(true).toUserVisible(true
        ).fromConnAddrCust(endpointExist.getAddress()).fromVport(endpointExist.getVport().toString()
        ).netType(EndpointChangeLog.NetTypeEnum.valueOf(endpointExist.getNetType().name())).rwType(EndpointChangeLog.RwTypeEnum.valueOf(endpointExist.getType().name())
        ).fromVpcId(endpointExist.getVpcId()).fromVip(endpointExist.getVip()).status(EndpointChangeLog.StatusEnum.CREATING);
        EndpointChangeLog changeLogCreated = metaService.getDefaultClient().createReplicaSetEndpointChangeLog(requestId,replicaSet.getName(), endpointChangeLog);
        metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_DELETING.toString());

        JSONObject taskParam = new JSONObject();
        taskParam.put("requestId", requestId);
        taskParam.put("changeLogId", changeLogCreated.getId());
        String domain = DOMAIN_MYSQL;
        String taskKey = PodDefaultConstants.TASK_MODIFY_ENDPOINT;
        Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, taskParam.toString(), 0);
        Map<String, Object> data = new HashMap<>();
        data.put("DBInstanceID", replicaSet.getId());
        data.put("DBInstanceName", replicaSet.getName());
        data.put("ConnectionString", endpointExist.getAddress());
        data.put("TaskId", taskId);
        return data;
    }

    /**
     * Serverless with Proxy架构处理
     * */
    private Map<String, Object> deleteDBProxyEndpointAddress(CustInstanceDO custins, Map<String, String> params) throws Exception {
        try {
            String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION && replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVE) {
                logger.error("{} replicaset is not active.", requestId);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String connectionString = mysqlParameterHelper.getParameterValue(ParamConstants.CONNECTION_STRING);
            Integer netType = paramSupport.getNetType(params);
            if (netType == null && connectionString == null) {
                logger.error("{} One of the parameters of NetType or ConnectionString must be specified", requestId);
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            if (netType == null) {
                Integer maxScaleId = serverlessResourceService.getMaxScaleID(requestId, custins.getInsName());
                if (maxScaleId == null) {
                    logger.error("{} MaxScale not found for {}", requestId, custins.getInsName());
                    throw new RdsException(ErrorCode.INVALID_DBMXS_INS_NAME);
                }
                List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                        .getCustinsConnAddrByCustinsId(maxScaleId, null, CustinsSupport.RW_TYPE_NORMAL);
                for (CustinsConnAddrDO connAddrDO : custinsConnAddrList) {
                    if (connectionString.equals(connAddrDO.getConnAddrCust()) && connAddrDO.isConnAddrUserVisible()) {
                        netType = connAddrDO.getNetType();
                    }
                }
            }

            if (netType == null) {
                logger.error("{} NetType not found.", requestId);
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            String endpointGroup = serverlessEndpointService.getEndpointGroup(requestId, replicaSet.getName(), connectionString);

            Map<String, String> proxyApiParams = serverlessResourceService.getProxyParam(params);
            proxyApiParams.put(ParamConstants.ACTION.toLowerCase(), "DeleteDBProxyEndpointAddress");
            proxyApiParams.put(ParamConstants.DB_INSTANCE_NET_TYPE.toLowerCase(), netType.toString());
            proxyApiParams.put(ParamConstants.CONNECTION_STRING, mysqlParameterHelper.getParameterValue(ParamConstants.CONNECTION_STRING));
            proxyApiParams.put(ParamConstants.PORT, mysqlParameterHelper.getParameterValue(ParamConstants.PORT));
            proxyApiParams.put(ParamConstants.ENDPOINT_NAME, endpointGroup);
            Map<String, Object> proxyApiResponse = serverlessResourceService.invokeProxyApi(proxyApiParams);
            logger.info("{} ProxyAPI Response {}", proxyApiResponse);
            Map<String, Object> proxyApiData = (Map<String, Object>) proxyApiResponse.get("Data");
            String proxyTaskId = proxyApiData.get("TaskId").toString();

            metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.NET_DELETING.toString());

            JSONObject taskParams = new JSONObject();
            taskParams.put("proxyTaskId", proxyTaskId);
            Object taskId = workFlowService.dispatchTask(
                    "custins",
                    replicaSet.getName(),
                    DOMAIN_MYSQL,
                    TASK_SERVERLESS_DELETE_NET_TYPE,
                    taskParams.toString(),
                    0);

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("ConnectionString", proxyApiData.getOrDefault(ParamConstants.CONNECTION_STRING, ""));
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException e) {
            logger.error(e.getMessage(), e);
            return createErrorResponse(e.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
