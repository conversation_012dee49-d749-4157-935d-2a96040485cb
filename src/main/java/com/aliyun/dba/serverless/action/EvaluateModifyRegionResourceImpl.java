package com.aliyun.dba.serverless.action;

import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevelListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceSpec;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_STORAGE_TYPE;
import static com.aliyun.dba.support.property.ParamConstants.ENGINE_VERSION;


/**
 * 变配资源评估接口
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessEvaluateModifyRegionResourceImpl")
public class EvaluateModifyRegionResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateModifyRegionResourceImpl.class);

    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;



    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(params);
            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String bid = user.getBid();
            String uid = user.getAliUid();
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String region = mysqlParamSupport.getAndCheckRegion(params);
            String dbType = replicaSetMeta.getService();
            String dbVersion = replicaSetMeta.getServiceVersion();
            Integer diskSize = replicaSetMeta.getDiskSizeMB() / 1024;

            String targetDiskType = getParameterValue(params, "StorageType");
            String insTypeDesc = replicaSetMeta.getInsType().toString();
            Integer targetDiskSize;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.STORAGE)) {
                targetDiskSize = CheckUtils.parseInt(
                        mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE),
                        CustinsSupport.ESSD_MIN_DISK_SIZE, 102400, ErrorCode.INVALID_STORAGE);
            } else {
                targetDiskSize = diskSize;
            }

            String classCode = replicaSetMeta.getClassCode();
            String targetClassCode;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.TARGET_DB_INSTANCE_CLASS)) {
                targetClassCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
            } else {
                targetClassCode = classCode;
            }


            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                    requestId, replicaSetMeta.getName(), null, null, null, null);
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            Optional<Replica> optReplica = currentReplicas.stream().filter(replica -> Replica.RoleEnum.MASTER.equals(replica.getRole())).findFirst();
            Replica masterReplica = optReplica.isPresent() ? optReplica.get() : currentReplicas.size() > 0 ? currentReplicas.get(0) : null;
            boolean isClassCodeChange = !classCode.equals(targetClassCode);
            boolean isDiskSizeChange = !diskSize.equals(targetDiskSize);

            InstanceLevel targetInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, targetClassCode, null);
            InstanceLevel srcInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSetMeta.getService(),
                    replicaSetMeta.getServiceVersion(), replicaSetMeta.getClassCode(), null);

            String compressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, replicaSetMeta.getName(), null);
            Double compressionRatio = cloudDiskCompressionHelper.getCompressionRatio(requestId, replicaSetMeta.getName(), null, null, null);
            Integer diskSizeBeforeCompression = targetDiskSize;
            if (CloudDiskCompressionHelper.isCompressionModeOn(compressionMode) && mysqlParamSupport.hasParameter(params, ParamConstants.STORAGE)) {
                Long checkParamDiskSize = null;
                if (mysqlParamSupport.hasParameter(params, ParamConstants.STORAGE)) {
                    checkParamDiskSize = Optional.ofNullable(diskSizeBeforeCompression).map(Integer::longValue).orElse(null);
                }
                cloudDiskCompressionHelper.checkCompressionSupportLimit(requestId, targetInstanceLevel, dbType, uid, regionId, checkParamDiskSize, targetDiskType, true);
                targetDiskSize = CloudDiskCompressionHelper.getLogicalSize(targetDiskSize, compressionRatio);
            }

            if (StringUtils.isBlank(targetDiskType)) {
                targetDiskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE,
                        masterReplica != null ? masterReplica.getStorageType().toString() : podParameterHelper.getDiskType(targetInstanceLevel));
            }


            //目标磁盘
            String targetPerformanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(targetDiskType);
            targetDiskType = PodParameterHelper.transferDiskTypeParam(targetDiskType);
            //原磁盘
            String srcDiskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
            String srcPerformanceLevel = replicaSetService.getVolumePerfLevel(requestId, replicaSetMeta.getName(), srcDiskType);

            boolean isDiskTypeChange = !srcDiskType.equals(targetDiskType) && !StringUtils.equals(srcPerformanceLevel, targetPerformanceLevel);
            boolean isOnlyDiskSizeChange = isDiskSizeChange && !isClassCodeChange && !isDiskTypeChange;


            //Class code not change ,then do not need to real check resource
            if (Objects.equals(targetInstanceLevel.getClassCode(), srcInstanceLevel.getClassCode())){

                if (ReplicaSetService.isStorageTypeCloudDisk(srcDiskType) && isOnlyDiskSizeChange) {
                    //云盘场景，如果只变磁盘大小，不需要走资源评估，直接返回有资源
                    Map<String, Object> result = new HashMap<>();
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                    result.put(ENGINE_VERSION, targetInstanceLevel.getServiceVersion());
                    result.put(ParamConstants.ENGINE, CustinsSupport.getEngine(targetInstanceLevel.getService()));
                    return result;
                } else {
                    // serverless only change range_rcu,do not need to evaluate resource;
                    Map<String, Object> result = new HashMap<>();
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                    result.put(ENGINE_VERSION, targetInstanceLevel.getServiceVersion());
                    result.put(ParamConstants.ENGINE, CustinsSupport.getEngine(targetInstanceLevel.getService()));
                    return result;
                }


            }
            // Upgrade from basic to standard
            else {
                Integer serviceSpecId = dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(requestId, replicaSetMeta.getName());
                ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, serviceSpecId);
                boolean isTransIns = !isClassCodeChange && !isDiskSizeChange;

                // Buid replicaSet resource
                ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
                replicaSetResourceRequest.setUserId(bid);
                replicaSetResourceRequest.setUid(uid);
                replicaSetResourceRequest.setInsType(insTypeDesc);
                replicaSetResourceRequest.setDbType(dbType);
                replicaSetResourceRequest.setDbVersion(dbVersion);
                replicaSetResourceRequest.setBizType(Objects.requireNonNull(replicaSetMeta.getBizType()).toString());
                replicaSetResourceRequest.setClassCode(targetClassCode);
                replicaSetResourceRequest.setStorageType(targetDiskType);
                replicaSetResourceRequest.setDiskSize(targetDiskSize);
                replicaSetResourceRequest.setSubDomain(region);
                replicaSetResourceRequest.setRegionId(regionId);

                replicaSetResourceRequest.setComposeTag(serviceSpec.getTag());

                boolean isTargetSingleTenant = replicaSetService.isCloudSingleTenant(replicaSetMeta.getBizType(), targetDiskType, targetInstanceLevel, false);
                //多租户查询实例是否指定调度模板
                replicaSetResourceRequest.setScheduleTemplate(

                        podTemplateHelper.getReplicaSetScheduleTemp(replicaSetMeta, targetInstanceLevel, isTargetSingleTenant, null)
                );

                // Build replicaResource
                List<ReplicaResourceRequest> replicas = new ArrayList<>();
                for (Replica currentReplica : currentReplicas) {

                    ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                    replicaResourceRequest.setClassCode(targetClassCode);
                    if (ReplicaSetService.isStorageTypeCloudDisk(targetDiskType)) {
                        //云盘需要有赠送
                        int extendDiskSize = podParameterHelper.getExtendDiskSizeGBForPod(replicaSetMeta.getBizType(), false, targetDiskSize);
                        replicaResourceRequest.setDiskSize(extendDiskSize);

                        VolumeSpec dataVolumeSpec = new VolumeSpec();
                        dataVolumeSpec.setName("data");
                        dataVolumeSpec.setCategory("data");
                        dataVolumeSpec.setPerformanceLevel(targetPerformanceLevel);
                        replicaResourceRequest.setVolumeSpecs(Collections.singletonList(dataVolumeSpec));
                    } else {
                        replicaResourceRequest.setDiskSize(targetDiskSize);
                    }

                    replicaResourceRequest.setRole(Objects.requireNonNull(currentReplica.getRole()).toString());
                    replicaResourceRequest.setStorageType(targetDiskType);
                    replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
                    replicaResourceRequest.setZoneId(currentReplica.getZoneId());
                    replicas.add(replicaResourceRequest);
                }

                replicaSetResourceRequest.setSingleTenant(isTargetSingleTenant);
                replicaSetResourceRequest.setDiskSize(diskSize);  //用户可见的磁盘空间
                replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
                replicaSetResourceRequest.setCatagory(Objects.requireNonNull(targetInstanceLevel.getCategory()).getValue());

                Map<String, Object> result = new HashMap<String, Object>() {{
                    this.put(ENGINE_VERSION, targetInstanceLevel.getServiceVersion());
                    this.put(ParamConstants.ENGINE, CustinsSupport.getEngine(targetInstanceLevel.getService()));
                }};
                // 评估资源
                try {
                    boolean isResourceSufficient = commonProviderService.getDefaultApi().evaluateReplicaSetResource(requestId, "no_need", evaluateNum, replicaSetResourceRequest);
                    if (isResourceSufficient) {
                        result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                    } else {
                        result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                    }
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
                }
                return result;
            }

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }



}
