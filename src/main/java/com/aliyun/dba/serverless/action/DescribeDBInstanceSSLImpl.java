package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessDescribeDBInstanceSSLImpl")
public class DescribeDBInstanceSSLImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.serverless.action.DescribeDBInstanceSSLImpl.class);

    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private CaServerApi caServerApi;
    @Resource
    private ServerlessResourceService serverlessResourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> map) throws RdsException {
        custins = mysqlParamSupport.getAndCheckCustInstance(map);
        String requestId = mysqlParamSupport.getParameterValue(map, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, custins.getInsName(), false);
            return describeServerlessDBInstanceSSL(requestId, replicaSet, map);
        } catch (ApiException e) {
            logger.error(String.format("%s get replica set failed.", requestId), e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        } catch (Exception e) {
            logger.error(e);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }

    }


    /**
     * Serverless with Proxy处理
     * */
    private Map<String, Object> describeServerlessDBInstanceSSL(String requestId,ReplicaSet replicaSet, Map<String, String> map) throws Exception {
        String replicaSetName = serverlessResourceService.getMaxScaleName(requestId,replicaSet.getName());
        Map<String, String> proxyApiParams = serverlessResourceService.getProxyParam(map);
        proxyApiParams.put(ParamConstants.ACTION.toLowerCase(), "DescribeDBProxyInstanceSSL");
        Map<String, Object> proxyApiData = serverlessResourceService.invokeProxyApi(proxyApiParams);
        logger.info("{} Get MaxScale proxyApiData  {}", requestId, JSON.toJSONString(proxyApiData));
        Map<String, Object> dataMap = new Gson().fromJson(JSON.toJSONString(proxyApiData.get("Data")), new TypeToken<Map<String, Object>>(){}.getType());
        logger.info("{} Get MaxScale dataMap  {}", requestId, JSON.toJSONString(dataMap));
        List<Map<String, Object>> certList = new Gson().fromJson(JSON.toJSONString(dataMap.get("CertList")), new TypeToken<List<Map<String, Object>>>(){}.getType());
        logger.info("{} Get MaxScale certlist  {}", requestId, JSON.toJSONString(certList));

        if(certList.isEmpty()){
            Map<String, Object> data = new HashMap<String, Object>();
            data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
            data.put(ParamConstants.DB_INSTANCE_NAME, replicaSetName);
            data.put(ParamConstants.CERT_COMMON_NAME, "");
            data.put(ParamConstants.SSL_EXPIRED_TIME, "");
            data.put(ParamConstants.SSL_ENABLED, false);
            data.put(ParamConstants.SSL_UPDATE_REASON, "");
            return data;
        }

        // serverless只有一个primary的endpoint
        Map<String, String> certInfo = new HashMap<>();
        for (Object cert : certList) {
            Map<String, String> certMap = new Gson().fromJson(JSON.toJSONString(cert), new TypeToken<Map<String, String>>(){}.getType());
            if ("Primary".equalsIgnoreCase(certMap.get(ParamConstants.ENDPOINT_TYPE))) {
                certInfo = certMap;
            }
        }
        logger.info("{} Get MaxScale certInfo  {}", requestId, JSON.toJSONString(certInfo));
        Map<String, Object> data = new HashMap<String, Object>();
        data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
        data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
        data.put(ParamConstants.CERT_COMMON_NAME, certInfo.getOrDefault(ParamConstants.CERT_COMMON_NAME, ""));
        data.put(ParamConstants.SSL_EXPIRED_TIME, certInfo.getOrDefault(ParamConstants.SSL_EXPIRED_TIME, ""));
        data.put(ParamConstants.SSL_ENABLED, !certInfo.isEmpty());
        data.put(ParamConstants.SSL_UPDATE_REASON, certInfo.getOrDefault(ParamConstants.SSL_UPDATE_REASON, ""));
        return data;
    }
}
