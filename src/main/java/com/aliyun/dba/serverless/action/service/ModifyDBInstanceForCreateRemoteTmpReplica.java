package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.TransferTaskListResult;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.poddefault.action.service.modify.AllocateTmpResourceService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.AllocateTmpResourceResult;
import com.aliyun.dba.poddefault.action.support.PodAvzSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.google.gson.Gson;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_BASIC;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_STANDARD;

/**
 * Serverless
 *
 * <AUTHOR> on 2021/8/18.
 */
@Service
public class ModifyDBInstanceForCreateRemoteTmpReplica extends BaseModifyDBInstanceService {


    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;


    /**
     * 变配实例
     *
     * @param custins
     * @param params
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        Integer transListId = null;
        boolean isSuccess = false;
        boolean isAllocated = false;

        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();

        try {
            // 初始化变配参数
            val modifyInsParam = initPodModifyInsParam(params);
            custins = modifyInsParam.getCustins();
            ReplicaSetListResult replicaSetListResult = dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId, custins.getInsName(), ReplicaSet.InsTypeEnum.TMP.getValue());
            if (!CollectionUtils.isEmpty(replicaSetListResult.getItems())) {

                String taskKey = null;
                if (mysqlParamSupport.hasParameter(params, "isSwitch")) {
                    taskKey = PodDefaultConstants.TASK_SWITCH_TO_TMP_INS;
                } else if (mysqlParamSupport.hasParameter(params, "isCancel")) {
                    taskKey = PodDefaultConstants.CANCEL_TMP_INS;
                } else {
                    throw new RdsException(ErrorCode.INVALID_STATUS, "Temp Instance for migration Already Exists.");
                }

                ReplicaSet destReplicaSet = replicaSetListResult.getItems().get(0);

                TransferTaskListResult transferTaskListResult = dBaasMetaService.getDefaultClient().listTransferTasks(requestId, custins.getInsName(), destReplicaSet.getName(), null, null, null);
                if (CollectionUtils.isNotEmpty(transferTaskListResult.getItems())) {
                    transListId = transferTaskListResult.getItems().get(0).getId();
                }
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, custins.getInsName(), ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

                // dispatch task
                JSONObject taskParamObject = new JSONObject();
                taskParamObject.put("requestId", modifyInsParam.getRequestId());


                taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
                taskParamObject.put("transTaskId", transListId);
                taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());

                // 以下参数传给Online Resize使用
                taskParamObject.put("srcDiskType", modifyInsParam.getSrcDiskType());
                taskParamObject.put("targetDiskType", modifyInsParam.getTargetDiskType());
                taskParamObject.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
                taskParamObject.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());

                String taskParam = taskParamObject.toJSONString();
                String domain = PodDefaultConstants.DOMAIN_MYSQL;

                Object taskId = workFlowService.dispatchTask(
                        "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);
                // build response
                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
                data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
                data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
                data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
                data.put("TaskId", taskId);
                data.put("Region", modifyInsParam.getRegionId());
                isSuccess = true;
                return data;
            }
            if (mysqlParamSupport.hasParameter(params, "isSwitch") || mysqlParamSupport.hasParameter(params, "isCancel")) {
                throw new RdsException(ErrorCode.INVALID_STATUS, "Temp Instance for migration not found!");
            }

            val avzInfo = modifyInsParam.getAvzInfo();

            // TODO Currently it uses a random az from resource in meta db
            // This is a temporary solution. Remove it when res-manager is ready.
            // SlaveAz is okay to be null

            val masterReplica = dBaasMetaService.getDefaultClient()
                    .listReplicasInReplicaSet(
                            modifyInsParam.getRequestId(),
                            modifyInsParam.getDbInstanceName(),
                            null, null, null, null
                    ).getItems()
                    .stream()
                    .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER))
                    .findFirst()
                    .get();


            AllocateTmpResourceResult result = allocateTmpResourceService.make(requestId, custins, modifyInsParam);


            isAllocated = result.isAllocated();
            transListId = result.getTransList().getId();
            resourceRequest = result.getResourceRequest();


            val multiAVZExParamDO = avzInfo.getMultiAVZExParamDO();

            val availableZoneInfoList = multiAVZExParamDO.getAvailableZoneInfoList();


            val masterLocation = avzInfo.getRegion(); // Master 子域
            val masterDO = new AvailableZoneInfoDO(masterLocation, "master");

            masterDO.setZoneID(masterReplica.getZoneId());
            availableZoneInfoList.add(masterDO);

            val tmpAvzInfo = new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, avzInfo.getRegion(), avzInfo.getRegionId(), avzInfo.getRegionCategory(), multiAVZExParamDO);

            val tmpCustinsId = Objects.requireNonNull(result.getReplicaSet().getId()).intValue();
            custinsParamService.updateAVZInfo(tmpCustinsId, tmpAvzInfo);

            // 审计日志管控参数补全
            // 集团业务不会使用此 Service，直接设置
            custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);

            // 更新系统参数模板
            val paramGroupIdDO = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
            if (paramGroupIdDO != null) {
                val paramGroupId = paramGroupIdDO.getValue();
                if (StringUtils.isNotEmpty(paramGroupId)) {
                    if (paramGroupId.startsWith("rpg-sys-")) {
                        val paramGroupInfo = SysParamGroupHelper.describeSysParamGroupId(paramGroupId);
                        paramGroupInfo.put("category", "standard");
                        val map = paramGroupInfo.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> (String) e.getValue()));
                        val newParamGroupId = SysParamGroupHelper.getSysParamGroupId(map);
                        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, newParamGroupId);
                        // next: 在任务流内切换管控参数
                    } else {
                        // 如果是用户参数模板，直接应用到临时实例
                        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, paramGroupId);
                    }
                }
            }


            // FIXME: 当前没有跨可用区场景，注释这行代码，避免错误情况master location
            // custinsParamService.updateAVZInfo(custins.getId(), modifyInsParam.getAvzInfo());

            String taskKey = PodDefaultConstants.TASK_CREATE_TMP_INS;

            // dispatch task
            JSONObject taskParamObject = new JSONObject();
            taskParamObject.put("requestId", modifyInsParam.getRequestId());


            taskParamObject.put("replicaSetName", modifyInsParam.getDbInstanceName());
            taskParamObject.put("transTaskId", transListId);
            taskParamObject.put("switchInfo", modifyInsParam.getSwitchInfo());

            // 以下参数传给Online Resize使用
            taskParamObject.put("srcDiskType", modifyInsParam.getSrcDiskType());
            taskParamObject.put("targetDiskType", modifyInsParam.getTargetDiskType());
            taskParamObject.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
            taskParamObject.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
            taskParamObject.put("rcu", modifyInsParam.getRcu());

            String taskParam = taskParamObject.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask(
                    "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParam, 0);

            //无需更新状态，由临时实例是否存在判断状态
//            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
//                    modifyInsParam.getRequestId(),
//                    modifyInsParam.getDbInstanceName(),
//                    modifyInsParam.isTransIns() ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.CLASS_CHANGING.toString()
//            );

            // build response
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
            data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
            data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
            data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
            data.put("TaskId", taskId);
            data.put("Region", modifyInsParam.getRegionId());
            isSuccess = true;
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 变配失败释放资源
            if (isAllocated && !isSuccess && StringUtils.isNotEmpty(resourceRequest.getReplicaSetName())) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, resourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }
}
