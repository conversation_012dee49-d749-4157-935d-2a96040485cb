package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.serverless.action.ModifyDBInstanceClassImpl;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ParamConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ServerlessExpandDiskService {
    private final Double DISK_EXPAND_THRESHOLD = 0.8;
    private final Integer DISK_EXPAND_MIN_MB = 5 * 1024;
    private final Double DISK_EXPAND_MIN_RATIO = 0.15;

    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    private InstanceService instanceService;

    public Map<String, Object> expandDisk(ReplicaSet replicaSet, String requestId) throws Exception {
        // get disk size used
        Integer diskUsedMB = replicaSet.getDiskSizeMB();
        Integer diskCurrentMB = replicaSet.getDiskSizeMB();
        try {
            InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(replicaSet.getId().intValue(), 0);
            diskUsedMB = new BigDecimal(instancePerf.getDiskCurr()).intValue();
            log.info("get {} disk used {} MB from perf", replicaSet.getName(), diskUsedMB);
        } catch (Exception e) {
            log.error("get instance perf for {} failed, {}", replicaSet.getName(), e.getMessage());
        }

        if (diskUsedMB <= diskCurrentMB * DISK_EXPAND_THRESHOLD) {
            log.error("{} used size is {} MB, current size is {}, use ratio is lower than {}, so no expand",
                    replicaSet.getName(), diskUsedMB, diskCurrentMB, DISK_EXPAND_THRESHOLD);
//            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "current disk not need expand");
        }

        // 扩容后存储空间需要为5的倍数
        Double targetDiskSizeMB = Math.ceil(diskCurrentMB * (1 + DISK_EXPAND_MIN_RATIO) / DISK_EXPAND_MIN_MB) * DISK_EXPAND_MIN_MB;
        Double finalDiskSizeMB = Math.min(targetDiskSizeMB, ServerlessConstant.SERVERLESS_STORAGE_UPPER_BOND*1024);

        return callModify(replicaSet, finalDiskSizeMB, requestId,ServerlessConstant.CallFrom.OP_API);
    }

    public Map<String, Object> expandDiskFromSCC(ReplicaSet replicaSet, String requestId) throws Exception {
        // get disk size used
        Integer diskUsedMB = replicaSet.getDiskSizeMB();
        Integer diskCurrentMB = replicaSet.getDiskSizeMB();
        try {
            InstancePerfDO instancePerf = instanceService.getInstancePerfByCustinsId(replicaSet.getId().intValue(), 0);
            diskUsedMB = new BigDecimal(instancePerf.getDiskCurr()).intValue();
            log.info("get {} disk used {} MB from perf", replicaSet.getName(), diskUsedMB);
        } catch (Exception e) {
            log.error("get instance perf for {} failed, {}", replicaSet.getName(), e.getMessage());
        }

        if (diskUsedMB <= diskCurrentMB * DISK_EXPAND_THRESHOLD) {
            log.error("{} used size is {} MB, current size is {}, use ratio is lower than {}, so no expand",
                    replicaSet.getName(), diskUsedMB, diskCurrentMB, DISK_EXPAND_THRESHOLD);
//            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "current disk not need expand");
        }

        // 扩容后存储空间需要为5的倍数
        Double targetDiskSizeMB = Math.ceil(diskCurrentMB * (1 + DISK_EXPAND_MIN_RATIO) / DISK_EXPAND_MIN_MB) * DISK_EXPAND_MIN_MB;
        Double finalDiskSizeMB = Math.min(targetDiskSizeMB, ServerlessConstant.SERVERLESS_STORAGE_UPPER_BOND*1024);

        return callModify(replicaSet, finalDiskSizeMB, requestId,ServerlessConstant.CallFrom.SCC);
    }


    private Map<String, Object> callModify(ReplicaSet replicaSet, Double targetDiskMB, String requestId, ServerlessConstant.CallFrom callFrom) throws Exception {
        Integer diskG = (int) Math.ceil(targetDiskMB / 1024);
        User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), null);

        Map<String, String> modifyParams = new HashMap<>();
        modifyParams.put(ParamConstants.ACTION.toLowerCase(), "ModifyDBInstanceClass");
        modifyParams.put(ParamConstants.USER_ID.toLowerCase(), user.getBid());
        modifyParams.put(ParamConstants.UID.toLowerCase(), user.getAliUid());
        modifyParams.put(ParamConstants.DB_INSTANCE_NAME.toLowerCase(), replicaSet.getName());
        modifyParams.put(ParamConstants.STORAGE.toLowerCase(), diskG.toString());
        modifyParams.put(ParamConstants.REQUEST_ID.toLowerCase(), requestId);
        modifyParams.put(ServerlessConstant.CALL_FROM,callFrom.getValue());

        log.info("serverless 磁盘满，下发扩容，params: {}", JSON.toJSONString(modifyParams));
        ModifyDBInstanceClassImpl rebuildSlaveInstanceImpl = SpringContextUtil.getBeanByClass(ModifyDBInstanceClassImpl.class);
        return rebuildSlaveInstanceImpl.doActionRequest(null, modifyParams);
    }
}
