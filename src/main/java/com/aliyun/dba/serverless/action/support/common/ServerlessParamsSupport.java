package com.aliyun.dba.serverless.action.support.common;

import com.aliyun.dba.commonkindcode.support.ParamExprException;
import com.aliyun.dba.commonkindcode.support.ParamExpressionParser;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.SERVERLESS_KERNEL_PARAMS_NO_SUPPORT_EDIT;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ServerlessParamsSupport {
    @Resource
    private ResourceService resourceService;


    /**
     * serverless 实例不支持修改的内核参数
     */
    public Set<String> getKernelParamsNoSupportEdit() {
        Set<String> paramsNoSupport = new HashSet<>(Arrays.asList(
//                "max_connections",
//                "max_user_connections",
                "innodb_buffer_pool_chunk_size",
                "innodb_buffer_pool_instances",

                // maxscale does not support caching_sha2_password
                "default_authentication_plugin"
        ));

        // 注意此处配置为空也会覆盖默认值
        ResourceDO resource = resourceService.getResourceByResKey(SERVERLESS_KERNEL_PARAMS_NO_SUPPORT_EDIT);
        if (resource != null) {
            paramsNoSupport = new HashSet<>(Arrays.asList(resource.getRealValue().split(",")));
        }
        log.info("params serverless not support edit: {}", paramsNoSupport);

        return paramsNoSupport;
    }

    /**
     * do not allow set bp size < BInstanceClassMemory*1/4 for serverless
     * because innodb_buffer_pool_chunk_size(32M) * innodb_buffer_pool_instances(8) = 256M
     * when serverless shrink to 0.5rcu (which memory is 1G), can not set bp to less than 256M
     **/
    public void checkBpSize(String value, ParamExpressionParser parser) throws ParamExprException {
        if ( StringUtils.isEmpty(value) || "null".equals(value)) {
            return;
        }
        BigInteger parsedValue = parser.parse(value);
        BigInteger minValue = parser.parse("{DBInstanceClassMemory*1/4}");
        if (parsedValue.compareTo(minValue) < 0) {
            throw new ParamExprException(String.format("parsed value %d out of range (<%d)", parsedValue, minValue));
        }
    }
}
