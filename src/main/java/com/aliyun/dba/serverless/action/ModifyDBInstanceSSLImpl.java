package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.consts.SSLConsts;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.endpoint.idao.EndpointIDaoImpl;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.serverless.action.service.ServerlessEndpointService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.DOMAIN_MYSQL;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_SERVERLESS_MODIFY_SSL;
import static com.aliyun.dba.support.property.ParamConstants.SSL_VALUE_DISABLED;
import static com.aliyun.dba.support.property.ParamConstants.SSL_VALUE_ENABLED;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessModifyDBInstanceSSLImpl")
public class ModifyDBInstanceSSLImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.serverless.action.ModifyDBInstanceSSLImpl.class);

    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private CaServerApi caServerApi;
    @Resource
    private ConnAddrCustinsService connAddrCustinsService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Resource
    private ServerlessEndpointService serverlessEndpointService;

    /**
     * 代码移植自本地盘接口
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            custins = mysqlParamSupport.getAndCheckCustInstance(actionParams);
            ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, custins.getInsName(), false);
            if (serverlessResourceService.isServerlessV2(replicaSet)) {
                return modifyDBProxyInstanceSSL(custins, actionParams);
            } else {
                return modifyDBInstanceSSL(custins, actionParams);
            }
        } catch (RdsException re) {
            log.warn(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(String.format("%s %s", requestId, ex.getMessage()), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private String getTaskKey(CustInstanceDO custins) throws ApiException {
        String taskKey;
        boolean isXDB = replicaSetService.isReplicaSetXDB(mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID), custins.getInsName());
        if (isXDB) {
            taskKey = custins.isInsTypeRead() ? PodDefaultConstants.MODIFY_READ_XDB_SSL_CONFIG : PodDefaultConstants.MODIFY_XDB_SSL_CONFIG;
        } else {
            taskKey = PodDefaultConstants.MODIFY_SSL;
        }
        return taskKey;
    }

    /**
     * 普通实例变更SSL信息
     * */
    private Map<String, Object> modifyDBInstanceSSL(CustInstanceDO custins, Map<String, String> actionParams) throws Exception {

        try {
            caServerApi.getCAServerConfig(custins.getClusterName());
        } catch (RdsException re) {
            //没有ca server，该集群无法开启ssl
            return createErrorResponse(ErrorCode.UNSUPPORTED_ENABLE_SSL);
        }

        if (!custins.isActive()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        if ("physical".equalsIgnoreCase(custins.getConnType())) {
            // physical实例没有链路，证书无法绑定稳定地址
            return createErrorResponse(ErrorCode.UNSUPPORTED_ENABLE_SSL);
        }

        if (custins.isLock()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }

        String connectionString = CheckUtils.checkNullForConnectionString(getParameterValue(actionParams, ParamConstants.CONNECTION_STRING));
        if (connectionString.length() > 64) {
            return createErrorResponse(ErrorCode.CONNECTIONSTRING_LENGTH_EXCEEDED);
        }

        //获取请求参数
        String sslStatus = getParameterValue(actionParams, ParamConstants.SSL_ENABLED);
        boolean sslEnable = !StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus);
        if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
            //关闭ssl, 0
            sslStatus = SSL_VALUE_DISABLED;
        } else {
            // 开启或更新 ssl, 1
            sslStatus = SSL_VALUE_ENABLED;
        }

        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService.getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);
        boolean connStringValid = false;
        for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
            if (connectionString.equals(custinsConnAddr.getConnAddrCust()) && custinsConnAddr.isConnAddrUserVisible()) {
                connStringValid = true;
            }
        }

        if (!connStringValid) {
            if (StringUtils.equalsIgnoreCase(SSL_VALUE_DISABLED, sslStatus)) {
                log.warn("close ssl, current connstr doesn't exists, continue");
            } else {
                return createErrorResponse(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
            }
        }

        //设置切换时间
        //立即切换，指定时间切换，运维时间切换
        Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(actionParams);

        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsSupport.CUSTINS_STATUS_SWITCH, CustinsState.STATE_SSL_MODIFYING.getComment());
        JSONObject taskParams = new JSONObject();
        taskParams.put("ssl_status", Integer.valueOf(sslStatus));
        taskParams.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
        String parameter = taskParams.toJSONString();

        String taskKey = getTaskKey(custins);
        if (sslEnable) {
            custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME, connectionString);
        } else {
            // 关闭ssl, 关联链路
            custinsParamService.deleteCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_CERT_CN_NAME);
        }
        Object taskId = workFlowService.dispatchTask("custins", custins.getInsName(), "mysql", taskKey, parameter, 0);
        Map<String, Object> data = new HashMap<>(3);
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", taskId);
        return data;
    }


    /**
     * Serverless with Proxy架构，请求转发给Proxy
     * */
    private Map<String, Object> modifyDBProxyInstanceSSL(CustInstanceDO custins, Map<String, String> map) throws Exception {
        String requestId = mysqlParameterHelper.getParameterValue(ParamConstants.REQUEST_ID);
        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, custins.getInsName(), false);
        if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION && replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVE) {
            logger.error("{} replicaset is not active.", requestId);
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        String caType = mysqlParamSupport.getParameterValue(map, ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
        if (SSLConsts.CA_TYPE_CUSTOM.equalsIgnoreCase(caType)) {  //serverless 不支持自定义SSL证书
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }

        String connectionString = mysqlParameterHelper.getParameterValue(ParamConstants.CONNECTION_STRING);
        String endpointGroup = serverlessEndpointService.getEndpointGroup(requestId, replicaSet.getName(), connectionString);
        Map<String, String> proxyApiParams = serverlessResourceService.getProxyParam(map);

        proxyApiParams.put(ParamConstants.ACTION.toLowerCase(), "ModifyDBProxyInstanceSSL");
        proxyApiParams.put(ParamConstants.ENDPOINT_NAME, endpointGroup);
        proxyApiParams.put(ParamConstants.SSL_ENABLED, mysqlParameterHelper.getParameterValue(ParamConstants.SSL_ENABLED));
        proxyApiParams.put(ParamConstants.CONNECTION_STRING, connectionString);

        Map<String, Object> proxyApiResponse = serverlessResourceService.invokeProxyApi(proxyApiParams);
        logger.info("{} ProxyAPI Response {}", proxyApiResponse);
        Map<String, Object> proxyApiData = (Map<String, Object>) proxyApiResponse.get("Data");
        String proxyTaskId = proxyApiData.get("TaskId").toString();

        // 更新实例状态
        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsSupport.CUSTINS_STATUS_SWITCH, CustinsState.STATE_SSL_MODIFYING.getComment());

        JSONObject taskParams = new JSONObject();
        taskParams.put("proxyTaskId", proxyTaskId);
        String parameter = taskParams.toJSONString();
        Object taskId = workFlowService.dispatchTask(
                "custins",
                custins.getInsName(),
                DOMAIN_MYSQL,
                TASK_SERVERLESS_MODIFY_SSL,
                parameter,
                0);

        Map<String, Object> data = new HashMap<>(3);
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("TaskId", taskId);
        return data;
    }
}
