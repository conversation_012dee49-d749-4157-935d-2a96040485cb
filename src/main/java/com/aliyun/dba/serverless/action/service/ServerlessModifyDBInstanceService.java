package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.migrate.MigrateDBInstanceAvzService;
import com.aliyun.dba.poddefault.action.service.modify.AliyunModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.ModifyDBInstanceDiskShrinkServiceV2;
import com.aliyun.dba.poddefault.action.service.modify.ModifyDBInstanceDiskTypeService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.support.PodScheduleTemplate;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.service.modify.ModifyServerlessDBInstanceOptimizedWritesService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES_INFO;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_PARAMETERS;

/**
 * 云上变配实例，待整合
 *
 * <AUTHOR> on 2020/6/12.
 */
@Service
public class ServerlessModifyDBInstanceService extends BaseModifyDBInstanceService {
    protected static final LogAgent logger = LogFactory.getLogAgent(ServerlessModifyDBInstanceService.class);
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Resource
    private MigrateDBInstanceAvzService migrateDBInstanceAvzService;
    @Resource
    private CustinsParamService custinsParamService;

    @Resource
    private AliyunModifyDBInstanceService aliyunModifyDBInstanceService;
    @Resource
    private ModifyDBInstanceForCreateRemoteTmpReplica modifyDBInstanceForCreateRemoteTmpReplica;
    @Resource
    private DbossApi dbossApi;
    @Resource
    ServerlessExpandDiskService serverlessExpandDiskService;

    @Resource
    ResourceService resourceService;
    @Resource
    private ModifyDBInstanceDiskTypeService modifyDBInstanceDiskTypeService;

    @Resource
    private ModifyDBInstanceDiskShrinkServiceV2 modifyDBInstanceDiskShrinkServiceV2;

    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private ModifyServerlessDBInstanceOptimizedWritesService modifyServerlessDBInstanceOptimizedWritesService;

    public static final Set<String> VIP_TASK_PRIORITY = Sets.newHashSet(PodDefaultConstants.TASK_ONLINE_RESIZE_INS_FOR_BASIC, PodDefaultConstants.TASK_ONLINE_RESIZE_INS, PodDefaultConstants.TASK_ONLINE_RESIZE_INS_FOR_CLUSTER);

    /**
     * 变配实例serverless
     *
     * @param custins
     * @param params
     * @return
     * @throws RdsException
     */

    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");

        PodModifyInsParam modifyInsParam;
        try {
            // 可用区迁移
            String migratingAvz = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false");
            if (Boolean.parseBoolean(migratingAvz)) {
                throw new RdsException(ErrorCode.INVALID_ACTION);
            }

            // Get request call from {SCC / OPENAPI/ OP_API },In  ServerlessConstant.CallFrom
            String callFrom = mysqlParamSupport.getParameterValue(params, ServerlessConstant.CALL_FROM, ServerlessConstant.CallFrom.OPENAPI.getValue());

            // 初始化变配参数
            modifyInsParam = initServerlessConfigPodModifyInsParam(params);
            logger.info("RequestId {} , modifyInsParam is {}.", modifyInsParam.getRequestId(), JSON.toJSONString(modifyInsParam.getTargetServerlessSpec()));


            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                    .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                            null, null, null, null);

            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            assert currentReplicas != null;

            //Checking the params
            modifyInsParam.isValidModifyRcu();

            String taskKey;
            String parameter;
            ReplicaSet replicaSet = modifyInsParam.getReplicaSetMeta();

            // 允许实例暂停状态下修改Serverless配置
            boolean modifySpecWhenStopped = replicaSetService.isStopped(replicaSet) && modifyInsParam.isServerlessConfigChanged();
            if (!replicaSetService.isActive(replicaSet) && !modifySpecWhenStopped) {
                throw new RdsException(ErrorCode.INVALID_STATUS);
            }

            if (modifyInsParam.isModifyCloudAutoConfig()) {
                if (modifyInsParam.isOnlyModifyCloudAutoConfig() && !modifyInsParam.isServerlessConfigChanged()) {
                    return modifyDBInstanceDiskTypeService.doModifyCloudAutoConfig(requestId, modifyInsParam, modifyInsParam.getReplicaSetMeta());
                }
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedModifyParam", "burst param must be only modified"});
            }

            String optimizedWrites = mysqlParamSupport.getParameterValue(params, ParamConstants.OPTIMIZED_WRITES, null);
            String primaryOptimizedWritesInfo;
            try {
                primaryOptimizedWritesInfo = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, mysqlParamSupport.getDBInstanceName(params), OPTIMIZED_WRITES_INFO);
            } catch (ApiException e) {
                throw new RuntimeException(e);
            }
            if (StringUtils.isNotBlank(optimizedWrites)) {
                boolean targetOptimizedWrites = PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites);
                boolean primaryOptimizedWrites = podCommonSupport.isOptimizedWrites(primaryOptimizedWritesInfo);
                if (targetOptimizedWrites != primaryOptimizedWrites) {
                    if (modifyInsParam.isDiskSizeChange() || modifyInsParam.isDiskTypeChange() || modifyInsParam.isClassCodeChange() || modifyInsParam.isPerformanceLevelChanged()) {
                        logger.error("optimized writes must be only modified");
                        throw new RdsException(INVALID_PARAMETERS);
                    }
                    return modifyServerlessDBInstanceOptimizedWritesService.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
                }
            }

            // 磁盘缩容
            ReplicaSet replicaSetMeta = null;
            try {
                replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);
            } catch (ApiException e) {
                logger.error(e.getMessage(), e);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
            String storage = mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE);
            if (StringUtils.isNotEmpty(storage)) {
                logger.info("storage is modifying to " + storage + " GB.");
                logger.info("Modify DB Class requestId: " + requestId);
                Integer targetDiskSize = CheckUtils.parseInt(storage, CustinsSupport.ESSD_MIN_DISK_SIZE, 100000, ErrorCode.INVALID_STORAGE);
                Integer srcDiskSize = replicaSetMeta.getDiskSizeMB() / 1024;
                if (targetDiskSize < srcDiskSize) {
                    // 灰度开关
                    if(!podParameterHelper.isServerlessSupportShrink()){
                        throw new RdsException(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
                    }
                    // 缩容前检查实例是否为激活状态
                    if (!(ReplicaSet.StatusEnum.ACTIVE.equals(replicaSetMeta.getStatus()) ||
                            ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSetMeta.getStatus()))) {
                        throw new RdsException(ErrorCode.INVALID_STATUS);
                    }
                    // Serverless其他属性变更不能和缩容同时执行
                    if(modifyInsParam.isServerlessConfigChanged()){
                        throw new RdsException(ErrorCode.INVALID_PARAM);
                    }
                    return modifyDBInstanceDiskShrinkServiceV2.doActionRequest(custins, params);
                }
            }

            // 非Serverless属性变配，实际是serverless实例变更磁盘，目前同其他实例同样流程
            if (modifyInsParam.isOnLineResize() && !modifyInsParam.isServerlessConfigChanged()) {
                Object transfer = buildTransListForOnlineResize(modifyInsParam, currentReplicas);
                TransferTask transferTask = getTransferTask(modifyInsParam, transfer);
                taskKey = getTaskKey(modifyInsParam, transferTask);
                JSONObject taskParamObject = aliyunModifyDBInstanceService.buildTaskParam(modifyInsParam, transferTask, new ArrayList<>());
                taskParamObject.put(ServerlessConstant.CALL_FROM,callFrom);
                parameter = taskParamObject.toJSONString();
                logger.error("{} Serverlss online resize disk size！", modifyInsParam.getCustins().getInsName());
            }
            else if(modifyInsParam.isOnLineResize()&& modifyInsParam.isServerlessConfigChanged()){
                logger.error("Serverlss not support change serverlessconfig && Diskconfig together！");
                throw new RdsException(ErrorCode.SERVERLESS_NOT_SUPPORT_MODIFY_SERVERLESSCONFIG_AND_DISK_TOGETHER);
            }

            else if (modifyInsParam.isDiskTypeChange()) {
                if (!modifyInsParam.isModifyCloudESSDToCloudAuto()) {
                    logger.info("serverless do not support target diskType");
                    throw new RdsException(ErrorCode.INVALID_PARAMETERS);
                }
                if (!modifyInsParam.isOnlyDiskTypeChange() || modifyInsParam.isServerlessConfigChanged()) {
                    logger.info("do not support change diskType with other config together");
                    throw new RdsException(ErrorCode.INVALID_PARAMETERS);
                }
                return modifyDBInstanceDiskTypeService.doModifyCloudAutoConfig(requestId, modifyInsParam, modifyInsParam.getReplicaSetMeta());
            }

            // 如果不是Serverless属性变配，那么退出
            else if (!modifyInsParam.isOnLineResize() && !modifyInsParam.isServerlessConfigChanged()){
                logger.info("{} nothing has changed, return it.", modifyInsParam.getRequestId());
                return getResponse(modifyInsParam, null);
            }
            // 实例启停依赖Proxy
            else if (modifyInsParam.getTargetServerlessSpec().getAutoPause()
                    && !serverlessResourceService.isServerlessV2(modifyInsParam.getReplicaSetMeta())) {
                logger.error("Serverlss without proxy, un-support auto pause.");
                throw new RdsException(ErrorCode.INVALID_AUTO_PAUSE);
            } else if (modifyInsParam.getTargetServerlessSpec().getScaleMin() > modifyInsParam.getTargetServerlessSpec().getScaleMax()) {
                logger.error("{} ScaleMin {} > ScaleMax {}", requestId, modifyInsParam.getTargetServerlessSpec().getScaleMin(),
                        modifyInsParam.getTargetServerlessSpec().getScaleMax());
                throw new RdsException(ErrorCode.INVALID_MIN_CAPACITY);
            }
            // 高可用会强制开启主库event_scheduler，导致无法开启暂停，先不拦截
//            if (modifyInsParam.getTargetServerlessSpec().getAutoPause()
//                    && isEventSchedulerEnabled(modifyInsParam.getReplicaSetMeta().getId().intValue())) {
//                logger.error("event scheduler enabled, can not pause");
//                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
//            }
            //Serverless属性变配
            else {
                // 只有v2版本的才需要触发下maxscale的变更
                if (serverlessResourceService.isServerlessV2(replicaSet)) {
                    // 修改maxscale代理规格 1C=>2C
                    modifyMaxscaleIns(modifyInsParam, params);
                }

                taskKey = ServerlessConstant.TASK_MODIFY_SERVERLESS_CONFIG_INS;
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("requestId", requestId);
                jsonObject.put("replicaSetName", modifyInsParam.getDbInstanceName());

                // 实例可能处于停止中，记录当前实例状态，便于任务流执行完成后恢复状态
                jsonObject.put("status", modifyInsParam.getReplicaSetMeta().getStatus().toString());

                // lable中的activities更改 => freeTierActivity ins modify maxRcu > 2.0 : set "freeTierActivity" false
                String activities = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, modifyInsParam.getDbInstanceName(), "activities");
                if (activities != null) {
                    Map<String, Boolean> activitiesMap = JSONObject.parseObject(activities, Map.class);
                    if (activitiesMap.containsKey("freeTierActivity")) {
                        if (activitiesMap.get("freeTierActivity") && modifyInsParam.getTargetServerlessSpec().getScaleMax() > 2.0){
                            activitiesMap.put("freeTierActivity", false);

                            Map<String, String> hashMap = new HashMap<>();
                            hashMap.put("activities", JSONObject.toJSONString(activitiesMap));
                            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, modifyInsParam.getDbInstanceName(), hashMap);
                            logger.info("RequestId {}, {} modify freeTierActivity to false", requestId, modifyInsParam.getDbInstanceName());
                        }
                    }
                    logger.info("RequestId {}, {} has activities: {}", requestId, modifyInsParam.getDbInstanceName(), activitiesMap);
                }

                // serverless信息
                jsonObject.put(ServerlessConstant.SCALE_MIN, modifyInsParam.getTargetServerlessSpec().getScaleMin());
                jsonObject.put(ServerlessConstant.SCALE_MAX, modifyInsParam.getTargetServerlessSpec().getScaleMax());
                jsonObject.put(ServerlessConstant.AUTO_PAUSE, modifyInsParam.getTargetServerlessSpec().getAutoPause());
                jsonObject.put(ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE, modifyInsParam.getTargetServerlessSpec().getSecondsUntilAutoPause());
                jsonObject.put(ServerlessConstant.DAS_AUTO_PAUSE, modifyInsParam.getTargetServerlessSpec().getDasAutoPause());
                jsonObject.put(ServerlessConstant.KEEP_RUNNING_TIME, modifyInsParam.getTargetServerlessSpec().getKeepRunningTime());
                jsonObject.put(ServerlessConstant.CALL_FROM, callFrom);

                parameter = jsonObject.toJSONString();
                //更新serverless配置参数 before create task
                serverlessResourceService.setServerlessSpec(
                        requestId, modifyInsParam.getDbInstanceName(), modifyInsParam.getTargetServerlessSpec());
            }

            // dispatch task
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            Object taskId = workFlowService.dispatchTask(
                    "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, parameter, getServerlessTaskPriority(taskKey));

            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                    modifyInsParam.isTransIns() ? ReplicaSet.StatusEnum.TRANSING.toString() : ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

            // build response
            return getResponse(modifyInsParam, taskId);
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * Call from SCC
     * @param custins
     * @param params
     * @return
     * @throws RdsException
     */
    public Map<String, Object> doSccActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");

        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
//            Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());

            if (!(ReplicaSet.StatusEnum.ACTIVE.equals(replicaSet.getStatus()) ||
                    ReplicaSet.StatusEnum.ACTIVATION.equals(replicaSet.getStatus()))) {
                throw new RdsException(ErrorCode.INVALID_STATUS);
            }

            String sccAction = mysqlParamSupport.getAndCheckSccAction(params);

            logger.info("Requestid {} from scc ,replicaset name is {} params is {} ", requestId, replicaSet.getName(), params.toString());

            if (sccAction.equalsIgnoreCase(ServerlessConstant.ACTION_TYPE_SCALE_DISK)) {
                logger.info("Requestid {} from scc ,replicaset name is {} action type is {} ", requestId, replicaSet.getName(), sccAction);
                if (this.allowExpendDiskFromSCC()) {
                    return serverlessExpandDiskService.expandDiskFromSCC(replicaSet,requestId);
                }
                logger.error("Requestid {} from scc ,replicaset name is {} disk_not_enough event {} do not allow expend from SCC", requestId, replicaSet.getName(),sccAction);
            }

            // Do not hit any action type,then return 400 error
            return createErrorResponse(ErrorCode.INVALID_ACTION);

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     *Judge if allow to expend disk from scc.
     */
    public Boolean allowExpendDiskFromSCC(){
        ResourceDO resource = resourceService.getResourceByResKey(ServerlessConstant.RESOURCE_KEY_ALLOW_EXPEND_DISK_FROM_SCC);
        if (resource == null || StringUtils.isBlank(resource.getRealValue())) {
            return true;
        } else {
            return Integer.parseInt(resource.getRealValue().trim()) != 0;
        }
    }

    /**
     * 申请资源变配场景，支持Serverless
     * */
    public Object modifyWithServerlessAllocateResource(PodModifyInsParam modifyInsParam) throws Exception{
        Object transfer = null;
        String tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName();
        List<String> modifyReplicas = new ArrayList<>();

        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                        null, null, null, null);
        List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
        assert currentReplicas != null;

        ModifyReplicaSetResourceRequest replicaSetResourceRequest = getModifyReplicaSetResourceRequest(modifyInsParam, tmpReplicaSetName);
        if (modifyInsParam.isTransByNc()) {
            // NC时强制申请新盘
            replicaSetResourceRequest.reuseCloudDisk(false);
        }
        PodScheduleTemplate podScheduleTemplate = modifyInsParam.getPodScheduleTemplate();

        List<ModifyReplicaResourceRequest> replicaRequestList = buildModifyReplicaResourceRequests(
                modifyInsParam, currentReplicas, modifyInsParam.isTargetSingleTenant(), podScheduleTemplate, modifyReplicas);
        replicaSetResourceRequest.setDiskSize(modifyInsParam.getTargetDiskSizeGB());
        replicaSetResourceRequest.setVswitchID(mysqlParamSupport.getParameterValue(modifyInsParam.getParams(), ParamConstants.VSWITCH_ID, null));
        replicaSetResourceRequest.setReplicaResourceRequestList(replicaRequestList);
        replicaSetResourceRequest.setModifyMode(modifyInsParam.getModifyMode());

        // 不申请反向VPC的资源
        replicaSetResourceRequest.setIgnoreCreateVpcMapping(true);

        // Serverless场景使用不生成trans_list的接口
        transfer = commonProviderService.getDefaultApi().allocateReplicaSetResourceForScaleTransTask(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), replicaSetResourceRequest);
        return transfer;
    }

    /**
     * 统一封装TransferId和Transfer String两种类型的TransferTask获取方式
     * */
    public TransferTask getTransferTask(PodModifyInsParam modifyInsParam, Object transfer) throws ApiException {
        TransferTask transferTask = null;
        if (transfer instanceof Integer) {
            logger.info("{} get TransferTask by id {}", modifyInsParam.getRequestId(), transfer);
            transferTask = dBaasMetaService.getDefaultClient().getTransferTask(modifyInsParam.getRequestId(), modifyInsParam.getReplicaSetMeta().getName(), (Integer) transfer);
        } else if (transfer instanceof String) {
            logger.info("{} get TransferTask by String {}", modifyInsParam.getRequestId(), transfer);
            transferTask = JSONObject.parseObject(transfer.toString(), TransferTask.class);
        }
        return transferTask;
    }


    /**
     * 接口返回
     * */
    private Map<String, Object> getResponse(PodModifyInsParam modifyInsParam, Object taskId) {
        Map<String, Object> data = new HashMap<>();
        data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
        data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
        data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
        data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
        data.put("SourceServerlessScaleMin", modifyInsParam.getSrcServerlessSpec().getScaleMin());
        data.put("TargetServerlessScaleMin", modifyInsParam.getTargetServerlessSpec().getScaleMin());
        data.put("SourceServerlessScaleMax", modifyInsParam.getSrcServerlessSpec().getScaleMax());
        data.put("TargetServerlessScaleMax", modifyInsParam.getTargetServerlessSpec().getScaleMax());
        data.put("TaskId", taskId);
        data.put("Region", modifyInsParam.getRegionId());
        return data;
    }

    public PodModifyInsParam initServerlessConfigPodModifyInsParam(Map<String, String> params) throws Exception {
        PodModifyInsParam modifyInsParam = new PodModifyInsParam(dependency, params);

        // 初始化实例基础信息
        modifyInsParam
                .initCustins()
                .initReplicaSetMeta()
                .multiWriteEngineValidate();

        // 获取用户与实例属性信息
        modifyInsParam
                .initUser()
                .initInstanceName()
                .initDBType()
                .initDBVersion()
                .initClusterName()
                .initOrderId()
                .initTmpReplicaSetName();

        // 设置业务属性标
        modifyInsParam
                .setIsDHG()
                .setClusterNameToParamsForDHG()
                .setIsTDDL()
                .setIsReadIns()
                .setIsEmergencyTransfer()
                .initRoleHostNameMapping();

        // 初始化迁移变配资源基本信息
        modifyInsParam
                .initSrcCompressionMode()
                .initTargetCompressionMode()
                .initDiskSizeGB()               // 获取原实例磁盘大小
                .initTargetDiskSizeGB()         // 获取目标磁盘大小
                .initClassCode()                // 获取原实例规格码
                .initTargetClassCode()          // 获取目标规格码
                .initSrcInstanceLevel()         // 获取原规格对象
                .initTargetInstanceLevel()      // 获取目标规格对象
                .initSrcDiskType()              // 获取原磁盘类型
                .setIsPfs()
                .initTargetDiskType()           // 获取目标磁盘类型
                .initAutoPLConfig()
                .setIsDiskSizeChange()
                .setIsDiskTypeChange()
                .initNetProtocol()
                .initPodType();
//                .setIsTransIns()
//                .diskSizeValidation();

        // 初始化调度信息
        modifyInsParam
                .initIsSrcSingleTenant()
                .initIsTargetSingleTenant()
                .initModifyMode()               // 设置资源申请方式
                .initRsTemplate();

        // 初始化AZ相关信息
        modifyInsParam.initOldAVZInfo();
        if (!modifyInsParam.isReadIns()) {
            modifyInsParam.checkDepend("isReadIns").setDispenseMode();
        }

        if (modifyInsParam.getIsEmergencyTransfer()) {
            modifyInsParam.checkDepend("isEmergencyTransfer").setParamsForEmergencyTransfer();
        }
        modifyInsParam
                .initAVZInfo()
                .initRegionId()
                .setIsModifyAvz();

        // 初始化切换信息
        modifyInsParam.initSwitchInfo();

        // serverless
        modifyInsParam.setServerlessInfo();

        // rcu 0.5-64 range check and is a multiple of 0.5
        modifyInsParam.isValidModifyRcu();

        // 检查云盘加密
        modifyInsParam.isEncryptionKeyAvailable();

        // check compression limit
        modifyInsParam.checkCompressionLimit();

        return modifyInsParam;
    }

    public PodModifyInsParam initServerlessSccModifyInsParam(Map<String, String> params) throws Exception {
        PodModifyInsParam modifyInsParam = new PodModifyInsParam(dependency, params);

        // 初始化实例基础信息
        modifyInsParam
                .initCustins()
                .initReplicaSetMeta()
                .multiWriteEngineValidate();

        // 获取用户与实例属性信息
        modifyInsParam
                .initUser()
                .initInstanceName()
                .initDBType()
                .initDBVersion()
                .initSrcMinorVersion()
                .initClusterName();
//                .initOrderId()
//                .initTmpReplicaSetName();

        // 设置业务属性标
        modifyInsParam
                .setIsDHG()
                .setClusterNameToParamsForDHG()
                .setIsTDDL()
                .setIsReadIns()
                .setIsEmergencyTransfer()
                .initRoleHostNameMapping();

        // 初始化迁移变配资源基本信息
        modifyInsParam
                .initSrcCompressionMode()
                .initTargetCompressionMode()
                .initDiskSizeGB()               // 获取原实例磁盘大小
                .initTargetDiskSizeGB()         // 获取目标磁盘大小
                .initClassCode()                // 获取原实例规格码
                .initTargetClassCode()          // 获取目标规格码
                .initSrcInstanceLevel()         // 获取原规格对象
                .initTargetInstanceLevel()      // 获取目标规格对象
                .initSrcDiskType()              // 获取原磁盘类型
                .initAutoPLConfig()
                .setIsPfs()
                .initTargetDiskType()           // 获取目标磁盘类型
                .setGeneralCloudDiskConfig()
                .initColdDataConfig()
                .initPodType();

        modifyInsParam.initOrderId()
                .initTmpReplicaSetName()
                .initIsTargetSingleTenant()
                .initRsTemplate();


        // 初始化AZ相关信息
        modifyInsParam.initOldAVZInfo();
        if (!modifyInsParam.isReadIns()) {
            modifyInsParam.checkDepend("isReadIns").setDispenseMode();
        }

        if (modifyInsParam.getIsEmergencyTransfer()) {
            modifyInsParam.checkDepend("isEmergencyTransfer").setParamsForEmergencyTransfer();
        }
        modifyInsParam
                .initAVZInfo()
                .initRegionId()
                .setIsModifyAvz();

        // 初始化切换信息
        modifyInsParam.initSwitchInfo();

        // serverless
        modifyInsParam.setServerlessInfo();

        // 检查密钥类型，多租户场景不允许创建使用用户密钥的云盘加密实例。
        modifyInsParam.checkEncryptionKey();

        // 检查云盘加密
        modifyInsParam.isEncryptionKeyAvailable();

        // 设置资源申请方式
        modifyInsParam.initServerlessModifyMode();

        modifyInsParam.initRcu();

        // rcu 0.5-64 range check and is a multiple of 0.5
        modifyInsParam.isValidModifyRcu();

        // check compression limit
        modifyInsParam.checkCompressionLimit();

        return modifyInsParam;
    }


    public boolean isEventSchedulerEnabled(Integer custinsId) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("custinsId", custinsId.toString());
        params.put("role", "master");
        params.put("parameter", "event_scheduler");
        Map<String, Object> result = dbossApi.doDbossInvoke(params, "mysql-op/variables");
        if (result == null || result.get("event_scheduler") == null) {
            return false;
        }
        String value = result.get("event_scheduler").toString();
        return "ON".equalsIgnoreCase(value);
    }

    public int getServerlessTaskPriority(String taskKey) {
        return VIP_TASK_PRIORITY.contains(taskKey) ? 1 : 0;
    }

    public void modifyMaxscaleIns(PodModifyInsParam modifyInsParam, Map<String, String> params) throws Exception {
        Double srcMaxRcu = modifyInsParam.getSrcServerlessSpec().getScaleMax();
        Double targetMaxRcu = modifyInsParam.getTargetServerlessSpec().getScaleMax();
        Map<String, String> proxyParam = serverlessResourceService.getProxyParam(params);
        String requestId = proxyParam.get(ParamConstants.REQUEST_ID);

        // 从serverless的replicaSet获取maxscale的replicaSet
        ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
        String maxscaleInsName = serverlessResourceService.getMaxScaleName(requestId, replicaSet.getName());

        ReplicaSet maxscaleReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, maxscaleInsName, true);
        if (null == maxscaleReplicaSet) {
            throw new Exception("modifyMaxscaleIns: cannot find maxscaleIns " + maxscaleInsName + " replicaset");
        }
        logger.info("modifyMaxscaleIns: maxscaleIns replicaset: {}", JSONObject.toJSONString(maxscaleReplicaSet));

        // 用maxscale的replicaSet获取其instanceLevel
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(
                requestId, maxscaleReplicaSet.getService(), maxscaleReplicaSet.getServiceVersion(), maxscaleReplicaSet.getClassCode(), true);
        if (null == instanceLevel) {
            throw new Exception("modifyMaxscaleIns: cannot find maxscaleIns " + maxscaleInsName + " instanceLevel");
        }
        logger.info("modifyMaxscaleIns: maxscaleIns instanceLevel: {}", JSONObject.toJSONString(instanceLevel));

        if (srcMaxRcu <= 16d && targetMaxRcu > 16d && null != instanceLevel.getCpuCores() && instanceLevel.getCpuCores() < 2) {
            proxyParam.put(ParamConstants.ACTION, "ModifyMXSDBInstanceClass");
            proxyParam.put(ParamConstants.DB_INSTANCE_NAME, modifyInsParam.getDbInstanceName());
            proxyParam.put("SwitchTimeMode", "0");
            proxyParam.put("SliceNum", "2");
            Map<String, Object> result = serverlessResourceService.invokeProxyApi(proxyParam);
            logger.info("modifyMaxscaleIns ModifyMXSDBInstanceClass Response {}, params {}", result, proxyParam);
        }
    }
}
