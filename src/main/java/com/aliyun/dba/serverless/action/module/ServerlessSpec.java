package com.aliyun.dba.serverless.action.module;

import com.aliyun.dba.base.common.utils.LangUtil;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.RCU;
import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.*;

@Data
public class ServerlessSpec {
    public Double rcu;
    public Double scaleMin = 0.5;
    public Double scaleMax = 8.0;
    public Boolean autoPause = false;
    public Integer secondsUntilAutoPause = 600;
    public Boolean dasAutoPause = false;
    public String keepRunningTime = "";
    public Boolean switchForce = true;

    public ServerlessSpec(Map<String, String> labels) {
        if (isContainsKey(labels, RCU)) {
            setRcu(Double.valueOf(getValue(labels, RCU)));
        }
        if (isContainsKey(labels, SCALE_MIN)) {
            setScaleMin(Double.valueOf(getValue(labels, SCALE_MIN)));
        }
        if (isContainsKey(labels, SCALE_MAX)) {
            setScaleMax(Double.valueOf(getValue(labels, SCALE_MAX)));
        }
        if (isContainsKey(labels, AUTO_PAUSE)) {
            setAutoPause(Boolean.valueOf(getValue(labels, AUTO_PAUSE)));
        }
        if (isContainsKey(labels, SECONDS_UNTIL_AUTO_PAUSE)) {
            setSecondsUntilAutoPause(Integer.valueOf(getValue(labels, SECONDS_UNTIL_AUTO_PAUSE)));
        }
        if (isContainsKey(labels, DAS_AUTO_PAUSE)) {
            setDasAutoPause(Boolean.valueOf(getValue(labels, DAS_AUTO_PAUSE)));
        }
        if (isContainsKey(labels, KEEP_RUNNING_TIME)) {
            setKeepRunningTime(getValue(labels, KEEP_RUNNING_TIME));
        }
        if (isContainsKey(labels, SWITCH_FORCE)) {
            setSwitchForce(LangUtil.getBoolean(getValue(labels, SWITCH_FORCE)));
        }
    }
    public ServerlessSpec(){
    }

    private boolean isContainsKey(Map<String, String> labels, String key) {
        return labels.containsKey(key) || labels.containsKey(key.toLowerCase());
    }

    private String getValue(Map<String, String> labels, String key) {
        if (labels.containsKey(key)) {
            return labels.get(key);
        } else {
            return labels.getOrDefault(key.toLowerCase(), "");
        }
    }


    /**
     * 获取label配置，不包括RCU，rcu作为规格配置单独更新
     * */
    public Map<String, String> getLabels() {
        Map<String, String> labels = new HashMap<>();
        labels.put(SCALE_MIN, getScaleMin().toString());
        labels.put(SCALE_MAX, getScaleMax().toString());
        labels.put(AUTO_PAUSE, getAutoPause().toString());
        labels.put(SECONDS_UNTIL_AUTO_PAUSE, getSecondsUntilAutoPause().toString());
        labels.put(DAS_AUTO_PAUSE, getDasAutoPause().toString());
        labels.put(KEEP_RUNNING_TIME, getKeepRunningTime());
        labels.put(SWITCH_FORCE, getSwitchForce().toString());
        return labels;
    }

    /**
     * 比较两个spec是否相同,current just the four params can be modify!
     * */
    public boolean compareModifyParams(ServerlessSpec serverlessSpec) {
            if(serverlessSpec.getScaleMin().equals(scaleMin)
                && serverlessSpec.getScaleMax().equals(scaleMax)
                && serverlessSpec.getAutoPause().equals(autoPause)
//                &&serverlessSpec.getRcu().equals(rcu)
//                && serverlessSpec.getSecondsUntilAutoPause().equals(secondsUntilAutoPause)
//                && serverlessSpec.getDasAutoPause().equals(dasAutoPause)
//                && serverlessSpec.getKeepRunningTime().equals(keepRunningTime)
                && serverlessSpec.getSwitchForce().equals(switchForce))
            {
                return false;
            }
            return true;
    }
}
