package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.AllocateTmpResourceService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.AllocateTmpResourceResult;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.serverless.action.service.StoppedServerlessMaintainService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.RCU;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessMigrateDBInstanceImpl")
@Slf4j
public class MigrateDBInstanceImpl implements IAction {
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private StoppedServerlessMaintainService stoppedServerlessMaintainService;
    @Resource
    protected PodParameterHelper podParameterHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID);

        boolean isSuccess = false;
        boolean isAllocated = false;
        ReplicaSetResourceRequest tmpResourceRequest = new ReplicaSetResourceRequest();

        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(actionParams);
            boolean isStandard = InstanceLevel.CategoryEnum.SERVERLESS_STANDARD .toString().equalsIgnoreCase(replicaSet.getCategory());
            boolean isReadOnly = ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType());
            boolean switchForce = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(actionParams, "switchForce", "false"));
            if (isReadOnly) {
                switchForce = true;
            }

            // 来自scc的跨机，而且是没有maxScale的v1版本直接拒绝
            String accessId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.ACCESSID);
            if (ServerlessConstant.SERVERLESS_SCC_ACCESS.equalsIgnoreCase(accessId) && serverlessResourceService.isServerlessV1(replicaSet)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // 检查Maxscale内核小版本
            Integer maxscaleInsId = replicaSetService.getMaxscaleInsId(replicaSet.getName());

            // when switchForce is true, it will automatic upgrade maxscale in workflow
            if (maxscaleInsId != null && !switchForce) {
                String maxScaleMinorVersion = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, maxscaleInsId);
                if (maxScaleMinorVersion.compareTo(ServerlessConstant.MAXSCALE_MINOR_VERVION) < 0) {
                    log.error("custins maxscale version {}, and migrate_minor_version request >= {}.", maxScaleMinorVersion, ServerlessConstant.MAXSCALE_MINOR_VERVION);
                    throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
                }
                if (isStandard) {
                    if (maxScaleMinorVersion.compareTo(ServerlessConstant.MAXSCALE_VERVION_FOR_SERVERLESS_STANDARD_MIGRATION) < 0) {
                        log.error("custins maxscale version {}, and migrate_minor_version request >= {}.", maxScaleMinorVersion, ServerlessConstant.MAXSCALE_VERVION_FOR_SERVERLESS_STANDARD_MIGRATION);
                        throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
                    }
                }
            }

            // 参数检查
            if (!PodParameterHelper.isAliYun(replicaSet.getBizType())) {
                log.error("only support aliyun!");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // 只读检查主实例状态
            if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), false);
                if (primaryReplicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION
                        || primaryReplicaSet.getLockMode() != ReplicaSet.LockModeEnum.NOLOCK) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_MASTER_DBINSTANCE_STATUS);
                }
            }

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());

            if (!replicaSetService.isActive(replicaSet) && !ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus())) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            // 确定serverless rcu
            ServerlessSpec serverlessSpec = serverlessResourceService.getServerlessSpec(requestId, replicaSet.getName());
            if (serverlessSpec == null) {
                log.error("can not find serverless spc");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            if (!mysqlParamSupport.hasParameter(actionParams, RCU.toLowerCase())) {
                String rcu = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSet.getName(), RCU.toLowerCase());
                actionParams.put(RCU.toLowerCase(), rcu);
            }
            val modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(actionParams);

            // 申请临时实例
            isAllocated = true;
            AllocateTmpResourceResult tmpResourceResult = allocateTmpResourceService.make(requestId, modifyInsParam.getCustins(), modifyInsParam);
            tmpResourceRequest = tmpResourceResult.getResourceRequest();

            // 临时实例关联主实例白名单标签，以及RCU信息
            Map<String, String> labels = ImmutableMap.of(
                    PodDefaultConstants.SG_USE_OTHER_CUSTINS, replicaSet.getId().toString(),
                    RCU, String.valueOf(modifyInsParam.getRcu())
            );
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpResourceRequest.getReplicaSetName(), labels);
            // 只读实例配置白名单同步label
            podParameterHelper.setReadInsSgLabel(requestId, replicaSet, tmpResourceRequest.getReplicaSetName());

            // 下发任务
            JSONObject taskParams = new JSONObject();
            taskParams.put("srcReplicaSetName", replicaSet.getName());
            taskParams.put("destReplicaSetName", tmpResourceRequest.getReplicaSetName());
            taskParams.put("switchInfo", modifyInsParam.getSwitchInfo());
            taskParams.put("rcu", modifyInsParam.getRcu());
            if (replicaSetService.isStopped(replicaSet)) {
                taskParams.put("needStart", "true");
            }
            taskParams.put("switchForce", switchForce);

            String taskParamString = taskParams.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = ServerlessConstant.TASK_MIGRATE_SERVERLESS_BASIC_INS;
            if (isStandard) {
                taskKey = "migrate_serverless_standard_ins";
            }

            // call from scc is for serverless scale, use taskKey: migrate_serverless_basic_ins / migrate_serverless_standard_ins
            // call from others use taskKey: migrate_serverless_ins_for_cost
            if (!ServerlessConstant.SERVERLESS_SCC_ACCESS.equalsIgnoreCase(accessId)) {
                taskKey = ServerlessConstant.TASK_MIGRATE_SERVERLESS_INS_FOR_COST;
            }

            if (isReadOnly) {
                taskKey = "migrate_serverless_basic_read_ins";
            }

            if (replicaSetService.isStopped(replicaSet)) {
                stoppedServerlessMaintainService.dispatchStartTask(requestId, replicaSet, null);
            }

            Object taskId = workFlowService.dispatchTask(
                    "custins", modifyInsParam.getDbInstanceName(), domain, taskKey, taskParamString, 0);

            // 修改实例状态
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.TRANSING.toString());
            if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getPrimaryInsName(), ReplicaSet.StatusEnum.INS_MAINTAINING.toString());
            }
            isSuccess = true;

            data.put("TaskId", taskId);
            return data;

        } catch (RdsException e) {
            log.warn("RdsException: ", e);
            return createErrorResponse(e.getErrorCode());
        } catch (ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception e) {
            log.error("migrate serverless ins failed: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (isAllocated && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, tmpResourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    log.error(" release resource for migrate serverless failed: ", e);
                }
            }
        }
    }
}
