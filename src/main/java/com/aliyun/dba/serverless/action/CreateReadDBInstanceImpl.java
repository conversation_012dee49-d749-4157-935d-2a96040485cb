package com.aliyun.dba.serverless.action;

import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.apsaradb.activityprovider.model.GeneralCloudDisk;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.dba.base.common.utils.LangUtil;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.serverless.action.service.ServerlessCreateReadDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_STORAGE_TYPE;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ_BACKUP;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessCreateReadDBInstanceImpl")
public class CreateReadDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateReadDBInstanceImpl.class);
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport paramSupport;
    @Resource
    private DBaasMetaService metaService;
    @Resource
    private GdnInstanceService gdnInstanceService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private MysqlEncryptionService mysqlEncryptionService;
    @Resource
    private ServerlessCreateReadDBInstanceService serverlessCreateReadDBInstanceService;
    @Resource
    private AliyunInstanceDependency dependency;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private CustinsService custinsService;
    @Resource
    private CustinsParamService custinsParamService;

    /**
     * 创建只读实例
     *
     * @param custins custins
     * @param params params
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        try {
            //step 1: set params thread local
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);

            //step 2: build read only request
            CreateReadOnlyInsRequest createReadOnlyRequest = buildCommonRequest(dependency, params);

            //step 3: do create read only
            Object result = serverlessCreateReadDBInstanceService.doCreateReadOnly(createReadOnlyRequest, params);

            if(result instanceof Map) {
                return (Map<String, Object>) result;
            }

            //step 4: build response
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", result);
            data.put("DBInstanceName", createReadOnlyRequest.getDbInstanceName());
            data.put("ReadDBInstanceName", createReadOnlyRequest.getReadInsName());
            data.put("ConnectionString", createReadOnlyRequest.getConnectionString());
            data.put("Port", createReadOnlyRequest.getConnStrPortStr());
            return data;
        } catch (RdsException e) {
            logger.error("doCreateReadOnly failed!", e);
            return createErrorResponse(e.getErrorCode());
        } catch (Exception e) {
            logger.error("doRecoverDBInstance failed!", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * build common request
     */
    private CreateReadOnlyInsRequest buildCommonRequest(AliyunInstanceDependency dependency,
                                                                  Map<String, String> params) throws Exception {
        checkMaxscaleVersion(params);
        CreateReadOnlyInsRequest request = new CreateReadOnlyInsRequest(dependency, params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        Integer userId = mysqlParameterHelper.getAndCreateUserId();
        User user = metaService.getDefaultClient().getUserById(requestId, userId, false);
        String bid = user.getBid();
        String uid = user.getAliUid();
        String dbInstanceName = paramSupport.getDBInstanceName(params);
        String insType = paramSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_USED_TYPE,
                CUSTINS_INSTYPE_READ.toString());
        String dbType = paramSupport.getAndCheckDBType(params, null);
        String dbVersion = paramSupport.getAndCheckDBVersion(params, dbType, true);
        String readInsName = CheckUtils.checkValidForInsName(paramSupport.getParameterValue(params,
                "ReadDBInstanceName"));
        String targetMinorVersion = paramSupport.getParameterValue(params, "TargetMinorVersion");
        String rsTemplateName = paramSupport.getParameterValue(params, "RsTemplateName");
        String orderId = paramSupport.getParameterValue(params, ParamConstants.ORDERID);
        String accessId = paramSupport.getParameterValue(params, ParamConstants.ACCESSID);
        String comment = null;
        if (CustinsParamSupport.hasParameterValue(params, ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            comment = CheckUtils.checkLength(
                    SupportUtils.decode(getParameterValue(params, ParamConstants.DB_INSTANCE_DESCRIPTION)),
                    1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
        }

        String centerRegionId = null;
        ReplicaSet primaryReplicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
        String portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), primaryReplicaSet.getService());
        String connStrPortStr = CheckUtils.parseInt(portStr, 1000, 65534, ErrorCode.INVALID_PORT).toString();
        String bizType = primaryReplicaSet.getBizType().toString();

        String diskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE);
        if (StringUtils.isEmpty(diskType)){
            diskType = getParameterValue(params, "StorageType");
        }
        if (StringUtils.isEmpty(diskType)){
            ReplicaListResult listReplicasInReplicaSet = metaService.getRegionClient(centerRegionId).listReplicasInReplicaSet(
                    requestId, primaryReplicaSet.getName(), null, null, null, null);
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            Optional<Replica> optReplica = currentReplicas.stream().filter(replica -> Replica.RoleEnum.MASTER.equals(replica.getRole())).findFirst();
            Replica masterReplica = optReplica.orElseGet(() -> !currentReplicas.isEmpty() ? currentReplicas.get(0) : null);
            diskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE, masterReplica != null ? masterReplica.getStorageType().toString() : "local_ssd");
        }

        Integer diskSize = CheckUtils.parseInt(paramSupport.getParameterValue(params, ParamConstants.STORAGE, primaryReplicaSet.getDiskSizeMB() / 1024),
                5, 102400, ErrorCode.INVALID_STORAGE);
        String classCode = paramSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
        String clusterName = paramSupport.getParameterValue(params, "ClusterName");
        String vpcId = getParameterValue(params, ParamConstants.VPC_ID);
        String vpcSwitch = getParameterValue(params, ParamConstants.VSWITCH_ID);
        String ipAddress = getParameterValue(params, ParamConstants.IP_ADDRESS);
        String vpcInstanceId = getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);
        String performanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(diskType);
        diskType = PodParameterHelper.transferDiskTypeParam(diskType);
        String instructionSetArch = paramSupport.getParameterValue(params, "InstructionSetArch");
        //公有云通过instance_level中的扩展信息字段来决定是否是arm架构
        InstanceLevel instanceLevel = metaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
        String dbEngine = paramSupport.isMysqlXDB(primaryReplicaSet.getService(), primaryReplicaSet.getServiceVersion(), primaryReplicaSet.getClassCode()) ? "XDB" : "MySQL";
        Boolean isDhg = paramSupport.isDHGCluster(clusterName);
        boolean isArmIns = CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(instructionSetArch);
        String insTypeDesc = CUSTINS_INSTYPE_READ_BACKUP.toString().equals(insType)
                ? ReplicaSet.InsTypeEnum.READBACKUP.toString() : ReplicaSet.InsTypeEnum.READONLY.toString();
        boolean isXdbEngine = "XDB".equals(dbEngine);
        dbVersion = isArmIns ? dbVersion : primaryReplicaSet.getServiceVersion();
        InstanceLevel primaryInsInstanceLevel = metaService.getDefaultClient().getInstanceLevel(requestId, primaryReplicaSet.getService(),
                primaryReplicaSet.getServiceVersion(), primaryReplicaSet.getClassCode(), true);
        if (!Objects.equals(primaryInsInstanceLevel.getHostType(), instanceLevel.getHostType())) {
            logger.error("inconsistent level between primary and read-only instances");
            throw new RdsException(ErrorCode.INVALID_LEVEL);
        }
        boolean isSingleTenant = replicaSetService.isCloudSingleTenant(primaryReplicaSet.getBizType(), diskType, instanceLevel, isDhg);
        // 多租户场景不允许创建使用用户密钥的云盘加密实例。
        mysqlEncryptionService.checkEncryptionKeyByReplicaSet(requestId, primaryReplicaSet, isSingleTenant);
        GeneralCloudDisk generalCloudDisk = podCommonSupport.setGeneralCloudDiskConfig(requestId, params, dbVersion, instanceLevel, diskType, primaryReplicaSet, dbInstanceName);

        // 只读实例写优化完全继承主实例配置
        String optimizedWritesInfo = podCommonSupport.getOptimizedWritesInfo(requestId, dbVersion, diskType, primaryReplicaSet);

        request.setBizType(bizType);
        request.setCenterRegionId(centerRegionId);
        request.setClassCode(classCode);
        request.setClusterName(clusterName);
        request.setConnStrPortStr(connStrPortStr);
        request.setConnType(CONN_TYPE_PHYSICAL);
        request.setDbEngine(dbEngine);
        request.setDbInstanceName(dbInstanceName);
        request.setDbType(dbType);
        request.setDbVersion(dbVersion);
        request.setDiskSize(diskSize);
        request.setInstructionSetArch(instructionSetArch);
        request.setInsType(insType);
        request.setIsArmIns(isArmIns);
        request.setIsDhg(isDhg);
        request.setRequestId(requestId);
        request.setRsTemplateName(rsTemplateName);
        request.setGeneralCloudDisk(generalCloudDisk);
        request.setOptimizedWritesInfo(optimizedWritesInfo);
        request.setUserId(userId);
        request.setDiskType(diskType);
        request.setReadInsName(readInsName);
        request.setBid(bid);
        request.setUid(uid);
        request.setPrimaryReplicaSet(primaryReplicaSet);
        request.setInsTypeDesc(insTypeDesc);
        request.setVpcId(vpcId);
        request.setVSwitchId(vpcSwitch);
        request.setIPAddress(ipAddress);
        request.setVpcInstanceId(vpcInstanceId);
        request.setPerformanceLevel(performanceLevel);
        request.setIsXdbEngine(isXdbEngine);
        request.setTargetMinorVersion(targetMinorVersion);
        request.setPrimaryInsInstanceLevel(primaryInsInstanceLevel);
        request.setComment(comment);
        request.setOrderId(orderId);
        request.setAccessId(accessId);

        String connectionString = getParameterValue(params, ParamConstants.CONNECTION_STRING);
        //String connectionString = getConnnectionString(request);
        request.setConnectionString(connectionString);
        request.setServerlessStorageAutoScale();
        request.setServerlessInfo();
        request.setActivities();

        return request;
    }

    private void checkMaxscaleVersion(Map<String, String> params) throws RdsException {
        CustInstanceDO custins = mysqlParamSupport.getCustInstance(params);
        List<CustinsServiceDO> custinsServiceDOS = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
        if (CollectionUtils.isEmpty(custinsServiceDOS)) {
            return;  //不带maxscale不用校验
        }
        Integer maxScaleCustinsId = LangUtil.getInteger(custinsServiceDOS.get(0).getServiceId());
        CustInstanceDO maxscale = custinsService.getCustInstanceByCustinsId(maxScaleCustinsId);
        if (maxscale == null || maxscale.isDeleting()) {
            return;  //maxscale正在删除不用校验
        }
        CustinsParamDO custinsMinorVersion = custinsParamService.getCustinsParam(maxScaleCustinsId, "minor_version");
        if (custinsMinorVersion != null && StringUtils.isNotBlank(custinsMinorVersion.getValue())) {
            String maxScaleMinorVersion = custinsMinorVersion.getValue();
            logger.info("maxScaleMinorVersion: {}", maxScaleMinorVersion);
            if (maxScaleMinorVersion.startsWith("1")) {
                throw new RdsException(ErrorCode.UNSUPPORTED_MAXSCALE_MINOR_VERSION,
                        "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first.");
            }
        }
    }
}