package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.commonkindcode.action.DescribeDBInstanceParameterListImpl;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.serverless.action.service.StoppedServerlessMaintainService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_LOCK_NO;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessUnlockDBInstanceImpl")
public class UnlockDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterListImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MycnfService mycnfService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    private ReplicaSetService replicaSetService;
    @Autowired
    private DBaasMetaService dBaasMetaService;
    @Resource
    private StoppedServerlessMaintainService stoppedServerlessMaintainService;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
        throws RdsException {

        try {
            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
            String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);
            custins = mysqlParaHelper.getWithoutCheckCustInstance();

            ReplicaSet replicaSet = replicaSetService.getReplicaSet(actionParams);
            if (!(replicaSetService.isActive(replicaSet) || replicaSetService.isStopped(replicaSet))) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }
            String unlockModeStr = mysqlParaHelper.getParameterValue(ParamConstants.UNLOCK_MODE);

            if (unlockModeStr != null) {
                Integer unlockMode =
                        CheckUtils.parseInt(unlockModeStr, null, null, ErrorCode.INVALID_PARAMETERS);
                if (!MysqlParameterHelper.checkUnlockPrecondition(unlockMode, custins)) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE,
                            "Cannot unlock for UnlockMode " + unlockMode +
                                    ", because the current LockMode is " + custins.getLockMode());
                }
            }

            // 返回值
            Map<String, Object> data = new HashMap<String, Object>(5);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("LockMode", CUSTINS_LOCK_NO);

            if (replicaSetService.isStopped(replicaSet)) {
                stoppedServerlessMaintainService.dispatchStartTask(requestId, replicaSet, ReplicaSet.StatusEnum.ACTIVATION);
            }

            String taskKey = PodDefaultConstants.TASK_UNLOCK_INS;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("lockMode","0");
            jsonObject.put("lockReason","");
            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask("custins", custins.getInsName(), "mysql", taskKey, parameter, 0);

            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
