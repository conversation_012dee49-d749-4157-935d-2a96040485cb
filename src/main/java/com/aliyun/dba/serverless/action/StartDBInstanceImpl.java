package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.poddefault.action.service.TransListService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.RCU;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessStartDBInstanceImpl")
public class StartDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(StartDBInstanceImpl.class);

    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private InstanceIDao instanceIDao;
    @Resource
    private TransListService transListService;
    @Resource
    private ServerlessResourceService serverlessResourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID, "");

        try {
            ReplicaSet replicaSetMeta = null;
            try {
                replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);

                if (!serverlessResourceService.isServerlessV2(replicaSetMeta)) {
                    logger.error("{} ReplicaSet is not serverless v2.", requestId);
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
                }

                else if (workFlowService.isTaskExist(requestId, replicaSetMeta.getName())) {
                    logger.error("{} replicaset {} has unfinished tasks.", requestId, replicaSetMeta.getName());
                    throw new RdsException(ErrorCode.TASK_HAS_EXIST);
                }

                else if (!(ReplicaSet.StatusEnum.STOPPED.equals(replicaSetMeta.getStatus()))) {
                    throw new RdsException(ErrorCode.INVALID_STATUS);
                }
            } catch (ApiException e) {
                logger.error("StartDBInstance api exception, requestId: {}, msg: {}", requestId, e.getMessage(), e);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }

            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                    requestId, replicaSetMeta.getName(), null, null, null, null);
            Optional<Replica> stoppedReplica = listReplicasInReplicaSet
                    .getItems()
                    .stream()
                    .filter(x -> Replica.StatusEnum.STOPPED.equals(x.getStatus()))
                    .findFirst();

            // 资源已释放/正在释放，重新申请资源，拉起实例
            if (stoppedReplica.isPresent()) {
                return startIns(replicaSetMeta, params);
            }
            // 计算资源还存在，直接激活实例
            else {
                return resumeIns(replicaSetMeta, params);
            }

        }  catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (RdsException re) {
            logger.error("{} StartDBInstance ex=" + re, requestId, re);
            return ResponseSupport.createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error("{} StartDBInstance ex=" + ex, requestId, ex);
            return ResponseSupport.createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 启动实例，需要申请计算资源
     * */
    private Map<String, Object> startIns(ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID, "");

        boolean isSuccess = false;
        boolean isAllocated = false;
        ReplicaSetResourceRequest replicaSetResourceRequest = null;

        try {
            // 申请计算资源
            ServerlessSpec serverlessSpec = serverlessResourceService.getServerlessSpec(requestId, replicaSetMeta.getName());
            String rcuParam = mysqlParamSupport.getParameterValue(params, RCU);

            // 不指定rcu时，默认以最小rcu启动
            Double rcu = StringUtils.isEmpty(rcuParam) ?
                    serverlessResourceService.getServerlessSpec(requestId, replicaSetMeta.getName()).getScaleMin()
                    : Double.valueOf(rcuParam);

            replicaSetResourceRequest = serverlessResourceService
                    .getResumeReplicasetResource(requestId, replicaSetMeta.getName(), rcu);

            //资源annotations相关
            String activities = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetMeta.getName(), "activities");
            replicaSetResourceRequest.setAnnotations(serverlessResourceService.getAnnotations(JSONObject.parseObject(activities, Map.class),
                    serverlessSpec.getScaleMin(), serverlessSpec.getScaleMax()));

            String tmpReplicaSetName = replicaSetResourceRequest.getReplicaSetName();
            logger.info("allocate resoure for start {}, request body: {}",
                    tmpReplicaSetName,
                    JSON.toJSONString(replicaSetResourceRequest));

            // 资源申请完毕
            isAllocated = commonProviderService
                    .getDefaultApi()
                    .allocateResourceForResume(requestId, replicaSetMeta.getName(), replicaSetResourceRequest);

            // 临时实例关联主实例白名单标签，以及RCU信息
            Map<String, String> labels = ImmutableMap.of(
                    PodDefaultConstants.SG_USE_OTHER_CUSTINS, replicaSetMeta.getId().toString(),
                    RCU, rcu.toString()
                    );
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSetResourceRequest.getReplicaSetName(), labels);

            // 只读实例配置白名单同步label
            podParameterHelper.setReadInsSgLabel(requestId, replicaSetMeta, tmpReplicaSetName);

            ReplicaSet tmpReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, tmpReplicaSetName, true);
            Integer transListId = transListService.create(replicaSetMeta, tmpReplicaSet, "start stopped ins", requestId);

            // 下发启动任务
            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);

            // 如果非scc 下发的启动请求，则任务流需要向scc 同步计费信息
            if (!StringUtils.equalsIgnoreCase(accessId, ServerlessConstant.SERVERLESS_SCC_ACCESS)) {
                taskParam.put("rcu", rcu);
            }

            taskParam.put("srcReplicaSetName", replicaSetMeta.getName());
            taskParam.put("destReplicaSetName", tmpReplicaSet.getName());
            taskParam.put("rcu", rcu);

            taskParam.put(CustinsSupport.TRANS_ID, transListId);

            Object taskId = workFlowService.dispatchTask(
                    "custins",
                    replicaSetMeta.getName(),
                    PodDefaultConstants.DOMAIN_MYSQL,
                    ServerlessConstant.TASK_START_SERVERLESS_INS,
                    taskParam.toString(),
                    0
            );
            isSuccess = true;

            Integer taskIdInt = Double.valueOf(taskId.toString()).intValue();
            instanceIDao.updateTransTaskIdById(transListId, taskIdInt);
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    requestId,
                    replicaSetMeta.getName(),
                    ReplicaSet.StatusEnum.STARTING.toString()
            );

            Map<String, Object> data = new HashMap<String, Object>(4);
            data.put("DBInstanceID", replicaSetMeta.getId());
            data.put("DBInstanceName", replicaSetMeta.getName());
            data.put("TaskId", taskId);

            return data;
        } finally {
            // 处理失败时释放资源
            if (isAllocated && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId,
                            replicaSetResourceRequest.getReplicaSetName()
                    );
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    logger.error(requestId + " release resource for start ins failed: " + e.getMessage(), e);
                }
            }
        }
    }


    /**
     * 唤醒实例，此时实例的计算资源还没有释放，直接激活即可
     * */
    private Map<String, Object> resumeIns(ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        String accessId = mysqlParamSupport.getParameterValue(params, ParamConstants.ACCESSID, "");

        // 不指定rcu时，默认以最小rcu启动
        Double rcu = serverlessResourceService.getServerlessSpec(requestId, replicaSetMeta.getName()).getScaleMin();

        JSONObject taskParamObject = new JSONObject();
        taskParamObject.put("requestId", requestId);
        taskParamObject.put("replicaSetName", replicaSetMeta.getName());

        // 如果非scc 下发的启动请求，则任务流需要向scc 同步计费信息
        if (!StringUtils.equalsIgnoreCase(accessId, ServerlessConstant.SERVERLESS_SCC_ACCESS)) {
            taskParamObject.put("rcu", rcu);
        }

        Object taskId = workFlowService.dispatchTask(
                "custins",
                replicaSetMeta.getName(),
                PodDefaultConstants.DOMAIN_MYSQL,
                ServerlessConstant.TASK_RESUME_SERVERLESS_INS,
                taskParamObject.toJSONString(),
                0
        );

        dBaasMetaService.getDefaultClient().deleteReplicaSetLabel(requestId, replicaSetMeta.getName(), PodDefaultConstants.TASK_STOP_INS_STAGE_2);
        dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSetMeta.getName(), ReplicaSet.StatusEnum.STARTING.toString());

        Map<String, Object> data = new HashMap<String, Object>(4);
        data.put("DBInstanceID", replicaSetMeta.getId());
        data.put("DBInstanceName", replicaSetMeta.getName());
        data.put("TaskId", taskId);

        return data;
    }
}



