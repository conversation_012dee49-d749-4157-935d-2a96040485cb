package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.serverless.action.service.StoppedServerlessMaintainService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessDeleteDBInstanceImpl")
public class DeleteDBInstanceImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.serverless.action.DeleteDBInstanceImpl.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    ReplicaSetService replicaSetService;
    @Autowired
    protected CheckService checkService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private StoppedServerlessMaintainService stoppedServerlessMaintainService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!replicaSetService.isServerless(replicaSet)) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            if (!replicaSetService.isActive(replicaSet) &&
                    !ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus())) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            String domain = ReplicaSetService.isTDDL(replicaSet) ? PodDefaultConstants.DOMAIN_XDB : PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = ServerlessConstant.TASK_DELETE_SERVERLESS_INS;

            // 存在只读不能做主实例删除
            if (replicaSet.getInsType() == ReplicaSet.InsTypeEnum.MAIN) {
                List<ReplicaSet> readOnlyReplicaSetList = dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId, replicaSet.getName(), ReplicaSet.InsTypeEnum.READONLY.toString()).getItems();
                if (CollectionUtils.isNotEmpty(readOnlyReplicaSetList)) {
                    long readCount = readOnlyReplicaSetList.stream().filter(v -> (v.getStatus() != ReplicaSet.StatusEnum.DELETING && v.getStatus() != ReplicaSet.StatusEnum.DELETED)).count();
                    if (readCount > 0) {
                        logger.error("there are {} readins, do not allow to delete", readCount);
                        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                    }
                }
            }
            boolean isTddlTaskMigrate = replicaSetService.isTddlDeleteIns(requestId, replicaSet);
            if(isTddlTaskMigrate){
                logger.error("Serverless instance do not support TDDL instance{}", JSON.toJSONString(replicaSet));
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            Map<String, Object> data = new HashMap<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);

            if (ReplicaSetService.isReplicaSetAliGroup(replicaSet)) {
                Integer slientHours = mysqlParamSupport.getSlientHours(params);
                //获取静默的模式(默认指定时间点静默)
                String slientMode = CustinsParamSupport.getParameterValue(params, ParamConstants.SWITCH_TIME_MODE, CustinsSupport.SWITCH_POINT);
                if (CustinsSupport.SWITCH_POINT.equalsIgnoreCase(slientMode)) {
                    // 可以指定时间静默时间(默认静默24小时)
                    if (!params.containsKey(ParamConstants.SWITCH_TIME.toLowerCase())) {
                        params.put(ParamConstants.SWITCH_TIME.toLowerCase(), mysqlParamSupport.getUtcTimeForSliceHours(slientHours));
                        params.put(ParamConstants.SWITCH_TIME_MODE.toLowerCase(), CustinsSupport.SWITCH_POINT);
                    }
                }
                // 如果指定立即切换模式，静默时间是0.如果不指定切换模式相当于直接走指定静默24小时
                Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);

                jsonObject.put("slient_Info", switchInfoMap);
                // 判断是否跳过备份，支持传参和传标两种方式
                if (mysqlParamSupport.isSkipBackup(params)){
                    jsonObject.put("skipBackup", true);
                } else {
                    // 由于当前若曼蒂不支持传参，需要河源裁撤不备份，这里当前通过实例打标的方式来识别(默认需要备份)
                    ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(requestId, dbInstanceName);
                    String skip_backup = replicaSetResource.getReplicaSet().getLabels().get(PodDefaultConstants.SKIP_BACKUP_LABLE);
                    if (StringUtils.isNotBlank(skip_backup)) {
                        jsonObject.put("skipBackup", true);
                    }
                }
            }

            if (replicaSetService.isStopped(replicaSet)) {
                stoppedServerlessMaintainService.dispatchStartTask(requestId, replicaSet, null);
            }

            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);

            // TODO 集团TDDL链路实例先不要在这里做标记删除
            if (!ReplicaSetService.isTDDL(replicaSet) || isTddlTaskMigrate) {
                replicaSet.setStatus(ReplicaSet.StatusEnum.DELETING);
                dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, dbInstanceName, replicaSet);
            }

            data.put("TaskId", taskId);
            data.put("DBInstanceName", dbInstanceName);
            return data;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
