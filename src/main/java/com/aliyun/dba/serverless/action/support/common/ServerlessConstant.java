package com.aliyun.dba.serverless.action.support.common;

import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.ImmutableList;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ServerlessConstant {
    // serverless 相关开关（配置于resource表）
    public static final String SERVERLESS_KERNEL_PARAMS_NO_SUPPORT_EDIT = "SERVERLESS_KERNEL_PARAMS_NO_SUPPORT_EDIT";
    public static final String ENABLE_MYSQL_SERVERLESS_UPGRADE_MINOR_VERSION = "ENABLE_MYSQL_SERVERLESS_UPGRADE_MINOR_VERSION";
    public static final String ENABLE_MYSQL_SERVERLESS_UPGRADE_V1_TO_V2 = "ENABLE_MYSQL_SERVERLESS_UPGRADE_V1_TO_V2";
    public static final String ENABLE_MYSQL_PROVISION_SERVERLESS_MIGRATE = "ENABLE_MYSQL_PROVISION_SERVERLESS_MIGRATE";
    public static final String MYSQL_SERVERLESS_V1_UID = "MYSQL_SERVERLESS_V1_UID";


//    public static final String GAD_ROLE_CENTRAL = "CENTRAL";// 中心节点
    public static final String ALLOW_REMOTE_SCALE = "allowRemoteScale";
    public static final String DEFAULT_ALLOW_REMOTE_SCALE = "true";
    public static final String SERVERLESS_ENV = "serverless";
    public static final String SERVERLESS_CATEGORY = "is_serverless";
    public static final String IS_SERVERLESS = "true";
    public static final String SCALE_MIN = "scaleMin";
    public static final String SCALE_MAX = "scaleMax";
    public static final String DISK_STORAGE_SIZE = "DiskStorageSize";
    public static final String DAS_AUTO_PAUSE = "dasAutoPause";
    public static final String KEEP_RUNNING_TIME = "keepRunningTime";
    public static final String AUTO_PAUSE = "autoPause";
    public static final String SECONDS_UNTIL_AUTO_PAUSE = "secondsUntilAutoPause";
    public static final String SWITCH_FORCE = "switchForce";
    public static final String SERVERLESS_RCU = "rcu";

    //For Task
    public final static String TASK_CREATE_SERVERLESS_INS = "create_serverless_ins";
    public final static String TASK_DELETE_SERVERLESS_INS = "delete_serverless_ins";
    public static final String TASK_CLONE_SERVERLESS_INS = "clone_serverless_ins";
    public final static String TASK_MODIFY_SERVERLESS_CONFIG_INS = "modify_serverless_config_ins";
    public final static String TASK_MODIFY_SERVERLESS_RCU_INS = "elastic_modify_serverless_basic";

    public final static String TASK_PAUSE_SERVERLESS_INS = "pause_serverless_ins";
    public final static String TASK_STOP_SERVERLESS_INS = "stop_serverless_ins";
    public final static String TASK_RESUME_SERVERLESS_INS = "resume_serverless_ins";
    public final static String TASK_START_SERVERLESS_INS = "start_serverless_ins";
    public final static String TASK_START_SERVERLESS_FOR_MAINTAIN = "start_serverless_for_maintain";

    public final static String TASK_LOCK_SERVERLESS_INS = "lock_serverless_ins";
    public final static String TASK_UNLOCK_SERVERLESS_INS = "unlock_serverless_ins";

    public final static String TASK_UPGRADE_MINOR_VERSION_FOR_SERVERLESS_BASIC = "upgrade_minor_version_for_serverless_basic";
    public final static String TASK_UPGRADE_MINOR_VERSION_FOR_SERVERLESS = "upgrade_minor_version_for_serverless";

    public final static String TASK_UPGRADE_SERVERLESS_V1_TO_V2 = "upgrade_serverless_v1_to_v2";
    public final static String TASK_MIGRATE_PROVISION_TO_SERVERLESS = "migrate_provision_to_serverless";
    public final static String TASK_MIGRATE_SERVERLESS_TO_PROVISION = "migrate_serverless_to_provision";

    public final static String TASK_MIGRATE_SERVERLESS_BASIC_INS = "migrate_serverless_basic_ins";
    public final static String TASK_MODIFY_SERVERLESS_BASIC_TO_STANDARD = "modify_serverless_basic_to_standard";
    public final static String TASK_MIGRATE_SERVERLESS_INS_FOR_COST = "migrate_serverless_ins_for_cost";

    public final static String TASK_MIGRATE_SERVERLESS_FAIL_OVER = "migrate_serverless_fail_over";

    /**
     * Serverless basic recovery
     */
    public final static String TASK_RECOVERY_SERVERLESS_BASIC = "recover_serverless_basic_ins";

    public static final String SERVERLESS_DEFAULT_REQUESTID = "1111111";
    public static final ImmutableList<String> serverlessSpecialAction =
            ImmutableList.of(
                    "createdbinstance",
                    "createreaddbinstance",
                    "clonedbinstance",
                    "deletedbinstance",
                    "destroydbinstance",
                    "modifydbinstanceclass",
                    "evaluatemodifyregionresource",
                    "startdbinstance",
                    "stopdbinstance",
                    "describedbinstancessl",
                    "modifydbinstancessl",
                    "createdbinstancenettype",
                    "deletedbinstancenettype",
                    "modifydbinstanceconnectionstring",
                    "modifydbinstancevip",
                    "lockdbinstance",
                    "unlockdbinstance",
                    "migratedbinstance",
                    "switchdbinstanceha",
                    "upgradedbversion"
            );
    public static final ImmutableList<String> SERVERLESS_CATEGORY_TYPE = ImmutableList.of("serverless_basic","serverless_standard");
    public static final String SERVERLESS_SCC_ACCESS = "SCC";
    public static final String SERVERLESS_OPENAPI_ACCESS = "OPENAPI";
    public static final String LAFITE_ACCESS = "Lafite";


    //means DAS invoke to autoscale disk size
    public static final String SERVERLESS_DISK_SCALE_SOURCEBIZ = "AutoScale";
    public static final Double ZERO_DOUBLE = 0d;

    //Category
    public static final String SERVERLESS_BASIC = "serverless_basic";
    public static final String SERVERLESS_STANDARD = "serverless_standard";


    // Serverless's category as same as mysql basic
    public static final String SERVERLESS_BASIC_PARAMS_GROUP_CATEGORY = "basic";
    public static final String SERVERLESS_STANDARD_PARAMS_GROUP_CATEGORY = "standard";



    //For DAS auto disk size
    public final static Integer  SERVERLESS_STORAGE_AUTO_SCALE = 1;
    public final static Integer  SERVERLESS_STORAGE_THRESHOLD = 20;
    public final static Integer  SERVERLESS_STORAGE_UPPER_BOND = 32000;
    public final static String RDS_API_WITH_LOCK = "rdsapiwithlock";

    // v1 升 v2 期间，允许实例进行弹升
    public static final String LABEL_SCALE_UPGRADE_FOR_V2 = "SCALE_UPGRADE_FOR_V2";

    // serverless 支持的最小版本号
    public static final Map<String, String> MINOR_VERSION_SERVERLESS_SUPPORTED = new HashMap<String, String>() {{
        put("5.7", "mysql57_20220731");
        put("8.0", "mysql80_20220331");
    }};

    //支持平滑迁移功能的maxscale最低版本
    public static final String MAXSCALE_MINOR_VERVION = "maxscale_docker_image_cloud_disk_20230314";
    public static final String MAXSCALE_VERVION_FOR_SERVERLESS_STANDARD_MIGRATION = "maxscale_docker_image_cloud_disk_20230714";

    //资源调度模版annotationKey
    public static final String KEY_BIZ_TAGS = "rm.alibaba-inc.com/product-biz-tags";
    public static final String KEY_SERVERLESS_CU_LIMIT = "rm.alibaba-inc.com/serverless-cu-limit";

    // Call from scc
    public static final String  ACTION_TYPE = "SccAction";
    public static final String ACTION_TYPE_SCALE_DISK = "scale_disk";
    public static final List<String > ACTION_TYPE_LIST = new ArrayList<>(Arrays.asList(ACTION_TYPE_SCALE_DISK));
    public static final String RESOURCE_KEY_ALLOW_EXPEND_DISK_FROM_SCC = "ALLOW_EXPEND_DISK_FROM_SCC";

    public static final String CALL_FROM = "call_from";
    public enum CallFrom {
        OPENAPI("OPENAPI"),
        OP_API("OP_API"),
        SCC("SCC");

        private String value;

        private CallFrom(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public String toString() {
            return String.valueOf(this.value);
        }
    }
}
