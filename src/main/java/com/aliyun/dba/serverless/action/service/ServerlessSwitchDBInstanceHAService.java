package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.poddefault.action.service.SwitchMasterDBInstanceHAService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.aliyun.dba.base.support.MySQLParamConstants.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.property.ParamConstants.TARGET_INSTANCE_ID;
@Service
public class ServerlessSwitchDBInstanceHAService {
    private static final LogAgent logger = LogFactory.getLogAgent(ServerlessSwitchDBInstanceHAService.class);

    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected CustinsService custinsService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    private PodParameterHelper podParameterHelper;

    public Map<String, Object> serverlessSwitchDBInstanceHa(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {

    String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
        String targetInstanceId = paramSupport.getParameterValue(params, TARGET_INSTANCE_ID);
        String accessId = paramSupport.getParameterValue(params, ParamConstants.ACCESSID);
        Integer switchType = CustinsValidator.getRealNumber(
                paramSupport.getParameterValue(params, ParamConstants.SWITCH_TYPE), CustinsSupport.SWITCH_TYPE_NORMAL);
        if (!CustinsSupport.SWITCH_TYPE_SET.contains(switchType)) {
            return createErrorResponse(ErrorCode.INVALID_SWITCH_TYPE);

        }
        Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);

        // 状态和切换条件判断
        ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
        if (ReplicaSet.StatusEnum.ACTIVATION != replicaSet.getStatus()) {
            return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        Replica targetReplica;

        if (targetInstanceId != null) {
            targetReplica = getTargetReplica(replicaSet, targetInstanceId);
            if (targetReplica == null) {
                return createErrorResponse(ErrorCode.INVALID_INSTANCE_ID);
            }
            // 如果不是强切，并且没有打开HA，则不允许指定该节点切换
            if (CustinsSupport.SWITCH_TYPE_NORMAL.equals(switchType) && !Objects.isNull(targetReplica.getWeight()) && Objects.equals(targetReplica.getWeight(), 0)) {
                return createErrorResponse(ErrorCode.INVALID_WEIGHT);
            }
        } else {
            Replica.RoleEnum role =  Replica.RoleEnum.SLAVE;
            targetReplica = replicaSetService.getSpecificReplica(requestId, replicaSet.getName(), null, null, role);
        }

        if (targetReplica == null || targetReplica.getRole().equals(Replica.RoleEnum.MASTER)) {
            return createErrorResponse(ErrorCode.INVALID_SWITCH_TYPE);
        }
        JSONObject taskParam = new JSONObject();
        // Task parameter format: {"switch_flag":4, "switch_info":{"mode":"immediate"}, "target_replica_id": 111111}
        Integer switchFlag;
        if (PodParameterHelper.isAliGroup(replicaSet.getBizType())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        } else {
            switchFlag = CustinsSupport.SWITCH_TYPE_NORMAL.equals(switchType) ? AURORA_SWITCH_FLAG_API_NORMAL : AURORA_SWITCH_FLAG_API_FORCE;
        }
        taskParam.put("requestId", requestId);
        taskParam.put("switch_flag", switchFlag);
        taskParam.put("target_replica_id", targetReplica.getId());

        taskParam.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
        String domain = ReplicaSetService.isTDDL(replicaSet) ? PodDefaultConstants.DOMAIN_XDB : PodDefaultConstants.DOMAIN_MYSQL;
        String taskKey = PodDefaultConstants.TASK_HA_SWITCH_INS;

        if (ServerlessConstant.SERVERLESS_SCC_ACCESS.equalsIgnoreCase(accessId)) {
            taskKey = "serverless_standard_scale_up_to_slave";
        }

        if (replicaSetService.isTddlTaskMigrate(requestId, replicaSet)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
        int priority = PodDefaultConstants.TASK_HA_SWITCH_INS.equalsIgnoreCase(taskKey)? 1 : 0;
        Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), domain, taskKey, taskParam.toJSONString(), priority);
        // 更新实例状态为 HA 切换中
        metaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.HA_SWITCHING.toString());

        Map<String, Object> data = new HashMap<>();
        data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
        data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
        data.put(TARGET_INSTANCE_ID, targetReplica.getId());
        data.put(ParamConstants.SWITCH_TYPE, switchType);
        data.put(ParamConstants.TASK_ID, taskId);
        return data;
    } catch (RdsException re) {
        return createErrorResponse(re.getErrorCode());
    } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
        logger.error(requestId + " MetaApi error: ", e);
        throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
    } catch (Exception e) {
        logger.error(requestId + " Exception: ", e);
        return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
    }
}

    /**
     * 获取切换目标节点
     * */
    private Replica getTargetReplica(ReplicaSet replicaSet, String targetInstanceId) throws ApiException {
        Replica targetReplica;
        if (StringUtils.isNumeric(targetInstanceId)) {
            targetReplica = replicaSetService.getSpecificReplica(
                    RequestSession.getRequestId(), replicaSet.getName(), null, Long.valueOf(targetInstanceId), null);
        } else {
            targetReplica = replicaSetService.getSpecificReplica(
                    RequestSession.getRequestId(), replicaSet.getName(), targetInstanceId, null, null);
        }
        return targetReplica;
    }
}
