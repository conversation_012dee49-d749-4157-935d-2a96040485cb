<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Fri Jun 29 14:32:01 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.base.idao.PengineIDao">
  <resultMap id="pengineBlobInfo" type="com.aliyun.dba.base.idao.PengineBlobInfo">
    <result property="context" column="context"/>
    <result property="taskStatus" column="status"/>
  </resultMap>

  <select id="listPengineBlobInfoByTaskId" parameterType="java.lang.Long" resultMap="pengineBlobInfo">
    SELECT P.context, TQ.status FROM pengine P, task_queue TQ WHERE P.task_id = TQ.id AND P.task_id = #{taskId}
  </select>
</mapper>
