<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Fri Jun 29 14:32:08 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.base.idao.ApplyResourceIDao">
    
    <insert id="addApplyResource" parameterType="com.aliyun.dba.base.dataobject.ApplyResourceDO" useGeneratedKeys="true" keyProperty="id">
        insert into customer_resource_request(user_id, region_id, zone_id, engine, engine_version, category,
        storage_type, storage, instance_network_type, class_code, quantity, expected_time, progress)
        values (#{userId}, #{regionId}, #{zoneId}, #{engine}, #{engineVersion}, #{category}, #{storageType},
        #{storage}, #{instanceNetworkType}, #{classCode}, #{quantity}, #{expectedTime}, #{progress});
    </insert>
    
    <resultMap id="applyResourceResult" type="com.aliyun.dba.base.dataobject.ApplyResourceDO">
        <result column="id" property="id"></result>
        <result column="user_id" property="userId"></result>
        <result column="region_id" property="regionId"></result>
        <result column="zone_id" property="zoneId"></result>
        <result column="engine" property="engine"></result>
        <result column="engine_version" property="engineVersion"></result>
        <result column="category" property="category"></result>
        <result column="storage_type" property="storageType"></result>
        <result column="storage" property="storage"></result>
        <result column="instance_network_type" property="instanceNetworkType"></result>
        <result column="class_code" property="classCode"></result>
        <result column="quantity" property="quantity"></result>
        <result column="expected_time" property="expectedTime"></result>
        <result column="progress" property="progress"></result>
        <result column="description" property="description"></result>
        <result column="aone_id" property="aoneId"></result>
        <result column="aone_url" property="aoneUrl"></result>
        <result column="owner" property="owner"></result>
    </resultMap>
    
    <select id="queryApplyResourceByCondition" resultMap="applyResourceResult">
        select 
            *
        from
            customer_resource_request
        where
            user_id = #{userId}
            <if test="regionId != null">
                and region_id = #{regionId}
            </if>
            <if test="zoneId != null">
                and zone_id = #{zoneId}
            </if>
            <if test="engine != null">
                and engine = #{engine}
            </if>
            <if test="progress != null">
                and progress = #{progress}
            </if>
        order by id desc
        limit #{offset},#{limitCount}
    </select>

    <select id="queryApplyResourceRecordCount" resultType="java.lang.Integer">
        select
            count(*)
        from
            customer_resource_request
        where
            user_id = #{userId}
            <if test="regionId != null">
                and region_id = #{regionId}
            </if>
            <if test="zoneId != null">
                and zone_id = #{zoneId}
            </if>
            <if test="engine != null">
                and engine = #{engine}
            </if>
            <if test="progress != null">
                and progress = #{progress}
            </if>
    </select>

    <update id="cancelApplyResource">
        update
            customer_resource_request
        set
            progress = 'Cancelled', description=#{description}
        where
            id = #{applyResourceId}
            and user_id = #{userId}
    </update>

</mapper>