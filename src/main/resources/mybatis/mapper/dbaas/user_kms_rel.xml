<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Fri Jun 29 14:32:01 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.base.idao.UserKmsRelIDao">
    <resultMap id="userKmsRel" type="com.aliyun.dba.base.idao.UserKmsRelDO">
        <result property="id" column="id"/>
        <result property="gmtCreated" column="gmt_created"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
        <result property="userId" column="user_id"/>
        <result property="kmsId" column="kms_id"/>
        <result property="masterKey" column="master_key"/>
        <result property="comment" column="comment"/>
    </resultMap>

    <sql id="userKmsRelFields">
        id,
        gmt_created,
        gmt_modified,
        creator,
        modifier,
        user_id,
        kms_id,
        master_key,
        comment
    </sql>

    <select id="getUserKmsRelByKmsIdAndUserId" parameterType="map" resultMap="userKmsRel">
        select
        <include refid="userKmsRelFields"/>
        from user_kms_rel
        where kms_id = #{kmsId}
        and user_id = #{userId}
        limit 1;
    </select>

    <insert id="addUserKmsRel" parameterType="com.aliyun.dba.base.idao.UserKmsRelDO">
        insert into user_kms_rel (<include refid="userKmsRelFields"/>)
        values (null, now(), now(), 777, 777, #{userId}, #{kmsId}, #{masterKey}, null)
    </insert>
</mapper>