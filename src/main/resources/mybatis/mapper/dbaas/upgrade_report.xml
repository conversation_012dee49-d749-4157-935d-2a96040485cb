<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Fri Jun 29 14:32:01 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.base.idao.UpgradeReportIDao">
    <resultMap id="upgradeReportInfo" type="com.aliyun.dba.base.idao.UpgradeReportDO">
        <result property="id" column="id"/>
        <result property="gmtCreated" column="gmt_created"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="taskId" column="task_id"/>
        <result property="srcInsName" column="src_ins_name"/>
        <result property="srcVersion" column="src_version"/>
        <result property="dstInsName" column="dst_ins_name"/>
        <result property="dstVersion" column="dst_version"/>
        <result property="type" column="type"/>
        <result property="upgradeMode" column="upgrade_mode"/>
        <result property="collectStatMode" column="collect_stat_mode"/>
        <result property="result" column="result"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="checkTime" column="check_time"/>
        <result property="switchTime" column="switch_time"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="detail" column="detail"/>
    </resultMap>

    <sql id="upgradeReportFields">
        id,
        gmt_created,
        gmt_modified,
        task_id,
        src_ins_name,
        src_version,
        dst_ins_name,
        dst_version,
        type,
        upgrade_mode,
        collect_stat_mode,
        result,
        start_time,
        end_time,
        check_time,
        switch_time,
        effective_time,
        detail
    </sql>

    <select id="getUpgradeReportByCondition" parameterType="map" resultMap="upgradeReportInfo">
        select
        <include refid="upgradeReportFields"/>
        from
        upgrade_report
        where
        src_ins_name = #{srcInsName}
        <if test="taskId!=null">
            and task_id = #{taskId}
        </if>
        <if test="dstVersion!=null">
            and dst_version = #{dstVersion}
        </if>
        and type = #{type}
        order by id desc
        limit #{pageFirst}, #{pageSize};
    </select>

    <select id="countUpgradeReportByCondition" parameterType="map" resultType="int" >
        select
        count(id)
        from
        upgrade_report
        where
        src_ins_name = #{srcInsName}
        <if test="taskId!=null">
            and task_id = #{taskId}
        </if>
        <if test="dstVersion!=null">
            and dst_version = #{dstVersion}
        </if>
        and type = #{type}
    </select>
</mapper>
