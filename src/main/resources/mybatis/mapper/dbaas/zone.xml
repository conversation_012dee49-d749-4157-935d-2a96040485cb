<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.base.idao.ZoneIDao">

  <resultMap id="zoneDOResult" type="com.aliyun.dba.base.dataobject.ZoneDO">
    <result property="id" column="id"/>
    <result property="zoneId" column="zone_id"/>
    <result property="regionId" column="region_id"/>
    <result property="siteName" column="site_name"/>
    <result property="vgwIp" column="vgw_ip"/>
    <result property="gmtCreated" column="gmt_created"/>
    <result property="gmtModified" column="gmt_modified"/>
  </resultMap>

  <select id="getZoneIdBySiteAndRegion" parameterType="map" resultType="String">
    SELECT
      zone_id
    FROM
      zone
    WHERE
     region_id = #{regionId} and site_name = #{siteName}
    LIMIT
    1
  </select>

</mapper>
