<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Fri Jun 29 14:32:01 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.onecs.idao.MysqlOnEcsDBCenterServiceIDao">

  <resultMap id="ecsSecurityGroupDOResult" type="com.aliyun.dba.ecs.dataobject.EcsSecurityGroupDO" >
    <result property="id" column="id"/>
    <result property="securityGroupId" column="security_group_id"/>
    <result property="securityGroupName" column="security_group_name"/>
    <result property="userName" column="user_name"/>
    <result property="vpcId" column="vpc_id"/>
    <result property="regionId" column="region_id"/>
    <result property="gmtCreated" column="gmt_created"/>
    <result property="gmtModified" column="gmt_modified"/>
    <result property="creator" column="creator"/>
    <result property="modifier" column="modifier"/>
    <result property="comment" column="comment"/>
    <result property="status" column="status"/>
  </resultMap>


  <sql id="ecsSecurityGroupDOFields">
        id,
        security_group_id,
        security_group_name,
        user_name,
        vpc_id,
        region_id,
        gmt_created,
        gmt_modified,
        creator,
        modifier,
        comment,
        status
  </sql>

  <sql id="whereEcsSecurityGroupDO">
    <where>
      <if test="obj.id!=null">
        and id=#{obj.id}
      </if>
      <if test="obj.securityGroupId!=null">
        and security_group_id=#{obj.securityGroupId}
      </if>
      <if test="obj.securityGroupName!=null">
        and security_group_name=#{obj.securityGroupName}
      </if>
      <if test="obj.userName!=null">
        and user_name=#{obj.userName}
      </if>
      <if test="obj.vpcId!=null">
        and vpc_id=#{obj.vpcId}
      </if>
      <if test="obj.regionId!=null">
        and region_id=#{obj.regionId}
      </if>
      <if test="obj.creator!=null">
        and creator=#{obj.creator}
      </if>
      <if test="obj.modifier!=null">
        and modifier=#{obj.modifier}
      </if>
      <if test="obj.comment!=null">
        and comment=#{obj.comment}
      </if>
      <if test="obj.status!=null">
        and status=#{obj.status}
      </if>
    </where>
  </sql>

  <select id="getEcsSecurityGroupDOList" resultMap="ecsSecurityGroupDOResult" parameterType="java.util.Map">
    select
      <include refid="ecsSecurityGroupDOFields"/>
    from ecs_security_group
      <include refid="whereEcsSecurityGroupDO"/>
    order by id desc
    limit #{pageStart}, #{pageSize}
  </select>

</mapper>
