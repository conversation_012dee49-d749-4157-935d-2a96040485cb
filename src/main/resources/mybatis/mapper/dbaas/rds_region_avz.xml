<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.onecs.idao.MysqlOnEcsRegionServiceIDao">

  <sql id="rdsRegionAvzDOFields">
        id,
        region,
        region_name,
        avz,
        avz_name,
        sub_domain,
        biz_type,
        region_category
    </sql>

  <resultMap id="rdsRegionAvzDOResult" type="com.aliyun.dba.onecs.dataobject.RdsRegionAvzDO">
    <result property="id" column="id"/>
    <result property="region" column="region"/>
    <result property="regionName" column="region_name"/>
    <result property="avz" column="avz"/>
    <result property="avzName" column="avz_name"/>
    <result property="subDomain" column="sub_domain"/>
    <result property="bizType" column="biz_type"/>
    <result property="regionCategory" column="region_category"/>
  </resultMap>

  <select id="getRdsRegionAvzDOByLocation" parameterType="java.lang.String" resultMap="rdsRegionAvzDOResult">
    SELECT
      <include refid="rdsRegionAvzDOFields"/>
    FROM
      rds_region_avzone
    WHERE
    sub_domain = #{subDomain}
    LIMIT
    1
  </select>

</mapper>
