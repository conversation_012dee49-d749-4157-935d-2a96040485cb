<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Fri Jun 29 14:32:01 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.onecs.idao.MysqlOnEcsDBCenterServiceIDao">

  <resultMap id="ecsImageDOResult" type="com.aliyun.dba.ecs.dataobject.EcsImageDO" >
    <result property="id" column="id"/>
    <result property="imageId" column="image_id"/>
    <result property="imageName" column="image_name"/>
    <result property="userName" column="user_name"/>
    <result property="regionId" column="region_id"/>
    <result property="dbType" column="db_type"/>
    <result property="gmtCreated" column="gmt_created"/>
    <result property="gmtModified" column="gmt_modified"/>
    <result property="creator" column="creator"/>
    <result property="modifier" column="modifier"/>
    <result property="comment" column="comment"/>
    <result property="status" column="status"/>
    <result property="version" column="version"/>
    <result property="vpcId" column="vpc_id"/>
  </resultMap>

  <sql id="ecsImageDOFields">
        id,
        image_id,
        image_name,
        user_name,
        region_id,
        db_type,
        gmt_created,
        gmt_modified,
        creator,
        modifier,
        comment,
        status,
        version,
        vpc_id
  </sql>

  <sql id="whereEcsImageDO">
    <where>
      <if test="obj.id!=null">
        and id=#{obj.id}
      </if>
      <if test="obj.imageId!=null">
        and image_id=#{obj.imageId}
      </if>
      <if test="obj.imageName!=null">
        and image_name=#{obj.imageName}
      </if>
      <if test="obj.userName!=null">
        and user_name=#{obj.userName}
      </if>
      <if test="obj.regionId!=null">
        and region_id=#{obj.regionId}
      </if>
      <if test="obj.dbType!=null">
        and db_type=#{obj.dbType}
      </if>
      <if test="obj.creator!=null">
        and creator=#{obj.creator}
      </if>
      <if test="obj.modifier!=null">
        and modifier=#{obj.modifier}
      </if>
      <if test="obj.comment!=null">
        and comment=#{obj.comment}
      </if>
      <if test="obj.status!=null">
        and status=#{obj.status}
      </if>
      <if test="obj.version!=null">
        and version=#{obj.version}
      </if>
      <if test="obj.vpcId!=null">
        and vpc_id=#{obj.vpcId}
      </if>
    </where>
  </sql>

  <select id="getEcsImageDOList" resultMap="ecsImageDOResult" parameterType="java.util.Map">
    select
      <include refid="ecsImageDOFields"/>
    from ecs_image
      <include refid="whereEcsImageDO"/>
    order by id desc
    limit #{pageStart}, #{pageSize}
  </select>

</mapper>
