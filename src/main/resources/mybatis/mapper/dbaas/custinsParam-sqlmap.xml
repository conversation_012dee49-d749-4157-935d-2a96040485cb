<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Fri Jun 29 14:32:08 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="custinsParam">

    <select id="getInsNameByCondition" parameterType="map" resultType="String">
        SELECT
        ci.ins_name
        FROM
        custins_param cp
        left join cust_instance ci
        on cp.custins_id = ci.id
        WHERE
        value = #{paramValue}
        AND name = #{paramName}
        AND ci.is_deleted=0
        AND ci.db_type=#{dbType}
    </select>
    
    <select id="getInsParamGroupByCondition" parameterType="map" resultType="map" >
        SELECT
            A.id,
            A.user_id,
            A.name AS ParameterGroupName,
            A.comment AS ParameterGroupDesc,
            A.db_type,
            A.param_group_id AS ParamGroupId,
            A.db_version,
            A.type AS ParameterGroupType,
            A.gmt_created,
            A.gmt_modified
        FROM
            param_groups A
        WHERE param_group_id=#{paramGroupId}
          and is_deleted=0
          and db_type=#{dbType}
          and db_version = #{dbVersion}
    </select>
</mapper>