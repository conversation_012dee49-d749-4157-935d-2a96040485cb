<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.commonkindcode.idao.ParamGroupTemplateIDao">
    <resultMap id="mycnfTemplateExtraEntityResult" type="mycnfTemplateExtraEntity">
        <result property="tempId" column="id"/>
        <result property="dbType" column="db_type"/>
        <result property="dbVersion" column="db_version"/>
        <result property="category" column="category"/>
        <result property="name" column="name"/>
        <result property="defaultValue" column="default_value"/>
        <result property="isDynamic" column="is_dynamic"/>
        <result property="isVisible" column="is_visible"/>
        <result property="isUserChangable" column="is_user_changable"/>
        <result property="optional" column="optional"/>
        <result property="unit" column="unit"/>
        <result property="divideBase" column="divide_base"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="characterType" column="character_type"/>
        <result property="minorVersion" column="minor_version"/>
    </resultMap>

    <select id="getParameterExtraWithParamGroupId" parameterType="map" resultMap="mycnfTemplateExtraEntityResult">
        SELECT *
        FROM mycnf_template_extra
        WHERE is_deleted = 0
        AND param_group_id = #{paramGroupId}
        AND param_group_id is not null
    </select>

</mapper>




