<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Fri Jun 29 14:32:01 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.dba.onecs.idao.MysqlOnEcsRegionServiceIDao">

  <resultMap id="clusterResult" type="com.aliyun.dba.onecs.dataobject.ClusterDO">
    <result property="id" column="id"/>
    <result property="clustername" column="clustername"/>
    <result property="location" column="location"/>
    <result property="dbType" column="db_type"/>
    <result property="syncMode" column="sync_mode"/>
    <result property="resourceMode" column="resource_mode"/>
    <result property="adminIps" column="admin_ips"/>
    <result property="siteName" column="site_name"/>

  </resultMap>

  <resultMap id="clusterDetailResult" type="com.aliyun.dba.onecs.dataobject.ClusterDO">
    <result property="id" column="id"/>
    <result property="clustername" column="clustername"/>
    <result property="location" column="location"/>
    <result property="dbType" column="db_type"/>
    <result property="syncMode" column="sync_mode"/>
    <result property="resourceMode" column="resource_mode"/>
    <result property="adminIps" column="admin_ips"/>
    <result property="siteName" column="site_name"/>
    <result property="isMultiSite" column="is_multi_site"/>
    <result property="connType" column="conn_type"/>

  </resultMap>

  <select id="getClusterByName" parameterType="java.lang.String" resultMap="clusterDetailResult">
    SELECT
        *
    FROM
    clusters
    WHERE
    clustername = #{clusterName}
    LIMIT
    1
  </select>

</mapper>
