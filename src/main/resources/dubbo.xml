<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

  <dubbo:application name="rdsapi-ext-mysql" organization="aliyun-biz-tech"/>

  <!-- 使用multicast广播注册中心暴露服务地址 -->
  <dubbo:registry address="${dubbo.registry.address:***********:9090}"/>

  <!-- 用dubbo协议在20990端口暴露服务 -->
  <dubbo:protocol name="dubbo" port="${dubbo.protocol.port:20990}" threads="800" threadpool="cached"/>
  <!--provider service, group must be given in dubbo service, else default is null, and not use group in rpc call-->
  <dubbo:service id="mysqlPhysical" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="mysqlDBEngineExtAdapter"
                 version="1.0-${envId:0}-mysql-physical" timeout="200000"/>

  <dubbo:service id="mysqlOnEcs" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="mysqlOnEcsDBEngineExtAdapter"
                 version="1.0-${envId:0}-mysql-onecs" retries="0" timeout="200000"/>

  <dubbo:service id="mysqlDockerDefault" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="mysqlDockerdefaultDBEngineExtAdapter"
                 version="1.0-${envId:0}-mysql-dockerdefault" retries="0" timeout="200000"/>

  <dubbo:service id="mysqlCommonKindcode" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="mysqlCommonKindcodeDBEngineExtAdapter"
                 version="1.0-${envId:0}-mysql-commonkindcode" retries="0" timeout="200000"/>

  <dubbo:service id="mysqlPodDefault" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="mysqlPodDefaultDBEgngineExtAdapter"
                 version="1.0-${envId:0}-mysql-poddefault" retries="0" timeout="200000"/>

  <dubbo:service id="mariadbDockerDefault" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="mariadbDockerDefaultDBEngineExtAdapter"
                 version="1.0-${envId:0}-mariadb-dockerdefault" retries="0" timeout="200000"/>

  <dubbo:service id="mariadbCommonKindcode" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="mariadbCommonKindcodeDBEngineExtAdapter"
                 version="1.0-${envId:0}-mariadb-commonkindcode" retries="0" timeout="200000"/>

  <dubbo:service id="mariadbPodKindcode" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="mariadbPodDefaultDBEngineExtAdapter"
                 version="1.0-${envId:0}-mariadb-poddefault" retries="0" timeout="200000"/>

<!--  <dubbo:service id="rdscustomPodDefault" interface="com.aliyun.dba.adapter.DBEngineExtAdapter" ref="rdscustomPodDefaultDBEngineExtAdapter"-->
<!--                 version="1.0-${envId:0}-rdscustom-poddefault" retries="0" timeout="200000"/>-->
</beans>
