#创建实例参数说明文档
serviceCode: rdsapi-ext-mysql
name: CreateDBInstance
params:
#基本参数
  -
    name: action
    dataType: String
    necessary: true
    valueOption: [CreateDBInstance]
    valueExample: CreateDBInstance
    paramMark: 创建只读实例接口名称
    supportVersions: '*'
  -
    name: UID
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: 1768383226435801
    paramMark: 阿里云UID账号
    supportVersions: '*'
  -
    name: USER_ID
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: 26842
    paramMark: 阿里云虚商账号,公有云中国站为26842
    supportVersions: '*'
  -
    name: AccessId
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: YaoChi
    paramMark: 调用方ID,例如YaoChi，Pengine等
    supportVersions: '*'
  -
    name: Signature
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: URhNTh2m5phqbUWrSKCknHDE+/4=
    paramMark: 调用方签名，用于身份识别
    supportVersions: '*'
  -
    name: TimeStamp
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: 2020-01-01T01:01:01Z
    paramMark: 调用时间戳
    supportVersions: '*'
  -
    name: requestId
    dataType: String
    necessary: false
    valueOption: '*'
    valueExample: AD92BF30-2827-43A4-AD6D-8A330344F6DA
    paramMark: 请求唯一标志，如果不传递，则根据UUID随机生成
    supportVersions: '*'
#地域与可用区模式参数
  -
    name: Region
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: cn-hangzhou-i-aliyun
    paramMark: 子域
    supportVersions: '*'
  -
    name: RegionId
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: cn-hangzhou
    paramMark: 单元
    supportVersions: '*'
  -
    name: zoneId
    dataType: String
    necessary: false
    valueOption: '*'
    valueExample: cn-hangzhou-i
    paramMark: 可用区ID
    supportVersions: '*'
  -
    name: DispenseMode
    dataType: Integer
    necessary: true
    valueOption: [0|1]
    valueExample: 0
    paramMark: 是否为主可用区模式，0：非主可用区模式，1：主可用区模式
    supportVersions: '*'
  -
    name: MultiAVZExParam
    dataType: JSON
    necessary: true
    valueOption: '*'
    valueExample: '{"availableZoneInfoList":[{"isUserSpecified":true,"role":"master","vSwitchID":"vsw-bp1en76fw6wcajeafjjx9","zoneID":"cn-hangzhou-i"},{"isUserSpecified":true,"role":"slave","vSwitchID":"vsw-bp1en76fw6wcajeafjjx9","zoneID":"cn-hangzhou-i"}]}'
    paramMark: 指定多可用区与主可用区参数，用于选择主备节点可用区
    supportVersions: '*'
#业务引擎参数
  -
    name: Engine
    dataType: String
    necessary: true
    valueOption: [MySQL|MariaDB]
    valueExample: MySQL
    paramMark: 引擎类别
    supportVersions: '*'
  -
    name: EngineVersion
    dataType: String
    necessary: true
    valueOption: [5.5|5.6|5.7|8.0|10.3]
    valueExample: 5.6
    paramMark: 引擎版本号
    supportVersions: '*'
  -
    name: ResourceGroupId
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: rg-acfm4l34aaqfd4y
    paramMark: 资源组
    supportVersions: '*'
  -
    name: DBInstanceName
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: rm-bp11f14q55eo49w3n
    paramMark: 实例名称，需要符合实例名称规则
    supportVersions: '*'
  -
    name: ServiceType
    dataType: Integer
    necessary: true
    valueOption: [0|1]
    valueExample: 0
    paramMark: 服务类型，0：普通实例，1：聚石塔实例
    supportVersions: '*'
#网络信息参数
  -
    name: DBInstanceNetType
    dataType: Integer
    necessary: true
    valueOption: [0|1|2]
    valueExample: 2
    paramMark: 0:公网网络，1:私网网络，2:专有网络
    supportVersions: '*'
  -
    name: VPCId
    dataType: String
    necessary: false
    valueOption: '*'
    valueExample: vpc-bp1r4ewp75evk3qcrmjcu
    paramMark: 专有网络ID
    supportVersions: '*'
  -
    name: VSwitchId
    dataType: String
    necessary: false
    valueOption: '*'
    valueExample: vsw-bp1en76fw6wcajeafjjx9
    paramMark: 专有网络下交换机ID
    supportVersions: '*'
  -
    name: IsUserVpc
    dataType: boolean
    necessary: false
    valueOption: '*'
    valueExample: false
    paramMark: 是否是用户VPC
    supportVersions: '*'
  -
    name: TunnelId
    dataType: String
    necessary: false
    valueOption: '*'
    valueExample: 994119
    paramMark: 选择Tunnel通道ID
    supportVersions: '*'
  -
    name: SecurityIPList
    dataType: String
    necessary: false
    valueOption: '*'
    valueExample: 127.0.0.1
    paramMark: IP白名单
    supportVersions: '*'
  -
    name: IPAddress
    dataType: String
    necessary: false
    valueOption: '*'
    valueExample: ************
    paramMark: 专有网络IP
    supportVersions: '*'
  -
    name: ConnectionString
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: rm-bp11f14q55eo49w3n
    paramMark: 实例链接地址，创建时指定，用于生成连接串使用
    supportVersions: '*'
  -
    name: Port
    dataType: Integer
    necessary: true
    valueOption: [3000-30000]
    valueExample: 3001
    paramMark: 连接串MySQL协议端口
    supportVersions: '*'
# 规格与存储参数
  -
    name: DBInstanceType
    dataType: String
    necessary: true
    valueOption: [s|x]
    valueExample: x
    paramMark: 资源使用模式，s：共享实例，x：独享实例
    supportVersions: '*'
  -
    name: HostType
    dataType: Integer
    necessary: false
    valueOption: '*'
    valueExample: 2
    paramMark: 主机类型，ECS为2，物理机为0
    supportVersions: '*'
  -
    name: DBInstanceClass
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: mysql.n1.micro.1
    paramMark: 选择的目标规格
    supportVersions: '*'
  -
    name: DBInstanceStorageType
    dataType: String
    necessary: true
    valueOption: [local_ssd|cloud_ssd|cloud_essd0|cloud_essd|cloud_essd2|cloud_essd3|cloud_auto]
    valueExample: local_ssd
    paramMark: 存储类型
    supportVersions: '*'
  -
    name: Storage
    dataType: Integer
    necessary: true
    valueOption: [1-32000000]
    valueExample: 20
    paramMark: 云盘最大支持32T，本地盘根据规格限定选择大小
    supportVersions: '*'
#备份设置参数
  -
    name: BackupRetentionPeriod
    dataType: Integer
    necessary: true
    valueOption: [7-30]
    valueExample: 7
    paramMark: 备份保留时间，单位：天
    supportVersions: '*'
  -
    name: PreferredBackupPeriod
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: 0101010
    paramMark: 备份周期，周一到周日，为1则备份
    supportVersions: '*'
  -
    name: PreferredBackupTime
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: 19:12Z
    paramMark: 当天备份开始时间，可根据业务峰谷选择
    supportVersions: '*'
#参数时区设置
  -
    name: DBParamGroupID
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: rpg-sys-01030401010200
    paramMark: 系统参数模板ID，指定实例应用的参数模板
    supportVersions: '*'
  -
    name: CharacterSetName
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: utf8
    paramMark: 指定字符集
    supportVersions: '*'
  -
    name: DBTimeZone
    dataType: String
    necessary: true
    valueOption: '*'
    valueExample: +8:00
    paramMark: 指定时区信息
    supportVersions: '*'
  -
    name: DBIsIgnoreCase
    dataType: boolean
    necessary: true
    valueOption: [true|false]
    valueExample: true
    paramMark: 指定库表不区分大小写
    supportVersions: '*'
#账号设置
  -
    name: AccountType
    dataType: Integer
    necessary: true
    valueOption: [0|1|2|3|4|5]
    valueExample: 0
    paramMark: 账户模式，0,1,2:老账户模式；3,4,5:新账户模式；0,3:不支持高权限账户,1,4:支持高权限账户但是没有高权限账户,2,5:有高权限账户
    supportVersions: '*'
#版本设置
  -
    name: AutoUpgradeMinorVersion
    dataType: boolean
    necessary: false
    valueOption: [true|false]
    valueExample: true
    paramMark: 是否自动小版本升级
    supportVersions: '*'
#其他参数
  -
    name: OrderId
    dataType: String
    necessary: false
    valueOption: '*'
    valueExample: ***************
    paramMark: 订单编号
    supportVersions: '*'
  -
    name: OptmizationService
    dataType: Integer
    necessary: true
    valueOption: [1|2]
    valueExample: 1
    paramMark: 1:老架构服务版本;2:新架构路由标志，设置kind_code=18
    supportVersions: '*'
errorCodes:
  -
    httpStatusCode: 403
    errorCode: IncorrectMasterDBInstanceState
    errorMessage: The operation is not permitted due to status of primary instance.
    suggestion: Please check primary instance state, it already has change task, you may retry after task finished.
  -
    httpStatusCode: 400
    errorCode: InvalidEngineVersion.Malformed
    errorMessage: The specified parameter "EngineVersion" is not valid.
    suggestion: Please check parameter 'EngineVersion'. It is missing or ilegal. Generally it is same with primary instance.
version: 0.0.1
docLink: Automatic generated