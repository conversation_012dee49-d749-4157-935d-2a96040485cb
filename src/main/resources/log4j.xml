<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "http://toolkit.alibaba-inc.com/dtd/log4j/log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

    <!-- Console Appender -->
    <appender name="stdout" class="org.apache.log4j.ConsoleAppender">
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-ddTHH:mm:ss.SSS}\t%p\t%F:%L\t%X{trace_id}\t%X{span_id}\t- traceId:%X{traceId} spanId:%X{spanId} requestId:%X{RequestId} %m%n" />
        </layout>
    </appender>

    <!-- File Appender -->
    <appender name="file" class="org.apache.log4j.RollingFileAppender">
        <param name="File" value="/usr/local/rds/rdsapi-ext-mysql/log/rdsapi-ext-mysql.log" />
        <param name="Append" value="true" />
        <param name="MaxBackupIndex" value="7" />
        <param name="MaxFileSize" value="1GB" />
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-ddTHH:mm:ss.SSS}\t%p\t%F:%L\t%X{trace_id}\t%X{span_id}\t- traceId:%X{traceId} spanId:%X{spanId} requestId:%X{RequestId} %m%n" />
        </layout>
    </appender>

    <!-- LockFreeAsyncAppender -->
    <appender name="LockFreeAsyncAppender" class="com.aliyun.dba.log4jutils.LockFreeAsyncAppender">
        <param name="MaxLength" value="20480" />
        <param name="BufferSize" value="1024" />
        <param name="LocationInfo" value="false" />
        <param name="MinLevelLocationInfo" value="6" />
        <appender-ref ref="file" />
    </appender>

    <!-- Root Logger -->
    <root>
        <priority value="INFO" />
        <appender-ref ref="LockFreeAsyncAppender" />
        <!-- Uncomment to enable stdout logging -->
        <!-- <appender-ref ref="stdout" /> -->
    </root>

</log4j:configuration>
