---
"$schema": http://json-schema.org/draft-04/schema#
additionalProperties: true
properties:
#  apiName:
#    description: An explanation about the purpose of this instance.
#    title: The Apiname schema.
#    type: ['string']
#  appKey:
#    description: An explanation about the purpose of this instance.
#    title: The Appkey schema.
#    type: ['string']
#  callerBid:
#    description: An explanation about the purpose of this instance.
#    title: The Callerbid schema.
#    type: ['string']
#  callerBidLoginEmail:
#    description: An explanation about the purpose of this instance.
#    title: The Callerbidloginemail schema.
#    type: ['null','string']
#  callerType:
#    description: An explanation about the purpose of this instance.
#    title: The Callertype schema.
#    type: ['string']
#  callerUid:
#    description: An explanation about the purpose of this instance.
#    title: The Calleruid schema.
#    type: ['integer']
#  callerUidLoginEmail:
#    description: An explanation about the purpose of this instance.
#    title: The Calleruidloginemail schema.
#    type: ['null','string']
#  clientIP:
#    description: An explanation about the purpose of this instance.
#    title: The Clientip schema.
#    type: ['null','string']
#  enable:
#    description: An explanation about the purpose of this instance.
#    title: The Enable schema.
#    type: ['null','boolean']
#  mfaPresent:
#    description: An explanation about the purpose of this instance.
#    title: The Mfapresent schema.
#    type: ['null','boolean']
#  ownerAccount:
#    description: An explanation about the purpose of this instance.
#    title: The Owneraccount schema.
#    type: ['null','string']
#  ownerId:
#    description: An explanation about the purpose of this instance.
#    title: The Ownerid schema.
#    type: ['null', 'integer']
#  ownerIdLoginEmail:
#    description: An explanation about the purpose of this instance.
#    title: The Owneridloginemail schema.
#    type: ['null','string']
#  product:
#    description: An explanation about the purpose of this instance.
#    title: The Product schema.
#    type: ['string']
#  regionId:
#    description: An explanation about the purpose of this instance.
#    title: The Regionid schema.
#    type: ['string']
#  requestContent:
#    description: An explanation about the purpose of this instance.
#    title: The Requestcontent schema.
#    type: ['null','string']
#  requestId:
#    description: An explanation about the purpose of this instance.
#    title: The Requestid schema.
#    type: ['string']
#  resourceOwnerAccount:
#    description: An explanation about the purpose of this instance.
#    title: The Resourceowneraccount schema.
#    type: ['null','string']
#  resourceOwnerId:
#    description: An explanation about the purpose of this instance.
#    title: The Resourceownerid schema.
#    type: ['null','integer']
#  sslCallerIp:
#    description: An explanation about the purpose of this instance.
#    title: The Sslcallerip schema.
#    type: ['null','string']
#  sslCallerSecurityTransport:
#    description: An explanation about the purpose of this instance.
#    title: The Sslcallersecuritytransport schema.
#    type: ['null','boolean']
#  sslProxyCallerIp:
#    description: An explanation about the purpose of this instance.
#    title: The Sslproxycallerip schema.
#    type: ['null','string']
#  sslProxyCallerSecurityTransport:
#    description: An explanation about the purpose of this instance.
#    title: The Sslproxycallersecuritytransport schema.
#    type: ['null','boolean']
#  sslProxyTrustTransportInfo:
#    description: An explanation about the purpose of this instance.
#    title: The Sslproxytrusttransportinfo schema.
#    type: ['null','boolean']
#  stsAccessKeyId:
#    description: An explanation about the purpose of this instance.
#    title: The Stsaccesskeyid schema.
#    type: ['null','string']
#  stsSecurityToken:
#    description: An explanation about the purpose of this instance.
#    title: The Stssecuritytoken schema.
#    type: ['null','string']
#  subCallerParentId:
#    description: An explanation about the purpose of this instance.
#    title: The Subcallerparentid schema.
#    type: ['null','integer']
required:
#- regionId
#- apiName
#- requestId
#- appKey
#- product
#- callerBid
#- callerUid
#- callerType
type: object