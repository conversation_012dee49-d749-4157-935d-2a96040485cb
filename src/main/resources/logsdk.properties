#æå¼æ¥å¿éç½®
logsdk.switch=ON
#ç»ä»¶åç§°
logsdk.componentId=RDSAPI-EXT-MYSQL
#åéç¬¦æ¨¡å¼æ¥å¿
logsdk.format=SEPARATOR
#è®°å½çæ¥å¿çº§å«,ææ¶æ¯æInfoï¼WARNï¼ERRORä¸ç§çº§å«
logsdk.logsdkLevel=INFO
#åæ°æ¥å¿è§£æç±»å
logsdk.parseClasses=com.aliyun.dba.custins.dataobject.CustInstanceDO
#ç´¢å¼å­æ®µ
logsdk.indexKeys=Action,AccessId,RequestId,DBInstanceID,DBInstanceName,Uid,User_id,Engine,EngineVersion
#æ¯å¦è®°å½è¿åç»æçè¡¨è¾¾å¼ï¼All || Key1=value1&Key2=value2...ï¼
logsdk.returnDataExpression=All
#è¿åç»æçæå¤§é¿åº¦ï¼é»è®¤300ï¼è¶è¿åä¸è®°å½
logsdk.maxReturnLogLength=4096
#å¦ææå¤ä¸ªæ­£ç¡®ç ï¼è¿åç¬¬ä¸ä¸ª
logsdk.successCode=200


#éè¦å­èç ææ¡©çHttpClientç±»,éç½®éè¦ä¿®æ¹çç±»å¨è·¯å¾ï¼åçä¿®æ¹çæ¹æ³ï¼ä¿®æ¹çHttpClientçå¼ç¨ç±»åï¼HttpClientçå±æ§åç§°ï¼ä¿®æ¹æ¹æ³çåæ°ç±»ååè¡¨(,åå²);å¤ä¸ªHttpClientï¼ç¨;åå²
#logsdk.httpClientClasses=org.apache.http.impl.client.InternalHttpClient@<EMAIL>@<EMAIL>,org.apache.http.HttpRequest,org.apache.http.protocol.HttpContext