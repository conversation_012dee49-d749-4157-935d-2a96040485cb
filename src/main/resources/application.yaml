spring:
  autoconfigure.exclude: com.aliyun.boot.dubbo.DubboAutoConfiguration
  #  profiles: default
  application:
    name: rdsapi-ext-mysql
  datasource:
    #1802
    url: ***********************************************************************************************************
    # 4102
#    url: ***********************************************************************************************************************
    # 3862
#    url: **********************************************************************************************************************
    #    url: ************************************************************************************************************************
    username: dbaas
    password: passwd4metadssvdsxcdsdfl
    driver-class-name: com.mysql.jdbc.Driver
    druid:
      name: dbaas_master
      initial-size: 5
      min-idle: 5
      max-active: 100
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      max-wait: 6000
      time-between-eviction-runs-millis: 30000

  cache:
    type: caffeine
    caffeine:
      spec: initialCapacity=50,maximumSize=200,expireAfterWrite=60m
rds:
  mybatis_plugins: true

  name-service:
    # 1802
    base-url: http://name-service-atp-onecs-1802.atpecsgw.rds.aliyuncs.com:80
#    base-url: http://name-service-atp-3862.adbgw.alibabacloud.test
#    base-url: http://name-service-atp-4102.igw.rdstest.tbsite.net

  output:
    ansi:
      enabled: DETECT

  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterAccess=1m

  h2:
    console:
      enabled: true
      path: /h2-console

opentracing:
  jaeger:
    service-name: RDSAPI_EXT_MYSQL
    enable-slf4j-mdc: true

server:
  port: 9010

debug: true

logging:
  level:
    org:
      springframework:
        web: DEBUG
      hibernate:
        SQL: DEBUG
        type:
          descriptor:
            sql:
              BasicBinder: TRACE
    com:
      aliyun: DEBUG

myapp:
  api-path:
    base: /api/rdsapi
    web: ${myapp.api-path.base}/mysql
    flux: ${myapp.api-path.web}-stream
  api-gateway:
    path-patterns:
      - "${myapp.api-path.web}/**"
      - "${myapp.api-path.flux}/**"

management:
  endpoints:
    web:
      exposure:
        # 将 Actuator 的 /actuator/prometheus 端点暴露出来
        include: prometheus
      # 把 actuator 根 path 改为 /
      base-path: /
      # 将 prometheus 指标地址映射到 metrics
      path-mapping:
        prometheus: metrics
        health: api/v1/actuator/health
  metrics:
    tags:
      application: RDSAPI_EXT_MYSQL


dubbo:
  metrics:
    enable-collector-sync: true
    use-global-registry: true
  registry:
    simplified: true

---
# server/system/spring configs section
spring:
  profiles: prod
  main:
    banner-mode: console #off
  h2.console.enabled: false

debug: true

# http://docs.spring.io/spring-boot/docs/current/reference/html/howto-logging.html
logging:
  level:
    org:
      springframework:
        web: DEBUG
      hibernate: DEBUG
    com:
      aliyun: DEBUG



# application configs section

---
spring:
  profiles: dev

# application configs section

---
spring:
  profiles: qa

server:
  port: 9011

---
spring:
  profiles: filelogging

logging:
  file: myapp.log