CASE_DISABLED|CASE_DESCRIBE|EXPECT_RC|EXPECT_RESULT|charactersetname|protocol|hbasecoreecsspecine|coreinstancetype|backupsetid|prefix|operatorid|prefermetacluster|dbinstanceregionid|regionid|paytype|zookeeperserver|ins_class|productcode|id|preferredgroupproxyid|masterinstancetype|containertype|buyerid|backupretentionperiod|zoneid|dbname|hostid|dbinstancedescription|clustername|token|nettype|systemdbcharset|dbinstanceclass|securityiplist|isuservpc|accountpassword|dbinstancenodecount|corediskquantity|hosttype|dbinstanceconntype|num|preferredbackautorenew|specifysitenamelist|port|orderid|mongostorageengine|dbinstancenettype|dbinstanceexparam|hbasecorequantity|dbinstancegroupcount|storageengine|coredisksize|inscount|coredisktype|hbasestoragetype|connectionstring|debug|hbasemasterecsspecification|proxyapiversion|restoretype|hbasecoreecsspecification|region|coreinstancequantity|autorenew|preferredbackuptime|isanytunnelvip|accountpasswordaccountname|period|accountname|hatype|targetuid|hbasecorestoragespacegb|servicetype|hbaseversion|optmizationservice|hbaseprotocolversion|bind_proxy_group|preferredbackupperiod|engine|dbinstancetype|sourcedbinstancename|masterdisktype|biztype|shardsinfo|maintainendtime|resourcegroupid|engineversion|restoretime|maintainstarttime|externalparameter|vpcinstanceid|dbinstanceid|multiavzexparam|accountpasswordaccountnamss|dbinstancename|proxynodeversion|sourcedbinstanceid|storage|role|accounttype|commoditycode|keplercluster|tunnelid|keplermetricsurl|keplermetadb|vswitchid|ipaddress|vpcid|targetuserid|enablepartition
true|test_case2_disabled|1EXPECT_RC|1EXPECT_RESULT|[null]|protocol|hbasecoreecsspecine|coreinstancetype|backupsetid|prefix|operatorid|prefermetacluster|dbinstanceregionid|regionid|paytype|zookeeperserver|ins_class|productcode|id|preferredgroupproxyid|masterinstancetype|containertype|buyerid|backupretentionperiod|zoneid|dbname|hostid|dbinstancedescription|clustername|token|nettype|systemdbcharset|dbinstanceclass|securityiplist|isuservpc|accountpassword|dbinstancenodecount|corediskquantity|hosttype|dbinstanceconntype|num|preferredbackautorenew|specifysitenamelist|port|orderid|mongostorageengine|dbinstancenettype|dbinstanceexparam|hbasecorequantity|dbinstancegroupcount|storageengine|coredisksize|inscount|coredisktype|hbasestoragetype|connectionstring|debug|hbasemasterecsspecification|proxyapiversion|restoretype|hbasecoreecsspecification|region|coreinstancequantity|autorenew|preferredbackuptime|isanytunnelvip|accountpasswordaccountname|period|accountname|hatype|targetuid|hbasecorestoragespacegb|servicetype|hbaseversion|optmizationservice|hbaseprotocolversion|bind_proxy_group|preferredbackupperiod|engine|dbinstancetype|sourcedbinstancename|masterdisktype|biztype|shardsinfo|maintainendtime|resourcegroupid|engineversion|restoretime|maintainstarttime|externalparameter|vpcinstanceid|dbinstanceid|multiavzexparam|accountpasswordaccountnamss|dbinstancename|proxynodeversion|sourcedbinstanceid|storage|role|accounttype|commoditycode|keplercluster|tunnelid|keplermetricsurl|keplermetadb|vswitchid|ipaddress|vpcid|targetuserid|enablepartition
