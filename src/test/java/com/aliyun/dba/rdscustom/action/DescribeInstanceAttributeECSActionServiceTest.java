///**
// * Alibaba.com Inc.
// * Copyright (c) 2004-2024 All Rights Reserved.
// */
//package com.aliyun.dba.rdscustom.action;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.when;
//
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.Map;
//import java.util.Set;
//
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.dba.base.parameter.MysqlParameterHelper;
//import com.aliyun.dba.base.service.MysqlParamSupport;
//import com.aliyun.dba.host.dataobject.HostLevelDO;
//import com.aliyun.dba.host.service.HostService;
//import com.aliyun.dba.rdscustom.action.service.DescribeInstanceAttributeECSActionService;
//import com.aliyun.dba.rdscustom.action.service.base.ECSActionServiceFactory;
//import com.aliyun.dba.rdscustom.action.support.ECSActionConstant;
//import com.aliyun.dba.support.property.RdsException;
//import com.aliyuncs.AcsResponse;
//import com.aliyuncs.exceptions.ClientException;
//import com.aliyuncs.exceptions.ServerException;
//import com.aliyuncs.transform.UnmarshallerContext;
//
///**
// *
// * <AUTHOR>
// * @version $Id: DeleteInstanceECSActionServiceTest.java, v 0.1 2024-08-01 11:41 DiYu Exp $$
// */
//@RunWith(MockitoJUnitRunner.class)
//public class DescribeInstanceAttributeECSActionServiceTest {
//    @InjectMocks
//    private DescribeInstanceAttributeECSActionService describeInstanceAttributeECSActionService;
//
//    @Mock
//    ECSActionServiceFactory ecsActionServiceFactory;
//
//    @Mock
//    HostService hostService;
//
//    @Mock
//    MysqlParamSupport mysqlParamSupport;
//
//    @Mock
//    MysqlParameterHelper mysqlParaHelper;
//
//    @Before
//    public void setup() throws RdsException {
//        when(mysqlParamSupport.getParameterValue(any(), any())).thenReturn("ch-xxx");
//        Set<Integer> hostIdSet = new HashSet<>();
//        hostIdSet.add(1);
//        when(mysqlParaHelper.getAndCheckHostIdSetOnlyByDedicatedHostNames(any())).thenReturn(hostIdSet);
//        HostLevelDO hostLevelDO = new HostLevelDO();
//        hostLevelDO.setName("hostlevel");
//        when(hostService.getHostLevelByHostId(any())).thenReturn(hostLevelDO);
//
//
//    }
//    @Test
//    public void doPostActionTest() throws RdsException {
//        Map<String, String> params = new HashMap<>();
//        params.put(ECSActionConstant.PARAM_RC_INSTANCE_IDS, "ch-xxx");
//        Map<String, Object> result = new HashMap<>();
//        AcsResponse acsResponse = new AcsResponse() {
//            @Override
//            public AcsResponse getInstance(UnmarshallerContext unmarshallerContext) throws ClientException, ServerException {
//                return null;
//            }
//        };
//        result.put(ECSActionConstant.PARAM_ECS_RESPONSE, JSONObject.toJSONString(acsResponse));
//        Map<String, String> mapping = new HashMap<>();
//
//        Map<String, Object> response = describeInstanceAttributeECSActionService.doPostAction(params, result, mapping);
//        Assert.assertNull(response);
//        Assert.assertTrue(result.containsKey(ECSActionConstant.PARAM_RESPONSE_EXTRA_INFO));
//    }
//}