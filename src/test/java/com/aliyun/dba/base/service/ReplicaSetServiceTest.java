package com.aliyun.dba.base.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.VBM_CUSTINS_LABEL_KEY;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.VBM_CUSTINS_LABEL_VALUE;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mock;

@RunWith(PowerMockRunner.class)
public class ReplicaSetServiceTest {

    @InjectMocks
    private ReplicaSetService replicaSetService;

    @Mock
    private ResourceService resourceService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private CustinsService custinsService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }


    @Test
    public void isAllowEncryptRegularCloudDisk() {
        when(resourceService.getResourceByResKey("ALLOW_ENCRYPT_REGULAR_CLOUD_DISK")).thenReturn(new ResourceDO(){{
            setRealValue("*");
        }});
        ReplicaSet replicaSet = mock(ReplicaSet.class);
        boolean res = replicaSetService.isAllowEncryptRegularCloudDisk(replicaSet);
        assertTrue(res);

        when(resourceService.getResourceByResKey("ALLOW_ENCRYPT_REGULAR_CLOUD_DISK")).thenReturn(new ResourceDO(){{
            setRealValue("[\"123\"]");
        }});
        when(replicaSet.getUserId()).thenReturn("26842_123");
        res = replicaSetService.isAllowEncryptRegularCloudDisk(replicaSet);
        assertTrue(res);

        when(resourceService.getResourceByResKey("ALLOW_ENCRYPT_REGULAR_CLOUD_DISK")).thenReturn(new ResourceDO(){{
            setRealValue("[\"234\"]");
        }});
        res = replicaSetService.isAllowEncryptRegularCloudDisk(replicaSet);
        assertFalse(res);
    }

    @Test
    public void isVbmInstance_LabelEqualsVBMValue_ReturnsTrue() throws ApiException {
        String requestId = "requestId";
        String replicaSetName = "replicaSetName";
        when(defaultApi.getReplicaSetLabel(requestId, replicaSetName, VBM_CUSTINS_LABEL_KEY)).thenReturn(VBM_CUSTINS_LABEL_VALUE);
        boolean result = replicaSetService.isVbmInstance(requestId, replicaSetName);
        assertTrue(result);
    }
    @Test
    public void isVbmInstance_LabelNotEqualsVBMValue_ReturnsFalse() throws ApiException {
        String requestId = "requestId";
        String replicaSetName = "replicaSetName";
        String nonVbmLabelValue = "nonVbmLabelValue";
        when(defaultApi.getReplicaSetLabel(requestId, replicaSetName, "vbmCustinsLabelKey")).thenReturn(nonVbmLabelValue);
        boolean result = replicaSetService.isVbmInstance(requestId, replicaSetName);
        assertFalse(result);
    }
    @Test
    public void isVbmInstance_LabelIsNull_ReturnsFalse() throws ApiException {
        String requestId = "requestId";
        String replicaSetName = "replicaSetName";
        when(defaultApi.getReplicaSetLabel(requestId, replicaSetName, "vbmCustinsLabelKey")).thenReturn(null);
        boolean result = replicaSetService.isVbmInstance(requestId, replicaSetName);
        assertFalse(result);
    }
    @Test
    public void isVbmInstance_LabelIsEmpty_ReturnsFalse() throws ApiException {
        String requestId = "requestId";
        String replicaSetName = "replicaSetName";
        when(defaultApi.getReplicaSetLabel(requestId, replicaSetName, "vbmCustinsLabelKey")).thenReturn("");
        boolean result = replicaSetService.isVbmInstance(requestId, replicaSetName);
        assertFalse(result);
    }

    @Test
    public void preCheckForExternalReplication_activate_failed() throws Exception {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setCategory("standard");
        replicaSet.setId(1L);
        String requestId = "requestId";
        try {
            replicaSetService.preCheckForExternalReplication(replicaSet, requestId, PodDefaultConstants.ExternalReplicationScenario.activate);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }

        replicaSet.setCategory("basic");
        when(custinsService.checkHaveMaxscaleService(any())).thenReturn("yes");
        try {
            replicaSetService.preCheckForExternalReplication(replicaSet, requestId, PodDefaultConstants.ExternalReplicationScenario.activate);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }
}