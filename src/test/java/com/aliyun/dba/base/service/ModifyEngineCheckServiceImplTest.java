package com.aliyun.dba.base.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ModifyEngineCheckServiceImplTest {

    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    protected CustinsService custinsService;
    @Mock
    protected WorkFlowService workFlowService;

    @InjectMocks
    private MysqlEngineCheckServiceImpl mysqlEngineCheckServiceImpl;

    @Before
    public void setUp() throws ApiException {

    }

    @Test
    public void checkMinorVersionWithMaxScale_CustinsIsNull_ReturnsTrue() {
        assertTrue(mysqlEngineCheckServiceImpl.checkMinorVersionWithMaxScale(null));
    }

    @Test
    public void checkMinorVersionWithMaxScale_NoMaxScaleService_ReturnsTrue() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(1, "maxscale")).thenReturn(Collections.emptyList());
        assertTrue(mysqlEngineCheckServiceImpl.checkMinorVersionWithMaxScale(custins));
    }

    @Test
    public void checkMinorVersionWithMaxScale_MaxScaleDeleting_ReturnsTrue() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        CustinsServiceDO custinsServiceDO = new CustinsServiceDO();
        custinsServiceDO.setServiceId("2");
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(1, "maxscale")).thenReturn(Collections.singletonList(custinsServiceDO));
        CustInstanceDO maxscale = new CustInstanceDO();
        maxscale.setStatus(3);
        when(custinsService.getCustInstanceByCustinsId(2)).thenReturn(maxscale);
        assertTrue(mysqlEngineCheckServiceImpl.checkMinorVersionWithMaxScale(custins));
    }

    @Test
    public void checkMinorVersionWithMaxScale_MinorVersionIsNull_ReturnsTrue() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        CustinsServiceDO custinsServiceDO = new CustinsServiceDO();
        custinsServiceDO.setServiceId("2");
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(1, "maxscale")).thenReturn(Collections.singletonList(custinsServiceDO));
        when(custinsService.getCustInstanceByCustinsId(2)).thenReturn(new CustInstanceDO());
        when(custinsParamService.getCustinsParam(2, "minor_version")).thenReturn(null);
        assertTrue(mysqlEngineCheckServiceImpl.checkMinorVersionWithMaxScale(custins));
    }

    @Test
    public void checkMinorVersionWithMaxScale_DbVersion35DateAfter_ReturnsTrue() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        CustinsServiceDO custinsServiceDO = new CustinsServiceDO();
        custinsServiceDO.setServiceId("2");
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(1, "maxscale")).thenReturn(Collections.singletonList(custinsServiceDO));
        CustInstanceDO maxscale = new CustInstanceDO();
        maxscale.setDbVersion("3.5");
        when(custinsService.getCustInstanceByCustinsId(2)).thenReturn(maxscale);
        CustinsParamDO custinsMinorVersion = new CustinsParamDO();
        custinsMinorVersion.setValue("3.5_20200222");
        when(custinsParamService.getCustinsParam(2, "minor_version")).thenReturn(custinsMinorVersion);
        assertTrue(mysqlEngineCheckServiceImpl.checkMinorVersionWithMaxScale(custins));
    }

    @Test
    public void checkMinorVersionWithMaxScale_DbVersion35DateBefore_ReturnsFalse() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        CustinsServiceDO custinsServiceDO = new CustinsServiceDO();
        custinsServiceDO.setServiceId("2");
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(1, "maxscale")).thenReturn(Collections.singletonList(custinsServiceDO));
        CustInstanceDO maxscale = new CustInstanceDO();
        maxscale.setDbVersion("3.5");
        when(custinsService.getCustInstanceByCustinsId(2)).thenReturn(maxscale);
        CustinsParamDO custinsMinorVersion = new CustinsParamDO();
        custinsMinorVersion.setValue("3.5_20200220");
        when(custinsParamService.getCustinsParam(2, "minor_version")).thenReturn(custinsMinorVersion);
        assertFalse(mysqlEngineCheckServiceImpl.checkMinorVersionWithMaxScale(custins));
    }

    @Test
    public void checkMinorVersionWithMaxScale_VersionAfter1_9_23_ReturnsTrue() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        CustinsServiceDO custinsServiceDO = new CustinsServiceDO();
        custinsServiceDO.setServiceId("2");
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(1, "maxscale")).thenReturn(Collections.singletonList(custinsServiceDO));
        CustInstanceDO maxscale = new CustInstanceDO();
        maxscale.setDbVersion("3.4");
        when(custinsService.getCustInstanceByCustinsId(2)).thenReturn(maxscale);
        CustinsParamDO custinsMinorVersion = new CustinsParamDO();
        custinsMinorVersion.setValue("mysql_service_1.9.24");
        when(custinsParamService.getCustinsParam(2, "minor_version")).thenReturn(custinsMinorVersion);
        assertTrue(mysqlEngineCheckServiceImpl.checkMinorVersionWithMaxScale(custins));
    }

    @Test
    public void checkMinorVersionWithMaxScale_VersionBefore1_9_23_ReturnsFalse() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        CustinsServiceDO custinsServiceDO = new CustinsServiceDO();
        custinsServiceDO.setServiceId("2");
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(1, "maxscale")).thenReturn(Collections.singletonList(custinsServiceDO));
        CustInstanceDO maxscale = new CustInstanceDO();
        maxscale.setDbVersion("3.4");
        when(custinsService.getCustInstanceByCustinsId(2)).thenReturn(maxscale);
        CustinsParamDO custinsMinorVersion = new CustinsParamDO();
        custinsMinorVersion.setValue("mysql_service_1.9.22");
        when(custinsParamService.getCustinsParam(2, "minor_version")).thenReturn(custinsMinorVersion);
        assertFalse(mysqlEngineCheckServiceImpl.checkMinorVersionWithMaxScale(custins));
    }
}
