package com.aliyun.dba.base.service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/22
 */

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.idao.UserKmsRelDO;
import com.aliyun.dba.base.idao.UserKmsRelIDao;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.resource.dataobject.BakOwnerDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyuncs.kms.model.v20160120.CreateServiceKeyResponse;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse.KeyMetadata;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.SERVICE_KEY_FILTERS;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TDE_KEY_USAGE;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mock;

@RunWith(PowerMockRunner.class)
@PrepareForTest({KmsService.class, KmsApi.class, DBaasMetaService.class})
public class MysqlEncryptionServiceTest {

    @InjectMocks
    private MysqlEncryptionService mysqlEncryptionService;

    @Mock
    private KmsApi kmsApi;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private KmsService kmsService;

    private DefaultApi defaultApi;

    @Mock
    private ResourceService resourceService;

    @Mock
    private UserKmsRelIDao userKmsRelIDao;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultApi = mock(DefaultApi.class);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(kmsService.getUserRoleArn(anyString())).thenReturn("roleArn");
    }

    @Test
    public void testEnsureTdeEncryptionKeyValid_TdeNotEnabled() {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSet");
        replicaSet.setResourceGroupName("testCluster");
        replicaSet.setUserId("testUserId");
        try {
            when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn("0");
            mysqlEncryptionService.ensureTdeEncryptionKeyValid("testRequestId", replicaSet);
        } catch (Exception e) {
            fail();
        }
    }

    @Test
    public void testEnsureTdeEncryptionKeyValid_EncryptionKeyNotSpecified() {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSet");
        replicaSet.setResourceGroupName("testCluster");
        replicaSet.setUserId("testUserId");
        try {
            when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn("1", "");
            mysqlEncryptionService.ensureTdeEncryptionKeyValid("testRequestId", replicaSet);
            fail();
        } catch (Exception e) {
            assertTrue(e instanceof RdsException);
            Object[] objects = ((RdsException)e).getErrorCode();
            assertEquals("IncorrectDBInstanceTdeStatus", objects[1].toString());
        }

    }

    @Test
    public void testEnsureTdeEncryptionKeyValid_DescribeKeyResponseIsNull() {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSet");
        replicaSet.setResourceGroupName("testCluster");
        replicaSet.setUserId("testUserId");
        try {
            when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn("1", "testEncryptionKey");
            when(defaultApi.getUser(anyString(), anyString(), any())).thenReturn(new User().aliUid("123"));
            when(kmsApi.describeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(null);
            mysqlEncryptionService.ensureTdeEncryptionKeyValid("testRequestId", replicaSet);
            fail();
        } catch (Exception e) {
            assertTrue(e instanceof RdsException);
            Object[] objects = ((RdsException)e).getErrorCode();
            assertEquals("EncryptionKeyNotSupport", objects[1].toString());
        }

    }

    @Test
    public void testEnsureTagExistence_success() throws Exception{
        String clusterName = "clusterName";
        String roleArn = "testRoleArn";
        String keyId = "123456";
        String uid = "uid";

        Map<String, Boolean> tempMap = new HashMap<>();
        tempMap.put(CustinsSupport.ROLE_ARN_TAG, true);
        when(kmsApi.resourceTags(anyString(), anyString(), anyString(), anyString())).thenReturn(tempMap);
        mysqlEncryptionService.ensureTagExistence(clusterName, roleArn, keyId, uid);
        assertEquals(true, tempMap.get(CustinsSupport.ROLE_ARN_TAG));
    }

    @Test
    public void testEnsureTagExistence_notExist() throws Exception{
        String clusterName = "clusterName";
        String roleArn = "testRoleArn";
        String keyId = "123456";
        String uid = "uid";

        Map<String, Boolean> tempMap = new HashMap<>();
        tempMap.put(CustinsSupport.ROLE_ARN_TAG, false);
        when(kmsApi.resourceTags(anyString(), anyString(), anyString(), anyString())).thenReturn(tempMap);
        mysqlEncryptionService.ensureTagExistence(clusterName, roleArn, keyId, uid);
        assertEquals(false, tempMap.get(CustinsSupport.ROLE_ARN_TAG));
    }


    @Test
    public void testEnsureTdeEncryptionKeyValid_KeyStateIsDisabled() {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSet");
        replicaSet.setResourceGroupName("testCluster");
        replicaSet.setUserId("testUserId");
        try {
            when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn("1", "testEncryptionKey");
            when(defaultApi.getUser(anyString(), anyString(), any())).thenReturn(new User().aliUid("123"));
            DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
            KeyMetadata keyMetadata = new KeyMetadata();
            keyMetadata.setKeyState("Disabled");
            describeKeyResponse.setKeyMetadata(keyMetadata);

            when(kmsApi.describeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(describeKeyResponse);
            mysqlEncryptionService.ensureTdeEncryptionKeyValid("testRequestId", replicaSet);
            fail();
        } catch (Exception e) {
            assertTrue(e instanceof RdsException);
            Object[] objects = ((RdsException)e).getErrorCode();
            assertEquals("EncryptionKeyNotSupport", objects[1].toString());
        }

    }

    @Test
    public void testEnsureTdeEncryptionKeyValid_ValidKey() {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSet");
        replicaSet.setResourceGroupName("testCluster");
        replicaSet.setUserId("testUserId");
        try {

            when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn("1", "testEncryptionKey");
            when(defaultApi.getUser(anyString(), anyString(), any())).thenReturn(new User().aliUid("123"));
            DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
            KeyMetadata keyMetadata = new KeyMetadata();
            keyMetadata.setKeyState("Enabled");
            describeKeyResponse.setKeyMetadata(keyMetadata);

            when(kmsApi.describeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(describeKeyResponse);
            mysqlEncryptionService.ensureTdeEncryptionKeyValid("testRequestId", replicaSet);
        } catch (Exception e) {
            fail();
        }
    }

    @Test
    public void testEnsureClsEncryptionKeyValid_ValidKey() {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSet");
        replicaSet.setResourceGroupName("testCluster");
        replicaSet.setUserId("testUserId");
        try {

            when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString()))
                    .thenReturn("kms_key", "testEncryptionKey");
            when(defaultApi.getUser(anyString(), anyString(), any())).thenReturn(new User().aliUid("123"));
            DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
            KeyMetadata keyMetadata = new KeyMetadata();
            keyMetadata.setKeyState("Enabled");
            describeKeyResponse.setKeyMetadata(keyMetadata);

            when(kmsApi.describeKey(anyString(), anyString(), anyString(), anyString()))
                    .thenReturn(describeKeyResponse);
            mysqlEncryptionService.ensureClsEncryptionKeyValid("testRequestId", replicaSet);
        } catch (Exception e) {
            fail();
        }
    }

    @Test
    public void getServiceKey_OneOwner_ReturnsServiceKey() throws Exception {
        // 准备
        BakOwnerDO bakOwnerDO = new BakOwnerDO();
        bakOwnerDO.setBakId(1);
        when(resourceService.getBakOwnerListByCluster("clusterName", BakOwnerDO.KMS_OWNER_TYPE))
                .thenReturn(Collections.singletonList(bakOwnerDO));

        UserKmsRelDO userKmsRelDO = new UserKmsRelDO();
        userKmsRelDO.setMasterKey("existingKey");
        when(userKmsRelIDao.getUserKmsRelByKmsIdAndUserId(any())).thenReturn(userKmsRelDO);
        CreateServiceKeyResponse createServiceKeyResponse = new CreateServiceKeyResponse();
        CreateServiceKeyResponse.KeyMetadata keyMetadata = new CreateServiceKeyResponse.KeyMetadata(); // 显式创建 KeyMetadata 对象
        keyMetadata.setKeyId("newKey");
        createServiceKeyResponse.setKeyMetadata(keyMetadata);
        when(kmsApi.createServiceKey(anyString(), isNull(), anyString(), eq(TDE_KEY_USAGE)))
                .thenReturn(createServiceKeyResponse);
        String result = mysqlEncryptionService.getServiceKey("clusterName", "uid", 1);
        assertEquals("newKey", result);
    }

    @Test
    public void getServiceKey_ServiceKeyExists_ReturnsExistingKey() throws Exception {
        // 准备
        BakOwnerDO bakOwnerDO = new BakOwnerDO();
        bakOwnerDO.setBakId(1);
        when(resourceService.getBakOwnerListByCluster("clusterName", BakOwnerDO.KMS_OWNER_TYPE))
                .thenReturn(Collections.singletonList(bakOwnerDO));
        UserKmsRelDO userKmsRelDO = new UserKmsRelDO();
        userKmsRelDO.setMasterKey("existingKey");
        when(userKmsRelIDao.getUserKmsRelByKmsIdAndUserId(any())).thenReturn(userKmsRelDO);
        List<String> serviceKeys = new ArrayList<>();
        serviceKeys.add("existingKey");
        when(kmsApi.listKeysByFilters(anyString(), anyString(), anyString(), eq(SERVICE_KEY_FILTERS)))
                .thenReturn(serviceKeys);
        DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
        KeyMetadata keyMetadata = new KeyMetadata();
        keyMetadata.setCreator("rds");
        describeKeyResponse.setKeyMetadata(keyMetadata);
        when(kmsApi.describeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(describeKeyResponse);

        String result = mysqlEncryptionService.getServiceKey("clusterName", "uid", 1);
        assertEquals("existingKey", result);
    }
}
