
package com.aliyun.dba.base.service;

import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.google.common.collect.ImmutableMap;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.APP_NAME;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class ResourceScheduleServiceTest {

    @InjectMocks
    private ResourceScheduleService resourceScheduleService;

    @Mock
    private LogAgent logger;


    @Before
    public void setUp() {
        System.out.println("开始测试------");
    }

    @Test
    public void testGetResGuaranteeModelMapKey_RebuildAction_ReturnsRebuildMapKey() {
        String action = "rebuildslaveinstance";
        setAction(action);

        String key = resourceScheduleService.getResGuaranteeModelMapKey();

        assertEquals("MYSQL_RES_GUARANTEE_MODEL_USER_MAP_FOR_REBUILD", key);
    }

    @Test
    public void testGetResGuaranteeModelMapKey_OtherAction_ReturnsDefaultMapKey() {
        String action = "someotheraction";
        setAction(action);

        String key = resourceScheduleService.getResGuaranteeModelMapKey();

        assertEquals("MYSQL_RES_GUARANTEE_MODEL_USER_MAP", key);
    }

    @Test
    public void testGetResGuaranteeModelMapKey_EmptyAction_ReturnsDefaultMapKey() {
        String action = "";
        setAction(action);

        String key = resourceScheduleService.getResGuaranteeModelMapKey();

        assertEquals("MYSQL_RES_GUARANTEE_MODEL_USER_MAP", key);
    }

    @After
    public void tearDown() {
        System.out.println("测试结束------");
    }

    private void setAction(String action) {
        Map<String, String> paramForRequestSession = ImmutableMap.of("requestid", UUID.randomUUID().toString(), "action", action);
        RequestSession.init(APP_NAME, paramForRequestSession);
    }
}