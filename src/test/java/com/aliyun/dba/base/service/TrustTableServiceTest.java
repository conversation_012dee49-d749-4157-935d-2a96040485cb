package com.aliyun.dba.base.service;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.ecs.dataobject.EcsUserInfoDO;
import com.aliyun.dba.ecs.idao.EcsUserInfoIDao;
import com.aliyun.dba.physical.action.service.DbossApiService;
import com.aliyun.dba.rdscustom.action.support.RoleArnService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.AES;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyuncs.AcsRequest;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;


/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2025/3/24 11:35
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({AES.class, DefaultAcsClient.class})
public class TrustTableServiceTest {

    @InjectMocks
    private TrustTableService trustTableService;

    @Mock
    private EcsUserInfoIDao ecsUserInfoIDao;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private RoleArnService roleArnService;

    @Mock
    private DefaultAcsClient defaultAcsClient;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    DbossApiService dbossApiService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(AES.class);
        when(AES.decryptPassword(any(), any())).thenReturn("1234");
    }

    @Test
    public void getKMSClientInnerEndpointTest() throws Exception {

        CustInstanceDO custins = new CustInstanceDO();
        String uid = "123456";
        String regionId = "cn-beijing";
        String keyVersionId = "abcd-1234";
        String requestId = "1234-5678";

        EcsUserInfoDO ecsUserInfoDO = new EcsUserInfoDO();
        ecsUserInfoDO.setAccessKeyId("1234");
        ecsUserInfoDO.setAccessKeySecretEncrypted("1234");

        List<EcsUserInfoDO> ecsUserInfoDOList = new ArrayList<>();
        ecsUserInfoDOList.add(ecsUserInfoDO);
        when(ecsUserInfoIDao.getEcsUserInfoList(any())).thenReturn(ecsUserInfoDOList);

        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("roleName");
        when(custinsParamService.getCustinsParam(any(), any())).thenReturn(custinsParamDO);

        when(roleArnService.getStsEndpoint(any(), any())).thenReturn("inner-endpoint");

        com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse.Credentials res = new com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse.Credentials();
        res.setAccessKeyId("1234");
        res.setAccessKeySecret("4567");
        res.setSecurityToken("abcd");

        AssumeRoleWithServiceIdentityResponse response = new AssumeRoleWithServiceIdentityResponse();
        response.setCredentials(res);

        when(defaultAcsClient.getAcsResponse(any())).thenReturn(response);
        try {
            trustTableService.getKMSClient(custins, uid, regionId, keyVersionId, requestId);
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("error"));
        }
    }

    @Test
    public void getKMSClientPublicEndpointTest() throws Exception {

        CustInstanceDO custins = new CustInstanceDO();
        String uid = "123456";
        String regionId = "cn-beijing";
        String keyVersionId = "abcd-1234";
        String requestId = "1234-5678";

        EcsUserInfoDO ecsUserInfoDO = new EcsUserInfoDO();
        ecsUserInfoDO.setAccessKeyId("1234");
        ecsUserInfoDO.setAccessKeySecretEncrypted("1234");

        List<EcsUserInfoDO> ecsUserInfoDOList = new ArrayList<>();
        ecsUserInfoDOList.add(ecsUserInfoDO);
        when(ecsUserInfoIDao.getEcsUserInfoList(any())).thenReturn(ecsUserInfoDOList);

        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("roleName");
        when(custinsParamService.getCustinsParam(any(), any())).thenReturn(custinsParamDO);

        when(roleArnService.getStsEndpoint(any(), any())).thenReturn("public-endpoint");

        com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse.Credentials res = new com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse.Credentials();
        res.setAccessKeyId("1234");
        res.setAccessKeySecret("4567");
        res.setSecurityToken("abcd");

        AssumeRoleWithServiceIdentityResponse response = new AssumeRoleWithServiceIdentityResponse();
        response.setCredentials(res);

        when(defaultAcsClient.getAcsResponse(any())).thenReturn(response);
        try {
            trustTableService.getKMSClient(custins, uid, regionId, keyVersionId, requestId);
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("error"));
        }
    }

    @Test
    public void testParamValidIsNot80() throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("5.7");
        custins.setStatus(1);

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);

        Map<String, String> params = new HashMap<>();
        try {
            trustTableService.checkParamValid(params);
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("majorversion"));
        }
    }

    @Test
    public void testParamValidNotStatus() throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");
        custins.setStatus(5);

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);

        Map<String, String> params = new HashMap<>();

        try {
            trustTableService.checkParamValid(params);
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("state"));
        }
    }

    @Test
    public void testParamValidNotFlag() throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");
        custins.setStatus(1);

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);

        CustinsParamDO custinsParamDO1 = new CustinsParamDO();
        custinsParamDO1.setValue("0");
        when(custinsParamService.getCustinsParam(any(), eq("trust_table_flag"))).thenReturn(custinsParamDO1);

        Map<String, String> params = new HashMap<>();

        try {
            trustTableService.checkParamValid(params);
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("databases"));
        }
    }

    @Test
    public void testParamValidOK() throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");
        custins.setStatus(1);

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);

        CustinsParamDO custinsParamDO1 = new CustinsParamDO();
        custinsParamDO1.setValue("1");
        when(custinsParamService.getCustinsParam(any(), eq("trust_table_flag"))).thenReturn(custinsParamDO1);

        Map<String, String> params = new HashMap<>();
        trustTableService.checkParamValid(params);
    }


    @Test
    public void testGetParamValue() {
        int id = 12345;
        String key = "abc";

        CustinsParamDO custinsParamDO1 = new CustinsParamDO();
        custinsParamDO1.setValue("success");
        when(custinsParamService.getCustinsParam(any(), any())).thenReturn(custinsParamDO1);

        String result = trustTableService.getParamValue(id, key);
        assertEquals("success", result);
    }



    @Test
    public void signTest() throws RdsException, IOException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");
        custins.setStatus(1);

        Map<String, String> params = new HashMap<>();
        params.put("uid", "123456");
        params.put("regionid", "cn-beijing");

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getParameterValue(anyMap(), any())).thenReturn("requestId");

        CustinsParamDO commonDo = new CustinsParamDO();
        commonDo.setValue("1");
        when(custinsParamService.getCustinsParam(any(), any())).thenReturn(commonDo);

        when(dbossApiService.listTrustTable(any(), any())).thenReturn(new HashMap<>());


        Map<String, Object> result = trustTableService.sign(custins, params);
        assertNotNull(result.get("kmsAlgorithm"));
    }


    @Test
    public void verifyTest() throws RdsException, IOException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");
        custins.setStatus(1);

        Map<String, String> params = new HashMap<>();
        params.put("uid", "123456");
        params.put("regionid", "cn-beijing");

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getParameterValue(anyMap(), any())).thenReturn("requestId");

        CustinsParamDO commonDo = new CustinsParamDO();
        commonDo.setValue("1");
        when(custinsParamService.getCustinsParam(any(), any())).thenReturn(commonDo);

        when(dbossApiService.querySignValue(any(), any(), any(), any())).thenReturn(new HashMap<>());

        Map<String, Object> result = trustTableService.verify(custins, params);
        assertNotNull(result.get("kmsAlgorithm"));
    }

}
