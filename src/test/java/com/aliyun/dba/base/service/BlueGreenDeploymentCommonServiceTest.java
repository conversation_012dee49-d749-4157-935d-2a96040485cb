package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.dba.base.common.consts.BlueGreenDeploymentConsts;
import com.aliyun.dba.base.idao.ZoneIDao;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.ecs.service.EcsDBService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.EcsHostDetailDO;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.api.SecurityGroupApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dts20200101.Client;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailRequest;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponse;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponseBody;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_ACTIVE;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.powermock.utils.Asserts.assertNotNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ RequestSession.class })
public class BlueGreenDeploymentCommonServiceTest {
    @Mock
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;
    @Mock
    private ZoneIDao zoneIDao;
    @Mock
    private MysqlParamSupport paramSupport;
    @Mock
    protected DbossApi dbossApi;
    @Mock
    protected ResourceService resourceService;
    @Mock
    protected CustinsService custinsService;
    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private InstanceIDao instanceIDao;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private ConnAddrCustinsService connAddrCustinsService;
    @Mock
    private InstanceService instanceService;
    @Mock
    private EcsDBService ecsDBService;
    @Mock
    private DockerCommonService dockerCommonService;
    @Mock
    private ClusterService clusterService;
    @Mock
    protected KmsService kmsService;
    @Mock
    protected MycnfService mycnfService;
    @Mock
    private DtsClientService dtsClientService;
    @Mock
    private IpWhiteListService ipWhiteListService;
    @Mock
    private SecurityGroupApi securityGroupApi;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private Client client;


    @InjectMocks
    private BlueGreenDeploymentCommonService blueGreenDeploymentCommonService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        Mockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(dtsClientService.getDtsClient(anyString(), anyString())).thenReturn(client);
        PowerMockito.mockStatic(RequestSession.class);
    }

    @Test
    public void testCheckAndCorrectRegionId_withNullRegionId() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setClusterName("test-cluster");
        ClustersDO cluster = new ClustersDO();
        cluster.setRegion("cn-hangzhou");
        when(clusterService.getClusterByClusterName(anyString())).thenReturn(cluster);

        String result = blueGreenDeploymentCommonService.checkAndCorrectRegionId(null, custins);

        assertEquals("cn-hangzhou", result);
    }

    @Test
    public void testCheckAndCorrectRegionId_withClusterNullRegionId() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setClusterName("test-cluster");
        ClustersDO cluster = new ClustersDO();
        when(clusterService.getClusterByClusterName(anyString())).thenReturn(cluster);

        String result = blueGreenDeploymentCommonService.checkAndCorrectRegionId("re", custins);

    }

    @Test
    public void testCheckAndCorrectRegionId_withClusterNonNullRegionId() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setClusterName("test-cluster");
        ClustersDO cluster = new ClustersDO();
        cluster.setRegion("cn-hangzhou");
        when(clusterService.getClusterByClusterName(anyString())).thenReturn(cluster);

        String result = blueGreenDeploymentCommonService.checkAndCorrectRegionId("re", custins);

    }

    @Test
    public void testCheckAndCorrectRegionId_withAllNullRegionId() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setClusterName("test-cluster");
        ClustersDO cluster = new ClustersDO();
        when(clusterService.getClusterByClusterName(anyString())).thenReturn(cluster);

        String result = blueGreenDeploymentCommonService.checkAndCorrectRegionId(null, custins);

    }

    @Test
    public void testGetNewPrimaryConfigFromParams_withValidPrimaryConfigs() {
        Map<String, String> params = new HashMap<>();
        params.put("engineVersion", "8.0");
        params.put("engine", "mysql");

        Map<String, Object> result = blueGreenDeploymentCommonService.getNewPrimaryConfigFromParams(params);

        assertEquals(2, result.size());
    }

    @Test
    public void testGetNewPrimaryConfigFromParams_withValidPrimaryConfigs2() {
        Map<String, String> params = new HashMap<>();
        params.put("engineVersion", null);
        params.put("engine", null);

        Map<String, Object> result = blueGreenDeploymentCommonService.getNewPrimaryConfigFromParams(params);

    }

    @Test
    public void testGetNewPrimaryConfigFromParams_withValidPrimaryConfigs3() {
        Map<String, String> params = new HashMap<>();
        params.put("engineVersion223", null);
        params.put("engine223", null);

        Map<String, Object> result = blueGreenDeploymentCommonService.getNewPrimaryConfigFromParams(params);

    }
    @Test
    public void testGetNewRoConfigFromParams_withValidData() throws Exception {
        // 准备测试数据
        Map<String, String> params = new HashMap<>();

        JSONArray readOnlyInstanceList = new JSONArray();
        JSONObject roInstance = new JSONObject();
        roInstance.put("engineVersion", "8.0");
        readOnlyInstanceList.add(roInstance);

        String str = JSONObject.toJSONString(readOnlyInstanceList);
        params.put("readOnlyInstanceList", str);
        params.put("engineVersion", "8.0");

        List<Map<String, Object>> result = blueGreenDeploymentCommonService.getNewRoConfigFromParams(params);
        assertNotNull(result, "返回结果不应该为null");

        params.put("readOnlyInstanceList", "");
        List<Map<String, Object>> result1 = blueGreenDeploymentCommonService.getNewRoConfigFromParams(params);
        assertNull(result1);

    }

    @Test
    public void testGetNewRoConfigFromParams_withValidData2() throws Exception {
        // 准备测试数据
        Map<String, String> params = new HashMap<>();

        JSONArray readOnlyInstanceList = new JSONArray();
        JSONObject roInstance = new JSONObject();
        roInstance.put("engineVersion", null);
        readOnlyInstanceList.add(roInstance);

        String str = JSONObject.toJSONString(readOnlyInstanceList);
        params.put("readOnlyInstanceList", str);
        params.put("engineVersion", null);

        List<Map<String, Object>> result = blueGreenDeploymentCommonService.getNewRoConfigFromParams(params);

        params.put("readOnlyInstanceList", "");
        List<Map<String, Object>> result1 = blueGreenDeploymentCommonService.getNewRoConfigFromParams(params);

    }


    @Test
    public void testGetNewRoConfigFromParams_withValidData3() throws Exception {
        // 准备测试数据
        Map<String, String> params = new HashMap<>();

        JSONArray readOnlyInstanceList = new JSONArray();
        JSONObject roInstance = new JSONObject();
        roInstance.put("engineVersion", null);
        readOnlyInstanceList.add(roInstance);

        String str = JSONObject.toJSONString(readOnlyInstanceList);
        params.put("readOnlyInstanceList2", str);
        params.put("engineVersion3", null);

        List<Map<String, Object>> result = blueGreenDeploymentCommonService.getNewRoConfigFromParams(params);

        params.put("readOnlyInstanceList1", "");
        List<Map<String, Object>> result1 = blueGreenDeploymentCommonService.getNewRoConfigFromParams(params);

    }
    @Test
    public void testGetNewReplicaConfigFromParams_withValidData() {
        Map<String, String> params = new HashMap<>();
        List<Map<String, Object>> replicas = new ArrayList<>();
        Map<String, Object> replica1 = new HashMap<>();
        replica1.put("zoneId", "cn-beijing-i");
        replicas.add(replica1);
        params.put("replicaList", JSON.toJSONString(replicas));
        params.put("zoneId", "cn-beijing-i");
        List<Map<String, Object>> result = blueGreenDeploymentCommonService.getNewReplicaConfigFromParams(params);
        assertNotNull(result, "返回结果不应该为null");

        params.put("replicaList", "");
        List<Map<String, Object>> result1 = blueGreenDeploymentCommonService.getNewReplicaConfigFromParams(params);
        assertNull(result1);


    }

    @Test
    public void testGetNewProxyConfigFromParams_withValidData() {
        Map<String, String> params = new HashMap<>();
        Map<String, Object> proxyInstance = new HashMap<>();
        proxyInstance.put("regionId", "cn-beijing");
        params.put("proxyInstance", JSON.toJSONString(proxyInstance));
        params.put("regionId", "cn-beijing");

        Map<String, Object> result = blueGreenDeploymentCommonService.getNewProxyConfigFromParams(params);
        assertNotNull(result, "返回结果不应该为null");
        params.put("proxyInstance", "");
        Map<String, Object> result1 = blueGreenDeploymentCommonService.getNewProxyConfigFromParams(params);
        assertNull(result1);
    }

    @Test
    public void testCheckBlueGreenTableCount_EqualCounts() throws Exception {

        CustInstanceDO blueCustins = new CustInstanceDO();
        blueCustins.setId(1);
        CustInstanceDO greenCustins = new CustInstanceDO();
        greenCustins.setId(2);
        Map<String, Object> checkTableMap = new HashMap<>();
        // Arrange
        Map<String, Integer> tableCountMap1 = new HashMap<>();
        tableCountMap1.put("count", 10);
        Map<String, Integer> tableCountMap2 = new HashMap<>();
        tableCountMap1.put("count", 20);

        when(dbossApi.queryTableCount(Long.valueOf(1))).thenReturn(tableCountMap1);
        when(dbossApi.queryTableCount(Long.valueOf(2))).thenReturn(tableCountMap2);

        // Act
        blueGreenDeploymentCommonService.checkBlueGreenTableCount(blueCustins, greenCustins, checkTableMap);

        // Assert
        assertFalse((Boolean) checkTableMap.get("checkPass"));
    }

    @Test
    public void testCheckBlueGreenList_BlueHasExtraItems() {
        String dbInstanceName = "test-instance";
        List<String> blueList = Arrays.asList("item1", "item2", "item3");
        List<String> greenList = Arrays.asList("item1", "item2");
        String checkItemName = "Read Replica";
        ErrorCode errorCode = ErrorCode.BLUE_GREEN_DEPLOYMENT_ALREADY_EXISTS;

        Map<String, Object> resultMap = new HashMap<>();

        blueGreenDeploymentCommonService.checkBlueGreenList(
                dbInstanceName, blueList, greenList, checkItemName, resultMap, errorCode);

        String expectedMessage = "Read Replicas in blue instance test-instance are more than green instance. Extra Read Replica are item3.";
        assertEquals(expectedMessage, resultMap.get("checkResult"));
        assertFalse((Boolean) resultMap.get("checkPass"));
        assertEquals(errorCode, resultMap.get("errorCode"));
        assertEquals(checkItemName, resultMap.get("checkItemName"));
    }

    @Test
    public void testCheckBlueGreenList_BlueHasExtraItems2() {
        String dbInstanceName = "test-instance";
        List<String> blueList = Arrays.asList("item1", "item2", "item3");
        List<String> greenList = Arrays.asList("item1", "item2");
        String checkItemName = "Read Replica";
        ErrorCode errorCode = ErrorCode.BLUE_GREEN_DEPLOYMENT_ALREADY_EXISTS;

        Map<String, Object> resultMap = new HashMap<>();


        ArrayList mockedList = mock(ArrayList.class);
        Mockito.when(mockedList.isEmpty()).thenReturn(false).thenReturn(true);
        Mockito.when(mockedList.contains(any())).thenReturn(false);

        blueGreenDeploymentCommonService.checkBlueGreenList(
            dbInstanceName, blueList, greenList, checkItemName, resultMap, errorCode);

        String expectedMessage = "Read Replicas in blue instance test-instance are more than green instance. Extra Read Replica are item3.";
        assertEquals(expectedMessage, resultMap.get("checkResult"));
        assertFalse((Boolean) resultMap.get("checkPass"));
        assertEquals(errorCode, resultMap.get("errorCode"));
        assertEquals(checkItemName, resultMap.get("checkItemName"));
    }

    @Test
    public void testCheckBlueGreenList_BlueHasExtraItems3() {
        String dbInstanceName = "test-instance";
        List<String> blueList = Arrays.asList("item1", "item2", "item3");
        List<String> greenList = Arrays.asList("item1", "item2");
        String checkItemName = "Read Replica";
        ErrorCode errorCode = ErrorCode.BLUE_GREEN_DEPLOYMENT_ALREADY_EXISTS;

        Map<String, Object> resultMap = new HashMap<>();


        ArrayList mockedList = mock(ArrayList.class);
        Mockito.when(mockedList.isEmpty()).thenReturn(true).thenReturn(false);
        Mockito.when(mockedList.contains(any())).thenReturn(true);

        blueGreenDeploymentCommonService.checkBlueGreenList(
            dbInstanceName, blueList, greenList, checkItemName, resultMap, errorCode);

        String expectedMessage = "Read Replicas in blue instance test-instance are more than green instance. Extra Read Replica are item3.";
        assertEquals(expectedMessage, resultMap.get("checkResult"));
        assertFalse((Boolean) resultMap.get("checkPass"));
        assertEquals(errorCode, resultMap.get("errorCode"));
        assertEquals(checkItemName, resultMap.get("checkItemName"));
    }

    @Test
    public void testGetDBsWithValidData() throws Exception {
        // 准备测试数据
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        String dbName = "test_db";

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.PAGE_NUMBER, "1");
        params.put(ParamConstants.PAGE_SIZE, "500");

        // 模拟paramSupport的行为
        when(paramSupport.getParameterValue(params, ParamConstants.PAGE_NUMBER, "1")).thenReturn("1");
        when(paramSupport.getParameterValue(params, ParamConstants.PAGE_SIZE, "500")).thenReturn("500");

        // 模拟dbossApi的行为
        List<Map<String, Object>> mockResults = new ArrayList<>();
        Map<String, Object> db1 = new HashMap<>();
        db1.put("dbname", "db1");
        Map<String, Object> db2 = new HashMap<>();
        db2.put("dbname", "db2");
        mockResults.add(db1);
        mockResults.add(db2);

        when(dbossApi.queryDBs(1, dbName, 0, 500)).thenReturn(mockResults);

        // 执行测试
        List<String> result = blueGreenDeploymentCommonService.getDBs(custins, dbName, params);

        // 验证结果
        assertNotNull(result, "xxx");
    }

    @Test
    public void testGetAccounts_NormalCase() throws Exception {
        HashMap params = new HashMap<>();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        // 设置分页参数
        when(paramSupport.getParameterValue(anyMap(), eq(ParamConstants.PAGE_NUMBER), anyString())).thenReturn("1");
        when(paramSupport.getParameterValue(anyMap(), eq(ParamConstants.PAGE_SIZE), anyString())).thenReturn("500");

        // 构建模拟的账户数据
        List<Map<String, Object>> mockAccounts = new ArrayList<>();
        Map<String, Object> account1 = new HashMap<>();
        account1.put("accountName", "user1");
        mockAccounts.add(account1);

        Map<String, Object> account2 = new HashMap<>();
        account2.put("accountName", "user2");
        mockAccounts.add(account2);

        when(dbossApi.queryAccounts(eq(1), isNull(String.class), isNull(String.class), eq(0), eq(500))).thenReturn(mockAccounts);

        // 执行测试
        List<String> result = blueGreenDeploymentCommonService.getAccounts(custins, null, null, params);

        // 验证结果
        assertNotNull(result, "xxx");
    }

    @Test
    public void testCheckStatus() {
        // 准备数据
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setStatus(CUSTINS_STATUS_ACTIVE);
        CustInstanceDO greenCustInstanceDO = new CustInstanceDO();
        greenCustInstanceDO.setStatus(999);

        Map<String, Object> checkStatusMap = new HashMap<>();

        // 执行方法
        blueGreenDeploymentCommonService.checkStatus(custInstanceDO, greenCustInstanceDO, checkStatusMap);

        // 验证结果
        assertFalse((Boolean) checkStatusMap.get("checkPass"));
        assertEquals(ErrorCode.UNSUPPORTED_STATUS_EXCEPT_ACTIVE, checkStatusMap.get("errorCode"));
        assertEquals("Current DB instance status should be active.", checkStatusMap.get("checkResult"));
    }

    @Test
    public void testCheckDts_InvalidStatus() throws Exception {
        // 模拟 DTS 响应
        DescribeDtsJobDetailResponseBody body = new DescribeDtsJobDetailResponseBody();
        body.setStatus("Paused");
        body.setDelay(3000L);

        DescribeDtsJobDetailResponse response = new DescribeDtsJobDetailResponse();
        response.setBody(body);

        when(client.describeDtsJobDetail(any(DescribeDtsJobDetailRequest.class))).thenReturn(response);

        BlueGreenDeploymentRel blueGreenDeploymentRel = new BlueGreenDeploymentRel();
        Map<String, Object> checkDtsStatusMap = new HashMap<>();
        Map<String, Object> checkDtsDelayMap = new HashMap<>();
        JSONObject dtsInfo = new JSONObject();
        dtsInfo.put(BlueGreenDeploymentConsts.DTS_JOB_ID, "test-job-id");
        dtsInfo.put(BlueGreenDeploymentConsts.DTS_INSTANCE_ID, "test-instance-id");
        blueGreenDeploymentRel.setDtsInfo(dtsInfo.toJSONString());

        // 执行测试
        blueGreenDeploymentCommonService.checkDts("cn-hangzhou", "123456", blueGreenDeploymentRel, checkDtsStatusMap, checkDtsDelayMap);

        // 验证状态检查失败
        assertFalse((Boolean) checkDtsStatusMap.get("checkPass"));
    }

    @Test(expected = Exception.class)
    public void testCheckDts_InvalidStatus2() throws Exception {
        // 模拟 DTS 响应
        DescribeDtsJobDetailResponseBody body = new DescribeDtsJobDetailResponseBody();
        body.setStatus("Paused");
        body.setDelay(3000L);

        DescribeDtsJobDetailResponse response = new DescribeDtsJobDetailResponse();
        response.setBody(body);

        when(client.describeDtsJobDetail(any(DescribeDtsJobDetailRequest.class))).thenReturn(null);

        BlueGreenDeploymentRel blueGreenDeploymentRel = new BlueGreenDeploymentRel();
        Map<String, Object> checkDtsStatusMap = new HashMap<>();
        Map<String, Object> checkDtsDelayMap = new HashMap<>();
        JSONObject dtsInfo = new JSONObject();
        dtsInfo.put(BlueGreenDeploymentConsts.DTS_JOB_ID, "test-job-id");
        dtsInfo.put(BlueGreenDeploymentConsts.DTS_INSTANCE_ID, "test-instance-id");
        blueGreenDeploymentRel.setDtsInfo(dtsInfo.toJSONString());

        // 执行测试
        blueGreenDeploymentCommonService.checkDts("cn-hangzhou", "123456", blueGreenDeploymentRel, checkDtsStatusMap, checkDtsDelayMap);

        // 验证状态检查失败
    }

    @Test
    public void testCheckDts_InvalidStatus3() throws Exception {
        // 模拟 DTS 响应
        DescribeDtsJobDetailResponseBody body = new DescribeDtsJobDetailResponseBody();
        body.setStatus("Paused");
        body.setDelay(30000L);

        DescribeDtsJobDetailResponse response = new DescribeDtsJobDetailResponse();
        response.setBody(body);

        when(client.describeDtsJobDetail(any(DescribeDtsJobDetailRequest.class))).thenReturn(response);

        BlueGreenDeploymentRel blueGreenDeploymentRel = new BlueGreenDeploymentRel();
        Map<String, Object> checkDtsStatusMap = new HashMap<>();
        Map<String, Object> checkDtsDelayMap = new HashMap<>();
        JSONObject dtsInfo = new JSONObject();
        dtsInfo.put(BlueGreenDeploymentConsts.DTS_JOB_ID, "test-job-id");
        dtsInfo.put(BlueGreenDeploymentConsts.DTS_INSTANCE_ID, "test-instance-id");
        blueGreenDeploymentRel.setDtsInfo(dtsInfo.toJSONString());

        // 执行测试
        blueGreenDeploymentCommonService.checkDts("cn-hangzhou", "123456", blueGreenDeploymentRel, checkDtsStatusMap, checkDtsDelayMap);

        // 验证状态检查失败
        assertFalse((Boolean) checkDtsStatusMap.get("checkPass"));
    }

    @Test
    public void testCheckIpWhiteList_DifferentIpContent() {
        CustInstanceDO blueCustInstance = new CustInstanceDO();
        CustInstanceDO greenCustInstance = new CustInstanceDO();
        Map<String, Object> checkIpWhiteListMap = new HashMap<>();
        blueCustInstance.setId(1);
        greenCustInstance.setId(2);

        List<CustinsIpWhiteListDO> blueList = createSampleIpWhiteList("default", "***********");
        List<CustinsIpWhiteListDO> greenList = createSampleIpWhiteList("default", "***********");

        when(ipWhiteListService.getCustinsIpWhiteList(eq(1), isNull(), isNull(), isNull())).thenReturn(blueList);
        when(ipWhiteListService.getCustinsIpWhiteList(eq(2), isNull(), isNull(), isNull())).thenReturn(greenList);

        blueGreenDeploymentCommonService.checkIpWhiteList(blueCustInstance, greenCustInstance, checkIpWhiteListMap);

        assertFalse((Boolean) checkIpWhiteListMap.get("checkPass"));
        assertEquals(ErrorCode.INCONSISTENT_IP_WHITELISTS, checkIpWhiteListMap.get("errorCode"));
    }

    private List<CustinsIpWhiteListDO> createSampleIpWhiteList(String groupName, String ipWhiteList) {
        CustinsIpWhiteListDO ipWhiteListDO = new CustinsIpWhiteListDO();
        ipWhiteListDO.setGroupName(groupName);
        ipWhiteListDO.setIpWhiteList(ipWhiteList);

        return Collections.singletonList(ipWhiteListDO);
    }

    @Test
    public void testCheckSecurityGroupList_DifferentSecurityGroups() throws RdsException {
        // Arrange
        CustInstanceDO blueInstance = new CustInstanceDO();
        CustInstanceDO greenInstance = new CustInstanceDO();

        List<Map<String, String>> blueList = createSecurityGroupList("sg-1", "sg-2");
        List<Map<String, String>> greenList = createSecurityGroupList("sg-3", "sg-4");

        when(securityGroupApi.getECSSGRel(blueInstance)).thenReturn(blueList);
        when(securityGroupApi.getECSSGRel(greenInstance)).thenReturn(greenList);

        Map<String, Object> checkResultMap = new HashMap<>();

        // Act
        blueGreenDeploymentCommonService.checkSecurityGroupList(blueInstance, greenInstance, checkResultMap);

        // Assert
        assertFalse((Boolean) checkResultMap.get("checkPass"));
        assertEquals("SecurityGroup is not consistent.", checkResultMap.get("checkResult"));
        assertEquals(ErrorCode.INCONSISTENT_SECURITY_GROUPS, checkResultMap.get("errorCode"));
    }

    private List<Map<String, String>> createSecurityGroupList(String... sgIds) {
        List<Map<String, String>> list = new ArrayList<>();
        for (String id : sgIds) {
            Map<String, String> sg = new HashMap<>();
            sg.put("SecurityGroupId", id);
            list.add(sg);
        }
        return list;
    }

    @Test
    public void testGenerateStringWithTenLength() {
        Integer length = 10;
        String result = blueGreenDeploymentCommonService.generateString(length);
        assertNotNull(result, "生成的字符串不应为null");
    }

    @Test
    public void testPreCheckBeforeDeleteDeployment_GreenInstanceNotActive() throws RdsException {
        CustInstanceDO greenCustins = new CustInstanceDO();
        greenCustins.setId(1);
        greenCustins.setInsName("green-instance");
        greenCustins.setStatus(10);
        // Arrange
        String mode = "deployment";

        // Act & Assert
        RdsException exception = assertThrows(RdsException.class, () -> blueGreenDeploymentCommonService.preCheckBeforeDeleteDeployment(greenCustins, mode));

        assertEquals(ErrorCode.INVALID_STATUS, ErrorCode.INVALID_STATUS);
    }

    @Test
    public void testGetRoInstanceZoneId_MasterAndSlaveDifferentZone() {
        // Arrange
        CustInstanceDO roCustins = new CustInstanceDO();
        roCustins.setId(1);

        InstanceDO masterInstance = mock(InstanceDO.class);
        when(masterInstance.getRole()).thenReturn(0); // INSTANCE_ROLE_MASTER
        when(masterInstance.getHostId()).thenReturn(1001);

        InstanceDO slaveInstance = mock(InstanceDO.class);
        when(slaveInstance.getRole()).thenReturn(1); // INSTANCE_ROLE_SLAVE
        when(slaveInstance.getHostId()).thenReturn(1002);

        when(instanceService.getInstanceByCustinsId(1)).thenReturn(Arrays.asList(masterInstance, slaveInstance));

        EcsHostDetailDO masterHost = mock(EcsHostDetailDO.class);
        when(masterHost.getZoneId()).thenReturn("cn-hangzhou-a");

        EcsHostDetailDO slaveHost = mock(EcsHostDetailDO.class);
        when(slaveHost.getZoneId()).thenReturn("cn-hangzhou-b");

        when(ecsDBService.getEcsHostDetailDOByHostId(1001)).thenReturn(masterHost);
        when(ecsDBService.getEcsHostDetailDOByHostId(1002)).thenReturn(slaveHost);

        // Act
        String result = blueGreenDeploymentCommonService.getRoInstanceZoneId(roCustins);

        // Assert
        assertEquals("cn-hangzhou-a:cn-hangzhou-b", result);
    }

    @Test
    public void testClusterInstanceSpecialtreatment_WithMatchingReplica() {
        // 准备测试数据
        Map<String, Object> greenPrimaryInstanceConfig = new HashMap<>();
        greenPrimaryInstanceConfig.put("dbInstanceClass", "rds.mysql.c1.large");
        greenPrimaryInstanceConfig.put("zoneId", "cn-beijing-i");

        List<Map<String, Object>> greenReplicaConfigList = new ArrayList<>();

        // 添加一个不匹配的副本
        Map<String, Object> replicaConfig1 = new HashMap<>();
        replicaConfig1.put("dbInstanceClass", "rds.mysql.c2.large");
        replicaConfig1.put("zoneId", "cn-beijing-ii");
        greenReplicaConfigList.add(replicaConfig1);

        // 添加一个匹配的副本
        Map<String, Object> replicaConfig2 = new HashMap<>();
        replicaConfig2.put("dbInstanceClass", "rds.mysql.c1.large");
        replicaConfig2.put("zoneId", "cn-beijing-iii");
        greenReplicaConfigList.add(replicaConfig2);

        // 执行测试
        blueGreenDeploymentCommonService.clusterInstanceSpecialtreatment(greenPrimaryInstanceConfig, greenReplicaConfigList);

        // 验证结果
        assertEquals(1, greenReplicaConfigList.size());

    }

    @Test
    public void testCheckHasReadOnly_blueHasReadOnly() {
        CustInstanceDO blue = new CustInstanceDO();
        blue.setId(1);

        CustInstanceDO green = new CustInstanceDO();
        green.setId(2);

        Map<String, Object> checkResultMap = new HashMap<>();

        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(eq(1), eq(false))).thenReturn(Collections.singletonList(new CustInstanceDO()));
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(eq(2), eq(false))).thenReturn(Collections.emptyList());
        blueGreenDeploymentCommonService.checkHasReadOnly(blue, green, checkResultMap);
        assertFalse((Boolean) checkResultMap.get("checkPass"));

        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(eq(1), eq(false))).thenReturn(Collections.emptyList());
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(eq(2), eq(false))).thenReturn(Collections.singletonList(new CustInstanceDO()));
        blueGreenDeploymentCommonService.checkHasReadOnly(blue, green, checkResultMap);
        assertFalse((Boolean) checkResultMap.get("checkPass"));

        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(eq(1), eq(false))).thenReturn(Collections.singletonList(new CustInstanceDO()));
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(eq(2), eq(false))).thenReturn(Collections.emptyList());
        blueGreenDeploymentCommonService.checkHasReadOnly(blue, green, checkResultMap);
        assertFalse((Boolean) checkResultMap.get("checkPass"));
    }

    @Test
    public void testCheckMaxScale_BothHaveMaxScale() {
        CustInstanceDO blueInstance = new CustInstanceDO();
        CustInstanceDO greenInstance = new CustInstanceDO();
        Map<String, Object> checkResultMap = new HashMap<>();

        blueInstance.setId(1);
        greenInstance.setId(2);

        List<CustinsServiceDO> mockBlueMaxScaleList = Collections.singletonList(new CustinsServiceDO());
        List<CustinsServiceDO> mockGreenMaxScaleList = Collections.singletonList(new CustinsServiceDO());

        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(eq(1), eq("maxscale"))).thenReturn(mockBlueMaxScaleList);
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(eq(2), eq("maxscale"))).thenReturn(mockGreenMaxScaleList);
        blueGreenDeploymentCommonService.checkMaxScale(blueInstance, greenInstance, checkResultMap);
        assertFalse((Boolean) checkResultMap.get("checkPass"));

        List<CustinsServiceDO> mockBlueMaxScaleList1 = Collections.singletonList(new CustinsServiceDO());
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(eq(1), eq("maxscale"))).thenReturn(mockBlueMaxScaleList1);
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(eq(2), eq("maxscale"))).thenReturn(Collections.emptyList());
        blueGreenDeploymentCommonService.checkMaxScale(blueInstance, greenInstance, checkResultMap);
        assertFalse((Boolean) checkResultMap.get("checkPass"));

        List<CustinsServiceDO> mockGreenMaxScaleList2 = Collections.singletonList(new CustinsServiceDO());
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(eq(1), eq("maxscale"))).thenReturn(Collections.emptyList());
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(eq(2), eq("maxscale"))).thenReturn(mockGreenMaxScaleList2);
        blueGreenDeploymentCommonService.checkMaxScale(blueInstance, greenInstance, checkResultMap);
        assertFalse((Boolean) checkResultMap.get("checkPass"));
    }

    @Test
    public void testCheckConnectionString_NotEqual_PrivateOnly() {
        CustInstanceDO blueInstance = new CustInstanceDO();
        blueInstance.setId(1);
        CustInstanceDO greenInstance = new CustInstanceDO();
        greenInstance.setId(2);

        Map<String, Object> checkResultMap = new HashMap<>();

        List<CustinsConnAddrDO> blueAddrs = createMockConnAddrList(1);
        List<CustinsConnAddrDO> greenAddrs = createMockConnAddrList(2);

        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(eq(1), isNull(), eq(CustinsSupport.RW_TYPE_NORMAL))).thenReturn(blueAddrs);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(eq(2), isNull(), eq(CustinsSupport.RW_TYPE_NORMAL))).thenReturn(greenAddrs);

        blueGreenDeploymentCommonService.checkConnectionString(blueInstance, greenInstance, checkResultMap);

        assertFalse((Boolean) checkResultMap.get("checkPass"));
    }

    @Test
    public void testCheckConnectionString_NotEqual_PrivateOnly2() {
        CustInstanceDO blueInstance = new CustInstanceDO();
        blueInstance.setId(1);
        CustInstanceDO greenInstance = new CustInstanceDO();
        greenInstance.setId(2);

        Map<String, Object> checkResultMap = new HashMap<>();

        List<CustinsConnAddrDO> blueAddrs = createMockConnAddrList(1);
        List<CustinsConnAddrDO> greenAddrs = createMockConnAddrList(2);

        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(eq(1), isNull(), eq(CustinsSupport.RW_TYPE_NORMAL))).thenReturn(blueAddrs);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(eq(2), isNull(), eq(CustinsSupport.RW_TYPE_NORMAL))).thenReturn(greenAddrs);

        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(eq(1), eq(CustinsSupport.NET_TYPE_PRIVATE), eq(CustinsSupport.RW_TYPE_NORMAL))).thenReturn(blueAddrs);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(eq(2), eq(CustinsSupport.NET_TYPE_PRIVATE), eq(CustinsSupport.RW_TYPE_NORMAL))).thenReturn(greenAddrs);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(eq(1), eq(CustinsSupport.NET_TYPE_PUBLIC), eq(CustinsSupport.RW_TYPE_NORMAL))).thenReturn(blueAddrs);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(eq(2), eq(CustinsSupport.NET_TYPE_PUBLIC), eq(CustinsSupport.RW_TYPE_NORMAL))).thenReturn(greenAddrs);

        blueGreenDeploymentCommonService.checkConnectionString(blueInstance, greenInstance, checkResultMap);

    }

    private List<CustinsConnAddrDO> createMockConnAddrList(Integer... netTypes) {
        List<CustinsConnAddrDO> list = new ArrayList<>();
        for (Integer type : netTypes) {
            CustinsConnAddrDO addr = mock(CustinsConnAddrDO.class);
            when(addr.getNetType()).thenReturn(type);
            list.add(addr);
        }
        return list;
    }

    @Test
    public void testGetQuantityLimit_validResourceAndValue() throws Exception {
        String reskey = "valid_key";
        ResourceDO resource = new ResourceDO();
        resource.setRealValue("100");

        when(resourceService.getResourceByResKey(reskey)).thenReturn(resource);

        Integer result = blueGreenDeploymentCommonService.getQuantityLimit(reskey);
        assertEquals(100, result.intValue());

        when(resourceService.getResourceByResKey(reskey)).thenReturn(null);
        Exception exception = assertThrows(Exception.class, () -> blueGreenDeploymentCommonService.getQuantityLimit(reskey));
        assertNotNull(exception, "xxx");
    }


    @Test
    public void testPreCheckWhenCreating_allChecksPassed_shouldNotThrowException() throws Exception {
        CustInstanceDO custInstance = new CustInstanceDO();
        custInstance.setId(1);
        custInstance.setInsName("test-instance");
        custInstance.setLevelId(1);
        custInstance.setStatus(CUSTINS_STATUS_ACTIVE); // 设置为激活状态
        // Arrange: 设置所有检查都通过的情况
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        instanceLevel.setCategory("standard");
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(instanceLevel);

        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(Collections.emptyList());
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(anyInt(), anyString())).thenReturn(Collections.emptyList());
        when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByBlueCustinsId(anyLong())).thenReturn(null);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.emptyList());
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(null);
        when(dbossApi.queryPdoByBlueGreenAcc(anyLong())).thenReturn(Collections.emptyList());
        when(custinsParamService.getCustinsParams(anyInt(), anyList())).thenReturn(Collections.emptyList());
        when(mycnfService.getMycnfCustinstance(anyInt(), anyString())).thenReturn(null);
        when(dbossApi.queryDBCount(anyInt())).thenReturn(Collections.singletonMap("totalCount", 1));
        when(dbossApi.queryAccounts(anyInt(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(Collections.emptyList());

        blueGreenDeploymentCommonService.preCheckWhenCreating("request123", custInstance);

        custInstance.setStatus(999);
        try {
            blueGreenDeploymentCommonService.preCheckWhenCreating("request123", custInstance);
        } catch (Exception e) {}


        try {
            custInstance.setStatus(CUSTINS_STATUS_ACTIVE);
            when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(Collections.singletonList(custInstance));
            blueGreenDeploymentCommonService.preCheckWhenCreating("request123", custInstance);
        } catch (Exception e) {}

        try {
            custInstance.setStatus(CUSTINS_STATUS_ACTIVE);
            when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(Collections.singletonList(custInstance));
            when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(anyInt(), anyString())).thenReturn(Collections.singletonList(new CustinsServiceDO()));
            blueGreenDeploymentCommonService.preCheckWhenCreating("request123", custInstance);
        } catch (Exception e) {}

        try {
            custInstance.setStatus(CUSTINS_STATUS_ACTIVE);
            when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(Collections.singletonList(custInstance));
            when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(anyInt(), anyString())).thenReturn(Collections.singletonList(new CustinsServiceDO()));
            when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByBlueCustinsId(anyLong())).thenReturn(new BlueGreenDeploymentRel());
            blueGreenDeploymentCommonService.preCheckWhenCreating("request123", custInstance);
        } catch (Exception e) {}

        try {
            custInstance.setStatus(CUSTINS_STATUS_ACTIVE);
            when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(Collections.singletonList(custInstance));
            when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(anyInt(), anyString())).thenReturn(Collections.singletonList(new CustinsServiceDO()));
            when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByBlueCustinsId(anyLong())).thenReturn(new BlueGreenDeploymentRel());
            InstanceLevelDO instanceLevel1 = new InstanceLevelDO();
            instanceLevel1.setCategory("Cluster");
            when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(instanceLevel1);
            blueGreenDeploymentCommonService.preCheckWhenCreating("request123", custInstance);
        } catch (Exception e) {}

        try {
            InstanceLevelDO instanceLevel2 = new InstanceLevelDO();
            instanceLevel2.setCategory("Basic");
            when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(instanceLevel2);
            blueGreenDeploymentCommonService.preCheckWhenCreating("request123", custInstance);
        } catch (Exception e) {}


    }


    @Test
    public void testGenerateBlueGreenDeploymentName_FirstAttemptUnique() {
        when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByDeploymentName(anyString())).thenReturn(null);
        String result = blueGreenDeploymentCommonService.generateBlueGreenDeploymentName();
        assertNotNull(result, "xxx");
    }

    @Test
    public void testGetGreenNormalInstanceConfig_VersionSame_ShouldKeepMinorVersion() throws Exception {
        // Arrange
        CustInstanceDO custInstance = new CustInstanceDO();
        custInstance.setId(1);
        custInstance.setKindCode(18);
        custInstance.setLevelId(1);
        custInstance.setDiskSize(102400L);
        custInstance.setInsName("test-instance");
        custInstance.setDbVersion("5.7");

        InstanceLevelDO levelDO = new InstanceLevelDO();
        levelDO.setClassCode("ecs.g5.large");
        levelDO.setCategory("standard");

        String expectedRequestId = "test-request-id";
        when(RequestSession.getRequestId()).thenReturn(expectedRequestId);

        when(instanceIDao.getInstanceLevelByLevelId(1)).thenReturn(levelDO);

        when(replicaSetService.getReplicaSetStorageType(anyString(), anyString())).thenReturn("cloud_auto");
        when(replicaSetService.getVolumePerfLevel(anyString(), anyString(), anyString())).thenReturn("PL1");

        List<CustinsConnAddrDO> connAddrs = new ArrayList<>();
        CustinsConnAddrDO connAddr = new CustinsConnAddrDO();
        connAddr.setVpcId("vpc-1");
        connAddr.setVip("***********");
        connAddrs.add(connAddr);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(connAddrs);
        when(custinsService.getVswitchIdByVpcIpAndVpcId(anyString(), anyString())).thenReturn("vsw-1");
        when(dockerCommonService.getZoneIdByCustins(any(CustInstanceDO.class))).thenReturn("cn-hangzhou-a");
        InstanceDO masterInstance = new InstanceDO();
        masterInstance.setRole(0);
        InstanceDO slaveInstance = new InstanceDO();
        slaveInstance.setRole(1);
        slaveInstance.setSiteName("siteName1");

        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(Arrays.asList(masterInstance, slaveInstance));

        CustinsParamDO minorVersionDo = new CustinsParamDO();
        minorVersionDo.setValue("minor_123");
        when(custinsParamService.getCustinsParam(anyInt(), eq("minor_version"))).thenReturn(minorVersionDo);

        Map<String, Object> newPrimaryConfig = new HashMap<>();
        newPrimaryConfig.put("engineVersion", "5.7");

        JSONObject result = blueGreenDeploymentCommonService.getGreenNormalInstanceConfig(custInstance, "cn-hangzhou", newPrimaryConfig, null, null);
        assertNotNull(result, "xxx");
        CustinsParamDO burstingEnabledParam = new CustinsParamDO();
        burstingEnabledParam.setValue("true");
        when(custinsParamService.getCustinsParam(any(), eq("burstingEnabled"))).thenReturn(burstingEnabledParam);
        result = blueGreenDeploymentCommonService.getGreenNormalInstanceConfig(custInstance, "cn-hangzhou", newPrimaryConfig, null, null);

        assertNotNull(result, "xxx");
    }

    @Test
    public void testGetBlueRoInstanceConfigList() throws RdsException, ApiException {
        CustInstanceDO custInstance = new CustInstanceDO();
        custInstance.setId(1);
        custInstance.setKindCode(18);
        custInstance.setLevelId(1);
        custInstance.setDiskSize(102400L);
        custInstance.setInsName("test-instance");
        custInstance.setDbVersion("5.7");
        custInstance.setInsType(3);

        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(Collections.singletonList(custInstance));

        InstanceLevelDO levelDO = new InstanceLevelDO();
        levelDO.setClassCode("ecs.g5.large");
        levelDO.setCategory("standard");
        levelDO.setIsolateHost(2);
        String expectedRequestId = "test-request-id";
        when(RequestSession.getRequestId()).thenReturn(expectedRequestId);

        when(instanceIDao.getInstanceLevelByLevelId(1)).thenReturn(levelDO);
        when(replicaSetService.getReplicaSetStorageType(anyString(), anyString())).thenReturn("cloud_auto");
        List<CustinsConnAddrDO> connAddrs = new ArrayList<>();
        CustinsConnAddrDO connAddr = new CustinsConnAddrDO();
        connAddr.setVpcId("vpc-1");
        connAddr.setVip("***********");
        connAddrs.add(connAddr);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(connAddrs);
        when(custinsService.getVswitchIdByVpcIpAndVpcId(anyString(), anyString())).thenReturn("vsw-1");
        InstanceDO masterInstance = new InstanceDO();
        masterInstance.setRole(0);
        masterInstance.setHostId(0);
        InstanceDO slaveInstance = new InstanceDO();
        slaveInstance.setRole(1);
        slaveInstance.setSiteName("siteName1");
        slaveInstance.setHostId(1);

        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(Arrays.asList(masterInstance, slaveInstance));
        EcsHostDetailDO masterHost = new EcsHostDetailDO();
        masterHost.setZoneId("cn-hangzhou-a");
        when(ecsDBService.getEcsHostDetailDOByHostId(anyInt())).thenReturn(masterHost);

        when(instanceIDao.getInstanceLevelByLevelId(1)).thenReturn(levelDO);

        List<Map<String, Object>> result = blueGreenDeploymentCommonService.getBlueRoInstanceConfigList(custInstance, "cn-hangzhou");

        assertNotNull(result, "xxx");
    }

    @Test
    public void testGetBlueReplicaConfigList_withMultipleSlaves() throws ApiException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsName("test-instance");
        // 准备模拟数据
        List<Replica> replicas = new ArrayList<>();
        replicas.add(createReplica("slave1", "slv1", "cn-hangzhou-a", Replica.RoleEnum.SLAVE));
        replicas.add(createReplica("slave2", "slv2", "cn-hangzhou-b", Replica.RoleEnum.SLAVE));

        ReplicaListResult replicasListResult = new ReplicaListResult();
        replicasListResult.setItems(replicas);
        String expectedRequestId = "test-request-id";
        when(RequestSession.getRequestId()).thenReturn(expectedRequestId);
        when(defaultApi.listReplicasInReplicaSet(anyString(), anyString(), isNull(), isNull(), isNull(), isNull())).thenReturn(replicasListResult);

        // 执行测试
        List<Map<String, Object>> result = blueGreenDeploymentCommonService.getBlueReplicaConfigList(custins, "cn-hangzhou");

        // 验证结果
        assertNotNull(result, "xxx");
    }

    private Replica createReplica(String name, String classCode, String zoneId, Replica.RoleEnum role) {
        Replica replica = new Replica();
        replica.setClassCode(classCode);
        replica.setZoneId(zoneId);
        replica.setRole(role);
        return replica;
    }

    @Test
    public void testGetBlueProxyInstanceConfig() throws RdsException, ApiException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setKindCode(0);
        List<CustinsServiceDO> custinsServiceList = new ArrayList<>();
        CustinsServiceDO serviceDO = new CustinsServiceDO();
        serviceDO.setServiceId("1");
        custinsServiceList.add(serviceDO);
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(anyInt(), anyString())).thenReturn(custinsServiceList);

        InstanceLevelDO levelDO = new InstanceLevelDO();
        levelDO.setClassCode("ecs.g5.large");
        levelDO.setCategory("standard");
        levelDO.setIsolateHost(2);
        String expectedRequestId = "test-request-id";
        when(RequestSession.getRequestId()).thenReturn(expectedRequestId);

        when(instanceIDao.getInstanceLevelByLevelId(anyInt())).thenReturn(levelDO);
        CustInstanceDO maxscale = new CustInstanceDO();
        maxscale.setId(1);
        maxscale.setLevelId(1);
        when(custinsService.getCustInstanceByCustinsId(anyInt())).thenReturn(maxscale);
        CustinsParamDO persistentConnectionDO = new CustinsParamDO();
        persistentConnectionDO.setValue("true");
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(persistentConnectionDO);
        List<CustinsConnAddrDO> connAddrs = new ArrayList<>();
        CustinsConnAddrDO connAddr = new CustinsConnAddrDO();
        connAddr.setVpcId("vpc-1");
        connAddr.setVip("***********");
        connAddrs.add(connAddr);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(connAddrs);
        when(custinsService.getVswitchIdByVpcIpAndVpcId(anyString(), anyString())).thenReturn("vsw-1");
        ReplicaListResult replicasListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        replicas.add(createReplica("slave1", "slv1", "cn-hangzhou-a", Replica.RoleEnum.SLAVE));
        replicas.add(createReplica("slave2", "slv2", "cn-hangzhou-b", Replica.RoleEnum.SLAVE));
        replicasListResult.setItems(replicas);
        when(defaultApi.listReplicasInReplicaSet(anyString(), anyString(), isNull(), isNull(), isNull(), isNull())).thenReturn(replicasListResult);
        CustInstanceDO physicalCustins = new CustInstanceDO();
        physicalCustins.setId(1);
        when(custinsService.getCustInstanceByParentId(anyInt())).thenReturn(Collections.singletonList(physicalCustins));
        InstanceDO masterInstance = new InstanceDO();
        masterInstance.setRole(0);
        masterInstance.setHostId(0);
        masterInstance.setCpuCores(5);
        InstanceDO slaveInstance = new InstanceDO();
        slaveInstance.setRole(1);
        slaveInstance.setSiteName("siteName1");
        slaveInstance.setHostId(1);
        slaveInstance.setCpuCores(5);

        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(Arrays.asList(masterInstance, slaveInstance));

        Map<String, Object> result = blueGreenDeploymentCommonService.getBlueProxyInstanceConfig(custins, "cn-beijing");
        assertNotNull(result, "xxx");
    }

    @Test
    public void testGetGreenRoInstanceConfigList_withMatchingInstances() {
        // 构造输入数据
        List<Map<String, Object>> blueList = new ArrayList<>();
        Map<String, Object> instance1 = new HashMap<>();
        instance1.put("roInstanceName", "RO1");
        instance1.put("configValue", "blue_value1");
        blueList.add(instance1);

        Map<String, Object> instance2 = new HashMap<>();
        instance2.put("roInstanceName", "RO2");
        instance2.put("configValue", "blue_value2");
        blueList.add(instance2);

        List<Map<String, Object>> greenList = new ArrayList<>();
        Map<String, Object> greenInstance1 = new HashMap<>();
        greenInstance1.put("roInstanceName", "ro1"); // 忽略大小写匹配
        greenInstance1.put("configValue", "green_value1");
        greenInstance1.put("newField", "added_value");
        greenList.add(greenInstance1);

        List<Map<String, Object>> result = blueGreenDeploymentCommonService.getGreenRoInstanceConfigList(blueList, greenList);

        assertNotNull(result, "xxx");
        assertEquals(2, result.size());
    }

    @Test
    public void testGetGreenReplicaListConfig_withMatchingReplicaName() {
        // 构造旧配置
        List<Map<String, Object>> replicaConfigList = new ArrayList<>();
        Map<String, Object> oldConfig = new HashMap<>();
        oldConfig.put("replicaName", "replica1");
        oldConfig.put("zoneId", "cn-beijing-a");
        replicaConfigList.add(oldConfig);

        // 构造新配置
        List<Map<String, Object>> newReplicaConfig = new ArrayList<>();
        Map<String, Object> newConfig = new HashMap<>();
        newConfig.put("replicaName", "replica1");
        newConfig.put("zoneId", "cn-shanghai-b");
        newConfig.put("cpu", 4);
        newReplicaConfig.add(newConfig);

        List<Map<String, Object>> result = blueGreenDeploymentCommonService.getGreenReplicaListConfig(replicaConfigList, newReplicaConfig);

        assertNotNull(result, "xxx");
        assertEquals(1, result.size());

        Map<String, Object> updated = result.get(0);
        assertEquals("replica1", updated.get("replicaName"));
        assertEquals("cn-shanghai-b", updated.get("zoneId"));
        assertEquals(4, updated.get("cpu"));
    }

    @Test
    public void testGetGreenProxyInstanceConfig_withBothDbProxyParams() {
        Map<String, Object> proxyConfig = new HashMap<>();
        proxyConfig.put("key1", "value1");

        Map<String, Object> newProxyConfig = new HashMap<>();
        newProxyConfig.put("dbProxyInstanceNum", 2);
        newProxyConfig.put("DBProxyNodes", "[{\"nodeId\": \"n1\"}]");

        Map<String, Object> result = blueGreenDeploymentCommonService.getGreenProxyInstanceConfig(proxyConfig, newProxyConfig);

        assertEquals(2, result.size());
    }

    @Test
    public void testCheckGreenInstanceConfig_withAllVersionsMatch() {
        Map<String, Object> greenPrimaryInstanceConfig = new HashMap<>();
        greenPrimaryInstanceConfig.put("engineVersion", "8.0");

        List<Map<String, Object>> greenRoInstanceConfigList = new ArrayList<>();

        Map<String, Object> roConfig1 = new HashMap<>();
        roConfig1.put("engineVersion", "8.0");
        greenRoInstanceConfigList.add(roConfig1);

        Map<String, Object> roConfig2 = new HashMap<>();
        roConfig2.put("engineVersion", "8.0");
        greenRoInstanceConfigList.add(roConfig2);
        blueGreenDeploymentCommonService.checkGreenInstanceConfig(greenPrimaryInstanceConfig, greenRoInstanceConfigList);
        assertEquals("8.0", roConfig1.get("engineVersion"));
    }

    @Test
    public void testPreCheckWhenSwitching_withReadOnlyInstance_shouldThrowException() throws RdsException {
        int insId = 123;
        List<CustInstanceDO> readinsList = Collections.singletonList(new CustInstanceDO());

        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(insId, false)).thenReturn(readinsList);
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(insId, "maxscale")).thenReturn(Collections.emptyList());

        RdsException exception = assertThrows(RdsException.class, () -> {
            blueGreenDeploymentCommonService.preCheckWhenSwitching(insId);
        });
    }

    @Test
    public void testPreCheckWhenSwitching_withMaxScaleProxy_shouldThrowException() throws RdsException {
        int insId = 123;

        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(insId, false)).thenReturn(Collections.emptyList());
        when(custinsService.getCustinsServicesByCustinsIdAndServiceRole(insId, "maxscale")).thenReturn(Collections.singletonList(new CustinsServiceDO()));

        RdsException exception = assertThrows(RdsException.class, () -> {
            blueGreenDeploymentCommonService.preCheckWhenSwitching(insId);
        });
    }
}
