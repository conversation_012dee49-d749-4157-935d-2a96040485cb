package com.aliyun.dba.base.service;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.Silent.class)
public class MysqlParamSupportTest {

    @InjectMocks
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private CheckService checkService;
    @Mock
    private CustinsService custinsService;
    @Mock
    private CustinsParamService custinsParamService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void getAndCheckSourceDBInstanceName() throws RdsException {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("requestid", "1111111111111");
        actionParams.put("user_id", "0");
        actionParams.put("uid", "uelbert01");
        actionParams.put("regionid", "ap-southeast-1");
        actionParams.put("readdbinstancename", "testReadDbInstanceName");
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(123);
        custInstanceDO.setInsName("testInstanceName");
        when(custinsService.getCustInstanceByInsName(anyInt(), any(), anyInt())).thenReturn(custInstanceDO);
        when(checkService.getAndCheckUserId(anyString(), anyString(), anyString())).thenReturn(0);
        CustInstanceDO andCheckReadCustInstance = mysqlParamSupport.getAndCheckReadCustInstance(actionParams);
        assertNotNull(andCheckReadCustInstance);
    }

    @Test
    public void isConnectionStringToSsl() throws RdsException {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("requestid", "1111111111111");
        actionParams.put("user_id", "0");
        actionParams.put("uid", "uelbert01");
        actionParams.put("regionid", "ap-southeast-1");
        actionParams.put("connectionstring", "test");
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(123);
        CustinsParamDO insSslConfig = new CustinsParamDO();
        insSslConfig.setValue("1");
        CustinsParamDO insCertConfig = new CustinsParamDO();
        insCertConfig.setValue("test");
        when(custinsParamService.getCustinsParam(custInstanceDO.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL)).thenReturn(insSslConfig);
        when(custinsParamService.getCustinsParam(custInstanceDO.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_CERT_CN_NAME)).thenReturn(insCertConfig);

        boolean isConnectionStringToSsl = mysqlParamSupport.isConnectionStringToSsl(actionParams, custInstanceDO);
        assertTrue(isConnectionStringToSsl);
    }

    @Test
    public void testGetAndCheckIpAddress() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("ip", "***********");
        String ipAddress = mysqlParamSupport.getAndCheckIpAddress(actionParams);
        assertEquals("***********", ipAddress);
        actionParams.put("ip", "invalidIp");
        try {
            mysqlParamSupport.getAndCheckIpAddress(actionParams);
        } catch (RdsException e) {
            assertNotNull(e);
        }
    }

    @Test
    public void testIsBuildReplication() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        assertFalse(mysqlParamSupport.isBuildReplication(actionParams));

        actionParams.put("buildreplication", "true");
        assertTrue(mysqlParamSupport.isBuildReplication(actionParams));
    }

}