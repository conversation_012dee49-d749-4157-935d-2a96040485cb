package com.aliyun.dba.base.service;

import com.aliyun.dts20200101.Client;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class DtsClientServiceTest {

    @InjectMocks
    private DtsClientService dtsClientService;

    @Mock
    private SLRService slrService;

    private final String testRegionId = "cn-hangzhou";
    private final String testAliUid = "123456";

    /**
     * 测试正常流程：成功获取DTS客户端
     */
    @Test
    public void testGetDtsClient_Success() throws Exception {
        // 构造mock响应
        AssumeRoleWithServiceIdentityResponse mockResponse = mock(AssumeRoleWithServiceIdentityResponse.class);
        AssumeRoleWithServiceIdentityResponse.Credentials credentials = new AssumeRoleWithServiceIdentityResponse.Credentials();
        credentials.setAccessKeyId("testKeyId");
        credentials.setAccessKeySecret("testKeySecret");
        credentials.setSecurityToken("testToken");

        when(mockResponse.getCredentials()).thenReturn(credentials);
        when(slrService.getAssumeAuthInfoForSLR(testRegionId, testAliUid)).thenReturn(mockResponse);

        // 执行方法
        Client client = dtsClientService.getDtsClient(testRegionId, testAliUid);

        // 断言
        assertNotNull(client);
    }

    /**
     * 测试异常情况：slrResponse为null时抛出异常
     */
    @Test
    public void testGetDtsClient_SlrResponseIsNull_ThrowsException() throws Exception {
        when(slrService.getAssumeAuthInfoForSLR(testRegionId, testAliUid)).thenReturn(null);

        Exception exception = assertThrows(Exception.class, () -> {
            dtsClientService.getDtsClient(testRegionId, testAliUid);
        });

        assertTrue(exception.getMessage().contains("AssumeRoleWithServiceIdentityResponse is null"));
    }
}
