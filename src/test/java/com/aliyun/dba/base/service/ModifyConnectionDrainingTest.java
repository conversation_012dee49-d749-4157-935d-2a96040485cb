package com.aliyun.dba.base.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.model.APISuccess;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.ModifyDBInstanceConnectionDrainingImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ModifyConnectionDrainingTest {

    @Mock
    private MysqlParameterHelper parameterHelper;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    protected MysqlParamSupport paramSupport;
    @Mock
    protected CustinsService custinsService;
    @Mock
    protected DBaasMetaService metaService;
    @Mock
    protected WorkFlowService workFlowService;

    @Mock
    protected LinksApi linksApi;

    @InjectMocks
    private ModifyConnectionDrainingService modifyConnectionDrainingService;

    private DefaultApi defaultApi;

    @Before
    public void setUp() throws ApiException {

        defaultApi = mock(DefaultApi.class);
        Mockito.when(metaService.getDefaultClient()).thenReturn(defaultApi);

    }

    @Test
    public void testDoActionRequest() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("DrainingTimeout", "10");
        params.put("DrainingEnable","1");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
//        replicaSet.setLockMode(ReplicaSet.LockModeEnum.LOCKMANUAL);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setCategory("standard");
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);

        Map<String, String> labels = new HashMap<>();
        labels.put("ALB_CONNECTION_DRAINING_SWITCH", "OFF");
        labels.put("Test111", "ON");
        replicaSet.setLabels(labels);

        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);


        EndpointListResult endpointListResult = new EndpointListResult();
        Endpoint endpoint1 = new Endpoint();
        endpoint1.setVip("*******");
        endpoint1.setPort(3306);
        endpoint1.setUserVisible(true);
        Endpoint endpoint2 = new Endpoint();
        endpoint2.setVip("*******");
        endpoint2.setPort(3306);
        endpoint2.setUserVisible(true);
        endpointListResult.setItems(Arrays.asList(endpoint1, endpoint2));

        when(metaService.getDefaultClient().listReplicaSetEndpoints(any(), any(), any(), any(), any(), any())).thenReturn(endpointListResult);
        when(custinsService.checkHaveMaxscaleService(any())).thenReturn(replicaSet.getName());

        APISuccess apiSuccess = new APISuccess();
        apiSuccess.setStatus(200);
        when(linksApi.configVipConf(any(), any(), any(), any())).thenReturn(new APISuccess());

        Map<String, Object> result = modifyConnectionDrainingService.modifyConnectionDraining(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));

        assertEquals(1L, result.get(ParamConstants.DB_INSTANCE_ID));
        assertEquals("testReplicaSetName", result.get(ParamConstants.DB_INSTANCE_NAME));
    }

    @Test
    public void testDoLinkFailedTest() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("DrainingTimeout", "10");
        params.put("DrainingEnable","1");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
//        replicaSet.setLockMode(ReplicaSet.LockModeEnum.LOCKMANUAL);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setCategory("standard");

        Map<String, String> labels = new HashMap<>();
        labels.put("ALB_CONNECTION_DRAINING_SWITCH", "OFF");
        labels.put("Test111", "ON");
        replicaSet.setLabels(labels);

        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);


        EndpointListResult endpointListResult = new EndpointListResult();
        Endpoint endpoint1 = new Endpoint();
        endpoint1.setVip("*******");
        endpoint1.setPort(3306);
        endpoint1.setUserVisible(true);
        Endpoint endpoint2 = new Endpoint();
        endpoint2.setVip("*******");
        endpoint2.setPort(3306);
        endpoint2.setUserVisible(true);
        endpointListResult.setItems(Arrays.asList(endpoint1, endpoint2));

        when(metaService.getDefaultClient().listReplicaSetEndpoints(any(), any(), any(), any(), any(), any())).thenReturn(endpointListResult);
//        when(custinsService.checkHaveMaxscaleService(any())).thenReturn(replicaSet.getName());

        APISuccess apiSuccess = new APISuccess();
        apiSuccess.setStatus(200);
//        when(modifyConnectionDrainingService.modifyConnectionDraining(any(),any())).thenThrow(new RdsException(ErrorCode.INVALID_PARAM));
        when(linksApi.configVipConf(any(),any(),any(),any())).thenThrow(new com.aliyun.dba.adb_vip_manager_client.ApiException(404,"Invalid params"));

        Map<String, Object> result = modifyConnectionDrainingService.modifyConnectionDraining(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));

        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.INVALID_PARAM.getCode(), errorArray[0]);
    }


    @Test
    public void testMetaAPIFailed() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("DrainingTimeout", "10");
        params.put("DrainingEnable","1");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
//        replicaSet.setLockMode(ReplicaSet.LockModeEnum.LOCKMANUAL);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setCategory("standard");

        Map<String, String> labels = new HashMap<>();
        labels.put("ALB_CONNECTION_DRAINING_SWITCH", "OFF");
        labels.put("Test111", "ON");
        replicaSet.setLabels(labels);

        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);

        when(metaService.getDefaultClient().listReplicaSetEndpoints(any(), any(), any(), any(), any(), any())).thenThrow(new ApiException(404, "Invalid params"));

        Map<String, Object> result = modifyConnectionDrainingService.modifyConnectionDraining(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));

        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(500, errorArray[0]);
    }

}
