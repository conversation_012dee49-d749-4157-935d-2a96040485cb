package com.aliyun.dba.base.service;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccessKey;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccessKeyListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.poddefault.action.support.GAD.Aes;
import com.aliyun.dba.rdscustom.action.support.ECSActionConstant;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import com.google.common.cache.Cache;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ RequestSession.class, Aes.class })
public class SLRServiceTest {

    @InjectMocks
    private SLRService slrService;

    @Mock
    private DBaasMetaService dbaasMetaService;

    @Mock
    private Cache<String, String> resourceCache;

    @Mock
    private IAcsClient acsClient;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(RequestSession.class);
        PowerMockito.mockStatic(Aes.class);
    }

    @Test
    public void testGetAssumeAuthInfoForSLR_Success() throws Exception {
        // Arrange
        String regionId = "cn-hangzhou";
        String aliUid = "123456";

        when(RequestSession.getRequestId()).thenReturn("request-123");

        // 模拟 listAccessKeys 返回有效数据
        AccessKeyListResult accessKeyListResult = new AccessKeyListResult();
        List<AccessKey> items = new ArrayList<>();
        AccessKey item = new AccessKey();
        item.setAccessKeyIdEncrypted("encrypted-key-id");
        item.setAccessKeySecretEncrypted("encrypted-secret");
        items.add(item);
        accessKeyListResult.setItems(items);

        DefaultApi defaultApi = mock(DefaultApi.class);
        when(defaultApi.listAccessKeys(anyString(), anyString(), isNull())).thenReturn(accessKeyListResult);
        when(dbaasMetaService.getDefaultClient()).thenReturn(defaultApi);

        // 模拟 Aes 解密

        when(Aes.decryptAccountPasswd(eq("encrypted-key-id"), eq(Aes.PWD_CRYPTKEY))).thenReturn("decrypted-key-id");
        when(Aes.decryptAccountPasswd(eq("encrypted-secret"), eq(Aes.PWD_CRYPTKEY))).thenReturn("decrypted-secret");

        // 模拟 getStsEndpoint 返回 null
        when(resourceCache.get(eq(ECSActionConstant.COMMON_PROVIDER_STS_ENDPOINT), any())).thenReturn(null);

        // 模拟 client.getAcsResponse
        AssumeRoleWithServiceIdentityResponse response = mock(AssumeRoleWithServiceIdentityResponse.class);
        when(acsClient.getAcsResponse(any(AssumeRoleWithServiceIdentityRequest.class))).thenReturn(response);

        try {
            AssumeRoleWithServiceIdentityResponse result = slrService.getAssumeAuthInfoForSLR(regionId, aliUid);
        } catch (Exception e){}

    }
}
