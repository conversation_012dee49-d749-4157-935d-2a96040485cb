package com.aliyun.dba.base.cache;

import com.aliyun.dba.poddefault.action.service.AligroupService;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

public class LocalCacheServiceTest {

    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void getValue() {
        LocalCacheService cacheService = new LocalCacheService();
        try {
            cacheService.getValue("TEST");
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }

    @Test
    public void getValueOrDefault() {
        LocalCacheService cacheService = new LocalCacheService();
        try {
            cacheService.getValueOrDefault("TEST","DEFAULT");
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }

    @Test
    public void deleteValue() {
        LocalCacheService cacheService = new LocalCacheService();
        try {
            cacheService.deleteValue("TEST");
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }
}