package com.aliyun.dba.base.parameter;

import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class MysqlParameterHelperTest {

    private MysqlParameterHelper mysqlParameterHelper = new MysqlParameterHelper();

    @BeforeEach
    public void setUp() {
        mysqlParameterHelper = new MysqlParameterHelper();
    }

    @Test
    public void testCheckAccountIsReserved_Mysql57_ReservedAccount() {
        // 测试 MySQL 5.7 及以上版本的保留账号
        Assert.assertTrue(mysqlParameterHelper.checkAccountIsReserved("5.7", "aliyun_root"));
        Assert.assertTrue(mysqlParameterHelper.checkAccountIsReserved("8.0", "aliyun_root"));
    }

    @Test
    public void testCheckAccountIsReserved_Mysql57_NonReservedAccount() {
        // 测试 MySQL 5.7 及以上版本的非保留账号
        Assert.assertFalse(mysqlParameterHelper.checkAccountIsReserved("5.7", "myuser"));
        Assert.assertFalse(mysqlParameterHelper.checkAccountIsReserved("8.0", "myuser"));
    }

    @Test
    public void testCheckAccountIsReserved_MysqlLt57_ReservedAccount() {
        // 测试 MySQL 5.7 以下版本的保留账号
        Assert.assertTrue(mysqlParameterHelper.checkAccountIsReserved("5.6", "root"));
    }

    @Test
    public void testCheckAccountIsReserved_MysqlLt57_NonReservedAccount() {
        // 测试 MySQL 5.7 以下版本的非保留账号
        Assert.assertFalse(mysqlParameterHelper.checkAccountIsReserved("5.6", "myuser"));
    }

    @Test
    public void testCheckAccountIsReserved_InvalidVersion() {
        // 测试无效的版本号
        Assert.assertFalse(mysqlParameterHelper.checkAccountIsReserved("invalid", "myuser"));
    }

}