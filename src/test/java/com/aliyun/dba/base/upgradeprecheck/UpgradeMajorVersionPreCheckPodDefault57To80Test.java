package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.WorkFlowService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UpgradeMajorVersionPreCheckPodDefault57To80Test {
    @InjectMocks
    private UpgradeMajorVersionPreCheckPodDefault57To80 upgradeMajorVersionPreCheckPodDefault57To80;
    @Mock
    private WorkFlowService workFlowService;


    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void testCheckUpgradeTask_ClusterCategory_TaskNotExists() throws Exception {
        ReplicaSet replicaSetMeta = new ReplicaSet();
        replicaSetMeta.setCategory("cluster");
        replicaSetMeta.setName("test");
        when(workFlowService.isTaskExist(anyString(), anyString(), anyString())).thenReturn(false);

        upgradeMajorVersionPreCheckPodDefault57To80.checkUpgradeTask(replicaSetMeta, "requestId");
    }
}