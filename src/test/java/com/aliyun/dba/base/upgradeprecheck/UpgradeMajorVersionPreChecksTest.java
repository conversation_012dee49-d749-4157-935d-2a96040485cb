package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UpgradeMajorVersionPreChecksTest {
    @InjectMocks
    private UpgradeMajorVersionPreChecks upgradeMajorVersionPreChecks;


    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void checkSourceCategory_ValidCategories_ShouldPass() throws Exception {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.category("standard");
        upgradeMajorVersionPreChecks.checkSourceCategory(replicaSet);

        replicaSet.category("basic");
        upgradeMajorVersionPreChecks.checkSourceCategory(replicaSet);

        replicaSet.category("cluster");
        upgradeMajorVersionPreChecks.checkSourceCategory(replicaSet);

    }
}