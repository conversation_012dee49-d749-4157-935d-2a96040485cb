package com.aliyun.dba.base.upgradeprecheck;

import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(SpringContextUtil.class)
public class UpgradeMajorVersionPreCheckStrategyFactoryTest {

    @Mock
    private ApplicationContext applicationContext;

    @InjectMocks
    private UpgradeMajorVersionPreCheckStrategyFactory factory;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getApplicationContext()).thenReturn(applicationContext);
    }

    @Test
    public void testGetStrategy_MySQL57To80_NewArchClusterCategory_ReturnsCorrectStrategy() throws RdsException {
        UpgradeMajorVersionPreCheckPodDefault57To80 mockStrategy = Mockito.mock(UpgradeMajorVersionPreCheckPodDefault57To80.class);
        when(applicationContext.getBean(UpgradeMajorVersionPreCheckPodDefault57To80.class)).thenReturn(mockStrategy);

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setKindCode(CustinsSupport.KIND_CODE_NEW_ARCH);
        replicaSet.setCategory(InstanceLevel.CategoryEnum.CLUSTER.toString());

        UpgradeMajorVersionPreCheckStrategy strategy = UpgradeMajorVersionPreCheckStrategyFactory.getStrategy(CustinsSupport.DB_VERSION_MYSQL_57, CustinsSupport.DB_VERSION_MYSQL_80, replicaSet);
        assertTrue(strategy instanceof UpgradeMajorVersionPreCheckPodDefault57To80);
    }

}
