package com.aliyun.dba.base.dataobject;

import java.util.*;
import java.math.*;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class ZoneDOTest {

    @InjectMocks
    private ZoneDO zoneDO;

    private Integer id;
    private String zoneId;
    private String regionId;
    private String siteName;
    private String vgwIp;
    private Date gmtCreated;
    private Date gmtModified;

    @Before
    public void setUp() {
        id = 1;
        zoneId = "testZoneId";
        regionId = "testRegionId";
        siteName = "testSiteName";
        vgwIp = "testVgwIp";
        gmtCreated = new Date();
        gmtModified = new Date();
    }

    @Test
    public void testZoneDO_Constructor_ShouldInitializeFieldsCorrectly() {
        ZoneDO zone = new ZoneDO(id, zoneId, regionId, siteName, vgwIp, gmtCreated, gmtModified);

        assertEquals(id, zone.getId());
        assertEquals(zoneId, zone.getZoneId());
        assertEquals(regionId, zone.getRegionId());
        assertEquals(siteName, zone.getSiteName());
        assertEquals(vgwIp, zone.getVgwIp());
        assertEquals(gmtCreated, zone.getGmtCreated());
        assertEquals(gmtModified, zone.getGmtModified());
    }

    @Test
    public void testZoneDO_SettersAndGetters_ShouldWorkCorrectly() {
        zoneDO.setId(id);
        zoneDO.setZoneId(zoneId);
        zoneDO.setRegionId(regionId);
        zoneDO.setSiteName(siteName);
        zoneDO.setVgwIp(vgwIp);
        zoneDO.setGmtCreated(gmtCreated);
        zoneDO.setGmtModified(gmtModified);

        assertEquals(id, zoneDO.getId());
        assertEquals(zoneId, zoneDO.getZoneId());
        assertEquals(regionId, zoneDO.getRegionId());
        assertEquals(siteName, zoneDO.getSiteName());
        assertEquals(vgwIp, zoneDO.getVgwIp());
        assertEquals(gmtCreated, zoneDO.getGmtCreated());
        assertEquals(gmtModified, zoneDO.getGmtModified());
    }

    @Test
    public void testZoneDO_DefaultConstructor_ShouldInitializeEmptyFields() {
        ZoneDO zone = new ZoneDO();

        assertNotNull(zone);
        assertEquals(null, zone.getId());
        assertEquals(null, zone.getZoneId());
        assertEquals(null, zone.getRegionId());
        assertEquals(null, zone.getSiteName());
        assertEquals(null, zone.getVgwIp());
        assertEquals(null, zone.getGmtCreated());
        assertEquals(null, zone.getGmtModified());
    }
}
