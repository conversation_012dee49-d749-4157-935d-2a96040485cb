package com.aliyun.dba.base.action.service;

import com.aliyun.dba.bak.dataobject.LogPlanDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.RdsApi;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ActionParamsProvider.class})
public class CrossArchServiceTest {
    @InjectMocks
    private CrossArchService crossArchService;
    @Mock
    private ClusterService clusterService;
    @Mock
    private ClusterIDao clusterIDao;
    @Mock
    private ResourceService resourceService;
    @Mock
    private BakService bakService;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;
    @Mock
    private RdsApi rdsApi;
    @Test
    public void test_checkNewArmClusterSupport(){
        ClustersDO clustersDO=new ClustersDO();
        List<ClustersDO> clustersDOList=new ArrayList<>();
        clustersDOList.add(clustersDO);
        when(clusterService.getClusterByCustinsId(any())).thenReturn(clustersDO);
        when(clusterIDao.getClusters(any())).thenReturn(clustersDOList);
        Boolean cross = crossArchService.checkNewArmClusterSupport(1);
        Assert.assertEquals(true,cross);
    }
    @Test
    public void test_checkNewArmClusterSupport_fail(){
        ClustersDO clustersDO=new ClustersDO();
        List<ClustersDO> clustersDOList=new ArrayList<>();
        when(clusterService.getClusterByCustinsId(any())).thenReturn(clustersDO);
        when(clusterIDao.getClusters(any())).thenReturn(clustersDOList);
        Boolean cross = crossArchService.checkNewArmClusterSupport(1);
        Assert.assertEquals(false,cross);
    }
    @Test
    public void test_getLocationSubDomain(){
        ResourceDO resourceDO = new ResourceDO();
        when(resourceService.getResourceByResKey(any())).thenReturn(resourceDO);
        resourceDO.setRealValue("{\"cn-hangzhou\":\"cn-hangzhou-finance\"}");
        String locationSubDomain = crossArchService.getLocationSubDomain("cn-hangzhou");
        Assert.assertEquals("cn-hangzhou-finance",locationSubDomain);

    }
    @Test
    public void test_getLocationSubDomain_not_json() {
        ResourceDO resourceDO = new ResourceDO();
        when(resourceService.getResourceByResKey(any())).thenReturn(resourceDO);
        resourceDO.setRealValue("{\"cn-hangzhou:\"cn-hangzhou-finance\"}");
        String locationSubDomain = crossArchService.getLocationSubDomain("cn-hangzhou");
        Assert.assertNotNull(locationSubDomain);

    }
    @Test
    public void test_validRestoreByTime() throws RdsException {
        CustInstanceDO custInstanceDO=new CustInstanceDO();
        custInstanceDO.setDbType("mysql");
        custInstanceDO.setId(1);
        LogPlanDO logPlanDO=new LogPlanDO();
        Date date=new Date();
        date.setTime(123L);
        logPlanDO.setEnableUploadTime(date);
        logPlanDO.setCharge(1);
        when(bakService.getLogPlanByCustinsId(any())).thenReturn(logPlanDO);
        when(mysqlParameterHelper.getAndCheckTimeByParam(any(),any(),any(),any())).thenReturn(date);
        Date res = crossArchService.validRestoreByTime(custInstanceDO);
        Assert.assertEquals(123L,res.getTime());
    }

    @Test
    public void test_validRestoreByTime_error_dbType() throws RdsException {
        CustInstanceDO custInstanceDO=new CustInstanceDO();
        custInstanceDO.setDbType("myql");
        custInstanceDO.setId(1);
        LogPlanDO logPlanDO=new LogPlanDO();
        Date date=new Date();
        date.setTime(123L);
        logPlanDO.setEnableUploadTime(date);
        logPlanDO.setCharge(1);
        when(bakService.getLogPlanByCustinsId(any())).thenReturn(logPlanDO);
        when(mysqlParameterHelper.getAndCheckTimeByParam(any(),any(),any(),any())).thenReturn(date);
        try {
            Date res = crossArchService.validRestoreByTime(custInstanceDO);
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }
    }
    @Test
    public void test_validRestoreByTime_error_no_logplan() throws RdsException {
        CustInstanceDO custInstanceDO=new CustInstanceDO();
        custInstanceDO.setDbType("mysql");
        custInstanceDO.setId(1);
        LogPlanDO logPlanDO=new LogPlanDO();
        logPlanDO.setCharge(-1);
        Date date=new Date();
        date.setTime(123L);
        when(bakService.getLogPlanByCustinsId(any())).thenReturn(logPlanDO);
        when(mysqlParameterHelper.getAndCheckTimeByParam(any(),any(),any(),any())).thenReturn(date);
        try {
            Date res = crossArchService.validRestoreByTime(custInstanceDO);
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void test_validRestoreByBakset() throws RdsException {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        when(mysqlParameterHelper.getParameterValue(any())).thenReturn("1");
        Long l = crossArchService.validRestoreByBakset(custInstanceDO);
        Long l1 = 1L;
        Assert.assertEquals(l1,l);
    }
    @Test
    public void test_onecsCloneToK8s() throws RdsException {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("ON");
        when(resourceService.getResourceByResKey(any())).thenReturn(resourceDO);
        when(mysqlParameterHelper.getParameterValue(any())).thenReturn("1");
        Map<String,String> m=new HashMap<>();
        Map<String, Object> result =new HashMap<>();
        result.put("isNewArch",true);
        when(rdsApi.getDataByRdsApi(any(),any())).thenReturn(result);
        boolean b = crossArchService.onecsCloneToK8s(m);
        Assert.assertTrue(b);
    }
}
