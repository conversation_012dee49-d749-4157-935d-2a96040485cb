package com.aliyun.dba.base.lib;


import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.support.utils.RequestSession;
import com.google.common.collect.ImmutableMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.APP_NAME;

@RunWith(MockitoJUnitRunner.class)
public class InventoryServiceTest {
    @Mock
    private DBaasMetaService dBaasMetaService;

    @InjectMocks
    private InventoryService inventoryService;


    @Before
    public void setUp() throws Exception {
        DefaultApi dbaasDefaultApi = Mockito.mock(DefaultApi.class);
        Mockito.when(dBaasMetaService.getDefaultClient())
                .thenReturn(dbaasDefaultApi);

        EcsHost ecsHost = new EcsHost();
        Mockito.when(dBaasMetaService.getDefaultClient().getEcsHostByHostName(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(ecsHost);
    }


    @Test
    public void testEvaluateEcsStock() throws Exception {
        Map<String, String> params = ImmutableMap.of("requestid", UUID.randomUUID().toString());
        RequestSession.init(APP_NAME, params);
        ReplicaResource replicaResource = new ReplicaResource();
        Replica replica = new Replica();
        replicaResource.setReplica(replica);

        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCpuCores(4);
        instanceLevel.setMemSizeMB(16 * 1024);

        List<String> result = inventoryService.evaluateEcsStock(instanceLevel, replicaResource, 4L, 16L);
        Assert.assertNotNull(result);
    }
}
