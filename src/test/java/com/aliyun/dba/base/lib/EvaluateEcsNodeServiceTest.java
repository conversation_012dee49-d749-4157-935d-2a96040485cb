package com.aliyun.dba.base.lib;

import com.alicloud.apsaradb.inventory.model.ECSStock;
import com.alicloud.apsaradb.inventory.model.EcsStockResponseData;
import com.alicloud.apsaradb.inventory.model.SpecModificationRequest;
import com.alicloud.apsaradb.inventory.model.SpecModificationResponse;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.poddefault.action.service.PoddefaultResourceGuaranteeModelService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class EvaluateEcsNodeServiceTest {
    @Mock
    private InventoryService inventoryService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private ResourceService resourceService;

    @Mock
    private PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;

    @InjectMocks
    private EvaluateEcsNodeService evaluateEcsNodeService;

    @Before
    public void setUp() throws Exception {
        DefaultApi dbaasDefaultApi = Mockito.mock(DefaultApi.class);
        Mockito.when(dBaasMetaService.getDefaultClient())
                .thenReturn(dbaasDefaultApi);

        EcsHost ecsHost = new EcsHost();
        Mockito.when(dBaasMetaService.getDefaultClient().getEcsHostByHostName(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(ecsHost);

        User user = new User();
        Mockito.when(dBaasMetaService.getDefaultClient().getUser(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(user);

        com.alicloud.apsaradb.inventory.api.DefaultApi inventoryDefaultApi = Mockito.mock(com.alicloud.apsaradb.inventory.api.DefaultApi.class);
        Mockito.when(inventoryService.getDefaultApi())
                .thenReturn(inventoryDefaultApi);
    }

    @Test
    public void testEvaluateReplicasCanLocalUpgrade() throws Exception {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setService("mysql");
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        Replica replica = new Replica();
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCpuCores(4);
        instanceLevel.setMemSizeMB(16 * 1024);

        SpecModificationResponse response = new SpecModificationResponse();
        response.setCode(200L);
        EcsStockResponseData data = new EcsStockResponseData();
        data.add(new ECSStock());
        response.setData(data);
        Mockito.when(inventoryService.getDefaultApi().describeSpecModification(Mockito.any(SpecModificationRequest.class)))
                .thenReturn(response);
        String result = evaluateEcsNodeService.evaluateNodeCanLocalUpgrade("", replicaSet, replica, instanceLevel);
        Assert.assertNull(result);
    }



    @Test
    public void evaluateReplicaSetCanLocalUpgrade_AllowLocalUpgrade() throws Exception {
        InstanceLevel targetLevel = new InstanceLevel();
        targetLevel.setClassCode("ecs.c6.large");
        targetLevel.setCpuCores(2);
        targetLevel.setMemSizeMB(4096);

        DefaultApi defaultApi = Mockito.mock(DefaultApi.class);
        Mockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("[\"123456\"]");
        Mockito.when(resourceService.getResourceByResKey(Mockito.anyString())).thenReturn(resourceDO);

        Replica replica = new Replica();
        replica.setId(1L);
        replica.setHostName("testHostName");

        List<Replica> replicas = Collections.singletonList(replica);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        replicaListResult.setItems(replicas);
        Mockito.when(defaultApi.listReplicasInReplicaSet(Mockito.anyString(), Mockito.anyString(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(replicaListResult);

        EcsHost ecsHost = new EcsHost();
        ecsHost.setEcsInsId("testEcsInsId");
        ecsHost.setClassCode("ecs.c5.large");
        Mockito.when(defaultApi.getEcsHostByHostName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(ecsHost);

        SpecModificationResponse response = new SpecModificationResponse();
        response.setCode(200L);
        EcsStockResponseData data = new EcsStockResponseData();
        ECSStock ecsStock = new ECSStock();
        ecsStock.setInstanceType("ecs.c5.xlarge");
        ecsStock.setStatus("Available");
        ecsStock.setStatusCategory("WithStock");
        data.add(ecsStock);
        response.setData(data);
        Mockito.when(inventoryService.getDefaultApi().describeSpecModification(Mockito.any(SpecModificationRequest.class)))
                .thenReturn(response);

        List<String> limitEcsClaasCodes = Collections.singletonList("ecs.c5.xlarge");
        Mockito.when(inventoryService.evaluateEcsStock(Mockito.any(InstanceLevel.class), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(limitEcsClaasCodes);

        User user = new User();
        user.setAliUid("testAliUid");
        Mockito.when(defaultApi.getUser(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(user);

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("testUserId");
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setService("MySQL");
        assertTrue(evaluateEcsNodeService.evaluateReplicaSetCanLocalUpgrade("testRequestId", replicaSet, targetLevel));

        replicaSet.setUserId("26842_123456");
        assertFalse(evaluateEcsNodeService.evaluateReplicaSetCanLocalUpgrade("testRequestId", replicaSet, targetLevel));

    }
}