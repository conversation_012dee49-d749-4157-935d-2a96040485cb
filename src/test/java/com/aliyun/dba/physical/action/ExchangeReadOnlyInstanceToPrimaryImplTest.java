package com.aliyun.dba.physical.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.support.property.ParamConstants.*;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_NAME;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ExchangeReadOnlyInstanceToPrimaryImplTest {
    @InjectMocks
    private ExchangeReadOnlyInstanceToPrimaryImpl exchangeReadOnlyInstanceToPrimary;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private DbossApi dbossApi;
    @Mock
    private CustinsService custinsService;
    @Mock
    private MySQLService mySQLService;

    @Before
    public void setUp() {
    }

    @Test
    public void doActionRequestSuccessfulTest() throws RdsException, ApiException, IOException {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(REQUEST_ID, "testRequestId");
        actionParams.put(UID, "12345");
        actionParams.put(REGION, "testRegion");
        actionParams.put(DB_INSTANCE_NAME, "testInstanceName");
        actionParams.put("readDbInstanceName", "testReadInstanceName");
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsType(RdsConstants.CUSTINS_INSTYPE_PRIMARY);
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setId(1234);
        when(mysqlParamSupport.getAndCheckReadCustInstance(anyMap())).thenReturn(readCustins);
        when(mysqlParamSupport.getRequiredParameterValue(anyMap(), anyString())).thenReturn("master");

        when(dbossApi.showSlaveStatus(any(), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "Yes");
            put("Slave_SQL_Running", "Yes");
        }});

        when(custinsService.getInstanceIdsByCustinsId(anyInt())).thenReturn(new ArrayList<Integer>(){{
            add(123);
            add(234);
            add(345);
        }});
        when(mySQLService.createExchangeReadInsToPrimaryTask(anyString(), any(), any(), anyInt(), any(), anyString())).thenReturn(0);
        Map<String, Object> result = exchangeReadOnlyInstanceToPrimary.doActionRequest(custins, actionParams);
        assertNotNull(result);
    }

    @Test
    public void doActionRequestSlaveSuccessfulTest() throws RdsException, ApiException, IOException {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(REQUEST_ID, "testRequestId");
        actionParams.put(UID, "12345");
        actionParams.put(REGION, "testRegion");
        actionParams.put(DB_INSTANCE_NAME, "testInstanceName");
        actionParams.put("readDbInstanceName", "testReadInstanceName");
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsType(RdsConstants.CUSTINS_INSTYPE_PRIMARY);
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setId(1234);
        when(mysqlParamSupport.getAndCheckReadCustInstance(anyMap())).thenReturn(readCustins);
        when(mysqlParamSupport.getRequiredParameterValue(anyMap(), anyString())).thenReturn("slave");

        when(dbossApi.showSlaveStatus(eq(123), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "No");
            put("Slave_SQL_Running", "Yes");
        }});
        when(dbossApi.showSlaveStatus(eq(1234), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "Yes");
            put("Slave_SQL_Running", "Yes");
        }});

        when(custinsService.getInstanceIdsByCustinsId(anyInt())).thenReturn(new ArrayList<Integer>(){{
            add(123);
            add(234);
            add(345);
        }});
        when(mySQLService.createExchangeReadInsToPrimaryTask(anyString(), any(), any(), anyInt(), any(), anyString())).thenReturn(0);
        Map<String, Object> result = exchangeReadOnlyInstanceToPrimary.doActionRequest(custins, actionParams);
        assertNotNull(result);
    }

    @Test
    public void doActionRequestNoPrimaryTest() throws RdsException, ApiException, IOException {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(REQUEST_ID, "testRequestId");
        actionParams.put(UID, "12345");
        actionParams.put(REGION, "testRegion");
        actionParams.put(DB_INSTANCE_NAME, "testInstanceName");
        actionParams.put("readDbInstanceName", "testReadInstanceName");
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsType(RdsConstants.CUSTINS_INSTYPE_READ);
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        try {
            exchangeReadOnlyInstanceToPrimary.doActionRequest(custins, actionParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
        assertTrue(true);
    }

    @Test
    public void doActionRequestInvalidParametersTest() throws RdsException, ApiException, IOException {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(REQUEST_ID, "testRequestId");
        actionParams.put(UID, "12345");
        actionParams.put(REGION, "testRegion");
        actionParams.put(DB_INSTANCE_NAME, "testInstanceName");
        actionParams.put("readDbInstanceName", "testReadInstanceName");
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsType(RdsConstants.CUSTINS_INSTYPE_PRIMARY);
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setId(1234);
        when(mysqlParamSupport.getAndCheckReadCustInstance(anyMap())).thenReturn(readCustins);
        when(mysqlParamSupport.getRequiredParameterValue(anyMap(), eq("primaryInsRole"))).thenReturn("logger");
        try {
            exchangeReadOnlyInstanceToPrimary.doActionRequest(custins, actionParams);
        } catch (RdsException e) {
            e.printStackTrace();
        }
        when(mysqlParamSupport.getRequiredParameterValue(anyMap(), eq("primaryInsRole"))).thenReturn("master");
        when(mysqlParamSupport.getRequiredParameterValue(anyMap(), eq("readInsRole"))).thenReturn("logger");
        try {
            exchangeReadOnlyInstanceToPrimary.doActionRequest(custins, actionParams);
        } catch (RdsException e) {
            e.printStackTrace();
        }
        assertTrue(true);
    }

    @Test
    public void doSlaveToSlaveCheckTest() throws RdsException, IOException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsType(RdsConstants.CUSTINS_INSTYPE_PRIMARY);
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setId(1234);
        when(dbossApi.showSlaveStatus(eq(123), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "No");
            put("Slave_SQL_Running", "Yes");
        }});
        when(dbossApi.showSlaveStatus(eq(1234), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "Yes");
            put("Slave_SQL_Running", "Yes");
        }});
        exchangeReadOnlyInstanceToPrimary.doSlaveToSlaveCheck(custins, readCustins);

        when(dbossApi.showSlaveStatus(eq(123), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "Yes");
            put("Slave_SQL_Running", "Yes");
        }});
        when(dbossApi.showSlaveStatus(eq(1234), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "Yes");
            put("Slave_SQL_Running", "Yes");
        }});
        try {
            exchangeReadOnlyInstanceToPrimary.doSlaveToSlaveCheck(custins, readCustins);
        } catch (RdsException e) {
            e.printStackTrace();
        }

        when(dbossApi.showSlaveStatus(eq(123), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "Yes");
            put("Slave_SQL_Running", "Yes");
        }});
        when(dbossApi.showSlaveStatus(eq(1234), any(), any())).thenReturn(new HashMap<>());
        try {
            exchangeReadOnlyInstanceToPrimary.doSlaveToSlaveCheck(custins, readCustins);
        } catch (RdsException e) {
            e.printStackTrace();
        }

        when(dbossApi.showSlaveStatus(eq(123), any(), any())).thenThrow(new IOException());
        try {
            exchangeReadOnlyInstanceToPrimary.doSlaveToSlaveCheck(custins, readCustins);
        } catch (RdsException e) {
            e.printStackTrace();
        }

        when(dbossApi.showSlaveStatus(eq(123), any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("Slave_IO_Running", "No");
            put("Slave_SQL_Running", "Yes");
        }});
        when(dbossApi.showSlaveStatus(eq(1234), any(), any())).thenThrow(new IOException());
        try {
            exchangeReadOnlyInstanceToPrimary.doSlaveToSlaveCheck(custins, readCustins);
        } catch (RdsException e) {
            e.printStackTrace();
        }
        assertTrue(true);
    }
}
