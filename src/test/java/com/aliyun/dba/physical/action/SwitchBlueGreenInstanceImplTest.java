package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/12
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({SwitchBlueGreenInstanceImpl.class})
public class SwitchBlueGreenInstanceImplTest {
    @InjectMocks
    private SwitchBlueGreenInstanceImpl switchBlueGreenInstance;

    @Mock
    private BlueGreenDeploymentService blueGreenDeploymentService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Before
    public void setUp() throws RdsException {
      }

    @Test
    public void testDoActionRequestSuccess() throws Exception {
        // 初始化mock对象
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("greendbinstanceid", "testGreenInstanceId");
        
        // 模拟依赖行为
        Map<String, Object> switchInfo = new HashMap<>();
        switchInfo.put("key", "value");
        when(podParameterHelper.getSwitchInfo(anyMap())).thenReturn(switchInfo);
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", "success");
        when(blueGreenDeploymentService.switchBlueGreenInstance(
            anyString(),
            anyString(),
            anyString(),
            any(CustInstanceDO.class),
            anyString(),
            anyString(),
            anyMap()
        )).thenReturn(result);
        
        // 调用方法
        Map<String, Object> response = switchBlueGreenInstance.doActionRequest(custInstance, params);
        
        // 验证结果
        assertNotNull(response);
        verify(mysqlParamSupport, times(1)).getUID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckRegionID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckCustInstance(anyMap());
        verify(podParameterHelper, times(1)).getSwitchInfo(anyMap());
        verify(blueGreenDeploymentService, times(1)).switchBlueGreenInstance(
            anyString(),
            anyString(),
            anyString(),
            any(CustInstanceDO.class),
            anyString(),
            anyString(),
            anyMap()
        );
    }

    @Test
    public void testDoActionRequestException() throws Exception {
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("greendbinstanceid", "testGreenInstanceId");

        // 模拟依赖行为
        Map<String, Object> switchInfo = new HashMap<>();
        switchInfo.put("key", "value");
        when(podParameterHelper.getSwitchInfo(anyMap())).thenReturn(switchInfo);

        Map<String, Object> result = new HashMap<>();
        result.put("status", "success");
        when(blueGreenDeploymentService.switchBlueGreenInstance(
            anyString(),
            anyString(),
            anyString(),
            any(CustInstanceDO.class),
            anyString(),
            anyString(),
            anyMap()
        )).thenReturn(result);

        // 调用方法
        try {
            Map<String, Object> response = switchBlueGreenInstance.doActionRequest(custInstance, null);
        } catch (Exception e) {
            fail();
        }


    }

    @Test(expected = RdsException.class)
    public void testDoActionRequestRdsException() throws Exception {
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("greendbinstanceid", "testGreenInstanceId");

        // 模拟依赖行为
        Map<String, Object> switchInfo = new HashMap<>();
        switchInfo.put("key", "value");
        when(podParameterHelper.getSwitchInfo(anyMap())).thenReturn(switchInfo);

        Map<String, Object> result = new HashMap<>();
        result.put("status", "success");
        when(blueGreenDeploymentService.switchBlueGreenInstance(
            anyString(),
            anyString(),
            anyString(),
            any(CustInstanceDO.class),
            anyString(),
            anyString(),
            anyMap()
        )).thenThrow(new RdsException(ErrorCode.INTERNAL_FAILURE));

        // 调用方法
        Map<String, Object> response = switchBlueGreenInstance.doActionRequest(custInstance, params);


    }
}