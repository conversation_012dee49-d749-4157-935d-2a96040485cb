package com.aliyun.dba.physical.action;

import com.aliyun.dba.bak.dataobject.ArchivelogListDO;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.CustinsRebuildResourceServiceImpl;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static com.aliyun.dba.custins.support.CustinsSupport.CLUSTER_RESOURCE_MODE_SINGLE;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;


@RunWith(MockitoJUnitRunner.class)
public class RebuildSlaveInstanceImplTest {


    @InjectMocks
    private RebuildSlaveInstanceImpl rebuildSlaveInstance;

    @Mock
    private InstanceService instanceService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private ClusterService clusterService;

    @Mock
    private CustinsRebuildResourceServiceImpl custinsRebuildResourceService;

    @Mock
    private TaskService taskService;

    @Mock
    private IResApi resApi;

    @Mock
    private ResManagerService resManagerService;

    @Mock
    private MycnfService mycnfService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private CommonProviderService commonProviderService;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private BakService bakService;

    @Mock
    private MinorVersionService minorVersionService;


    @Before
    public void setUp() {

    }

    private Method getBakSetMinorVersionMethod() throws Exception {
        Method method = RebuildSlaveInstanceImpl.class.getDeclaredMethod("getBakSetMinorVersion", CustInstanceDO.class, Long.class);
        method.setAccessible(true);
        return method;
    }

    @Test
    public void getBakSetMinorVersionBackupSetNotFoundThrowsException() throws Exception {
        Method method = getBakSetMinorVersionMethod();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        Long backupSetId = 1L;
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(null);
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.BACKUPSET_NOT_FOUND.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getBakSetMinorVersionInvalidBackupSetThrowsException() throws Exception {
        Method method = getBakSetMinorVersionMethod();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("fail");
        bakHistory.setIsAvail(0);
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getBakSetMinorVersionValidBackupSetReturnsMinorVersion() throws Exception {
        Method method = getBakSetMinorVersionMethod();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setSlaveStatus("{\"GTID_PURGED\": \"e909d67e-48cf-11ef-8929-506b4b3fc7f6:1-7473\", \"SERVER_ID\": \"1365310187\", \"BINLOG_FILE\": \"mysql-bin.000012\", \"BACKUP_WORK_MODE\": \"full-scan\", \"BACKUP_TIME\": \"total 3, innodb_data 2, FTWRL_lock 1, innodb_log 3, consistent_time 1721836870\", \"MASTER_HOSTINS_ID\": 6165, \"SLAVE_HINSID\": \"6165\", \"VARIABLES\": {\"innodb_data_file_path\": \"ibdata1:200M:autoextend\", \"innodb_log_file_size\": \"524288000\", \"innodb_undo_tablespaces\": \"2\", \"lower_case_table_names\": \"1\", \"innodb_checksum_algorithm\": \"crc32\", \"version\": \"8.0.25\", \"rds_release_date\": \"20220331\"}, \"START_CHECKPOINT_LSN\": \"48955692\", \"BINLOG_HINSID\": 6177, \"INNODB_TO_LSN\": \"48955692\", \"SLAVE_POS\": \"91391\", \"BACKUP_HOSTINS_ROLE\": \"slave\", \"BINLOG_POS\": \"196\", \"MINOR_VERSION\": \"mysql80_20220331\", \"SLAVE_FILE\": \"mysql-bin.000008\", \"ConsistentTime\": 1721836870}");
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);
        try {
            assertEquals("mysql80_20220331", method.invoke(rebuildSlaveInstance, custins, backupSetId));
        } catch (Exception e) {
            e.printStackTrace();
            fail("Unexpected RdsException to be thrown");
        }
    }

    @Test
    public void getBakSetMinorVersionInvalidSlaveStatusThrowsException() throws Exception {
        Method method = getBakSetMinorVersionMethod();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Method getCheckBakSetValidMethod() throws Exception {
        Method method = RebuildSlaveInstanceImpl.class.getDeclaredMethod("checkBakSetValid", CustInstanceDO.class, Long.class);
        method.setAccessible(true);
        return method;
    }

    @Test
    public void checkBakSetValidBackupSetNotFoundThrowsRdsException() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;

        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(null);
        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.BACKUPSET_NOT_FOUND.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkBakSetValidInvalidBackupSetStatusThrowsRdsException() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        bakHistory.setStatus("abaaba");
        bakHistory.setIsAvail(0);

        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);
        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkBakSetValidInvalidBackupSetTypeThrowsRdsException() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();

        bakHistory.setType("INC");
        bakHistory.setBakWay("O");
        bakHistory.setBakScale(1);

        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);

        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkBakSetValidInvalidIncrementBackupSetThrowsRdsException() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();

        bakHistory.setType("I");
        bakHistory.setBaksetInfo("{}");

        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);

        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkBakSetValidIncrementBackupSetNoExceptionThrown() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setBakType("I");
        bakHistory.setSlaveStatus("{\"GTID_PURGED\": \"b925f50d-4a6c-11ef-b543-1c34da79a93c:1-27126\", \"SERVER_ID\": \"2135377644\", \"BINLOG_FILE\": \"mysql-bin.000001\", \"BACKUP_WORK_MODE\": \"pagetrack\", \"BACKUP_TIME\": \"total 304, innodb_data 2, FTWRL_lock 302, innodb_log 304, consistent_time 1722316733\", \"MASTER_HOSTINS_ID\": 8912307, \"LAST_HIS_ID\": 2151824836, \"INNODB_FROM_LSN\": \"81248552\", \"VARIABLES\": {\"innodb_data_file_path\": \"ibdata1:200M:autoextend\", \"innodb_log_file_size\": \"524288000\", \"innodb_undo_tablespaces\": \"2\", \"lower_case_table_names\": \"1\", \"innodb_checksum_algorithm\": \"crc32\", \"version\": \"8.0.36\", \"rds_release_date\": \"20240615\"}, \"START_CHECKPOINT_LSN\": \"81248552\", \"SLAVE_HINSID\": \"8912307\", \"BINLOG_HINSID\": 8912308, \"INNODB_TO_LSN\": \"81248552\", \"SLAVE_POS\": \"93527\", \"BACKUP_HOSTINS_ROLE\": \"slave\", \"BINLOG_POS\": \"6437\", \"MINOR_VERSION\": \"mysql80_20240615\", \"SLAVE_FILE\": \"mysql-bin.000001\", \"ConsistentTime\": 1722316733}");
        bakHistory.setBaksetInfo("{\"category\": \"standard\", \"last_his_id\": 1L, \"disk_size\": 1814, \"innodb_log_size\": 0, \"ibdata_size\": 209715200, \"compress\": 4, \"binlog_size\": 1614467, \"dbs_size\": 629955959}");
        List<ArchivelogListDO> binlogList = getBinlogListDO();
        when(bakService.getArchivelogByCustinsId(custins.getId(), 1, 1)).thenReturn(binlogList);
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);

        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
        } catch (Exception e) {
            e.printStackTrace();
            fail("Unexpected RdsException thrown");
        }
    }

    @Test
    public void checkBakSetValidInvalidIncrementEmptyBaksetInfoThrowsRdsException() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setBakType("I");
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);

        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkBakSetValidInvalidIncrementNullBakInfoThrowsRdsException() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setBakType("I");
        bakHistory.setBaksetInfo(null);
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);

        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkBakSetValidInvalidIncrementInvalidBaksetInfo1ThrowsRdsException() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setBakType("I");
        bakHistory.setBaksetInfo("{\"category\": \"standard\", \"last_his_id\": 2323, \"disk_size\": 1814, \"innodb_log_size\": 0, \"ibdata_size\": 209715200, \"compress\": 4, \"binlog_size\": 1614467, \"dbs_size\": 629955959}");

        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);

        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkBakSetValidInvalidIncrementInvalidBaksetInfo2ThrowsRdsException() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setBakType("I");
        bakHistory.setBaksetInfo("{\"category\": \"standard\",  \"disk_size\": 1814, \"innodb_log_size\": 0, \"ibdata_size\": 209715200, \"compress\": 4, \"binlog_size\": 1614467, \"dbs_size\": 629955959}");

        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);

        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
            fail("Expected RdsException to be thrown");
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
            assertEquals(ErrorCode.INVALID_BAKSET.getCode(), rdsException.getErrorCode()[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkBakSetValidValidBackupSetNoExceptionThrown() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        List<ArchivelogListDO> binlogList = getBinlogListDO();

        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus("{\"GTID_PURGED\": \"b925f50d-4a6c-11ef-b543-1c34da79a93c:1-27126\", \"SERVER_ID\": \"2135377644\", \"BINLOG_FILE\": \"mysql-bin.000001\", \"BACKUP_WORK_MODE\": \"pagetrack\", \"BACKUP_TIME\": \"total 304, innodb_data 2, FTWRL_lock 302, innodb_log 304, consistent_time 1722316733\", \"MASTER_HOSTINS_ID\": 8912307, \"LAST_HIS_ID\": 2151824836, \"INNODB_FROM_LSN\": \"81248552\", \"VARIABLES\": {\"innodb_data_file_path\": \"ibdata1:200M:autoextend\", \"innodb_log_file_size\": \"524288000\", \"innodb_undo_tablespaces\": \"2\", \"lower_case_table_names\": \"1\", \"innodb_checksum_algorithm\": \"crc32\", \"version\": \"8.0.36\", \"rds_release_date\": \"20240615\"}, \"START_CHECKPOINT_LSN\": \"81248552\", \"SLAVE_HINSID\": \"8912307\", \"BINLOG_HINSID\": 8912308, \"INNODB_TO_LSN\": \"81248552\", \"SLAVE_POS\": \"93527\", \"BACKUP_HOSTINS_ROLE\": \"slave\", \"BINLOG_POS\": \"6437\", \"MINOR_VERSION\": \"mysql80_20240615\", \"SLAVE_FILE\": \"mysql-bin.000001\", \"ConsistentTime\": 1722316733}");
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);
        when(bakService.getArchivelogByCustinsId(custins.getId(), 1, 1)).thenReturn(binlogList);
        Method method = getCheckBakSetValidMethod();
        try {
            method.invoke(rebuildSlaveInstance, custins, backupSetId);
        } catch (Exception e) {
            fail("Unexpected RdsException thrown");
        }
    }

    private CustInstanceDO getCustInstanceDO() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("test");
        custins.setClusterName("cluster");
        custins.setKindCode(18);
        custins.setDbType("rds");
        custins.setDbVersion("8.0");
        custins.setLevelId(1);
        return custins;
    }

    private BakhistoryDO getBakHistoryDO() {
        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setHisId(1L);
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setHostinsId(1);
        bakHistory.setBaksetInfo("{}");
        bakHistory.setSlaveStatus("{}");
        return bakHistory;
    }

    private List<ArchivelogListDO> getBinlogListDO() {
        List<ArchivelogListDO> binlogList = new ArrayList<>();
        ArchivelogListDO binlog = new ArchivelogListDO();
        binlog.setHostinsId(1);
        binlog.setRemoteStatus(2);
        binlogList.add(binlog);
        return binlogList;
    }

    private Method getTargetMinorVersion() throws Exception {
        Method method = RebuildSlaveInstanceImpl.class.getDeclaredMethod("getTargetMinorVersion", Map.class, CustInstanceDO.class);
        method.setAccessible(true);
        return method;
    }

    @Test
    public void getTargetMinorVersionWithBakNoExceptionThrown() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Map<String, String> actionParams = new HashMap<>();
        String targetMinorVersionParam = "rds_20240615";
        actionParams.put("targetMinorVersion", targetMinorVersionParam);
        actionParams.put("mysql80AllowDowngrade", "true");
        actionParams.put("BackupSetId", "1");
        when(mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion")).thenReturn(targetMinorVersionParam);
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionParam)).thenReturn("20240615");
        String classCode = "18";
        String dbEngine = MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL;
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode(classCode);
        when(instanceService.getInstanceLevelByLevelId(custins.getLevelId())).thenReturn(instanceLevelDO);
        when(mysqlParamSupport.isMysqlXDB(custins.getDbType(), custins.getDbVersion(), classCode)).thenReturn(false);
        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(custins.getDbType(), custins.getDbVersion(), classCode, dbEngine, targetMinorVersionParam)).thenReturn("mysql80_20240615");
        when(mysqlParamSupport.getParameterValue(actionParams, "mysql80AllowDowngrade", true)).thenReturn("true");
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        List<ArchivelogListDO> binlogList = getBinlogListDO();
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus("{\"GTID_PURGED\": \"b925f50d-4a6c-11ef-b543-1c34da79a93c:1-27126\", \"SERVER_ID\": \"2135377644\", \"BINLOG_FILE\": \"mysql-bin.000001\", \"BACKUP_WORK_MODE\": \"pagetrack\", \"BACKUP_TIME\": \"total 304, innodb_data 2, FTWRL_lock 302, innodb_log 304, consistent_time 1722316733\", \"MASTER_HOSTINS_ID\": 8912307, \"LAST_HIS_ID\": 2151824836, \"INNODB_FROM_LSN\": \"81248552\", \"VARIABLES\": {\"innodb_data_file_path\": \"ibdata1:200M:autoextend\", \"innodb_log_file_size\": \"524288000\", \"innodb_undo_tablespaces\": \"2\", \"lower_case_table_names\": \"1\", \"innodb_checksum_algorithm\": \"crc32\", \"version\": \"8.0.36\", \"rds_release_date\": \"20240615\"}, \"START_CHECKPOINT_LSN\": \"81248552\", \"SLAVE_HINSID\": \"8912307\", \"BINLOG_HINSID\": 8912308, \"INNODB_TO_LSN\": \"81248552\", \"SLAVE_POS\": \"93527\", \"BACKUP_HOSTINS_ROLE\": \"slave\", \"BINLOG_POS\": \"6437\", \"MINOR_VERSION\": \"mysql80_20240615\", \"SLAVE_FILE\": \"mysql-bin.000001\", \"ConsistentTime\": 1722316733}");
        Method method = getTargetMinorVersion();
        try {
            method.invoke(rebuildSlaveInstance, actionParams, custins);
        } catch (Exception e) {
            fail("Unexpected RdsException thrown");
        }
    }

    @Test
    public void getTargetMinorVersionWithoutBakNoExceptionThrown() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Map<String, String> actionParams = new HashMap<>();
        String targetMinorVersionParam = "rds_20240615";
        actionParams.put("targetMinorVersion", targetMinorVersionParam);
        actionParams.put("mysql80AllowDowngrade", "true");
        actionParams.put("BackupSetId", "1");
        when(mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion")).thenReturn(targetMinorVersionParam);
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionParam)).thenReturn("20240615");
        String classCode = "18";
        String dbEngine = MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL;
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode(classCode);
        when(instanceService.getInstanceLevelByLevelId(custins.getLevelId())).thenReturn(instanceLevelDO);
        when(mysqlParamSupport.isMysqlXDB(custins.getDbType(), custins.getDbVersion(), classCode)).thenReturn(false);
        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(custins.getDbType(), custins.getDbVersion(), classCode, dbEngine, targetMinorVersionParam)).thenReturn("mysql80_20240615");
        when(mysqlParamSupport.getParameterValue(actionParams, "mysql80AllowDowngrade", true)).thenReturn("true");

        Method method = getTargetMinorVersion();
        try {
            method.invoke(rebuildSlaveInstance, actionParams, custins);
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
        } catch (Exception e) {
            fail("Unexpected RdsException thrown");
        }
    }

    @Test
    public void getTargetMinorVersion80WithBakExceptionThrown() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Map<String, String> actionParams = new HashMap<>();
        String targetMinorVersionParam = "rds_20240615";
        actionParams.put("targetMinorVersion", targetMinorVersionParam);
        actionParams.put("mysql80AllowDowngrade", "true");
        actionParams.put("BackupSetId", "1");
        when(mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion")).thenReturn(targetMinorVersionParam);
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionParam)).thenReturn("20240615");
        String classCode = "18";
        String dbEngine = MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL;
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode(classCode);
        when(instanceService.getInstanceLevelByLevelId(custins.getLevelId())).thenReturn(instanceLevelDO);
        when(mysqlParamSupport.isMysqlXDB(custins.getDbType(), custins.getDbVersion(), classCode)).thenReturn(false);
        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(custins.getDbType(), custins.getDbVersion(), classCode, dbEngine, targetMinorVersionParam)).thenReturn("mysql80_20240615");
        when(mysqlParamSupport.getParameterValue(actionParams, "mysql80AllowDowngrade", true)).thenReturn("false");
        when(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.BACKUP_SET_ID)).thenReturn("1");

        Method method = getTargetMinorVersion();
        try {
            method.invoke(rebuildSlaveInstance, actionParams, custins);
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
        }

    }

    @Test
    public void getTargetMinorVersion80WithoutBakExceptionThrown() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Map<String, String> actionParams = new HashMap<>();
        String targetMinorVersionParam = "rds_20240615";
        actionParams.put("targetMinorVersion", targetMinorVersionParam);
        actionParams.put("mysql80AllowDowngrade", "true");
        actionParams.put("BackupSetId", "1");
        when(mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion")).thenReturn(targetMinorVersionParam);
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionParam)).thenReturn("20240615");
        String classCode = "18";
        String dbEngine = MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL;
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode(classCode);
        when(instanceService.getInstanceLevelByLevelId(custins.getLevelId())).thenReturn(instanceLevelDO);
        when(mysqlParamSupport.isMysqlXDB(custins.getDbType(), custins.getDbVersion(), classCode)).thenReturn(false);
        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(custins.getDbType(), custins.getDbVersion(), classCode, dbEngine, targetMinorVersionParam)).thenReturn("mysql80_20240615");
        when(mysqlParamSupport.getParameterValue(actionParams, "mysql80AllowDowngrade", true)).thenReturn("false");
        when(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.BACKUP_SET_ID)).thenReturn(null);

        Method method = getTargetMinorVersion();
        try {
            method.invoke(rebuildSlaveInstance, actionParams, custins);
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            RdsException rdsException = (RdsException) targetException;
            rdsException.printStackTrace();
        }

    }

    @Test
    public void primaryRebuildSlaveInstanceRemoteModeNoExceptionThrown() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        when(clusterService.getClusterResourceMode(custins.getClusterName())).thenReturn(CLUSTER_RESOURCE_MODE_SINGLE);
        Map<String, String> actionParams = new HashMap<>();
        String targetMinorVersionParam = "rds_20240615";
        actionParams.put("targetMinorVersion", targetMinorVersionParam);
        actionParams.put("mysql80AllowDowngrade", "true");
        actionParams.put("BackupSetId", "1");
        when(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.BACKUP_SET_ID)).thenReturn("1");
        Long backupSetId = 1L;
        BakhistoryDO bakHistory = getBakHistoryDO();
        List<ArchivelogListDO> binlogList = getBinlogListDO();
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus("{\"GTID_PURGED\": \"b925f50d-4a6c-11ef-b543-1c34da79a93c:1-27126\", \"SERVER_ID\": \"2135377644\", \"BINLOG_FILE\": \"mysql-bin.000001\", \"BACKUP_WORK_MODE\": \"pagetrack\", \"BACKUP_TIME\": \"total 304, innodb_data 2, FTWRL_lock 302, innodb_log 304, consistent_time 1722316733\", \"MASTER_HOSTINS_ID\": 8912307, \"LAST_HIS_ID\": 2151824836, \"INNODB_FROM_LSN\": \"81248552\", \"VARIABLES\": {\"innodb_data_file_path\": \"ibdata1:200M:autoextend\", \"innodb_log_file_size\": \"524288000\", \"innodb_undo_tablespaces\": \"2\", \"lower_case_table_names\": \"1\", \"innodb_checksum_algorithm\": \"crc32\", \"version\": \"8.0.36\", \"rds_release_date\": \"20240615\"}, \"START_CHECKPOINT_LSN\": \"81248552\", \"SLAVE_HINSID\": \"8912307\", \"BINLOG_HINSID\": 8912308, \"INNODB_TO_LSN\": \"81248552\", \"SLAVE_POS\": \"93527\", \"BACKUP_HOSTINS_ROLE\": \"slave\", \"BINLOG_POS\": \"6437\", \"MINOR_VERSION\": \"mysql80_20240615\", \"SLAVE_FILE\": \"mysql-bin.000001\", \"ConsistentTime\": 1722316733}");
        when(bakService.getBakhistoryByBackupSetId(custins.getId(), backupSetId)).thenReturn(bakHistory);
        when(bakService.getArchivelogByCustinsId(custins.getId(), 1, 1)).thenReturn(binlogList);
        when(mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion")).thenReturn(targetMinorVersionParam);
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionParam)).thenReturn("20240615");
        String classCode = "18";
        String dbEngine = MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL;
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode(classCode);
        when(instanceService.getInstanceLevelByLevelId(custins.getLevelId())).thenReturn(instanceLevelDO);
        when(mysqlParamSupport.isMysqlXDB(custins.getDbType(), custins.getDbVersion(), classCode)).thenReturn(false);
        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(custins.getDbType(), custins.getDbVersion(), classCode, dbEngine, targetMinorVersionParam)).thenReturn("mysql80_20240615");
        when(mysqlParamSupport.getParameterValue(actionParams, "mysql80AllowDowngrade", true)).thenReturn("false");
        InstanceDO instanceDO = new InstanceDO();
        when(instanceService.getInstanceByInsId(-1)).thenReturn(instanceDO);
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setPrimaryCustinsId(custins.getId());
        custInstanceQuery.setIsTmp(1);
        when(custinsService.getCustIns(any())).thenReturn(new LinkedList<CustInstanceDO>() {{
            add(getCustInstanceDO());
        }});
        when(mysqlParamSupport.getParameterValue(actionParams, "IsForce", "false")).thenReturn("true");
        rebuildSlaveInstance.primaryRebuildSlaveInstanceRemoteMode(custins, actionParams);
        assertTrue(true);
    }

    @Test
    public void primaryRebuildSlaveInstanceLocalModeNoExceptionThrown() throws Exception {
        CustInstanceDO custins = getCustInstanceDO();
        Map<String, String> actionParams = new HashMap<>();
        String targetMinorVersionParam = "rds_20240615";
        actionParams.put("targetMinorVersion", targetMinorVersionParam);
        actionParams.put("mysql80AllowDowngrade", "true");
        when(mysqlParamSupport.getParameterValue(actionParams, "IsForce", "false")).thenReturn("true");
        when(mysqlParamSupport.getParameterValue(actionParams, "TargetMinorVersion")).thenReturn(targetMinorVersionParam);
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(targetMinorVersionParam)).thenReturn("20240615");
        String classCode = "18";
        String dbEngine = MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL;
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode(classCode);
        when(instanceService.getInstanceLevelByLevelId(custins.getLevelId())).thenReturn(instanceLevelDO);
        when(mysqlParamSupport.isMysqlXDB(custins.getDbType(), custins.getDbVersion(), classCode)).thenReturn(false);
        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(custins.getDbType(), custins.getDbVersion(), classCode, dbEngine, targetMinorVersionParam)).thenReturn("mysql80_20240615");
        when(mysqlParamSupport.getParameterValue(actionParams, "mysql80AllowDowngrade", true)).thenReturn("true");
        rebuildSlaveInstance.rebuildSlaveInstanceLocalMode(actionParams, custins, null);
        assertTrue(true);
    }


}
