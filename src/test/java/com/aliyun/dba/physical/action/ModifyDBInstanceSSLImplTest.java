package com.aliyun.dba.physical.action;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/25
 */

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.consts.SSLConsts;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.CAServerApiExt;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.instance.service.InstanceServiceImpl;
import com.aliyun.dba.physical.action.ModifyDBInstanceSSLImpl;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.user.service.UserServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_SWITCH;
import static com.aliyun.dba.task.support.TaskSupport.TASK_MODIFY_SSL;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ActionParamsProvider.class, RequestSession.class, CheckUtils.class, CustinsParamSupport.class})
public class ModifyDBInstanceSSLImplTest {

    @InjectMocks
    private ModifyDBInstanceSSLImpl modifyDBInstanceSSLImpl;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private CaServerApi caServerApi;

    @Mock
    private CAServerApiExt caServerApiExt;

    @Mock
    private UserServiceImpl userService;

    @Mock
    private CustinsServiceImpl custinsService;

    @Mock
    private InstanceServiceImpl instanceService;

    @Mock
    private ConnAddrCustinsService connAddrCustinsService;

    @Mock
    private WorkFlowService workFlowService;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;
    @Mock
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Mock
    private TaskService taskService;
    @Mock
    private CustinsIDao custinsIDao;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(CheckUtils.class);
        PowerMockito.mockStatic(CustinsParamSupport.class);
        PowerMockito.when(CheckUtils.checkNullForConnectionString(any())).thenReturn("validConnectionString");
    }

    @Test
    public void doActionRequest_ForceEncryption_Success() throws Exception {
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("mysql:20241224");
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.REQUEST_ID, "requestId");
        actionParams.put(ParamConstants.UID, "uid");
        actionParams.put(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
        actionParams.put(ParamConstants.FORCE_ENCRYPTION, SSLConsts.FORCE_ENCRYPTION);
        actionParams.put(ParamConstants.CONNECTION_STRING, "validConnectionString");
        actionParams.put(ParamConstants.SSL_ENABLED, ParamConstants.SSL_VALUE_ENABLED);
        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        custInstanceDO.setId(1);
        Map<String, Object> taskParamMap = new HashMap<String, Object>();
        TaskQueueDO taskQueue = new TaskQueueDO(getAction(actionParams), getOperatorId(actionParams), custInstanceDO.getId(), TASK_TYPE_CUSTINS,
                TASK_MODIFY_SSL, JSON.toJSONString(taskParamMap));
        when(mysqlParamSupport.getAndCheckCustInstance(actionParams)).thenReturn(custInstanceDO);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);
        when(custInstanceDO.isMysql51()).thenReturn(false);
        when(custInstanceDO.isShare()).thenReturn(false);
        when(custInstanceDO.isCustinsOnEcs()).thenReturn(false);
        when(mysqlParamSupport.getParameterValue(actionParams,ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN)).thenReturn(SSLConsts.CA_TYPE_ALIYUN);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(podParameterHelper.getParameterValue(anyString())).thenReturn("value");
        when(defaultApi.getReplicaSet(anyString(), anyString(), any())).thenReturn(mock(ReplicaSet.class));
        when(caServerApi.getCAServerConfig(anyString())).thenReturn(Collections.emptyMap());
        when(replicaSetService.isReplicaSetXDB(anyString(), anyString())).thenReturn(true);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.singletonList(mock(CustinsConnAddrDO.class)));
        when(custinsIDao.updateCustInstanceStatusByCustinsId(anyInt(), eq(CUSTINS_STATUS_SWITCH),eq(CustinsState.STATE_SSL_MODIFYING.getComment()))).thenReturn(0);
        // 准备
        when(custinsParamService.getCustinsParam(any(), eq("minor_version"))).thenReturn(custinsParamDO);
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.SSL_ENABLED))).thenReturn("0");
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.FORCE_ENCRYPTION),any())).thenReturn("0");
        Map<String, Object> result = modifyDBInstanceSSLImpl.doActionRequest(custInstanceDO, actionParams);

        assertEquals(null, result.get("TaskId"));
    }


    @Test
    public void doActionRequest_ForceEncryption_1_Success() throws Exception {
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("mysql_20241224");
        String requestId = "requestId";
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.REQUEST_ID, "requestId");
        actionParams.put(ParamConstants.UID, "uid");
        actionParams.put(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
        actionParams.put(ParamConstants.FORCE_ENCRYPTION, SSLConsts.FORCE_ENCRYPTION);
        actionParams.put(ParamConstants.CONNECTION_STRING, "validConnectionString");
        actionParams.put(ParamConstants.SSL_ENABLED, ParamConstants.SSL_VALUE_ENABLED);
        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        custInstanceDO.setId(1);
        Map<String, Object> taskParamMap = new HashMap<String, Object>();
        TaskQueueDO taskQueue = new TaskQueueDO(getAction(actionParams), getOperatorId(actionParams), custInstanceDO.getId(), TASK_TYPE_CUSTINS,
                TASK_MODIFY_SSL, JSON.toJSONString(taskParamMap));
        when(mysqlParamSupport.getAndCheckCustInstance(actionParams)).thenReturn(custInstanceDO);
        when(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID)).thenReturn(requestId);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);
        when(custInstanceDO.isMysql51()).thenReturn(false);
        when(custInstanceDO.isShare()).thenReturn(false);
        when(custInstanceDO.isCustinsOnEcs()).thenReturn(false);
        when(custInstanceDO.isMysql57()).thenReturn(true);
        when(custInstanceDO.isMysql80()).thenReturn(true);
        when(mysqlParamSupport.getParameterValue(actionParams,ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN)).thenReturn(SSLConsts.CA_TYPE_ALIYUN);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(podParameterHelper.getParameterValue(anyString())).thenReturn("value");
        when(defaultApi.getReplicaSet(anyString(), anyString(), any())).thenReturn(mock(ReplicaSet.class));
        when(caServerApi.getCAServerConfig(anyString())).thenReturn(Collections.emptyMap());
        when(replicaSetService.isReplicaSetXDB(anyString(), anyString())).thenReturn(true);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.singletonList(mock(CustinsConnAddrDO.class)));
        when(custinsIDao.updateCustInstanceStatusByCustinsId(anyInt(), eq(CUSTINS_STATUS_SWITCH),eq(CustinsState.STATE_SSL_MODIFYING.getComment()))).thenReturn(0);
        // 准备
        List<Config> configs = new ArrayList();
        Config config = new Config();
        config.setKey(PodDefaultConstants.MYSQL_SSL_FORCE_ENCRYPTION_SWITCH);
        config.setValue("20241130");
        configs.add(config);
        ConfigListResult configListResult = new ConfigListResult();
        configListResult.setItems(configs);
        when(defaultApi.listConfigs(eq(requestId), eq(PodDefaultConstants.MYSQL_SSL_FORCE_ENCRYPTION_SWITCH))).thenReturn(configListResult);
        when(mysqlEngineCheckService.checkMinorVersionWithMaxScale(any())).thenReturn(false);
        when(custinsParamService.getCustinsParam(any(), eq("minor_version"))).thenReturn(custinsParamDO);
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.SSL_ENABLED))).thenReturn("1");
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.FORCE_ENCRYPTION),any())).thenReturn("1");
        Map<String, Object> result = modifyDBInstanceSSLImpl.doActionRequest(custInstanceDO, actionParams);
        Object[] errorCode = (Object[])result.get("errorCode");
        assertNotNull(errorCode);
        assertEquals(ErrorCode.UNSUPPORTED_MAXSCALE_MINOR_VERSION.getCode(), errorCode[0]);
    }

    @Test
    public void doActionRequest_ForceEncryption_NotNull_Success() throws Exception {
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("mysql:20241224");
        CustinsParamDO forceEncryptionParamDO = new CustinsParamDO();
        forceEncryptionParamDO.setValue("0");
        CustinsParamDO sslParamDO = new CustinsParamDO();
        sslParamDO.setValue("1");
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.REQUEST_ID, "requestId");
        actionParams.put(ParamConstants.UID, "uid");
        actionParams.put(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
        actionParams.put(ParamConstants.FORCE_ENCRYPTION, SSLConsts.FORCE_ENCRYPTION);
        actionParams.put(ParamConstants.CONNECTION_STRING, "validConnectionString");
        actionParams.put(ParamConstants.SSL_ENABLED, ParamConstants.SSL_VALUE_ENABLED);
        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        custInstanceDO.setId(1);
        Map<String, Object> taskParamMap = new HashMap<String, Object>();
        TaskQueueDO taskQueue = new TaskQueueDO(getAction(actionParams), getOperatorId(actionParams), custInstanceDO.getId(), TASK_TYPE_CUSTINS,
                TASK_MODIFY_SSL, JSON.toJSONString(taskParamMap));
        when(mysqlParamSupport.getAndCheckCustInstance(actionParams)).thenReturn(custInstanceDO);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);
        when(custInstanceDO.isMysql51()).thenReturn(false);
        when(custInstanceDO.isShare()).thenReturn(false);
        when(custInstanceDO.isCustinsOnEcs()).thenReturn(false);
        when(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN)).thenReturn(SSLConsts.CA_TYPE_ALIYUN);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(podParameterHelper.getParameterValue(anyString())).thenReturn("value");
        when(defaultApi.getReplicaSet(anyString(), anyString(), any())).thenReturn(mock(ReplicaSet.class));
        when(caServerApi.getCAServerConfig(anyString())).thenReturn(Collections.emptyMap());
        when(replicaSetService.isReplicaSetXDB(anyString(), anyString())).thenReturn(true);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.singletonList(mock(CustinsConnAddrDO.class)));
        when(custinsIDao.updateCustInstanceStatusByCustinsId(anyInt(), eq(CUSTINS_STATUS_SWITCH), eq(CustinsState.STATE_SSL_MODIFYING.getComment()))).thenReturn(0);
        // 准备
        when(custinsParamService.getCustinsParam(any(), eq(CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL))).thenReturn(sslParamDO);
        when(custinsParamService.getCustinsParam(any(), eq("minor_version"))).thenReturn(custinsParamDO);
        when(custinsParamService.getCustinsParam(any(), eq(CustinsParamSupport.CUSTINS_PARAM_NAME_FORCE_ENCRYPTION))).thenReturn(forceEncryptionParamDO);
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.SSL_ENABLED))).thenReturn("1");
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.FORCE_ENCRYPTION), any())).thenReturn("1");
        Map<String, Object> result = modifyDBInstanceSSLImpl.doActionRequest(custInstanceDO, actionParams);

        assertEquals(null, result.get("TaskId"));
    }

    @Test
    public void doActionRequest_ForceEncryption_Null_Success() throws Exception {
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("mysql:20241224");
        CustinsParamDO forceEncryptionParamDO = new CustinsParamDO();
        forceEncryptionParamDO.setValue(null);
        CustinsParamDO sslParamDO = new CustinsParamDO();
        sslParamDO.setValue("1");
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.REQUEST_ID, "requestId");
        actionParams.put(ParamConstants.UID, "uid");
        actionParams.put(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
        actionParams.put(ParamConstants.FORCE_ENCRYPTION, SSLConsts.FORCE_ENCRYPTION);
        actionParams.put(ParamConstants.CONNECTION_STRING, "validConnectionString");
        actionParams.put(ParamConstants.SSL_ENABLED, ParamConstants.SSL_VALUE_ENABLED);
        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        custInstanceDO.setId(1);
        Map<String, Object> taskParamMap = new HashMap<String, Object>();
        TaskQueueDO taskQueue = new TaskQueueDO(getAction(actionParams), getOperatorId(actionParams), custInstanceDO.getId(), TASK_TYPE_CUSTINS,
                TASK_MODIFY_SSL, JSON.toJSONString(taskParamMap));
        when(mysqlParamSupport.getAndCheckCustInstance(actionParams)).thenReturn(custInstanceDO);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);
        when(custInstanceDO.isMysql51()).thenReturn(false);
        when(custInstanceDO.isShare()).thenReturn(false);
        when(custInstanceDO.isCustinsOnEcs()).thenReturn(false);
        when(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN)).thenReturn(SSLConsts.CA_TYPE_ALIYUN);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(podParameterHelper.getParameterValue(anyString())).thenReturn("value");
        when(defaultApi.getReplicaSet(anyString(), anyString(), any())).thenReturn(mock(ReplicaSet.class));
        when(caServerApi.getCAServerConfig(anyString())).thenReturn(Collections.emptyMap());
        when(replicaSetService.isReplicaSetXDB(anyString(), anyString())).thenReturn(true);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.singletonList(mock(CustinsConnAddrDO.class)));
        when(custinsIDao.updateCustInstanceStatusByCustinsId(anyInt(), eq(CUSTINS_STATUS_SWITCH), eq(CustinsState.STATE_SSL_MODIFYING.getComment()))).thenReturn(0);
        // 准备
        when(custinsParamService.getCustinsParam(any(), eq(CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL))).thenReturn(sslParamDO);
        when(custinsParamService.getCustinsParam(any(), eq("minor_version"))).thenReturn(custinsParamDO);
        when(custinsParamService.getCustinsParam(any(), eq(CustinsParamSupport.CUSTINS_PARAM_NAME_FORCE_ENCRYPTION))).thenReturn(forceEncryptionParamDO);
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.SSL_ENABLED))).thenReturn("1");
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.FORCE_ENCRYPTION), any())).thenReturn("1");
        Map<String, Object> result = modifyDBInstanceSSLImpl.doActionRequest(custInstanceDO, actionParams);

        assertEquals(null, result.get("TaskId"));
    }
}
