package com.aliyun.dba.physical.action;

import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.service.WhitelistTemplateService;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsIpWhiteListDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.CustinsRebuildResourceServiceImpl;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.physical.action.support.RecoveryHelper;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsSupport.getTaskQueueParam;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RecoverDBInstanceImplTest {
    @Spy
    @InjectMocks
    private RecoverDBInstanceImpl recoverDBInstance;
    @Mock
    protected MysqlParamSupport mysqlParamSupport;
    @Mock
    private RecoveryHelper recoveryHelper;
    @Mock
    protected InstanceService instanceService;
    @Mock
    protected CustinsService custinsService;
    @Mock
    private InstanceIDao instanceIDao;
    @Mock
    private CustinsIDao custinsIDao;
    @Mock
    protected ClusterService clusterService;
    @Mock
    protected CustinsParamService custinsParamService;
    @Mock
    protected CustinsRebuildResourceServiceImpl custinsRebuildResourceService;
    @Mock
    protected TaskService taskService;
    @Mock
    protected MySQLService mySQLService;
    @Mock
    protected IResApi resApi;
    @Mock
    private ResManagerService resManagerService;
    @Mock
    protected MycnfService mycnfService;
    @Mock
    protected ReplicaSetService replicaSetService;
    @Mock
    protected CommonProviderService commonProviderService;
    @Mock
    protected DBaasMetaService dBaasMetaService;
    @Mock
    protected WorkFlowService workFlowService;
    @Mock
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    protected MysqlParamGroupHelper mysqlParamGroupHelper;
    @Mock
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Mock
    protected BakService bakService;
    @Mock
    protected AVZSupport avzSupport;
    @Mock
    protected IpWhiteListService ipWhiteListService;
    @Mock
    private WhitelistTemplateService whitelistTemplateService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    public CustInstanceDO dbInstance_main_standard80(){
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");
        custins.setLevelId(12345);
        custins.setKindCode(0);
        custins.setIsTmp(0);
        custins.setId(123);
        custins.setStatus(1);
        custins.setLockMode(0);
        custins.setBizType("aliyun");
        custins.setInsType(0);
        custins.setConnType("lvs");
        custins.setParentId(0);
        custins.setPrimaryCustinsId(0);
        custins.setInsName("test_ins001");
        custins.setDiskSize(102400L);
        custins.setUserId(2222);

        return custins;
    }

    @Test
    public void test_doActionRequest01() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");

        CustInstanceDO custins = dbInstance_main_standard80();
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        InstanceDO recoveryInstanceNode = new InstanceDO();

        when(mysqlParamSupport.getAndCheckCustInstance(params)).thenReturn(custins);
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(instanceLevel);
        doNothing().when(recoveryHelper).checkSupportInstance(custins, instanceLevel);

        doReturn(recoveryInstanceNode).when(recoverDBInstance).getAndCheckRecoveryInstanceNode(any(), any(), anyString());
        doReturn(ImmutableMap.of("data", "ok", "taskId", 321)).when(recoverDBInstance).recoverySlaveNode(any(), any(), any(), any());

        Object o = recoverDBInstance.doActionRequest(custins, params);
        Assert.assertNotNull(o);
    }

    @Test
    public void test_doActionRequest02() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("sitename", "nm125");
        params.put("backupsetid", "2159401918");
        params.put("changemaster", "false");
        params.put("recoverytype", "ins");

        CustInstanceDO custins = dbInstance_main_standard80();
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        InstanceDO recoveryInstanceNode = new InstanceDO();

        Object o = recoverDBInstance.doActionRequest(custins, params);
        Assert.assertNull(o);
    }

    @Test
    public void test_doActionRequest03() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("sitename", "nm125");
        params.put("backupsetid", "2159401918");
        params.put("changemaster", "false");
        params.put("recoverytype", "clone");

        CustInstanceDO custins = dbInstance_main_standard80();
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        InstanceDO recoveryInstanceNode = new InstanceDO();

        Object o = recoverDBInstance.doActionRequest(custins, params);
        Assert.assertNull(o);
    }

    @Test
    public void test_doActionRequest04() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("sitename", "nm125");
        params.put("backupsetid", "2159401918");
        params.put("changemaster", "false");
        params.put("recoverytype", "error");

        CustInstanceDO custins = dbInstance_main_standard80();
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        InstanceDO recoveryInstanceNode = new InstanceDO();

        Object o = recoverDBInstance.doActionRequest(custins, params);
        Assert.assertNotNull(o);
    }

    @Test
    public void test_doActionRequest05() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("sitename", "nm125");
        params.put("backupsetid", "2159401918");
        params.put("changemaster", "false");
        params.put("recoverytype", "deleted");

        CustInstanceDO custins = dbInstance_main_standard80();
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        InstanceDO recoveryInstanceNode = new InstanceDO();

        Object o = recoverDBInstance.doActionRequest(custins, params);
        Assert.assertNotNull(o);
    }

    @Test
    public void test_getAndCheckRecoveryInstanceNode() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
        params.put("instanceid", "222");

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);
        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        Object o = recoverDBInstance.getAndCheckRecoveryInstanceNode(custins, params, "slave");
        Assert.assertNotNull(o);
    }

    @Test
    public void test_getAndCheckRecoveryInstanceNode2() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
//        params.put("instanceid", "222");

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);
        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        Object o = recoverDBInstance.getAndCheckRecoveryInstanceNode(custins, params, "slave");
        Assert.assertNotNull(o);
    }

    @Test(expected = RdsException.class)
    public void test_getAndCheckRecoveryInstanceNode3() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
        params.put("instanceid", "222");

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave, slave);
        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        Object o = recoverDBInstance.getAndCheckRecoveryInstanceNode(custins, params, "slave");
        Assert.assertNotNull(o);
    }

    @Test
    public void test_getAndCheckRecoveryInstanceNode4() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
        params.put("instanceid", "222");

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);
        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        Object o = recoverDBInstance.getAndCheckRecoveryInstanceNode(custins, params, "error");
        Assert.assertNull(o);
    }

    @Test
    public void test_getAndCheckRecoveryInstanceNode5() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
//        params.put("instanceid", "222");

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);
        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        Object o = recoverDBInstance.getAndCheckRecoveryInstanceNode(custins, params, "slave");
        Assert.assertNotNull(o);
    }

    @Test(expected = RdsException.class)
    public void test_getAndCheckRecoveryInstanceNode6() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
//        params.put("instanceid", "222");

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);
        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        Object o = recoverDBInstance.getAndCheckRecoveryInstanceNode(custins, params, "master");
        Assert.assertNotNull(o);
    }

    @Test
    public void test_recoveryIns() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, String> params = new HashMap<>();

        Object o = recoverDBInstance.recoveryIns(custins, params);
        Assert.assertNull(o);
    }

    @Test
    public void test_cloneForRecovery() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, String> params = new HashMap<>();

        Object o = recoverDBInstance.cloneForRecovery(custins, params);
        Assert.assertNull(o);
    }

    @Test
    public void test_createTransListForRebuildSlave() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);
        when(instanceIDao.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        Object o = recoverDBInstance.createTransListForRebuildSlave(custins, custins, 222, instanceDOList, true);
        Assert.assertNotNull(o);
    }

    @Test
    public void test_recoverySlaveNode1() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("backupsetid", "2159401918");
        params.put("ChangeMaster", "false");
        CustInstanceDO custins = dbInstance_main_standard80();

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);

        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        InstanceDO recoveryInstanceNode = new InstanceDO();

        when(clusterService.getClusterResourceMode(anyString())).thenReturn(2);

        when(recoveryHelper.getTargetMinorVersion(any(), any())).thenReturn("rds_20250331");
        when(mysqlParamSupport.getResourceStrategy(any())).thenReturn("resx");

        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        when(custinsService.countCustIns(any())).thenReturn(0L);
        doNothing().when(custinsIDao).createCustInstance(any());
        doNothing().when(custinsParamService).createCustinsParam(any());

        ResourceContainer resourceContainer = new ResourceContainer("cn-beijing", "mysql");
        resourceContainer.setSubDomain("cn-beijing");
        resourceContainer.setAccessId("xxxx");

        when(custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(any(), any(),
                any(), any(), any(), any(), any())).thenReturn(resourceContainer);

        Response response = new Response();
        response.setCode(200);

        when(resManagerService.allocateRes(any())).thenReturn(response);
        when(instanceIDao.getInstanceByCustinsId(any())).thenReturn(instanceDOList);

        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(anyString())).thenReturn("20250331");

        doNothing().when(taskService).updateTaskPenginePolicy(anyInt(), anyInt());

        Object o = recoverDBInstance.recoverySlaveNode(custins, params, null, slave);
        Assert.assertNotNull(o);
    }

    @Test(expected = RdsException.class)
    public void test_recoverySlaveNode2() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("backupsetid", "2159401918");
        params.put("ChangeMaster", "false");
        CustInstanceDO custins = dbInstance_main_standard80();

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        when(clusterService.getClusterResourceMode(anyString())).thenReturn(1);

        Object o = recoverDBInstance.recoverySlaveNode(custins, params, null, slave);
        Assert.assertNotNull(o);
    }

    @Test
    public void test_recoverySlaveNode3() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("backupsetid", "2159401918");
        params.put("ChangeMaster", "false");
        CustInstanceDO custins = dbInstance_main_standard80();

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);

        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        InstanceDO recoveryInstanceNode = new InstanceDO();

        when(clusterService.getClusterResourceMode(anyString())).thenReturn(2);

        when(recoveryHelper.getTargetMinorVersion(any(), any())).thenReturn("rds_20250331");
        when(mysqlParamSupport.getResourceStrategy(any())).thenReturn("resx");

        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(instanceDOList);

        when(custinsService.countCustIns(any())).thenReturn(0L);
        doNothing().when(custinsIDao).createCustInstance(any());
        doNothing().when(custinsParamService).createCustinsParam(any());

        ResourceContainer resourceContainer = new ResourceContainer("cn-beijing", "mysql");
        resourceContainer.setSubDomain("cn-beijing");
        resourceContainer.setAccessId("xxxx");

        when(custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(any(), any(),
                any(), any(), any(), any(), any())).thenReturn(resourceContainer);

        Response response = new Response();
        response.setCode(400);

        when(resManagerService.allocateRes(any())).thenReturn(response);

        doNothing().when(custinsService).deleteCustInstance(any());

        Object o = recoverDBInstance.recoverySlaveNode(custins, params, null, slave);
        Assert.assertNotNull(o);
    }

    @Test(expected = Exception.class)
    public void test_recoverySlaveNode4() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("backupsetid", "2159401918");
        params.put("ChangeMaster", "false");
        CustInstanceDO custins = dbInstance_main_standard80();

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);

        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        InstanceDO recoveryInstanceNode = new InstanceDO();

        ResourceContainer resourceContainer = new ResourceContainer("cn-beijing", "mysql");
        resourceContainer.setSubDomain("cn-beijing");
        resourceContainer.setAccessId("xxxx");

        Response response = new Response();
        response.setCode(200);

        doThrow(new Exception("error")).when(taskService).updateTaskPenginePolicy(anyInt(), anyInt());

        Object o = recoverDBInstance.recoverySlaveNode(custins, params, null, slave);
        Assert.assertNotNull(o);
    }

    @Test
    public void test_recoveryDeleteIns1() throws Exception{
        // Arrange
        Map<String, String> params = new HashMap<>();
        CustInstanceDO custins = dbInstance_main_standard80();
        Map<String, Object> result = recoverDBInstance.recoveryDeleteIns(custins, params);
        Assert.assertNotNull(result);
    }

    @Test
    public void test_recoveryDeleteIns2() throws Exception{
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("DBInstanceClass", "rds.mysql.s2.large");
        params.put("sourcedbinstanceid", "1234");
        CustInstanceDO custins = dbInstance_main_standard80();
        CustInstanceDO srcCustins = dbInstance_main_standard80();
        srcCustins.setIsDeleted(0);

        //when(mysqlParamSupport.getAndCheckCustInstanceById(params, "sourcedbinstanceid")).thenReturn(srcCustins);
        Map<String, Object> result = recoverDBInstance.recoveryDeleteIns(custins, params);
        Assert.assertNotNull(result);
    }

    @Test
    public void test_recoveryDeleteIns3() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("DBInstanceClass".toLowerCase(), "rds.mysql.s2.large");
        params.put("sourcedbinstanceid", "1234");
        CustInstanceDO custins = dbInstance_main_standard80();
        CustInstanceDO srcCustins = dbInstance_main_standard80();
        srcCustins.setIsDeleted(1);

        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS)).thenReturn("class.1");
        when(mysqlParamSupport.getSourceDBInstanceID(params)).thenReturn("ok");
        when(mysqlParamSupport.getAndCheckCustInstanceById(params, "sourcedbinstanceid")).thenReturn(srcCustins);
        InstanceLevelDO insLevel = new InstanceLevelDO();
        insLevel.setHostType(1);
        insLevel.setId(123);

        when(instanceService.getInstanceLevelByClassCode(any(), any(), any(),
                any(), any())).thenReturn(insLevel);

        Map<String, Object> result = recoverDBInstance.recoveryDeleteIns(custins, params);
        Assert.assertNotNull(result);
    }

    @Test
    public void test_recoveryDeleteIns4() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("DBInstanceClass".toLowerCase(), "rds.mysql.s2.large");
        params.put("sourcedbinstanceid", "1234");
        CustInstanceDO custins = dbInstance_main_standard80();
        CustInstanceDO srcCustins = dbInstance_main_standard80();
        srcCustins.setIsDeleted(1);

        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS)).thenReturn("class.1");
        when(mysqlParamSupport.getSourceDBInstanceID(params)).thenReturn("ok");
        when(mysqlParamSupport.getAndCheckCustInstanceById(params, "sourcedbinstanceid")).thenReturn(srcCustins);
        InstanceLevelDO insLevel = new InstanceLevelDO();
        insLevel.setHostType(0);
        insLevel.setId(123);
        insLevel.setIsolateHost(2);

        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(insLevel);

        when(instanceService.getInstanceLevelByClassCode(any(), any(), any(), any(), any())).thenReturn(insLevel);
        doReturn(123456L).when(recoverDBInstance).recoveryDeleteInsBackupSetId(any(), any());
        doReturn("rds_20250101").when(recoverDBInstance).recoveryDeleteInsMinorVersion(any(), any(),
                any(), any(), any(), any(), any());
        when(mysqlParamSupport.getResourceStrategy(any())).thenReturn("res");
        doReturn("sys-param-xxx").when(recoverDBInstance).recoveryDeleteInsCheckParamGroupId(any(), any(), any(), any());


        Map<String, String> userparam = ImmutableMap.of("time_zone", "sys");
        when(mysqlParamSupport.getAndCheckMysqlCustomParams(any())).thenReturn(userparam);

        doNothing().when(mysqlParamSupport).updateCustinsCommonProperties(any(), any());

        when(mysqlParamSupport.getAndCheckBizType(any())).thenReturn(0);
        doReturn(custins).when(recoverDBInstance).recoveryDeleteInsSqlwall(any(), any());
        doReturn(params).when(recoverDBInstance).recoveryDeleteInsAddDefaultBakPolicy(any(), any());

        when(mysqlParamSupport.getRegionIdByClusterName(anyString())).thenReturn("cn-beijing");
        doReturn(params).when(recoverDBInstance).recoveryDeleteInsAddZoneInfo(any(), any(), any());

        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "cn-beijing", "cn-beijing","", null);
        when(avzSupport.getAVZInfo(any())).thenReturn(avzInfo);

        when(custinsParamService.createCustinsParamsForNewCustins(any(), any(), any(), any(),
                any(), any(), any())).thenReturn(null);

        custins.setId(123);
        when(custinsService.createCustInstance(any())).thenReturn(custins);
        List<Integer> instanceIds = ImmutableList.of(1,2);
        doReturn(instanceIds).when(recoverDBInstance).createMysql(any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any());

        doNothing().when(avzSupport).updateAVZInfoByInstanceIds(any(), any());
        doNothing().when(custinsParamService).updateAVZInfo(any(), any());

        doNothing().when(custinsParamService).createCustinsParam(any());

        when(ipWhiteListService.getAndCheckCustinsIpWhiteList(any(), any(), any(), any())).thenReturn(null);
        when(mysqlParamSupport.getAndCreateUserId(any())).thenReturn(111);
        CustinsIpWhiteListDO[] templateList = new CustinsIpWhiteListDO[0];
        when(mysqlParamSupport.getAndCheckWhitelistTemplateList(anyMap(), anyInt())).thenReturn(templateList);
        when(mysqlParamSupport.getAndCheckTemplateIdList(any(), any())).thenReturn(null);

        params.put("preferredbackupperiod", "0101010");
        params.put("preferredbackuptime", "16:43Z");
        params.put("backupretentionperiod", "7");

        TransListDO trans = new TransListDO();
        trans.setId(999);

        doReturn(trans).when(recoverDBInstance).recoveryDeleteInsTransList(any(), any(), any(), any(), any());

//        when(mySQLService.createCustInstanceTask(anyString(), any(), any(),
//                any(),anyMap(),any(),anyInt(), anyString())).thenReturn(123);

        doNothing().when(taskService).updateTaskPenginePolicy(anyInt(), anyInt());
        Map<String, Object> result = recoverDBInstance.recoveryDeleteIns(custins, params);
        Assert.assertNotNull(result);
    }

    @Test
    public void test_recoveryDeleteInsAddZoneInfo() throws Exception{
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("DBInstanceClass", "rds.mysql.s2.large");
        CustInstanceDO custins = dbInstance_main_standard80();
        when(mysqlParamSupport.getRegionIdByClusterName(anyString())).thenReturn("cn-beijing");

        when(clusterService.getZoneIdByClusterName(anyString())).thenReturn("cn-beijing-a");
        when(clusterService.getRegionByCluster(anyString())).thenReturn("cn-beijing");
        Object a = recoverDBInstance.recoveryDeleteInsAddZoneInfo(custins, custins, params);
        Assert.assertNotNull(a);
    }

    @Test
    public void test_recoveryDeleteInsAddDefaultBakPolicy(){
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("DBInstanceClass", "rds.mysql.s2.large");
        CustInstanceDO custins = dbInstance_main_standard80();
        Object a = recoverDBInstance.recoveryDeleteInsAddDefaultBakPolicy(custins, params);
        Assert.assertNotNull(a);
    }

    @Test
    public void test_recoveryDeleteInsCheckParamGroupId() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("DBInstanceClass", "rds.mysql.s2.large");
        CustInstanceDO custins = dbInstance_main_standard80();
        when(mysqlParamSupport.getParameterValue(any(), anyString())).thenReturn("rpg-sys-01040402010200");
        when(mysqlParamGroupHelper.isMgr(anyString())).thenReturn(false);
        when(parameterGroupTemplateGenerator.paramGroupMatchValidate(anyString(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(null);

        Object a = recoverDBInstance.recoveryDeleteInsCheckParamGroupId(custins, params, "mysql", "8.0");
        Assert.assertNotNull(a);
    }

    @Test(expected = RdsException.class)
    public void test_recoveryDeleteInsCheckParamGroupId2() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("DBInstanceClass", "rds.mysql.s2.large");
        CustInstanceDO custins = dbInstance_main_standard80();
        when(mysqlParamSupport.getParameterValue(any(), anyString())).thenReturn("rpg-sys-01040402010200");
        when(mysqlParamGroupHelper.isMgr(anyString())).thenReturn(true);

        Object a = recoverDBInstance.recoveryDeleteInsCheckParamGroupId(custins, params, "mysql", "8.0");
        Assert.assertNotNull(a);
    }

    @Test
    public void test_recoveryDeleteInsBackupSetId() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("backupsetid", "123456");
        CustInstanceDO custins = dbInstance_main_standard80();

        Object a = recoverDBInstance.recoveryDeleteInsBackupSetId(custins, params);
        Assert.assertNotNull(a);
    }

    @Test
    public void test_recoveryDeleteInsBackupSetId2() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        CustInstanceDO custins = dbInstance_main_standard80();

        when(bakService.getBakHistoryMapByCondition(anyMap())).thenReturn(ImmutableList.of(ImmutableMap.of()));

        recoverDBInstance.recoveryDeleteInsBackupSetId(custins, params);
        verify(bakService).getBakHistoryMapByCondition(anyMap());
    }

    @Test(expected = RdsException.class)
    public void test_recoveryDeleteInsBackupSetId3() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        CustInstanceDO custins = dbInstance_main_standard80();

        when(bakService.getBakHistoryMapByCondition(anyMap())).thenReturn(ImmutableList.of());

        recoverDBInstance.recoveryDeleteInsBackupSetId(custins, params);
        verify(bakService).getBakHistoryMapByCondition(anyMap());
    }

    @Test
    public void test_recoveryDeleteInsSqlwall() throws RdsException {
        Map<String, String> params = new HashMap<>();
        CustInstanceDO custins = dbInstance_main_standard80();
        custins.setBizType("top");
        Object o =recoverDBInstance.recoveryDeleteInsSqlwall(custins, params);
        Assert.assertNotNull(o);
    }

    @Test
    public void test_recoveryDeleteInsSqlwall2() throws RdsException {
        Map<String, String> params = new HashMap<>();
        CustInstanceDO custins = dbInstance_main_standard80();
        custins.setBizType("aliyun");
        Object o =recoverDBInstance.recoveryDeleteInsSqlwall(custins, params);
        Assert.assertNotNull(o);
    }

    @Test
    public void test_recoveryDeleteInsMinorVersion() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("TargetMinorVersion".toLowerCase(), "rds_20250101");
        CustInstanceDO custins = dbInstance_main_standard80();
        when(mysqlParamSupport.getParameterValue(params, "TargetMinorVersion")).thenReturn("rds_20250101");

        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn("rds_20250101");
        String vers = recoverDBInstance.recoveryDeleteInsMinorVersion(custins, params, 123456L, "mysql", "8.0", "class.code.1", new InstanceLevelDO());
        Assert.assertNotNull(vers);
    }

    @Test
    public void test_recoveryDeleteInsMinorVersion2() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("TargetMinorVersion".toLowerCase(), "rds_20250101");
        CustInstanceDO custins = dbInstance_main_standard80();
        when(mysqlParamSupport.getParameterValue(params, "TargetMinorVersion")).thenReturn("");
        when(recoveryHelper.getBakSetMinorVersion(any(), any())).thenReturn("rds_20250101");

        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn("rds_20250101");
        String vers = recoverDBInstance.recoveryDeleteInsMinorVersion(custins, params, 123456L, "mysql", "8.0", "class.code.1", new InstanceLevelDO());
        Assert.assertNotNull(vers);
    }

    @Test
    public void test_recoveryDeleteInsMinorVersion3() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("TargetMinorVersion".toLowerCase(), "");
        CustInstanceDO custins = dbInstance_main_standard80();
        when(mysqlParamSupport.getParameterValue(params, "TargetMinorVersion")).thenReturn("");
        when(recoveryHelper.getBakSetMinorVersion(any(), any())).thenReturn("");
        when(minorVersionServiceHelper.tryGetLatestMinorVersion(any(), any(),any(), any(), any())).thenReturn("rds_20250101");

        String vers = recoverDBInstance.recoveryDeleteInsMinorVersion(custins, params, 123456L, "mysql", "8.0", "class.code.1", new InstanceLevelDO());
        Assert.assertNotNull(vers);
    }

    @Test
    public void test_recoveryDeleteInsTransList() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put("User_ID", "1111");
        params.put("Action", "ExchangeDBInstance");
        params.put("UID", "1377539968280501");
        params.put("DBInstanceName", "rm-test001");
        params.put("TargetDBInstanceName", "rm-recovery001");
        params.put("TargetMinorVersion".toLowerCase(), "");
        CustInstanceDO custins = dbInstance_main_standard80();

        when(custinsService.getInstanceIdsByCustinsId(anyInt())).thenReturn(ImmutableList.of(1,2));
        doNothing().when(instanceService).createTransList(any());

        TransListDO vers = recoverDBInstance.recoveryDeleteInsTransList(custins, custins, "0", 123456L, ImmutableList.of(1,2));
        Assert.assertNotNull(vers);
    }

    @Test
    public void test_createMysql() throws RdsException {
        CustInstanceDO custins = dbInstance_main_standard80();
        ResourceContainer resourceContainer = new ResourceContainer("cn-beijing", "mysql");
        when(avzSupport.getRegionInitialedResourceContainer((AVZInfo) any(), any())).thenReturn(resourceContainer);

        Response<AllocateResRespModel> response = new Response<>();
        response.setCode(200);
        AllocateResRespModel alc = new AllocateResRespModel();
        alc.setCustinsResRespModelList(ImmutableList.of(new AllocateResRespModel.CustinsResRespModel()));
        response.setData(alc);
        when(resManagerService.allocateRes(any())).thenReturn(response);

        Object obj = recoverDBInstance.createMysql("recovery", "1111", custins,
                new InstanceLevelDO(), null, "", "lvs", 2, 0, 0,
                120L,  null, "", "", "11111");

        Assert.assertNull(obj);
    }

    @Test(expected = RdsException.class)
    public void test_createMysql2() throws RdsException {
        CustInstanceDO custins = dbInstance_main_standard80();
        ResourceContainer resourceContainer = new ResourceContainer("cn-beijing", "mysql");
        when(avzSupport.getRegionInitialedResourceContainer((AVZInfo) any(), any())).thenReturn(resourceContainer);

        Response<AllocateResRespModel> response = new Response<>();
        response.setCode(400);
        AllocateResRespModel alc = new AllocateResRespModel();
        alc.setCustinsResRespModelList(ImmutableList.of(new AllocateResRespModel.CustinsResRespModel()));
        response.setData(alc);
        when(resManagerService.allocateRes(any())).thenReturn(response);

        Object obj = recoverDBInstance.createMysql("recovery", "1111", custins,
                new InstanceLevelDO(), null, "", "lvs", 2, 0, 0,
                120L,  null, "", "", "11111");
    }
}



