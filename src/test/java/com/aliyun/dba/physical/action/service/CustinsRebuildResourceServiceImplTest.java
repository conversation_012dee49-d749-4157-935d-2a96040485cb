
package com.aliyun.dba.physical.action.service;


import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.HostInfo;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.property.ParamConstants.DispenseMode;
import com.aliyun.dba.support.property.RdsException;
import com.google.common.collect.ImmutableList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CustinsRebuildResourceServiceImplTest {
    @Spy
    @InjectMocks
    private CustinsRebuildResourceServiceImpl custinsRebuildResourceService;

    @Mock
    private AVZSupport avzSupport;

    @Mock
    private InstanceService instanceService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private HostService hostService;

    @Mock
    private ClusterService clusterService;

    @Mock
    private ClusterIDao clusterIDao;

    private CustInstanceDO custins;
    private CustInstanceDO tempCustins;
    private List<InstanceDO> instanceList;
    private Set<Integer> hostIdSet;

    @Before
    public void setUp() {
        custins = new CustInstanceDO();
        custins.setLevelId(1);
        tempCustins = new CustInstanceDO();
        instanceList = new ArrayList<>();
        InstanceDO instanceDO = new InstanceDO();
        instanceDO.setId(1);
        instanceDO.setRole(0);
        instanceDO.setSiteName("test");
        instanceList.add(instanceDO);
        hostIdSet = new HashSet<>();
    }


    @Test
    public void getMysqlResContainerForReadOnlyRebuild_ReadOrBackup_CalculatesDiskUsage() throws RdsException {
        custins.setPrimaryCustinsId(1);
        custins.setInsType(3);
        when(custinsParamService.getDispenseMode(custins.getId())).thenReturn(DispenseMode.MultiAVZDispenseMode);
        when(instanceService.getInstanceDiskUsage(any(CustInstanceDO.class), eq(0))).thenReturn(100L);
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(new InstanceLevelDO());
        when(custinsService.getCustInstanceByCustinsId(anyInt())).thenReturn(new CustInstanceDO());
        when(avzSupport.getRebuildSlaveResourceContainer(any(CustInstanceDO.class), anySet(), anyInt())).thenReturn(new ResourceContainer(null, null));
        when(clusterService.getClusterByClusterName(anyString())).thenReturn(new ClustersDO());
        InstanceDO srcInstanceDO = new InstanceDO();
        when(instanceService.getInstanceByInsId(anyInt())).thenReturn(srcInstanceDO);

        ResourceContainer resourceContainer = custinsRebuildResourceService.getMysqlResContainerForReadOnlyRebuild(hostIdSet, custins, tempCustins, instanceList, 1, null, null, false);
        assertNotNull(resourceContainer);
        custins.setInsType(1);
        ResourceContainer resourceContainer2 = custinsRebuildResourceService.getMysqlResContainerForReadOnlyRebuild(hostIdSet, custins, tempCustins, instanceList, 1, null, null, false);
        assertNotNull(resourceContainer2);
    }

    public CustInstanceDO dbInstance_main_standard80(){
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");
        custins.setLevelId(12345);
        custins.setKindCode(0);
        custins.setIsTmp(0);
        custins.setId(123);
        custins.setStatus(1);
        custins.setLockMode(0);
        custins.setBizType("aliyun");
        custins.setInsType(0);
        custins.setConnType("lvs");
        custins.setParentId(0);
        custins.setPrimaryCustinsId(0);
        custins.setInsName("test_ins001");
        custins.setDiskSize(102400L);
        custins.setUserId(2222);

        return custins;
    }

    @Test
    public void test_getMysqlResContainerForRecoveryNode() throws RdsException {
        custins = dbInstance_main_standard80();

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);


        custins.setPrimaryCustinsId(1);
        custins.setInsType(3);

        when(instanceService.getInstanceDiskUsage(any(CustInstanceDO.class), eq(0))).thenReturn(100L);
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(new InstanceLevelDO());

        when(avzSupport.getRebuildSlaveResourceContainer(any(CustInstanceDO.class), anySet(), anyInt())).thenReturn(new ResourceContainer(null, null));

        InstanceDO srcInstanceDO = new InstanceDO();

        ResourceContainer resourceContainer = custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(custins, custins,
                instanceDOList, 111, "33322", "et91", hostIdSet);
        assertNotNull(resourceContainer);
        custins.setInsType(1);
        ResourceContainer resourceContainer2 = custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(custins, custins,
                instanceDOList, 111, "33322", "et91", hostIdSet);
        assertNotNull(resourceContainer2);
    }

    @Test(expected = NullPointerException.class)
    public void test_getMysqlResContainerForRecoveryNode2() throws RdsException {
        custins = dbInstance_main_standard80();

        ResourceContainer res = custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(custins, custins,
                null, 111, "33322", "", null);
        assertNotNull(res);
    }

    @Test
    public void test_getMysqlResContainerForRecoveryNode3() throws RdsException {
        custins = dbInstance_main_standard80();
        custins.setConnType("dns");

        InstanceDO master = new InstanceDO();
        master.setId(111);
        master.setRole(0);
        master.setLevelId(12345L);
        master.setCustinsId(custins.getId());
        master.setHostId(321);
        master.setHostName("host001");
        master.setIp("***********");
        master.setPort(3000);

        InstanceDO slave = new InstanceDO();
        slave.setId(222);
        slave.setRole(1);
        slave.setLevelId(12345L);
        slave.setCustinsId(custins.getId());
        slave.setHostId(321);
        slave.setHostName("host001");
        slave.setIp("***********");
        slave.setPort(3000);

        List<InstanceDO> instanceDOList = ImmutableList.of(master, slave);


        custins.setPrimaryCustinsId(1);
        custins.setInsType(3);

        when(instanceService.getInstanceDiskUsage(any(CustInstanceDO.class), eq(0))).thenReturn(100L);
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(new InstanceLevelDO());

        when(avzSupport.getRebuildSlaveResourceContainer(any(CustInstanceDO.class), anySet(), anyInt())).thenReturn(new ResourceContainer(null, null));

        InstanceDO srcInstanceDO = new InstanceDO();

        ResourceContainer resourceContainer = custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(custins, custins,
                instanceDOList, 111, "33322", "et91", hostIdSet);
        assertNotNull(resourceContainer);
        custins.setInsType(1);
        ResourceContainer resourceContainer2 = custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(custins, custins,
                instanceDOList, 111, "33322", "et91", hostIdSet);
        assertNotNull(resourceContainer2);

        hostIdSet.add(111);
        ResourceContainer resourceContainer3 = custinsRebuildResourceService.getMysqlResContainerForRecoveryNode(custins, custins,
                instanceDOList, 111, "33322", "", hostIdSet);
        assertNotNull(resourceContainer2);
    }

}