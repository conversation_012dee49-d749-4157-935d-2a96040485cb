package com.aliyun.dba.physical.action.service;

import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.UpgradeResContainer;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.EvaluateUpgradeResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.base.service.CrmService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ResManagerServiceTest {

    @InjectMocks
    private ResManagerService resManagerService;

    @Mock
    private IResApi resApi;

    @Mock
    private ResourceService resourceService;

    @Mock
    private CrmService crmService;

    @Mock
    private UserService userService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private PhysicalResourceGuaranteeModelService physicalResourceGuaranteeModelService;

    @Before
    public void setUp() {
        System.out.println("开始测试------");
    }

    @Test
    public void evaluateUpgradeRes_WithExceptionInAddResourceGuaranteeModelPolicy() throws Exception {
        // Arrange
        UpgradeResContainer upgradeResContainer = new UpgradeResContainer("dummyRegion", "dummyRegionCategory");
        doThrow(new RuntimeException("Test Exception")).when(physicalResourceGuaranteeModelService).addResourceGuaranteeModelPolicy(any(UpgradeResContainer.class));
        Response<EvaluateUpgradeResRespModel> expectedResponse = new Response<>();
        when(resApi.evaluateUpgradeRes(any(UpgradeResContainer.class))).thenReturn(expectedResponse);

        // Act
        Response<EvaluateUpgradeResRespModel> actualResponse = resManagerService.evaluateUpgradeRes(upgradeResContainer);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(physicalResourceGuaranteeModelService, times(1)).addResourceGuaranteeModelPolicy(any(UpgradeResContainer.class));
        verify(resApi, times(1)).evaluateUpgradeRes(any(UpgradeResContainer.class));
    }

    @Test
    public void evaluateUpgradeRes_SuccessfulExecution() {
        // Arrange
        UpgradeResContainer upgradeResContainer = new UpgradeResContainer("dummyRegion", "dummyRegionCategory");
        Response<EvaluateUpgradeResRespModel> expectedResponse = new Response<>();
        when(resApi.evaluateUpgradeRes(any(UpgradeResContainer.class))).thenReturn(expectedResponse);

        // Act
        Response<EvaluateUpgradeResRespModel> actualResponse = resManagerService.evaluateUpgradeRes(upgradeResContainer);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(physicalResourceGuaranteeModelService, times(1)).addResourceGuaranteeModelPolicy(any(UpgradeResContainer.class));
        verify(resApi, times(1)).evaluateUpgradeRes(any(UpgradeResContainer.class));
    }

    @Test
    public void allocateRes_WithExceptionInAddResourceGuaranteeModelPolicy() {
        // Arrange
        ResourceContainer resourceContainer = new ResourceContainer("dummyRegion", "dummyRegionCategory");
        doThrow(new RuntimeException("Test Exception")).when(physicalResourceGuaranteeModelService).addResourceGuaranteeModelPolicy(any(ResourceContainer.class));
        Response<AllocateResRespModel> expectedResponse = new Response<>();
        when(resApi.allocateRes(any(ResourceContainer.class))).thenReturn(expectedResponse);

        // Act
        Response<AllocateResRespModel> actualResponse = resManagerService.allocateRes(resourceContainer);

        // Assert
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void allocateRes_SuccessfulExecution() {
        // Arrange
        ResourceContainer resourceContainer = new ResourceContainer("dummyRegion", "dummyRegionCategory");
        Response<AllocateResRespModel> expectedResponse = new Response<>();
        when(resApi.allocateRes(any(ResourceContainer.class))).thenReturn(expectedResponse);

        // Act
        Response<AllocateResRespModel> actualResponse = resManagerService.allocateRes(resourceContainer);

        // Assert
        assertEquals(expectedResponse, actualResponse);
    }

    private ResourceDO createMockResourceDO(String realValue) {
        ResourceDO resourceDO = mock(ResourceDO.class);
        when(resourceDO.getRealValue()).thenReturn(realValue);
        return resourceDO;
    }


    @After
    public void tearDown() throws Exception {
        System.out.println("finish test ------");
    }
}
