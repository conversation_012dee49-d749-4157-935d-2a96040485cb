package com.aliyun.dba.physical.action;


import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.instance.entity.MycnfChangeLog;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.EncdbService;
import com.aliyun.dba.poddefault.action.service.TdeKmsService;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.user.service.UserServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest({ActionParamsProvider.class, RequestSession.class})
public class ModifyDBInstanceCLSImplTest {
    @InjectMocks
    private ModifyDBInstanceCLSImpl modifyDBInstanceCLS;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;
    @Mock
    private CustinsServiceImpl custinsService;
    @Mock
    private UserServiceImpl userService;
    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    private EncdbService encdbService;
    @Mock
    private TdeKmsService tdeKmsService;
    @Mock
    private KmsApi kmsApi;
    @Mock
    private TaskService taskService;
    @Mock
    private InstanceService instanceService;
    @Mock
    private MycnfService mycnfService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(ActionParamsProvider.class);
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
        doNothing().when(encdbService).checkCustinsStatusAvailable(any());
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("regionID");
        when(tdeKmsService.checkTdeSupported("testRequestId", "regionID")).thenReturn(true);
    }

    /*
     * API 参数：
     * EncryptionStatus: "1"
     * EncryptionAlgorithm: "AES_256_GCM"
     * WhiteListMode: "true"
     * EncryptionKeyMode: "client_key"
     */
    @Test
    public void testDoActionRequest_success_doConfigAndTurnOnEncryption() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custins);
        when(mysqlParamSupport.getParameterValue(any(), eq("EncryptionAlgorithm"))).thenReturn("AES_256_GCM");
        when(mysqlParamSupport.getParameterValue(any(), eq("WhiteListMode"))).thenReturn("true");
        when(mysqlParamSupport.getParameterValue(any(), eq("EncryptionKeyMode"))).thenReturn("EncryptionKeyMode");
        when(mysqlParamSupport.getParameterValue(any(), eq("EncryptionStatus"))).thenReturn("1");
        doNothing().when(encdbService).setEncdbGlobalAlgo(any(), eq("AES_256_GCM"));
        doNothing().when(encdbService).setWhiteListMode(any(), eq("true"));
        when(instanceService.getMycnfCustinsHistoryByName(1, "loose_encdb")).thenReturn(null);
        doNothing().when(mycnfService).createMycnfChangeLog(any());
        when(mysqlParameterHelper.getAction()).thenReturn("ModifyDBInstanceCLS");
        when(mysqlParameterHelper.getOperatorId()).thenReturn(99999);
        doAnswer(
                invocationOnMock -> {
                    ((TaskQueueDO)invocationOnMock.getArgument(0)).setId(0);
                    return null;
                }
        ).when(taskService).createTaskQueue(any());
        doNothing().when(taskService).updateTaskPenginePolicy(any(), any());
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertNotNull(result);
        assertNotNull(result.get("TaskIDs"));
        verify(encdbService).setEncdbGlobalAlgo(any(), eq("AES_256_GCM"));
        verify(encdbService).setWhiteListMode(any(), eq("true"));
        verify(taskService).createTaskQueue(any());
        verify(taskService).updateTaskPenginePolicy(any(), any());
    }

    /*
     * API 参数：
     * EncryptionStatus: "0"
     */
    @Test
    public void testDoActionRequest_success_turnOffEncryption() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custins);
        when(mysqlParamSupport.getParameterValue(any(), eq("EncryptionAlgorithm"))).thenReturn(null);
        when(mysqlParamSupport.getParameterValue(any(), eq("WhiteListMode"))).thenReturn(null);
        when(mysqlParamSupport.getParameterValue(any(), eq("EncryptionKeyMode"))).thenReturn(null);
        when(mysqlParamSupport.getParameterValue(any(), eq("EncryptionStatus"))).thenReturn("0");
        MycnfChangeLog log = new MycnfChangeLog();
        log.setNewValue("ON");
        when(instanceService.getMycnfCustinsHistoryByName(1, "loose_encdb")).thenReturn(log);
        doNothing().when(mycnfService).createMycnfChangeLog(any());
        when(mysqlParameterHelper.getAction()).thenReturn("ModifyDBInstanceCLS");
        when(mysqlParameterHelper.getOperatorId()).thenReturn(99999);
        doAnswer(
                invocationOnMock -> {
                    ((TaskQueueDO)invocationOnMock.getArgument(0)).setId(0);
                    return null;
                }
        ).when(taskService).createTaskQueue(any());
        doNothing().when(taskService).updateTaskPenginePolicy(any(), any());
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertNotNull(result);
        assertNotNull(result.get("TaskIDs"));
        verify(taskService).createTaskQueue(any());
        verify(taskService).updateTaskPenginePolicy(any(), any());
    }

    @Test
    public void testDoActionRequest_error_nonPrimaryInstance() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_READ.getValue());
        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custins);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_error_dbossIOException() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custins);
        when(mysqlParamSupport.getParameterValue(any(), eq("EncryptionAlgorithm"))).thenReturn("AES_256_GCM");
        doThrow(new IOException()).when(encdbService).setEncdbGlobalAlgo(any(), eq("AES_256_GCM"));
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertEquals(ErrorCode.INTERNAL_FAILURE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_error_encdbServiceRdsException() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custins);
        when(mysqlParamSupport.getParameterValue(any(), eq("EncryptionAlgorithm"))).thenReturn("AES_256_GCM");
        doThrow(new RdsException(ErrorCode.INVALID_STATUS)).when(encdbService).setEncdbGlobalAlgo(any(), eq("AES_256_GCM"));
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertEquals(ErrorCode.INVALID_STATUS.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_error_unsupportedKmsRegion() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custins);
        when(tdeKmsService.checkTdeSupported("testRequestId", "regionID")).thenReturn(false);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }




}
