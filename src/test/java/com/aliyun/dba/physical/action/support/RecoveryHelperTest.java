package com.aliyun.dba.physical.action.support;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.ArchivelogListDO;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.RecoverDBInstanceImpl;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.physical.action.support.RecoveryHelper;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RecoveryHelperTest {
    @Spy
    @InjectMocks
    private RecoveryHelper recoveryHelper;

    @Mock
    protected InstanceService instanceService;
    @Mock
    protected CustinsService custinsService;
    @Mock
    protected ClusterService clusterService;
    @Mock
    protected TaskService taskService;
    @Mock
    protected IResApi resApi;
    @Mock
    protected MycnfService mycnfService;
    @Mock
    protected MysqlParamSupport mysqlParamSupport;
    @Mock
    protected ReplicaSetService replicaSetService;
    @Mock
    protected CommonProviderService commonProviderService;
    @Mock
    protected DBaasMetaService dBaasMetaService;
    @Mock
    protected WorkFlowService workFlowService;
    @Mock
    protected CustinsParamService custinsParamService;
    @Mock
    protected MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    private BakService bakService;
    @Mock
    private IpWhiteListService ipWhiteListService;
    @Mock
    private DbsService dbsService;
    @Mock
    private InstanceIDao instanceIDao;
    @Mock
    private ModuleService moduleService;
    @Mock
    private CustinsIDao custinsIDao;
    @Mock
    private HostService hostService;
    @Mock
    private ConnAddrCustinsService connAddrCustinsService;

    @Mock
    protected AVZSupport avzSupport;
    @Mock
    protected ClusterIDao clusterIDao;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    public CustInstanceDO dbInstance_main_standard80(){
        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");
        custins.setLevelId(12345);
        custins.setKindCode(0);
        custins.setIsTmp(0);
        custins.setId(123);
        custins.setStatus(1);
        custins.setLockMode(0);
        custins.setBizType("aliyun");
        custins.setInsType(0);
        custins.setConnType("lvs");
        custins.setParentId(0);
        custins.setPrimaryCustinsId(0);
        custins.setInsName("test_ins001");
        custins.setDiskSize(102400L);
        custins.setUserId(2222);

        return custins;
    }

    @Test
    public void test_checkSupportInstance1() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        instanceLevel.setCategory("standard");
        recoveryHelper.checkSupportInstance(custins, instanceLevel);
    }

    @Test(expected = RdsException.class)
    public void test_checkSupportInstance2() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        custins.setType("s");
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        instanceLevel.setCategory("standard");
        recoveryHelper.checkSupportInstance(custins, instanceLevel);
    }

    @Test(expected = RdsException.class)
    public void test_checkSupportInstance3() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        custins.setCharacterType("logic");
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        instanceLevel.setCategory("standard");
        recoveryHelper.checkSupportInstance(custins, instanceLevel);
    }

    @Test(expected = RdsException.class)
    public void test_checkSupportInstance4() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        instanceLevel.setCategory("basic");
        recoveryHelper.checkSupportInstance(custins, instanceLevel);
    }

    @Test(expected = RdsException.class)
    public void test_checkSupportInstance5() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        custins.setInsType(1);
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        instanceLevel.setCategory("standard");
        recoveryHelper.checkSupportInstance(custins, instanceLevel);
    }

    @Test(expected = RdsException.class)
    public void test_checkSupportInstance6() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        custins.setConnType("proxy");
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        instanceLevel.setCategory("standard");
        recoveryHelper.checkSupportInstance(custins, instanceLevel);
    }

    @Test(expected = RdsException.class)
    public void test_checkSupportInstance7() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        custins.setBizType("aligroup");
        InstanceLevelDO instanceLevel = new InstanceLevelDO();
        instanceLevel.setCategory("standard");
        recoveryHelper.checkSupportInstance(custins, instanceLevel);
    }

    @Test
    public void test_checkBakSetValid() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus(JSON.toJSONString(ImmutableMap.of("BINLOG_FILE", "binlog.0001")));
        bakHistory.setHostinsId(111);

        ArchivelogListDO arch1 = new ArchivelogListDO();
        arch1.setId(1L);
        arch1.setHostinsId(111);
        arch1.setRemoteStatus(2);
        arch1.setLocalStatus(0);
        arch1.setLocation("host");

        List<ArchivelogListDO> binlogList = ImmutableList.of(arch1);
        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
        when(bakService.getArchivelogByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(binlogList);

        recoveryHelper.checkBakSetValid(custins, 111L);
    }


    @Test(expected = RdsException.class)
    public void test_checkBakSetValid2() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(null);
        recoveryHelper.checkBakSetValid(custins, 111L);
    }


    @Test(expected = RdsException.class)
    public void test_checkBakSetValid3() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("notok");
        bakHistory.setIsAvail(1);
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus(JSON.toJSONString(ImmutableMap.of("BINLOG_FILE", "binlog.0001")));
        bakHistory.setHostinsId(111);

        ArchivelogListDO arch1 = new ArchivelogListDO();
        arch1.setId(1L);
        arch1.setHostinsId(111);
        arch1.setRemoteStatus(2);
        arch1.setLocalStatus(0);
        arch1.setLocation("host");

        List<ArchivelogListDO> binlogList = ImmutableList.of(arch1);
        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
//        when(bakService.getArchivelogByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(binlogList);

        recoveryHelper.checkBakSetValid(custins, 111L);
    }


    @Test(expected = RdsException.class)
    public void test_checkBakSetValid4() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setType("errorDATA");
        bakHistory.setBakWay("1P");
        bakHistory.setBakScale(1);
        bakHistory.setSlaveStatus(JSON.toJSONString(ImmutableMap.of("BINLOG_FILE", "binlog.0001")));
        bakHistory.setHostinsId(111);

        ArchivelogListDO arch1 = new ArchivelogListDO();
        arch1.setId(1L);
        arch1.setHostinsId(111);
        arch1.setRemoteStatus(2);
        arch1.setLocalStatus(0);
        arch1.setLocation("host");

        List<ArchivelogListDO> binlogList = ImmutableList.of(arch1);
        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
//        when(bakService.getArchivelogByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(binlogList);

        recoveryHelper.checkBakSetValid(custins, 111L);
    }


    @Test(expected = RdsException.class)
    public void test_checkBakSetValid5() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus(JSON.toJSONString(ImmutableMap.of()));
        bakHistory.setHostinsId(111);
        bakHistory.setBaksetInfo(JSON.toJSONString(ImmutableMap.of()));

        ArchivelogListDO arch1 = new ArchivelogListDO();
        arch1.setId(1L);
        arch1.setHostinsId(111);
        arch1.setRemoteStatus(2);
        arch1.setLocalStatus(0);
        arch1.setLocation("host");

        List<ArchivelogListDO> binlogList = ImmutableList.of(arch1);
        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
//        when(bakService.getArchivelogByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(binlogList);

        recoveryHelper.checkBakSetValid(custins, 111L);
    }

    @Test(expected = RdsException.class)
    public void test_checkBakSetValid6() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus(JSON.toJSONString(ImmutableMap.of("BINLOG_FILE", "")));
        bakHistory.setHostinsId(111);

        ArchivelogListDO arch1 = new ArchivelogListDO();
        arch1.setId(1L);
        arch1.setHostinsId(111);
        arch1.setRemoteStatus(2);
        arch1.setLocalStatus(0);
        arch1.setLocation("host");

        List<ArchivelogListDO> binlogList = ImmutableList.of(arch1);
        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
//        when(bakService.getArchivelogByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(binlogList);

        recoveryHelper.checkBakSetValid(custins, 111L);
    }

    @Test(expected = RdsException.class)
    public void test_checkBakSetValid7() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus(JSON.toJSONString(ImmutableMap.of("BINLOG_FILE", "binlog.0001")));
        bakHistory.setHostinsId(111);

        ArchivelogListDO arch1 = new ArchivelogListDO();
//        arch1.setId(1L);
//        arch1.setHostinsId(111);
//        arch1.setRemoteStatus(2);
//        arch1.setLocalStatus(0);
//        arch1.setLocation("host");

        List<ArchivelogListDO> binlogList = ImmutableList.of(arch1);
        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
        when(bakService.getArchivelogByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(binlogList);


        recoveryHelper.checkBakSetValid(custins, 111L);
    }


    @Test(expected = RdsException.class)
    public void test_checkBakSetValid8() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setType("DATA");
        bakHistory.setBakWay("P");
        bakHistory.setBakScale(0);
        bakHistory.setSlaveStatus(JSON.toJSONString(ImmutableMap.of("BINLOG_FILE", "binlog.0001")));
        bakHistory.setHostinsId(112);

        ArchivelogListDO arch1 = new ArchivelogListDO();
        arch1.setId(1L);
        arch1.setHostinsId(111);
        arch1.setRemoteStatus(2);
        arch1.setLocalStatus(0);
        arch1.setLocation("host");

        List<ArchivelogListDO> binlogList = ImmutableList.of(arch1);
        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
        when(bakService.getArchivelogByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(binlogList);


        recoveryHelper.checkBakSetValid(custins, 111L);
    }

    @Test(expected = RdsException.class)
    public void test_getTargetMinorVersion() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
        params.put("targetminorversion", "rds_20250331");

        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode("mysql.xxx.xxxx");
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(instanceLevelDO);

        when(mysqlParamSupport.getParameterValue(params, "TargetMinorVersion")).thenReturn("rds_20250331");
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(anyString())).thenReturn("20250331");
//        when(mysqlParamSupport.getParameterValue(params, "mysql80AllowDowngrade", true)).thenReturn("true");

        String minorVersion = recoveryHelper.getTargetMinorVersion(params, custins);
        Assert.assertNotNull(minorVersion);
    }

    @Test(expected = RdsException.class)
    public void test_getTargetMinorVersion2() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
        params.put("targetminorversion", "rds_20250331");

        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode("mysql.xxx.xxxx");
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(instanceLevelDO);

        when(mysqlParamSupport.getParameterValue(params, "TargetMinorVersion")).thenReturn("");
        when(mysqlParamSupport.getParameterValue(params, "MinorVersion")).thenReturn("rds_20250331");
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(anyString())).thenReturn("20250331");
//        when(mysqlParamSupport.getParameterValue(params, "mysql80AllowDowngrade", true)).thenReturn("true");

        String minorVersion = recoveryHelper.getTargetMinorVersion(params, custins);
        Assert.assertNotNull(minorVersion);
    }

    @Test
    public void test_getTargetMinorVersion3() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
        params.put("targetminorversion", "rds_20250331");

        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode("mysql.xxx.xxxx");
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(instanceLevelDO);

        when(mysqlParamSupport.getParameterValue(params, "TargetMinorVersion")).thenReturn("");
        when(mysqlParamSupport.getParameterValue(params, "MinorVersion")).thenReturn("rds_20250331");
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(anyString())).thenReturn("20250331");
//        when(mysqlParamSupport.getParameterValue(params, "mysql80AllowDowngrade", true)).thenReturn("true");
        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn("rds_20250331");

        String minorVersion = recoveryHelper.getTargetMinorVersion(params, custins);
        Assert.assertNotNull(minorVersion);
    }

    @Test(expected = RdsException.class)
    public void test_getTargetMinorVersion4() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        Map<String, String> params = new HashMap<>();
        params.put("SiteName", "nm125");
        params.put("BackupSetId", "2159401918");
        params.put("ChangeMaster", "false");
        params.put("targetminorversion", "rds_20250331");

        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setClassCode("mysql.xxx.xxxx");
//        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(instanceLevelDO);

        when(mysqlParamSupport.getParameterValue(params, "TargetMinorVersion")).thenReturn("");
        when(mysqlParamSupport.getParameterValue(params, "MinorVersion")).thenReturn("rds_20250331");
        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(anyString())).thenReturn("");
//        when(mysqlParamSupport.getParameterValue(params, "mysql80AllowDowngrade", true)).thenReturn("true");
//        when(minorVersionServiceHelper.checkAndGetTargetMinorVersion(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn("rds_20250331");

        String minorVersion = recoveryHelper.getTargetMinorVersion(params, custins);
        Assert.assertNotNull(minorVersion);
    }

    @Test
    public void test_recoverySlaveInstanceTask() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();

        doNothing().when(dbsService).syncAllDbsAndAccounts(any(), any());
        doNothing().when(ipWhiteListService).syncCustinsIpWhiteList(anyInt(), anyInt());
        doNothing().when(instanceIDao).createTransList(any());
        doNothing().when(taskService).createTaskQueue(any());
//        doNothing().when(instanceIDao).updateTransTaskIdById(anyInt(), anyInt());

        TransListDO transListDO = new TransListDO();
        transListDO.setId(1111);

        Integer taskid = recoveryHelper.recoverySlaveInstanceTask("recovery", custins, custins, transListDO, 123, ImmutableMap.of());
        Assert.assertNull(taskid);
    }

    @Test(expected = RdsException.class)
    public void test_getBakSetMinorVersion() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        BakhistoryDO bakHistory = new BakhistoryDO();

        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
        String res = recoveryHelper.getBakSetMinorVersion(custins, 1111L);
        Assert.assertNull(res);
    }

    @Test(expected = RdsException.class)
    public void test_getBakSetMinorVersion2() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("error");

        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
        String res = recoveryHelper.getBakSetMinorVersion(custins, 1111L);
        Assert.assertNull(res);
    }

    @Test(expected = RdsException.class)
    public void test_getBakSetMinorVersion3() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setSlaveStatus(null);

        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
        String res = recoveryHelper.getBakSetMinorVersion(custins, 1111L);
        Assert.assertNull(res);
    }

    @Test
    public void test_getBakSetMinorVersion4() throws Exception {
        CustInstanceDO custins = dbInstance_main_standard80();
        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setStatus("ok");
        bakHistory.setIsAvail(1);
        bakHistory.setSlaveStatus(JSON.toJSONString(ImmutableMap.of("MINOR_VERSION", "xxxx")));

        when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);
        String res = recoveryHelper.getBakSetMinorVersion(custins, 1111L);
        Assert.assertNotNull(res);
    }
}

