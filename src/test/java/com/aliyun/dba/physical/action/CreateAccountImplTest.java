package com.aliyun.dba.physical.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Account;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccountListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.dataobject.AccountsDTO;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.service.TaskServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.apsaradb.dbaasmetaapi.api.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dbs.support.DbsSupport.BIZ_TYPE_USER;
import static com.aliyun.dba.dbs.support.DbsSupport.STATUS_ACTIVE;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CreateAccountImplTest {

    @InjectMocks
    private CreateAccountImpl createAccountImpl;

    @Mock
    private AccountService accountService;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private DbossApi dbossApi;

    @Mock
    private MysqlParameterHelper parameterHelper;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private CustinsIDao custinsIDao;

    @Mock
    private AccountIDao accountIDao;

    @Mock
    private TaskServiceImpl taskService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    public void accountHelper(Map<String,String> params,CustInstanceDO cdo) throws Exception {
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put(ParamConstants.ACCOUNT_NAME, "testAccount");
        params.put(ParamConstants.ACCOUNT_PASSWORD, "testPassword");
        params.put(ParamConstants.ACCOUNT_TYPE, "common");
        params.put(ParamConstants.ACCOUNT_BIZ_TYPE, "user");
        params.put(ParamConstants.ACCOUNT_PRIVILEGE, "ReadWrite");
        params.put(ParamConstants.COMMENT, "Test Account");
        params.put(ParamConstants.ACCOUNT_ADMIN_TYPE, "0");

        when(parameterHelper.getParameterValue(ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(parameterHelper.getDBInstanceName()).thenReturn("replicaSet");

        when(parameterHelper.getAccountName()).thenReturn("testAccount");

        when(parameterHelper.getAccountPrivilege()).thenReturn("ALL");
        when(parameterHelper.getAndCheckDecryptedAccountPassword()).thenReturn("testPassword");
        when(parameterHelper.getDbInfo()).thenReturn(null);
        when(parameterHelper.getDBNames()).thenReturn(null);
        when(parameterHelper.getAndCheckAccountDesc()).thenReturn("Test Account");
        when(parameterHelper.getAction()).thenReturn("action");
        when(parameterHelper.getOperatorId()).thenReturn(123);
        when(parameterHelper.getParameterValue(ParamConstants.ACCESSID)).thenReturn("accessId");
        when(parameterHelper.getPenginePolicyID()).thenReturn(123);

        when(dbossApi.isHandleByDBoss(any(CustInstanceDO.class))).thenReturn(true);


        cdo.setInsType(RdsConstants.CUSTINS_INSTYPE_PRIMARY);
        cdo.setId(123);
        cdo.setAccountMode(NEW_CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE);
        cdo.setStatus(CUSTINS_STATUS_ACTIVE);
        cdo.setCharacterType(CHARACTER_TYPE_MYSQL_MYSQLS);
        cdo.setMaxAccounts(1);
        cdo.setKindCode(0);
        cdo.setDbType("mysql");
        cdo.setDbVersion("DB_VERSION_MYSQL_57");
    }

    // 正常创建普通账号
    @Test
    public void doActionRequest_Success_ReturnsSuccessResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        CustInstanceDO cdo = new CustInstanceDO();
        accountHelper(params,cdo);

        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON))
                .thenReturn(CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON);

        when(accountIDao.queryAccountByAccountName(anyInt(), anyString())).thenReturn(null);

        Map<String, Object> result = createAccountImpl.doActionRequest(cdo, params);

        assertNotNull(result);
        assertEquals("testAccount", result.get("AccountName"));
    }

    // 正常创建高权限账号，元数据中没有高权限账号
    @Test
    public void doActionRequest_SuccessSuper_ReturnsSuccessResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        CustInstanceDO cdo = new CustInstanceDO();
        accountHelper(params,cdo);

        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON))
                .thenReturn(CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER);

        Account resAcc = new Account();
        resAcc.id(123456L);

        // 元数据库中没有高权限账号
        cdo.setAccountMode(NEW_CUSTINS_ACCOUNT_MODE_ALLOW_UPGRADE);
        when(accountService.countAccountByCustInsId(cdo.getId())).thenReturn(0);

        // 用户实例上的账号列表
        List<Map<String, Object>> accountList = new ArrayList<>();
        Map<String, Object> account1 = new HashMap<>();
        account1.put("accountName", "JohnDoe");
        accountList.add(account1);
        Map<String, Object> account2 = new HashMap<>();
        account2.put("accountName", "Alice");
        accountList.add(account2);
        when(dbossApi.queryAccounts(cdo.getId(), null, null,0, accountList.size()))
                .thenReturn(accountList);

        // 查用户实例
        Map<String,Integer> accountCount = new HashMap<>();
        accountCount.put("accounts", accountList.size());
        when(dbossApi.getAccountCount(cdo.getId().toString(),null, RdsConstants.ROLETYPE_USER))
                .thenReturn(accountCount);

        AccountsDTO adto = new AccountsDTO();
        adto.setTaskId(123);
        adto.setAccount("testAccount");
        when(accountService.addAccount(any(AccountsDTO.class),anyString(),anyInt(),anyString())).thenReturn(adto);

        doNothing().when(taskService).updateTaskPenginePolicy(anyInt(),anyInt());

        Map<String, Object> result = createAccountImpl.doActionRequest(cdo, params);

        assertNotNull(result);
        assertEquals("testAccount", result.get("AccountName"));
    }

    // 创建高权限账号，元数据中有高权限账号，用户数据中也有
    @Test
    public void doActionRequest_FailSuper_ReturnsSuccessResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        CustInstanceDO cdo = new CustInstanceDO();
        accountHelper(params,cdo);

        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON))
                .thenReturn(CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER);

        // 元数据库中有高权限账号
        cdo.setAccountMode(NEW_CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED);
        when(accountService.countAccountByCustInsId(cdo.getId())).thenReturn(1);

        AccountsDO superAcc = new AccountsDO();
        superAcc.setAccount("testAccount");
        superAcc.setId(123456);
        when(accountService.getAccount(cdo.getId(), DbsSupport.BIZ_TYPE_USER, AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue()))
                .thenReturn(superAcc);

        // 用户实例上的账号列表
        List<Map<String, Object>> accountList = new ArrayList<>();
        Map<String, Object> account1 = new HashMap<>();
        account1.put("accountName", "testAccount");
        accountList.add(account1);
        Map<String, Object> account2 = new HashMap<>();
        account2.put("accountName", "Alice");
        accountList.add(account2);
        when(dbossApi.queryAccounts(cdo.getId(), null, null,0,accountList.size()))
                .thenReturn(accountList);

        // 查用户实例
        Map<String,Integer> accountCount = new HashMap<>();
        accountCount.put("accounts", accountList.size());
        when(dbossApi.getAccountCount(cdo.getId().toString(),null, RdsConstants.ROLETYPE_USER))
                .thenReturn(accountCount);

        AccountsDTO adto = new AccountsDTO();
        adto.setTaskId(123);
        adto.setAccount("testAccount");

        Map<String, Object> result = createAccountImpl.doActionRequest(cdo, params);

        assertNotNull(result.get("errorCode"));
    }

    // 创建高权限账号，元数据中有高权限账号，用户数据中没有
    @Test
    public void doActionRequest_SuccessSuperDelete_ReturnsSuccessResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        CustInstanceDO cdo = new CustInstanceDO();
        accountHelper(params,cdo);

        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON))
                .thenReturn(CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER);

        // 元数据库中有高权限账号
        cdo.setAccountMode(NEW_CUSTINS_ACCOUNT_MODE_UPGRADE_FINISHED);
        when(accountService.countAccountByCustInsId(cdo.getId())).thenReturn(1);

        AccountsDO superAcc = new AccountsDO();
        superAcc.setAccount("testAccount");
        superAcc.setId(123456);
        when(accountService.getAccount(cdo.getId(), DbsSupport.BIZ_TYPE_USER, AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue()))
                .thenReturn(superAcc);
        when(accountIDao.deleteAccountByIdDirectly(superAcc.getId(), superAcc.getAccount())).thenReturn(1);
        doNothing().when(custinsIDao).updateCustinsAccountMode(cdo);

        // 用户实例上的账号列表
        List<Map<String, Object>> accountList = new ArrayList<>();
        Map<String, Object> account1 = new HashMap<>();
        account1.put("accountName", "Bob");
        accountList.add(account1);
        Map<String, Object> account2 = new HashMap<>();
        account2.put("accountName", "Alice");
        accountList.add(account2);
        when(dbossApi.queryAccounts(cdo.getId(), null, null,0,accountList.size()))
                .thenReturn(accountList);

        // 查用户实例
        Map<String,Integer> accountCount = new HashMap<>();
        accountCount.put("accounts", accountList.size());
        when(dbossApi.getAccountCount(cdo.getId().toString(),null, RdsConstants.ROLETYPE_USER))
                .thenReturn(accountCount);

        AccountsDTO adto = new AccountsDTO();
        adto.setTaskId(123);
        adto.setAccount("testAccount");
        when(accountService.addAccount(any(AccountsDTO.class),anyString(),anyInt(),anyString())).thenReturn(adto);

        doNothing().when(taskService).updateTaskPenginePolicy(anyInt(),anyInt());

        Map<String, Object> result = createAccountImpl.doActionRequest(cdo, params);

        assertNotNull(result);
        assertEquals("testAccount", result.get("AccountName"));
    }

}
