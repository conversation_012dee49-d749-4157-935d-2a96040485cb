package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.service.BlueGreenDeploymentServiceImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/12
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({SwitchHostinsPerfMetaImpl.class, JSON.class})
public class SwitchHostinsPerfMetaImplTest {

	@InjectMocks
	private SwitchHostinsPerfMetaImpl switchHostinsPerfMeta;

	@Mock
	private BlueGreenDeploymentService blueGreenDeploymentService;

	@Mock
	private MysqlParamSupport mysqlParamSupport;

	@Mock
	private CustinsService custinsService;

	CustInstanceDO greenCustins;

	@Before
	public void setUp() throws Exception {
		greenCustins = new CustInstanceDO();
		greenCustins.setId(1);
		greenCustins.setRegionId("testRegionId");
		greenCustins.setInsName("testInsName");
		greenCustins.setInsType(0);

	}

	@Test
	public void testDoActionRequestSuccess() throws Exception {

		// 初始化mock对象
		when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
		when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
		when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(greenCustins);
		when(mysqlParamSupport.getParameterValue(anyMap(), anyString())).thenReturn("testValue");


		when(custinsService.getCustInstanceByInsName(any(), anyString())).thenReturn(new CustInstanceDO());


		// 准备测试数据
		CustInstanceDO custInstance = new CustInstanceDO();
		custInstance.setId(1);
		custInstance.setRegionId("testRegionId");
		custInstance.setInsName("testInsName");
		custInstance.setInsType(1);
		Map<String, String> params = new HashMap<>();
		params.put("Greendbinstancename", "testGreenInstanceName");

		// 模拟依赖行为
		Map<String, Object> data = new HashMap<>();
		data.put("key", "value");
		when(blueGreenDeploymentService.switchHostinsPerfMeta(
			anyInt(), anyInt()
		)).thenReturn(data);

		// 调用方法
		Map<String, Object> response = null;
		try {
			response = switchHostinsPerfMeta.doActionRequest(custInstance, params);
		} catch (NullPointerException e) {
			fail();
		}

	}


	@Test
	public void testDoActionRequestSuccess2() throws Exception {

		// 初始化mock对象
		when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
		when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
		when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(greenCustins);
		when(mysqlParamSupport.getParameterValue(anyMap(), anyString())).thenReturn("testValue");

		when(custinsService.getCustInstanceByInsName(any(), any(), any())).thenReturn(greenCustins);


		// 准备测试数据
		CustInstanceDO custInstance = new CustInstanceDO();
		custInstance.setId(1);
		custInstance.setRegionId("testRegionId");
		custInstance.setInsName("testInsName");
		custInstance.setInsType(1);
		Map<String, String> params = new HashMap<>();
		params.put("Greendbinstancename", "testGreenInstanceName");

		//
		// when(service.switchHostinsPerfMeta(
		// 	anyInt(), anyInt()
		// )).thenThrow(new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_MAXSCALE));

		// 调用被测方法
		Map<String, Object> response = switchHostinsPerfMeta.doActionRequest(custInstance, params);

		// 验证返回结果是否包含预期错误码
		assertNotNull(response);
	}

	@Test(expected = RdsException.class)
	public void testDoActionRequestSuccess3() throws Exception {


		// 初始化mock对象
		when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
		when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
		when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenThrow(new RdsException(ErrorCode.INTERNAL_FAILURE));
		when(mysqlParamSupport.getParameterValue(anyMap(), anyString())).thenReturn("testValue");
		when(custinsService.getCustInstanceByInsName(any(), any(), any())).thenReturn(greenCustins);



		// 准备测试数据
		CustInstanceDO custInstance = new CustInstanceDO();
		custInstance.setId(1);
		custInstance.setRegionId("testRegionId");
		custInstance.setInsName("testInsName");
		custInstance.setInsType(1);
		Map<String, String> params = new HashMap<>();
		params.put("Greendbinstancename", "testGreenInstanceName");



		// when(custinsService.getCustInstanceByInsName(any(), any(), any())).thenThrow(new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_MAXSCALE));
		// doThrow(new RdsException(ErrorCode.UNSUPPORTED_INSTANCE_WITH_MAXSCALE)).when(blueGreenDeploymentService).switchHostinsPerfMeta(any(), any());


		// 调用被测方法
		Map<String, Object> response = switchHostinsPerfMeta.doActionRequest(custInstance, params);

		// 验证返回结果是否包含预期错误码
		assertNotNull(response);
	}
}