package com.aliyun.dba.physical.action.service;

import java.math.BigDecimal;
import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.idao.ZoneIDao;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.HostInfo;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.support.PysicalToPodModifyInsParam;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.AllocateTmpResourceResult;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.resource.dataobject.IpResourceDO;
import com.aliyun.dba.resource.dataobject.RegionAVZonDO;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import javax.validation.constraints.NotNull;

import static com.aliyun.dba.support.property.ParamConstants.DispenseMode.ClassicDispenseMode;
import static com.aliyun.dba.support.property.ParamConstants.DispenseMode.MultiAVZDispenseMode;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TransferPhysicalToK8sServiceTest {
    @InjectMocks
    private TransferPhysicalToK8sService transferPhysicalToK8sService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private AVZSupport avzSupport;

    @Mock
    private InstanceService instanceService;

    @InjectMocks
    AliyunInstanceDependency dependency;

    @Mock
    private CustinsService custinsService;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private DbsService dbsService;

    @Mock
    private IpWhiteListService ipWhiteListService;

    @Mock
    private DTZSupport dtzSupport;

    @Mock
    private TaskService taskService;

    @Mock
    private ResourceService resourceService;

    @Mock
    private HostIDao hostIDao;

    @Mock
    private ZoneIDao zoneIDao;

    @Mock
    private ClusterService clusterService;
    @Mock
    private ConnAddrCustinsService connAddrCustinsService;
    @Mock
    private HostService hostService;
    @Mock
    private ClusterIDao clusterIDao;
    @Mock
    private PodParameterHelper podParameterHelper;

    @Test
    public void testDoActionRequest_hitmap_Success() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("testInstance");
        custins.setLevelId(1);
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "TransferPhysicalToK8sServiceTest_RequestId");
        params.put("RoTargetDBInstanceClass".toLowerCase(), "mysqlro.n2.small.1c");
        PysicalToPodModifyInsParam modifyInsParam = new PysicalToPodModifyInsParam(dependency, params);
        TransferPhysicalToK8sService transferPhysicalToK8sService1 = spy(transferPhysicalToK8sService);
        Mockito.doReturn(modifyInsParam).when(transferPhysicalToK8sService1).initPodModifyInsParam(anyMap());
        Mockito.doReturn("TransferPhysicalToK8sServiceTest_RequestId").when(mysqlParamSupport).getParameterValue(anyMap(), anyString());
        Mockito.doReturn(custins).when(mysqlParamSupport).getAndCheckCustInstance(params);
        Mockito.doNothing().when(transferPhysicalToK8sService1).validatePhysicalToK8s(custins, params);
        AvailableZoneInfoDO masterAvzInfoExParam = new AvailableZoneInfoDO();
        masterAvzInfoExParam.setRegion("cn-beijing-h-aliyun");
        masterAvzInfoExParam.setZoneID("cn-beijing-h");
        masterAvzInfoExParam.setRole("master");
        masterAvzInfoExParam.setUserSpecified(true);
        AvailableZoneInfoDO slaveAvzInfoExParam = new AvailableZoneInfoDO();
        slaveAvzInfoExParam.setRegion("cn-beijing-h-aliyun");
        slaveAvzInfoExParam.setZoneID("cn-beijing-h");
        slaveAvzInfoExParam.setRole("slave");
        slaveAvzInfoExParam.setUserSpecified(true);
        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO> availableZoneInfoList = multiAVZExParamDO.getAvailableZoneInfoList();
        availableZoneInfoList.add(masterAvzInfoExParam);
//        availableZoneInfoList.add(slaveAvzInfoExParam);
        AVZInfo avzInfo = new AVZInfo(MultiAVZDispenseMode, "cn-beijing-h-aliyun", "cn-beijing-h", "", "cn-beijing-h", "", multiAVZExParamDO);
        System.out.println(JSONObject.toJSONString(avzInfo));
        InstanceLevelDO oldLevel = new InstanceLevelDO();
        oldLevel.setClassCode("mysqlro.n2.small.1c");
        CustInstanceDO tempCustins = new CustInstanceDO();
        tempCustins.setInsName("tmp_replicaset");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        readinsList.add(tempCustins);
        AllocateTmpResourceResult allocateTmpResourceResult = new AllocateTmpResourceResult();
        allocateTmpResourceResult.setAllocated(true);
        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();
        resourceRequest.setReplicaSetName("tmp_replicaset");
        allocateTmpResourceResult.setResourceRequest(resourceRequest);
        TransListDO transList = new TransListDO();
        allocateTmpResourceResult.setTransList(transList);
        ClustersDO clustersDO=new ClustersDO();
        clustersDO.setIsMultiSite(0);
        Map<String, Object> result = transferPhysicalToK8sService1.doActionRequest(custins, params);
        assertNotNull(result);
    }
    @Test
    public void validateRoInsType_NotReadOnlyInstance_ThrowsException() {
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isRead()).thenReturn(false);

        RdsException exception = assertThrows(RdsException.class, () -> {
            transferPhysicalToK8sService.validateRoInsType(custins, new HashMap<>());
        });
        assertNotNull(exception.getErrorCode());
    }

    @Test
    public void validateRoInsType_NotStandardInstance_ThrowsException() {
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isRead()).thenReturn(true);
        when(custins.getLevelId()).thenReturn(1);

        InstanceLevelDO oldLevel = mock(InstanceLevelDO.class);
        when(oldLevel.getCategory()).thenReturn("not_standard");
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(oldLevel);

        RdsException exception = assertThrows(RdsException.class, () -> {
            transferPhysicalToK8sService.validateRoInsType(custins, new HashMap<>());
        });
        assertNotNull(exception.getErrorCode());
    }

    @Test
    public void validateRoInsType_NotPhysicalInstance_ThrowsException() {
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isRead()).thenReturn(true);
        when(custins.getLevelId()).thenReturn(1);

        InstanceLevelDO oldLevel = mock(InstanceLevelDO.class);
        when(oldLevel.getCategory()).thenReturn("standard");
        when(oldLevel.getHostType()).thenReturn(2);
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(oldLevel);

        RdsException exception = assertThrows(RdsException.class, () -> {
            transferPhysicalToK8sService.validateRoInsType(custins, new HashMap<>());
        });
        assertNotNull(exception.getErrorCode());
    }

    @Test
    public void validateRoInsType_UnsupportedDbVersion_ThrowsException() {
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isRead()).thenReturn(true);
        when(custins.getLevelId()).thenReturn(1);

        InstanceLevelDO oldLevel = mock(InstanceLevelDO.class);
        when(oldLevel.getCategory()).thenReturn("standard");
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(oldLevel);

        when(custins.getDbVersion()).thenReturn("5.6");

        RdsException exception = assertThrows(RdsException.class, () -> {
            transferPhysicalToK8sService.validateRoInsType(custins, new HashMap<>());
        });
        assertNotNull(exception.getErrorCode());
    }

    @Test
    public void validateStorage() {
        try {
            CustInstanceDO custins = new CustInstanceDO();
            custins.setId(1);
            custins.setDiskSize(100L);
            custins.setInsType(RdsConstants.CUSTINS_INSTYPE_READ);
            custins.setPrimaryCustinsId(456);

            List<CustInstanceDO> readCustInstances = new ArrayList<>();
            Mockito.doReturn(readCustInstances).when(custinsService).getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean());


            Map<String, String> params = new HashMap<>();
            params.put(ParamConstants.REQUEST_ID, "testRequestId");
            params.put(ParamConstants.STORAGE, "100"); // Requested storage greater than current used
            Mockito.doReturn("100").when(mysqlParamSupport).getParameterValue(params, ParamConstants.STORAGE);

            InstancePerfDO instancePerf = new InstancePerfDO();
            Long diskUsed = (new BigDecimal(85*1024)).longValue();
            instancePerf.setDiskCurr(diskUsed.toString()); // Current disk usage
            Mockito.doReturn(instancePerf).when(instanceService).getInstancePerfByCustinsId(anyInt(), anyInt());


            RdsException exception = assertThrows(RdsException.class, () -> {
                transferPhysicalToK8sService.validateTargetStorageSize(custins, params);
            });
            assertNotNull(exception.getErrorCode());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetAvzInfoFromParamAndCluster() {
        try {

            List<InstanceDO> instanceList = new ArrayList<>();
            InstanceDO instanceM = new InstanceDO();
            instanceM.setHostId(123);
            InstanceDO instanceS = new InstanceDO();
            instanceM.setHostId(456);
            instanceList.add(instanceM);
            instanceList.add(instanceS);
            Mockito.doReturn(instanceList).when(instanceService).getInstanceByCustinsId(any());

            Map<String, String> params = new HashMap<>();
            params.put(ParamConstants.REQUEST_ID, "testRequestId");
            params.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SLAVE_LOCATION, "cn-shenzhen-d-aliyun");
            params.put(CustinsParamSupport.CUSTINS_PARAM_NAME_MASTER_LOCATION, "cn-shenzhen-d-aliyun");

            Mockito.doReturn("cn-shenzhen-d-aliyun").when(custinsParamService).getMasterLocation(any());
            Mockito.doReturn(new String[]{}).when(custinsParamService).getSlaveLocations(any());

            List<RegionAVZonDO> regionAVZoneList = new ArrayList<>();
            RegionAVZonDO regionAVZonDO = new RegionAVZonDO("cn-shenzhen", "深圳", "cn-shenzhen-d", "华南 1 可用区 D", "cn-shenzhen-d-aliyun");
            regionAVZoneList.add(regionAVZonDO);
            Mockito.doReturn(regionAVZoneList).when(resourceService).getRegionAVZoneList(any());
//            Mockito.doReturn(regionAVZonDO).when(resourceService).getRegionAVZoneBySubDomain(any());

            HostInfo hostInfo = new HostInfo();
            hostInfo.setHostId(456);
            hostInfo.setRegion("cn-shenzhen");
            hostInfo.setSiteName("sm92");
            hostInfo.setClusterName("cluster");
//            Mockito.doReturn(hostInfo).when(hostIDao).getHostInfoByHostId(any(), any(), any());

//            Mockito.doReturn("cn-shenzhen-d").when(zoneIDao).getZoneIdBySiteAndRegion(any(), any());
            Mockito.doReturn("cn-shenzhen-d-aliyun").when(clusterService).getRegionByCluster(any());

            CustInstanceDO custins = new CustInstanceDO();
            custins.setId(1);
            custins.setDiskSize(100L);
            custins.setInsType(RdsConstants.CUSTINS_INSTYPE_PRIMARY);

            AVZInfo avzInfo = transferPhysicalToK8sService.getAvzInfoFromParamAndCluster(custins);
            System.out.println(JSONObject.toJSONString(avzInfo));
            assertNotNull(avzInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCheckIsClassicDispenseMode(){
        CustInstanceDO custins=new CustInstanceDO();
        CustinsParamDO custinsParamDO=new CustinsParamDO();
        custinsParamDO.setName("dispense_mode");
        custinsParamDO.setValue("ClassicDispenseMode");
        when(custinsParamService.getCustinsParam(any(),any())).thenReturn(custinsParamDO);
        boolean isClassicDispenseMode = transferPhysicalToK8sService.checkIsClassicDispenseMode(custins);
        Assert.assertEquals(true, isClassicDispenseMode);
    }

    @Test
    public void testGenerateAvzInfoForClassicDispenseMode() throws RdsException {
        CustInstanceDO custins=new CustInstanceDO();
        List<ClustersDO> clustersDOS=new ArrayList<>();
        ClustersDO clustersDO=new ClustersDO();
        clustersDO.setIsMultiSite(1);
        clustersDO.setSiteName("NM125_ALIYUN");
        clustersDO.setLocation("location");
        clustersDOS.add(clustersDO);
        List<CustinsConnAddrDO> custinsConnAddrDOList=new ArrayList<>();
        custinsConnAddrDOList.add(new CustinsConnAddrDO());
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(any(),any(),any())).thenReturn(custinsConnAddrDOList);
        when(resourceService.getIpResourceByIpAndVpc(any(),any())).thenReturn(new IpResourceDO());
        List<InstanceDO> instanceDOList=new ArrayList<>();
        instanceDOList.add(new InstanceDO());
        instanceDOList.add(new InstanceDO());
        when(instanceService.getInstanceByCustinsId(any())).thenReturn(instanceDOList);
        HostInfo hostInfo = new HostInfo();
        hostInfo.setSiteName("NM125_ALIYUN");
        when(hostService.getHostInfo(any(),any(),any())).thenReturn(hostInfo);
        when(clusterIDao.getClusters(any())).thenReturn(clustersDOS);
        when(podParameterHelper.getBizType(any(),any())).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);
        ResourceDO resourceDO=new ResourceDO();
        resourceDO.setRealValue("{\"ot7\":\"us-west-1\",\"oc27\":\"us-west-2\",\"hk45\":\"cn-hongkong02\"}");
        when(resourceService.getResourceByResKey(any())).thenReturn(resourceDO);
        List<RegionAVZonDO> regionAVZonDOS=new ArrayList<>();
        regionAVZonDOS.add(new RegionAVZonDO());
        when(resourceService.getRegionAVZoneList(any())).thenReturn(regionAVZonDOS);
        AVZInfo avzInfo = transferPhysicalToK8sService.generateAvzInfoByLBAndHostInfo(custins, "cn-beijing", "requestId");
        Assert.assertEquals("cn-beijing", avzInfo.getRegionId());
    }
    @Test
    public void testGenerateAvzInfoForClassicDispenseMode_hitMap() throws RdsException {
        CustInstanceDO custins=new CustInstanceDO();
        List<ClustersDO> clustersDOS=new ArrayList<>();
        ClustersDO clustersDO=new ClustersDO();
        clustersDO.setIsMultiSite(1);
        clustersDO.setSiteName("ot7");
        clustersDO.setLocation("us-west-1-a-aliyun");
        ClustersDO clustersDO1=new ClustersDO();
        clustersDO1.setIsMultiSite(1);
        clustersDO1.setSiteName("ot7");
        clustersDO1.setLocation("us-west-1");
        clustersDOS.add(clustersDO);
        clustersDOS.add(clustersDO1);
        List<CustinsConnAddrDO> custinsConnAddrDOList=new ArrayList<>();
        custinsConnAddrDOList.add(new CustinsConnAddrDO());
        ResourceDO resourceDO=new ResourceDO();
        resourceDO.setRealValue("{\"ot7\":\"us-west-1\",\"oc27\":\"us-west-2\",\"hk45\":\"cn-hongkong02\"}");
        when(resourceService.getResourceByResKey(any())).thenReturn(resourceDO);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(any(),any(),any())).thenReturn(custinsConnAddrDOList);
        IpResourceDO ipResourceDO = new IpResourceDO();
        ipResourceDO.setSiteName("ot7");
        when(resourceService.getIpResourceByIpAndVpc(any(),any())).thenReturn(ipResourceDO);
        List<InstanceDO> instanceDOList=new ArrayList<>();
        instanceDOList.add(new InstanceDO());
        instanceDOList.add(new InstanceDO());
        when(instanceService.getInstanceByCustinsId(any())).thenReturn(instanceDOList);
        HostInfo hostInfo = new HostInfo();
        hostInfo.setSiteName("ot7");
        when(hostService.getHostInfo(any(),any(),any())).thenReturn(hostInfo);
        when(clusterIDao.getClusters(any())).thenReturn(clustersDOS);
        when(podParameterHelper.getBizType(any(),any())).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);
        List<RegionAVZonDO> regionAVZonDOS=new ArrayList<>();
        regionAVZonDOS.add(new RegionAVZonDO());
        when(resourceService.getRegionAVZoneList(any())).thenReturn(regionAVZonDOS);
        AVZInfo avzInfo = transferPhysicalToK8sService.generateAvzInfoByLBAndHostInfo(custins, "cn-beijing", "requestId");
        Assert.assertEquals("us-west-1", avzInfo.getMultiAVZExParamDO().getMasterLocation());
    }
    @Test
    public void testValidateLocationSupportK8s() throws RdsException {
        CustInstanceDO custins=new CustInstanceDO();
        custins.setId(123);
        Map<String,String> params =new HashMap<>();
        ClustersDO clustersDO=new ClustersDO();
        when(clusterService.getClusterByCustinsId(any())).thenReturn(clustersDO);
        CustinsParamDO custinsParamDO=new CustinsParamDO();
        custinsParamDO.setName("dispense_mode");
        custinsParamDO.setValue("ClassicDispenseMode");
        when(custinsParamService.getCustinsParam(any(),any())).thenReturn(custinsParamDO);
        List<CustinsConnAddrDO> custinsConnAddrDOList=new ArrayList<>();
        custinsConnAddrDOList.add(new CustinsConnAddrDO());
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(any(),any(),any())).thenReturn(custinsConnAddrDOList);
        when(resourceService.getIpResourceByIpAndVpc(any(),any())).thenReturn(new IpResourceDO());
        try {
            transferPhysicalToK8sService.validateLocationSupportK8s(custins, params);
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }
    }
}
