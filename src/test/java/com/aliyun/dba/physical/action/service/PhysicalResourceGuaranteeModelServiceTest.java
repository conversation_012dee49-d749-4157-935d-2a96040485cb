package com.aliyun.dba.physical.action.service;

import com.alicloud.apsaradb.resmanager.HostLevelDistributeRule;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.aliyun.dba.base.common.consts.ResourceScheduleConsts;
import com.aliyun.dba.base.service.ResourceScheduleService;
import com.google.common.collect.ImmutableMap;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PhysicalResourceGuaranteeModelServiceTest {

    @InjectMocks
    private PhysicalResourceGuaranteeModelService physicalResourceGuaranteeModelService;

    @Mock
    private ResourceScheduleService resourceScheduleService;

    @Before
    public void setUp() {
        System.out.println("开始测试------");
    }

    @Test
    public void testAddResourceGuaranteeModelPolicy_WhenResGuaranteeLevelMapIsEmpty_ShouldSkip() {
        ResourceContainer resourceContainer = new ResourceContainer("sub_domain", "mysql");
        resourceContainer.setUserId(123);
        resourceContainer.setUid("testUid");

        Map<String, String> resGuaranteeLevelMap = ImmutableMap.of(
                ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL, "guaranteeLevelValue",
                ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL_BACKUP, "guaranteeLevelBackupValue"
        );

        when(resourceScheduleService.getResGuaranteeModelMapKey()).thenReturn("testMapKey");
        when(resourceScheduleService.getResourceGuaranteeModelPolicyMap(anyInt(), anyString(), anyString())).thenReturn(resGuaranteeLevelMap);

        physicalResourceGuaranteeModelService.addResourceGuaranteeModelPolicy(resourceContainer);


        verify(resourceScheduleService, times(1)).getResGuaranteeModelMapKey();
        verify(resourceScheduleService, times(1)).getResourceGuaranteeModelPolicyMap(123, "testUid", "testMapKey");

        HostLevelDistributeRule hostLevelDistributeRule = resourceContainer.getHostLevelDistributeRule();
        assertEquals("guaranteeLevelValue", hostLevelDistributeRule.getResourceGuaranteeLevel());
        assertEquals("guaranteeLevelBackupValue", hostLevelDistributeRule.getResourceGuaranteeLevelBackup());
    }

    @After
    public void tearDown() {
        System.out.println("测试结束------");
    }

}
