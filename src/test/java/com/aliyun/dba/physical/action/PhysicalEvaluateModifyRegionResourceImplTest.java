package com.aliyun.dba.physical.action;

import afu.org.checkerframework.checker.igj.qual.I;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.TransferPhysicalToK8sService;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(SpringContextUtil.class)
public class PhysicalEvaluateModifyRegionResourceImplTest {
    @InjectMocks
    private EvaluateModifyRegionResourceImpl evaluateModifyRegionResourceImpl;

    @Mock
    private ResourceService resourceService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private StorageCompressionHelper storageCompressionHelper;
    @Mock
    private CustinsService custinsService;
    @Mock
    private ModifyDBInstanceClassImpl modifyDBInstanceClass;
    @Mock
    private TransferPhysicalToK8sService transferPhysicalToK8sService;
    @Mock
    private ClusterService clusterService;
    @Mock
    private InstanceService instanceService;
    @Mock
    private MysqlDBCustinsService mysqlDBCustinsService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBeanByClass(ModifyDBInstanceClassImpl.class)).thenReturn(modifyDBInstanceClass);
        when(SpringContextUtil.getBeanByClass(com.aliyun.dba.physical.action.service.TransferPhysicalToK8sService.class)).thenReturn(transferPhysicalToK8sService);
        System.out.println("开始测试 ------");
    }

    @Ignore
    @Test
    public void evalResource() {
        Map<String, Object> result_map = new HashMap<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        //`kind_code` int(11) DEFAULT '0' COMMENT '@desc 用于标识实例宿主类型,0:物理机，1:ECS VM，2: Docker',
        custInstanceDO.setKindCode(0);
        //  `level_id` int(11) DEFAULT NULL COMMENT '@desc 用户申请的实例级\n',
        custInstanceDO.setLevelId(141);
        custInstanceDO.setUserId(9470);

        Map<String, String> actionParams = new HashMap<>();

        actionParams.put("user_id","0");
        actionParams.put("uid","uelbert01");
//        actionParams.put("inneruserid","0_uelbert01");
//        actionParams.put("dbinstancename","rr-xiwu-1");
        actionParams.put("dbinstancename","rm-testins-03");
        actionParams.put("region","cn-zhangjiakou");



        // EvaluateRegionResourceImpl evaluateRegionResource = new EvaluateRegionResourceImpl();
        try {


            System.out.println("Test Print" + actionParams.toString());
            System.out.println("Test Print" + custInstanceDO.getUserId());
            result_map = evaluateModifyRegionResourceImpl.doActionRequest(custInstanceDO,actionParams);

            System.out.println(Arrays.asList(result_map));
            System.out.println(Collections.singletonList(result_map)); // method 2


        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }

    @Test
    public void test_evaluatePhysicalToK8s() throws RdsException {
        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("false");
        when(resourceService.getResourceByResKey("ALLOW_EXPIRED_INS_MODIFY")).thenReturn(resourceDO);

        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.REQUEST_ID, "requestId");
        actionParams.put(ParamConstants.STORAGE, "100");
        actionParams.put(ParamConstants.TARGET_DB_INSTANCE_CLASS, "targetClass");
        actionParams.put(ParamConstants.UID, "uid");
        actionParams.put(ParamConstants.REGION_ID, "regionId");
        actionParams.put(ParamConstants.TARGET_DB_INSTANCE_CLASS.toLowerCase(),"mysql.x6.large.2c");

        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(1);
        custInstanceDO.setDbType("mysql");
        custInstanceDO.setDbVersion("5.7");
        custInstanceDO.setLevelId(1);
        custInstanceDO.setDiskSize(100L);
        InstanceLevelDO instanceLevelDO=new InstanceLevelDO();
        instanceLevelDO.setDbVersion("5.7");
        instanceLevelDO.setCategory("standard");
        instanceLevelDO.setHostType(0);
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        InstanceLevelDO tinstanceLevelDO=new InstanceLevelDO();
        tinstanceLevelDO.setDbVersion("5.7");
        tinstanceLevelDO.setHostType(2);
        tinstanceLevelDO.setCategory("standard");
        when(instanceService.getInstanceLevelByClassCode(any(),any(),any(),any(),any())).thenReturn(tinstanceLevelDO);
        MultiAVZExParamDO multiAVZExParamDO=new MultiAVZExParamDO();
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, "region", "zoneId", "vSwitchId", "regionId", "regionCategory", multiAVZExParamDO);
        ResourceDO resourceDO1=new ResourceDO();
        resourceDO.setRealValue("{\"ot7\":\"us-west-1\",\"oc27\":\"us-west-2\",\"hk45\":\"cn-hongkong02\"}");
        when(resourceService.getResourceByResKey(any())).thenReturn(resourceDO1);
        when(transferPhysicalToK8sService.generateAvzInfoByLBAndHostInfo(any(),any(),any())).thenReturn(avzInfo);

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custInstanceDO);
        Map<String, Object> result = evaluateModifyRegionResourceImpl.doActionRequest(custInstanceDO, actionParams);
        Assert.assertNotNull(result);
    }

    @After
    public void tearDown() throws Exception {
        System.out.println("finish test ------");
    }


}
