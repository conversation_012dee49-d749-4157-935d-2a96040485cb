package com.aliyun.dba.physical.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.support.property.ParamConstants.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class DescribeDBInstanceStorageCompressionImplTest {
    @InjectMocks
    private DescribeDBInstanceStorageCompressionImpl describeDBInstanceStorageCompression;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private StorageCompressionHelper storageCompressionHelper;
    @Mock
    private MysqlParamSupport paramSupport;
    @Mock
    private MysqlParameterHelper mysqlParaHelper;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private CustinsService custinsService;
    @Mock
    private ResourceService resourceService;

    @Before
    public void setUp() {
        System.out.println("开始测试------");
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
    }

    @Test
    public void doActionRequestSuccessfulTest() throws RdsException, ApiException {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        when(paramSupport.getParameterValue(any(), eq(ParamConstants.REQUEST_ID))).thenReturn("testRequestId");
        when(paramSupport.getParameterValue(any(), eq(ParamConstants.UID))).thenReturn("12345");
        when(mysqlParaHelper.getDBInstanceName()).thenReturn("testInstanceName");
        when(storageCompressionHelper.isCompressionOn(any())).thenReturn(true);
        when(storageCompressionHelper.getPhysicalDiskSizeGB(any())).thenReturn(1100);
        when(storageCompressionHelper.getCustinsCompressionRatio(any())).thenReturn(2.0);

        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(REQUEST_ID, "testRequestId");
        actionParams.put(UID, "12345");
        actionParams.put(REGION, "testRegion");
        actionParams.put(DB_INSTANCE_NAME, "testInstanceName");
        custins.setId(123);
        Map<String, Object> result = describeDBInstanceStorageCompression.doActionRequest(custins, actionParams);
        assertNotNull(result);
    }

    @Test
    public void isSupportCompressionTest() throws Exception {
        try {
            when(resourceService.getResourceByResKey(eq("compression_ration"))).thenReturn(new ResourceDO(){{
                setRealValue("{}");
            }});

            InstanceLevel instanceLevel = new InstanceLevel();
            instanceLevel.setIsolationType(InstanceLevel.IsolationTypeEnum.DEDICATED);
            DefaultApi defaultApi = mock(DefaultApi.class);
            when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
            when(defaultApi.getInstanceLevel(any(), any(), any(), any(), anyBoolean())).thenReturn(instanceLevel);

            List<CustInstanceDO> readCustinsList = new ArrayList<>();
            when(custinsService.getReadCustInstanceListByPrimaryCustinsId(any(), anyBoolean())).thenReturn(readCustinsList);

            ReplicaSet replicaSet = new ReplicaSet();
            replicaSet.setId(123L);
            replicaSet.setName("testInstanceName");
            replicaSet.setDiskSizeMB(1100 * 1024);
            boolean supportCompression = describeDBInstanceStorageCompression.isSupportCompression("12345", replicaSet);
            assertTrue(supportCompression);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
        }
    }
}
