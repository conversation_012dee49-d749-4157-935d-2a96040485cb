package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class DeleteDBInstanceNetTypeImplTest {
    @InjectMocks
    private DeleteDBInstanceNetTypeImpl deleteDBInstanceNetTypeImpl;
    @Mock
    private DBaasMetaService dBaasMetaService;

    private DefaultApi defaultApi;
    @Mock
    private MysqlParamSupport paramSupport;
    @Mock
    private CustinsService custinsService;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultApi = PowerMockito.mock(DefaultApi.class);
        PowerMockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test
    public void isConnectiongStringToSsl() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(1);
        custInstanceDO.setStatus(CustinsSupport.CUSTINS_STATUS_ACTIVE);
        custInstanceDO.setType("x");
        custInstanceDO.setLockMode(2);
        Map<String, String> params = new HashMap<>();
        params.put("connectionstring", "test");
        when(paramSupport.getAndCheckCustInstance(anyMap())).thenReturn(custInstanceDO);
        when(paramSupport.isConnectionStringToSsl(params, custInstanceDO)).thenReturn(true);
        custinsService.checkConnAddrChangeTimesExceed(1, "test",null);
        Map<String, Object> result = deleteDBInstanceNetTypeImpl.doActionRequest(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.INVALID_CONNECTIONSTRING.getCode(), errorArray[0]);
    }
}