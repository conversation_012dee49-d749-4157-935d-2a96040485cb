package com.aliyun.dba.physical.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.physical.action.support.StorageCompressionHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.support.property.ParamConstants.*;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_NAME;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class PhysicalModifyDBInstanceClassImplTest {
    @InjectMocks
    private ModifyDBInstanceClassImpl modifyDBInstanceClass;
    @Mock
    private MysqlParamSupport paramSupport;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private StorageCompressionHelper storageCompressionHelper;
    @Mock
    private CustinsService custinsService;
    @Mock
    private DescribeDBInstanceStorageCompressionImpl describeDBInstanceStorageCompression;
    @Mock
    private ResourceService resourceService;

    @Before
    public void setUp() {
        System.out.println("开始测试------");
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
    }

    @Test
    public void doActionRequestSuccessfulTest() throws Exception {
        try {
            ReplicaSet replicaSet = new ReplicaSet();
            replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
            replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
            when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
            when(paramSupport.getParameterValue(any(), eq(ParamConstants.REQUEST_ID))).thenReturn("testRequestId");
            when(paramSupport.getParameterValue(any(), eq(ParamConstants.UID))).thenReturn("12345");
            when(paramSupport.getParameterValue(any(), eq("compressionMode"))).thenReturn("on");
            when(storageCompressionHelper.isCompressionOn(any())).thenReturn(true);
            when(storageCompressionHelper.getPhysicalDiskSizeGB(any())).thenReturn(1100);
            when(storageCompressionHelper.getCustinsCompressionRatio(any())).thenReturn(2.0);
            when(describeDBInstanceStorageCompression.isSupportCompression(any(), any())).thenReturn(true);
            CustInstanceDO custInstanceDO = new CustInstanceDO();
            custInstanceDO.setId(123);
            custInstanceDO.setInsName("testInstanceName");
            when(custinsService.getCustInstanceByInsName(any(), any())).thenReturn(custInstanceDO);
            when(mysqlParamSupport.getGlobalCompressionRatio(any())).thenReturn(2.0);
            when(mysqlParamSupport.getUserCompresionRatio(any())).thenReturn(2.0);

            when(custinsService.getCustInstanceByInsName(anyInt(), any(), anyInt())).thenReturn(custInstanceDO);
            when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_CUST_INSTANCE_NAME")).thenReturn(null);
            when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_UID")).thenReturn(null);


            Map<String, String> actionParams = new HashMap<>();
            actionParams.put(REQUEST_ID, "testRequestId");
            actionParams.put(ACTION, "ModifyDBInstance");
            actionParams.put(UID, "12345");
            actionParams.put(REGION, "testRegion");
            actionParams.put(DB_INSTANCE_NAME, "testInstanceName");
            actionParams.put("compressionMode", "on");
            Map<String, Object> result = modifyDBInstanceClass.doActionRequest(custInstanceDO, actionParams);
            assertNotNull(result);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void setInstanceCompressionTest(){
        try {
            CustInstanceDO custInstanceDO = new CustInstanceDO();
            custInstanceDO.setId(123);
            custInstanceDO.setRegionId("testRegion");
            modifyDBInstanceClass.setInstanceCompression(custInstanceDO, "on", "12345", 1110L * 1024);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
        }
    }


    @Test
    public void checkAndSetTransTypeLocalRemote_CustInstanceNameInResource_ReturnsLocalRemote() {
        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("instance1");
        PowerMockito.when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_CUST_INSTANCE_NAME")).thenReturn(resourceDO);

        String result = modifyDBInstanceClass.checkAndSetTransTypeLocalRemote("instance1", "123456", "0");
        assertEquals("3", result); // TRANS_TYPE_LOCAL_REMOTE=3
    }

    @Test
    public void checkAndSetTransTypeLocalRemote_UidInResource_ReturnsLocalRemote() {
        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setResKey("TRANS_TYPE_LOCAL_REMOTE_UID");
        resourceDO.setRealValue("123456");
        when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_CUST_INSTANCE_NAME")).thenReturn(null);
        when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_UID")).thenReturn(resourceDO);

        String result = modifyDBInstanceClass.checkAndSetTransTypeLocalRemote("instance1", "123456", "0");
        assertEquals("3", result);
    }

    @Test
    public void checkAndSetTransTypeLocalRemote_NoMatchingResource_ReturnsOriginalType() {
        when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_CUST_INSTANCE_NAME")).thenReturn(null);
        when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_UID")).thenReturn(null);

        String result = modifyDBInstanceClass.checkAndSetTransTypeLocalRemote("instance1", "uid1", "0");
        assertEquals("0", result);
    }

    @Test
    public void checkAndSetTransTypeLocalRemote_ReturnsOriginalType() {
        when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_CUST_INSTANCE_NAME")).thenReturn(null);
        when(resourceService.getResourceByResKey("TRANS_TYPE_LOCAL_REMOTE_UID")).thenReturn(null);

        String result = modifyDBInstanceClass.checkAndSetTransTypeLocalRemote("instance1", "uid1", "2");
        assertEquals("2", result);
    }
}
