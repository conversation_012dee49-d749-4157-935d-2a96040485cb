package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class RepairDBInstanceParameterImplTest {
    @Mock
    protected CustinsService custinsService;

    @Mock
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Mock
    protected InstanceService instanceService;

    @Mock
    protected TaskService taskService;
    @Mock
    protected MysqlParamSupport mysqlParamSupport;
    @Mock
    protected ResourceService resourceService;
    @Mock
    private MysqlParameterHelper mysqlParaHelper;
    @Mock
    protected DbossApi dbossApi;
    @Mock
    protected MycnfService mycnfService;

    @InjectMocks
    RepairDBInstanceParameterImpl repairDBInstanceParameterImpl;

    @Before
    public void init() throws RdsException {
        Map<String, String> params = new HashMap<>();
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);

        Mockito.when(mysqlDBCustinsService.isMysqlEnterprise(Mockito.any()))
                .thenReturn(false);

        List<InstanceDO> instanceList = new ArrayList<>();
        instanceList.add(new InstanceDO());
        instanceList.add(new InstanceDO());
        Mockito.when(instanceService.getInstanceByCustinsId(Mockito.any()))
                .thenReturn(instanceList);

        Mockito.when(taskService.countTaskQueueByCondition(Mockito.any()))
                .thenReturn(0);

        CustInstanceDO custins = mockCustins();
        Mockito.when(mysqlParamSupport.getAndCheckCustInstance(Mockito.any()))
                .thenReturn(custins);
    }

    private CustInstanceDO mockCustins() {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setStatus(1);
        custins.setType("x");
        custins.setInsType(0);
        return custins;
    }

    @Test
    public void testDoBasicCheck() throws RdsException {
        CustInstanceDO custins;

        try {
            custins = mockCustins();
            custins.setStatus(0);
            repairDBInstanceParameterImpl.doBasicCheck(custins);
            Assert.fail();
        } catch (RdsException e) {
            Assert.assertNotNull(e.getErrorCode());
        }

        try {
            custins = mockCustins();
            custins.setType("s");
            repairDBInstanceParameterImpl.doBasicCheck(custins);
            Assert.fail();
        } catch (RdsException e) {
            Assert.assertNotNull(e.getErrorCode());
        }

        try {
            custins = mockCustins();
            Mockito.when(mysqlDBCustinsService.isMysqlEnterprise(Mockito.any()))
                    .thenReturn(true);
            repairDBInstanceParameterImpl.doBasicCheck(custins);
            Assert.fail();
        } catch (RdsException e) {
            Assert.assertNotNull(e.getErrorCode());
        }

        try {
            custins = mockCustins();
            Mockito.when(mysqlDBCustinsService.isMysqlEnterprise(Mockito.any()))
                    .thenReturn(false);

            List<InstanceDO> instanceList = new ArrayList<>();
            Mockito.when(instanceService.getInstanceByCustinsId(Mockito.any()))
                    .thenReturn(instanceList);
            repairDBInstanceParameterImpl.doBasicCheck(custins);
            Assert.fail();
        } catch (RdsException e) {
            Assert.assertNotNull(e.getErrorCode());
        }

        try {
            custins = mockCustins();

            List<InstanceDO> instanceList = new ArrayList<>();
            instanceList.add(new InstanceDO());
            instanceList.add(new InstanceDO());
            Mockito.when(instanceService.getInstanceByCustinsId(Mockito.any()))
                    .thenReturn(instanceList);

            Mockito.when(taskService.countTaskQueueByCondition(Mockito.any()))
                            .thenReturn(1);

            repairDBInstanceParameterImpl.doBasicCheck(custins);
            Assert.fail();
        } catch (RdsException e) {
            Assert.assertNotNull(e.getErrorCode());
        }

        custins = mockCustins();
        Mockito.when(taskService.countTaskQueueByCondition(Mockito.any()))
                .thenReturn(0);
        repairDBInstanceParameterImpl.doBasicCheck(custins);
    }

    @Test
    public void testRepairDefaultTimeZone() throws Exception {
        Mockito.when(mycnfService.getMycnfCustinstance(Mockito.any(), Mockito.any()))
                .thenReturn(null);

        Map<String, Object> params = new HashMap<>();
        Mockito.when(dbossApi.getParameter(Mockito.any(), Mockito.any()))
                .thenReturn(params);

        Mockito.doNothing()
                .when(taskService)
                .createTaskQueue(Mockito.any());

        Mockito.doNothing()
                .when(taskService)
                .updateTaskPenginePolicy(Mockito.any(), Mockito.any());

        CustInstanceDO custins = new CustInstanceDO();
        Map<String, Object> result = repairDBInstanceParameterImpl.repairDefaultTimeZone(custins);
        Assert.assertNotNull(result);
    }


    @Test
    public void testDoActionRequest() throws Exception {
        Map<String, String> params = new HashMap<>();
        Map<String, Object> result = new HashMap<>();

        result = repairDBInstanceParameterImpl.doActionRequest(null, params);
        Assert.assertNotNull(result);

        params.put("ParameterName", "test_parameter");
        result = repairDBInstanceParameterImpl.doActionRequest(null, params);
        Assert.assertNotNull(result);

        params.put("ParameterName", "default_time_zone");
        result = repairDBInstanceParameterImpl.doActionRequest(null, params);
        Assert.assertNotNull(result);
    }
}
