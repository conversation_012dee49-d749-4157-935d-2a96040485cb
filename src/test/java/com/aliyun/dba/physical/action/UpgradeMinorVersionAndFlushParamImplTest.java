//package com.aliyun.dba.physical.action;
//
//import java.util.*;
//
//import com.aliyun.dba.base.parameter.MysqlParameterHelper;
//import com.aliyun.dba.base.service.MysqlEngineCheckService;
//import com.aliyun.dba.base.service.MysqlParamSupport;
//import com.aliyun.dba.custins.dataobject.CustInstanceDO;
//import com.aliyun.dba.custins.service.CustinsService;
//import com.aliyun.dba.custins.service.MycnfService;
//import com.aliyun.dba.instance.service.InstanceService;
//import com.aliyun.dba.support.property.ErrorCode;
//import com.aliyun.dba.support.property.RdsException;
//import com.aliyun.dba.task.service.TaskService;
//import com.aliyun.dba.base.lib.DBaasMetaService;
//import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
//import com.aliyun.dba.custins.dataobject.CustinsParamDO;
//import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
//import com.aliyun.dba.custins.service.CustinsParamService;
//import com.aliyun.dba.custins.service.MinorVersionService;
//import com.aliyun.dba.custins.support.CustinsParamSupport;
//import com.aliyun.dba.kpi.service.KpiService;
//import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import static org.junit.Assert.assertEquals;
//import static org.junit.Assert.assertNotNull;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
//@RunWith(MockitoJUnitRunner.class)
//public class UpgradeMinorVersionAndFlushParamImplTest {
//
//    @InjectMocks
//    private UpgradeMinorVersionAndFlushParamImpl upgradeMinorVersionAndFlushParamImpl;
//
//    @Mock
//    private MysqlParameterHelper mysqlParameterHelper;
//
//    @Mock
//    private CustinsService custinsService;
//
//    @Mock
//    private MysqlParamSupport mysqlParamSupport;
//
//    @Mock
//    private MinorVersionServiceHelper minorVersionServiceHelper;
//
//    @Mock
//    private CustinsParamService custinsParamService;
//
//    @Mock
//    private MinorVersionService minorVersionService;
//
//    @Mock
//    private MysqlEngineCheckService mysqlEngineCheckService;
//
//    @Mock
//    private TaskService taskService;
//
//    @Mock
//    private DBaasMetaService dBaasMetaService;
//
//    @Mock
//    private ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
//
//    @Mock
//    private MycnfService mycnfService;
//
//    @Mock
//    private InstanceService instanceService;
//
//    @Mock
//    private KpiService kpiService;
//
//    @Mock
//    private CustinsParamSupport custinsParamSupport;
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testDoActionRequest_Success() throws RdsException {
//        // Arrange
//        Map<String, String> actionParams = new HashMap<>();
//        actionParams.put("targetminorversion", "rds_20240430");
//        CustInstanceDO custins = new CustInstanceDO();
//        custins.setId(1);
//        custins.setInsName("testInstance");
//        custins.setIsDeleted(0);
//        custins.setType("x");
//        custins.setDbVersion("5.7");
//        custins.setLockMode(0);
//        custins.setStatus(1);
//        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
//        minorVersionReleaseDO.setDbVersion("5.7");
//        List<MinorVersionReleaseDO> list = new ArrayList<>();
//        list.add(minorVersionReleaseDO);
//        List<CustInstanceDO> listdb = new ArrayList<>();
//        CustinsParamDO minorVersionInfo = new CustinsParamDO();
//        minorVersionInfo.setValue("rds_20240130");
//        //when(custinsParamSupport.getParameterValue(anyMap(), anyString())).thenReturn("rds_20240130");
//        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(custins);
//        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(custins);
//        when(custinsService.checkDeletedCustInstanceByInsName(anyString())).thenReturn(false);
//        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(listdb);
//        when(mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(anyMap())).thenReturn(null);
//        when(minorVersionServiceHelper.getMinorVersionCategory(any(CustInstanceDO.class))).thenReturn("category");
//        when(minorVersionServiceHelper.getAndCheckMinorVersionTag(any(CustInstanceDO.class))).thenReturn("tag");
//        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(minorVersionInfo);
//        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion(minorVersionInfo.getValue())).thenReturn("20240130");
//        when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion("rds_20240430")).thenReturn("20240430");
//
//        when(minorVersionService.querySpecifyMinorVersionListByCondition(anyString(), anyString(), anyInt(), anyString(), anyString(), anyString())).thenReturn(list);
//        when(mysqlEngineCheckService.checkCanUpgradeMinorVersionWithMaxScale(any(CustInstanceDO.class), anyString())).thenReturn(true);
//        when(custinsService.getEffectiveTimeMapTimeZoneSafe(anyString(), any(Date.class))).thenReturn(new HashMap<>());
//        when(custinsService.updateCustInstanceStatusByCustinsId(anyInt(), anyInt(), anyString())).thenReturn(1);
//
//
//        // Act
//        Map<String, Object> result = upgradeMinorVersionAndFlushParamImpl.doActionRequest(custins, actionParams);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals("testInstance", result.get("DBInstanceName"));
//    }
//
//    @Test
//    public void testDoActionRequest_InstanceNotFound() throws RdsException {
//        // Arrange
//        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(null);
//        when(custinsService.checkDeletedCustInstanceByInsName(anyString())).thenReturn(false);
//
//        Map<String, String> actionParams = new HashMap<>();
//
//        // Act
//        Map<String, Object> response = upgradeMinorVersionAndFlushParamImpl.doActionRequest(null, actionParams);
//
//        // Assert
//        assertEquals(ErrorCode.DBINSTANCE_NOT_FOUND, response.get("Code"));
//    }
//
//    @Test
//    public void testDoActionRequest_InstanceIsNull() throws RdsException {
//        // Arrange
//        CustInstanceDO custins = new CustInstanceDO();
//        custins.setId(1);
//        custins.setInsName("testInstance");
//
//        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(null);
//
//        Map<String, String> actionParams = new HashMap<>();
//
//        // Act
//        Map<String, Object> response = upgradeMinorVersionAndFlushParamImpl.doActionRequest(custins, actionParams);
//
//        // Assert
//        Object[] errorArray = (Object[]) response.get("errorCode");
//        assertEquals(ErrorCode.DBINSTANCE_NOT_FOUND.getCode(),errorArray[0]);
//    }
//
//    @Test
//    public void testDoActionRequest_InstanceIsDeleted() throws RdsException {
//        // Arrange
//        CustInstanceDO custins = new CustInstanceDO();
//        custins.setId(1);
//        custins.setInsName("testInstance");
//        custins.setIsDeleted(1);
//
//        when(custinsService.checkDeletedCustInstanceByInsName(anyString())).thenReturn(true);
//
//        Map<String, String> actionParams = new HashMap<>();
//
//        // Act
//        Map<String, Object> response = upgradeMinorVersionAndFlushParamImpl.doActionRequest(custins, actionParams);
//
//        // Assert
//        Object[] errorArray = (Object[]) response.get("errorCode");
//        assertEquals(ErrorCode.DBINSTANCE_IS_DELETED.getCode(),errorArray[0]);
//    }
//
//    @Test
//    public void testDoActionRequest_InstanceIsLocked() throws RdsException {
//        // Arrange
//        CustInstanceDO custins = new CustInstanceDO();
//        custins.setId(1);
//        custins.setInsName("testInstance");
//        custins.setLockMode(1);
//
//        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(custins);
//
//        Map<String, String> actionParams = new HashMap<>();
//
//        // Act
//        Map<String, Object> response = upgradeMinorVersionAndFlushParamImpl.doActionRequest(custins, actionParams);
//
//        // Assert
//        Object[] errorArray = (Object[]) response.get("errorCode");
//        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE.getCode(),errorArray[0]);
//    }
//
//    @Test
//    public void testDoActionRequest_InstanceIsShared() throws RdsException {
//        // Arrange
//        CustInstanceDO custins = new CustInstanceDO();
//        custins.setId(1);
//        custins.setInsName("testInstance");
//        custins.setType("s");
//
//        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(custins);
//
//        Map<String, String> actionParams = new HashMap<>();
//
//        // Act
//        Map<String, Object> response = upgradeMinorVersionAndFlushParamImpl.doActionRequest(custins, actionParams);
//
//        // Assert
//        Object[] errorArray = (Object[]) response.get("errorCode");
//        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE.getCode(),errorArray[0]);
//    }
//
//    @Test
//    public void testDoActionRequest_InstanceIsInactive() throws RdsException {
//        // Arrange
//        CustInstanceDO custins = new CustInstanceDO();
//        custins.setId(1);
//        custins.setInsName("testInstance");
//        custins.setStatus(0);
//        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(custins);
//
//        Map<String, String> actionParams = new HashMap<>();
//
//        // Act
//        Map<String, Object> response = upgradeMinorVersionAndFlushParamImpl.doActionRequest(custins, actionParams);
//
//        // Assert
//        Object[] errorArray = (Object[]) response.get("errorCode");
//        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS.getCode(),errorArray[0]);
//    }
//
//    @Test
//    public void testDoActionRequest_Mysql51UpgradeNotSupported() throws RdsException {
//        // Arrange
//        CustInstanceDO custins = new CustInstanceDO();
//        custins.setId(1);
//        custins.setInsName("testInstance");
//        custins.setDbVersion("5.1");
//        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(custins);
//
//        Map<String, String> actionParams = new HashMap<>();
//
//        // Act
//        Map<String, Object> response = upgradeMinorVersionAndFlushParamImpl.doActionRequest(custins, actionParams);
//
//        // Assert
//        Object[] errorArray = (Object[]) response.get("errorCode");
//        assertEquals(ErrorCode.UNSUPPORTED_ENGINE_VERSION.getCode(),errorArray[0]);
//    }
//}
