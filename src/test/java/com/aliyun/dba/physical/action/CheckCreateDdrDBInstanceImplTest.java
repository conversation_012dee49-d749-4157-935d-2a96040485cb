package com.aliyun.dba.physical.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.response.backup.DescribeCrossRegionMigrateNewArchStatusResponse;
import com.aliyun.dba.base.response.backup.DescribeRestoreArchiveLogResponse;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.support.PodDateTimeUtils;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class CheckCreateDdrDBInstanceImplTest {
    @Mock
    DbsGateWayService dbsGateWayService;

    @Mock
    MysqlParamSupport mysqlParamSupport;

    @Mock
    PodDateTimeUtils podDateTimeUtils;

    @Mock
    InstanceService instanceService;

    @InjectMocks
    CheckCreateDdrDBInstanceImpl checkCreateDdrDBInstanceImpl;


    @Before
    public void setUp() throws Exception {
        DescribeCrossRegionMigrateNewArchStatusResponse describeCrossRegionMigrateNewArchStatusResponse = new DescribeCrossRegionMigrateNewArchStatusResponse();
        describeCrossRegionMigrateNewArchStatusResponse.setEnable("true");
        describeCrossRegionMigrateNewArchStatusResponse.setMigrated("true");
        Mockito.doReturn(describeCrossRegionMigrateNewArchStatusResponse)
                .when(dbsGateWayService)
                .describeCrossRegionMigrateNewArchStatus(Mockito.any());


        DescribeRestoreBackupSetResponse describeRestoreBackupSetResponse = new DescribeRestoreBackupSetResponse();
        describeRestoreBackupSetResponse.setRestoreTimeValid(true);
        DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
        backupSetInfo.setConsistentTime(1L);
        describeRestoreBackupSetResponse.setBackupSetInfoObj(backupSetInfo);
        Mockito.doReturn(describeRestoreBackupSetResponse)
                .when(dbsGateWayService)
                .describeRestoreBackupSet(Mockito.any());

        DescribeRestoreArchiveLogResponse describeRestoreArchiveLogResponse = new DescribeRestoreArchiveLogResponse();
        describeRestoreArchiveLogResponse.setRestoreTimeValid(true);
        Mockito.doReturn(describeRestoreArchiveLogResponse)
                .when(dbsGateWayService)
                .describeRestoreArchiveLog(Mockito.any());

        Mockito.doReturn("1")
                .when(mysqlParamSupport)
                .getParameterValue(Mockito.any(), Mockito.any());
        Mockito.doReturn("0")
                .when(mysqlParamSupport)
                .getAndCheckRestoreType(Mockito.any());
        Mockito.doReturn("0")
                .when(mysqlParamSupport)
                .getSourceDBInstanceName(Mockito.any());


        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setCategory("standard");
        Mockito.doReturn(instanceLevelDO)
                .when(instanceService)
                .getInstanceLevelByClassCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());

        Date today = PodDateTimeUtils.getTodayStartTime();
        Mockito.doReturn(today)
                .when(podDateTimeUtils)
                .getUTCDateByDateStr(Mockito.any());
    }

    @Test
    public void testIsBackupByDbsNewArch() {
        boolean result1 = checkCreateDdrDBInstanceImpl.isBackupByDbsNewArch("", "", "", "", "", "");
        Assert.assertFalse(result1);

        boolean result2 = checkCreateDdrDBInstanceImpl.isBackupByDbsNewArch("", "", "", "", "cn-hangzhou", "test_ins_1");
        Assert.assertTrue(result2);
    }

    @Test
    public void testUpdateBackupSetInfoToParams() throws RdsException {
        DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
        Map<String, String> params = new HashMap<>();
        backupSetInfo.setConsistentTime(1L);
        checkCreateDdrDBInstanceImpl.updateBackupSetInfoToParams(backupSetInfo, params);

        try {
            backupSetInfo.setConsistentTime(null);
            checkCreateDdrDBInstanceImpl.updateBackupSetInfoToParams(backupSetInfo, params);
            Assert.fail("expect throw exception");
        } catch (RdsException e) {
            Assert.assertNotNull(e.getErrorCode());
        }
    }

    @Test
    public void testCheckAndUpdateBackupSetParamsByDbsNewArch() throws Exception {
        Map<String, String> params = new HashMap<>();

        try {
            checkCreateDdrDBInstanceImpl.checkAndUpdateBackupSetParamsByDbsNewArch("", "", "", "", params);
        } catch (Exception e) {
           log.info("testCheckAndUpdateBackupSetParamsByDbsNewArch {}", JSONObject.toJSONString(e));
            Assert.fail("expect no exception");
        }
    }


    @Test
    public void testCheckAndUpdateRestoreByTimeParamsByDbsNewArch() throws Exception {
        Map<String, String> params = new HashMap<>();

        try {
            checkCreateDdrDBInstanceImpl.checkAndUpdateRestoreByTimeParamsByDbsNewArch("", "", "", "", params);
        } catch (Exception e) {
            log.info("testCheckAndUpdateRestoreByTimeParamsByDbsNewArch {}", JSONObject.toJSONString(e));
            Assert.fail("expect no exception");
        }
    }

    @Test
    public void testDoCheckAndUpdateDdrRestoreParamsByDbsNewArch() {
        Map<String, String> params = new HashMap<>();

        try {
            checkCreateDdrDBInstanceImpl.doCheckAndUpdateDdrRestoreParamsByDbsNewArch(params);
        } catch (Exception e) {
            log.info("testDoCheckAndUpdateDdrRestoreParamsByDbsNewArch {}", JSONObject.toJSONString(e));
            Assert.fail("expect no exception");
        }
    }

    @Test
    public void testDoCheckDdrRestore() {
        Map<String, String> params = new HashMap<>();

        try {
            checkCreateDdrDBInstanceImpl.doCheckDdrRestore(null, params, "");
        } catch (Exception e) {
            log.info("testDoCheckDdrRestore {}", JSONObject.toJSONString(e));
            Assert.fail("expect no exception");
        }
    }

}
