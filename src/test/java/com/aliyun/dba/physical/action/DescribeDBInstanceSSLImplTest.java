package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(RequestSession.class)
public class DescribeDBInstanceSSLImplTest {

    @InjectMocks
    private DescribeDBInstanceSSLImpl describeDBInstanceSSLImpl;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private CustinsParamService custinsParamService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
    }

    @Test
    public void testDoActionRequest_Success() throws RdsException {
        // Arrange
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("testInstance");
        custins.setDbVersion("8.0");
        custins.setType("x");

        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custins);
        when(custinsParamService.getCustinsParam(anyInt(), eq(CustinsParamSupport.CUSTINS_PARAM_NAME_FORCE_ENCRYPTION))).thenReturn(null);

        // Act
        Map<String, Object> result = describeDBInstanceSSLImpl.doActionRequest(custins, actionParams);

        // Assert
        assertEquals("0", result.get(ParamConstants.FORCE_ENCRYPTION));

    }
}
