package com.aliyun.dba.physical.action;

import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.APP_NAME;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/12
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({EvaluateModifyRegionResourceImpl.class})
public class EvaluateModifyRegionResourceImplTest {

    @InjectMocks
    private EvaluateModifyRegionResourceImpl evaluateModifyRegionResource;

    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private MysqlParamSupport mysqlParamSupport;

    private Map<String, String> params;

    private CustInstanceDO custInstanceDO;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private com.aliyun.apsaradb.activityprovider.api.DefaultApi comDefaultApi;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private PodCommonSupport podCommonSupport;

    @Mock
    private CommonProviderService commonProviderService;

    @Mock
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;

    @Mock
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;


    @Before
    public void setUp() throws Exception {

        params = new HashMap<>();
        custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(123);
        // 初始化mock对象
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));
        when(mysqlParamSupport.getParameterValue(anyMap(), anyString())).thenReturn("testValue");
    }
    
    @Test
    public void testDoActionRequestSuccess() throws Exception {
        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "testRequestId");
        
        // 模拟依赖行为
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        
        // 调用方法
        Map<String, Object> response = evaluateModifyRegionResource.doActionRequest(custInstance, params);
        
        // 验证结果
        assertNotNull(response);
    }

    @Test
    public void testEvaluateWhenBlueGreen() throws Exception {
        String requestId = "requestId";
        Map<String, String> paramForRequestSession = ImmutableMap.of("requestid", UUID.randomUUID().toString());
        RequestSession.init(APP_NAME, paramForRequestSession);
        params.put(ParamConstants.TARGET_DB_INSTANCE_CLASS, "mysql.n4.large.2c");
        params.put("StorageType", "cloud_essd");
        params.put(OPTIMIZED_WRITES, "optimized");
        ReplicaSet replicaSetMeta = new ReplicaSet();
        replicaSetMeta.setName("rm-test");
        replicaSetMeta.setDiskSizeMB(1024 * 100);
        replicaSetMeta.setUserId("testUserId");
        replicaSetMeta.setService("mysql");
        replicaSetMeta.setServiceVersion("8.0");
        replicaSetMeta.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSetMeta.setClassCode("mysql.n2.medium.2c");
        replicaSetMeta.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        User user = new User();
        user.setUserId("testUserId");
        user.setBid("testBid");
        user.setAliUid("testAliUid");
        InstanceLevel targetLevel = new InstanceLevel();
        targetLevel.setExtraInfo("{\"instructionSetArch\":\"arm\"}");
        targetLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        InstanceLevel srcLevel = new InstanceLevel();
        srcLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn(requestId);
        when(mysqlParamSupport.getAndCheckEvaluateNum(params)).thenReturn(1);
        when(mysqlParamSupport.getAndCheckRegionID(params)).thenReturn("cn-test");
        when(mysqlParamSupport.getAndCheckRegion(params)).thenReturn("cn-test");
        when(mysqlParamSupport.getParameterValue(params, "TargetDbInstanceClass")).thenReturn("mysql.n2.medium.2c");
        when(mysqlParamSupport.getAndCheckStorage(params)).thenReturn(100);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        com. aliyun. dba. custins. entity. BlueGreenDeploymentRel rel = new  com.aliyun.dba.custins.entity.BlueGreenDeploymentRel();
        when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(any(),
            any(),
            any(),
            any()))
            .then(invocation -> rel);
        ScheduleTemplate scheduleTemplate = new ScheduleTemplate();
        ReplicaListResult replicaListResult = new ReplicaListResult();
        when(commonProviderService.getDefaultApi()).thenReturn(comDefaultApi);
        //when(commonProviderService.getDefaultApi().evaluateReplicaSetResource(anyString(), anyString(), anyInt(), any())).thenReturn(true);
        when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("rm-test"), eq(null))).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
        when(cloudDiskCompressionHelper.getCompressionRatio(anyString(),eq("rm-test"), any(), any(), any())).thenReturn(2.0);
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setRole(Replica.RoleEnum.MASTER);
        replica.setStorageType(Replica.StorageTypeEnum.CLOUD_ESSD);
        replicas.add(replica);
        replicaListResult.setItems(replicas);
        ServiceSpec serviceSpec = new ServiceSpec();
        serviceSpec.setTag("testTag");
        org.apache.commons.lang3.tuple.Pair<String, ReplicaSet> pair = new Pair<String, ReplicaSet>() {
            @Override
            public String getLeft() {
                return "";
            }
            @Override
            public ReplicaSet getRight() {
                return replicaSetMeta;
            }
            @Override
            public ReplicaSet setValue(ReplicaSet value) {
                return null;
            }
        };
        when(podCommonSupport.getPrimaryReplicaSet(requestId, replicaSetMeta)).thenReturn(pair);
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, 0)).thenReturn(serviceSpec);


        when(defaultApi.getInstanceLevel(requestId, "mysql", "8.0", "mysql.n2.medium.2c", null)).thenReturn(srcLevel);
        when(defaultApi.listReplicasInReplicaSet(requestId, "rm-test", null, null, null, null)).thenReturn(replicaListResult);
        when(defaultApi.getUser(requestId, "testUserId", false)).thenReturn(new com.aliyun.apsaradb.dbaasmetaapi.model.User());


        // mock getAndCheckUserReplicaSet 返回这个实例
        when(replicaSetService.getAndCheckUserReplicaSet(anyMap())).thenReturn(replicaSetMeta);


        //when(minorVersionServiceHelper.getServiceSpecTag("20240630", null, "mysql", "8.0", "MySQL", KIND_CODE_NEW_ARCH, targetLevel, "cloud_essd", false, true, true)).thenReturn("serviceSpec_tag");
        CustInstanceDO custinsDO = new CustInstanceDO();

        Map<String, Object> result = null;
        try {
            result = evaluateModifyRegionResource.doActionRequest(custinsDO, params);
        } catch (RdsException e) {
            fail("Operation prohibited during active blue-green deployment.");
        }
    }

}