package com.aliyun.dba.physical.action;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.physical.action.DescribeDBInstanceCLSImpl;
import com.aliyun.dba.poddefault.action.service.EncdbService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;

@RunWith(MockitoJUnitRunner.class)
public class DescribeDBInstanceCLSImplTest {
    @InjectMocks
    DescribeDBInstanceCLSImpl describeDBInstanceCLS;
    @Mock
    EncdbService encdbService;
    @Mock
    CustinsParamService custinsParamService;

    @Test
    public void testDoActionRequest_success() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(0);
        Map<String, Object> params = new HashMap<>();
        params.put(EncdbService.RES_KEY_ALGO, "AES_256_GCM");
        params.put(EncdbService.RES_KEY_WHITE_LIST_MODE, "true");
        Mockito.when(encdbService.getEncdbParams(any())).thenReturn(params);
        CustinsParamDO custinsParamDO = new CustinsParamDO(0, "xxx", "xxx");
        Mockito.when(custinsParamService.getCustinsParam(any(), any())).thenReturn(custinsParamDO);
        Map<String, Object> result = describeDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        Assertions.assertNotNull(result);
        Assertions.assertEquals(result.get("encryptionKeyMode"), "xxx");
        Assertions.assertEquals(result.get(EncdbService.RES_KEY_ALGO), "AES_256_GCM");
        Assertions.assertEquals(result.get(EncdbService.RES_KEY_WHITE_LIST_MODE), "true");
    }

    @Test
    public void testDoActionReques_encdbServiceRdsException() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(0);
        doThrow(new RdsException(ErrorCode.INTERNAL_FAILURE)).when(encdbService).getEncdbParams(any());
        Map<String, Object> result = describeDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        Assertions.assertNotNull(result);
        assertEquals(ErrorCode.INTERNAL_FAILURE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionReques_encdbServiceIOException() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(0);
        doThrow(new IOException()).when(encdbService).getEncdbParams(any());
        Map<String, Object> result = describeDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        Assertions.assertNotNull(result);
        assertEquals(ErrorCode.INTERNAL_FAILURE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }
}
