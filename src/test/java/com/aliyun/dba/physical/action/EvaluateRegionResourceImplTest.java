package com.aliyun.dba.physical.action;

import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.physical.action.service.TransferPhysicalToK8sService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.user.support.UserSupport;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(SpringContextUtil.class)
public class EvaluateRegionResourceImplTest {

    @InjectMocks
    private EvaluateRegionResourceImpl evaluateRegionResourceImpl;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private InstanceService instanceService;

    @Mock
    private AVZSupport avzSupport;

    @Mock
    private CustinsService custinsService;

    @Mock
    com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl k8sEvaluateRegionResourceImpl;

    @Mock
    TransferPhysicalToK8sService transferPhysicalToK8sService;

    @Before
    public void setUp() {
        System.out.println("开始测试------");
        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBeanByClass(TransferPhysicalToK8sService.class)).thenReturn(transferPhysicalToK8sService);
        when(SpringContextUtil.getBeanByClass(com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl.class)).thenReturn(k8sEvaluateRegionResourceImpl);
    }

    @Test
    public void doActionRequest_SuccessfulEvaluation_ReturnsAvailableData() throws RdsException {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("region", "cn-hangzhou");
        actionParams.put("dbType", "MySQL");
        actionParams.put("classCode", "dds.mongo.mid");
        actionParams.put("dbVersion", "5.7");
        actionParams.put("bizType", "0");
        actionParams.put("uid", "123456");
        actionParams.put("containerType", "HOST");
        actionParams.put("hostType", "2");
        actionParams.put("clusterName", "clusterName");
        actionParams.put("dbInstanceUsedType", "1");
        actionParams.put("evaluateNum", "1");

        custins.setKindCode(0);
        custins.setLevelId(1);
        when(mysqlParamSupport.setInstanceLevel(any(), any(), any(), any())).thenReturn(custins);


        InstanceLevelDO insLevel = new InstanceLevelDO();
        insLevel.setId(1);
        insLevel.setDbType("MySQL");
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(insLevel);

        ResourceContainer resourceContainer = new ResourceContainer("cn-hangzhou", "", "mysql");
        when(avzSupport.getRegionInitialedResourceContainer((Map<String, String>) any(), any())).thenReturn(resourceContainer);

        // Act
        Map<String, Object> result = evaluateRegionResourceImpl.doActionRequest(custins, actionParams);
    }

    @Test
    public void testFromPhysicalToK8Mysql57() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("region", "cn-hangzhou");
        actionParams.put("dbType", "MySQL");
        actionParams.put("classCode", "dds.mongo.mid");
        actionParams.put("dbVersion", "5.7");
        actionParams.put("bizType", "0");
        actionParams.put("uid", "123456");
        actionParams.put("containerType", "HOST");
        actionParams.put("hostType", "2");
        actionParams.put("dbInstanceUsedType", "1");
        actionParams.put("DBInstanceClass".toLowerCase(), "mysqlro.n2.samll.1c");
        actionParams.put("evaluateNum", "1");

        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(3);
        custins.setPrimaryCustinsId(1);
        custins.setType("x");
        custins.setDbType("MySQL");
        custins.setDbVersion("5.7");
        custins.setLevelId(111);

        InstanceLevelDO oldLevel = new InstanceLevelDO();
        oldLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD.getValue());
        oldLevel.setHostType(0);
        InstanceLevelDO newLevel = new InstanceLevelDO();
        newLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD.getValue());
        newLevel.setHostType(2);

        Mockito.doReturn(oldLevel).when(instanceService).getInstanceLevelByLevelId(custins.getLevelId());
        Mockito.doReturn("5.7").when(custinsService).getDBVersion(anyString(), anyString());
        Mockito.doReturn(newLevel).when(instanceService).getInstanceLevelByClassCode("mysqlro.n2.samll.1c", custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
        Mockito.doNothing().when(transferPhysicalToK8sService).validatePhysicalToK8sForRo(custins, actionParams);

        AVZInfo oldAvzInf = new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "cn-beijing", "cn-beijing", "cn", null);
        Mockito.doReturn(oldAvzInf).when(avzSupport).getAVZInfoFromCustInstance(custins);

        Map<String, Object> ret = new HashMap<>();
        ret.put("action", "k8sEvaluateRegionResource");
        Mockito.doReturn(ret).when(k8sEvaluateRegionResourceImpl).doActionRequest(custins, actionParams);

        EvaluateRegionResourceImpl evaluateRegionResourceImplSpy = spy(evaluateRegionResourceImpl);
        Map<String, Object> result = evaluateRegionResourceImplSpy.doActionRequest(custins, actionParams);
        assertEquals(ret, result);
    }

    @Test
    public void testFromPhysicalToK8Mysql80() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("region", "cn-hangzhou");
        actionParams.put("dbType", "MySQL");
        actionParams.put("classCode", "dds.mongo.mid");
        actionParams.put("dbVersion", "8.0");
        actionParams.put("bizType", "0");
        actionParams.put("uid", "123456");
        actionParams.put("containerType", "HOST");
        actionParams.put("hostType", "2");
        actionParams.put("dbInstanceUsedType", "1");
        actionParams.put("DBInstanceClass".toLowerCase(), "mysqlro.n2.samll.1c");
        actionParams.put("evaluateNum", "1");

        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(3);
        custins.setPrimaryCustinsId(1);
        custins.setType("x");
        custins.setDbType("MySQL");
        custins.setDbVersion("8.0");
        custins.setLevelId(1);

        InstanceLevelDO oldLevel = new InstanceLevelDO();
        oldLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD.getValue());
        oldLevel.setHostType(0);
        InstanceLevelDO newLevel = new InstanceLevelDO();
        newLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD.getValue());
        newLevel.setHostType(2);

        Mockito.doReturn(oldLevel).when(instanceService).getInstanceLevelByLevelId(custins.getLevelId());
        Mockito.doReturn("8.0").when(custinsService).getDBVersion(anyString(), anyString());
        Mockito.doReturn(newLevel).when(instanceService).getInstanceLevelByClassCode("mysqlro.n2.samll.1c", custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
        Mockito.doNothing().when(transferPhysicalToK8sService).validatePhysicalToK8sForRo(custins, actionParams);

        AVZInfo oldAvzInf = new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "cn-beijing", "cn-beijing", "cn", null);
        Mockito.doReturn(oldAvzInf).when(avzSupport).getAVZInfoFromCustInstance(custins);

        Map<String, Object> ret = new HashMap<>();
        ret.put("action", "k8sEvaluateRegionResource");
        Mockito.doReturn(ret).when(k8sEvaluateRegionResourceImpl).doActionRequest(custins, actionParams);

        EvaluateRegionResourceImpl evaluateRegionResourceImplSpy = spy(evaluateRegionResourceImpl);
        Map<String, Object> result = evaluateRegionResourceImplSpy.doActionRequest(custins, actionParams);
        assertEquals(ret, result);
    }

    @After
    public void tearDown() throws Exception {
        System.out.println("finish test ------");
    }
}
