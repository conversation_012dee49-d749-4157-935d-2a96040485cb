package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/12
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({SwitchBlueGreenInstancePreCheckImpl.class})
public class SwitchBlueGreenInstancePreCheckImplTest {
    
    @InjectMocks
    private SwitchBlueGreenInstancePreCheckImpl switchBlueGreenInstancePreCheck;
    
    @Mock
    private BlueGreenDeploymentService blueGreenDeploymentService;
    
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    
    @Before
    public void setUp() throws Exception {
       }
    
    @Test
    public void testDoActionRequestSuccess() throws Exception {
        // 初始化mock对象
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));
        when(mysqlParamSupport.getAndCheckDBInstanceName(anyMap())).thenReturn("testDbInstanceName");

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("greendbinstanceid", "testGreenInstanceId");
        params.put("skipstatuscheck", "false");
        
        // 模拟依赖行为
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        when(blueGreenDeploymentService.switchBlueGreenInstancePreCheck(
            anyString(), anyString(), anyString(), 
            any(CustInstanceDO.class), anyString(), 
            anyString(), anyString(), anyBoolean()
        )).thenReturn(data);
        
        // 调用方法
        Map<String, Object> response = switchBlueGreenInstancePreCheck.doActionRequest(custInstance, params);
        
        // 验证结果
        assertNotNull(response);
        verify(mysqlParamSupport, times(1)).getUID(anyMap());
        verify(mysqlParamSupport, times(1)).getBID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckRegionID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckCustInstance(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckDBInstanceName(anyMap());
        verify(blueGreenDeploymentService, times(1)).switchBlueGreenInstancePreCheck(
            anyString(),
            anyString(),
            anyString(),
            any(CustInstanceDO.class),
            anyString(),
            anyString(),
            anyString(),
            anyBoolean()
        );
    }


    @Test(expected = RdsException.class)
    public void testDoActionRequestRdsException() throws Exception {
        // 初始化mock对象
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));
        when(mysqlParamSupport.getAndCheckDBInstanceName(anyMap())).thenReturn("testDbInstanceName");

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("greendbinstanceid", "testGreenInstanceId");
        params.put("skipstatuscheck", "false");

        // 模拟依赖行为
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        when(blueGreenDeploymentService.switchBlueGreenInstancePreCheck(
            anyString(), anyString(), anyString(),
            any(CustInstanceDO.class), anyString(),
            anyString(), anyString(), anyBoolean()
        )).thenThrow(new RdsException(ErrorCode.INTERNAL_FAILURE));

        // 调用方法
        Map<String, Object> response = switchBlueGreenInstancePreCheck.doActionRequest(custInstance, params);


    }

    @Test
    public void testDoActionRequestException() throws Exception {
        // 初始化mock对象
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));
        when(mysqlParamSupport.getAndCheckDBInstanceName(anyMap())).thenReturn("testDbInstanceName");

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("greendbinstanceid", "testGreenInstanceId");
        params.put("skipstatuscheck", "false");

        // 模拟依赖行为
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        when(blueGreenDeploymentService.switchBlueGreenInstancePreCheck(
            anyString(), anyString(), anyString(),
            any(CustInstanceDO.class), anyString(),
            anyString(), anyString(), anyBoolean()
        )).thenThrow(new Exception(ErrorCode.INTERNAL_FAILURE.getSummary()));

        // 调用方法
        Map<String, Object> response = switchBlueGreenInstancePreCheck.doActionRequest(custInstance, params);

    }


}