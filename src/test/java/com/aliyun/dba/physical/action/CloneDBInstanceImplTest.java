package com.aliyun.dba.physical.action;

import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.support.EcsCommonSupport;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.task.service.TaskService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static com.aliyun.dba.base.parameter.MysqlParameterHelper.CLONE_MODE;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.CUSTINS_PARAM_NAME_IS_POLARX_HATP;
import static com.aliyun.dba.support.property.ParamConstants.DispenseMode.MultiAVZDispenseMode;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class, CheckUtils.class, ConnAddrSupport.class})
public class CloneDBInstanceImplTest {
    private static final String DEFAULT_MODE = "default";
    @InjectMocks
    private CloneDBInstanceImpl cloneDBInstance;
    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    private CustinsService custinsService;
    @Mock
    private InstanceService instanceService;
    @Mock
    private CheckService checkService;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private BakService bakService;
    @Mock
    private ClusterService clusterService;
    @Mock
    private MysqlDBCustinsService mysqlDBCustinsService;
    @Mock
    private TaskService taskService;
    @Mock
    private ConnAddrCustinsService connAddrCustinsService;
    @Mock
    private IResApi resApi;
    @Mock
    private ResManagerService resManagerService;
    @Mock
    private ResourceService resourceService;
    @Mock
    private HostService hostService;
    @Mock
    private AVZSupport avzSupport;
    @Mock
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Mock
    private DTZSupport dtzSupport;
    @Mock
    private EcsCommonSupport ecsCommonSupport;
    @Mock
    private CustinsIDao custinsIDao;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(CheckUtils.class);
    }

    @Test
    public void updateCompressionParamsTest(){
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsName("compressionTestIns");
        custins.setDiskSize(1000 * 1024L);
        CustInstanceDO cloneCustins = new CustInstanceDO();
        cloneCustins.setInsName("cloneCompressionTestIns");
        cloneCustins.setDiskSize(1000 * 1024L);
        CustinsParamDO srcCompressionRatioParam = new CustinsParamDO();
        srcCompressionRatioParam.setValue("2.0");
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(srcCompressionRatioParam);
        doNothing().when(custinsParamService).setCustinsParam(anyInt(), anyString(), anyString());
        cloneDBInstance.updateCompressionParams(custins, cloneCustins);
        assertTrue(true);
    }

    @Test
    public void updateCompressionInsDiskSizeTest(){
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsName("compressionTestIns");
        custins.setDiskSize(1000 * 1024L);
        CustInstanceDO cloneCustins = new CustInstanceDO();
        cloneCustins.setInsName("cloneCompressionTestIns");
        cloneCustins.setDiskSize(1000 * 1024L);
        CustinsParamDO srcCompressionRatioParam = new CustinsParamDO();
        srcCompressionRatioParam.setValue("2.0");
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(srcCompressionRatioParam);
        when(custinsService.getCustInstanceByInsName(any(), anyString())).thenReturn(new CustInstanceDO());
        when(custinsService.updateCustInstance(any())).thenReturn(new CustInstanceDO());
        when(instanceService.getInstanceByCustinsIds(any())).thenReturn(new ArrayList<InstanceDO>(){{
            add(new InstanceDO());
        }});
        doNothing().when(instanceService).updateInstanceDiskSizeByInsId(any(), any());
        cloneDBInstance.updateCompressionInsDiskSize(custins, cloneCustins);
        assertEquals(2 * 1000 * 1024L, (long) cloneCustins.getDiskSize());
    }

    @Test
    public void updateSqlAuditParams(){
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsName("test");
        CustinsParamDO srcCompressionRatioParam = new CustinsParamDO();
        srcCompressionRatioParam.setValue("2.0");
        doNothing().when(custinsParamService).setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SQL_LOG, CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SQL_LOG_NO);
        when(custinsParamService.getCustinsParam(custins.getId(),"enable_das_pro")).thenReturn(srcCompressionRatioParam);
        doNothing().when(custinsParamService).deleteCustinsParam(custins.getId(),"enable_das_pro");
        when(custinsParamService.getCustinsParam(custins.getId(),CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_VISIBLE_TIME)).thenReturn(srcCompressionRatioParam);
        when(custinsParamService.getCustinsParam(custins.getId(),CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_RETENTION)).thenReturn(srcCompressionRatioParam);
        doNothing().when(custinsParamService).deleteCustinsParam(custins.getId(),CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_VISIBLE_TIME);
        doNothing().when(custinsParamService).deleteCustinsParam(custins.getId(),CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_RETENTION);
        cloneDBInstance.updateSqlAuditParams(custins);
        assertEquals("test", custins.getInsName());
    }

    @Test
    public void doActionRequest_CloneDBInstanceForSecurity_Success() throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setDbVersion("5.7");
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.ACTION, "CloneDBInstance");
        actionParams.put(ParamConstants.USER_ID, "345");
        actionParams.put("isValidCount", "1");
        actionParams.put("BackupSetID", "567");
        actionParams.put("region", "cn-beijing");
        actionParams.put(ParamConstants.DISPENSE_MODE, "");
        actionParams.put("regionid", "cn-beijing");
        actionParams.put("ZoneID", "cn-beijing-a");
        actionParams.put("VSwitchId", "testvs");
        actionParams.put(CLONE_MODE, DEFAULT_MODE);
        ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);
        when(mysqlParamSupport.getAndCheckSourceCustInstance(actionParams)).thenReturn(custins);
        List<Integer> insIds = new ArrayList<>();
        insIds.add(123);
        when(mysqlParamSupport.getParameterValue(actionParams, ParamConstants.SOURCE_DBINSTANCE_ID)).thenReturn("567");

        when(custinsService.getCustInstanceByInsName(anyInt(), any(), anyInt())).thenReturn(custins);
        when(custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP)).thenReturn(null);
        when(custinsService.getInstanceIdsByCustinsId(custins.getId())).thenReturn(insIds);
        PowerMockito.mockStatic(CheckUtils.class);
        PowerMockito.when(CheckUtils.parseLong("567", null, null, ErrorCode.BACKUPSET_NOT_FOUND)).thenReturn(567L);
        BakhistoryDO history = new BakhistoryDO();
        history.setDbVersion("5.7");
        history.setHisId(567L);
        history.setBaksetSize(1024L);
        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();

        when(bakService.getBakhistoryByBackupSetId(custins.getId(), 0L)).thenReturn(history);
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, "cn-beijing", "cn-beijing", "testvs", "cn-beijing", null, multiAVZExParamDO);
        when(avzSupport.getAVZInfo(actionParams)).thenReturn(avzInfo);
        when(avzSupport.getAVZInfoFromCustInstance(custins)).thenReturn(avzInfo);
        InstanceLevelDO insLevel = new InstanceLevelDO();
        insLevel.setCategory("standard");
        insLevel.setDbVersion("5.7");
        when(instanceService.getInstanceLevelByLevelId(null)).thenReturn(insLevel);
        when(custinsService.getCustinsHostType(custins.getId())).thenReturn("0");
        List<CustinsConnAddrDO> custinsConnAddrList = new ArrayList<>();
        CustinsConnAddrDO custinsConnAddrDO = new CustinsConnAddrDO();
        custinsConnAddrDO.setVport("3302");
        custinsConnAddrList.add(custinsConnAddrDO);
        List<Integer> cr = new ArrayList<>();
        cr.add(1);
        ResourceContainer resourceContainer;
        resourceContainer = new ResourceContainer(avzInfo.getRegion(), custins.getDbType());
        when(avzSupport.getRegionInitialedResourceContainer(avzInfo, custins.getDbType())).thenReturn(resourceContainer);
        PowerMockito.mockStatic(ConnAddrSupport.class);
        PowerMockito.when(ConnAddrSupport.createCustinsConnAddr(any(),any(),any(),any(),any(),any(), any(),any())).thenReturn(custinsConnAddrDO);

        AllocateResRespModel allocateResRespModel = new AllocateResRespModel();
        AllocateResRespModel.CustinsResRespModel custinsResRespModel = new AllocateResRespModel.CustinsResRespModel();
        custinsResRespModel.setClusterName("mock-cluster-name");
        custinsResRespModel.setConnType("mock-conn-type");
        List<Integer> listI = new ArrayList<>();
        listI.add(1);
        custinsResRespModel.setInstanceIdList(listI);
        List<AllocateResRespModel.CustinsResRespModel> c =new ArrayList<>();
        c.add(custinsResRespModel);
        allocateResRespModel.setCustinsResRespModelList(c);
        Response<AllocateResRespModel> response = new Response<>();
        response.setCode(200);
        response.setData(allocateResRespModel);
        when(resManagerService.allocateRes(any())).thenReturn(response);
        Map<String, Object> result = cloneDBInstance.doActionRequest(custins, actionParams);

        assertNotNull(result);
        assertEquals(0, result.get("TaskId"));
    }
}
