package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.commonkindcode.support.ParamChecker;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.physical.action.service.ResManagerService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import io.kubernetes.client.util.common.Collections;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CreateDBInstanceImplTest {

    @InjectMocks
    private CreateDBInstanceImpl createDBInstanceImpl;

    @Mock
    private InstanceService instanceService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private AccountService accountService;

    @Mock
    private DbsService dbsService;

    @Mock
    private IResApi resApi;

    @Mock
    private ResManagerService resManagerService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private MySQLService mySQLService;

    @Mock
    private HostService hostService;


    @Mock
    private ResourceService resourceService;


    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;

    @Mock
    private MysqlParameterHelper mysqlParaHelper;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void setSuperAccount_WithValidParams_SetsSuperAccount() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.SUPER_ACCOUNT_NAME.toLowerCase(), "account1");
        params.put(ParamConstants.ENCRYPT_SUPER_ACCOUNT_PASSWORD.toLowerCase(), "encryptedPassword");

        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");

        AccountsDO account = new AccountsDO();

        when(mysqlParaHelper.hasParameter(ParamConstants.SUPER_ACCOUNT_NAME)).thenReturn(true);
        when(mysqlParaHelper.hasParameter(ParamConstants.ENCRYPT_SUPER_ACCOUNT_PASSWORD)).thenReturn(true);
        when(mysqlParaHelper.getAndCheckSuperAccountPassword()).thenReturn("password");

        // Act
        AccountsDO accountsDO = createDBInstanceImpl.getAccount(custins, params);

        // Assert
        assertEquals("password", accountsDO.getPassword());
        assertEquals("account1", accountsDO.getAccount());
    }

    @Test
    public void setSuperAccount_WithMissingParams_DoesNotSetSuperAccount() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.ACCOUNT_NAME.toLowerCase(), "normal_account");
        params.put(ParamConstants.ACCOUNT_PASSWORD.toLowerCase(), "password1");
        params.put(ParamConstants.SUPER_ACCOUNT_NAME.toLowerCase(), "super_account");

        CustInstanceDO custins = new CustInstanceDO();
        custins.setDbType("mysql");
        custins.setDbVersion("8.0");

        AccountsDO account = new AccountsDO();

        when(mysqlParaHelper.hasParameter(ParamConstants.SUPER_ACCOUNT_NAME)).thenReturn(true);
        when(mysqlParaHelper.hasParameter(ParamConstants.ENCRYPT_SUPER_ACCOUNT_PASSWORD)).thenReturn(false);

        // Act
        AccountsDO accountsDO = createDBInstanceImpl.getAccount(custins, params);
        assertNull(accountsDO);
    }

    @Test
    public void updateCompressionParamsTest(){
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsName("compressionTestIns");
        custins.setDiskSize(1000 * 1024L);
        Map<String, String> params = new HashMap<>();
        params.put("compressionmode", "on");
        params.put("compressionratio", "2.0");
        List< CustinsParamDO > custinsParams = new ArrayList<>();
        createDBInstanceImpl.updateCompressionParams(custins, params, custinsParams);
        assertNotNull(custinsParams);
    }

    @Test
    public void updateCompressionInsDiskSizeTest(){
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsName("compressionTestIns");
        custins.setDiskSize(1000 * 1024L);
        Map<String, String> params = new HashMap<>();
        params.put("compressionmode", "on");
        params.put("compressionratio", "2.0");
        createDBInstanceImpl.updateCompressionInsDiskSize(custins, params);
        assertEquals(2 * 1000 * 1024L, custins.getDiskSize().longValue());
    }
}
