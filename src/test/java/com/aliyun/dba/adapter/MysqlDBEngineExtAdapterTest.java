package com.aliyun.dba.adapter;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/13
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({MysqlDBEngineExtAdapter.class})
public class MysqlDBEngineExtAdapterTest {


	@InjectMocks
	MysqlDBEngineExtAdapter mysqlDBEngineExtAdapter;

	CustInstanceDO custInstance;
	Map<String, String> actionParams;


	@Before
	public void setup() {
		custInstance = new CustInstanceDO();
		custInstance.setId(1);
		actionParams = new HashMap<>();
	}

	@Test
	public void createBlueGreenDeployment() throws RdsException {
		mysqlDBEngineExtAdapter.createBlueGreenDeployment(custInstance, actionParams);
	}

	@Test
	public void switchBlueGreenInstance() throws RdsException {
		mysqlDBEngineExtAdapter.switchBlueGreenInstance(custInstance, actionParams);
	}

	@Test
	public void deleteBlueGreenDeployment() throws RdsException {
		mysqlDBEngineExtAdapter.deleteBlueGreenDeployment(custInstance, actionParams);
	}

	@Test
	public void describeBlueGreenSyncInfo() throws RdsException {
		mysqlDBEngineExtAdapter.describeBlueGreenSyncInfo(custInstance, actionParams);
	}

	@Test
	public void switchBlueGreenInstancePreCheck() throws RdsException {
		mysqlDBEngineExtAdapter.switchBlueGreenInstancePreCheck(custInstance, actionParams);
	}

	@Test
	public void switchHostinsPerfMeta() {
		mysqlDBEngineExtAdapter.switchHostinsPerfMeta(custInstance, actionParams);
	}
}