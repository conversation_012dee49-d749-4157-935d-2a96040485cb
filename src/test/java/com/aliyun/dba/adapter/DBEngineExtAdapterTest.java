package com.aliyun.dba.adapter;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/13
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({DBEngineExtAdapter.class})
public class DBEngineExtAdapterTest {

	@InjectMocks
	MysqlCommonKindcodeDBEngineExtAdapter engineExtAdapter;

	CustInstanceDO custInstance;
	Map<String, String> actionParams;

	@Rule
	public ExpectedException expectedException = ExpectedException.none();


	@Before
	public void setup() {
		custInstance = new CustInstanceDO();
		custInstance.setId(1);
		actionParams = new HashMap<>();
	}

	@Test(expected = UnsupportedEncodingException.class)
	public void createBlueGreenDeployment() throws RdsException, UnsupportedEncodingException {
		engineExtAdapter.createBlueGreenDeployment(custInstance, actionParams);
	}

	@Test
	public void switchBlueGreenInstance() throws RdsException, UnsupportedEncodingException {
		expectedException.expect(UnsupportedEncodingException.class);
		engineExtAdapter.switchBlueGreenInstance(custInstance, actionParams);
	}

	@Test
	public void deleteBlueGreenDeployment() throws RdsException, UnsupportedEncodingException {
		expectedException.expect(UnsupportedEncodingException.class);
		engineExtAdapter.deleteBlueGreenDeployment(custInstance, actionParams);
	}

	@Test
	public void describeBlueGreenSyncInfo() throws RdsException, UnsupportedEncodingException {
		expectedException.expect(UnsupportedEncodingException.class);
		engineExtAdapter.describeBlueGreenSyncInfo(custInstance, actionParams);
	}

	@Test
	public void switchBlueGreenInstancePreCheck() throws RdsException, UnsupportedEncodingException {
		expectedException.expect(UnsupportedEncodingException.class);
		engineExtAdapter.switchBlueGreenInstancePreCheck(custInstance, actionParams);
	}

	@Test
	public void switchHostinsPerfMeta() throws RdsException, UnsupportedEncodingException {
		expectedException.expect(UnsupportedEncodingException.class);
		engineExtAdapter.switchHostinsPerfMeta(custInstance, actionParams);
	}


}