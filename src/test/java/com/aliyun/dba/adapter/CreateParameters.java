package com.aliyun.dba.adapter;

import com.aliyun.bls.tests.TestParameterBase;
import lombok.ToString;

import java.lang.reflect.InvocationTargetException;
import java.util.LinkedHashMap;
import java.util.Map;

@ToString(callSuper = true)
public class CreateParameters extends TestParameterBase {
    String charactersetname;
    String protocol;
    String hbasecoreecsspecine;
    String coreinstancetype;
    String backupsetid;
    String prefix;
    String operatorid;
    String prefermetacluster;
    String dbinstanceregionid;
    String regionid;
    String paytype;
    String zookeeperserver;
    String ins_class;
    String productcode;
    String id;
    String preferredgroupproxyid;
    String masterinstancetype;
    String containertype;
    String buyerid;
    String backupretentionperiod;
    String zoneid;
    String dbname;
    String hostid;
    String dbinstancedescription;
    String clustername;
    String token;
    String nettype;
    String systemdbcharset;
    String dbinstanceclass;
    String securityiplist;
    String isuservpc;
    String accountpassword;
    String dbinstancenodecount;
    String corediskquantity;
    String hosttype;
    String dbinstanceconntype;
    String num;
    String preferredbackautorenew;
    String specifysitenamelist;
    String port;
    String orderid;
    String mongostorageengine;
    String dbinstancenettype;
    String dbinstanceexparam;
    String hbasecorequantity;
    String dbinstancegroupcount;
    String storageengine;
    String coredisksize;
    String inscount;
    String coredisktype;
    String hbasestoragetype;
    String connectionstring;
    String debug;
    String hbasemasterecsspecification;
    String proxyapiversion;
    String restoretype;
    String hbasecoreecsspecification;
    String region;
    String coreinstancequantity;
    String autorenew;
    String preferredbackuptime;
    String isanytunnelvip;
    String accountpasswordaccountname;
    String period;
    String accountname;
    String hatype;
    String targetuid;
    String hbasecorestoragespacegb;
    String servicetype;
    String hbaseversion;
    String optmizationservice;
    String hbaseprotocolversion;
    String bind_proxy_group;
    String preferredbackupperiod;
    String engine;
    String dbinstancetype;
    String sourcedbinstancename;
    String masterdisktype;
    String biztype;
    String shardsinfo;
    String maintainendtime;
    String resourcegroupid;
    String engineversion;
    String restoretime;
    String maintainstarttime;
    String externalparameter;
    String vpcinstanceid;
    String dbinstanceid;
    String multiavzexparam;
    String accountpasswordaccountnamss;
    String dbinstancename;
    String proxynodeversion;
    String sourcedbinstanceid;
    String storage;
    String role;
    String accounttype;
    String commoditycode;
    String keplercluster;
    String tunnelid;
    String keplermetricsurl;
    String keplermetadb;
    String vswitchid;
    String ipaddress;
    String vpcid;
    String targetuserid;
    String enablepartition;

    public CreateParameters(Map<String, Object> obj) throws InvocationTargetException, IllegalAccessException {
        super(obj);
    }

    // if constructed from JUnitParamsRunner need to use concrete type
    public CreateParameters(LinkedHashMap<String, Object> obj) throws InvocationTargetException, IllegalAccessException {
        super(obj);
    }

    public String getCharactersetname() {
        return charactersetname;
    }

    public void setCharactersetname(String charactersetname) {
        this.charactersetname = charactersetname;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getHbasecoreecsspecine() {
        return hbasecoreecsspecine;
    }

    public void setHbasecoreecsspecine(String hbasecoreecsspecine) {
        this.hbasecoreecsspecine = hbasecoreecsspecine;
    }

    public String getCoreinstancetype() {
        return coreinstancetype;
    }

    public void setCoreinstancetype(String coreinstancetype) {
        this.coreinstancetype = coreinstancetype;
    }

    public String getBackupsetid() {
        return backupsetid;
    }

    public void setBackupsetid(String backupsetid) {
        this.backupsetid = backupsetid;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getOperatorid() {
        return operatorid;
    }

    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }

    public String getPrefermetacluster() {
        return prefermetacluster;
    }

    public void setPrefermetacluster(String prefermetacluster) {
        this.prefermetacluster = prefermetacluster;
    }

    public String getDbinstanceregionid() {
        return dbinstanceregionid;
    }

    public void setDbinstanceregionid(String dbinstanceregionid) {
        this.dbinstanceregionid = dbinstanceregionid;
    }

    public String getRegionid() {
        return regionid;
    }

    public void setRegionid(String regionid) {
        this.regionid = regionid;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getZookeeperserver() {
        return zookeeperserver;
    }

    public void setZookeeperserver(String zookeeperserver) {
        this.zookeeperserver = zookeeperserver;
    }

    public String getIns_class() {
        return ins_class;
    }

    public void setIns_class(String ins_class) {
        this.ins_class = ins_class;
    }

    public String getProductcode() {
        return productcode;
    }

    public void setProductcode(String productcode) {
        this.productcode = productcode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPreferredgroupproxyid() {
        return preferredgroupproxyid;
    }

    public void setPreferredgroupproxyid(String preferredgroupproxyid) {
        this.preferredgroupproxyid = preferredgroupproxyid;
    }

    public String getMasterinstancetype() {
        return masterinstancetype;
    }

    public void setMasterinstancetype(String masterinstancetype) {
        this.masterinstancetype = masterinstancetype;
    }

    public String getContainertype() {
        return containertype;
    }

    public void setContainertype(String containertype) {
        this.containertype = containertype;
    }

    public String getBuyerid() {
        return buyerid;
    }

    public void setBuyerid(String buyerid) {
        this.buyerid = buyerid;
    }

    public String getBackupretentionperiod() {
        return backupretentionperiod;
    }

    public void setBackupretentionperiod(String backupretentionperiod) {
        this.backupretentionperiod = backupretentionperiod;
    }

    public String getZoneid() {
        return zoneid;
    }

    public void setZoneid(String zoneid) {
        this.zoneid = zoneid;
    }

    public String getDbname() {
        return dbname;
    }

    public void setDbname(String dbname) {
        this.dbname = dbname;
    }

    public String getHostid() {
        return hostid;
    }

    public void setHostid(String hostid) {
        this.hostid = hostid;
    }

    public String getDbinstancedescription() {
        return dbinstancedescription;
    }

    public void setDbinstancedescription(String dbinstancedescription) {
        this.dbinstancedescription = dbinstancedescription;
    }

    public String getClustername() {
        return clustername;
    }

    public void setClustername(String clustername) {
        this.clustername = clustername;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getNettype() {
        return nettype;
    }

    public void setNettype(String nettype) {
        this.nettype = nettype;
    }

    public String getSystemdbcharset() {
        return systemdbcharset;
    }

    public void setSystemdbcharset(String systemdbcharset) {
        this.systemdbcharset = systemdbcharset;
    }

    public String getDbinstanceclass() {
        return dbinstanceclass;
    }

    public void setDbinstanceclass(String dbinstanceclass) {
        this.dbinstanceclass = dbinstanceclass;
    }

    public String getSecurityiplist() {
        return securityiplist;
    }

    public void setSecurityiplist(String securityiplist) {
        this.securityiplist = securityiplist;
    }

    public String getIsuservpc() {
        return isuservpc;
    }

    public void setIsuservpc(String isuservpc) {
        this.isuservpc = isuservpc;
    }

    public String getAccountpassword() {
        return accountpassword;
    }

    public void setAccountpassword(String accountpassword) {
        this.accountpassword = accountpassword;
    }

    public String getDbinstancenodecount() {
        return dbinstancenodecount;
    }

    public void setDbinstancenodecount(String dbinstancenodecount) {
        this.dbinstancenodecount = dbinstancenodecount;
    }

    public String getCorediskquantity() {
        return corediskquantity;
    }

    public void setCorediskquantity(String corediskquantity) {
        this.corediskquantity = corediskquantity;
    }

    public String getHosttype() {
        return hosttype;
    }

    public void setHosttype(String hosttype) {
        this.hosttype = hosttype;
    }

    public String getDbinstanceconntype() {
        return dbinstanceconntype;
    }

    public void setDbinstanceconntype(String dbinstanceconntype) {
        this.dbinstanceconntype = dbinstanceconntype;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getPreferredbackautorenew() {
        return preferredbackautorenew;
    }

    public void setPreferredbackautorenew(String preferredbackautorenew) {
        this.preferredbackautorenew = preferredbackautorenew;
    }

    public String getSpecifysitenamelist() {
        return specifysitenamelist;
    }

    public void setSpecifysitenamelist(String specifysitenamelist) {
        this.specifysitenamelist = specifysitenamelist;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getMongostorageengine() {
        return mongostorageengine;
    }

    public void setMongostorageengine(String mongostorageengine) {
        this.mongostorageengine = mongostorageengine;
    }

    public String getDbinstancenettype() {
        return dbinstancenettype;
    }

    public void setDbinstancenettype(String dbinstancenettype) {
        this.dbinstancenettype = dbinstancenettype;
    }

    public String getDbinstanceexparam() {
        return dbinstanceexparam;
    }

    public void setDbinstanceexparam(String dbinstanceexparam) {
        this.dbinstanceexparam = dbinstanceexparam;
    }

    public String getHbasecorequantity() {
        return hbasecorequantity;
    }

    public void setHbasecorequantity(String hbasecorequantity) {
        this.hbasecorequantity = hbasecorequantity;
    }

    public String getDbinstancegroupcount() {
        return dbinstancegroupcount;
    }

    public void setDbinstancegroupcount(String dbinstancegroupcount) {
        this.dbinstancegroupcount = dbinstancegroupcount;
    }

    public String getStorageengine() {
        return storageengine;
    }

    public void setStorageengine(String storageengine) {
        this.storageengine = storageengine;
    }

    public String getCoredisksize() {
        return coredisksize;
    }

    public void setCoredisksize(String coredisksize) {
        this.coredisksize = coredisksize;
    }

    public String getInscount() {
        return inscount;
    }

    public void setInscount(String inscount) {
        this.inscount = inscount;
    }

    public String getCoredisktype() {
        return coredisktype;
    }

    public void setCoredisktype(String coredisktype) {
        this.coredisktype = coredisktype;
    }

    public String getHbasestoragetype() {
        return hbasestoragetype;
    }

    public void setHbasestoragetype(String hbasestoragetype) {
        this.hbasestoragetype = hbasestoragetype;
    }

    public String getConnectionstring() {
        return connectionstring;
    }

    public void setConnectionstring(String connectionstring) {
        this.connectionstring = connectionstring;
    }

    public String getDebug() {
        return debug;
    }

    public void setDebug(String debug) {
        this.debug = debug;
    }

    public String getHbasemasterecsspecification() {
        return hbasemasterecsspecification;
    }

    public void setHbasemasterecsspecification(String hbasemasterecsspecification) {
        this.hbasemasterecsspecification = hbasemasterecsspecification;
    }

    public String getProxyapiversion() {
        return proxyapiversion;
    }

    public void setProxyapiversion(String proxyapiversion) {
        this.proxyapiversion = proxyapiversion;
    }

    public String getRestoretype() {
        return restoretype;
    }

    public void setRestoretype(String restoretype) {
        this.restoretype = restoretype;
    }

    public String getHbasecoreecsspecification() {
        return hbasecoreecsspecification;
    }

    public void setHbasecoreecsspecification(String hbasecoreecsspecification) {
        this.hbasecoreecsspecification = hbasecoreecsspecification;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCoreinstancequantity() {
        return coreinstancequantity;
    }

    public void setCoreinstancequantity(String coreinstancequantity) {
        this.coreinstancequantity = coreinstancequantity;
    }

    public String getAutorenew() {
        return autorenew;
    }

    public void setAutorenew(String autorenew) {
        this.autorenew = autorenew;
    }

    public String getPreferredbackuptime() {
        return preferredbackuptime;
    }

    public void setPreferredbackuptime(String preferredbackuptime) {
        this.preferredbackuptime = preferredbackuptime;
    }

    public String getIsanytunnelvip() {
        return isanytunnelvip;
    }

    public void setIsanytunnelvip(String isanytunnelvip) {
        this.isanytunnelvip = isanytunnelvip;
    }

    public String getAccountpasswordaccountname() {
        return accountpasswordaccountname;
    }

    public void setAccountpasswordaccountname(String accountpasswordaccountname) {
        this.accountpasswordaccountname = accountpasswordaccountname;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getAccountname() {
        return accountname;
    }

    public void setAccountname(String accountname) {
        this.accountname = accountname;
    }

    public String getHatype() {
        return hatype;
    }

    public void setHatype(String hatype) {
        this.hatype = hatype;
    }

    public String getTargetuid() {
        return targetuid;
    }

    public void setTargetuid(String targetuid) {
        this.targetuid = targetuid;
    }

    public String getHbasecorestoragespacegb() {
        return hbasecorestoragespacegb;
    }

    public void setHbasecorestoragespacegb(String hbasecorestoragespacegb) {
        this.hbasecorestoragespacegb = hbasecorestoragespacegb;
    }

    public String getServicetype() {
        return servicetype;
    }

    public void setServicetype(String servicetype) {
        this.servicetype = servicetype;
    }

    public String getHbaseversion() {
        return hbaseversion;
    }

    public void setHbaseversion(String hbaseversion) {
        this.hbaseversion = hbaseversion;
    }

    public String getOptmizationservice() {
        return optmizationservice;
    }

    public void setOptmizationservice(String optmizationservice) {
        this.optmizationservice = optmizationservice;
    }

    public String getHbaseprotocolversion() {
        return hbaseprotocolversion;
    }

    public void setHbaseprotocolversion(String hbaseprotocolversion) {
        this.hbaseprotocolversion = hbaseprotocolversion;
    }

    public String getBind_proxy_group() {
        return bind_proxy_group;
    }

    public void setBind_proxy_group(String bind_proxy_group) {
        this.bind_proxy_group = bind_proxy_group;
    }

    public String getPreferredbackupperiod() {
        return preferredbackupperiod;
    }

    public void setPreferredbackupperiod(String preferredbackupperiod) {
        this.preferredbackupperiod = preferredbackupperiod;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getDbinstancetype() {
        return dbinstancetype;
    }

    public void setDbinstancetype(String dbinstancetype) {
        this.dbinstancetype = dbinstancetype;
    }

    public String getSourcedbinstancename() {
        return sourcedbinstancename;
    }

    public void setSourcedbinstancename(String sourcedbinstancename) {
        this.sourcedbinstancename = sourcedbinstancename;
    }

    public String getMasterdisktype() {
        return masterdisktype;
    }

    public void setMasterdisktype(String masterdisktype) {
        this.masterdisktype = masterdisktype;
    }

    public String getBiztype() {
        return biztype;
    }

    public void setBiztype(String biztype) {
        this.biztype = biztype;
    }

    public String getShardsinfo() {
        return shardsinfo;
    }

    public void setShardsinfo(String shardsinfo) {
        this.shardsinfo = shardsinfo;
    }

    public String getMaintainendtime() {
        return maintainendtime;
    }

    public void setMaintainendtime(String maintainendtime) {
        this.maintainendtime = maintainendtime;
    }

    public String getResourcegroupid() {
        return resourcegroupid;
    }

    public void setResourcegroupid(String resourcegroupid) {
        this.resourcegroupid = resourcegroupid;
    }

    public String getEngineversion() {
        return engineversion;
    }

    public void setEngineversion(String engineversion) {
        this.engineversion = engineversion;
    }

    public String getRestoretime() {
        return restoretime;
    }

    public void setRestoretime(String restoretime) {
        this.restoretime = restoretime;
    }

    public String getMaintainstarttime() {
        return maintainstarttime;
    }

    public void setMaintainstarttime(String maintainstarttime) {
        this.maintainstarttime = maintainstarttime;
    }

    public String getExternalparameter() {
        return externalparameter;
    }

    public void setExternalparameter(String externalparameter) {
        this.externalparameter = externalparameter;
    }

    public String getVpcinstanceid() {
        return vpcinstanceid;
    }

    public void setVpcinstanceid(String vpcinstanceid) {
        this.vpcinstanceid = vpcinstanceid;
    }

    public String getDbinstanceid() {
        return dbinstanceid;
    }

    public void setDbinstanceid(String dbinstanceid) {
        this.dbinstanceid = dbinstanceid;
    }

    public String getMultiavzexparam() {
        return multiavzexparam;
    }

    public void setMultiavzexparam(String multiavzexparam) {
        this.multiavzexparam = multiavzexparam;
    }

    public String getAccountpasswordaccountnamss() {
        return accountpasswordaccountnamss;
    }

    public void setAccountpasswordaccountnamss(String accountpasswordaccountnamss) {
        this.accountpasswordaccountnamss = accountpasswordaccountnamss;
    }

    public String getDbinstancename() {
        return dbinstancename;
    }

    public void setDbinstancename(String dbinstancename) {
        this.dbinstancename = dbinstancename;
    }

    public String getProxynodeversion() {
        return proxynodeversion;
    }

    public void setProxynodeversion(String proxynodeversion) {
        this.proxynodeversion = proxynodeversion;
    }

    public String getSourcedbinstanceid() {
        return sourcedbinstanceid;
    }

    public void setSourcedbinstanceid(String sourcedbinstanceid) {
        this.sourcedbinstanceid = sourcedbinstanceid;
    }

    public String getStorage() {
        return storage;
    }

    public void setStorage(String storage) {
        this.storage = storage;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getAccounttype() {
        return accounttype;
    }

    public void setAccounttype(String accounttype) {
        this.accounttype = accounttype;
    }

    public String getCommoditycode() {
        return commoditycode;
    }

    public void setCommoditycode(String commoditycode) {
        this.commoditycode = commoditycode;
    }

    public String getKeplercluster() {
        return keplercluster;
    }

    public void setKeplercluster(String keplercluster) {
        this.keplercluster = keplercluster;
    }

    public String getTunnelid() {
        return tunnelid;
    }

    public void setTunnelid(String tunnelid) {
        this.tunnelid = tunnelid;
    }

    public String getKeplermetricsurl() {
        return keplermetricsurl;
    }

    public void setKeplermetricsurl(String keplermetricsurl) {
        this.keplermetricsurl = keplermetricsurl;
    }

    public String getKeplermetadb() {
        return keplermetadb;
    }

    public void setKeplermetadb(String keplermetadb) {
        this.keplermetadb = keplermetadb;
    }

    public String getVswitchid() {
        return vswitchid;
    }

    public void setVswitchid(String vswitchid) {
        this.vswitchid = vswitchid;
    }

    public String getIpaddress() {
        return ipaddress;
    }

    public void setIpaddress(String ipaddress) {
        this.ipaddress = ipaddress;
    }

    public String getVpcid() {
        return vpcid;
    }

    public void setVpcid(String vpcid) {
        this.vpcid = vpcid;
    }

    public String getTargetuserid() {
        return targetuserid;
    }

    public void setTargetuserid(String targetuserid) {
        this.targetuserid = targetuserid;
    }

    public String getEnablepartition() {
        return enablepartition;
    }

    public void setEnablepartition(String enablepartition) {
        this.enablepartition = enablepartition;
    }
}
