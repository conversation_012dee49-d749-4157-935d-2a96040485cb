package com.aliyun.dba.adapter;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.factory.ActionImplFactory;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.rdscustom.action.support.ECSActionConstant.IS_RDS_CUSTOM;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/13
 **/
@RunWith(PowerMockRunner.class)
@PrepareForTest({BaseDBEngineExtAdapter.class, SpringContextUtil.class})
public class BaseDBEngineExtAdapterTest {

	@InjectMocks
	MysqlOnEcsDBEngineExtAdapter adapter;

	CustInstanceDO custInstance;
	Map<String, String> actionParams;

	@Mock
	MysqlParamSupport mysqlParamSupport;


	@Before
	public void setup() throws ApiException, RdsException {

		mockStatic(SpringContextUtil.class);
		when(SpringContextUtil.getBeanByClass(eq(ActionImplFactory.class))).thenReturn(new ActionImplFactory());


		custInstance = new CustInstanceDO();
		custInstance.setId(1);
		actionParams = new HashMap<>();
		actionParams.put("requestid", "RequestId");
		actionParams.put("action", "CreateDBInstance");
		actionParams.put("uid", "UID");


		// when(adapter.getEnvType(eq(actionParams))).thenReturn("ecs");
		when(mysqlParamSupport.getParameterValue(any(), eq(ServerlessConstant.SERVERLESS_CATEGORY), eq("0"))).thenReturn("1");
		when(mysqlParamSupport.getParameterValue(any(), eq(IS_RDS_CUSTOM), eq("false"))).thenReturn("false");

		when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.ENGINE), any())).thenReturn(null);
		when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.ENGINE_VERSION), any())).thenReturn(null);
		when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.DB_INSTANCE_CLASS), any())).thenReturn(null);
		when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.REQUEST_ID), eq(ServerlessConstant.SERVERLESS_DEFAULT_REQUESTID))).thenReturn("RequestId");

		when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(null);


	}

	@Test
	public void getActionResult() {
		adapter.getActionResult(custInstance, actionParams);
	}
}