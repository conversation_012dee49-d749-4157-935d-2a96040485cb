package com.aliyun.dba.serverless.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InlineResponse200;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2024/12/18 15:59
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({ModifyDBInstanceClassImplTest.class, CustinsSupport.class, ReplicaSetService.class})
public class ModifyDBInstanceClassImplTest {
    private ModifyDBInstanceClassImpl modifyDBInstanceClassImpl;

    private MysqlParamSupport mysqlParamSupport;

    private ReplicaSetService replicaSetService;

    private DBaasMetaService dBaasMetaService;

    private DefaultApi defaultApi;



    @Before
    public void setUp() throws Exception {
        modifyDBInstanceClassImpl = PowerMockito.spy(new ModifyDBInstanceClassImpl());

        mysqlParamSupport = PowerMockito.mock(MysqlParamSupport.class);
        Whitebox.setInternalState(modifyDBInstanceClassImpl, "mysqlParamSupport", mysqlParamSupport);
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), eq("RequestId"))).thenReturn("123456");


        replicaSetService = PowerMockito.mock(ReplicaSetService.class);
        Whitebox.setInternalState(modifyDBInstanceClassImpl, "replicaSetService", replicaSetService);

        PowerMockito.mockStatic(ReplicaSetService.class);
        PowerMockito.when(ReplicaSetService.isTDDL(any())).thenReturn(false);
//        Whitebox.setInternalState(dependency, "replicaSetService", replicaSetService);


        dBaasMetaService = PowerMockito.mock(DBaasMetaService.class);
        Whitebox.setInternalState(modifyDBInstanceClassImpl, "dBaasMetaService", dBaasMetaService);


        defaultApi = PowerMockito.mock(DefaultApi.class);
        PowerMockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setDiskSizeMB(1024 * 200);
        PowerMockito.doReturn(replicaSet).when(replicaSetService, "getAndCheckUserReplicaSet", any());


        PowerMockito.doReturn(1L).when(replicaSetService, "getAutoConfigProvisionedIops", any(), any(), any(), any(), any(), any());
        PowerMockito.doReturn(true).when(replicaSetService, "getAutoConfigBurstingEnabled", any(), any(), any(), any());


    }

    @Test
    public void checkUpdateRemoteScaleFailed() {
        Map<String, String> params = new HashMap<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();

        try {
            PowerMockito.when(defaultApi.getReplicaSetLabel(any(), any(), any())).thenReturn(null);
            PowerMockito.when(defaultApi.updateReplicaSetLabels(any(), any(), any())).thenThrow(new ApiException("test"));
            modifyDBInstanceClassImpl.doActionRequest(custInstanceDO, params);
            Assert.fail();
        } catch (Exception e) {
            assertNotNull(e.getMessage());
        }
    }


    @Test
    public void checkUpdateRemoteScaleSuccess() {
        Map<String, String> params = new HashMap<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();

        try {
            PowerMockito.when(defaultApi.getReplicaSetLabel(any(), any(), any())).thenReturn("true");
            modifyDBInstanceClassImpl.doActionRequest(custInstanceDO, params);
            Assert.fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

}
