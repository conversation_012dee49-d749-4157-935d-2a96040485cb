package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.serverless.action.service.ServerlessCreateReadDBInstanceService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.CheckUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_STORAGE_TYPE;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_READ;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CreateReadDBInstanceImpl.class, CustinsSupport.class, CheckUtils.class})
public class CreateReadDBInstanceImplTest {
    private ReplicaSetService replicaSetService;
    private DBaasMetaService metaService;
    private GdnInstanceService gdnInstanceService;
    private MysqlParameterHelper mysqlParameterHelper;
    private PodCommonSupport podCommonSupport;
    private MysqlEncryptionService mysqlEncryptionService;
    private AliyunInstanceDependency dependency;
    protected MysqlParamSupport mysqlParamSupport;
    private CustinsService custinsService;
    private CustinsParamService custinsParamService;
    private CreateReadDBInstanceImpl createReadDBInstanceImpl;
    private CustInstanceDO custInstanceDO;
    private DefaultApi defaultApi;
    private ServerlessCreateReadDBInstanceService serverlessCreateReadDBInstanceService;
    private MysqlParamSupport paramSupport;

    @Before
    public void setUp() throws Exception {
        createReadDBInstanceImpl = PowerMockito.spy(new CreateReadDBInstanceImpl());
        custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(0);

        mysqlParamSupport = PowerMockito.mock(MysqlParamSupport.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "mysqlParamSupport", mysqlParamSupport);
        PowerMockito.doReturn(custInstanceDO).when(mysqlParamSupport, "getCustInstance", any());

        custinsService = PowerMockito.mock(CustinsService.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "custinsService", custinsService);
        List<CustinsServiceDO> custinsServiceDOS = new ArrayList<>();
        CustinsServiceDO custInstanceDO = new CustinsServiceDO();
        custInstanceDO.setServiceId("0");
        custinsServiceDOS.add(custInstanceDO);
        PowerMockito.doReturn(custinsServiceDOS).when(custinsService, "getCustinsServicesByCustinsIdAndServiceRole", anyInt(), any());

        CustInstanceDO maxscaleDo = new CustInstanceDO();
        maxscaleDo.setStatus(1);
        PowerMockito.doReturn(maxscaleDo).when(custinsService, "getCustInstanceByCustinsId", anyInt());

        custinsParamService = PowerMockito.mock(CustinsParamService.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "custinsParamService", custinsParamService);
        CustinsParamDO custinsMinorVersion = new CustinsParamDO();
        custinsMinorVersion.setValue("0");
        PowerMockito.doReturn(custinsMinorVersion).when(custinsParamService, "getCustinsParam", anyInt(), any());

        dependency = PowerMockito.spy(new AliyunInstanceDependency());
        Whitebox.setInternalState(createReadDBInstanceImpl, "dependency", dependency);
        Whitebox.setInternalState(dependency, "mysqlParamSupport", mysqlParamSupport);

        metaService = PowerMockito.mock(DBaasMetaService.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "metaService", metaService);

        defaultApi = PowerMockito.mock(DefaultApi.class);
        PowerMockito.when(metaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.doReturn(defaultApi).when(metaService, "getRegionClient", any());
        PowerMockito.doReturn(new User()).when(defaultApi, "getUserById", any(), anyInt(), anyBoolean());

        InstanceLevel instanceLevel = new InstanceLevel();;
        instanceLevel.setHostType(0);
        PowerMockito.doReturn(instanceLevel).when(defaultApi, "getInstanceLevel", any(), any(), any(), any(), any());

        podCommonSupport = PowerMockito.mock(PodCommonSupport.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "podCommonSupport", podCommonSupport);

        serverlessCreateReadDBInstanceService = PowerMockito.mock(ServerlessCreateReadDBInstanceService.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "serverlessCreateReadDBInstanceService", serverlessCreateReadDBInstanceService);
        PowerMockito.doReturn(new Object()).when(serverlessCreateReadDBInstanceService, "doCreateReadOnly", any(), any());

        paramSupport = PowerMockito.spy(new MysqlParamSupport());
        Whitebox.setInternalState(createReadDBInstanceImpl, "paramSupport", paramSupport);
        PowerMockito.doReturn("0").when(paramSupport, "getAndCheckDBType", any(), any());
        PowerMockito.doReturn("0").when(paramSupport, "getAndCheckDBVersion", any(), any(), anyBoolean());

        replicaSetService = PowerMockito.mock(ReplicaSetService.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "replicaSetService", replicaSetService);
        ReplicaSet primaryReplicaSet = new ReplicaSet();
        primaryReplicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        primaryReplicaSet.setDiskSizeMB(1024 * 200);
        PowerMockito.doReturn(primaryReplicaSet).when(replicaSetService, "getAndCheckUserReplicaSet", any());

        PowerMockito.mockStatic(CustinsSupport.class);
        PowerMockito.when(CustinsSupport.getConnPort(any(), any())).thenReturn("2000");

        mysqlParameterHelper = PowerMockito.mock(MysqlParameterHelper.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "mysqlParameterHelper", mysqlParameterHelper);
        PowerMockito.doReturn(0).when(mysqlParameterHelper, "getAndCreateUserId");

        PowerMockito.mockStatic(CheckUtils.class);
        PowerMockito.when(CheckUtils.checkValidForInsName(any())).thenReturn("0");
        PowerMockito.when(CheckUtils.parseInt(any(), anyInt(), anyInt(), any())).thenReturn(0);

        mysqlEncryptionService = PowerMockito.mock(MysqlEncryptionService.class);
        Whitebox.setInternalState(createReadDBInstanceImpl, "mysqlEncryptionService", mysqlEncryptionService);
        PowerMockito.doNothing().when(mysqlEncryptionService, "checkEncryptionKeyByReplicaSet", any(), any(), any());
    }

    @Test
    public void doActionRequest() throws RdsException {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "0");
        params.put("dbinstancename", "0");
        params.put(ParamConstants.DB_INSTANCE_USED_TYPE, CUSTINS_INSTYPE_READ.toString());
        params.put("ReadDBInstanceName", "rr-asdf1234as123");
        params.put("TargetMinorVersion", "123");
        params.put("RsTemplateName", "0");
        params.put(ParamConstants.ORDERID, "0");
        params.put(ParamConstants.ACCESSID, "0");
        params.put(ParamConstants.DB_INSTANCE_DESCRIPTION, "asfasf");
        params.put("storagetype", "0");
        params.put("user_id", "0");
        params.put("storage", "200");
        Map<String, String> activities = new HashMap<>();
        activities.put("0", "0");
        String activitiesStr = JSON.toJSONString(activities);
        params.put("activities", activitiesStr);

        createReadDBInstanceImpl.doActionRequest(custInstanceDO, params);
        Assert.assertNotNull(createReadDBInstanceImpl);
    }
}