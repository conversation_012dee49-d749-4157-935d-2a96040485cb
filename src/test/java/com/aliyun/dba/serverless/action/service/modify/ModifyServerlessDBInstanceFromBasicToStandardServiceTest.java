package com.aliyun.dba.serverless.action.service.modify;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
public class ModifyServerlessDBInstanceFromBasicToStandardServiceTest {

    @InjectMocks
    private ModifyServerlessDBInstanceFromBasicToStandardService service;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private ReplicaSetService replicaSetService;

    private CustInstanceDO custins;
    private Map<String, String> params;

    @Before
    public void setUp() throws Exception {
        custins = new CustInstanceDO();
        params = new HashMap<>();

        PowerMockito.when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "")).thenReturn("requestId");
        PowerMockito.when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(new ReplicaSet(){{setStatus(ReplicaSet.StatusEnum.ACTIVE);}});
        PowerMockito.when(replicaSetService.isReplicaSetExternalReplication(any(ReplicaSet.class))).thenReturn(true);
    }

    @Test
    public void doActionRequest_externalReplication() throws RdsException {
        assertNotNull(service.doActionRequest(custins, params));
    }
}