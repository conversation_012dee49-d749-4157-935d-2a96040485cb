package com.aliyun.dba.serverless.action.service;

import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.support.property.ParamConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ServerlessCreateReadDBInstanceService.class, DBaasMetaService.class, PodCommonSupport.class})
public class ServerlessCreateReadDBInstanceServiceTest {
    private ServerlessCreateReadDBInstanceService serverlessCreateReadDBInstanceService;
    private DefaultApi defaultApi;
    private com.aliyun.apsaradb.activityprovider.api.DefaultApi activityDefaultApi;
    private DBaasMetaService metaService;
    private MinorVersionServiceHelper minorVersionServiceHelper;
    private PodCommonSupport podCommonSupport;
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    private ServerlessResourceService serverlessResourceService;
    private CommonProviderService commonProviderService;
    private WorkFlowService workFlowService;
    public PodParameterHelper podParameterHelper;
    CreateReadOnlyInsRequest request;

    @Before
    public void setUp() throws Exception {
        serverlessCreateReadDBInstanceService = PowerMockito.spy(new ServerlessCreateReadDBInstanceService());

        metaService = PowerMockito.mock(DBaasMetaService.class);
        Whitebox.setInternalState(serverlessCreateReadDBInstanceService, "metaService", metaService);

        defaultApi = PowerMockito.mock(DefaultApi.class);
        PowerMockito.when(metaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.doReturn(defaultApi).when(metaService, "getRegionClient", any());

        PowerMockito.doReturn(new CreateReadOnlyInsRequest()).when(serverlessCreateReadDBInstanceService, "buildSpecialRequest", any(), any());
        PowerMockito.doReturn(new HashMap<>()).when(serverlessCreateReadDBInstanceService, "doBasicCheck", any());
        PowerMockito.doNothing().when(serverlessCreateReadDBInstanceService, "doSpecialCheck", any());

        minorVersionServiceHelper = PowerMockito.mock(MinorVersionServiceHelper.class);
        Whitebox.setInternalState(serverlessCreateReadDBInstanceService, "minorVersionServiceHelper", minorVersionServiceHelper);

        podCommonSupport = PowerMockito.mock(PodCommonSupport.class);
        Whitebox.setInternalState(serverlessCreateReadDBInstanceService, "podCommonSupport", podCommonSupport);

        podReplicaSetResourceHelper = PowerMockito.mock(PodReplicaSetResourceHelper.class);
        Whitebox.setInternalState(serverlessCreateReadDBInstanceService, "podReplicaSetResourceHelper", podReplicaSetResourceHelper);

        serverlessResourceService = PowerMockito.mock(ServerlessResourceService.class);
        Whitebox.setInternalState(serverlessCreateReadDBInstanceService, "serverlessResourceService", serverlessResourceService);

        commonProviderService = PowerMockito.mock(CommonProviderService.class);
        Whitebox.setInternalState(serverlessCreateReadDBInstanceService, "commonProviderService", commonProviderService);

        activityDefaultApi = PowerMockito.mock(com.aliyun.apsaradb.activityprovider.api.DefaultApi.class);
        PowerMockito.when(commonProviderService.getDefaultApi()).thenReturn(activityDefaultApi);

        workFlowService = PowerMockito.mock(WorkFlowService.class);
        Whitebox.setInternalState(serverlessCreateReadDBInstanceService, "workFlowService", workFlowService);

        podParameterHelper = PowerMockito.mock(PodParameterHelper.class);
        Whitebox.setInternalState(serverlessCreateReadDBInstanceService, "podParameterHelper", podParameterHelper);

        request = CreateReadOnlyInsRequest.builder().build();
        request.setReadInsReplicaCount(1);
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "", "", "", new MultiAVZExParamDO());
        request.setAvzInfo(avzInfo);
        request.setBizType("");
        request.setServerlessSpec(new ServerlessSpec());
        request.setIsCreatingGdnInstance(false);
        request.setPrimaryReplicaSet(new ReplicaSet());
        request.setBizType("aliyun");
        request.setIsDhg(false);
        request.setIsArmIns(false);
        request.setClassCode("classCode");
        request.setIsSingleTenant(false);
        request.setDiskSize(0);

        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.SERVERLESS_BASIC);
        PowerMockito.when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
    }

    @Test
    public void doCreateReadOnly() throws Exception {
        PowerMockito.mockStatic(PodCommonSupport.class);
        PowerMockito.when(PodCommonSupport.getRoles(any(), any(), eq(true), any())).thenReturn(new Replica.RoleEnum[]{Replica.RoleEnum.MASTER});

        ReplicaSetResource primaryReplicaSetResource = new ReplicaSetResource();
        PowerMockito.when(defaultApi.getReplicaSetBundleResource(any(), any())).thenReturn(primaryReplicaSetResource);
        ReplicaSet replicaSet = new ReplicaSet();
        primaryReplicaSetResource.setReplicaSet(replicaSet);
        Map<String, String> primaryInsLabels = new HashMap<>();
        replicaSet.setLabels(primaryInsLabels);

        ScheduleTemplate replicaSetTemplate = new ScheduleTemplate();
        PowerMockito.doReturn(replicaSetTemplate).when(serverlessCreateReadDBInstanceService, "getReplicaSetTemplate", any(), any());
        PowerMockito.doReturn("").when(serverlessCreateReadDBInstanceService, "getTargetMinorVersionForReadIns", any(), any());

        String primaryMinorVersion = "primaryMinorVersion";
        PowerMockito.when(serverlessCreateReadDBInstanceService, "getTargetMinorVersionForReadIns", any(), any()).thenReturn(primaryMinorVersion);

        String serviceSpecTag = "serviceSpecTag";
        PowerMockito.doReturn(serviceSpecTag).when(minorVersionServiceHelper, "getServiceSpecTag", any(), any(), any(), any(), any(),
                anyInt(), any(), any(), anyBoolean(), anyBoolean(), eq(true));

        PowerMockito.doReturn(false).when(podCommonSupport, "isIoAccelerationEnabled", any());

        ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
        PowerMockito.doReturn(replicaResourceRequest).when(serverlessCreateReadDBInstanceService, "buildReplicaResourceRequestUseZoneId",
                any(), anyInt(), any(), any(), any(), any(), eq(Replica.RoleEnum.MASTER), any(), anyBoolean(), any());

        PowerMockito.doNothing().when(podReplicaSetResourceHelper, "mockReplicaSetResource", any());
        PowerMockito.doReturn(new HashMap<>()).when(serverlessResourceService, "getAnnotations", any(), any(), any());
        PowerMockito.when(activityDefaultApi.allocateReplicaSetResourceV1(anyString(), anyString(), anyObject())).thenReturn(true);

        ReplicaSet repSet = new ReplicaSet();
        repSet.setConnType(ReplicaSet.ConnTypeEnum.PHYSICAL);
        PowerMockito.doReturn(repSet).when(defaultApi, "getReplicaSet", any(), any(), anyBoolean());
        PowerMockito.doNothing().when(serverlessCreateReadDBInstanceService, "syncDbAccountMeta", any());

        Map<String, String> map = new HashMap<>();
        map.put("", "");
        PowerMockito.doReturn(map).when(defaultApi, "listReplicaSetLabels", any(), any());

        PowerMockito.doReturn("").when(minorVersionServiceHelper, "resetReplicaSetMinorVersion", any(), any(), any());
        PowerMockito.doReturn(new InlineResponse200()).when(defaultApi, "updateReplicaSetLabels", any(), any(), any());
        PowerMockito.doReturn(new HashMap<>()).when(serverlessResourceService, "getProxyParam", any());

        Map<String, Object> result = new HashMap<>();
        Map<String, Long> taskInfo = new HashMap<>();
        taskInfo.put("TaskId", 0L);
        result.put("Data", taskInfo);
        PowerMockito.doReturn(result).when(serverlessResourceService, "invokeProxyApi", any());

        PowerMockito.doReturn("").when(workFlowService, "dispatchTask", any(),any(), any(), any(), any(), any());
        PowerMockito.doReturn(0).when(podParameterHelper, "getExtendDiskSizeGBForPod", any(), anyBoolean(), anyInt());

        PowerMockito.doNothing().when(serverlessCreateReadDBInstanceService, "addSpecialInstanceLables", any(), any(), any(), any());

        serverlessCreateReadDBInstanceService.doCreateReadOnly(request, null);
        Assert.assertNotNull(request);
    }

    @Test
    public void getNodeCount() throws ApiException {
        serverlessCreateReadDBInstanceService.getNodeCount(request);
        Assert.assertNotNull(request);
    }
}