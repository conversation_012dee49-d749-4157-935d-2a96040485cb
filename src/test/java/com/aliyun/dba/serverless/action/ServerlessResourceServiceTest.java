package com.aliyun.dba.serverless.action;

import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2025/5/14 17:09
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({ServerlessResourceService.class, DBaasMetaService.class, PodCommonSupport.class})
public class ServerlessResourceServiceTest {
    private ServerlessResourceService serverlessResourceService;

    @Before
    public void setUp() throws Exception {
        serverlessResourceService = PowerMockito.spy(new ServerlessResourceService());
    }


    @Test
    public void testMinTmp() throws Exception {
        String requestId = "1234-5678";
        double current = 3.0;
        double min = 4.0;
        double max = 8.0;

        ServerlessSpec serverlessSpec = new ServerlessSpec();
        serverlessSpec.setScaleMin(min);
        serverlessSpec.setScaleMax(max);
        double result = serverlessResourceService.getRcuForTmpInsByCurrent(requestId, current, serverlessSpec);
        assertEquals(min, result, 0.1);
    }


    @Test
    public void testCurrentTmp() throws Exception {
        String requestId = "1234-5678";
        double current = 8.0;
        double min = 0.5;
        double max = 4.0;

        ServerlessSpec serverlessSpec = new ServerlessSpec();
        serverlessSpec.setScaleMin(min);
        serverlessSpec.setScaleMax(max);
        double result = serverlessResourceService.getRcuForTmpInsByCurrent(requestId, current, serverlessSpec);
        assertEquals(current, result, 0.1);
    }

    @Test
    public void testOtherTmp() throws Exception {
        String requestId = "1234-5678";
        double current = 8.0;
        double min = 0.5;
        double max = 16.0;

        ServerlessSpec serverlessSpec = new ServerlessSpec();
        serverlessSpec.setScaleMin(min);
        serverlessSpec.setScaleMax(max);
        double result = serverlessResourceService.getRcuForTmpInsByCurrent(requestId, current, serverlessSpec);
        assertEquals(12.0, result, 0.1);
    }
}
