package com.aliyun.dba.serverless.action.modify;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.ModifyDBInstanceOptimizedWritesService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.serverless.action.service.modify.ModifyServerlessDBInstanceOptimizedWritesService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES_INFO;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_CLOUD;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_MODIFY_OPTIMIZED_WRITES;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class ModifyServerlessDBInstanceOptimizedWritesServiceTest {
    @InjectMocks
    private ModifyServerlessDBInstanceOptimizedWritesService modifyServerlessDBInstanceOptimizedWritesService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private PodCommonSupport podCommonSupport;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private BaseModifyDBInstanceService baseModifyDBInstanceService;

    @Mock
    private AliyunInstanceDependency dependency;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private WorkFlowService workFlowService;

    @Test
    public void doActionRequest_Success() throws Exception {
        // Arrange
        String primaryOptimizedWritesInfo = "{\"optimizedWrites\":false}";
        boolean targetOptimizedWrites = true;
        HashMap<String, String> params = new HashMap<String, String>();
        params.put(ParamConstants.REQUEST_ID, "requestId");

        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        ReplicaSet replicaSetMeta = Mockito.mock(ReplicaSet.class);
        replicaSetMeta.setPrimaryInsName("testName");

        when(podCommonSupport.isInitOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        //when(modifyInsParam.getCustins()).thenReturn(Mockito.mock(CustInstanceDO.class));
        when(modifyInsParam.getDbInstanceName()).thenReturn("dbInstanceName");
        when(modifyInsParam.getReplicaSetMeta()).thenReturn(Mockito.mock(ReplicaSet.class));
        when(modifyInsParam.getSwitchInfo()).thenReturn(new HashMap<>());
        when(modifyInsParam.getRequestId()).thenReturn("requestId");

        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        List<CustInstanceDO> readCustinsList = new ArrayList<>();
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setInsName("readIns");
        readCustinsList.add(readCustins);

        when(defaultApi.updateReplicaSetStatus("requestId", "dbInstanceName", ReplicaSet.StatusEnum.INS_MAINTAINING.toString())).thenReturn("success");
        //when(custinsService.getReadCustInstanceListByPrimaryCustinsId(123, true)).thenReturn(readCustinsList);

        // Act
        ModifyServerlessDBInstanceOptimizedWritesService modifyServerlessDBInstanceOptimizedWritesServiceSpy = spy(modifyServerlessDBInstanceOptimizedWritesService);
        doReturn(modifyInsParam).when(modifyServerlessDBInstanceOptimizedWritesServiceSpy).initPodModifyInsParam(params);
        Map<String, Object> data = modifyServerlessDBInstanceOptimizedWritesServiceSpy.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
        boolean expected = true;
        boolean actual = (boolean) data.get("targetInitOptimizedWrites");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void doActionRequest_Exception() throws Exception {
        // Arrange
        String primaryOptimizedWritesInfo = "{\"optimizedWrites\":false}";
        boolean targetOptimizedWrites = false;
        HashMap<String, String> params = new HashMap<String, String>();
        params.put(ParamConstants.REQUEST_ID, "requestId");

        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        ReplicaSet replicaSetMeta = Mockito.mock(ReplicaSet.class);
        replicaSetMeta.setPrimaryInsName("testName");

        when(podCommonSupport.isInitOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        //when(modifyInsParam.getCustins()).thenReturn(Mockito.mock(CustInstanceDO.class));
        when(modifyInsParam.getDbInstanceName()).thenReturn("dbInstanceName");
        //when(modifyInsParam.getReplicaSetMeta()).thenReturn(Mockito.mock(ReplicaSet.class));
        when(modifyInsParam.getSwitchInfo()).thenReturn(new HashMap<>());
        when(modifyInsParam.getRequestId()).thenReturn("requestId");

//        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        List<CustInstanceDO> readCustinsList = new ArrayList<>();
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setInsName("readIns");
        readCustinsList.add(readCustins);

        //when(defaultApi.updateReplicaSetStatus("requestId", "dbInstanceName", ReplicaSet.StatusEnum.RESTARTING.toString())).thenReturn("success");
        //when(custinsService.getReadCustInstanceListByPrimaryCustinsId(123, true)).thenReturn(readCustinsList);

        // Act
        ModifyServerlessDBInstanceOptimizedWritesService modifyServerlessDBInstanceOptimizedWritesServiceSpy = spy(modifyServerlessDBInstanceOptimizedWritesService);
        doReturn(modifyInsParam).when(modifyServerlessDBInstanceOptimizedWritesServiceSpy).initPodModifyInsParam(params);
        Map<String, Object> data = modifyServerlessDBInstanceOptimizedWritesServiceSpy.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
        Object[] errorInfo = (Object[])data.get("errorCode");
        Assert.assertEquals("InternalFailure", errorInfo[1]);
        Assert.assertEquals("The request processing has failed due to some unknown error, exception or failure.", errorInfo[2]);
    }
}
