package com.aliyun.dba.serverless.action;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.service.ServerlessEndpointService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.powermock.reflect.Whitebox.invokeMethod;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2025/5/14 17:09
 */

@RunWith(MockitoJUnitRunner.class)
@PrepareForTest({DeleteDBInstanceNetTypeImpl.class})
public class DeleteDBInstanceNetTypeImplTest {
    @InjectMocks
    private DeleteDBInstanceNetTypeImpl deleteDBInstanceNetType;

    @Mock
    private MysqlParameterHelper mysqlParameterHelper;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private ServerlessResourceService serverlessResourceService;

    @Mock
    private MysqlParamSupport paramSupport;

    @Mock
    private ReplicaSet mockReplicaSet;

    @Mock
    private ConnAddrCustinsService connAddrCustinsService;

    @Mock
    private DBaasMetaService metaService;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private ServerlessEndpointService serverlessEndpointService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(mockReplicaSet);
        when(mockReplicaSet.getName()).thenReturn("mockReplicaSetName");

        when(serverlessResourceService.getProxyParam(any())).thenReturn(new HashMap<>());
        Map<String, Object> proxyApiData = new HashMap<>();
        proxyApiData.put("TaskId", "1111");
        Map<String, Object> proxyApiResponse = new HashMap<>();
        proxyApiResponse.put("Data", proxyApiData);
        when(serverlessResourceService.invokeProxyApi(any())).thenReturn(proxyApiResponse);

        when(metaService.getDefaultClient()).thenReturn(defaultApi);
        when(defaultApi.updateReplicaSetStatus(anyString(), anyString(), anyString())).thenReturn("some");

        when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("1111");

        when(serverlessEndpointService.getEndpointGroup(anyString(), anyString(), anyString())).thenReturn("group");
    }

    @Test
    public void testDeleteDBProxyEndpointAddressStatusIsDeleted() throws Exception {
        // Example setup for a mock
        when(mockReplicaSet.getStatus()).thenReturn(ReplicaSet.StatusEnum.DELETED);
        when(mysqlParameterHelper.getParameterValue(any())).thenReturn(null);

        CustInstanceDO mockCustInstance = mock(CustInstanceDO.class);
        Map<String, String> mockParams = new HashMap<>();
        mockParams.put("someKey", "someValue");

        // Invoke the method
        try {
            Object response = invokeMethod(deleteDBInstanceNetType, "deleteDBProxyEndpointAddress", mockCustInstance, mockParams);
            Assert.assertNotNull(response);
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testDeleteDBProxyEndpointAddressConnectionStringIsNull() throws Exception {
        // Example setup for a mock
        when(mockReplicaSet.getStatus()).thenReturn(ReplicaSet.StatusEnum.ACTIVE);
        when(mysqlParameterHelper.getParameterValue(any())).thenReturn(null);
        when(paramSupport.getNetType(any())).thenReturn(null);

        CustInstanceDO mockCustInstance = mock(CustInstanceDO.class);
        Map<String, String> mockParams = new HashMap<>();
        mockParams.put("someKey", "someValue");

        // Invoke the method
        try {
            Object response = invokeMethod(deleteDBInstanceNetType, "deleteDBProxyEndpointAddress", mockCustInstance, mockParams);
            Assert.assertNotNull(response);
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testDeleteDBProxyEndpointAddressConnectionStringIsNotNull() throws Exception {
        // Example setup for a mock
        when(mockReplicaSet.getStatus()).thenReturn(ReplicaSet.StatusEnum.ACTIVE);
        when(mysqlParameterHelper.getParameterValue(any())).thenReturn("mockValue");
        when(paramSupport.getNetType(any())).thenReturn(null);

        CustInstanceDO mockCustInstance = mock(CustInstanceDO.class);
        Map<String, String> mockParams = new HashMap<>();
        mockParams.put("someKey", "someValue");

        CustinsConnAddrDO custinsConnAddrDO = new CustinsConnAddrDO();
        custinsConnAddrDO.setConnAddrCust("mockValue");
        custinsConnAddrDO.setUserVisible(1);
        custinsConnAddrDO.setNetType(1);
        List<CustinsConnAddrDO> custinsConnAddrList = new ArrayList<>();
        custinsConnAddrList.add(custinsConnAddrDO);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), any(), anyInt())).thenReturn(custinsConnAddrList);

        // Invoke the method
        try {
            Object response = invokeMethod(deleteDBInstanceNetType, "deleteDBProxyEndpointAddress", mockCustInstance, mockParams);
            Assert.assertNotNull(response);
        } catch (Exception e) {
            Assert.fail();
        }
    }

}
