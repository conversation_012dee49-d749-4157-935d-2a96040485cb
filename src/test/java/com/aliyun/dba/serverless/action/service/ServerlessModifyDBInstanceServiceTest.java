package com.aliyun.dba.serverless.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.NetProtocolEnum;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodType;
import com.aliyun.dba.support.property.ParamConstants;
import com.google.common.collect.ImmutableList;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
public class ServerlessModifyDBInstanceServiceTest {

    @InjectMocks
    private ServerlessModifyDBInstanceService serverlessModifyDBInstanceService;
    @Mock
    private AliyunInstanceDependency dependency;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private PodCommonSupport podCommonSupport;
    @Mock
    private AligroupService aligroupService;
    @Mock
    private DBaasMetaService dbaasMetaService;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Mock
    private CustinsParamService custinsParamService;

    private Map<String, String> params;

    @Before
    public void setUp() throws Exception {
        params = new HashMap<String, String>();

        PowerMockito.when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PowerMockito.when(dependency.getReplicaSetService()).thenReturn(replicaSetService);
        PowerMockito.when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        PowerMockito.when(dependency.getAligroupService()).thenReturn(aligroupService);
        PowerMockito.when(dependency.getDBaasMetaService()).thenReturn(dbaasMetaService);
        PowerMockito.when(dependency.getPodParameterHelper()).thenReturn(podParameterHelper);
        PowerMockito.when(dependency.getCloudDiskCompressionHelper()).thenReturn(cloudDiskCompressionHelper);
        PowerMockito.when(dependency.getCustinsParamService()).thenReturn(custinsParamService);

        PowerMockito.when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "")).thenReturn("requestId");
        PowerMockito.when(mysqlParamSupport.getDBInstanceName(params)).thenReturn("rm-xxx");
        PowerMockito.when(mysqlParamSupport.getAndCheckCustInstance(params)).thenReturn(new CustInstanceDO(){{setId(1);}});
        PowerMockito.when(mysqlParamSupport.getParameterValue(params, "TargetNetProtocol", null)).thenReturn("IPV4");
        PowerMockito.when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(new ReplicaSet(){{setInsType(InsTypeEnum.MAIN);setName("rm-xxx");setCategory("serverless_basic");setBizType(BizTypeEnum.ALIYUN);setUserId("26842");setConnType(ConnTypeEnum.LVS);setDiskSizeMB(10240);}});
        PowerMockito.when(podCommonSupport.getPrimaryReplicaSet(anyString(), any(ReplicaSet.class))).thenReturn(new Pair<String, ReplicaSet>() {
            @Override
            public String getLeft() {
                return "";
            }

            @Override
            public ReplicaSet getRight() {
                return new ReplicaSet(){{setCategory("serverless_basic");setBizType(BizTypeEnum.ALIYUN);}};
            }

            @Override
            public ReplicaSet setValue(ReplicaSet value) {
                return null;
            }
        });
        PowerMockito.when(podCommonSupport.getReplicaRuntimeType(any())).thenReturn(PodType.POD_ECS_RUND);
        PowerMockito.when(dbaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.when(defaultApi.getUser(anyString(), anyString(), anyBoolean())).thenReturn(new User(){{setBid("26842");setAliUid("12345");}});
        PowerMockito.when(defaultApi.getInstanceLevel(anyString(), any(), any(), any(), any())).thenReturn(new InstanceLevel(){{setCategory(CategoryEnum.SERVERLESS_BASIC);}});
        PowerMockito.when(defaultApi.listReplicasInReplicaSet(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ReplicaListResult(){{setItems(ImmutableList.of(new Replica(){{setId(1L);}}));}});
        PowerMockito.when(defaultApi.getReplica(anyString(), anyLong(), any())).thenReturn(new ReplicaResource());
        PowerMockito.when(podParameterHelper.getParameterValue("EmergencyTransfer", "0")).thenReturn("1");
        PowerMockito.when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(new CustinsParamDO(){{setValue("value");}});
    }

    @Test
    public void initServerlessConfigPodModifyInsParam() {
        try {
            serverlessModifyDBInstanceService.initServerlessConfigPodModifyInsParam(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void initServerlessSccModifyInsParam() {
        try {
            serverlessModifyDBInstanceService.initServerlessSccModifyInsParam(params);
        } catch (Exception e) {
            assertNotNull(e);
        }
    }
}