package com.aliyun.dba.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.dba.adapter.BaseDBEngineExtAdapter;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.BlueGreenDeploymentCommonService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/13
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({BlueGreenDeploymentServiceImpl.class})
public class BlueGreenDeploymentServiceImp2lTest {
	@InjectMocks
	private BlueGreenDeploymentServiceImpl blueGreenDeploymentService;

	@Mock
	private BlueGreenDeploymentCommonService mockCommonService;

	@Mock
	private BlueGreenDeploymentRelServiceIDao mockRelDao;

	@Mock
	private WorkFlowService mockWorkFlowService;

	@Mock
	private DBaasMetaService mockDBaasMetaService;

	@Mock
	DefaultApi mockdefaultApi;

	@Before
	public void setUp() throws Exception {
		// 初始化被测类并注入 mock 依赖
		blueGreenDeploymentService = new BlueGreenDeploymentServiceImpl();

		when(mockDBaasMetaService.getDefaultClient()).thenReturn(mockdefaultApi);
		when(mockdefaultApi.getReplicaSetLabel(any(), any(),any())).thenReturn("134");

	}

	@Test
	public void testCreateBlueGreenDeployment_Success() throws Exception {
		// 准备输入参数
		String requestId = "req123";
		String regionId = "cn-hangzhou";
		String aliUid = "uid123";
		CustInstanceDO custInstance = new CustInstanceDO();
		custInstance.setId(1);
		custInstance.setInsName("dbinstance1");

		Map<String, Object> newPrimaryConfig = new HashMap<>();
		newPrimaryConfig.put("instanceType", "rds.mysql.c1.large");

		// 模拟返回值
		when(mockCommonService.generateBlueGreenDeploymentName()).thenReturn("deployment-123");
		when(mockCommonService.getGreenNormalInstanceConfig(any(), any(), any(), any(), any()))
			.thenReturn(new JSONObject());

		when(mockRelDao.createBlueGreenDeploymentRel(any())).thenReturn(1);

		when(mockWorkFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt()))
			.thenReturn("taskId123");

		// 调用方法
		try {
			Map<String, Object> result = blueGreenDeploymentService.createBlueGreenDeployment(
				requestId, regionId, aliUid, custInstance,
				newPrimaryConfig, null, null, null);
		} catch (Exception e) {
			fail();
		}

	}

	@Test
	public void testCreateBlueGreenDeployment_PreCheckFails() throws Exception {
		// 准备输入参数
		CustInstanceDO custInstance = new CustInstanceDO();
		custInstance.setId(1);
		custInstance.setInsName("dbinstance1");

		// 模拟抛出异常
		doThrow(new RdsException(ErrorCode.INVALID_STATUS))
			.when(mockCommonService).preCheckWhenCreating(anyString(), eq(custInstance));

		// 调用方法，应抛出异常
		blueGreenDeploymentService.createBlueGreenDeployment(
			"req123", "cn-hangzhou", "uid123", custInstance,
			new HashMap<>(), null, null, null);
	}

	@Test
	public void testCreateBlueGreenDeployment_TaskDispatchFails() throws Exception {
		// 准备输入参数
		CustInstanceDO custInstance = new CustInstanceDO();
		custInstance.setId(1);
		custInstance.setInsName("dbinstance1");

		// 模拟行为
		when(mockCommonService.generateBlueGreenDeploymentName()).thenReturn("deployment-123");
		when(mockCommonService.getGreenNormalInstanceConfig(any(), any(), any(), any(), any()))
			.thenReturn(new JSONObject());
		when(mockRelDao.createBlueGreenDeploymentRel(any())).thenReturn(1);
		when(mockRelDao.deleteBlueGreenDeploymentRel(anyLong())).thenReturn(1);
		when(mockWorkFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt()))
			.thenThrow(new RuntimeException("Task dispatch failed"));

		// 调用方法
		try {
			Map<String, Object> result = blueGreenDeploymentService.createBlueGreenDeployment(
				"req123", "cn-hangzhou", "uid123", custInstance,
				new HashMap<>(), null, null, null);
		} catch (Exception e) {
			fail();
		}

	}


}