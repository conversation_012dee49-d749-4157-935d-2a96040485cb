package com.aliyun.dba.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.CrmService;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ActionParamsProvider.class})
public class UserGrayServiceTest {
    @InjectMocks
    private UserGrayService userGrayService;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private CrmService crmService;
    @Mock
    private ResourceService resourceService;


    @Test
    public void test_hit() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(1);
        minorVersionReleaseDO.setEngineComposeInfo("{\"grayConfig\":{\"ratio\":0,\"allowList\":[\"1336888804881051\"]}}");
        JSONObject grayConfig = JSON.parseObject(minorVersionReleaseDO.getEngineComposeInfo()).getJSONObject("grayConfig");
        Map<String, String> m = new HashMap<>();
        m.put("UID", "1336888804881051");
        when(podParameterHelper.getParameterValue(anyString())).thenReturn(m.get("UID"));
        ActionParamsProvider.ACTION_PARAMS_MAP.set(m);
        boolean hit = userGrayService.isHit(grayConfig);
        Assert.assertTrue(hit);
    }

    @Test
    public void test_not_hit() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(1);
        minorVersionReleaseDO.setEngineComposeInfo("{\"grayConfig\":{\"ratio\":0,\"allowList\":[\"1336888804881051\"]}}");
        JSONObject grayConfig = JSON.parseObject(minorVersionReleaseDO.getEngineComposeInfo()).getJSONObject("grayConfig");
        Map<String, String> m = new HashMap<>();
        m.put("UID", "133688880488105");
        when(podParameterHelper.getParameterValue(anyString())).thenReturn(m.get("UID"));
        ActionParamsProvider.ACTION_PARAMS_MAP.set(m);
        boolean hit = userGrayService.isHit(grayConfig);
        Assert.assertFalse(hit);
    }


    @Test
    public void isHitCompression_ResourceIsNull_ReturnsFalse() {
        PowerMockito.when(resourceService.getResourceByResKey(Mockito.anyString())).thenReturn(null);

        boolean result = userGrayService.isHitCompression("requestId", "uid", "regionId");

        Assert.assertFalse(result);
    }

    @Test
    public void isHitCompression_ResourceRealValueIsEmpty_ReturnsFalse() {
        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("");
        PowerMockito.when(resourceService.getResourceByResKey(Mockito.anyString())).thenReturn(resourceDO);

        boolean result = userGrayService.isHitCompression("requestId", "uid", "regionId");

        Assert.assertFalse(result);
    }

    @Test
    public void isHitCompression_UserInGlobalBlackList_ReturnsFalse() {
        ResourceDO resourceDO = new ResourceDO();
        JSONObject grayPolicy = new JSONObject();
        grayPolicy.put("GlobalBlackUIDs", "uid");
        resourceDO.setRealValue(grayPolicy.toJSONString());
        PowerMockito.when(resourceService.getResourceByResKey(Mockito.anyString())).thenReturn(resourceDO);

        boolean result = userGrayService.isHitCompression("requestId", "uid", "regionId");

        Assert.assertFalse(result);
    }

    @Test
    public void isHitCompression_UserGcLevelHigher_ReturnsFalse() {
        ResourceDO resourceDO = new ResourceDO();
        JSONObject grayPolicy = new JSONObject();
        grayPolicy.put("GCLevel", "A");
        resourceDO.setRealValue(grayPolicy.toJSONString());
        PowerMockito.when(resourceService.getResourceByResKey(Mockito.anyString())).thenReturn(resourceDO);
        PowerMockito.when(crmService.getGcLevel("uid")).thenReturn("B");

        boolean result = userGrayService.isHitCompression("requestId", "uid", "regionId");

        Assert.assertFalse(result);
    }


    @Test
    public void isHitCompression_UserInRegionWhiteList_ReturnsTrue() {
        ResourceDO resourceDO = new ResourceDO();
        JSONObject grayPolicy = new JSONObject();
        JSONObject regionConfig = new JSONObject();
        regionConfig.put("UID", "uid");
        grayPolicy.put("regionId", regionConfig);
        resourceDO.setRealValue(grayPolicy.toJSONString());
        PowerMockito.when(resourceService.getResourceByResKey(Mockito.anyString())).thenReturn(resourceDO);

        boolean result = userGrayService.isHitCompression("requestId", "uid", "regionId");

        Assert.assertTrue(result);
    }

    @Test
    public void isHitCompression_UserNotInRegionWhiteListButMeetsRatio_ReturnsTrue() {
        ResourceDO resourceDO = new ResourceDO();
        JSONObject grayPolicy = new JSONObject();
        JSONObject regionConfig = new JSONObject();
        regionConfig.put("UID", "");
        regionConfig.put("SwitchRatio", "50");
        grayPolicy.put("regionId", regionConfig);
        resourceDO.setRealValue(grayPolicy.toJSONString());
        PowerMockito.when(resourceService.getResourceByResKey(Mockito.anyString())).thenReturn(resourceDO);

        boolean result = userGrayService.isHitCompression("requestId", "uid", "regionId");

        Assert.assertTrue(result);
    }

    @Test
    public void isHitCompression_UserNotInRegionWhiteListAndDoesNotMeetRatio_ReturnsFalse() {
        ResourceDO resourceDO = new ResourceDO();
        JSONObject grayPolicy = new JSONObject();
        JSONObject regionConfig = new JSONObject();
        regionConfig.put("UID", "");
        regionConfig.put("SwitchRatio", "0");
        grayPolicy.put("regionId", regionConfig);
        resourceDO.setRealValue(grayPolicy.toJSONString());
        PowerMockito.when(resourceService.getResourceByResKey(Mockito.anyString())).thenReturn(resourceDO);

        boolean result = userGrayService.isHitCompression("requestId", "uid", "regionId");

        Assert.assertFalse(result);
    }

}
