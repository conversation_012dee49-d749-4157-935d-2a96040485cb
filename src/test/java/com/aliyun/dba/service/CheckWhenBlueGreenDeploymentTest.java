package com.aliyun.dba.service;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CustinsParamSupport.class})
public class CheckWhenBlueGreenDeploymentTest {

    @InjectMocks
    private CheckWhenBlueGreenDeployment checkWhenBlueGreenDeployment;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private CustinsService custinsService;

    @Mock
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;

    private Map<String, String> params;

    @Before
    public void setUp() throws Exception {
        // mock 静态方法
        PowerMockito.mockStatic(CustinsParamSupport.class);

        params = new HashMap<>();
        params.put("DBInstanceId", "test-instance");
    }

    @Test
    public void testCheck_ActionNotInBlockSet_ShouldReturnWithoutException() throws RdsException {
        when(CustinsParamSupport.getAction(params)).thenReturn("otherAction");

        checkWhenBlueGreenDeployment.check(params);

        // 没有抛出异常即成功
        assertTrue(true);
    }

    @Test
    public void testCheck_DbInstanceNameBlank_ShouldReturnWithoutException() throws RdsException {
        when(CustinsParamSupport.getAction(params)).thenReturn("createdbinstancenettype");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_NAME)).thenReturn(null);

        checkWhenBlueGreenDeployment.check(params);

        // 没有抛出异常即成功
        assertTrue(true);
    }

    @Test
    public void testCheck_CustInstanceIsNull_ShouldReturnWithoutException() throws RdsException {
        when(CustinsParamSupport.getAction(params)).thenReturn("createdbinstancenettype");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_NAME)).thenReturn("test-instance");
        when(custinsService.getCustInstanceByInsName(null, "test-instance")).thenReturn(null);

        checkWhenBlueGreenDeployment.check(params);

        // 没有抛出异常即成功
        assertTrue(true);
    }

    @Test
    public void testCheck_KindCodeNotMatch_ShouldReturnWithoutException() throws RdsException {
        when(CustinsParamSupport.getAction(params)).thenReturn("createdbinstancenettype");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_NAME)).thenReturn("test-instance");

        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setInsName("test-instance");
        custInstanceDO.setKindCode(1);  // 不是 KIND_CODE_NEW_ARCH 或 KIND_CODE_NC

        when(custinsService.getCustInstanceByInsName(null, "test-instance")).thenReturn(custInstanceDO);

        checkWhenBlueGreenDeployment.check(params);

        // 没有抛出异常即成功
        assertTrue(true);
    }

    @Test
    public void testCheck_NotInBlueGreenDeployment_ShouldReturnWithoutException() throws RdsException {
        when(CustinsParamSupport.getAction(params)).thenReturn("createdbinstancenettype");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_NAME)).thenReturn("test-instance");

        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setInsName("test-instance");
        custInstanceDO.setId(Integer.valueOf("123"));
        custInstanceDO.setKindCode(com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_NEW_ARCH);

        when(custinsService.getCustInstanceByInsName(null, "test-instance")).thenReturn(custInstanceDO);
        when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, Long.valueOf(123), null)).thenReturn(null);
        when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, null, Long.valueOf(123))).thenReturn(null);

        checkWhenBlueGreenDeployment.check(params);

        // 没有抛出异常即成功
        assertTrue(true);
    }

    @Test
    public void testCheck_InCreatingBlueGreenDeployment_ShouldThrowRdsException() throws RdsException {
        when(CustinsParamSupport.getAction(params)).thenReturn("createdbinstancenettype");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_NAME)).thenReturn("test-instance");

        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setInsName("test-instance");
        custInstanceDO.setId(1);
        custInstanceDO.setKindCode(com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_NEW_ARCH);

        when(custinsService.getCustInstanceByInsName(null, "test-instance")).thenReturn(custInstanceDO);

        BlueGreenDeploymentRel rel = new BlueGreenDeploymentRel();
        rel.setStatus((byte) 0);  // 创建中状态

        when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, Long.valueOf(123), null)).thenReturn(rel);

        checkWhenBlueGreenDeployment.check(params);

    }

    @Test
    public void testCheck_ValidActionInBlueGreenDeployment_ShouldThrowRdsException() throws RdsException {
        when(CustinsParamSupport.getAction(params)).thenReturn("createdbinstancenettype");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_NAME)).thenReturn("test-instance");

        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setInsName("test-instance");
        custInstanceDO.setId(1);
        custInstanceDO.setKindCode(com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_NEW_ARCH);

        when(custinsService.getCustInstanceByInsName(null, "test-instance")).thenReturn(custInstanceDO);

        BlueGreenDeploymentRel asBlue = new BlueGreenDeploymentRel();
        asBlue.setStatus((byte) 1);  // 已创建

        when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, Long.valueOf(123), null)).thenReturn(asBlue);
        when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, null, Long.valueOf(123))).thenReturn(null);

        checkWhenBlueGreenDeployment.check(params);
    }
}
