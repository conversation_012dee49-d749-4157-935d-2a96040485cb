package com.aliyun.dba.service;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.BlueGreenSwitchInfo;
import com.aliyun.apsaradb.dbaasmetaapi.model.BlueGreenSwitchInfoListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.BlueGreenDeploymentCommonService;
import com.aliyun.dba.base.service.DtsClientService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.idao.InstancePerfIDaoImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dts20200101.Client;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailRequest;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponse;
import com.aliyun.dts20200101.models.DescribeDtsJobDetailResponseBody;
import org.apache.commons.collections4.MapUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ RequestSession.class, MapUtils.class })
public class BlueGreenDeploymentServiceImplTest {

	@InjectMocks
	private BlueGreenDeploymentServiceImpl blueGreenDeploymentService;

	@Mock
	private BlueGreenDeploymentCommonService commonService;

	@Mock
	private DefaultApi defaultApi;

	@Mock
	private DBaasMetaService dBaasMetaService;

	@Mock
	private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;

	@Mock
	private WorkFlowService workFlowService;

	@Mock
	private MysqlParamSupport paramSupport;

	@Mock
	private CustInstanceDO custInstanceDO;

	@Mock
	private CustinsService custinsService;

	@Mock
	private DtsClientService dtsClientService;

	@Mock
	private Client client;

	@Mock
	private InstancePerfIDaoImpl instancePerfIDao;

	@Before
	public void setUp() throws Exception {
		MockitoAnnotations.initMocks(this);
		PowerMockito.mockStatic(RequestSession.class);
		PowerMockito.mockStatic(MapUtils.class);
		Mockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
		when(dtsClientService.getDtsClient(anyString(), anyString())).thenReturn(client);
	}

	@Test
	public void testCreateBlueGreenDeployment() throws Exception {
		CustInstanceDO custInstance = new CustInstanceDO();
		custInstance.setId(1);
		String blueCustomParamGroupId = "blueCustomParamGroupId";
		when(dBaasMetaService.getDefaultClient().getReplicaSetLabel(anyString(), anyString(), anyString())).thenReturn(blueCustomParamGroupId);
		Mockito.doNothing().when(commonService).preCheckWhenCreating(anyString(), any(CustInstanceDO.class));
		JSONObject jsonObject = new JSONObject();
		when(commonService.getGreenNormalInstanceConfig(any(), anyString(), anyMap(), anyList(), anyMap())).thenReturn(jsonObject);
		String deploymentName = "deploymentName";
		when(commonService.generateBlueGreenDeploymentName()).thenReturn(deploymentName);
		when(blueGreenDeploymentRelServiceIDao.createBlueGreenDeploymentRel(any(BlueGreenDeploymentRel.class))).thenReturn(1);
		Object taskId = 1;
		when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(taskId);

		blueGreenDeploymentService.createBlueGreenDeployment("requestId", "cn-beijing", "aliUid", custInstance, new HashMap<>(), new ArrayList<>(), new ArrayList<>(), new HashMap<>());
	}

	@Test
	public void testSwitchBlueGreenInstance() throws Exception {
		when(defaultApi.getReplicaSet(any(), anyString(), eq(false))).thenReturn(new ReplicaSet().id(1L).kindCode(18));
		BlueGreenDeploymentRel rel = new BlueGreenDeploymentRel();
		rel.setBlueCustinsName("blueCustinsName");
		BlueGreenDeploymentRel rel1 = new BlueGreenDeploymentRel();
		rel1.setId(1L);
		when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByDeploymentName(anyString())).thenReturn(rel).thenReturn(rel1);
		CustInstanceDO greenCustInstance = new CustInstanceDO();
		when(paramSupport.getAndCheckCustInstance(anyMap())).thenReturn(greenCustInstance);
		Mockito.doNothing().when(commonService).checkStatus(any(CustInstanceDO.class), any(CustInstanceDO.class), anyMap());
		when(commonService.getDBs(any(CustInstanceDO.class), anyString(), anyMap())).thenReturn(Collections.singletonList("db1"));
		Mockito.doNothing().when(commonService).checkBlueGreenList(anyString(),  anyList(), anyList(), anyString(), anyMap(), any(ErrorCode.class));
		Mockito.doNothing().when(commonService).checkBlueGreenTableCount(any(CustInstanceDO.class), any(CustInstanceDO.class), anyMap());
		Mockito.doNothing().when(commonService).checkDts(anyString(), anyString(), any(BlueGreenDeploymentRel.class), anyMap(), anyMap());
		Mockito.doNothing().when(commonService).checkHasReadOnly(any(CustInstanceDO.class), any(CustInstanceDO.class), anyMap());
		Mockito.doNothing().when(commonService).checkMaxScale(any(CustInstanceDO.class), any(CustInstanceDO.class), anyMap());
		Mockito.doNothing().when(commonService).checkConnectionString(any(CustInstanceDO.class), any(CustInstanceDO.class), anyMap());
		List<Map<String, Object>> checkItems = new ArrayList<>();
		Map<String, Object> checkItem = new HashMap<>();
		checkItem.put("checkPass", true);
		checkItems.add(checkItem);
		when(MapUtils.getObject(anyMap(), anyString())).thenReturn(checkItems);
		when(dBaasMetaService.getDefaultClient().updateReplicaSetStatus(anyString(), anyString(), anyString())).thenReturn("xx");
		Object taskId = 1;
		when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(taskId);
		when(custInstanceDO.getId()).thenReturn(1);
		when(custInstanceDO.getKindCode()).thenReturn(18);
		when(custInstanceDO.getInsName()).thenReturn("blueCustinsName");

		BlueGreenSwitchInfo result = new BlueGreenSwitchInfo();
		result.setId(1L);
		when(defaultApi.createBlueGreenSwitchInfo(any(), any(BlueGreenSwitchInfo.class))).thenReturn(result);


		String regionId = "cn-beijing";
		String aliUid = "123";
		String bid = "bid";
		String deploymentName = "deploymentName";
		String greenCustinsName = "greenCustinsName";
		blueGreenDeploymentService.switchBlueGreenInstance(regionId, aliUid, bid, custInstanceDO, deploymentName, greenCustinsName, new HashMap<>());
	}

	@Test
	public void testDeleteBlueGreenDeployment() throws Exception {
		String regionId = "cn-beijing";
		String aliUid = "123";
		String deploymentName = "deploymentName";
		String mode = "";
		CustInstanceDO custInstance = new CustInstanceDO();
		custInstance.setInsName("insName");
		custInstance.setId(1);
		BlueGreenDeploymentRel rel = new BlueGreenDeploymentRel();
		rel.setDeploymentName("name");
		rel.setGreenCustinsName("greenCustinsName");
		when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(any(), anyString(), any(), any())).thenReturn(rel);
		CustInstanceDO greenCustins = new CustInstanceDO();
		when(custinsService.getCustInstanceByInsName(any(), anyString())).thenReturn(greenCustins);
		Mockito.doNothing().when(commonService).preCheckBeforeDeleteDeployment(any(CustInstanceDO.class), anyString());
		when(blueGreenDeploymentRelServiceIDao.updateBlueGreenDeploymentRel(any(BlueGreenDeploymentRel.class))).thenReturn(1);

		blueGreenDeploymentService.deleteBlueGreenDeployment(regionId, aliUid, custInstance, deploymentName, mode);
	}

	@Test
	public void testDescribeBlueGreenSyncInfo() throws Exception {
		CustInstanceDO custins = new CustInstanceDO();
		custins.setId(1);
		when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByBlueCustinsId(anyLong())).thenReturn(null);
		String requestId = "requestId";
		String regionId = "cn-beijing";
		String aliUid = "123";
		String dbInstanceName = "instanceName";
		try {
			blueGreenDeploymentService.describeBlueGreenSyncInfo(custins, requestId, regionId, aliUid, dbInstanceName);
		} catch(Exception e) {}

		BlueGreenDeploymentRel blueGreenDeploymentRel = new BlueGreenDeploymentRel();
		blueGreenDeploymentRel.setId(1L);
		blueGreenDeploymentRel.setDeploymentName("deploymentName");
		blueGreenDeploymentRel.setBlueCustinsName("xxxx");
		blueGreenDeploymentRel.setGreenCustinsName("xxxx");
		blueGreenDeploymentRel.setStatus((byte)0);
		when(blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRelByBlueCustinsId(anyLong())).thenReturn(blueGreenDeploymentRel);
		blueGreenDeploymentService.describeBlueGreenSyncInfo(custins, requestId, regionId, aliUid, dbInstanceName);

		blueGreenDeploymentRel.setStatus((byte)1);
		blueGreenDeploymentService.describeBlueGreenSyncInfo(custins, requestId, regionId, aliUid, dbInstanceName);

		JSONObject dtsInfo = new JSONObject();
		dtsInfo.put("dtsJobId", "dtsJobId");
		dtsInfo.put("dtsInstanceId", "");
		dtsInfo.put("endTime", "endTime");
		blueGreenDeploymentRel.setDtsInfo(JSON.toJSONString(dtsInfo));
		try {
			blueGreenDeploymentService.describeBlueGreenSyncInfo(custins, requestId, regionId, aliUid, dbInstanceName);
		} catch(Exception e) {}

		dtsInfo.put("dtsJobId", "dtsJobId");
		dtsInfo.put("dtsInstanceId", "dtsInstanceId");
		dtsInfo.put("endTime", "endTime");
		blueGreenDeploymentRel.setDtsInfo(JSON.toJSONString(dtsInfo));
		when(client.describeDtsJobDetail(any(DescribeDtsJobDetailRequest.class))).thenReturn(null);
		try {
			blueGreenDeploymentService.describeBlueGreenSyncInfo(custins, requestId, regionId, aliUid, dbInstanceName);
		} catch(Exception e) {}

		DescribeDtsJobDetailResponse response = new DescribeDtsJobDetailResponse();
		DescribeDtsJobDetailResponseBody body = new DescribeDtsJobDetailResponseBody();
		body.setStatus("1");
		body.setCheckpoint(1L);
		body.setDelay(1000L);
		body.setCreateTime("createTime");
		DescribeDtsJobDetailResponseBody.DescribeDtsJobDetailResponseBodySourceEndpoint endpoint = new DescribeDtsJobDetailResponseBody.DescribeDtsJobDetailResponseBodySourceEndpoint();
		endpoint.setInstanceID("safd");
		DescribeDtsJobDetailResponseBody.DescribeDtsJobDetailResponseBodyDestinationEndpoint endpoint2 = new DescribeDtsJobDetailResponseBody.DescribeDtsJobDetailResponseBodyDestinationEndpoint();
		endpoint2.setInstanceID("dfgsdf");
		body.setSourceEndpoint(endpoint);
		body.setDestinationEndpoint(endpoint2);
		response.setBody(body);
		when(client.describeDtsJobDetail(any(DescribeDtsJobDetailRequest.class))).thenReturn(response);
		BlueGreenSwitchInfoListResult result = new BlueGreenSwitchInfoListResult();
		List<BlueGreenSwitchInfo> items = new ArrayList<>();
		BlueGreenSwitchInfo item = new BlueGreenSwitchInfo();
		item.setSwitchStatus(1);
		items.add(item);
		result.setItems(items);
		when(defaultApi.getBlueGreenSwitchInfo(any(), anyLong(), any(), any())).thenReturn(result);
		when(defaultApi.getReplicaSet(any(), any(), any())).thenReturn(new ReplicaSet().comment("comment").name("name")).thenReturn(new ReplicaSet().comment("comment").name("name"));
		when(defaultApi.getReplicaSetLabel(any(), any(), any())).thenReturn("xxx");
		blueGreenDeploymentService.describeBlueGreenSyncInfo(custins, requestId, regionId, aliUid, dbInstanceName);
	}

	@Test
	public void testSwitchHostinsPerfMeta() {
		Integer blueInstanceId = 1;
		Integer greenInstanceId = 2;
		List<InstancePerfDO> bluePerfList = new ArrayList<>();
		InstancePerfDO perfDO = new InstancePerfDO();
		perfDO.setInsId(blueInstanceId);
		bluePerfList.add(perfDO);
		when(instancePerfIDao.getInstancePerfByCondition(any())).thenReturn(bluePerfList);
		Mockito.doNothing().when(instancePerfIDao).deleteByCustinsId(any(), any());
		blueGreenDeploymentService.switchHostinsPerfMeta(blueInstanceId, greenInstanceId);
	}
}