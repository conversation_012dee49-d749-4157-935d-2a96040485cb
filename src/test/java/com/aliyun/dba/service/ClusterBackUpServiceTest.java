package com.aliyun.dba.service;

import com.aliyun.core.utils.IOUtils;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.SlrCheckService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.SimplifiedObjectMeta;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;

import javax.net.ssl.SSLContext;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({OSSClient.class, ClusterBackUpService.class, OSSObject.class, InputStream.class, IOUtils.class, Arrays.class})
public class ClusterBackUpServiceTest {

    @InjectMocks
    @Spy
    private ClusterBackUpService clusterBackUpService = new ClusterBackUpService();
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private SlrCheckService slrCheckService;

    private Map<String, String> params;

    @Before
    public void setUp() throws Exception {
        params = new HashMap<String, String>(){{

        }};
    }

    @Test
    public void getBackClusterConfig() {
    }

    @Test
    public void getDiskSize() {
    }

    @Test
    public void allocateClusterResource() {
    }

    @Test
    public void createRestoreTaskWithOss() {
    }

    @Test
    public void allocateDBInstanceResource() {
    }

    @Test
    public void getDetachedInsBackupInfo() {
    }

    @Test
    public void reloadOssParams() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("");
        when(mysqlParamSupport.getUID(params)).thenReturn("123456");
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath.xb");
        when(mysqlParamSupport.getParameterValue(params, "OssBucket")).thenReturn("ossBucket");
        when(mysqlParamSupport.getParameterValue(params, "Location")).thenReturn("cn-hangzhou");
        when(slrCheckService.getCredentials(anyString(), anyString(), anyLong())).thenReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{
            setAccessKeyId("accessKeyId");
            setAccessKeySecret("accessKeySecret");
            setSecurityToken("securityToken");
        }});
        OSSClient ossClient = PowerMockito.mock(OSSClient.class);
        doReturn(ossClient).when(clusterBackUpService, "getOSSClient", anyString(), any(AssumeRoleWithServiceIdentityResponse.Credentials.class));
        when(ossClient.doesObjectExist(anyString(), anyString())).thenReturn(true);
        when(ossClient.getSimplifiedObjectMeta(anyString(), anyString())).thenReturn(new SimplifiedObjectMeta(){{
            setSize(99999L);
        }});
        when(ossClient.getObject(any())).thenReturn(mock(OSSObject.class));
        doReturn(ClusterBackUpService.BackupType.XTRABACKUP).when(clusterBackUpService, "getAndCheckBackupType", any(OSSClient.class), anyString(), anyString(), anyString());
        mockStatic(IOUtils.class);
        when(IOUtils.toByteArray(any())).thenReturn("PBBBBxtrabackup_info".getBytes());
        doReturn(1L).when(clusterBackUpService, "readLong", any(), anyInt());
        mockStatic(Arrays.class);
        when(Arrays.copyOfRange((byte[]) any(), anyInt(), anyInt())).thenReturn("testData".getBytes());
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertTrue(true);
        }
        assertTrue(true);
        when(IOUtils.toByteArray(any())).thenReturn("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\"begin\":\t123, \"end\":\t456}\"".getBytes());
        when(Arrays.copyOfRange((byte[]) any(), anyInt(), anyInt())).thenReturn("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\"begin\":\t123, \"end\":\t456}\"".getBytes());
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertTrue(true);
        }
    }

    @Test
    public void reloadOssParams_failed() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("");
        when(mysqlParamSupport.getUID(params)).thenReturn("123456");
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath");
        when(mysqlParamSupport.getParameterValue(params, "OssBucket")).thenReturn("ossBucket");
        when(mysqlParamSupport.getParameterValue(params, "Location")).thenReturn("cn-hangzhou");
        when(slrCheckService.getCredentials(anyString(), anyString(), anyLong())).thenReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{
            setAccessKeyId("accessKeyId");
            setAccessKeySecret("accessKeySecret");
            setSecurityToken("securityToken");
        }});
        OSSClient ossClient = PowerMockito.mock(OSSClient.class);
        doReturn(ossClient).when(clusterBackUpService, "getOSSClient", anyString(), any(AssumeRoleWithServiceIdentityResponse.Credentials.class));
        when(ossClient.doesObjectExist(anyString(), anyString())).thenReturn(false);
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void reloadOssParams_failed_2() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("");
        when(mysqlParamSupport.getUID(params)).thenReturn("123456");
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath");
        when(mysqlParamSupport.getParameterValue(params, "OssBucket")).thenReturn("ossBucket");
        when(mysqlParamSupport.getParameterValue(params, "Location")).thenReturn("cn-hangzhou");
        when(slrCheckService.getCredentials(anyString(), anyString(), anyLong())).thenReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{
            setAccessKeyId("accessKeyId");
            setAccessKeySecret("accessKeySecret");
            setSecurityToken("securityToken");
        }});
        OSSClient ossClient = PowerMockito.mock(OSSClient.class);
        doReturn(ossClient).when(clusterBackUpService, "getOSSClient", anyString(), any(AssumeRoleWithServiceIdentityResponse.Credentials.class));
        when(ossClient.doesObjectExist(anyString(), anyString())).thenReturn(true);
        when(ossClient.getSimplifiedObjectMeta(anyString(), anyString())).thenReturn(new SimplifiedObjectMeta(){{
            setSize(99999L);
        }});
        when(ossClient.getObject(any())).thenReturn(mock(OSSObject.class));
        doReturn(ClusterBackUpService.BackupType.XTRABACKUP).when(clusterBackUpService, "getAndCheckBackupType", any(OSSClient.class), anyString(), anyString(), anyString());
        mockStatic(IOUtils.class);
        when(IOUtils.toByteArray(any())).thenReturn(new byte[0]);
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void reloadOssParams_failed_3() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("");
        when(mysqlParamSupport.getUID(params)).thenReturn("123456");
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath");
        when(mysqlParamSupport.getParameterValue(params, "OssBucket")).thenReturn("ossBucket");
        when(mysqlParamSupport.getParameterValue(params, "Location")).thenReturn("cn-hangzhou");
        when(slrCheckService.getCredentials(anyString(), anyString(), anyLong())).thenReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{
            setAccessKeyId("accessKeyId");
            setAccessKeySecret("accessKeySecret");
            setSecurityToken("securityToken");
        }});
        OSSClient ossClient = PowerMockito.mock(OSSClient.class);
        doReturn(ossClient).when(clusterBackUpService, "getOSSClient", anyString(), any(AssumeRoleWithServiceIdentityResponse.Credentials.class));
        when(ossClient.doesObjectExist(anyString(), anyString())).thenReturn(true);
        when(ossClient.getSimplifiedObjectMeta(anyString(), anyString())).thenReturn(new SimplifiedObjectMeta(){{
            setSize(99999L);
        }});
        when(ossClient.getObject(any())).thenReturn(mock(OSSObject.class));
        doReturn(ClusterBackUpService.BackupType.XTRABACKUP).when(clusterBackUpService, "getAndCheckBackupType", any(OSSClient.class), anyString(), anyString(), anyString());
        mockStatic(IOUtils.class);
        when(IOUtils.toByteArray(any())).thenReturn("AAAAAxtrabackup_info".getBytes());
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void reloadOssParams_failed_4() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("test");
        clusterBackUpService.reloadOssParams(params);
        assertTrue(true);
    }

    @Test
    public void reloadOssParams_failed_5() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("");
        when(mysqlParamSupport.getUID(params)).thenReturn("123456");
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath");
        when(mysqlParamSupport.getParameterValue(params, "OssBucket")).thenReturn("ossBucket");
        when(mysqlParamSupport.getParameterValue(params, "Location")).thenReturn("cn-hangzhou");
        when(slrCheckService.getCredentials(anyString(), anyString(), anyLong())).thenReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{
            setAccessKeyId("accessKeyId");
            setAccessKeySecret("accessKeySecret");
            setSecurityToken("securityToken");
        }});
        OSSClient ossClient = PowerMockito.mock(OSSClient.class);
        doReturn(ossClient).when(clusterBackUpService, "getOSSClient", anyString(), any(AssumeRoleWithServiceIdentityResponse.Credentials.class));
        when(ossClient.doesObjectExist(anyString(), anyString())).thenReturn(true);
        when(ossClient.getSimplifiedObjectMeta(anyString(), anyString())).thenReturn(new SimplifiedObjectMeta(){{
            setSize(99999L);
        }});
        when(ossClient.getObject(any())).thenReturn(mock(OSSObject.class));
        doReturn(ClusterBackUpService.BackupType.XTRABACKUP).when(clusterBackUpService, "getAndCheckBackupType", any(OSSClient.class), anyString(), anyString(), anyString());
        mockStatic(IOUtils.class);
        when(IOUtils.toByteArray(any())).thenReturn("PBBBBxtrabackup_info".getBytes());
        doReturn(0L).when(clusterBackUpService, "readLong", any(), anyInt());
        mockStatic(Arrays.class);
        when(Arrays.copyOfRange((byte[]) any(), anyInt(), anyInt())).thenReturn("testData".getBytes());
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void reloadOssParams_6() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("");
        when(mysqlParamSupport.getUID(params)).thenReturn("123456");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath.xb");
        when(mysqlParamSupport.getParameterValue(params, "OssBucket")).thenReturn("ossBucket");
        when(mysqlParamSupport.getParameterValue(params, "Location")).thenReturn("cn-hangzhou");
        when(slrCheckService.getCredentials(anyString(), anyString(), anyLong())).thenReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{
            setAccessKeyId("accessKeyId");
            setAccessKeySecret("accessKeySecret");
            setSecurityToken("securityToken");
        }});
        OSSClient ossClient = PowerMockito.mock(OSSClient.class);
        doReturn(ossClient).when(clusterBackUpService, "getOSSClient", anyString(), any(AssumeRoleWithServiceIdentityResponse.Credentials.class));
        when(ossClient.doesObjectExist(anyString(), anyString())).thenReturn(true);
        when(ossClient.getSimplifiedObjectMeta(anyString(), anyString())).thenReturn(new SimplifiedObjectMeta(){{
            setSize(99999L);
        }});
        when(ossClient.getObject(any())).thenReturn(mock(OSSObject.class));
        doReturn(ClusterBackUpService.BackupType.XTRABACKUP).when(clusterBackUpService, "getAndCheckBackupType", any(OSSClient.class), anyString(), anyString(), anyString());
        mockStatic(IOUtils.class);
        when(IOUtils.toByteArray(any())).thenReturn("PBBBBxtrabackup_info".getBytes());
        doReturn(0L).when(clusterBackUpService, "readLong", any(), anyInt());
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath.zst");
        doReturn(new byte[] {
                (byte) 0x28, (byte) 0xB5, (byte) 0x2F, (byte) 0xFD
        }).when(clusterBackUpService, "getObjectMetaFromOss", any(), anyString(), anyString(), anyLong(), anyLong());
        clusterBackUpService.reloadOssParams(params);
        assertTrue(true);
        doReturn(new byte[] {
                (byte) 0x28, (byte) 0xB5, (byte) 0x2F, (byte) 0xAA
        }).when(clusterBackUpService, "getObjectMetaFromOss", any(), anyString(), anyString(), anyLong(), anyLong());
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void reloadOssParams_getStsCredentials_fail() throws Exception {
        when(slrCheckService.getCredentials(any(), anyString(), anyLong())).thenThrow(new ClientException());
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void reloadOssParams_findAndCheckXtrabackupInfo() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("");
        when(mysqlParamSupport.getUID(params)).thenReturn("123456");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath.xb");
        when(mysqlParamSupport.getParameterValue(params, "OssBucket")).thenReturn("ossBucket");
        when(mysqlParamSupport.getParameterValue(params, "Location")).thenReturn("cn-hangzhou");
        when(slrCheckService.getCredentials(anyString(), anyString(), anyLong())).thenReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{
            setAccessKeyId("accessKeyId");
            setAccessKeySecret("accessKeySecret");
            setSecurityToken("securityToken");
        }});
        OSSClient ossClient = PowerMockito.mock(OSSClient.class);
        doReturn(ossClient).when(clusterBackUpService, "getOSSClient", anyString(), any(AssumeRoleWithServiceIdentityResponse.Credentials.class));
        when(ossClient.doesObjectExist(anyString(), anyString())).thenReturn(true);
        when(ossClient.getSimplifiedObjectMeta(anyString(), anyString())).thenReturn(new SimplifiedObjectMeta(){{
            setSize(99999L);
        }});
        when(ossClient.getObject(any())).thenReturn(mock(OSSObject.class));
        doReturn(ClusterBackUpService.BackupType.XTRABACKUP).when(clusterBackUpService, "getAndCheckBackupType", any(OSSClient.class), anyString(), anyString(), anyString());
        mockStatic(IOUtils.class);
        when(IOUtils.toByteArray(any())).thenReturn("PBBBBxtrabackup_info".getBytes());
        doReturn(1L).when(clusterBackUpService, "readLong", any(), anyInt());
        doReturn(ClusterBackUpService.CompressType.NONE).when(clusterBackUpService, "getAndCheckCompressType", any(OSSClient.class), anyString(), anyString(), anyString());
        try {
            clusterBackUpService.reloadOssParams(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void reloadOssParams_dbVersion() throws Exception {
        when(mysqlParamSupport.getParameterValue(params, "StsToken")).thenReturn("");
        when(mysqlParamSupport.getUID(params)).thenReturn("123456");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParamSupport.getParameterValue(params, "OssFilePath")).thenReturn("ossFilePath.xb");
        when(mysqlParamSupport.getParameterValue(params, "OssBucket")).thenReturn("ossBucket");
        when(mysqlParamSupport.getParameterValue(params, "Location")).thenReturn("cn-hangzhou");
        doReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{
            setAccessKeyId("accessKeyId");
            setAccessKeySecret("accessKeySecret");
            setSecurityToken("securityToken");
        }}).when(clusterBackUpService, "getStsCredentials", anyString());

        doReturn(new HashMap<String, Object>(){{
            put("key", "value");
        }}).when(clusterBackUpService, "checkBackupFile", any(), anyString(), anyString(), anyString(), anyString());
        clusterBackUpService.reloadOssParams(params);

        doReturn(new HashMap<String, Object>(){{
            put("server_version", "5.7");
        }}).when(clusterBackUpService, "checkBackupFile", any(), anyString(), anyString(), anyString(), anyString());
        clusterBackUpService.reloadOssParams(params);

        assertTrue(true);
    }

    @Test
    public void isXtrabackupInfoValid() throws Exception {
        byte[] objectMeta = "testData".getBytes();
        int position = -1;
        Map<String, Object> meta = new HashMap<>();
        String requestId = "requestId";
        assertFalse(clusterBackUpService.isXtrabackupInfoValid(objectMeta, position, meta, requestId));
    }

    @Test
    public void isXtrabackupInfoValid_1() throws Exception {
        byte[] objectMeta = "testData".getBytes();
        int position = 1;
        Map<String, Object> meta = new HashMap<>();
        String requestId = "requestId";
        assertFalse(clusterBackUpService.isXtrabackupInfoValid(objectMeta, position, meta, requestId));
    }

    @Test
    public void isXtrabackupInfoValid_3() throws Exception {
        byte[] objectMeta = "testData".getBytes();
        int position = 1;
        Map<String, Object> meta = new HashMap<>();
        String requestId = "requestId";
        objectMeta[1] = 'P';
        doNothing().when(clusterBackUpService, "parseAndFillBackFileMeta", meta, objectMeta, position, requestId);
        assertTrue(clusterBackUpService.isXtrabackupInfoValid(objectMeta, position, meta, requestId));
    }

    @Test
    public void decompressQPress() {
        try {
            byte[] res = clusterBackUpService.decompressQPress("testDataAAAAAAAAAAAAAAAAAAAA".getBytes());
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void getXtrabackupInfoRange() throws Exception {
        assertNotNull(ClusterBackUpService.getXtrabackupInfoRange("\"filepath\":\t\"xtrabackup_info\", \"begin\":\t123, \"end\":\t456}".getBytes()));
    }

    @Test
    public void getAndCheckBackupType() throws Exception {
        OSSClient ossClient = PowerMockito.mock(OSSClient.class);
        String bucketName = "bucketName";
        String object = "object.xb";
        String requestId = "requestId";
        doReturn("XBSTCK01".getBytes()).when(clusterBackUpService, "getObjectMetaFromOss", ossClient, bucketName, object, 0L, 7L);
        ClusterBackUpService.BackupType res = clusterBackUpService.getAndCheckBackupType(ossClient, bucketName, object, requestId);
        assertEquals(ClusterBackUpService.BackupType.XTRABACKUP, res);

        object = "object.xb.zst";
        res = clusterBackUpService.getAndCheckBackupType(ossClient, bucketName, object, requestId);
        assertEquals(ClusterBackUpService.BackupType.XTRABACKUP, res);

        object = "object.tar.gz";
        try {
            clusterBackUpService.getAndCheckBackupType(ossClient, bucketName, object, requestId);
            fail();
        } catch (Exception e) {
            assertTrue(e instanceof RdsException);
        }
    }

    @Test
    public void truncateOssFileMetaData() {
        String ossFileMetaData = "{\"tool_name\":\"xtrabackup\",\"binlog_pos\":\"filename 'mysql-bin.000069', position '197', GTID of the last change '67160d5c-3d20-11f0-a8af-b8cef6b7428c:1-12067'\",\"innodb_from_lsn\":\"0\",\"end_time\":\"2025-05-30 15:47:53\",\"format\":\"xbstream\",\"incremental\":\"N\",\"server_version\":\"8.0.30-txsql\",\"uuid\":\"64de8cb7-3d2a-11f0-9ecd-9cc2c49e9cd7\",\"tool_command\":\"--no-defaults --host=127.0.0.1 --port=20313 --user=cdb_dumper --progress=/data/dbs2.0/dbs_agent/data/bk_337_115039880/progress --result=/data/dbs2.0/dbs_agent/data/bk_337_115039880/result.xtrabackup --parallel=8 --compress --compress-threads=8 --compress-chunk-size=262144 --ftwrl-wait-timeout=300 --ftwrl-wait-threshold=1 --ftwrl-wait-query-type=all --backup --stream=xbstream --slave-info --lock-ddl=off --redo-individual-compress --tables-compatibility-check=off --open-files-limit=8192000 --backup-lock-timeout=300 --backup-lock-retry-count=3 --target-dir=./ --redo-overflow-action=lock --redo-max-size=************\",\"lock_time\":\"1\",\"start_time\":\"2025-05-30 15:46:01\",\"encrypted\":\"N\",\"fileSize\":\"8147719564\",\"tool_version\":\"8.0.30-23\",\"innodb_to_lsn\":\"35548856688\",\"name\":\"\",\"ibbackup_version\":\"8.0.30-23\",\"compressed\":\"compressed\",\"partial\":\"N\"}";
        String ret = clusterBackUpService.truncateOssFileMetaData(ossFileMetaData, "requestId");
        byte[] b = ret.getBytes(StandardCharsets.UTF_8);
        assertTrue(b.length <= 1000);

        ossFileMetaData = "{\"tool_name\":\"xtrabackup\"}";
        ret = clusterBackUpService.truncateOssFileMetaData(ossFileMetaData, "requestId");
        b = ret.getBytes(StandardCharsets.UTF_8);
        assertTrue(b.length <= 1000);

        ossFileMetaData = "{\"tool_name\":\"xtrabackup\",\"binlog_pos\":\"filename 'mysql-bin.000069', position '197', GTID of the last change '67160d5c-3d20-11f0-a8af-b8cef6b7428c:1-12067'\",\"innodb_from_lsn\":\"0\",\"end_time\":\"2025-05-30 15:47:53\",\"format\":\"xbstream\",\"incremental\":\"N\",\"server_version\":\"8.0.30-txsql\",\"uuid\":\"64de8cb7-3d2a-11f0-9ecd-9cc2c49e9cd7\",\"tool_command1\":\"--no-defaults --host=127.0.0.1 --port=20313 --user=cdb_dumper --progress=/data/dbs2.0/dbs_agent/data/bk_337_115039880/progress --result=/data/dbs2.0/dbs_agent/data/bk_337_115039880/result.xtrabackup --parallel=8 --compress --compress-threads=8 --compress-chunk-size=262144 --ftwrl-wait-timeout=300 --ftwrl-wait-threshold=1 --ftwrl-wait-query-type=all --backup --stream=xbstream --slave-info --lock-ddl=off --redo-individual-compress --tables-compatibility-check=off --open-files-limit=8192000 --backup-lock-timeout=300 --backup-lock-retry-count=3 --target-dir=./ --redo-overflow-action=lock --redo-max-size=************\",\"lock_time\":\"1\",\"start_time\":\"2025-05-30 15:46:01\",\"encrypted\":\"N\",\"fileSize\":\"8147719564\",\"tool_version\":\"8.0.30-23\",\"innodb_to_lsn\":\"35548856688\",\"name\":\"\",\"ibbackup_version\":\"8.0.30-23\",\"compressed\":\"compressed\",\"partial\":\"N\"}";
        ret = clusterBackUpService.truncateOssFileMetaData(ossFileMetaData, "requestId");
        b = ret.getBytes(StandardCharsets.UTF_8);
        assertTrue(b.length <= 1000);
    }
}