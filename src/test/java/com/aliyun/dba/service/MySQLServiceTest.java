package com.aliyun.dba.service;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;


import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class MySQLServiceTest {
    @InjectMocks
    private MySQLServiceImpl mySQLService;
    @Mock
    private InstanceIDao instanceIDao;
    @Mock
    private TaskService taskService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void createExchangeReadInsToPrimaryTaskTest() throws RdsException {
        CustInstanceDO primaryCustins = new CustInstanceDO();
        CustInstanceDO exchangeReadInstance = new CustInstanceDO();
        TransListDO trans = new TransListDO();
        doNothing().when(this.instanceIDao).createTransList(any());
        doAnswer(
                invocationOnMock -> {
                    ((TaskQueueDO)invocationOnMock.getArgument(0)).setId(0);
                    return null;
                }
        ).when(this.taskService).createTaskQueue(any());
        doNothing().when(this.instanceIDao).updateTransTaskIdById(any(), any());
        assertNotNull(mySQLService.createExchangeReadInsToPrimaryTask("ExchangeReadOnlyInstanceToPrimary", primaryCustins, exchangeReadInstance, 123456,
                trans, "exchange_read_ins_master_to_primary"));
    }
}
