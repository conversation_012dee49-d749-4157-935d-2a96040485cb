package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class ModifyDBInstanceParameterImplTest {

    @Mock
    MysqlParameterHelper mysqlParaHelper;

    @Mock
    ReplicaSetService replicaSetService;

    @InjectMocks
    ModifyDBInstanceParameterImpl modifyDBInstanceParameterImpl;

    @Test
    public void doActionRequest() throws RdsException, ApiException {
        when(mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID)).thenReturn(UUID.randomUUID().toString());
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("test");
        replicaSet.setId(123L);
        when(mysqlParaHelper.getDBInstanceName()).thenReturn("test");
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        when(mysqlParaHelper.getParameterValue("DryRun")).thenReturn("true");

        var resposne = modifyDBInstanceParameterImpl.doActionRequest(null, null);
        log.info("resposne:{}", resposne);

        assertNotNull(resposne);
        assertEquals(123L, resposne.get("DBInstanceID"));
        assertEquals("test", resposne.get("DBInstanceName"));
    }
}