package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceSpec;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.UserGrayService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ActionParamsProvider.class, RequestSession.class})
public class MinorVersionServiceHelperTest {
    @InjectMocks
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    private UserGrayService userGrayService;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private MinorVersionService minorVersionService;
    @Mock
    private ResourceService resourceService;
    @Mock
    private CustinsService custinsService;


    @Before
    public void setUp() throws Exception {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setDbVersion("5.7");
        minorVersionReleaseDO.setCommunityMinorVersion("5.7.44");
        minorVersionReleaseDO.setHotfixVersion(0);
        minorVersionReleaseDO.setDbType("mysql");
        minorVersionReleaseDO.setKindCode(18);
        minorVersionReleaseDO.setReleaseDate("20240930");
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("requestId");
    }

    @Test
    public void testiIsAvailableMinorVersion_online() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(1);
        minorVersionReleaseDO.setEngineComposeInfo("{}");
        boolean availableMinorVersion = minorVersionServiceHelper.isAvailableMinorVersion(minorVersionReleaseDO);
        Assert.assertTrue(availableMinorVersion);
    }

    @Test
    public void testiIsAvailableMinorVersion_online_config_null() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(1);
        minorVersionReleaseDO.setEngineComposeInfo("");
        boolean availableMinorVersion = minorVersionServiceHelper.isAvailableMinorVersion(minorVersionReleaseDO);
        Assert.assertTrue(availableMinorVersion);
    }

    @Test
    public void testiIsAvailableMinorVersion_online_withWrongInfo() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(1);
        minorVersionReleaseDO.setEngineComposeInfo("{{\"w}");
        boolean availableMinorVersion = minorVersionServiceHelper.isAvailableMinorVersion(minorVersionReleaseDO);
        Assert.assertTrue(availableMinorVersion);
    }

    @Test
    public void testiIsAvailableMinorVersion_offline_withoutInfo() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(0);
        minorVersionReleaseDO.setEngineComposeInfo("{}");
        boolean availableMinorVersion = minorVersionServiceHelper.isAvailableMinorVersion(minorVersionReleaseDO);
        Assert.assertFalse(availableMinorVersion);
    }

    @Test
    public void testiIsAvailableMinorVersion_offline_withWrongInfo() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(0);
        minorVersionReleaseDO.setEngineComposeInfo("{{\"w}");
        boolean availableMinorVersion = minorVersionServiceHelper.isAvailableMinorVersion(minorVersionReleaseDO);
        Assert.assertFalse(availableMinorVersion);
    }

    @Test
    public void testIsAvailableMinorVersion_offline_hit() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(0);
        minorVersionReleaseDO.setEngineComposeInfo("{\"grayConfig\":{\"ratio\":0,\"allowList\":[\"1336888804881051\"]}}");
        when(userGrayService.isHit(any())).thenReturn(true);
        boolean availableMinorVersion = minorVersionServiceHelper.isAvailableMinorVersion(minorVersionReleaseDO);
        Assert.assertTrue(availableMinorVersion);
    }

    @Test
    public void testIsAvailableMinorVersion_offline_not_hit() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(0);
        minorVersionReleaseDO.setEngineComposeInfo("{\"grayConfig\":{\"ratio\":0,\"allowList\":[\"1336888804881051\"]}}");
        when(userGrayService.isHit(any())).thenReturn(false);
        boolean availableMinorVersion = minorVersionServiceHelper.isAvailableMinorVersion(minorVersionReleaseDO);
        Assert.assertFalse(availableMinorVersion);
    }

    @Test
    public void testIsAvailableMinorVersion_online_not_hit() {
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setStatus(1);
        minorVersionReleaseDO.setEngineComposeInfo("{\"grayConfig\":{\"ratio\":0,\"allowList\":[\"1336888804881051\"]}}");
        when(userGrayService.isHit(any())).thenReturn(false);
        boolean availableMinorVersion = minorVersionServiceHelper.isAvailableMinorVersion(minorVersionReleaseDO);
        Assert.assertFalse(availableMinorVersion);
    }

    @Test
    public void test_getServiceSpecTag() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(any(), any())).thenReturn(1);
        ServiceSpec serviceSpec = new ServiceSpec();
        serviceSpec.setTag("alisql_docker_image_cloud_disk_20241231");
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(), any())).thenReturn(serviceSpec);
        List<MinorVersionReleaseDO> minorVersionReleaseDOList = new ArrayList<>();
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setTag("alisql_docker_image");
        minorVersionReleaseDOList.add(minorVersionReleaseDO);
        when(minorVersionService.querySpecifyMinorVersionListByCondition(any(), any(), any(), any(), any(), any())).thenReturn(minorVersionReleaseDOList);
        String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag("replicasetName", "rds_xc_20241231", ReplicaSet.BizTypeEnum.ALIYUN, "mysql", "5.7", "mysql", 18, instanceLevel, "cloud_essd", false, false, null);
        Assert.assertEquals("alisql_docker_image_cloud_disk_20241231", serviceSpecTag);
    }

    @Test
    public void test_getServiceSpecTag_xc() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        instanceLevel.setExtraInfo("{\"instructionSetArch\":\"x86hg\"}");
        when(dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(any(), any())).thenReturn(1);
        ServiceSpec serviceSpec = new ServiceSpec();
        serviceSpec.setTag("alisql_docker_image_cloud_disk_20241231");
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(), any())).thenReturn(serviceSpec);
        List<MinorVersionReleaseDO> minorVersionReleaseDOList = new ArrayList<>();
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setTag("alisql_docker_image");
        minorVersionReleaseDOList.add(minorVersionReleaseDO);
        when(minorVersionService.querySpecifyMinorVersionListByCondition(any(), any(), any(), any(), any(), any())).thenReturn(minorVersionReleaseDOList);
        String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag("replicasetName", "rds_xc_20241231", ReplicaSet.BizTypeEnum.ALIYUN, "mysql", "5.7", "mysql", 18, instanceLevel, "cloud_essd", false, false,null);
        Assert.assertEquals("alisql_docker_image_cloud_disk_20241231", serviceSpecTag);
    }

    @Test
    public void test_getServiceSpecTag_aarch() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(any(), any())).thenReturn(1);
        ServiceSpec serviceSpec = new ServiceSpec();
        serviceSpec.setTag("alisql_docker_image_cloud_disk_20241231");
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(), any())).thenReturn(serviceSpec);
        List<MinorVersionReleaseDO> minorVersionReleaseDOList = new ArrayList<>();
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setTag("alisql_aarch_docker_image");
        minorVersionReleaseDOList.add(minorVersionReleaseDO);
        when(minorVersionService.querySpecifyMinorVersionListByCondition(any(), any(), any(), any(), any(), any())).thenReturn(minorVersionReleaseDOList);
        String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag("rds_xc_20241231", ReplicaSet.BizTypeEnum.ALIYUN, "mysql", "5.7", "mysql", 18, instanceLevel, "cloud_essd", true, false, null);
        Assert.assertEquals("alisql_aarch_docker_image_cloud_disk_20241231", serviceSpecTag);
    }

    @Test
    public void test_getServiceSpecTag_xchg() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(any(), any())).thenReturn(1);
        ServiceSpec serviceSpec = new ServiceSpec();
        serviceSpec.setTag("alisql_docker_image_cloud_disk_20241231");
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(), any())).thenReturn(serviceSpec);
        List<MinorVersionReleaseDO> minorVersionReleaseDOList = new ArrayList<>();
        MinorVersionReleaseDO minorVersionReleaseDO = new MinorVersionReleaseDO();
        minorVersionReleaseDO.setTag("alisql_xchg_docker_image");
        minorVersionReleaseDOList.add(minorVersionReleaseDO);
        when(minorVersionService.querySpecifyMinorVersionListByCondition(any(), any(), any(), any(), any(), any())).thenReturn(minorVersionReleaseDOList);
        String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag("rds_xc_20241231", ReplicaSet.BizTypeEnum.ALIYUN, "mysql", "5.7", "mysql", 18, instanceLevel, "cloud_essd", false, false, null);
        Assert.assertEquals("alisql_xchg_docker_image_cloud_disk_20241231", serviceSpecTag);
    }

    @Test
    public void test_isXCChangeSpecTag() throws RdsException, ApiException {
        ReplicaSet replicaSet = new ReplicaSet();
        boolean xcChangeSpecTag = minorVersionServiceHelper.isChangeSubType(replicaSet, null);
        Assert.assertFalse(xcChangeSpecTag);
    }

    @Test
    public void test_isXCChangeSpecTag_true1() throws RdsException, ApiException {
        ReplicaSet replicaSet = new ReplicaSet();
        when(dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(any(), any())).thenReturn(1);
        ServiceSpec spec = new ServiceSpec();
        spec.setTag("alisql_docker_image_cloud_disk_20241231");
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(), any())).thenReturn(spec);
        boolean xcChangeSpecTag = minorVersionServiceHelper.isChangeSubType(replicaSet, "rds_xc_20241231");
        Assert.assertTrue(xcChangeSpecTag);
    }

    @Test
    public void test_isXCChangeSpecTag_true2() throws RdsException, ApiException {
        ReplicaSet replicaSet = new ReplicaSet();
        when(dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(any(), any())).thenReturn(1);
        ServiceSpec spec = new ServiceSpec();
        spec.setTag("alisql_xc_docker_image_cloud_disk_20241231");
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(), any())).thenReturn(spec);
        boolean xcChangeSpecTag = minorVersionServiceHelper.isChangeSubType(replicaSet, "rds_20241231");
        Assert.assertTrue(xcChangeSpecTag);
    }

    @Test
    public void test_parseReleaseDate() throws RdsException {
        String s = minorVersionServiceHelper.parseReleaseDate("rds_xc_20241231", null, null, null);
        Assert.assertEquals("20241231", s);
    }

    @Test
    public void test_resetReplicaSetMinorVersion() throws ApiException {
        when(dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(any(), any())).thenReturn(1);
        ServiceSpec spec = new ServiceSpec();
        spec.setTag("alisql_xc_docker_image_cloud_disk_20241231");
        when(dBaasMetaService.getMetaDbClient(any())).thenReturn(defaultApi);
        Map<String, String> labels = new HashMap<>();
        labels.put("minor_version", "mysql57_20241231");
        when(defaultApi.listReplicaSetLabels(any(), any())).thenReturn(labels);
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(), any())).thenReturn(spec);
        String s = minorVersionServiceHelper.resetReplicaSetMinorVersion("requestId", "replicaSetName", "regionId");
    }

    @Test
    public void test_resetReplicaSetMinorVersion1() throws ApiException {
        when(dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(any(), any())).thenReturn(1);
        ServiceSpec spec = new ServiceSpec();
        spec.setTag("alisql_xc_docker_image_cloud_disk_20241231");
        when(dBaasMetaService.getMetaDbClient(any())).thenReturn(defaultApi);
        Map<String, String> labels = new HashMap<>();
        labels.put("minor_version", "apsaradb-alios7u-mysql80:20241231-20250530153503");
        when(defaultApi.listReplicaSetLabels(any(), any())).thenReturn(labels);
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(), any())).thenReturn(spec);
        String s = minorVersionServiceHelper.resetReplicaSetMinorVersion("requestId", "replicaSetName", "regionId");
    }

    @Test
    public void test_checkCrossTagRules() throws RdsException, ExecutionException, ApiException {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setId(1L);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        ResourceDO resource = new ResourceDO();
        resource.setRealValue("{\"alisql_docker_image\":[{\"legalTargetTag\":\"alisql_xc_docker_image\",\"uids\":[\"1875785612443619\"],\"kindCode\":[18],\"category\":[\"standard\"],\"arch\":[\"x86\"]},{\"legalTargetTag\":\"alisql_duckdb_docker_image\",\"uids\":[\"\"],\"kindCode\":[18],\"category\":[\"standard\"],\"arch\":[\"x86\"]}],\"alisql_xc_docker_image\":[{\"legalTargetTag\":\"alisql_docker_image\",\"uids\":[\"1875785612443619\"],\"kindCode\":[0,18],\"category\":[\"*\"],\"arch\":[\"x86\",\"arm\"]}]}");
        when(resourceService.getResourceByResKey("CROSS_TAG_RULES")).thenReturn(resource);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient()
                .getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        minorVersionServiceHelper.checkCrossTagRules(replicaSet, "alisql_docker_image", "alisql_xc_docker_image", "1875785612443619");
        Assert.assertNotNull(replicaSet);
    }

    @Test
    public void test_checkCrossTagRules_resource_null() throws RdsException, ExecutionException, ApiException {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setId(1L);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        when(resourceService.getResourceByResKey("CROSS_TAG_RULES")).thenReturn(null);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient()
                .getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        try {
            minorVersionServiceHelper.checkCrossTagRules(replicaSet, "alisql_docker_image", "alisql_xc_docker_image", "1875785612443619");
            Assert.fail();
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void test_checkCrossTagRules_resource_empty() throws RdsException, ExecutionException, ApiException {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setId(1L);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        ResourceDO resource = new ResourceDO();
        resource.setRealValue("");
        when(resourceService.getResourceByResKey("CROSS_TAG_RULES")).thenReturn(resource);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient()
                .getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        try {
            minorVersionServiceHelper.checkCrossTagRules(replicaSet, "alisql_docker_image", "alisql_xc_docker_image", "1875785612443619");
            Assert.fail();
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }
}
