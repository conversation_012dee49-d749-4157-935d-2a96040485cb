package com.aliyun.dba.poddefault.action.support;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.bak.dataobject.ExtendedLogPlanDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.CHARGE_YES;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PodParameterHelperTest {

    @InjectMocks
    private PodParameterHelper podParameterHelper;

    @Mock
    private ResourceService resourceService;

    @Mock
    private InstanceService instanceService;

    @Mock
    private BakService bakService;

    private String requestId = "requestId";
    private String tmpReplicaSetName = "tmpReplicaSetName";
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private ReplicaSet replicaSet;
    @Mock
    private ReplicaSet tmpReplicaSet;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testFixAvailableZoneInfo_ValidDataWithNullSlaveVSwitch() {
        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO> azList = Arrays.asList(
                createAzInfo("master", "vsw-1"),
                createAzInfo("slave", null)
        );
        multiAVZExParamDO.setAvailableZoneInfoList(azList);
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, "region", "zoneId", "vSwitchId", "regionId", "regionCategory", multiAVZExParamDO);

        podParameterHelper.fixAvailableZoneInfo(avzInfo);
        Assert.assertEquals("vsw-1", avzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList().get(1).getVSwitchID());
    }

    @Test
    public void testFixAvailableZoneInfo_ValidDataWithNonNullSlaveVSwitch() {

        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO> azList = Arrays.asList(
                createAzInfo("master", "vsw-1"),
                createAzInfo("slave", "vsw-2")
        );
        multiAVZExParamDO.setAvailableZoneInfoList(azList);
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, "region", "zoneId", "vSwitchId", "regionId", "regionCategory", multiAVZExParamDO);

        podParameterHelper.fixAvailableZoneInfo(avzInfo);
        Assert.assertEquals("vsw-2", avzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList().get(1).getVSwitchID());
    }

    private AvailableZoneInfoDO createAzInfo(String role, String vSwitchID) {
        AvailableZoneInfoDO azInfo = new AvailableZoneInfoDO();
        azInfo.setRole(role);
        azInfo.setVSwitchID(vSwitchID);
        return azInfo;
    }

    @Test
    public void setVbmLabelForTempReplicaSet_ValidConditions_ShouldSetLabel() throws ApiException {
        when(replicaSet.getBizType()).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);
        when(replicaSet.getInsType()).thenReturn(ReplicaSet.InsTypeEnum.MAIN);
        when(dBaasMetaService.getDefaultClient()).thenReturn(mock(DefaultApi.class));
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, tmpReplicaSetName, false)).thenReturn(tmpReplicaSet);
        podParameterHelper.setVbmLabelForTempReplicaSet(requestId, replicaSet, tmpReplicaSetName);
        Map<String, String> labels = new HashMap<>();
        labels.put(PodDefaultConstants.VBM_CUSTINS_LABEL_KEY, PodDefaultConstants.VBM_CUSTINS_LABEL_VALUE);
        verify(dBaasMetaService.getDefaultClient()).updateReplicaSetLabels(requestId, tmpReplicaSet.getName(), labels);
    }
    @Test
    public void setVbmLabelForTempReplicaSet_InvalidBizType_ShouldNotSetLabel() throws ApiException {
        when(replicaSet.getBizType()).thenReturn(ReplicaSet.BizTypeEnum.ALIGROUP);
        when(dBaasMetaService.getDefaultClient()).thenReturn(mock(DefaultApi.class));
        podParameterHelper.setVbmLabelForTempReplicaSet(requestId, replicaSet, tmpReplicaSetName);
        verify(dBaasMetaService.getDefaultClient(), never()).getReplicaSet(anyString(), anyString(), anyBoolean());
    }
    @Test
    public void setVbmLabelForTempReplicaSet_NullTmpReplicaSetName_ShouldNotSetLabel() throws ApiException {
        when(dBaasMetaService.getDefaultClient()).thenReturn(mock(DefaultApi.class));
        podParameterHelper.setVbmLabelForTempReplicaSet(requestId, replicaSet, null);
        verify(dBaasMetaService.getDefaultClient(), never()).getReplicaSet(anyString(), anyString(), anyBoolean());
    }





        @Test
        public void checkShrinkCloudESSDValid_InstancePerfNull_ShouldThrowException() throws Exception {
            when(instanceService.getInstancePerfByCustinsId(anyInt(), anyInt())).thenReturn(null);

            assertThrows(RdsException.class, () -> podParameterHelper.checkShrinkCloudESSDValid("requestId", 1, "uid", 100, 50, new ExtendedLogPlanDO()));
        }

        @Test
        public void checkShrinkCloudESSDValid_InstancePerfDiskCurrEmpty_ShouldUseSrcDiskSize() throws Exception {
            InstancePerfDO instancePerf = new InstancePerfDO();
            instancePerf.setDiskCurr("");
            when(instanceService.getInstancePerfByCustinsId(anyInt(), anyInt())).thenReturn(instancePerf);

            assertThrows(RdsException.class, () -> podParameterHelper.checkShrinkCloudESSDValid("requestId", 1, "uid", 100, 50, new ExtendedLogPlanDO()));
        }

        @Test
        public void checkShrinkCloudESSDValid_InstancePerfDiskCurrValid_ShouldUseDiskCurr() throws Exception {
            InstancePerfDO instancePerf = new InstancePerfDO();
            instancePerf.setDiskCurr("50000");
            when(instanceService.getInstancePerfByCustinsId(anyInt(), anyInt())).thenReturn(instancePerf);

            assertThrows(RdsException.class, () -> podParameterHelper.checkShrinkCloudESSDValid("requestId", 1, "uid", 100, 50, new ExtendedLogPlanDO()));
        }

        @Test
        public void checkShrinkCloudESSDValid_ResourceDoNull_ShouldUseDefaultValues() throws Exception {
            InstancePerfDO instancePerf = new InstancePerfDO();
            instancePerf.setDiskCurr("50000");
            when(instanceService.getInstancePerfByCustinsId(anyInt(), anyInt())).thenReturn(instancePerf);
            when(resourceService.getResourceByResKey(anyString())).thenReturn(null);

            assertThrows(RdsException.class, () -> podParameterHelper.checkShrinkCloudESSDValid("requestId", 1, "uid", 100, 50, new ExtendedLogPlanDO()));
        }

        @Test
        public void checkShrinkCloudESSDValid_ResourceDoRealValueEmpty_ShouldUseDefaultValues() throws Exception {
            InstancePerfDO instancePerf = new InstancePerfDO();
            instancePerf.setDiskCurr("50000");
            when(instanceService.getInstancePerfByCustinsId(anyInt(), anyInt())).thenReturn(instancePerf);

            ResourceDO resourceDO = new ResourceDO();
            resourceDO.setRealValue("");
            when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

            assertThrows(RdsException.class, () -> podParameterHelper.checkShrinkCloudESSDValid("requestId", 1, "uid", 100, 50, new ExtendedLogPlanDO()));
        }

        @Test
        public void checkShrinkCloudESSDValid_ResourceDoRealValueValid_ShouldUseCustomValues() throws Exception {
            InstancePerfDO instancePerf = new InstancePerfDO();
            instancePerf.setDiskCurr("50000");
            when(instanceService.getInstancePerfByCustinsId(anyInt(), anyInt())).thenReturn(instancePerf);

            ResourceDO resourceDO = new ResourceDO();
            resourceDO.setRealValue("{\"uid\":{\"limitRatio\":0.5,\"limitOffset\":10,\"minOffset\":5,\"checkBinlog\":true}}");
            when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

            ExtendedLogPlanDO extendedLogPlanDO = new ExtendedLogPlanDO();
            extendedLogPlanDO.setLocalRetain(1000);
            extendedLogPlanDO.setLocalRetainNum(100);
            extendedLogPlanDO.setCharge(CHARGE_YES);

            assertThrows(RdsException.class, () -> podParameterHelper.checkShrinkCloudESSDValid("requestId", 1, "uid", 100, 50, extendedLogPlanDO));
        }

        @Test
        public void checkShrinkCloudESSDValid_CheckBinlogTrue_ShouldInvokeBinlogCheck() throws Exception {
            InstancePerfDO instancePerf = new InstancePerfDO();
            instancePerf.setDiskCurr("50000");
            when(instanceService.getInstancePerfByCustinsId(anyInt(), anyInt())).thenReturn(instancePerf);

            ResourceDO resourceDO = new ResourceDO();
            resourceDO.setRealValue("{\"uid\":{\"limitRatio\":0.5,\"limitOffset\":10,\"minOffset\":5,\"checkBinlog\":true}}");
            when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

            ExtendedLogPlanDO extendedLogPlanDO = new ExtendedLogPlanDO();
            extendedLogPlanDO.setLocalRetain(1000);
            extendedLogPlanDO.setLocalRetainNum(100);
            extendedLogPlanDO.setCharge(CHARGE_YES);

            when(bakService.countBinlogFileListByCondition(anyMap())).thenReturn(100);

            assertThrows(RdsException.class, () -> podParameterHelper.checkShrinkCloudESSDValid("requestId", 1, "uid", 100, 50, extendedLogPlanDO));
        }

        @Test
        public void checkShrinkCloudESSDValid_TargetDiskSizeValid_ShouldNotThrowException() throws Exception {
            InstancePerfDO instancePerf = new InstancePerfDO();
            instancePerf.setDiskCurr("50000");
            when(instanceService.getInstancePerfByCustinsId(anyInt(), anyInt())).thenReturn(instancePerf);

            ResourceDO resourceDO = new ResourceDO();
            resourceDO.setRealValue("{\"uid\":{\"limitRatio\":0.5,\"limitOffset\":10,\"minOffset\":5,\"checkBinlog\":false}}");
            when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

            podParameterHelper.checkShrinkCloudESSDValid("requestId", 1, "uid", 100, 100, new ExtendedLogPlanDO());
        }

        @Test
        public void test_isServerlessV2() {
            assertFalse(podParameterHelper.isServerlessV2("uid", PodType.POD_ECS_RUND));
        }

}
