package com.aliyun.dba.poddefault.action.service;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.GeneralCloudDisk;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.poddefault.action.service.createReadOnly.BaseCreateReadOnlyInsService;
import com.aliyun.dba.poddefault.action.service.createReadOnly.aliyun.AliyunCreateReadOnlyInsImpl;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.support.property.ParamConstants.DispenseMode.ClassicDispenseMode;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@PrepareForTest({LogFactory.class})
public class BaseCreateReadOnlyInsServiceTest {

    @Mock
    private DBaasMetaService metaService;

    @Mock
    private IpWhiteListService ipWhiteListService;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private PodCommonSupport podCommonSupport;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Test
    public void testAddInstanceLabels_Success() throws Exception {
        // Arrange
        CreateReadOnlyInsRequest request = new CreateReadOnlyInsRequest();
        request.setIsArmIns(true);
        request.setCenterRegionId("centerRegionId");
        InstanceLevel instanceLevel = new InstanceLevel();
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("instructionSetArch", "x86");
        instanceLevel.setExtraInfo(extraInfo.toString());
        request.setPrimaryInsInstanceLevel(instanceLevel);
        request.setDbType("mysql");
        request.setIsCreatingGdnInstance(false);
        ParamConstants.DispenseMode dispenseMode = ClassicDispenseMode;
        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<com. aliyun. dba. common. dataobject. AvailableZoneInfoDO> availableZoneInfoList = new ArrayList<>();
        multiAVZExParamDO.setAvailableZoneInfoList(availableZoneInfoList);
        AVZInfo avzInfo = new AVZInfo(dispenseMode, "regionId", "zoneId", "regionCategory", multiAVZExParamDO);
        request.setAvzInfo(avzInfo);
        request.setRequestId("requestId");
        ReplicaSet readReplicaSet = new ReplicaSet();
        readReplicaSet.setId(123456L);
        ReplicaSet primaryReplicaSet = new ReplicaSet();
        primaryReplicaSet.setId(123456L);
        request.setPrimaryReplicaSet(primaryReplicaSet);
        request.setIsXdbEngine(false);
        String composeTag = "testComposeTag";
        String config = "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT";
        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config configItem = new Config();
        configItem.setValue("true");
        items.add(configItem);
        configListResult.setItems(items);
        // add compression
        request.setCompressionMode("on");
        request.setCompressionRatio(2.0);
        request.setDiskSizeGBBeforeCompression(100);

        Map<String, String> primaryInsLabels = new HashMap<>();
        primaryInsLabels.put("key1", "value1");

        DefaultApi defaultApi = mock(DefaultApi.class);
        DefaultApi regionClient = mock(DefaultApi.class);

        when(metaService.getRegionClient("centerRegionId")).thenReturn(regionClient);
        when(regionClient.listReplicaSetLabels("requestId", null)).thenReturn(primaryInsLabels);
        when(metaService.getDefaultClient()).thenReturn(defaultApi);
//        when(defaultApi.listConfigs("requestId", config)).thenReturn(configListResult);
        //when(minorVersionServiceHelper.resetReplicaSetMinorVersion(anyString(), anyString())).thenReturn("minorVersionValue");
        //when(podCommonSupport.isIoAccelerationEnabled(any(GeneralCloudDisk.class))).thenReturn(true);

        // Act
        BaseCreateReadOnlyInsService baseCreateReadOnlyInsServiceSpy = spy(new AliyunCreateReadOnlyInsImpl());
        Whitebox.setInternalState(baseCreateReadOnlyInsServiceSpy, "metaService", metaService);
        Whitebox.setInternalState(baseCreateReadOnlyInsServiceSpy, "minorVersionServiceHelper", minorVersionServiceHelper);
        Whitebox.setInternalState(baseCreateReadOnlyInsServiceSpy, "podCommonSupport", podCommonSupport);
        Whitebox.setInternalState(baseCreateReadOnlyInsServiceSpy, "podParameterHelper", podParameterHelper);
        Whitebox.setInternalState(baseCreateReadOnlyInsServiceSpy, "ipWhiteListService", ipWhiteListService);
        Whitebox.invokeMethod(baseCreateReadOnlyInsServiceSpy, "addInstanceLables", request, readReplicaSet, composeTag);

        // Assert
        verify(metaService.getDefaultClient()).updateReplicaSetLabels(eq("requestId"), eq(null), any(Map.class));
    }
}
