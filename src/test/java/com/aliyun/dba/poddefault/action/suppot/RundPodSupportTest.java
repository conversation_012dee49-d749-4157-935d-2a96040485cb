package com.aliyun.dba.poddefault.action.suppot;

import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.support.RundPodSupport;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.google.common.collect.ImmutableMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.APP_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class RundPodSupportTest {
    @InjectMocks
    private RundPodSupport rundPodSupport;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private MySQLServiceImpl mySQLService;

    @Before
    public void setUp() throws RdsException {
        PowerMockito.mockStatic(RequestSession.class);
        DefaultApi dbaasDefaultApi = Mockito.mock(DefaultApi.class);
        Mockito.when(dBaasMetaService.getDefaultClient())
                .thenReturn(dbaasDefaultApi);
    }

    @Test
    public void testIsRundReplica() {
        ReplicaResource replicaResource = new ReplicaResource();
        Vpod vpod = new Vpod();
        vpod.setRuntimeType("rund");
        replicaResource.setVpod(vpod);
        boolean rundReplica = rundPodSupport.isRundReplica(replicaResource);
        Assert.assertEquals(true, rundReplica);
    }

    @Test
    public void testIsRundReplicaSet() throws ApiException {
        Map<String, String> params = ImmutableMap.of("requestid", UUID.randomUUID().toString());
        RequestSession.init("rdsapi_ext_mysql", params);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("replicaSet");
        replicaSet.setKindCode(18);
        ReplicaResource replicaResource = new ReplicaResource();
        Vpod vpod = new Vpod();
        vpod.setRuntimeType("rund");
        replicaResource.setVpod(vpod);
        Replica replica = new Replica();
        when(mySQLService.getReplicaByRole(RequestSession.getRequestId(), replicaSet.getName(), Replica.RoleEnum.MASTER)).thenReturn(replica);
        when(dBaasMetaService.getDefaultClient().getReplica(any(), any(), any())).thenReturn(replicaResource);
        boolean rundReplica = rundPodSupport.isRundReplicaSet(replicaSet);
        Assert.assertEquals(true, rundReplica);
    }
}
