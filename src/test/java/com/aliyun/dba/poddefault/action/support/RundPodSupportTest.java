
package com.aliyun.dba.poddefault.action.support;

import com.aliyun.dba.base.service.CrmService;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel.CategoryEnum;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ParamConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RundPodSupportTest {

    @InjectMocks
    private RundPodSupport rundPodSupport;

    @Mock
    private ResourceService resourceService;

    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private CrmService crmService;

    private AVZInfo avzInfo;

    @Before
    public void setUp() throws Exception {
        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO> zoneList = new ArrayList<>();
        AvailableZoneInfoDO availableZoneInfoDO = new AvailableZoneInfoDO();
        availableZoneInfoDO.setZoneID("cn-beijing-i");
        zoneList.add(availableZoneInfoDO);
        multiAVZExParamDO.setAvailableZoneInfoList(zoneList);
        when(crmService.getGcLevel(any())).thenReturn("GC5");
        avzInfo = new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, "cn-beijing", "cn-beijing", "aliyun", multiAVZExParamDO);

    }

    @Test
    public void testGetPodType_ByGrayConfig_CategoryNotBasic_ReturnsFalse() {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.STANDARD);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("region", "uid", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_ArmInstance_ReturnsFalse() {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);
        instanceLevel.setExtraInfo("{\"instructionSetArch\":\"ARM\"}");

        PodType result = rundPodSupport.getPodTypeByGrayConfig("region", "uid", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_ResourceNotFound_ReturnsFalse() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);
        when(resourceService.getResourceByResKey(anyString())).thenReturn(null);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("region", "uid", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_InvalidJson_ReturnsFalse() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{invalid json}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("region", "uid", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_NotInGrayList_ReturnsFalse() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497,1692999668519777,1336888804881051,1825188702113631,1984829570443947,1618711616067156,1452886459974240,1875785612443619,1056512728263377\",\"gc\":\"5-7\",\"ratio\":\"0-0\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"serverless_basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497,1692999668519777,1336888804881051,1825188702113631,1984829570443947,1618711616067156,1452886459974240,1875785612443619,1266348003653919\",\"gc\":\"5-7\",\"ratio\":\"0-0\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-l,cn-beijing-i\",\"uid\":\"1768383226435801,1056512728263377\",\"gc\":\"0-5\",\"ratio\":\"0-0\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-chengdu-a,cn-chengdu-b\",\"uid\":\"1582064160422497,1056512728263377,1875785612443619,1056512728263377\",\"gc\":\"0-4\",\"ratio\":\"0-0\"},\"result\":\"ecs_rund\"}]}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("cn-chengdu", "uid3", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_InGrayList_ReturnsTrue() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497,1582064160422498\",\"gc\":\"0-7\",\"ratio\":\"0-1\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"123\",\"gc\":\"5-7\",\"ratio\":\"50-80\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-4\",\"ratio\":\"0-40\"},\"result\":\"ecs_rund\"}]}");

        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("region", "uid", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_ExceptionHandling_ReturnsFalse() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        when(resourceService.getResourceByResKey(anyString())).thenThrow(new RuntimeException("test exception"));

        PodType result = rundPodSupport.getPodTypeByGrayConfig("region", "uid", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_Vbm_ReturnsTrue() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422498\",\"gc\":\"0-50\",\"ratio\":\"0-1\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"123\",\"gc\":\"5-7\",\"ratio\":\"50-100\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-4\",\"ratio\":\"0-40\"},\"result\":\"ecs_rund\"}]}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("cn-beijing", "1582064160422497", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_VBM_RUND,result);
    }


    @Test
    public void testGetPodType_ByGrayConfig_not_hit_region() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497,1582064160422498\",\"gc\":\"0-7\",\"ratio\":\"0-1\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"123\",\"gc\":\"5-7\",\"ratio\":\"50-80\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-4\",\"ratio\":\"0-40\"},\"result\":\"ecs_rund\"}]}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("cn-biejing", "1582064160422497", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_not_hit_category() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.STANDARD);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497,1582064160422498\",\"gc\":\"0-7\",\"ratio\":\"0-1\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"123\",\"gc\":\"5-7\",\"ratio\":\"50-80\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-4\",\"ratio\":\"0-40\"},\"result\":\"ecs_rund\"}]}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("cn-beijing", "1582064160422497", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_not_hit_zoneId() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497,1582064160422498\",\"gc\":\"0-7\",\"ratio\":\"0-1\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"123\",\"gc\":\"5-7\",\"ratio\":\"50-80\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-4\",\"ratio\":\"0-40\"},\"result\":\"ecs_rund\"}]}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("cn-beijing", "1582064160422497", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_hit_uid() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497,1582064160422498\",\"gc\":\"0-7\",\"ratio\":\"0-1\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"123\",\"gc\":\"5-7\",\"ratio\":\"50-80\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-4\",\"ratio\":\"0-40\"},\"result\":\"ecs_rund\"}]}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("cn-beijing", "1582064160422497", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_ECS_RUND,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_not_hit_uid() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497,1582064160422498\",\"gc\":\"0-7\",\"ratio\":\"0-1\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"123\",\"gc\":\"5-7\",\"ratio\":\"50-80\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-4\",\"ratio\":\"0-40\"},\"result\":\"ecs_rund\"}]}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);

        PodType result = rundPodSupport.getPodTypeByGrayConfig("cn-beijing", "1582064160422499", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_RUNC,result);
    }

    @Test
    public void testGetPodType_ByGrayConfig_hit_Ratio() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(CategoryEnum.BASIC);

        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue("{\"cn-beijing\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-7\",\"ratio\":\"0-85\"},\"result\":\"ecs_rund\"},{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"123\",\"gc\":\"5-7\",\"ratio\":\"50-80\"},\"result\":\"vbm_rund\"}],\"cn-chengdu\":[{\"conditions\":{\"category\":\"basic\",\"zoneId\":\"cn-beijing-i,cn-beijing-h,cn-beijing-k\",\"uid\":\"1582064160422497\",\"gc\":\"0-4\",\"ratio\":\"0-40\"},\"result\":\"ecs_rund\"}]}");
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);
        PodType result = rundPodSupport.getPodTypeByGrayConfig("cn-beijing", "158206416042249", instanceLevel, avzInfo);
        Assert.assertEquals(PodType.POD_ECS_RUND,result);
    }
}