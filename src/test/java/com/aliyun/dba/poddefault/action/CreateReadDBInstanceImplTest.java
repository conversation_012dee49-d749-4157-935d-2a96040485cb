package com.aliyun.dba.poddefault.action;


import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.createReadOnly.BaseCreateReadOnlyInsService;
import com.aliyun.dba.poddefault.action.service.createReadOnly.aligroup.AligroupCreateReadOnlyInsImpl;
import com.aliyun.dba.poddefault.action.service.createReadOnly.aliyun.AliyunCreateReadOnlyInsImpl;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(SpringContextUtil.class)
public class CreateReadDBInstanceImplTest {

    @InjectMocks
    private CreateReadDBInstanceImpl createReadDBInstanceImpl;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private MysqlParamSupport paramSupport;

    @Mock
    private DBaasMetaService metaService;

    @Mock
    private GdnInstanceService gdnInstanceService;

    @Mock
    private MysqlParameterHelper mysqlParameterHelper;

    @Mock
    private PodCommonSupport podCommonSupport;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;

    @Mock
    private MysqlEncryptionService mysqlEncryptionService;

    @Mock
    private BaseCreateReadOnlyInsService baseCreateReadOnlyInsService;

    @Mock
    private LogAgent logger;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private AliyunCreateReadOnlyInsImpl aliyunCreateReadOnlyIns;
    @Mock
    private AligroupCreateReadOnlyInsImpl aligroupCreateReadOnlyInsService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(metaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.mockStatic(SpringContextUtil.class);
        Mockito.when(SpringContextUtil.getBeanByClassName(eq(AliyunCreateReadOnlyInsImpl.class))).thenReturn(aliyunCreateReadOnlyIns);
        Mockito.when(SpringContextUtil.getBeanByClass(eq(AligroupCreateReadOnlyInsImpl.class))).thenReturn(aligroupCreateReadOnlyInsService);
    }


    @Test
    public void doActionRequest_WithGdn_ShouldDispatchTask() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.BIZ_TYPE, ReplicaSet.BizTypeEnum.ALIYUN.toString());
        params.put(ParamConstants.DB_INSTANCE_STORAGE_TYPE.toLowerCase(), "cloud_essd");
        params.put(ParamConstants.DB_INSTANCE_CLASS, "classCode");
        params.put(ParamConstants.DB_INSTANCE_NAME, "dbInstanceName");
        params.put(ParamConstants.ENGINE_VERSION, "dbVersion");
        params.put(ParamConstants.DB_INSTANCE_TYPE, "dbType");
        params.put(ParamConstants.DB_INSTANCE_CONN_TYPE, "connType");
        params.put(ParamConstants.PORT, "1234");
        params.put(ParamConstants.STORAGE, "100");
        params.put(ParamConstants.VPC_ID, "vpcId");
        params.put(ParamConstants.VSWITCH_ID, "vSwitchId");
        params.put(ParamConstants.IP_ADDRESS, "ipAddress");
        params.put(ParamConstants.VPC_INSTANCE_ID, "vpcInstanceId");
        params.put(ParamConstants.ACCESSID, "accessId");
        params.put(ParamConstants.ORDERID, "orderId");
        params.put("ReadDBInstanceName", "rr-2zen5c2kf327ib48s");
        params.put("TargetMinorVersion", "targetMinorVersion");
        params.put("RsTemplateName", "rsTemplateName");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setDiskSizeMB(102400);
        replicaSet.setService("mysql");
        replicaSet.setServiceVersion("8.0");
        replicaSet.setClassCode("classCode");

        when(replicaSetService.getAndCheckUserReplicaSetFromCenterRegion(any(), any())).thenReturn(replicaSet);
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        when(paramSupport.getAutoCreateProxy(any())).thenReturn(true);
        when(aliyunCreateReadOnlyIns.doCreateReadOnly(any(), any())).thenReturn("taskId");

        when(defaultApi.getUserById(any(), any(), any())).thenReturn(new User());
        when(paramSupport.getParameterValue(params, "ReadDBInstanceName")).thenReturn("rr-2zen5c2kf327ib48s");
        when(paramSupport.getParameterValue(params, "GdnInstanceName")).thenReturn("GdnInstanceName");
        when(paramSupport.getParameterValue(params, ParamConstants.STORAGE, 100)).thenReturn("100");
        when(gdnInstanceService.getPrimaryMember(any(), any(), any())).thenReturn(new InstanceMember());
        when(replicaSetService.getAndCheckUserReplicaSetFromCenterRegion(any(), any())).thenReturn(replicaSet);
        when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(new InstanceLevel());

        Map<String, Object> result = createReadDBInstanceImpl.doActionRequest(new CustInstanceDO(), params);

        verify(workFlowService, times(1)).dispatchTask(anyString(), any(), anyString(), anyString(), anyString(), anyInt());
        assertEquals("taskId", result.get("TaskId"));
    }

    @Test
    public void doActionRequest_NotGdn_CompressionON_ShouldDispatchTask() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.BIZ_TYPE, ReplicaSet.BizTypeEnum.ALIYUN.toString());
        params.put(ParamConstants.DB_INSTANCE_STORAGE_TYPE.toLowerCase(), "cloud_essd");
        params.put(ParamConstants.DB_INSTANCE_CLASS, "classCode");
        params.put(ParamConstants.DB_INSTANCE_NAME, "dbInstanceName");
        params.put(ParamConstants.ENGINE_VERSION, "dbVersion");
        params.put(ParamConstants.DB_INSTANCE_TYPE, "dbType");
        params.put(ParamConstants.DB_INSTANCE_CONN_TYPE, "connType");
        params.put(ParamConstants.PORT, "1234");
        params.put(ParamConstants.STORAGE, "100");
        params.put(ParamConstants.VPC_ID, "vpcId");
        params.put(ParamConstants.VSWITCH_ID, "vSwitchId");
        params.put(ParamConstants.IP_ADDRESS, "ipAddress");
        params.put(ParamConstants.VPC_INSTANCE_ID, "vpcInstanceId");
        params.put(ParamConstants.ACCESSID, "accessId");
        params.put(ParamConstants.ORDERID, "orderId");
        params.put("ReadDBInstanceName", "rr-2zen5c2kf327ib48s");
        params.put("TargetMinorVersion", "targetMinorVersion");
        params.put("RsTemplateName", "rsTemplateName");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setDiskSizeMB(102400);
        replicaSet.setService("mysql");
        replicaSet.setServiceVersion("8.0");
        replicaSet.setClassCode("classCode");

        when(replicaSetService.getAndCheckUserReplicaSetFromCenterRegion(any(), any())).thenReturn(replicaSet);
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        when(paramSupport.getAutoCreateProxy(any())).thenReturn(true);
        when(aliyunCreateReadOnlyIns.doCreateReadOnly(any(), any())).thenReturn("taskId");

        when(defaultApi.getUserById(any(), any(), any())).thenReturn(new User());
        when(paramSupport.getParameterValue(params, "ReadDBInstanceName")).thenReturn("rr-2zen5c2kf327ib48s");
        when(paramSupport.getParameterValue(params, ParamConstants.STORAGE, 100)).thenReturn("100");
        when(gdnInstanceService.getPrimaryMember(any(), any(), any())).thenReturn(new InstanceMember());
        when(replicaSetService.getAndCheckUserReplicaSetFromCenterRegion(any(), any())).thenReturn(replicaSet);
        when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(new InstanceLevel());
        when(cloudDiskCompressionHelper.getCompressionMode(any(), any(), any())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
        when(paramSupport.hasParameter(params, ParamConstants.STORAGE)).thenReturn(true);
        Map<String, Object> result = createReadDBInstanceImpl.doActionRequest(new CustInstanceDO(), params);

        verify(workFlowService, times(1)).dispatchTask(anyString(), any(), anyString(), anyString(), anyString(), anyInt());
        assertEquals("taskId", result.get("TaskId"));
    }


}
