package com.aliyun.dba.poddefault.action;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/17
 */

import java.util.*;
import java.math.*;

import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceServiceImpl;
import com.aliyun.dba.poddefault.action.service.TdeKmsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserServiceImpl;
import com.google.common.collect.ImmutableMap;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ActionParamsProvider.class, RequestSession.class})
public class ModifyDBInstanceTDEImplTest {

    @InjectMocks
    private ModifyDBInstanceTDEImpl modifyDBInstanceTDEImpl;

    @Mock
    private WorkFlowService workflowService;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private MysqlEncryptionService mysqlEncryptionService;

    @Mock
    private TdeKmsService tdeKmsService;

    @Mock
    private UserServiceImpl userService;

    @Mock
    private CustinsServiceImpl custinsService;

    @Mock
    private InstanceServiceImpl instanceService;

    @Mock
    private KmsApi kmsApi;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(ActionParamsProvider.class);
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("regionID");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setCategory("standard");
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        when(tdeKmsService.checkTdeSupported(any(), any())).thenReturn(true);
        when(kmsApi.checkAssumeRoleOk(any(), any(), any())).thenReturn(true);
    }

    @Test
    public void testDoActionRequestBYOKKey_Success() throws Exception {
        // Arrange

        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("");

        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);

        when(podParameterHelper.getParameterValue(anyString())).thenReturn("1", "keyId");
        when(workflowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");

        CustInstanceDO coCustInstanceDo = new CustInstanceDO();
        coCustInstanceDo.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        coCustInstanceDo.setId(2);

        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(any(), anyBoolean())).thenReturn(Collections.singletonList(coCustInstanceDo));

        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequestServiceKey_Success() throws Exception {
        // Arrange

        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("roleArn");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);

        when(podParameterHelper.getParameterValue(anyString())).thenReturn("1", "");
        when(workflowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(any(), eq(false))).thenReturn(new ArrayList<>());

        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_UnsupportedDBInstanceTypeReadins() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(2); // Not primary

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);

        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_InvalidParams() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn(null);

        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_TdeStatusNotEnabled() throws RdsException {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_TDE_STATUS)).thenReturn("0");

        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_TdeAlreadyConfigured() throws RdsException {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_TDE_STATUS)).thenReturn("1");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("1");
        when(custinsParamService.getCustinsParam(any(), eq(PodDefaultConstants.TDE_ENABLED))).thenReturn(custinsParamDO);

        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        Object[] errorCode = (Object[])result.get("errorCode");
        assertNotNull(errorCode);
        assertEquals(ErrorCode.TDESTATUS_ALREADY_CONFIGED.getCode(), errorCode[0]);
    }

    @Test
    public void testDoActionRequest_SupportedDBType() throws RdsException {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_TDE_STATUS)).thenReturn("1");

        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_UnsupportedDBInstanceType() throws Exception {
        // Arrange

        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setCategory("basic");
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);


        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_InternalFailure() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_TDE_STATUS)).thenReturn("1");
        when(workflowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), any())).thenThrow(new RuntimeException());

        // Act
        Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_UnsupportedDBState() {
        try {
            CustInstanceDO custins = new CustInstanceDO();
            custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
            custins.setId(1);
            custins.setClusterName("clusterName");
            custins.setInsName("insName");
            custins.setDbType("mysql");
            custins.setInsType(0);
            custins.setDbVersion("5.7");
            when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);

            List<Map<String, Object>> stateList = Collections.singletonList(ImmutableMap.of("DBInstanceStatus", 0));
            when(custinsService.getCustinsStatusByCustinsNames(any())).thenReturn(stateList);

            Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(custins, new HashMap<>());
            Object[] errorCode = (Object[])result.get("errorCode");
            assertNotNull(errorCode);
            assertEquals(ErrorCode.UNSUPPORTED_DB_STATUS.getSummary(), errorCode[1]);
        } catch (Exception e) {
            fail();
        }
    }

    @Test
    public void testDoActionRequest_UnsupportedTDE() {
        try {
            when(tdeKmsService.checkTdeSupported(any(), any())).thenReturn(false);
            Map<String, Object> result = modifyDBInstanceTDEImpl.doActionRequest(null, new HashMap<>());
            Object[] errorCode = (Object[]) result.get("errorCode");
            assertNotNull(errorCode);
            assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE.getSummary(), errorCode[1]);
        } catch (Exception e) {
            fail();
        }
    }
}
