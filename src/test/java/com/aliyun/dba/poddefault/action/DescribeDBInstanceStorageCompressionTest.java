package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DescribeDBInstanceStorageCompressionTest {
    private static final Logger log = LoggerFactory.getLogger(DescribeDBInstanceStorageCompressionTest.class);
    @Mock
    protected MysqlParamSupport mysqlParamSupport;
    @Mock
    protected ReplicaSetService replicaSetService;
    @Mock
    protected CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Mock
    protected DBaasMetaService dBaasMetaService;
    @InjectMocks
    protected DescribeDBInstanceStorageCompressionImpl describeDBInstanceStorageCompression;


    @Mock
    private DefaultApi defaultApi;

    @Before
    public void setUp() {
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test
    public void doActionRequest_SuccessfulExecution_ReturnsResult_ReadOnly() throws Exception {
        // 准备
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put(ParamConstants.REGION_ID, "regionId");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setDiskSizeMB(10240);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.READONLY);
        replicaSet.setName("replicaSetName");

        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParamSupport.getAndCheckRegionID(params)).thenReturn("regionId");
        when(replicaSetService.getReplicaSetStorageType(replicaSet.getName(), "requestId")).thenReturn(CustinsSupport.STORAGE_TYPE_CLOUD_AUTO);

        User user = new User();
        user.setAliUid("uid");
        when(defaultApi.getUser("requestId", replicaSet.getUserId(), false)).thenReturn(user);

        InstanceLevel instanceLevel = new InstanceLevel();
        when(defaultApi.getInstanceLevel("requestId", replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), null)).thenReturn(instanceLevel);

        when(cloudDiskCompressionHelper.getCompressionRatio("requestId", replicaSet.getName(), null, null, null)).thenReturn(1.0);
        when(cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, replicaSet.getService(), "uid", "regionId", 10L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO,false)).thenReturn(new HashMap<>());
        when(defaultApi.getReplicaSetLabel("requestId", replicaSet.getName(), CloudDiskCompressionHelper.CUSTINS_PARAM_COMPRESSION_MODE)).thenReturn("on");
        when(defaultApi.getReplicaSetLabel("requestId", replicaSet.getName(), CUSTINS_PARAM_DISK_SIZE_BEFORE_COMPRESSION)).thenReturn("102400");

        Map<String, Object> result = describeDBInstanceStorageCompression.doActionRequest(null, params);

        assertEquals(true, result.get(SUPPORT_COMPRESSION));
        assertEquals("on", result.get(CloudDiskCompressionHelper.COMPRESSION_MODE));
        assertEquals(1.0, result.get(CloudDiskCompressionHelper.COMPRESSION_RATIO));
        assertEquals(100, result.get("DbInstanceStorage"));
    }

    @Test
    public void doActionRequest_SuccessfulExecution_ReturnsResult_MASTER() throws Exception {
        // 准备
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put(ParamConstants.REGION_ID, "regionId");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setDiskSizeMB(10240);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("replicaSetName");

        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParamSupport.getAndCheckRegionID(params)).thenReturn("regionId");
        when(replicaSetService.getReplicaSetStorageType(replicaSet.getName(), "requestId")).thenReturn(CustinsSupport.STORAGE_TYPE_CLOUD_AUTO);

        User user = new User();
        user.setAliUid("uid");
        when(defaultApi.getUser("requestId", replicaSet.getUserId(), false)).thenReturn(user);

        InstanceLevel instanceLevel = new InstanceLevel();
        when(defaultApi.getInstanceLevel("requestId", replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), null)).thenReturn(instanceLevel);

        Map<String, Object> checkResult = new HashMap<>();
        checkResult.put(LIMIT_REASON, VALUE_BLANK_REASON);
        checkResult.put(SUPPORT_COMPRESSION, true);
        when(cloudDiskCompressionHelper.getCompressionRatio("requestId", replicaSet.getName(), null, null, null)).thenReturn(1.0);
        when(cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, replicaSet.getService(), "uid", "regionId", 10L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO,false)).thenReturn(checkResult);
        when(cloudDiskCompressionHelper.checkReadInsCompressionModeOn("requestId", replicaSet.getName(), false)).thenReturn(new HashMap<>());
        when(defaultApi.getReplicaSetLabel("requestId", replicaSet.getName(), CloudDiskCompressionHelper.CUSTINS_PARAM_COMPRESSION_MODE)).thenReturn("on");

        Map<String, Object> result = describeDBInstanceStorageCompression.doActionRequest(null, params);

        assertEquals(true, result.get(SUPPORT_COMPRESSION));
        assertEquals("on", result.get(CloudDiskCompressionHelper.COMPRESSION_MODE));
        assertEquals(1.0, result.get(CloudDiskCompressionHelper.COMPRESSION_RATIO));
        assertEquals(10, result.get("DbInstanceStorage"));
    }

    @Test
    public void doActionRequest_ExceptionThrown_ReturnsErrorResponse() throws Exception {
        // 准备
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put(ParamConstants.REGION_ID, "regionId");

        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenThrow(new RuntimeException("internal exception"));

        Map<String, Object> result = describeDBInstanceStorageCompression.doActionRequest(null, params);
        assertEquals(JSONObject.toJSONString(createErrorResponse(ErrorCode.INTERNAL_FAILURE).get("errorCode")), JSONObject.toJSONString(result.get("errorCode")));
    }

    @Test
    public void doActionRequest_RdsException_ErrorResponse() throws Exception {
        // 准备
        Map<String, String> params = new HashMap<>();
        params.put("requestId", "requestId");
        params.put("regionId", "regionId");

        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenThrow(new RdsException(ErrorCode.INVALID_PARAM));

        Map<String, Object> result = describeDBInstanceStorageCompression.doActionRequest(new CustInstanceDO(), params);

        assertEquals(JSONObject.toJSONString(createErrorResponse(ErrorCode.INVALID_PARAM).get("errorCode")), JSONObject.toJSONString(result.get("errorCode")));
    }

    @Test
    public void doActionRequest_ApiException_ErrorResponse() throws Exception {
        // 准备
        Map<String, String> params = new HashMap<>();
        params.put("requestId", "requestId");
        params.put("regionId", "regionId");
        ApiException expectedException = new ApiException(400, "Expected error message");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenThrow(expectedException);

        try {
            describeDBInstanceStorageCompression.doActionRequest(new CustInstanceDO(), params);
            Assert.fail();
        } catch (RdsException e) {
            assertEquals(new RdsException(ErrorCode.API_CALLING_FAILED).getMessage(), e.getMessage());
        }
    }



}
