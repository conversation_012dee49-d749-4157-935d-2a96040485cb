package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.service.KmsService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.base.support.MySQLParamConstants.LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.READ_ONLY_STATUS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

public class ModifyDBInstanceReadOnlyStatusImplTest {

    @InjectMocks
    private ModifyDBInstanceReadOnlyStatusImpl modifyDBInstanceReadOnlyStatus;

    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private WorkFlowService workFlowService;
    @Mock
    private KmsService kmsService;
    @Mock
    private DefaultApi defaultApi;

    private CustInstanceDO custInstanceDO;
    private Map<String, String> params;
    private String requestId = "test-request-id";
    private String dbInstanceName = "test-db-instance";
    private ReplicaSet replicaSet;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(123);
        custInstanceDO.setInsName(dbInstanceName);

        params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, requestId);
        params.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
        params.put("readOnlyStatus", PodDefaultConstants.readOnlyStatus.ON.name());

        replicaSet = new ReplicaSet();
        replicaSet.setName(dbInstanceName);
        replicaSet.setId(123L);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);

        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn(requestId);
        when(mysqlParamSupport.getDBInstanceName(params)).thenReturn(dbInstanceName);
        when(mysqlParamSupport.getAndCheckCustInstance(params)).thenReturn(custInstanceDO);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), anyString())).thenReturn(true);
        when(workFlowService.isTaskExist(anyString(), anyString())).thenReturn(false);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test
    public void testDoActionRequest_success() throws Exception {
        // 准备测试数据
        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(new HashMap<>());

        // 模拟工作流服务返回任务ID
        Object taskId = 12345;
        when(workFlowService.dispatchTask(
            anyString(), anyString(), anyString(),
            eq(PodDefaultConstants.TASK_MODIFY_INS_READ_ONLY_STATUS),
            anyString(), anyInt()))
            .thenReturn(taskId);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_statusUnchanged() throws Exception {
        // 准备测试数据 - 当前状态已经是ON
        Map<String, String> labels = new HashMap<>();
        labels.put(PodDefaultConstants.READ_ONLY_STATUS, "ON");
        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(labels);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_wrongStatus() throws Exception {
        // 准备测试数据 - 实例状态不是ACTIVATION
        replicaSet.setStatus(ReplicaSet.StatusEnum.CREATING);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
        assertEquals(null, result.get("Code"));
    }

    @Test
    public void testDoActionRequest_ins_locked() throws Exception {
        // 准备测试数据 - 实例被锁定
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.LOCKMANUAL);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_readOnlyStatusOn() throws Exception {
        // 准备测试数据 - 实例因临时表导致的磁盘满
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.DISKFULL);
        custInstanceDO.setLockReason(LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE);

        String uid = "test-uid";
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.UID)).thenReturn(uid);
        when(mysqlParamSupport.getParameterValue(params, "readOnlyStatus")).thenReturn("ON");
        // 确保KMS校验通过
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(eq(replicaSet), eq(uid))).thenReturn(true);

        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(new HashMap<>());

        // 模拟工作流服务返回任务ID
        Object taskId = 12345;
        when(workFlowService.dispatchTask(
            anyString(), anyString(), anyString(),
            eq(PodDefaultConstants.TASK_MODIFY_INS_READ_ONLY_STATUS),
            anyString(), anyInt()))
            .thenReturn(taskId);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
        assertEquals(taskId, result.get("TaskId"));
    }

    @Test
    public void testDoActionRequest_KMS_invalid() throws Exception {
        // 准备测试数据 - KMS密钥无效
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), anyString())).thenReturn(false);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_task_is_exist() throws Exception {
        // 准备测试数据 - 已存在任务
        when(workFlowService.isTaskExist(anyString(), anyString())).thenReturn(true);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_unsupport_ins_type() throws Exception {
        // 准备测试数据 - 只读实例
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.READONLY);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_invalid_param() throws Exception {
        // 准备测试数据 - 无效的只读状态
        params.put("readOnlyStatus", "INVALID_STATUS");

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest() throws Exception {
        // 准备测试数据 - 实例因临时表导致的磁盘满
        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(new HashMap<>());

        // 模拟工作流服务返回任务ID
        Object taskId = 12345;
        when(workFlowService.dispatchTask(
            anyString(), anyString(), anyString(),
            eq(PodDefaultConstants.TASK_MODIFY_INS_READ_ONLY_STATUS),
            anyString(), anyInt()))
            .thenReturn(taskId);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_readOnlyStatusOff() throws Exception {
        // 准备测试数据 - 实例因临时表导致的磁盘满
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.DISKFULL);
        custInstanceDO.setLockReason(LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE);

        String uid = "test-uid";
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.UID)).thenReturn(uid);
        when(mysqlParamSupport.getParameterValue(params, "readOnlyStatus")).thenReturn("OFF");
        // 确保KMS校验通过
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(eq(replicaSet), eq(uid))).thenReturn(true);
        Map<String, String> insLabels = new HashMap<>();
        insLabels.put(READ_ONLY_STATUS, "ON");
        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(insLabels);

        // 模拟工作流服务返回任务ID
        Object taskId = 12345;
        when(workFlowService.dispatchTask(
            anyString(), anyString(), anyString(),
            eq(PodDefaultConstants.TASK_MODIFY_INS_READ_ONLY_STATUS),
            anyString(), anyInt()))
            .thenReturn(taskId);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
        assertEquals(taskId, result.get("TaskId"));
    }

    @Test
    public void testDoActionRequest_unChange() throws Exception {
        // 准备测试数据 - 实例因临时表导致的磁盘满
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.DISKFULL);
        custInstanceDO.setLockReason(LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE);

        String uid = "test-uid";
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.UID)).thenReturn(uid);
        when(mysqlParamSupport.getParameterValue(params, "readOnlyStatus")).thenReturn("OFF");
        // 确保KMS校验通过
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(eq(replicaSet), eq(uid))).thenReturn(true);
        Map<String, String> insLabels = new HashMap<>();
        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(insLabels);

        // 模拟工作流服务返回任务ID
        Object taskId = 12345;
        when(workFlowService.dispatchTask(
            anyString(), anyString(), anyString(),
            eq(PodDefaultConstants.TASK_MODIFY_INS_READ_ONLY_STATUS),
            anyString(), anyInt()))
            .thenReturn(taskId);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_hasTask() throws Exception {
        // 准备测试数据 - 实例因临时表导致的磁盘满
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.DISKFULL);
        custInstanceDO.setLockReason(LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE);

        String uid = "test-uid";
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.UID)).thenReturn(uid);
        when(mysqlParamSupport.getParameterValue(params, "readOnlyStatus")).thenReturn("OFF");
        // 确保KMS校验通过
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(eq(replicaSet), eq(uid))).thenReturn(true);
        Map<String, String> insLabels = new HashMap<>();
        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(insLabels);

        // 模拟工作流服务返回任务ID
        when(workFlowService.isTaskExist(anyString(), anyString())).thenReturn(true);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_readOnlyInsTypes() throws Exception {
        // 准备测试数据 - 实例因临时表导致的磁盘满
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.DISKFULL);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.READONLY);
        custInstanceDO.setLockReason(LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE);

        String uid = "test-uid";
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.UID)).thenReturn(uid);
        when(mysqlParamSupport.getParameterValue(params, "readOnlyStatus")).thenReturn("OFF");
        // 确保KMS校验通过
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(eq(replicaSet), eq(uid))).thenReturn(true);
        Map<String, String> insLabels = new HashMap<>();
        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(insLabels);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_readOnlyStatusOn2() throws Exception {
        // 准备测试数据 - 实例因临时表导致的磁盘满
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.DISKFULL);
        custInstanceDO.setLockReason(LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE);

        String uid = "test-uid";
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.UID)).thenReturn(uid);
        when(mysqlParamSupport.getParameterValue(params, "readOnlyStatus")).thenReturn("ON");
        // 确保KMS校验通过
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(eq(replicaSet), eq(uid))).thenReturn(true);

        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(new HashMap<>());

        Map<String, String> insLabels = new HashMap<>();
        insLabels.put(READ_ONLY_STATUS, "ON");
        when(defaultApi.listReplicaSetLabels(anyString(), anyString())).thenReturn(insLabels);

        // 模拟工作流服务返回任务ID
        Object taskId = 12345;
        when(workFlowService.dispatchTask(
            anyString(), anyString(), anyString(),
            eq(PodDefaultConstants.TASK_MODIFY_INS_READ_ONLY_STATUS),
            anyString(), anyInt()))
            .thenReturn(taskId);

        // 执行测试
        Map<String, Object> result = modifyDBInstanceReadOnlyStatus.doActionRequest(custInstanceDO, params);

        // 验证结果
        assertNotNull(result);
    }

}