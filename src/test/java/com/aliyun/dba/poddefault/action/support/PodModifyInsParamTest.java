package com.aliyun.dba.poddefault.action.support;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.support.property.RdsException;
import com.google.common.collect.ImmutableList;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
public class PodModifyInsParamTest {
    @Mock
    private AliyunInstanceDependency dependency;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private PodCommonSupport podCommonSupport;
    @Mock
    private AligroupService aligroupService;
    @Mock
    private DBaasMetaService dbaasMetaService;
    @Mock
    private DefaultApi defaultApi;

    private Map<String, String> params;

    @Before
    public void setUp() throws Exception {
        params = new HashMap<String, String>();
        params.put("RequestId", "requestId");
        PowerMockito.when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PowerMockito.when(mysqlParamSupport.getParameterValue(anyMap(), anyString(), anyString())).thenReturn("paramValue");
        PowerMockito.when(dependency.getReplicaSetService()).thenReturn(replicaSetService);
        PowerMockito.when(replicaSetService.getAndCheckUserReplicaSet(anyMap())).thenReturn(new ReplicaSet(){{
            setId(1L);
            setCategory("standard");
            setName("rm-xxx");
        }});
        PowerMockito.when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        PowerMockito.when(podCommonSupport.getPrimaryReplicaSet(anyString(), any(ReplicaSet.class))).thenReturn(new Pair<String, ReplicaSet>() {
            @Override
            public String getLeft() {
                return "";
            }

            @Override
            public ReplicaSet getRight() {
                return new ReplicaSet(){{
                    setId(2L);
                    setCategory("standard");
                    setName("rm-yyy");
                }};
            }

            @Override
            public ReplicaSet setValue(ReplicaSet value) {
                return null;
            }
        });
        PowerMockito.when(dependency.getAligroupService()).thenReturn(aligroupService);
        PowerMockito.when(aligroupService.isXdbMultiWriteEngine(anyString(), any())).thenReturn(false);
        PowerMockito.when(dependency.getDBaasMetaService()).thenReturn(dbaasMetaService);
        PowerMockito.when(dbaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(new InstanceLevel(){{
            setCategory(CategoryEnum.STANDARD);
        }});
        PowerMockito.when(defaultApi.listReplicasInReplicaSet(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ReplicaListResult(){{
            setItems(ImmutableList.of(new Replica(){{
                setId(1L);
            }}));
        }});
        PowerMockito.when(defaultApi.getReplica(anyString(), anyLong(), any())).thenReturn(new ReplicaResource(){{}});
        PowerMockito.when(podCommonSupport.getReplicaRuntimeType(any())).thenReturn(PodType.POD_RUNC);
    }

    @Test
    public void testInitPodType() throws Exception {
        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        PowerMockito.spy(podModifyInsParam);
        PodModifyInsParam res = podModifyInsParam.initReplicaSetMeta().initDBType().initDBVersion().initClassCode().initTargetClassCode()
                .setIsReadIns()
                .initSrcInstanceLevel()
                .initTargetInstanceLevel().initPodType();
        assertNotNull(res);
    }
}