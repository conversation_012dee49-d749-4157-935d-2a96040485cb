package com.aliyun.dba.poddefault.action.support;

import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.poddefault.action.service.PoddefaultResourceGuaranteeModelService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;


import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PodTemplateHelperTest {

    @InjectMocks
    private PodTemplateHelper podTemplateHelper;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Mock
    private InsArchHelper insArchHelper;

    @Mock
    private PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;

    @Mock
    private RundPodSupport rundPodSupport;

    @Mock
    private PodCommonSupport podCommonSupport;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @After
    public void tearDown() {
        // Resetting mocks after each test case
        reset(dBaasMetaService, custinsParamService, podParameterHelper, insArchHelper, poddefaultResourceGuaranteeModelService, rundPodSupport, podCommonSupport);
    }

    @Test
    public void testGetBizSysScheduleTemplateName_AliGroup_ShouldReturnEmpty() {
        ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIGROUP;
        String dbEngine = "MYSQL";
        InstanceLevel instanceLevel = mock(InstanceLevel.class);
        String result = podTemplateHelper.getBizSysScheduleTemplateName("requestId", bizType, dbEngine, instanceLevel, false, "insTypeStr", "uid");

        assertEquals("", result);
    }

    @Test
    public void testGetBizSysScheduleTemplateName_SingleTenant_ShouldReturnEmpty() {
        ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIYUN;
        String dbEngine = "MYSQL";
        InstanceLevel instanceLevel = mock(InstanceLevel.class);
        String result = podTemplateHelper.getBizSysScheduleTemplateName("requestId", bizType, dbEngine, instanceLevel, true, "insTypeStr", "uid");

        assertEquals("", result);
    }

    @Test
    public void testGetBizSysScheduleTemplateName_XDB_ShouldReturnEmpty() {
        ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIYUN;
        String dbEngine = "XDB";
        InstanceLevel instanceLevel = mock(InstanceLevel.class);
        String result = podTemplateHelper.getBizSysScheduleTemplateName("requestId", bizType, dbEngine, instanceLevel, false, "insTypeStr", "uid");

        assertEquals("", result);
    }

    @Test
    public void testGetBizSysScheduleTemplateName_MySqlXResource_ShouldReturnCloudHaTemplate() {
        ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIYUN;
        String dbEngine = "MYSQL";
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        String result = podTemplateHelper.getBizSysScheduleTemplateName("requestId", bizType, dbEngine, instanceLevel, false, "insTypeStr", "uid");
        System.out.println(result);
        assertEquals(PodDefaultConstants.RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_HA, result);
    }

    @Test
    public void testGetBizSysScheduleTemplateName_ServerlessBasic_ShouldReturnServerlessTemplate() {
        ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIYUN;
        String dbEngine = "MYSQL";
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.SERVERLESS_BASIC);
        instanceLevel.setCpuCores(4);
        String insTypeStr = "main";
        String result = podTemplateHelper.getBizSysScheduleTemplateName("requestId", bizType, dbEngine, instanceLevel, false, insTypeStr, "uid");
        assertEquals(PodDefaultConstants.RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_SERVERLESS_V2, result);
    }

    @Test
    public void testGetBizSysScheduleTemplateName_ServerlessStandard_ShouldReturnServerlessTemplate() {
        ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIYUN;
        String dbEngine = "MYSQL";
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.SERVERLESS_STANDARD);
        instanceLevel.setCpuCores(4);
        String insTypeStr = "main";
        String result = podTemplateHelper.getBizSysScheduleTemplateName("requestId", bizType, dbEngine, instanceLevel, false, insTypeStr, "uid");
        assertEquals(PodDefaultConstants.RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_SERVERLESS_V2, result);
    }

    @Test
    public void testGetBizSysScheduleTemplateName_Vbm_ShouldReturnVbmTemplate() {
        ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIYUN;
        String dbEngine = "MYSQL";
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        instanceLevel.setCpuCores(4);
        String insTypeStr = "main";

        String result = podTemplateHelper.getBizSysScheduleTemplateName("requestId", bizType, dbEngine, instanceLevel, false, insTypeStr, "uid");

        assertEquals("", result);
    }

    @Test
    public void testGetBizSysScheduleTemplateName_Default_ShouldReturnEmpty() {
        ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIYUN;
        String dbEngine = "MYSQL";
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        instanceLevel.setCpuCores(4);
        String insTypeStr = "main";
        String result = podTemplateHelper.getBizSysScheduleTemplateName("requestId", bizType, dbEngine, instanceLevel, false, insTypeStr, "uid");

        assertEquals("", result);
    }
}

