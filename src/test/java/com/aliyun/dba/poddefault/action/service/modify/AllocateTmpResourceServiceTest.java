package com.aliyun.dba.poddefault.action.service.modify;

import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;

import static com.aliyun.dba.support.property.ParamConstants.DispenseMode.MultiAVZDispenseMode;
import static org.mockito.Matchers.any;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@PrepareForTest({LogFactory.class})
public class AllocateTmpResourceServiceTest {
    @InjectMocks
    AllocateTmpResourceService allocateTmpResourceService;
    @Mock
    private AliyunInstanceDependency dependency;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    private PodTemplateHelper podTemplateHelper;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private RundPodSupport rundPodSupport;
    @Mock
    private InstanceService instanceService;
    @Mock
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Test
    public void testMake() throws Exception {
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        String requestId = "requestId";
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicaList = new ArrayList<>();
        Replica replica = new Replica();
        replica.setRole(Replica.RoleEnum.MASTER);
        replica.setName("replica");
        replicaList.add(replica);
        replicaListResult.setItems(replicaList);
        PodModifyInsParam modifyInsParam = new PodModifyInsParam(dependency, params);
        String category = "basic";
        List<Replica.RoleEnum> roles = new ArrayList<>();
        roles.add(Replica.RoleEnum.MASTER);
        Map<Replica.RoleEnum, String> zoneIdMap = new HashMap<>();
        Map<Replica.RoleEnum, String> subDomainMap = new HashMap<>();
        ArrayList<ReplicaResourceRequest> replicas = new ArrayList<>();
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        modifyInsParam.setTargetInstanceLevel(instanceLevel);
        modifyInsParam.setAvzInfo(new AVZInfo(MultiAVZDispenseMode, "", "", "", new MultiAVZExParamDO()));
        modifyInsParam.setReplicaSetMeta(new ReplicaSet());
        modifyInsParam.setTmpReplicaSetName("tmp");
        modifyInsParam.setRoleHostNameMapping(new HashMap<>());
        modifyInsParam.setPodType(PodType.POD_ECS_RUND);
        when(dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(any(), any(), any(), any(), any(), any())).thenReturn(replicaListResult);
        when(replicaSetService.isServerless((InstanceLevel) any())).thenReturn(false);
        when(minorVersionServiceHelper.getServiceSpecTagByCustinsId(any(), any())).thenReturn("compose");
        when(replicaSetService.getReplicaSetVpcEndpoint(any(), any())).thenReturn(new Endpoint());
        //when(podParameterHelper.getExtendDiskSizeGBForPod(any(),any(),any())).thenReturn(100);
        try {
            AllocateTmpResourceResult make = allocateTmpResourceService.make(requestId, custins, modifyInsParam, category, roles, zoneIdMap, subDomainMap, replicas);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

    }
}
