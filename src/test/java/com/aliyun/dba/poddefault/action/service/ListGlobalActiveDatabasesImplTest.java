package com.aliyun.dba.poddefault.action.service;

import com.aliyun.dba.RdsapiExtMysqlApplication;
import com.aliyun.dba.support.common.action.IAction;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = RdsapiExtMysqlApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ListGlobalActiveDatabasesImplTest {

    @Resource(name = "poddefaultListGlobalActiveDatabasesImpl")
    private IAction iAction;


    @Ignore
    @Test
    public void listGADInstance() throws Exception{
        HashMap<String, String> params = new HashMap<String, String>();
        params.put("requestid", "1111111111111");
        params.put("user_id", "0");
        params.put("uid", "uelbert01");
        params.put("gadinstancename", "gadrm-rdstest1");
        params.put("engineversion", "5.7");
        params.put("engine", "MySQL");

        System.out.println(params.toString());

        Map<String,Object> re = iAction.doActionRequest(null,params);
        System.out.println(re.toString());
        System.out.println(re.get("members").toString());
    }}
