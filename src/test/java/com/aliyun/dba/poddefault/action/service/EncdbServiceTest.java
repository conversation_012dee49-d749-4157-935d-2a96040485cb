package com.aliyun.dba.poddefault.action.service;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.physical.action.service.DbossApiService;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class EncdbServiceTest {
    @InjectMocks
    EncdbService encdbService;
    @Mock
    DbossApiService dbossApiService;
    @Mock
    CustinsService custinsService;
    @Mock
    MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    ResourceService resourceService;


    private CustInstanceDO custins = null;
    @Before
    public void setUp() throws Exception {
        custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        custins.setStatus(CustinsSupport.CUSTINS_STATUS_ACTIVE);
        ResourceDO releaseDateResourceDO = new ResourceDO();
        releaseDateResourceDO.setRealValue("20241231");
        Mockito.when(resourceService.getResourceByResKey(eq("CLS_KMS_SUPPORT_MINOR_VERSION_RELEASE_DATE"))).thenReturn(releaseDateResourceDO);
    }

    @Test
    public void testSetEncdbGlobalAlgo() throws Exception {
        Mockito.when(custinsService.getCustInstanceCurrentMinorVersionByCustinsId(anyInt())).thenReturn("");
        Mockito.doNothing().when(dbossApiService).configEncdbParam(anyInt(), anyString(), anyString());
        encdbService.setEncdbGlobalAlgo(custins, "AES_256_GCM");
        Assertions.assertTrue(true);
    }

    @Test
    public void testSetEncdbGlobalAlgo_invalidPara() throws Exception {
        Exception exception = Assertions.assertThrows(RdsException.class, () -> {
            encdbService.setEncdbGlobalAlgo(custins, "AES_999");
        });
    }

    @Test
    public void testCheckCustinsSupportEncdbConfigAndKMS_Success() throws Exception {
        Mockito.when(custinsService.getCustInstanceCurrentMinorVersionByCustinsId(anyInt())).thenReturn("mysql57_20241231");
        Mockito.when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion("mysql57_20241231")).thenReturn("20241231");
        ResourceDO r = new ResourceDO();
        r.setRealValue("20240115");
        Mockito.when(resourceService.getResourceByResKey("ENCDB_57_MINOR_VERSION_RELEASE_DATE")).thenReturn(r);
        encdbService.checkCustinsSupportEncdbConfigAndKMS(custins);
        Assertions.assertTrue(true);
    }

    @Test
    public void testCheckCustinsSupportEncdbConfigAndKMS_Fail() throws Exception {
        Mockito.when(custinsService.getCustInstanceCurrentMinorVersionByCustinsId(anyInt())).thenReturn("mysql57_20241130");
        Mockito.when(minorVersionServiceHelper.parseReleaseDateFromMinorVersion("mysql57_20241130")).thenReturn("20241130");
        ResourceDO r = new ResourceDO();
        r.setRealValue("20240115");
        Mockito.when(resourceService.getResourceByResKey("ENCDB_57_MINOR_VERSION_RELEASE_DATE")).thenReturn(r);
        Exception exception = Assertions.assertThrows(RdsException.class, () -> {
            encdbService.checkCustinsSupportEncdbConfigAndKMS(custins);
        });

    }



    @Test
    public void testSetWhiteListMode() throws Exception {
        Mockito.when(custinsService.getCustInstanceCurrentMinorVersionByCustinsId(anyInt())).thenReturn("");
        Mockito.doNothing().when(dbossApiService).configEncdbParam(anyInt(), anyString(), anyString());
        encdbService.setWhiteListMode(custins, "true");
        Assertions.assertTrue(true);
    }

    @Test
    public void testSetWhiteListMode_invalidPara() throws Exception {
        Exception exception = Assertions.assertThrows(RdsException.class, () -> {
            encdbService.setWhiteListMode(custins, "1");
        });
    }

    @Test
    public void testGetEncdbParams() throws Exception {
        Mockito.when(custinsService.getCustInstanceCurrentMinorVersionByCustinsId(anyInt())).thenReturn("");
        Map<String, Object> res = new HashMap<>();
        res.put("encDBParams", new HashMap<>());
        ((Map) res.get("encDBParams")).put(EncdbService.PARAM_ALGO, "AES_256_GCM");
        Mockito.when(dbossApiService.showEncdbParams(anyInt())).thenReturn(res);
        Assertions.assertEquals("AES_256_GCM", encdbService.getEncdbParams(custins).get("globalAlgo"));
    }

    @Test
    public void testIsUIDAllowedWithCLSKmsMode_success_all() throws Exception {
        ResourceDO whiteListResourceDO = new ResourceDO();
        whiteListResourceDO.setRealValue("*");
        Mockito.when(resourceService.getResourceByResKey(eq("ALLOW_CLS_KMS_MODE"))).thenReturn(whiteListResourceDO);
        Assertions.assertTrue(encdbService.isUIDAllowedWithCLSKmsMode("12345"));
    }

    @Test
    public void testIsUIDAllowedWithCLSKmsMode_success_inWhiteList() throws Exception {
        ResourceDO whiteListResourceDO = new ResourceDO();
        whiteListResourceDO.setRealValue("[\"12345\"]");
        Mockito.when(resourceService.getResourceByResKey(eq("ALLOW_CLS_KMS_MODE"))).thenReturn(whiteListResourceDO);
        Assertions.assertTrue(encdbService.isUIDAllowedWithCLSKmsMode("12345"));
    }

    @Test
    public void testIsUIDAllowedWithCLSKmsMode_fail() throws Exception {
        ResourceDO whiteListResourceDO = new ResourceDO();
        whiteListResourceDO.setRealValue("[\"12345\"]");
        Mockito.when(resourceService.getResourceByResKey(eq("ALLOW_CLS_KMS_MODE"))).thenReturn(whiteListResourceDO);
        Assertions.assertFalse(encdbService.isUIDAllowedWithCLSKmsMode("22345"));
    }
}
