package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InlineResponse200;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DeleteOperatorPermissionImplTest {

    @InjectMocks
    private DeleteOperatorPermissionImpl deleteOperatorPermissionImpl;

    @Mock
    private MysqlParameterHelper parameterHelper;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    private DefaultApi defaultApi;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultApi= mock(DefaultApi.class);
        Mockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test
    public void doActionRequest_UnsupportedDbInstanceStatus_ReturnsErrorResponse() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("param1", "value1");
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.CREATING);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);

        // Act
        Map<String, Object> result = deleteOperatorPermissionImpl.doActionRequest(custInstanceDO, params);
        Object[] errorArray = (Object[]) result.get("errorCode");
        // Assert
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS.getCode(),  errorArray[0]);
    }

    @Test
    public void doActionRequest_UnsupportedDbInstanceLockMode_ReturnsErrorResponse() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("param1", "value1");
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.LOCKMANUAL);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);

        // Act
        Map<String, Object> result = deleteOperatorPermissionImpl.doActionRequest(custInstanceDO, params);
        Object[] errorArray = (Object[]) result.get("errorCode");
        // Assert
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE.getCode(), errorArray[0]);
    }

    @Test
    public void doActionRequest_OperatorPermissionNotFound_ReturnsErrorResponse() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("param1", "value1");
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.HOST_LOCK);
        replicaSet.setId(1L);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(parameterHelper.getParameterValue(anyString(), anyString())).thenReturn("13");
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(null);

        // Act
        Map<String, Object> result = deleteOperatorPermissionImpl.doActionRequest(custInstanceDO, params);
        Object[] errorArray = (Object[]) result.get("errorCode");
        // Assert
        assertEquals(ErrorCode.OPERATOR_PERMISSION_NOT_FOUNT.getCode(), errorArray[0]);
    }

    @Test
    public void doActionRequest_Success_ReturnsData() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("param1", "value1");
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(parameterHelper.getParameterValue(anyString(), anyString())).thenReturn("13");
        CustinsParamDO authParam = new CustinsParamDO();
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(authParam);
        InlineResponse200 response = new InlineResponse200();
        response.setRequestId("testRequestId");
        response.setMessage("Success");
        // Act
        Map<String, Object> result = deleteOperatorPermissionImpl.doActionRequest(custInstanceDO, params);

        // Assert
        assertNotNull(result);
        assertEquals(replicaSet.getId(), result.get(ParamConstants.DB_INSTANCE_ID));
        assertEquals(replicaSet.getName(), result.get(ParamConstants.DB_INSTANCE_NAME));
    }
}