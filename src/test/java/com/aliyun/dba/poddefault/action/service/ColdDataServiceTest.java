package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.activityprovider.model.ColdDataDisk;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class ColdDataServiceTest {

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private DbossApi dbossApi;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private PodCommonSupport podCommonSupport;

    @InjectMocks
    private ColdDataService coldDataService;
    @Mock
    private DbsGateWayService dbsGateWayService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("requestId");
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test
    public void describeColdDataFromDboss_ColdDataNotEnabled_ReturnsEmptyList() throws Exception {
        ReplicaSetResource replicaSetResource = mock(ReplicaSetResource.class);
        ReplicaSet replicaSet = mock(ReplicaSet.class);
        Map<String, String> labels = new HashMap<>();
        labels.put("coldDataEnabled", "false");
        when(defaultApi.getReplicaSetBundleResource(any(), eq("testReplicaSet"))).thenReturn(replicaSetResource);
        when(replicaSetResource.getReplicaSet()).thenReturn(replicaSet);
        when(replicaSet.getLabels()).thenReturn(labels);

        List<Map<String, Object>> result = coldDataService.describeColdDataFromDboss("testReplicaSet", 1, 10);

        assertEquals(0, result.size());
    }

    @Test
    public void describeColdDataFromDboss_ColdDataEnabledAndSupportsV2_ReturnsData() throws Exception {
        ReplicaSetResource replicaSetResource = mock(ReplicaSetResource.class);
        ReplicaSet replicaSet = mock(ReplicaSet.class);
        Map<String, String> labels = new HashMap<>();
        labels.put("coldDataEnabled", "true");
        labels.put("coldDataType", "testType");
        when(defaultApi.getReplicaSetBundleResource(any(), eq("testReplicaSet"))).thenReturn(replicaSetResource);
        when(replicaSetResource.getReplicaSet()).thenReturn(replicaSet);
        when(replicaSet.getLabels()).thenReturn(labels);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), eq("testReplicaSet"))).thenReturn("testServiceSpecTag");
        when(podCommonSupport.getReleaseDate(any(), eq("testServiceSpecTag"), any())).thenReturn("20241201");
        when(dbossApi.queryColdTablesWithVersion(anyLong(), any(), eq(0), eq(10), eq(2)))
                .thenReturn(new ArrayList<Map<String, Object>>());

        List<Map<String, Object>> result = coldDataService.describeColdDataFromDboss("testReplicaSet", 1, 10);

        assertEquals(0, result.size());
    }

    @Test
    public void describeColdDataFromDboss_ColdDataEnabledAndDoesNotSupportV2_ReturnsData() throws Exception {
        ReplicaSetResource replicaSetResource = mock(ReplicaSetResource.class);
        ReplicaSet replicaSet = mock(ReplicaSet.class);
        Map<String, String> labels = new HashMap<>();
        labels.put("coldDataEnabled", "true");
        labels.put("coldDataType", "testType");
        when(defaultApi.getReplicaSetBundleResource(any(), eq("testReplicaSet"))).thenReturn(replicaSetResource);
        when(replicaSetResource.getReplicaSet()).thenReturn(replicaSet);
        when(replicaSet.getLabels()).thenReturn(labels);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), eq("testReplicaSet"))).thenReturn("testServiceSpecTag");
        when(podCommonSupport.getReleaseDate(any(), eq("testServiceSpecTag"), any())).thenReturn("20241101");
        when(dbossApi.queryColdTables(anyLong(), any(), eq(0), eq(10)))
                .thenReturn(new ArrayList<Map<String, Object>>());

        List<Map<String, Object>> result = coldDataService.describeColdDataFromDboss("testReplicaSet", 1, 10);

        assertEquals(0, result.size());
    }

    @Test
    public void describeColdDataFromDboss_ExceptionDuringSupportCheck_ReturnsEmptyList() throws Exception {
        ReplicaSetResource replicaSetResource = mock(ReplicaSetResource.class);
        ReplicaSet replicaSet = mock(ReplicaSet.class);
        Map<String, String> labels = new HashMap<>();
        labels.put("coldDataEnabled", "true");
        labels.put("coldDataType", "testType");
        when(defaultApi.getReplicaSetBundleResource(any(), eq("testReplicaSet"))).thenReturn(replicaSetResource);
        when(replicaSetResource.getReplicaSet()).thenReturn(replicaSet);
        when(replicaSet.getLabels()).thenReturn(labels);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), eq("testReplicaSet"))).thenThrow(new RuntimeException("Test exception"));

        List<Map<String, Object>> result = coldDataService.describeColdDataFromDboss("testReplicaSet", 1, 10);

        assertEquals(0, result.size());
    }

    @Test
    public void getColdDataDiskFromSnapshotOrBackupSetId_RestoreColdDataFalse_ReturnsColdDataDisk() throws Exception {
        ColdDataDisk result = coldDataService.getColdDataDiskFromSnapshotOrBackupSetId(
                "requestId", true, "regionId", false, "replicaSetName", "backupSetId", "bid", "uid", false, null, null);
        assertEquals(true, result.getColdDataEnabled());
    }

    @Test
    public void getColdDataDiskFromSnapshotOrBackupSetId_BackupSetIdBlank_ReturnsColdDataDisk() throws Exception {
        ColdDataDisk result = coldDataService.getColdDataDiskFromSnapshotOrBackupSetId(
                "requestId", true, "regionId", true, "replicaSetName", "", "bid", "uid", false, null, null);
        assertEquals(true, result.getColdDataEnabled());
    }

    @Test
    public void getColdDataDiskFromSnapshotOrBackupSetId_RestoreColdDataFalse_ReturnsNull() throws Exception {
        ColdDataDisk result = coldDataService.getColdDataDiskFromSnapshotOrBackupSetId(
                "requestId", false, "regionId", false, "replicaSetName", "backupSetId", "bid", "uid", false, null, null);
        assertNull(result);
    }

    @Test
    public void getColdDataDiskFromSnapshotOrBackupSetId_BackupSetIdBlank_ReturnsNull() throws Exception {
        ColdDataDisk result = coldDataService.getColdDataDiskFromSnapshotOrBackupSetId(
                "requestId", false, "regionId", true, "replicaSetName", "", "bid", "uid", false, null, null);
        assertNull(result);
    }
    @Test
    public void getColdDataDiskFromSnapshotOrBackupSetId_ColdDataSnapshotIdAndJFSIdNotNull_ReturnsColdDataDisk() throws Exception {
        ColdDataDisk result = coldDataService.getColdDataDiskFromSnapshotOrBackupSetId(
                "requestId", true, "regionId", true, "replicaSetName", "backupSetId", "bid", "uid", false, "coldDataSnapshotId", "coldDataJFSId");
        assertEquals(true, result.getColdDataEnabled());
    }


    @Test
    public void getColdDataDiskFromSnapshotOrBackupSetId_BackupSetHasColdData_ReturnsColdDataDisk() throws Exception {
        DescribeRestoreBackupSetResponse mockResponse = Mockito.mock(DescribeRestoreBackupSetResponse.class);
        DescribeRestoreBackupSetResponse.BackupSetInfo mockBackupSetInfo = Mockito.mock(DescribeRestoreBackupSetResponse.BackupSetInfo.class);
        DescribeRestoreBackupSetResponse.ExtraInfo mockExtraInfo = Mockito.mock(DescribeRestoreBackupSetResponse.ExtraInfo.class);

        when(dbsGateWayService.describeRestoreBackupSet(any(DescribeRestoreBackupSetParam.class))).thenReturn(mockResponse);
        when(mockResponse.getBackupSetInfo()).thenReturn(mockBackupSetInfo);
        when(mockBackupSetInfo.getExtraInfo()).thenReturn(mockExtraInfo);
        when(mockExtraInfo.getColdDataSnapshotId()).thenReturn("coldDataSnapshotId");
        when(mockExtraInfo.getColdDataFsId()).thenReturn("coldDataFsId");

        ColdDataDisk result = coldDataService.getColdDataDiskFromSnapshotOrBackupSetId(
                "requestId", false, "regionId", true, "replicaSetName", "backupSetId", "bid", "uid", false, null, null);
        assertEquals(true, result.getColdDataEnabled());
    }
}
