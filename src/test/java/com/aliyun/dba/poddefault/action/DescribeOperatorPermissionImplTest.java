
package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DescribeOperatorPermissionImplTest {

    @InjectMocks
    private DescribeOperatorPermissionImpl describeOperatorPermissionImpl;

    @Mock
    private MysqlParameterHelper parameterHelper;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private CustinsParamService custinsParamService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void doActionRequest_ValidAccountPrivilege_OutterUserGrant() throws RdsException {
        // Arrange
        ActionParamsProvider.ACTION_PARAMS_MAP.set(Collections.singletonMap(ParamConstants.ACCOUNT_PRIVILEGE, String.valueOf(AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue())));
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(123);
        custInstanceDO.setInsName("test");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custInstanceDO);
        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_PRIVILEGE, AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()))
                .thenReturn(String.valueOf(AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()));
        CustinsParamDO custinsParamDO1 = new CustinsParamDO();
        custinsParamDO1.setName(CustinsParamSupport.CUSTINS_PARAM_NAME_SYSTEM_OPERATOR);
        custinsParamDO1.setValue(CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SYSTEM_OPERATION);

        CustinsParamDO custinsParamDO2 = new CustinsParamDO();
        custinsParamDO2.setName(PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER);
        custinsParamDO2.setValue(PodDefaultConstants.K8S_DATA_PERMISSION_IDENTIFIER_VALUE);
        CustinsParamDO custinsParamDO3 = new CustinsParamDO();
        custinsParamDO3.setName(CustinsParamSupport.CUSTINS_PARAM_NAME_USER_GRANT_INFO);
        String paramValue = "{\"created_time\":\"2023-01-01 00:00:00\",\"expired_time\":\"2023-12-31 23:59:59\"}";
        custinsParamDO3.setValue(paramValue);

        when(custinsParamService.getCustinsParams(anyInt(), any())).thenReturn(Arrays.asList(custinsParamDO1, custinsParamDO2,custinsParamDO3));

        // Act
        Map<String, Object> result = describeOperatorPermissionImpl.doActionRequest(custInstanceDO, Collections.singletonMap("param", "value"));

        // Assert
        assertEquals(123, result.get(ParamConstants.DB_INSTANCE_ID));
        assertEquals("test", result.get(ParamConstants.DB_INSTANCE_NAME));
        assertEquals("Control,Data", result.get(ParamConstants.OPERATOR_TYPE));
        assertNotNull(result.get(ParamConstants.EXPIRED_TIME));
        assertNotNull(result.get(ParamConstants.CREATED_TIME));
    }

}