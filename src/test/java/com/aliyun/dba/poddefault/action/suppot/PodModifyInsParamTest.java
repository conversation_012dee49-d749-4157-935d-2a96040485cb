package com.aliyun.dba.poddefault.action.suppot;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsParamSupport.ECS_ClOUD_AUTO;
import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES_INFO;
import static com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper.COMPRESSION_MODE;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PodModifyInsParamTest {
    @Mock
    private AliyunInstanceDependency dependency;

    private Map<String, String> params;

    private Map<String, Boolean> initParams;

    private CustInstanceDO custins;

    @Mock
    private PodCommonSupport podCommonSupport;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private DefaultApi defaultApi;
    @Mock
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;


    private String requestId;

    private String dbInstanceName;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Before
    public void setUp() throws RdsException {
        when(dependency.getReplicaSetService()).thenReturn(replicaSetService);
        when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(dependency.getCloudDiskCompressionHelper()).thenReturn(cloudDiskCompressionHelper);
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        //when(mysqlParamSupport.getAndCheckCustInstance(params)).thenReturn(custins);
        initParams = new HashMap<>();
        params = new HashMap<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setInsName("test-db");
        requestId = "requestId";
        dbInstanceName = "test123";

    }

    @Test
    public void testInitOptimizedWritesInfo_Success() throws Exception {
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put("dbVersion", "8.0");
        params.put("diskType", "cloud_essd");
        params.put("dbInstanceName", "test123");
        requestId = null;
        dbInstanceName = null;

        // Act
        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        initParams.put("dbVersion", true);
        initParams.put("diskType", true);
        initParams.put("dbInstanceName", true);
        podModifyInsParam.setInitParams(initParams);
        String primaryOptimizedWritesInfo = "{\"optimized_writes\":true,\"init_optimized_writes\":true}";
        when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        when(dependency.getPodCommonSupport().isOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        when(dependency.getPodCommonSupport().isInitOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        when(dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, dbInstanceName, OPTIMIZED_WRITES_INFO)).thenReturn(primaryOptimizedWritesInfo);
        PodModifyInsParam result = podModifyInsParam.initOptimizedWritesInfo();
        String expected = "true";
        String primaryOptimizedWritesString = result.getPrimaryOptimizedWritesString();
        String primaryInitOptimizedWritesString = result.getPrimaryInitOptimizedWritesString();
        Assert.assertEquals(expected, primaryOptimizedWritesString);
        Assert.assertEquals(expected, primaryInitOptimizedWritesString);
    }

    @Test
    public void testInitOptimizedWritesInfo_SuccessV2() throws Exception {
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put("dbVersion", "8.0");
        params.put("diskType", "cloud_essd");
        params.put("dbInstanceName", "test123");
        requestId = "requestId";
        dbInstanceName = "test123";

        // Act
        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        initParams.put("dbVersion", true);
        initParams.put("diskType", true);
        initParams.put("dbInstanceName", true);
        podModifyInsParam.setInitParams(initParams);
        //String primaryOptimizedWritesInfo = "{\"optimized_writes\":true,\"init_optimized_writes\":true}";
        when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        //when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        //when(dependency.getPodCommonSupport().isOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        //when(dependency.getPodCommonSupport().isInitOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        //when(dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, dbInstanceName, OPTIMIZED_WRITES_INFO)).thenReturn(primaryOptimizedWritesInfo);
        PodModifyInsParam result = podModifyInsParam.initOptimizedWritesInfo();
        String expected = "false";
        String targetInitOptimizedWritesString = result.getTargetInitOptimizedWritesString();
        Assert.assertEquals(expected, targetInitOptimizedWritesString);
    }




        private PodModifyInsParam initCompressionPodModifyInsParam(){
            Map<String, String> params;
            params = new HashMap<>();
            params.put("requestId", "requestId");
            params.put("dbInstanceName", "dbInstanceName");
            params.put("dbType", "dbType");
            params.put("uid", "uid");
            params.put("regionId", "regionId");
            params.put("targetInstanceLevel", "targetInstanceLevel");
            params.put("targetDiskType", "cloud_auto");
            params.put("srcPerformanceLevel", "srcPerformanceLevel");
            params.put("targetPerformanceLevel", "targetPerformanceLevel");
            params.put("storage", "100");
            PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
            initParams.put("dbVersion", true);
            initParams.put("diskType", true);
            initParams.put("dbInstanceName", true);
            initParams.put("replicaSetMeta", true);
            initParams.put("targetInstanceLevel", true);
            initParams.put("dbType", true);
            initParams.put("uid", true);
            initParams.put("regionId", true);
            initParams.put("classCode", true);
            initParams.put("targetClassCode", true);
            initParams.put("coldDataEnabled", true);
            initParams.put("targetDiskType", true);

            podModifyInsParam.setInitParams(initParams);
            podModifyInsParam.setRequestId("requestId");
            podModifyInsParam.setDbInstanceName("dbInstanceName");
            podModifyInsParam.setReplicaSetMeta(new ReplicaSet().diskSizeMB(1024));
            return podModifyInsParam;
        }


        @Test
        public void initSrcCompressionMode_CompressionModeOn_ShouldSetCompressionRatio() throws Exception {
            when(dependency.getCloudDiskCompressionHelper()).thenReturn(cloudDiskCompressionHelper);
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), anyString(), any())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
            when(cloudDiskCompressionHelper.getCompressionRatio(anyString(), anyString(), any(), any(), any())).thenReturn(2.0);
            PodModifyInsParam podModifyInsParam = initCompressionPodModifyInsParam();
            podModifyInsParam.initSrcCompressionMode();
            assertEquals(CloudDiskCompressionHelper.COMPRESSION_MODE_ON, podModifyInsParam.getSrcCompressionMode());
            assertEquals(2.0, podModifyInsParam.getCompressionRatio(), 0.01);
        }



        @Test
        public void initTargetCompressionMode_SameCompressionMode_ShouldNotSetChangeFlag() throws Exception {
            when(mysqlParamSupport.hasParameter(anyMap(), eq(ParamConstants.STORAGE))).thenReturn(true);
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(ParamConstants.STORAGE))).thenReturn("100");
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(COMPRESSION_MODE))).thenReturn("OFF");
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), eq(null))).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF);
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), anyString())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF);


            PodModifyInsParam podModifyInsParam = initCompressionPodModifyInsParam();
            podModifyInsParam.setReplicaSetMeta(new ReplicaSet().diskSizeMB(1024).insType(ReplicaSet.InsTypeEnum.MAIN));
            podModifyInsParam.initSrcCompressionMode();
            podModifyInsParam.initTargetCompressionMode();
            podModifyInsParam.initDiskSizeGB();
            podModifyInsParam.initTargetDiskSizeGB();
            podModifyInsParam.setClassCode("classCode");
            podModifyInsParam.setTargetClassCode("classCode");
            podModifyInsParam.setColdDataEnabled(true);
            podModifyInsParam.setTargetDiskType(ECS_ClOUD_AUTO);
            podModifyInsParam.setIsReadIns();

            assertEquals(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF, podModifyInsParam.getTargetCompressionMode());
            assertFalse(podModifyInsParam.isCompressionModeChange());
            assertEquals(1.0, podModifyInsParam.getCompressionRatio(), 0.01);
        }

        @Test
        public void initTargetCompressionMode_DifferentCompressionMode_ShouldSetChangeFlag() throws Exception {
            when(mysqlParamSupport.hasParameter(anyMap(), eq(ParamConstants.STORAGE))).thenReturn(true);
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(ParamConstants.STORAGE))).thenReturn("100");
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(COMPRESSION_MODE))).thenReturn("ON");
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), eq(null))).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF);
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), anyString())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);

            PodModifyInsParam podModifyInsParam = initCompressionPodModifyInsParam();
            podModifyInsParam.setReplicaSetMeta(new ReplicaSet().diskSizeMB(1024).insType(ReplicaSet.InsTypeEnum.MAIN));
            podModifyInsParam.initSrcCompressionMode();
            podModifyInsParam.initTargetCompressionMode();
            podModifyInsParam.initDiskSizeGB();
            podModifyInsParam.initTargetDiskSizeGB();
            podModifyInsParam.setClassCode("classCode");
            podModifyInsParam.setTargetClassCode("classCode");
            podModifyInsParam.setColdDataEnabled(true);
            podModifyInsParam.setTargetDiskType(ECS_ClOUD_AUTO);
            podModifyInsParam.setIsReadIns();

            assertEquals(CloudDiskCompressionHelper.COMPRESSION_MODE_ON, podModifyInsParam.getTargetCompressionMode());
            assertTrue(podModifyInsParam.isCompressionModeChange());
        }

        @Test
        public void checkCompressionLimit_CloseCompression_ShouldThrowException() throws Exception {
            when(mysqlParamSupport.hasParameter(anyMap(), eq(ParamConstants.STORAGE))).thenReturn(true);
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(ParamConstants.STORAGE))).thenReturn("100");
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(COMPRESSION_MODE))).thenReturn("OFF");
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), eq(null))).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), anyString())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF);
            when(cloudDiskCompressionHelper.getCompressionRatio(anyString(),eq("dbInstanceName"), any(), any(), any())).thenReturn(2.0);

            PodModifyInsParam podModifyInsParam = initCompressionPodModifyInsParam();
            podModifyInsParam.setReplicaSetMeta(new ReplicaSet().diskSizeMB(1024).insType(ReplicaSet.InsTypeEnum.MAIN));
            podModifyInsParam.initSrcCompressionMode();
            podModifyInsParam.initTargetCompressionMode();
            podModifyInsParam.initDiskSizeGB();
            podModifyInsParam.initTargetDiskSizeGB();
            podModifyInsParam.setClassCode("classCode");
            podModifyInsParam.setTargetClassCode("classCode");
            podModifyInsParam.setColdDataEnabled(true);
            podModifyInsParam.setTargetDiskType(ECS_ClOUD_AUTO);
            podModifyInsParam.setIsReadIns();

            assertThrows(RdsException.class, podModifyInsParam::checkCompressionLimit);
        }

        @Test
        public void checkCompressionLimit_OpenCompression_ShouldCheckSupport() throws Exception {
            when(mysqlParamSupport.hasParameter(anyMap(), eq(ParamConstants.STORAGE))).thenReturn(true);
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(ParamConstants.STORAGE))).thenReturn("100");
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(COMPRESSION_MODE))).thenReturn("ON");
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), eq(null))).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF);
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), anyString())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);

            PodModifyInsParam podModifyInsParam = initCompressionPodModifyInsParam();
            podModifyInsParam.setReplicaSetMeta(new ReplicaSet().diskSizeMB(1024).insType(ReplicaSet.InsTypeEnum.MAIN));
            podModifyInsParam.initSrcCompressionMode();
            podModifyInsParam.initTargetCompressionMode();
            podModifyInsParam.initDiskSizeGB();
            podModifyInsParam.initTargetDiskSizeGB();
            podModifyInsParam.setClassCode("classCode");
            podModifyInsParam.setTargetClassCode("classCode");
            podModifyInsParam.setColdDataEnabled(true);
            podModifyInsParam.setTargetDiskType(ECS_ClOUD_AUTO);
            podModifyInsParam.setIsReadIns();
            podModifyInsParam.checkCompressionLimit();

            verify(cloudDiskCompressionHelper, times(1)).checkCompressionSupportLimit(anyString(), any(), any(), any(), any(), any(), any(), anyBoolean());
            verify(cloudDiskCompressionHelper, times(1)).checkReadInsCompressionModeOn(anyString(), anyString(), anyBoolean());
        }

        @Test
        public void checkCompressionLimit_CompressionChange_ShouldCheckChangeLimit() throws Exception {

            when(mysqlParamSupport.hasParameter(anyMap(), eq(ParamConstants.STORAGE))).thenReturn(true);
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(ParamConstants.STORAGE))).thenReturn("100");
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(COMPRESSION_MODE))).thenReturn("ON");
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), eq(null))).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF);
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), anyString())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);


            PodModifyInsParam podModifyInsParam = initCompressionPodModifyInsParam();
            podModifyInsParam.initSrcCompressionMode();
            podModifyInsParam.initTargetCompressionMode();
            podModifyInsParam.initDiskSizeGB();
            podModifyInsParam.initTargetDiskSizeGB();
            podModifyInsParam.setClassCode("classCode");
            podModifyInsParam.setTargetClassCode("classCode");
            podModifyInsParam.setColdDataEnabled(true);
            podModifyInsParam.setTargetDiskType(ECS_ClOUD_AUTO);
            when(replicaSetService.getColdDataEnabled(anyString(), eq("dbInstanceName"),eq(null))).thenReturn(true);

            when(cloudDiskCompressionHelper.checkCompressionChangeLimit(anyBoolean(), anyMap())).thenReturn(new HashMap<String, Object>() {{
                put(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE, false);
                put(CloudDiskCompressionHelper.LIMIT_REASON, "limit reason");
            }});

            assertThrows(RdsException.class, podModifyInsParam::checkCompressionLimit);
        }

        @Test
        public void checkCompressionLimit_UpdateReplicaSetLabels() throws Exception {
            when(mysqlParamSupport.hasParameter(anyMap(), eq(ParamConstants.STORAGE))).thenReturn(true);
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(ParamConstants.STORAGE))).thenReturn("100");
            when(mysqlParamSupport.getParameterValue(anyMap(), eq(COMPRESSION_MODE))).thenReturn("ON");
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), eq(null))).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
            when(cloudDiskCompressionHelper.getCompressionMode(anyString(), eq("dbInstanceName"), anyString())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
            when(cloudDiskCompressionHelper.getCompressionRatio(anyString(),eq("dbInstanceName"), any(), any(), any())).thenReturn(2.0);
            PodModifyInsParam podModifyInsParam = initCompressionPodModifyInsParam();
            podModifyInsParam.initSrcCompressionMode();
            podModifyInsParam.initTargetCompressionMode();
            podModifyInsParam.initDiskSizeGB();
            podModifyInsParam.initTargetDiskSizeGB();
            podModifyInsParam.checkCompressionLimit();
            verify(dBaasMetaService.getDefaultClient(), times(1)).updateReplicaSetLabels(anyString(), anyString(), anyMap());
        }

    @Test
    public void initVbm_RuntimeTypePOD_RUND_CurrentAndTargetVBM_IsVbmTrue() throws Exception {
        params = new HashMap<>();
        params.put("requestId", "testRequestId");
        params.put("uid", "testUid");
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        podModifyInsParam.setRequestId("testRequestId");
        podModifyInsParam.setUid("testUid");
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("insName");
        podModifyInsParam.setReplicaSetMeta(replicaSet);
        InstanceLevel instanceLevel = new InstanceLevel();
        podModifyInsParam.setTargetInstanceLevel(instanceLevel);
        podModifyInsParam.setPodType(PodType.POD_ECS_RUND);
        initParams.put("replicaSetMeta", true);
        initParams.put("targetInstanceLevel", true);
        initParams.put("podType", true);
        podModifyInsParam.setInitParams(initParams);
        podModifyInsParam.initVbm();
        assertFalse(podModifyInsParam.isVbm());
    }
    @Test
    public void initVbm_RuntimeTypePOD_RUND_CurrentNotVBM_IsVbmFalse() throws Exception {
        params = new HashMap<>();
        params.put("requestId", "testRequestId");
        params.put("uid", "testUid");
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        podModifyInsParam.setRequestId("testRequestId");
        podModifyInsParam.setUid("testUid");
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("insName");
        podModifyInsParam.setReplicaSetMeta(replicaSet);
        InstanceLevel instanceLevel = new InstanceLevel();
        podModifyInsParam.setTargetInstanceLevel(instanceLevel);
        podModifyInsParam.setPodType(PodType.POD_ECS_RUND);
        initParams.put("replicaSetMeta", true);
        initParams.put("targetInstanceLevel", true);
        initParams.put("podType", true);
        podModifyInsParam.setInitParams(initParams);
        podModifyInsParam.initVbm();
        assertFalse(podModifyInsParam.isVbm());
    }
    @Test
    public void initVbm_RuntimeTypePOD_RUND_TargetNotVBMGray_IsVbmFalse() throws Exception {
        params = new HashMap<>();
        params.put("requestId", "testRequestId");
        params.put("uid", "testUid");
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        podModifyInsParam.setRequestId("testRequestId");
        podModifyInsParam.setUid("testUid");
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("insName");
        podModifyInsParam.setReplicaSetMeta(replicaSet);
        InstanceLevel instanceLevel = new InstanceLevel();
        podModifyInsParam.setTargetInstanceLevel(instanceLevel);
        podModifyInsParam.setPodType(PodType.POD_ECS_RUND);
        initParams.put("replicaSetMeta", true);
        initParams.put("targetInstanceLevel", true);
        initParams.put("podType", true);
        podModifyInsParam.setInitParams(initParams);
        podModifyInsParam.initVbm();
        assertFalse(podModifyInsParam.isVbm());
    }

    @Test
    public void test_isValidModifyRcu_exception() throws Exception {
        params = new HashMap<>();
        params.put("requestId", "testRequestId");
        params.put("uid", "testUid");
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        ServerlessSpec targetServerlessSpec=new ServerlessSpec();
        targetServerlessSpec.setScaleMax(128.0);
        podModifyInsParam.setTargetServerlessSpec(targetServerlessSpec);
        try {
            podModifyInsParam.isValidModifyRcu();
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }

    }

}
