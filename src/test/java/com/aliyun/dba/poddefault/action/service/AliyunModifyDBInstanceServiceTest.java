package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.modify.AliyunModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.ModifyDBInstanceDiskTypeService;
import com.aliyun.dba.poddefault.action.service.modify.ModifyDBInstanceOptimizedWritesService;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.service.migrate.MigrateDBInstanceAvzService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.poddefault.action.support.ResourceScheduleHelper;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.support.property.ParamConstants;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.MockitoAnnotations;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES;
import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES_INFO;
import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AliyunModifyDBInstanceServiceTest {

    @InjectMocks
    private AliyunModifyDBInstanceService aliyunModifyDBInstanceService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private MigrateDBInstanceAvzService migrateDBInstanceAvzService;

    @Mock
    private ModifyDBInstanceDiskTypeService modifyDBInstanceDiskTypeService;

    @Mock
    private ColdDataService coldDataService;

    @Mock
    private PodCommonSupport podCommonSupport;

    @Mock
    private ModifyDBInstanceOptimizedWritesService modifyDBInstanceOptimizedWritesService;

    @Mock
    private ResourceScheduleHelper resourceScheduleHelper;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private AliyunInstanceDependency dependency;

    private Map<String, String> params;

    private Map<String, Boolean> initParams;

    private String requestId;

    private String dbInstanceName;

    @Mock
    private PodModifyInsParam podModifyInsParam;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        initParams = new HashMap<>();
        initParams.put("classCode", true);
        initParams.put("targetClassCode", true);

        params = new HashMap<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        custInstanceDO.setInsName("test-db");
        requestId = "requestId";
        dbInstanceName = "test123";
    }

    @After
    public void tearDown() {
        // Reset mock objects after each test
        clearInvocations(mysqlParamSupport, migrateDBInstanceAvzService, modifyDBInstanceDiskTypeService,
                coldDataService, podCommonSupport, modifyDBInstanceOptimizedWritesService, resourceScheduleHelper, replicaSetService, podParameterHelper);
    }

    @Test
    public void doActionRequest_WithSingleTenantLocalModify_Success() throws Exception {
        // Arrange
        String primaryOptimizedWritesInfo = "{\"optimized_writes\":true,\"init_optimized_writes\":true}";
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("RequestId", "testRequestId");
        params.put(OPTIMIZED_WRITES, "optimized");

        PodModifyInsParam modifyInsParam = new PodModifyInsParam(dependency, params);
        modifyInsParam.setReplicaSetMeta(new ReplicaSet());
        modifyInsParam.setClassCode("mysql.n2.medium.2c");
        modifyInsParam.setTargetClassCode("mysql.n2.medium.2c");
        modifyInsParam.setInitParams(initParams);

        // Mock the methods
        when(mysqlParamSupport.getParameterValue(anyMap(), anyString(), anyString())).thenReturn("testRequestId");

        // Create local modify scenario
        modifyInsParam.setModifyMode(ModifyReplicaSetResourceRequest.ModifyModeEnum.MIGRATE);
        modifyInsParam.getReplicaSetMeta().setBizType(ReplicaSet.BizTypeEnum.ALIGROUP);
        modifyInsParam.getReplicaSetMeta().setStatus(ReplicaSet.StatusEnum.MODIFY_PARAM);
        modifyInsParam.setTargetInstanceLevel(new InstanceLevel());
        modifyInsParam.setDbInstanceName(dbInstanceName);
        modifyInsParam.setColdDataEnabled(false);


        AliyunModifyDBInstanceService aliyunModifyDBInstanceServiceSpy = spy(aliyunModifyDBInstanceService);
        doReturn(modifyInsParam).when(aliyunModifyDBInstanceServiceSpy).initPodModifyInsParam(params);

        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        ReplicaSetResource replicaSetResource = new ReplicaSetResource();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSetResource.setReplicaSet(replicaSet);
        when(dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName())).thenReturn(replicaSetResource);

        ReplicaListResult replicaListResult = new ReplicaListResult();
        when(dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                        null, null, null, null)).thenReturn(replicaListResult);

        when(mysqlParamSupport.hasParameter(params, ParamConstants.OPTIMIZED_WRITES)).thenReturn(true);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.OPTIMIZED_WRITES, null)).thenReturn("optimized");

        // Act
        Map<String, Object> result = aliyunModifyDBInstanceServiceSpy.doActionRequest(custins, params);

        verify(modifyDBInstanceOptimizedWritesService, times(1)).doActionRequest(null,
                true,
                params);
        assertEquals(new HashMap<String, Object>(), result);

    }
}
