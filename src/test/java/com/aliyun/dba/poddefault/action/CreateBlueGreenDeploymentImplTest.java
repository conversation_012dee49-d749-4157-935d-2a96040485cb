package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.service.BlueGreenDeploymentCommonService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/12
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({com.aliyun.dba.poddefault.action.CreateBlueGreenDeploymentImpl.class})
public class CreateBlueGreenDeploymentImplTest {
    
    @InjectMocks
    private CreateBlueGreenDeploymentImpl createBlueGreenDeployment;
    
    @Mock
    private BlueGreenDeploymentService blueGreenDeploymentService;
    
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    
    @Mock
    private BlueGreenDeploymentCommonService commonService;

    @Mock
    private ResourceService resourceService;
    
    @Before
    public void setUp() throws Exception {
        // 初始化mock对象
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));
        when(mysqlParamSupport.getParameterValue(anyMap(), eq("RequestId"))).thenReturn("testRequestId");
        when(mysqlParamSupport.getParameterValue(anyMap(), eq("minorVersion"))).thenReturn("mysql_20249815");

    }
    
    @Test
    public void testDoActionRequestSuccess() throws Exception {
        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("RequestId", "testRequestId");
        // minorVersion
        params.put("minorVersion", "mysql_20249815");
        
        // 模拟依赖行为
        Map<String, Object> newPrimaryConfig = new HashMap<>();
        List<Map<String, Object>> newRoConfig = new ArrayList<>();
        List<Map<String, Object>> newNodeConfig = new ArrayList<>();
        Map<String, Object> newProxyConfig = new HashMap<>();
        
        when(commonService.checkAndCorrectRegionId(anyString(), any(CustInstanceDO.class))).thenReturn("testRegionId");
        when(commonService.getNewPrimaryConfigFromParams(anyMap())).thenReturn(newPrimaryConfig);
        when(commonService.getNewRoConfigFromParams(anyMap())).thenReturn(newRoConfig);
        when(commonService.getNewReplicaConfigFromParams(anyMap())).thenReturn(newNodeConfig);
        when(commonService.getNewProxyConfigFromParams(anyMap())).thenReturn(newProxyConfig);
        
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        when(blueGreenDeploymentService.createBlueGreenDeployment(
            anyString(), anyString(), anyString(), 
            any(CustInstanceDO.class), anyMap(), 
            anyList(), anyList(), anyMap()
        )).thenReturn(data);
        
        // 调用方法
        ResourceDO newArchSwitch = new ResourceDO();
        newArchSwitch.setRealValue("true");
        when(resourceService.getResourceByResKey(any())).thenReturn(newArchSwitch);
        Map<String, Object> response = createBlueGreenDeployment.doActionRequest(custInstance, params);
        
        // 验证结果
        assertNotNull(response);
        verify(mysqlParamSupport, times(1)).getUID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckRegionID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckCustInstance(anyMap());
        verify(commonService, times(1)).checkAndCorrectRegionId(anyString(), any(CustInstanceDO.class));
        verify(commonService, times(1)).getNewPrimaryConfigFromParams(anyMap());
        verify(commonService, times(1)).getNewRoConfigFromParams(anyMap());
        verify(commonService, times(1)).getNewReplicaConfigFromParams(anyMap());
        verify(commonService, times(1)).getNewProxyConfigFromParams(anyMap());
        verify(blueGreenDeploymentService, times(1)).createBlueGreenDeployment(
            anyString(),
            anyString(),
            anyString(),
            any(CustInstanceDO.class),
            anyMap(),
            anyList(),
            anyList(),
            anyMap()
        );

        ResourceDO newArchSwitch1 = new ResourceDO();
        newArchSwitch1.setRealValue("false");
        when(resourceService.getResourceByResKey(any())).thenReturn(newArchSwitch1);
        Map<String, Object> response1 = createBlueGreenDeployment.doActionRequest(custInstance, params);
        assertNotNull(response1);
    }
}