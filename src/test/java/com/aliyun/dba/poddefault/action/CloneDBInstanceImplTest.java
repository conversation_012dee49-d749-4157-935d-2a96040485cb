package com.aliyun.dba.poddefault.action;

import java.util.*;

import com.aliyun.apsaradb.activityprovider.model.GeneralCloudDisk;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.service.ColdDataService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.PodAccountService;
import com.aliyun.dba.poddefault.action.service.PodIpWhiteListService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.VBM_CUSTINS_LABEL_KEY;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.VBM_CUSTINS_LABEL_VALUE;
import static com.aliyun.dba.support.property.ParamConstants.BACKUP_SET_ID;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LogFactory.class})
public class CloneDBInstanceImplTest {

    @InjectMocks
    private CloneDBInstanceImpl cloneDBInstanceImpl;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private PodCommonSupport podCommonSupport;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private BakService bakService;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private PodAvzSupport avzSupport;
    @Mock
    private KmsService kmsService;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private BackupService backupService;
    @Mock
    private RundPodSupport rundPodSupport;
    @Mock
    private PodTemplateHelper podTemplateHelper;
    @Mock
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Mock
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Mock
    private ColdDataService coldDataService;
    @Mock
    private CommonProviderService commonProviderService;
    @Mock
    private com.aliyun.apsaradb.activityprovider.api.DefaultApi commonDefaultApi;
    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    private PodIpWhiteListService podIpWhiteListService;
    @Mock
    private PodAccountService podAccountService;
    @Mock
    private PodDateTimeUtils podDateTimeUtils;
    @Mock
    private WorkFlowService workFlowService;
    @Mock
    private CustinsService custinsService;
    @Mock
    private CrossArchService crossArchService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(LogFactory.class);
    }

    @Test
    public void testSetReplicaSetLabels() throws ApiException {
        // Arrange
        String requestId = "testRequestId";
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSetName");
        ReplicaSet sourceReplicaSet = new ReplicaSet();
        sourceReplicaSet.setServiceVersion("5.7");
        Map<String, String> srcReplicaSetLabels = new HashMap<>();
        srcReplicaSetLabels.put("key1", "value1");
        String dbEngine = "MySQL";
        String rsTemplateName = "testRsTemplateName";
        String resourceGroupId = "testResourceGroupId";
        boolean isDataApplicationIns = true;
        boolean isArchChange = false;
        GeneralCloudDisk generalCloudDisk = mock(GeneralCloudDisk.class);
        boolean isArm = true;
        boolean isVbm = false;

        String config = "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT";
        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config configItem = new Config();
        configItem.setValue("true");
        items.add(configItem);
        configListResult.setItems(items);

        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(minorVersionServiceHelper.resetReplicaSetMinorVersion(anyString(), anyString())).thenReturn("testMinorVersion");
        when(podCommonSupport.isIoAccelerationEnabled(any(GeneralCloudDisk.class))).thenReturn(false);
        when(defaultApi.listConfigs(requestId, config)).thenReturn(configListResult);
        String optimizedWritesInfo = null;

        // Act
        cloneDBInstanceImpl.setReplicaSetLabels(requestId, replicaSet, sourceReplicaSet, srcReplicaSetLabels, dbEngine, rsTemplateName, resourceGroupId, isDataApplicationIns, isArchChange, generalCloudDisk, isArm, optimizedWritesInfo, null, isVbm);

        // Assert
        verify(dBaasMetaService.getDefaultClient(), times(1)).updateReplicaSetLabels(eq(requestId), eq(replicaSet.getName()), any(Map.class));
    }

    @Test
    public void testRund() throws Exception {
        // Arrange
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSetName");
        ReplicaSet sourceReplicaSet = new ReplicaSet();
        sourceReplicaSet.setServiceVersion("5.7");
        sourceReplicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        sourceReplicaSet.setCategory("basic");
        sourceReplicaSet.setId(123L);
        sourceReplicaSet.setName("sourceReplicaSetName");
        sourceReplicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        Map<String, String> srcReplicaSetLabels = new HashMap<>();
        srcReplicaSetLabels.put("key1", "value1");

        String config = "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT";
        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config configItem = new Config();
        configItem.setValue("true");
        items.add(configItem);
        configListResult.setItems(items);
        User user = new User();
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(minorVersionServiceHelper.resetReplicaSetMinorVersion(anyString(), anyString())).thenReturn("testMinorVersion");
        when(podCommonSupport.isIoAccelerationEnabled(any(GeneralCloudDisk.class))).thenReturn(false);
        when(defaultApi.listConfigs(requestId, config)).thenReturn(configListResult);
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.RESTORE_TYPE.toLowerCase(), RESTORE_TYPE_BAKID);
        params.put(BACKUP_SET_ID.toLowerCase(), "123");
        params.put("PreferredBackupTime".toLowerCase(), "00:00Z");
        params.put("preferredbackupperiod", "0000000");
        BakhistoryDO bakhistoryDO = new BakhistoryDO();
        bakhistoryDO.setKindCode(18);
        bakhistoryDO.setHisId(11L);
        bakhistoryDO.setBaksetSize(100L);
        bakhistoryDO.setDbVersion("5.7");
        CustInstanceDO srcCustins = new CustInstanceDO();
        srcCustins.setId(123);
        when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(null);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE)).thenReturn("100");
        when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(srcCustins);
        when(bakService.getBakhistoryByBackupSetId(any(), any())).thenReturn(bakhistoryDO);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CONN_TYPE, "lvs")).thenReturn("physical");
        when(dBaasMetaService.getDefaultClient().getUser(any(), any(), any())).thenReturn(user);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParameterHelper.getAndCreateUserId()).thenReturn(405400);
        when(mysqlParamSupport.getAndCheckSourceDBInstanceName(any())).thenReturn(sourceReplicaSet.getName());
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, sourceReplicaSet.getName(), true)).thenReturn(sourceReplicaSet);
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, null, true)).thenReturn(null);
        when(replicaSetService.isReplicaSetXDB(any(), any())).thenReturn(false);
        when(backupService.getBackupSet(any())).thenReturn(new GetBackupSetResponse());
        when(mysqlParamSupport.getDBInstanceName(any())).thenReturn("dbs-123");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok((ReplicaSet) any(), any())).thenReturn(true);
        AVZInfo avzInfo = new AVZInfo(null, "cn-beijing", null, null, null);
        when(avzSupport.getAVZInfo((Map<String, String>) any())).thenReturn(avzInfo);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_20240731");
        when(rundPodSupport.getPodTypeByGrayConfig(any(), any(), any(),any())).thenReturn(PodType.POD_ECS_RUND);
        when(rundPodSupport.isRundReplicaSet(any())).thenReturn(true);
        when(rundPodSupport.minorVersionSupportRund(any())).thenReturn(true);
        Map<String, Object> result = cloneDBInstanceImpl.doActionRequest(custins, params);
        Assert.assertNotNull(result);
    }


    @Test
    public void testCompression() throws Exception {
        // Arrange
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSetName");
        ReplicaSet sourceReplicaSet = new ReplicaSet();
        sourceReplicaSet.setServiceVersion("5.7");
        sourceReplicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        sourceReplicaSet.setCategory("basic");
        sourceReplicaSet.setId(123L);
        sourceReplicaSet.setName("sourceReplicaSetName");
        sourceReplicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        Map<String, String> srcReplicaSetLabels = new HashMap<>();
        srcReplicaSetLabels.put("key1", "value1");

        String config = "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT";
        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config configItem = new Config();
        configItem.setValue("true");
        items.add(configItem);
        configListResult.setItems(items);
        User user = new User();
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(minorVersionServiceHelper.resetReplicaSetMinorVersion(anyString(), anyString())).thenReturn("testMinorVersion");
        when(podCommonSupport.isIoAccelerationEnabled(any(GeneralCloudDisk.class))).thenReturn(false);
        when(defaultApi.listConfigs(requestId, config)).thenReturn(configListResult);
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.RESTORE_TYPE.toLowerCase(), RESTORE_TYPE_BAKID);
        params.put(BACKUP_SET_ID.toLowerCase(), "123");
        params.put("PreferredBackupTime".toLowerCase(), "00:00Z");
        params.put("preferredbackupperiod", "0000000");
        BakhistoryDO bakhistoryDO = new BakhistoryDO();
        bakhistoryDO.setKindCode(18);
        bakhistoryDO.setHisId(11L);
        bakhistoryDO.setBaksetSize(100L);
        bakhistoryDO.setDbVersion("5.7");
        CustInstanceDO srcCustins = new CustInstanceDO();
        srcCustins.setId(123);
        when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(null);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE)).thenReturn("100");
        when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(srcCustins);
        when(bakService.getBakhistoryByBackupSetId(any(), any())).thenReturn(bakhistoryDO);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CONN_TYPE, "lvs")).thenReturn("physical");
        when(dBaasMetaService.getDefaultClient().getUser(any(), any(), any())).thenReturn(user);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParameterHelper.getAndCreateUserId()).thenReturn(405400);
        when(mysqlParamSupport.getAndCheckSourceDBInstanceName(any())).thenReturn(sourceReplicaSet.getName());
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, sourceReplicaSet.getName(), true)).thenReturn(sourceReplicaSet);
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, null, true)).thenReturn(null);
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, "dbs-123", null)).thenReturn(new ReplicaSet().id(1L).name("dbs-123"));
        when(replicaSetService.isReplicaSetXDB(any(), any())).thenReturn(false);
        GetBackupSetResponse backupSetResponse = new GetBackupSetResponse();
        backupSetResponse.setSlaveStatusObj(new GetBackupSetResponse.SlaveStatus());
        when(backupService.getBackupSet(any())).thenReturn(backupSetResponse);
        when(mysqlParamSupport.getDBInstanceName(any())).thenReturn("dbs-123");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok((ReplicaSet) any(), any())).thenReturn(true);
        AVZInfo avzInfo = new AVZInfo(null, "cn-beijing", null, null, null);
        when(avzSupport.getAVZInfo((Map<String, String>) any())).thenReturn(avzInfo);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_20240731");
        when(rundPodSupport.getPodTypeByGrayConfig(any(), any(), any(),any())).thenReturn(PodType.POD_ECS_RUND);
        when(rundPodSupport.isRundReplicaSet(any())).thenReturn(true);
        when(rundPodSupport.minorVersionSupportRund(any())).thenReturn(true);
        when(cloudDiskCompressionHelper.getCompressionMode(any(),any(), any())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
        when(cloudDiskCompressionHelper.getCompressionRatio(any(),any(), any(),any(), any())).thenReturn(2.0);
        ScheduleTemplate scheduleTemplate = new ScheduleTemplate();
        scheduleTemplate.setStrategyName("testScheduleTemplate");
        org.apache.commons.lang3.tuple.Pair<String, ScheduleTemplate> testPair = new Pair<String, ScheduleTemplate>() {
            @Override
            public String getLeft() {
                return "";
            }
            @Override
            public ScheduleTemplate getRight() {
                return scheduleTemplate;
            }
            @Override
            public ScheduleTemplate setValue(ScheduleTemplate value) {
                return null;
            }
            public ScheduleTemplate getValue() {
                return scheduleTemplate;
            }
        };
        when(podTemplateHelper.getBizSysScheduleTemplate(eq(PodType.POD_ECS_RUND), eq(ReplicaSet.BizTypeEnum.ALIYUN), any(),
                any(), anyBoolean(), any(), any(), any(), any())).thenReturn(testPair);
        when(podReplicaSetResourceHelper.getReplicaResourceRequestList(any(), any())).thenReturn(new ArrayList<>());
        when(commonProviderService.getDefaultApi()).thenReturn(commonDefaultApi);
        when(workFlowService.dispatchTask(eq("custins"), any(), any(), eq("clone_ins"), any(), any())).thenReturn(1);
        Map<String, Object> result = cloneDBInstanceImpl.doActionRequest(custins, params);
        Assert.assertNotNull(result);
        Assert.assertEquals(null, result.get("TaskId"));
    }

    @Test
    public void initReplicaSetLabels_VBM_LabelAdded() throws Exception {
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("insName");
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setServiceVersion("1");
        ReplicaSet sourceReplicaSet = new ReplicaSet();
        sourceReplicaSet.setServiceVersion("1");
        Map<String, String> srcReplicaSetLabels = new HashMap<>();
        String dbEngine = "MySQL";
        String rsTemplateName = "rsTemplateName";
        String resourceGroupId = "resourceGroupId";
        boolean isDataApplicationIns = false;
        boolean isArchChange = false;
        GeneralCloudDisk generalCloudDisk = new GeneralCloudDisk();
        boolean isArm = false;
        String optimizedWritesInfo= "";
        Map<String, String> compressionLabels = new HashMap<>();
        boolean isVbm = true;
        String minorVersion = "20240830";
        when(minorVersionServiceHelper.resetReplicaSetMinorVersion(requestId, replicaSet.getName())).thenReturn(minorVersion);
        when(podCommonSupport.isIoAccelerationEnabled(generalCloudDisk)).thenReturn(false);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        cloneDBInstanceImpl.setReplicaSetLabels(requestId, replicaSet, sourceReplicaSet, srcReplicaSetLabels,
                dbEngine, rsTemplateName, resourceGroupId, isDataApplicationIns, isArchChange, generalCloudDisk,
                isArm, optimizedWritesInfo, compressionLabels, isVbm);
        Map<String, String> expectedLabels = new HashMap<>();
        expectedLabels.put(CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, resourceGroupId);
        expectedLabels.put("minor_version", minorVersion);
        expectedLabels.put(VBM_CUSTINS_LABEL_KEY, VBM_CUSTINS_LABEL_VALUE);
        expectedLabels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
        expectedLabels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME, rsTemplateName);
        expectedLabels.put(ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto");
        verify(defaultApi, times(1)).updateReplicaSetLabels(requestId, replicaSet.getName(), expectedLabels);
    }

    @Test
    public void test_restore_by_time_old_arch() throws Exception {
        // Arrange
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSetName");
        ReplicaSet sourceReplicaSet = new ReplicaSet();
        sourceReplicaSet.setServiceVersion("5.7");
        sourceReplicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        sourceReplicaSet.setCategory("basic");
        sourceReplicaSet.setId(123L);
        sourceReplicaSet.setName("sourceReplicaSetName");
        sourceReplicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        Map<String, String> srcReplicaSetLabels = new HashMap<>();
        srcReplicaSetLabels.put("key1", "value1");

        String config = "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT";
        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config configItem = new Config();
        configItem.setValue("true");
        items.add(configItem);
        configListResult.setItems(items);
        User user = new User();
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(minorVersionServiceHelper.resetReplicaSetMinorVersion(anyString(), anyString())).thenReturn("testMinorVersion");
        when(podCommonSupport.isIoAccelerationEnabled(any(GeneralCloudDisk.class))).thenReturn(false);
        when(defaultApi.listConfigs(requestId, config)).thenReturn(configListResult);
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.RESTORE_TYPE.toLowerCase(), RESTORE_TYPE_TIME);
        params.put(BACKUP_SET_ID.toLowerCase(), "123");
        params.put("PreferredBackupTime".toLowerCase(), "00:00Z");
        params.put("preferredbackupperiod", "0000000");
        BakhistoryDO bakhistoryDO = new BakhistoryDO();
        bakhistoryDO.setKindCode(18);
        bakhistoryDO.setHisId(11L);
        bakhistoryDO.setBaksetSize(100L);
        bakhistoryDO.setDbVersion("5.7");
        CustInstanceDO srcCustins = new CustInstanceDO();
        srcCustins.setId(123);
        when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(null);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE)).thenReturn("100");
        when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(srcCustins);
        when(bakService.getBakhistoryByBackupSetId(any(), any())).thenReturn(bakhistoryDO);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CONN_TYPE, "lvs")).thenReturn("physical");
        when(dBaasMetaService.getDefaultClient().getUser(any(), any(), any())).thenReturn(user);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParameterHelper.getAndCreateUserId()).thenReturn(405400);
        when(mysqlParamSupport.getAndCheckSourceDBInstanceName(any())).thenReturn(sourceReplicaSet.getName());
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, sourceReplicaSet.getName(), true)).thenReturn(sourceReplicaSet);
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, null, true)).thenReturn(null);
        when(replicaSetService.isReplicaSetXDB(any(), any())).thenReturn(false);
        when(backupService.getBackupSet(any())).thenReturn(new GetBackupSetResponse());
        when(mysqlParamSupport.getDBInstanceName(any())).thenReturn("dbs-123");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok((ReplicaSet) any(), any())).thenReturn(true);
        AVZInfo avzInfo = new AVZInfo(null, "cn-beijing", null, null, null);
        when(avzSupport.getAVZInfo((Map<String, String>) any())).thenReturn(avzInfo);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_20240731");
        when(rundPodSupport.isRundReplicaSet(any())).thenReturn(true);
        when(rundPodSupport.minorVersionSupportRund(any())).thenReturn(true);
        Map<String, Object> result = cloneDBInstanceImpl.doActionRequest(custins, params);
        Assert.assertNotNull(result);
    }

    @Test
    public void test_restore_by_time_new_arch() throws Exception {
        // Arrange
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSetName");
        ReplicaSet sourceReplicaSet = new ReplicaSet();
        sourceReplicaSet.setServiceVersion("5.7");
        sourceReplicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        sourceReplicaSet.setCategory("basic");
        sourceReplicaSet.setKindCode(18);
        sourceReplicaSet.setId(123L);
        sourceReplicaSet.setName("sourceReplicaSetName");
        sourceReplicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        Map<String, String> srcReplicaSetLabels = new HashMap<>();
        srcReplicaSetLabels.put("key1", "value1");

        String config = "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT";
        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config configItem = new Config();
        configItem.setValue("true");
        items.add(configItem);
        configListResult.setItems(items);
        User user = new User();
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(minorVersionServiceHelper.resetReplicaSetMinorVersion(anyString(), anyString())).thenReturn("testMinorVersion");
        when(podCommonSupport.isIoAccelerationEnabled(any(GeneralCloudDisk.class))).thenReturn(false);
        when(defaultApi.listConfigs(requestId, config)).thenReturn(configListResult);
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.RESTORE_TYPE.toLowerCase(), RESTORE_TYPE_TIME);
        params.put(BACKUP_SET_ID.toLowerCase(), "123");
        params.put("PreferredBackupTime".toLowerCase(), "00:00Z");
        params.put("preferredbackupperiod", "0000000");
        BakhistoryDO bakhistoryDO = new BakhistoryDO();
        bakhistoryDO.setKindCode(18);
        bakhistoryDO.setHisId(11L);
        bakhistoryDO.setBaksetSize(100L);
        bakhistoryDO.setDbVersion("5.7");
        CustInstanceDO srcCustins = new CustInstanceDO();
        srcCustins.setId(123);
        srcCustins.setKindCode(18);
        when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(null);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE)).thenReturn("100");
        when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(srcCustins);
        when(bakService.getBakhistoryByBackupSetId(any(), any())).thenReturn(bakhistoryDO);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CONN_TYPE, "lvs")).thenReturn("physical");
        when(dBaasMetaService.getDefaultClient().getUser(any(), any(), any())).thenReturn(user);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParameterHelper.getAndCreateUserId()).thenReturn(405400);
        when(mysqlParamSupport.getAndCheckSourceDBInstanceName(any())).thenReturn(sourceReplicaSet.getName());
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, sourceReplicaSet.getName(), true)).thenReturn(sourceReplicaSet);
        when(dBaasMetaService.getDefaultClient().getReplicaSet(requestId, null, true)).thenReturn(null);
        when(replicaSetService.isReplicaSetXDB(any(), any())).thenReturn(false);
        when(backupService.getBackupSet(any())).thenReturn(new GetBackupSetResponse());
        when(mysqlParamSupport.getDBInstanceName(any())).thenReturn("dbs-123");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok((ReplicaSet) any(), any())).thenReturn(true);
        AVZInfo avzInfo = new AVZInfo(null, "cn-beijing", null, null, null);
        when(avzSupport.getAVZInfo((Map<String, String>) any())).thenReturn(avzInfo);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_20240731");
        when(rundPodSupport.isRundReplicaSet(any())).thenReturn(true);
        when(rundPodSupport.minorVersionSupportRund(any())).thenReturn(true);
        Map<String, Object> result = cloneDBInstanceImpl.doActionRequest(custins, params);
        Assert.assertNotNull(result);
    }
}
