package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.dba.RdsapiExtMysqlApplication;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.action.IAction;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = RdsapiExtMysqlApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CreateGlobalActiveDatabaseImplTest {

    @Resource(name = "poddefaultCreateGlobalActiveDatabaseImpl")
    private IAction iAction;


    @Ignore
    @Test
    public void createGADInstance() throws Exception{
        HashMap<String, String> params = new HashMap<String, String>();
        params.put("requestid", "*************");
        params.put("user_id", "0");
        params.put("uid", "uelbert01");
//        params.put("gadinstancename", "gad-rm-t4n7d23x971hbjx4v");
        params.put("engineversion", "5.7");
        params.put("engine", "MySQL");
        params.put("regionid","ap-southeast-1");
        params.put("centraldbinstancename","rm-t4n7d23x971hbjx4v");
        params.put("centralrdsdtsadminaccount","ldy");
        params.put("centralrdsdtsadminpassword","ldy123#@!");
        params.put("description","ldy-test");

        String aa = "[{\"dBInstanceClass\":\"rds.mysql.t1.small\",\"dBInstanceNetType\":\"Intranet\",\"dBInstanceStorage\":\"50\",\"dtsInstanceClass\":\"small\",\"instanceNetworkType\":\"VPC\",\"regionId\":\"ap-southeast-1\",\"securityIPList\":\"127.0.0.1\",\"vSwitchId\":\"vsw-t4nnhn6s57xhqx8pbnrcw\",\"vpcId\":\"vpc-t4nhi4wf05rhe1utm66yw\",\"zoneId\":\"ap-southeast-1c\"},{\"dBInstanceClass\":\"rds.mysql.t1.small\",\"dBInstanceNetType\":\"Intranet\",\"dBInstanceStorage\":\"50\",\"dtsInstanceClass\":\"small\",\"instanceNetworkType\":\"VPC\",\"regionId\":\"ap-southeast-1\",\"securityIPList\":\"127.0.0.1\",\"vSwitchId\":\"vsw-t4nnhn6s57xhqx8pbnrcw\",\"vpcId\":\"vpc-t4nhi4wf05rhe1utm66yw\",\"zoneId\":\"ap-southeast-1c\"}]";
        params.put("unitmemberparams",aa);
        List<Map<String, String>> unitMemberParams = JSON.parseObject(aa,new TypeReference<List<Map<String,String>>>(){});

        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String,Object> re = iAction.doActionRequest(null,params);
        System.out.println(re.toString());
        System.out.println(re.get("gadInstanceName").toString());
    }}
