package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.InlineResponse200;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetResource;
import com.aliyun.dba.adb_vip_manager_client.api.LinksApi;
import com.aliyun.dba.adb_vip_manager_client.model.APISuccess;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ModifyConnectionDrainingService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.poddefault.action.CreateOperatorPermissionImpl;
import com.aliyun.dba.poddefault.action.ModifyDBInstanceConnectionDrainingImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateUTCFormat;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.base.support.MySQLParamConstants.ALB_CONNECTION_DRAINING_SWITCH;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ModifyDBInstanceConnectionDrainingTest {

    @Mock
    private MysqlParameterHelper parameterHelper;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    protected MysqlParamSupport paramSupport;
    @Mock
    protected CustinsService custinsService;
    @Mock
    protected DBaasMetaService metaService;
    @Mock
    protected WorkFlowService workFlowService;

    @Mock
    protected LinksApi linksApi;

    @Mock
    protected ModifyConnectionDrainingService modifyConnectionDrainingService;

    @InjectMocks
    private ModifyDBInstanceConnectionDrainingImpl modifyDBInstanceConnectionDraining;

    private DefaultApi defaultApi;

    @Before
    public void setUp() throws ApiException {
        defaultApi = mock(DefaultApi.class);
//        Mockito.when(metaService.getDefaultClient()).thenReturn(defaultApi);

    }

    @Test
    public void testDoActionRequest() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("DrainingTimeout", "10");
        params.put("DrainingEnable","1");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
//        replicaSet.setLockMode(ReplicaSet.LockModeEnum.LOCKMANUAL);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setCategory("standard");
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);

        Map<String, String> labels = new HashMap<>();
        labels.put("ALB_CONNECTION_DRAINING_SWITCH", "OFF");
        labels.put("Test111", "ON");
        replicaSet.setLabels(labels);

        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);


        Map<String, Object> data = new HashMap<>();
        data.put("DBInstanceID", replicaSet.getId());
        data.put("DBInstanceName", replicaSet.getName());
        data.put("MaxscaleName", "maxscaleInstanceName");
        when(modifyConnectionDrainingService.modifyConnectionDraining(any(), any())).thenReturn(data);

        Map<String, Object> result = modifyDBInstanceConnectionDraining.doActionRequest(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));

        assertEquals(1L, result.get(ParamConstants.DB_INSTANCE_ID));
        assertEquals("testReplicaSetName", result.get(ParamConstants.DB_INSTANCE_NAME));
    }

    @Test
    public void testDoLinkFailedTest() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("DrainingTimeout", "10");
        params.put("DrainingEnable","1");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
//        replicaSet.setLockMode(ReplicaSet.LockModeEnum.LOCKMANUAL);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setCategory("standard");
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);

        Map<String, String> labels = new HashMap<>();
        labels.put("ALB_CONNECTION_DRAINING_SWITCH", "OFF");
        labels.put("Test111", "ON");
        replicaSet.setLabels(labels);

        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);


        APISuccess apiSuccess = new APISuccess();
        apiSuccess.setStatus(200);
        when(modifyConnectionDrainingService.modifyConnectionDraining(any(),any())).thenThrow(new RdsException(ErrorCode.INVALID_PARAM));
        Map<String, Object> result = modifyDBInstanceConnectionDraining.doActionRequest(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.INTERNAL_FAILURE.getCode(), errorArray[0]);
    }


    @Test
    public void testReplicaSETISLock() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("DrainingTimeout", "10");
        params.put("DrainingEnable","1");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setCategory("standard");
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.LOCKMANUAL);

        Map<String, String> labels = new HashMap<>();
        labels.put("ALB_CONNECTION_DRAINING_SWITCH", "OFF");
        labels.put("Test111", "ON");
        replicaSet.setLabels(labels);

        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);

//        when(modifyConnectionDrainingService.modifyConnectionDraining(any(),any())).thenThrow(new RdsException(ErrorCode.INVALID_PARAM));
        Map<String, Object> result = modifyDBInstanceConnectionDraining.doActionRequest(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE.getCode(), errorArray[0]);
    }

    @Test
    public void testReplicaSETIsDNS() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("DrainingTimeout", "10");
        params.put("DrainingEnable","1");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.DNS);
        replicaSet.setCategory("standard");
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);

        Map<String, String> labels = new HashMap<>();
        labels.put("ALB_CONNECTION_DRAINING_SWITCH", "OFF");
        labels.put("Test111", "ON");
        replicaSet.setLabels(labels);

        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);

//        when(modifyConnectionDrainingService.modifyConnectionDraining(any(),any())).thenThrow(new RdsException(ErrorCode.INVALID_PARAM));
        Map<String, Object> result = modifyDBInstanceConnectionDraining.doActionRequest(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE.getCode(), errorArray[0]);
    }


    @Test
    public void testReplicaSETIsSTOP() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("DrainingTimeout", "10");
        params.put("DrainingEnable","1");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.STOPPED);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setCategory("standard");
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);

        Map<String, String> labels = new HashMap<>();
        labels.put("ALB_CONNECTION_DRAINING_SWITCH", "OFF");
        labels.put("Test111", "ON");
        replicaSet.setLabels(labels);

        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);

//        when(modifyConnectionDrainingService.modifyConnectionDraining(any(),any())).thenThrow(new RdsException(ErrorCode.INVALID_PARAM));
        Map<String, Object> result = modifyDBInstanceConnectionDraining.doActionRequest(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE.getCode(), errorArray[0]);
    }
}
