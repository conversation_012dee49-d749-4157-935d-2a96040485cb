package com.aliyun.dba.poddefault.action;

import java.util.*;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceServiceImpl;
import com.aliyun.dba.poddefault.action.service.EncdbService;
import com.aliyun.dba.poddefault.action.service.TdeKmsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;
@RunWith(PowerMockRunner.class)
@PrepareForTest({ActionParamsProvider.class, RequestSession.class})
public class ModifyDBInstanceCLSImplTest {
    @InjectMocks
    private ModifyDBInstanceCLSImpl modifyDBInstanceCLS;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private InstanceServiceImpl instanceService;
    @Mock
    private CustinsServiceImpl custinsService;
    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    private EncdbService encdbService;
    @Mock
    private TdeKmsService tdeKmsService;
    @Mock
    private WorkFlowService workflowService;
    @Mock
    private UserServiceImpl userService;
    @Mock
    private KmsApi kmsApi;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(ActionParamsProvider.class);
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("regionID");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setCategory("standard");
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        when(tdeKmsService.checkTdeSupported("testRequestId", "regionID")).thenReturn(true);
    }

    @Test
    public void testDoActionRequest_Success_setKmsKey() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("rolearn");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE)).thenReturn(PodDefaultConstants.CLS_MODE_KMS_KEY);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO)).thenReturn("AES_256_GCM");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_WHITELIST_MODE)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_KEY)).thenReturn("kmsKeyId");
        when(kmsApi.checkAssumeRoleOk(any(), any(), any())).thenReturn(true);
        when(encdbService.isUIDAllowedWithCLSKmsMode(any())).thenReturn(true);

        when(workflowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        CustInstanceDO coCustInstanceDo = new CustInstanceDO();
        coCustInstanceDo.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        coCustInstanceDo.setId(2);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(any(), anyBoolean())).thenReturn(Collections.singletonList(coCustInstanceDo));
        // Act
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_error_invalidCLSKeyMode() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE)).thenReturn("XXX");
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(null);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertNotNull(result);
        assertEquals(ErrorCode.INVALID_PARAM.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_error_emptykmsKeyId() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE)).thenReturn("kms_key");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_KEY)).thenReturn(null);
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(null);
        when(encdbService.isUIDAllowedWithCLSKmsMode(any())).thenReturn(true);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertNotNull(result);
        assertEquals(ErrorCode.INVALID_PARAM.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_error_assumeRoleFail() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE)).thenReturn("kms_key");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_KEY)).thenReturn("key-xxxxx");
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(null);
        when(kmsApi.checkAssumeRoleOk(any(), any(), any())).thenReturn(false);
        when(encdbService.isUIDAllowedWithCLSKmsMode(any())).thenReturn(true);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertNotNull(result);
        assertEquals(ErrorCode.TDEPARAM_ERROR_ROLE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_error_rotationNotSupported() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("rolearn");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE)).thenReturn(PodDefaultConstants.CLS_MODE_KMS_KEY);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO)).thenReturn("AES_256_GCM");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_WHITELIST_MODE)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_KEY)).thenReturn("kmsKeyId");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_IS_ROTATE, "0")).thenReturn("1");
        when(kmsApi.checkAssumeRoleOk(any(), any(), any())).thenReturn(true);

        when(workflowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        CustInstanceDO coCustInstanceDo = new CustInstanceDO();
        coCustInstanceDo.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        coCustInstanceDo.setId(2);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(any(), anyBoolean())).thenReturn(Collections.singletonList(coCustInstanceDo));

        CustinsParamDO param = new CustinsParamDO(1, PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_KMS_KEY);
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(param);
        when(encdbService.isUIDAllowedWithCLSKmsMode(any())).thenReturn(true);
        // Act
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        // Assert
        assertNotNull(result);
        assertEquals(ErrorCode.UNSUPPORTED_OPTION_VALUE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }


    @Test
    public void testDoActionRequest_error_readOnlyIns() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_READ.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(null);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertNotNull(result);
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    // shutdown cls.
    @Test
    public void testDoActionRequest_Success_shutDownCLS() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("0");
        CustinsParamDO param = new CustinsParamDO(1, PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_KMS_KEY);
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(param);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertNotNull(result);
    }

    // shutdown cls.
    @Test
    public void testDoActionRequest_Error_shutDownCLSButCLSNotEnabled() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("0");
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(null);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertNotNull(result);
        assertEquals(ErrorCode.INVALID_STATUS.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_Success_noSetKmsKey() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn(null);
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE)).thenReturn(PodDefaultConstants.CLS_MODE_CLIENT_KEY);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO)).thenReturn("AES_256_GCM");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_WHITELIST_MODE)).thenReturn("1");
        when(kmsApi.checkAssumeRoleOk(any(), any(), any())).thenReturn(true);

        when(workflowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        CustInstanceDO coCustInstanceDo = new CustInstanceDO();
        coCustInstanceDo.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        coCustInstanceDo.setId(2);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(any(), anyBoolean())).thenReturn(Collections.singletonList(coCustInstanceDo));
        // Act
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_duplicateSetKmsKeyMode() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("rolearn");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE)).thenReturn(PodDefaultConstants.CLS_MODE_KMS_KEY);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO)).thenReturn("AES_256_GCM");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_WHITELIST_MODE)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_KEY)).thenReturn("kmsKeyId");
        when(kmsApi.checkAssumeRoleOk(any(), any(), any())).thenReturn(true);
        when(encdbService.isUIDAllowedWithCLSKmsMode(any())).thenReturn(true);

        when(workflowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        CustInstanceDO coCustInstanceDo = new CustInstanceDO();
        coCustInstanceDo.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        coCustInstanceDo.setId(2);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(any(), anyBoolean())).thenReturn(Collections.singletonList(coCustInstanceDo));

        CustinsParamDO param = new CustinsParamDO(1, PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_KMS_KEY);
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(param);
        // Act
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_UnsupportSetToKmsKeyFromClientKeyMode() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("rolearn");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_KEY_MODE)).thenReturn(PodDefaultConstants.CLS_MODE_CLIENT_KEY);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO)).thenReturn("AES_256_GCM");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_WHITELIST_MODE)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_KEY)).thenReturn("kmsKeyId");
        when(kmsApi.checkAssumeRoleOk(any(), any(), any())).thenReturn(true);

        when(workflowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        CustInstanceDO coCustInstanceDo = new CustInstanceDO();
        coCustInstanceDo.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        coCustInstanceDo.setId(2);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(any(), anyBoolean())).thenReturn(Collections.singletonList(coCustInstanceDo));

        CustinsParamDO param = new CustinsParamDO(1, PodDefaultConstants.CLS_KEY_MODE, PodDefaultConstants.CLS_MODE_KMS_KEY);
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(param);
        // Act
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        // Assert
        assertNotNull(result);
        assertEquals(ErrorCode.INVALID_STATUS.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }


    @Test
    public void testDoActionRequest_InvalidParam_EmptyClsStatus() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("rolearn");
        // Act
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        // Assert
        assertNotNull(result);
        assertEquals(ErrorCode.INVALID_PARAM.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_InvalidParam_InvalidAlgo() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("rolearn");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO)).thenReturn("AES_XXX");
        doThrow(new RdsException(ErrorCode.INVALID_PARAM)).when(encdbService).setEncdbGlobalAlgo(any(), any());
        // Act
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        // Assert
        assertNotNull(result);
        assertEquals(ErrorCode.INVALID_PARAM.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_DbossIOException() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(custins);
        when(mysqlParamSupport.getRoleArn(anyMap())).thenReturn("rolearn");
        UserDO userDo = new UserDO();
        userDo.setLoginId("123_123");
        when(userService.getUserDOByUserId(any())).thenReturn(userDo);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_ENCRYPTION_ALGO)).thenReturn("AES_XXX");
        doThrow(new java.io.IOException()).when(encdbService).setEncdbGlobalAlgo(any(), any());
        // Act
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        // Assert
        assertNotNull(result);
        assertEquals(ErrorCode.INTERNAL_FAILURE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }

    @Test
    public void testDoActionRequest_error_unsupportedKmsRegion() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setInsType(CustInsType.CUST_INS_TYPE_PRIMARY.getValue());
        custins.setId(1);
        custins.setClusterName("clusterName");
        custins.setInsName("insName");
        custins.setDbType("mysql");
        custins.setInsType(0);
        custins.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custins);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.PARAM_CLS_STATUS)).thenReturn("1");
        when(tdeKmsService.checkTdeSupported("testRequestId", "regionID")).thenReturn(false);
        Map<String, Object> result = modifyDBInstanceCLS.doActionRequest(custins, new HashMap<>());
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE.getSummary(), ((Object[])result.get("errorCode"))[1]);
    }
}
