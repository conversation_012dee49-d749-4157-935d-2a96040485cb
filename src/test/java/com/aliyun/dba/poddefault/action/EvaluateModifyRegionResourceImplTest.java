package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.poddefault.action.EvaluateModifyRegionResourceImpl;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({EvaluateModifyRegionResourceImpl.class,RequestSession.class})
public class EvaluateModifyRegionResourceImplTest {

    @InjectMocks
    private com.aliyun.dba.poddefault.action.EvaluateModifyRegionResourceImpl evaluateModifyRegionResource;

    private Map<String, String> params;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    private CustInstanceDO custInstanceDO;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    private DockerCommonService dockerCommonService;
    @Mock
    private PodCommonSupport podCommonSupport;
    @Mock
    private ResourceScheduleHelper resourceScheduleHelper;
    @Before
    public void setUp() throws Exception {

        params = new HashMap<>();
        custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(123);
        // 初始化mock对象
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));
        when(mysqlParamSupport.getParameterValue(anyMap(), anyString())).thenReturn("testValue");
        DefaultApi defaultApi = mock(DefaultApi.class);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
    }

    @Test
    public void test_EvaluateForResourceGuarantee() throws RdsException, ApiException {
        ReplicaSet replicaSetMeta = new ReplicaSet();
        replicaSetMeta.setDiskSizeMB(102400);
        replicaSetMeta.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSetMeta.setClassCode("mysql.x2.xlarge.2c");
        replicaSetMeta.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSetMeta);
        User user=new User();
        when(dBaasMetaService.getDefaultClient().getUser(any(),any(),any())).thenReturn(user);
        ReplicaListResult listReplicasInReplicaSet = new ReplicaListResult();
        List<Replica> replicaList=new ArrayList<>();
        listReplicasInReplicaSet.setItems(replicaList);
        when(dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                any(),any(),any(),any(),any(),any())).thenReturn(listReplicasInReplicaSet);
        when(minorVersionServiceHelper.getServiceSpecTagByCustinsId(any(),any())).thenReturn("alisql_docker_image_cloud_disk_20241231");
        InstanceLevel srcInstanceLevel = new InstanceLevel();
        srcInstanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(),any(),any(),any(),any())).thenReturn(srcInstanceLevel);
        when(replicaSetService.getReplicaSetStorageType(any(),any())).thenReturn("CLOUD_ESSD");
        ServiceSpec serviceSpec = new ServiceSpec();
        serviceSpec.setTag("alisql_docker_image_cloud_disk");
        when(dBaasMetaService.getDefaultClient().getServiceSpecById(any(),any())).thenReturn(serviceSpec);
        boolean isCloudSingleTenant=true;
        when(replicaSetService.isCloudSingleTenant(any(ReplicaSet.BizTypeEnum.class),anyString(),any(InstanceLevel.class),anyBoolean())).thenReturn(isCloudSingleTenant);
        Pair<String, String> resourceEnsurancePair =Pair.of(PodDefaultConstants.LABEL_ALIGNMENT_STRATEGY, PodDefaultConstants.SINGLE_TENANT_RESOURCE_STRATEGY_COMPUTING);
        when(resourceScheduleHelper.makeResourceGuaranteeStrategy(any(),any())).thenReturn(resourceEnsurancePair);


        Map<String, Object> stringObjectMap = evaluateModifyRegionResource.doActionRequest(custInstanceDO, params);
        Assert.assertNotNull(stringObjectMap);
    }
}
