package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.lang.reflect.Method;
import java.util.*;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CheckCreateDdrDBInstanceImpl.class, PodCommonSupport.class})
public class CheckCreateDdrDBInstanceImplTest {
    private CheckCreateDdrDBInstanceImpl checkCreateDdrDBInstance;
    private MysqlParamSupport mysqlParamSupport;
    private DefaultApi defaultApi;
    private DBaasMetaService dBaasMetaService;

    @Before
    public void setUp() {
        checkCreateDdrDBInstance = PowerMockito.spy(new CheckCreateDdrDBInstanceImpl());
        mysqlParamSupport = PowerMockito.mock(MysqlParamSupport.class);
        defaultApi = PowerMockito.mock(DefaultApi.class);
        dBaasMetaService = PowerMockito.mock(DBaasMetaService.class);

        Whitebox.setInternalState(checkCreateDdrDBInstance, "dBaasMetaService", dBaasMetaService);
        Whitebox.setInternalState(checkCreateDdrDBInstance, "mysqlParamSupport", mysqlParamSupport);

        PowerMockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test(expected = RdsException.class)
    public void doCheckAndUpdateDdrRestoreParams() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("backupsettype", "stub");
        actionParams.put("preferredbackuptime", "stub");
        actionParams.put("securityiplist", "stub");
        actionParams.put("backupretentionperiod", "stub");
        actionParams.put("backupperiod", "stub");
        actionParams.put("restoretype", "0");
        actionParams.put("backupsetregion", "stub");

        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.CLUSTER);

        PowerMockito.when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        String stub = "stub";
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), any())).thenReturn(stub);
        PowerMockito.when(mysqlParamSupport.getDBVersion(any(), any())).thenReturn(stub);
        PowerMockito.when(mysqlParamSupport.getAndCheckDBType(any(), any())).thenReturn(stub);
        PowerMockito.when(mysqlParamSupport.getAndCheckBID(any())).thenReturn(stub);
        PowerMockito.when(mysqlParamSupport.getAndCheckUID(any())).thenReturn(stub);
        PowerMockito.when(mysqlParamSupport.getSourceDBInstanceName(any())).thenReturn(stub);
        PowerMockito.when(mysqlParamSupport.getAndCheckRestoreType(any())).thenReturn(RESTORE_TYPE_BAKID);
        instanceLevel.setExtraInfo(stub);

        PowerMockito.mockStatic(PodCommonSupport.class);
        PowerMockito.when(PodCommonSupport.isArchChange(anyString(), anyString())).thenReturn(true);

        PowerMockito.doNothing().when(checkCreateDdrDBInstance, "checkAndUpdateBackupSetParams", any(), any(), any(), any(), any());
        checkCreateDdrDBInstance.doCheckAndUpdateDdrRestoreParams(actionParams);
    }

    @Test
    public void setBackupSetInfoToParams() throws Exception {
        String stub = "stub";
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put("requestid", "stub");
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), any())).thenReturn(stub);

        DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
        DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();
        backupSetInfo.setExtraInfoObj(extraInfo);
        backupSetInfo.setConsistentTime(0L);
        DescribeRestoreBackupSetResponse.SlaveStatus slaveStatus = new DescribeRestoreBackupSetResponse.SlaveStatus();
        extraInfo.setSlaveStatusObj(slaveStatus);

        Method setBackupSetInfoToParams = CheckCreateDdrDBInstanceImpl.class.getDeclaredMethod("setBackupSetInfoToParams", DescribeRestoreBackupSetResponse.BackupSetInfo.class, Map.class);
        setBackupSetInfoToParams.setAccessible(true);
        setBackupSetInfoToParams.invoke(checkCreateDdrDBInstance, backupSetInfo, actionParams);
        Assert.assertNotNull(backupSetInfo);
    }
}