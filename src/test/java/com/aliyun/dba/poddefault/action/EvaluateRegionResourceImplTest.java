package com.aliyun.dba.poddefault.action;

import java.util.HashMap;
import java.util.Map;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.SlrCheckService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;
import org.checkerframework.checker.units.qual.C;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.support.property.ParamConstants.RESTORE_TYPE;
import static com.aliyun.dba.support.property.ParamConstants.SpecifyReleaseDate;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CustinsParamSupport.class, KmsApi.class})
public class EvaluateRegionResourceImplTest {

    @InjectMocks
    private EvaluateRegionResourceImpl evaluateRegionResourceImpl;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private PodAvzSupport avzSupport;

    @Mock
    private CommonProviderService commonProviderService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private MySQLAvzService mySQLAvzService;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private AligroupService aligroupService;

    @Mock
    private PodTemplateHelper podTemplateHelper;

    @Mock
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Mock
    private MysqlEncryptionService mysqlEncryptionService;

    @Mock
    private PodCommonSupport podCommonSupport;
    @Mock
    private CrossArchService crossArchService;

    @Mock
    private KmsApi kmsApi;

    @Mock
    private BakService bakService;

    @Mock
    private BackupService backupService;

    @Mock
    private DbsGateWayService dbsGateWayService;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;
    @Mock
    private RundPodSupport rundPodSupport;
    @Mock
    private SlrCheckService slrCheckService;

    @Before
    public void setUp() {
        // Any setup can be done here if needed
        PowerMockito.mockStatic(CustinsParamSupport.class);
    }

    @Test
    public void testEvaluateRegionResourceImpl_TDENotEnabled() {
        Map<String, String> params = new HashMap<>();
        params.put("BackupSetId", "rbh-restore");
        try {
            DefaultApi defaultApi = mock(DefaultApi.class);
            when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
            when(mysqlParamSupport.getParameterValue(any(), eq(RESTORE_TYPE))).thenReturn(null);
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.REQUEST_ID))).thenReturn("testRequestId");
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.STORAGE))).thenReturn("100");
            InstanceLevel instanceLevel = mock(InstanceLevel.class);
            when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
            when(mysqlParamSupport.getEncryptionKey(any())).thenReturn(null);
            when(mysqlParamSupport.getRoleArn(any())).thenReturn("test-role");
            when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.BACKUP_SET_ID), any())).thenReturn("rbh-restore");

            AVZInfo avzInfo = mock(AVZInfo.class);
            when(avzInfo.getRegionId()).thenReturn("regionid");
            when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
            DescribeRestoreBackupSetResponse describeRestoreBackupSetResponse = new DescribeRestoreBackupSetResponse();
            DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
            DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();
            extraInfo.setTdeEnabled(true);
            extraInfo.setTdeEncryptionKeyId("test-key");
            backupSetInfo.setExtraInfoObj(extraInfo);
            describeRestoreBackupSetResponse.setBackupSetInfoObj(backupSetInfo);

            DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
            DescribeKeyResponse.KeyMetadata keyMetadata = new DescribeKeyResponse.KeyMetadata();
            keyMetadata.setKeyState("Disabled");
            describeKeyResponse.setKeyMetadata(keyMetadata);

            when(kmsApi.describeKeyByRegionId(any(), any(), any(), any())).thenReturn(describeKeyResponse);
            when(dbsGateWayService.describeRestoreBackupSet(any())).thenReturn(describeRestoreBackupSetResponse);

            // Arrange, Act & Assert
            Map<String, Object> result = evaluateRegionResourceImpl.doActionRequest(null, params);
            Object[] errorCode = (Object[])result.get("errorCode");
            assertEquals(ErrorCode.INVALID_KMS_KEY.getSummary(), errorCode[1]);
        } catch (Exception e) {
            fail();

        }
    }

    @Test
    public void testEvaluateRegionResourceImpl_TDECLSBothEnabled() {
        Map<String, String> params = new HashMap<>();
        params.put("BackupSetId", "rbh-restore");
        try {
            DefaultApi defaultApi = mock(DefaultApi.class);
            when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
            when(mysqlParamSupport.getParameterValue(any(), eq(RESTORE_TYPE))).thenReturn(null);
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.REQUEST_ID))).thenReturn("testRequestId");
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.STORAGE))).thenReturn("100");
            InstanceLevel instanceLevel = mock(InstanceLevel.class);
            when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
            when(mysqlParamSupport.getEncryptionKey(any())).thenReturn(null);
            when(mysqlParamSupport.getRoleArn(any())).thenReturn("test-role");
            when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.BACKUP_SET_ID), any())).thenReturn("rbh-restore");

            AVZInfo avzInfo = mock(AVZInfo.class);
            when(avzInfo.getRegionId()).thenReturn("regionid");
            when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
            DescribeRestoreBackupSetResponse describeRestoreBackupSetResponse = new DescribeRestoreBackupSetResponse();
            DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
            DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();
            extraInfo.setTdeEnabled(true);
            extraInfo.setTdeEncryptionKeyId("test-key");
            extraInfo.setClsKeyMode("kms_key");
            extraInfo.setClsEncryptionKeyId("test-key");
            backupSetInfo.setExtraInfoObj(extraInfo);
            describeRestoreBackupSetResponse.setBackupSetInfoObj(backupSetInfo);

            DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
            DescribeKeyResponse.KeyMetadata keyMetadata = new DescribeKeyResponse.KeyMetadata();
            keyMetadata.setKeyState("Enabled");
            describeKeyResponse.setKeyMetadata(keyMetadata);

            when(kmsApi.describeKeyByRegionId(any(), any(), any(), any())).thenReturn(describeKeyResponse);
            when(dbsGateWayService.describeRestoreBackupSet(any())).thenReturn(describeRestoreBackupSetResponse);

            // Arrange, Act & Assert
            Map<String, Object> result = evaluateRegionResourceImpl.doActionRequest(null, params);
            Assert.assertNotNull(result);
        } catch (Exception e) {
            fail();

        }
    }

    @Test
    public void testEvaluateRegionResourceImpl_from_docker() {
        Map<String, String> params = new HashMap<>();
        params.put("BackupSetId", "rbh-restore");

        try {
            DefaultApi defaultApi = mock(DefaultApi.class);
            when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
            when(mysqlParamSupport.getParameterValue(any(), eq(RESTORE_TYPE))).thenReturn(RESTORE_TYPE_TIME);
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.REQUEST_ID))).thenReturn("testRequestId");
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.STORAGE))).thenReturn("100");
            InstanceLevel instanceLevel = mock(InstanceLevel.class);
            when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
            when(mysqlParamSupport.getEncryptionKey(any())).thenReturn(null);
            when(mysqlParamSupport.getRoleArn(any())).thenReturn("test-role");
            when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.BACKUP_SET_ID), any())).thenReturn("rbh-restore");

            AVZInfo avzInfo = mock(AVZInfo.class);
            when(avzInfo.getRegionId()).thenReturn("regionid");
            when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
            DescribeRestoreBackupSetResponse describeRestoreBackupSetResponse = new DescribeRestoreBackupSetResponse();
            DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
            DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();
            extraInfo.setTdeEnabled(true);
            extraInfo.setTdeEncryptionKeyId("test-key");
            extraInfo.setClsKeyMode("kms_key");
            extraInfo.setClsEncryptionKeyId("test-key");
            backupSetInfo.setExtraInfoObj(extraInfo);
            describeRestoreBackupSetResponse.setBackupSetInfoObj(backupSetInfo);

            DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
            DescribeKeyResponse.KeyMetadata keyMetadata = new DescribeKeyResponse.KeyMetadata();
            keyMetadata.setKeyState("Disabled");
            describeKeyResponse.setKeyMetadata(keyMetadata);

            when(kmsApi.describeKeyByRegionId(any(), any(), any(), any())).thenReturn(describeKeyResponse);
            when(dbsGateWayService.describeRestoreBackupSet(any())).thenReturn(describeRestoreBackupSetResponse);
            ReplicaSet replicaSet = new ReplicaSet();
            when(dBaasMetaService.getDefaultClient().getReplicaSet(any(),any(),any())).thenReturn(replicaSet);
            CustInstanceDO custInstanceDO=new CustInstanceDO();
            custInstanceDO.setKindCode(18);
            when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(custInstanceDO);
            Map<String, Object> result = evaluateRegionResourceImpl.doActionRequest(null, params);
            Assert.assertNotNull(result);
        } catch (Exception e) {
            fail();

        }
    }

    @Test
    public void testEvaluateRegionResourceImpl_from_docker1() {
        Map<String, String> params = new HashMap<>();
        params.put("BackupSetId", "rbh-restore");

        try {
            DefaultApi defaultApi = mock(DefaultApi.class);
            when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
            when(mysqlParamSupport.getParameterValue(any(), eq(RESTORE_TYPE))).thenReturn(RESTORE_TYPE_TIME);
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.REQUEST_ID))).thenReturn("testRequestId");
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.STORAGE))).thenReturn("100");
            InstanceLevel instanceLevel = mock(InstanceLevel.class);
            when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
            when(mysqlParamSupport.getEncryptionKey(any())).thenReturn(null);
            when(mysqlParamSupport.getRoleArn(any())).thenReturn("test-role");
            when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.BACKUP_SET_ID), any())).thenReturn("rbh-restore");

            AVZInfo avzInfo = mock(AVZInfo.class);
            when(avzInfo.getRegionId()).thenReturn("regionid");
            when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
            DescribeRestoreBackupSetResponse describeRestoreBackupSetResponse = new DescribeRestoreBackupSetResponse();
            DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
            DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();
            extraInfo.setTdeEnabled(true);
            extraInfo.setTdeEncryptionKeyId("test-key");
            extraInfo.setClsKeyMode("kms_key");
            extraInfo.setClsEncryptionKeyId("test-key");
            backupSetInfo.setExtraInfoObj(extraInfo);
            describeRestoreBackupSetResponse.setBackupSetInfoObj(backupSetInfo);

            DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
            DescribeKeyResponse.KeyMetadata keyMetadata = new DescribeKeyResponse.KeyMetadata();
            keyMetadata.setKeyState("Disabled");
            describeKeyResponse.setKeyMetadata(keyMetadata);

            when(kmsApi.describeKeyByRegionId(any(), any(), any(), any())).thenReturn(describeKeyResponse);
            when(dbsGateWayService.describeRestoreBackupSet(any())).thenReturn(describeRestoreBackupSetResponse);
            ReplicaSet replicaSet = new ReplicaSet();
            when(dBaasMetaService.getDefaultClient().getReplicaSet(any(),any(),any())).thenReturn(replicaSet);
            CustInstanceDO custInstanceDO=new CustInstanceDO();
            custInstanceDO.setKindCode(3);
            when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(custInstanceDO);
            Map<String, Object> result = evaluateRegionResourceImpl.doActionRequest(null, params);
            Assert.assertNotNull(result);

        } catch (Exception e) {
            fail();

        }
    }

    @Test
    public void testEvaluateRegionResourceImpl_rund() {
        Map<String, String> params = new HashMap<>();
        params.put("SpecifyReleaseDate".toLowerCase(),"20241231");
        params.put("dbtype".toLowerCase(),"mysql");
        params.put("dbversion".toLowerCase(),"5.7");
        try {
            DefaultApi defaultApi = mock(DefaultApi.class);
            when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.REQUEST_ID))).thenReturn("testRequestId");
            when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.STORAGE))).thenReturn("100");
            when(mysqlParamSupport.getAndCheckDBType(any(),any())).thenReturn("msyql");
            when(mysqlParamSupport.getAndCheckDBVersion(anyMap(),anyString(),anyBoolean())).thenReturn("5.7");
            when(mysqlParamSupport.getParameterValue(params, SpecifyReleaseDate)).thenReturn("20241231");
            when(podParameterHelper.getDiskType(any(),any())).thenReturn("cloud_essd");
            InstanceLevel instanceLevel = new InstanceLevel();
            instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
            when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
            when(mysqlParamSupport.getEncryptionKey(any())).thenReturn(null);
            when(mysqlParamSupport.getRoleArn(any())).thenReturn("test-role");
            when( podParameterHelper.getBizType(any(), any())).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);
            AVZInfo avzInfo = mock(AVZInfo.class);
            when(avzInfo.getRegionId()).thenReturn("regionid");
            when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
            DescribeRestoreBackupSetResponse describeRestoreBackupSetResponse = new DescribeRestoreBackupSetResponse();
            DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
            DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();
            extraInfo.setTdeEnabled(true);
            extraInfo.setTdeEncryptionKeyId("test-key");
            extraInfo.setClsKeyMode("kms_key");
            extraInfo.setClsEncryptionKeyId("test-key");
            backupSetInfo.setExtraInfoObj(extraInfo);
            describeRestoreBackupSetResponse.setBackupSetInfoObj(backupSetInfo);
            when(minorVersionServiceHelper.getServiceSpecTag(any(), any(ReplicaSet.BizTypeEnum.class), any(), any(), any(), anyInt(), any(InstanceLevel.class), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyMap())).thenReturn("serviceSpecTag");

            DescribeKeyResponse describeKeyResponse = new DescribeKeyResponse();
            DescribeKeyResponse.KeyMetadata keyMetadata = new DescribeKeyResponse.KeyMetadata();
            keyMetadata.setKeyState("Disabled");
            describeKeyResponse.setKeyMetadata(keyMetadata);
            when(rundPodSupport.routeToRund(anyString(),anyBoolean())).thenReturn(true);
            when(rundPodSupport.getPodTypeByGrayConfig(any(), any(),any(),any())).thenReturn(PodType.POD_ECS_RUND);
            when(kmsApi.describeKeyByRegionId(any(), any(), any(), any())).thenReturn(describeKeyResponse);
            when(dbsGateWayService.describeRestoreBackupSet(any())).thenReturn(describeRestoreBackupSetResponse);
            ReplicaSet replicaSet = new ReplicaSet();
            when(dBaasMetaService.getDefaultClient().getReplicaSet(any(),any(),any())).thenReturn(replicaSet);
            CustInstanceDO custInstanceDO=new CustInstanceDO();
            custInstanceDO.setKindCode(3);
            Map<String, Object> result = evaluateRegionResourceImpl.doActionRequest(null, params);
            Assert.assertNotNull(result);

        } catch (Exception e) {
            fail();

        }
    }
}
