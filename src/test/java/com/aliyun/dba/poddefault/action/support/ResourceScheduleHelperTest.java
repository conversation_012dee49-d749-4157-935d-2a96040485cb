
package com.aliyun.dba.poddefault.action.support;


import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class ResourceScheduleHelperTest {

    @InjectMocks
    private ResourceScheduleHelper resourceScheduleHelper;

    @Mock
    private ResourceService resourceService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testMakeResourceGuaranteeStrategy_WhiteListUser_CalculatingPowerEnsure() {
        ResourceDO resourceDO = mock(ResourceDO.class);
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);
        when(resourceDO.getRealValue()).thenReturn("[\"123\"]");

        String uid = "123";
        String replicaSetName ="replicasetName";

        Pair<String, String> result = resourceScheduleHelper.makeResourceGuaranteeStrategy(uid, replicaSetName);

        assertEquals(PodDefaultConstants.LABEL_ALIGNMENT_STRATEGY, result.getKey());
        assertEquals(PodDefaultConstants.SINGLE_TENANT_RESOURCE_STRATEGY_COMPUTING, result.getValue());
    }

    @Test
    public void testMakeResourceGuaranteeStrategy_NotWhiteListUser_Null() {
        ResourceDO resourceDO = mock(ResourceDO.class);
        when(resourceService.getResourceByResKey(anyString())).thenReturn(resourceDO);
        when(resourceDO.getRealValue()).thenReturn("[\"123\"]");

        String uid = "1234";
        String replicaSetName ="replicasetName";

        Pair<String, String> result = resourceScheduleHelper.makeResourceGuaranteeStrategy(uid, replicaSetName);

        assertNull(result);
    }

    @Test
    public void testMakeResourceGuaranteeStrategy_ResourceNotFound_Null() {
        when(resourceService.getResourceByResKey(anyString())).thenReturn(null);

        String uid = "1234";
        String replicaSetName ="replicasetName";

        Pair<String, String> result = resourceScheduleHelper.makeResourceGuaranteeStrategy(uid,replicaSetName);

        assertNull(result);
    }


}