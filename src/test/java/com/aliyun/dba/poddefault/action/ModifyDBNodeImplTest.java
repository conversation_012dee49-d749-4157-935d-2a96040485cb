package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.TransferTask;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.poddefault.action.service.PoddefaultResourceGuaranteeModelService;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;

import com.aliyun.dba.support.utils.RequestSession;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class, DBaasMetaService.class, DefaultApi.class})
public class ModifyDBNodeImplTest {

    @InjectMocks
    private ModifyDBNodeImpl modifyDBNodeImpl;

    @Mock
    private PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApiClient;

    @Mock
    private PodModifyInsParam mockPodModifyInsParam;

    @Captor
    private ArgumentCaptor<TransferTask> transferTaskCaptor;


    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testBuildTransListForLocalModify() throws Exception {

        ModifyDBNodeImpl modifyDBNodeImpl = spy(new ModifyDBNodeImpl());
        Field field = ModifyDBNodeImpl.class.getDeclaredField("dBaasMetaService");
        field.setAccessible(true);
        field.set(modifyDBNodeImpl, dBaasMetaService);

        PodModifyInsParam mockPodModifyInsParam = mock(PodModifyInsParam.class);
        when(mockPodModifyInsParam.getRequestId()).thenReturn("testRequestId");
        when(mockPodModifyInsParam.getDbInstanceName()).thenReturn("testInstance");
        when(mockPodModifyInsParam.getClassCode()).thenReturn("classA");
        when(mockPodModifyInsParam.getTargetClassCode()).thenReturn("classB");
        when(mockPodModifyInsParam.getDiskSizeGB()).thenReturn(100);
        when(mockPodModifyInsParam.getTargetDiskSizeGB()).thenReturn(200);
        when(mockPodModifyInsParam.getUid()).thenReturn("uid123");
        when(mockPodModifyInsParam.getTargetInstanceLevel()).thenReturn(null);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testInstance");
        when(mockPodModifyInsParam.getReplicaSetMeta()).thenReturn(replicaSet);

        List<ModifyDBNodeImpl.NodeModifyEntity> nodeModifyEntityList = new ArrayList<>();
        ModifyDBNodeImpl.NodeModifyEntity entity = mock(ModifyDBNodeImpl.NodeModifyEntity.class);
        when(entity.getReplicaId()).thenReturn(1L);
        when(entity.getSrcClassCode()).thenReturn("classA");
        when(entity.getDstClassCode()).thenReturn("classB");
        nodeModifyEntityList.add(entity);
        TransferTask transferTask = new TransferTask();
        transferTask.setId(12345);

        PoddefaultResourceGuaranteeModelService guaranteeService = mock(PoddefaultResourceGuaranteeModelService.class);
        Map<String, String> resourceMap = new HashMap<>();
        resourceMap.put("resourceGuaranteeLevel", "level1");
        resourceMap.put("resourceGuaranteeLevelType", "type1");
        resourceMap.put("resourceGuaranteeBackUpLevels", "backup1");
        when(guaranteeService.getAllResourceGuaranteeLevelMapForUid(anyString())).thenReturn(resourceMap);
        PowerMockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApiClient);
        when(dBaasMetaService.getDefaultClient().createTransferTask(any(),anyString(),any())).thenReturn(transferTask);
        // 设置依赖
        modifyDBNodeImpl.poddefaultResourceGuaranteeModelService = guaranteeService;

        //使用反射调用 private 方法
        Method method = ModifyDBNodeImpl.class.getDeclaredMethod("buildTransListForLocalModify", PodModifyInsParam.class, List.class);
        method.setAccessible(true);  // 允许访问私有方法

        // 6. 执行私有方法
        Integer taskId = (Integer) method.invoke(modifyDBNodeImpl, mockPodModifyInsParam, nodeModifyEntityList);

        // 7. 断言结果
        assertNotNull(taskId);
        assertEquals(12345, taskId.intValue());
    }

}
