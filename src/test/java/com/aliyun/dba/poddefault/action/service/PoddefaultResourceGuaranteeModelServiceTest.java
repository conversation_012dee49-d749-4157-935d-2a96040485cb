package com.aliyun.dba.poddefault.action.service;

import com.alicloud.apsaradb.inventory.model.SpecModificationRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.dba.base.common.consts.ResourceScheduleConsts;
import com.aliyun.dba.base.service.ResourceScheduleService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PoddefaultResourceGuaranteeModelServiceTest {

    @InjectMocks
    private PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;

    @Mock
    private ResourceScheduleService resourceScheduleService;

    @Before
    public void setUp() {
        System.out.println("开始测试------");
    }

    @Test
    public void testAddResourceGuaranteeModelForLocalUpgrade_ValidInput_ShouldSetResourceGuaranteePolicies() {
        // Arrange
        SpecModificationRequest request = new SpecModificationRequest();
        request.setUid("testUid");

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL, "performance,super-user");
        resultMap.put(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL_BACKUP, "regular,low-cost");

        when(resourceScheduleService.getResGuaranteeModelMapKey()).thenReturn("mapKey");
        when(resourceScheduleService.getResourceGuaranteeModelPolicyMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(resultMap);

        // Act
        poddefaultResourceGuaranteeModelService.addResourceGuaranteeModelForLocalUpgrade(request);

        // Assert
        assertEquals("performance", request.getResourceGuaranteeLevel());
        assertEquals("prefer", request.getResourceGuaranteeLevelType());
        assertEquals("super-user", request.getResourceGuaranteeBackUpLevels().get(0));
        assertEquals(true, request.isUseResourceGuarantee());
    }



    @After
    public void tearDown() {
        System.out.println("测试结束------");
    }
}
