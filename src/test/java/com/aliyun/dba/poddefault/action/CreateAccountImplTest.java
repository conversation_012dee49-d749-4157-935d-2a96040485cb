package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Account;
import com.aliyun.apsaradb.dbaasmetaapi.model.AccountListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.CustinsIDao;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.idao.AccountIDao;
import com.aliyun.dba.dbs.service.AccountService;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.apsaradb.dbaasmetaapi.api.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.dbs.support.DbsSupport.BIZ_TYPE_USER;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CreateAccountImplTest {

    @InjectMocks
    private CreateAccountImpl createAccountImpl;

    @Mock
    private AccountService accountService;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private DbossApi dbossApi;

    @Mock
    private MysqlParameterHelper parameterHelper;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private CustinsIDao custinsIDao;

    @Mock
    private AccountIDao accountIDao;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void doActionRequest_UnsupportedDBInstanceType_ReturnsErrorResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        ReplicaSet replicaSet = mock(ReplicaSet.class);
        when(replicaSet.getInsType()).thenReturn(ReplicaSet.InsTypeEnum.TMP);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);

        Map<String, Object> result = createAccountImpl.doActionRequest(null, params);

        assertNotNull(result.get("errorCode"));
    }

    @Test
    public void doActionRequest_UnsupportedDBInstanceStatus_ReturnsErrorResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        ReplicaSet replicaSet = mock(ReplicaSet.class);
        when(replicaSet.getInsType()).thenReturn(ReplicaSet.InsTypeEnum.MAIN);
        when(replicaSet.getStatus()).thenReturn(ReplicaSet.StatusEnum.DELETING);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);

        Map<String, Object> result = createAccountImpl.doActionRequest(null, params);

        assertNotNull(result.get("errorCode"));
    }


    public void accountHelper(Map<String,String> params, ReplicaSet replicaSet) throws Exception {
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put(ParamConstants.ACCOUNT_NAME, "testAccount");
        params.put(ParamConstants.ACCOUNT_PASSWORD, "testPassword");
        params.put(ParamConstants.ACCOUNT_TYPE, "common");
        params.put(ParamConstants.ACCOUNT_BIZ_TYPE, "user");
        params.put(ParamConstants.ACCOUNT_PRIVILEGE, "ReadWrite");
        params.put(ParamConstants.COMMENT, "Test Account");
        params.put(ParamConstants.ACCOUNT_ADMIN_TYPE, "0");

        replicaSet.serviceVersion("DB_VERSION_MYSQL_57");
        replicaSet.insType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.status(ReplicaSet.StatusEnum.ACTIVE);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.fromValue("lvs"));
        replicaSet.id(123L);
        replicaSet.name("replicaSet");

        when(parameterHelper.getParameterValue(ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(parameterHelper.getDBInstanceName()).thenReturn("replicaSet");

        when(parameterHelper.getAccountName()).thenReturn("testAccount");
        when(parameterHelper.checkAccountName("testAccount","DB_VERSION_MYSQL_57")).thenReturn("testAccount");

        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(replicaSetService.replicasetInAvailableStatus(any(ReplicaSet.StatusEnum.class))).thenReturn(true);
        when(parameterHelper.getAccountPrivilege()).thenReturn("ALL");
        when(parameterHelper.getAdminType()).thenReturn(0);
        when(parameterHelper.getAndCheckDecryptedAccountPassword()).thenReturn("testPassword");
        when(parameterHelper.getDbInfo()).thenReturn("{}");
        when(parameterHelper.getDBNames()).thenReturn(null);
        when(parameterHelper.getAndCheckAccountDesc()).thenReturn("Test Account");
        when(workFlowService.dispatchTask(any(ReplicaSet.class), anyString(), anyString(), anyInt())).thenReturn("1.0");

        CustInstanceDO cdo = new CustInstanceDO();
        when(custinsIDao.getCustInstanceByCustinsId(replicaSet.getId().intValue())).thenReturn(cdo);
        doNothing().when(custinsIDao).updateCustinsAccountMode(cdo);
    }

    // 正常创建普通账号
    @Test
    public void doActionRequest_Success_ReturnsSuccessResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        ReplicaSet replicaSet = new ReplicaSet();
        accountHelper(params,replicaSet);

        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON))
                .thenReturn(CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON);

        when(accountIDao.queryAccountByAccountName(anyInt(), anyString())).thenReturn(null);

        Map<String, Object> result = createAccountImpl.doActionRequest(null, params);

        assertNotNull(result);
        assertEquals("testAccount", result.get("AccountName"));
    }

    // 正常创建高权限账号，元数据中没有高权限账号
    @Test
    public void doActionRequest_SuccessSuper_ReturnsSuccessResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        ReplicaSet replicaSet = new ReplicaSet();
        accountHelper(params,replicaSet);

        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON))
                .thenReturn(CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER);

        Account resAcc = new Account();
        resAcc.id(123456L);

        DefaultApi da = mock(DefaultApi.class);
        when(dBaasMetaService.getDefaultClient()).thenReturn(da);
        when(da.createAccountForReplicaSet(anyString(), anyString(), any(Account.class))).thenReturn(resAcc);

        Account queryExample = new Account();
        queryExample.setBizType(BIZ_TYPE_USER);
        queryExample.setStatus(Account.StatusEnum.ACTIVE);
        queryExample.setPriviledgeType(Account.PriviledgeTypeEnum.ALIYUN_SUPER);

        // 元数据库中没有高权限账号
        when(da.listReplicaSetAccountsByExample("requestId", "replicaSet", null,20 ,queryExample)).thenReturn(null);

        // 用户实例上的账号列表
        List<Map<String, Object>> accountList = new ArrayList<>();
        Map<String, Object> account1 = new HashMap<>();
        account1.put("accountName", "JohnDoe");
        accountList.add(account1);
        Map<String, Object> account2 = new HashMap<>();
        account2.put("accountName", "Alice");
        accountList.add(account2);
        when(dbossApi.queryAccounts(replicaSet.getId().intValue(), null, null,0,accountList.size(),null))
                .thenReturn(accountList);

        // 查用户实例
        Map<String,Integer> accountCount = new HashMap<>();
        accountCount.put("accounts", accountList.size());
        when(dbossApi.getAccountCount(replicaSet.getId().toString(),null, RdsConstants.ROLETYPE_USER))
                .thenReturn(accountCount);


        Map<String, Object> result = createAccountImpl.doActionRequest(null, params);

        assertNotNull(result);
        assertEquals("testAccount", result.get("AccountName"));
    }

    // 创建高权限账号，元数据中有高权限账号，用户数据中也有
    @Test
    public void doActionRequest_FailSuper_ReturnsSuccessResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        ReplicaSet replicaSet = new ReplicaSet();
        accountHelper(params,replicaSet);

        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON))
                .thenReturn(CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER);

        Account resAcc = new Account();
        resAcc.id(123456L);

        DefaultApi da = mock(DefaultApi.class);
        when(dBaasMetaService.getDefaultClient()).thenReturn(da);

        Account queryExample = new Account();
        queryExample.setBizType(BIZ_TYPE_USER);
        queryExample.setStatus(Account.StatusEnum.ACTIVE);
        queryExample.setPriviledgeType(Account.PriviledgeTypeEnum.ALIYUN_SUPER);

        // 元数据库中的高权限账号
        AccountListResult alr = new AccountListResult();
        List<Account> items = new ArrayList<>();
        Account acc = new Account();
        acc.name("superAccount");
        items.add(acc);
        alr.items(items);

        when(da.listReplicaSetAccountsByExample("requestId", "replicaSet", null,20 ,queryExample)).thenReturn(alr);

        // 用户实例上的账号列表
        List<Map<String, Object>> accountList = new ArrayList<>();
        Map<String, Object> account1 = new HashMap<>();
        account1.put("accountName", "JohnDoe");
        accountList.add(account1);
        Map<String, Object> account2 = new HashMap<>();
        account2.put("accountName", "testAccount");
        accountList.add(account2);
        when(dbossApi.queryAccounts(replicaSet.getId().intValue(), null, null,0,accountList.size(),null))
                .thenReturn(accountList);

        // 查用户实例
        Map<String,Integer> accountCount = new HashMap<>();
        accountCount.put("accounts", accountList.size());
        when(dbossApi.getAccountCount(replicaSet.getId().toString(),null, RdsConstants.ROLETYPE_USER))
                .thenReturn(accountCount);


        Map<String, Object> result = createAccountImpl.doActionRequest(null, params);

        assertNotNull(result.get("errorCode"));
    }

    // 创建高权限账号，元数据中有高权限账号，用户数据中没有
    @Test
    public void doActionRequest_SuccessSuperDelete_ReturnsSuccessResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        ReplicaSet replicaSet = new ReplicaSet();
        accountHelper(params,replicaSet);

        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_TYPE,CustinsSupport.CUSTINS_ACCOUNT_TYPE_COMMON))
                .thenReturn(CustinsSupport.CUSTINS_ACCOUNT_TYPE_ALIYUN_SUPER);

        Account resAcc = new Account();
        resAcc.id(123456L);

        DefaultApi da = mock(DefaultApi.class);
        when(dBaasMetaService.getDefaultClient()).thenReturn(da);
        when(da.createAccountForReplicaSet(anyString(), anyString(), any(Account.class))).thenReturn(resAcc);

        Account queryExample = new Account();
        queryExample.setBizType(BIZ_TYPE_USER);
        queryExample.setStatus(Account.StatusEnum.ACTIVE);
        queryExample.setPriviledgeType(Account.PriviledgeTypeEnum.ALIYUN_SUPER);

        // 元数据库中的高权限账号
        AccountListResult alr = new AccountListResult();
        List<Account> items = new ArrayList<>();
        Account acc = new Account();
        acc.name("superAccount");
        items.add(acc);
        alr.items(items);

        when(da.listReplicaSetAccountsByExample("requestId", "replicaSet", null,20 ,queryExample)).thenReturn(alr);
        when(da.deleteReplicaSetAccount("requestId", replicaSet.getName(),acc.getName())).thenReturn(null);

        // 用户实例上的账号列表
        List<Map<String, Object>> accountList = new ArrayList<>();
        Map<String, Object> account1 = new HashMap<>();
        account1.put("accountName", "JohnDoe");
        accountList.add(account1);
        Map<String, Object> account2 = new HashMap<>();
        account2.put("accountName", "Alice");
        accountList.add(account2);
        when(dbossApi.queryAccounts(replicaSet.getId().intValue(), null, null,0,accountList.size(),null))
                .thenReturn(accountList);

        // 查用户实例
        Map<String,Integer> accountCount = new HashMap<>();
        accountCount.put("accounts", accountList.size());
        when(dbossApi.getAccountCount(replicaSet.getId().toString(),null, RdsConstants.ROLETYPE_USER))
                .thenReturn(accountCount);


        Map<String, Object> result = createAccountImpl.doActionRequest(null, params);

        assertNotNull(result);
        assertEquals("testAccount", result.get("AccountName"));
        verify(da, times(1))
                .deleteReplicaSetAccount("requestId", replicaSet.getName(),acc.getName());
    }

}
