package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.migrate.MigrateDBInstanceAvzService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.service.KmsService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_CLOUD;
import static com.aliyun.dba.support.property.ParamConstants.DispenseMode.MultiAVZDispenseMode;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)

public class MigrateDBNodesImplTest {

    @Mock
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @InjectMocks
    private MigrateDBNodesImpl migrateDBNodesImpl;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    com.aliyun.apsaradb.activityprovider.api.DefaultApi defaultApiForCommon;
    @Mock
    private com.aliyun.apsaradb.activityprovider.api.DefaultApi comDefaultApi;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    private PodCommonSupport podCommonSupport;
    @Mock
    private PodTemplateHelper podTemplateHelper;
    @Mock
    private CommonProviderService commonProviderService;
    @Mock
    private BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Mock
    private PodModifyInsParam modifyInsParam;
    @Mock
    private KmsService kmsService;
    @Mock
    private MigrateDBInstanceAvzService migrateDBInstanceAvzService;
    @Mock
    protected CustinsService custinsService;
    @Mock
    private InstanceService instanceService;
    @Mock
    protected InstanceIDao instanceIDao;
    @Mock
    protected WorkFlowService workFlowService;


    @Before
    public void setUp() throws ApiException {
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(commonProviderService.getDefaultApi()).thenReturn(defaultApiForCommon);
    }

    @Test
    public void doActionRequest_BizTypeNotAliyun_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"node1\",\"zoneId\":\"zone1\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ODBS);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);

        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_MGR_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"node1\",\"zoneId\":\"zone1\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        when(replicaSetService.isMgr(null, null)).thenReturn(true);

        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_BasicAndStandard_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"node1\",\"zoneId\":\"zone1\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("basic");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        when(replicaSetService.isMgr(null, null)).thenReturn(false);

        migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        replicaSet.setCategory("standard");
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_Tmp_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"node1\",\"zoneId\":\"zone1\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        when(replicaSetService.isMgr(null, null)).thenReturn(false);
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_InvaildKMS_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"node1\",\"zoneId\":\"zone1\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        when(replicaSetService.isMgr(null, null)).thenReturn(false);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(false);
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_EmptyReplicasetList_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"node1\",\"zoneId\":\"zone1\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        replicaListResult.setItems(null);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_EmptyDbNodes_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
//        params.put("DBNode", "[{\"nodeId\":\"node1\",\"zoneId\":\"zone1\"}]");
        params.put("DBNode", null);

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(1L);
        replica.setZoneId("cn-beijing");
        replica.setClassCode("mysql-x5");
        replica.setName("replicaId1");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        Replica replica1 = new Replica();
        replica1.setId(2L);
        replica1.setZoneId("cn-beijing");
        replica1.setClassCode("mysql-x5");
        replica1.setName("replicaId2");
        replica1.setRole(Replica.RoleEnum.SLAVE);
        replicas.add(replica1);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_InvalidNodeId_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"node1\",\"zoneId\":\"zone1\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(1L);
        replica.setZoneId("cn-beijing");
        replica.setClassCode("mysql-x5");
        replica.setName("replicaId1");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        Replica replica1 = new Replica();
        replica1.setId(2L);
        replica1.setZoneId("cn-beijing");
        replica1.setClassCode("mysql-x5");
        replica1.setName("replicaId2");
        replica1.setRole(Replica.RoleEnum.SLAVE);
        replicas.add(replica1);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        when(mysqlParamSupport.getParameterValue(params, "DBNode")).thenReturn(params.get("DBNode"));
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_DuplicateNodeId_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"replicaId1\", \"classCode\":\"mysql-x5\", \"role\":\"master\", \"zoneId\":\"cn-beijing-l\"}, {\"nodeId\":\"replicaId1\", \"classCode\":\"mysql-x5\", \"role\":\"master\", \"zoneId\":\"cn-beijing-l\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(1L);
        replica.setZoneId("cn-beijing");
        replica.setClassCode("mysql-x5");
        replica.setName("replicaId1");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        Replica replica1 = new Replica();
        replica1.setId(2L);
        replica1.setZoneId("cn-beijing");
        replica1.setClassCode("mysql-x5");
        replica1.setName("replicaId2");
        replica1.setRole(Replica.RoleEnum.SLAVE);
        replicas.add(replica1);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        when(mysqlParamSupport.getParameterValue(params, "DBNode")).thenReturn(params.get("DBNode"));
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_InvalidZoneId_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"replicaId1\", \"classCode\":\"mysql-x5\", \"role\":\"master\", \"zoneId\":null}, {\"nodeId\":\"replicaId2\", \"classCode\":\"mysql-x5\", \"role\":\"master\", \"zoneId\":\"cn-beijing-l\"}]");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(1L);
        replica.setZoneId("cn-beijing");
        replica.setClassCode("mysql-x5");
        replica.setName("replicaId1");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        Replica replica1 = new Replica();
        replica1.setId(2L);
        replica1.setZoneId("cn-beijing");
        replica1.setClassCode("mysql-x5");
        replica1.setName("replicaId2");
        replica1.setRole(Replica.RoleEnum.SLAVE);
        replicas.add(replica1);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        when(mysqlParamSupport.getParameterValue(params, "DBNode")).thenReturn(params.get("DBNode"));
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_NothingChange_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"replicaId1\", \"classCode\":\"mysql-x5\", \"role\":\"master\", \"zoneId\":\"cn-beijing-i\"}]");
        params.put(ParamConstants.VSWITCH_ID, "vsw-xxxx");
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(1L);
        replica.setZoneId("cn-beijing-i");
        replica.setClassCode("mysql-x5");
        replica.setName("replicaId1");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        Replica replica1 = new Replica();
        replica1.setId(2L);
        replica1.setZoneId("cn-beijing-i");
        replica1.setClassCode("mysql-x5");
        replica1.setName("replicaId2");
        replica1.setRole(Replica.RoleEnum.SLAVE);
        replicas.add(replica1);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        when(mysqlParamSupport.getParameterValue(params, "DBNode")).thenReturn(params.get("DBNode"));
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID)).thenReturn(params.get(ParamConstants.VSWITCH_ID));
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }


    @Test
    public void doActionRequest_InvalidMinorVersion_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"replicaId1\", \"classCode\":\"mysql-x5\", \"role\":\"master\", \"zoneId\":\"cn-beijing-l\"}]");
        params.put(ParamConstants.VSWITCH_ID, "vsw-xxxx");
        ReplicaSet replicaSet = new ReplicaSet();
        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(1L);
        replica.setZoneId("cn-beijing-i");
        replica.setClassCode("mysql-x5");
        replica.setName("replicaId1");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        Replica replica1 = new Replica();
        replica1.setId(2L);
        replica1.setZoneId("cn-beijing-i");
        replica1.setClassCode("mysql-x5");
        replica1.setName("replicaId2");
        replica1.setRole(Replica.RoleEnum.SLAVE);
        replicas.add(replica1);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        when(mysqlParamSupport.getParameterValue(params, "DBNode")).thenReturn(params.get("DBNode"));
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID)).thenReturn(params.get(ParamConstants.VSWITCH_ID));
        when(minorVersionServiceHelper.getServiceSpecTagByCustinsId(null, 123)).thenReturn(null);
        when(baseModifyDBInstanceService.initPodModifyInsParam(any())).thenReturn(modifyInsParam);
        when(modifyInsParam.getCustins()).thenReturn(custins);
        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

    @Test
    public void doActionRequest_InsLevel_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"replicaId1\", \"classCode\":\"mysql-x5\", \"role\":\"master\", \"zoneId\":\"cn-beijing-l\"}]");
        params.put(ParamConstants.VSWITCH_ID, "vsw-xxxx");
        ReplicaSet replicaSet = new ReplicaSet();
        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        replicaSet.setId(123456789L);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(1L);
        replica.setZoneId("cn-beijing-i");
        replica.setClassCode("mysql-x5");
        replica.setName("replicaId1");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        Replica replica1 = new Replica();
        replica1.setId(2L);
        replica1.setZoneId("cn-beijing-i");
        replica1.setClassCode("mysql-x5");
        replica1.setName("replicaId2");
        replica1.setRole(Replica.RoleEnum.SLAVE);
        replicas.add(replica1);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        when(mysqlParamSupport.getParameterValue(params, "DBNode")).thenReturn(params.get("DBNode"));
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID)).thenReturn(params.get(ParamConstants.VSWITCH_ID));
        when(minorVersionServiceHelper.getServiceSpecTagByCustinsId(null, 123)).thenReturn("testServiceSpec");
        when(baseModifyDBInstanceService.initPodModifyInsParam(any())).thenReturn(modifyInsParam);
        Endpoint endpoint = new Endpoint();
        endpoint.setVport(123456);
        when(replicaSetService.getReplicaSetVpcEndpoint(any(), any())).thenReturn(endpoint);
        when(modifyInsParam.getCustins()).thenReturn(custins);
        when(modifyInsParam.getAvzInfo()).thenReturn(new AVZInfo(MultiAVZDispenseMode, "", "", "", new MultiAVZExParamDO()));
        when(modifyInsParam.getReplicaSetMeta()).thenReturn(replicaSet);
        doNothing().when(podReplicaSetResourceHelper).mockReplicaSetResource(any());
        when(commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(any(), any(), any())).thenReturn(true);
        doNothing().when(custinsParamService).updateAVZInfo(any(), any());
        ReplicaSet tmpReplicaSet = new ReplicaSet();
        tmpReplicaSet.setId(23313213L);
        when(dBaasMetaService.getDefaultClient().getReplicaSet(any(), any(), any())).thenReturn(tmpReplicaSet);
        doNothing().when(migrateDBInstanceAvzService).checkEndPointList(any(), any(), any());

        List<Replica> destReplicas = new ArrayList<>();
        Replica destReplica1 = new Replica();
        destReplica1.setId(11L);
        destReplica1.setZoneId("cn-beijing-l");
        destReplica1.setClassCode("mysql-x5");
        destReplica1.setName("destReplicaId1");
        destReplica1.setRole(Replica.RoleEnum.MASTER);
        destReplicas.add(destReplica1);
        Replica destReplica2 = new Replica();
        destReplica2.setId(12L);
        destReplica2.setZoneId("cn-beijing-l");
        destReplica2.setClassCode("mysql-x5");
        destReplica2.setName("destReplicaId2");
        destReplica2.setRole(Replica.RoleEnum.SLAVE);
        destReplicas.add(destReplica2);
        ReplicaListResult destReplicaListResult = new ReplicaListResult();
        destReplicaListResult.setItems(destReplicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, null, null, null, null, null)).thenReturn(destReplicaListResult);
        when(custinsService.getCustInstanceByCustinsId(123456789)).thenReturn(custins);
        InstanceLevelDO targetLevel = new InstanceLevelDO();
        targetLevel.setId(1);
        targetLevel.setDbType("mysql");
        targetLevel.setDbVersion("8.0");
        targetLevel.setClassCode("mysql-x5");
        targetLevel.setMaxDiskSize(100);
        targetLevel.setMaxDiskMBPS(100);
        when(instanceService.getInstanceLevelByClassCode(any(), any(), any(), any(), any())).thenReturn(targetLevel);
        doNothing().when(this.instanceIDao).createTransList(any());
        when(workFlowService.dispatchTask(any(), any(), any(), any(), any(), any())).thenReturn(1);
        doNothing().when(this.instanceIDao).updateTransTaskIdById(any(), any());

        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }


    @Test
    public void doActionRequest_NodeLevel_ThrowsRdsException() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("REQUEST_ID", "requestId");
        params.put("DBNode", "[{\"nodeId\":\"replicaId2\", \"classCode\":\"mysql-x5\", \"role\":\"master\", \"zoneId\":\"cn-beijing-l\"}]");
        params.put(ParamConstants.VSWITCH_ID, "vsw-xxxx");
        ReplicaSet replicaSet = new ReplicaSet();
        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setCategory("cluster");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setName("testReplicaSet");
        replicaSet.setId(123456789L);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        //when(replicaSetService.isMgr(null, null)).thenReturn(true);
        User user = new User();
        user.setAliUid("testUid");
        when(dBaasMetaService.getDefaultClient().getUser(null, null, false)).thenReturn(user);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, "testUid")).thenReturn(true);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(1L);
        replica.setZoneId("cn-beijing-i");
        replica.setClassCode("mysql-x5");
        replica.setName("replicaId1");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        Replica replica1 = new Replica();
        replica1.setId(2L);
        replica1.setZoneId("cn-beijing-i");
        replica1.setClassCode("mysql-x5");
        replica1.setName("replicaId2");
        replica1.setRole(Replica.RoleEnum.SLAVE);
        replicas.add(replica1);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, replicaSet.getName(), null, null, null, null)).thenReturn(replicaListResult);
        when(mysqlParamSupport.getParameterValue(params, "DBNode")).thenReturn(params.get("DBNode"));
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID)).thenReturn(params.get(ParamConstants.VSWITCH_ID));
        when(minorVersionServiceHelper.getServiceSpecTagByCustinsId(null, 123)).thenReturn("testServiceSpec");
        when(baseModifyDBInstanceService.initPodModifyInsParam(any())).thenReturn(modifyInsParam);
        Endpoint endpoint = new Endpoint();
        endpoint.setVport(123456);
        when(replicaSetService.getReplicaSetVpcEndpoint(any(), any())).thenReturn(endpoint);
        when(modifyInsParam.getCustins()).thenReturn(custins);
        when(modifyInsParam.getAvzInfo()).thenReturn(new AVZInfo(MultiAVZDispenseMode, "", "", "", new MultiAVZExParamDO()));
        when(modifyInsParam.getReplicaSetMeta()).thenReturn(replicaSet);
        doNothing().when(podReplicaSetResourceHelper).mockReplicaSetResource(any());
        when(commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(any(), any(), any())).thenReturn(true);
        doNothing().when(custinsParamService).updateAVZInfo(any(), any());
        ReplicaSet tmpReplicaSet = new ReplicaSet();
        tmpReplicaSet.setId(23313213L);
        when(dBaasMetaService.getDefaultClient().getReplicaSet(any(), any(), any())).thenReturn(tmpReplicaSet);
        //doNothing().when(migrateDBInstanceAvzService).checkEndPointList(any(), any(), any());

        List<Replica> destReplicas = new ArrayList<>();
        Replica destReplica1 = new Replica();
        destReplica1.setId(11L);
        destReplica1.setZoneId("cn-beijing-l");
        destReplica1.setClassCode("mysql-x5");
        destReplica1.setName("destReplicaId1");
        destReplica1.setRole(Replica.RoleEnum.MASTER);
        destReplicas.add(destReplica1);
        Replica destReplica2 = new Replica();
        destReplica2.setId(12L);
        destReplica2.setZoneId("cn-beijing-l");
        destReplica2.setClassCode("mysql-x5");
        destReplica2.setName("destReplicaId2");
        destReplica2.setRole(Replica.RoleEnum.SLAVE);
        destReplicas.add(destReplica2);
        ReplicaListResult destReplicaListResult = new ReplicaListResult();
        destReplicaListResult.setItems(destReplicas);
        when(dBaasMetaService.getDefaultClient()
            .listReplicasInReplicaSet(null, null, null, null, null, null)).thenReturn(destReplicaListResult);
        when(custinsService.getCustInstanceByCustinsId(123456789)).thenReturn(custins);
        InstanceLevelDO targetLevel = new InstanceLevelDO();
        targetLevel.setId(1);
        targetLevel.setDbType("mysql");
        targetLevel.setDbVersion("8.0");
        targetLevel.setClassCode("mysql-x5");
        targetLevel.setMaxDiskSize(100);
        targetLevel.setMaxDiskMBPS(100);
        when(instanceService.getInstanceLevelByClassCode(any(), any(), any(), any(), any())).thenReturn(targetLevel);
        doNothing().when(this.instanceIDao).createTransList(any());
        when(workFlowService.dispatchTask(any(), any(), any(), any(), any(), any())).thenReturn(1);
        doNothing().when(this.instanceIDao).updateTransTaskIdById(any(), any());

        Map<String, Object> result = migrateDBNodesImpl.doActionRequest(new CustInstanceDO(), params);
        Assert.assertNotNull(result);
    }

}