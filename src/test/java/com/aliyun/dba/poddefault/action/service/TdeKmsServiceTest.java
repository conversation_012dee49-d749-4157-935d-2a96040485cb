package com.aliyun.dba.poddefault.action.service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/17
 */

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.UserRoleArnRel;
import com.aliyun.apsaradb.dbaasmetaapi.model.UserRoleArnRelListResult;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse.KeyMetadata;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class TdeKmsServiceTest {

    @InjectMocks
    private TdeKmsService tdeKmsService;

    @Mock
    private KmsApi kmsApi;

    @Mock
    private DBaasMetaService dBaasMetaService;

    private DefaultApi defaultApi;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
        defaultApi = mock(DefaultApi.class);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        doNothing().when(kmsApi).tagResource(any(), any(), any(), any());
        doNothing().when(kmsApi).setDeletionProtection(any(), any(), any(), any());
    }

    @Test
    public void testCheckKeyIsAvailable_KeyEnabled_Success() throws Exception {
        DescribeKeyResponse describeKeyResponse = mock(DescribeKeyResponse.class);
        KeyMetadata keyMetadata = new KeyMetadata();
        keyMetadata.setKeyState("Enabled");
        keyMetadata.setKeySpec("Aliyun_AES_256");
        when(describeKeyResponse.getKeyMetadata()).thenReturn(keyMetadata);
        when(kmsApi.describeKey(any(), any(), any(), any())).thenReturn(describeKeyResponse);

        tdeKmsService.checkKeyIsAvailable(mock(CustInstanceDO.class), "roleArn", "keyId", "uid");
        verify(kmsApi, times(1)).describeKey(any(), any(), any(), any());
    }

    @Test
    public void testCheckKeyIsAvailable_KeyNotEnabled_ThrowsException() throws Exception {
        DescribeKeyResponse describeKeyResponse = mock(DescribeKeyResponse.class);
        KeyMetadata keyMetadata = new KeyMetadata();
        keyMetadata.setKeyState("Disabled");
        when(describeKeyResponse.getKeyMetadata()).thenReturn(keyMetadata);
        when(kmsApi.describeKey(any(), any(), any(), any())).thenReturn(describeKeyResponse);

        RdsException exception = assertThrows(RdsException.class, () -> {
            tdeKmsService.checkKeyIsAvailable(mock(CustInstanceDO.class), "roleArn", "keyId", "uid");
        });
        assertNotNull(exception);
    }

    @Test
    public void testCheckKeyIsAvailable_KeyIsNotAes_ThrowsException() throws Exception {
        DescribeKeyResponse describeKeyResponse = mock(DescribeKeyResponse.class);
        KeyMetadata keyMetadata = new KeyMetadata();
        keyMetadata.setKeyState("Enabled");
        keyMetadata.setKeySpec("RSA_2048");
        when(describeKeyResponse.getKeyMetadata()).thenReturn(keyMetadata);
        when(kmsApi.describeKey(any(), any(), any(), any())).thenReturn(describeKeyResponse);

        RdsException exception = assertThrows(RdsException.class, () -> {
            tdeKmsService.checkKeyIsAvailable(mock(CustInstanceDO.class), "roleArn", "keyId", "uid");
        });
        assertNotNull(exception);
    }

    @Test
    public void testEnsureTagExistence_TagExists_DoesNothing() throws Exception {
        Map<String, Boolean> resourceTags = new HashMap<>();
        resourceTags.put(CustinsSupport.ROLE_ARN_TAG, true);
        when(kmsApi.resourceTags(any(), any(), any(), any())).thenReturn(resourceTags);

        tdeKmsService.ensureTagExistence(mock(CustInstanceDO.class), "roleArn", "keyId", "uid");

        verify(kmsApi, never()).tagResource(any(), any(), any(), any());
    }

    @Test
    public void testEnsureTagExistence_TagDoesNotExist_CallsTagResource() throws Exception {
        Map<String, Boolean> resourceTags = new HashMap<>();
        resourceTags.put(CustinsSupport.ROLE_ARN_TAG, false);
        when(kmsApi.resourceTags(any(), any(), any(), any())).thenReturn(resourceTags);

        tdeKmsService.ensureTagExistence(mock(CustInstanceDO.class), "roleArn", "keyId", "uid");
        verify(kmsApi, times(1)).tagResource(any(), any(), any(), any());
    }

    @Test
    public void testEnsureUserRoleArn_UserRoleArnExists_DoesNothing() throws Exception {
        UserRoleArnRelListResult result = mock(UserRoleArnRelListResult.class);
        UserRoleArnRel userRoleArnRel = mock(UserRoleArnRel.class);
        when(userRoleArnRel.getRoleArn()).thenReturn("roleArn");
        when(result.getItems()).thenReturn(Collections.singletonList(userRoleArnRel));
        when(defaultApi.getUserRoleArnRel(any(), any(), any(), any(), any())).thenReturn(result);

        tdeKmsService.ensureUserRoleArn(new CustInstanceDO(), "roleArn", "uid");
        verify(defaultApi, never()).createUserRoleArnRel(any(), any(), any());
    }

    @Test
    public void testEnsureUserRoleArn_UserRoleArnDoesNotExist_CallsCreateUserRoleArnRel() throws Exception {
        UserRoleArnRelListResult result = mock(UserRoleArnRelListResult.class);
        when(result.getItems()).thenReturn(Collections.emptyList());
        when(defaultApi.getUserRoleArnRel(any(), any(), any(), any(), any())).thenReturn(result);
        when(defaultApi.createUserRoleArnRel(any(), any(), any())).thenReturn(new UserRoleArnRel());
        tdeKmsService.ensureUserRoleArn(mock(CustInstanceDO.class), "roleArn", "uid");
        verify(defaultApi, times(1)).createUserRoleArnRel(any(), any(), any());
    }

    @Test
    public void testSetDeletionProtection_Success() throws Exception {
        DescribeKeyResponse describeKeyResponse = mock(DescribeKeyResponse.class);
        KeyMetadata keyMetadata = new KeyMetadata();
        keyMetadata.setKeyState("Enabled");
        when(describeKeyResponse.getKeyMetadata()).thenReturn(keyMetadata);
        when(kmsApi.describeKey(any(), any(), any(), any())).thenReturn(describeKeyResponse);

        tdeKmsService.setDeletionProtection(mock(CustInstanceDO.class), "roleArn", "keyId", "uid");
        verify(kmsApi, times(1)).setDeletionProtection(any(), any(), any(), any());
    }

    @Test
    public void checkTdeSupported_ConfigOff_ReturnsFalse() throws ApiException {
        Config config = new Config();
        config.setValue(PodDefaultConstants.TDE_SUPPORT_SWITCH_OFF);
        ConfigListResult configListResult = new ConfigListResult();
        configListResult.setItems(Collections.singletonList(config));
        when(defaultApi.listConfigs(any(), any())).thenReturn(configListResult);

        boolean result = tdeKmsService.checkTdeSupported("requestId", "regionId");

        assertFalse(result);
    }

    @Test
    public void checkTdeSupported_ConfigOn_ReturnsTrue() throws ApiException {
        Config config = new Config();
        config.setValue(StringUtils.EMPTY);
        ConfigListResult configListResult = new ConfigListResult();
        configListResult.setItems(Collections.singletonList(config));
        when(defaultApi.listConfigs(any(), any())).thenReturn(configListResult);

        boolean result = tdeKmsService.checkTdeSupported("requestId", "regionId");

        assertTrue(result);
    }

    @Test
    public void checkTdeSupported_NoConfig_ReturnsTrue() throws ApiException {
        ConfigListResult configListResult = new ConfigListResult();
        configListResult.setItems(Collections.emptyList());
        when(defaultApi.listConfigs(any(), any())).thenReturn(configListResult);

        boolean result = tdeKmsService.checkTdeSupported("requestId", "regionId");

        assertTrue(result);
    }

    @Test
    public void checkTdeSupported_ApiException_ReturnsFalse() throws ApiException {
        when(defaultApi.listConfigs(any(), any())).thenThrow(new ApiException("API Exception"));

        boolean result = tdeKmsService.checkTdeSupported("requestId", "regionId");

        assertFalse(result);
    }
}
