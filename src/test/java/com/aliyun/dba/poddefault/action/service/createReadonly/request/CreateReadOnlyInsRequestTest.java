package com.aliyun.dba.poddefault.action.service.createReadonly.request;

import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.SCALE_MAX;

@RunWith(MockitoJUnitRunner.class)
public class CreateReadOnlyInsRequestTest {
    @Test
    public void test_setServerlessInfo() throws RdsException {
        CreateReadOnlyInsRequest createReadOnlyInsRequest = new CreateReadOnlyInsRequest();
        Map<String,String> params = new HashMap<>();
        params.put(SCALE_MAX,"128");
        createReadOnlyInsRequest.setParams(params);
        try {
            createReadOnlyInsRequest.setServerlessInfo();
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }

    }
}
