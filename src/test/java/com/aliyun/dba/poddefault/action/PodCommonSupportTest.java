package com.aliyun.dba.poddefault.action;


import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.utils.SupportUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import javax.validation.constraints.AssertFalse;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_CLOUD;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_ClOUD_SSD;
import static com.aliyun.dba.support.property.ParamConstants.OPTIMIZED_WRITES;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PodCommonSupportTest {
    @InjectMocks
    private PodCommonSupport podCommonSupport;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getOptimizedWritesInfo_NullPrimaryReplicaSet_ReturnsNull() throws Exception {
        String optimizedWritesInfo = podCommonSupport.getOptimizedWritesInfo("requestId", "dbVersion", "diskType", null);
        assertNull(optimizedWritesInfo);
    }

    @Test
    public void getOptimizedWritesInfo_BlankPrimaryReplicaSetName_ReturnsNull() throws Exception {
        ReplicaSet primaryReplicaSet = new ReplicaSet();
        primaryReplicaSet.setName("  ");
        String optimizedWritesInfo = podCommonSupport.getOptimizedWritesInfo("requestId", "dbVersion", "diskType", primaryReplicaSet);
        assertNull(optimizedWritesInfo);
    }

    @Test
    public void getOptimizedWritesInfo_OptimizedWritesTrue_ThrowsException() throws Exception {
        ReplicaSet primaryReplicaSet = new ReplicaSet();
        primaryReplicaSet.setName("replicaSetName");

        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(dBaasMetaService.getDefaultClient().listReplicaSetLabels(anyString(), anyString()))
                .thenReturn(new HashMap<String, String>() {{
                    put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, "{\"optimized_writes\":true,\"init_optimized_writes\":true}");
                }});

        try {
            podCommonSupport.getOptimizedWritesInfo("requestId", "dbVersion", "diskType", primaryReplicaSet);
            fail("Expected Exception to be thrown");
        } catch (Exception e) {
            assertEquals("Specified engine version is not valid.", e.getMessage());
        }
    }

    @Test
    public void getOptimizedWritesInfo_Success() throws Exception {
        ReplicaSet primaryReplicaSet = new ReplicaSet();
        primaryReplicaSet.setName("replicaSetName");

        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(dBaasMetaService.getDefaultClient().listReplicaSetLabels(anyString(), anyString()))
                .thenReturn(new HashMap<String, String>() {{
                    put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, "{\"optimized_writes\":true,\"init_optimized_writes\":true}");
                }});

        String result = podCommonSupport.getOptimizedWritesInfo("requestId", "8.0", ECS_CLOUD, primaryReplicaSet);
        assertEquals("{\"optimized_writes\":true,\"init_optimized_writes\":true}", result);
    }

    @Test
    public void isInitOptimizedWrites_ReturnsFalse1() {
        assertFalse(podCommonSupport.isInitOptimizedWrites(null));
    }

    @Test
    public void isInitOptimizedWrites_ReturnsFalse2() {
        assertFalse(podCommonSupport.isInitOptimizedWrites(""));
    }

    @Test
    public void isInitOptimizedWrites_Success() {
        assertTrue(podCommonSupport.isInitOptimizedWrites("{\"optimized_writes\":true,\"init_optimized_writes\":true}"));
    }

    @Test
    public void isOptimizedWrites_ReturnsFalse1() {
        assertFalse(podCommonSupport.isOptimizedWrites(null));
    }

    @Test
    public void isOptimizedWrites_ReturnsFalse2() {
        assertFalse(podCommonSupport.isOptimizedWrites(""));
    }

    @Test
    public void isOptimizedWrites_Success() {
        assertTrue(podCommonSupport.isOptimizedWrites("{\"optimized_writes\":true,\"init_optimized_writes\":true}"));
    }

    @Test
    public void getOptimizedWrites_PrimaryReplicaSet() throws Exception {
        String requestId = "testRequestId";
        Map<String, String> actionParams = new HashMap<>();
        String dbVersion = "8.0";
        String diskType = ECS_CLOUD;
        ReplicaSet primaryReplicaSet = new ReplicaSet();
        primaryReplicaSet.setName("primaryReplicaSet");
        String replicaSetName = "replicaSetName";

        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(dBaasMetaService.getDefaultClient().listReplicaSetLabels(anyString(), anyString()))
                .thenReturn(new HashMap<String, String>() {{
                    put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, "{\"optimized_writes\":true,\"init_optimized_writes\":true}");
                }});
        when(dBaasMetaService.getDefaultClient().getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn("{\"optimized_writes\":true,\"init_optimized_writes\":true}");

        podCommonSupport.getOptimizedWrites(requestId, actionParams, dbVersion, diskType, primaryReplicaSet, replicaSetName);

        podCommonSupport.getOptimizedWrites(requestId, actionParams, dbVersion, diskType, null, replicaSetName);

        try {
            podCommonSupport.getOptimizedWrites(requestId, actionParams, "5.6", diskType, null, replicaSetName);
            Assert.fail();
        } catch (Exception e) {
            assertSame("Specified engine version is not valid.", e.getMessage());
        }

        try {
            podCommonSupport.getOptimizedWrites(requestId, actionParams, dbVersion, ECS_ClOUD_SSD, null, replicaSetName);
            Assert.fail();
        } catch (Exception e) {
            assertSame("Specific disk type does not support", e.getMessage());
        }

        actionParams.put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, "{\"optimized_writes\":true,\"init_optimized_writes\":true}");
        //when(mysqlParamSupport.hasParameter(actionParams, OPTIMIZED_WRITES)).thenReturn(true);
        //when(mysqlParamSupport.getParameterValue(actionParams, OPTIMIZED_WRITES, null)).thenReturn("optimized");
        podCommonSupport.getOptimizedWrites(requestId, actionParams, dbVersion, diskType, null, replicaSetName);

        when(mysqlParamSupport.getParameterValue(actionParams, OPTIMIZED_WRITES, null)).thenReturn("optimized");
        podCommonSupport.getOptimizedWrites(requestId, actionParams, dbVersion, diskType, null, replicaSetName);


    }



}
