package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.onecs.dataobject.ClusterDO;
import com.aliyun.dba.physical.action.service.MysqlMajorVersionCheckService;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ParamConstants;
import org.checkerframework.checker.units.qual.A;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsSupport.DB_VERSION_MYSQL_57;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_VERSION_MYSQL_80;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

//@RunWith(MockitoJUnitRunner.class)
public class UpgradeDBMajorVersionImplTest {
    @InjectMocks
    private UpgradeDBMajorVersionImpl upgradeDBMajorVersion;

    @Mock
    private MysqlParameterHelper parameterHelper;

    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    protected ResourceService resourceService;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private BaseModifyDBInstanceService baseModifyDBInstanceService;

    @InjectMocks
    private AliyunInstanceDependency dependency;
    @Mock
    private ClusterService clusterService;
    @Mock
    private CustinsService custinsService;
    @Mock
    private PodModifyInsParam modifyInsParam;
    @Mock
    private MysqlMajorVersionCheckService mysqlMajorVersionCheckService;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    protected DBaasMetaService metaService;

    private DefaultApi defaultApi;

    @Mock
    private WorkFlowService workFlowService;
    @Before
    public void setUp() {

        MockitoAnnotations.initMocks(this);
        defaultApi= mock(DefaultApi.class);
        //Mockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        //when(metaService.getDefaultClient()).thenReturn(defaultApi);
    }

//    @Test
    public void doActionRequest_UpgradeDBMajorVersionImplTest_ReturnsErrorResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put("engineversion","5.7");
        params.put(ParamConstants.MAJOR_VERSION,"8.0");
        params.put("engine","mysql");
        ResourceDO resourceDo=new ResourceDO();
        resourceDo.setRealValue("true");
        when(resourceService.getResourceByResKey("UPGRADE_MAJOR_VERSION_SWITCH")).thenReturn(resourceDo);
        ReplicaSet replicaSet1=new ReplicaSet();
        replicaSet1.setName("test");
        replicaSet1.setId(1L);
        replicaSet1.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet1.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet1.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet1.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet1.setCategory(InstanceLevel.CategoryEnum.CLUSTER.getValue());
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet1);
        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        ClustersDO clusterDO=new ClustersDO();
        clusterDO.setClusterName("aliyun");
        clusterDO.setLocation("aliyun");
        when(clusterService.getClusterByCustinsId(anyLong())).thenReturn(clusterDO);
        CustInstanceDO custins=new CustInstanceDO();
        custins.setClusterName("aliyun");
        Map<String, Object> result = upgradeDBMajorVersion.doActionRequest(custins, params);
    }
    @Test
    public void doActionRequest_UpgradeDBMajorVersionImplTest_cluster_ReturnsErrorResponse() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put("engineversion","5.7");
        params.put(ParamConstants.MAJOR_VERSION,"8.0");
        params.put("engine","mysql");
        ResourceDO resourceDo=new ResourceDO();
        resourceDo.setRealValue("true");
        when(resourceService.getResourceByResKey("UPGRADE_MAJOR_VERSION_SWITCH")).thenReturn(resourceDo);
        ReplicaSet replicaSet1=new ReplicaSet();
        replicaSet1.setName("test");
        replicaSet1.setId(1L);
        replicaSet1.setCategory("cluster");
        replicaSet1.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet1.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet1.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet1.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet1.setCategory(InstanceLevel.CategoryEnum.CLUSTER.getValue());
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet1);
//        when(baseModifyDBInstanceService.initPodModifyInsParam(params)).thenReturn(modifyInsParam);
        ClustersDO clusterDO=new ClustersDO();
        clusterDO.setClusterName("aliyun");
        clusterDO.setLocation("aliyun");
//        when(clusterService.getClusterByCustinsId(anyLong())).thenReturn(clusterDO);
        CustInstanceDO custins=new CustInstanceDO();
        custins.setClusterName("aliyun");
        Map<String, Object> result = upgradeDBMajorVersion.doActionRequest(custins, params);
        assertNotNull(result);
    }
}
