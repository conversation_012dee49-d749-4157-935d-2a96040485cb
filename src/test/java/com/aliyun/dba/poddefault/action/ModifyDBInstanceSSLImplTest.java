package com.aliyun.dba.poddefault.action;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/25
 */

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.common.consts.SSLConsts;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.*;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsServiceImpl;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceServiceImpl;
import com.aliyun.dba.poddefault.action.service.TdeKmsService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.CaServerApi;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.user.dataobject.UserDO;
import com.aliyun.dba.user.service.UserServiceImpl;
import com.google.common.collect.ImmutableMap;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ActionParamsProvider.class, RequestSession.class, CheckUtils.class, CustinsParamSupport.class})
public class ModifyDBInstanceSSLImplTest {

    @InjectMocks
    private ModifyDBInstanceSSLImpl modifyDBInstanceSSLImpl;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private CaServerApi caServerApi;

    @Mock
    private CAServerApiExt caServerApiExt;

    @Mock
    private UserServiceImpl userService;

    @Mock
    private CustinsServiceImpl custinsService;

    @Mock
    private InstanceServiceImpl instanceService;

    @Mock
    private ConnAddrCustinsService connAddrCustinsService;

    @Mock
    private WorkFlowService workFlowService;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;
    @Mock
    private MysqlEngineCheckService mysqlEngineCheckService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(CheckUtils.class);
        PowerMockito.mockStatic(CustinsParamSupport.class);
        PowerMockito.when(CheckUtils.checkNullForConnectionString(any())).thenReturn("validConnectionString");
    }

    @Test
    public void doActionRequest_ForceEncryption_Success() throws Exception {
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("mysql:20241224");
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.REQUEST_ID, "requestId");
        actionParams.put(ParamConstants.UID, "uid");
        actionParams.put(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
        actionParams.put(ParamConstants.FORCE_ENCRYPTION, SSLConsts.FORCE_ENCRYPTION);
        actionParams.put(ParamConstants.CONNECTION_STRING, "validConnectionString");
        actionParams.put(ParamConstants.SSL_ENABLED, ParamConstants.SSL_VALUE_ENABLED);

        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);
        when(custInstanceDO.getConnType()).thenReturn("non-physical");
        when(custInstanceDO.getInsName()).thenReturn("instanceName");
        when(custInstanceDO.getId()).thenReturn(1);
        when(podParameterHelper.getParameterValue(ParamConstants.UID)).thenReturn("uid");
        when(podParameterHelper.getParameterValue(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN)).thenReturn(SSLConsts.CA_TYPE_ALIYUN);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(mysqlParamSupport.getAndCheckCustInstance(actionParams)).thenReturn(custInstanceDO);
        when(podParameterHelper.getParameterValue(anyString())).thenReturn("value");
        when(defaultApi.getReplicaSet(anyString(), anyString(), any())).thenReturn(mock(ReplicaSet.class));
        when(caServerApi.getCAServerConfig(anyString())).thenReturn(Collections.emptyMap());
        when(replicaSetService.isReplicaSetXDB(anyString(), anyString())).thenReturn(true);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.singletonList(mock(CustinsConnAddrDO.class)));
        when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        // 准备
        when(custinsParamService.getCustinsParam(any(), eq("minor_version"))).thenReturn(custinsParamDO);
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.SSL_ENABLED))).thenReturn("0");
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.FORCE_ENCRYPTION),any())).thenReturn("0");
        Map<String, Object> result = modifyDBInstanceSSLImpl.doActionRequest(custInstanceDO, actionParams);

        assertEquals("taskId", result.get("TaskId"));
    }


    @Test
    public void doActionRequest_MinorVersion_ErrorResponse() throws Exception {
        // 准备
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("mysql:20241129");
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.REQUEST_ID, "requestId");
        actionParams.put(ParamConstants.UID, "uid");
        actionParams.put(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
        actionParams.put(ParamConstants.FORCE_ENCRYPTION, SSLConsts.FORCE_ENCRYPTION);
        actionParams.put(ParamConstants.CONNECTION_STRING, "validConnectionString");
        actionParams.put(ParamConstants.SSL_ENABLED, ParamConstants.SSL_VALUE_ENABLED);

        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);
        when(custInstanceDO.getConnType()).thenReturn("non-physical");
        when(custInstanceDO.getInsName()).thenReturn("instanceName");
        when(custInstanceDO.getId()).thenReturn(1);
        when(podParameterHelper.getParameterValue(ParamConstants.UID)).thenReturn("uid");
        when(podParameterHelper.getParameterValue(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN)).thenReturn(SSLConsts.CA_TYPE_ALIYUN);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(mysqlParamSupport.getAndCheckCustInstance(actionParams)).thenReturn(custInstanceDO);
        when(podParameterHelper.getParameterValue(anyString())).thenReturn("value");
        when(defaultApi.getReplicaSet(anyString(), anyString(), any())).thenReturn(mock(ReplicaSet.class));
        when(caServerApi.getCAServerConfig(anyString())).thenReturn(Collections.emptyMap());
        when(replicaSetService.isReplicaSetXDB(anyString(), anyString())).thenReturn(true);
        List<Config> configs = new ArrayList();
        Config config = new Config();
        config.setKey(PodDefaultConstants.MYSQL_SSL_FORCE_ENCRYPTION_SWITCH);
        config.setValue("20241130");
        configs.add(config);
        ConfigListResult configListResult = new ConfigListResult();
        configListResult.setItems(configs);
        when(defaultApi.listConfigs(anyString(), anyString())).thenReturn(configListResult);

        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.singletonList(mock(CustinsConnAddrDO.class)));
        when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        // 准备
        when(custinsParamService.getCustinsParam(any(), eq("minor_version"))).thenReturn(custinsParamDO);
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.SSL_ENABLED))).thenReturn("1");
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.FORCE_ENCRYPTION),any())).thenReturn("1");
        Map<String, Object> result = modifyDBInstanceSSLImpl.doActionRequest(custInstanceDO, actionParams);
        Object[] errorCode = (Object[])result.get("errorCode");
        assertNotNull(errorCode);
        assertEquals(ErrorCode.MINOR_VERSION_NOT_SUPPORT_SSLENABLED.getCode(), errorCode[0]);
    }

    @Test
    public void doActionRequest_MaxScale_ErrorResponse() throws Exception {
        // 准备
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("mysql:20241131");
        Map<String, String> actionParams = new HashMap<>();
        actionParams.put(ParamConstants.REQUEST_ID, "requestId");
        actionParams.put(ParamConstants.UID, "uid");
        actionParams.put(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN);
        actionParams.put(ParamConstants.FORCE_ENCRYPTION, SSLConsts.FORCE_ENCRYPTION);
        actionParams.put(ParamConstants.CONNECTION_STRING, "validConnectionString");
        actionParams.put(ParamConstants.SSL_ENABLED, ParamConstants.SSL_VALUE_ENABLED);

        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);
        when(custInstanceDO.getConnType()).thenReturn("non-physical");
        when(custInstanceDO.getInsName()).thenReturn("instanceName");
        when(custInstanceDO.getId()).thenReturn(1);
        when(podParameterHelper.getParameterValue(ParamConstants.UID)).thenReturn("uid");
        when(podParameterHelper.getParameterValue(ParamConstants.CA_YTPE, SSLConsts.CA_TYPE_ALIYUN)).thenReturn(SSLConsts.CA_TYPE_ALIYUN);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(mysqlParamSupport.getAndCheckCustInstance(actionParams)).thenReturn(custInstanceDO);
        when(podParameterHelper.getParameterValue(anyString())).thenReturn("value");
        when(defaultApi.getReplicaSet(anyString(), anyString(), any())).thenReturn(mock(ReplicaSet.class));
        when(caServerApi.getCAServerConfig(anyString())).thenReturn(Collections.emptyMap());
        when(replicaSetService.isReplicaSetXDB(anyString(), anyString())).thenReturn(true);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.singletonList(mock(CustinsConnAddrDO.class)));
        when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        when(mysqlEngineCheckService.checkMinorVersionWithMaxScale(any())).thenReturn(false);
        List<Config> configs = new ArrayList();
        Config config = new Config();
        config.setKey(PodDefaultConstants.MYSQL_SSL_FORCE_ENCRYPTION_SWITCH);
        config.setValue("20241231");
        configs.add(config);
        ConfigListResult configListResult = new ConfigListResult();
        configListResult.setItems(configs);
        when(defaultApi.listConfigs(anyString(), anyString())).thenReturn(configListResult);
        when(custinsParamService.getCustinsParam(any(), eq("minor_version"))).thenReturn(custinsParamDO);
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.SSL_ENABLED))).thenReturn("1");
        when(CustinsParamSupport.getParameterValue(any(), eq(ParamConstants.FORCE_ENCRYPTION),any())).thenReturn("1");
        Map<String, Object> result = modifyDBInstanceSSLImpl.doActionRequest(custInstanceDO, actionParams);
        Object[] errorCode = (Object[])result.get("errorCode");
        assertNotNull(errorCode);
        assertEquals(ErrorCode.UNSUPPORTED_MAXSCALE_MINOR_VERSION.getCode(), errorCode[0]);
    }
}
