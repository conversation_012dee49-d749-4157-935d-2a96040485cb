package com.aliyun.dba.poddefault.action;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/17
 */

import java.util.HashMap;
import java.util.Map;

import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class RefreshKmsConfigImplTest {

    @InjectMocks
    private RefreshKmsConfigImpl refreshKmsConfigImpl;

    @Mock
    private CustinsParamService custinsParamService;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Before
    public void setUp() {
        // Mock the static method of RequestSession
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("requestId");
    }

    @Test
    public void doActionRequest_SuccessfulExecution_ReturnsExpectedData() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("insName");
        custins.setDbVersion("5.7");
        custins.setDbType("mysql");

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap(), eq(null))).thenReturn(custins);
        when(custinsParamService.getCustinsParam(any(), any())).thenReturn(createMockCustinsParamDO("1"));
        when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), any())).thenReturn("taskId");

        // Act
        Map<String, Object> result = refreshKmsConfigImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    @Test
    public void doActionRequest_UnsupportedDBType_ThrowsRdsException() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("insName");
        custins.setDbVersion("5.6");
        custins.setDbType("mysql");

        when(mysqlParamSupport.getAndCheckCustInstance(any(), any())).thenReturn(custins);
        // Act
        Map<String, Object> result = refreshKmsConfigImpl.doActionRequest(custins, new HashMap<>());

    }

    @Test
    public void doActionRequest_TdeNotEnabled_ThrowsRdsException() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("insName");
        custins.setDbVersion("5.7");
        custins.setDbType("mysql");

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap(), eq(null))).thenReturn(custins);
        when(custinsParamService.getCustinsParam(any(), any())).thenReturn(createMockCustinsParamDO("0"));

        // Act
        Map<String, Object> result = refreshKmsConfigImpl.doActionRequest(custins, new HashMap<>());
    }

    @Test
    public void doActionRequest_InternalException_ReturnsErrorResponse() throws Exception {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("insName");
        custins.setDbVersion("5.7");
        custins.setDbType("mysql");

        when(mysqlParamSupport.getAndCheckCustInstance(anyMap(), eq(null))).thenReturn(custins);
        when(custinsParamService.getCustinsParam(any(), any())).thenReturn(createMockCustinsParamDO("1"));
        when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), eq(WorkFlowService.TASK_PRIORITY_VIP))).thenThrow(
            new RuntimeException());

        // Act
        Map<String, Object> result = refreshKmsConfigImpl.doActionRequest(custins, new HashMap<>());

        // Assert
        assertNotNull(result);
    }

    private CustinsParamDO createMockCustinsParamDO(String value) {
        CustinsParamDO param = new CustinsParamDO();
        param.setValue(value);
        return param;
    }
}
