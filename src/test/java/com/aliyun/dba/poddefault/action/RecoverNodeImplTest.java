package com.aliyun.dba.poddefault.action;


import java.util.*;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet.LockModeEnum.NOLOCK;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_RESTART_RESOURCE_NODE;
import static org.mockito.Mockito.*;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.support.service.KmsService;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(MockitoJUnitRunner.class)
public class RecoverNodeImplTest {
    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private KmsService kmsService;

    @Mock
    private PodParameterHelper podParameterHelper;

    @InjectMocks
    private RecoverNodeImpl recoverNodeImpl;

    @Mock
    private DefaultApi defaultApi;

    @Before
    public void setUp() {
    }

    @Test
    public void doActionRequest_SuccessfulExecution_ReturnsDataMap() throws Exception {
        // Setup
        Map<String, String> params = new HashMap<>();
        params.put("replicaId", "123");
        params.put("recoverMethod", "TASK_RESTART_MYSQLD");
        params.put("wait_node_timeout", "300");
        params.put("uid", "12345");

        CustInstanceDO custInstanceDO = new CustInstanceDO();
        ReplicaSet replicaSet = new ReplicaSet();

        when(mysqlParamSupport.getDBInstanceName(params)).thenReturn("dbInstanceName");
        when(mysqlParamSupport.getAndCheckCustInstance(params)).thenReturn(custInstanceDO);
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setRole(Replica.RoleEnum.SLAVE);
        replica.setZoneId("zoneId");
        replica.setId(123L);
        replicas.add(replica);
        replicaListResult.setItems(replicas);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);

        // Execute
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        replicaSet.setLockMode(NOLOCK);
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, null)).thenReturn(true);
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        params.put("recoverMethod", TASK_RESTART_RESOURCE_NODE);
        when(mysqlParamSupport.getParameterValue(params, "recoverMethod")).thenReturn(TASK_RESTART_RESOURCE_NODE);
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        when(podParameterHelper.isSingleTenant(replicaSet)).thenReturn(false);
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        when(podParameterHelper.isSingleTenant(replicaSet)).thenReturn(true);
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        when(mysqlParamSupport.hasParameter(params, "replicaId")).thenReturn(true);
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        replicaSet.setCategory("cluster");
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

//        when(workFlowService.isTaskExist(anyString(), anyString())).thenReturn(false);
        recoverNodeImpl.doActionRequest(custInstanceDO, params);

        when(dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(null, "dbInstanceName", null, null, null, null)).thenReturn(replicaListResult);
        when(mysqlParamSupport.getParameterValue(params, "replicaId")).thenReturn("123");
        Map<String, Object> result = recoverNodeImpl.doActionRequest(custInstanceDO, params);

        // Verify
        assertEquals(3, result.size());
    }
}
