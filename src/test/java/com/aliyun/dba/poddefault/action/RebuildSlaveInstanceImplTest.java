package com.aliyun.dba.poddefault.action;

import java.util.*;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.support.property.ParamConstants;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RebuildSlaveInstanceImplTest {

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private BakService bakService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private CustinsService custinsService;

    @InjectMocks
    private RebuildSlaveInstanceImpl rebuildSlaveInstanceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testRebuildSlaveForAliyun_InstanceNotFound() throws Exception {
        RebuildSlaveInstanceImpl rebuildSlaveInstanceImplSpy = PowerMockito.spy(rebuildSlaveInstanceImpl);
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        String zoneId = "cn_test";
        String rebuidType = "rebuild";
        boolean backupOnMaster = true;
        User user = new User();
        Long instanceId = 123434L;
        String dbInstanceName = "test";
        ReplicaSet replicaSetMeta = new ReplicaSet();
        replicaSetMeta.setId(2334455L);
        ReplicaSetResource replicaSetResource = new ReplicaSetResource();
        ReplicaListResult listReplicasInReplicaSet = new ReplicaListResult();
        Replica replica = new Replica();
        replica.setId(instanceId);
        List<Replica> replicaList = new ArrayList<>();
        replicaList.add(replica);
        listReplicasInReplicaSet.setItems(replicaList);
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setPrimaryCustinsId(2334455);
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
        List<CustInstanceDO> mirrorIns = new ArrayList<>();
        BakhistoryDO bakHistory = null;


        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.BACKUP_SET_ID)).thenReturn("123231");
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(bakService.getBakhistoryByBackupSetId(replicaSetMeta.getId().intValue(), Long.valueOf("123231"))).thenReturn(bakHistory);

        when(dBaasMetaService.getDefaultClient().getReplicaSetLabel("requestId", "mirrorIns", PodDefaultConstants.REBUILD_REPLICA)).thenReturn("123434");
        CustInstanceDO ins = new CustInstanceDO();
        ins.setInsName("mirrorIns");
        mirrorIns.add(ins);
        CustInstanceQuery custInstanceQuery2 = new CustInstanceQuery();
        custInstanceQuery2.setPrimaryCustinsId(2334455);
        custInstanceQuery2.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery2.setIsTmp(CUSTINS_INSTYPE_TMP);
        when(custinsService.getCustIns(any())).thenReturn(mirrorIns);

        try {
            Whitebox.invokeMethod(rebuildSlaveInstanceImplSpy, "rebuildSlaveForAliyun", params, user, instanceId, dbInstanceName, zoneId, replicaSetMeta, replicaSetResource, listReplicasInReplicaSet, rebuidType, backupOnMaster);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        when(dBaasMetaService.getDefaultClient().getReplicaSetLabel("requestId", "mirrorIns", PodDefaultConstants.REBUILD_REPLICA)).thenReturn("233456");

        try {
            Whitebox.invokeMethod(rebuildSlaveInstanceImplSpy, "rebuildSlaveForAliyun", params, user, instanceId, dbInstanceName, zoneId, replicaSetMeta, replicaSetResource, listReplicasInReplicaSet, rebuidType, backupOnMaster);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        // Assert
        verify(dBaasMetaService.getDefaultClient(), times(2)).getReplicaSetLabel("requestId", "mirrorIns", PodDefaultConstants.REBUILD_REPLICA);

    }
}
