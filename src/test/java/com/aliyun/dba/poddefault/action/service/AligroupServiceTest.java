package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.UUID;

import static com.aliyun.dba.base.support.MySQLParamConstants.ALIGROU_DHG_PARTERN;
import static org.junit.Assert.*;

public class AligroupServiceTest {

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void isTddlClusterNeedAllocateDedicatedResourceGroup() {
        AligroupService aligroupService = new AligroupService();
        try {
            aligroupService.isTddlClusterNeedAllocateDedicatedResourceGroup(UUID.randomUUID().toString(),"TDDLCLUSTNAME");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void getPromotionClassCode() {
        AligroupService aligroupService = new AligroupService();
        try {
            aligroupService.getPromotionClassCode(UUID.randomUUID().toString(),"TDDLCLUSTNAME", "CLASSCODE");
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }

    @Test
    public void isAligroupDHG() {
        AligroupService aligroupService = new AligroupService();
        try {
            aligroupService.isAligroupDHG(UUID.randomUUID().toString(),ALIGROU_DHG_PARTERN);
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }

    @Test
    public void isFailBackRouteXDBProvider() {
        AligroupService aligroupService = new AligroupService();
        try {
            aligroupService.isFailBackRouteXDBProvider("TEST_ACTION");
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }

    @Test
    public void getAligroupLoggerClassCode() {
        AligroupService aligroupService = new AligroupService();
        try {
            aligroupService.getAligroupLoggerClassCode("TEST_ACTION");
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }

    @Test
    public void isXdbMultiWriteEngine() {
        AligroupService aligroupService = new AligroupService();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setService("test_service");
        replicaSet.serviceVersion("test_version");
        replicaSet.setClassCode("test_class_code");
        try {
            aligroupService.isXdbMultiWriteEngine(UUID.randomUUID().toString(),replicaSet);
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }
}