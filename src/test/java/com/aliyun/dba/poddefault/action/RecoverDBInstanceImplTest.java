package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.poddefault.action.support.RundPodSupport;
import com.aliyun.dba.service.MySQLServiceImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RecoverDBInstanceImplTest {
    @InjectMocks
    private RecoverDBInstanceImpl recoverDBInstance;
    @Mock
    private MysqlParamSupport paramSupport;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private BakService bakService;
    @Mock
    private RundPodSupport rundPodSupport;
    @Mock
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Mock
    private MySQLServiceImpl mySQLService;

    @Test
    public void test_doRecoverDBInstance() throws Exception {
        Map<String,String> params=new HashMap<>();
        CustInstanceDO custInstanceDO=new CustInstanceDO();
        JSONObject param1=new JSONObject();
        ReplicaSet replicaSet=new ReplicaSet();
        replicaSet.setId(1L);
        replicaSet.setCategory("basic");
        params.put("restoretype","0");
        params.put("backupsetid","12345");
        BakhistoryDO bakhistoryDO=new BakhistoryDO();
        when(bakService.getBakhistoryByBackupSetId(any(),any())).thenReturn(bakhistoryDO);
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        try {
            Object o = recoverDBInstance.doRecoverDBInstance(params, param1, custInstanceDO);
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }

    }
}
