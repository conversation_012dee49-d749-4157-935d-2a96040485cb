package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InlineResponse200;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.utils.DateUTCFormat;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CreateOperatorPermissionImplTest {

    @Mock
    private MysqlParameterHelper parameterHelper;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @InjectMocks
    private CreateOperatorPermissionImpl createOperatorPermissionImpl;

    private DefaultApi defaultApi;

    @Before
    public void setUp() {
        createOperatorPermissionImpl = new CreateOperatorPermissionImpl();
        createOperatorPermissionImpl.parameterHelper = parameterHelper;
        createOperatorPermissionImpl.replicaSetService = replicaSetService;
        createOperatorPermissionImpl.dBaasMetaService = dBaasMetaService;
        defaultApi= mock(DefaultApi.class);
        Mockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test
    public void testDoActionRequest() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "testRequestId");
        params.put(ParamConstants.USER_ID, "test001");
        params.put(ParamConstants.UID, "111");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");

        when(parameterHelper.getParameterValue(ParamConstants.REQUEST_ID)).thenReturn("testRequestId");
        when(parameterHelper.getParameterValue(ParamConstants.OPERATOR_TYPE)).thenReturn("Data");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(parameterHelper.hasParameter(ParamConstants.EXPIRED_TIME)).thenReturn(false);
        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_PRIVILEGE, AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()))
                .thenReturn(String.valueOf(AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()));

        InlineResponse200 response = new InlineResponse200();
        response.setRequestId("testRequestId");
        response.setMessage("Success");
        when(dBaasMetaService.getDefaultClient().updateReplicaSetLabels(anyString(), anyString(), anyMap())).thenReturn(response);
        Map<String, Object> result = createOperatorPermissionImpl.doActionRequest(custInstanceDO, params);
        assertEquals(1L, result.get(ParamConstants.DB_INSTANCE_ID));
        assertEquals("testReplicaSetName", result.get(ParamConstants.DB_INSTANCE_NAME));
        assertNotNull(result.get(ParamConstants.EXPIRED_TIME));
    }

    @Test
    public void testDoActionRequestWithInvalidStatus() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "testRequestId");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.DELETING);

        when(parameterHelper.getParameterValue(ParamConstants.REQUEST_ID)).thenReturn("testRequestId");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);

        Map<String, Object> result = createOperatorPermissionImpl.doActionRequest(custInstanceDO, params);
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS.getCode(), errorArray[0]);
    }

    @Test
    public void testDoActionRequestWithInvalidExpiredTime() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "testRequestId");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);

        when(parameterHelper.getParameterValue(ParamConstants.REQUEST_ID)).thenReturn("testRequestId");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(parameterHelper.hasParameter(ParamConstants.EXPIRED_TIME)).thenReturn(true);
        when(parameterHelper.getAndCheckTimeByParam(eq(ParamConstants.EXPIRED_TIME), eq(DateUTCFormat.SECOND_UTC_FORMAT), eq(ErrorCode.INVALID_EXPIREDTIME)))
                .thenReturn(new Date(System.currentTimeMillis() - 10000));

        Map<String, Object> result = createOperatorPermissionImpl.doActionRequest(custInstanceDO, params);
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.INVALID_EXPIREDTIME.getCode(),errorArray[0]);
    }

    @Test
    public void testDoActionRequestWithMissingOperatorType() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "testRequestId");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);

        when(parameterHelper.getParameterValue(ParamConstants.REQUEST_ID)).thenReturn("testRequestId");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(parameterHelper.getParameterValue(ParamConstants.OPERATOR_TYPE)).thenReturn(null);
        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_PRIVILEGE, AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()))
                .thenReturn(String.valueOf(AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()));
        Map<String, Object> result = createOperatorPermissionImpl.doActionRequest(custInstanceDO, params);
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.MISSING_OPERATOR_TYPE.getCode(), errorArray[0]);
    }

    @Test
    public void testDoActionRequestWithInvalidOperatorType() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "testRequestId");
        params.put(ParamConstants.OPERATOR_TYPE, "Invalid");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);

        when(parameterHelper.getParameterValue(ParamConstants.REQUEST_ID)).thenReturn("testRequestId");
        when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(replicaSet);
        when(parameterHelper.getParameterValue(ParamConstants.ACCOUNT_PRIVILEGE, AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()))
                .thenReturn(String.valueOf(AccountPriviledgeType.PRIVILEDGE_OUTTER_USER_GRANT.getValue()));
        Map<String, Object> result = createOperatorPermissionImpl.doActionRequest(custInstanceDO, params);
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.INVALID_OPERATOR_TYPE.getCode(), errorArray[0]);
    }

    /*@Test
    public void testGetAndCheckTimeByParam() throws Exception {
        String dateParamName = "testDateParam";
        DateUTCFormat format = DateUTCFormat.SECOND_UTC_FORMAT;
        ErrorCode errorCode = ErrorCode.INVALID_EXPIREDTIME;

        String dateStr = "2023-10-10T10:10:10Z";
        Date expectedDate = new Date();

        when(parameterHelper.getParameterValue(dateParamName)).thenReturn(dateStr);
        when(parameterHelper.getAndCheckTimeByDateStr(dateStr, format, errorCode)).thenReturn(expectedDate);

        Date result = parameterHelper.getAndCheckTimeByParam(dateParamName, format, errorCode);

        assertEquals(expectedDate, result);
    }*/

    /*@Test
    public void testGetAndCheckTimeByDateStr() throws Exception {
        String dateStr = "2023-10-10T10:10:10Z";
        DateUTCFormat format = DateUTCFormat.SECOND_UTC_FORMAT;
        ErrorCode errorCode = ErrorCode.INVALID_EXPIREDTIME;
        int secondsDiff = 0;

        Date expectedDate = new Date();

        when(parameterHelper.getMetaDBTimeZoneDiffSeconds()).thenReturn(secondsDiff);
        when(DateSupport.str2second_utc(dateStr)).thenReturn(expectedDate);

        Date result = parameterHelper.getAndCheckTimeByDateStr(dateStr, format, errorCode);

        assertEquals(expectedDate, result);
    }*/
}