package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Config;
import com.aliyun.apsaradb.dbaasmetaapi.model.ConfigListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodCreateInsParam;
import com.aliyun.dba.support.property.ParamConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.SCALE_MIN;
import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.SCALE_MAX;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PodCreateInsParamTest {

    @Mock
    private AliyunInstanceDependency dependency;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private DBaasMetaService dbaasMetaService;
    @Mock
    private DefaultApi api;

    @InjectMocks
    private PodCreateInsParam podCreateInsParam;

    private Map<String, String> params;

    private Map<String, Boolean> initParams;

    private InstanceLevel instanceLevel;

    @Mock
    private PodCommonSupport podCommonSupport;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        params = new HashMap<>();
        initParams = new HashMap<>();
        instanceLevel= new InstanceLevel();
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        when(dependency.getDBaasMetaService()).thenReturn(dbaasMetaService);
        when(dbaasMetaService.getDefaultClient()).thenReturn(api);
    }

    @Test
    public void testSetInitOptimizedWrites_Success() throws Exception {
        // Arrange
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put("dbVersion", "8.0");
        params.put("diskType", "cloud_essd");
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        instanceLevel.setExtraInfo("{\"instructionSetArch\":\"arm\"}");
        when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        //when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        //when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);

        // Act
        podCreateInsParam = new PodCreateInsParam(dependency, params);
        initParams.put("dbVersion", true);
        initParams.put("diskType", true);
        podCreateInsParam.setInitParams(initParams);
        podCreateInsParam.setInstanceLevel(instanceLevel);
        podCreateInsParam.setDbType("mysql");
        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config config = new Config();
        config.setValue("true");
        items.add(config);
        configListResult.setItems(items);
        //when(defaultApi.listConfigs(null, "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT")).thenReturn(configListResult);
        PodCreateInsParam result = podCreateInsParam.setInitOptimizedWrites();
        boolean expected = false;
        boolean actual = result.isInitOptimizedWrites();
        Assert.assertEquals(expected, actual);
    }
    @Test
    public void testSetServerlessInfo_exception() throws Exception {
        // Arrange

        // Act
        podCreateInsParam = new PodCreateInsParam(dependency, params);
        params.put(SCALE_MIN,"0.5");
        params.put(SCALE_MAX,"128");
        try {
            podCreateInsParam.setServerlessInfo();
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }

    }

    @Test
    public void testSetExternalReplication() throws Exception {
        podCreateInsParam = new PodCreateInsParam(dependency, params);
        params.put("externalreplication", "true");

        params.put("dbVersion", "8.0");
        params.put("dbType", "sqlserver");
        params.put("dbInstanceClass", "rds.mysql.s3.large");
        when(mysqlParamSupport.getAndCheckDBType(params, null)).thenReturn("sqlserver");
        when(mysqlParamSupport.getAndCheckDBVersion(anyMap(), anyString(), anyBoolean())).thenReturn("8.0");
        when(api.getInstanceLevel(any(), anyString(), anyString(), any(), any())).thenReturn(new InstanceLevel(){{
            setCategory(CategoryEnum.STANDARD);
            setClassCode("rds.mysql.s3.large");
        }});
        try {
            podCreateInsParam
                    .setDBType()
                    .setDBVersion()
                    .setClassCode()
                    .setInstanceLevel()
                    .setExternalReplication();
            Assert.fail();
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
        params.put("dbType", "mysql");
        when(mysqlParamSupport.getAndCheckDBType(params, null)).thenReturn("mysql");
        try {
            podCreateInsParam
                    .setDBType()
                    .setDBVersion()
                    .setClassCode()
                    .setInstanceLevel()
                    .setExternalReplication();
            Assert.fail();
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
        params.put("dbVersion", "5.7");
        when(mysqlParamSupport.getAndCheckDBVersion(anyMap(), anyString(), anyBoolean())).thenReturn("5.7");
        try {
            podCreateInsParam
                    .setDBType()
                    .setDBVersion()
                    .setClassCode()
                    .setInstanceLevel()
                    .setExternalReplication();
            Assert.fail();
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
        params.put("dbInstanceClass", "mysql.n2.xlarge.1");
        when(api.getInstanceLevel(any(), anyString(), anyString(), any(), any())).thenReturn(new InstanceLevel(){{
            setCategory(CategoryEnum.BASIC);
            setClassCode("mysql.n2.xlarge.1");
        }});
        podCreateInsParam
                .setDBType()
                .setDBVersion()
                .setClassCode()
                .setInstanceLevel()
                .setExternalReplication();
        Assert.assertTrue(true);
    }
}
