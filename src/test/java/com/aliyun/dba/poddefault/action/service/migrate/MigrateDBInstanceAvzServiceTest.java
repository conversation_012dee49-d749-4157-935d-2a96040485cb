package com.aliyun.dba.poddefault.action.service.migrate;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
@RunWith(MockitoJUnitRunner.class)
public class MigrateDBInstanceAvzServiceTest {
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private DefaultApi defaultApi;
    @InjectMocks
    private MigrateDBInstanceAvzService migrateDBInstanceAvzService;
    @Before
    public void setUp() throws ApiException {
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }
    @Test
    public void addLabelForTmpReplicaSetIfVbm_IsVbmTrue_ShouldUpdateLabels() throws ApiException {
        PodModifyInsParam modifyInsParam = mock(PodModifyInsParam.class);
        when(modifyInsParam.isVbm()).thenReturn(true);
        String requestId = "requestId";
        String tmpReplicaSetName = "tmpReplicaSetName";
        Map<String, String> expectedLabels = new HashMap<>();
        expectedLabels.put(PodDefaultConstants.VBM_CUSTINS_LABEL_KEY, PodDefaultConstants.VBM_CUSTINS_LABEL_VALUE);
        migrateDBInstanceAvzService.addLabelForTmpReplicaSetIfVbm(modifyInsParam, requestId, tmpReplicaSetName);
        verify(defaultApi, times(1)).updateReplicaSetLabels(requestId, tmpReplicaSetName, expectedLabels);
    }
    @Test
    public void addLabelForTmpReplicaSetIfVbm_IsVbmFalse_ShouldNotUpdateLabels() throws ApiException {
        PodModifyInsParam modifyInsParam = mock(PodModifyInsParam.class);
        when(modifyInsParam.isVbm()).thenReturn(false);
        String requestId = "requestId";
        String tmpReplicaSetName = "tmpReplicaSetName";
        migrateDBInstanceAvzService.addLabelForTmpReplicaSetIfVbm(modifyInsParam, requestId, tmpReplicaSetName);
        verify(defaultApi, never()).updateReplicaSetLabels(anyString(), anyString(), anyMap());
    }
}