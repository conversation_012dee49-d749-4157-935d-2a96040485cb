package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.support.api.KmsApi;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ModifyDBInstanceConfigImplTest {
    @InjectMocks
    private ModifyDBInstanceConfigImpl modifyDBInstanceConfig;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private PodParameterHelper podParameterHelper;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    protected KmsService kmsService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private KmsApi kmsApi;

    @Mock
    private MysqlEncryptionService mysqlEncryptionService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);

        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custInstanceDO);
        when(mysqlParamSupport.getUserId(any())).thenReturn(1);

        ReplicaSet replicaSet = mock(ReplicaSet.class);
        when(replicaSet.getResourceGroupName()).thenReturn("clusterName");
        when(replicaSet.getName()).thenReturn("rm-abcdtest");
        when(replicaSet.getUserId()).thenReturn("1aaa");
        when(replicaSet.getCategory()).thenReturn("standard");

        User user = mock(User.class);
        ReplicaSetListResult replicaSetListResult = mock(ReplicaSetListResult.class);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(defaultApi.getReplicaSet(any(), any(), any())).thenReturn(replicaSet);
        when(defaultApi.getReplicaSetLabel(any(), any(), any())).thenReturn("123456-123456");
        when(defaultApi.listReplicaSetSubIns(any(), any(), any())).thenReturn(replicaSetListResult);
        when(defaultApi.getUser(any(), any(), any())).thenReturn(user);
        when(defaultApi.updateReplicaSetStatus(any(), any(), any())).thenReturn("ok");

        when(kmsService.getUserRoleArn(any())).thenReturn("roleArn");
        when(mysqlEncryptionService.getServiceKey(any(), any(), any())).thenReturn("default_key");

        Map<String, Object> switchInfoMap = new HashMap<>();
        when(podParameterHelper.getSwitchInfo(any())).thenReturn(switchInfoMap);

//        when(workFlowService.dispatchTask(any(ReplicaSet.class), anyString(), anyString(), anyInt())).thenReturn("1.0");
    }

    @Test
    public void doActionRequest_UnsupportedDiskType_ReturnsErrorResponse() throws Exception {
        String requestId = "requestId";
        String configName = "encryptionKey";
        String configValue = "serviceKey";
        String targetKeyId = "123456-123456";
        String diskType = "cloud_ssd";
        boolean isSingleTenant = true;

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, requestId);

        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_NAME)).thenReturn(configName);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_VALUE)).thenReturn(configValue);

        when(replicaSetService.getReplicaSetStorageType(any(), any())).thenReturn(diskType);
        when(replicaSetService.isCloudDiskSingleTenant(any(), any())).thenReturn(isSingleTenant);

        when(mysqlEncryptionService.getServiceKey(any(), any(), any())).thenReturn(targetKeyId);

        Map<String, Object> result = modifyDBInstanceConfig.doActionRequest(null, params);
        assertNotNull(result.get("errorCode"));
    }

    @Test
    public void doActionRequest_UnsupportedKeyId_ReturnsErrorResponse() throws Exception {
        String requestId = "requestId";
        String configName = "encryptionKey";
        String configValue = "serviceKey";
        String targetKeyId = "123456-123456";
        String diskType = "cloud_essd";
        boolean isSingleTenant = true;

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, requestId);

        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_NAME)).thenReturn(configName);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_VALUE)).thenReturn(configValue);

        when(replicaSetService.getReplicaSetStorageType(any(), any())).thenReturn(diskType);
        when(replicaSetService.isCloudDiskSingleTenant(any(), any())).thenReturn(isSingleTenant);

        when(mysqlEncryptionService.getServiceKey(any(), any(), any())).thenReturn(targetKeyId);

        Map<String, Object> result = modifyDBInstanceConfig.doActionRequest(null, params);
        Object[] objects = (Object[])result.get("errorCode");
        System.out.println(objects[1].toString());
        assertNotNull(result.get("errorCode"));
    }

    @Test
    public void doActionRequest_ShareChangeServiceKey() throws Exception {
        String requestId = "requestId";
        String configName = "encryptionKey";
        String configValue = "serviceKey";
        String diskType = "cloud_essd";
        boolean isSingleTenant = false;

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, requestId);

        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_NAME)).thenReturn(configName);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_VALUE)).thenReturn(configValue);

        when(replicaSetService.getReplicaSetStorageType(any(), any())).thenReturn(diskType);
        when(replicaSetService.isCloudDiskSingleTenant(any(), any())).thenReturn(isSingleTenant);

        when(mysqlEncryptionService.getServiceKey(any(), any(), any())).thenReturn(configValue);

        DescribeKeyResponse describeKeyResponse = mock(DescribeKeyResponse.class);
        when(kmsApi.describeKey(any(), any(), any(), any())).thenReturn(describeKeyResponse);

        DescribeKeyResponse.KeyMetadata keyMetadata = mock(DescribeKeyResponse.KeyMetadata.class);
        when(describeKeyResponse.getKeyMetadata()).thenReturn(keyMetadata);
        when(keyMetadata.getKeySpec()).thenReturn("Aliyun_AES_256");
        when(keyMetadata.getKeyState()).thenReturn("Enable");

        Map<String, Object> result = modifyDBInstanceConfig.doActionRequest(null, params);
        assertNotNull(result.get("ConfigName"));
    }

    @Test
    public void doActionRequest_ExclusiveChangeByokKey() throws Exception {
        String requestId = "requestId";
        String configName = "encryptionKey";
        String configValue = "abcd-abcd";
        String diskType = "cloud_essd";
        boolean isSingleTenant = true;

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, requestId);

        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_NAME)).thenReturn(configName);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_VALUE)).thenReturn(configValue);

        when(replicaSetService.getReplicaSetStorageType(any(), any())).thenReturn(diskType);
        when(replicaSetService.isCloudDiskSingleTenant(any(), any())).thenReturn(isSingleTenant);


        DescribeKeyResponse describeKeyResponse = mock(DescribeKeyResponse.class);
        when(kmsApi.describeKey(any(), any(), any(), any())).thenReturn(describeKeyResponse);

        DescribeKeyResponse.KeyMetadata keyMetadata = mock(DescribeKeyResponse.KeyMetadata.class);
        when(describeKeyResponse.getKeyMetadata()).thenReturn(keyMetadata);
        when(keyMetadata.getKeySpec()).thenReturn("Aliyun_AES_256");
        when(keyMetadata.getKeyState()).thenReturn("Enable");

        when(kmsService.isKeyEnable(any(), any(), any(), any())).thenReturn(true);
        Map<String, Object> result = modifyDBInstanceConfig.doActionRequest(null, params);
        assertNotNull(result.get("ConfigName"));
    }

    @Test
    public void doActionRequest_ExclusiveChangeServiceKey() throws Exception {
        String requestId = "requestId";
        String configName = "encryptionKey";
        String configValue = "serviceKey";
        String diskType = "cloud_essd";
        boolean isSingleTenant = true;

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, requestId);

        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_NAME)).thenReturn(configName);
        when(podParameterHelper.getParameterValue(PodDefaultConstants.CONFIG_VALUE)).thenReturn(configValue);

        when(replicaSetService.getReplicaSetStorageType(any(), any())).thenReturn(diskType);
        when(replicaSetService.isCloudDiskSingleTenant(any(), any())).thenReturn(isSingleTenant);

        DescribeKeyResponse describeKeyResponse = mock(DescribeKeyResponse.class);
        when(kmsApi.describeKey(any(), any(), any(), any())).thenReturn(describeKeyResponse);

        DescribeKeyResponse.KeyMetadata keyMetadata = mock(DescribeKeyResponse.KeyMetadata.class);
        when(describeKeyResponse.getKeyMetadata()).thenReturn(keyMetadata);
        when(keyMetadata.getKeySpec()).thenReturn("Aliyun_AES_256");
        when(keyMetadata.getKeyState()).thenReturn("Enable");
        when(kmsService.isKeyEnable(any(), any(), any(), any())).thenReturn(true);

        Map<String, Object> result = modifyDBInstanceConfig.doActionRequest(null, params);
        assertNotNull(result.get("ConfigName"));
    }

    @Test
    public void doActionRequest_ClusterMgr() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        when(replicaSetService.isMgr(Mockito.any(), Mockito.any())).thenReturn(true);
        CustInstanceDO custInstanceDO = mock(CustInstanceDO.class);
        when(custInstanceDO.isActive()).thenReturn(true);
        when(custInstanceDO.isLock()).thenReturn(false);
        when(mysqlParamSupport.getAndCheckCustInstance(any())).thenReturn(custInstanceDO);
        ReplicaSet replicaSetMeta = mock(ReplicaSet.class);
        when(replicaSetMeta.getCategory()).thenReturn("cluster");
        when(dBaasMetaService.getDefaultClient().getReplicaSet(any(), any(), any())).thenReturn(replicaSetMeta);
        Map<String, Object> result = modifyDBInstanceConfig.doActionRequest(null, params);
        assertNotNull(result.get("errorCode"));
    }

}
