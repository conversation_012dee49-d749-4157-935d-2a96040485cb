package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.service.modify.ModifyDBInstanceOptimizedWritesService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.aliyun.dba.custins.support.CustinsParamSupport.OPTIMIZED_WRITES_INFO;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_CLOUD;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_MODIFY_OPTIMIZED_WRITES;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ModifyDBInstanceOptimizedWritesServiceTest {

    @InjectMocks
    private ModifyDBInstanceOptimizedWritesService modifyDBInstanceOptimizedWritesService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private PodCommonSupport podCommonSupport;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private BaseModifyDBInstanceService baseModifyDBInstanceService;

    @Mock
    private AliyunInstanceDependency dependency;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private WorkFlowService workFlowService;

    @Test
    public void doActionRequest_ReadInstanceOptimizedWritesEnabled_ShouldThrowRdsException() throws RdsException {
        // Arrange
        String primaryOptimizedWritesInfo = "{\"optimizedWrites\":true}";
        boolean targetOptimizedWrites = false;
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");

        // Act & Assert
        Map<String, Object> result = modifyDBInstanceOptimizedWritesService.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
        Object[] errorInfo = (Object[])result.get("errorCode");
        Assert.assertEquals("InternalFailure", errorInfo[1]);
        Assert.assertEquals("The request processing has failed due to some unknown error, exception or failure.", errorInfo[2]);
    }

    @Test
    public void doActionRequest_ShouldThrowRdsException() throws Exception {
        // Arrange
        String primaryOptimizedWritesInfo = "{\"optimizedWrites\":false}";
        boolean targetOptimizedWrites = true;
        HashMap<String, String> params = new HashMap<String, String>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        ReplicaSet replicaSetMeta = Mockito.mock(ReplicaSet.class);
        replicaSetMeta.setPrimaryInsName("testName");

        // Act
        Map<String, Object> result = modifyDBInstanceOptimizedWritesService.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
        Object[] errorInfo = (Object[])result.get("errorCode");
        Assert.assertEquals("InternalFailure", errorInfo[1]);
        Assert.assertEquals("The request processing has failed due to some unknown error, exception or failure.", errorInfo[2]);
    }

    @Test
    public void doActionRequest_Success() throws Exception {
        // Arrange
        String primaryOptimizedWritesInfo = "{\"optimizedWrites\":false}";
        boolean targetOptimizedWrites = true;
        HashMap<String, String> params = new HashMap<String, String>();
        params.put(ParamConstants.REQUEST_ID, "requestId");

        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        ReplicaSet replicaSetMeta = Mockito.mock(ReplicaSet.class);
        replicaSetMeta.setPrimaryInsName("testName");

        when(podCommonSupport.isInitOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        when(podCommonSupport.isOptimizedWrites("{\"optimized_writes\":true,\"init_optimized_writes\":true}")).thenReturn(true);
        //when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);

        when(modifyInsParam.getCustins()).thenReturn(Mockito.mock(CustInstanceDO.class));
        when(modifyInsParam.getDbInstanceName()).thenReturn("dbInstanceName");
        when(modifyInsParam.getReplicaSetMeta()).thenReturn(Mockito.mock(ReplicaSet.class));
        when(modifyInsParam.getSwitchInfo()).thenReturn(new HashMap<>());
        when(modifyInsParam.getRequestId()).thenReturn("requestId");
        when(modifyInsParam.getCustins()).thenReturn(custins);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(defaultApi.getReplicaSetLabel(null, "readIns", OPTIMIZED_WRITES_INFO)).thenReturn("{\"optimized_writes\":true,\"init_optimized_writes\":true}");
        List<CustInstanceDO> readCustinsList = new ArrayList<>();
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setInsName("readIns");
        readCustinsList.add(readCustins);

        //when(workFlowService.dispatchTask("custins", "dbInstanceName", "mysql", TASK_MODIFY_OPTIMIZED_WRITES, "{\"optimized_writes\":true,\"init_optimized_writes\":true}", 0)).thenReturn("taskId123");
        when(defaultApi.updateReplicaSetStatus("requestId", "dbInstanceName", ReplicaSet.StatusEnum.INS_MAINTAINING.toString())).thenReturn("success");
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(123, true)).thenReturn(readCustinsList);

        // Act
        ModifyDBInstanceOptimizedWritesService modifyDBInstanceOptimizedWritesServiceSpy = spy(modifyDBInstanceOptimizedWritesService);
        doReturn(modifyInsParam).when(modifyDBInstanceOptimizedWritesServiceSpy).initPodModifyInsParam(params);
        Map<String, Object> data = modifyDBInstanceOptimizedWritesServiceSpy.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
        boolean expected = true;
        boolean actual = (boolean) data.get("targetInitOptimizedWrites");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void doActionRequest_Success_ForReadIns() throws Exception {
        // Arrange
        String primaryOptimizedWritesInfo = "{\"optimizedWrites\":false}";
        boolean targetOptimizedWrites = true;
        HashMap<String, String> params = new HashMap<String, String>();
        params.put(ParamConstants.REQUEST_ID, "requestId");

        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        ReplicaSet replicaSetMeta = Mockito.mock(ReplicaSet.class);
        replicaSetMeta.setPrimaryInsName("testName");

        when(podCommonSupport.isInitOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        //when(podCommonSupport.isOptimizedWrites("{\"optimized_writes\":true,\"init_optimized_writes\":true}")).thenReturn(true);
        //when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);

        when(modifyInsParam.getCustins()).thenReturn(Mockito.mock(CustInstanceDO.class));
        when(modifyInsParam.getDbInstanceName()).thenReturn("dbInstanceName");
        when(modifyInsParam.getReplicaSetMeta()).thenReturn(Mockito.mock(ReplicaSet.class));
        when(modifyInsParam.getSwitchInfo()).thenReturn(new HashMap<>());
        when(modifyInsParam.getRequestId()).thenReturn("requestId");
        when(modifyInsParam.getCustins()).thenReturn(custins);
        when(modifyInsParam.isReadIns()).thenReturn(true);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        //when(defaultApi.getReplicaSetLabel(null, "readIns", OPTIMIZED_WRITES_INFO)).thenReturn("{\"optimized_writes\":true,\"init_optimized_writes\":true}");
        List<CustInstanceDO> readCustinsList = new ArrayList<>();
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setInsName("readIns");
        readCustinsList.add(readCustins);

        //when(workFlowService.dispatchTask("custins", "dbInstanceName", "mysql", TASK_MODIFY_OPTIMIZED_WRITES, "{\"optimized_writes\":true,\"init_optimized_writes\":true}", 0)).thenReturn("taskId123");
        when(defaultApi.updateReplicaSetStatus("requestId", "dbInstanceName", ReplicaSet.StatusEnum.INS_MAINTAINING.toString())).thenReturn("success");
        //when(custinsService.getReadCustInstanceListByPrimaryCustinsId(123, true)).thenReturn(readCustinsList);

        // Act
        ModifyDBInstanceOptimizedWritesService modifyDBInstanceOptimizedWritesServiceSpy = spy(modifyDBInstanceOptimizedWritesService);
        doReturn(modifyInsParam).when(modifyDBInstanceOptimizedWritesServiceSpy).initPodModifyInsParam(params);
        Map<String, Object> data = modifyDBInstanceOptimizedWritesServiceSpy.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
        boolean expected = true;
        boolean actual = (boolean) data.get("targetInitOptimizedWrites");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void doActionRequest_Exception_ForReadIns() throws Exception {
        // Arrange
        String primaryOptimizedWritesInfo = "{\"optimized_writes\":true,\"init_optimized_writes\":true}";
        boolean targetOptimizedWrites = false;
        HashMap<String, String> params = new HashMap<String, String>();
        params.put(ParamConstants.REQUEST_ID, "requestId");

        PodModifyInsParam modifyInsParam = Mockito.mock(PodModifyInsParam.class);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        modifyInsParam.setCustins(custins);
        modifyInsParam.setDbVersion("8.0");
        modifyInsParam.setSrcDiskType(ECS_CLOUD);
        ReplicaSet replicaSetMeta = Mockito.mock(ReplicaSet.class);
        replicaSetMeta.setPrimaryInsName("testName");

        //when(podCommonSupport.isInitOptimizedWrites(primaryOptimizedWritesInfo)).thenReturn(true);
        when(podCommonSupport.isOptimizedWrites("{\"optimized_writes\":true,\"init_optimized_writes\":true}")).thenReturn(true);
        //when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);

        when(modifyInsParam.getCustins()).thenReturn(Mockito.mock(CustInstanceDO.class));
        //when(modifyInsParam.getDbInstanceName()).thenReturn("dbInstanceName");
        when(modifyInsParam.getReplicaSetMeta()).thenReturn(Mockito.mock(ReplicaSet.class));
        when(modifyInsParam.getReplicaSetMeta().getPrimaryInsName()).thenReturn("readIns");
        //when(modifyInsParam.getSwitchInfo()).thenReturn(new HashMap<>());
        //when(modifyInsParam.getRequestId()).thenReturn("requestId");
        when(modifyInsParam.getCustins()).thenReturn(custins);
        when(modifyInsParam.isReadIns()).thenReturn(true);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(defaultApi.getReplicaSetLabel(null, "readIns", OPTIMIZED_WRITES_INFO)).thenReturn(primaryOptimizedWritesInfo);
        List<CustInstanceDO> readCustinsList = new ArrayList<>();
        CustInstanceDO readCustins = new CustInstanceDO();
        readCustins.setInsName("readIns");
        readCustinsList.add(readCustins);

        //when(workFlowService.dispatchTask("custins", "dbInstanceName", "mysql", TASK_MODIFY_OPTIMIZED_WRITES, "{\"optimized_writes\":true,\"init_optimized_writes\":true}", 0)).thenReturn("taskId123");
        //when(defaultApi.updateReplicaSetStatus("requestId", "dbInstanceName", ReplicaSet.StatusEnum.RESTARTING.toString())).thenReturn("success");
        //when(custinsService.getReadCustInstanceListByPrimaryCustinsId(123, true)).thenReturn(readCustinsList);

        // Act
        ModifyDBInstanceOptimizedWritesService modifyDBInstanceOptimizedWritesServiceSpy = spy(modifyDBInstanceOptimizedWritesService);
        doReturn(modifyInsParam).when(modifyDBInstanceOptimizedWritesServiceSpy).initPodModifyInsParam(params);
        Map<String, Object> data = modifyDBInstanceOptimizedWritesServiceSpy.doActionRequest(primaryOptimizedWritesInfo, targetOptimizedWrites, params);
        Object[] errorInfo = (Object[])data.get("errorCode");
        Assert.assertEquals("ReadDBInstanceNotSupport", errorInfo[1]);
        Assert.assertEquals("Read instance does not support this operation.", errorInfo[2]);
    }
}
