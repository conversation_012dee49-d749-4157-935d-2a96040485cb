package com.aliyun.dba.poddefault.action;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/17
 */

import java.util.HashMap;
import java.util.Map;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import org.assertj.core.util.Arrays;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.powermock.api.mockito.PowerMockito.doThrow;

@RunWith(PowerMockRunner.class)
@PrepareForTest(RequestSession.class)
public class DescribeDBInstanceTDEImplTest {

    @InjectMocks
    private DescribeDBInstanceTDEImpl describeDBInstanceTDEImpl;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private CustinsParamService custinsParamService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(RequestSession.class);
        when(RequestSession.getRequestId()).thenReturn("testRequestId");
    }

    @Test
    public void testDoActionRequest_Success() throws RdsException {
        // Arrange
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("testInstance");

        when(mysqlParamSupport.getCustInstance(any())).thenReturn(custins);
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(createCustinsParamDO("1"), createCustinsParamDO("AES"), createCustinsParamDO("keyId"));

        // Act
        Map<String, Object> result = describeDBInstanceTDEImpl.doActionRequest(custins, actionParams);

        // Assert
        assertEquals(1, result.get("TDEStatus"));
        assertEquals("AES", result.get("TDEMode"));
        assertEquals("keyId", result.get("EncryptionKey"));
        assertEquals(1, result.get("DBInstanceID"));
        assertEquals("testInstance", result.get("DBInstanceName"));
    }

    @Test
    public void testDoActionRequest_TDENotEnabled() throws RdsException {
        // Arrange
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("testInstance");

        when(mysqlParamSupport.getCustInstance(any(Map.class))).thenReturn(custins);
        when(custinsParamService.getCustinsParam(anyInt(), anyString())).thenReturn(createCustinsParamDO("0"), null, null);

        // Act
        Map<String, Object> result = describeDBInstanceTDEImpl.doActionRequest(custins, actionParams);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDoActionRequest_Exception() throws RdsException {
        // Arrange
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setInsName("testInstance");

        when(mysqlParamSupport.getCustInstance(any())).thenThrow(new RdsException(ErrorCode.INVALID_INSTANCE));

        // Act
        Map<String, Object> result = describeDBInstanceTDEImpl.doActionRequest(custins, actionParams);

        // Assert
        Object[] errorCode = (Object[])result.get("errorCode");
        assertEquals(ErrorCode.INVALID_INSTANCE.getCode(), errorCode[0]);
    }

    private CustinsParamDO createCustinsParamDO(String value) {
        CustinsParamDO param = new CustinsParamDO();
        param.setValue(value);
        return param;
    }
}
