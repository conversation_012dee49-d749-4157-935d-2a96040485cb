package com.aliyun.dba.poddefault.action.service;

import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;

import static org.junit.Assert.*;

public class AligroupCreateDBInstanceServiceTest {

    @Test
    public void createDBIntance() {
            HashMap<String, String> params = new HashMap<String, String>();
            params.put("ResourceGroupId", "rg-58aiyyy78vgw86n");
            params.put("DBInstanceNetType", "1");
            params.put("DBInstanceType", "x");
            params.put("Timezone", "china");
            params.put("MultiAVZExParam", "%7B%22availableZoneInfoList%22%3A%5B%7B%22isUserSpecified%22%3Atrue%2C%22region%22%3A%22cn-zhangjiakou-a-aliyun%22%2C%22role%22%3A%22master%22%2C%22zoneID%22%3A%22cn-zhangji<PERSON>ou-a%22%7D%5D%7D");
            params.put("StorageEngine", "innodb");
            params.put("SecurityIPList", "0.0.0.0%2F0");
            params.put("ClusterName", "test_mysql_dedicate_group_console");
            params.put("OpsServiceVersion", "2.0");
            params.put("Engine", "mysql");
            params.put("DBInstanceName", "k8smysqlbasic1602565479pk3");
            params.put("EngineVersion", "5.7");
            params.put("DBInstanceClass", "mysql.n1.micro.1");
            params.put("VPCId", "vpc-xxxmmxjqqi8go8153uq3b");
            params.put("DBInstanceStorageType", "cloud_ssd");
            params.put("VswitchId", "vsw-8vb5wxejg4owbtmkdepzo");
            params.put("Storage", "20");
            params.put("Region", "cn-zhangjiakou-a-aligroup");
            params.put("OssFileSize", "2097152");
            params.put("OssUrl", "https://mysql57-restore.oss-cn-zhangjiakou.aliyuncs.com/backupall_qp.xb?Expires=1606220745%26OSSAccessKeyId=TMP.3KeazaTKrA98SB8oxqNFwTQxqpqv9JAByJVAQgcb1nzkkRa9WYqpG3bdzXpcmwcD9Q6bBWZdmnuHBQBQmig8aoa23xZ13p%26Signature=dP%252BDGGD4FdZGQsGfHVVSSehRSF0%253D");
            params.put("OssBucket", "test");
            params.put("OssFilePath", "test");
            params.put("PreferredBackupTime".toLowerCase(), "18:28Z");
            params.put("preferredbackupperiod".toLowerCase(), "1010101");
            params.put("OssFileName", "backup_all_qp.xb");
            params.put("OssFileMetaData", "");
        AligroupCreateDBInstanceService aligroupCreateDBInstanceService = new AligroupCreateDBInstanceService();
            try {
                aligroupCreateDBInstanceService.createDBIntance(null,params);
            }catch (Exception e){
                Assert.assertTrue(e != null);
            }
    }
}