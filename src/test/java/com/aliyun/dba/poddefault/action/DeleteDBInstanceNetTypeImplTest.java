package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class DeleteDBInstanceNetTypeImplTest {
    @InjectMocks
    private DeleteDBInstanceNetTypeImpl deleteDBInstanceNetTypeImpl;
    @Mock
    private DBaasMetaService dBaasMetaService;

    private DefaultApi defaultApi;
    @Mock
    private MysqlParamSupport paramSupport;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private CustinsService custinsService;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultApi = PowerMockito.mock(DefaultApi.class);
        PowerMockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test
    public void isConnectiongStringToSsl() throws Exception {
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setId(1L);
        replicaSet.setName("testReplicaSetName");
        replicaSet.setUserId("test001_111");
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setCategory("standard");
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        String requestId = "testRequestId";
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> replicas = new ArrayList<>();
        Replica replica = new Replica();
        replica.setRole(Replica.RoleEnum.MASTER);
        replicaListResult.setItems(replicas);

        when(defaultApi.listReplicasInReplicaSet(requestId, "testReplicaSetName", null, null, null, null)).thenReturn(replicaListResult);
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        when(paramSupport.isConnectionStringToSsl(params, custInstanceDO)).thenReturn(true);
        custinsService.checkConnAddrChangeTimesExceed(1, "test",null);

        Map<String, Object> result = deleteDBInstanceNetTypeImpl.doActionRequest(custInstanceDO, params);
        System.out.print(JSONObject.toJSONString(result));
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.INVALID_CONNECTIONSTRING.getCode(), errorArray[0]);
    }
}