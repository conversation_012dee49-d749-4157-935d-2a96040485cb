package com.aliyun.dba.poddefault.action.support;

import java.util.*;
import java.math.*;

import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.UserGrayService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class PodCommonSupportTest {
    @InjectMocks
    private PodCommonSupport podCommonSupport;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private ReplicaSetService replicaSetService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private CustinsService custinsService;

    @Mock
    private ResourceService resourceService;

    @Mock
    private UserGrayService userGrayService;

    @Mock
    private LogAgent logAgent;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private CustinsParamService custinsParamService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockStatic(RequestSession.class);
        PowerMockito.when(RequestSession.getRequestId()).thenReturn(UUID.randomUUID().toString());
    }

    @Test
    public void putLabelForOptimizedWrites_ConfigEnabled_ShouldAddLabels() throws ApiException {
        // Arrange
        String requestId = "testRequestId";
        String dbType = "mysql";
        Map<String, String> labels = new HashMap<>();

        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config configItem = new Config();
        configItem.setValue("true");
        items.add(configItem);
        configListResult.setItems(items);

        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(dBaasMetaService.getDefaultClient().listConfigs(requestId, "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT"))
                .thenReturn(configListResult);

        // Act
        podCommonSupport.putLabelForOptimizedWrites(requestId, dbType, labels);

        // Assert
        verify(dBaasMetaService.getDefaultClient(), times(1)).listConfigs(eq(requestId), eq("MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT"));
    }

    @Test
    public void putLabelForOptimizedWritesForVip_ConfigEnabled_ShouldAddLabels() throws ApiException {
        // Arrange
        String requestId = "testRequestId";
        String uid = "testUid";
        String zoneId = "testZoneId";

        ConfigListResult configListResult = new ConfigListResult();
        List<Config> items = new ArrayList<>();
        Config configItem = new Config();
        HashMap<String, String> azJson = new HashMap<>();
        azJson.put(zoneId, uid);
        configItem.setValue(new Gson().toJson(azJson));
        items.add(configItem);
        configListResult.setItems(items);
        boolean isOptimizedWrites = false;

        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(defaultApi.listConfigs(requestId, "WHITE_LIST_FOR_VIP_TURN_ON_OPTIMIZED_WRITES")).thenReturn(configListResult);

        // Act
        podCommonSupport.setOptimizedWritesForVip(requestId, uid, zoneId, isOptimizedWrites);

        // Assert
        verify(defaultApi, times(1)).listConfigs(eq(requestId), eq("WHITE_LIST_FOR_VIP_TURN_ON_OPTIMIZED_WRITES"));
    }

    @Test
    public void checkMinorVersionWithMaxScale_ReleaseDate_ReturnsFalse() {
        Integer maxScaleCustinsId = 1;
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("3.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_20210220");
        when(custinsService.getCustInstanceByCustinsId(maxScaleCustinsId)).thenReturn(custins);
        when(custinsParamService.getCustinsParam(1, "minor_version")).thenReturn(custinsParamDO);
        assertFalse(podCommonSupport.checkMinorVersionWithMaxScale(maxScaleCustinsId));
    }

    @Test
    public void checkMinorVersionWithMaxScale_DateLength_ReturnsTrue() {
        Integer maxScaleCustinsId = 1;
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("3.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_202502200");
        when(custinsService.getCustInstanceByCustinsId(maxScaleCustinsId)).thenReturn(custins);
        when(custinsParamService.getCustinsParam(1, "minor_version")).thenReturn(custinsParamDO);
        assertTrue(podCommonSupport.checkMinorVersionWithMaxScale(maxScaleCustinsId));
    }

    @Test
    public void checkMinorVersionWithMaxScale_No35_Length_ReturnsTrue() {
        Integer maxScaleCustinsId = 1;
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("1.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_20250220");
        when(custinsService.getCustInstanceByCustinsId(maxScaleCustinsId)).thenReturn(custins);
        when(custinsParamService.getCustinsParam(1, "minor_version")).thenReturn(custinsParamDO);
        assertTrue(podCommonSupport.checkMinorVersionWithMaxScale(maxScaleCustinsId));
    }

    @Test
    public void checkMinorVersionWithMaxScale_No35_Length2_ReturnsTrue() {
        Integer maxScaleCustinsId = 1;
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("1.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_20250220_11");
        when(custinsService.getCustInstanceByCustinsId(maxScaleCustinsId)).thenReturn(custins);
        when(custinsParamService.getCustinsParam(1, "minor_version")).thenReturn(custinsParamDO);
        assertTrue(podCommonSupport.checkMinorVersionWithMaxScale(maxScaleCustinsId));
    }

    @Test
    public void checkMinorVersionWithMaxScale_No35_Length3_ReturnsTrue() {
        Integer maxScaleCustinsId = 1;
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("1.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_20250220_1.1.n");
        when(custinsService.getCustInstanceByCustinsId(maxScaleCustinsId)).thenReturn(custins);
        when(custinsParamService.getCustinsParam(1, "minor_version")).thenReturn(custinsParamDO);
        assertTrue(podCommonSupport.checkMinorVersionWithMaxScale(maxScaleCustinsId));
    }

    @Test
    public void checkMinorVersionWithMaxScale_No35_Length4_ReturnsTrue() {
        Integer maxScaleCustinsId = 1;
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("1.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_20250220_1.13.1");
        when(custinsService.getCustInstanceByCustinsId(maxScaleCustinsId)).thenReturn(custins);
        when(custinsParamService.getCustinsParam(1, "minor_version")).thenReturn(custinsParamDO);
        assertFalse(podCommonSupport.checkMinorVersionWithMaxScale(maxScaleCustinsId));
    }

    @Test
    public void checkMinorVersionWithMaxScale_MaxscaleNull_ReturnTrue() {
        Integer maxScaleCustinsId = 1;
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("1.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_20250220_1.13.1");
        when(custinsService.getCustInstanceByCustinsId(maxScaleCustinsId)).thenReturn(null);
        assertTrue(podCommonSupport.checkMinorVersionWithMaxScale(maxScaleCustinsId));
    }

    @Test
    public void checkMinorVersionWithMaxScale_MinorVersion_ReturnsTrue() {
        Integer maxScaleCustinsId = 1;
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("1.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_20250220_1.13.1");
        when(custinsService.getCustInstanceByCustinsId(maxScaleCustinsId)).thenReturn(custins);
        when(custinsParamService.getCustinsParam(1, "minor_version")).thenReturn(null);
        assertTrue(podCommonSupport.checkMinorVersionWithMaxScale(maxScaleCustinsId));
    }

    @Test
    public void checkHaToClusterCondition_ReturnsFalse() throws ApiException {
        ReplicaSet replicaSetMeta = new ReplicaSet();
        replicaSetMeta.setName("test");
        String requestId = "123";
        Integer maxscaleInsId = 1;
        InstanceLevel instanceLevel = new InstanceLevel();
        ReplicaSetListResult replicaSetListResult = new ReplicaSetListResult();
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setIsDeleted(1);
        custins.setDbVersion("3.5");
        CustinsParamDO custinsParamDO = new CustinsParamDO();
        custinsParamDO.setValue("2.25.1_20210220");
        when(custinsService.getCustInstanceByCustinsId(maxscaleInsId)).thenReturn(custins);
        when(replicaSetService.getMaxscaleInsId("test")).thenReturn(maxscaleInsId);
        when(custinsParamService.getCustinsParam(1, "minor_version")).thenReturn(custinsParamDO);
        assertThrows(RdsException.class, () -> {
            podCommonSupport.checkHaToClusterCondition("requestId", replicaSetMeta, instanceLevel);
        });
        verify(custinsParamService, times(1)).getCustinsParam(anyInt(),anyString());
    }

    @Test
    public void getReplicaSetRuntimeType() throws RdsException, ApiException {
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(defaultApi.listReplicasInReplicaSet(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ReplicaListResult(){{setItems(ImmutableList.of(new Replica(){{setId(1L);}}));}});
        when(defaultApi.getReplica(anyString(), anyLong(), any())).thenReturn(new ReplicaResource());
        PodType podType = podCommonSupport.getReplicaSetRuntimeType("rm-xxx", new InstanceLevel(){{setCategory(CategoryEnum.SERVERLESS_BASIC);}});
        assertEquals(PodType.POD_RUNC, podType);
    }
}
