package com.aliyun.dba.poddefault.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.service.BlueGreenDeploymentService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/12
 **/

@RunWith(PowerMockRunner.class)
@PrepareForTest({DeleteBlueGreenDeploymentImpl.class})
public class DeleteBlueGreenDeploymentImplTest {
    
    @InjectMocks
    private DeleteBlueGreenDeploymentImpl deleteBlueGreenDeployment;
    
    @Mock
    private BlueGreenDeploymentService blueGreenDeploymentService;
    
    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;

    @Before
    public void setUp() throws Exception {
        // 初始化mock对象
    }

    @Test
    public void testDoActionRequestSuccess() throws Exception {
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("mode", "testMode");

        // 模拟依赖行为
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        when(blueGreenDeploymentService.deleteBlueGreenDeployment(
            anyString(), anyString(), any(CustInstanceDO.class),
            anyString(), anyString()
        )).thenReturn(data);

        // 调用方法
        Map<String, Object> response = deleteBlueGreenDeployment.doActionRequest(custInstance, params);

        // 验证结果
        assertNotNull(response);
        verify(mysqlParamSupport, times(1)).getUID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckRegionID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckCustInstance(anyMap());
        verify(blueGreenDeploymentService, times(1)).deleteBlueGreenDeployment(
            anyString(),
            anyString(),
            any(CustInstanceDO.class),
            anyString(),
            anyString()
        );
    }

    @Test
    public void testDoActionRequestException() throws Exception {
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenReturn(mock(CustInstanceDO.class));

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("mode", "testMode");

        // 模拟依赖行为
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        when(blueGreenDeploymentService.deleteBlueGreenDeployment(
            anyString(), anyString(), any(CustInstanceDO.class),
            anyString(), anyString()
        )).thenReturn(data);

        // 调用方法
        try {
            Map<String, Object> response = deleteBlueGreenDeployment.doActionRequest(custInstance, null);
        } catch (Exception e) {
            fail();
        }

    }


    @Test(expected = RdsException.class)
    public void testDoActionRequestRdsException() throws Exception {
        when(mysqlParamSupport.getUID(anyMap())).thenReturn("testUid");
        when(mysqlParamSupport.getBID(anyMap())).thenReturn("testBid");
        when(mysqlParamSupport.getAndCheckRegionID(anyMap())).thenReturn("testRegionId");
        when(mysqlParamSupport.getAndCheckCustInstance(anyMap())).thenThrow(new RdsException(ErrorCode.INTERNAL_FAILURE));

        // 准备测试数据
        CustInstanceDO custInstance = mock(CustInstanceDO.class);
        Map<String, String> params = new HashMap<>();
        params.put("bluegreendeploymentname", "testDeploymentName");
        params.put("mode", "testMode");

        // 模拟依赖行为
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        when(blueGreenDeploymentService.deleteBlueGreenDeployment(
            anyString(), anyString(), any(CustInstanceDO.class),
            anyString(), anyString()
        )).thenReturn(data);

        // 调用方法
        Map<String, Object> response = deleteBlueGreenDeployment.doActionRequest(custInstance, params);

        // 验证结果
        assertNotNull(response);
        verify(mysqlParamSupport, times(1)).getUID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckRegionID(anyMap());
        verify(mysqlParamSupport, times(1)).getAndCheckCustInstance(anyMap());
        verify(blueGreenDeploymentService, times(1)).deleteBlueGreenDeployment(
            anyString(),
            anyString(),
            any(CustInstanceDO.class),
            anyString(),
            anyString()
        );
    }
}