package com.aliyun.dba.poddefault.action.service;


import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.RebuildReplicaResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;

import javax.validation.constraints.Min;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class CompressionModeServiceTest {

    @InjectMocks
    private CompressionModeService compressionModeService;

    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private com.aliyun.apsaradb.activityprovider.api.DefaultApi commonDefaultApi;

    @Mock
    private WorkFlowService workFlowService;

    @Mock
    private MySQLServiceImpl mySQLService;

    @Mock
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;

    @Mock
    private CommonProviderService commonProviderService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private RundPodSupport rundPodSupport;
    @Mock
    private PodParameterHelper podParameterHelper;

    @Mock
    private AliyunInstanceDependency aliyunInstanceDependency;
    @Mock
    private MysqlParamSupport mysqlParamSupport;

    private PodModifyInsParam modifyInsParam;
    @Mock
    private PodCommonSupport podCommonSupport;

    @Before
    public void setUp() throws ApiException, com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(aliyunInstanceDependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        when(commonProviderService.getDefaultApi()).thenReturn(commonDefaultApi);

        when(mySQLService.getReplicaByRole(anyString(), anyString(), eq(Replica.RoleEnum.MASTER))).thenReturn(new Replica().id(10L));
        when(defaultApi.getReplica(anyString(), anyLong(), any())).thenReturn(new ReplicaResource());
        when(defaultApi.getReplicaSet(anyString(), anyString(), any())).thenReturn(new ReplicaSet().name("dbInstanceName"));
        when(custinsService.getCustInstanceByInsName(any(), anyString(), anyInt())).thenReturn(new CustInstanceDO());
        List<Replica> replicaList = Arrays.asList(new Replica().id(1L), new Replica().id(2L));
        PowerMockito.when(defaultApi.listReplicasInReplicaSet(anyString(), anyString(), any(), any(), any(), any()))
                .thenReturn(new com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult().items(replicaList));
        modifyInsParam = new PodModifyInsParam(aliyunInstanceDependency, null);
        modifyInsParam.setRequestId("requestId");
        modifyInsParam.setDbInstanceName("dbInstanceName");
        modifyInsParam.setTargetCompressionMode("on");
        modifyInsParam.setCompressionModeChange(true);
    }

    @Test
    public void modifyCompressionMode_NullParam_ThrowsException() {
        assertThrows(RdsException.class, () -> compressionModeService.modifyCompressionMode(null));
    }

    @Test
    public void modifyCompressionMode_CompressionModeNotChanged_ThrowsException() {
        modifyInsParam.setTargetCompressionMode("off");
        assertThrows(RdsException.class, () -> compressionModeService.modifyCompressionMode(modifyInsParam));
    }

    @Test
    public void modifyCompressionMode_AllocateResourceThrowsException_ThrowsException() throws Exception {
        when(podReplicaSetResourceHelper.allocateRebuildResource4Basic(any(), any(), any())).thenThrow(new Exception("Allocation failed"));
        assertThrows(Exception.class, () -> compressionModeService.modifyCompressionMode(modifyInsParam));
    }

    @Test
    public void modifyCompressionMode_Success_ReturnsData() throws Exception {
        RebuildReplicaResourceRequest allocateReplicaResource = new RebuildReplicaResourceRequest();
        allocateReplicaResource.setTmpReplicaSetName("tmpReplicaSetName");
        when(podReplicaSetResourceHelper.allocateRebuildResource4Basic(any(), any(), any())).thenReturn(allocateReplicaResource);
        when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn("taskId");
        when(podCommonSupport.getReplicaRuntimeType(any())).thenReturn(PodType.POD_RUNC);
        Map<String, Object> result = compressionModeService.modifyCompressionMode(modifyInsParam);

        assertNotNull(result);
        assertEquals("dbInstanceName", result.get("DBInstanceName"));
        assertEquals("taskId", result.get("TaskId"));
    }
}
