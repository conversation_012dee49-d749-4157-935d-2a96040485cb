package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.UserGrayService;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CloudDiskCompressionHelperTest {
    @Mock
    private ResourceService resourceService;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private UserGrayService userGrayService;
    @InjectMocks
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;

    @Mock
    private CloudDiskCompressionHelper.CloudDiskCompressionLimit cloudDiskCompressionLimit;

    @Mock
    private InstanceLevel instanceLevel;

    @Mock
    private List<ReplicaSet> replicaSetList;

    @Before
    public void setUp() throws ApiException {
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        when(resourceService.getResourceByResKey(anyString())).thenReturn(new ResourceDO());
        when(dBaasMetaService.getDefaultClient().listReplicaSetSubIns(anyString(), anyString(), anyString()))
                .thenReturn(new com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetListResult().items(replicaSetList));
        when(userGrayService.isHitCompression(anyString(), anyString(), anyString())).thenReturn(true);
    }

    @Test
    public void checkCompressionSupportLimit_NonMySQL_ThrowsException() {
        assertThrows(RdsException.class, () -> {
            cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "PostgreSQL", "uid", "regionId", 1000L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, true);
        });
    }

    @Test
    public void checkCompressionSupportLimit_Serverless_ThrowsException() {
        when(instanceLevel.getCategory()).thenReturn(InstanceLevel.CategoryEnum.SERVERLESS_BASIC);
        assertThrows(RdsException.class, () -> {
            cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid", "regionId", 1000L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, true);
        });
    }

    @Test
    public void checkCompressionSupportLimit_NotSingleTenant_ThrowsException() {
        when(instanceLevel.getIsolationType()).thenReturn(InstanceLevel.IsolationTypeEnum.COMMON);
        assertThrows(RdsException.class, () -> {
            cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid", "regionId", 1000L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, true);
        });
    }

    @Test
    public void checkCompressionSupportLimit_NotCloudAuto_ThrowsException() {
        assertThrows(RdsException.class, () -> {
            cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid", "regionId", 1000L, CustinsSupport.STORAGE_TYPE_CLOUD_ESSD, true);
        });
    }

    @Test
    public void checkCompressionSupportLimit_DiskSizeOutOfLimit_ThrowsException() {
        assertThrows(RdsException.class, () -> {
            cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid", "regionId", 900L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, true);
        });
    }

    @Test
    public void checkCompressionSupportLimit_NotInGrayPolicy_ThrowsException() {
        when(userGrayService.isHitCompression(anyString(), anyString(), anyString())).thenReturn(false);
        assertThrows(RdsException.class, () -> {
            cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid", "regionId", 1025L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, true);
        });
    }

    @Test
    public void checkCompressionSupportLimit_UserGrayService() throws RdsException {
        when(userGrayService.isHitCompression(eq("requestId"), eq("uid"), eq("regionId"))).thenReturn(false);
        Map<String, Object> result = cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid", "regionId", 1025L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, false);
        assertEquals("BlockLimit", result.get(CloudDiskCompressionHelper.LIMIT_REASON));
        assertEquals(false, result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION));
    }

    @Test
    public void checkCompressionSupportLimit_NoExceptionThrown() throws RdsException {
        Map<String, Object> result = cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid", "regionId", 1025L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, false);
        assertEquals("blank reason", result.get(CloudDiskCompressionHelper.LIMIT_REASON));
        assertEquals(true, result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION));
    }

    @Test
    public void checkCompressionSupportLimit_checkResource() throws RdsException, NoSuchFieldException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        CloudDiskCompressionHelper.invalidateCache("cloudDiskCompressionLimitCache", "CLOUD_DISK_COMPRESSION_LIMIT");
        // 初始化 JSON 对象
        String jsonString = "{"
                + "\"default\": {"
                + "\"compressionMaxDiskSizeGB\": 25000,"
                + "\"compressionMinDiskSizeGB\": 1000,"
                + "\"priority\": 1,"
                + "\"limitSingleTenant\": true,"
                + "\"supportStorageTypes\": \"cloud_essd,cloud_auto\""
                + "},"
                + "\"uid1\":{"
                + "\"compressionMaxDiskSizeGB\": 25000,"
                + "\"compressionMinDiskSizeGB\": 2000,"
                + "\"priority\": 2,"
                + "\"limitSingleTenant\": true"
                + "},"
                + "\"uid2\":{},"
                + "\"uid3\":\"\""
                + "}";
        JSONObject jsonObject = JSONObject.parseObject(jsonString);

        // 创建 ResourceDO 并设置 realValue
        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue(jsonObject.toJSONString());
        when(resourceService.getResourceByResKey(eq("CLOUD_DISK_COMPRESSION_LIMIT"))).thenReturn(resourceDO);
        Map<String, Object> result = cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid", "regionId", 1000L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, false);
        assertEquals("blank reason", result.get(CloudDiskCompressionHelper.LIMIT_REASON));
        assertEquals(true, result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION));

        Map<String, Object> result1 = cloudDiskCompressionHelper.checkCompressionSupportLimit("requestId", instanceLevel, "MySQL", "uid1", "regionId", 1000L, CustinsSupport.STORAGE_TYPE_CLOUD_AUTO, false);
        assertEquals("DiskSizeLimit", result1.get(CloudDiskCompressionHelper.LIMIT_REASON));
        assertEquals(false, result1.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION));
        CloudDiskCompressionHelper.invalidateCache("cloudDiskCompressionLimitCache", "CLOUD_DISK_COMPRESSION_LIMIT");
    }


    @Test
    public void getCompressionRatio_ReplicaSetNameBlank_ReturnsFromResource_Default() throws ApiException {
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "", "uid", "regionId", CloudDiskCompressionHelper.CompressionRatioKeyType.CLOUD_COMPRESSION_RATIO);
        assertEquals(2.0, result, 0.0);
    }


    @Test
    public void getCompressionRatio_ValidSrcCompressionRatio_ReturnsParsedValue() throws ApiException {
        when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString())).thenReturn("1.5");
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "replicaSetName", "uid", "regionId", CloudDiskCompressionHelper.CompressionRatioKeyType.CLOUD_COMPRESSION_RATIO);
        assertEquals(1.5, result, 0.0);
    }

    @Test
    public void getCompressionRatio_InvalidSrcCompressionRatio_ReturnsDefault() throws ApiException {
        when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString())).thenReturn("invalid");
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "replicaSetName", "uid", "regionId", CloudDiskCompressionHelper.CompressionRatioKeyType.CLOUD_COMPRESSION_RATIO);
        assertEquals(1.0, result, 0.0);
    }

    @Test
    public void getCompressionRatio_ReplicaSetNameBlank_ReturnsFromResource_UID() throws ApiException {
        CloudDiskCompressionHelper.invalidateCache("compressionRatioCache", "RDS_UID_COMPRESSION_CONFIG");
        // 初始化 JSON 对象
        String jsonString = "{" +
                "  \"uid\":" +
                "  {" +
                "    \"local_compression_ratio\": {" +
                "      \"min\": 1.78," +
                "      \"max\": 2.5," +
                "      \"default\": 2.0," +
                "    }," +
                "    \"cloud_compression_ratio\": {" +
                "      \"min\": 1.78," +
                "      \"max\": 2.5," +
                "      \"default\": 2.1," +
                "    }" +
                "  }" +
                "}";
        JSONObject jsonObject = JSONObject.parseObject(jsonString);

        // 创建 ResourceDO 并设置 realValue
        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue(jsonObject.toJSONString());
        when(resourceService.getResourceByResKey(eq("RDS_UID_COMPRESSION_CONFIG"))).thenReturn(resourceDO);

        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "", "uid", "regionId", CloudDiskCompressionHelper.CompressionRatioKeyType.CLOUD_COMPRESSION_RATIO);
        assertEquals(2.1, result, 0.0);

        CloudDiskCompressionHelper.invalidateCache("compressionRatioCache", "RDS_UID_COMPRESSION_CONFIG");
    }

    @Test
    public void getCompressionRatio_ReplicaSetNameBlank_ReturnsFromResource_Region() throws ApiException {
        CloudDiskCompressionHelper.invalidateCache("compressionRatioCache", "RDS_REGION_COMPRESSION_CONFIG");
        // 初始化 JSON 对象
        String jsonString = "{" +
                "  \"regionId\":" +
                "  {" +
                "    \"local_compression_ratio\": {" +
                "      \"min\": 1.78," +
                "      \"max\": 2.5," +
                "      \"default\": 2.0," +
                "    }," +
                "    \"cloud_compression_ratio\": {" +
                "      \"min\": 1.78," +
                "      \"max\": 2.5," +
                "      \"default\": 2.2," +
                "    }" +
                "  }" +
                "}";
        JSONObject jsonObject = JSONObject.parseObject(jsonString);

        // 创建 ResourceDO 并设置 realValue
        ResourceDO resourceDO = new ResourceDO();
        resourceDO.setRealValue(jsonObject.toJSONString());
        when(resourceService.getResourceByResKey(eq("RDS_REGION_COMPRESSION_CONFIG"))).thenReturn(resourceDO);
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "", "uid", "regionId", CloudDiskCompressionHelper.CompressionRatioKeyType.CLOUD_COMPRESSION_RATIO);
        assertEquals(2.2, result, 0.0);
        CloudDiskCompressionHelper.invalidateCache("compressionRatioCache", "RDS_REGION_COMPRESSION_CONFIG");
    }

    @Test
    public void getCompressionMode_ParamCompressionModeValid_ReturnsParamCompressionMode() throws ApiException {
        String paramCompressionMode = "on";
        String result = cloudDiskCompressionHelper.getCompressionMode("requestId", "replicaSetName", paramCompressionMode);
        assertEquals(paramCompressionMode, result);
    }

    @Test
    public void getCompressionMode_ReplicaSetNameBlank_ReturnsDefaultOff() throws ApiException {
        String paramCompressionMode = null;
        String replicaSetName = "";
        String result = cloudDiskCompressionHelper.getCompressionMode("requestId", replicaSetName, paramCompressionMode);
        assertEquals(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF, result);
    }

    @Test
    public void getCompressionMode_ReplicaSetNameNotBlankAndSrcCompressionModeValid_ReturnsSrcCompressionMode() throws ApiException {
        String paramCompressionMode = null;
        String replicaSetName = "validReplicaSet";
        String srcCompressionMode = "on";
        when(defaultApi.getReplicaSetLabel(eq("requestId"), eq(replicaSetName), eq(CloudDiskCompressionHelper.CUSTINS_PARAM_COMPRESSION_MODE)))
                .thenReturn(srcCompressionMode);
        String result = cloudDiskCompressionHelper.getCompressionMode("requestId", replicaSetName, paramCompressionMode);
        assertEquals(srcCompressionMode, result);
    }

    @Test
    public void getCompressionMode_ReplicaSetNameNotBlankAndSrcCompressionModeInvalid_ReturnsDefaultOff() throws ApiException {
        String paramCompressionMode = null;
        String replicaSetName = "validReplicaSet";
        String srcCompressionMode = "invalid";
        when(defaultApi.getReplicaSetLabel(eq("requestId"), eq(replicaSetName), eq(CloudDiskCompressionHelper.CUSTINS_PARAM_COMPRESSION_MODE)))
                .thenReturn(srcCompressionMode);
        String result = cloudDiskCompressionHelper.getCompressionMode("requestId", replicaSetName, paramCompressionMode);
        assertEquals(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF, result);
    }


    @Test
    public void isCompressionModeOn_NullInput_ReturnsFalse() {
        assertFalse(CloudDiskCompressionHelper.isCompressionModeOn(null));
    }

    @Test
    public void isCompressionModeOn_EmptyString_ReturnsFalse() {
        assertFalse(CloudDiskCompressionHelper.isCompressionModeOn(""));
    }

    @Test
    public void isCompressionModeOn_CompressionModeOn_ReturnsTrue() {
        assertTrue(CloudDiskCompressionHelper.isCompressionModeOn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON));
    }

    @Test
    public void isCompressionModeOn_CompressionModeOff_ReturnsFalse() {
        assertFalse(CloudDiskCompressionHelper.isCompressionModeOn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF));
    }

    @Test
    public void isCompressionModeOn_OtherString_ReturnsFalse() {
        assertFalse(CloudDiskCompressionHelper.isCompressionModeOn("other"));
    }

    @Test
    public void isCompressionModeOff_NullInput_ReturnsFalse() {
        assertFalse(CloudDiskCompressionHelper.isCompressionModeOff(null));
    }

    @Test
    public void isCompressionModeOff_EmptyString_ReturnsFalse() {
        assertFalse(CloudDiskCompressionHelper.isCompressionModeOff(""));
    }

    @Test
    public void isCompressionModeOff_CompressionModeOn_ReturnsFalse() {
        assertFalse(CloudDiskCompressionHelper.isCompressionModeOff(CloudDiskCompressionHelper.COMPRESSION_MODE_ON));
    }

    @Test
    public void isCompressionModeOff_CompressionModeOff_ReturnsTrue() {
        assertTrue(CloudDiskCompressionHelper.isCompressionModeOff(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF));
    }

    @Test
    public void isCompressionModeOff_OtherString_ReturnsFalse() {
        assertFalse(CloudDiskCompressionHelper.isCompressionModeOff("other"));
    }

    @Test
    public void getPhysicalSize_NullLogicalSize_ReturnsNull() {
        assertNull(CloudDiskCompressionHelper.getPhysicalSize(null, 1.0));
    }

    @Test
    public void getPhysicalSize_NullCompressionRatio_ReturnsLogicalSize() {
        assertEquals(Optional.of(100).get(), CloudDiskCompressionHelper.getPhysicalSize(100, null));
    }

    @Test
    public void getPhysicalSize_ValidInputs_ReturnsPhysicalSize() {
        assertEquals(Optional.of(100).get(), CloudDiskCompressionHelper.getPhysicalSize(500, 5.0));
    }

    @Test
    public void getPhysicalSize_DecimalResult_ReturnsRoundedDown() {
        assertEquals(Optional.of(100).get(), CloudDiskCompressionHelper.getPhysicalSize(501, 5.0));
    }

    @Test
    public void getLogicalSize_NullPhysicalSize_ReturnsNull() {
        assertNull(CloudDiskCompressionHelper.getLogicalSize(null, 1.0));
    }

    @Test
    public void getLogicalSize_NullCompressionRatio_ReturnsPhysicalSize() {
        assertEquals(Optional.of(100).get(), CloudDiskCompressionHelper.getLogicalSize(100, null));
    }

    @Test
    public void getLogicalSize_ValidInputs_ReturnsLogicalSize() {
        assertEquals(Optional.of(500).get(), CloudDiskCompressionHelper.getLogicalSize(100, 5.0));
    }

    @Test
    public void getLogicalSize_DecimalResult_ReturnsRoundedUp() {
        assertEquals(Optional.of(505).get(), CloudDiskCompressionHelper.getLogicalSize(101, 5.0));
    }


    @Test
    public void checkCompressionChangeLimit_CompressionChangeFalse_ReturnsSupport() {
        Map<String, Boolean> changeLimits = new HashMap<>();
        changeLimits.put("key1", true);
        Map<String, Object> result = cloudDiskCompressionHelper.checkCompressionChangeLimit(false, changeLimits);
        assertTrue((boolean) result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE));
    }

    @Test
    public void checkCompressionChangeLimit_EmptyChangeLimits_ReturnsSupport() {
        Map<String, Boolean> changeLimits = new HashMap<>();
        Map<String, Object> result = cloudDiskCompressionHelper.checkCompressionChangeLimit(true, changeLimits);
        assertTrue((boolean) result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE));
    }

    @Test
    public void checkCompressionChangeLimit_ChangeLimitsTrue_ReturnsNotSupport() {
        Map<String, Boolean> changeLimits = new HashMap<>();
        changeLimits.put("key1", true);
        Map<String, Object> result = cloudDiskCompressionHelper.checkCompressionChangeLimit(true, changeLimits);
        assertFalse((boolean) result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE));
    }

    @Test
    public void checkCompressionChangeLimit_ChangeLimitsFalse_ReturnsSupport() {
        Map<String, Boolean> changeLimits = new HashMap<>();
        changeLimits.put("key1", false);
        Map<String, Object> result = cloudDiskCompressionHelper.checkCompressionChangeLimit(true, changeLimits);
        assertTrue((boolean) result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE));
    }

    @Test
    public void checkReadInsCompressionModeOn_NoReadOnlyReplicaSets_ReturnsSupport() throws ApiException, RdsException {
        when(dBaasMetaService.getDefaultClient().listReplicaSetSubIns(anyString(), anyString(), anyString()))
                .thenReturn(new com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetListResult().items(new ArrayList<ReplicaSet>()));
        Map<String, Object> result = cloudDiskCompressionHelper.checkReadInsCompressionModeOn("requestId", "primaryReplicaSetName", false);
        assertTrue((boolean) result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE));
    }

    @Test
    public void checkReadInsCompressionModeOn_ReadOnlyCompressionModeOn_ReturnsSupport() throws ApiException, RdsException {
        List<ReplicaSet> readOnlyReplicaSetList = new ArrayList<>();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("roReplicaSet");
        readOnlyReplicaSetList.add(replicaSet);

        when(dBaasMetaService.getDefaultClient().listReplicaSetSubIns(anyString(), anyString(), anyString()))
                .thenReturn(new com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetListResult().items(readOnlyReplicaSetList));
        when(dBaasMetaService.getDefaultClient().getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);

        Map<String, Object> result = cloudDiskCompressionHelper.checkReadInsCompressionModeOn("requestId", "primaryReplicaSetName", false);
        assertTrue((boolean) result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE));
    }

    @Test
    public void checkReadInsCompressionModeOn_ReadOnlyCompressionModeOff_ReturnsNotSupport() throws ApiException, RdsException {
        List<ReplicaSet> readOnlyReplicaSetList = new ArrayList<>();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("roReplicaSet");
        readOnlyReplicaSetList.add(replicaSet);

        when(dBaasMetaService.getDefaultClient().listReplicaSetSubIns(anyString(), anyString(), anyString()))
                .thenReturn(new com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetListResult().items(readOnlyReplicaSetList));
        when(dBaasMetaService.getDefaultClient().getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF);

        Map<String, Object> result = cloudDiskCompressionHelper.checkReadInsCompressionModeOn("requestId", "primaryReplicaSetName", false);
        assertFalse((boolean) result.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE));
    }

    @Test(expected = RdsException.class)
    public void checkReadInsCompressionModeOn_ReadOnlyCompressionModeOff_ThrowsException() throws ApiException, RdsException {
        List<ReplicaSet> readOnlyReplicaSetList = new ArrayList<>();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("roReplicaSet");
        readOnlyReplicaSetList.add(replicaSet);

        when(dBaasMetaService.getDefaultClient().listReplicaSetSubIns(anyString(), anyString(), anyString()))
                .thenReturn(new com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSetListResult().items(readOnlyReplicaSetList));
        when(dBaasMetaService.getDefaultClient().getReplicaSetLabel(anyString(), anyString(), anyString()))
                .thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_OFF);

        cloudDiskCompressionHelper.checkReadInsCompressionModeOn("requestId", "primaryReplicaSetName", true);
    }

    @Test
    public void getCompressionRatio_ValidParamCompressionRatio_ReturnsParsedValue() throws ApiException {
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "replicaSetName", "1.5");
        assertEquals(1.5, result, 0.0);
    }

    @Test
    public void getCompressionRatio_ReplicaSetNameBlank_ReturnsDefault() throws ApiException {
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "", null);
        assertEquals(1.0, result, 0.0);
    }

    @Test
    public void getCompressionRatio_InvalidParamCompressionRatioAndValidSrcCompressionRatio_ReturnsSrcCompressionRatio() throws ApiException {
        when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString())).thenReturn("1.5");
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "replicaSetName", "invalid");
        assertEquals(1.5, result, 0.0);
    }

    @Test
    public void getCompressionRatio_InvalidParamCompressionRatioAndInvalidSrcCompressionRatio_ReturnsDefault() throws ApiException {
        when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString())).thenReturn("invalid");
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "replicaSetName", "invalid");
        assertEquals(1.0, result, 0.0);
    }

    @Test
    public void getCompressionRatio_ExceptionCompressionRatioAndInvalidSrcCompressionRatio_ReturnsDefault() throws ApiException {
        when(defaultApi.getReplicaSetLabel(anyString(), anyString(), anyString())).thenThrow(new ApiException());
        Double result = cloudDiskCompressionHelper.getCompressionRatio("requestId", "replicaSetName", "invalid");
        assertEquals(1.0, result, 0.0);
    }

    @Test
    public void checkCompressionRatioStr_ValidCompressionRatio_ReturnsTrue() {
        boolean result = cloudDiskCompressionHelper.checkCompressionRatioStr("1.5");
        assertTrue(result);
    }

    @Test
    public void checkCompressionRatioStr_InvalidCompressionRatio_ReturnsFalse() {
        boolean result = cloudDiskCompressionHelper.checkCompressionRatioStr("invalid");
        assertFalse(result);
    }

    @Test
    public void convertDiskSizeGBToMB_NullInput_ReturnsZero() {
        Integer diskSizeGB = null;
        Integer expectedMB = 0;
        Integer actualMB = CloudDiskCompressionHelper.convertDiskSizeGBToMB(diskSizeGB);
        assertEquals(expectedMB, actualMB);
    }

    @Test
    public void convertDiskSizeGBToMB_NegativeInput_ReturnsZero() {
        Integer diskSizeGB = -1;
        Integer expectedMB = 0;
        Integer actualMB = CloudDiskCompressionHelper.convertDiskSizeGBToMB(diskSizeGB);
        assertEquals(expectedMB, actualMB);
    }

    @Test
    public void convertDiskSizeGBToMB_ZeroInput_ReturnsZero() {
        Integer diskSizeGB = 0;
        Integer expectedMB = 0;
        Integer actualMB = CloudDiskCompressionHelper.convertDiskSizeGBToMB(diskSizeGB);
        assertEquals(expectedMB, actualMB);
    }

    @Test
    public void convertDiskSizeGBToMB_PositiveInput_ReturnsCorrectMB() {
        Integer diskSizeGB = 5;
        Integer expectedMB = 5 * 1024;
        Integer actualMB = CloudDiskCompressionHelper.convertDiskSizeGBToMB(diskSizeGB);
        assertEquals(expectedMB, actualMB);
    }

}


