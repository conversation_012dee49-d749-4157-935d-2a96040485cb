package com.aliyun.dba.onecs.action;

import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LogFactory.class})
public class CreateDBInstanceImplTest {
    @InjectMocks
    private CreateDBInstanceImpl createDBInstanceImpl;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;
    @Mock
    private KmsService kmsService;
    @Mock
    private ClusterService clusterService;
    @Mock
    private ResourceService resourceService;
    @Mock
    private MySQLServiceImpl mySQLservice;
    @Mock
    private DbsGateWayService dbsGateWayService;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private CrossArchService crossArchService;
    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    private com.aliyun.dba.poddefault.action.CreateDBInstanceImpl poddefaultCreateDBInstance;

    @Test
    public void test_CreateDBInstance_k8s() throws Exception {
        CustInstanceDO custInstanceDO=new CustInstanceDO();
        when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(custInstanceDO);
        Map<String,String> m =new HashMap<>();
        when(mysqlParameterHelper.getUID()).thenReturn("uid");
        ResourceDO resourceDO=new ResourceDO();
        resourceDO.setRealValue("ON");
        when(resourceService.getResourceByResKey(PodDefaultConstants.DOCKERONECS_CLONE_TO_K8S_SWITCH)).thenReturn(resourceDO);
        when(mySQLservice.isRebuildBackupSet(any())).thenReturn(true);
        DescribeRestoreBackupSetResponse response=new DescribeRestoreBackupSetResponse();
        DescribeRestoreBackupSetResponse.BackupSetInfo info =new DescribeRestoreBackupSetResponse.BackupSetInfo();
        DescribeRestoreBackupSetResponse.ExtraInfo extraInfo=new DescribeRestoreBackupSetResponse.ExtraInfo();
        extraInfo.setMINOR_VERSION("123");
        info.setInstanceKindCode("1");
        info.setBackupId("1");
        info.setExtraInfoObj(extraInfo);
        response.setBackupSetInfoObj(info);
        when(crossArchService.onecsCloneToK8s(any())).thenReturn(true);
        when(crossArchService.checkNewArmClusterSupport(any())).thenReturn(true);
        when(dbsGateWayService.describeRestoreBackupSet(any())).thenReturn(response);
        when(mysqlParamSupport.getParameterValue(m, "storage")).thenReturn("100");
        Map<String, Object> result = createDBInstanceImpl.doActionRequest(custInstanceDO, m);
        Assert.assertNotNull(result);
    }

    @Test
    public void test_CreateDBInstance_k8s_rebuild() throws Exception {
        CustInstanceDO custInstanceDO=new CustInstanceDO();
        when(mysqlParamSupport.getAndCheckDBType(any(),any())).thenReturn("mysql");
        when(mysqlParamSupport.getAndCheckDBVersion(anyMap(),anyString(),anyBoolean())).thenReturn("5.7");
        when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(custInstanceDO);
        Map<String,String> m =new HashMap<>();
        when(mysqlParameterHelper.getUID()).thenReturn("uid");
        ResourceDO resourceDO=new ResourceDO();
        resourceDO.setRealValue("ON");
        when(resourceService.getResourceByResKey(PodDefaultConstants.DOCKERONECS_CLONE_TO_K8S_SWITCH)).thenReturn(resourceDO);
        when(mysqlParamSupport.getSourceDBInstanceID(any())).thenReturn("source");
        CustInstanceDO instance=new CustInstanceDO();
        instance.setDbType("mysql");
        instance.setDbVersion("5.7");
        when(mysqlParamSupport.getAndCheckCustInstanceById(any(),any())).thenReturn(instance);
        DescribeRestoreBackupSetResponse response=new DescribeRestoreBackupSetResponse();
        DescribeRestoreBackupSetResponse.BackupSetInfo info =new DescribeRestoreBackupSetResponse.BackupSetInfo();
        DescribeRestoreBackupSetResponse.ExtraInfo extraInfo=new DescribeRestoreBackupSetResponse.ExtraInfo();
        extraInfo.setMINOR_VERSION("123");
        info.setInstanceKindCode("1");
        info.setBackupId("1");
        info.setExtraInfoObj(extraInfo);
        response.setBackupSetInfoObj(info);
        when(crossArchService.onecsCloneToK8s(any())).thenReturn(true);
        when(crossArchService.checkNewArmClusterSupport(any())).thenReturn(true);
        when(dbsGateWayService.describeRestoreBackupSet(any())).thenReturn(response);
        when(mysqlParamSupport.getParameterValue(m, "storage")).thenReturn("100");
        Map<String, Object> result = createDBInstanceImpl.doActionRequest(custInstanceDO, m);
        Assert.assertNotNull(result);
    }
}
