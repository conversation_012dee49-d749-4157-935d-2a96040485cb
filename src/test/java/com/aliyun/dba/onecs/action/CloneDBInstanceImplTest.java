package com.aliyun.dba.onecs.action;

import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LogFactory.class})
public class CloneDBInstanceImplTest {
    @InjectMocks
    private CloneDBInstanceImpl cloneDBInstanceImpl;
    @Mock
    private MysqlParameterHelper mysqlParameterHelper;
    @Mock
    private KmsService kmsService;
    @Mock
    private ClusterService clusterService;
    @Mock
    private ResourceService resourceService;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private CrossArchService crossArchService;
    @Mock
    private com.aliyun.dba.poddefault.action.CloneDBInstanceImpl poddefaultCloneDBInstance;

    @Test
    public void test_CloneDBInstance_k8s() throws Exception {
        CustInstanceDO custInstanceDO=new CustInstanceDO();
        when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(custInstanceDO);
        Map<String,String> m =new HashMap<>();
        when(mysqlParameterHelper.getUID()).thenReturn("uid");
        ClustersDO clustersDO=new ClustersDO();
        clustersDO.setType(0);
        when(clusterService.getClusterByClusterName(any())).thenReturn(clustersDO);
        ResourceDO resourceDO=new ResourceDO();
        resourceDO.setRealValue("ON");
        when(crossArchService.onecsCloneToK8s(any())).thenReturn(true);
        when(crossArchService.checkNewArmClusterSupport(any())).thenReturn(true);
        when(resourceService.getResourceByResKey(PodDefaultConstants.DOCKERONECS_CLONE_TO_K8S_SWITCH)).thenReturn(resourceDO);
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(CustInstanceDO.class),anyString())).thenReturn(true);
        when(mysqlParamSupport.getAndCheckSourceCustInstance(any())).thenReturn(custInstanceDO);
        Map<String, Object> result = cloneDBInstanceImpl.doActionRequest(custInstanceDO, m);
        Assert.assertNotNull(result);
    }
}
