package com.aliyun.dba.onecs.action;

import com.aliyun.dba.base.action.service.CrossArchService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LogFactory.class})
public class EvaluateRegionResourceImplTest {
    @InjectMocks
    private EvaluateRegionResourceImpl evaluateRegionResourceImpl;
    @Mock
    private ResourceService resourceService;
    @Mock
    private CrossArchService crossArchService;
    @Mock
    private com.aliyun.dba.poddefault.action.EvaluateRegionResourceImpl poddefaultEvaluateDockerToK8SResource;

    @Test
    public void test_evaluate() throws RdsException {
        CustInstanceDO custInstanceDO=new CustInstanceDO();
        Map<String,String> m =new HashMap<>();
        ResourceDO resourceDO=new ResourceDO();
        resourceDO.setRealValue("ON");
        when(crossArchService.onecsCloneToK8s(any())).thenReturn(true);
        when(crossArchService.checkNewArmClusterSupport(any())).thenReturn(true);
        when(resourceService.getResourceByResKey(PodDefaultConstants.DOCKERONECS_CLONE_TO_K8S_SWITCH)).thenReturn(resourceDO);
        Map<String, Object> stringObjectMap = evaluateRegionResourceImpl.doActionRequest(custInstanceDO, m);
        Assert.assertNotNull(stringObjectMap);
    }
}
