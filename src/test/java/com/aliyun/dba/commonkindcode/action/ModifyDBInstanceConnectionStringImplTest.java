package com.aliyun.dba.commonkindcode.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class})
public class ModifyDBInstanceConnectionStringImplTest {
    @InjectMocks
    private ModifyDBInstanceConnectionStringImpl modifyDBInstanceConnectionStringImpl;
    @Mock
    private DBaasMetaService dBaasMetaService;

    private DefaultApi defaultApi;
    @Mock
    private MysqlParamSupport paramSupport;
    @Mock
    private MysqlParameterHelper mysqlParaHelper;
    @Mock
    private CustinsService custinsService;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultApi = PowerMockito.mock(DefaultApi.class);
        PowerMockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
    }

    @Test
    public void isConnectiongStringToSsl() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID.toLowerCase(), "testRequestId");
        params.put(ParamConstants.USER_ID.toLowerCase(), "test001");
        params.put(ParamConstants.UID.toLowerCase(), "111");
        params.put("connectionstring", "test");
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsName("test");
        custins.setLockMode(2);
        custins.setStatus(1);
        custins.setType("x");

        when(mysqlParaHelper.getAndCheckCustInstance()).thenReturn(custins);
        when(paramSupport.isConnectionStringToSsl(params, custins)).thenReturn(true);
        custinsService.checkConnAddrChangeTimesExceed(123, "test",null);

        Map<String, Object> result = modifyDBInstanceConnectionStringImpl.doActionRequest(custins, params);
        System.out.print(JSONObject.toJSONString(result));
        Object[] errorArray = (Object[]) result.get("errorCode");
        assertEquals(ErrorCode.INVALID_CONNECTIONSTRING.getCode(), errorArray[0]);
    }
}