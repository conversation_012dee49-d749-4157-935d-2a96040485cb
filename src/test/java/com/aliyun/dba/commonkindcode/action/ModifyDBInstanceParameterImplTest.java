package com.aliyun.dba.commonkindcode.action;


import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.property.RdsException;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class ModifyDBInstanceParameterImplTest {
    @Mock
    MysqlParameterHelper mysqlParaHelper;


    @InjectMocks
    ModifyDBInstanceParameterImpl modifyDBInstanceParameterImpl;


    @Test
    public void doActionRequest() throws RdsException {
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(123);
        custins.setInsName("test");

        when(mysqlParaHelper.getAndCheckCustInstance()).thenReturn(custins);
        when(mysqlParaHelper.getAndCheckDBInstanceName()).thenReturn("test");
        when(mysqlParaHelper.getParameterValue("DryRun")).thenReturn("true");

        var response = modifyDBInstanceParameterImpl.doActionRequest(null, null);

        assertNotNull(response);
        assertEquals("test", response.get("DBInstanceName"));
        assertEquals(123, response.get("DBInstanceID"));
    }
}