
package com.aliyun.dba.commonkindcode.support.helper;

import java.math.*;

import com.aliyun.dba.base.support.SizeUnitTransTool;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.common.util.SpringContextUtil;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtil.class, SizeUnitTransTool.class})
public class InnodbLogFileSizeHelperTest {

    @InjectMocks
    private InnodbLogFileSizeHelper logFileSizeHelper;

    @Mock
    private DbossApi dbossApi;

    @Mock
    private ParamContext paramContext;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(SizeUnitTransTool.class);
        when(SpringContextUtil.getBeanByClass(DbossApi.class)).thenReturn(dbossApi);
    }

    @Test
    public void testValidator_ValidValue_ReturnsTrue() throws Exception {
        when(paramContext.getDiskSize()).thenReturn(1024 * 100L);
        when(SizeUnitTransTool.trans("10M", "B")).thenReturn(new BigDecimal(10 * 1024 * 1024));
        when(dbossApi.getParameter(anyInt(), anyString())).thenReturn(createMockParameter(2));

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("innodb_log_file_size", "10M");
        assertTrue(logFileSizeHelper.validator(parameters, "key", paramContext));
    }

    @Test
    public void testValidator_InvalidValue_ReturnsFalse() throws Exception {
        when(paramContext.getDiskSize()).thenReturn(10L);
        when(SizeUnitTransTool.trans("50M", "B")).thenReturn(new BigDecimal(50 * 1024 * 1024));
        when(dbossApi.getParameter(anyInt(), anyString())).thenReturn(createMockParameter(2));

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("innodb_log_file_size", "50M");
        assertFalse(logFileSizeHelper.validator(parameters, "key", paramContext));
    }

    @Test(expected = RdsException.class)
    public void testValidator_ExceptionThrown() throws Exception {
        when(paramContext.getDiskSize()).thenReturn(100L);
        when(SizeUnitTransTool.trans("10M", "B")).thenReturn(new BigDecimal(10 * 1024 * 1024));
        when(dbossApi.getParameter(anyInt(), anyString())).thenThrow(new RuntimeException("Test Exception"));

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("innodb_log_file_size", "10M");
        logFileSizeHelper.validator(parameters, "key", paramContext);
    }

    private Map<String, Object> createMockParameter(int fileInGroups) {
        Map<String, Object> mockParameter = new HashMap<>();
        mockParameter.put("innodb_log_files_in_group", fileInGroups);
        return mockParameter;
    }
}