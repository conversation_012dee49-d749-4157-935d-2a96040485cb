
package com.aliyun.dba.commonkindcode.action;

import java.util.*;

import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.AuroraListDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;


import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_TYPE_EXCLUSIVE;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class SwitchDBInstanceHAImplTest {

    @InjectMocks
    private SwitchDBInstanceHAImpl switchDBInstanceHA;

    @Mock
    private MysqlParameterHelper mysqlParameterHelper;

    @Mock
    private MysqlParamSupport paramSupport;

    @Mock
    private InstanceService instanceService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private TaskService taskService;

    @Mock
    private LogAgent logAgent;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testDoActionRequest_Success() throws RdsException {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setStatus(1);
        custins.setLevelId(1);
        custins.setType(String.valueOf(CUSTINS_TYPE_EXCLUSIVE));

        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(custins);
        InstanceDO master = new InstanceDO();
        master.setId(123);
        master.setRole(0);
        InstanceDO slave = new InstanceDO();
        slave.setId(234);
        slave.setRole(1);
        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(Arrays.asList(master, slave));
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(new InstanceLevelDO());
        AuroraListDO auroraListDO = new AuroraListDO();
        auroraListDO.setStatus(0);
        when(custinsService.getAuroraListByCustinsId(anyInt())).thenReturn(auroraListDO);
        when(paramSupport.parseCheckSwitchTimeTimeZoneSafe(anyMap())).thenReturn(new Date());
        when(mysqlParameterHelper.hasParameter(anyString())).thenReturn(true);
        when(mysqlParameterHelper.getParameterValue(ParamConstants.TARGET_INSTANCE_ID)).thenReturn("234");

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.SWITCH_TIME_MODE.toLowerCase(), CustinsSupport.SWITCH_NOW);
        params.put(ParamConstants.ACTION, "SwitchDBInstanceHA");
        // Act
        Map<String, Object> result = switchDBInstanceHA.doActionRequest(custins, params);

        // Assert
        assertTrue(result.containsKey("TaskId"));
    }



    @Test
    public void testDoActionRequest_General_Success() throws RdsException {
        // Arrange
        CustInstanceDO custins = new CustInstanceDO();
        custins.setId(1);
        custins.setStatus(1);
        custins.setLevelId(1);
        custins.setType(String.valueOf(CUSTINS_TYPE_EXCLUSIVE));
        custins.setParentId(1);

        when(mysqlParameterHelper.getAndCheckCustInstance()).thenReturn(custins);
        InstanceDO master = new InstanceDO();
        master.setId(123);
        master.setRole(0);
        master.setCustinsId(1);
        when(instanceService.getInstanceByCustinsId(anyInt())).thenReturn(Collections.singletonList(master));
        when(instanceService.getInstanceByInsId(anyInt())).thenReturn(master);
        when(custinsService.getCustInstanceByCustinsId(anyInt())).thenReturn(custins);
        InstanceLevelDO levelDO = new InstanceLevelDO();
        levelDO.setCategory("general");
        when(instanceService.getInstanceLevelByLevelId(anyInt())).thenReturn(levelDO);
        AuroraListDO auroraListDO = new AuroraListDO();
        auroraListDO.setStatus(0);
        when(custinsService.getAuroraListByCustinsId(anyInt())).thenReturn(auroraListDO);
        when(paramSupport.parseCheckSwitchTimeTimeZoneSafe(anyMap())).thenReturn(new Date());
        when(mysqlParameterHelper.hasParameter(anyString())).thenReturn(true);
        when(mysqlParameterHelper.getParameterValue(ParamConstants.TARGET_INSTANCE_ID)).thenReturn("234");

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.SWITCH_TIME_MODE.toLowerCase(), CustinsSupport.SWITCH_NOW);
        params.put(ParamConstants.ACTION, "SwitchDBInstanceHA");
        // Act
        Map<String, Object> result = switchDBInstanceHA.doActionRequest(custins, params);

        // Assert
        assertTrue(result.containsKey("TaskId"));
    }


}