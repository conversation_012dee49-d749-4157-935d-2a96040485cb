package com.aliyun.backup;

import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.IPWhiteList;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.response.backup.OssBakRestoreResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.poddefault.action.service.AligroupCreateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.AliyunCreateDBInstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.ClusterBackUpService;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.aliyun.support.BaseTest;
import org.apache.jute.compiler.JString;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

public class CreateDBInstanceWithOssTest extends BaseTest {
    private static final LogAgent LOG = LogFactory.getLogAgent(CreateDBInstanceWithOssTest.class);

    @Override
    public void init() {
        System.out.println("init  CreateDBInstanceWithOssTest ------------");
    }

    @Test
    public void testBackUpOssImpl() {

        HashMap<String, String> params = new HashMap<String, String>();
        params.put("ResourceGroupId", "rg-58aiyyy78vgw86n");
        params.put("DBInstanceNetType", "1");
        params.put("DBInstanceType", "x");
        params.put("Timezone", "china");
        params.put("MultiAVZExParam", "%7B%22availableZoneInfoList%22%3A%5B%7B%22isUserSpecified%22%3Atrue%2C%22region%22%3A%22cn-zhangjiakou-a-aliyun%22%2C%22role%22%3A%22master%22%2C%22zoneID%22%3A%22cn-zhangjiakou-a%22%7D%5D%7D");
        params.put("StorageEngine", "innodb");
        params.put("SecurityIPList", "0.0.0.0%2F0");
        params.put("ClusterName", "test_mysql_dedicate_group_console");
        params.put("OpsServiceVersion", "2.0");
        params.put("Engine", "mysql");
        params.put("DBInstanceName", "k8smysqlbasic1602565479pk3");
        params.put("EngineVersion", "5.7");
        params.put("DBInstanceClass", "mysql.n1.micro.1");
        params.put("VPCId", "vpc-xxxmmxjqqi8go8153uq3b");
        params.put("DBInstanceStorageType", "cloud_ssd");
        params.put("VswitchId", "vsw-8vb5wxejg4owbtmkdepzo");
        params.put("Storage", "20");
        params.put("Region", "cn-zhangjiakou-a-aligroup");
        params.put("regionid", "ap-southeast-1");
        params.put("OssFileSize", "2097152");
        params.put("OssUrl", "https://mysql57-restore.oss-cn-zhangjiakou.aliyuncs.com/backupall_qp.xb?Expires=1606220745%26OSSAccessKeyId=TMP.3KeazaTKrA98SB8oxqNFwTQxqpqv9JAByJVAQgcb1nzkkRa9WYqpG3bdzXpcmwcD9Q6bBWZdmnuHBQBQmig8aoa23xZ13p%26Signature=dP%252BDGGD4FdZGQsGfHVVSSehRSF0%253D");
        params.put("OssBucket", "test");
        params.put("OssFilePath", "test");
        params.put("PreferredBackupTime".toLowerCase(), "18:28Z");
        params.put("preferredbackupperiod".toLowerCase(), "1010101");
        params.put("OssFileName", "backup_all_qp.xb");
        params.put("OssFileMetaData", "");

        try {

            ClusterBackUpService clusterBackUpService = new ClusterBackUpService();

            Field mySQLAvzService = clusterBackUpService.getClass().getDeclaredField("mySQLAvzService");
            mySQLAvzService.setAccessible(true);
            MySQLAvzService mySQLAvzServiceObject = mock(MySQLAvzService.class);
            mySQLAvzService.set(clusterBackUpService, mySQLAvzServiceObject);

            Field podParameterHelper = clusterBackUpService.getClass().getDeclaredField("podParameterHelper");
            podParameterHelper.setAccessible(true);
            podParameterHelper.set(clusterBackUpService, mock(PodParameterHelper.class));

            Field minorVersionServiceHelper = clusterBackUpService.getClass().getDeclaredField("minorVersionServiceHelper");
            minorVersionServiceHelper.setAccessible(true);
            minorVersionServiceHelper.set(clusterBackUpService, mock(MinorVersionServiceHelper.class));

            Field mysqlParamSupport = clusterBackUpService.getClass().getDeclaredField("mysqlParamSupport");
            mysqlParamSupport.setAccessible(true);
            MysqlParamSupport mysqlParamSupportObject = mock(MysqlParamSupport.class);
            mysqlParamSupport.set(clusterBackUpService, mysqlParamSupportObject);

            Field backupService = clusterBackUpService.getClass().getDeclaredField("backupService");
            backupService.setAccessible(true);
            BackupService backupServiceObject = mock(BackupService.class);
            backupService.set(clusterBackUpService, backupServiceObject);

            Field dBaasMetaService = clusterBackUpService.getClass().getDeclaredField("dBaasMetaService");
            dBaasMetaService.setAccessible(true);
            DBaasMetaService dBaasMetaServiceObject = mock(DBaasMetaService.class);
            dBaasMetaService.set(clusterBackUpService, dBaasMetaServiceObject);

            Field workFlowService = clusterBackUpService.getClass().getDeclaredField("workFlowService");
            workFlowService.setAccessible(true);
            WorkFlowService workFlowServiceObject = mock(WorkFlowService.class);
            workFlowService.set(clusterBackUpService, workFlowServiceObject);

            Field custinsParamService = clusterBackUpService.getClass().getDeclaredField("custinsParamService");
            custinsParamService.setAccessible(true);
            custinsParamService.set(clusterBackUpService, mock(CustinsParamService.class));

            Field commonProviderService = clusterBackUpService.getClass().getDeclaredField("commonProviderService");
            commonProviderService.setAccessible(true);
            CommonProviderService commonProviderServiceObject = mock(CommonProviderService.class);
            commonProviderService.set(clusterBackUpService, commonProviderServiceObject);

            when(backupServiceObject.createOssBakRestore(any())).thenReturn(new OssBakRestoreResponse());
            when(workFlowServiceObject.dispatchTask(any(), any(), any(), any(), any(), any())).thenReturn(1);

            when(mysqlParamSupportObject.getParameterValue(anyMap(), anyString())).thenReturn(String.valueOf(1024));
            when(mysqlParamSupportObject.isDHGCluster(any())).thenReturn(true);

            when(dBaasMetaServiceObject.getDefaultClient()).thenReturn(new DefaultApi());
            when(commonProviderServiceObject.getDefaultApi()).thenReturn(new com.aliyun.apsaradb.activityprovider.api.DefaultApi());

            when(mySQLAvzServiceObject.getRoleZoneId(any(), any())).thenReturn(new AvailableZoneInfoDO());

            ResourceService rs = mock(ResourceService.class);
            Map<String, String> result = clusterBackUpService.getBackClusterConfig(params.get("Region"), rs);

            Integer diskSize = clusterBackUpService.getDiskSize(params);

            clusterBackUpService.getDiskSize(params);

            List<Map<String, String>> az = new ArrayList<>();
            HashMap<String, String> zoneinfo = new HashMap<>();
            zoneinfo.put("RealValue", "{\"ap-southeast-1\":\"ap-southeast-1a\", \"Cluster-ap-southeast-1\":\"dhg-b8k1209227704n6j\",\"SubDomain-ap-southeast-1\":\"ap-southeast-1a\"}");
            az.add(zoneinfo);

            when(rs.getResourceMapList(any())).thenReturn(az);
            clusterBackUpService.getBackClusterConfig("ap-southeast-1", rs);

            try {
                clusterBackUpService.createRestoreTaskWithOss("", params.get("Engine"), params.get("EngineVersion"), params.get("dbInstanceName"), params);
            } catch (Exception e) {
            }


            /** condition test */
            try {
                params.put("OssFileSize", "0");
                clusterBackUpService.createRestoreTaskWithOss("", params.get("Engine"), params.get("EngineVersion"), params.get("dbInstanceName"), params);
            } catch (Exception e) {

            }
            try {
                params.put("PreferredBackupTime", "0");
                clusterBackUpService.createRestoreTaskWithOss("", params.get("Engine"), params.get("EngineVersion"), params.get("dbInstanceName"), params);
            } catch (Exception e) {
            }

            params.put("OssFileSize", "10000000000000");
            diskSize = clusterBackUpService.getDiskSize(params);

            params.put("OssFileSize", "10000000000");
            diskSize = clusterBackUpService.getDiskSize(params);


            ReplicaSetResourceRequest replicaSetResourceRequestZone = clusterBackUpService.allocateClusterResource("uelbert01", "0", "k8smysqlbasic1602565479pk3",
                    "", params.get("ClusterName"), params.get("dbType"), params.get("dbVersion"), params.get("portStr"),
                    params.get("insTypeDesc"), new InstanceLevel(), ReplicaSet.BizTypeEnum.ALIYUN, params.get("classCode")
                    , params.get("diskType"), 10, new AVZInfo(null, "", "", "", null), new Replica.RoleEnum[]{Replica.RoleEnum.MASTER}, params.get("dbEngine"));

            ReplicaSetResourceRequest replicaSetResourceRequest = clusterBackUpService.allocateClusterResource("uelbert01", "0", "k8smysqlbasic1602565479pk3",
                    "cn-hangzhou", params.get("ClusterName"), params.get("dbType"), params.get("dbVersion"), params.get("portStr"),
                    params.get("insTypeDesc"), new InstanceLevel(), ReplicaSet.BizTypeEnum.ALIYUN, params.get("classCode")
                    , params.get("diskType"), 10, new AVZInfo(null, "", "", "", null), new Replica.RoleEnum[]{Replica.RoleEnum.MASTER}, params.get("dbEngine"));

            try {
                clusterBackUpService.allocateDBInstanceResource("", params.get("dbInstanceName"), new IPWhiteList(),
                        null,
                        replicaSetResourceRequest);
            } catch (Exception e) {
            }

            AliyunCreateDBInstanceService aliyunCreateDBInstanceService = new AliyunCreateDBInstanceService();


            Field mysqlParamSupport2 = aliyunCreateDBInstanceService.getClass().getDeclaredField("mysqlParamSupport");
            mysqlParamSupport2.setAccessible(true);
            MysqlParamSupport mysqlParamSupportObject2 = mock(MysqlParamSupport.class);
            mysqlParamSupport2.set(aliyunCreateDBInstanceService, mysqlParamSupportObject2);

            Field clusterBackUpService2 = aliyunCreateDBInstanceService.getClass().getDeclaredField("clusterBackUpService");
            clusterBackUpService2.setAccessible(true);
            ClusterBackUpService clusterBackUpServiceObject2 = mock(ClusterBackUpService.class);
            clusterBackUpService2.set(aliyunCreateDBInstanceService, clusterBackUpServiceObject2);

            Field resourceService2 = aliyunCreateDBInstanceService.getClass().getDeclaredField("resourceService");
            resourceService2.setAccessible(true);
            resourceService2.set(aliyunCreateDBInstanceService, rs);

            try {
                aliyunCreateDBInstanceService.createDBInstanceWithOss(params);
            } catch (Exception e) {
            }

            AliyunCreateDBInstanceService aliyunCreateDBInstanceService3 = new AliyunCreateDBInstanceService();


            Field mysqlParamSupport3 = aliyunCreateDBInstanceService.getClass().getDeclaredField("mysqlParamSupport");
            mysqlParamSupport3.setAccessible(true);
            MysqlParamSupport mysqlParamSupportObject3 = mock(MysqlParamSupport.class);
            mysqlParamSupport3.set(aliyunCreateDBInstanceService3, mysqlParamSupportObject3);

            Field clusterBackUpService3 = aliyunCreateDBInstanceService.getClass().getDeclaredField("clusterBackUpService");
            clusterBackUpService3.setAccessible(true);
            ClusterBackUpService clusterBackUpServiceObject3 = mock(ClusterBackUpService.class);
            Map<String, String> rrs = new HashMap<>();
            rrs.put("zoneId", "");
            rrs.put("clusterName", "");
            rrs.put("subDomain", "subDomain");
            when(clusterBackUpServiceObject3.getBackClusterConfig(any(), any())).thenReturn(rrs);
            clusterBackUpService3.set(aliyunCreateDBInstanceService3, clusterBackUpServiceObject3);


            try {
                aliyunCreateDBInstanceService3.createDBInstanceWithOss(params);
            } catch (Exception e) {
            }


        } catch (Exception e) {
            //Assert.assertTrue("The request processing has failed due to some unknown error, exception or failure.".equals(e.getMessage()));
        }

    }

}

