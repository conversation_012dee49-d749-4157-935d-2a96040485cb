package com.aliyun.rm;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.alibaba.fastjson.JSONObject;

import com.alicloud.apsaradb.k8s.resmanager.Client;
import com.alicloud.apsaradb.k8s.resmanager.Constants;
import com.alicloud.apsaradb.k8s.resmanager.request.K8sNodeAnnotationRequest;
import com.alicloud.apsaradb.k8s.resmanager.request.K8sNodeLabelRequest;
import com.alicloud.apsaradb.k8s.resmanager.response.K8sNodeAnnotationRespData;
import com.alicloud.apsaradb.k8s.resmanager.response.K8sNodeLabelRespData;
import com.alicloud.apsaradb.k8s.resmanager.response.Response;
import com.google.gson.Gson;
import org.junit.Test;

/**
 * https://yuque.antfin.com/docs/share/2ad3bc3f-a954-4a9d-8b2d-582d99e1a9c3
 *
 * */
public class RmLabelTest {

    private Client getClient() {
        Client client = new Client("http://**************:8888");
        return client;
    }

    @Test
    public void testCreateK8sNodeLabels() {
        Client client = new Client("http://**************:8888");
        K8sNodeLabelRequest request = new K8sNodeLabelRequest();
        Map<String, String> map = new HashMap<>();
        //map.put(Constants.Ipv6Label, "true");
        map.put("rm.alibaba-inc.com/eniIpv6", "2406.1880.f.ff04.b673.480.4b1.11b9");
        request.setNodeNames(Arrays.asList("cn-zhangjiakou.i-8vbjau4bw6tse8t59poa","cn-zhangjiakou.i-8vbj4k19k5y2b30xaucb","cn-zhangjiakou.i-8vbhv1p5nq7akg78robh"));
        request.setLabels(map);
        try {
            Response<K8sNodeLabelRespData> response = client.createK8sNodeLabels(request);
            System.out.println(JSONObject.toJSONString(response));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }

    @Test
    public void testCreateK8sNodeAnnotations() {

        K8sNodeAnnotationRequest request = new K8sNodeAnnotationRequest();
        Map<String, String> map = new HashMap<String, String>();
        map.put("rm.alibaba-inc.com/eniIpv6", "2406:1880:f:ff04:b673:480:4b1:1112");
        //request.setNodeNames(Arrays.asList("cn-zhangjiakou.i-8vbjau4bw6tse8t59poa","cn-zhangjiakou.i-8vbj4k19k5y2b30xaucb","cn-zhangjiakou.i-8vbhv1p5nq7akg78robh"));
        request.setNodeNames(Arrays.asList("cn-zhangjiakou.i-8vbj4k19k5y2b30xaucb","cn-zhangjiakou.i-8vbhv1p5nq7akg78robh"));
        //request.setNodeNames(Arrays.asList("cn-zhangjiakou.i-8vbjau4bw6tse8t59poa"));
        request.setAnnotations(map);
        String requestId = UUID.randomUUID().toString();
        System.out.println("requestId: " + requestId);
        request.setRequestId(requestId);
        try {
            Response<K8sNodeAnnotationRespData> response = getClient().updateK8sNodeAnnotations(request);
            System.out.println(new Gson().toJson(response));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }

}
