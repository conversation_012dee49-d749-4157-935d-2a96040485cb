package com.aliyun.test;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.DateUTCFormat;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.aliyun.dba.custins.support.CustinsSupport.STORAGE_TYPE_LOCAL_SSD;

//@RunWith(SpringJUnit4ClassRunner.class)
public class MyTest {

    @Test
    public void testCheckParams() throws RdsException {

        String storageCategory = "cloud_essd";
        String storageStr = "20";
        Integer storage = null;

        try{
            if(storageStr != null){
                storage = Integer.valueOf(storageStr);
                //不能小于5，云盘不能小于20，必须为5的整数倍
                if(storage < 5 || (!STORAGE_TYPE_LOCAL_SSD.equalsIgnoreCase(storageCategory) && storage < 20) || storage % 5 !=0){
                    //logger.error("存储空间不能小于5GB，云盘不能小于20GB，且必须为5的整数倍");
                    throw new RdsException(ErrorCode.INVALID_STORAGE);
                }
            }
        }
        catch(Exception ex){
            //logger.error("parse storage fail", ex);
            throw new RdsException(ErrorCode.INVALID_STORAGE);
        }
    }


    private String parseReleaseDateFromMinorVersion(String minorVersion){

        //控制台格式
        String rdsPattern = "^rds_\\d{8}$";
        String physicalOnEcsPattern = "^mysql\\d{0,2}_\\d{8}$";
        String physicalXclusterPattern = "^xcluster\\d{0,2}_\\d{8}$";
        String dockerPattern = "^apsaradb-alios\\d{1}u-mysql\\d{2,4}_\\d{8}-\\d{14}$";
        String xclusterOnecsPattern = "^apsaradb-alios\\d{1}u-mysql_xdb\\d{2}:\\d{8}-\\d{14}$";

        List<String> patternList = new ArrayList<>();
        patternList.add(rdsPattern);
        patternList.add(physicalOnEcsPattern);
        patternList.add(physicalXclusterPattern);
        patternList.add(dockerPattern);
        patternList.add(xclusterOnecsPattern);

        for(String pattern : patternList){
            Matcher matcher = Pattern.compile(pattern).matcher(minorVersion);
            if(matcher.find()){
                String[] arr = minorVersion.split("_|-|:");
                for(String str : arr){
                    if(str.length() == 8){
                        try{
                            int dateDigit = Integer.valueOf(str);
                            return str;
                        }
                        catch(Exception ex){
                            //logger.error("cannot parse release data from minor version:"+minorVersion);
                            ex.printStackTrace();
                        }
                    }
                }
            }
        }

        return null;
    }

    @Test
    public void testCheckReadinsVersion(){
        String masterVersion = "5.6";
        String readinsVersion = "5.7";
        boolean res = readinsVersion.compareTo(masterVersion) < 0;
        Assert.assertEquals(res, false);
    }

}
