package com.aliyun.test;

import com.aliyun.dba.concurrentlimit.ConcurrentRule;
import com.aliyun.dba.concurrentlimit.KeyBasedConcurrentLimiter;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static java.util.Collections.singletonList;

public class KeyBasedConcurrentLimiterTest {

    public static void main(String[] args) {
        final KeyBasedConcurrentLimiter limiter = new KeyBasedConcurrentLimiter(""); // 假设每个Key最多允许3个并发

        // 模拟20个并发请求，它们随机地针对Key "A" 或 "B" 请求执行许可
        int threadsCount = 20;
        for (int i = 0; i < threadsCount; i++) {
            new Thread(() -> {

                String key = Math.random() > 0.5 ? "A" : "B";
                List<ConcurrentRule> concurrentRuleList = singletonList(ConcurrentRule.builder().key(key).maxPermits(3).tag("interface").build());
                boolean acquired = limiter.tryAcquire(concurrentRuleList);

                if (acquired) {
                    System.out.println(Thread.currentThread().getName() + " acquired permit for key " + key);

                    // 模拟执行某种操作
                    try {
                        Thread.sleep((long) (Math.random() * 1500));
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        limiter.release(concurrentRuleList);
                        System.out.println(Thread.currentThread().getName() + " released permit for key " + key);
                    }
                } else {
                    System.out.println(Thread.currentThread().getName() + " could not acquire permit for key " + key);
                }
            }).start();
        }
    }
}

