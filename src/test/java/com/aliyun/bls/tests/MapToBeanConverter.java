package com.aliyun.bls.tests;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import junitparams.converters.ConversionFailedException;
import junitparams.converters.Converter;
import junitparams.converters.Param;

// hard to handle TypeReference with generic T in Kotlin, written in Java instead
public class MapToBeanConverter<T> implements Converter<Param, T> {
    @Override
    public void initialize(Param param) {

    }

    @Override
    public T convert(Object obj) throws ConversionFailedException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(obj, new TypeReference<T>(){});
    }
}

