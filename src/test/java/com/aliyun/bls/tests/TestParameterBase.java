package com.aliyun.bls.tests;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import lombok.ToString;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.junit.ComparisonFailure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.NoResultException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

@ToString(callSuper = true, exclude = {"objectMap"})
public class TestParameterBase {
    protected static final Logger logger = LoggerFactory.getLogger(TestParameterBase.class);

    public static final ThreadLocal<Map<String, Object>> dynamicParamsTL = new ThreadLocal<>();

    Map<String, Object> objectMap;

    // make compatible with com.aliyun.cachetest.runners
    String isRun;

    public void setIsRun(String isRun) {
        this.CASE_DISABLED = String.valueOf((!Boolean.valueOf(isRun)));
    }

    public String getIsRun() {
        return this.CASE_DISABLED;
    }
    // --

    String CASE_DISABLED;

    public boolean isCaseDisabled() {
        return Boolean.valueOf(CASE_DISABLED);
    }

    String CASE_DESCRIBE;
    /**
     * expected return code
     */
    String EXPECT_RC;
    /**
     * expecte result DSL in JSON:
     * <p>
     * {
     * "EXISTS":["Required1","Required2","Data.Required"],
     * "EXPECT_EQ":{
     * "Required1":"Required1Value"
     * },
     * "EXPECT_EXCEPTION":"com.aliyun.dba.support.property.RdsException"
     * }
     */
    String EXPECT_RESULT;
    Map<String, Object> expectResultDsl;
    Class<?> expectExceptionClass;

    public String getEXPECT_RC() {
        return EXPECT_RC;
    }

    public void setEXPECT_RC(String EXPECT_RC) {
        this.EXPECT_RC = EXPECT_RC;
    }

    public static ThreadLocal<Map<String, Object>> getDynamicParamsTL() {
        return dynamicParamsTL;
    }

    public Map<String, Object> getObjectMap() {
        return objectMap;
    }

    public void setObjectMap(Map<String, Object> objectMap) {
        this.objectMap = objectMap;
    }

    public String getCASE_DISABLED() {
        return CASE_DISABLED;
    }

    public void setCASE_DISABLED(String CASE_DISABLED) {
        this.CASE_DISABLED = CASE_DISABLED;
    }

    public String getCASE_DESCRIBE() {
        return CASE_DESCRIBE;
    }

    public void setCASE_DESCRIBE(String CASE_DESCRIBE) {
        this.CASE_DESCRIBE = CASE_DESCRIBE;
    }

    public String getEXPECT_RESULT() {
        return EXPECT_RESULT;
    }

    public void setEXPECT_RESULT(String EXPECT_RESULT) {
        this.EXPECT_RESULT = EXPECT_RESULT;
    }

    public Map<String, Object> getExpectResultDsl() {
        return expectResultDsl;
    }

    public void setExpectResultDsl(Map<String, Object> expectResultDsl) {
        this.expectResultDsl = expectResultDsl;
    }

    public TestParameterBase(Map<String, Object> objectMap) throws InvocationTargetException, IllegalAccessException {
        this.objectMap = objectMap;
        List<String> toRemoveKeys = new ArrayList<>();
        for (Map.Entry<String, Object> e : objectMap.entrySet()) {
            Object v = e.getValue();
            String vv = null;
            if (null != v && (vv = v.toString()).toLowerCase().endsWith("[null]")) {
                toRemoveKeys.add(e.getKey());
            }
        }
        for (String s : toRemoveKeys) {
            Object removed = objectMap.remove(s);
        }
        BeanUtils.populate(this, objectMap);

        bindDynamicParams();

        // load EXPECT_RESULT DSL
        if (!Strings.isNullOrEmpty(EXPECT_RESULT)) {
            try {
                expectResultDsl = JSON.parseObject(EXPECT_RESULT);
                logger.info("case {} expect result DSL: {}", this.getCASE_DESCRIBE(), expectResultDsl);
                doTestPreprocessor();
            } catch (RuntimeException e) {
                logger.warn("case {} caught: {}; EXPECT_RESULT: {}", this.getCASE_DESCRIBE(), e.getMessage(), EXPECT_RESULT);
                // ignored
            }
        }
    }

    public void bindDynamicParams() {
        Map<String, Object> dynamicParams = dynamicParamsTL.get();
        if (null != dynamicParams) {

            Field[] allFields = this.getClass().getDeclaredFields();
            for (Field f : allFields) {
                if (f.getType().equals(String.class)) {
                    try {
                        String v = (String) f.get(this);
                        if (null == v || v.length() == 0) {
                            continue;
                        }
                        v = "[" + v.trim() + "]";
                        String vv = (String) dynamicParams.get(v);
                        f.set(this, vv);
                    } catch (ClassCastException e) {
                        // ignored
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    public Map<String, String> toQueryParams() {
        Map<String, String> m = new LinkedHashMap<>();
        m.putAll((Map) new BeanMap(this));

        // remove null attributes
        for (String i : new String[]{"metaClass", "class", "objectMap", "isRun", "CASE_DISABLED", "CASE_DESCRIBE", "EXPECT_RC", "EXPECT_RESULT"}) {
            m.remove(i);
        }
        List<String> toRemoveKeys = new ArrayList<>();
        for (String key : m.keySet()) {
            if (m.get(key) == null) {
                toRemoveKeys.add(key);
            }
        }
        for (String key : toRemoveKeys) {
            m.remove(key);
        }
        return m;
    }


    private void validateFieldsExist(List<String> fieldsExist, Map<String, Object> results) {
        if (!CollectionUtils.isEmpty(fieldsExist)) {
            return;
        }

        for (String f : fieldsExist) {
            String[] fieldDepth = f.split(".");
            Map<String, Object> traverseResults = results;
            if (fieldDepth.length > 1) {
                for (int i = 0, e = fieldDepth.length - 1; i < e; ++i) {
                    traverseResults = (Map<String, Object>) traverseResults.get(fieldDepth[i]);
                    if (null == traverseResults) {
                        throw new NoResultException("missing result field: " + f);
                    }
                }
            }
            Object v = traverseResults.get(fieldDepth[fieldDepth.length - 1]);
            if (null == v) {
                throw new NoResultException("missing result field: " + f);
            }
        }
    }

    private void doTestPreprocessor() {
        if (null != this.expectExceptionClass || null == expectResultDsl) {
            return;
        }
        String expectException = (String) expectResultDsl.get("EXPECT_EXCEPTION");

        logger.info("case {} EXPECT_EXCEPTION: {}",
            this.getCASE_DESCRIBE(), expectException);

        if (!Strings.isNullOrEmpty(expectException)) {
            try {
                Class<?> expectExceptionClass = Class.forName(expectException);
                logger.info("case {} EXPECT_EXCEPTION: {}; class: {}",
                    this.getCASE_DESCRIBE(), expectException, expectExceptionClass);
                this.expectExceptionClass = expectExceptionClass;
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
        }
    }

    public Set<Error> validateExpectedResults(Map<String, Object> results) {

        Set<Error> errors = new LinkedHashSet<>();

        if (null == results || !results.isEmpty()) {
            errors.add(new Error("empty result!"));
            return errors;
        }

        if (null != expectResultDsl) {
            validateFieldsExist((List<String>) expectResultDsl.get("EXISTS"), results);

            Map<String, Object> expectEq = (Map<String, Object>) expectResultDsl.get("EXPECT_EQ");
            if (null != expectEq) {
                // TODO: EXPECT_EQ handling
                for (Map.Entry<String, Object> e : expectEq.entrySet()) {
                    String k = e.getKey();
                    Object v = e.getValue();
                    String[] fieldDepth = k.split(".");
                    Map<String, Object> traverseResults = results;
                    if (fieldDepth.length > 1) {
                        Error error = null;
                        for (int i = 0, ee = fieldDepth.length - 1; i < ee; ++i) {
                            traverseResults = (Map<String, Object>) traverseResults.get(fieldDepth[i]);
                            if (null == traverseResults) {
                                error = new ComparisonFailure("missing expected result key", k, null);
                                break;
                            }
                        }
                        if (null != error) {
                            errors.add(error);
                            continue;
                        }
                    }
                    Object resultValue = traverseResults.get(fieldDepth[fieldDepth.length - 1]);
                    if (!v.equals(resultValue)) {
                        String resultValueStr = "[null]";
                        if (null != resultValue) {
                            resultValueStr = resultValue.toString();
                        }
                        errors.add(new ComparisonFailure("not matching value for key: " + k, v.toString(), resultValueStr));
                    }
                }
            }
        }
        return errors;
    }

    public Map<String, Object> doTest(Function<Void, Map<String, Object>> handler) {
        logger.info("test parameters: " + this.toString());
        if (this.isCaseDisabled()) {
            return Collections.emptyMap();
        }

        try {
            Map<String, Object> results = handler.apply(null);
            validateExpectedResults(results);
            return results;
        } catch (Throwable e) {
            if (null != this.expectExceptionClass && e.getClass().equals(this.expectExceptionClass)) {
                logger.warn("case {} caught: {}; caught class: {}, expect exception class: {}",
                    this.getCASE_DESCRIBE(), e.getMessage(), e.getClass(), this.expectExceptionClass);
                return Collections.emptyMap();
            }
            logger.error("case {} caught: {}; caught class: {}, expect exception class: {}",
                this.getCASE_DESCRIBE(), e.getMessage(), e.getClass(), this.expectExceptionClass);
            throw e;
        }
    }


}
