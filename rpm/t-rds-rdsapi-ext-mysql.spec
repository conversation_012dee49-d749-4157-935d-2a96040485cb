Name: t-rds-rdsapi-ext-mysql
Version:*******
Release: %(echo $RELEASE)%{?dist}
# if you want use the parameter of rpm_create on build time,
# uncomment below
Summary: RDSAPI MySQL extension
Group: alibaba/application
License: Commercial
AutoReqProv: none
%define _prefix /usr/local/rds/rdsapi-ext-mysql

BuildArch:noarch

# uncomment below, if depend on other packages
#Requires: package_name = 1.0.0

%description
rdsapi-ext-mysql


%prep
mkdir -p $RPM_BUILD_ROOT/%{_prefix}

#%setup -q

%build
cd $OLDPWD/../
mvn clean package -DskipTests=true

# prepare your files
%install


# create dirs
cd $OLDPWD/../
alias cp='cp'
mkdir -p $RPM_BUILD_ROOT%{_prefix}
rm -rf $RPM_BUILD_ROOT/%{_prefix}/rdsapi-ext-mysql.jar
cp -rf target/rdsapi-ext-mysql.jar $RPM_BUILD_ROOT/%{_prefix}/.


# package infomation
%files
# set file attribute here
%defattr(-,root,root)
# need not list every file here, keep it as this

%{_prefix}


%pre


%post
#define the scripts for post install


%postun
#define the scripts for post uninstall
chkconfig --del rdsapi-ext-mysql
if [ -e "/etc/init.d/rdsapi-ext-mysql" ]; then rm -f /etc/init.d/rdsapi-ext-mysql; fi


%changelog
* Wed Sep 21 2017 huangheng.hh
- add spec of t-rds-rdsapi-ext-mysql
