<?xml version="1.0"?>
<settings>
  <!--
  <localRepository>/Users/<USER>/.m2/repository</localRepository>
  -->
  <servers>
    <server>
      <id>b2b.repo.server</id>
      <username>admin</username>
      <password>secret</password>
    </server>
    <server>
      <id>scm.deploy.account</id>
      <username>admin</username>
      <password>secret</password>
    </server>
    <server>
      <id>releases</id>
      <username>admin</username>
      <password>secret</password>
    </server>
    <server>
      <id>snapshots</id>
      <username>snapshotsAdmin</username>
      <password>123456</password>
    </server>
    <server>
      <id>archiva.admin</id>
      <username>admin</username>
      <password>secret</password>
    </server>
  </servers>

  <!-- mirrors settings-->
  <mirrors>
    <mirror>
      <id>b2bmirror-all</id>
      <mirrorOf>*,!staging-repository,!staging-repository-1,!staging-repository-2,!staging-repository-3</mirrorOf>
      <url>http://repo.alibaba-inc.com/nexus/content/groups/alirepositiry</url>
    </mirror>
  </mirrors>


  <!-- ======================================================================== -->
  <!--  Profiles                                                                -->
  <!-- ======================================================================== -->
  <profiles>
    <profile>
      <!-- ======================================================================== -->
      <!--  Managed Repositories for alibaba Dev, include center repo-->
      <!-- ======================================================================== -->
      <id>alibaba</id>
      <repositories>
        <repository>
          <id>central</id>
          <name>alibaba Repositories Group</name>
          <url>http://repo.alibaba-inc.com/nexus/content/groups/alirepositiry/</url>
        </repository>
      </repositories>

      <!-- ======================================================================== -->
      <!--  Repositories for maven plugins          -->
      <!-- ======================================================================== -->
      <pluginRepositories>
        <pluginRepository>
          <id>central</id>
          <name>alibaba Plugin Repos Group</name>
          <url>http://repo.alibaba-inc.com/nexus/content/groups/alirepositiry/</url>
        </pluginRepository>
      </pluginRepositories>
    </profile>
    <profile>
      <id>nexus</id>
      <repositories>
        <repository>
          <id>central</id>
          <url>http://mvnrepo.alibaba-inc.com/mvn/repository</url>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <id>central</id>
          <url>http://mvnrepo.alibaba-inc.com/mvn/repository</url>
        </pluginRepository>
      </pluginRepositories>
    </profile>


  </profiles>

  <pluginGroups>
    <pluginGroup>com.alibaba.org.apache.maven.plugins</pluginGroup>
    <pluginGroup>com.alibaba.maven.plugins</pluginGroup>
  </pluginGroups>
  <activeProfiles>
    <activeProfile>alibaba</activeProfile>
  </activeProfiles>
</settings>
