# RDSAPI MySQL Extension module

- [Extension Module Developer Guides](https://lark.alipay.com/rdsapi/api/vt2wgr)


- Quick run cmd:

```bash

# download SAR file
PANDORA_VERSION=2018-05-release
SAR_FILE=/sar/taobao-hsf.tgz
sudo mkdir -p /sar && sudo chown $USER /sar && sudo curl -L -o "$SAR_FILE" "http://pandora-repos.cn-hangzhou.oss-share.aliyun-inc.com/sar/${PANDORA_VERSION}/taobao-hsf.tgz"

# your name or pinin initial, i.e., 聪心 as cx, for production use 0
ENV_ID=cx
ENABLE_HSF=true

mvn clean -DenvId=${ENV_ID} -Dhotcode.base="$(pwd)" -Dhsf.server.port=10990 -Dhsf.generic.throw.exception=true -Dspring-boot.run.arguments="--spring.hsf.enabled=$ENABLE_HSF" spring-boot:run | tee out.log

# OR if you are using dubbo multicast address, need to add -Djava.net.preferIPv4Stack=true 
mvn clean -DenvId=${ENV_ID} -Djava.net.preferIPv4Stack=true -Dhotcode.base="$(pwd)" -Dhsf.server.port=10990  -Dhsf.generic.throw.exception=true -Dspring-boot.run.arguments="--spring.hsf.enabled=$ENABLE_HSF" spring-boot:run | tee out.log



# run all unit tests
mvn clean test | tee test.log

# specify test classes
mvn clean test -Dtest=DBEngineExtAdapterTest | tee test.log

# specify CSV separator, default is '|', also try to avoid "," as EXPECT_RESULT is a JSON "," will need to be escaped
CSV_SEP=';'
mvn clean test -Dtest.csv.separator=${CSV_SEP} | tee test.log


# package final fat-jar distribution, ./target/${APP_NAME}-${BUILD_VERSION}.jar
source ./.gitlab-ci-variables
mvn versions:set -DnewVersion=${BUILD_VERSION}
mvn -T 4C -U -Dmaven.test.skip=${MAVEN_TEST_SKIP} -Dspring.profiles.active=unit-test -Ddebug=false clean package | tee package.log
```

- local integration environment, this comprise of :

* MySQL 5.6 DB docker container running, servicing localhost:3306
* Zookeeper docker container running, servicing localhost:2181

```bash
# docker compose up
docker-compose -p rds -f ./docker-compose.yml up -d

# your name or pinin initial, i.e., 聪心 as cx, for production use 0
ENV_ID=cx
mvn clean -DenvId=${ENV_ID} -Dhotcode.base="$(pwd)" -Dhsf.server.port=10990 -Dhsf.generic.throw.exception=true -Ddubbo.registry.address=zookeeper://localhost:2181  spring-boot:run | tee out.log

# docker compose down
docker-compose -p rds -f ./docker-compose.yml down
```

# Change Banner
```bash
# on MacOS use brew to install figlet
brew install figlet

# on  *nix
FIGLET_VERSION=2.2.3
curl ftp://ftp.figlet.org/pub/figlet/program/unix/figlet-$FIGLET_VERSION.tar.gz  | tar xvz
cd figlet-$FIGLET_VERSION
make && sudo make install

# generate banner
figlet "RDSAPI-EXT MYSQL" > src/main/resources/banner.txt
```

# References

- [Spring Boot 2.0.1.RELEASE Reference Guide](https://docs.spring.io/spring-boot/docs/2.0.1.RELEASE/reference/htmlsingle/)
- [Spring Boot 2.0.1.RELEASE Common application properties](https://docs.spring.io/spring-boot/docs/2.0.1.RELEASE/reference/html/common-application-properties.html)
- [Spring Boot 2.0.1.RELEASE Metrics](https://docs.spring.io/spring-boot/docs/2.0.1.RELEASE/reference/html/production-ready-metrics.html)
- [Spring Boot 2.0.1.RELEASE Caching](https://docs.spring.io/spring-boot/docs/current/reference/html/boot-features-caching.html)
- [Spring 5.0.5.RELEASE WebFlux](https://docs.spring.io/spring/docs/5.0.5.RELEASE/spring-framework-reference/web-reactive.html)
- [Spring 5.0.5.RELEASE Kotlin Support](https://docs.spring.io/spring/docs/5.0.5.RELEASE/spring-framework-reference/languages.html#kotlin)