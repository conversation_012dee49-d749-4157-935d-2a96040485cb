FROM reg.docker.alibaba-inc.com/apsaradb/ajdk-run-java:8.4.7GA

EXPOSE 9010

#EXPOSE 9999

ARG JAR_FILE

WORKDIR /

ADD target/${JAR_FILE} /rdsapi-ext-mysql.jar

#CMD ["java", "-Xdebug", "-Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=9999", "-Djava.net.preferIPv4Stack=true ", "-Xloggc:gc.log", "-XX:+PrintGCTimeStamps", "-XX:+PrintGCDetails", "-Duser.timezone=Asia/Shanghai", "-jar", "/usr/local/rds/rdsapi-ext-mysql/rdsapi-ext-mysql.jar", "--controller-server.use-shell-command=false"]
ENTRYPOINT ["/bin/sh", "-c" , "echo 127.0.0.1 $HOSTNAME >> /etc/hosts && java -jar /rdsapi-ext-mysql.jar"]
