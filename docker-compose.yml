version: '2.3'

services:
  mydb:
    image: physical:5.6
    restart: always
    network_mode: bridge
    mem_limit: 256M
    ports:
     - "3306:3306"
    environment:
      - "MYSQL_ROOT_PASSWORD=root"
      - "MYSQL_USER=user"
      - "MYSQL_PASSWORD=user"
      - "MYSQL_DATABASE=mydb"

  zookeeper:
    image: reg.docker.alibaba-inc.com/apsaradb/zookeeper:1.1.1
    network_mode: bridge
    restart: always
    shm_size: 256M
    mem_limit: 1G
    ports:
      - "2181:2181"
      - "2888:2888"
      - "3888:3888"