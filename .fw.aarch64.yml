# 构建docker镜像(专有云)模版样例
---
# 完整的构建模板标签说明：https://lark.alipay.com/mq4l6o/val2ny/prwhsh
# version表示构建模板.fw.yml的版本，默认值为1.0
version: 1.0
# name表示当前代码仓库的模块名称
name: dockerfiles

# variables表示用户自定义的参数，可在构建脚本中引用
# 在触发构建的时候，可通过动态参数覆盖variables中定义的默认值
# DOCKERFILE_PATH定义当前docker镜像的Dockerfile的目录，如果在源码根目录，使用"/"标识
variables:
  skipType: '0'
  RHEL_VERSION: 7
  PACKAGE_NAME: rdsapi_ext_mysql
  IMAGE_TAG: *******-aarch64
  DOCKERFILE_PATH: docker/Dockerfile-rdsapi_ext_mysql

# privatecloud标识专有云构建，version目前专有云支持v2和v3两个版本，v3会对编译过程中安装的依赖和安装包做源码溯源检查，v2版本不会
privatecloud:
  version: v3

# machine表示编译机配置，standard表示基于VM编译
# 5u7_base表示宿主机的装机模板是alios5的，default表示使用镜像自带内核版本
machine:
  standard:
    arm64_apsara_kunp:
      - default

# scripts中定义构建命令行，通过定义的构建命令行可以构建出artifacts中定义的构建产物
# 在scripts中可直接使用系统环境变量
scripts:
  - cd docker && sudo sh build-aarch64.sh build {{PACKAGE_NAME}} {{IMAGE_TAG}}  {{FW_REPO_URL}} {{FW_BRANCH_NAME}} {{FW_REPO_VERSION}}

# artifacts定义构建产物类型，目前支持rpms,packages,tianji,images四种类型定义
# images支持定义多个镜像名称，格式为name:tag，需要显示声明tag版本，否则会默认使用latest
# 对于images类型，需要单独给公共账号apsara-scm授权镜像的读写权限，否则会出现镜像因为没有授权push失败的情况
artifacts:
  images:
    - reg.docker.alibaba-inc.com/apsaradb/apsaradb-alios{{RHEL_VERSION}}u2-{{PACKAGE_NAME}}:{{IMAGE_TAG}}

# notifications定义通知人员列表，目前只支持邮件通知
# notifications:
#   email:

