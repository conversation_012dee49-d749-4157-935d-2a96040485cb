<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.aliyun.dba</groupId>
  <artifactId>rdsapi-ext-mysql</artifactId>
  <version>*******-SNAPSHOT</version>
  <packaging>jar</packaging>

  <name>${project.artifactId}</name>
  <description>RDSAPI-EXT MYSQL</description>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.0.3.RELEASE</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <properties>
    <start-class>com.aliyun.dba.RdsapiExtMysqlApplication</start-class>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <pandora-boot.version>2018-05-release</pandora-boot.version>

    <fastjson.version>1.2.68.noneautotype</fastjson.version>

    <LightApi.version>1.1.0</LightApi.version>
    <!-- open source dubbo version: 2.5.9; there are issues to for Aliyun's Dubbo registry -->
    <dubbo.all.version>2024.02.1.RELEASE</dubbo.all.version>
    <lombok.version>1.18.0</lombok.version>
    <rdsapi-common-lib.version>2.8.9-guangqi3-SNAPSHOT</rdsapi-common-lib.version>
    <commons-beanutils.version>1.9.3</commons-beanutils.version>
    <commons-lang.version>2.6</commons-lang.version>
    <guava.version>25.1-jre</guava.version>
<!--    <spring-boot-starter-dubbo.version>2.5.3-RC10</spring-boot-starter-dubbo.version>-->
    <mockito-all.version>1.10.19</mockito-all.version>
    <!--<skipTests>true</skipTests>-->
  </properties>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>

      <!--<dependency>-->
        <!--<groupId>pl.pragmatists</groupId>-->
        <!--<artifactId>JUnitParams</artifactId>-->
        <!--<version>1.1.1</version>-->
      <!--</dependency>-->

      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-csv</artifactId>
        <version>2.8.8</version>
      </dependency>

    </dependencies>
  </dependencyManagement>
  <dependencies>

    <!--add dependency for logsdk-->
    <dependency>
      <groupId>com.aliyun.rds</groupId>
      <artifactId>logsdk</artifactId>
      <version>0.2.8-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun.apsaradb</groupId>
      <artifactId>dbaas-meta-api-client</artifactId>
      <version>1.3.79-EXT</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun.apsaradb</groupId>
      <artifactId>gdn-meta-api-client</artifactId>
      <version>1.0.7-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun.apsaradb</groupId>
      <artifactId>common-provider-api-client</artifactId>
      <version>1.34.0</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun.apsaradb</groupId>
      <artifactId>workflow-api-client</artifactId>
      <version>1.0.5-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun.dba</groupId>
      <artifactId>adb-name-service-client</artifactId>
      <version>1.1.7-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>servlet-api</artifactId>
      <version>3.1.0</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
      <version>1.3.2</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun.dubbo</groupId>
      <artifactId>dubbo-all-starter</artifactId>
      <version>${dubbo.all.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>micrometer-tracing</artifactId>
          <groupId>io.micrometer</groupId>
        </exclusion>
        <exclusion>
          <groupId>io.micrometer</groupId>
          <artifactId>micrometer-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>



    <!--    <dependency>-->
<!--      <groupId>com.aliyun.boot</groupId>-->
<!--      <artifactId>spring-boot-starter-dubbo</artifactId>-->
<!--      <version>${spring-boot-starter-dubbo.version}</version>-->
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <artifactId>java.servlet</artifactId>-->
<!--          <groupId>com.alibaba.external</groupId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
<!--    </dependency>-->

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-cache</artifactId>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>${guava.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>

    <dependency>
      <groupId>com.udojava</groupId>
      <artifactId>EvalEx</artifactId>
      <version>2.5</version>
    </dependency>

    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>${commons-lang.version}</version>
    </dependency>

    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
      <version>${commons-beanutils.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.retry</groupId>
      <artifactId>spring-retry</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aliyun.dbpaas</groupId>
      <artifactId>rdsapi-ext-observibility-starter</artifactId>
      <version>0.0.9</version>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>druid</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.micrometer</groupId>
          <artifactId>micrometer-registry-prometheus</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.restdocs</groupId>
      <artifactId>spring-restdocs-webtestclient</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.aliyun.dba</groupId>
      <artifactId>rdsapi-common-lib</artifactId>
      <version>${rdsapi-common-lib.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>resmanager-sdk</artifactId>
          <groupId>com.alicloud.apsaradb</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>druid-spring-boot-starter</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <version>1.2.19</version>
    </dependency>

    <dependency>
      <artifactId>resmanager-sdk</artifactId>
      <groupId>com.alicloud.apsaradb</groupId>
      <version>3.0.15-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.alicloud.apsaradb</groupId>
      <artifactId>rm-provider-sdk</artifactId>
      <version>1.2.19-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.alicloud.apsaradb</groupId>
      <artifactId>inventory-sdk</artifactId>
      <version>1.1.15-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.101tec</groupId>
      <artifactId>zkclient</artifactId>
      <version>0.10</version>
      <!--<scope>test</scope>-->
    </dependency>

    <!-- test dependencies -->
    <!--<dependency>-->
      <!--<groupId>pl.pragmatists</groupId>-->
      <!--<artifactId>JUnitParams</artifactId>-->
      <!--<scope>test</scope>-->
    <!--</dependency>-->
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-csv</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.aliyun.dba</groupId>
      <artifactId>adb_vip_manager_client</artifactId>
      <version>1.1.33-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.alicloud.apsaradb</groupId>
      <artifactId>rm-k8s-sdk</artifactId>
      <version>2.0.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>aliyun-java-sdk-sts-internal</artifactId>
      <version>3.2.0</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>aliyun-java-sdk-ram-inner</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>aliyun-java-sdk-kms-inner</artifactId>
      <version>2.16.2</version>
    </dependency>


    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>dts20200101</artifactId>
      <version>2.5.4</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>kms20160120</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>aliyun-java-sdk-accountlabel</artifactId>
      <version>1.0.4</version>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-core</artifactId>
      <version>1.12.5</version>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
      <version>1.12.5</version>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-tracing</artifactId>
      <version>1.2.5</version>
    </dependency>
    <dependency>
      <groupId>com.lmax</groupId>
      <artifactId>disruptor</artifactId>
      <version>3.4.2</version>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-core</artifactId>
      <version>2.0.9</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-api-mockito2</artifactId>
      <version>2.0.9</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-module-junit4</artifactId>
      <version>2.0.9</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>alibabacloud-cms20190101</artifactId>
      <version>1.0.8</version>
    </dependency>
    <dependency>
      <groupId>org.jacoco</groupId>
      <artifactId>jacoco-maven-plugin</artifactId>
      <version>0.8.7</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <repositories>
    <repository>
      <id>central</id>
      <url>http://mvnrepo.alibaba-inc.com/mvn/repository</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>snapshots</id>
      <url>http://mvnrepo.alibaba-inc.com/mvn/repository</url>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>Bundled libs</id>
      <url>file://${basedir}/lib</url>
    </repository>
  </repositories>

  <pluginRepositories>
    <pluginRepository>
      <id>central</id>
      <url>http://mvnrepo.alibaba-inc.com/mvn/repository</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </pluginRepository>
    <pluginRepository>
      <id>snapshots</id>
      <url>http://mvnrepo.alibaba-inc.com/mvn/repository</url>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>

  <distributionManagement>
    <repository>
      <id>releases</id>
      <url>http://mvnrepo.alibaba-inc.com/mvn/releases</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <url>http://mvnrepo.alibaba-inc.com/mvn/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.7</version>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.spotify</groupId>
        <artifactId>dockerfile-maven-plugin</artifactId>
        <version>1.4.10</version>
<!--        <executions>-->
<!--          <execution>-->
<!--            <id>default</id>-->
<!--            <goals>-->
<!--              <goal>build</goal>-->
<!--&lt;!&ndash;              <goal>push</goal>&ndash;&gt;-->
<!--            </goals>-->
<!--          </execution>-->
<!--        </executions>-->
        <configuration>
          <repository>reg.docker.alibaba-inc.com/apsaradb/${project.artifactId}</repository>
          <tag>${project.version}</tag>
          <buildArgs>
            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
          </buildArgs>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
