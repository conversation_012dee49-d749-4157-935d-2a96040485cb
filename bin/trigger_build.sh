#!/bin/bash

token=$1
branch=$2

if [ -z "$token" ];then
   token=4efdc65aee1f3c3fc38a2f617e7bfc
fi

if [ -z "$branch" ];then
    branch=$(git rev-parse --abbrev-ref HEAD)
    if [ -z "$branch" ];then
        branch=master
    fi
fi

echo "token=$token"
echo "branch=$branch"

curl -X POST \
     -F token=$token \
     -F ref=$branch \
     http://gitlab.alibaba-inc.com/api/v3/projects/169092/trigger/builds